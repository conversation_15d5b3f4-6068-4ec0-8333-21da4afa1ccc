<?php

namespace App\Console\Commands\ProjectManagement;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * أمر مراقبة الأداء
 * 
 * هذا الأمر يراقب أداء قاعدة البيانات ويرسل تنبيهات عند وجود مشاكل
 * يمكن جدولته للتشغيل كل دقيقة أو حسب الحاجة
 * 
 * @package App\Console\Commands\ProjectManagement
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
class MonitorPerformanceCommand extends Command
{
    /**
     * اسم الأمر وتوقيعه
     *
     * @var string
     */
    protected $signature = 'pm:monitor-performance 
                            {--alert : إرسال تنبيهات عند وجود مشاكل}
                            {--log : تسجيل النتائج في ملف السجل}
                            {--threshold-slow=5 : حد الاستعلامات البطيئة بالثواني}
                            {--threshold-size=1000 : حد حجم الجدول بالميجابايت}
                            {--threshold-connections=80 : حد نسبة الاتصالات المستخدمة}';

    /**
     * وصف الأمر
     *
     * @var string
     */
    protected $description = 'مراقبة أداء قاعدة البيانات وإرسال تنبيهات عند وجود مشاكل';

    /**
     * حدود التنبيهات الافتراضية
     */
    const DEFAULT_SLOW_QUERY_THRESHOLD = 5; // ثواني
    const DEFAULT_TABLE_SIZE_THRESHOLD = 1000; // ميجابايت
    const DEFAULT_CONNECTION_THRESHOLD = 80; // نسبة مئوية

    /**
     * تنفيذ الأمر
     *
     * @return int
     */
    public function handle(): int
    {
        $this->info('🔍 بدء مراقبة أداء قاعدة البيانات...');
        $this->newLine();

        try {
            $monitoringResults = [
                'timestamp' => now()->toDateTimeString(),
                'database_size' => $this->checkDatabaseSize(),
                'table_sizes' => $this->checkTableSizes(),
                'slow_queries' => $this->checkSlowQueries(),
                'connections' => $this->checkConnections(),
                'index_usage' => $this->checkIndexUsage(),
                'cache_performance' => $this->checkCachePerformance(),
                'disk_space' => $this->checkDiskSpace(),
            ];

            // تحليل النتائج والبحث عن مشاكل
            $issues = $this->analyzeResults($monitoringResults);

            // عرض النتائج
            $this->displayResults($monitoringResults, $issues);

            // إرسال تنبيهات إذا طُلب ذلك
            if ($this->option('alert') && !empty($issues)) {
                $this->sendAlerts($issues);
            }

            // تسجيل النتائج إذا طُلب ذلك
            if ($this->option('log')) {
                $this->logResults($monitoringResults, $issues);
            }

            // حفظ النتائج في قاعدة البيانات للمتابعة التاريخية
            $this->saveMonitoringData($monitoringResults, $issues);

            if (empty($issues)) {
                $this->info('✅ جميع المؤشرات ضمن الحدود الطبيعية');
            } else {
                $this->warn("⚠️  تم اكتشاف " . count($issues) . " مشكلة تحتاج للمتابعة");
            }

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('❌ خطأ في مراقبة الأداء: ' . $e->getMessage());
            Log::error('خطأ في أمر مراقبة الأداء', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return Command::FAILURE;
        }
    }

    /**
     * فحص حجم قاعدة البيانات الإجمالي
     *
     * @return array
     */
    protected function checkDatabaseSize(): array
    {
        $result = DB::selectOne("
            SELECT 
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb,
                COUNT(*) AS table_count
            FROM information_schema.tables 
            WHERE table_schema = DATABASE()
        ");

        return [
            'total_size_mb' => $result->size_mb ?? 0,
            'table_count' => $result->table_count ?? 0,
            'status' => $result->size_mb > 5000 ? 'warning' : 'ok'
        ];
    }

    /**
     * فحص أحجام الجداول الفردية
     *
     * @return array
     */
    protected function checkTableSizes(): array
    {
        $threshold = (float) $this->option('threshold-size');
        
        $tables = DB::select("
            SELECT 
                table_name,
                ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb,
                table_rows
            FROM information_schema.tables 
            WHERE table_schema = DATABASE()
            ORDER BY (data_length + index_length) DESC
            LIMIT 10
        ");

        $largeTables = array_filter($tables, function($table) use ($threshold) {
            return $table->size_mb > $threshold;
        });

        return [
            'largest_tables' => $tables,
            'large_tables_count' => count($largeTables),
            'threshold_mb' => $threshold,
            'status' => count($largeTables) > 0 ? 'warning' : 'ok'
        ];
    }

    /**
     * فحص الاستعلامات البطيئة
     *
     * @return array
     */
    protected function checkSlowQueries(): array
    {
        $threshold = (float) $this->option('threshold-slow');
        
        try {
            $slowQueries = DB::select("
                SELECT 
                    query_time,
                    lock_time,
                    rows_sent,
                    rows_examined,
                    LEFT(sql_text, 100) as sql_preview
                FROM mysql.slow_log 
                WHERE start_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
                AND query_time > ?
                ORDER BY query_time DESC 
                LIMIT 5
            ", [$threshold]);

            return [
                'slow_queries' => $slowQueries,
                'count' => count($slowQueries),
                'threshold_seconds' => $threshold,
                'status' => count($slowQueries) > 0 ? 'warning' : 'ok'
            ];

        } catch (\Exception $e) {
            return [
                'slow_queries' => [],
                'count' => 0,
                'error' => 'لا يمكن الوصول لجدول slow_log',
                'status' => 'unknown'
            ];
        }
    }

    /**
     * فحص اتصالات قاعدة البيانات
     *
     * @return array
     */
    protected function checkConnections(): array
    {
        $threshold = (int) $this->option('threshold-connections');
        
        try {
            $status = DB::selectOne("SHOW STATUS LIKE 'Threads_connected'");
            $variables = DB::selectOne("SHOW VARIABLES LIKE 'max_connections'");
            
            $currentConnections = (int) $status->Value;
            $maxConnections = (int) $variables->Value;
            $usagePercentage = round(($currentConnections / $maxConnections) * 100, 2);

            return [
                'current_connections' => $currentConnections,
                'max_connections' => $maxConnections,
                'usage_percentage' => $usagePercentage,
                'threshold_percentage' => $threshold,
                'status' => $usagePercentage > $threshold ? 'warning' : 'ok'
            ];

        } catch (\Exception $e) {
            return [
                'error' => 'لا يمكن الحصول على معلومات الاتصالات',
                'status' => 'unknown'
            ];
        }
    }

    /**
     * فحص استخدام الفهارس
     *
     * @return array
     */
    protected function checkIndexUsage(): array
    {
        try {
            $unusedIndexes = DB::select("
                SELECT 
                    t.table_schema,
                    t.table_name,
                    t.index_name
                FROM information_schema.statistics t
                LEFT JOIN performance_schema.table_io_waits_summary_by_index_usage p 
                    ON t.table_schema = p.object_schema 
                    AND t.table_name = p.object_name 
                    AND t.index_name = p.index_name
                WHERE t.table_schema = DATABASE()
                AND p.index_name IS NULL
                AND t.index_name != 'PRIMARY'
                LIMIT 10
            ");

            return [
                'unused_indexes' => $unusedIndexes,
                'count' => count($unusedIndexes),
                'status' => count($unusedIndexes) > 5 ? 'warning' : 'ok'
            ];

        } catch (\Exception $e) {
            return [
                'error' => 'لا يمكن فحص استخدام الفهارس',
                'status' => 'unknown'
            ];
        }
    }

    /**
     * فحص أداء التخزين المؤقت
     *
     * @return array
     */
    protected function checkCachePerformance(): array
    {
        try {
            $cacheStats = DB::table('statistics_cache')
                ->selectRaw('
                    COUNT(*) as total_entries,
                    SUM(hit_count) as total_hits,
                    AVG(hit_count) as avg_hits,
                    COUNT(CASE WHEN expires_at > NOW() THEN 1 END) as active_entries,
                    COUNT(CASE WHEN expires_at <= NOW() THEN 1 END) as expired_entries
                ')
                ->first();

            $hitRate = $cacheStats->total_entries > 0 
                ? round(($cacheStats->total_hits / $cacheStats->total_entries) * 100, 2) 
                : 0;

            return [
                'total_entries' => $cacheStats->total_entries,
                'active_entries' => $cacheStats->active_entries,
                'expired_entries' => $cacheStats->expired_entries,
                'hit_rate_percentage' => $hitRate,
                'status' => $hitRate < 50 ? 'warning' : 'ok'
            ];

        } catch (\Exception $e) {
            return [
                'error' => 'لا يمكن فحص أداء التخزين المؤقت',
                'status' => 'unknown'
            ];
        }
    }

    /**
     * فحص مساحة القرص
     *
     * @return array
     */
    protected function checkDiskSpace(): array
    {
        try {
            $dataDir = DB::selectOne("SHOW VARIABLES LIKE 'datadir'")->Value;
            $freeBytes = disk_free_space($dataDir);
            $totalBytes = disk_total_space($dataDir);
            
            $freePercentage = round(($freeBytes / $totalBytes) * 100, 2);

            return [
                'free_space_gb' => round($freeBytes / 1024 / 1024 / 1024, 2),
                'total_space_gb' => round($totalBytes / 1024 / 1024 / 1024, 2),
                'free_percentage' => $freePercentage,
                'status' => $freePercentage < 20 ? 'critical' : ($freePercentage < 50 ? 'warning' : 'ok')
            ];

        } catch (\Exception $e) {
            return [
                'error' => 'لا يمكن فحص مساحة القرص',
                'status' => 'unknown'
            ];
        }
    }

    /**
     * تحليل النتائج والبحث عن مشاكل
     *
     * @param array $results النتائج
     * @return array المشاكل المكتشفة
     */
    protected function analyzeResults(array $results): array
    {
        $issues = [];

        // فحص حجم قاعدة البيانات
        if ($results['database_size']['status'] === 'warning') {
            $issues[] = [
                'type' => 'database_size',
                'severity' => 'warning',
                'message' => "حجم قاعدة البيانات كبير: {$results['database_size']['total_size_mb']} MB"
            ];
        }

        // فحص الجداول الكبيرة
        if ($results['table_sizes']['status'] === 'warning') {
            $issues[] = [
                'type' => 'large_tables',
                'severity' => 'warning',
                'message' => "يوجد {$results['table_sizes']['large_tables_count']} جدول كبير الحجم"
            ];
        }

        // فحص الاستعلامات البطيئة
        if ($results['slow_queries']['status'] === 'warning') {
            $issues[] = [
                'type' => 'slow_queries',
                'severity' => 'warning',
                'message' => "يوجد {$results['slow_queries']['count']} استعلام بطيء في الساعة الماضية"
            ];
        }

        // فحص الاتصالات
        if ($results['connections']['status'] === 'warning') {
            $issues[] = [
                'type' => 'high_connections',
                'severity' => 'warning',
                'message' => "استخدام عالي للاتصالات: {$results['connections']['usage_percentage']}%"
            ];
        }

        // فحص مساحة القرص
        if ($results['disk_space']['status'] === 'critical') {
            $issues[] = [
                'type' => 'disk_space',
                'severity' => 'critical',
                'message' => "مساحة القرص منخفضة: {$results['disk_space']['free_percentage']}% متبقية"
            ];
        } elseif ($results['disk_space']['status'] === 'warning') {
            $issues[] = [
                'type' => 'disk_space',
                'severity' => 'warning',
                'message' => "مساحة القرص تحتاج للمراقبة: {$results['disk_space']['free_percentage']}% متبقية"
            ];
        }

        return $issues;
    }

    /**
     * عرض النتائج
     *
     * @param array $results النتائج
     * @param array $issues المشاكل
     */
    protected function displayResults(array $results, array $issues): void
    {
        $this->info('📊 نتائج مراقبة الأداء:');
        $this->newLine();

        // حجم قاعدة البيانات
        $this->line("💾 حجم قاعدة البيانات: {$results['database_size']['total_size_mb']} MB");
        
        // أكبر الجداول
        $this->line("📋 أكبر الجداول:");
        foreach (array_slice($results['table_sizes']['largest_tables'], 0, 3) as $table) {
            $this->line("   • {$table->table_name}: {$table->size_mb} MB");
        }

        // الاتصالات
        if (isset($results['connections']['current_connections'])) {
            $this->line("🔗 الاتصالات: {$results['connections']['current_connections']}/{$results['connections']['max_connections']} ({$results['connections']['usage_percentage']}%)");
        }

        // مساحة القرص
        if (isset($results['disk_space']['free_space_gb'])) {
            $this->line("💿 مساحة القرص: {$results['disk_space']['free_space_gb']} GB متبقية ({$results['disk_space']['free_percentage']}%)");
        }

        $this->newLine();

        // عرض المشاكل
        if (!empty($issues)) {
            $this->warn('⚠️  المشاكل المكتشفة:');
            foreach ($issues as $issue) {
                $icon = $issue['severity'] === 'critical' ? '🔴' : '🟡';
                $this->line("   {$icon} {$issue['message']}");
            }
        }
    }

    /**
     * إرسال تنبيهات
     *
     * @param array $issues المشاكل
     */
    protected function sendAlerts(array $issues): void
    {
        foreach ($issues as $issue) {
            if ($issue['severity'] === 'critical') {
                // إرسال تنبيه فوري للمشاكل الحرجة
                Log::critical('مشكلة حرجة في أداء قاعدة البيانات', $issue);
                // يمكن إضافة إرسال إيميل أو SMS هنا
            } else {
                Log::warning('تحذير في أداء قاعدة البيانات', $issue);
            }
        }
    }

    /**
     * تسجيل النتائج في ملف السجل
     *
     * @param array $results النتائج
     * @param array $issues المشاكل
     */
    protected function logResults(array $results, array $issues): void
    {
        Log::info('نتائج مراقبة أداء قاعدة البيانات', [
            'results' => $results,
            'issues_count' => count($issues),
            'issues' => $issues
        ]);
    }

    /**
     * حفظ بيانات المراقبة في قاعدة البيانات
     *
     * @param array $results النتائج
     * @param array $issues المشاكل
     */
    protected function saveMonitoringData(array $results, array $issues): void
    {
        try {
            DB::table('daily_statistics')->insert([
                'date' => now()->toDateString(),
                'metric_type' => 'database_monitoring',
                'entity_type' => 'system',
                'value' => count($issues),
                'metadata' => json_encode([
                    'results' => $results,
                    'issues' => $issues
                ]),
                'calculated_at' => now()
            ]);
        } catch (\Exception $e) {
            Log::warning('فشل في حفظ بيانات المراقبة', ['error' => $e->getMessage()]);
        }
    }
}
