<?php
// This file was auto-generated from sdk-root/src/data/invoicing/2024-12-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2024-12-01', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'invoicing', 'jsonVersion' => '1.0', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'AWS Invoicing', 'serviceId' => 'Invoicing', 'signatureVersion' => 'v4', 'signingName' => 'invoicing', 'targetPrefix' => 'Invoicing', 'uid' => 'invoicing-2024-12-01', ], 'operations' => [ 'BatchGetInvoiceProfile' => [ 'name' => 'BatchGetInvoiceProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetInvoiceProfileRequest', ], 'output' => [ 'shape' => 'BatchGetInvoiceProfileResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateInvoiceUnit' => [ 'name' => 'CreateInvoiceUnit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateInvoiceUnitRequest', ], 'output' => [ 'shape' => 'CreateInvoiceUnitResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteInvoiceUnit' => [ 'name' => 'DeleteInvoiceUnit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteInvoiceUnitRequest', ], 'output' => [ 'shape' => 'DeleteInvoiceUnitResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetInvoiceUnit' => [ 'name' => 'GetInvoiceUnit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInvoiceUnitRequest', ], 'output' => [ 'shape' => 'GetInvoiceUnitResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListInvoiceSummaries' => [ 'name' => 'ListInvoiceSummaries', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListInvoiceSummariesRequest', ], 'output' => [ 'shape' => 'ListInvoiceSummariesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListInvoiceUnits' => [ 'name' => 'ListInvoiceUnits', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListInvoiceUnitsRequest', ], 'output' => [ 'shape' => 'ListInvoiceUnitsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateInvoiceUnit' => [ 'name' => 'UpdateInvoiceUnit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateInvoiceUnitRequest', ], 'output' => [ 'shape' => 'UpdateInvoiceUnitResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'BasicString', ], 'resourceName' => [ 'shape' => 'InvoiceUnitArnString', ], ], 'exception' => true, ], 'AccountIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountIdString', ], 'max' => 1000, 'min' => 1, ], 'AccountIdString' => [ 'type' => 'string', 'pattern' => '\\d{12}', ], 'AmountBreakdown' => [ 'type' => 'structure', 'members' => [ 'SubTotalAmount' => [ 'shape' => 'BasicString', ], 'Discounts' => [ 'shape' => 'DiscountsBreakdown', ], 'Taxes' => [ 'shape' => 'TaxesBreakdown', ], 'Fees' => [ 'shape' => 'FeesBreakdown', ], ], ], 'AsOfTimestamp' => [ 'type' => 'timestamp', ], 'BasicString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'BasicStringWithoutSpace' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '\\S+', ], 'BatchGetInvoiceProfileRequest' => [ 'type' => 'structure', 'required' => [ 'AccountIds', ], 'members' => [ 'AccountIds' => [ 'shape' => 'AccountIdList', ], ], ], 'BatchGetInvoiceProfileResponse' => [ 'type' => 'structure', 'members' => [ 'Profiles' => [ 'shape' => 'ProfileList', ], ], ], 'BillingPeriod' => [ 'type' => 'structure', 'required' => [ 'Month', 'Year', ], 'members' => [ 'Month' => [ 'shape' => 'Month', ], 'Year' => [ 'shape' => 'Year', ], ], ], 'CreateInvoiceUnitRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'InvoiceReceiver', 'Rule', ], 'members' => [ 'Name' => [ 'shape' => 'InvoiceUnitName', ], 'InvoiceReceiver' => [ 'shape' => 'AccountIdString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'TaxInheritanceDisabled' => [ 'shape' => 'TaxInheritanceDisabledFlag', ], 'Rule' => [ 'shape' => 'InvoiceUnitRule', ], 'ResourceTags' => [ 'shape' => 'ResourceTagList', ], ], ], 'CreateInvoiceUnitResponse' => [ 'type' => 'structure', 'members' => [ 'InvoiceUnitArn' => [ 'shape' => 'InvoiceUnitArnString', ], ], ], 'CurrencyCode' => [ 'type' => 'string', 'max' => 3, 'min' => 3, ], 'CurrencyExchangeDetails' => [ 'type' => 'structure', 'members' => [ 'SourceCurrencyCode' => [ 'shape' => 'BasicString', ], 'TargetCurrencyCode' => [ 'shape' => 'BasicString', ], 'Rate' => [ 'shape' => 'BasicString', ], ], ], 'DateInterval' => [ 'type' => 'structure', 'required' => [ 'StartDate', 'EndDate', ], 'members' => [ 'StartDate' => [ 'shape' => 'Timestamp', ], 'EndDate' => [ 'shape' => 'Timestamp', ], ], ], 'DeleteInvoiceUnitRequest' => [ 'type' => 'structure', 'required' => [ 'InvoiceUnitArn', ], 'members' => [ 'InvoiceUnitArn' => [ 'shape' => 'InvoiceUnitArnString', ], ], ], 'DeleteInvoiceUnitResponse' => [ 'type' => 'structure', 'members' => [ 'InvoiceUnitArn' => [ 'shape' => 'InvoiceUnitArnString', ], ], ], 'DescriptionString' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '[\\S\\s]*', ], 'DiscountsBreakdown' => [ 'type' => 'structure', 'members' => [ 'Breakdown' => [ 'shape' => 'DiscountsBreakdownAmountList', ], 'TotalAmount' => [ 'shape' => 'BasicString', ], ], ], 'DiscountsBreakdownAmount' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'BasicString', ], 'Amount' => [ 'shape' => 'BasicString', ], 'Rate' => [ 'shape' => 'BasicString', ], ], ], 'DiscountsBreakdownAmountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DiscountsBreakdownAmount', ], ], 'Entity' => [ 'type' => 'structure', 'members' => [ 'InvoicingEntity' => [ 'shape' => 'BasicString', ], ], ], 'FeesBreakdown' => [ 'type' => 'structure', 'members' => [ 'Breakdown' => [ 'shape' => 'FeesBreakdownAmountList', ], 'TotalAmount' => [ 'shape' => 'BasicString', ], ], ], 'FeesBreakdownAmount' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'BasicString', ], 'Amount' => [ 'shape' => 'BasicString', ], 'Rate' => [ 'shape' => 'BasicString', ], ], ], 'FeesBreakdownAmountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FeesBreakdownAmount', ], ], 'Filters' => [ 'type' => 'structure', 'members' => [ 'Names' => [ 'shape' => 'InvoiceUnitNames', ], 'InvoiceReceivers' => [ 'shape' => 'AccountIdList', ], 'Accounts' => [ 'shape' => 'AccountIdList', ], ], ], 'GetInvoiceUnitRequest' => [ 'type' => 'structure', 'required' => [ 'InvoiceUnitArn', ], 'members' => [ 'InvoiceUnitArn' => [ 'shape' => 'InvoiceUnitArnString', ], 'AsOf' => [ 'shape' => 'AsOfTimestamp', ], ], ], 'GetInvoiceUnitResponse' => [ 'type' => 'structure', 'members' => [ 'InvoiceUnitArn' => [ 'shape' => 'InvoiceUnitArnString', ], 'InvoiceReceiver' => [ 'shape' => 'AccountIdString', ], 'Name' => [ 'shape' => 'InvoiceUnitName', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'TaxInheritanceDisabled' => [ 'shape' => 'TaxInheritanceDisabledFlag', 'box' => true, ], 'Rule' => [ 'shape' => 'InvoiceUnitRule', ], 'LastModified' => [ 'shape' => 'LastModifiedTimestamp', ], ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'retryAfterSeconds' => [ 'shape' => 'Integer', ], 'message' => [ 'shape' => 'BasicString', ], ], 'exception' => true, 'fault' => true, ], 'InvoiceCurrencyAmount' => [ 'type' => 'structure', 'members' => [ 'TotalAmount' => [ 'shape' => 'BasicString', ], 'TotalAmountBeforeTax' => [ 'shape' => 'BasicString', ], 'CurrencyCode' => [ 'shape' => 'CurrencyCode', ], 'AmountBreakdown' => [ 'shape' => 'AmountBreakdown', ], 'CurrencyExchangeDetails' => [ 'shape' => 'CurrencyExchangeDetails', ], ], ], 'InvoiceProfile' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountIdString', ], 'ReceiverName' => [ 'shape' => 'BasicStringWithoutSpace', ], 'ReceiverAddress' => [ 'shape' => 'ReceiverAddress', ], 'ReceiverEmail' => [ 'shape' => 'SensitiveBasicStringWithoutSpace', ], 'Issuer' => [ 'shape' => 'BasicStringWithoutSpace', ], 'TaxRegistrationNumber' => [ 'shape' => 'SensitiveBasicStringWithoutSpace', ], ], ], 'InvoiceSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'InvoiceSummary', ], ], 'InvoiceSummariesFilter' => [ 'type' => 'structure', 'members' => [ 'TimeInterval' => [ 'shape' => 'DateInterval', ], 'BillingPeriod' => [ 'shape' => 'BillingPeriod', ], 'InvoicingEntity' => [ 'shape' => 'BasicString', ], ], ], 'InvoiceSummariesMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'InvoiceSummariesSelector' => [ 'type' => 'structure', 'required' => [ 'ResourceType', 'Value', ], 'members' => [ 'ResourceType' => [ 'shape' => 'ListInvoiceSummariesResourceType', ], 'Value' => [ 'shape' => 'StringWithoutNewLine', ], ], ], 'InvoiceSummary' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountIdString', ], 'InvoiceId' => [ 'shape' => 'BasicString', ], 'IssuedDate' => [ 'shape' => 'Timestamp', ], 'DueDate' => [ 'shape' => 'Timestamp', ], 'Entity' => [ 'shape' => 'Entity', ], 'BillingPeriod' => [ 'shape' => 'BillingPeriod', ], 'InvoiceType' => [ 'shape' => 'InvoiceType', ], 'OriginalInvoiceId' => [ 'shape' => 'BasicString', ], 'PurchaseOrderNumber' => [ 'shape' => 'BasicString', ], 'BaseCurrencyAmount' => [ 'shape' => 'InvoiceCurrencyAmount', ], 'TaxCurrencyAmount' => [ 'shape' => 'InvoiceCurrencyAmount', ], 'PaymentCurrencyAmount' => [ 'shape' => 'InvoiceCurrencyAmount', ], ], ], 'InvoiceType' => [ 'type' => 'string', 'enum' => [ 'INVOICE', 'CREDIT_MEMO', ], ], 'InvoiceUnit' => [ 'type' => 'structure', 'members' => [ 'InvoiceUnitArn' => [ 'shape' => 'InvoiceUnitArnString', ], 'InvoiceReceiver' => [ 'shape' => 'AccountIdString', ], 'Name' => [ 'shape' => 'InvoiceUnitName', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'TaxInheritanceDisabled' => [ 'shape' => 'TaxInheritanceDisabledFlag', 'box' => true, ], 'Rule' => [ 'shape' => 'InvoiceUnitRule', ], 'LastModified' => [ 'shape' => 'LastModifiedTimestamp', ], ], ], 'InvoiceUnitArnString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => 'arn:aws[-a-z0-9]*:[a-z0-9]+:[-a-z0-9]*:[0-9]{12}:[-a-zA-Z0-9/:_]+', ], 'InvoiceUnitName' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '(?! )[\\p{L}\\p{N}\\p{Z}-_]*(?<! )', ], 'InvoiceUnitNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'InvoiceUnitName', ], ], 'InvoiceUnitRule' => [ 'type' => 'structure', 'members' => [ 'LinkedAccounts' => [ 'shape' => 'AccountIdList', ], ], ], 'InvoiceUnits' => [ 'type' => 'list', 'member' => [ 'shape' => 'InvoiceUnit', ], ], 'LastModifiedTimestamp' => [ 'type' => 'timestamp', ], 'ListInvoiceSummariesRequest' => [ 'type' => 'structure', 'required' => [ 'Selector', ], 'members' => [ 'Selector' => [ 'shape' => 'InvoiceSummariesSelector', ], 'Filter' => [ 'shape' => 'InvoiceSummariesFilter', ], 'NextToken' => [ 'shape' => 'NextTokenString', ], 'MaxResults' => [ 'shape' => 'InvoiceSummariesMaxResults', ], ], ], 'ListInvoiceSummariesResourceType' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT_ID', 'INVOICE_ID', ], ], 'ListInvoiceSummariesResponse' => [ 'type' => 'structure', 'required' => [ 'InvoiceSummaries', ], 'members' => [ 'InvoiceSummaries' => [ 'shape' => 'InvoiceSummaries', ], 'NextToken' => [ 'shape' => 'NextTokenString', ], ], ], 'ListInvoiceUnitsRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'Filters', ], 'NextToken' => [ 'shape' => 'NextTokenString', ], 'MaxResults' => [ 'shape' => 'MaxResultsInteger', ], 'AsOf' => [ 'shape' => 'AsOfTimestamp', ], ], ], 'ListInvoiceUnitsResponse' => [ 'type' => 'structure', 'members' => [ 'InvoiceUnits' => [ 'shape' => 'InvoiceUnits', ], 'NextToken' => [ 'shape' => 'NextTokenString', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'TagrisArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceTags' => [ 'shape' => 'ResourceTagList', ], ], ], 'MaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 500, 'min' => 1, ], 'Month' => [ 'type' => 'integer', 'box' => true, 'max' => 12, 'min' => 1, ], 'NextTokenString' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[\\S\\s]*', ], 'ProfileList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InvoiceProfile', ], ], 'ReceiverAddress' => [ 'type' => 'structure', 'members' => [ 'AddressLine1' => [ 'shape' => 'BasicString', ], 'AddressLine2' => [ 'shape' => 'BasicString', ], 'AddressLine3' => [ 'shape' => 'BasicString', ], 'DistrictOrCounty' => [ 'shape' => 'BasicString', ], 'City' => [ 'shape' => 'BasicString', ], 'StateOrRegion' => [ 'shape' => 'BasicString', ], 'CountryCode' => [ 'shape' => 'BasicString', ], 'CompanyName' => [ 'shape' => 'BasicString', ], 'PostalCode' => [ 'shape' => 'BasicString', ], ], 'sensitive' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'BasicString', ], 'resourceName' => [ 'shape' => 'InvoiceUnitArnString', ], ], 'exception' => true, ], 'ResourceTag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'ResourceTagKey', ], 'Value' => [ 'shape' => 'ResourceTagValue', ], ], ], 'ResourceTagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'ResourceTagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceTagKey', ], 'max' => 200, 'min' => 0, ], 'ResourceTagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceTag', ], 'max' => 200, 'min' => 0, ], 'ResourceTagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'SensitiveBasicStringWithoutSpace' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '\\S+', 'sensitive' => true, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'BasicString', ], ], 'exception' => true, ], 'StringWithoutNewLine' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '.*', ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'ResourceTags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'TagrisArn', ], 'ResourceTags' => [ 'shape' => 'ResourceTagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagrisArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[-a-z0-9]*:(invoicing)::[0-9]{12}:[-a-zA-Z0-9/:_]+', ], 'TaxInheritanceDisabledFlag' => [ 'type' => 'boolean', ], 'TaxesBreakdown' => [ 'type' => 'structure', 'members' => [ 'Breakdown' => [ 'shape' => 'TaxesBreakdownAmountList', ], 'TotalAmount' => [ 'shape' => 'BasicString', ], ], ], 'TaxesBreakdownAmount' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'BasicString', ], 'Amount' => [ 'shape' => 'BasicString', ], 'Rate' => [ 'shape' => 'BasicString', ], ], ], 'TaxesBreakdownAmountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaxesBreakdownAmount', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'BasicString', ], ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'ResourceTagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'TagrisArn', ], 'ResourceTagKeys' => [ 'shape' => 'ResourceTagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateInvoiceUnitRequest' => [ 'type' => 'structure', 'required' => [ 'InvoiceUnitArn', ], 'members' => [ 'InvoiceUnitArn' => [ 'shape' => 'InvoiceUnitArnString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'TaxInheritanceDisabled' => [ 'shape' => 'TaxInheritanceDisabledFlag', 'box' => true, ], 'Rule' => [ 'shape' => 'InvoiceUnitRule', ], ], ], 'UpdateInvoiceUnitResponse' => [ 'type' => 'structure', 'members' => [ 'InvoiceUnitArn' => [ 'shape' => 'InvoiceUnitArnString', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'BasicString', ], 'resourceName' => [ 'shape' => 'InvoiceUnitArnString', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'name', 'message', ], 'members' => [ 'name' => [ 'shape' => 'BasicString', ], 'message' => [ 'shape' => 'BasicString', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'nonMemberPresent', 'maxAccountsExceeded', 'maxInvoiceUnitsExceeded', 'duplicateInvoiceUnit', 'mutualExclusionError', 'accountMembershipError', 'taxSettingsError', 'expiredNextToken', 'invalidNextToken', 'invalidInput', 'fieldValidationFailed', 'cannotParse', 'unknownOperation', 'other', ], ], 'Year' => [ 'type' => 'integer', 'box' => true, 'max' => 2050, 'min' => 2005, ], ],];
