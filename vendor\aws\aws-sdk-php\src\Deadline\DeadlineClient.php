<?php
namespace Aws\Deadline;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWSDeadlineCloud** service.
 * @method \Aws\Result associateMemberToFarm(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateMemberToFarmAsync(array $args = [])
 * @method \Aws\Result associateMemberToFleet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateMemberToFleetAsync(array $args = [])
 * @method \Aws\Result associateMemberToJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateMemberToJobAsync(array $args = [])
 * @method \Aws\Result associateMemberToQueue(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateMemberToQueueAsync(array $args = [])
 * @method \Aws\Result assumeFleetRoleForRead(array $args = [])
 * @method \GuzzleHttp\Promise\Promise assumeFleetRoleForReadAsync(array $args = [])
 * @method \Aws\Result assumeFleetRoleForWorker(array $args = [])
 * @method \GuzzleHttp\Promise\Promise assumeFleetRoleForWorkerAsync(array $args = [])
 * @method \Aws\Result assumeQueueRoleForRead(array $args = [])
 * @method \GuzzleHttp\Promise\Promise assumeQueueRoleForReadAsync(array $args = [])
 * @method \Aws\Result assumeQueueRoleForUser(array $args = [])
 * @method \GuzzleHttp\Promise\Promise assumeQueueRoleForUserAsync(array $args = [])
 * @method \Aws\Result assumeQueueRoleForWorker(array $args = [])
 * @method \GuzzleHttp\Promise\Promise assumeQueueRoleForWorkerAsync(array $args = [])
 * @method \Aws\Result batchGetJobEntity(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchGetJobEntityAsync(array $args = [])
 * @method \Aws\Result copyJobTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise copyJobTemplateAsync(array $args = [])
 * @method \Aws\Result createBudget(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createBudgetAsync(array $args = [])
 * @method \Aws\Result createFarm(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createFarmAsync(array $args = [])
 * @method \Aws\Result createFleet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createFleetAsync(array $args = [])
 * @method \Aws\Result createJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createJobAsync(array $args = [])
 * @method \Aws\Result createLicenseEndpoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createLicenseEndpointAsync(array $args = [])
 * @method \Aws\Result createLimit(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createLimitAsync(array $args = [])
 * @method \Aws\Result createMonitor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createMonitorAsync(array $args = [])
 * @method \Aws\Result createQueue(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createQueueAsync(array $args = [])
 * @method \Aws\Result createQueueEnvironment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createQueueEnvironmentAsync(array $args = [])
 * @method \Aws\Result createQueueFleetAssociation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createQueueFleetAssociationAsync(array $args = [])
 * @method \Aws\Result createQueueLimitAssociation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createQueueLimitAssociationAsync(array $args = [])
 * @method \Aws\Result createStorageProfile(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createStorageProfileAsync(array $args = [])
 * @method \Aws\Result createWorker(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createWorkerAsync(array $args = [])
 * @method \Aws\Result deleteBudget(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteBudgetAsync(array $args = [])
 * @method \Aws\Result deleteFarm(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteFarmAsync(array $args = [])
 * @method \Aws\Result deleteFleet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteFleetAsync(array $args = [])
 * @method \Aws\Result deleteLicenseEndpoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteLicenseEndpointAsync(array $args = [])
 * @method \Aws\Result deleteLimit(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteLimitAsync(array $args = [])
 * @method \Aws\Result deleteMeteredProduct(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteMeteredProductAsync(array $args = [])
 * @method \Aws\Result deleteMonitor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteMonitorAsync(array $args = [])
 * @method \Aws\Result deleteQueue(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteQueueAsync(array $args = [])
 * @method \Aws\Result deleteQueueEnvironment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteQueueEnvironmentAsync(array $args = [])
 * @method \Aws\Result deleteQueueFleetAssociation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteQueueFleetAssociationAsync(array $args = [])
 * @method \Aws\Result deleteQueueLimitAssociation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteQueueLimitAssociationAsync(array $args = [])
 * @method \Aws\Result deleteStorageProfile(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteStorageProfileAsync(array $args = [])
 * @method \Aws\Result deleteWorker(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteWorkerAsync(array $args = [])
 * @method \Aws\Result disassociateMemberFromFarm(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateMemberFromFarmAsync(array $args = [])
 * @method \Aws\Result disassociateMemberFromFleet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateMemberFromFleetAsync(array $args = [])
 * @method \Aws\Result disassociateMemberFromJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateMemberFromJobAsync(array $args = [])
 * @method \Aws\Result disassociateMemberFromQueue(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateMemberFromQueueAsync(array $args = [])
 * @method \Aws\Result getBudget(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getBudgetAsync(array $args = [])
 * @method \Aws\Result getFarm(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getFarmAsync(array $args = [])
 * @method \Aws\Result getFleet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getFleetAsync(array $args = [])
 * @method \Aws\Result getJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getJobAsync(array $args = [])
 * @method \Aws\Result getLicenseEndpoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getLicenseEndpointAsync(array $args = [])
 * @method \Aws\Result getLimit(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getLimitAsync(array $args = [])
 * @method \Aws\Result getMonitor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getMonitorAsync(array $args = [])
 * @method \Aws\Result getQueue(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getQueueAsync(array $args = [])
 * @method \Aws\Result getQueueEnvironment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getQueueEnvironmentAsync(array $args = [])
 * @method \Aws\Result getQueueFleetAssociation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getQueueFleetAssociationAsync(array $args = [])
 * @method \Aws\Result getQueueLimitAssociation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getQueueLimitAssociationAsync(array $args = [])
 * @method \Aws\Result getSession(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSessionAsync(array $args = [])
 * @method \Aws\Result getSessionAction(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSessionActionAsync(array $args = [])
 * @method \Aws\Result getSessionsStatisticsAggregation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSessionsStatisticsAggregationAsync(array $args = [])
 * @method \Aws\Result getStep(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getStepAsync(array $args = [])
 * @method \Aws\Result getStorageProfile(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getStorageProfileAsync(array $args = [])
 * @method \Aws\Result getStorageProfileForQueue(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getStorageProfileForQueueAsync(array $args = [])
 * @method \Aws\Result getTask(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTaskAsync(array $args = [])
 * @method \Aws\Result getWorker(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getWorkerAsync(array $args = [])
 * @method \Aws\Result listAvailableMeteredProducts(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAvailableMeteredProductsAsync(array $args = [])
 * @method \Aws\Result listBudgets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBudgetsAsync(array $args = [])
 * @method \Aws\Result listFarmMembers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listFarmMembersAsync(array $args = [])
 * @method \Aws\Result listFarms(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listFarmsAsync(array $args = [])
 * @method \Aws\Result listFleetMembers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listFleetMembersAsync(array $args = [])
 * @method \Aws\Result listFleets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listFleetsAsync(array $args = [])
 * @method \Aws\Result listJobMembers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listJobMembersAsync(array $args = [])
 * @method \Aws\Result listJobParameterDefinitions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listJobParameterDefinitionsAsync(array $args = [])
 * @method \Aws\Result listJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listJobsAsync(array $args = [])
 * @method \Aws\Result listLicenseEndpoints(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listLicenseEndpointsAsync(array $args = [])
 * @method \Aws\Result listLimits(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listLimitsAsync(array $args = [])
 * @method \Aws\Result listMeteredProducts(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMeteredProductsAsync(array $args = [])
 * @method \Aws\Result listMonitors(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMonitorsAsync(array $args = [])
 * @method \Aws\Result listQueueEnvironments(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listQueueEnvironmentsAsync(array $args = [])
 * @method \Aws\Result listQueueFleetAssociations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listQueueFleetAssociationsAsync(array $args = [])
 * @method \Aws\Result listQueueLimitAssociations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listQueueLimitAssociationsAsync(array $args = [])
 * @method \Aws\Result listQueueMembers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listQueueMembersAsync(array $args = [])
 * @method \Aws\Result listQueues(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listQueuesAsync(array $args = [])
 * @method \Aws\Result listSessionActions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSessionActionsAsync(array $args = [])
 * @method \Aws\Result listSessions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSessionsAsync(array $args = [])
 * @method \Aws\Result listSessionsForWorker(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSessionsForWorkerAsync(array $args = [])
 * @method \Aws\Result listStepConsumers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listStepConsumersAsync(array $args = [])
 * @method \Aws\Result listStepDependencies(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listStepDependenciesAsync(array $args = [])
 * @method \Aws\Result listSteps(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listStepsAsync(array $args = [])
 * @method \Aws\Result listStorageProfiles(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listStorageProfilesAsync(array $args = [])
 * @method \Aws\Result listStorageProfilesForQueue(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listStorageProfilesForQueueAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result listTasks(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTasksAsync(array $args = [])
 * @method \Aws\Result listWorkers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listWorkersAsync(array $args = [])
 * @method \Aws\Result putMeteredProduct(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putMeteredProductAsync(array $args = [])
 * @method \Aws\Result searchJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise searchJobsAsync(array $args = [])
 * @method \Aws\Result searchSteps(array $args = [])
 * @method \GuzzleHttp\Promise\Promise searchStepsAsync(array $args = [])
 * @method \Aws\Result searchTasks(array $args = [])
 * @method \GuzzleHttp\Promise\Promise searchTasksAsync(array $args = [])
 * @method \Aws\Result searchWorkers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise searchWorkersAsync(array $args = [])
 * @method \Aws\Result startSessionsStatisticsAggregation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startSessionsStatisticsAggregationAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateBudget(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateBudgetAsync(array $args = [])
 * @method \Aws\Result updateFarm(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateFarmAsync(array $args = [])
 * @method \Aws\Result updateFleet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateFleetAsync(array $args = [])
 * @method \Aws\Result updateJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateJobAsync(array $args = [])
 * @method \Aws\Result updateLimit(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateLimitAsync(array $args = [])
 * @method \Aws\Result updateMonitor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateMonitorAsync(array $args = [])
 * @method \Aws\Result updateQueue(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateQueueAsync(array $args = [])
 * @method \Aws\Result updateQueueEnvironment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateQueueEnvironmentAsync(array $args = [])
 * @method \Aws\Result updateQueueFleetAssociation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateQueueFleetAssociationAsync(array $args = [])
 * @method \Aws\Result updateQueueLimitAssociation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateQueueLimitAssociationAsync(array $args = [])
 * @method \Aws\Result updateSession(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateSessionAsync(array $args = [])
 * @method \Aws\Result updateStep(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateStepAsync(array $args = [])
 * @method \Aws\Result updateStorageProfile(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateStorageProfileAsync(array $args = [])
 * @method \Aws\Result updateTask(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateTaskAsync(array $args = [])
 * @method \Aws\Result updateWorker(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateWorkerAsync(array $args = [])
 * @method \Aws\Result updateWorkerSchedule(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateWorkerScheduleAsync(array $args = [])
 */
class DeadlineClient extends AwsClient {}
