<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * إنشاء جدول الملفات المرفقة
 * 
 * جدول متعدد الاستخدامات لحفظ الملفات المرفقة بالمشاريع والمهام
 * مع معلومات تفصيلية عن كل ملف
 * 
 * @package Database\Migrations
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
return new class extends Migration
{
    /**
     * تشغيل الـ Migration
     * 
     * @return void
     */
    public function up(): void
    {
        Schema::create('project_files', function (Blueprint $table) {
            // المعرف الأساسي
            $table->id()->comment('معرف الملف الفريد');
            
            // معلومات الملف الأساسية
            $table->string('name', 255)->comment('اسم الملف الأصلي');
            $table->string('filename', 255)->comment('اسم الملف المحفوظ على الخادم');
            $table->string('path', 500)->comment('مسار الملف على الخادم');
            $table->string('disk', 50)->default('local')->comment('قرص التخزين المستخدم');
            
            // معلومات نوع الملف
            $table->string('mime_type', 100)->comment('نوع MIME للملف');
            $table->string('extension', 10)->comment('امتداد الملف');
            $table->unsignedBigInteger('size')->comment('حجم الملف بالبايت');
            
            // العلاقة متعددة الأشكال (Polymorphic)
            $table->string('fileable_type')->comment('نوع الكائن المرفق به الملف');
            $table->unsignedBigInteger('fileable_id')->comment('معرف الكائن المرفق به الملف');
            
            // معلومات المستخدم
            $table->foreignId('uploaded_by')
                  ->constrained('users')
                  ->onDelete('cascade')
                  ->comment('معرف المستخدم الذي رفع الملف');
            
            // تصنيف الملف
            $table->enum('category', [
                'document',      // مستند
                'image',         // صورة
                'video',         // فيديو
                'audio',         // صوت
                'archive',       // أرشيف مضغوط
                'code',          // ملف كود
                'design',        // ملف تصميم
                'other'          // أخرى
            ])->default('other')->comment('تصنيف الملف');
            
            // وصف الملف
            $table->text('description')->nullable()->comment('وصف الملف والغرض منه');
            
            // معلومات الأمان
            $table->boolean('is_public')->default(false)->comment('هل الملف عام أم خاص');
            $table->string('access_token', 100)->nullable()->comment('رمز الوصول للملفات الخاصة');
            
            // معلومات إضافية للصور
            $table->integer('width')->nullable()->comment('عرض الصورة بالبكسل');
            $table->integer('height')->nullable()->comment('ارتفاع الصورة بالبكسل');
            
            // معلومات النسخ المصغرة
            $table->json('thumbnails')->nullable()->comment('مسارات النسخ المصغرة للصور');
            
            // إحصائيات الاستخدام
            $table->unsignedInteger('download_count')->default(0)->comment('عدد مرات التحميل');
            $table->timestamp('last_downloaded_at')->nullable()->comment('آخر وقت تحميل');
            
            // بيانات إضافية
            $table->json('metadata')->nullable()->comment('بيانات إضافية مثل EXIF للصور');
            
            // طوابع زمنية
            $table->timestamps();
            $table->softDeletes();
            
            // الفهارس
            $table->index(['fileable_type', 'fileable_id'], 'idx_files_fileable');
            $table->index(['uploaded_by', 'created_at'], 'idx_files_uploader_date');
            $table->index(['category', 'is_public'], 'idx_files_category_public');
            $table->index(['mime_type', 'category'], 'idx_files_type_category');
            $table->index(['size'], 'idx_files_size');
            $table->index(['access_token'], 'idx_files_access_token');
            
            // فهرس نصي للبحث
            $table->fullText(['name', 'description'], 'idx_files_search');
            
            // قيود
            $table->check('size >= 0', 'chk_files_size_positive');
            $table->check('download_count >= 0', 'chk_files_download_count_positive');
            $table->check('width IS NULL OR width > 0', 'chk_files_width_positive');
            $table->check('height IS NULL OR height > 0', 'chk_files_height_positive');
        });
        
        DB::statement("ALTER TABLE project_files COMMENT = 'جدول الملفات المرفقة - ملفات متعددة الاستخدامات للمشاريع والمهام'");
    }

    /**
     * التراجع عن الـ Migration
     * 
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('project_files');
    }
};
