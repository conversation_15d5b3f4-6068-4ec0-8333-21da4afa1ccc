<?php
namespace Aws\NetworkFlowMonitor;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Network Flow Monitor** service.
 * @method \Aws\Result createMonitor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createMonitorAsync(array $args = [])
 * @method \Aws\Result createScope(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createScopeAsync(array $args = [])
 * @method \Aws\Result deleteMonitor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteMonitorAsync(array $args = [])
 * @method \Aws\Result deleteScope(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteScopeAsync(array $args = [])
 * @method \Aws\Result getMonitor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getMonitorAsync(array $args = [])
 * @method \Aws\Result getQueryResultsMonitorTopContributors(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getQueryResultsMonitorTopContributorsAsync(array $args = [])
 * @method \Aws\Result getQueryResultsWorkloadInsightsTopContributors(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getQueryResultsWorkloadInsightsTopContributorsAsync(array $args = [])
 * @method \Aws\Result getQueryResultsWorkloadInsightsTopContributorsData(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getQueryResultsWorkloadInsightsTopContributorsDataAsync(array $args = [])
 * @method \Aws\Result getQueryStatusMonitorTopContributors(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getQueryStatusMonitorTopContributorsAsync(array $args = [])
 * @method \Aws\Result getQueryStatusWorkloadInsightsTopContributors(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getQueryStatusWorkloadInsightsTopContributorsAsync(array $args = [])
 * @method \Aws\Result getQueryStatusWorkloadInsightsTopContributorsData(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getQueryStatusWorkloadInsightsTopContributorsDataAsync(array $args = [])
 * @method \Aws\Result getScope(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getScopeAsync(array $args = [])
 * @method \Aws\Result listMonitors(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMonitorsAsync(array $args = [])
 * @method \Aws\Result listScopes(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listScopesAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result startQueryMonitorTopContributors(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startQueryMonitorTopContributorsAsync(array $args = [])
 * @method \Aws\Result startQueryWorkloadInsightsTopContributors(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startQueryWorkloadInsightsTopContributorsAsync(array $args = [])
 * @method \Aws\Result startQueryWorkloadInsightsTopContributorsData(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startQueryWorkloadInsightsTopContributorsDataAsync(array $args = [])
 * @method \Aws\Result stopQueryMonitorTopContributors(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopQueryMonitorTopContributorsAsync(array $args = [])
 * @method \Aws\Result stopQueryWorkloadInsightsTopContributors(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopQueryWorkloadInsightsTopContributorsAsync(array $args = [])
 * @method \Aws\Result stopQueryWorkloadInsightsTopContributorsData(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopQueryWorkloadInsightsTopContributorsDataAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateMonitor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateMonitorAsync(array $args = [])
 * @method \Aws\Result updateScope(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateScopeAsync(array $args = [])
 */
class NetworkFlowMonitorClient extends AwsClient {}
