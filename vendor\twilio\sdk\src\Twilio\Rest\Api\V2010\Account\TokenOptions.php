<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Api\V2010\Account;

use Twilio\Options;
use Twilio\Values;

abstract class TokenOptions
{
    /**
     * @param int $ttl The duration in seconds for which the generated credentials are valid. The default value is 86400 (24 hours).
     * @return CreateTokenOptions Options builder
     */
    public static function create(
        
        int $ttl = Values::INT_NONE

    ): CreateTokenOptions
    {
        return new CreateTokenOptions(
            $ttl
        );
    }

}

class CreateTokenOptions extends Options
    {
    /**
     * @param int $ttl The duration in seconds for which the generated credentials are valid. The default value is 86400 (24 hours).
     */
    public function __construct(
        
        int $ttl = Values::INT_NONE

    ) {
        $this->options['ttl'] = $ttl;
    }

    /**
     * The duration in seconds for which the generated credentials are valid. The default value is 86400 (24 hours).
     *
     * @param int $ttl The duration in seconds for which the generated credentials are valid. The default value is 86400 (24 hours).
     * @return $this Fluent Builder
     */
    public function setTtl(int $ttl): self
    {
        $this->options['ttl'] = $ttl;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Api.V2010.CreateTokenOptions ' . $options . ']';
    }
}

