<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * إنشاء جدول متابعي المهام
 * 
 * هذا الجدول يحدد المستخدمين الذين يتابعون مهام معينة
 * ويتلقون إشعارات عند حدوث تحديثات
 * 
 * @package Database\Migrations
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
return new class extends Migration
{
    /**
     * تشغيل الـ Migration
     * 
     * @return void
     */
    public function up(): void
    {
        Schema::create('task_watchers', function (Blueprint $table) {
            // المعرف الأساسي
            $table->id()->comment('معرف المتابعة الفريد');
            
            // العلاقات الأساسية
            $table->foreignId('task_id')
                  ->constrained('tasks')
                  ->onDelete('cascade')
                  ->comment('معرف المهمة المتابعة');
                  
            $table->foreignId('user_id')
                  ->constrained('users')
                  ->onDelete('cascade')
                  ->comment('معرف المستخدم المتابع');
            
            // إعدادات الإشعارات
            $table->boolean('notify_on_status_change')->default(true)->comment('إشعار عند تغيير الحالة');
            $table->boolean('notify_on_comment')->default(true)->comment('إشعار عند إضافة تعليق');
            $table->boolean('notify_on_assignment')->default(true)->comment('إشعار عند تغيير التكليف');
            $table->boolean('notify_on_due_date')->default(true)->comment('إشعار عند اقتراب الموعد النهائي');
            $table->boolean('notify_on_completion')->default(true)->comment('إشعار عند الإنجاز');
            
            // معلومات المتابعة
            $table->enum('watch_reason', [
                'assigned',      // مكلف بالمهمة
                'created',       // منشئ المهمة
                'mentioned',     // مذكور في المهمة
                'manual',        // متابعة يدوية
                'team_member'    // عضو في فريق المشروع
            ])->default('manual')->comment('سبب المتابعة');
            
            // طوابع زمنية
            $table->timestamps();
            
            // فهرس فريد لمنع التكرار
            $table->unique(['task_id', 'user_id'], 'unique_task_watcher');
            
            // فهارس إضافية
            $table->index(['user_id', 'created_at'], 'idx_watchers_user_date');
            $table->index(['task_id', 'watch_reason'], 'idx_watchers_task_reason');
            $table->index(['user_id', 'watch_reason'], 'idx_watchers_user_reason');
        });
        
        DB::statement("ALTER TABLE task_watchers COMMENT = 'جدول متابعي المهام - المستخدمون الذين يتابعون مهام معينة'");
    }

    /**
     * التراجع عن الـ Migration
     * 
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('task_watchers');
    }
};
