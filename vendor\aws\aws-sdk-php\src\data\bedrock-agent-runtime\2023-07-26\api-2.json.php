<?php
// This file was auto-generated from sdk-root/src/data/bedrock-agent-runtime/2023-07-26/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-07-26', 'endpointPrefix' => 'bedrock-agent-runtime', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Agents for Amazon Bedrock Runtime', 'serviceId' => 'Bedrock Agent Runtime', 'signatureVersion' => 'v4', 'signingName' => 'bedrock', 'uid' => 'bedrock-agent-runtime-2023-07-26', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'CreateInvocation' => [ 'name' => 'CreateInvocation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/sessions/{sessionIdentifier}/invocations/', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateInvocationRequest', ], 'output' => [ 'shape' => 'CreateInvocationResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateSession' => [ 'name' => 'CreateSession', 'http' => [ 'method' => 'PUT', 'requestUri' => '/sessions/', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateSessionRequest', ], 'output' => [ 'shape' => 'CreateSessionResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'DeleteAgentMemory' => [ 'name' => 'DeleteAgentMemory', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/agents/{agentId}/agentAliases/{agentAliasId}/memories', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteAgentMemoryRequest', ], 'output' => [ 'shape' => 'DeleteAgentMemoryResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DependencyFailedException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'DeleteSession' => [ 'name' => 'DeleteSession', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/sessions/{sessionIdentifier}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteSessionRequest', ], 'output' => [ 'shape' => 'DeleteSessionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'EndSession' => [ 'name' => 'EndSession', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/sessions/{sessionIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'EndSessionRequest', ], 'output' => [ 'shape' => 'EndSessionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'GenerateQuery' => [ 'name' => 'GenerateQuery', 'http' => [ 'method' => 'POST', 'requestUri' => '/generateQuery', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GenerateQueryRequest', ], 'output' => [ 'shape' => 'GenerateQueryResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DependencyFailedException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'GetAgentMemory' => [ 'name' => 'GetAgentMemory', 'http' => [ 'method' => 'GET', 'requestUri' => '/agents/{agentId}/agentAliases/{agentAliasId}/memories', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAgentMemoryRequest', ], 'output' => [ 'shape' => 'GetAgentMemoryResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DependencyFailedException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'GetExecutionFlowSnapshot' => [ 'name' => 'GetExecutionFlowSnapshot', 'http' => [ 'method' => 'GET', 'requestUri' => '/flows/{flowIdentifier}/aliases/{flowAliasIdentifier}/executions/{executionIdentifier}/flowsnapshot', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetExecutionFlowSnapshotRequest', ], 'output' => [ 'shape' => 'GetExecutionFlowSnapshotResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetFlowExecution' => [ 'name' => 'GetFlowExecution', 'http' => [ 'method' => 'GET', 'requestUri' => '/flows/{flowIdentifier}/aliases/{flowAliasIdentifier}/executions/{executionIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFlowExecutionRequest', ], 'output' => [ 'shape' => 'GetFlowExecutionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetInvocationStep' => [ 'name' => 'GetInvocationStep', 'http' => [ 'method' => 'POST', 'requestUri' => '/sessions/{sessionIdentifier}/invocationSteps/{invocationStepId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetInvocationStepRequest', ], 'output' => [ 'shape' => 'GetInvocationStepResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetSession' => [ 'name' => 'GetSession', 'http' => [ 'method' => 'GET', 'requestUri' => '/sessions/{sessionIdentifier}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSessionRequest', ], 'output' => [ 'shape' => 'GetSessionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'InvokeAgent' => [ 'name' => 'InvokeAgent', 'http' => [ 'method' => 'POST', 'requestUri' => '/agents/{agentId}/agentAliases/{agentAliasId}/sessions/{sessionId}/text', 'responseCode' => 200, ], 'input' => [ 'shape' => 'InvokeAgentRequest', ], 'output' => [ 'shape' => 'InvokeAgentResponse', ], 'errors' => [ [ 'shape' => 'ModelNotReadyException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DependencyFailedException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'InvokeFlow' => [ 'name' => 'InvokeFlow', 'http' => [ 'method' => 'POST', 'requestUri' => '/flows/{flowIdentifier}/aliases/{flowAliasIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'InvokeFlowRequest', ], 'output' => [ 'shape' => 'InvokeFlowResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DependencyFailedException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'InvokeInlineAgent' => [ 'name' => 'InvokeInlineAgent', 'http' => [ 'method' => 'POST', 'requestUri' => '/agents/{sessionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'InvokeInlineAgentRequest', ], 'output' => [ 'shape' => 'InvokeInlineAgentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DependencyFailedException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'ListFlowExecutionEvents' => [ 'name' => 'ListFlowExecutionEvents', 'http' => [ 'method' => 'GET', 'requestUri' => '/flows/{flowIdentifier}/aliases/{flowAliasIdentifier}/executions/{executionIdentifier}/events', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFlowExecutionEventsRequest', ], 'output' => [ 'shape' => 'ListFlowExecutionEventsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListFlowExecutions' => [ 'name' => 'ListFlowExecutions', 'http' => [ 'method' => 'GET', 'requestUri' => '/flows/{flowIdentifier}/executions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFlowExecutionsRequest', ], 'output' => [ 'shape' => 'ListFlowExecutionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListInvocationSteps' => [ 'name' => 'ListInvocationSteps', 'http' => [ 'method' => 'POST', 'requestUri' => '/sessions/{sessionIdentifier}/invocationSteps/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListInvocationStepsRequest', ], 'output' => [ 'shape' => 'ListInvocationStepsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListInvocations' => [ 'name' => 'ListInvocations', 'http' => [ 'method' => 'POST', 'requestUri' => '/sessions/{sessionIdentifier}/invocations/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListInvocationsRequest', ], 'output' => [ 'shape' => 'ListInvocationsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListSessions' => [ 'name' => 'ListSessions', 'http' => [ 'method' => 'POST', 'requestUri' => '/sessions/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSessionsRequest', ], 'output' => [ 'shape' => 'ListSessionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'OptimizePrompt' => [ 'name' => 'OptimizePrompt', 'http' => [ 'method' => 'POST', 'requestUri' => '/optimize-prompt', 'responseCode' => 200, ], 'input' => [ 'shape' => 'OptimizePromptRequest', ], 'output' => [ 'shape' => 'OptimizePromptResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DependencyFailedException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'PutInvocationStep' => [ 'name' => 'PutInvocationStep', 'http' => [ 'method' => 'PUT', 'requestUri' => '/sessions/{sessionIdentifier}/invocationSteps/', 'responseCode' => 201, ], 'input' => [ 'shape' => 'PutInvocationStepRequest', ], 'output' => [ 'shape' => 'PutInvocationStepResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'Rerank' => [ 'name' => 'Rerank', 'http' => [ 'method' => 'POST', 'requestUri' => '/rerank', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RerankRequest', ], 'output' => [ 'shape' => 'RerankResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DependencyFailedException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'Retrieve' => [ 'name' => 'Retrieve', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/retrieve', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RetrieveRequest', ], 'output' => [ 'shape' => 'RetrieveResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DependencyFailedException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'RetrieveAndGenerate' => [ 'name' => 'RetrieveAndGenerate', 'http' => [ 'method' => 'POST', 'requestUri' => '/retrieveAndGenerate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RetrieveAndGenerateRequest', ], 'output' => [ 'shape' => 'RetrieveAndGenerateResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DependencyFailedException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'RetrieveAndGenerateStream' => [ 'name' => 'RetrieveAndGenerateStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/retrieveAndGenerateStream', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RetrieveAndGenerateStreamRequest', ], 'output' => [ 'shape' => 'RetrieveAndGenerateStreamResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DependencyFailedException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'StartFlowExecution' => [ 'name' => 'StartFlowExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/flows/{flowIdentifier}/aliases/{flowAliasIdentifier}/executions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartFlowExecutionRequest', ], 'output' => [ 'shape' => 'StartFlowExecutionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DependencyFailedException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'StopFlowExecution' => [ 'name' => 'StopFlowExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/flows/{flowIdentifier}/aliases/{flowAliasIdentifier}/executions/{executionIdentifier}/stop', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopFlowExecutionRequest', ], 'output' => [ 'shape' => 'StopFlowExecutionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DependencyFailedException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateSession' => [ 'name' => 'UpdateSession', 'http' => [ 'method' => 'PUT', 'requestUri' => '/sessions/{sessionIdentifier}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSessionRequest', ], 'output' => [ 'shape' => 'UpdateSessionResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'APISchema' => [ 'type' => 'structure', 'members' => [ 'payload' => [ 'shape' => 'Payload', ], 's3' => [ 'shape' => 'S3Identifier', ], ], 'union' => true, ], 'AWSResourceARN' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:agent/[0-9a-zA-Z]{10}$', ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'ActionGroupExecutor' => [ 'type' => 'structure', 'members' => [ 'customControl' => [ 'shape' => 'CustomControlMethod', ], 'lambda' => [ 'shape' => 'LambdaResourceArn', ], ], 'union' => true, ], 'ActionGroupInvocationInput' => [ 'type' => 'structure', 'members' => [ 'actionGroupName' => [ 'shape' => 'ActionGroupName', ], 'apiPath' => [ 'shape' => 'ApiPath', ], 'executionType' => [ 'shape' => 'ExecutionType', ], 'function' => [ 'shape' => 'Function', ], 'invocationId' => [ 'shape' => 'String', ], 'parameters' => [ 'shape' => 'Parameters', ], 'requestBody' => [ 'shape' => 'RequestBody', ], 'verb' => [ 'shape' => 'Verb', ], ], ], 'ActionGroupInvocationOutput' => [ 'type' => 'structure', 'members' => [ 'metadata' => [ 'shape' => 'Metadata', ], 'text' => [ 'shape' => 'ActionGroupOutputString', ], ], ], 'ActionGroupName' => [ 'type' => 'string', 'sensitive' => true, ], 'ActionGroupOutputString' => [ 'type' => 'string', 'sensitive' => true, ], 'ActionGroupSignature' => [ 'type' => 'string', 'enum' => [ 'AMAZON.UserInput', 'AMAZON.CodeInterpreter', 'ANTHROPIC.Computer', 'ANTHROPIC.Bash', 'ANTHROPIC.TextEditor', ], ], 'ActionGroupSignatureParams' => [ 'type' => 'map', 'key' => [ 'shape' => 'ActionGroupSignatureParamsKeyString', ], 'value' => [ 'shape' => 'ActionGroupSignatureParamsValueString', ], ], 'ActionGroupSignatureParamsKeyString' => [ 'type' => 'string', 'max' => 100, 'min' => 0, ], 'ActionGroupSignatureParamsValueString' => [ 'type' => 'string', 'max' => 100, 'min' => 0, ], 'ActionInvocationType' => [ 'type' => 'string', 'enum' => [ 'RESULT', 'USER_CONFIRMATION', 'USER_CONFIRMATION_AND_RESULT', ], ], 'AdditionalModelRequestFields' => [ 'type' => 'map', 'key' => [ 'shape' => 'AdditionalModelRequestFieldsKey', ], 'value' => [ 'shape' => 'AdditionalModelRequestFieldsValue', ], ], 'AdditionalModelRequestFieldsKey' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'AdditionalModelRequestFieldsValue' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'AgentActionGroup' => [ 'type' => 'structure', 'required' => [ 'actionGroupName', ], 'members' => [ 'actionGroupExecutor' => [ 'shape' => 'ActionGroupExecutor', ], 'actionGroupName' => [ 'shape' => 'ResourceName', ], 'apiSchema' => [ 'shape' => 'APISchema', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'functionSchema' => [ 'shape' => 'FunctionSchema', ], 'parentActionGroupSignature' => [ 'shape' => 'ActionGroupSignature', ], 'parentActionGroupSignatureParams' => [ 'shape' => 'ActionGroupSignatureParams', ], ], ], 'AgentActionGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentActionGroup', ], ], 'AgentAliasArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:agent-alias/[0-9a-zA-Z]{10}/[0-9a-zA-Z]{10}$', ], 'AgentAliasId' => [ 'type' => 'string', 'max' => 10, 'min' => 0, 'pattern' => '^[0-9a-zA-Z]+$', ], 'AgentCollaboration' => [ 'type' => 'string', 'enum' => [ 'SUPERVISOR', 'SUPERVISOR_ROUTER', 'DISABLED', ], ], 'AgentCollaboratorInputPayload' => [ 'type' => 'structure', 'members' => [ 'returnControlResults' => [ 'shape' => 'ReturnControlResults', ], 'text' => [ 'shape' => 'AgentCollaboratorPayloadString', ], 'type' => [ 'shape' => 'PayloadType', ], ], ], 'AgentCollaboratorInvocationInput' => [ 'type' => 'structure', 'members' => [ 'agentCollaboratorAliasArn' => [ 'shape' => 'AgentAliasArn', ], 'agentCollaboratorName' => [ 'shape' => 'String', ], 'input' => [ 'shape' => 'AgentCollaboratorInputPayload', ], ], ], 'AgentCollaboratorInvocationOutput' => [ 'type' => 'structure', 'members' => [ 'agentCollaboratorAliasArn' => [ 'shape' => 'AgentAliasArn', ], 'agentCollaboratorName' => [ 'shape' => 'String', ], 'metadata' => [ 'shape' => 'Metadata', ], 'output' => [ 'shape' => 'AgentCollaboratorOutputPayload', ], ], ], 'AgentCollaboratorOutputPayload' => [ 'type' => 'structure', 'members' => [ 'returnControlPayload' => [ 'shape' => 'ReturnControlPayload', ], 'text' => [ 'shape' => 'AgentCollaboratorPayloadString', ], 'type' => [ 'shape' => 'PayloadType', ], ], ], 'AgentCollaboratorPayloadString' => [ 'type' => 'string', 'sensitive' => true, ], 'AgentId' => [ 'type' => 'string', 'max' => 10, 'min' => 0, 'pattern' => '^[0-9a-zA-Z]+$', ], 'AgentVersion' => [ 'type' => 'string', 'max' => 5, 'min' => 1, 'pattern' => '^(DRAFT|[0-9]{0,4}[1-9][0-9]{0,4})$', ], 'AnalyzePromptEvent' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'event' => true, 'sensitive' => true, ], 'ApiContentMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'PropertyParameters', ], ], 'ApiInvocationInput' => [ 'type' => 'structure', 'required' => [ 'actionGroup', ], 'members' => [ 'actionGroup' => [ 'shape' => 'String', ], 'actionInvocationType' => [ 'shape' => 'ActionInvocationType', ], 'agentId' => [ 'shape' => 'String', ], 'apiPath' => [ 'shape' => 'ApiPath', ], 'collaboratorName' => [ 'shape' => 'Name', ], 'httpMethod' => [ 'shape' => 'String', ], 'parameters' => [ 'shape' => 'ApiParameters', ], 'requestBody' => [ 'shape' => 'ApiRequestBody', ], ], ], 'ApiParameter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], ], 'ApiParameters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApiParameter', ], ], 'ApiPath' => [ 'type' => 'string', 'sensitive' => true, ], 'ApiRequestBody' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'ApiContentMap', ], ], ], 'ApiResult' => [ 'type' => 'structure', 'required' => [ 'actionGroup', ], 'members' => [ 'actionGroup' => [ 'shape' => 'String', ], 'agentId' => [ 'shape' => 'String', ], 'apiPath' => [ 'shape' => 'ApiPath', ], 'confirmationState' => [ 'shape' => 'ConfirmationState', ], 'httpMethod' => [ 'shape' => 'String', ], 'httpStatusCode' => [ 'shape' => 'Integer', ], 'responseBody' => [ 'shape' => 'ResponseBody', ], 'responseState' => [ 'shape' => 'ResponseState', ], ], ], 'AttributeType' => [ 'type' => 'string', 'enum' => [ 'STRING', 'NUMBER', 'BOOLEAN', 'STRING_LIST', ], ], 'Attribution' => [ 'type' => 'structure', 'members' => [ 'citations' => [ 'shape' => 'Citations', ], ], ], 'BadGatewayException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], 'resourceName' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 502, ], 'exception' => true, 'fault' => true, ], 'BasePromptTemplate' => [ 'type' => 'string', 'max' => 100000, 'min' => 1, 'sensitive' => true, ], 'BedrockModelArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(arn:aws(-[^:]+)?:(bedrock|sagemaker):[a-z0-9-]{1,20}:([0-9]{12})?:([a-z-]+/)?)?([a-z0-9.-]{1,63}){0,2}(([:][a-z0-9-]{1,63}){0,2})?(/[a-z0-9]{1,12})?$', ], 'BedrockModelConfigurations' => [ 'type' => 'structure', 'members' => [ 'performanceConfig' => [ 'shape' => 'PerformanceConfiguration', ], ], ], 'BedrockRerankingConfiguration' => [ 'type' => 'structure', 'required' => [ 'modelConfiguration', ], 'members' => [ 'modelConfiguration' => [ 'shape' => 'BedrockRerankingModelConfiguration', ], 'numberOfResults' => [ 'shape' => 'BedrockRerankingConfigurationNumberOfResultsInteger', ], ], ], 'BedrockRerankingConfigurationNumberOfResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'BedrockRerankingModelArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}::foundation-model/(.*))?$', ], 'BedrockRerankingModelConfiguration' => [ 'type' => 'structure', 'required' => [ 'modelArn', ], 'members' => [ 'additionalModelRequestFields' => [ 'shape' => 'AdditionalModelRequestFields', ], 'modelArn' => [ 'shape' => 'BedrockModelArn', ], ], ], 'BedrockSessionContentBlock' => [ 'type' => 'structure', 'members' => [ 'image' => [ 'shape' => 'ImageBlock', ], 'text' => [ 'shape' => 'BedrockSessionContentBlockTextString', ], ], 'sensitive' => true, 'union' => true, ], 'BedrockSessionContentBlockTextString' => [ 'type' => 'string', 'min' => 1, ], 'BedrockSessionContentBlocks' => [ 'type' => 'list', 'member' => [ 'shape' => 'BedrockSessionContentBlock', ], 'min' => 1, ], 'Blob' => [ 'type' => 'blob', ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ByteContentBlob' => [ 'type' => 'blob', 'max' => 10485760, 'min' => 1, 'sensitive' => true, ], 'ByteContentDoc' => [ 'type' => 'structure', 'required' => [ 'contentType', 'data', 'identifier', ], 'members' => [ 'contentType' => [ 'shape' => 'ContentType', ], 'data' => [ 'shape' => 'ByteContentBlob', ], 'identifier' => [ 'shape' => 'Identifier', ], ], ], 'ByteContentFile' => [ 'type' => 'structure', 'required' => [ 'data', 'mediaType', ], 'members' => [ 'data' => [ 'shape' => 'ByteContentBlob', ], 'mediaType' => [ 'shape' => 'MimeType', ], ], ], 'Caller' => [ 'type' => 'structure', 'members' => [ 'agentAliasArn' => [ 'shape' => 'AgentAliasArn', ], ], 'union' => true, ], 'CallerChain' => [ 'type' => 'list', 'member' => [ 'shape' => 'Caller', ], ], 'Citation' => [ 'type' => 'structure', 'members' => [ 'generatedResponsePart' => [ 'shape' => 'GeneratedResponsePart', ], 'retrievedReferences' => [ 'shape' => 'RetrievedReferences', ], ], ], 'CitationEvent' => [ 'type' => 'structure', 'members' => [ 'citation' => [ 'shape' => 'Citation', 'deprecated' => true, 'deprecatedMessage' => 'Citation is deprecated. Please use GeneratedResponsePart and RetrievedReferences for citation event.', ], 'generatedResponsePart' => [ 'shape' => 'GeneratedResponsePart', ], 'retrievedReferences' => [ 'shape' => 'RetrievedReferences', ], ], 'event' => true, ], 'Citations' => [ 'type' => 'list', 'member' => [ 'shape' => 'Citation', ], ], 'CodeInterpreterInvocationInput' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'String', ], 'files' => [ 'shape' => 'Files', ], ], ], 'CodeInterpreterInvocationOutput' => [ 'type' => 'structure', 'members' => [ 'executionError' => [ 'shape' => 'String', ], 'executionOutput' => [ 'shape' => 'String', ], 'executionTimeout' => [ 'shape' => 'Boolean', ], 'files' => [ 'shape' => 'Files', ], 'metadata' => [ 'shape' => 'Metadata', ], ], ], 'CollaborationInstruction' => [ 'type' => 'string', 'max' => 4000, 'min' => 1, 'sensitive' => true, ], 'Collaborator' => [ 'type' => 'structure', 'required' => [ 'foundationModel', 'instruction', ], 'members' => [ 'actionGroups' => [ 'shape' => 'AgentActionGroups', ], 'agentCollaboration' => [ 'shape' => 'AgentCollaboration', ], 'agentName' => [ 'shape' => 'Name', ], 'collaboratorConfigurations' => [ 'shape' => 'CollaboratorConfigurations', ], 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'foundationModel' => [ 'shape' => 'ModelIdentifier', ], 'guardrailConfiguration' => [ 'shape' => 'GuardrailConfigurationWithArn', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'instruction' => [ 'shape' => 'Instruction', ], 'knowledgeBases' => [ 'shape' => 'KnowledgeBases', ], 'promptOverrideConfiguration' => [ 'shape' => 'PromptOverrideConfiguration', ], ], ], 'CollaboratorConfiguration' => [ 'type' => 'structure', 'required' => [ 'collaboratorInstruction', 'collaboratorName', ], 'members' => [ 'agentAliasArn' => [ 'shape' => 'AgentAliasArn', ], 'collaboratorInstruction' => [ 'shape' => 'CollaborationInstruction', ], 'collaboratorName' => [ 'shape' => 'Name', ], 'relayConversationHistory' => [ 'shape' => 'RelayConversationHistory', ], ], ], 'CollaboratorConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'CollaboratorConfiguration', ], ], 'Collaborators' => [ 'type' => 'list', 'member' => [ 'shape' => 'Collaborator', ], ], 'ConditionResultEvent' => [ 'type' => 'structure', 'required' => [ 'nodeName', 'satisfiedConditions', 'timestamp', ], 'members' => [ 'nodeName' => [ 'shape' => 'NodeName', ], 'satisfiedConditions' => [ 'shape' => 'SatisfiedConditions', ], 'timestamp' => [ 'shape' => 'DateTimestamp', ], ], 'sensitive' => true, ], 'ConfirmationState' => [ 'type' => 'string', 'enum' => [ 'CONFIRM', 'DENY', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ContentBlock' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'String', ], ], 'sensitive' => true, 'union' => true, ], 'ContentBlocks' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContentBlock', ], ], 'ContentBody' => [ 'type' => 'structure', 'members' => [ 'body' => [ 'shape' => 'String', ], 'images' => [ 'shape' => 'ImageInputs', ], ], ], 'ContentMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'Parameters', ], ], 'ContentType' => [ 'type' => 'string', 'pattern' => '[a-z]{1,20}/.{1,20}', ], 'ConversationHistory' => [ 'type' => 'structure', 'members' => [ 'messages' => [ 'shape' => 'Messages', ], ], ], 'ConversationRole' => [ 'type' => 'string', 'enum' => [ 'user', 'assistant', ], ], 'CreateInvocationRequest' => [ 'type' => 'structure', 'required' => [ 'sessionIdentifier', ], 'members' => [ 'description' => [ 'shape' => 'InvocationDescription', ], 'invocationId' => [ 'shape' => 'Uuid', ], 'sessionIdentifier' => [ 'shape' => 'SessionIdentifier', 'location' => 'uri', 'locationName' => 'sessionIdentifier', ], ], ], 'CreateInvocationResponse' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'invocationId', 'sessionId', ], 'members' => [ 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'invocationId' => [ 'shape' => 'Uuid', ], 'sessionId' => [ 'shape' => 'Uuid', ], ], ], 'CreateSessionRequest' => [ 'type' => 'structure', 'members' => [ 'encryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'sessionMetadata' => [ 'shape' => 'SessionMetadataMap', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateSessionResponse' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'sessionArn', 'sessionId', 'sessionStatus', ], 'members' => [ 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'sessionArn' => [ 'shape' => 'SessionArn', ], 'sessionId' => [ 'shape' => 'Uuid', ], 'sessionStatus' => [ 'shape' => 'SessionStatus', ], ], ], 'CreationMode' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'OVERRIDDEN', ], ], 'CustomControlMethod' => [ 'type' => 'string', 'enum' => [ 'RETURN_CONTROL', ], ], 'CustomOrchestration' => [ 'type' => 'structure', 'members' => [ 'executor' => [ 'shape' => 'OrchestrationExecutor', ], ], ], 'CustomOrchestrationTrace' => [ 'type' => 'structure', 'members' => [ 'event' => [ 'shape' => 'CustomOrchestrationTraceEvent', ], 'traceId' => [ 'shape' => 'TraceId', ], ], 'sensitive' => true, ], 'CustomOrchestrationTraceEvent' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'DateTimestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'DeleteAgentMemoryRequest' => [ 'type' => 'structure', 'required' => [ 'agentAliasId', 'agentId', ], 'members' => [ 'agentAliasId' => [ 'shape' => 'AgentAliasId', 'location' => 'uri', 'locationName' => 'agentAliasId', ], 'agentId' => [ 'shape' => 'AgentId', 'location' => 'uri', 'locationName' => 'agentId', ], 'memoryId' => [ 'shape' => 'MemoryId', 'location' => 'querystring', 'locationName' => 'memoryId', ], 'sessionId' => [ 'shape' => 'SessionId', 'location' => 'querystring', 'locationName' => 'sessionId', ], ], ], 'DeleteAgentMemoryResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSessionRequest' => [ 'type' => 'structure', 'required' => [ 'sessionIdentifier', ], 'members' => [ 'sessionIdentifier' => [ 'shape' => 'SessionIdentifier', 'location' => 'uri', 'locationName' => 'sessionIdentifier', ], ], ], 'DeleteSessionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DependencyFailedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], 'resourceName' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 424, 'senderFault' => true, ], 'exception' => true, ], 'Document' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'EndSessionRequest' => [ 'type' => 'structure', 'required' => [ 'sessionIdentifier', ], 'members' => [ 'sessionIdentifier' => [ 'shape' => 'SessionIdentifier', 'location' => 'uri', 'locationName' => 'sessionIdentifier', ], ], ], 'EndSessionResponse' => [ 'type' => 'structure', 'required' => [ 'sessionArn', 'sessionId', 'sessionStatus', ], 'members' => [ 'sessionArn' => [ 'shape' => 'SessionArn', ], 'sessionId' => [ 'shape' => 'Uuid', ], 'sessionStatus' => [ 'shape' => 'SessionStatus', ], ], ], 'ExecutionType' => [ 'type' => 'string', 'enum' => [ 'LAMBDA', 'RETURN_CONTROL', ], ], 'ExternalSource' => [ 'type' => 'structure', 'required' => [ 'sourceType', ], 'members' => [ 'byteContent' => [ 'shape' => 'ByteContentDoc', ], 's3Location' => [ 'shape' => 'S3ObjectDoc', ], 'sourceType' => [ 'shape' => 'ExternalSourceType', ], ], ], 'ExternalSourceType' => [ 'type' => 'string', 'enum' => [ 'S3', 'BYTE_CONTENT', ], ], 'ExternalSources' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExternalSource', ], 'max' => 1, 'min' => 1, ], 'ExternalSourcesGenerationConfiguration' => [ 'type' => 'structure', 'members' => [ 'additionalModelRequestFields' => [ 'shape' => 'AdditionalModelRequestFields', ], 'guardrailConfiguration' => [ 'shape' => 'GuardrailConfiguration', ], 'inferenceConfig' => [ 'shape' => 'InferenceConfig', ], 'performanceConfig' => [ 'shape' => 'PerformanceConfiguration', ], 'promptTemplate' => [ 'shape' => 'PromptTemplate', ], ], ], 'ExternalSourcesRetrieveAndGenerateConfiguration' => [ 'type' => 'structure', 'required' => [ 'modelArn', 'sources', ], 'members' => [ 'generationConfiguration' => [ 'shape' => 'ExternalSourcesGenerationConfiguration', ], 'modelArn' => [ 'shape' => 'BedrockModelArn', ], 'sources' => [ 'shape' => 'ExternalSources', ], ], ], 'FailureReasonString' => [ 'type' => 'string', 'sensitive' => true, ], 'FailureTrace' => [ 'type' => 'structure', 'members' => [ 'failureCode' => [ 'shape' => 'Integer', ], 'failureReason' => [ 'shape' => 'FailureReasonString', ], 'metadata' => [ 'shape' => 'Metadata', ], 'traceId' => [ 'shape' => 'TraceId', ], ], 'sensitive' => true, ], 'FieldForReranking' => [ 'type' => 'structure', 'required' => [ 'fieldName', ], 'members' => [ 'fieldName' => [ 'shape' => 'FieldForRerankingFieldNameString', ], ], ], 'FieldForRerankingFieldNameString' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, ], 'FieldsForReranking' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldForReranking', ], 'max' => 100, 'min' => 1, 'sensitive' => true, ], 'FileBody' => [ 'type' => 'blob', 'max' => 1000000, 'min' => 0, 'sensitive' => true, ], 'FilePart' => [ 'type' => 'structure', 'members' => [ 'files' => [ 'shape' => 'OutputFiles', ], ], 'event' => true, ], 'FileSource' => [ 'type' => 'structure', 'required' => [ 'sourceType', ], 'members' => [ 'byteContent' => [ 'shape' => 'ByteContentFile', ], 's3Location' => [ 'shape' => 'S3ObjectFile', ], 'sourceType' => [ 'shape' => 'FileSourceType', ], ], ], 'FileSourceType' => [ 'type' => 'string', 'enum' => [ 'S3', 'BYTE_CONTENT', ], ], 'FileUseCase' => [ 'type' => 'string', 'enum' => [ 'CODE_INTERPRETER', 'CHAT', ], ], 'Files' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'FilterAttribute' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'FilterKey', ], 'value' => [ 'shape' => 'FilterValue', ], ], ], 'FilterKey' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'FilterValue' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'FinalResponse' => [ 'type' => 'structure', 'members' => [ 'metadata' => [ 'shape' => 'Metadata', ], 'text' => [ 'shape' => 'FinalResponseString', ], ], ], 'FinalResponseString' => [ 'type' => 'string', 'sensitive' => true, ], 'Float' => [ 'type' => 'float', 'box' => true, ], 'FlowAliasIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^(arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:flow/[0-9a-zA-Z]{10}/alias/[0-9a-zA-Z]{10})|(\\bTSTALIASID\\b|[0-9a-zA-Z]+)$', ], 'FlowCompletionEvent' => [ 'type' => 'structure', 'required' => [ 'completionReason', ], 'members' => [ 'completionReason' => [ 'shape' => 'FlowCompletionReason', ], ], 'event' => true, 'sensitive' => true, ], 'FlowCompletionReason' => [ 'type' => 'string', 'enum' => [ 'SUCCESS', 'INPUT_REQUIRED', ], ], 'FlowErrorCode' => [ 'type' => 'string', 'enum' => [ 'VALIDATION', 'INTERNAL_SERVER', 'NODE_EXECUTION_FAILED', ], ], 'FlowExecutionContent' => [ 'type' => 'structure', 'members' => [ 'document' => [ 'shape' => 'Document', ], ], 'sensitive' => true, 'union' => true, ], 'FlowExecutionError' => [ 'type' => 'structure', 'members' => [ 'error' => [ 'shape' => 'FlowExecutionErrorType', ], 'message' => [ 'shape' => 'String', ], 'nodeName' => [ 'shape' => 'NodeName', ], ], ], 'FlowExecutionErrorType' => [ 'type' => 'string', 'enum' => [ 'ExecutionTimedOut', ], ], 'FlowExecutionErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowExecutionError', ], ], 'FlowExecutionEvent' => [ 'type' => 'structure', 'members' => [ 'conditionResultEvent' => [ 'shape' => 'ConditionResultEvent', ], 'flowFailureEvent' => [ 'shape' => 'FlowFailureEvent', ], 'flowInputEvent' => [ 'shape' => 'FlowExecutionInputEvent', ], 'flowOutputEvent' => [ 'shape' => 'FlowExecutionOutputEvent', ], 'nodeFailureEvent' => [ 'shape' => 'NodeFailureEvent', ], 'nodeInputEvent' => [ 'shape' => 'NodeInputEvent', ], 'nodeOutputEvent' => [ 'shape' => 'NodeOutputEvent', ], ], 'union' => true, ], 'FlowExecutionEventType' => [ 'type' => 'string', 'enum' => [ 'Node', 'Flow', ], ], 'FlowExecutionEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowExecutionEvent', ], 'max' => 10, 'min' => 0, ], 'FlowExecutionId' => [ 'type' => 'string', 'max' => 100, 'min' => 2, 'pattern' => '^[0-9a-zA-Z._:-]+$', ], 'FlowExecutionIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^[a-zA-Z0-9-]{1,36}$|^(arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:flow/[0-9a-zA-Z]{10}/alias/[0-9a-zA-Z]{10}/execution/[a-zA-Z0-9-]{1,36})$', ], 'FlowExecutionInputEvent' => [ 'type' => 'structure', 'required' => [ 'fields', 'nodeName', 'timestamp', ], 'members' => [ 'fields' => [ 'shape' => 'FlowInputFields', ], 'nodeName' => [ 'shape' => 'NodeName', ], 'timestamp' => [ 'shape' => 'DateTimestamp', ], ], 'sensitive' => true, ], 'FlowExecutionName' => [ 'type' => 'string', 'max' => 36, 'min' => 0, 'pattern' => '^[a-zA-Z0-9-]{1,36}$', ], 'FlowExecutionOutputEvent' => [ 'type' => 'structure', 'required' => [ 'fields', 'nodeName', 'timestamp', ], 'members' => [ 'fields' => [ 'shape' => 'FlowOutputFields', ], 'nodeName' => [ 'shape' => 'NodeName', ], 'timestamp' => [ 'shape' => 'DateTimestamp', ], ], 'sensitive' => true, ], 'FlowExecutionRoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^arn:aws(-[^:]+)?:iam::([0-9]{12})?:role/(service-role/)?.+$', ], 'FlowExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'Running', 'Succeeded', 'Failed', 'TimedOut', 'Aborted', ], ], 'FlowExecutionSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowExecutionSummary', ], 'max' => 10, 'min' => 0, ], 'FlowExecutionSummary' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'executionArn', 'flowAliasIdentifier', 'flowIdentifier', 'flowVersion', 'status', ], 'members' => [ 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'endedAt' => [ 'shape' => 'DateTimestamp', ], 'executionArn' => [ 'shape' => 'FlowExecutionIdentifier', ], 'flowAliasIdentifier' => [ 'shape' => 'FlowAliasIdentifier', ], 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', ], 'flowVersion' => [ 'shape' => 'Version', ], 'status' => [ 'shape' => 'FlowExecutionStatus', ], ], ], 'FlowFailureEvent' => [ 'type' => 'structure', 'required' => [ 'errorCode', 'errorMessage', 'timestamp', ], 'members' => [ 'errorCode' => [ 'shape' => 'FlowErrorCode', ], 'errorMessage' => [ 'shape' => 'String', ], 'timestamp' => [ 'shape' => 'DateTimestamp', ], ], 'sensitive' => true, ], 'FlowIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^(arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:flow/[0-9a-zA-Z]{10})|([0-9a-zA-Z]{10})$', ], 'FlowInput' => [ 'type' => 'structure', 'required' => [ 'content', 'nodeName', ], 'members' => [ 'content' => [ 'shape' => 'FlowInputContent', ], 'nodeInputName' => [ 'shape' => 'NodeInputName', ], 'nodeName' => [ 'shape' => 'NodeName', ], 'nodeOutputName' => [ 'shape' => 'NodeOutputName', ], ], ], 'FlowInputContent' => [ 'type' => 'structure', 'members' => [ 'document' => [ 'shape' => 'Document', ], ], 'sensitive' => true, 'union' => true, ], 'FlowInputField' => [ 'type' => 'structure', 'required' => [ 'content', 'name', ], 'members' => [ 'content' => [ 'shape' => 'FlowExecutionContent', ], 'name' => [ 'shape' => 'NodeInputName', ], ], 'sensitive' => true, ], 'FlowInputFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowInputField', ], 'max' => 5, 'min' => 1, ], 'FlowInputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowInput', ], 'max' => 1, 'min' => 1, ], 'FlowMultiTurnInputContent' => [ 'type' => 'structure', 'members' => [ 'document' => [ 'shape' => 'Document', ], ], 'union' => true, ], 'FlowMultiTurnInputRequestEvent' => [ 'type' => 'structure', 'required' => [ 'content', 'nodeName', 'nodeType', ], 'members' => [ 'content' => [ 'shape' => 'FlowMultiTurnInputContent', ], 'nodeName' => [ 'shape' => 'NodeName', ], 'nodeType' => [ 'shape' => 'NodeType', ], ], 'event' => true, 'sensitive' => true, ], 'FlowOutputContent' => [ 'type' => 'structure', 'members' => [ 'document' => [ 'shape' => 'Document', ], ], 'union' => true, ], 'FlowOutputEvent' => [ 'type' => 'structure', 'required' => [ 'content', 'nodeName', 'nodeType', ], 'members' => [ 'content' => [ 'shape' => 'FlowOutputContent', ], 'nodeName' => [ 'shape' => 'NodeName', ], 'nodeType' => [ 'shape' => 'NodeType', ], ], 'event' => true, 'sensitive' => true, ], 'FlowOutputField' => [ 'type' => 'structure', 'required' => [ 'content', 'name', ], 'members' => [ 'content' => [ 'shape' => 'FlowExecutionContent', ], 'name' => [ 'shape' => 'NodeOutputName', ], ], 'sensitive' => true, ], 'FlowOutputFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowOutputField', ], 'max' => 5, 'min' => 1, ], 'FlowResponseStream' => [ 'type' => 'structure', 'members' => [ 'accessDeniedException' => [ 'shape' => 'AccessDeniedException', ], 'badGatewayException' => [ 'shape' => 'BadGatewayException', ], 'conflictException' => [ 'shape' => 'ConflictException', ], 'dependencyFailedException' => [ 'shape' => 'DependencyFailedException', ], 'flowCompletionEvent' => [ 'shape' => 'FlowCompletionEvent', ], 'flowMultiTurnInputRequestEvent' => [ 'shape' => 'FlowMultiTurnInputRequestEvent', ], 'flowOutputEvent' => [ 'shape' => 'FlowOutputEvent', ], 'flowTraceEvent' => [ 'shape' => 'FlowTraceEvent', ], 'internalServerException' => [ 'shape' => 'InternalServerException', ], 'resourceNotFoundException' => [ 'shape' => 'ResourceNotFoundException', ], 'serviceQuotaExceededException' => [ 'shape' => 'ServiceQuotaExceededException', ], 'throttlingException' => [ 'shape' => 'ThrottlingException', ], 'validationException' => [ 'shape' => 'ValidationException', ], ], 'eventstream' => true, ], 'FlowTrace' => [ 'type' => 'structure', 'members' => [ 'conditionNodeResultTrace' => [ 'shape' => 'FlowTraceConditionNodeResultEvent', ], 'nodeActionTrace' => [ 'shape' => 'FlowTraceNodeActionEvent', ], 'nodeInputTrace' => [ 'shape' => 'FlowTraceNodeInputEvent', ], 'nodeOutputTrace' => [ 'shape' => 'FlowTraceNodeOutputEvent', ], ], 'sensitive' => true, 'union' => true, ], 'FlowTraceCondition' => [ 'type' => 'structure', 'required' => [ 'conditionName', ], 'members' => [ 'conditionName' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'FlowTraceConditionNodeResultEvent' => [ 'type' => 'structure', 'required' => [ 'nodeName', 'satisfiedConditions', 'timestamp', ], 'members' => [ 'nodeName' => [ 'shape' => 'NodeName', ], 'satisfiedConditions' => [ 'shape' => 'FlowTraceConditions', ], 'timestamp' => [ 'shape' => 'DateTimestamp', ], ], 'sensitive' => true, ], 'FlowTraceConditions' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowTraceCondition', ], 'max' => 5, 'min' => 1, ], 'FlowTraceEvent' => [ 'type' => 'structure', 'required' => [ 'trace', ], 'members' => [ 'trace' => [ 'shape' => 'FlowTrace', ], ], 'event' => true, ], 'FlowTraceNodeActionEvent' => [ 'type' => 'structure', 'required' => [ 'nodeName', 'operationName', 'requestId', 'serviceName', 'timestamp', ], 'members' => [ 'nodeName' => [ 'shape' => 'NodeName', ], 'operationName' => [ 'shape' => 'String', ], 'requestId' => [ 'shape' => 'String', ], 'serviceName' => [ 'shape' => 'String', ], 'timestamp' => [ 'shape' => 'DateTimestamp', ], ], 'sensitive' => true, ], 'FlowTraceNodeInputContent' => [ 'type' => 'structure', 'members' => [ 'document' => [ 'shape' => 'Document', ], ], 'sensitive' => true, 'union' => true, ], 'FlowTraceNodeInputEvent' => [ 'type' => 'structure', 'required' => [ 'fields', 'nodeName', 'timestamp', ], 'members' => [ 'fields' => [ 'shape' => 'FlowTraceNodeInputFields', ], 'nodeName' => [ 'shape' => 'NodeName', ], 'timestamp' => [ 'shape' => 'DateTimestamp', ], ], 'sensitive' => true, ], 'FlowTraceNodeInputField' => [ 'type' => 'structure', 'required' => [ 'content', 'nodeInputName', ], 'members' => [ 'content' => [ 'shape' => 'FlowTraceNodeInputContent', ], 'nodeInputName' => [ 'shape' => 'NodeInputName', ], ], 'sensitive' => true, ], 'FlowTraceNodeInputFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowTraceNodeInputField', ], 'max' => 5, 'min' => 1, ], 'FlowTraceNodeOutputContent' => [ 'type' => 'structure', 'members' => [ 'document' => [ 'shape' => 'Document', ], ], 'union' => true, ], 'FlowTraceNodeOutputEvent' => [ 'type' => 'structure', 'required' => [ 'fields', 'nodeName', 'timestamp', ], 'members' => [ 'fields' => [ 'shape' => 'FlowTraceNodeOutputFields', ], 'nodeName' => [ 'shape' => 'NodeName', ], 'timestamp' => [ 'shape' => 'DateTimestamp', ], ], 'sensitive' => true, ], 'FlowTraceNodeOutputField' => [ 'type' => 'structure', 'required' => [ 'content', 'nodeOutputName', ], 'members' => [ 'content' => [ 'shape' => 'FlowTraceNodeOutputContent', ], 'nodeOutputName' => [ 'shape' => 'NodeOutputName', ], ], 'sensitive' => true, ], 'FlowTraceNodeOutputFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowTraceNodeOutputField', ], 'max' => 2, 'min' => 1, ], 'Function' => [ 'type' => 'string', 'sensitive' => true, ], 'FunctionDefinition' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'description' => [ 'shape' => 'FunctionDescription', ], 'name' => [ 'shape' => 'ResourceName', ], 'parameters' => [ 'shape' => 'ParameterMap', ], 'requireConfirmation' => [ 'shape' => 'RequireConfirmation', ], ], ], 'FunctionDescription' => [ 'type' => 'string', 'max' => 1200, 'min' => 1, ], 'FunctionInvocationInput' => [ 'type' => 'structure', 'required' => [ 'actionGroup', ], 'members' => [ 'actionGroup' => [ 'shape' => 'String', ], 'actionInvocationType' => [ 'shape' => 'ActionInvocationType', ], 'agentId' => [ 'shape' => 'String', ], 'collaboratorName' => [ 'shape' => 'Name', ], 'function' => [ 'shape' => 'String', ], 'parameters' => [ 'shape' => 'FunctionParameters', ], ], ], 'FunctionParameter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], ], 'FunctionParameters' => [ 'type' => 'list', 'member' => [ 'shape' => 'FunctionParameter', ], ], 'FunctionResult' => [ 'type' => 'structure', 'required' => [ 'actionGroup', ], 'members' => [ 'actionGroup' => [ 'shape' => 'String', ], 'agentId' => [ 'shape' => 'String', ], 'confirmationState' => [ 'shape' => 'ConfirmationState', ], 'function' => [ 'shape' => 'String', ], 'responseBody' => [ 'shape' => 'ResponseBody', ], 'responseState' => [ 'shape' => 'ResponseState', ], ], ], 'FunctionSchema' => [ 'type' => 'structure', 'members' => [ 'functions' => [ 'shape' => 'Functions', ], ], 'union' => true, ], 'Functions' => [ 'type' => 'list', 'member' => [ 'shape' => 'FunctionDefinition', ], ], 'GenerateQueryRequest' => [ 'type' => 'structure', 'required' => [ 'queryGenerationInput', 'transformationConfiguration', ], 'members' => [ 'queryGenerationInput' => [ 'shape' => 'QueryGenerationInput', ], 'transformationConfiguration' => [ 'shape' => 'TransformationConfiguration', ], ], ], 'GenerateQueryResponse' => [ 'type' => 'structure', 'members' => [ 'queries' => [ 'shape' => 'GeneratedQueries', ], ], ], 'GeneratedQueries' => [ 'type' => 'list', 'member' => [ 'shape' => 'GeneratedQuery', ], 'min' => 0, ], 'GeneratedQuery' => [ 'type' => 'structure', 'members' => [ 'sql' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'GeneratedQueryType', ], ], 'sensitive' => true, ], 'GeneratedQueryType' => [ 'type' => 'string', 'enum' => [ 'REDSHIFT_SQL', ], ], 'GeneratedResponsePart' => [ 'type' => 'structure', 'members' => [ 'textResponsePart' => [ 'shape' => 'TextResponsePart', ], ], ], 'GenerationConfiguration' => [ 'type' => 'structure', 'members' => [ 'additionalModelRequestFields' => [ 'shape' => 'AdditionalModelRequestFields', ], 'guardrailConfiguration' => [ 'shape' => 'GuardrailConfiguration', ], 'inferenceConfig' => [ 'shape' => 'InferenceConfig', ], 'performanceConfig' => [ 'shape' => 'PerformanceConfiguration', ], 'promptTemplate' => [ 'shape' => 'PromptTemplate', ], ], ], 'GetAgentMemoryRequest' => [ 'type' => 'structure', 'required' => [ 'agentAliasId', 'agentId', 'memoryId', 'memoryType', ], 'members' => [ 'agentAliasId' => [ 'shape' => 'AgentAliasId', 'location' => 'uri', 'locationName' => 'agentAliasId', ], 'agentId' => [ 'shape' => 'AgentId', 'location' => 'uri', 'locationName' => 'agentId', ], 'maxItems' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxItems', ], 'memoryId' => [ 'shape' => 'MemoryId', 'location' => 'querystring', 'locationName' => 'memoryId', ], 'memoryType' => [ 'shape' => 'MemoryType', 'location' => 'querystring', 'locationName' => 'memoryType', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetAgentMemoryResponse' => [ 'type' => 'structure', 'members' => [ 'memoryContents' => [ 'shape' => 'Memories', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetExecutionFlowSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'executionIdentifier', 'flowAliasIdentifier', 'flowIdentifier', ], 'members' => [ 'executionIdentifier' => [ 'shape' => 'FlowExecutionIdentifier', 'location' => 'uri', 'locationName' => 'executionIdentifier', ], 'flowAliasIdentifier' => [ 'shape' => 'FlowAliasIdentifier', 'location' => 'uri', 'locationName' => 'flowAliasIdentifier', ], 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', 'location' => 'uri', 'locationName' => 'flowIdentifier', ], ], ], 'GetExecutionFlowSnapshotResponse' => [ 'type' => 'structure', 'required' => [ 'definition', 'executionRoleArn', 'flowAliasIdentifier', 'flowIdentifier', 'flowVersion', ], 'members' => [ 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'definition' => [ 'shape' => 'String', ], 'executionRoleArn' => [ 'shape' => 'FlowExecutionRoleArn', ], 'flowAliasIdentifier' => [ 'shape' => 'FlowAliasIdentifier', ], 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', ], 'flowVersion' => [ 'shape' => 'Version', ], ], ], 'GetFlowExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'executionIdentifier', 'flowAliasIdentifier', 'flowIdentifier', ], 'members' => [ 'executionIdentifier' => [ 'shape' => 'FlowExecutionIdentifier', 'location' => 'uri', 'locationName' => 'executionIdentifier', ], 'flowAliasIdentifier' => [ 'shape' => 'FlowAliasIdentifier', 'location' => 'uri', 'locationName' => 'flowAliasIdentifier', ], 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', 'location' => 'uri', 'locationName' => 'flowIdentifier', ], ], ], 'GetFlowExecutionResponse' => [ 'type' => 'structure', 'required' => [ 'executionArn', 'flowAliasIdentifier', 'flowIdentifier', 'flowVersion', 'startedAt', 'status', ], 'members' => [ 'endedAt' => [ 'shape' => 'DateTimestamp', ], 'errors' => [ 'shape' => 'FlowExecutionErrors', ], 'executionArn' => [ 'shape' => 'FlowExecutionIdentifier', ], 'flowAliasIdentifier' => [ 'shape' => 'FlowAliasIdentifier', ], 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', ], 'flowVersion' => [ 'shape' => 'Version', ], 'startedAt' => [ 'shape' => 'DateTimestamp', ], 'status' => [ 'shape' => 'FlowExecutionStatus', ], ], ], 'GetInvocationStepRequest' => [ 'type' => 'structure', 'required' => [ 'invocationIdentifier', 'invocationStepId', 'sessionIdentifier', ], 'members' => [ 'invocationIdentifier' => [ 'shape' => 'InvocationIdentifier', ], 'invocationStepId' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'invocationStepId', ], 'sessionIdentifier' => [ 'shape' => 'SessionIdentifier', 'location' => 'uri', 'locationName' => 'sessionIdentifier', ], ], ], 'GetInvocationStepResponse' => [ 'type' => 'structure', 'required' => [ 'invocationStep', ], 'members' => [ 'invocationStep' => [ 'shape' => 'InvocationStep', ], ], ], 'GetSessionRequest' => [ 'type' => 'structure', 'required' => [ 'sessionIdentifier', ], 'members' => [ 'sessionIdentifier' => [ 'shape' => 'SessionIdentifier', 'location' => 'uri', 'locationName' => 'sessionIdentifier', ], ], ], 'GetSessionResponse' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'lastUpdatedAt', 'sessionArn', 'sessionId', 'sessionStatus', ], 'members' => [ 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'encryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'lastUpdatedAt' => [ 'shape' => 'DateTimestamp', ], 'sessionArn' => [ 'shape' => 'SessionArn', ], 'sessionId' => [ 'shape' => 'Uuid', ], 'sessionMetadata' => [ 'shape' => 'SessionMetadataMap', ], 'sessionStatus' => [ 'shape' => 'SessionStatus', ], ], ], 'GuadrailAction' => [ 'type' => 'string', 'enum' => [ 'INTERVENED', 'NONE', ], ], 'GuardrailAction' => [ 'type' => 'string', 'enum' => [ 'INTERVENED', 'NONE', ], ], 'GuardrailAssessment' => [ 'type' => 'structure', 'members' => [ 'contentPolicy' => [ 'shape' => 'GuardrailContentPolicyAssessment', ], 'sensitiveInformationPolicy' => [ 'shape' => 'GuardrailSensitiveInformationPolicyAssessment', ], 'topicPolicy' => [ 'shape' => 'GuardrailTopicPolicyAssessment', ], 'wordPolicy' => [ 'shape' => 'GuardrailWordPolicyAssessment', ], ], 'sensitive' => true, ], 'GuardrailAssessmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailAssessment', ], ], 'GuardrailConfiguration' => [ 'type' => 'structure', 'required' => [ 'guardrailId', 'guardrailVersion', ], 'members' => [ 'guardrailId' => [ 'shape' => 'GuardrailConfigurationGuardrailIdString', ], 'guardrailVersion' => [ 'shape' => 'GuardrailConfigurationGuardrailVersionString', ], ], ], 'GuardrailConfigurationGuardrailIdString' => [ 'type' => 'string', 'max' => 64, 'min' => 0, 'pattern' => '^[a-z0-9]+$', ], 'GuardrailConfigurationGuardrailVersionString' => [ 'type' => 'string', 'max' => 5, 'min' => 1, 'pattern' => '^(([1-9][0-9]{0,7})|(DRAFT))$', ], 'GuardrailConfigurationWithArn' => [ 'type' => 'structure', 'required' => [ 'guardrailIdentifier', 'guardrailVersion', ], 'members' => [ 'guardrailIdentifier' => [ 'shape' => 'GuardrailIdentifierWithArn', ], 'guardrailVersion' => [ 'shape' => 'GuardrailVersion', ], ], ], 'GuardrailContentFilter' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'GuardrailContentPolicyAction', ], 'confidence' => [ 'shape' => 'GuardrailContentFilterConfidence', ], 'type' => [ 'shape' => 'GuardrailContentFilterType', ], ], 'sensitive' => true, ], 'GuardrailContentFilterConfidence' => [ 'type' => 'string', 'enum' => [ 'NONE', 'LOW', 'MEDIUM', 'HIGH', ], ], 'GuardrailContentFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailContentFilter', ], 'sensitive' => true, ], 'GuardrailContentFilterType' => [ 'type' => 'string', 'enum' => [ 'INSULTS', 'HATE', 'SEXUAL', 'VIOLENCE', 'MISCONDUCT', 'PROMPT_ATTACK', ], ], 'GuardrailContentPolicyAction' => [ 'type' => 'string', 'enum' => [ 'BLOCKED', ], ], 'GuardrailContentPolicyAssessment' => [ 'type' => 'structure', 'members' => [ 'filters' => [ 'shape' => 'GuardrailContentFilterList', ], ], 'sensitive' => true, ], 'GuardrailCustomWord' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'GuardrailWordPolicyAction', ], 'match' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'GuardrailCustomWordList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailCustomWord', ], 'sensitive' => true, ], 'GuardrailEvent' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'GuadrailAction', ], ], 'event' => true, ], 'GuardrailIdentifierWithArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^(([a-z0-9]+)|(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:guardrail/[a-z0-9]+))$', ], 'GuardrailManagedWord' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'GuardrailWordPolicyAction', ], 'match' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'GuardrailManagedWordType', ], ], 'sensitive' => true, ], 'GuardrailManagedWordList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailManagedWord', ], 'sensitive' => true, ], 'GuardrailManagedWordType' => [ 'type' => 'string', 'enum' => [ 'PROFANITY', ], ], 'GuardrailPiiEntityFilter' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'GuardrailSensitiveInformationPolicyAction', ], 'match' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'GuardrailPiiEntityType', ], ], 'sensitive' => true, ], 'GuardrailPiiEntityFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailPiiEntityFilter', ], 'sensitive' => true, ], 'GuardrailPiiEntityType' => [ 'type' => 'string', 'enum' => [ 'ADDRESS', 'AGE', 'AWS_ACCESS_KEY', 'AWS_SECRET_KEY', 'CA_HEALTH_NUMBER', 'CA_SOCIAL_INSURANCE_NUMBER', 'CREDIT_DEBIT_CARD_CVV', 'CREDIT_DEBIT_CARD_EXPIRY', 'CREDIT_DEBIT_CARD_NUMBER', 'DRIVER_ID', 'EMAIL', 'INTERNATIONAL_BANK_ACCOUNT_NUMBER', 'IP_ADDRESS', 'LICENSE_PLATE', 'MAC_ADDRESS', 'NAME', 'PASSWORD', 'PHONE', 'PIN', 'SWIFT_CODE', 'UK_NATIONAL_HEALTH_SERVICE_NUMBER', 'UK_NATIONAL_INSURANCE_NUMBER', 'UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER', 'URL', 'USERNAME', 'US_BANK_ACCOUNT_NUMBER', 'US_BANK_ROUTING_NUMBER', 'US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER', 'US_PASSPORT_NUMBER', 'US_SOCIAL_SECURITY_NUMBER', 'VEHICLE_IDENTIFICATION_NUMBER', ], ], 'GuardrailRegexFilter' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'GuardrailSensitiveInformationPolicyAction', ], 'match' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], 'regex' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'GuardrailRegexFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailRegexFilter', ], 'sensitive' => true, ], 'GuardrailSensitiveInformationPolicyAction' => [ 'type' => 'string', 'enum' => [ 'BLOCKED', 'ANONYMIZED', ], ], 'GuardrailSensitiveInformationPolicyAssessment' => [ 'type' => 'structure', 'members' => [ 'piiEntities' => [ 'shape' => 'GuardrailPiiEntityFilterList', ], 'regexes' => [ 'shape' => 'GuardrailRegexFilterList', ], ], 'sensitive' => true, ], 'GuardrailTopic' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'GuardrailTopicPolicyAction', ], 'name' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'GuardrailTopicType', ], ], 'sensitive' => true, ], 'GuardrailTopicList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailTopic', ], 'sensitive' => true, ], 'GuardrailTopicPolicyAction' => [ 'type' => 'string', 'enum' => [ 'BLOCKED', ], ], 'GuardrailTopicPolicyAssessment' => [ 'type' => 'structure', 'members' => [ 'topics' => [ 'shape' => 'GuardrailTopicList', ], ], 'sensitive' => true, ], 'GuardrailTopicType' => [ 'type' => 'string', 'enum' => [ 'DENY', ], ], 'GuardrailTrace' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'GuardrailAction', ], 'inputAssessments' => [ 'shape' => 'GuardrailAssessmentList', ], 'metadata' => [ 'shape' => 'Metadata', ], 'outputAssessments' => [ 'shape' => 'GuardrailAssessmentList', ], 'traceId' => [ 'shape' => 'TraceId', ], ], 'sensitive' => true, ], 'GuardrailVersion' => [ 'type' => 'string', 'max' => 5, 'min' => 1, 'pattern' => '^(([1-9][0-9]{0,7})|(DRAFT))$', ], 'GuardrailWordPolicyAction' => [ 'type' => 'string', 'enum' => [ 'BLOCKED', ], ], 'GuardrailWordPolicyAssessment' => [ 'type' => 'structure', 'members' => [ 'customWords' => [ 'shape' => 'GuardrailCustomWordList', ], 'managedWordLists' => [ 'shape' => 'GuardrailManagedWordList', ], ], 'sensitive' => true, ], 'Identifier' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'sensitive' => true, ], 'ImageBlock' => [ 'type' => 'structure', 'required' => [ 'format', 'source', ], 'members' => [ 'format' => [ 'shape' => 'ImageFormat', ], 'source' => [ 'shape' => 'ImageSource', ], ], ], 'ImageFormat' => [ 'type' => 'string', 'enum' => [ 'png', 'jpeg', 'gif', 'webp', ], ], 'ImageInput' => [ 'type' => 'structure', 'required' => [ 'format', 'source', ], 'members' => [ 'format' => [ 'shape' => 'ImageInputFormat', ], 'source' => [ 'shape' => 'ImageInputSource', ], ], ], 'ImageInputFormat' => [ 'type' => 'string', 'enum' => [ 'png', 'jpeg', 'gif', 'webp', ], ], 'ImageInputSource' => [ 'type' => 'structure', 'members' => [ 'bytes' => [ 'shape' => 'ImageInputSourceBytesBlob', ], ], 'union' => true, ], 'ImageInputSourceBytesBlob' => [ 'type' => 'blob', 'min' => 1, ], 'ImageInputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImageInput', ], ], 'ImageSource' => [ 'type' => 'structure', 'members' => [ 'bytes' => [ 'shape' => 'ImageSourceBytesBlob', ], 's3Location' => [ 'shape' => 'S3Location', ], ], 'union' => true, ], 'ImageSourceBytesBlob' => [ 'type' => 'blob', 'min' => 1, ], 'ImplicitFilterConfiguration' => [ 'type' => 'structure', 'required' => [ 'metadataAttributes', 'modelArn', ], 'members' => [ 'metadataAttributes' => [ 'shape' => 'MetadataAttributeSchemaList', ], 'modelArn' => [ 'shape' => 'BedrockModelArn', ], ], ], 'InferenceConfig' => [ 'type' => 'structure', 'members' => [ 'textInferenceConfig' => [ 'shape' => 'TextInferenceConfig', ], ], ], 'InferenceConfiguration' => [ 'type' => 'structure', 'members' => [ 'maximumLength' => [ 'shape' => 'MaximumLength', ], 'stopSequences' => [ 'shape' => 'StopSequences', ], 'temperature' => [ 'shape' => 'Temperature', ], 'topK' => [ 'shape' => 'TopK', ], 'topP' => [ 'shape' => 'TopP', ], ], ], 'InlineAgentFilePart' => [ 'type' => 'structure', 'members' => [ 'files' => [ 'shape' => 'OutputFiles', ], ], 'event' => true, ], 'InlineAgentPayloadPart' => [ 'type' => 'structure', 'members' => [ 'attribution' => [ 'shape' => 'Attribution', ], 'bytes' => [ 'shape' => 'PartBody', ], ], 'event' => true, 'sensitive' => true, ], 'InlineAgentResponseStream' => [ 'type' => 'structure', 'members' => [ 'accessDeniedException' => [ 'shape' => 'AccessDeniedException', ], 'badGatewayException' => [ 'shape' => 'BadGatewayException', ], 'chunk' => [ 'shape' => 'InlineAgentPayloadPart', ], 'conflictException' => [ 'shape' => 'ConflictException', ], 'dependencyFailedException' => [ 'shape' => 'DependencyFailedException', ], 'files' => [ 'shape' => 'InlineAgentFilePart', ], 'internalServerException' => [ 'shape' => 'InternalServerException', ], 'resourceNotFoundException' => [ 'shape' => 'ResourceNotFoundException', ], 'returnControl' => [ 'shape' => 'InlineAgentReturnControlPayload', ], 'serviceQuotaExceededException' => [ 'shape' => 'ServiceQuotaExceededException', ], 'throttlingException' => [ 'shape' => 'ThrottlingException', ], 'trace' => [ 'shape' => 'InlineAgentTracePart', ], 'validationException' => [ 'shape' => 'ValidationException', ], ], 'eventstream' => true, ], 'InlineAgentReturnControlPayload' => [ 'type' => 'structure', 'members' => [ 'invocationId' => [ 'shape' => 'String', ], 'invocationInputs' => [ 'shape' => 'InvocationInputs', ], ], 'event' => true, 'sensitive' => true, ], 'InlineAgentTracePart' => [ 'type' => 'structure', 'members' => [ 'callerChain' => [ 'shape' => 'CallerChain', ], 'collaboratorName' => [ 'shape' => 'Name', ], 'eventTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'sessionId' => [ 'shape' => 'SessionId', ], 'trace' => [ 'shape' => 'Trace', ], ], 'event' => true, 'sensitive' => true, ], 'InlineBedrockModelConfigurations' => [ 'type' => 'structure', 'members' => [ 'performanceConfig' => [ 'shape' => 'PerformanceConfiguration', ], ], ], 'InlineSessionState' => [ 'type' => 'structure', 'members' => [ 'conversationHistory' => [ 'shape' => 'ConversationHistory', ], 'files' => [ 'shape' => 'InputFiles', ], 'invocationId' => [ 'shape' => 'String', ], 'promptSessionAttributes' => [ 'shape' => 'PromptSessionAttributesMap', ], 'returnControlInvocationResults' => [ 'shape' => 'ReturnControlInvocationResults', ], 'sessionAttributes' => [ 'shape' => 'SessionAttributesMap', ], ], ], 'InputFile' => [ 'type' => 'structure', 'required' => [ 'name', 'source', 'useCase', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'source' => [ 'shape' => 'FileSource', ], 'useCase' => [ 'shape' => 'FileUseCase', ], ], ], 'InputFiles' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputFile', ], ], 'InputPrompt' => [ 'type' => 'structure', 'members' => [ 'textPrompt' => [ 'shape' => 'TextPrompt', ], ], 'union' => true, ], 'InputQueryType' => [ 'type' => 'string', 'enum' => [ 'TEXT', ], ], 'InputText' => [ 'type' => 'string', 'max' => 25000000, 'min' => 0, 'sensitive' => true, ], 'Instruction' => [ 'type' => 'string', 'min' => 40, 'sensitive' => true, ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], 'reason' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InvocationDescription' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'InvocationIdentifier' => [ 'type' => 'string', 'pattern' => '^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', ], 'InvocationInput' => [ 'type' => 'structure', 'members' => [ 'actionGroupInvocationInput' => [ 'shape' => 'ActionGroupInvocationInput', ], 'agentCollaboratorInvocationInput' => [ 'shape' => 'AgentCollaboratorInvocationInput', ], 'codeInterpreterInvocationInput' => [ 'shape' => 'CodeInterpreterInvocationInput', ], 'invocationType' => [ 'shape' => 'InvocationType', ], 'knowledgeBaseLookupInput' => [ 'shape' => 'KnowledgeBaseLookupInput', ], 'traceId' => [ 'shape' => 'TraceId', ], ], 'sensitive' => true, ], 'InvocationInputMember' => [ 'type' => 'structure', 'members' => [ 'apiInvocationInput' => [ 'shape' => 'ApiInvocationInput', ], 'functionInvocationInput' => [ 'shape' => 'FunctionInvocationInput', ], ], 'union' => true, ], 'InvocationInputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'InvocationInputMember', ], 'max' => 5, 'min' => 1, ], 'InvocationResultMember' => [ 'type' => 'structure', 'members' => [ 'apiResult' => [ 'shape' => 'ApiResult', ], 'functionResult' => [ 'shape' => 'FunctionResult', ], ], 'union' => true, ], 'InvocationStep' => [ 'type' => 'structure', 'required' => [ 'invocationId', 'invocationStepId', 'invocationStepTime', 'payload', 'sessionId', ], 'members' => [ 'invocationId' => [ 'shape' => 'Uuid', ], 'invocationStepId' => [ 'shape' => 'Uuid', ], 'invocationStepTime' => [ 'shape' => 'DateTimestamp', ], 'payload' => [ 'shape' => 'InvocationStepPayload', ], 'sessionId' => [ 'shape' => 'Uuid', ], ], ], 'InvocationStepPayload' => [ 'type' => 'structure', 'members' => [ 'contentBlocks' => [ 'shape' => 'BedrockSessionContentBlocks', ], ], 'union' => true, ], 'InvocationStepSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'InvocationStepSummary', ], ], 'InvocationStepSummary' => [ 'type' => 'structure', 'required' => [ 'invocationId', 'invocationStepId', 'invocationStepTime', 'sessionId', ], 'members' => [ 'invocationId' => [ 'shape' => 'Uuid', ], 'invocationStepId' => [ 'shape' => 'Uuid', ], 'invocationStepTime' => [ 'shape' => 'DateTimestamp', ], 'sessionId' => [ 'shape' => 'Uuid', ], ], ], 'InvocationSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'InvocationSummary', ], ], 'InvocationSummary' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'invocationId', 'sessionId', ], 'members' => [ 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'invocationId' => [ 'shape' => 'Uuid', ], 'sessionId' => [ 'shape' => 'Uuid', ], ], ], 'InvocationType' => [ 'type' => 'string', 'enum' => [ 'ACTION_GROUP', 'KNOWLEDGE_BASE', 'FINISH', 'ACTION_GROUP_CODE_INTERPRETER', 'AGENT_COLLABORATOR', ], ], 'InvokeAgentRequest' => [ 'type' => 'structure', 'required' => [ 'agentAliasId', 'agentId', 'sessionId', ], 'members' => [ 'agentAliasId' => [ 'shape' => 'AgentAliasId', 'location' => 'uri', 'locationName' => 'agentAliasId', ], 'agentId' => [ 'shape' => 'AgentId', 'location' => 'uri', 'locationName' => 'agentId', ], 'bedrockModelConfigurations' => [ 'shape' => 'BedrockModelConfigurations', ], 'enableTrace' => [ 'shape' => 'Boolean', ], 'endSession' => [ 'shape' => 'Boolean', ], 'inputText' => [ 'shape' => 'InputText', ], 'memoryId' => [ 'shape' => 'MemoryId', ], 'promptCreationConfigurations' => [ 'shape' => 'PromptCreationConfigurations', ], 'sessionId' => [ 'shape' => 'SessionId', 'location' => 'uri', 'locationName' => 'sessionId', ], 'sessionState' => [ 'shape' => 'SessionState', ], 'sourceArn' => [ 'shape' => 'AWSResourceARN', 'location' => 'header', 'locationName' => 'x-amz-source-arn', ], 'streamingConfigurations' => [ 'shape' => 'StreamingConfigurations', ], ], ], 'InvokeAgentResponse' => [ 'type' => 'structure', 'required' => [ 'completion', 'contentType', 'sessionId', ], 'members' => [ 'completion' => [ 'shape' => 'ResponseStream', ], 'contentType' => [ 'shape' => 'MimeType', 'location' => 'header', 'locationName' => 'x-amzn-bedrock-agent-content-type', ], 'memoryId' => [ 'shape' => 'MemoryId', 'location' => 'header', 'locationName' => 'x-amz-bedrock-agent-memory-id', ], 'sessionId' => [ 'shape' => 'SessionId', 'location' => 'header', 'locationName' => 'x-amz-bedrock-agent-session-id', ], ], 'payload' => 'completion', ], 'InvokeFlowRequest' => [ 'type' => 'structure', 'required' => [ 'flowAliasIdentifier', 'flowIdentifier', 'inputs', ], 'members' => [ 'enableTrace' => [ 'shape' => 'Boolean', ], 'executionId' => [ 'shape' => 'FlowExecutionId', ], 'flowAliasIdentifier' => [ 'shape' => 'FlowAliasIdentifier', 'location' => 'uri', 'locationName' => 'flowAliasIdentifier', ], 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', 'location' => 'uri', 'locationName' => 'flowIdentifier', ], 'inputs' => [ 'shape' => 'FlowInputs', ], 'modelPerformanceConfiguration' => [ 'shape' => 'ModelPerformanceConfiguration', ], ], ], 'InvokeFlowResponse' => [ 'type' => 'structure', 'required' => [ 'responseStream', ], 'members' => [ 'executionId' => [ 'shape' => 'FlowExecutionId', 'location' => 'header', 'locationName' => 'x-amz-bedrock-flow-execution-id', ], 'responseStream' => [ 'shape' => 'FlowResponseStream', ], ], 'payload' => 'responseStream', ], 'InvokeInlineAgentRequest' => [ 'type' => 'structure', 'required' => [ 'foundationModel', 'instruction', 'sessionId', ], 'members' => [ 'actionGroups' => [ 'shape' => 'AgentActionGroups', ], 'agentCollaboration' => [ 'shape' => 'AgentCollaboration', ], 'agentName' => [ 'shape' => 'Name', ], 'bedrockModelConfigurations' => [ 'shape' => 'InlineBedrockModelConfigurations', ], 'collaboratorConfigurations' => [ 'shape' => 'CollaboratorConfigurations', ], 'collaborators' => [ 'shape' => 'Collaborators', ], 'customOrchestration' => [ 'shape' => 'CustomOrchestration', ], 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'enableTrace' => [ 'shape' => 'Boolean', ], 'endSession' => [ 'shape' => 'Boolean', ], 'foundationModel' => [ 'shape' => 'ModelIdentifier', ], 'guardrailConfiguration' => [ 'shape' => 'GuardrailConfigurationWithArn', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'inlineSessionState' => [ 'shape' => 'InlineSessionState', ], 'inputText' => [ 'shape' => 'InputText', ], 'instruction' => [ 'shape' => 'Instruction', ], 'knowledgeBases' => [ 'shape' => 'KnowledgeBases', ], 'orchestrationType' => [ 'shape' => 'OrchestrationType', ], 'promptCreationConfigurations' => [ 'shape' => 'PromptCreationConfigurations', ], 'promptOverrideConfiguration' => [ 'shape' => 'PromptOverrideConfiguration', ], 'sessionId' => [ 'shape' => 'SessionId', 'location' => 'uri', 'locationName' => 'sessionId', ], 'streamingConfigurations' => [ 'shape' => 'StreamingConfigurations', ], ], ], 'InvokeInlineAgentResponse' => [ 'type' => 'structure', 'required' => [ 'completion', 'contentType', 'sessionId', ], 'members' => [ 'completion' => [ 'shape' => 'InlineAgentResponseStream', ], 'contentType' => [ 'shape' => 'MimeType', 'location' => 'header', 'locationName' => 'x-amzn-bedrock-agent-content-type', ], 'sessionId' => [ 'shape' => 'SessionId', 'location' => 'header', 'locationName' => 'x-amz-bedrock-agent-session-id', ], ], 'payload' => 'completion', ], 'KmsKeyArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^arn:aws(|-cn|-us-gov):kms:[a-zA-Z0-9-]*:[0-9]{12}:key/[a-zA-Z0-9-]{36}$', ], 'KnowledgeBase' => [ 'type' => 'structure', 'required' => [ 'description', 'knowledgeBaseId', ], 'members' => [ 'description' => [ 'shape' => 'ResourceDescription', ], 'knowledgeBaseId' => [ 'shape' => 'KnowledgeBaseId', ], 'retrievalConfiguration' => [ 'shape' => 'KnowledgeBaseRetrievalConfiguration', ], ], ], 'KnowledgeBaseArn' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^arn:aws(|-cn|-us-gov):bedrock:[a-zA-Z0-9-]*:[0-9]{12}:knowledge-base/[0-9a-zA-Z]+$', ], 'KnowledgeBaseConfiguration' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'retrievalConfiguration', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'KnowledgeBaseId', ], 'retrievalConfiguration' => [ 'shape' => 'KnowledgeBaseRetrievalConfiguration', ], ], ], 'KnowledgeBaseConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'KnowledgeBaseConfiguration', ], 'min' => 1, ], 'KnowledgeBaseId' => [ 'type' => 'string', 'max' => 10, 'min' => 0, 'pattern' => '^[0-9a-zA-Z]+$', ], 'KnowledgeBaseLookupInput' => [ 'type' => 'structure', 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'TraceKnowledgeBaseId', ], 'text' => [ 'shape' => 'KnowledgeBaseLookupInputString', ], ], ], 'KnowledgeBaseLookupInputString' => [ 'type' => 'string', 'sensitive' => true, ], 'KnowledgeBaseLookupOutput' => [ 'type' => 'structure', 'members' => [ 'metadata' => [ 'shape' => 'Metadata', ], 'retrievedReferences' => [ 'shape' => 'RetrievedReferences', ], ], ], 'KnowledgeBaseQuery' => [ 'type' => 'structure', 'required' => [ 'text', ], 'members' => [ 'text' => [ 'shape' => 'KnowledgeBaseQueryTextString', ], ], 'sensitive' => true, ], 'KnowledgeBaseQueryTextString' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'KnowledgeBaseRetrievalConfiguration' => [ 'type' => 'structure', 'required' => [ 'vectorSearchConfiguration', ], 'members' => [ 'vectorSearchConfiguration' => [ 'shape' => 'KnowledgeBaseVectorSearchConfiguration', ], ], ], 'KnowledgeBaseRetrievalResult' => [ 'type' => 'structure', 'required' => [ 'content', ], 'members' => [ 'content' => [ 'shape' => 'RetrievalResultContent', ], 'location' => [ 'shape' => 'RetrievalResultLocation', ], 'metadata' => [ 'shape' => 'RetrievalResultMetadata', ], 'score' => [ 'shape' => 'Double', ], ], ], 'KnowledgeBaseRetrievalResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'KnowledgeBaseRetrievalResult', ], 'sensitive' => true, ], 'KnowledgeBaseRetrieveAndGenerateConfiguration' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'modelArn', ], 'members' => [ 'generationConfiguration' => [ 'shape' => 'GenerationConfiguration', ], 'knowledgeBaseId' => [ 'shape' => 'KnowledgeBaseId', ], 'modelArn' => [ 'shape' => 'BedrockModelArn', ], 'orchestrationConfiguration' => [ 'shape' => 'OrchestrationConfiguration', ], 'retrievalConfiguration' => [ 'shape' => 'KnowledgeBaseRetrievalConfiguration', ], ], ], 'KnowledgeBaseVectorSearchConfiguration' => [ 'type' => 'structure', 'members' => [ 'filter' => [ 'shape' => 'RetrievalFilter', ], 'implicitFilterConfiguration' => [ 'shape' => 'ImplicitFilterConfiguration', ], 'numberOfResults' => [ 'shape' => 'KnowledgeBaseVectorSearchConfigurationNumberOfResultsInteger', 'box' => true, ], 'overrideSearchType' => [ 'shape' => 'SearchType', ], 'rerankingConfiguration' => [ 'shape' => 'VectorSearchRerankingConfiguration', ], ], ], 'KnowledgeBaseVectorSearchConfigurationNumberOfResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'KnowledgeBases' => [ 'type' => 'list', 'member' => [ 'shape' => 'KnowledgeBase', ], ], 'LambdaArn' => [ 'type' => 'string', ], 'LambdaResourceArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^arn:(aws[a-zA-Z-]*)?:lambda:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:function:[a-zA-Z0-9-_\\.]+(:(\\$LATEST|[a-zA-Z0-9-_]+))?$', ], 'ListFlowExecutionEventsRequest' => [ 'type' => 'structure', 'required' => [ 'eventType', 'executionIdentifier', 'flowAliasIdentifier', 'flowIdentifier', ], 'members' => [ 'eventType' => [ 'shape' => 'FlowExecutionEventType', 'location' => 'querystring', 'locationName' => 'eventType', ], 'executionIdentifier' => [ 'shape' => 'FlowExecutionIdentifier', 'location' => 'uri', 'locationName' => 'executionIdentifier', ], 'flowAliasIdentifier' => [ 'shape' => 'FlowAliasIdentifier', 'location' => 'uri', 'locationName' => 'flowAliasIdentifier', ], 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', 'location' => 'uri', 'locationName' => 'flowIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListFlowExecutionEventsResponse' => [ 'type' => 'structure', 'required' => [ 'flowExecutionEvents', ], 'members' => [ 'flowExecutionEvents' => [ 'shape' => 'FlowExecutionEvents', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFlowExecutionsRequest' => [ 'type' => 'structure', 'required' => [ 'flowIdentifier', ], 'members' => [ 'flowAliasIdentifier' => [ 'shape' => 'FlowAliasIdentifier', 'location' => 'querystring', 'locationName' => 'flowAliasIdentifier', ], 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', 'location' => 'uri', 'locationName' => 'flowIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListFlowExecutionsResponse' => [ 'type' => 'structure', 'required' => [ 'flowExecutionSummaries', ], 'members' => [ 'flowExecutionSummaries' => [ 'shape' => 'FlowExecutionSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListInvocationStepsRequest' => [ 'type' => 'structure', 'required' => [ 'sessionIdentifier', ], 'members' => [ 'invocationIdentifier' => [ 'shape' => 'InvocationIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'sessionIdentifier' => [ 'shape' => 'SessionIdentifier', 'location' => 'uri', 'locationName' => 'sessionIdentifier', ], ], ], 'ListInvocationStepsResponse' => [ 'type' => 'structure', 'required' => [ 'invocationStepSummaries', ], 'members' => [ 'invocationStepSummaries' => [ 'shape' => 'InvocationStepSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListInvocationsRequest' => [ 'type' => 'structure', 'required' => [ 'sessionIdentifier', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'sessionIdentifier' => [ 'shape' => 'SessionIdentifier', 'location' => 'uri', 'locationName' => 'sessionIdentifier', ], ], ], 'ListInvocationsResponse' => [ 'type' => 'structure', 'required' => [ 'invocationSummaries', ], 'members' => [ 'invocationSummaries' => [ 'shape' => 'InvocationSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSessionsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListSessionsResponse' => [ 'type' => 'structure', 'required' => [ 'sessionSummaries', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'sessionSummaries' => [ 'shape' => 'SessionSummaries', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TaggableResourcesArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'MaxTokens' => [ 'type' => 'integer', 'box' => true, 'max' => 65536, 'min' => 0, ], 'MaximumLength' => [ 'type' => 'integer', 'box' => true, 'max' => 8192, 'min' => 0, ], 'Memories' => [ 'type' => 'list', 'member' => [ 'shape' => 'Memory', ], ], 'Memory' => [ 'type' => 'structure', 'members' => [ 'sessionSummary' => [ 'shape' => 'MemorySessionSummary', ], ], 'union' => true, ], 'MemoryId' => [ 'type' => 'string', 'max' => 100, 'min' => 2, 'pattern' => '^[0-9a-zA-Z._:-]+$', ], 'MemorySessionSummary' => [ 'type' => 'structure', 'members' => [ 'memoryId' => [ 'shape' => 'MemoryId', ], 'sessionExpiryTime' => [ 'shape' => 'DateTimestamp', ], 'sessionId' => [ 'shape' => 'SessionId', ], 'sessionStartTime' => [ 'shape' => 'DateTimestamp', ], 'summaryText' => [ 'shape' => 'SummaryText', ], ], ], 'MemoryType' => [ 'type' => 'string', 'enum' => [ 'SESSION_SUMMARY', ], ], 'Message' => [ 'type' => 'structure', 'required' => [ 'content', 'role', ], 'members' => [ 'content' => [ 'shape' => 'ContentBlocks', ], 'role' => [ 'shape' => 'ConversationRole', ], ], ], 'Messages' => [ 'type' => 'list', 'member' => [ 'shape' => 'Message', ], ], 'Metadata' => [ 'type' => 'structure', 'members' => [ 'clientRequestId' => [ 'shape' => 'String', ], 'endTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'operationTotalTimeMs' => [ 'shape' => 'Long', ], 'startTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'totalTimeMs' => [ 'shape' => 'Long', ], 'usage' => [ 'shape' => 'Usage', ], ], 'sensitive' => true, ], 'MetadataAttributeSchema' => [ 'type' => 'structure', 'required' => [ 'description', 'key', 'type', ], 'members' => [ 'description' => [ 'shape' => 'MetadataAttributeSchemaDescriptionString', ], 'key' => [ 'shape' => 'MetadataAttributeSchemaKeyString', ], 'type' => [ 'shape' => 'AttributeType', ], ], 'sensitive' => true, ], 'MetadataAttributeSchemaDescriptionString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^[\\s\\S]+$', ], 'MetadataAttributeSchemaKeyString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[\\s\\S]+$', ], 'MetadataAttributeSchemaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetadataAttributeSchema', ], 'max' => 25, 'min' => 1, ], 'MetadataConfigurationForReranking' => [ 'type' => 'structure', 'required' => [ 'selectionMode', ], 'members' => [ 'selectionMode' => [ 'shape' => 'RerankingMetadataSelectionMode', ], 'selectiveModeConfiguration' => [ 'shape' => 'RerankingMetadataSelectiveModeConfiguration', ], ], ], 'MimeType' => [ 'type' => 'string', ], 'ModelIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '(^arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:(([0-9]{12}:custom-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}(([:][a-z0-9-]{1,63}){0,2})?/[a-z0-9]{12})|(:foundation-model/([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.]?[a-z0-9-]{1,63})([:][a-z0-9-]{1,63}){0,2})))|(([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.]?[a-z0-9-]{1,63})([:][a-z0-9-]{1,63}){0,2}))|(([0-9a-zA-Z][_-]?)+))$|(^arn:aws(|-us-gov|-cn|-iso|-iso-b):bedrock:(|[0-9a-z-]{1,20}):(|[0-9]{12}):inference-profile/[a-zA-Z0-9-:.]+)$', ], 'ModelInvocationInput' => [ 'type' => 'structure', 'members' => [ 'foundationModel' => [ 'shape' => 'ModelIdentifier', ], 'inferenceConfiguration' => [ 'shape' => 'InferenceConfiguration', ], 'overrideLambda' => [ 'shape' => 'LambdaArn', ], 'parserMode' => [ 'shape' => 'CreationMode', ], 'promptCreationMode' => [ 'shape' => 'CreationMode', ], 'text' => [ 'shape' => 'PromptText', ], 'traceId' => [ 'shape' => 'TraceId', ], 'type' => [ 'shape' => 'PromptType', ], ], 'sensitive' => true, ], 'ModelNotReadyException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 424, 'senderFault' => true, ], 'exception' => true, ], 'ModelPerformanceConfiguration' => [ 'type' => 'structure', 'members' => [ 'performanceConfig' => [ 'shape' => 'PerformanceConfiguration', ], ], ], 'Name' => [ 'type' => 'string', 'pattern' => '^([0-9a-zA-Z][_-]?){1,100}$', 'sensitive' => true, ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^\\S*$', ], 'NodeErrorCode' => [ 'type' => 'string', 'enum' => [ 'VALIDATION', 'DEPENDENCY_FAILED', 'BAD_GATEWAY', 'INTERNAL_SERVER', ], ], 'NodeExecutionContent' => [ 'type' => 'structure', 'members' => [ 'document' => [ 'shape' => 'Document', ], ], 'sensitive' => true, 'union' => true, ], 'NodeFailureEvent' => [ 'type' => 'structure', 'required' => [ 'errorCode', 'errorMessage', 'nodeName', 'timestamp', ], 'members' => [ 'errorCode' => [ 'shape' => 'NodeErrorCode', ], 'errorMessage' => [ 'shape' => 'String', ], 'nodeName' => [ 'shape' => 'NodeName', ], 'timestamp' => [ 'shape' => 'DateTimestamp', ], ], 'sensitive' => true, ], 'NodeInputEvent' => [ 'type' => 'structure', 'required' => [ 'fields', 'nodeName', 'timestamp', ], 'members' => [ 'fields' => [ 'shape' => 'NodeInputFields', ], 'nodeName' => [ 'shape' => 'NodeName', ], 'timestamp' => [ 'shape' => 'DateTimestamp', ], ], 'sensitive' => true, ], 'NodeInputField' => [ 'type' => 'structure', 'required' => [ 'content', 'name', ], 'members' => [ 'content' => [ 'shape' => 'NodeExecutionContent', ], 'name' => [ 'shape' => 'NodeInputName', ], ], 'sensitive' => true, ], 'NodeInputFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeInputField', ], 'max' => 5, 'min' => 1, ], 'NodeInputName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z]([_]?[0-9a-zA-Z]){0,99}$', ], 'NodeName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z]([_]?[0-9a-zA-Z]){0,99}$', ], 'NodeOutputEvent' => [ 'type' => 'structure', 'required' => [ 'fields', 'nodeName', 'timestamp', ], 'members' => [ 'fields' => [ 'shape' => 'NodeOutputFields', ], 'nodeName' => [ 'shape' => 'NodeName', ], 'timestamp' => [ 'shape' => 'DateTimestamp', ], ], 'sensitive' => true, ], 'NodeOutputField' => [ 'type' => 'structure', 'required' => [ 'content', 'name', ], 'members' => [ 'content' => [ 'shape' => 'NodeExecutionContent', ], 'name' => [ 'shape' => 'NodeOutputName', ], ], 'sensitive' => true, ], 'NodeOutputFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeOutputField', ], 'max' => 2, 'min' => 1, ], 'NodeOutputName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z]([_]?[0-9a-zA-Z]){0,99}$', ], 'NodeType' => [ 'type' => 'string', 'enum' => [ 'FlowInputNode', 'FlowOutputNode', 'LambdaFunctionNode', 'KnowledgeBaseNode', 'PromptNode', 'ConditionNode', 'LexNode', ], ], 'NonBlankString' => [ 'type' => 'string', 'pattern' => '^[\\s\\S]*$', ], 'Observation' => [ 'type' => 'structure', 'members' => [ 'actionGroupInvocationOutput' => [ 'shape' => 'ActionGroupInvocationOutput', ], 'agentCollaboratorInvocationOutput' => [ 'shape' => 'AgentCollaboratorInvocationOutput', ], 'codeInterpreterInvocationOutput' => [ 'shape' => 'CodeInterpreterInvocationOutput', ], 'finalResponse' => [ 'shape' => 'FinalResponse', ], 'knowledgeBaseLookupOutput' => [ 'shape' => 'KnowledgeBaseLookupOutput', ], 'repromptResponse' => [ 'shape' => 'RepromptResponse', ], 'traceId' => [ 'shape' => 'TraceId', ], 'type' => [ 'shape' => 'Type', ], ], 'sensitive' => true, ], 'OptimizePromptRequest' => [ 'type' => 'structure', 'required' => [ 'input', 'targetModelId', ], 'members' => [ 'input' => [ 'shape' => 'InputPrompt', ], 'targetModelId' => [ 'shape' => 'OptimizePromptRequestTargetModelIdString', ], ], ], 'OptimizePromptRequestTargetModelIdString' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:(([0-9]{12}:custom-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}/[a-z0-9]{12})|(:foundation-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.:]?[a-z0-9-]{1,63}))|([0-9]{12}:provisioned-model/[a-z0-9]{12})))|([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.:]?[a-z0-9-]{1,63}))|(([0-9a-zA-Z][_-]?)+)$', ], 'OptimizePromptResponse' => [ 'type' => 'structure', 'required' => [ 'optimizedPrompt', ], 'members' => [ 'optimizedPrompt' => [ 'shape' => 'OptimizedPromptStream', ], ], 'payload' => 'optimizedPrompt', ], 'OptimizedPrompt' => [ 'type' => 'structure', 'members' => [ 'textPrompt' => [ 'shape' => 'TextPrompt', ], ], 'union' => true, ], 'OptimizedPromptEvent' => [ 'type' => 'structure', 'members' => [ 'optimizedPrompt' => [ 'shape' => 'OptimizedPrompt', ], ], 'event' => true, 'sensitive' => true, ], 'OptimizedPromptStream' => [ 'type' => 'structure', 'members' => [ 'accessDeniedException' => [ 'shape' => 'AccessDeniedException', ], 'analyzePromptEvent' => [ 'shape' => 'AnalyzePromptEvent', ], 'badGatewayException' => [ 'shape' => 'BadGatewayException', ], 'dependencyFailedException' => [ 'shape' => 'DependencyFailedException', ], 'internalServerException' => [ 'shape' => 'InternalServerException', ], 'optimizedPromptEvent' => [ 'shape' => 'OptimizedPromptEvent', ], 'throttlingException' => [ 'shape' => 'ThrottlingException', ], 'validationException' => [ 'shape' => 'ValidationException', ], ], 'eventstream' => true, ], 'OrchestrationConfiguration' => [ 'type' => 'structure', 'members' => [ 'additionalModelRequestFields' => [ 'shape' => 'AdditionalModelRequestFields', ], 'inferenceConfig' => [ 'shape' => 'InferenceConfig', ], 'performanceConfig' => [ 'shape' => 'PerformanceConfiguration', ], 'promptTemplate' => [ 'shape' => 'PromptTemplate', ], 'queryTransformationConfiguration' => [ 'shape' => 'QueryTransformationConfiguration', ], ], ], 'OrchestrationExecutor' => [ 'type' => 'structure', 'members' => [ 'lambda' => [ 'shape' => 'LambdaArn', ], ], 'union' => true, ], 'OrchestrationModelInvocationOutput' => [ 'type' => 'structure', 'members' => [ 'metadata' => [ 'shape' => 'Metadata', ], 'rawResponse' => [ 'shape' => 'RawResponse', ], 'reasoningContent' => [ 'shape' => 'ReasoningContentBlock', ], 'traceId' => [ 'shape' => 'TraceId', ], ], 'sensitive' => true, ], 'OrchestrationTrace' => [ 'type' => 'structure', 'members' => [ 'invocationInput' => [ 'shape' => 'InvocationInput', ], 'modelInvocationInput' => [ 'shape' => 'ModelInvocationInput', ], 'modelInvocationOutput' => [ 'shape' => 'OrchestrationModelInvocationOutput', ], 'observation' => [ 'shape' => 'Observation', ], 'rationale' => [ 'shape' => 'Rationale', ], ], 'sensitive' => true, 'union' => true, ], 'OrchestrationType' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'CUSTOM_ORCHESTRATION', ], ], 'OutputFile' => [ 'type' => 'structure', 'members' => [ 'bytes' => [ 'shape' => 'FileBody', ], 'name' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'MimeType', ], ], 'sensitive' => true, ], 'OutputFiles' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputFile', ], 'max' => 5, 'min' => 0, ], 'OutputString' => [ 'type' => 'string', 'sensitive' => true, ], 'Parameter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], ], 'ParameterDescription' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'ParameterDetail' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'description' => [ 'shape' => 'ParameterDescription', ], 'required' => [ 'shape' => 'Boolean', ], 'type' => [ 'shape' => 'ParameterType', ], ], ], 'ParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Parameter', ], ], 'ParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ParameterName', ], 'value' => [ 'shape' => 'ParameterDetail', ], ], 'ParameterName' => [ 'type' => 'string', 'pattern' => '^([0-9a-zA-Z][_-]?){1,100}$', ], 'ParameterType' => [ 'type' => 'string', 'enum' => [ 'string', 'number', 'integer', 'boolean', 'array', ], ], 'Parameters' => [ 'type' => 'list', 'member' => [ 'shape' => 'Parameter', ], ], 'PartBody' => [ 'type' => 'blob', 'max' => 1000000, 'min' => 0, 'sensitive' => true, ], 'Payload' => [ 'type' => 'string', 'sensitive' => true, ], 'PayloadPart' => [ 'type' => 'structure', 'members' => [ 'attribution' => [ 'shape' => 'Attribution', ], 'bytes' => [ 'shape' => 'PartBody', ], ], 'event' => true, 'sensitive' => true, ], 'PayloadType' => [ 'type' => 'string', 'enum' => [ 'TEXT', 'RETURN_CONTROL', ], ], 'PerformanceConfigLatency' => [ 'type' => 'string', 'enum' => [ 'standard', 'optimized', ], ], 'PerformanceConfiguration' => [ 'type' => 'structure', 'members' => [ 'latency' => [ 'shape' => 'PerformanceConfigLatency', ], ], ], 'PostProcessingModelInvocationOutput' => [ 'type' => 'structure', 'members' => [ 'metadata' => [ 'shape' => 'Metadata', ], 'parsedResponse' => [ 'shape' => 'PostProcessingParsedResponse', ], 'rawResponse' => [ 'shape' => 'RawResponse', ], 'reasoningContent' => [ 'shape' => 'ReasoningContentBlock', ], 'traceId' => [ 'shape' => 'TraceId', ], ], 'sensitive' => true, ], 'PostProcessingParsedResponse' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'OutputString', ], ], 'sensitive' => true, ], 'PostProcessingTrace' => [ 'type' => 'structure', 'members' => [ 'modelInvocationInput' => [ 'shape' => 'ModelInvocationInput', ], 'modelInvocationOutput' => [ 'shape' => 'PostProcessingModelInvocationOutput', ], ], 'sensitive' => true, 'union' => true, ], 'PreProcessingModelInvocationOutput' => [ 'type' => 'structure', 'members' => [ 'metadata' => [ 'shape' => 'Metadata', ], 'parsedResponse' => [ 'shape' => 'PreProcessingParsedResponse', ], 'rawResponse' => [ 'shape' => 'RawResponse', ], 'reasoningContent' => [ 'shape' => 'ReasoningContentBlock', ], 'traceId' => [ 'shape' => 'TraceId', ], ], 'sensitive' => true, ], 'PreProcessingParsedResponse' => [ 'type' => 'structure', 'members' => [ 'isValid' => [ 'shape' => 'Boolean', ], 'rationale' => [ 'shape' => 'RationaleString', ], ], 'sensitive' => true, ], 'PreProcessingTrace' => [ 'type' => 'structure', 'members' => [ 'modelInvocationInput' => [ 'shape' => 'ModelInvocationInput', ], 'modelInvocationOutput' => [ 'shape' => 'PreProcessingModelInvocationOutput', ], ], 'sensitive' => true, 'union' => true, ], 'PromptConfiguration' => [ 'type' => 'structure', 'members' => [ 'additionalModelRequestFields' => [ 'shape' => 'Document', ], 'basePromptTemplate' => [ 'shape' => 'BasePromptTemplate', ], 'foundationModel' => [ 'shape' => 'ModelIdentifier', ], 'inferenceConfiguration' => [ 'shape' => 'InferenceConfiguration', ], 'parserMode' => [ 'shape' => 'CreationMode', ], 'promptCreationMode' => [ 'shape' => 'CreationMode', ], 'promptState' => [ 'shape' => 'PromptState', ], 'promptType' => [ 'shape' => 'PromptType', ], ], ], 'PromptConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'PromptConfiguration', ], 'max' => 10, 'min' => 0, ], 'PromptCreationConfigurations' => [ 'type' => 'structure', 'members' => [ 'excludePreviousThinkingSteps' => [ 'shape' => 'Boolean', ], 'previousConversationTurnsToInclude' => [ 'shape' => 'PromptCreationConfigurationsPreviousConversationTurnsToIncludeInteger', ], ], ], 'PromptCreationConfigurationsPreviousConversationTurnsToIncludeInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'PromptOverrideConfiguration' => [ 'type' => 'structure', 'required' => [ 'promptConfigurations', ], 'members' => [ 'overrideLambda' => [ 'shape' => 'LambdaResourceArn', ], 'promptConfigurations' => [ 'shape' => 'PromptConfigurations', ], ], 'sensitive' => true, ], 'PromptSessionAttributesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'PromptState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'PromptTemplate' => [ 'type' => 'structure', 'members' => [ 'textPromptTemplate' => [ 'shape' => 'TextPromptTemplate', ], ], ], 'PromptText' => [ 'type' => 'string', 'sensitive' => true, ], 'PromptType' => [ 'type' => 'string', 'enum' => [ 'PRE_PROCESSING', 'ORCHESTRATION', 'KNOWLEDGE_BASE_RESPONSE_GENERATION', 'POST_PROCESSING', 'ROUTING_CLASSIFIER', ], ], 'PropertyParameters' => [ 'type' => 'structure', 'members' => [ 'properties' => [ 'shape' => 'ParameterList', ], ], ], 'PutInvocationStepRequest' => [ 'type' => 'structure', 'required' => [ 'invocationIdentifier', 'invocationStepTime', 'payload', 'sessionIdentifier', ], 'members' => [ 'invocationIdentifier' => [ 'shape' => 'InvocationIdentifier', ], 'invocationStepId' => [ 'shape' => 'Uuid', ], 'invocationStepTime' => [ 'shape' => 'DateTimestamp', ], 'payload' => [ 'shape' => 'InvocationStepPayload', ], 'sessionIdentifier' => [ 'shape' => 'SessionIdentifier', 'location' => 'uri', 'locationName' => 'sessionIdentifier', ], ], ], 'PutInvocationStepResponse' => [ 'type' => 'structure', 'required' => [ 'invocationStepId', ], 'members' => [ 'invocationStepId' => [ 'shape' => 'Uuid', ], ], ], 'QueryGenerationInput' => [ 'type' => 'structure', 'required' => [ 'text', 'type', ], 'members' => [ 'text' => [ 'shape' => 'QueryGenerationInputTextString', ], 'type' => [ 'shape' => 'InputQueryType', ], ], 'sensitive' => true, ], 'QueryGenerationInputTextString' => [ 'type' => 'string', 'max' => 20000, 'min' => 1, ], 'QueryTransformationConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'QueryTransformationType', ], ], ], 'QueryTransformationMode' => [ 'type' => 'string', 'enum' => [ 'TEXT_TO_SQL', ], ], 'QueryTransformationType' => [ 'type' => 'string', 'enum' => [ 'QUERY_DECOMPOSITION', ], ], 'RAGStopSequences' => [ 'type' => 'list', 'member' => [ 'shape' => 'RAGStopSequencesMemberString', ], 'max' => 4, 'min' => 0, ], 'RAGStopSequencesMemberString' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'Rationale' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'RationaleString', ], 'traceId' => [ 'shape' => 'TraceId', ], ], 'sensitive' => true, ], 'RationaleString' => [ 'type' => 'string', 'sensitive' => true, ], 'RawResponse' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'ReasoningContentBlock' => [ 'type' => 'structure', 'members' => [ 'reasoningText' => [ 'shape' => 'ReasoningTextBlock', ], 'redactedContent' => [ 'shape' => 'Blob', ], ], 'sensitive' => true, 'union' => true, ], 'ReasoningTextBlock' => [ 'type' => 'structure', 'required' => [ 'text', ], 'members' => [ 'signature' => [ 'shape' => 'String', ], 'text' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'RelayConversationHistory' => [ 'type' => 'string', 'enum' => [ 'TO_COLLABORATOR', 'DISABLED', ], ], 'RepromptResponse' => [ 'type' => 'structure', 'members' => [ 'source' => [ 'shape' => 'Source', ], 'text' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'RequestBody' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'ContentMap', ], ], ], 'RequireConfirmation' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'RerankDocument' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'jsonDocument' => [ 'shape' => 'Document', ], 'textDocument' => [ 'shape' => 'RerankTextDocument', ], 'type' => [ 'shape' => 'RerankDocumentType', ], ], 'sensitive' => true, ], 'RerankDocumentType' => [ 'type' => 'string', 'enum' => [ 'TEXT', 'JSON', ], ], 'RerankQueriesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RerankQuery', ], 'max' => 1, 'min' => 1, 'sensitive' => true, ], 'RerankQuery' => [ 'type' => 'structure', 'required' => [ 'textQuery', 'type', ], 'members' => [ 'textQuery' => [ 'shape' => 'RerankTextDocument', ], 'type' => [ 'shape' => 'RerankQueryContentType', ], ], 'sensitive' => true, ], 'RerankQueryContentType' => [ 'type' => 'string', 'enum' => [ 'TEXT', ], ], 'RerankRequest' => [ 'type' => 'structure', 'required' => [ 'queries', 'rerankingConfiguration', 'sources', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'queries' => [ 'shape' => 'RerankQueriesList', ], 'rerankingConfiguration' => [ 'shape' => 'RerankingConfiguration', ], 'sources' => [ 'shape' => 'RerankSourcesList', ], ], ], 'RerankResponse' => [ 'type' => 'structure', 'required' => [ 'results', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'results' => [ 'shape' => 'RerankResultsList', ], ], ], 'RerankResult' => [ 'type' => 'structure', 'required' => [ 'index', 'relevanceScore', ], 'members' => [ 'document' => [ 'shape' => 'RerankDocument', ], 'index' => [ 'shape' => 'RerankResultIndexInteger', ], 'relevanceScore' => [ 'shape' => 'Float', ], ], ], 'RerankResultIndexInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 0, ], 'RerankResultsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RerankResult', ], ], 'RerankSource' => [ 'type' => 'structure', 'required' => [ 'inlineDocumentSource', 'type', ], 'members' => [ 'inlineDocumentSource' => [ 'shape' => 'RerankDocument', ], 'type' => [ 'shape' => 'RerankSourceType', ], ], 'sensitive' => true, ], 'RerankSourceType' => [ 'type' => 'string', 'enum' => [ 'INLINE', ], ], 'RerankSourcesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RerankSource', ], 'max' => 1000, 'min' => 1, 'sensitive' => true, ], 'RerankTextDocument' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'RerankTextDocumentTextString', ], ], 'sensitive' => true, ], 'RerankTextDocumentTextString' => [ 'type' => 'string', 'max' => 32000, 'min' => 1, ], 'RerankingConfiguration' => [ 'type' => 'structure', 'required' => [ 'bedrockRerankingConfiguration', 'type', ], 'members' => [ 'bedrockRerankingConfiguration' => [ 'shape' => 'BedrockRerankingConfiguration', ], 'type' => [ 'shape' => 'RerankingConfigurationType', ], ], ], 'RerankingConfigurationType' => [ 'type' => 'string', 'enum' => [ 'BEDROCK_RERANKING_MODEL', ], ], 'RerankingMetadataSelectionMode' => [ 'type' => 'string', 'enum' => [ 'SELECTIVE', 'ALL', ], ], 'RerankingMetadataSelectiveModeConfiguration' => [ 'type' => 'structure', 'members' => [ 'fieldsToExclude' => [ 'shape' => 'FieldsForReranking', ], 'fieldsToInclude' => [ 'shape' => 'FieldsForReranking', ], ], 'union' => true, ], 'ResourceDescription' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'sensitive' => true, ], 'ResourceName' => [ 'type' => 'string', 'pattern' => '^([0-9a-zA-Z][_-]?){1,100}$', 'sensitive' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResponseBody' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'ContentBody', ], ], 'ResponseState' => [ 'type' => 'string', 'enum' => [ 'FAILURE', 'REPROMPT', ], ], 'ResponseStream' => [ 'type' => 'structure', 'members' => [ 'accessDeniedException' => [ 'shape' => 'AccessDeniedException', ], 'badGatewayException' => [ 'shape' => 'BadGatewayException', ], 'chunk' => [ 'shape' => 'PayloadPart', ], 'conflictException' => [ 'shape' => 'ConflictException', ], 'dependencyFailedException' => [ 'shape' => 'DependencyFailedException', ], 'files' => [ 'shape' => 'FilePart', ], 'internalServerException' => [ 'shape' => 'InternalServerException', ], 'modelNotReadyException' => [ 'shape' => 'ModelNotReadyException', ], 'resourceNotFoundException' => [ 'shape' => 'ResourceNotFoundException', ], 'returnControl' => [ 'shape' => 'ReturnControlPayload', ], 'serviceQuotaExceededException' => [ 'shape' => 'ServiceQuotaExceededException', ], 'throttlingException' => [ 'shape' => 'ThrottlingException', ], 'trace' => [ 'shape' => 'TracePart', ], 'validationException' => [ 'shape' => 'ValidationException', ], ], 'eventstream' => true, ], 'RetrievalFilter' => [ 'type' => 'structure', 'members' => [ 'andAll' => [ 'shape' => 'RetrievalFilterList', ], 'equals' => [ 'shape' => 'FilterAttribute', ], 'greaterThan' => [ 'shape' => 'FilterAttribute', ], 'greaterThanOrEquals' => [ 'shape' => 'FilterAttribute', ], 'in' => [ 'shape' => 'FilterAttribute', ], 'lessThan' => [ 'shape' => 'FilterAttribute', ], 'lessThanOrEquals' => [ 'shape' => 'FilterAttribute', ], 'listContains' => [ 'shape' => 'FilterAttribute', ], 'notEquals' => [ 'shape' => 'FilterAttribute', ], 'notIn' => [ 'shape' => 'FilterAttribute', ], 'orAll' => [ 'shape' => 'RetrievalFilterList', ], 'startsWith' => [ 'shape' => 'FilterAttribute', ], 'stringContains' => [ 'shape' => 'FilterAttribute', ], ], 'sensitive' => true, 'union' => true, ], 'RetrievalFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RetrievalFilter', ], 'min' => 2, ], 'RetrievalResultConfluenceLocation' => [ 'type' => 'structure', 'members' => [ 'url' => [ 'shape' => 'String', ], ], ], 'RetrievalResultContent' => [ 'type' => 'structure', 'members' => [ 'byteContent' => [ 'shape' => 'String', ], 'row' => [ 'shape' => 'RetrievalResultContentRow', ], 'text' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'RetrievalResultContentType', ], ], 'sensitive' => true, ], 'RetrievalResultContentColumn' => [ 'type' => 'structure', 'members' => [ 'columnName' => [ 'shape' => 'String', ], 'columnValue' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'RetrievalResultContentColumnType', ], ], 'sensitive' => true, ], 'RetrievalResultContentColumnType' => [ 'type' => 'string', 'enum' => [ 'BLOB', 'BOOLEAN', 'DOUBLE', 'NULL', 'LONG', 'STRING', ], ], 'RetrievalResultContentRow' => [ 'type' => 'list', 'member' => [ 'shape' => 'RetrievalResultContentColumn', ], 'sensitive' => true, ], 'RetrievalResultContentType' => [ 'type' => 'string', 'enum' => [ 'TEXT', 'IMAGE', 'ROW', ], ], 'RetrievalResultCustomDocumentLocation' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', ], ], ], 'RetrievalResultKendraDocumentLocation' => [ 'type' => 'structure', 'members' => [ 'uri' => [ 'shape' => 'String', ], ], ], 'RetrievalResultLocation' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'confluenceLocation' => [ 'shape' => 'RetrievalResultConfluenceLocation', ], 'customDocumentLocation' => [ 'shape' => 'RetrievalResultCustomDocumentLocation', ], 'kendraDocumentLocation' => [ 'shape' => 'RetrievalResultKendraDocumentLocation', ], 's3Location' => [ 'shape' => 'RetrievalResultS3Location', ], 'salesforceLocation' => [ 'shape' => 'RetrievalResultSalesforceLocation', ], 'sharePointLocation' => [ 'shape' => 'RetrievalResultSharePointLocation', ], 'sqlLocation' => [ 'shape' => 'RetrievalResultSqlLocation', ], 'type' => [ 'shape' => 'RetrievalResultLocationType', ], 'webLocation' => [ 'shape' => 'RetrievalResultWebLocation', ], ], 'sensitive' => true, ], 'RetrievalResultLocationType' => [ 'type' => 'string', 'enum' => [ 'S3', 'WEB', 'CONFLUENCE', 'SALESFORCE', 'SHAREPOINT', 'CUSTOM', 'KENDRA', 'SQL', ], ], 'RetrievalResultMetadata' => [ 'type' => 'map', 'key' => [ 'shape' => 'RetrievalResultMetadataKey', ], 'value' => [ 'shape' => 'RetrievalResultMetadataValue', ], 'min' => 1, 'sensitive' => true, ], 'RetrievalResultMetadataKey' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'RetrievalResultMetadataValue' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'RetrievalResultS3Location' => [ 'type' => 'structure', 'members' => [ 'uri' => [ 'shape' => 'String', ], ], ], 'RetrievalResultSalesforceLocation' => [ 'type' => 'structure', 'members' => [ 'url' => [ 'shape' => 'String', ], ], ], 'RetrievalResultSharePointLocation' => [ 'type' => 'structure', 'members' => [ 'url' => [ 'shape' => 'String', ], ], ], 'RetrievalResultSqlLocation' => [ 'type' => 'structure', 'members' => [ 'query' => [ 'shape' => 'String', ], ], ], 'RetrievalResultWebLocation' => [ 'type' => 'structure', 'members' => [ 'url' => [ 'shape' => 'String', ], ], ], 'RetrieveAndGenerateConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'externalSourcesConfiguration' => [ 'shape' => 'ExternalSourcesRetrieveAndGenerateConfiguration', ], 'knowledgeBaseConfiguration' => [ 'shape' => 'KnowledgeBaseRetrieveAndGenerateConfiguration', ], 'type' => [ 'shape' => 'RetrieveAndGenerateType', ], ], ], 'RetrieveAndGenerateInput' => [ 'type' => 'structure', 'required' => [ 'text', ], 'members' => [ 'text' => [ 'shape' => 'RetrieveAndGenerateInputTextString', ], ], 'sensitive' => true, ], 'RetrieveAndGenerateInputTextString' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'RetrieveAndGenerateOutput' => [ 'type' => 'structure', 'required' => [ 'text', ], 'members' => [ 'text' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'RetrieveAndGenerateOutputEvent' => [ 'type' => 'structure', 'required' => [ 'text', ], 'members' => [ 'text' => [ 'shape' => 'String', ], ], 'event' => true, 'sensitive' => true, ], 'RetrieveAndGenerateRequest' => [ 'type' => 'structure', 'required' => [ 'input', ], 'members' => [ 'input' => [ 'shape' => 'RetrieveAndGenerateInput', ], 'retrieveAndGenerateConfiguration' => [ 'shape' => 'RetrieveAndGenerateConfiguration', ], 'sessionConfiguration' => [ 'shape' => 'RetrieveAndGenerateSessionConfiguration', ], 'sessionId' => [ 'shape' => 'SessionId', ], ], ], 'RetrieveAndGenerateResponse' => [ 'type' => 'structure', 'required' => [ 'output', 'sessionId', ], 'members' => [ 'citations' => [ 'shape' => 'Citations', ], 'guardrailAction' => [ 'shape' => 'GuadrailAction', ], 'output' => [ 'shape' => 'RetrieveAndGenerateOutput', ], 'sessionId' => [ 'shape' => 'SessionId', ], ], ], 'RetrieveAndGenerateSessionConfiguration' => [ 'type' => 'structure', 'required' => [ 'kmsKeyArn', ], 'members' => [ 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'RetrieveAndGenerateStreamRequest' => [ 'type' => 'structure', 'required' => [ 'input', ], 'members' => [ 'input' => [ 'shape' => 'RetrieveAndGenerateInput', ], 'retrieveAndGenerateConfiguration' => [ 'shape' => 'RetrieveAndGenerateConfiguration', ], 'sessionConfiguration' => [ 'shape' => 'RetrieveAndGenerateSessionConfiguration', ], 'sessionId' => [ 'shape' => 'SessionId', ], ], ], 'RetrieveAndGenerateStreamResponse' => [ 'type' => 'structure', 'required' => [ 'sessionId', 'stream', ], 'members' => [ 'sessionId' => [ 'shape' => 'SessionId', 'location' => 'header', 'locationName' => 'x-amzn-bedrock-knowledge-base-session-id', ], 'stream' => [ 'shape' => 'RetrieveAndGenerateStreamResponseOutput', ], ], 'payload' => 'stream', ], 'RetrieveAndGenerateStreamResponseOutput' => [ 'type' => 'structure', 'members' => [ 'accessDeniedException' => [ 'shape' => 'AccessDeniedException', ], 'badGatewayException' => [ 'shape' => 'BadGatewayException', ], 'citation' => [ 'shape' => 'CitationEvent', ], 'conflictException' => [ 'shape' => 'ConflictException', ], 'dependencyFailedException' => [ 'shape' => 'DependencyFailedException', ], 'guardrail' => [ 'shape' => 'GuardrailEvent', ], 'internalServerException' => [ 'shape' => 'InternalServerException', ], 'output' => [ 'shape' => 'RetrieveAndGenerateOutputEvent', ], 'resourceNotFoundException' => [ 'shape' => 'ResourceNotFoundException', ], 'serviceQuotaExceededException' => [ 'shape' => 'ServiceQuotaExceededException', ], 'throttlingException' => [ 'shape' => 'ThrottlingException', ], 'validationException' => [ 'shape' => 'ValidationException', ], ], 'eventstream' => true, ], 'RetrieveAndGenerateType' => [ 'type' => 'string', 'enum' => [ 'KNOWLEDGE_BASE', 'EXTERNAL_SOURCES', ], ], 'RetrieveRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'retrievalQuery', ], 'members' => [ 'guardrailConfiguration' => [ 'shape' => 'GuardrailConfiguration', ], 'knowledgeBaseId' => [ 'shape' => 'KnowledgeBaseId', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'retrievalConfiguration' => [ 'shape' => 'KnowledgeBaseRetrievalConfiguration', ], 'retrievalQuery' => [ 'shape' => 'KnowledgeBaseQuery', ], ], ], 'RetrieveResponse' => [ 'type' => 'structure', 'required' => [ 'retrievalResults', ], 'members' => [ 'guardrailAction' => [ 'shape' => 'GuadrailAction', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'retrievalResults' => [ 'shape' => 'KnowledgeBaseRetrievalResults', ], ], ], 'RetrievedReference' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'RetrievalResultContent', ], 'location' => [ 'shape' => 'RetrievalResultLocation', ], 'metadata' => [ 'shape' => 'RetrievalResultMetadata', ], ], ], 'RetrievedReferences' => [ 'type' => 'list', 'member' => [ 'shape' => 'RetrievedReference', ], ], 'ReturnControlInvocationResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'InvocationResultMember', ], 'max' => 5, 'min' => 1, ], 'ReturnControlPayload' => [ 'type' => 'structure', 'members' => [ 'invocationId' => [ 'shape' => 'String', ], 'invocationInputs' => [ 'shape' => 'InvocationInputs', ], ], 'event' => true, 'sensitive' => true, ], 'ReturnControlResults' => [ 'type' => 'structure', 'members' => [ 'invocationId' => [ 'shape' => 'String', ], 'returnControlInvocationResults' => [ 'shape' => 'ReturnControlInvocationResults', ], ], ], 'RoutingClassifierModelInvocationOutput' => [ 'type' => 'structure', 'members' => [ 'metadata' => [ 'shape' => 'Metadata', ], 'rawResponse' => [ 'shape' => 'RawResponse', ], 'traceId' => [ 'shape' => 'TraceId', ], ], 'sensitive' => true, ], 'RoutingClassifierTrace' => [ 'type' => 'structure', 'members' => [ 'invocationInput' => [ 'shape' => 'InvocationInput', ], 'modelInvocationInput' => [ 'shape' => 'ModelInvocationInput', ], 'modelInvocationOutput' => [ 'shape' => 'RoutingClassifierModelInvocationOutput', ], 'observation' => [ 'shape' => 'Observation', ], ], 'sensitive' => true, 'union' => true, ], 'S3BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9]$', ], 'S3Identifier' => [ 'type' => 'structure', 'members' => [ 's3BucketName' => [ 'shape' => 'S3BucketName', ], 's3ObjectKey' => [ 'shape' => 'S3ObjectKey', ], ], ], 'S3Location' => [ 'type' => 'structure', 'required' => [ 'uri', ], 'members' => [ 'uri' => [ 'shape' => 'S3Uri', ], ], ], 'S3ObjectDoc' => [ 'type' => 'structure', 'required' => [ 'uri', ], 'members' => [ 'uri' => [ 'shape' => 'S3Uri', ], ], ], 'S3ObjectFile' => [ 'type' => 'structure', 'required' => [ 'uri', ], 'members' => [ 'uri' => [ 'shape' => 'S3Uri', ], ], ], 'S3ObjectKey' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^[\\.\\-\\!\\*\\_\\\'\\(\\)a-zA-Z0-9][\\.\\-\\!\\*\\_\\\'\\(\\)\\/a-zA-Z0-9]*$', ], 'S3Uri' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^s3://[a-z0-9][a-z0-9.-]{1,61}[a-z0-9]/.{1,1024}$', ], 'SatisfiedCondition' => [ 'type' => 'structure', 'required' => [ 'conditionName', ], 'members' => [ 'conditionName' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'SatisfiedConditions' => [ 'type' => 'list', 'member' => [ 'shape' => 'SatisfiedCondition', ], 'max' => 5, 'min' => 1, ], 'SearchType' => [ 'type' => 'string', 'enum' => [ 'HYBRID', 'SEMANTIC', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SessionArn' => [ 'type' => 'string', 'pattern' => '^arn:aws(-[^:]+)?:bedrock:[a-z0-9-]+:[0-9]{12}:session/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', ], 'SessionAttributesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'SessionId' => [ 'type' => 'string', 'max' => 100, 'min' => 2, 'pattern' => '^[0-9a-zA-Z._:-]+$', ], 'SessionIdentifier' => [ 'type' => 'string', 'pattern' => '^(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]+:[0-9]{12}:session/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})|([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})$', ], 'SessionMetadataKey' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'SessionMetadataMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'SessionMetadataKey', ], 'value' => [ 'shape' => 'SessionMetadataValue', ], 'max' => 50, 'min' => 0, ], 'SessionMetadataValue' => [ 'type' => 'string', 'max' => 5000, 'min' => 0, ], 'SessionState' => [ 'type' => 'structure', 'members' => [ 'conversationHistory' => [ 'shape' => 'ConversationHistory', ], 'files' => [ 'shape' => 'InputFiles', ], 'invocationId' => [ 'shape' => 'String', ], 'knowledgeBaseConfigurations' => [ 'shape' => 'KnowledgeBaseConfigurations', ], 'promptSessionAttributes' => [ 'shape' => 'PromptSessionAttributesMap', ], 'returnControlInvocationResults' => [ 'shape' => 'ReturnControlInvocationResults', ], 'sessionAttributes' => [ 'shape' => 'SessionAttributesMap', ], ], ], 'SessionStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'EXPIRED', 'ENDED', ], ], 'SessionSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SessionSummary', ], ], 'SessionSummary' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'lastUpdatedAt', 'sessionArn', 'sessionId', 'sessionStatus', ], 'members' => [ 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'lastUpdatedAt' => [ 'shape' => 'DateTimestamp', ], 'sessionArn' => [ 'shape' => 'SessionArn', ], 'sessionId' => [ 'shape' => 'Uuid', ], 'sessionStatus' => [ 'shape' => 'SessionStatus', ], ], ], 'SessionTTL' => [ 'type' => 'integer', 'box' => true, 'max' => 3600, 'min' => 60, ], 'Source' => [ 'type' => 'string', 'enum' => [ 'ACTION_GROUP', 'KNOWLEDGE_BASE', 'PARSER', ], 'sensitive' => true, ], 'Span' => [ 'type' => 'structure', 'members' => [ 'end' => [ 'shape' => 'SpanEndInteger', ], 'start' => [ 'shape' => 'SpanStartInteger', ], ], ], 'SpanEndInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'SpanStartInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'StartFlowExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'flowAliasIdentifier', 'flowIdentifier', 'inputs', ], 'members' => [ 'flowAliasIdentifier' => [ 'shape' => 'FlowAliasIdentifier', 'location' => 'uri', 'locationName' => 'flowAliasIdentifier', ], 'flowExecutionName' => [ 'shape' => 'FlowExecutionName', ], 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', 'location' => 'uri', 'locationName' => 'flowIdentifier', ], 'inputs' => [ 'shape' => 'FlowInputs', ], 'modelPerformanceConfiguration' => [ 'shape' => 'ModelPerformanceConfiguration', ], ], ], 'StartFlowExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'executionArn' => [ 'shape' => 'FlowExecutionIdentifier', ], ], ], 'StopFlowExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'executionIdentifier', 'flowAliasIdentifier', 'flowIdentifier', ], 'members' => [ 'executionIdentifier' => [ 'shape' => 'FlowExecutionIdentifier', 'location' => 'uri', 'locationName' => 'executionIdentifier', ], 'flowAliasIdentifier' => [ 'shape' => 'FlowAliasIdentifier', 'location' => 'uri', 'locationName' => 'flowAliasIdentifier', ], 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', 'location' => 'uri', 'locationName' => 'flowIdentifier', ], ], ], 'StopFlowExecutionResponse' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'executionArn' => [ 'shape' => 'FlowExecutionIdentifier', ], 'status' => [ 'shape' => 'FlowExecutionStatus', ], ], ], 'StopSequences' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 4, 'min' => 0, ], 'StreamingConfigurations' => [ 'type' => 'structure', 'members' => [ 'applyGuardrailInterval' => [ 'shape' => 'StreamingConfigurationsApplyGuardrailIntervalInteger', ], 'streamFinalResponse' => [ 'shape' => 'Boolean', ], ], ], 'StreamingConfigurationsApplyGuardrailIntervalInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'String' => [ 'type' => 'string', ], 'SummaryText' => [ 'type' => 'string', 'max' => 25000000, 'min' => 0, ], 'SyntheticTimestamp_date_time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\s._:/=+@-]*$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TaggableResourcesArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^[a-zA-Z0-9\\s._:/=+@-]*$', ], 'TaggableResourcesArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 20, 'pattern' => '(^arn:aws(-[^:]+)?:bedrock:[a-zA-Z0-9-]+:[0-9]{12}:(session)/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$)', ], 'TagsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 1, ], 'Temperature' => [ 'type' => 'float', 'box' => true, 'max' => 1, 'min' => 0, ], 'TextInferenceConfig' => [ 'type' => 'structure', 'members' => [ 'maxTokens' => [ 'shape' => 'MaxTokens', ], 'stopSequences' => [ 'shape' => 'RAGStopSequences', ], 'temperature' => [ 'shape' => 'Temperature', ], 'topP' => [ 'shape' => 'TopP', ], ], ], 'TextPrompt' => [ 'type' => 'structure', 'required' => [ 'text', ], 'members' => [ 'text' => [ 'shape' => 'TextPromptTextString', ], ], 'sensitive' => true, ], 'TextPromptTemplate' => [ 'type' => 'string', 'max' => 4000, 'min' => 1, 'sensitive' => true, ], 'TextPromptTextString' => [ 'type' => 'string', 'max' => 200000, 'min' => 1, ], 'TextResponsePart' => [ 'type' => 'structure', 'members' => [ 'span' => [ 'shape' => 'Span', ], 'text' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'TextToSqlConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'knowledgeBaseConfiguration' => [ 'shape' => 'TextToSqlKnowledgeBaseConfiguration', ], 'type' => [ 'shape' => 'TextToSqlConfigurationType', ], ], ], 'TextToSqlConfigurationType' => [ 'type' => 'string', 'enum' => [ 'KNOWLEDGE_BASE', ], ], 'TextToSqlKnowledgeBaseConfiguration' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseArn', ], 'members' => [ 'knowledgeBaseArn' => [ 'shape' => 'KnowledgeBaseArn', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'TopK' => [ 'type' => 'integer', 'box' => true, 'max' => 500, 'min' => 0, ], 'TopP' => [ 'type' => 'float', 'box' => true, 'max' => 1, 'min' => 0, ], 'Trace' => [ 'type' => 'structure', 'members' => [ 'customOrchestrationTrace' => [ 'shape' => 'CustomOrchestrationTrace', ], 'failureTrace' => [ 'shape' => 'FailureTrace', ], 'guardrailTrace' => [ 'shape' => 'GuardrailTrace', ], 'orchestrationTrace' => [ 'shape' => 'OrchestrationTrace', ], 'postProcessingTrace' => [ 'shape' => 'PostProcessingTrace', ], 'preProcessingTrace' => [ 'shape' => 'PreProcessingTrace', ], 'routingClassifierTrace' => [ 'shape' => 'RoutingClassifierTrace', ], ], 'sensitive' => true, 'union' => true, ], 'TraceId' => [ 'type' => 'string', 'max' => 16, 'min' => 2, ], 'TraceKnowledgeBaseId' => [ 'type' => 'string', 'sensitive' => true, ], 'TracePart' => [ 'type' => 'structure', 'members' => [ 'agentAliasId' => [ 'shape' => 'AgentAliasId', ], 'agentId' => [ 'shape' => 'AgentId', ], 'agentVersion' => [ 'shape' => 'AgentVersion', ], 'callerChain' => [ 'shape' => 'CallerChain', ], 'collaboratorName' => [ 'shape' => 'Name', ], 'eventTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'sessionId' => [ 'shape' => 'SessionId', ], 'trace' => [ 'shape' => 'Trace', ], ], 'event' => true, 'sensitive' => true, ], 'TransformationConfiguration' => [ 'type' => 'structure', 'required' => [ 'mode', ], 'members' => [ 'mode' => [ 'shape' => 'QueryTransformationMode', ], 'textToSqlConfiguration' => [ 'shape' => 'TextToSqlConfiguration', ], ], ], 'Type' => [ 'type' => 'string', 'enum' => [ 'ACTION_GROUP', 'AGENT_COLLABORATOR', 'KNOWLEDGE_BASE', 'FINISH', 'ASK_USER', 'REPROMPT', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TaggableResourcesArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateSessionRequest' => [ 'type' => 'structure', 'required' => [ 'sessionIdentifier', ], 'members' => [ 'sessionIdentifier' => [ 'shape' => 'SessionIdentifier', 'location' => 'uri', 'locationName' => 'sessionIdentifier', ], 'sessionMetadata' => [ 'shape' => 'SessionMetadataMap', ], ], ], 'UpdateSessionResponse' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'lastUpdatedAt', 'sessionArn', 'sessionId', 'sessionStatus', ], 'members' => [ 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'lastUpdatedAt' => [ 'shape' => 'DateTimestamp', ], 'sessionArn' => [ 'shape' => 'SessionArn', ], 'sessionId' => [ 'shape' => 'Uuid', ], 'sessionStatus' => [ 'shape' => 'SessionStatus', ], ], ], 'Usage' => [ 'type' => 'structure', 'members' => [ 'inputTokens' => [ 'shape' => 'Integer', ], 'outputTokens' => [ 'shape' => 'Integer', ], ], 'sensitive' => true, ], 'Uuid' => [ 'type' => 'string', 'pattern' => '^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'VectorSearchBedrockRerankingConfiguration' => [ 'type' => 'structure', 'required' => [ 'modelConfiguration', ], 'members' => [ 'metadataConfiguration' => [ 'shape' => 'MetadataConfigurationForReranking', ], 'modelConfiguration' => [ 'shape' => 'VectorSearchBedrockRerankingModelConfiguration', ], 'numberOfRerankedResults' => [ 'shape' => 'VectorSearchBedrockRerankingConfigurationNumberOfRerankedResultsInteger', ], ], ], 'VectorSearchBedrockRerankingConfigurationNumberOfRerankedResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'VectorSearchBedrockRerankingModelConfiguration' => [ 'type' => 'structure', 'required' => [ 'modelArn', ], 'members' => [ 'additionalModelRequestFields' => [ 'shape' => 'AdditionalModelRequestFields', ], 'modelArn' => [ 'shape' => 'BedrockRerankingModelArn', ], ], ], 'VectorSearchRerankingConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'bedrockRerankingConfiguration' => [ 'shape' => 'VectorSearchBedrockRerankingConfiguration', ], 'type' => [ 'shape' => 'VectorSearchRerankingConfigurationType', ], ], ], 'VectorSearchRerankingConfigurationType' => [ 'type' => 'string', 'enum' => [ 'BEDROCK_RERANKING_MODEL', ], ], 'Verb' => [ 'type' => 'string', 'sensitive' => true, ], 'Version' => [ 'type' => 'string', 'max' => 5, 'min' => 1, 'pattern' => '^(DRAFT|[0-9]{0,4}[1-9][0-9]{0,4})$', ], ],];
