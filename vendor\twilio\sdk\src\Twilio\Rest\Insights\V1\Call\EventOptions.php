<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Insights
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Insights\V1\Call;

use Twilio\Options;
use Twilio\Values;

abstract class EventOptions
{
    /**
     * @param string $edge The Edge of this Event. One of `unknown_edge`, `carrier_edge`, `sip_edge`, `sdk_edge` or `client_edge`.
     * @return ReadEventOptions Options builder
     */
    public static function read(
        
        string $edge = Values::NONE

    ): ReadEventOptions
    {
        return new ReadEventOptions(
            $edge
        );
    }

}

class ReadEventOptions extends Options
    {
    /**
     * @param string $edge The Edge of this Event. One of `unknown_edge`, `carrier_edge`, `sip_edge`, `sdk_edge` or `client_edge`.
     */
    public function __construct(
        
        string $edge = Values::NONE

    ) {
        $this->options['edge'] = $edge;
    }

    /**
     * The Edge of this Event. One of `unknown_edge`, `carrier_edge`, `sip_edge`, `sdk_edge` or `client_edge`.
     *
     * @param string $edge The Edge of this Event. One of `unknown_edge`, `carrier_edge`, `sip_edge`, `sdk_edge` or `client_edge`.
     * @return $this Fluent Builder
     */
    public function setEdge(string $edge): self
    {
        $this->options['edge'] = $edge;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Insights.V1.ReadEventOptions ' . $options . ']';
    }
}

