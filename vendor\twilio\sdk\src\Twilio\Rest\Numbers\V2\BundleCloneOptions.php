<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Numbers\V2;

use Twilio\Options;
use Twilio\Values;

abstract class BundleCloneOptions
{
    /**
     * @param bool $moveToDraft If set to true, the cloned bundle will be in the DRAFT state, else it will be twilio-approved
     * @param string $friendlyName The string that you assigned to describe the cloned bundle.
     * @return CreateBundleCloneOptions Options builder
     */
    public static function create(
        
        bool $moveToDraft = Values::BOOL_NONE,
        string $friendlyName = Values::NONE

    ): CreateBundleCloneOptions
    {
        return new CreateBundleCloneOptions(
            $moveToDraft,
            $friendlyName
        );
    }

}

class CreateBundleCloneOptions extends Options
    {
    /**
     * @param bool $moveToDraft If set to true, the cloned bundle will be in the DRAFT state, else it will be twilio-approved
     * @param string $friendlyName The string that you assigned to describe the cloned bundle.
     */
    public function __construct(
        
        bool $moveToDraft = Values::BOOL_NONE,
        string $friendlyName = Values::NONE

    ) {
        $this->options['moveToDraft'] = $moveToDraft;
        $this->options['friendlyName'] = $friendlyName;
    }

    /**
     * If set to true, the cloned bundle will be in the DRAFT state, else it will be twilio-approved
     *
     * @param bool $moveToDraft If set to true, the cloned bundle will be in the DRAFT state, else it will be twilio-approved
     * @return $this Fluent Builder
     */
    public function setMoveToDraft(bool $moveToDraft): self
    {
        $this->options['moveToDraft'] = $moveToDraft;
        return $this;
    }

    /**
     * The string that you assigned to describe the cloned bundle.
     *
     * @param string $friendlyName The string that you assigned to describe the cloned bundle.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Numbers.V2.CreateBundleCloneOptions ' . $options . ']';
    }
}

