<?php
// This file was auto-generated from sdk-root/src/data/location/2020-11-19/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-11-19', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'geo', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Amazon Location Service', 'serviceId' => 'Location', 'signatureVersion' => 'v4', 'signingName' => 'geo', 'uid' => 'location-2020-11-19', ], 'operations' => [ 'AssociateTrackerConsumer' => [ 'name' => 'AssociateTrackerConsumer', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/consumers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateTrackerConsumerRequest', ], 'output' => [ 'shape' => 'AssociateTrackerConsumerResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.tracking.', ], ], 'BatchDeleteDevicePositionHistory' => [ 'name' => 'BatchDeleteDevicePositionHistory', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/delete-positions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchDeleteDevicePositionHistoryRequest', ], 'output' => [ 'shape' => 'BatchDeleteDevicePositionHistoryResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], 'BatchDeleteGeofence' => [ 'name' => 'BatchDeleteGeofence', 'http' => [ 'method' => 'POST', 'requestUri' => '/geofencing/v0/collections/{CollectionName}/delete-geofences', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchDeleteGeofenceRequest', ], 'output' => [ 'shape' => 'BatchDeleteGeofenceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], ], 'BatchEvaluateGeofences' => [ 'name' => 'BatchEvaluateGeofences', 'http' => [ 'method' => 'POST', 'requestUri' => '/geofencing/v0/collections/{CollectionName}/positions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchEvaluateGeofencesRequest', ], 'output' => [ 'shape' => 'BatchEvaluateGeofencesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], ], 'BatchGetDevicePosition' => [ 'name' => 'BatchGetDevicePosition', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/get-positions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchGetDevicePositionRequest', ], 'output' => [ 'shape' => 'BatchGetDevicePositionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], 'BatchPutGeofence' => [ 'name' => 'BatchPutGeofence', 'http' => [ 'method' => 'POST', 'requestUri' => '/geofencing/v0/collections/{CollectionName}/put-geofences', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchPutGeofenceRequest', ], 'output' => [ 'shape' => 'BatchPutGeofenceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], ], 'BatchUpdateDevicePosition' => [ 'name' => 'BatchUpdateDevicePosition', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/positions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchUpdateDevicePositionRequest', ], 'output' => [ 'shape' => 'BatchUpdateDevicePositionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], 'CalculateRoute' => [ 'name' => 'CalculateRoute', 'http' => [ 'method' => 'POST', 'requestUri' => '/routes/v0/calculators/{CalculatorName}/calculate/route', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CalculateRouteRequest', ], 'output' => [ 'shape' => 'CalculateRouteResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'routes.', ], ], 'CalculateRouteMatrix' => [ 'name' => 'CalculateRouteMatrix', 'http' => [ 'method' => 'POST', 'requestUri' => '/routes/v0/calculators/{CalculatorName}/calculate/route-matrix', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CalculateRouteMatrixRequest', ], 'output' => [ 'shape' => 'CalculateRouteMatrixResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'routes.', ], ], 'CreateGeofenceCollection' => [ 'name' => 'CreateGeofenceCollection', 'http' => [ 'method' => 'POST', 'requestUri' => '/geofencing/v0/collections', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateGeofenceCollectionRequest', ], 'output' => [ 'shape' => 'CreateGeofenceCollectionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.geofencing.', ], 'idempotent' => true, ], 'CreateKey' => [ 'name' => 'CreateKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/metadata/v0/keys', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateKeyRequest', ], 'output' => [ 'shape' => 'CreateKeyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.metadata.', ], 'idempotent' => true, ], 'CreateMap' => [ 'name' => 'CreateMap', 'http' => [ 'method' => 'POST', 'requestUri' => '/maps/v0/maps', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMapRequest', ], 'output' => [ 'shape' => 'CreateMapResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.maps.', ], 'idempotent' => true, ], 'CreatePlaceIndex' => [ 'name' => 'CreatePlaceIndex', 'http' => [ 'method' => 'POST', 'requestUri' => '/places/v0/indexes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreatePlaceIndexRequest', ], 'output' => [ 'shape' => 'CreatePlaceIndexResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.places.', ], 'idempotent' => true, ], 'CreateRouteCalculator' => [ 'name' => 'CreateRouteCalculator', 'http' => [ 'method' => 'POST', 'requestUri' => '/routes/v0/calculators', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateRouteCalculatorRequest', ], 'output' => [ 'shape' => 'CreateRouteCalculatorResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.routes.', ], 'idempotent' => true, ], 'CreateTracker' => [ 'name' => 'CreateTracker', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateTrackerRequest', ], 'output' => [ 'shape' => 'CreateTrackerResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.tracking.', ], 'idempotent' => true, ], 'DeleteGeofenceCollection' => [ 'name' => 'DeleteGeofenceCollection', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/geofencing/v0/collections/{CollectionName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteGeofenceCollectionRequest', ], 'output' => [ 'shape' => 'DeleteGeofenceCollectionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.geofencing.', ], 'idempotent' => true, ], 'DeleteKey' => [ 'name' => 'DeleteKey', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/metadata/v0/keys/{KeyName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteKeyRequest', ], 'output' => [ 'shape' => 'DeleteKeyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.metadata.', ], 'idempotent' => true, ], 'DeleteMap' => [ 'name' => 'DeleteMap', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/maps/v0/maps/{MapName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteMapRequest', ], 'output' => [ 'shape' => 'DeleteMapResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.maps.', ], 'idempotent' => true, ], 'DeletePlaceIndex' => [ 'name' => 'DeletePlaceIndex', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/places/v0/indexes/{IndexName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeletePlaceIndexRequest', ], 'output' => [ 'shape' => 'DeletePlaceIndexResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.places.', ], 'idempotent' => true, ], 'DeleteRouteCalculator' => [ 'name' => 'DeleteRouteCalculator', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/routes/v0/calculators/{CalculatorName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteRouteCalculatorRequest', ], 'output' => [ 'shape' => 'DeleteRouteCalculatorResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.routes.', ], 'idempotent' => true, ], 'DeleteTracker' => [ 'name' => 'DeleteTracker', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tracking/v0/trackers/{TrackerName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteTrackerRequest', ], 'output' => [ 'shape' => 'DeleteTrackerResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.tracking.', ], 'idempotent' => true, ], 'DescribeGeofenceCollection' => [ 'name' => 'DescribeGeofenceCollection', 'http' => [ 'method' => 'GET', 'requestUri' => '/geofencing/v0/collections/{CollectionName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeGeofenceCollectionRequest', ], 'output' => [ 'shape' => 'DescribeGeofenceCollectionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.geofencing.', ], ], 'DescribeKey' => [ 'name' => 'DescribeKey', 'http' => [ 'method' => 'GET', 'requestUri' => '/metadata/v0/keys/{KeyName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeKeyRequest', ], 'output' => [ 'shape' => 'DescribeKeyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.metadata.', ], ], 'DescribeMap' => [ 'name' => 'DescribeMap', 'http' => [ 'method' => 'GET', 'requestUri' => '/maps/v0/maps/{MapName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeMapRequest', ], 'output' => [ 'shape' => 'DescribeMapResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.maps.', ], ], 'DescribePlaceIndex' => [ 'name' => 'DescribePlaceIndex', 'http' => [ 'method' => 'GET', 'requestUri' => '/places/v0/indexes/{IndexName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribePlaceIndexRequest', ], 'output' => [ 'shape' => 'DescribePlaceIndexResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.places.', ], ], 'DescribeRouteCalculator' => [ 'name' => 'DescribeRouteCalculator', 'http' => [ 'method' => 'GET', 'requestUri' => '/routes/v0/calculators/{CalculatorName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeRouteCalculatorRequest', ], 'output' => [ 'shape' => 'DescribeRouteCalculatorResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.routes.', ], ], 'DescribeTracker' => [ 'name' => 'DescribeTracker', 'http' => [ 'method' => 'GET', 'requestUri' => '/tracking/v0/trackers/{TrackerName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeTrackerRequest', ], 'output' => [ 'shape' => 'DescribeTrackerResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.tracking.', ], ], 'DisassociateTrackerConsumer' => [ 'name' => 'DisassociateTrackerConsumer', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/consumers/{ConsumerArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateTrackerConsumerRequest', ], 'output' => [ 'shape' => 'DisassociateTrackerConsumerResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.tracking.', ], ], 'ForecastGeofenceEvents' => [ 'name' => 'ForecastGeofenceEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/geofencing/v0/collections/{CollectionName}/forecast-geofence-events', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ForecastGeofenceEventsRequest', ], 'output' => [ 'shape' => 'ForecastGeofenceEventsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], ], 'GetDevicePosition' => [ 'name' => 'GetDevicePosition', 'http' => [ 'method' => 'GET', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/devices/{DeviceId}/positions/latest', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDevicePositionRequest', ], 'output' => [ 'shape' => 'GetDevicePositionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], 'GetDevicePositionHistory' => [ 'name' => 'GetDevicePositionHistory', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/devices/{DeviceId}/list-positions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDevicePositionHistoryRequest', ], 'output' => [ 'shape' => 'GetDevicePositionHistoryResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], 'GetGeofence' => [ 'name' => 'GetGeofence', 'http' => [ 'method' => 'GET', 'requestUri' => '/geofencing/v0/collections/{CollectionName}/geofences/{GeofenceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetGeofenceRequest', ], 'output' => [ 'shape' => 'GetGeofenceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], ], 'GetMapGlyphs' => [ 'name' => 'GetMapGlyphs', 'http' => [ 'method' => 'GET', 'requestUri' => '/maps/v0/maps/{MapName}/glyphs/{FontStack}/{FontUnicodeRange}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMapGlyphsRequest', ], 'output' => [ 'shape' => 'GetMapGlyphsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'maps.', ], ], 'GetMapSprites' => [ 'name' => 'GetMapSprites', 'http' => [ 'method' => 'GET', 'requestUri' => '/maps/v0/maps/{MapName}/sprites/{FileName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMapSpritesRequest', ], 'output' => [ 'shape' => 'GetMapSpritesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'maps.', ], ], 'GetMapStyleDescriptor' => [ 'name' => 'GetMapStyleDescriptor', 'http' => [ 'method' => 'GET', 'requestUri' => '/maps/v0/maps/{MapName}/style-descriptor', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMapStyleDescriptorRequest', ], 'output' => [ 'shape' => 'GetMapStyleDescriptorResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'maps.', ], ], 'GetMapTile' => [ 'name' => 'GetMapTile', 'http' => [ 'method' => 'GET', 'requestUri' => '/maps/v0/maps/{MapName}/tiles/{Z}/{X}/{Y}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMapTileRequest', ], 'output' => [ 'shape' => 'GetMapTileResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'maps.', ], ], 'GetPlace' => [ 'name' => 'GetPlace', 'http' => [ 'method' => 'GET', 'requestUri' => '/places/v0/indexes/{IndexName}/places/{PlaceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPlaceRequest', ], 'output' => [ 'shape' => 'GetPlaceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'places.', ], ], 'ListDevicePositions' => [ 'name' => 'ListDevicePositions', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/list-positions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDevicePositionsRequest', ], 'output' => [ 'shape' => 'ListDevicePositionsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], 'ListGeofenceCollections' => [ 'name' => 'ListGeofenceCollections', 'http' => [ 'method' => 'POST', 'requestUri' => '/geofencing/v0/list-collections', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListGeofenceCollectionsRequest', ], 'output' => [ 'shape' => 'ListGeofenceCollectionsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.geofencing.', ], ], 'ListGeofences' => [ 'name' => 'ListGeofences', 'http' => [ 'method' => 'POST', 'requestUri' => '/geofencing/v0/collections/{CollectionName}/list-geofences', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListGeofencesRequest', ], 'output' => [ 'shape' => 'ListGeofencesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], ], 'ListKeys' => [ 'name' => 'ListKeys', 'http' => [ 'method' => 'POST', 'requestUri' => '/metadata/v0/list-keys', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListKeysRequest', ], 'output' => [ 'shape' => 'ListKeysResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.metadata.', ], ], 'ListMaps' => [ 'name' => 'ListMaps', 'http' => [ 'method' => 'POST', 'requestUri' => '/maps/v0/list-maps', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMapsRequest', ], 'output' => [ 'shape' => 'ListMapsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.maps.', ], ], 'ListPlaceIndexes' => [ 'name' => 'ListPlaceIndexes', 'http' => [ 'method' => 'POST', 'requestUri' => '/places/v0/list-indexes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPlaceIndexesRequest', ], 'output' => [ 'shape' => 'ListPlaceIndexesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.places.', ], ], 'ListRouteCalculators' => [ 'name' => 'ListRouteCalculators', 'http' => [ 'method' => 'POST', 'requestUri' => '/routes/v0/list-calculators', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRouteCalculatorsRequest', ], 'output' => [ 'shape' => 'ListRouteCalculatorsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.routes.', ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.metadata.', ], ], 'ListTrackerConsumers' => [ 'name' => 'ListTrackerConsumers', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/list-consumers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTrackerConsumersRequest', ], 'output' => [ 'shape' => 'ListTrackerConsumersResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.tracking.', ], ], 'ListTrackers' => [ 'name' => 'ListTrackers', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/list-trackers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTrackersRequest', ], 'output' => [ 'shape' => 'ListTrackersResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.tracking.', ], ], 'PutGeofence' => [ 'name' => 'PutGeofence', 'http' => [ 'method' => 'PUT', 'requestUri' => '/geofencing/v0/collections/{CollectionName}/geofences/{GeofenceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutGeofenceRequest', ], 'output' => [ 'shape' => 'PutGeofenceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], ], 'SearchPlaceIndexForPosition' => [ 'name' => 'SearchPlaceIndexForPosition', 'http' => [ 'method' => 'POST', 'requestUri' => '/places/v0/indexes/{IndexName}/search/position', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchPlaceIndexForPositionRequest', ], 'output' => [ 'shape' => 'SearchPlaceIndexForPositionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'places.', ], ], 'SearchPlaceIndexForSuggestions' => [ 'name' => 'SearchPlaceIndexForSuggestions', 'http' => [ 'method' => 'POST', 'requestUri' => '/places/v0/indexes/{IndexName}/search/suggestions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchPlaceIndexForSuggestionsRequest', ], 'output' => [ 'shape' => 'SearchPlaceIndexForSuggestionsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'places.', ], ], 'SearchPlaceIndexForText' => [ 'name' => 'SearchPlaceIndexForText', 'http' => [ 'method' => 'POST', 'requestUri' => '/places/v0/indexes/{IndexName}/search/text', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchPlaceIndexForTextRequest', ], 'output' => [ 'shape' => 'SearchPlaceIndexForTextResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'places.', ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.metadata.', ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.metadata.', ], 'idempotent' => true, ], 'UpdateGeofenceCollection' => [ 'name' => 'UpdateGeofenceCollection', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/geofencing/v0/collections/{CollectionName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateGeofenceCollectionRequest', ], 'output' => [ 'shape' => 'UpdateGeofenceCollectionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.geofencing.', ], 'idempotent' => true, ], 'UpdateKey' => [ 'name' => 'UpdateKey', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/metadata/v0/keys/{KeyName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateKeyRequest', ], 'output' => [ 'shape' => 'UpdateKeyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.metadata.', ], 'idempotent' => true, ], 'UpdateMap' => [ 'name' => 'UpdateMap', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/maps/v0/maps/{MapName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMapRequest', ], 'output' => [ 'shape' => 'UpdateMapResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.maps.', ], 'idempotent' => true, ], 'UpdatePlaceIndex' => [ 'name' => 'UpdatePlaceIndex', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/places/v0/indexes/{IndexName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdatePlaceIndexRequest', ], 'output' => [ 'shape' => 'UpdatePlaceIndexResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.places.', ], 'idempotent' => true, ], 'UpdateRouteCalculator' => [ 'name' => 'UpdateRouteCalculator', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/routes/v0/calculators/{CalculatorName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateRouteCalculatorRequest', ], 'output' => [ 'shape' => 'UpdateRouteCalculatorResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.routes.', ], 'idempotent' => true, ], 'UpdateTracker' => [ 'name' => 'UpdateTracker', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/tracking/v0/trackers/{TrackerName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateTrackerRequest', ], 'output' => [ 'shape' => 'UpdateTrackerResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'cp.tracking.', ], 'idempotent' => true, ], 'VerifyDevicePosition' => [ 'name' => 'VerifyDevicePosition', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/positions/verify', 'responseCode' => 200, ], 'input' => [ 'shape' => 'VerifyDevicePositionRequest', ], 'output' => [ 'shape' => 'VerifyDevicePositionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'ApiKey' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'sensitive' => true, ], 'ApiKeyAction' => [ 'type' => 'string', 'max' => 200, 'min' => 5, 'pattern' => '(geo|geo-routes|geo-places|geo-maps):\\w*\\*?', ], 'ApiKeyFilter' => [ 'type' => 'structure', 'members' => [ 'KeyStatus' => [ 'shape' => 'Status', ], ], ], 'ApiKeyRestrictions' => [ 'type' => 'structure', 'required' => [ 'AllowActions', 'AllowResources', ], 'members' => [ 'AllowActions' => [ 'shape' => 'ApiKeyRestrictionsAllowActionsList', ], 'AllowResources' => [ 'shape' => 'ApiKeyRestrictionsAllowResourcesList', ], 'AllowReferers' => [ 'shape' => 'ApiKeyRestrictionsAllowReferersList', ], ], ], 'ApiKeyRestrictionsAllowActionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApiKeyAction', ], 'max' => 24, 'min' => 1, ], 'ApiKeyRestrictionsAllowReferersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RefererPattern', ], 'max' => 5, 'min' => 1, ], 'ApiKeyRestrictionsAllowResourcesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GeoArnV2', ], 'max' => 8, 'min' => 1, ], 'Arn' => [ 'type' => 'string', 'max' => 1600, 'min' => 0, 'pattern' => 'arn(:[a-z0-9]+([.-][a-z0-9]+)*){2}(:([a-z0-9]+([.-][a-z0-9]+)*)?){2}:([^/].*)?', ], 'ArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], ], 'AssociateTrackerConsumerRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', 'ConsumerArn', ], 'members' => [ 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], 'ConsumerArn' => [ 'shape' => 'Arn', ], ], ], 'AssociateTrackerConsumerResponse' => [ 'type' => 'structure', 'members' => [], ], 'Base64EncodedGeobuf' => [ 'type' => 'blob', 'max' => 600000, 'min' => 0, 'sensitive' => true, ], 'BatchDeleteDevicePositionHistoryError' => [ 'type' => 'structure', 'required' => [ 'DeviceId', 'Error', ], 'members' => [ 'DeviceId' => [ 'shape' => 'Id', ], 'Error' => [ 'shape' => 'BatchItemError', ], ], ], 'BatchDeleteDevicePositionHistoryErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchDeleteDevicePositionHistoryError', ], ], 'BatchDeleteDevicePositionHistoryRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', 'DeviceIds', ], 'members' => [ 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], 'DeviceIds' => [ 'shape' => 'BatchDeleteDevicePositionHistoryRequestDeviceIdsList', ], ], ], 'BatchDeleteDevicePositionHistoryRequestDeviceIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Id', ], 'max' => 100, 'min' => 1, ], 'BatchDeleteDevicePositionHistoryResponse' => [ 'type' => 'structure', 'required' => [ 'Errors', ], 'members' => [ 'Errors' => [ 'shape' => 'BatchDeleteDevicePositionHistoryErrorList', ], ], ], 'BatchDeleteGeofenceError' => [ 'type' => 'structure', 'required' => [ 'GeofenceId', 'Error', ], 'members' => [ 'GeofenceId' => [ 'shape' => 'Id', ], 'Error' => [ 'shape' => 'BatchItemError', ], ], ], 'BatchDeleteGeofenceErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchDeleteGeofenceError', ], ], 'BatchDeleteGeofenceRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', 'GeofenceIds', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], 'GeofenceIds' => [ 'shape' => 'BatchDeleteGeofenceRequestGeofenceIdsList', ], ], ], 'BatchDeleteGeofenceRequestGeofenceIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Id', ], 'max' => 10, 'min' => 1, ], 'BatchDeleteGeofenceResponse' => [ 'type' => 'structure', 'required' => [ 'Errors', ], 'members' => [ 'Errors' => [ 'shape' => 'BatchDeleteGeofenceErrorList', ], ], ], 'BatchEvaluateGeofencesError' => [ 'type' => 'structure', 'required' => [ 'DeviceId', 'SampleTime', 'Error', ], 'members' => [ 'DeviceId' => [ 'shape' => 'Id', ], 'SampleTime' => [ 'shape' => 'Timestamp', ], 'Error' => [ 'shape' => 'BatchItemError', ], ], ], 'BatchEvaluateGeofencesErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchEvaluateGeofencesError', ], ], 'BatchEvaluateGeofencesRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', 'DevicePositionUpdates', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], 'DevicePositionUpdates' => [ 'shape' => 'BatchEvaluateGeofencesRequestDevicePositionUpdatesList', ], ], ], 'BatchEvaluateGeofencesRequestDevicePositionUpdatesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DevicePositionUpdate', ], 'max' => 10, 'min' => 1, ], 'BatchEvaluateGeofencesResponse' => [ 'type' => 'structure', 'required' => [ 'Errors', ], 'members' => [ 'Errors' => [ 'shape' => 'BatchEvaluateGeofencesErrorList', ], ], ], 'BatchGetDevicePositionError' => [ 'type' => 'structure', 'required' => [ 'DeviceId', 'Error', ], 'members' => [ 'DeviceId' => [ 'shape' => 'Id', ], 'Error' => [ 'shape' => 'BatchItemError', ], ], ], 'BatchGetDevicePositionErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetDevicePositionError', ], ], 'BatchGetDevicePositionRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', 'DeviceIds', ], 'members' => [ 'TrackerName' => [ 'shape' => 'BatchGetDevicePositionRequestTrackerNameString', 'location' => 'uri', 'locationName' => 'TrackerName', ], 'DeviceIds' => [ 'shape' => 'BatchGetDevicePositionRequestDeviceIdsList', ], ], ], 'BatchGetDevicePositionRequestDeviceIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Id', ], 'max' => 10, 'min' => 1, ], 'BatchGetDevicePositionRequestTrackerNameString' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[-._\\w]+', ], 'BatchGetDevicePositionResponse' => [ 'type' => 'structure', 'required' => [ 'Errors', 'DevicePositions', ], 'members' => [ 'Errors' => [ 'shape' => 'BatchGetDevicePositionErrorList', ], 'DevicePositions' => [ 'shape' => 'DevicePositionList', ], ], ], 'BatchItemError' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'BatchItemErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], ], 'BatchItemErrorCode' => [ 'type' => 'string', 'enum' => [ 'AccessDeniedError', 'ConflictError', 'InternalServerError', 'ResourceNotFoundError', 'ThrottlingError', 'ValidationError', ], ], 'BatchPutGeofenceError' => [ 'type' => 'structure', 'required' => [ 'GeofenceId', 'Error', ], 'members' => [ 'GeofenceId' => [ 'shape' => 'Id', ], 'Error' => [ 'shape' => 'BatchItemError', ], ], ], 'BatchPutGeofenceErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPutGeofenceError', ], ], 'BatchPutGeofenceRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', 'Entries', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], 'Entries' => [ 'shape' => 'BatchPutGeofenceRequestEntriesList', ], ], ], 'BatchPutGeofenceRequestEntriesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPutGeofenceRequestEntry', ], 'max' => 10, 'min' => 1, ], 'BatchPutGeofenceRequestEntry' => [ 'type' => 'structure', 'required' => [ 'GeofenceId', 'Geometry', ], 'members' => [ 'GeofenceId' => [ 'shape' => 'Id', ], 'Geometry' => [ 'shape' => 'GeofenceGeometry', ], 'GeofenceProperties' => [ 'shape' => 'PropertyMap', ], ], ], 'BatchPutGeofenceResponse' => [ 'type' => 'structure', 'required' => [ 'Successes', 'Errors', ], 'members' => [ 'Successes' => [ 'shape' => 'BatchPutGeofenceSuccessList', ], 'Errors' => [ 'shape' => 'BatchPutGeofenceErrorList', ], ], ], 'BatchPutGeofenceSuccess' => [ 'type' => 'structure', 'required' => [ 'GeofenceId', 'CreateTime', 'UpdateTime', ], 'members' => [ 'GeofenceId' => [ 'shape' => 'Id', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'BatchPutGeofenceSuccessList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPutGeofenceSuccess', ], ], 'BatchUpdateDevicePositionError' => [ 'type' => 'structure', 'required' => [ 'DeviceId', 'SampleTime', 'Error', ], 'members' => [ 'DeviceId' => [ 'shape' => 'Id', ], 'SampleTime' => [ 'shape' => 'Timestamp', ], 'Error' => [ 'shape' => 'BatchItemError', ], ], ], 'BatchUpdateDevicePositionErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchUpdateDevicePositionError', ], ], 'BatchUpdateDevicePositionRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', 'Updates', ], 'members' => [ 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], 'Updates' => [ 'shape' => 'BatchUpdateDevicePositionRequestUpdatesList', ], ], ], 'BatchUpdateDevicePositionRequestUpdatesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DevicePositionUpdate', ], 'max' => 10, 'min' => 1, ], 'BatchUpdateDevicePositionResponse' => [ 'type' => 'structure', 'required' => [ 'Errors', ], 'members' => [ 'Errors' => [ 'shape' => 'BatchUpdateDevicePositionErrorList', ], ], ], 'Blob' => [ 'type' => 'blob', ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'BoundingBox' => [ 'type' => 'list', 'member' => [ 'shape' => 'Double', ], 'max' => 4, 'min' => 4, 'sensitive' => true, ], 'CalculateRouteCarModeOptions' => [ 'type' => 'structure', 'members' => [ 'AvoidFerries' => [ 'shape' => 'Boolean', ], 'AvoidTolls' => [ 'shape' => 'Boolean', ], ], ], 'CalculateRouteMatrixRequest' => [ 'type' => 'structure', 'required' => [ 'CalculatorName', 'DeparturePositions', 'DestinationPositions', ], 'members' => [ 'CalculatorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CalculatorName', ], 'DeparturePositions' => [ 'shape' => 'CalculateRouteMatrixRequestDeparturePositionsList', ], 'DestinationPositions' => [ 'shape' => 'CalculateRouteMatrixRequestDestinationPositionsList', ], 'TravelMode' => [ 'shape' => 'TravelMode', ], 'DepartureTime' => [ 'shape' => 'Timestamp', ], 'DepartNow' => [ 'shape' => 'Boolean', ], 'DistanceUnit' => [ 'shape' => 'DistanceUnit', ], 'CarModeOptions' => [ 'shape' => 'CalculateRouteCarModeOptions', ], 'TruckModeOptions' => [ 'shape' => 'CalculateRouteTruckModeOptions', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], ], ], 'CalculateRouteMatrixRequestDeparturePositionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Position', ], 'max' => 350, 'min' => 1, ], 'CalculateRouteMatrixRequestDestinationPositionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Position', ], 'max' => 350, 'min' => 1, ], 'CalculateRouteMatrixResponse' => [ 'type' => 'structure', 'required' => [ 'RouteMatrix', 'Summary', ], 'members' => [ 'RouteMatrix' => [ 'shape' => 'RouteMatrix', ], 'SnappedDeparturePositions' => [ 'shape' => 'CalculateRouteMatrixResponseSnappedDeparturePositionsList', ], 'SnappedDestinationPositions' => [ 'shape' => 'CalculateRouteMatrixResponseSnappedDestinationPositionsList', ], 'Summary' => [ 'shape' => 'CalculateRouteMatrixSummary', ], ], ], 'CalculateRouteMatrixResponseSnappedDeparturePositionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Position', ], 'max' => 350, 'min' => 1, ], 'CalculateRouteMatrixResponseSnappedDestinationPositionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Position', ], 'max' => 350, 'min' => 1, ], 'CalculateRouteMatrixSummary' => [ 'type' => 'structure', 'required' => [ 'DataSource', 'RouteCount', 'ErrorCount', 'DistanceUnit', ], 'members' => [ 'DataSource' => [ 'shape' => 'String', ], 'RouteCount' => [ 'shape' => 'CalculateRouteMatrixSummaryRouteCountInteger', ], 'ErrorCount' => [ 'shape' => 'CalculateRouteMatrixSummaryErrorCountInteger', ], 'DistanceUnit' => [ 'shape' => 'DistanceUnit', ], ], ], 'CalculateRouteMatrixSummaryErrorCountInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 160000, 'min' => 1, ], 'CalculateRouteMatrixSummaryRouteCountInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 160000, 'min' => 1, ], 'CalculateRouteRequest' => [ 'type' => 'structure', 'required' => [ 'CalculatorName', 'DeparturePosition', 'DestinationPosition', ], 'members' => [ 'CalculatorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CalculatorName', ], 'DeparturePosition' => [ 'shape' => 'Position', ], 'DestinationPosition' => [ 'shape' => 'Position', ], 'WaypointPositions' => [ 'shape' => 'CalculateRouteRequestWaypointPositionsList', ], 'TravelMode' => [ 'shape' => 'TravelMode', ], 'DepartureTime' => [ 'shape' => 'Timestamp', ], 'DepartNow' => [ 'shape' => 'Boolean', ], 'DistanceUnit' => [ 'shape' => 'DistanceUnit', ], 'IncludeLegGeometry' => [ 'shape' => 'Boolean', ], 'CarModeOptions' => [ 'shape' => 'CalculateRouteCarModeOptions', ], 'TruckModeOptions' => [ 'shape' => 'CalculateRouteTruckModeOptions', ], 'ArrivalTime' => [ 'shape' => 'Timestamp', ], 'OptimizeFor' => [ 'shape' => 'OptimizationMode', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], ], ], 'CalculateRouteRequestWaypointPositionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Position', ], 'max' => 23, 'min' => 0, ], 'CalculateRouteResponse' => [ 'type' => 'structure', 'required' => [ 'Legs', 'Summary', ], 'members' => [ 'Legs' => [ 'shape' => 'LegList', ], 'Summary' => [ 'shape' => 'CalculateRouteSummary', ], ], ], 'CalculateRouteSummary' => [ 'type' => 'structure', 'required' => [ 'RouteBBox', 'DataSource', 'Distance', 'DurationSeconds', 'DistanceUnit', ], 'members' => [ 'RouteBBox' => [ 'shape' => 'BoundingBox', ], 'DataSource' => [ 'shape' => 'String', ], 'Distance' => [ 'shape' => 'CalculateRouteSummaryDistanceDouble', ], 'DurationSeconds' => [ 'shape' => 'CalculateRouteSummaryDurationSecondsDouble', ], 'DistanceUnit' => [ 'shape' => 'DistanceUnit', ], ], ], 'CalculateRouteSummaryDistanceDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'CalculateRouteSummaryDurationSecondsDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'CalculateRouteTruckModeOptions' => [ 'type' => 'structure', 'members' => [ 'AvoidFerries' => [ 'shape' => 'Boolean', ], 'AvoidTolls' => [ 'shape' => 'Boolean', ], 'Dimensions' => [ 'shape' => 'TruckDimensions', ], 'Weight' => [ 'shape' => 'TruckWeight', ], ], ], 'CellSignals' => [ 'type' => 'structure', 'required' => [ 'LteCellDetails', ], 'members' => [ 'LteCellDetails' => [ 'shape' => 'CellSignalsLteCellDetailsList', ], ], ], 'CellSignalsLteCellDetailsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LteCellDetails', ], 'max' => 16, 'min' => 1, ], 'Circle' => [ 'type' => 'structure', 'required' => [ 'Center', 'Radius', ], 'members' => [ 'Center' => [ 'shape' => 'Position', ], 'Radius' => [ 'shape' => 'Double', ], ], 'sensitive' => true, ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CountryCode3' => [ 'type' => 'string', 'max' => 3, 'min' => 3, 'pattern' => '[A-Z]{3}', ], 'CountryCode3OrEmpty' => [ 'type' => 'string', 'max' => 3, 'min' => 0, 'pattern' => '[A-Z]{3}$|^', ], 'CountryCodeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CountryCode3', ], 'max' => 100, 'min' => 1, ], 'CreateGeofenceCollectionRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. If included, the only allowed value is RequestBasedUsage.', 'deprecatedSince' => '2022-02-01', ], 'PricingPlanDataSource' => [ 'shape' => 'String', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. No longer allowed.', 'deprecatedSince' => '2022-02-01', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'Tags' => [ 'shape' => 'TagMap', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'CreateGeofenceCollectionResponse' => [ 'type' => 'structure', 'required' => [ 'CollectionName', 'CollectionArn', 'CreateTime', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', ], 'CollectionArn' => [ 'shape' => 'Arn', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], ], ], 'CreateKeyRequest' => [ 'type' => 'structure', 'required' => [ 'KeyName', 'Restrictions', ], 'members' => [ 'KeyName' => [ 'shape' => 'ResourceName', ], 'Restrictions' => [ 'shape' => 'ApiKeyRestrictions', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'ExpireTime' => [ 'shape' => 'Timestamp', ], 'NoExpiry' => [ 'shape' => 'Boolean', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateKeyResponse' => [ 'type' => 'structure', 'required' => [ 'Key', 'KeyArn', 'KeyName', 'CreateTime', ], 'members' => [ 'Key' => [ 'shape' => 'ApiKey', ], 'KeyArn' => [ 'shape' => 'Arn', ], 'KeyName' => [ 'shape' => 'ResourceName', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], ], ], 'CreateMapRequest' => [ 'type' => 'structure', 'required' => [ 'MapName', 'Configuration', ], 'members' => [ 'MapName' => [ 'shape' => 'ResourceName', ], 'Configuration' => [ 'shape' => 'MapConfiguration', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. If included, the only allowed value is RequestBasedUsage.', 'deprecatedSince' => '2022-02-01', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateMapResponse' => [ 'type' => 'structure', 'required' => [ 'MapName', 'MapArn', 'CreateTime', ], 'members' => [ 'MapName' => [ 'shape' => 'ResourceName', ], 'MapArn' => [ 'shape' => 'GeoArn', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], ], ], 'CreatePlaceIndexRequest' => [ 'type' => 'structure', 'required' => [ 'IndexName', 'DataSource', ], 'members' => [ 'IndexName' => [ 'shape' => 'ResourceName', ], 'DataSource' => [ 'shape' => 'String', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. If included, the only allowed value is RequestBasedUsage.', 'deprecatedSince' => '2022-02-01', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'DataSourceConfiguration' => [ 'shape' => 'DataSourceConfiguration', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreatePlaceIndexResponse' => [ 'type' => 'structure', 'required' => [ 'IndexName', 'IndexArn', 'CreateTime', ], 'members' => [ 'IndexName' => [ 'shape' => 'ResourceName', ], 'IndexArn' => [ 'shape' => 'GeoArn', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], ], ], 'CreateRouteCalculatorRequest' => [ 'type' => 'structure', 'required' => [ 'CalculatorName', 'DataSource', ], 'members' => [ 'CalculatorName' => [ 'shape' => 'ResourceName', ], 'DataSource' => [ 'shape' => 'String', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. If included, the only allowed value is RequestBasedUsage.', 'deprecatedSince' => '2022-02-01', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateRouteCalculatorResponse' => [ 'type' => 'structure', 'required' => [ 'CalculatorName', 'CalculatorArn', 'CreateTime', ], 'members' => [ 'CalculatorName' => [ 'shape' => 'ResourceName', ], 'CalculatorArn' => [ 'shape' => 'GeoArn', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], ], ], 'CreateTrackerRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', ], 'members' => [ 'TrackerName' => [ 'shape' => 'ResourceName', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. If included, the only allowed value is RequestBasedUsage.', 'deprecatedSince' => '2022-02-01', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'PricingPlanDataSource' => [ 'shape' => 'String', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. No longer allowed.', 'deprecatedSince' => '2022-02-01', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'Tags' => [ 'shape' => 'TagMap', ], 'PositionFiltering' => [ 'shape' => 'PositionFiltering', ], 'EventBridgeEnabled' => [ 'shape' => 'Boolean', ], 'KmsKeyEnableGeospatialQueries' => [ 'shape' => 'Boolean', ], ], ], 'CreateTrackerResponse' => [ 'type' => 'structure', 'required' => [ 'TrackerName', 'TrackerArn', 'CreateTime', ], 'members' => [ 'TrackerName' => [ 'shape' => 'ResourceName', ], 'TrackerArn' => [ 'shape' => 'Arn', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], ], ], 'CustomLayer' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[-._\\w]+', ], 'CustomLayerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomLayer', ], 'max' => 10, 'min' => 0, ], 'DataSourceConfiguration' => [ 'type' => 'structure', 'members' => [ 'IntendedUse' => [ 'shape' => 'IntendedUse', ], ], ], 'DeleteGeofenceCollectionRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], ], ], 'DeleteGeofenceCollectionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteKeyRequest' => [ 'type' => 'structure', 'required' => [ 'KeyName', ], 'members' => [ 'KeyName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'KeyName', ], 'ForceDelete' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'forceDelete', ], ], ], 'DeleteKeyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteMapRequest' => [ 'type' => 'structure', 'required' => [ 'MapName', ], 'members' => [ 'MapName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'MapName', ], ], ], 'DeleteMapResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePlaceIndexRequest' => [ 'type' => 'structure', 'required' => [ 'IndexName', ], 'members' => [ 'IndexName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'IndexName', ], ], ], 'DeletePlaceIndexResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRouteCalculatorRequest' => [ 'type' => 'structure', 'required' => [ 'CalculatorName', ], 'members' => [ 'CalculatorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CalculatorName', ], ], ], 'DeleteRouteCalculatorResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTrackerRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', ], 'members' => [ 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'DeleteTrackerResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeGeofenceCollectionRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], ], ], 'DescribeGeofenceCollectionResponse' => [ 'type' => 'structure', 'required' => [ 'CollectionName', 'CollectionArn', 'Description', 'CreateTime', 'UpdateTime', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', ], 'CollectionArn' => [ 'shape' => 'Arn', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Always returns RequestBasedUsage.', 'deprecatedSince' => '2022-02-01', ], 'PricingPlanDataSource' => [ 'shape' => 'String', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Unused.', 'deprecatedSince' => '2022-02-01', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'Tags' => [ 'shape' => 'TagMap', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], 'GeofenceCount' => [ 'shape' => 'DescribeGeofenceCollectionResponseGeofenceCountInteger', ], ], ], 'DescribeGeofenceCollectionResponseGeofenceCountInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'DescribeKeyRequest' => [ 'type' => 'structure', 'required' => [ 'KeyName', ], 'members' => [ 'KeyName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'KeyName', ], ], ], 'DescribeKeyResponse' => [ 'type' => 'structure', 'required' => [ 'Key', 'KeyArn', 'KeyName', 'Restrictions', 'CreateTime', 'ExpireTime', 'UpdateTime', ], 'members' => [ 'Key' => [ 'shape' => 'ApiKey', ], 'KeyArn' => [ 'shape' => 'Arn', ], 'KeyName' => [ 'shape' => 'ResourceName', ], 'Restrictions' => [ 'shape' => 'ApiKeyRestrictions', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'ExpireTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'DescribeMapRequest' => [ 'type' => 'structure', 'required' => [ 'MapName', ], 'members' => [ 'MapName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'MapName', ], ], ], 'DescribeMapResponse' => [ 'type' => 'structure', 'required' => [ 'MapName', 'MapArn', 'DataSource', 'Configuration', 'Description', 'CreateTime', 'UpdateTime', ], 'members' => [ 'MapName' => [ 'shape' => 'ResourceName', ], 'MapArn' => [ 'shape' => 'GeoArn', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Always returns RequestBasedUsage.', 'deprecatedSince' => '2022-02-01', ], 'DataSource' => [ 'shape' => 'String', ], 'Configuration' => [ 'shape' => 'MapConfiguration', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'Tags' => [ 'shape' => 'TagMap', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribePlaceIndexRequest' => [ 'type' => 'structure', 'required' => [ 'IndexName', ], 'members' => [ 'IndexName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'IndexName', ], ], ], 'DescribePlaceIndexResponse' => [ 'type' => 'structure', 'required' => [ 'IndexName', 'IndexArn', 'Description', 'CreateTime', 'UpdateTime', 'DataSource', 'DataSourceConfiguration', ], 'members' => [ 'IndexName' => [ 'shape' => 'ResourceName', ], 'IndexArn' => [ 'shape' => 'GeoArn', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Always returns RequestBasedUsage.', 'deprecatedSince' => '2022-02-01', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], 'DataSource' => [ 'shape' => 'String', ], 'DataSourceConfiguration' => [ 'shape' => 'DataSourceConfiguration', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'DescribeRouteCalculatorRequest' => [ 'type' => 'structure', 'required' => [ 'CalculatorName', ], 'members' => [ 'CalculatorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CalculatorName', ], ], ], 'DescribeRouteCalculatorResponse' => [ 'type' => 'structure', 'required' => [ 'CalculatorName', 'CalculatorArn', 'Description', 'CreateTime', 'UpdateTime', 'DataSource', ], 'members' => [ 'CalculatorName' => [ 'shape' => 'ResourceName', ], 'CalculatorArn' => [ 'shape' => 'GeoArn', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Always returns RequestBasedUsage.', 'deprecatedSince' => '2022-02-01', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], 'DataSource' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'DescribeTrackerRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', ], 'members' => [ 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'DescribeTrackerResponse' => [ 'type' => 'structure', 'required' => [ 'TrackerName', 'TrackerArn', 'Description', 'CreateTime', 'UpdateTime', ], 'members' => [ 'TrackerName' => [ 'shape' => 'ResourceName', ], 'TrackerArn' => [ 'shape' => 'Arn', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Always returns RequestBasedUsage.', 'deprecatedSince' => '2022-02-01', ], 'PricingPlanDataSource' => [ 'shape' => 'String', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Unused.', 'deprecatedSince' => '2022-02-01', ], 'Tags' => [ 'shape' => 'TagMap', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'PositionFiltering' => [ 'shape' => 'PositionFiltering', ], 'EventBridgeEnabled' => [ 'shape' => 'Boolean', ], 'KmsKeyEnableGeospatialQueries' => [ 'shape' => 'Boolean', ], ], ], 'DevicePosition' => [ 'type' => 'structure', 'required' => [ 'SampleTime', 'ReceivedTime', 'Position', ], 'members' => [ 'DeviceId' => [ 'shape' => 'Id', ], 'SampleTime' => [ 'shape' => 'Timestamp', ], 'ReceivedTime' => [ 'shape' => 'Timestamp', ], 'Position' => [ 'shape' => 'Position', ], 'Accuracy' => [ 'shape' => 'PositionalAccuracy', ], 'PositionProperties' => [ 'shape' => 'PositionPropertyMap', ], ], ], 'DevicePositionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DevicePosition', ], ], 'DevicePositionUpdate' => [ 'type' => 'structure', 'required' => [ 'DeviceId', 'SampleTime', 'Position', ], 'members' => [ 'DeviceId' => [ 'shape' => 'Id', ], 'SampleTime' => [ 'shape' => 'Timestamp', ], 'Position' => [ 'shape' => 'Position', ], 'Accuracy' => [ 'shape' => 'PositionalAccuracy', ], 'PositionProperties' => [ 'shape' => 'PositionPropertyMap', ], ], ], 'DeviceState' => [ 'type' => 'structure', 'required' => [ 'DeviceId', 'SampleTime', 'Position', ], 'members' => [ 'DeviceId' => [ 'shape' => 'Id', ], 'SampleTime' => [ 'shape' => 'Timestamp', ], 'Position' => [ 'shape' => 'Position', ], 'Accuracy' => [ 'shape' => 'PositionalAccuracy', ], 'Ipv4Address' => [ 'shape' => 'DeviceStateIpv4AddressString', ], 'WiFiAccessPoints' => [ 'shape' => 'WiFiAccessPointList', ], 'CellSignals' => [ 'shape' => 'CellSignals', ], ], ], 'DeviceStateIpv4AddressString' => [ 'type' => 'string', 'pattern' => '(?:(?:25[0-5]|(?:2[0-4]|1\\d|[0-9]|)\\d)\\.?\\b){4}', ], 'DimensionUnit' => [ 'type' => 'string', 'enum' => [ 'Meters', 'Feet', ], ], 'DisassociateTrackerConsumerRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', 'ConsumerArn', ], 'members' => [ 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], 'ConsumerArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ConsumerArn', ], ], ], 'DisassociateTrackerConsumerResponse' => [ 'type' => 'structure', 'members' => [], ], 'DistanceUnit' => [ 'type' => 'string', 'enum' => [ 'Kilometers', 'Miles', ], ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'Earfcn' => [ 'type' => 'integer', 'max' => 262143, 'min' => 0, ], 'EutranCellId' => [ 'type' => 'integer', 'max' => 268435455, 'min' => 0, ], 'FilterPlaceCategoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlaceCategory', ], 'max' => 5, 'min' => 1, ], 'ForecastGeofenceEventsDeviceState' => [ 'type' => 'structure', 'required' => [ 'Position', ], 'members' => [ 'Position' => [ 'shape' => 'Position', ], 'Speed' => [ 'shape' => 'ForecastGeofenceEventsDeviceStateSpeedDouble', ], ], ], 'ForecastGeofenceEventsDeviceStateSpeedDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'ForecastGeofenceEventsRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', 'DeviceState', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], 'DeviceState' => [ 'shape' => 'ForecastGeofenceEventsDeviceState', ], 'TimeHorizonMinutes' => [ 'shape' => 'ForecastGeofenceEventsRequestTimeHorizonMinutesDouble', ], 'DistanceUnit' => [ 'shape' => 'DistanceUnit', ], 'SpeedUnit' => [ 'shape' => 'SpeedUnit', ], 'NextToken' => [ 'shape' => 'LargeToken', ], 'MaxResults' => [ 'shape' => 'ForecastGeofenceEventsRequestMaxResultsInteger', ], ], ], 'ForecastGeofenceEventsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 20, 'min' => 1, ], 'ForecastGeofenceEventsRequestTimeHorizonMinutesDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'ForecastGeofenceEventsResponse' => [ 'type' => 'structure', 'required' => [ 'ForecastedEvents', 'DistanceUnit', 'SpeedUnit', ], 'members' => [ 'ForecastedEvents' => [ 'shape' => 'ForecastedEventsList', ], 'NextToken' => [ 'shape' => 'LargeToken', ], 'DistanceUnit' => [ 'shape' => 'DistanceUnit', ], 'SpeedUnit' => [ 'shape' => 'SpeedUnit', ], ], ], 'ForecastedEvent' => [ 'type' => 'structure', 'required' => [ 'EventId', 'GeofenceId', 'IsDeviceInGeofence', 'NearestDistance', 'EventType', ], 'members' => [ 'EventId' => [ 'shape' => 'Uuid', ], 'GeofenceId' => [ 'shape' => 'Id', ], 'IsDeviceInGeofence' => [ 'shape' => 'Boolean', ], 'NearestDistance' => [ 'shape' => 'NearestDistance', ], 'EventType' => [ 'shape' => 'ForecastedGeofenceEventType', ], 'ForecastedBreachTime' => [ 'shape' => 'Timestamp', ], 'GeofenceProperties' => [ 'shape' => 'PropertyMap', ], ], ], 'ForecastedEventsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ForecastedEvent', ], ], 'ForecastedGeofenceEventType' => [ 'type' => 'string', 'enum' => [ 'ENTER', 'EXIT', 'IDLE', ], ], 'GeoArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 0, 'pattern' => 'arn(:[a-z0-9]+([.-][a-z0-9]+)*):geo(:([a-z0-9]+([.-][a-z0-9]+)*))(:[0-9]+):((\\*)|([-a-z]+[/][*-._\\w]+))', ], 'GeoArnV2' => [ 'type' => 'string', 'max' => 1600, 'min' => 0, 'pattern' => '.*(^arn(:[a-z0-9]+([.-][a-z0-9]+)*):geo(:([a-z0-9]+([.-][a-z0-9]+)*))(:[0-9]+):((\\*)|([-a-z]+[/][*-._\\w]+))$)|(^arn(:[a-z0-9]+([.-][a-z0-9]+)*):(geo-routes|geo-places|geo-maps)(:((\\*)|([a-z0-9]+([.-][a-z0-9]+)*)))::((provider[\\/][*-._\\w]+))$).*', ], 'GeofenceGeometry' => [ 'type' => 'structure', 'members' => [ 'Polygon' => [ 'shape' => 'LinearRings', ], 'Circle' => [ 'shape' => 'Circle', ], 'Geobuf' => [ 'shape' => 'Base64EncodedGeobuf', ], ], ], 'GetDevicePositionHistoryRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', 'DeviceId', ], 'members' => [ 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], 'DeviceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'DeviceId', ], 'NextToken' => [ 'shape' => 'Token', ], 'StartTimeInclusive' => [ 'shape' => 'Timestamp', ], 'EndTimeExclusive' => [ 'shape' => 'Timestamp', ], 'MaxResults' => [ 'shape' => 'GetDevicePositionHistoryRequestMaxResultsInteger', ], ], ], 'GetDevicePositionHistoryRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'GetDevicePositionHistoryResponse' => [ 'type' => 'structure', 'required' => [ 'DevicePositions', ], 'members' => [ 'DevicePositions' => [ 'shape' => 'DevicePositionList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetDevicePositionRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', 'DeviceId', ], 'members' => [ 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], 'DeviceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'DeviceId', ], ], ], 'GetDevicePositionResponse' => [ 'type' => 'structure', 'required' => [ 'SampleTime', 'ReceivedTime', 'Position', ], 'members' => [ 'DeviceId' => [ 'shape' => 'Id', ], 'SampleTime' => [ 'shape' => 'Timestamp', ], 'ReceivedTime' => [ 'shape' => 'Timestamp', ], 'Position' => [ 'shape' => 'Position', ], 'Accuracy' => [ 'shape' => 'PositionalAccuracy', ], 'PositionProperties' => [ 'shape' => 'PositionPropertyMap', ], ], ], 'GetGeofenceRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', 'GeofenceId', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], 'GeofenceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'GeofenceId', ], ], ], 'GetGeofenceResponse' => [ 'type' => 'structure', 'required' => [ 'GeofenceId', 'Geometry', 'Status', 'CreateTime', 'UpdateTime', ], 'members' => [ 'GeofenceId' => [ 'shape' => 'Id', ], 'Geometry' => [ 'shape' => 'GeofenceGeometry', ], 'Status' => [ 'shape' => 'String', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], 'GeofenceProperties' => [ 'shape' => 'PropertyMap', ], ], ], 'GetMapGlyphsRequest' => [ 'type' => 'structure', 'required' => [ 'MapName', 'FontStack', 'FontUnicodeRange', ], 'members' => [ 'MapName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'MapName', ], 'FontStack' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'FontStack', ], 'FontUnicodeRange' => [ 'shape' => 'GetMapGlyphsRequestFontUnicodeRangeString', 'location' => 'uri', 'locationName' => 'FontUnicodeRange', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], ], ], 'GetMapGlyphsRequestFontUnicodeRangeString' => [ 'type' => 'string', 'pattern' => '[0-9]+-[0-9]+\\.pbf', ], 'GetMapGlyphsResponse' => [ 'type' => 'structure', 'members' => [ 'Blob' => [ 'shape' => 'Blob', ], 'ContentType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Content-Type', ], 'CacheControl' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Cache-Control', ], ], 'payload' => 'Blob', ], 'GetMapSpritesRequest' => [ 'type' => 'structure', 'required' => [ 'MapName', 'FileName', ], 'members' => [ 'MapName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'MapName', ], 'FileName' => [ 'shape' => 'GetMapSpritesRequestFileNameString', 'location' => 'uri', 'locationName' => 'FileName', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], ], ], 'GetMapSpritesRequestFileNameString' => [ 'type' => 'string', 'pattern' => 'sprites(@2x)?\\.(png|json)', ], 'GetMapSpritesResponse' => [ 'type' => 'structure', 'members' => [ 'Blob' => [ 'shape' => 'Blob', ], 'ContentType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Content-Type', ], 'CacheControl' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Cache-Control', ], ], 'payload' => 'Blob', ], 'GetMapStyleDescriptorRequest' => [ 'type' => 'structure', 'required' => [ 'MapName', ], 'members' => [ 'MapName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'MapName', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], ], ], 'GetMapStyleDescriptorResponse' => [ 'type' => 'structure', 'members' => [ 'Blob' => [ 'shape' => 'Blob', ], 'ContentType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Content-Type', ], 'CacheControl' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Cache-Control', ], ], 'payload' => 'Blob', ], 'GetMapTileRequest' => [ 'type' => 'structure', 'required' => [ 'MapName', 'Z', 'X', 'Y', ], 'members' => [ 'MapName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'MapName', ], 'Z' => [ 'shape' => 'GetMapTileRequestZString', 'location' => 'uri', 'locationName' => 'Z', ], 'X' => [ 'shape' => 'GetMapTileRequestXString', 'location' => 'uri', 'locationName' => 'X', ], 'Y' => [ 'shape' => 'GetMapTileRequestYString', 'location' => 'uri', 'locationName' => 'Y', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], ], ], 'GetMapTileRequestXString' => [ 'type' => 'string', 'pattern' => '.*\\d+.*', ], 'GetMapTileRequestYString' => [ 'type' => 'string', 'pattern' => '.*\\d+.*', ], 'GetMapTileRequestZString' => [ 'type' => 'string', 'pattern' => '.*\\d+.*', ], 'GetMapTileResponse' => [ 'type' => 'structure', 'members' => [ 'Blob' => [ 'shape' => 'Blob', ], 'ContentType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Content-Type', ], 'CacheControl' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Cache-Control', ], ], 'payload' => 'Blob', ], 'GetPlaceRequest' => [ 'type' => 'structure', 'required' => [ 'IndexName', 'PlaceId', ], 'members' => [ 'IndexName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'IndexName', ], 'PlaceId' => [ 'shape' => 'PlaceId', 'location' => 'uri', 'locationName' => 'PlaceId', ], 'Language' => [ 'shape' => 'LanguageTag', 'location' => 'querystring', 'locationName' => 'language', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], ], ], 'GetPlaceResponse' => [ 'type' => 'structure', 'required' => [ 'Place', ], 'members' => [ 'Place' => [ 'shape' => 'Place', ], ], ], 'Id' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[-._\\p{L}\\p{N}]+', ], 'InferredState' => [ 'type' => 'structure', 'required' => [ 'ProxyDetected', ], 'members' => [ 'Position' => [ 'shape' => 'Position', ], 'Accuracy' => [ 'shape' => 'PositionalAccuracy', ], 'DeviationDistance' => [ 'shape' => 'Double', ], 'ProxyDetected' => [ 'shape' => 'Boolean', ], ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'IntendedUse' => [ 'type' => 'string', 'enum' => [ 'SingleUse', 'Storage', ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'LanguageTag' => [ 'type' => 'string', 'max' => 35, 'min' => 2, ], 'LargeToken' => [ 'type' => 'string', 'max' => 60000, 'min' => 1, ], 'Leg' => [ 'type' => 'structure', 'required' => [ 'StartPosition', 'EndPosition', 'Distance', 'DurationSeconds', 'Steps', ], 'members' => [ 'StartPosition' => [ 'shape' => 'Position', ], 'EndPosition' => [ 'shape' => 'Position', ], 'Distance' => [ 'shape' => 'LegDistanceDouble', ], 'DurationSeconds' => [ 'shape' => 'LegDurationSecondsDouble', ], 'Geometry' => [ 'shape' => 'LegGeometry', ], 'Steps' => [ 'shape' => 'StepList', ], ], ], 'LegDistanceDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'LegDurationSecondsDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'LegGeometry' => [ 'type' => 'structure', 'members' => [ 'LineString' => [ 'shape' => 'LineString', ], ], ], 'LegList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Leg', ], ], 'LineString' => [ 'type' => 'list', 'member' => [ 'shape' => 'Position', ], 'min' => 2, ], 'LinearRing' => [ 'type' => 'list', 'member' => [ 'shape' => 'Position', ], 'min' => 4, ], 'LinearRings' => [ 'type' => 'list', 'member' => [ 'shape' => 'LinearRing', ], 'min' => 1, ], 'ListDevicePositionsRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', ], 'members' => [ 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], 'MaxResults' => [ 'shape' => 'ListDevicePositionsRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], 'FilterGeometry' => [ 'shape' => 'TrackingFilterGeometry', ], ], ], 'ListDevicePositionsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListDevicePositionsResponse' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'ListDevicePositionsResponseEntryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListDevicePositionsResponseEntry' => [ 'type' => 'structure', 'required' => [ 'DeviceId', 'SampleTime', 'Position', ], 'members' => [ 'DeviceId' => [ 'shape' => 'Id', ], 'SampleTime' => [ 'shape' => 'Timestamp', ], 'Position' => [ 'shape' => 'Position', ], 'Accuracy' => [ 'shape' => 'PositionalAccuracy', ], 'PositionProperties' => [ 'shape' => 'PositionPropertyMap', ], ], ], 'ListDevicePositionsResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListDevicePositionsResponseEntry', ], ], 'ListGeofenceCollectionsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListGeofenceCollectionsRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListGeofenceCollectionsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListGeofenceCollectionsResponse' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'ListGeofenceCollectionsResponseEntryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListGeofenceCollectionsResponseEntry' => [ 'type' => 'structure', 'required' => [ 'CollectionName', 'Description', 'CreateTime', 'UpdateTime', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Always returns RequestBasedUsage.', 'deprecatedSince' => '2022-02-01', ], 'PricingPlanDataSource' => [ 'shape' => 'String', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Unused.', 'deprecatedSince' => '2022-02-01', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListGeofenceCollectionsResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListGeofenceCollectionsResponseEntry', ], ], 'ListGeofenceResponseEntry' => [ 'type' => 'structure', 'required' => [ 'GeofenceId', 'Geometry', 'Status', 'CreateTime', 'UpdateTime', ], 'members' => [ 'GeofenceId' => [ 'shape' => 'Id', ], 'Geometry' => [ 'shape' => 'GeofenceGeometry', ], 'Status' => [ 'shape' => 'String', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], 'GeofenceProperties' => [ 'shape' => 'PropertyMap', ], ], ], 'ListGeofenceResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListGeofenceResponseEntry', ], ], 'ListGeofencesRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], 'NextToken' => [ 'shape' => 'LargeToken', ], 'MaxResults' => [ 'shape' => 'ListGeofencesRequestMaxResultsInteger', ], ], ], 'ListGeofencesRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListGeofencesResponse' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'ListGeofenceResponseEntryList', ], 'NextToken' => [ 'shape' => 'LargeToken', ], ], ], 'ListKeysRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListKeysRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], 'Filter' => [ 'shape' => 'ApiKeyFilter', ], ], ], 'ListKeysRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListKeysResponse' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'ListKeysResponseEntryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListKeysResponseEntry' => [ 'type' => 'structure', 'required' => [ 'KeyName', 'ExpireTime', 'Restrictions', 'CreateTime', 'UpdateTime', ], 'members' => [ 'KeyName' => [ 'shape' => 'ResourceName', ], 'ExpireTime' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'Restrictions' => [ 'shape' => 'ApiKeyRestrictions', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListKeysResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListKeysResponseEntry', ], ], 'ListMapsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListMapsRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListMapsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListMapsResponse' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'ListMapsResponseEntryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListMapsResponseEntry' => [ 'type' => 'structure', 'required' => [ 'MapName', 'Description', 'DataSource', 'CreateTime', 'UpdateTime', ], 'members' => [ 'MapName' => [ 'shape' => 'ResourceName', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'DataSource' => [ 'shape' => 'String', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Always returns RequestBasedUsage.', 'deprecatedSince' => '2022-02-01', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListMapsResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListMapsResponseEntry', ], ], 'ListPlaceIndexesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListPlaceIndexesRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListPlaceIndexesRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListPlaceIndexesResponse' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'ListPlaceIndexesResponseEntryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListPlaceIndexesResponseEntry' => [ 'type' => 'structure', 'required' => [ 'IndexName', 'Description', 'DataSource', 'CreateTime', 'UpdateTime', ], 'members' => [ 'IndexName' => [ 'shape' => 'ResourceName', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'DataSource' => [ 'shape' => 'String', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Always returns RequestBasedUsage.', 'deprecatedSince' => '2022-02-01', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListPlaceIndexesResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListPlaceIndexesResponseEntry', ], ], 'ListRouteCalculatorsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListRouteCalculatorsRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListRouteCalculatorsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListRouteCalculatorsResponse' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'ListRouteCalculatorsResponseEntryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListRouteCalculatorsResponseEntry' => [ 'type' => 'structure', 'required' => [ 'CalculatorName', 'Description', 'DataSource', 'CreateTime', 'UpdateTime', ], 'members' => [ 'CalculatorName' => [ 'shape' => 'ResourceName', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'DataSource' => [ 'shape' => 'String', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Always returns RequestBasedUsage.', 'deprecatedSince' => '2022-02-01', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListRouteCalculatorsResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListRouteCalculatorsResponseEntry', ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'ListTrackerConsumersRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', ], 'members' => [ 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], 'MaxResults' => [ 'shape' => 'ListTrackerConsumersRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListTrackerConsumersRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListTrackerConsumersResponse' => [ 'type' => 'structure', 'required' => [ 'ConsumerArns', ], 'members' => [ 'ConsumerArns' => [ 'shape' => 'ArnList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListTrackersRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListTrackersRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListTrackersRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListTrackersResponse' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'ListTrackersResponseEntryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListTrackersResponseEntry' => [ 'type' => 'structure', 'required' => [ 'TrackerName', 'Description', 'CreateTime', 'UpdateTime', ], 'members' => [ 'TrackerName' => [ 'shape' => 'ResourceName', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Always returns RequestBasedUsage.', 'deprecatedSince' => '2022-02-01', ], 'PricingPlanDataSource' => [ 'shape' => 'String', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. Unused.', 'deprecatedSince' => '2022-02-01', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListTrackersResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListTrackersResponseEntry', ], ], 'LteCellDetails' => [ 'type' => 'structure', 'required' => [ 'CellId', 'Mcc', 'Mnc', ], 'members' => [ 'CellId' => [ 'shape' => 'EutranCellId', ], 'Mcc' => [ 'shape' => 'LteCellDetailsMccInteger', ], 'Mnc' => [ 'shape' => 'LteCellDetailsMncInteger', ], 'LocalId' => [ 'shape' => 'LteLocalId', ], 'NetworkMeasurements' => [ 'shape' => 'LteCellDetailsNetworkMeasurementsList', ], 'TimingAdvance' => [ 'shape' => 'LteCellDetailsTimingAdvanceInteger', ], 'NrCapable' => [ 'shape' => 'Boolean', ], 'Rsrp' => [ 'shape' => 'Rsrp', ], 'Rsrq' => [ 'shape' => 'Rsrq', ], 'Tac' => [ 'shape' => 'LteCellDetailsTacInteger', ], ], ], 'LteCellDetailsMccInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 999, 'min' => 200, ], 'LteCellDetailsMncInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 999, 'min' => 0, ], 'LteCellDetailsNetworkMeasurementsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LteNetworkMeasurements', ], 'max' => 32, 'min' => 1, ], 'LteCellDetailsTacInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 65535, 'min' => 0, ], 'LteCellDetailsTimingAdvanceInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1282, 'min' => 0, ], 'LteLocalId' => [ 'type' => 'structure', 'required' => [ 'Earfcn', 'Pci', ], 'members' => [ 'Earfcn' => [ 'shape' => 'Earfcn', ], 'Pci' => [ 'shape' => 'Pci', ], ], ], 'LteNetworkMeasurements' => [ 'type' => 'structure', 'required' => [ 'Earfcn', 'CellId', 'Pci', ], 'members' => [ 'Earfcn' => [ 'shape' => 'Earfcn', ], 'CellId' => [ 'shape' => 'EutranCellId', ], 'Pci' => [ 'shape' => 'Pci', ], 'Rsrp' => [ 'shape' => 'Rsrp', ], 'Rsrq' => [ 'shape' => 'Rsrq', ], ], ], 'MapConfiguration' => [ 'type' => 'structure', 'required' => [ 'Style', ], 'members' => [ 'Style' => [ 'shape' => 'MapStyle', ], 'PoliticalView' => [ 'shape' => 'CountryCode3', ], 'CustomLayers' => [ 'shape' => 'CustomLayerList', ], ], ], 'MapConfigurationUpdate' => [ 'type' => 'structure', 'members' => [ 'PoliticalView' => [ 'shape' => 'CountryCode3OrEmpty', ], 'CustomLayers' => [ 'shape' => 'CustomLayerList', ], ], ], 'MapStyle' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[-._\\w]+', ], 'NearestDistance' => [ 'type' => 'double', 'min' => 0, ], 'OptimizationMode' => [ 'type' => 'string', 'enum' => [ 'FastestRoute', 'ShortestRoute', ], ], 'Pci' => [ 'type' => 'integer', 'max' => 503, 'min' => 0, ], 'Place' => [ 'type' => 'structure', 'required' => [ 'Geometry', ], 'members' => [ 'Label' => [ 'shape' => 'String', ], 'Geometry' => [ 'shape' => 'PlaceGeometry', ], 'AddressNumber' => [ 'shape' => 'String', ], 'Street' => [ 'shape' => 'String', ], 'Neighborhood' => [ 'shape' => 'String', ], 'Municipality' => [ 'shape' => 'String', ], 'SubRegion' => [ 'shape' => 'String', ], 'Region' => [ 'shape' => 'String', ], 'Country' => [ 'shape' => 'String', ], 'PostalCode' => [ 'shape' => 'String', ], 'Interpolated' => [ 'shape' => 'Boolean', ], 'TimeZone' => [ 'shape' => 'TimeZone', ], 'UnitType' => [ 'shape' => 'String', ], 'UnitNumber' => [ 'shape' => 'String', ], 'Categories' => [ 'shape' => 'PlaceCategoryList', ], 'SupplementalCategories' => [ 'shape' => 'PlaceSupplementalCategoryList', ], 'SubMunicipality' => [ 'shape' => 'String', ], ], ], 'PlaceCategory' => [ 'type' => 'string', 'max' => 35, 'min' => 0, ], 'PlaceCategoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlaceCategory', ], 'max' => 10, 'min' => 1, ], 'PlaceGeometry' => [ 'type' => 'structure', 'members' => [ 'Point' => [ 'shape' => 'Position', ], ], ], 'PlaceId' => [ 'type' => 'string', ], 'PlaceIndexSearchResultLimit' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'PlaceSupplementalCategory' => [ 'type' => 'string', 'max' => 35, 'min' => 0, ], 'PlaceSupplementalCategoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlaceSupplementalCategory', ], 'max' => 10, 'min' => 1, ], 'Position' => [ 'type' => 'list', 'member' => [ 'shape' => 'Double', ], 'max' => 2, 'min' => 2, 'sensitive' => true, ], 'PositionFiltering' => [ 'type' => 'string', 'enum' => [ 'TimeBased', 'DistanceBased', 'AccuracyBased', ], ], 'PositionPropertyMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'PositionPropertyMapKeyString', ], 'value' => [ 'shape' => 'PositionPropertyMapValueString', ], 'max' => 4, 'min' => 0, 'sensitive' => true, ], 'PositionPropertyMapKeyString' => [ 'type' => 'string', 'max' => 20, 'min' => 1, ], 'PositionPropertyMapValueString' => [ 'type' => 'string', 'max' => 150, 'min' => 1, ], 'PositionalAccuracy' => [ 'type' => 'structure', 'required' => [ 'Horizontal', ], 'members' => [ 'Horizontal' => [ 'shape' => 'PositionalAccuracyHorizontalDouble', ], ], ], 'PositionalAccuracyHorizontalDouble' => [ 'type' => 'double', 'box' => true, 'max' => 10000000, 'min' => 0, ], 'PricingPlan' => [ 'type' => 'string', 'enum' => [ 'RequestBasedUsage', 'MobileAssetTracking', 'MobileAssetManagement', ], ], 'PropertyMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'PropertyMapKeyString', ], 'value' => [ 'shape' => 'PropertyMapValueString', ], 'max' => 3, 'min' => 0, 'sensitive' => true, ], 'PropertyMapKeyString' => [ 'type' => 'string', 'max' => 20, 'min' => 1, ], 'PropertyMapValueString' => [ 'type' => 'string', 'max' => 40, 'min' => 1, ], 'PutGeofenceRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', 'GeofenceId', 'Geometry', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], 'GeofenceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'GeofenceId', ], 'Geometry' => [ 'shape' => 'GeofenceGeometry', ], 'GeofenceProperties' => [ 'shape' => 'PropertyMap', ], ], ], 'PutGeofenceResponse' => [ 'type' => 'structure', 'required' => [ 'GeofenceId', 'CreateTime', 'UpdateTime', ], 'members' => [ 'GeofenceId' => [ 'shape' => 'Id', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'RefererPattern' => [ 'type' => 'string', 'max' => 253, 'min' => 0, 'pattern' => '([$\\-._+!*\\x{60}(),;/?:@=&\\w]|%([0-9a-fA-F?]{2}|[0-9a-fA-F?]?[*]))+', ], 'ResourceDescription' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'ResourceName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[-._\\w]+', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RouteMatrix' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteMatrixRow', ], ], 'RouteMatrixEntry' => [ 'type' => 'structure', 'members' => [ 'Distance' => [ 'shape' => 'RouteMatrixEntryDistanceDouble', ], 'DurationSeconds' => [ 'shape' => 'RouteMatrixEntryDurationSecondsDouble', ], 'Error' => [ 'shape' => 'RouteMatrixEntryError', ], ], ], 'RouteMatrixEntryDistanceDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'RouteMatrixEntryDurationSecondsDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'RouteMatrixEntryError' => [ 'type' => 'structure', 'required' => [ 'Code', ], 'members' => [ 'Code' => [ 'shape' => 'RouteMatrixErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], ], 'RouteMatrixErrorCode' => [ 'type' => 'string', 'enum' => [ 'RouteNotFound', 'RouteTooLong', 'PositionsNotFound', 'DestinationPositionNotFound', 'DeparturePositionNotFound', 'OtherValidationError', ], ], 'RouteMatrixRow' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteMatrixEntry', ], ], 'Rsrp' => [ 'type' => 'integer', 'box' => true, 'max' => -44, 'min' => -140, ], 'Rsrq' => [ 'type' => 'float', 'box' => true, 'max' => -3, 'min' => -19.5, ], 'SearchForPositionResult' => [ 'type' => 'structure', 'required' => [ 'Place', 'Distance', ], 'members' => [ 'Place' => [ 'shape' => 'Place', ], 'Distance' => [ 'shape' => 'SearchForPositionResultDistanceDouble', ], 'PlaceId' => [ 'shape' => 'PlaceId', ], ], ], 'SearchForPositionResultDistanceDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'SearchForPositionResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchForPositionResult', ], ], 'SearchForSuggestionsResult' => [ 'type' => 'structure', 'required' => [ 'Text', ], 'members' => [ 'Text' => [ 'shape' => 'String', ], 'PlaceId' => [ 'shape' => 'PlaceId', ], 'Categories' => [ 'shape' => 'PlaceCategoryList', ], 'SupplementalCategories' => [ 'shape' => 'PlaceSupplementalCategoryList', ], ], ], 'SearchForSuggestionsResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchForSuggestionsResult', ], ], 'SearchForTextResult' => [ 'type' => 'structure', 'required' => [ 'Place', ], 'members' => [ 'Place' => [ 'shape' => 'Place', ], 'Distance' => [ 'shape' => 'SearchForTextResultDistanceDouble', ], 'Relevance' => [ 'shape' => 'SearchForTextResultRelevanceDouble', ], 'PlaceId' => [ 'shape' => 'PlaceId', ], ], ], 'SearchForTextResultDistanceDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'SearchForTextResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchForTextResult', ], ], 'SearchForTextResultRelevanceDouble' => [ 'type' => 'double', 'box' => true, 'max' => 1, 'min' => 0, ], 'SearchPlaceIndexForPositionRequest' => [ 'type' => 'structure', 'required' => [ 'IndexName', 'Position', ], 'members' => [ 'IndexName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'IndexName', ], 'Position' => [ 'shape' => 'Position', ], 'MaxResults' => [ 'shape' => 'PlaceIndexSearchResultLimit', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], ], ], 'SearchPlaceIndexForPositionResponse' => [ 'type' => 'structure', 'required' => [ 'Summary', 'Results', ], 'members' => [ 'Summary' => [ 'shape' => 'SearchPlaceIndexForPositionSummary', ], 'Results' => [ 'shape' => 'SearchForPositionResultList', ], ], ], 'SearchPlaceIndexForPositionSummary' => [ 'type' => 'structure', 'required' => [ 'Position', 'DataSource', ], 'members' => [ 'Position' => [ 'shape' => 'Position', ], 'MaxResults' => [ 'shape' => 'PlaceIndexSearchResultLimit', ], 'DataSource' => [ 'shape' => 'String', ], 'Language' => [ 'shape' => 'LanguageTag', ], ], ], 'SearchPlaceIndexForSuggestionsRequest' => [ 'type' => 'structure', 'required' => [ 'IndexName', 'Text', ], 'members' => [ 'IndexName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'IndexName', ], 'Text' => [ 'shape' => 'SearchPlaceIndexForSuggestionsRequestTextString', ], 'BiasPosition' => [ 'shape' => 'Position', ], 'FilterBBox' => [ 'shape' => 'BoundingBox', ], 'FilterCountries' => [ 'shape' => 'CountryCodeList', ], 'MaxResults' => [ 'shape' => 'SearchPlaceIndexForSuggestionsRequestMaxResultsInteger', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'FilterCategories' => [ 'shape' => 'FilterPlaceCategoryList', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], ], ], 'SearchPlaceIndexForSuggestionsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 15, 'min' => 1, ], 'SearchPlaceIndexForSuggestionsRequestTextString' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'sensitive' => true, ], 'SearchPlaceIndexForSuggestionsResponse' => [ 'type' => 'structure', 'required' => [ 'Summary', 'Results', ], 'members' => [ 'Summary' => [ 'shape' => 'SearchPlaceIndexForSuggestionsSummary', ], 'Results' => [ 'shape' => 'SearchForSuggestionsResultList', ], ], ], 'SearchPlaceIndexForSuggestionsSummary' => [ 'type' => 'structure', 'required' => [ 'Text', 'DataSource', ], 'members' => [ 'Text' => [ 'shape' => 'SensitiveString', ], 'BiasPosition' => [ 'shape' => 'Position', ], 'FilterBBox' => [ 'shape' => 'BoundingBox', ], 'FilterCountries' => [ 'shape' => 'CountryCodeList', ], 'MaxResults' => [ 'shape' => 'Integer', ], 'DataSource' => [ 'shape' => 'String', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'FilterCategories' => [ 'shape' => 'FilterPlaceCategoryList', ], ], ], 'SearchPlaceIndexForTextRequest' => [ 'type' => 'structure', 'required' => [ 'IndexName', 'Text', ], 'members' => [ 'IndexName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'IndexName', ], 'Text' => [ 'shape' => 'SearchPlaceIndexForTextRequestTextString', ], 'BiasPosition' => [ 'shape' => 'Position', ], 'FilterBBox' => [ 'shape' => 'BoundingBox', ], 'FilterCountries' => [ 'shape' => 'CountryCodeList', ], 'MaxResults' => [ 'shape' => 'PlaceIndexSearchResultLimit', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'FilterCategories' => [ 'shape' => 'FilterPlaceCategoryList', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], ], ], 'SearchPlaceIndexForTextRequestTextString' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'sensitive' => true, ], 'SearchPlaceIndexForTextResponse' => [ 'type' => 'structure', 'required' => [ 'Summary', 'Results', ], 'members' => [ 'Summary' => [ 'shape' => 'SearchPlaceIndexForTextSummary', ], 'Results' => [ 'shape' => 'SearchForTextResultList', ], ], ], 'SearchPlaceIndexForTextSummary' => [ 'type' => 'structure', 'required' => [ 'Text', 'DataSource', ], 'members' => [ 'Text' => [ 'shape' => 'SensitiveString', ], 'BiasPosition' => [ 'shape' => 'Position', ], 'FilterBBox' => [ 'shape' => 'BoundingBox', ], 'FilterCountries' => [ 'shape' => 'CountryCodeList', ], 'MaxResults' => [ 'shape' => 'PlaceIndexSearchResultLimit', ], 'ResultBBox' => [ 'shape' => 'BoundingBox', ], 'DataSource' => [ 'shape' => 'String', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'FilterCategories' => [ 'shape' => 'FilterPlaceCategoryList', ], ], ], 'SensitiveString' => [ 'type' => 'string', 'sensitive' => true, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SpeedUnit' => [ 'type' => 'string', 'enum' => [ 'KilometersPerHour', 'MilesPerHour', ], ], 'Status' => [ 'type' => 'string', 'enum' => [ 'Active', 'Expired', ], ], 'Step' => [ 'type' => 'structure', 'required' => [ 'StartPosition', 'EndPosition', 'Distance', 'DurationSeconds', ], 'members' => [ 'StartPosition' => [ 'shape' => 'Position', ], 'EndPosition' => [ 'shape' => 'Position', ], 'Distance' => [ 'shape' => 'StepDistanceDouble', ], 'DurationSeconds' => [ 'shape' => 'StepDurationSecondsDouble', ], 'GeometryOffset' => [ 'shape' => 'StepGeometryOffsetInteger', ], ], ], 'StepDistanceDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'StepDurationSecondsDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'StepGeometryOffsetInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'StepList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Step', ], ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '([\\p{L}\\p{Z}\\p{N}_.,:/=+\\-@]*)', ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 50, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '([\\p{L}\\p{Z}\\p{N}_.,:/=+\\-@]*)', ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'TimeZone' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Offset' => [ 'shape' => 'Integer', ], ], ], 'Timestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'Token' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, ], 'TrackingFilterGeometry' => [ 'type' => 'structure', 'members' => [ 'Polygon' => [ 'shape' => 'LinearRings', ], ], ], 'TravelMode' => [ 'type' => 'string', 'enum' => [ 'Car', 'Truck', 'Walking', 'Bicycle', 'Motorcycle', ], ], 'TruckDimensions' => [ 'type' => 'structure', 'members' => [ 'Length' => [ 'shape' => 'TruckDimensionsLengthDouble', ], 'Height' => [ 'shape' => 'TruckDimensionsHeightDouble', ], 'Width' => [ 'shape' => 'TruckDimensionsWidthDouble', ], 'Unit' => [ 'shape' => 'DimensionUnit', ], ], ], 'TruckDimensionsHeightDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'TruckDimensionsLengthDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'TruckDimensionsWidthDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'TruckWeight' => [ 'type' => 'structure', 'members' => [ 'Total' => [ 'shape' => 'TruckWeightTotalDouble', ], 'Unit' => [ 'shape' => 'VehicleWeightUnit', ], ], ], 'TruckWeightTotalDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateGeofenceCollectionRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. If included, the only allowed value is RequestBasedUsage.', 'deprecatedSince' => '2022-02-01', ], 'PricingPlanDataSource' => [ 'shape' => 'String', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. No longer allowed.', 'deprecatedSince' => '2022-02-01', ], 'Description' => [ 'shape' => 'ResourceDescription', ], ], ], 'UpdateGeofenceCollectionResponse' => [ 'type' => 'structure', 'required' => [ 'CollectionName', 'CollectionArn', 'UpdateTime', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', ], 'CollectionArn' => [ 'shape' => 'Arn', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateKeyRequest' => [ 'type' => 'structure', 'required' => [ 'KeyName', ], 'members' => [ 'KeyName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'KeyName', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'ExpireTime' => [ 'shape' => 'Timestamp', ], 'NoExpiry' => [ 'shape' => 'Boolean', ], 'ForceUpdate' => [ 'shape' => 'Boolean', ], 'Restrictions' => [ 'shape' => 'ApiKeyRestrictions', ], ], ], 'UpdateKeyResponse' => [ 'type' => 'structure', 'required' => [ 'KeyArn', 'KeyName', 'UpdateTime', ], 'members' => [ 'KeyArn' => [ 'shape' => 'Arn', ], 'KeyName' => [ 'shape' => 'ResourceName', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateMapRequest' => [ 'type' => 'structure', 'required' => [ 'MapName', ], 'members' => [ 'MapName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'MapName', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. If included, the only allowed value is RequestBasedUsage.', 'deprecatedSince' => '2022-02-01', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'ConfigurationUpdate' => [ 'shape' => 'MapConfigurationUpdate', ], ], ], 'UpdateMapResponse' => [ 'type' => 'structure', 'required' => [ 'MapName', 'MapArn', 'UpdateTime', ], 'members' => [ 'MapName' => [ 'shape' => 'ResourceName', ], 'MapArn' => [ 'shape' => 'GeoArn', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdatePlaceIndexRequest' => [ 'type' => 'structure', 'required' => [ 'IndexName', ], 'members' => [ 'IndexName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'IndexName', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. If included, the only allowed value is RequestBasedUsage.', 'deprecatedSince' => '2022-02-01', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'DataSourceConfiguration' => [ 'shape' => 'DataSourceConfiguration', ], ], ], 'UpdatePlaceIndexResponse' => [ 'type' => 'structure', 'required' => [ 'IndexName', 'IndexArn', 'UpdateTime', ], 'members' => [ 'IndexName' => [ 'shape' => 'ResourceName', ], 'IndexArn' => [ 'shape' => 'GeoArn', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateRouteCalculatorRequest' => [ 'type' => 'structure', 'required' => [ 'CalculatorName', ], 'members' => [ 'CalculatorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CalculatorName', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. If included, the only allowed value is RequestBasedUsage.', 'deprecatedSince' => '2022-02-01', ], 'Description' => [ 'shape' => 'ResourceDescription', ], ], ], 'UpdateRouteCalculatorResponse' => [ 'type' => 'structure', 'required' => [ 'CalculatorName', 'CalculatorArn', 'UpdateTime', ], 'members' => [ 'CalculatorName' => [ 'shape' => 'ResourceName', ], 'CalculatorArn' => [ 'shape' => 'GeoArn', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateTrackerRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', ], 'members' => [ 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. If included, the only allowed value is RequestBasedUsage.', 'deprecatedSince' => '2022-02-01', ], 'PricingPlanDataSource' => [ 'shape' => 'String', 'deprecated' => true, 'deprecatedMessage' => 'Deprecated. No longer allowed.', 'deprecatedSince' => '2022-02-01', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'PositionFiltering' => [ 'shape' => 'PositionFiltering', ], 'EventBridgeEnabled' => [ 'shape' => 'Boolean', ], 'KmsKeyEnableGeospatialQueries' => [ 'shape' => 'Boolean', ], ], ], 'UpdateTrackerResponse' => [ 'type' => 'structure', 'required' => [ 'TrackerName', 'TrackerArn', 'UpdateTime', ], 'members' => [ 'TrackerName' => [ 'shape' => 'ResourceName', ], 'TrackerArn' => [ 'shape' => 'Arn', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'Uuid' => [ 'type' => 'string', 'pattern' => '[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}', ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', 'Reason', 'FieldList', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], 'Reason' => [ 'shape' => 'ValidationExceptionReason', 'locationName' => 'reason', ], 'FieldList' => [ 'shape' => 'ValidationExceptionFieldList', 'locationName' => 'fieldList', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'Name', 'Message', ], 'members' => [ 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'UnknownOperation', 'Missing', 'CannotParse', 'FieldValidationFailed', 'Other', 'UnknownField', ], ], 'VehicleWeightUnit' => [ 'type' => 'string', 'enum' => [ 'Kilograms', 'Pounds', ], ], 'VerifyDevicePositionRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', 'DeviceState', ], 'members' => [ 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], 'DeviceState' => [ 'shape' => 'DeviceState', ], 'DistanceUnit' => [ 'shape' => 'DistanceUnit', ], ], ], 'VerifyDevicePositionResponse' => [ 'type' => 'structure', 'required' => [ 'InferredState', 'DeviceId', 'SampleTime', 'ReceivedTime', 'DistanceUnit', ], 'members' => [ 'InferredState' => [ 'shape' => 'InferredState', ], 'DeviceId' => [ 'shape' => 'Id', ], 'SampleTime' => [ 'shape' => 'Timestamp', ], 'ReceivedTime' => [ 'shape' => 'Timestamp', ], 'DistanceUnit' => [ 'shape' => 'DistanceUnit', ], ], ], 'WiFiAccessPoint' => [ 'type' => 'structure', 'required' => [ 'MacAddress', 'Rss', ], 'members' => [ 'MacAddress' => [ 'shape' => 'WiFiAccessPointMacAddressString', ], 'Rss' => [ 'shape' => 'WiFiAccessPointRssInteger', ], ], ], 'WiFiAccessPointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WiFiAccessPoint', ], ], 'WiFiAccessPointMacAddressString' => [ 'type' => 'string', 'max' => 17, 'min' => 12, 'pattern' => '([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})', ], 'WiFiAccessPointRssInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 0, 'min' => -128, ], ],];
