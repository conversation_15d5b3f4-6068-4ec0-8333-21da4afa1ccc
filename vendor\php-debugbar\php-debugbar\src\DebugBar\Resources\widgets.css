pre.phpdebugbar-widgets-code-block {
    white-space: pre;
    word-wrap: normal;
    overflow: hidden;
}
  pre.phpdebugbar-widgets-code-block code {
    display: block;
    overflow-x: auto;
    overflow-y: hidden;
  }
  pre.phpdebugbar-widgets-code-block code.phpdebugbar-widgets-numbered-code {
    padding: 5px;
  }
  pre.phpdebugbar-widgets-code-block ul li.phpdebugbar-widgets-highlighted-line {
    font-weight: bolder;
    text-decoration: underline;
  }
  pre.phpdebugbar-widgets-code-block ul li.phpdebugbar-widgets-highlighted-line span {
    position: absolute;
    background: var(--debugbar-text);
    min-width: calc(100% - 85px);
    margin-left: 10px;
    opacity: 0.15;
  }
  pre.phpdebugbar-widgets-code-block ul {
    position: static;
    float: left;
    padding: 5px;
    border-right: 1px solid var(--debugbar-header-border);
    text-align: right;
  }

  .phpdebugbar-widgets-kvlist span.phpdebugbar-widgets-filename,
  li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-filename {
    display: block;
    font-style: italic;
    float: right;
    margin-left: 8px;
    color: var(--debugbar-link);
  }
  a.phpdebugbar-widgets-editor-link {
    color: var(--debugbar-link);
  }
  .phpdebugbar-widgets-kvlist span.phpdebugbar-widgets-filename:hover,
  li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-filename:hover,
  a.phpdebugbar-widgets-editor-link:hover {
    color: var(--debugbar-hover);
  }

  a.phpdebugbar-widgets-editor-link:before {
    font-family: PhpDebugbarFontAwesome;
    margin-right: 4px;
    font-size: 12px;
    font-style: normal;
  }

  a.phpdebugbar-widgets-editor-link:before {
    content: "\f08e";
    margin-left: 4px;
  }

/* -------------------------------------- */

ul.phpdebugbar-widgets-list {
  margin: 0;
  padding: 0;
  list-style: none;
  font-family: var(--debugbar-font-mono);
}
  ul.phpdebugbar-widgets-list li.phpdebugbar-widgets-list-item {
    padding: 3px;
    border-bottom: 1px solid var(--debugbar-border);
    position: relative;
    overflow: hidden;
  }

/* -------------------------------------- */

div.phpdebugbar-widgets-messages {
  position: relative;
  height: 100%;
}
  div.phpdebugbar-widgets-messages ul.phpdebugbar-widgets-list {
    padding-bottom: 45px;
  }
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-value:before {
    font-family: PhpDebugbarFontAwesome;
    margin-right: 8px;
    font-size: 11px;
  }
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-value.phpdebugbar-widgets-alert:before {
    content: "\f0f3";
    color: #cbcf38;
  }
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-value.phpdebugbar-widgets-debug:before {
    content: "\f188";
    color: #78d79a;
  }
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-value.phpdebugbar-widgets-warning:before,
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-value.phpdebugbar-widgets-emergency:before,
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-value.phpdebugbar-widgets-notice:before {
    content: "\f071";
    color: #ecb03d;
  }
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-value.phpdebugbar-widgets-error,
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-value.phpdebugbar-widgets-critical:before {
    color: red;
  }
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-value.phpdebugbar-widgets-error:before,
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-value.phpdebugbar-widgets-critical:before {
    content: "\f057";
  }
  dl.phpdebugbar-widgets-kvlist dd.phpdebugbar-widgets-value pre.sf-dump,
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item pre.sf-dump {
    display: inline-block !important;
    padding-top: 0px;
    padding-bottom: 0px;
  }
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-collector,
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-label {
    float: right;
    font-size: 12px;
    padding: 2px 4px;
    color: #888;
    margin: 0 2px;
    text-decoration: none;
    text-shadow: none;
    background: none;
    font-weight: normal;
  }
  div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-collector {
    color: #555;
    font-style: italic;
  }
  div.phpdebugbar-widgets-messages div.phpdebugbar-widgets-toolbar {
    position: fixed;
    bottom: 0;
    width: 100%;
    background: var(--debugbar-header);
    color: var(--debugbar-text);
    border-top: 1px solid var(--debugbar-border);
    border-bottom: 0px;
    height: 20px;
    padding: 4px 0px 4px;
  }
  div.phpdebugbar-widgets-messages li .phpdebugbar-widgets-label-called-from {
    float: right;
    color: var(--debugbar-text-muted);
    padding-left: 5px;
    border-bottom: 1px dotted var(--debugbar-border);
  }
  div.phpdebugbar-widgets-messages li .phpdebugbar-widgets-label-called-from:before {
    content: "\f08d";
    font-family: PhpDebugbarFontAwesome;
    margin-right: 4px;
    font-size: 12px;
  }
    div.phpdebugbar-widgets-messages div.phpdebugbar-widgets-toolbar input {
      border: 0;
      margin: 0;
      margin-left: 7px;
      width: 30%;
      box-shadow: none;
      border-radius: 3px;
      padding: 2px 6px;
      height: 15px;
    }
    div.phpdebugbar-widgets-messages div.phpdebugbar-widgets-toolbar input:focus {
      outline: none;
    }
      div.phpdebugbar-widgets-messages div.phpdebugbar-widgets-toolbar a.phpdebugbar-widgets-filter {
        float: right;
        font-size: 12px;
        padding: 2px 4px;
        background: #7cacd5;
        margin: 0 2px;
        border-radius: 4px;
        color: var(--debugbar-background);
        text-decoration: none;
      }
      div.phpdebugbar-widgets-messages div.phpdebugbar-widgets-toolbar a.phpdebugbar-widgets-filter.phpdebugbar-widgets-excluded {
        background: var(--debugbar-active);
        color: var(--debugbar-text-muted);
      }

/* -------------------------------------- */

dl.phpdebugbar-widgets-kvlist {
  margin: 0;
}
  dl.phpdebugbar-widgets-kvlist dt {
    float: left;
    width: 150px;
    padding: 5px;
    border-top: 1px solid var(--debugbar-border);
    font-weight: bold;
    clear: both;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  dl.phpdebugbar-widgets-kvlist dd {
    margin-left: 160px;
    padding: 5px;
    border-top: 1px solid var(--debugbar-border);
    cursor: pointer;
    min-height: 17px;
  }

/* -------------------------------------- */

dl.phpdebugbar-widgets-varlist,
dl.phpdebugbar-widgets-htmlvarlist {
  font-family: var(--debugbar-font-mono);
}
  dl.phpdebugbar-widgets-htmlvarlist dd {
    cursor: initial;
  }

/* -------------------------------------- */

ul.phpdebugbar-widgets-timeline {
  margin: 0;
  padding: 0;
  list-style: none;
}
  ul.phpdebugbar-widgets-timeline .phpdebugbar-widgets-measure {
    height: 20px;
    position: relative;
    border: none;
    display: block;
  }
    ul.phpdebugbar-widgets-timeline li span.phpdebugbar-widgets-label,
    ul.phpdebugbar-widgets-timeline li span.phpdebugbar-widgets-collector {
      position: absolute;
      font-size: 12px;
      font-family: var(--debugbar-font-mono);
      color: var(--debugbar-text);
      top: 4px;
      left: 5px;
      background: none;
      text-shadow: none;
      font-weight: normal;
      white-space: pre;
    }
    ul.phpdebugbar-widgets-timeline li span.phpdebugbar-widgets-collector {
      left: initial;
      right: 5px;
    }
    ul.phpdebugbar-widgets-timeline li span.phpdebugbar-widgets-value {
      display: block;
      position: absolute;
      height: calc(100% - 4px);
      background-color: var(--debugbar-accent);
      border-bottom: 2px solid var(--debugbar-accent-border);
      top: 2px;
      border-radius: 3px;
      min-width: 2px;
    }

    ul.phpdebugbar-widgets-timeline table.phpdebugbar-widgets-params {
      display: none;
      width: 70%;
      margin: 10px;
      border: 1px solid var(--debugbar-border);
      font-family: var(--debugbar-font-mono);
      border-collapse: collapse;
    }
      ul.phpdebugbar-widgets-timeline table.phpdebugbar-widgets-params td {
        border: 1px solid var(--debugbar-border);
        border-left: none ;
        border-right: none;
        padding: 0 5px;
      }
      ul.phpdebugbar-widgets-timeline table.phpdebugbar-widgets-params .phpdebugbar-widgets-name {
        width: 20%;
        font-weight: bold;
      }

/* -------------------------------------- */

div.phpdebugbar-widgets-exceptions li.phpdebugbar-widgets-list-item {
  cursor: pointer;
}
  div.phpdebugbar-widgets-exceptions li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-message {
    display: block;
    color: red;
  }

  div.phpdebugbar-widgets-exceptions li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-type {
    display: block;
    position: absolute;
    right: 4px;
    top: 4px;
    font-weight: bold;
  }

  div.phpdebugbar-widgets-exceptions li.phpdebugbar-widgets-list-item pre.phpdebugbar-widgets-file {
    display: none;
    margin: 10px;
    padding: 5px;
    border: 1px solid var(--debugbar-border);
    font-family: var(--debugbar-font-mono);
  }

  div.phpdebugbar-widgets-exceptions li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-filename {
    float: none;
  }

ul.phpdebugbar-widgets-timeline table.phpdebugbar-widgets-params {
    display: table;
    border: 0;
    width: 99%;
}

