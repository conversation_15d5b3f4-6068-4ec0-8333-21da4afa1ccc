<?php
namespace Aws\LookoutEquipment;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon Lookout for Equipment** service.
 * @method \Aws\Result createDataset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDatasetAsync(array $args = [])
 * @method \Aws\Result createInferenceScheduler(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createInferenceSchedulerAsync(array $args = [])
 * @method \Aws\Result createLabel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createLabelAsync(array $args = [])
 * @method \Aws\Result createLabelGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createLabelGroupAsync(array $args = [])
 * @method \Aws\Result createModel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createModelAsync(array $args = [])
 * @method \Aws\Result createRetrainingScheduler(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createRetrainingSchedulerAsync(array $args = [])
 * @method \Aws\Result deleteDataset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDatasetAsync(array $args = [])
 * @method \Aws\Result deleteInferenceScheduler(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteInferenceSchedulerAsync(array $args = [])
 * @method \Aws\Result deleteLabel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteLabelAsync(array $args = [])
 * @method \Aws\Result deleteLabelGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteLabelGroupAsync(array $args = [])
 * @method \Aws\Result deleteModel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteModelAsync(array $args = [])
 * @method \Aws\Result deleteResourcePolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteResourcePolicyAsync(array $args = [])
 * @method \Aws\Result deleteRetrainingScheduler(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteRetrainingSchedulerAsync(array $args = [])
 * @method \Aws\Result describeDataIngestionJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeDataIngestionJobAsync(array $args = [])
 * @method \Aws\Result describeDataset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeDatasetAsync(array $args = [])
 * @method \Aws\Result describeInferenceScheduler(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeInferenceSchedulerAsync(array $args = [])
 * @method \Aws\Result describeLabel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeLabelAsync(array $args = [])
 * @method \Aws\Result describeLabelGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeLabelGroupAsync(array $args = [])
 * @method \Aws\Result describeModel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeModelAsync(array $args = [])
 * @method \Aws\Result describeModelVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeModelVersionAsync(array $args = [])
 * @method \Aws\Result describeResourcePolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeResourcePolicyAsync(array $args = [])
 * @method \Aws\Result describeRetrainingScheduler(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeRetrainingSchedulerAsync(array $args = [])
 * @method \Aws\Result importDataset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise importDatasetAsync(array $args = [])
 * @method \Aws\Result importModelVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise importModelVersionAsync(array $args = [])
 * @method \Aws\Result listDataIngestionJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDataIngestionJobsAsync(array $args = [])
 * @method \Aws\Result listDatasets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDatasetsAsync(array $args = [])
 * @method \Aws\Result listInferenceEvents(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listInferenceEventsAsync(array $args = [])
 * @method \Aws\Result listInferenceExecutions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listInferenceExecutionsAsync(array $args = [])
 * @method \Aws\Result listInferenceSchedulers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listInferenceSchedulersAsync(array $args = [])
 * @method \Aws\Result listLabelGroups(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listLabelGroupsAsync(array $args = [])
 * @method \Aws\Result listLabels(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listLabelsAsync(array $args = [])
 * @method \Aws\Result listModelVersions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listModelVersionsAsync(array $args = [])
 * @method \Aws\Result listModels(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listModelsAsync(array $args = [])
 * @method \Aws\Result listRetrainingSchedulers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listRetrainingSchedulersAsync(array $args = [])
 * @method \Aws\Result listSensorStatistics(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSensorStatisticsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result putResourcePolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putResourcePolicyAsync(array $args = [])
 * @method \Aws\Result startDataIngestionJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startDataIngestionJobAsync(array $args = [])
 * @method \Aws\Result startInferenceScheduler(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startInferenceSchedulerAsync(array $args = [])
 * @method \Aws\Result startRetrainingScheduler(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startRetrainingSchedulerAsync(array $args = [])
 * @method \Aws\Result stopInferenceScheduler(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopInferenceSchedulerAsync(array $args = [])
 * @method \Aws\Result stopRetrainingScheduler(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopRetrainingSchedulerAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateActiveModelVersion(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateActiveModelVersionAsync(array $args = [])
 * @method \Aws\Result updateInferenceScheduler(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateInferenceSchedulerAsync(array $args = [])
 * @method \Aws\Result updateLabelGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateLabelGroupAsync(array $args = [])
 * @method \Aws\Result updateModel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateModelAsync(array $args = [])
 * @method \Aws\Result updateRetrainingScheduler(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateRetrainingSchedulerAsync(array $args = [])
 */
class LookoutEquipmentClient extends AwsClient {}
