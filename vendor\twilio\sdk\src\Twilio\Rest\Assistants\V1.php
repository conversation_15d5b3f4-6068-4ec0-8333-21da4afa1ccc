<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Assistants
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Assistants;

use Twilio\Domain;
use Twilio\Exceptions\TwilioException;
use Twilio\InstanceContext;
use Twilio\Rest\Assistants\V1\AssistantList;
use Twilio\Rest\Assistants\V1\KnowledgeList;
use Twilio\Rest\Assistants\V1\PolicyList;
use Twilio\Rest\Assistants\V1\SessionList;
use Twilio\Rest\Assistants\V1\ToolList;
use Twilio\Version;

/**
 * @property AssistantList $assistants
 * @property KnowledgeList $knowledge
 * @property PolicyList $policies
 * @property SessionList $sessions
 * @property ToolList $tools
 * @method \Twilio\Rest\Assistants\V1\AssistantContext assistants(string $id)
 * @method \Twilio\Rest\Assistants\V1\KnowledgeContext knowledge(string $id)
 * @method \Twilio\Rest\Assistants\V1\ToolContext tools(string $id)
 * @method \Twilio\Rest\Assistants\V1\SessionContext sessions(string $id)
 */
class V1 extends Version
{
    protected $_assistants;
    protected $_knowledge;
    protected $_policies;
    protected $_sessions;
    protected $_tools;

    /**
     * Construct the V1 version of Assistants
     *
     * @param Domain $domain Domain that contains the version
     */
    public function __construct(Domain $domain)
    {
        parent::__construct($domain);
        $this->version = 'v1';
    }

    protected function getAssistants(): AssistantList
    {
        if (!$this->_assistants) {
            $this->_assistants = new AssistantList($this);
        }
        return $this->_assistants;
    }

    protected function getKnowledge(): KnowledgeList
    {
        if (!$this->_knowledge) {
            $this->_knowledge = new KnowledgeList($this);
        }
        return $this->_knowledge;
    }

    protected function getPolicies(): PolicyList
    {
        if (!$this->_policies) {
            $this->_policies = new PolicyList($this);
        }
        return $this->_policies;
    }

    protected function getSessions(): SessionList
    {
        if (!$this->_sessions) {
            $this->_sessions = new SessionList($this);
        }
        return $this->_sessions;
    }

    protected function getTools(): ToolList
    {
        if (!$this->_tools) {
            $this->_tools = new ToolList($this);
        }
        return $this->_tools;
    }

    /**
     * Magic getter to lazy load root resources
     *
     * @param string $name Resource to return
     * @return \Twilio\ListResource The requested resource
     * @throws TwilioException For unknown resource
     */
    public function __get(string $name)
    {
        $method = 'get' . \ucfirst($name);
        if (\method_exists($this, $method)) {
            return $this->$method();
        }

        throw new TwilioException('Unknown resource ' . $name);
    }

    /**
     * Magic caller to get resource contexts
     *
     * @param string $name Resource to return
     * @param array $arguments Context parameters
     * @return InstanceContext The requested resource context
     * @throws TwilioException For unknown resource
     */
    public function __call(string $name, array $arguments): InstanceContext
    {
        $property = $this->$name;
        if (\method_exists($property, 'getContext')) {
            return \call_user_func_array(array($property, 'getContext'), $arguments);
        }

        throw new TwilioException('Resource does not have a context');
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Assistants.V1]';
    }
}
