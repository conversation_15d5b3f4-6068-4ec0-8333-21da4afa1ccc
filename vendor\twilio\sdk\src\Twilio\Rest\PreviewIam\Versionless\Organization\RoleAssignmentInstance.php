<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Organization Public API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\PreviewIam\Versionless\Organization;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Values;
use Twilio\Version;


/**
 * @property string $sid
 * @property string $roleSid
 * @property string $scope
 * @property string $identity
 */
class RoleAssignmentInstance extends InstanceResource
{
    /**
     * Initialize the RoleAssignmentInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $organizationSid
     * @param string $sid
     */
    public function __construct(Version $version, array $payload, string $organizationSid, ?string $sid = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'sid' => Values::array_get($payload, 'sid'),
            'roleSid' => Values::array_get($payload, 'role_sid'),
            'scope' => Values::array_get($payload, 'scope'),
            'identity' => Values::array_get($payload, 'identity'),
        ];

        $this->solution = ['organizationSid' => $organizationSid, 'sid' => $sid ?: $this->properties['sid'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return RoleAssignmentContext Context for this RoleAssignmentInstance
     */
    protected function proxy(): RoleAssignmentContext
    {
        if (!$this->context) {
            $this->context = new RoleAssignmentContext(
                $this->version,
                $this->solution['organizationSid'],
                $this->solution['sid']
            );
        }

        return $this->context;
    }

    /**
     * Delete the RoleAssignmentInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->proxy()->delete();
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.PreviewIam.Versionless.RoleAssignmentInstance ' . \implode(' ', $context) . ']';
    }
}

