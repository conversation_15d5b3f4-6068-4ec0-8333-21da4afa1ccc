<?php

namespace App\Models\ProjectManagement;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use App\Models\User;
use App\Traits\ProjectManagement\HasTimestamps;
use App\Traits\ProjectManagement\HasStatus;
use App\Traits\ProjectManagement\HasPriority;
use App\Traits\ProjectManagement\Searchable;

/**
 * نموذج المهمة المتقدم
 *
 * هذا النموذج يمثل المهام في النظام ويحتوي على جميع الخصائص
 * والعلاقات والطرق المطلوبة لإدارة المهام بشكل احترافي
 *
 * @package App\Models\ProjectManagement
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 *
 * @property int $id معرف المهمة الفريد
 * @property string $title عنوان المهمة
 * @property string|null $description وصف المهمة
 * @property string $type نوع المهمة
 * @property string $status حالة المهمة
 * @property string $priority أولوية المهمة
 * @property int $project_id معرف المشروع
 * @property int|null $parent_task_id معرف المهمة الأب
 * @property int|null $assigned_to معرف المستخدم المكلف
 * @property int|null $milestone_id معرف المعلم
 * @property float $estimated_hours الساعات المقدرة
 * @property float $actual_hours الساعات الفعلية
 * @property Carbon|null $start_date تاريخ البداية
 * @property Carbon|null $due_date تاريخ الاستحقاق
 * @property Carbon|null $completed_at تاريخ الإنجاز
 * @property int $order_index ترتيب المهمة
 * @property array|null $metadata بيانات إضافية
 * @property int $created_by معرف منشئ المهمة
 * @property Carbon $created_at تاريخ الإنشاء
 * @property Carbon $updated_at تاريخ آخر تحديث
 * @property Carbon|null $deleted_at تاريخ الحذف الناعم
 */
class Task extends Model
{
    use HasFactory, SoftDeletes, HasTimestamps, HasStatus, HasPriority, Searchable;

    /**
     * اسم الجدول في قاعدة البيانات
     *
     * @var string
     */
    protected $table = 'tasks';

    /**
     * الحقول القابلة للتعبئة الجماعية
     *
     * @var array<string>
     */
    protected $fillable = [
        'title',
        'description',
        'type',
        'status',
        'priority',
        'project_id',
        'parent_task_id',
        'assigned_to',
        'milestone_id',
        'estimated_hours',
        'actual_hours',
        'start_date',
        'due_date',
        'completed_at',
        'order_index',
        'metadata',
        'created_by',
    ];

    /**
     * الحقول المخفية من التسلسل
     *
     * @var array<string>
     */
    protected $hidden = [
        'deleted_at',
    ];

    /**
     * تحويل أنواع البيانات
     *
     * @var array<string, string>
     */
    protected $casts = [
        'estimated_hours' => 'float',
        'actual_hours' => 'float',
        'start_date' => 'date',
        'due_date' => 'date',
        'completed_at' => 'datetime',
        'order_index' => 'integer',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * الحقول القابلة للبحث
     *
     * @var array<string>
     */
    protected $searchable = [
        'title',
        'description',
        'type',
        'status',
    ];

    /**
     * القيم الافتراضية للحقول
     *
     * @var array<string, mixed>
     */
    protected $attributes = [
        'status' => 'todo',
        'priority' => 'normal',
        'estimated_hours' => 0,
        'actual_hours' => 0,
        'order_index' => 0,
    ];

    /**
     * أحداث النموذج
     *
     * @return void
     */
    protected static function boot(): void
    {
        parent::boot();

        // تحديث تاريخ الإنجاز عند تغيير الحالة إلى مكتملة
        static::updating(function ($task) {
            if ($task->isDirty('status')) {
                if ($task->status === 'completed' && $task->getOriginal('status') !== 'completed') {
                    $task->completed_at = now();
                } elseif ($task->status !== 'completed') {
                    $task->completed_at = null;
                }
            }
        });

        // تحديث نسبة إنجاز المشروع عند تحديث المهمة
        static::updated(function ($task) {
            if ($task->project) {
                $task->project->updateProgressPercentage();
            }
        });

        // تحديث نسبة إنجاز المشروع عند حذف المهمة
        static::deleted(function ($task) {
            if ($task->project) {
                $task->project->updateProgressPercentage();
            }
        });
    }

    /*
    |--------------------------------------------------------------------------
    | العلاقات (Relationships)
    |--------------------------------------------------------------------------
    */

    /**
     * العلاقة مع المشروع
     *
     * @return BelongsTo
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * العلاقة مع المهمة الأب
     *
     * @return BelongsTo
     */
    public function parentTask(): BelongsTo
    {
        return $this->belongsTo(Task::class, 'parent_task_id');
    }

    /**
     * العلاقة مع المهام الفرعية
     *
     * @return HasMany
     */
    public function subTasks(): HasMany
    {
        return $this->hasMany(Task::class, 'parent_task_id');
    }

    /**
     * العلاقة مع المستخدم المكلف
     *
     * @return BelongsTo
     */
    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * العلاقة مع منشئ المهمة
     *
     * @return BelongsTo
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * العلاقة مع المعلم
     *
     * @return BelongsTo
     */
    public function milestone(): BelongsTo
    {
        return $this->belongsTo(Milestone::class);
    }

    /**
     * العلاقة مع سجلات تتبع الوقت
     *
     * @return HasMany
     */
    public function timeEntries(): HasMany
    {
        return $this->hasMany(TimeEntry::class);
    }

    /**
     * العلاقة مع التعليقات
     *
     * @return MorphMany
     */
    public function comments(): MorphMany
    {
        return $this->morphMany(Comment::class, 'commentable');
    }

    /**
     * العلاقة مع الملفات المرفقة
     *
     * @return MorphMany
     */
    public function attachments(): MorphMany
    {
        return $this->morphMany(ProjectFile::class, 'fileable');
    }

    /**
     * العلاقة مع المتابعين
     *
     * @return BelongsToMany
     */
    public function watchers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'task_watchers')
                    ->withTimestamps();
    }

    /**
     * العلاقة مع التبعيات
     *
     * @return BelongsToMany
     */
    public function dependencies(): BelongsToMany
    {
        return $this->belongsToMany(Task::class, 'task_dependencies', 'task_id', 'depends_on_task_id')
                    ->withPivot(['dependency_type'])
                    ->withTimestamps();
    }

    /**
     * العلاقة مع المهام التي تعتمد على هذه المهمة
     *
     * @return BelongsToMany
     */
    public function dependentTasks(): BelongsToMany
    {
        return $this->belongsToMany(Task::class, 'task_dependencies', 'depends_on_task_id', 'task_id')
                    ->withPivot(['dependency_type'])
                    ->withTimestamps();
    }

    /*
    |--------------------------------------------------------------------------
    | النطاقات (Scopes)
    |--------------------------------------------------------------------------
    */

    /**
     * نطاق المهام المكتملة
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeCompleted(Builder $query): Builder
    {
        return $query->where('status', 'completed');
    }

    /**
     * نطاق المهام المتأخرة
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeOverdue(Builder $query): Builder
    {
        return $query->where('due_date', '<', now())
                    ->where('status', '!=', 'completed');
    }

    /**
     * نطاق المهام المكلفة لمستخدم معين
     *
     * @param Builder $query
     * @param int $userId
     * @return Builder
     */
    public function scopeAssignedTo(Builder $query, int $userId): Builder
    {
        return $query->where('assigned_to', $userId);
    }

    /**
     * نطاق المهام حسب الأولوية
     *
     * @param Builder $query
     * @param string $priority
     * @return Builder
     */
    public function scopeByPriority(Builder $query, string $priority): Builder
    {
        return $query->where('priority', $priority);
    }

    /**
     * نطاق المهام الرئيسية (بدون مهمة أب)
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeMainTasks(Builder $query): Builder
    {
        return $query->whereNull('parent_task_id');
    }

    /**
     * نطاق المهام الفرعية
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeSubTasks(Builder $query): Builder
    {
        return $query->whereNotNull('parent_task_id');
    }

    /*
    |--------------------------------------------------------------------------
    | الطرق المساعدة (Helper Methods)
    |--------------------------------------------------------------------------
    */

    /**
     * التحقق من تأخر المهمة
     *
     * @return bool
     */
    public function isOverdue(): bool
    {
        return $this->due_date &&
               $this->due_date->isPast() &&
               $this->status !== 'completed';
    }

    /**
     * التحقق من اكتمال المهمة
     *
     * @return bool
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * حساب المدة المتبقية للمهمة
     *
     * @return int|null عدد الأيام المتبقية
     */
    public function daysRemaining(): ?int
    {
        if (!$this->due_date || $this->isCompleted()) {
            return null;
        }

        return now()->diffInDays($this->due_date, false);
    }

    /**
     * حساب نسبة الإنجاز بناءً على الوقت المستغرق
     *
     * @return float
     */
    public function getProgressPercentage(): float
    {
        if ($this->estimated_hours <= 0) {
            return $this->isCompleted() ? 100 : 0;
        }

        $percentage = ($this->actual_hours / $this->estimated_hours) * 100;
        return min(100, round($percentage, 2));
    }

    /**
     * التحقق من تجاوز الوقت المقدر
     *
     * @return bool
     */
    public function isOverEstimated(): bool
    {
        return $this->actual_hours > $this->estimated_hours;
    }

    /**
     * الحصول على الوقت المتبقي المقدر
     *
     * @return float
     */
    public function getRemainingHours(): float
    {
        return max(0, $this->estimated_hours - $this->actual_hours);
    }

    /**
     * التحقق من إمكانية البدء في المهمة
     *
     * @return bool
     */
    public function canStart(): bool
    {
        // التحقق من اكتمال جميع المهام التي تعتمد عليها
        foreach ($this->dependencies as $dependency) {
            if (!$dependency->isCompleted()) {
                return false;
            }
        }

        return true;
    }

    /**
     * الحصول على لون الحالة
     *
     * @return string
     */
    public function getStatusColorAttribute(): string
    {
        $statuses = config('project_management.tasks.statuses');
        return $statuses[$this->status]['color'] ?? '#6c757d';
    }

    /**
     * الحصول على أيقونة الحالة
     *
     * @return string
     */
    public function getStatusIconAttribute(): string
    {
        $statuses = config('project_management.tasks.statuses');
        return $statuses[$this->status]['icon'] ?? 'fas fa-question';
    }

    /**
     * الحصول على تسمية الحالة
     *
     * @return string
     */
    public function getStatusLabelAttribute(): string
    {
        $statuses = config('project_management.tasks.statuses');
        return $statuses[$this->status]['label'] ?? $this->status;
    }

    /**
     * الحصول على لون الأولوية
     *
     * @return string
     */
    public function getPriorityColorAttribute(): string
    {
        $priorities = config('project_management.tasks.priorities');
        return $priorities[$this->priority]['color'] ?? '#6c757d';
    }

    /**
     * الحصول على تسمية الأولوية
     *
     * @return string
     */
    public function getPriorityLabelAttribute(): string
    {
        $priorities = config('project_management.tasks.priorities');
        return $priorities[$this->priority]['label'] ?? $this->priority;
    }

    /**
     * تحديث الساعات الفعلية من سجلات تتبع الوقت
     *
     * @return void
     */
    public function updateActualHours(): void
    {
        $totalHours = $this->timeEntries()->sum('hours');
        $this->update(['actual_hours' => $totalHours]);
    }

    /**
     * إضافة متابع للمهمة
     *
     * @param int $userId
     * @return void
     */
    public function addWatcher(int $userId): void
    {
        $this->watchers()->syncWithoutDetaching([$userId]);
    }

    /**
     * إزالة متابع من المهمة
     *
     * @param int $userId
     * @return void
     */
    public function removeWatcher(int $userId): void
    {
        $this->watchers()->detach($userId);
    }

    /**
     * التحقق من كون المستخدم متابعاً للمهمة
     *
     * @param int $userId
     * @return bool
     */
    public function isWatchedBy(int $userId): bool
    {
        return $this->watchers()->where('user_id', $userId)->exists();
    }
}
