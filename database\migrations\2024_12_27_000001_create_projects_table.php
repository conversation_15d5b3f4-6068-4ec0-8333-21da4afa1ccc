<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * إنشاء جدول المشاريع المتقدم
 * 
 * هذا الـ Migration ينشئ جدول المشاريع مع جميع الحقول والفهارس
 * والقيود المطلوبة لنظام إدارة المشاريع المتقدم
 * 
 * @package Database\Migrations
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
return new class extends Migration
{
    /**
     * تشغيل الـ Migration
     * 
     * إنشاء جدول المشاريع مع جميع الحقول والفهارس المطلوبة
     * 
     * @return void
     */
    public function up(): void
    {
        Schema::create('projects', function (Blueprint $table) {
            // المعرف الأساسي
            $table->id()->comment('معرف المشروع الفريد');
            
            // معلومات المشروع الأساسية
            $table->string('name', 255)->comment('اسم المشروع');
            $table->string('slug', 255)->unique()->comment('الرابط الودود للمشروع');
            $table->text('description')->nullable()->comment('وصف المشروع');
            
            // تصنيف وحالة المشروع
            $table->enum('type', [
                'web_development',
                'mobile_app', 
                'desktop_app',
                'consulting',
                'maintenance',
                'research',
                'training',
                'other'
            ])->default('other')->comment('نوع المشروع');
            
            $table->enum('status', [
                'planning',
                'in_progress',
                'testing',
                'completed',
                'on_hold',
                'cancelled'
            ])->default('planning')->comment('حالة المشروع');
            
            $table->enum('priority', [
                'low',
                'normal',
                'high',
                'urgent',
                'critical'
            ])->default('normal')->comment('أولوية المشروع');
            
            // المعلومات المالية
            $table->decimal('budget', 15, 2)->default(0)->comment('الميزانية المخصصة');
            $table->decimal('spent_budget', 15, 2)->default(0)->comment('الميزانية المستخدمة');
            
            // التواريخ المهمة
            $table->date('start_date')->comment('تاريخ بداية المشروع');
            $table->date('end_date')->nullable()->comment('تاريخ انتهاء المشروع المخطط');
            $table->timestamp('actual_end_date')->nullable()->comment('التاريخ الفعلي للانتهاء');
            
            // معلومات التقدم
            $table->tinyInteger('progress_percentage')->default(0)->comment('نسبة الإنجاز');
            
            // العلاقات مع المستخدمين
            $table->foreignId('client_id')
                  ->nullable()
                  ->constrained('users')
                  ->onDelete('set null')
                  ->comment('معرف العميل');
                  
            $table->foreignId('manager_id')
                  ->nullable()
                  ->constrained('users')
                  ->onDelete('set null')
                  ->comment('معرف مدير المشروع');
                  
            $table->foreignId('created_by')
                  ->constrained('users')
                  ->onDelete('cascade')
                  ->comment('معرف منشئ المشروع');
            
            // إعدادات إضافية
            $table->json('settings')->nullable()->comment('إعدادات إضافية للمشروع');
            
            // طوابع زمنية
            $table->timestamps();
            $table->softDeletes();
            
            // الفهارس لتحسين الأداء
            $table->index(['status', 'priority'], 'idx_projects_status_priority');
            $table->index(['start_date', 'end_date'], 'idx_projects_dates');
            $table->index(['client_id', 'status'], 'idx_projects_client_status');
            $table->index(['manager_id', 'status'], 'idx_projects_manager_status');
            $table->index(['created_by', 'created_at'], 'idx_projects_creator_date');
            $table->index('type', 'idx_projects_type');
            $table->index('progress_percentage', 'idx_projects_progress');
            
            // فهرس نصي للبحث
            $table->fullText(['name', 'description'], 'idx_projects_search');
            
            // قيود إضافية
            $table->check('progress_percentage >= 0 AND progress_percentage <= 100', 'chk_projects_progress_range');
            $table->check('budget >= 0', 'chk_projects_budget_positive');
            $table->check('spent_budget >= 0', 'chk_projects_spent_budget_positive');
        });
        
        // إضافة تعليق على الجدول
        DB::statement("ALTER TABLE projects COMMENT = 'جدول المشاريع - يحتوي على معلومات المشاريع وحالاتها وتقدمها'");
    }

    /**
     * التراجع عن الـ Migration
     * 
     * حذف جدول المشاريع
     * 
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('projects');
    }
};
