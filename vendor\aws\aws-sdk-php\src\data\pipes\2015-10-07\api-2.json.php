<?php
// This file was auto-generated from sdk-root/src/data/pipes/2015-10-07/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2015-10-07', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'pipes', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Amazon EventBridge Pipes', 'serviceId' => 'Pipes', 'signatureVersion' => 'v4', 'signingName' => 'pipes', 'uid' => 'pipes-2015-10-07', ], 'operations' => [ 'CreatePipe' => [ 'name' => 'CreatePipe', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/pipes/{Name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreatePipeRequest', ], 'output' => [ 'shape' => 'CreatePipeResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'DeletePipe' => [ 'name' => 'DeletePipe', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/pipes/{Name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeletePipeRequest', ], 'output' => [ 'shape' => 'DeletePipeResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DescribePipe' => [ 'name' => 'DescribePipe', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/pipes/{Name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribePipeRequest', ], 'output' => [ 'shape' => 'DescribePipeResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'NotFoundException', ], ], ], 'ListPipes' => [ 'name' => 'ListPipes', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/pipes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPipesRequest', ], 'output' => [ 'shape' => 'ListPipesResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'NotFoundException', ], ], ], 'StartPipe' => [ 'name' => 'StartPipe', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/pipes/{Name}/start', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartPipeRequest', ], 'output' => [ 'shape' => 'StartPipeResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'StopPipe' => [ 'name' => 'StopPipe', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/pipes/{Name}/stop', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopPipeRequest', ], 'output' => [ 'shape' => 'StopPipeResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'NotFoundException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'NotFoundException', ], ], 'idempotent' => true, ], 'UpdatePipe' => [ 'name' => 'UpdatePipe', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/pipes/{Name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdatePipeRequest', ], 'output' => [ 'shape' => 'UpdatePipeResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'Arn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => 'arn:(aws[a-zA-Z0-9-]*):([a-zA-Z0-9\\-]+):([a-z]{2,4}((-gov)|(-de)|(-iso([a-z]?)))?-[a-z]+(-\\d{1})?)?:(\\d{12})?:(.+)', ], 'ArnOrJsonPath' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => 'arn:(aws[a-zA-Z0-9-]*):([a-zA-Z0-9\\-]+):([a-z]{2,4}((-gov)|(-de)|(-iso([a-z]?)))?-[a-z]+(-\\d{1})?)?:(\\d{12})?:(.+)|(\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*)', ], 'ArnOrUrl' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => 'smk://(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\\-]*[a-zA-Z0-9])\\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9\\-]*[A-Za-z0-9]):[0-9]{1,5}|arn:(aws[a-zA-Z0-9-]*):([a-zA-Z0-9\\-]+):([a-z]{2,4}((-gov)|(-de)|(-iso([a-z]?)))?-[a-z]+(-\\d{1})?)?:(\\d{12})?:(.+)', ], 'AssignPublicIp' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'AwsVpcConfiguration' => [ 'type' => 'structure', 'required' => [ 'Subnets', ], 'members' => [ 'Subnets' => [ 'shape' => 'Subnets', ], 'SecurityGroups' => [ 'shape' => 'SecurityGroups', ], 'AssignPublicIp' => [ 'shape' => 'AssignPublicIp', ], ], ], 'BatchArrayProperties' => [ 'type' => 'structure', 'members' => [ 'Size' => [ 'shape' => 'BatchArraySize', ], ], ], 'BatchArraySize' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 2, ], 'BatchContainerOverrides' => [ 'type' => 'structure', 'members' => [ 'Command' => [ 'shape' => 'StringList', ], 'Environment' => [ 'shape' => 'BatchEnvironmentVariableList', ], 'InstanceType' => [ 'shape' => 'String', ], 'ResourceRequirements' => [ 'shape' => 'BatchResourceRequirementsList', ], ], ], 'BatchDependsOn' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchJobDependency', ], 'max' => 20, 'min' => 0, ], 'BatchEnvironmentVariable' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'BatchEnvironmentVariableList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchEnvironmentVariable', ], ], 'BatchJobDependency' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'BatchJobDependencyType', ], ], ], 'BatchJobDependencyType' => [ 'type' => 'string', 'enum' => [ 'N_TO_N', 'SEQUENTIAL', ], ], 'BatchParametersMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'BatchResourceRequirement' => [ 'type' => 'structure', 'required' => [ 'Type', 'Value', ], 'members' => [ 'Type' => [ 'shape' => 'BatchResourceRequirementType', ], 'Value' => [ 'shape' => 'String', ], ], ], 'BatchResourceRequirementType' => [ 'type' => 'string', 'enum' => [ 'GPU', 'MEMORY', 'VCPU', ], ], 'BatchResourceRequirementsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchResourceRequirement', ], ], 'BatchRetryAttempts' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 1, ], 'BatchRetryStrategy' => [ 'type' => 'structure', 'members' => [ 'Attempts' => [ 'shape' => 'BatchRetryAttempts', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'CapacityProvider' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'sensitive' => true, ], 'CapacityProviderStrategy' => [ 'type' => 'list', 'member' => [ 'shape' => 'CapacityProviderStrategyItem', ], 'max' => 6, 'min' => 0, ], 'CapacityProviderStrategyItem' => [ 'type' => 'structure', 'required' => [ 'capacityProvider', ], 'members' => [ 'capacityProvider' => [ 'shape' => 'CapacityProvider', ], 'weight' => [ 'shape' => 'CapacityProviderStrategyItemWeight', ], 'base' => [ 'shape' => 'CapacityProviderStrategyItemBase', ], ], ], 'CapacityProviderStrategyItemBase' => [ 'type' => 'integer', 'max' => 100000, 'min' => 0, ], 'CapacityProviderStrategyItemWeight' => [ 'type' => 'integer', 'max' => 1000, 'min' => 0, ], 'CloudwatchLogGroupArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '(^arn:aws([a-z]|\\-)*:logs:([a-z]{2,4}((-gov)|(-de)|(-iso([a-z]?)))?-[a-z]+(-\\d{1})?):(\\d{12}):log-group:[\\.\\-_/#A-Za-z0-9]{1,512}(:\\*)?)', ], 'CloudwatchLogsLogDestination' => [ 'type' => 'structure', 'members' => [ 'LogGroupArn' => [ 'shape' => 'CloudwatchLogGroupArn', ], ], ], 'CloudwatchLogsLogDestinationParameters' => [ 'type' => 'structure', 'required' => [ 'LogGroupArn', ], 'members' => [ 'LogGroupArn' => [ 'shape' => 'CloudwatchLogGroupArn', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreatePipeRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Source', 'Target', 'RoleArn', ], 'members' => [ 'Name' => [ 'shape' => 'PipeName', 'location' => 'uri', 'locationName' => 'Name', ], 'Description' => [ 'shape' => 'PipeDescription', ], 'DesiredState' => [ 'shape' => 'RequestedPipeState', ], 'Source' => [ 'shape' => 'ArnOrUrl', ], 'SourceParameters' => [ 'shape' => 'PipeSourceParameters', ], 'Enrichment' => [ 'shape' => 'OptionalArn', ], 'EnrichmentParameters' => [ 'shape' => 'PipeEnrichmentParameters', ], 'Target' => [ 'shape' => 'Arn', ], 'TargetParameters' => [ 'shape' => 'PipeTargetParameters', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Tags' => [ 'shape' => 'TagMap', ], 'LogConfiguration' => [ 'shape' => 'PipeLogConfigurationParameters', ], 'KmsKeyIdentifier' => [ 'shape' => 'KmsKeyIdentifier', ], ], ], 'CreatePipeResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PipeArn', ], 'Name' => [ 'shape' => 'PipeName', ], 'DesiredState' => [ 'shape' => 'RequestedPipeState', ], 'CurrentState' => [ 'shape' => 'PipeState', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'Database' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'sensitive' => true, ], 'DbUser' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'sensitive' => true, ], 'DeadLetterConfig' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], ], ], 'DeletePipeRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'PipeName', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'DeletePipeResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PipeArn', ], 'Name' => [ 'shape' => 'PipeName', ], 'DesiredState' => [ 'shape' => 'RequestedPipeStateDescribeResponse', ], 'CurrentState' => [ 'shape' => 'PipeState', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribePipeRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'PipeName', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'DescribePipeResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PipeArn', ], 'Name' => [ 'shape' => 'PipeName', ], 'Description' => [ 'shape' => 'PipeDescription', ], 'DesiredState' => [ 'shape' => 'RequestedPipeStateDescribeResponse', ], 'CurrentState' => [ 'shape' => 'PipeState', ], 'StateReason' => [ 'shape' => 'PipeStateReason', ], 'Source' => [ 'shape' => 'ArnOrUrl', ], 'SourceParameters' => [ 'shape' => 'PipeSourceParameters', ], 'Enrichment' => [ 'shape' => 'OptionalArn', ], 'EnrichmentParameters' => [ 'shape' => 'PipeEnrichmentParameters', ], 'Target' => [ 'shape' => 'Arn', ], 'TargetParameters' => [ 'shape' => 'PipeTargetParameters', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Tags' => [ 'shape' => 'TagMap', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LogConfiguration' => [ 'shape' => 'PipeLogConfiguration', ], 'KmsKeyIdentifier' => [ 'shape' => 'KmsKeyIdentifier', ], ], ], 'DimensionMapping' => [ 'type' => 'structure', 'required' => [ 'DimensionValue', 'DimensionValueType', 'DimensionName', ], 'members' => [ 'DimensionValue' => [ 'shape' => 'DimensionValue', ], 'DimensionValueType' => [ 'shape' => 'DimensionValueType', ], 'DimensionName' => [ 'shape' => 'DimensionName', ], ], ], 'DimensionMappings' => [ 'type' => 'list', 'member' => [ 'shape' => 'DimensionMapping', ], 'max' => 128, 'min' => 1, ], 'DimensionName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'DimensionValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'DimensionValueType' => [ 'type' => 'string', 'enum' => [ 'VARCHAR', ], ], 'DynamoDBStreamStartPosition' => [ 'type' => 'string', 'enum' => [ 'TRIM_HORIZON', 'LATEST', ], ], 'EcsContainerOverride' => [ 'type' => 'structure', 'members' => [ 'Command' => [ 'shape' => 'StringList', ], 'Cpu' => [ 'shape' => 'Integer', ], 'Environment' => [ 'shape' => 'EcsEnvironmentVariableList', ], 'EnvironmentFiles' => [ 'shape' => 'EcsEnvironmentFileList', ], 'Memory' => [ 'shape' => 'Integer', ], 'MemoryReservation' => [ 'shape' => 'Integer', ], 'Name' => [ 'shape' => 'String', ], 'ResourceRequirements' => [ 'shape' => 'EcsResourceRequirementsList', ], ], ], 'EcsContainerOverrideList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EcsContainerOverride', ], ], 'EcsEnvironmentFile' => [ 'type' => 'structure', 'required' => [ 'type', 'value', ], 'members' => [ 'type' => [ 'shape' => 'EcsEnvironmentFileType', ], 'value' => [ 'shape' => 'String', ], ], ], 'EcsEnvironmentFileList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EcsEnvironmentFile', ], ], 'EcsEnvironmentFileType' => [ 'type' => 'string', 'enum' => [ 's3', ], ], 'EcsEnvironmentVariable' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], ], 'EcsEnvironmentVariableList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EcsEnvironmentVariable', ], ], 'EcsEphemeralStorage' => [ 'type' => 'structure', 'required' => [ 'sizeInGiB', ], 'members' => [ 'sizeInGiB' => [ 'shape' => 'EphemeralStorageSize', ], ], ], 'EcsInferenceAcceleratorOverride' => [ 'type' => 'structure', 'members' => [ 'deviceName' => [ 'shape' => 'String', ], 'deviceType' => [ 'shape' => 'String', ], ], ], 'EcsInferenceAcceleratorOverrideList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EcsInferenceAcceleratorOverride', ], ], 'EcsResourceRequirement' => [ 'type' => 'structure', 'required' => [ 'type', 'value', ], 'members' => [ 'type' => [ 'shape' => 'EcsResourceRequirementType', ], 'value' => [ 'shape' => 'String', ], ], ], 'EcsResourceRequirementType' => [ 'type' => 'string', 'enum' => [ 'GPU', 'InferenceAccelerator', ], ], 'EcsResourceRequirementsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EcsResourceRequirement', ], ], 'EcsTaskOverride' => [ 'type' => 'structure', 'members' => [ 'ContainerOverrides' => [ 'shape' => 'EcsContainerOverrideList', ], 'Cpu' => [ 'shape' => 'String', ], 'EphemeralStorage' => [ 'shape' => 'EcsEphemeralStorage', ], 'ExecutionRoleArn' => [ 'shape' => 'ArnOrJsonPath', ], 'InferenceAcceleratorOverrides' => [ 'shape' => 'EcsInferenceAcceleratorOverrideList', ], 'Memory' => [ 'shape' => 'String', ], 'TaskRoleArn' => [ 'shape' => 'ArnOrJsonPath', ], ], ], 'EndpointString' => [ 'type' => 'string', 'max' => 300, 'min' => 1, 'pattern' => '(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\\-]*[a-zA-Z0-9])\\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9\\-]*[A-Za-z0-9]):[0-9]{1,5}', 'sensitive' => true, ], 'EphemeralStorageSize' => [ 'type' => 'integer', 'box' => true, 'max' => 200, 'min' => 21, ], 'EpochTimeUnit' => [ 'type' => 'string', 'enum' => [ 'MILLISECONDS', 'SECONDS', 'MICROSECONDS', 'NANOSECONDS', ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'EventBridgeDetailType' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'sensitive' => true, ], 'EventBridgeEndpointId' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '[A-Za-z0-9\\-]+[\\.][A-Za-z0-9\\-]+', 'sensitive' => true, ], 'EventBridgeEventResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ArnOrJsonPath', ], 'max' => 10, 'min' => 0, ], 'EventBridgeEventSource' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*(?=[/\\.\\-_A-Za-z0-9]+)((?!aws\\.).*)|(\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*).*', 'sensitive' => true, ], 'EventPattern' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, 'sensitive' => true, ], 'Filter' => [ 'type' => 'structure', 'members' => [ 'Pattern' => [ 'shape' => 'EventPattern', ], ], ], 'FilterCriteria' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], ], ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], 'max' => 5, 'min' => 0, ], 'FirehoseArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '(^arn:aws([a-z]|\\-)*:firehose:([a-z]{2,4}((-gov)|(-de)|(-iso([a-z]?)))?-[a-z]+(-\\d{1})?):(\\d{12}):deliverystream/[a-zA-Z0-9_.-]{1,64})', ], 'FirehoseLogDestination' => [ 'type' => 'structure', 'members' => [ 'DeliveryStreamArn' => [ 'shape' => 'FirehoseArn', ], ], ], 'FirehoseLogDestinationParameters' => [ 'type' => 'structure', 'required' => [ 'DeliveryStreamArn', ], 'members' => [ 'DeliveryStreamArn' => [ 'shape' => 'FirehoseArn', ], ], ], 'HeaderKey' => [ 'type' => 'string', 'max' => 512, 'min' => 0, 'pattern' => '[!#$%&\'*+-.^_`|~0-9a-zA-Z]+|(\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*)', ], 'HeaderParametersMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'HeaderKey', ], 'value' => [ 'shape' => 'HeaderValue', ], ], 'HeaderValue' => [ 'type' => 'string', 'max' => 512, 'min' => 0, 'pattern' => '[ \\t]*[\\x20-\\x7E]+([ \\t]+[\\x20-\\x7E]+)*[ \\t]*|(\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*)', 'sensitive' => true, ], 'IncludeExecutionData' => [ 'type' => 'list', 'member' => [ 'shape' => 'IncludeExecutionDataOption', ], ], 'IncludeExecutionDataOption' => [ 'type' => 'string', 'enum' => [ 'ALL', ], ], 'InputTemplate' => [ 'type' => 'string', 'max' => 8192, 'min' => 0, 'sensitive' => true, ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'JsonPath' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*', ], 'KafkaBootstrapServers' => [ 'type' => 'list', 'member' => [ 'shape' => 'EndpointString', ], 'max' => 2, 'min' => 0, ], 'KafkaTopicName' => [ 'type' => 'string', 'max' => 249, 'min' => 1, 'pattern' => '[^.]([a-zA-Z0-9\\-_.]+)', 'sensitive' => true, ], 'KinesisPartitionKey' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'sensitive' => true, ], 'KinesisStreamStartPosition' => [ 'type' => 'string', 'enum' => [ 'TRIM_HORIZON', 'LATEST', 'AT_TIMESTAMP', ], ], 'KmsKeyIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[a-zA-Z0-9_\\-/:]*', ], 'LaunchType' => [ 'type' => 'string', 'enum' => [ 'EC2', 'FARGATE', 'EXTERNAL', ], ], 'LimitMax10' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 1, ], 'LimitMax100' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'LimitMax10000' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 1, ], 'LimitMin1' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'ListPipesRequest' => [ 'type' => 'structure', 'members' => [ 'NamePrefix' => [ 'shape' => 'PipeName', 'location' => 'querystring', 'locationName' => 'NamePrefix', ], 'DesiredState' => [ 'shape' => 'RequestedPipeState', 'location' => 'querystring', 'locationName' => 'DesiredState', ], 'CurrentState' => [ 'shape' => 'PipeState', 'location' => 'querystring', 'locationName' => 'CurrentState', ], 'SourcePrefix' => [ 'shape' => 'ResourceArn', 'location' => 'querystring', 'locationName' => 'SourcePrefix', ], 'TargetPrefix' => [ 'shape' => 'ResourceArn', 'location' => 'querystring', 'locationName' => 'TargetPrefix', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'Limit' => [ 'shape' => 'LimitMax100', 'location' => 'querystring', 'locationName' => 'Limit', ], ], ], 'ListPipesResponse' => [ 'type' => 'structure', 'members' => [ 'Pipes' => [ 'shape' => 'PipeList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'PipeArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'LogLevel' => [ 'type' => 'string', 'enum' => [ 'OFF', 'ERROR', 'INFO', 'TRACE', ], ], 'LogStreamName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'MQBrokerAccessCredentials' => [ 'type' => 'structure', 'members' => [ 'BasicAuth' => [ 'shape' => 'SecretManagerArn', ], ], 'union' => true, ], 'MQBrokerQueueName' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '[\\s\\S]*', 'sensitive' => true, ], 'MSKAccessCredentials' => [ 'type' => 'structure', 'members' => [ 'SaslScram512Auth' => [ 'shape' => 'SecretManagerArn', ], 'ClientCertificateTlsAuth' => [ 'shape' => 'SecretManagerArn', ], ], 'union' => true, ], 'MSKStartPosition' => [ 'type' => 'string', 'enum' => [ 'TRIM_HORIZON', 'LATEST', ], ], 'MaximumBatchingWindowInSeconds' => [ 'type' => 'integer', 'box' => true, 'max' => 300, 'min' => 0, ], 'MaximumRecordAgeInSeconds' => [ 'type' => 'integer', 'box' => true, 'max' => 604800, 'min' => -1, ], 'MaximumRetryAttemptsESM' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => -1, ], 'MeasureName' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'MeasureValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'MeasureValueType' => [ 'type' => 'string', 'enum' => [ 'DOUBLE', 'BIGINT', 'VARCHAR', 'BOOLEAN', 'TIMESTAMP', ], ], 'MessageDeduplicationId' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'sensitive' => true, ], 'MessageGroupId' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'sensitive' => true, ], 'MultiMeasureAttributeMapping' => [ 'type' => 'structure', 'required' => [ 'MeasureValue', 'MeasureValueType', 'MultiMeasureAttributeName', ], 'members' => [ 'MeasureValue' => [ 'shape' => 'MeasureValue', ], 'MeasureValueType' => [ 'shape' => 'MeasureValueType', ], 'MultiMeasureAttributeName' => [ 'shape' => 'MultiMeasureAttributeName', ], ], ], 'MultiMeasureAttributeMappings' => [ 'type' => 'list', 'member' => [ 'shape' => 'MultiMeasureAttributeMapping', ], 'max' => 256, 'min' => 1, ], 'MultiMeasureAttributeName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'MultiMeasureMapping' => [ 'type' => 'structure', 'required' => [ 'MultiMeasureName', 'MultiMeasureAttributeMappings', ], 'members' => [ 'MultiMeasureName' => [ 'shape' => 'MultiMeasureName', ], 'MultiMeasureAttributeMappings' => [ 'shape' => 'MultiMeasureAttributeMappings', ], ], ], 'MultiMeasureMappings' => [ 'type' => 'list', 'member' => [ 'shape' => 'MultiMeasureMapping', ], 'max' => 1024, 'min' => 0, ], 'MultiMeasureName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'NetworkConfiguration' => [ 'type' => 'structure', 'members' => [ 'awsvpcConfiguration' => [ 'shape' => 'AwsVpcConfiguration', ], ], ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'sensitive' => true, ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'OnPartialBatchItemFailureStreams' => [ 'type' => 'string', 'enum' => [ 'AUTOMATIC_BISECT', ], ], 'OptionalArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 0, 'pattern' => '$|arn:(aws[a-zA-Z0-9-]*):([a-zA-Z0-9\\-]+):([a-z]{2,4}((-gov)|(-de)|(-iso([a-z]?)))?-[a-z]+(-\\d{1})?)?:(\\d{12})?:(.+)', ], 'PathParameter' => [ 'type' => 'string', 'pattern' => '(?!\\s*$).+|(\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*)', 'sensitive' => true, ], 'PathParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PathParameter', ], ], 'Pipe' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'PipeName', ], 'Arn' => [ 'shape' => 'PipeArn', ], 'DesiredState' => [ 'shape' => 'RequestedPipeState', ], 'CurrentState' => [ 'shape' => 'PipeState', ], 'StateReason' => [ 'shape' => 'PipeStateReason', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'Source' => [ 'shape' => 'ArnOrUrl', ], 'Target' => [ 'shape' => 'Arn', ], 'Enrichment' => [ 'shape' => 'OptionalArn', ], ], ], 'PipeArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => 'arn:aws([a-z]|\\-)*:([a-zA-Z0-9\\-]+):([a-z]|\\d|\\-)*:([0-9]{12})?:(.+)', ], 'PipeDescription' => [ 'type' => 'string', 'max' => 512, 'min' => 0, 'pattern' => '.*', 'sensitive' => true, ], 'PipeEnrichmentHttpParameters' => [ 'type' => 'structure', 'members' => [ 'PathParameterValues' => [ 'shape' => 'PathParameterList', ], 'HeaderParameters' => [ 'shape' => 'HeaderParametersMap', ], 'QueryStringParameters' => [ 'shape' => 'QueryStringParametersMap', ], ], ], 'PipeEnrichmentParameters' => [ 'type' => 'structure', 'members' => [ 'InputTemplate' => [ 'shape' => 'InputTemplate', ], 'HttpParameters' => [ 'shape' => 'PipeEnrichmentHttpParameters', ], ], ], 'PipeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Pipe', ], ], 'PipeLogConfiguration' => [ 'type' => 'structure', 'members' => [ 'S3LogDestination' => [ 'shape' => 'S3LogDestination', ], 'FirehoseLogDestination' => [ 'shape' => 'FirehoseLogDestination', ], 'CloudwatchLogsLogDestination' => [ 'shape' => 'CloudwatchLogsLogDestination', ], 'Level' => [ 'shape' => 'LogLevel', ], 'IncludeExecutionData' => [ 'shape' => 'IncludeExecutionData', ], ], ], 'PipeLogConfigurationParameters' => [ 'type' => 'structure', 'required' => [ 'Level', ], 'members' => [ 'S3LogDestination' => [ 'shape' => 'S3LogDestinationParameters', ], 'FirehoseLogDestination' => [ 'shape' => 'FirehoseLogDestinationParameters', ], 'CloudwatchLogsLogDestination' => [ 'shape' => 'CloudwatchLogsLogDestinationParameters', ], 'Level' => [ 'shape' => 'LogLevel', ], 'IncludeExecutionData' => [ 'shape' => 'IncludeExecutionData', ], ], ], 'PipeName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[\\.\\-_A-Za-z0-9]+', ], 'PipeSourceActiveMQBrokerParameters' => [ 'type' => 'structure', 'required' => [ 'Credentials', 'QueueName', ], 'members' => [ 'Credentials' => [ 'shape' => 'MQBrokerAccessCredentials', ], 'QueueName' => [ 'shape' => 'MQBrokerQueueName', ], 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], ], ], 'PipeSourceDynamoDBStreamParameters' => [ 'type' => 'structure', 'required' => [ 'StartingPosition', ], 'members' => [ 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'DeadLetterConfig' => [ 'shape' => 'DeadLetterConfig', ], 'OnPartialBatchItemFailure' => [ 'shape' => 'OnPartialBatchItemFailureStreams', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], 'MaximumRecordAgeInSeconds' => [ 'shape' => 'MaximumRecordAgeInSeconds', ], 'MaximumRetryAttempts' => [ 'shape' => 'MaximumRetryAttemptsESM', ], 'ParallelizationFactor' => [ 'shape' => 'LimitMax10', ], 'StartingPosition' => [ 'shape' => 'DynamoDBStreamStartPosition', ], ], ], 'PipeSourceKinesisStreamParameters' => [ 'type' => 'structure', 'required' => [ 'StartingPosition', ], 'members' => [ 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'DeadLetterConfig' => [ 'shape' => 'DeadLetterConfig', ], 'OnPartialBatchItemFailure' => [ 'shape' => 'OnPartialBatchItemFailureStreams', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], 'MaximumRecordAgeInSeconds' => [ 'shape' => 'MaximumRecordAgeInSeconds', ], 'MaximumRetryAttempts' => [ 'shape' => 'MaximumRetryAttemptsESM', ], 'ParallelizationFactor' => [ 'shape' => 'LimitMax10', ], 'StartingPosition' => [ 'shape' => 'KinesisStreamStartPosition', ], 'StartingPositionTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'PipeSourceManagedStreamingKafkaParameters' => [ 'type' => 'structure', 'required' => [ 'TopicName', ], 'members' => [ 'TopicName' => [ 'shape' => 'KafkaTopicName', ], 'StartingPosition' => [ 'shape' => 'MSKStartPosition', ], 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], 'ConsumerGroupID' => [ 'shape' => 'URI', ], 'Credentials' => [ 'shape' => 'MSKAccessCredentials', ], ], ], 'PipeSourceParameters' => [ 'type' => 'structure', 'members' => [ 'FilterCriteria' => [ 'shape' => 'FilterCriteria', ], 'KinesisStreamParameters' => [ 'shape' => 'PipeSourceKinesisStreamParameters', ], 'DynamoDBStreamParameters' => [ 'shape' => 'PipeSourceDynamoDBStreamParameters', ], 'SqsQueueParameters' => [ 'shape' => 'PipeSourceSqsQueueParameters', ], 'ActiveMQBrokerParameters' => [ 'shape' => 'PipeSourceActiveMQBrokerParameters', ], 'RabbitMQBrokerParameters' => [ 'shape' => 'PipeSourceRabbitMQBrokerParameters', ], 'ManagedStreamingKafkaParameters' => [ 'shape' => 'PipeSourceManagedStreamingKafkaParameters', ], 'SelfManagedKafkaParameters' => [ 'shape' => 'PipeSourceSelfManagedKafkaParameters', ], ], ], 'PipeSourceRabbitMQBrokerParameters' => [ 'type' => 'structure', 'required' => [ 'Credentials', 'QueueName', ], 'members' => [ 'Credentials' => [ 'shape' => 'MQBrokerAccessCredentials', ], 'QueueName' => [ 'shape' => 'MQBrokerQueueName', ], 'VirtualHost' => [ 'shape' => 'URI', ], 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], ], ], 'PipeSourceSelfManagedKafkaParameters' => [ 'type' => 'structure', 'required' => [ 'TopicName', ], 'members' => [ 'TopicName' => [ 'shape' => 'KafkaTopicName', ], 'StartingPosition' => [ 'shape' => 'SelfManagedKafkaStartPosition', ], 'AdditionalBootstrapServers' => [ 'shape' => 'KafkaBootstrapServers', ], 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], 'ConsumerGroupID' => [ 'shape' => 'URI', ], 'Credentials' => [ 'shape' => 'SelfManagedKafkaAccessConfigurationCredentials', ], 'ServerRootCaCertificate' => [ 'shape' => 'SecretManagerArn', ], 'Vpc' => [ 'shape' => 'SelfManagedKafkaAccessConfigurationVpc', ], ], ], 'PipeSourceSqsQueueParameters' => [ 'type' => 'structure', 'members' => [ 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], ], ], 'PipeState' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'STOPPED', 'CREATING', 'UPDATING', 'DELETING', 'STARTING', 'STOPPING', 'CREATE_FAILED', 'UPDATE_FAILED', 'START_FAILED', 'STOP_FAILED', 'DELETE_FAILED', 'CREATE_ROLLBACK_FAILED', 'DELETE_ROLLBACK_FAILED', 'UPDATE_ROLLBACK_FAILED', ], ], 'PipeStateReason' => [ 'type' => 'string', 'max' => 512, 'min' => 0, 'pattern' => '.*', ], 'PipeTargetBatchJobParameters' => [ 'type' => 'structure', 'required' => [ 'JobDefinition', 'JobName', ], 'members' => [ 'JobDefinition' => [ 'shape' => 'String', ], 'JobName' => [ 'shape' => 'String', ], 'ArrayProperties' => [ 'shape' => 'BatchArrayProperties', ], 'RetryStrategy' => [ 'shape' => 'BatchRetryStrategy', ], 'ContainerOverrides' => [ 'shape' => 'BatchContainerOverrides', ], 'DependsOn' => [ 'shape' => 'BatchDependsOn', ], 'Parameters' => [ 'shape' => 'BatchParametersMap', ], ], ], 'PipeTargetCloudWatchLogsParameters' => [ 'type' => 'structure', 'members' => [ 'LogStreamName' => [ 'shape' => 'LogStreamName', ], 'Timestamp' => [ 'shape' => 'JsonPath', ], ], ], 'PipeTargetEcsTaskParameters' => [ 'type' => 'structure', 'required' => [ 'TaskDefinitionArn', ], 'members' => [ 'TaskDefinitionArn' => [ 'shape' => 'ArnOrJsonPath', ], 'TaskCount' => [ 'shape' => 'LimitMin1', ], 'LaunchType' => [ 'shape' => 'LaunchType', ], 'NetworkConfiguration' => [ 'shape' => 'NetworkConfiguration', ], 'PlatformVersion' => [ 'shape' => 'String', ], 'Group' => [ 'shape' => 'String', ], 'CapacityProviderStrategy' => [ 'shape' => 'CapacityProviderStrategy', ], 'EnableECSManagedTags' => [ 'shape' => 'Boolean', ], 'EnableExecuteCommand' => [ 'shape' => 'Boolean', ], 'PlacementConstraints' => [ 'shape' => 'PlacementConstraints', ], 'PlacementStrategy' => [ 'shape' => 'PlacementStrategies', ], 'PropagateTags' => [ 'shape' => 'PropagateTags', ], 'ReferenceId' => [ 'shape' => 'ReferenceId', ], 'Overrides' => [ 'shape' => 'EcsTaskOverride', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'PipeTargetEventBridgeEventBusParameters' => [ 'type' => 'structure', 'members' => [ 'EndpointId' => [ 'shape' => 'EventBridgeEndpointId', ], 'DetailType' => [ 'shape' => 'EventBridgeDetailType', ], 'Source' => [ 'shape' => 'EventBridgeEventSource', ], 'Resources' => [ 'shape' => 'EventBridgeEventResourceList', ], 'Time' => [ 'shape' => 'JsonPath', ], ], ], 'PipeTargetHttpParameters' => [ 'type' => 'structure', 'members' => [ 'PathParameterValues' => [ 'shape' => 'PathParameterList', ], 'HeaderParameters' => [ 'shape' => 'HeaderParametersMap', ], 'QueryStringParameters' => [ 'shape' => 'QueryStringParametersMap', ], ], ], 'PipeTargetInvocationType' => [ 'type' => 'string', 'enum' => [ 'REQUEST_RESPONSE', 'FIRE_AND_FORGET', ], ], 'PipeTargetKinesisStreamParameters' => [ 'type' => 'structure', 'required' => [ 'PartitionKey', ], 'members' => [ 'PartitionKey' => [ 'shape' => 'KinesisPartitionKey', ], ], ], 'PipeTargetLambdaFunctionParameters' => [ 'type' => 'structure', 'members' => [ 'InvocationType' => [ 'shape' => 'PipeTargetInvocationType', ], ], ], 'PipeTargetParameters' => [ 'type' => 'structure', 'members' => [ 'InputTemplate' => [ 'shape' => 'InputTemplate', ], 'LambdaFunctionParameters' => [ 'shape' => 'PipeTargetLambdaFunctionParameters', ], 'StepFunctionStateMachineParameters' => [ 'shape' => 'PipeTargetStateMachineParameters', ], 'KinesisStreamParameters' => [ 'shape' => 'PipeTargetKinesisStreamParameters', ], 'EcsTaskParameters' => [ 'shape' => 'PipeTargetEcsTaskParameters', ], 'BatchJobParameters' => [ 'shape' => 'PipeTargetBatchJobParameters', ], 'SqsQueueParameters' => [ 'shape' => 'PipeTargetSqsQueueParameters', ], 'HttpParameters' => [ 'shape' => 'PipeTargetHttpParameters', ], 'RedshiftDataParameters' => [ 'shape' => 'PipeTargetRedshiftDataParameters', ], 'SageMakerPipelineParameters' => [ 'shape' => 'PipeTargetSageMakerPipelineParameters', ], 'EventBridgeEventBusParameters' => [ 'shape' => 'PipeTargetEventBridgeEventBusParameters', ], 'CloudWatchLogsParameters' => [ 'shape' => 'PipeTargetCloudWatchLogsParameters', ], 'TimestreamParameters' => [ 'shape' => 'PipeTargetTimestreamParameters', ], ], ], 'PipeTargetRedshiftDataParameters' => [ 'type' => 'structure', 'required' => [ 'Database', 'Sqls', ], 'members' => [ 'SecretManagerArn' => [ 'shape' => 'SecretManagerArnOrJsonPath', ], 'Database' => [ 'shape' => 'Database', ], 'DbUser' => [ 'shape' => 'DbUser', ], 'StatementName' => [ 'shape' => 'StatementName', ], 'WithEvent' => [ 'shape' => 'Boolean', ], 'Sqls' => [ 'shape' => 'Sqls', ], ], ], 'PipeTargetSageMakerPipelineParameters' => [ 'type' => 'structure', 'members' => [ 'PipelineParameterList' => [ 'shape' => 'SageMakerPipelineParameterList', ], ], ], 'PipeTargetSqsQueueParameters' => [ 'type' => 'structure', 'members' => [ 'MessageGroupId' => [ 'shape' => 'MessageGroupId', ], 'MessageDeduplicationId' => [ 'shape' => 'MessageDeduplicationId', ], ], ], 'PipeTargetStateMachineParameters' => [ 'type' => 'structure', 'members' => [ 'InvocationType' => [ 'shape' => 'PipeTargetInvocationType', ], ], ], 'PipeTargetTimestreamParameters' => [ 'type' => 'structure', 'required' => [ 'TimeValue', 'VersionValue', 'DimensionMappings', ], 'members' => [ 'TimeValue' => [ 'shape' => 'TimeValue', ], 'EpochTimeUnit' => [ 'shape' => 'EpochTimeUnit', ], 'TimeFieldType' => [ 'shape' => 'TimeFieldType', ], 'TimestampFormat' => [ 'shape' => 'TimestampFormat', ], 'VersionValue' => [ 'shape' => 'VersionValue', ], 'DimensionMappings' => [ 'shape' => 'DimensionMappings', ], 'SingleMeasureMappings' => [ 'shape' => 'SingleMeasureMappings', ], 'MultiMeasureMappings' => [ 'shape' => 'MultiMeasureMappings', ], ], ], 'PlacementConstraint' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'PlacementConstraintType', ], 'expression' => [ 'shape' => 'PlacementConstraintExpression', ], ], ], 'PlacementConstraintExpression' => [ 'type' => 'string', 'max' => 2000, 'min' => 0, 'sensitive' => true, ], 'PlacementConstraintType' => [ 'type' => 'string', 'enum' => [ 'distinctInstance', 'memberOf', ], ], 'PlacementConstraints' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlacementConstraint', ], 'max' => 10, 'min' => 0, ], 'PlacementStrategies' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlacementStrategy', ], 'max' => 5, 'min' => 0, ], 'PlacementStrategy' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'PlacementStrategyType', ], 'field' => [ 'shape' => 'PlacementStrategyField', ], ], ], 'PlacementStrategyField' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'sensitive' => true, ], 'PlacementStrategyType' => [ 'type' => 'string', 'enum' => [ 'random', 'spread', 'binpack', ], ], 'PropagateTags' => [ 'type' => 'string', 'enum' => [ 'TASK_DEFINITION', ], ], 'QueryStringKey' => [ 'type' => 'string', 'max' => 512, 'min' => 0, 'pattern' => '[^\\x00-\\x1F\\x7F]+|(\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*)', ], 'QueryStringParametersMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'QueryStringKey', ], 'value' => [ 'shape' => 'QueryStringValue', ], ], 'QueryStringValue' => [ 'type' => 'string', 'max' => 512, 'min' => 0, 'pattern' => '[^\\x00-\\x09\\x0B\\x0C\\x0E-\\x1F\\x7F]+|(\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*)', 'sensitive' => true, ], 'ReferenceId' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'sensitive' => true, ], 'RequestedPipeState' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'STOPPED', ], ], 'RequestedPipeStateDescribeResponse' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'STOPPED', 'DELETED', ], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, ], 'RoleArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => 'arn:(aws[a-zA-Z-]*)?:iam::\\d{12}:role/?[a-zA-Z0-9+=,.@\\-_/]+', ], 'S3LogDestination' => [ 'type' => 'structure', 'members' => [ 'BucketName' => [ 'shape' => 'String', ], 'Prefix' => [ 'shape' => 'String', ], 'BucketOwner' => [ 'shape' => 'String', ], 'OutputFormat' => [ 'shape' => 'S3OutputFormat', ], ], ], 'S3LogDestinationParameters' => [ 'type' => 'structure', 'required' => [ 'BucketName', 'BucketOwner', ], 'members' => [ 'BucketName' => [ 'shape' => 'S3LogDestinationParametersBucketNameString', ], 'BucketOwner' => [ 'shape' => 'S3LogDestinationParametersBucketOwnerString', ], 'OutputFormat' => [ 'shape' => 'S3OutputFormat', ], 'Prefix' => [ 'shape' => 'S3LogDestinationParametersPrefixString', ], ], ], 'S3LogDestinationParametersBucketNameString' => [ 'type' => 'string', 'max' => 63, 'min' => 3, ], 'S3LogDestinationParametersBucketOwnerString' => [ 'type' => 'string', 'pattern' => '\\d{12}', ], 'S3LogDestinationParametersPrefixString' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'S3OutputFormat' => [ 'type' => 'string', 'enum' => [ 'json', 'plain', 'w3c', ], ], 'SageMakerPipelineParameter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'SageMakerPipelineParameterName', ], 'Value' => [ 'shape' => 'SageMakerPipelineParameterValue', ], ], ], 'SageMakerPipelineParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SageMakerPipelineParameter', ], 'max' => 200, 'min' => 0, ], 'SageMakerPipelineParameterName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9](-*[a-zA-Z0-9])*|(\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*)', 'sensitive' => true, ], 'SageMakerPipelineParameterValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'sensitive' => true, ], 'SecretManagerArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '(^arn:aws([a-z]|\\-)*:secretsmanager:([a-z]{2,4}((-gov)|(-de)|(-iso([a-z]?)))?-[a-z]+(-\\d{1})?):(\\d{12}):secret:.+)', ], 'SecretManagerArnOrJsonPath' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '(^arn:aws([a-z]|\\-)*:secretsmanager:([a-z]{2,4}((-gov)|(-de)|(-iso([a-z]?)))?-[a-z]+(-\\d{1})?):(\\d{12}):secret:.+)|(\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*)', ], 'SecurityGroup' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => 'sg-[0-9a-zA-Z]*|(\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*)', 'sensitive' => true, ], 'SecurityGroupId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => 'sg-[0-9a-zA-Z]*', 'sensitive' => true, ], 'SecurityGroupIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 5, 'min' => 1, ], 'SecurityGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroup', ], 'max' => 5, 'min' => 0, ], 'SelfManagedKafkaAccessConfigurationCredentials' => [ 'type' => 'structure', 'members' => [ 'BasicAuth' => [ 'shape' => 'SecretManagerArn', ], 'SaslScram512Auth' => [ 'shape' => 'SecretManagerArn', ], 'SaslScram256Auth' => [ 'shape' => 'SecretManagerArn', ], 'ClientCertificateTlsAuth' => [ 'shape' => 'SecretManagerArn', ], ], 'union' => true, ], 'SelfManagedKafkaAccessConfigurationVpc' => [ 'type' => 'structure', 'members' => [ 'Subnets' => [ 'shape' => 'SubnetIds', ], 'SecurityGroup' => [ 'shape' => 'SecurityGroupIds', ], ], ], 'SelfManagedKafkaStartPosition' => [ 'type' => 'string', 'enum' => [ 'TRIM_HORIZON', 'LATEST', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', 'serviceCode', 'quotaCode', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SingleMeasureMapping' => [ 'type' => 'structure', 'required' => [ 'MeasureValue', 'MeasureValueType', 'MeasureName', ], 'members' => [ 'MeasureValue' => [ 'shape' => 'MeasureValue', ], 'MeasureValueType' => [ 'shape' => 'MeasureValueType', ], 'MeasureName' => [ 'shape' => 'MeasureName', ], ], ], 'SingleMeasureMappings' => [ 'type' => 'list', 'member' => [ 'shape' => 'SingleMeasureMapping', ], 'max' => 8192, 'min' => 0, ], 'Sql' => [ 'type' => 'string', 'max' => 100000, 'min' => 1, 'sensitive' => true, ], 'Sqls' => [ 'type' => 'list', 'member' => [ 'shape' => 'Sql', ], 'max' => 40, 'min' => 1, ], 'StartPipeRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'PipeName', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'StartPipeResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PipeArn', ], 'Name' => [ 'shape' => 'PipeName', ], 'DesiredState' => [ 'shape' => 'RequestedPipeState', ], 'CurrentState' => [ 'shape' => 'PipeState', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'StatementName' => [ 'type' => 'string', 'max' => 500, 'min' => 1, 'sensitive' => true, ], 'StopPipeRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'PipeName', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'StopPipeResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PipeArn', ], 'Name' => [ 'shape' => 'PipeName', ], 'DesiredState' => [ 'shape' => 'RequestedPipeState', ], 'CurrentState' => [ 'shape' => 'PipeState', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Subnet' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => 'subnet-[0-9a-z]*|(\\$(\\.[\\w/_-]+(\\[(\\d+|\\*)\\])*)*)', 'sensitive' => true, ], 'SubnetId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => 'subnet-[0-9a-z]*', 'sensitive' => true, ], 'SubnetIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], 'max' => 16, 'min' => 1, ], 'Subnets' => [ 'type' => 'list', 'member' => [ 'shape' => 'Subnet', ], 'max' => 16, 'min' => 0, ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'PipeArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'sensitive' => true, ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'TimeFieldType' => [ 'type' => 'string', 'enum' => [ 'EPOCH', 'TIMESTAMP_FORMAT', ], ], 'TimeValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TimestampFormat' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'URI' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[a-zA-Z0-9-\\/*:_+=.@-]*', 'sensitive' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'PipeArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdatePipeRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RoleArn', ], 'members' => [ 'Name' => [ 'shape' => 'PipeName', 'location' => 'uri', 'locationName' => 'Name', ], 'Description' => [ 'shape' => 'PipeDescription', ], 'DesiredState' => [ 'shape' => 'RequestedPipeState', ], 'SourceParameters' => [ 'shape' => 'UpdatePipeSourceParameters', ], 'Enrichment' => [ 'shape' => 'OptionalArn', ], 'EnrichmentParameters' => [ 'shape' => 'PipeEnrichmentParameters', ], 'Target' => [ 'shape' => 'Arn', ], 'TargetParameters' => [ 'shape' => 'PipeTargetParameters', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'LogConfiguration' => [ 'shape' => 'PipeLogConfigurationParameters', ], 'KmsKeyIdentifier' => [ 'shape' => 'KmsKeyIdentifier', ], ], ], 'UpdatePipeResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PipeArn', ], 'Name' => [ 'shape' => 'PipeName', ], 'DesiredState' => [ 'shape' => 'RequestedPipeState', ], 'CurrentState' => [ 'shape' => 'PipeState', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdatePipeSourceActiveMQBrokerParameters' => [ 'type' => 'structure', 'required' => [ 'Credentials', ], 'members' => [ 'Credentials' => [ 'shape' => 'MQBrokerAccessCredentials', ], 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], ], ], 'UpdatePipeSourceDynamoDBStreamParameters' => [ 'type' => 'structure', 'members' => [ 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'DeadLetterConfig' => [ 'shape' => 'DeadLetterConfig', ], 'OnPartialBatchItemFailure' => [ 'shape' => 'OnPartialBatchItemFailureStreams', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], 'MaximumRecordAgeInSeconds' => [ 'shape' => 'MaximumRecordAgeInSeconds', ], 'MaximumRetryAttempts' => [ 'shape' => 'MaximumRetryAttemptsESM', ], 'ParallelizationFactor' => [ 'shape' => 'LimitMax10', ], ], ], 'UpdatePipeSourceKinesisStreamParameters' => [ 'type' => 'structure', 'members' => [ 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'DeadLetterConfig' => [ 'shape' => 'DeadLetterConfig', ], 'OnPartialBatchItemFailure' => [ 'shape' => 'OnPartialBatchItemFailureStreams', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], 'MaximumRecordAgeInSeconds' => [ 'shape' => 'MaximumRecordAgeInSeconds', ], 'MaximumRetryAttempts' => [ 'shape' => 'MaximumRetryAttemptsESM', ], 'ParallelizationFactor' => [ 'shape' => 'LimitMax10', ], ], ], 'UpdatePipeSourceManagedStreamingKafkaParameters' => [ 'type' => 'structure', 'members' => [ 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'Credentials' => [ 'shape' => 'MSKAccessCredentials', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], ], ], 'UpdatePipeSourceParameters' => [ 'type' => 'structure', 'members' => [ 'FilterCriteria' => [ 'shape' => 'FilterCriteria', ], 'KinesisStreamParameters' => [ 'shape' => 'UpdatePipeSourceKinesisStreamParameters', ], 'DynamoDBStreamParameters' => [ 'shape' => 'UpdatePipeSourceDynamoDBStreamParameters', ], 'SqsQueueParameters' => [ 'shape' => 'UpdatePipeSourceSqsQueueParameters', ], 'ActiveMQBrokerParameters' => [ 'shape' => 'UpdatePipeSourceActiveMQBrokerParameters', ], 'RabbitMQBrokerParameters' => [ 'shape' => 'UpdatePipeSourceRabbitMQBrokerParameters', ], 'ManagedStreamingKafkaParameters' => [ 'shape' => 'UpdatePipeSourceManagedStreamingKafkaParameters', ], 'SelfManagedKafkaParameters' => [ 'shape' => 'UpdatePipeSourceSelfManagedKafkaParameters', ], ], ], 'UpdatePipeSourceRabbitMQBrokerParameters' => [ 'type' => 'structure', 'required' => [ 'Credentials', ], 'members' => [ 'Credentials' => [ 'shape' => 'MQBrokerAccessCredentials', ], 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], ], ], 'UpdatePipeSourceSelfManagedKafkaParameters' => [ 'type' => 'structure', 'members' => [ 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], 'Credentials' => [ 'shape' => 'SelfManagedKafkaAccessConfigurationCredentials', ], 'ServerRootCaCertificate' => [ 'shape' => 'SecretManagerArn', ], 'Vpc' => [ 'shape' => 'SelfManagedKafkaAccessConfigurationVpc', ], ], ], 'UpdatePipeSourceSqsQueueParameters' => [ 'type' => 'structure', 'members' => [ 'BatchSize' => [ 'shape' => 'LimitMax10000', ], 'MaximumBatchingWindowInSeconds' => [ 'shape' => 'MaximumBatchingWindowInSeconds', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'name', 'message', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'ErrorMessage', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'VersionValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], ],];
