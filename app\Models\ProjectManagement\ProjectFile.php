<?php

namespace App\Models\ProjectManagement;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

/**
 * نموذج ملفات المشروع
 * 
 * يدير الملفات المرفقة مع المشاريع والمهام
 * مع دعم أنواع ملفات متعددة وإدارة التخزين
 * 
 * @package App\Models\ProjectManagement
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
class ProjectFile extends Model
{
    use SoftDeletes;

    /**
     * اسم الجدول
     * 
     * @var string
     */
    protected $table = 'project_files';

    /**
     * الحقول القابلة للتعبئة
     * 
     * @var array
     */
    protected $fillable = [
        'project_id',
        'task_id',
        'uploaded_by',
        'name',
        'original_name',
        'file_path',
        'file_size',
        'file_type',
        'mime_type',
        'description',
        'is_public',
        'download_count',
        'metadata'
    ];

    /**
     * الحقول المخفية
     * 
     * @var array
     */
    protected $hidden = [
        'file_path'
    ];

    /**
     * تحويل الحقول
     * 
     * @var array
     */
    protected $casts = [
        'is_public' => 'boolean',
        'file_size' => 'integer',
        'download_count' => 'integer',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * العلاقة مع المشروع
     * 
     * @return BelongsTo
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * العلاقة مع المهمة
     * 
     * @return BelongsTo
     */
    public function task(): BelongsTo
    {
        return $this->belongsTo(Task::class);
    }

    /**
     * العلاقة مع المستخدم الذي رفع الملف
     * 
     * @return BelongsTo
     */
    public function uploader(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'uploaded_by');
    }

    /**
     * الحصول على رابط تحميل الملف
     * 
     * @return string
     */
    public function getDownloadUrlAttribute(): string
    {
        return route('project-files.download', $this->id);
    }

    /**
     * الحصول على رابط عرض الملف
     * 
     * @return string
     */
    public function getViewUrlAttribute(): string
    {
        if ($this->isImage()) {
            return Storage::url($this->file_path);
        }
        
        return route('project-files.view', $this->id);
    }

    /**
     * الحصول على حجم الملف منسق
     * 
     * @return string
     */
    public function getFormattedSizeAttribute(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * الحصول على أيقونة الملف
     * 
     * @return string
     */
    public function getIconAttribute(): string
    {
        $icons = [
            'pdf' => 'fas fa-file-pdf text-red-500',
            'doc' => 'fas fa-file-word text-blue-500',
            'docx' => 'fas fa-file-word text-blue-500',
            'xls' => 'fas fa-file-excel text-green-500',
            'xlsx' => 'fas fa-file-excel text-green-500',
            'ppt' => 'fas fa-file-powerpoint text-orange-500',
            'pptx' => 'fas fa-file-powerpoint text-orange-500',
            'txt' => 'fas fa-file-alt text-gray-500',
            'zip' => 'fas fa-file-archive text-yellow-500',
            'rar' => 'fas fa-file-archive text-yellow-500',
            'jpg' => 'fas fa-file-image text-purple-500',
            'jpeg' => 'fas fa-file-image text-purple-500',
            'png' => 'fas fa-file-image text-purple-500',
            'gif' => 'fas fa-file-image text-purple-500',
            'mp4' => 'fas fa-file-video text-red-500',
            'avi' => 'fas fa-file-video text-red-500',
            'mp3' => 'fas fa-file-audio text-green-500',
            'wav' => 'fas fa-file-audio text-green-500'
        ];

        $extension = strtolower(pathinfo($this->original_name, PATHINFO_EXTENSION));
        return $icons[$extension] ?? 'fas fa-file text-gray-500';
    }

    /**
     * التحقق من أن الملف صورة
     * 
     * @return bool
     */
    public function isImage(): bool
    {
        $imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'];
        $extension = strtolower(pathinfo($this->original_name, PATHINFO_EXTENSION));
        
        return in_array($extension, $imageTypes);
    }

    /**
     * التحقق من أن الملف مستند
     * 
     * @return bool
     */
    public function isDocument(): bool
    {
        $documentTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];
        $extension = strtolower(pathinfo($this->original_name, PATHINFO_EXTENSION));
        
        return in_array($extension, $documentTypes);
    }

    /**
     * التحقق من أن الملف فيديو
     * 
     * @return bool
     */
    public function isVideo(): bool
    {
        $videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
        $extension = strtolower(pathinfo($this->original_name, PATHINFO_EXTENSION));
        
        return in_array($extension, $videoTypes);
    }

    /**
     * التحقق من أن الملف صوتي
     * 
     * @return bool
     */
    public function isAudio(): bool
    {
        $audioTypes = ['mp3', 'wav', 'ogg', 'aac', 'flac'];
        $extension = strtolower(pathinfo($this->original_name, PATHINFO_EXTENSION));
        
        return in_array($extension, $audioTypes);
    }

    /**
     * زيادة عداد التحميل
     * 
     * @return void
     */
    public function incrementDownloadCount(): void
    {
        $this->increment('download_count');
    }

    /**
     * حذف الملف من التخزين
     * 
     * @return bool
     */
    public function deleteFile(): bool
    {
        if (Storage::exists($this->file_path)) {
            return Storage::delete($this->file_path);
        }
        
        return true;
    }

    /**
     * التحقق من وجود الملف في التخزين
     * 
     * @return bool
     */
    public function fileExists(): bool
    {
        return Storage::exists($this->file_path);
    }

    /**
     * الحصول على محتوى الملف
     * 
     * @return string|null
     */
    public function getFileContent(): ?string
    {
        if ($this->fileExists()) {
            return Storage::get($this->file_path);
        }
        
        return null;
    }

    /**
     * scope للملفات العامة
     * 
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * scope للملفات الخاصة
     * 
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePrivate($query)
    {
        return $query->where('is_public', false);
    }

    /**
     * scope للصور
     * 
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeImages($query)
    {
        return $query->whereIn('file_type', ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']);
    }

    /**
     * scope للمستندات
     * 
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDocuments($query)
    {
        return $query->whereIn('file_type', ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']);
    }

    /**
     * scope لنوع ملف معين
     * 
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('file_type', $type);
    }

    /**
     * scope للملفات المرفوعة بواسطة مستخدم معين
     * 
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUploadedBy($query, int $userId)
    {
        return $query->where('uploaded_by', $userId);
    }

    /**
     * أحداث النموذج
     */
    protected static function boot()
    {
        parent::boot();

        // حذف الملف من التخزين عند حذف السجل نهائياً
        static::forceDeleted(function ($file) {
            $file->deleteFile();
        });
    }
}
