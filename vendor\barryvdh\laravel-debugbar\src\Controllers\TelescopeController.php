<?php

namespace Barryvdh\Debugbar\Controllers;

use Barryvdh\Debugbar\Support\Clockwork\Converter;
use DebugBar\OpenHandler;
use Illuminate\Http\Response;
use <PERSON><PERSON>\Telescope\Contracts\EntriesRepository;
use <PERSON><PERSON>\Telescope\IncomingEntry;
use <PERSON><PERSON>\Telescope\Storage\EntryQueryOptions;
use <PERSON><PERSON>\Telescope\Telescope;

class TelescopeController extends BaseController
{
    public function show(EntriesRepository $storage, $uuid)
    {

        $entry = $storage->find($uuid);
        $result = $storage->get('request', (new EntryQueryOptions())->batchId($entry->batchId))->first();

        return redirect(config('telescope.domain') . '/' . config('telescope.path') . '/requests/' . $result->id);
    }
}
