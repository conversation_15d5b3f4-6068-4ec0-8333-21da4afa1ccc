net\authorize\api\contract\v1\GetCustomerPaymentProfileListRequest:
    xml_root_name: getCustomerPaymentProfileListRequest
    xml_root_namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
    properties:
        searchType:
            expose: true
            access_type: public_method
            serialized_name: searchType
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getSearchType
                setter: setSearchType
            type: string
        month:
            expose: true
            access_type: public_method
            serialized_name: month
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getMonth
                setter: setMonth
            type: string
        sorting:
            expose: true
            access_type: public_method
            serialized_name: sorting
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getSorting
                setter: setSorting
            type: net\authorize\api\contract\v1\CustomerPaymentProfileSortingType
        paging:
            expose: true
            access_type: public_method
            serialized_name: paging
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getPaging
                setter: setPaging
            type: net\authorize\api\contract\v1\PagingType
