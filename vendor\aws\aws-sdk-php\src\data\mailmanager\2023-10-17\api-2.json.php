<?php
// This file was auto-generated from sdk-root/src/data/mailmanager/2023-10-17/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-10-17', 'endpointPrefix' => 'mail-manager', 'jsonVersion' => '1.0', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'MailManager', 'serviceId' => 'MailManager', 'signatureVersion' => 'v4', 'signingName' => 'ses', 'targetPrefix' => 'MailManagerSvc', 'uid' => 'mailmanager-2023-10-17', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'CreateAddonInstance' => [ 'name' => 'CreateAddonInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAddonInstanceRequest', ], 'output' => [ 'shape' => 'CreateAddonInstanceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'CreateAddonSubscription' => [ 'name' => 'CreateAddonSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAddonSubscriptionRequest', ], 'output' => [ 'shape' => 'CreateAddonSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'CreateAddressList' => [ 'name' => 'CreateAddressList', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAddressListRequest', ], 'output' => [ 'shape' => 'CreateAddressListResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'CreateAddressListImportJob' => [ 'name' => 'CreateAddressListImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAddressListImportJobRequest', ], 'output' => [ 'shape' => 'CreateAddressListImportJobResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'CreateArchive' => [ 'name' => 'CreateArchive', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateArchiveRequest', ], 'output' => [ 'shape' => 'CreateArchiveResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'CreateIngressPoint' => [ 'name' => 'CreateIngressPoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateIngressPointRequest', ], 'output' => [ 'shape' => 'CreateIngressPointResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'CreateRelay' => [ 'name' => 'CreateRelay', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRelayRequest', ], 'output' => [ 'shape' => 'CreateRelayResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'CreateRuleSet' => [ 'name' => 'CreateRuleSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRuleSetRequest', ], 'output' => [ 'shape' => 'CreateRuleSetResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'CreateTrafficPolicy' => [ 'name' => 'CreateTrafficPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTrafficPolicyRequest', ], 'output' => [ 'shape' => 'CreateTrafficPolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteAddonInstance' => [ 'name' => 'DeleteAddonInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAddonInstanceRequest', ], 'output' => [ 'shape' => 'DeleteAddonInstanceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteAddonSubscription' => [ 'name' => 'DeleteAddonSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAddonSubscriptionRequest', ], 'output' => [ 'shape' => 'DeleteAddonSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteAddressList' => [ 'name' => 'DeleteAddressList', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAddressListRequest', ], 'output' => [ 'shape' => 'DeleteAddressListResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteArchive' => [ 'name' => 'DeleteArchive', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteArchiveRequest', ], 'output' => [ 'shape' => 'DeleteArchiveResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteIngressPoint' => [ 'name' => 'DeleteIngressPoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteIngressPointRequest', ], 'output' => [ 'shape' => 'DeleteIngressPointResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteRelay' => [ 'name' => 'DeleteRelay', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRelayRequest', ], 'output' => [ 'shape' => 'DeleteRelayResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteRuleSet' => [ 'name' => 'DeleteRuleSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRuleSetRequest', ], 'output' => [ 'shape' => 'DeleteRuleSetResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteTrafficPolicy' => [ 'name' => 'DeleteTrafficPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTrafficPolicyRequest', ], 'output' => [ 'shape' => 'DeleteTrafficPolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeregisterMemberFromAddressList' => [ 'name' => 'DeregisterMemberFromAddressList', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterMemberFromAddressListRequest', ], 'output' => [ 'shape' => 'DeregisterMemberFromAddressListResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'GetAddonInstance' => [ 'name' => 'GetAddonInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAddonInstanceRequest', ], 'output' => [ 'shape' => 'GetAddonInstanceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAddonSubscription' => [ 'name' => 'GetAddonSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAddonSubscriptionRequest', ], 'output' => [ 'shape' => 'GetAddonSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAddressList' => [ 'name' => 'GetAddressList', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAddressListRequest', ], 'output' => [ 'shape' => 'GetAddressListResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetAddressListImportJob' => [ 'name' => 'GetAddressListImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAddressListImportJobRequest', ], 'output' => [ 'shape' => 'GetAddressListImportJobResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetArchive' => [ 'name' => 'GetArchive', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetArchiveRequest', ], 'output' => [ 'shape' => 'GetArchiveResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetArchiveExport' => [ 'name' => 'GetArchiveExport', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetArchiveExportRequest', ], 'output' => [ 'shape' => 'GetArchiveExportResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetArchiveMessage' => [ 'name' => 'GetArchiveMessage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetArchiveMessageRequest', ], 'output' => [ 'shape' => 'GetArchiveMessageResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetArchiveMessageContent' => [ 'name' => 'GetArchiveMessageContent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetArchiveMessageContentRequest', ], 'output' => [ 'shape' => 'GetArchiveMessageContentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetArchiveSearch' => [ 'name' => 'GetArchiveSearch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetArchiveSearchRequest', ], 'output' => [ 'shape' => 'GetArchiveSearchResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetArchiveSearchResults' => [ 'name' => 'GetArchiveSearchResults', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetArchiveSearchResultsRequest', ], 'output' => [ 'shape' => 'GetArchiveSearchResultsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetIngressPoint' => [ 'name' => 'GetIngressPoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetIngressPointRequest', ], 'output' => [ 'shape' => 'GetIngressPointResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetMemberOfAddressList' => [ 'name' => 'GetMemberOfAddressList', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMemberOfAddressListRequest', ], 'output' => [ 'shape' => 'GetMemberOfAddressListResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetRelay' => [ 'name' => 'GetRelay', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRelayRequest', ], 'output' => [ 'shape' => 'GetRelayResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetRuleSet' => [ 'name' => 'GetRuleSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRuleSetRequest', ], 'output' => [ 'shape' => 'GetRuleSetResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetTrafficPolicy' => [ 'name' => 'GetTrafficPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTrafficPolicyRequest', ], 'output' => [ 'shape' => 'GetTrafficPolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListAddonInstances' => [ 'name' => 'ListAddonInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAddonInstancesRequest', ], 'output' => [ 'shape' => 'ListAddonInstancesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'ListAddonSubscriptions' => [ 'name' => 'ListAddonSubscriptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAddonSubscriptionsRequest', ], 'output' => [ 'shape' => 'ListAddonSubscriptionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'ListAddressListImportJobs' => [ 'name' => 'ListAddressListImportJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAddressListImportJobsRequest', ], 'output' => [ 'shape' => 'ListAddressListImportJobsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListAddressLists' => [ 'name' => 'ListAddressLists', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAddressListsRequest', ], 'output' => [ 'shape' => 'ListAddressListsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListArchiveExports' => [ 'name' => 'ListArchiveExports', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListArchiveExportsRequest', ], 'output' => [ 'shape' => 'ListArchiveExportsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListArchiveSearches' => [ 'name' => 'ListArchiveSearches', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListArchiveSearchesRequest', ], 'output' => [ 'shape' => 'ListArchiveSearchesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListArchives' => [ 'name' => 'ListArchives', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListArchivesRequest', ], 'output' => [ 'shape' => 'ListArchivesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListIngressPoints' => [ 'name' => 'ListIngressPoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListIngressPointsRequest', ], 'output' => [ 'shape' => 'ListIngressPointsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'ListMembersOfAddressList' => [ 'name' => 'ListMembersOfAddressList', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListMembersOfAddressListRequest', ], 'output' => [ 'shape' => 'ListMembersOfAddressListResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListRelays' => [ 'name' => 'ListRelays', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRelaysRequest', ], 'output' => [ 'shape' => 'ListRelaysResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'ListRuleSets' => [ 'name' => 'ListRuleSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRuleSetsRequest', ], 'output' => [ 'shape' => 'ListRuleSetsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTrafficPolicies' => [ 'name' => 'ListTrafficPolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTrafficPoliciesRequest', ], 'output' => [ 'shape' => 'ListTrafficPoliciesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'RegisterMemberToAddressList' => [ 'name' => 'RegisterMemberToAddressList', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterMemberToAddressListRequest', ], 'output' => [ 'shape' => 'RegisterMemberToAddressListResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'StartAddressListImportJob' => [ 'name' => 'StartAddressListImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartAddressListImportJobRequest', ], 'output' => [ 'shape' => 'StartAddressListImportJobResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'StartArchiveExport' => [ 'name' => 'StartArchiveExport', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartArchiveExportRequest', ], 'output' => [ 'shape' => 'StartArchiveExportResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'StartArchiveSearch' => [ 'name' => 'StartArchiveSearch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartArchiveSearchRequest', ], 'output' => [ 'shape' => 'StartArchiveSearchResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'StopAddressListImportJob' => [ 'name' => 'StopAddressListImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopAddressListImportJobRequest', ], 'output' => [ 'shape' => 'StopAddressListImportJobResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'StopArchiveExport' => [ 'name' => 'StopArchiveExport', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopArchiveExportRequest', ], 'output' => [ 'shape' => 'StopArchiveExportResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'StopArchiveSearch' => [ 'name' => 'StopArchiveSearch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopArchiveSearchRequest', ], 'output' => [ 'shape' => 'StopArchiveSearchResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateArchive' => [ 'name' => 'UpdateArchive', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateArchiveRequest', ], 'output' => [ 'shape' => 'UpdateArchiveResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdateIngressPoint' => [ 'name' => 'UpdateIngressPoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateIngressPointRequest', ], 'output' => [ 'shape' => 'UpdateIngressPointResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateRelay' => [ 'name' => 'UpdateRelay', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRelayRequest', ], 'output' => [ 'shape' => 'UpdateRelayResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateRuleSet' => [ 'name' => 'UpdateRuleSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRuleSetRequest', ], 'output' => [ 'shape' => 'UpdateRuleSetResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateTrafficPolicy' => [ 'name' => 'UpdateTrafficPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTrafficPolicyRequest', ], 'output' => [ 'shape' => 'UpdateTrafficPolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AcceptAction' => [ 'type' => 'string', 'enum' => [ 'ALLOW', 'DENY', ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ActionFailurePolicy' => [ 'type' => 'string', 'enum' => [ 'CONTINUE', 'DROP', ], ], 'AddHeaderAction' => [ 'type' => 'structure', 'required' => [ 'HeaderName', 'HeaderValue', ], 'members' => [ 'HeaderName' => [ 'shape' => 'HeaderName', ], 'HeaderValue' => [ 'shape' => 'HeaderValue', ], ], ], 'AddonInstance' => [ 'type' => 'structure', 'members' => [ 'AddonInstanceArn' => [ 'shape' => 'AddonInstanceArn', ], 'AddonInstanceId' => [ 'shape' => 'AddonInstanceId', ], 'AddonName' => [ 'shape' => 'AddonName', ], 'AddonSubscriptionId' => [ 'shape' => 'AddonSubscriptionId', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'AddonInstanceArn' => [ 'type' => 'string', ], 'AddonInstanceId' => [ 'type' => 'string', 'max' => 67, 'min' => 4, 'pattern' => '^ai-[a-zA-Z0-9]{1,64}$', ], 'AddonInstances' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddonInstance', ], ], 'AddonName' => [ 'type' => 'string', ], 'AddonSubscription' => [ 'type' => 'structure', 'members' => [ 'AddonName' => [ 'shape' => 'AddonName', ], 'AddonSubscriptionArn' => [ 'shape' => 'AddonSubscriptionArn', ], 'AddonSubscriptionId' => [ 'shape' => 'AddonSubscriptionId', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'AddonSubscriptionArn' => [ 'type' => 'string', ], 'AddonSubscriptionId' => [ 'type' => 'string', 'max' => 67, 'min' => 4, 'pattern' => '^as-[a-zA-Z0-9]{1,64}$', ], 'AddonSubscriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddonSubscription', ], ], 'Address' => [ 'type' => 'string', 'max' => 320, 'min' => 3, 'sensitive' => true, ], 'AddressFilter' => [ 'type' => 'structure', 'members' => [ 'AddressPrefix' => [ 'shape' => 'AddressPrefix', ], ], ], 'AddressList' => [ 'type' => 'structure', 'required' => [ 'AddressListArn', 'AddressListId', 'AddressListName', 'CreatedTimestamp', 'LastUpdatedTimestamp', ], 'members' => [ 'AddressListArn' => [ 'shape' => 'AddressListArn', ], 'AddressListId' => [ 'shape' => 'AddressListId', ], 'AddressListName' => [ 'shape' => 'AddressListName', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'AddressListArn' => [ 'type' => 'string', ], 'AddressListId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-]+$', ], 'AddressListName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_.-]+$', ], 'AddressLists' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddressList', ], ], 'AddressPageSize' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'AddressPrefix' => [ 'type' => 'string', 'max' => 320, 'min' => 1, 'sensitive' => true, ], 'Analysis' => [ 'type' => 'structure', 'required' => [ 'Analyzer', 'ResultField', ], 'members' => [ 'Analyzer' => [ 'shape' => 'AnalyzerArn', ], 'ResultField' => [ 'shape' => 'ResultField', ], ], ], 'AnalyzerArn' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9:_/+=,@.#-]+$', ], 'Archive' => [ 'type' => 'structure', 'required' => [ 'ArchiveId', ], 'members' => [ 'ArchiveId' => [ 'shape' => 'ArchiveIdString', ], 'ArchiveName' => [ 'shape' => 'ArchiveNameString', ], 'ArchiveState' => [ 'shape' => 'ArchiveState', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'ArchiveAction' => [ 'type' => 'structure', 'required' => [ 'TargetArchive', ], 'members' => [ 'ActionFailurePolicy' => [ 'shape' => 'ActionFailurePolicy', ], 'TargetArchive' => [ 'shape' => 'NameOrArn', ], ], ], 'ArchiveArn' => [ 'type' => 'string', ], 'ArchiveBooleanEmailAttribute' => [ 'type' => 'string', 'enum' => [ 'HAS_ATTACHMENTS', ], ], 'ArchiveBooleanExpression' => [ 'type' => 'structure', 'required' => [ 'Evaluate', 'Operator', ], 'members' => [ 'Evaluate' => [ 'shape' => 'ArchiveBooleanToEvaluate', ], 'Operator' => [ 'shape' => 'ArchiveBooleanOperator', ], ], ], 'ArchiveBooleanOperator' => [ 'type' => 'string', 'enum' => [ 'IS_TRUE', 'IS_FALSE', ], ], 'ArchiveBooleanToEvaluate' => [ 'type' => 'structure', 'members' => [ 'Attribute' => [ 'shape' => 'ArchiveBooleanEmailAttribute', ], ], 'union' => true, ], 'ArchiveFilterCondition' => [ 'type' => 'structure', 'members' => [ 'BooleanExpression' => [ 'shape' => 'ArchiveBooleanExpression', ], 'StringExpression' => [ 'shape' => 'ArchiveStringExpression', ], ], 'union' => true, ], 'ArchiveFilterConditions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ArchiveFilterCondition', ], 'max' => 10, 'min' => 0, ], 'ArchiveFilters' => [ 'type' => 'structure', 'members' => [ 'Include' => [ 'shape' => 'ArchiveFilterConditions', ], 'Unless' => [ 'shape' => 'ArchiveFilterConditions', ], ], ], 'ArchiveId' => [ 'type' => 'string', 'max' => 66, 'min' => 3, 'pattern' => '^a-[\\w]{1,64}$', ], 'ArchiveIdString' => [ 'type' => 'string', 'max' => 66, 'min' => 1, ], 'ArchiveNameString' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]$', ], 'ArchiveRetention' => [ 'type' => 'structure', 'members' => [ 'RetentionPeriod' => [ 'shape' => 'RetentionPeriod', ], ], 'union' => true, ], 'ArchiveState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'PENDING_DELETION', ], ], 'ArchiveStringEmailAttribute' => [ 'type' => 'string', 'enum' => [ 'TO', 'FROM', 'CC', 'SUBJECT', 'ENVELOPE_TO', 'ENVELOPE_FROM', ], ], 'ArchiveStringExpression' => [ 'type' => 'structure', 'required' => [ 'Evaluate', 'Operator', 'Values', ], 'members' => [ 'Evaluate' => [ 'shape' => 'ArchiveStringToEvaluate', ], 'Operator' => [ 'shape' => 'ArchiveStringOperator', ], 'Values' => [ 'shape' => 'StringValueList', ], ], ], 'ArchiveStringOperator' => [ 'type' => 'string', 'enum' => [ 'CONTAINS', ], ], 'ArchiveStringToEvaluate' => [ 'type' => 'structure', 'members' => [ 'Attribute' => [ 'shape' => 'ArchiveStringEmailAttribute', ], ], 'union' => true, ], 'ArchivedMessageId' => [ 'type' => 'string', ], 'ArchivesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Archive', ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'CreateAddonInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'AddonSubscriptionId', ], 'members' => [ 'AddonSubscriptionId' => [ 'shape' => 'AddonSubscriptionId', ], 'ClientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateAddonInstanceResponse' => [ 'type' => 'structure', 'required' => [ 'AddonInstanceId', ], 'members' => [ 'AddonInstanceId' => [ 'shape' => 'AddonInstanceId', ], ], ], 'CreateAddonSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'AddonName', ], 'members' => [ 'AddonName' => [ 'shape' => 'AddonName', ], 'ClientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateAddonSubscriptionResponse' => [ 'type' => 'structure', 'required' => [ 'AddonSubscriptionId', ], 'members' => [ 'AddonSubscriptionId' => [ 'shape' => 'AddonSubscriptionId', ], ], ], 'CreateAddressListImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'AddressListId', 'ImportDataFormat', 'Name', ], 'members' => [ 'AddressListId' => [ 'shape' => 'AddressListId', ], 'ClientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'ImportDataFormat' => [ 'shape' => 'ImportDataFormat', ], 'Name' => [ 'shape' => 'JobName', ], ], ], 'CreateAddressListImportJobResponse' => [ 'type' => 'structure', 'required' => [ 'JobId', 'PreSignedUrl', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'PreSignedUrl' => [ 'shape' => 'PreSignedUrl', ], ], ], 'CreateAddressListRequest' => [ 'type' => 'structure', 'required' => [ 'AddressListName', ], 'members' => [ 'AddressListName' => [ 'shape' => 'AddressListName', ], 'ClientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateAddressListResponse' => [ 'type' => 'structure', 'required' => [ 'AddressListId', ], 'members' => [ 'AddressListId' => [ 'shape' => 'AddressListId', ], ], ], 'CreateArchiveRequest' => [ 'type' => 'structure', 'required' => [ 'ArchiveName', ], 'members' => [ 'ArchiveName' => [ 'shape' => 'ArchiveNameString', ], 'ClientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'KmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'Retention' => [ 'shape' => 'ArchiveRetention', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateArchiveResponse' => [ 'type' => 'structure', 'required' => [ 'ArchiveId', ], 'members' => [ 'ArchiveId' => [ 'shape' => 'ArchiveIdString', ], ], ], 'CreateIngressPointRequest' => [ 'type' => 'structure', 'required' => [ 'IngressPointName', 'RuleSetId', 'TrafficPolicyId', 'Type', ], 'members' => [ 'ClientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'IngressPointConfiguration' => [ 'shape' => 'IngressPointConfiguration', ], 'IngressPointName' => [ 'shape' => 'IngressPointName', ], 'NetworkConfiguration' => [ 'shape' => 'NetworkConfiguration', ], 'RuleSetId' => [ 'shape' => 'RuleSetId', ], 'Tags' => [ 'shape' => 'TagList', ], 'TrafficPolicyId' => [ 'shape' => 'TrafficPolicyId', ], 'Type' => [ 'shape' => 'IngressPointType', ], ], ], 'CreateIngressPointResponse' => [ 'type' => 'structure', 'required' => [ 'IngressPointId', ], 'members' => [ 'IngressPointId' => [ 'shape' => 'IngressPointId', ], ], ], 'CreateRelayRequest' => [ 'type' => 'structure', 'required' => [ 'Authentication', 'RelayName', 'ServerName', 'ServerPort', ], 'members' => [ 'Authentication' => [ 'shape' => 'RelayAuthentication', ], 'ClientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'RelayName' => [ 'shape' => 'RelayName', ], 'ServerName' => [ 'shape' => 'RelayServerName', ], 'ServerPort' => [ 'shape' => 'RelayServerPort', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateRelayResponse' => [ 'type' => 'structure', 'required' => [ 'RelayId', ], 'members' => [ 'RelayId' => [ 'shape' => 'RelayId', ], ], ], 'CreateRuleSetRequest' => [ 'type' => 'structure', 'required' => [ 'RuleSetName', 'Rules', ], 'members' => [ 'ClientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'RuleSetName' => [ 'shape' => 'RuleSetName', ], 'Rules' => [ 'shape' => 'Rules', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateRuleSetResponse' => [ 'type' => 'structure', 'required' => [ 'RuleSetId', ], 'members' => [ 'RuleSetId' => [ 'shape' => 'RuleSetId', ], ], ], 'CreateTrafficPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'DefaultAction', 'PolicyStatements', 'TrafficPolicyName', ], 'members' => [ 'ClientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'DefaultAction' => [ 'shape' => 'AcceptAction', ], 'MaxMessageSizeBytes' => [ 'shape' => 'MaxMessageSizeBytes', ], 'PolicyStatements' => [ 'shape' => 'PolicyStatementList', ], 'Tags' => [ 'shape' => 'TagList', ], 'TrafficPolicyName' => [ 'shape' => 'TrafficPolicyName', ], ], ], 'CreateTrafficPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'TrafficPolicyId', ], 'members' => [ 'TrafficPolicyId' => [ 'shape' => 'TrafficPolicyId', ], ], ], 'DeleteAddonInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'AddonInstanceId', ], 'members' => [ 'AddonInstanceId' => [ 'shape' => 'AddonInstanceId', ], ], ], 'DeleteAddonInstanceResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAddonSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'AddonSubscriptionId', ], 'members' => [ 'AddonSubscriptionId' => [ 'shape' => 'AddonSubscriptionId', ], ], ], 'DeleteAddonSubscriptionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAddressListRequest' => [ 'type' => 'structure', 'required' => [ 'AddressListId', ], 'members' => [ 'AddressListId' => [ 'shape' => 'AddressListId', ], ], ], 'DeleteAddressListResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteArchiveRequest' => [ 'type' => 'structure', 'required' => [ 'ArchiveId', ], 'members' => [ 'ArchiveId' => [ 'shape' => 'ArchiveIdString', ], ], ], 'DeleteArchiveResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteIngressPointRequest' => [ 'type' => 'structure', 'required' => [ 'IngressPointId', ], 'members' => [ 'IngressPointId' => [ 'shape' => 'IngressPointId', ], ], ], 'DeleteIngressPointResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRelayRequest' => [ 'type' => 'structure', 'required' => [ 'RelayId', ], 'members' => [ 'RelayId' => [ 'shape' => 'RelayId', ], ], ], 'DeleteRelayResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRuleSetRequest' => [ 'type' => 'structure', 'required' => [ 'RuleSetId', ], 'members' => [ 'RuleSetId' => [ 'shape' => 'RuleSetId', ], ], ], 'DeleteRuleSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTrafficPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'TrafficPolicyId', ], 'members' => [ 'TrafficPolicyId' => [ 'shape' => 'TrafficPolicyId', ], ], ], 'DeleteTrafficPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeliverToMailboxAction' => [ 'type' => 'structure', 'required' => [ 'MailboxArn', 'RoleArn', ], 'members' => [ 'ActionFailurePolicy' => [ 'shape' => 'ActionFailurePolicy', ], 'MailboxArn' => [ 'shape' => 'NameOrArn', ], 'RoleArn' => [ 'shape' => 'IamRoleArn', ], ], ], 'DeliverToQBusinessAction' => [ 'type' => 'structure', 'required' => [ 'ApplicationId', 'IndexId', 'RoleArn', ], 'members' => [ 'ActionFailurePolicy' => [ 'shape' => 'ActionFailurePolicy', ], 'ApplicationId' => [ 'shape' => 'QBusinessApplicationId', ], 'IndexId' => [ 'shape' => 'QBusinessIndexId', ], 'RoleArn' => [ 'shape' => 'IamRoleArn', ], ], ], 'DeregisterMemberFromAddressListRequest' => [ 'type' => 'structure', 'required' => [ 'Address', 'AddressListId', ], 'members' => [ 'Address' => [ 'shape' => 'Address', ], 'AddressListId' => [ 'shape' => 'AddressListId', ], ], ], 'DeregisterMemberFromAddressListResponse' => [ 'type' => 'structure', 'members' => [], ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'DropAction' => [ 'type' => 'structure', 'members' => [], ], 'EmailAddress' => [ 'type' => 'string', 'max' => 254, 'min' => 0, 'pattern' => '^[0-9A-Za-z@+.-]+$', 'sensitive' => true, ], 'EmailReceivedHeadersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Envelope' => [ 'type' => 'structure', 'members' => [ 'From' => [ 'shape' => 'String', ], 'Helo' => [ 'shape' => 'String', ], 'To' => [ 'shape' => 'StringList', ], ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'ExportDestinationConfiguration' => [ 'type' => 'structure', 'members' => [ 'S3' => [ 'shape' => 'S3ExportDestinationConfiguration', ], ], 'union' => true, ], 'ExportId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ExportMaxResults' => [ 'type' => 'integer', 'box' => true, ], 'ExportState' => [ 'type' => 'string', 'enum' => [ 'QUEUED', 'PREPROCESSING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED', ], ], 'ExportStatus' => [ 'type' => 'structure', 'members' => [ 'CompletionTimestamp' => [ 'shape' => 'Timestamp', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], 'State' => [ 'shape' => 'ExportState', ], 'SubmissionTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'ExportSummary' => [ 'type' => 'structure', 'members' => [ 'ExportId' => [ 'shape' => 'ExportId', ], 'Status' => [ 'shape' => 'ExportStatus', ], ], ], 'ExportSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportSummary', ], ], 'GetAddonInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'AddonInstanceId', ], 'members' => [ 'AddonInstanceId' => [ 'shape' => 'AddonInstanceId', ], ], ], 'GetAddonInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'AddonInstanceArn' => [ 'shape' => 'AddonInstanceArn', ], 'AddonName' => [ 'shape' => 'AddonName', ], 'AddonSubscriptionId' => [ 'shape' => 'AddonSubscriptionId', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'GetAddonSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'AddonSubscriptionId', ], 'members' => [ 'AddonSubscriptionId' => [ 'shape' => 'AddonSubscriptionId', ], ], ], 'GetAddonSubscriptionResponse' => [ 'type' => 'structure', 'members' => [ 'AddonName' => [ 'shape' => 'AddonName', ], 'AddonSubscriptionArn' => [ 'shape' => 'AddonSubscriptionArn', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'GetAddressListImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'GetAddressListImportJobResponse' => [ 'type' => 'structure', 'required' => [ 'AddressListId', 'CreatedTimestamp', 'ImportDataFormat', 'JobId', 'Name', 'PreSignedUrl', 'Status', ], 'members' => [ 'AddressListId' => [ 'shape' => 'AddressListId', ], 'CompletedTimestamp' => [ 'shape' => 'Timestamp', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'Error' => [ 'shape' => 'ErrorMessage', ], 'FailedItemsCount' => [ 'shape' => 'JobItemsCount', ], 'ImportDataFormat' => [ 'shape' => 'ImportDataFormat', ], 'ImportedItemsCount' => [ 'shape' => 'JobItemsCount', ], 'JobId' => [ 'shape' => 'JobId', ], 'Name' => [ 'shape' => 'JobName', ], 'PreSignedUrl' => [ 'shape' => 'PreSignedUrl', ], 'StartTimestamp' => [ 'shape' => 'Timestamp', ], 'Status' => [ 'shape' => 'ImportJobStatus', ], ], ], 'GetAddressListRequest' => [ 'type' => 'structure', 'required' => [ 'AddressListId', ], 'members' => [ 'AddressListId' => [ 'shape' => 'AddressListId', ], ], ], 'GetAddressListResponse' => [ 'type' => 'structure', 'required' => [ 'AddressListArn', 'AddressListId', 'AddressListName', 'CreatedTimestamp', 'LastUpdatedTimestamp', ], 'members' => [ 'AddressListArn' => [ 'shape' => 'AddressListArn', ], 'AddressListId' => [ 'shape' => 'AddressListId', ], 'AddressListName' => [ 'shape' => 'AddressListName', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'GetArchiveExportRequest' => [ 'type' => 'structure', 'required' => [ 'ExportId', ], 'members' => [ 'ExportId' => [ 'shape' => 'ExportId', ], ], ], 'GetArchiveExportResponse' => [ 'type' => 'structure', 'members' => [ 'ArchiveId' => [ 'shape' => 'ArchiveId', ], 'ExportDestinationConfiguration' => [ 'shape' => 'ExportDestinationConfiguration', ], 'Filters' => [ 'shape' => 'ArchiveFilters', ], 'FromTimestamp' => [ 'shape' => 'Timestamp', ], 'MaxResults' => [ 'shape' => 'ExportMaxResults', ], 'Status' => [ 'shape' => 'ExportStatus', ], 'ToTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'GetArchiveMessageContentRequest' => [ 'type' => 'structure', 'required' => [ 'ArchivedMessageId', ], 'members' => [ 'ArchivedMessageId' => [ 'shape' => 'ArchivedMessageId', ], ], ], 'GetArchiveMessageContentResponse' => [ 'type' => 'structure', 'members' => [ 'Body' => [ 'shape' => 'MessageBody', ], ], ], 'GetArchiveMessageRequest' => [ 'type' => 'structure', 'required' => [ 'ArchivedMessageId', ], 'members' => [ 'ArchivedMessageId' => [ 'shape' => 'ArchivedMessageId', ], ], ], 'GetArchiveMessageResponse' => [ 'type' => 'structure', 'members' => [ 'Envelope' => [ 'shape' => 'Envelope', ], 'MessageDownloadLink' => [ 'shape' => 'S3PresignedURL', ], 'Metadata' => [ 'shape' => 'Metadata', ], ], ], 'GetArchiveRequest' => [ 'type' => 'structure', 'required' => [ 'ArchiveId', ], 'members' => [ 'ArchiveId' => [ 'shape' => 'ArchiveIdString', ], ], ], 'GetArchiveResponse' => [ 'type' => 'structure', 'required' => [ 'ArchiveArn', 'ArchiveId', 'ArchiveName', 'ArchiveState', 'Retention', ], 'members' => [ 'ArchiveArn' => [ 'shape' => 'ArchiveArn', ], 'ArchiveId' => [ 'shape' => 'ArchiveIdString', ], 'ArchiveName' => [ 'shape' => 'ArchiveNameString', ], 'ArchiveState' => [ 'shape' => 'ArchiveState', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'KmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], 'Retention' => [ 'shape' => 'ArchiveRetention', ], ], ], 'GetArchiveSearchRequest' => [ 'type' => 'structure', 'required' => [ 'SearchId', ], 'members' => [ 'SearchId' => [ 'shape' => 'SearchId', ], ], ], 'GetArchiveSearchResponse' => [ 'type' => 'structure', 'members' => [ 'ArchiveId' => [ 'shape' => 'ArchiveId', ], 'Filters' => [ 'shape' => 'ArchiveFilters', ], 'FromTimestamp' => [ 'shape' => 'Timestamp', ], 'MaxResults' => [ 'shape' => 'SearchMaxResults', ], 'Status' => [ 'shape' => 'SearchStatus', ], 'ToTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'GetArchiveSearchResultsRequest' => [ 'type' => 'structure', 'required' => [ 'SearchId', ], 'members' => [ 'SearchId' => [ 'shape' => 'SearchId', ], ], ], 'GetArchiveSearchResultsResponse' => [ 'type' => 'structure', 'members' => [ 'Rows' => [ 'shape' => 'RowsList', ], ], ], 'GetIngressPointRequest' => [ 'type' => 'structure', 'required' => [ 'IngressPointId', ], 'members' => [ 'IngressPointId' => [ 'shape' => 'IngressPointId', ], ], ], 'GetIngressPointResponse' => [ 'type' => 'structure', 'required' => [ 'IngressPointId', 'IngressPointName', ], 'members' => [ 'ARecord' => [ 'shape' => 'IngressPointARecord', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'IngressPointArn' => [ 'shape' => 'IngressPointArn', ], 'IngressPointAuthConfiguration' => [ 'shape' => 'IngressPointAuthConfiguration', ], 'IngressPointId' => [ 'shape' => 'IngressPointId', ], 'IngressPointName' => [ 'shape' => 'IngressPointName', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], 'NetworkConfiguration' => [ 'shape' => 'NetworkConfiguration', ], 'RuleSetId' => [ 'shape' => 'RuleSetId', ], 'Status' => [ 'shape' => 'IngressPointStatus', ], 'TrafficPolicyId' => [ 'shape' => 'TrafficPolicyId', ], 'Type' => [ 'shape' => 'IngressPointType', ], ], ], 'GetMemberOfAddressListRequest' => [ 'type' => 'structure', 'required' => [ 'Address', 'AddressListId', ], 'members' => [ 'Address' => [ 'shape' => 'Address', ], 'AddressListId' => [ 'shape' => 'AddressListId', ], ], ], 'GetMemberOfAddressListResponse' => [ 'type' => 'structure', 'required' => [ 'Address', 'CreatedTimestamp', ], 'members' => [ 'Address' => [ 'shape' => 'Address', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'GetRelayRequest' => [ 'type' => 'structure', 'required' => [ 'RelayId', ], 'members' => [ 'RelayId' => [ 'shape' => 'RelayId', ], ], ], 'GetRelayResponse' => [ 'type' => 'structure', 'required' => [ 'RelayId', ], 'members' => [ 'Authentication' => [ 'shape' => 'RelayAuthentication', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'LastModifiedTimestamp' => [ 'shape' => 'Timestamp', ], 'RelayArn' => [ 'shape' => 'RelayArn', ], 'RelayId' => [ 'shape' => 'RelayId', ], 'RelayName' => [ 'shape' => 'RelayName', ], 'ServerName' => [ 'shape' => 'RelayServerName', ], 'ServerPort' => [ 'shape' => 'RelayServerPort', ], ], ], 'GetRuleSetRequest' => [ 'type' => 'structure', 'required' => [ 'RuleSetId', ], 'members' => [ 'RuleSetId' => [ 'shape' => 'RuleSetId', ], ], ], 'GetRuleSetResponse' => [ 'type' => 'structure', 'required' => [ 'CreatedDate', 'LastModificationDate', 'RuleSetArn', 'RuleSetId', 'RuleSetName', 'Rules', ], 'members' => [ 'CreatedDate' => [ 'shape' => 'Timestamp', ], 'LastModificationDate' => [ 'shape' => 'Timestamp', ], 'RuleSetArn' => [ 'shape' => 'RuleSetArn', ], 'RuleSetId' => [ 'shape' => 'RuleSetId', ], 'RuleSetName' => [ 'shape' => 'RuleSetName', ], 'Rules' => [ 'shape' => 'Rules', ], ], ], 'GetTrafficPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'TrafficPolicyId', ], 'members' => [ 'TrafficPolicyId' => [ 'shape' => 'TrafficPolicyId', ], ], ], 'GetTrafficPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'TrafficPolicyId', 'TrafficPolicyName', ], 'members' => [ 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'DefaultAction' => [ 'shape' => 'AcceptAction', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], 'MaxMessageSizeBytes' => [ 'shape' => 'MaxMessageSizeBytes', ], 'PolicyStatements' => [ 'shape' => 'PolicyStatementList', ], 'TrafficPolicyArn' => [ 'shape' => 'TrafficPolicyArn', ], 'TrafficPolicyId' => [ 'shape' => 'TrafficPolicyId', ], 'TrafficPolicyName' => [ 'shape' => 'TrafficPolicyName', ], ], ], 'HeaderName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[xX]\\-[a-zA-Z0-9\\-]+$', ], 'HeaderValue' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'IamRoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^[a-zA-Z0-9:_/+=,@.#-]+$', ], 'IdOrArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^[a-zA-Z0-9:_/+=,@.#-]+$', ], 'IdempotencyToken' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'ImportDataFormat' => [ 'type' => 'structure', 'required' => [ 'ImportDataType', ], 'members' => [ 'ImportDataType' => [ 'shape' => 'ImportDataType', ], ], ], 'ImportDataType' => [ 'type' => 'string', 'enum' => [ 'CSV', 'JSON', ], ], 'ImportJob' => [ 'type' => 'structure', 'required' => [ 'AddressListId', 'CreatedTimestamp', 'ImportDataFormat', 'JobId', 'Name', 'PreSignedUrl', 'Status', ], 'members' => [ 'AddressListId' => [ 'shape' => 'AddressListId', ], 'CompletedTimestamp' => [ 'shape' => 'Timestamp', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'Error' => [ 'shape' => 'ErrorMessage', ], 'FailedItemsCount' => [ 'shape' => 'JobItemsCount', ], 'ImportDataFormat' => [ 'shape' => 'ImportDataFormat', ], 'ImportedItemsCount' => [ 'shape' => 'JobItemsCount', ], 'JobId' => [ 'shape' => 'JobId', ], 'Name' => [ 'shape' => 'JobName', ], 'PreSignedUrl' => [ 'shape' => 'PreSignedUrl', ], 'StartTimestamp' => [ 'shape' => 'Timestamp', ], 'Status' => [ 'shape' => 'ImportJobStatus', ], ], ], 'ImportJobStatus' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'PROCESSING', 'COMPLETED', 'FAILED', 'STOPPED', ], ], 'ImportJobs' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImportJob', ], ], 'IngressAddressListArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddressListArn', ], 'max' => 1, 'min' => 1, ], 'IngressAddressListEmailAttribute' => [ 'type' => 'string', 'enum' => [ 'RECIPIENT', ], ], 'IngressAnalysis' => [ 'type' => 'structure', 'required' => [ 'Analyzer', 'ResultField', ], 'members' => [ 'Analyzer' => [ 'shape' => 'AnalyzerArn', ], 'ResultField' => [ 'shape' => 'ResultField', ], ], ], 'IngressBooleanExpression' => [ 'type' => 'structure', 'required' => [ 'Evaluate', 'Operator', ], 'members' => [ 'Evaluate' => [ 'shape' => 'IngressBooleanToEvaluate', ], 'Operator' => [ 'shape' => 'IngressBooleanOperator', ], ], ], 'IngressBooleanOperator' => [ 'type' => 'string', 'enum' => [ 'IS_TRUE', 'IS_FALSE', ], ], 'IngressBooleanToEvaluate' => [ 'type' => 'structure', 'members' => [ 'Analysis' => [ 'shape' => 'IngressAnalysis', ], 'IsInAddressList' => [ 'shape' => 'IngressIsInAddressList', ], ], 'union' => true, ], 'IngressIpOperator' => [ 'type' => 'string', 'enum' => [ 'CIDR_MATCHES', 'NOT_CIDR_MATCHES', ], ], 'IngressIpToEvaluate' => [ 'type' => 'structure', 'members' => [ 'Attribute' => [ 'shape' => 'IngressIpv4Attribute', ], ], 'union' => true, ], 'IngressIpv4Attribute' => [ 'type' => 'string', 'enum' => [ 'SENDER_IP', ], ], 'IngressIpv4Expression' => [ 'type' => 'structure', 'required' => [ 'Evaluate', 'Operator', 'Values', ], 'members' => [ 'Evaluate' => [ 'shape' => 'IngressIpToEvaluate', ], 'Operator' => [ 'shape' => 'IngressIpOperator', ], 'Values' => [ 'shape' => 'Ipv4Cidrs', ], ], ], 'IngressIpv6Attribute' => [ 'type' => 'string', 'enum' => [ 'SENDER_IPV6', ], ], 'IngressIpv6Expression' => [ 'type' => 'structure', 'required' => [ 'Evaluate', 'Operator', 'Values', ], 'members' => [ 'Evaluate' => [ 'shape' => 'IngressIpv6ToEvaluate', ], 'Operator' => [ 'shape' => 'IngressIpOperator', ], 'Values' => [ 'shape' => 'Ipv6Cidrs', ], ], ], 'IngressIpv6ToEvaluate' => [ 'type' => 'structure', 'members' => [ 'Attribute' => [ 'shape' => 'IngressIpv6Attribute', ], ], 'union' => true, ], 'IngressIsInAddressList' => [ 'type' => 'structure', 'required' => [ 'AddressLists', 'Attribute', ], 'members' => [ 'AddressLists' => [ 'shape' => 'IngressAddressListArnList', ], 'Attribute' => [ 'shape' => 'IngressAddressListEmailAttribute', ], ], ], 'IngressPoint' => [ 'type' => 'structure', 'required' => [ 'IngressPointId', 'IngressPointName', 'Status', 'Type', ], 'members' => [ 'ARecord' => [ 'shape' => 'IngressPointARecord', ], 'IngressPointId' => [ 'shape' => 'IngressPointId', ], 'IngressPointName' => [ 'shape' => 'IngressPointName', ], 'Status' => [ 'shape' => 'IngressPointStatus', ], 'Type' => [ 'shape' => 'IngressPointType', ], ], ], 'IngressPointARecord' => [ 'type' => 'string', ], 'IngressPointArn' => [ 'type' => 'string', ], 'IngressPointAuthConfiguration' => [ 'type' => 'structure', 'members' => [ 'IngressPointPasswordConfiguration' => [ 'shape' => 'IngressPointPasswordConfiguration', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], ], ], 'IngressPointConfiguration' => [ 'type' => 'structure', 'members' => [ 'SecretArn' => [ 'shape' => 'SecretArn', ], 'SmtpPassword' => [ 'shape' => 'SmtpPassword', ], ], 'union' => true, ], 'IngressPointId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'IngressPointName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[A-Za-z0-9_\\-]+$', ], 'IngressPointPasswordConfiguration' => [ 'type' => 'structure', 'members' => [ 'PreviousSmtpPasswordExpiryTimestamp' => [ 'shape' => 'Timestamp', ], 'PreviousSmtpPasswordVersion' => [ 'shape' => 'String', ], 'SmtpPasswordVersion' => [ 'shape' => 'String', ], ], ], 'IngressPointStatus' => [ 'type' => 'string', 'enum' => [ 'PROVISIONING', 'DEPROVISIONING', 'UPDATING', 'ACTIVE', 'CLOSED', 'FAILED', ], ], 'IngressPointStatusToUpdate' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'CLOSED', ], ], 'IngressPointType' => [ 'type' => 'string', 'enum' => [ 'OPEN', 'AUTH', ], ], 'IngressPointsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IngressPoint', ], ], 'IngressStringEmailAttribute' => [ 'type' => 'string', 'enum' => [ 'RECIPIENT', ], ], 'IngressStringExpression' => [ 'type' => 'structure', 'required' => [ 'Evaluate', 'Operator', 'Values', ], 'members' => [ 'Evaluate' => [ 'shape' => 'IngressStringToEvaluate', ], 'Operator' => [ 'shape' => 'IngressStringOperator', ], 'Values' => [ 'shape' => 'StringList', ], ], ], 'IngressStringOperator' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'NOT_EQUALS', 'STARTS_WITH', 'ENDS_WITH', 'CONTAINS', ], ], 'IngressStringToEvaluate' => [ 'type' => 'structure', 'members' => [ 'Analysis' => [ 'shape' => 'IngressAnalysis', ], 'Attribute' => [ 'shape' => 'IngressStringEmailAttribute', ], ], 'union' => true, ], 'IngressTlsAttribute' => [ 'type' => 'string', 'enum' => [ 'TLS_PROTOCOL', ], ], 'IngressTlsProtocolAttribute' => [ 'type' => 'string', 'enum' => [ 'TLS1_2', 'TLS1_3', ], ], 'IngressTlsProtocolExpression' => [ 'type' => 'structure', 'required' => [ 'Evaluate', 'Operator', 'Value', ], 'members' => [ 'Evaluate' => [ 'shape' => 'IngressTlsProtocolToEvaluate', ], 'Operator' => [ 'shape' => 'IngressTlsProtocolOperator', ], 'Value' => [ 'shape' => 'IngressTlsProtocolAttribute', ], ], ], 'IngressTlsProtocolOperator' => [ 'type' => 'string', 'enum' => [ 'MINIMUM_TLS_VERSION', 'IS', ], ], 'IngressTlsProtocolToEvaluate' => [ 'type' => 'structure', 'members' => [ 'Attribute' => [ 'shape' => 'IngressTlsAttribute', ], ], 'union' => true, ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'IpType' => [ 'type' => 'string', 'enum' => [ 'IPV4', 'DUAL_STACK', ], ], 'Ipv4Cidr' => [ 'type' => 'string', 'pattern' => '^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)/([0-9]|[12][0-9]|3[0-2])$', ], 'Ipv4Cidrs' => [ 'type' => 'list', 'member' => [ 'shape' => 'Ipv4Cidr', ], ], 'Ipv6Cidr' => [ 'type' => 'string', 'max' => 49, 'min' => 0, 'pattern' => '^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:))\\/(12[0-8]|1[0-1][0-9]|[1-9][0-9]|[0-9])$', ], 'Ipv6Cidrs' => [ 'type' => 'list', 'member' => [ 'shape' => 'Ipv6Cidr', ], ], 'JobId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-]+$', ], 'JobItemsCount' => [ 'type' => 'integer', 'box' => true, ], 'JobName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_.-]+$', ], 'KmsKeyArn' => [ 'type' => 'string', 'pattern' => '^arn:aws(|-cn|-us-gov):kms:[a-z0-9-]{1,20}:[0-9]{12}:(key|alias)/.+$', ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^[a-zA-Z0-9-:/]+$', ], 'ListAddonInstancesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'PageSize' => [ 'shape' => 'PageSize', ], ], ], 'ListAddonInstancesResponse' => [ 'type' => 'structure', 'members' => [ 'AddonInstances' => [ 'shape' => 'AddonInstances', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListAddonSubscriptionsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'PageSize' => [ 'shape' => 'PageSize', ], ], ], 'ListAddonSubscriptionsResponse' => [ 'type' => 'structure', 'members' => [ 'AddonSubscriptions' => [ 'shape' => 'AddonSubscriptions', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListAddressListImportJobsRequest' => [ 'type' => 'structure', 'required' => [ 'AddressListId', ], 'members' => [ 'AddressListId' => [ 'shape' => 'AddressListId', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'PageSize' => [ 'shape' => 'PageSize', ], ], ], 'ListAddressListImportJobsResponse' => [ 'type' => 'structure', 'required' => [ 'ImportJobs', ], 'members' => [ 'ImportJobs' => [ 'shape' => 'ImportJobs', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListAddressListsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'PageSize' => [ 'shape' => 'PageSize', ], ], ], 'ListAddressListsResponse' => [ 'type' => 'structure', 'required' => [ 'AddressLists', ], 'members' => [ 'AddressLists' => [ 'shape' => 'AddressLists', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListArchiveExportsRequest' => [ 'type' => 'structure', 'required' => [ 'ArchiveId', ], 'members' => [ 'ArchiveId' => [ 'shape' => 'ArchiveId', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'PageSize' => [ 'shape' => 'PageSize', ], ], ], 'ListArchiveExportsResponse' => [ 'type' => 'structure', 'members' => [ 'Exports' => [ 'shape' => 'ExportSummaryList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListArchiveSearchesRequest' => [ 'type' => 'structure', 'required' => [ 'ArchiveId', ], 'members' => [ 'ArchiveId' => [ 'shape' => 'ArchiveId', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'PageSize' => [ 'shape' => 'PageSize', ], ], ], 'ListArchiveSearchesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'Searches' => [ 'shape' => 'SearchSummaryList', ], ], ], 'ListArchivesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'PageSize' => [ 'shape' => 'PageSize', ], ], ], 'ListArchivesResponse' => [ 'type' => 'structure', 'required' => [ 'Archives', ], 'members' => [ 'Archives' => [ 'shape' => 'ArchivesList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListIngressPointsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'PageSize' => [ 'shape' => 'PageSize', ], ], ], 'ListIngressPointsResponse' => [ 'type' => 'structure', 'members' => [ 'IngressPoints' => [ 'shape' => 'IngressPointsList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListMembersOfAddressListRequest' => [ 'type' => 'structure', 'required' => [ 'AddressListId', ], 'members' => [ 'AddressListId' => [ 'shape' => 'AddressListId', ], 'Filter' => [ 'shape' => 'AddressFilter', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'PageSize' => [ 'shape' => 'AddressPageSize', ], ], ], 'ListMembersOfAddressListResponse' => [ 'type' => 'structure', 'required' => [ 'Addresses', ], 'members' => [ 'Addresses' => [ 'shape' => 'SavedAddresses', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListRelaysRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'PageSize' => [ 'shape' => 'Integer', ], ], ], 'ListRelaysResponse' => [ 'type' => 'structure', 'required' => [ 'Relays', ], 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'Relays' => [ 'shape' => 'Relays', ], ], ], 'ListRuleSetsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'PageSize' => [ 'shape' => 'PageSize', ], ], ], 'ListRuleSetsResponse' => [ 'type' => 'structure', 'required' => [ 'RuleSets', ], 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'RuleSets' => [ 'shape' => 'RuleSets', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'TaggableResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'required' => [ 'Tags', ], 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ListTrafficPoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'PageSize' => [ 'shape' => 'PageSize', ], ], ], 'ListTrafficPoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'TrafficPolicies' => [ 'shape' => 'TrafficPolicyList', ], ], ], 'MailFrom' => [ 'type' => 'string', 'enum' => [ 'REPLACE', 'PRESERVE', ], ], 'MaxMessageSizeBytes' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'MessageBody' => [ 'type' => 'structure', 'members' => [ 'Html' => [ 'shape' => 'String', ], 'MessageMalformed' => [ 'shape' => 'Boolean', ], 'Text' => [ 'shape' => 'String', ], ], ], 'Metadata' => [ 'type' => 'structure', 'members' => [ 'ConfigurationSet' => [ 'shape' => 'String', ], 'IngressPointId' => [ 'shape' => 'IngressPointId', ], 'RuleSetId' => [ 'shape' => 'RuleSetId', ], 'SenderHostname' => [ 'shape' => 'String', ], 'SenderIpAddress' => [ 'shape' => 'SenderIpAddress', ], 'SendingMethod' => [ 'shape' => 'String', ], 'SendingPool' => [ 'shape' => 'String', ], 'SourceArn' => [ 'shape' => 'String', ], 'SourceIdentity' => [ 'shape' => 'String', ], 'Timestamp' => [ 'shape' => 'Timestamp', ], 'TlsCipherSuite' => [ 'shape' => 'String', ], 'TlsProtocol' => [ 'shape' => 'String', ], 'TrafficPolicyId' => [ 'shape' => 'TrafficPolicyId', ], ], ], 'MimeHeaderAttribute' => [ 'type' => 'string', 'pattern' => '^X-[a-zA-Z0-9-]{1,256}$', ], 'NameOrArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^[a-zA-Z0-9:_/+=,@.#-]+$', ], 'NetworkConfiguration' => [ 'type' => 'structure', 'members' => [ 'PrivateNetworkConfiguration' => [ 'shape' => 'PrivateNetworkConfiguration', ], 'PublicNetworkConfiguration' => [ 'shape' => 'PublicNetworkConfiguration', ], ], 'union' => true, ], 'NoAuthentication' => [ 'type' => 'structure', 'members' => [], ], 'PageSize' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'PaginationToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'PolicyCondition' => [ 'type' => 'structure', 'members' => [ 'BooleanExpression' => [ 'shape' => 'IngressBooleanExpression', ], 'IpExpression' => [ 'shape' => 'IngressIpv4Expression', ], 'Ipv6Expression' => [ 'shape' => 'IngressIpv6Expression', ], 'StringExpression' => [ 'shape' => 'IngressStringExpression', ], 'TlsExpression' => [ 'shape' => 'IngressTlsProtocolExpression', ], ], 'union' => true, ], 'PolicyConditions' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolicyCondition', ], 'min' => 1, ], 'PolicyStatement' => [ 'type' => 'structure', 'required' => [ 'Action', 'Conditions', ], 'members' => [ 'Action' => [ 'shape' => 'AcceptAction', ], 'Conditions' => [ 'shape' => 'PolicyConditions', ], ], ], 'PolicyStatementList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolicyStatement', ], ], 'PreSignedUrl' => [ 'type' => 'string', 'sensitive' => true, ], 'PrivateNetworkConfiguration' => [ 'type' => 'structure', 'required' => [ 'VpcEndpointId', ], 'members' => [ 'VpcEndpointId' => [ 'shape' => 'VpcEndpointId', ], ], ], 'PublicNetworkConfiguration' => [ 'type' => 'structure', 'required' => [ 'IpType', ], 'members' => [ 'IpType' => [ 'shape' => 'IpType', ], ], ], 'QBusinessApplicationId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[a-z0-9-]+$', ], 'QBusinessIndexId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[a-z0-9-]+$', ], 'Recipients' => [ 'type' => 'list', 'member' => [ 'shape' => 'EmailAddress', ], 'max' => 100, 'min' => 1, ], 'RegisterMemberToAddressListRequest' => [ 'type' => 'structure', 'required' => [ 'Address', 'AddressListId', ], 'members' => [ 'Address' => [ 'shape' => 'Address', ], 'AddressListId' => [ 'shape' => 'AddressListId', ], ], ], 'RegisterMemberToAddressListResponse' => [ 'type' => 'structure', 'members' => [], ], 'Relay' => [ 'type' => 'structure', 'members' => [ 'LastModifiedTimestamp' => [ 'shape' => 'Timestamp', ], 'RelayId' => [ 'shape' => 'RelayId', ], 'RelayName' => [ 'shape' => 'RelayName', ], ], ], 'RelayAction' => [ 'type' => 'structure', 'required' => [ 'Relay', ], 'members' => [ 'ActionFailurePolicy' => [ 'shape' => 'ActionFailurePolicy', ], 'MailFrom' => [ 'shape' => 'MailFrom', ], 'Relay' => [ 'shape' => 'IdOrArn', ], ], ], 'RelayArn' => [ 'type' => 'string', ], 'RelayAuthentication' => [ 'type' => 'structure', 'members' => [ 'NoAuthentication' => [ 'shape' => 'NoAuthentication', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], ], 'union' => true, ], 'RelayId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-]+$', ], 'RelayName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-_]+$', ], 'RelayServerName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-\\.]+$', ], 'RelayServerPort' => [ 'type' => 'integer', 'box' => true, 'max' => 65535, 'min' => 1, ], 'Relays' => [ 'type' => 'list', 'member' => [ 'shape' => 'Relay', ], ], 'ReplaceRecipientAction' => [ 'type' => 'structure', 'members' => [ 'ReplaceWith' => [ 'shape' => 'Recipients', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResultField' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^(addon\\.)?[\\sa-zA-Z0-9_]+$', ], 'RetentionPeriod' => [ 'type' => 'string', 'enum' => [ 'THREE_MONTHS', 'SIX_MONTHS', 'NINE_MONTHS', 'ONE_YEAR', 'EIGHTEEN_MONTHS', 'TWO_YEARS', 'THIRTY_MONTHS', 'THREE_YEARS', 'FOUR_YEARS', 'FIVE_YEARS', 'SIX_YEARS', 'SEVEN_YEARS', 'EIGHT_YEARS', 'NINE_YEARS', 'TEN_YEARS', 'PERMANENT', ], ], 'Row' => [ 'type' => 'structure', 'members' => [ 'ArchivedMessageId' => [ 'shape' => 'ArchivedMessageId', ], 'Cc' => [ 'shape' => 'String', ], 'Date' => [ 'shape' => 'String', ], 'Envelope' => [ 'shape' => 'Envelope', ], 'From' => [ 'shape' => 'String', ], 'HasAttachments' => [ 'shape' => 'Boolean', ], 'InReplyTo' => [ 'shape' => 'String', ], 'IngressPointId' => [ 'shape' => 'IngressPointId', ], 'MessageId' => [ 'shape' => 'String', ], 'ReceivedHeaders' => [ 'shape' => 'EmailReceivedHeadersList', ], 'ReceivedTimestamp' => [ 'shape' => 'Timestamp', ], 'SenderHostname' => [ 'shape' => 'String', ], 'SenderIpAddress' => [ 'shape' => 'SenderIpAddress', ], 'SourceArn' => [ 'shape' => 'String', ], 'Subject' => [ 'shape' => 'String', ], 'To' => [ 'shape' => 'String', ], 'XMailer' => [ 'shape' => 'String', ], 'XOriginalMailer' => [ 'shape' => 'String', ], 'XPriority' => [ 'shape' => 'String', ], ], ], 'RowsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Row', ], ], 'Rule' => [ 'type' => 'structure', 'required' => [ 'Actions', ], 'members' => [ 'Actions' => [ 'shape' => 'RuleActions', ], 'Conditions' => [ 'shape' => 'RuleConditions', ], 'Name' => [ 'shape' => 'RuleName', ], 'Unless' => [ 'shape' => 'RuleConditions', ], ], ], 'RuleAction' => [ 'type' => 'structure', 'members' => [ 'AddHeader' => [ 'shape' => 'AddHeaderAction', ], 'Archive' => [ 'shape' => 'ArchiveAction', ], 'DeliverToMailbox' => [ 'shape' => 'DeliverToMailboxAction', ], 'DeliverToQBusiness' => [ 'shape' => 'DeliverToQBusinessAction', ], 'Drop' => [ 'shape' => 'DropAction', ], 'PublishToSns' => [ 'shape' => 'SnsAction', ], 'Relay' => [ 'shape' => 'RelayAction', ], 'ReplaceRecipient' => [ 'shape' => 'ReplaceRecipientAction', ], 'Send' => [ 'shape' => 'SendAction', ], 'WriteToS3' => [ 'shape' => 'S3Action', ], ], 'union' => true, ], 'RuleActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleAction', ], 'max' => 10, 'min' => 1, ], 'RuleAddressListArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddressListArn', ], 'max' => 1, 'min' => 1, ], 'RuleAddressListEmailAttribute' => [ 'type' => 'string', 'enum' => [ 'RECIPIENT', 'MAIL_FROM', 'SENDER', 'FROM', 'TO', 'CC', ], ], 'RuleBooleanEmailAttribute' => [ 'type' => 'string', 'enum' => [ 'READ_RECEIPT_REQUESTED', 'TLS', 'TLS_WRAPPED', ], ], 'RuleBooleanExpression' => [ 'type' => 'structure', 'required' => [ 'Evaluate', 'Operator', ], 'members' => [ 'Evaluate' => [ 'shape' => 'RuleBooleanToEvaluate', ], 'Operator' => [ 'shape' => 'RuleBooleanOperator', ], ], ], 'RuleBooleanOperator' => [ 'type' => 'string', 'enum' => [ 'IS_TRUE', 'IS_FALSE', ], ], 'RuleBooleanToEvaluate' => [ 'type' => 'structure', 'members' => [ 'Analysis' => [ 'shape' => 'Analysis', ], 'Attribute' => [ 'shape' => 'RuleBooleanEmailAttribute', ], 'IsInAddressList' => [ 'shape' => 'RuleIsInAddressList', ], ], 'union' => true, ], 'RuleCondition' => [ 'type' => 'structure', 'members' => [ 'BooleanExpression' => [ 'shape' => 'RuleBooleanExpression', ], 'DmarcExpression' => [ 'shape' => 'RuleDmarcExpression', ], 'IpExpression' => [ 'shape' => 'RuleIpExpression', ], 'NumberExpression' => [ 'shape' => 'RuleNumberExpression', ], 'StringExpression' => [ 'shape' => 'RuleStringExpression', ], 'VerdictExpression' => [ 'shape' => 'RuleVerdictExpression', ], ], 'union' => true, ], 'RuleConditions' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleCondition', ], 'max' => 10, 'min' => 0, ], 'RuleDmarcExpression' => [ 'type' => 'structure', 'required' => [ 'Operator', 'Values', ], 'members' => [ 'Operator' => [ 'shape' => 'RuleDmarcOperator', ], 'Values' => [ 'shape' => 'RuleDmarcValueList', ], ], ], 'RuleDmarcOperator' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'NOT_EQUALS', ], ], 'RuleDmarcPolicy' => [ 'type' => 'string', 'enum' => [ 'NONE', 'QUARANTINE', 'REJECT', ], ], 'RuleDmarcValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleDmarcPolicy', ], 'max' => 10, 'min' => 1, ], 'RuleIpEmailAttribute' => [ 'type' => 'string', 'enum' => [ 'SOURCE_IP', ], ], 'RuleIpExpression' => [ 'type' => 'structure', 'required' => [ 'Evaluate', 'Operator', 'Values', ], 'members' => [ 'Evaluate' => [ 'shape' => 'RuleIpToEvaluate', ], 'Operator' => [ 'shape' => 'RuleIpOperator', ], 'Values' => [ 'shape' => 'RuleIpValueList', ], ], ], 'RuleIpOperator' => [ 'type' => 'string', 'enum' => [ 'CIDR_MATCHES', 'NOT_CIDR_MATCHES', ], ], 'RuleIpStringValue' => [ 'type' => 'string', 'max' => 43, 'min' => 1, 'pattern' => '^(([0-9]|.|:|/)*)$', ], 'RuleIpToEvaluate' => [ 'type' => 'structure', 'members' => [ 'Attribute' => [ 'shape' => 'RuleIpEmailAttribute', ], ], 'union' => true, ], 'RuleIpValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleIpStringValue', ], 'max' => 10, 'min' => 1, ], 'RuleIsInAddressList' => [ 'type' => 'structure', 'required' => [ 'AddressLists', 'Attribute', ], 'members' => [ 'AddressLists' => [ 'shape' => 'RuleAddressListArnList', ], 'Attribute' => [ 'shape' => 'RuleAddressListEmailAttribute', ], ], ], 'RuleName' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_.-]+$', ], 'RuleNumberEmailAttribute' => [ 'type' => 'string', 'enum' => [ 'MESSAGE_SIZE', ], ], 'RuleNumberExpression' => [ 'type' => 'structure', 'required' => [ 'Evaluate', 'Operator', 'Value', ], 'members' => [ 'Evaluate' => [ 'shape' => 'RuleNumberToEvaluate', ], 'Operator' => [ 'shape' => 'RuleNumberOperator', ], 'Value' => [ 'shape' => 'Double', ], ], ], 'RuleNumberOperator' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'NOT_EQUALS', 'LESS_THAN', 'GREATER_THAN', 'LESS_THAN_OR_EQUAL', 'GREATER_THAN_OR_EQUAL', ], ], 'RuleNumberToEvaluate' => [ 'type' => 'structure', 'members' => [ 'Attribute' => [ 'shape' => 'RuleNumberEmailAttribute', ], ], 'union' => true, ], 'RuleSet' => [ 'type' => 'structure', 'members' => [ 'LastModificationDate' => [ 'shape' => 'Timestamp', ], 'RuleSetId' => [ 'shape' => 'RuleSetId', ], 'RuleSetName' => [ 'shape' => 'RuleSetName', ], ], ], 'RuleSetArn' => [ 'type' => 'string', ], 'RuleSetId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'RuleSetName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_.-]+$', ], 'RuleSets' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleSet', ], ], 'RuleStringEmailAttribute' => [ 'type' => 'string', 'enum' => [ 'MAIL_FROM', 'HELO', 'RECIPIENT', 'SENDER', 'FROM', 'SUBJECT', 'TO', 'CC', ], ], 'RuleStringExpression' => [ 'type' => 'structure', 'required' => [ 'Evaluate', 'Operator', 'Values', ], 'members' => [ 'Evaluate' => [ 'shape' => 'RuleStringToEvaluate', ], 'Operator' => [ 'shape' => 'RuleStringOperator', ], 'Values' => [ 'shape' => 'RuleStringList', ], ], ], 'RuleStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleStringValue', ], 'max' => 10, 'min' => 1, ], 'RuleStringOperator' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'NOT_EQUALS', 'STARTS_WITH', 'ENDS_WITH', 'CONTAINS', ], ], 'RuleStringToEvaluate' => [ 'type' => 'structure', 'members' => [ 'Analysis' => [ 'shape' => 'Analysis', ], 'Attribute' => [ 'shape' => 'RuleStringEmailAttribute', ], 'MimeHeaderAttribute' => [ 'shape' => 'MimeHeaderAttribute', ], ], 'union' => true, ], 'RuleStringValue' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'RuleVerdict' => [ 'type' => 'string', 'enum' => [ 'PASS', 'FAIL', 'GRAY', 'PROCESSING_FAILED', ], ], 'RuleVerdictAttribute' => [ 'type' => 'string', 'enum' => [ 'SPF', 'DKIM', ], ], 'RuleVerdictExpression' => [ 'type' => 'structure', 'required' => [ 'Evaluate', 'Operator', 'Values', ], 'members' => [ 'Evaluate' => [ 'shape' => 'RuleVerdictToEvaluate', ], 'Operator' => [ 'shape' => 'RuleVerdictOperator', ], 'Values' => [ 'shape' => 'RuleVerdictValueList', ], ], ], 'RuleVerdictOperator' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'NOT_EQUALS', ], ], 'RuleVerdictToEvaluate' => [ 'type' => 'structure', 'members' => [ 'Analysis' => [ 'shape' => 'Analysis', ], 'Attribute' => [ 'shape' => 'RuleVerdictAttribute', ], ], 'union' => true, ], 'RuleVerdictValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleVerdict', ], 'max' => 10, 'min' => 1, ], 'Rules' => [ 'type' => 'list', 'member' => [ 'shape' => 'Rule', ], 'max' => 40, 'min' => 0, ], 'S3Action' => [ 'type' => 'structure', 'required' => [ 'RoleArn', 'S3Bucket', ], 'members' => [ 'ActionFailurePolicy' => [ 'shape' => 'ActionFailurePolicy', ], 'RoleArn' => [ 'shape' => 'IamRoleArn', ], 'S3Bucket' => [ 'shape' => 'S3Bucket', ], 'S3Prefix' => [ 'shape' => 'S3Prefix', ], 'S3SseKmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'S3Bucket' => [ 'type' => 'string', 'max' => 62, 'min' => 1, 'pattern' => '^[a-zA-Z0-9.-]+$', ], 'S3ExportDestinationConfiguration' => [ 'type' => 'structure', 'members' => [ 'S3Location' => [ 'shape' => 'S3Location', ], ], ], 'S3Location' => [ 'type' => 'string', 'pattern' => '^s3://[a-zA-Z0-9.-]{3,63}(/[a-zA-Z0-9!_.*\'()/-]*)*$', ], 'S3Prefix' => [ 'type' => 'string', 'max' => 62, 'min' => 1, 'pattern' => '^[a-zA-Z0-9!_.*\'()/-]+$', ], 'S3PresignedURL' => [ 'type' => 'string', ], 'SavedAddress' => [ 'type' => 'structure', 'required' => [ 'Address', 'CreatedTimestamp', ], 'members' => [ 'Address' => [ 'shape' => 'Address', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'SavedAddresses' => [ 'type' => 'list', 'member' => [ 'shape' => 'SavedAddress', ], ], 'SearchId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'SearchMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 0, ], 'SearchState' => [ 'type' => 'string', 'enum' => [ 'QUEUED', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', ], ], 'SearchStatus' => [ 'type' => 'structure', 'members' => [ 'CompletionTimestamp' => [ 'shape' => 'Timestamp', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], 'State' => [ 'shape' => 'SearchState', ], 'SubmissionTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'SearchSummary' => [ 'type' => 'structure', 'members' => [ 'SearchId' => [ 'shape' => 'SearchId', ], 'Status' => [ 'shape' => 'SearchStatus', ], ], ], 'SearchSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchSummary', ], ], 'SecretArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-cn|aws-us-gov):secretsmanager:[a-z0-9-]+:\\d{12}:secret:[a-zA-Z0-9/_+=,.@-]+$', ], 'SendAction' => [ 'type' => 'structure', 'required' => [ 'RoleArn', ], 'members' => [ 'ActionFailurePolicy' => [ 'shape' => 'ActionFailurePolicy', ], 'RoleArn' => [ 'shape' => 'IamRoleArn', ], ], ], 'SenderIpAddress' => [ 'type' => 'string', 'sensitive' => true, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'SmtpPassword' => [ 'type' => 'string', 'max' => 64, 'min' => 8, 'pattern' => '^[A-Za-z0-9!@#$%^&*()_+\\-=\\[\\]{}|.,?]+$', 'sensitive' => true, ], 'SnsAction' => [ 'type' => 'structure', 'required' => [ 'RoleArn', 'TopicArn', ], 'members' => [ 'ActionFailurePolicy' => [ 'shape' => 'ActionFailurePolicy', ], 'Encoding' => [ 'shape' => 'SnsNotificationEncoding', ], 'PayloadType' => [ 'shape' => 'SnsNotificationPayloadType', ], 'RoleArn' => [ 'shape' => 'IamRoleArn', ], 'TopicArn' => [ 'shape' => 'SnsTopicArn', ], ], ], 'SnsNotificationEncoding' => [ 'type' => 'string', 'enum' => [ 'UTF-8', 'BASE64', ], ], 'SnsNotificationPayloadType' => [ 'type' => 'string', 'enum' => [ 'HEADERS', 'CONTENT', ], ], 'SnsTopicArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov):sns:[a-z]{2}-[a-z]+-\\d{1}:\\d{12}:[\\w\\-]{1,256}$', ], 'StartAddressListImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'StartAddressListImportJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartArchiveExportRequest' => [ 'type' => 'structure', 'required' => [ 'ArchiveId', 'ExportDestinationConfiguration', 'FromTimestamp', 'ToTimestamp', ], 'members' => [ 'ArchiveId' => [ 'shape' => 'ArchiveId', ], 'ExportDestinationConfiguration' => [ 'shape' => 'ExportDestinationConfiguration', ], 'Filters' => [ 'shape' => 'ArchiveFilters', ], 'FromTimestamp' => [ 'shape' => 'Timestamp', ], 'IncludeMetadata' => [ 'shape' => 'Boolean', ], 'MaxResults' => [ 'shape' => 'ExportMaxResults', ], 'ToTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'StartArchiveExportResponse' => [ 'type' => 'structure', 'members' => [ 'ExportId' => [ 'shape' => 'ExportId', ], ], ], 'StartArchiveSearchRequest' => [ 'type' => 'structure', 'required' => [ 'ArchiveId', 'FromTimestamp', 'MaxResults', 'ToTimestamp', ], 'members' => [ 'ArchiveId' => [ 'shape' => 'ArchiveId', ], 'Filters' => [ 'shape' => 'ArchiveFilters', ], 'FromTimestamp' => [ 'shape' => 'Timestamp', ], 'MaxResults' => [ 'shape' => 'SearchMaxResults', ], 'ToTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'StartArchiveSearchResponse' => [ 'type' => 'structure', 'members' => [ 'SearchId' => [ 'shape' => 'SearchId', ], ], ], 'StopAddressListImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'StopAddressListImportJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopArchiveExportRequest' => [ 'type' => 'structure', 'required' => [ 'ExportId', ], 'members' => [ 'ExportId' => [ 'shape' => 'ExportId', ], ], ], 'StopArchiveExportResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopArchiveSearchRequest' => [ 'type' => 'structure', 'required' => [ 'SearchId', ], 'members' => [ 'SearchId' => [ 'shape' => 'SearchId', ], ], ], 'StopArchiveSearchResponse' => [ 'type' => 'structure', 'members' => [], ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'StringValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'StringValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringValue', ], 'max' => 10, 'min' => 1, ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9/_\\+=\\.:@\\-]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'TaggableResourceArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^[a-zA-Z0-9/_\\+=\\.:@\\-]*$', ], 'TaggableResourceArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 20, 'pattern' => '^arn:aws(|-cn|-us-gov):ses:[a-z0-9-]{1,20}:[0-9]{12}:(mailmanager-|addon-).+$', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TrafficPolicy' => [ 'type' => 'structure', 'required' => [ 'DefaultAction', 'TrafficPolicyId', 'TrafficPolicyName', ], 'members' => [ 'DefaultAction' => [ 'shape' => 'AcceptAction', ], 'TrafficPolicyId' => [ 'shape' => 'TrafficPolicyId', ], 'TrafficPolicyName' => [ 'shape' => 'TrafficPolicyName', ], ], ], 'TrafficPolicyArn' => [ 'type' => 'string', ], 'TrafficPolicyId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'TrafficPolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrafficPolicy', ], ], 'TrafficPolicyName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[A-Za-z0-9_\\-]+$', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'TaggableResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateArchiveRequest' => [ 'type' => 'structure', 'required' => [ 'ArchiveId', ], 'members' => [ 'ArchiveId' => [ 'shape' => 'ArchiveIdString', ], 'ArchiveName' => [ 'shape' => 'ArchiveNameString', ], 'Retention' => [ 'shape' => 'ArchiveRetention', ], ], ], 'UpdateArchiveResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateIngressPointRequest' => [ 'type' => 'structure', 'required' => [ 'IngressPointId', ], 'members' => [ 'IngressPointConfiguration' => [ 'shape' => 'IngressPointConfiguration', ], 'IngressPointId' => [ 'shape' => 'IngressPointId', ], 'IngressPointName' => [ 'shape' => 'IngressPointName', ], 'RuleSetId' => [ 'shape' => 'RuleSetId', ], 'StatusToUpdate' => [ 'shape' => 'IngressPointStatusToUpdate', ], 'TrafficPolicyId' => [ 'shape' => 'TrafficPolicyId', ], ], ], 'UpdateIngressPointResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateRelayRequest' => [ 'type' => 'structure', 'required' => [ 'RelayId', ], 'members' => [ 'Authentication' => [ 'shape' => 'RelayAuthentication', ], 'RelayId' => [ 'shape' => 'RelayId', ], 'RelayName' => [ 'shape' => 'RelayName', ], 'ServerName' => [ 'shape' => 'RelayServerName', ], 'ServerPort' => [ 'shape' => 'RelayServerPort', ], ], ], 'UpdateRelayResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateRuleSetRequest' => [ 'type' => 'structure', 'required' => [ 'RuleSetId', ], 'members' => [ 'RuleSetId' => [ 'shape' => 'RuleSetId', ], 'RuleSetName' => [ 'shape' => 'RuleSetName', ], 'Rules' => [ 'shape' => 'Rules', ], ], ], 'UpdateRuleSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateTrafficPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'TrafficPolicyId', ], 'members' => [ 'DefaultAction' => [ 'shape' => 'AcceptAction', ], 'MaxMessageSizeBytes' => [ 'shape' => 'MaxMessageSizeBytes', ], 'PolicyStatements' => [ 'shape' => 'PolicyStatementList', ], 'TrafficPolicyId' => [ 'shape' => 'TrafficPolicyId', ], 'TrafficPolicyName' => [ 'shape' => 'TrafficPolicyName', ], ], ], 'UpdateTrafficPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'VpcEndpointId' => [ 'type' => 'string', 'pattern' => '^vpce-[a-zA-Z0-9]{17}$', ], ],];
