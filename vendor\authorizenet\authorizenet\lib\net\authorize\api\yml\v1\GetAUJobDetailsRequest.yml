net\authorize\api\contract\v1\GetAUJobDetailsRequest:
    xml_root_name: getAUJobDetailsRequest
    xml_root_namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
    properties:
        month:
            expose: true
            access_type: public_method
            serialized_name: month
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getMonth
                setter: setMonth
            type: string
        modifiedTypeFilter:
            expose: true
            access_type: public_method
            serialized_name: modifiedTypeFilter
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getModifiedTypeFilter
                setter: setModifiedTypeFilter
            type: string
        paging:
            expose: true
            access_type: public_method
            serialized_name: paging
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getPaging
                setter: setPaging
            type: net\authorize\api\contract\v1\PagingType
