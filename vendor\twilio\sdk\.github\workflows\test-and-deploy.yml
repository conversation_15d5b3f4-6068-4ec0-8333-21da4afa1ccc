name: Test and Deploy
on:
  push:
    branches: [ '*' ]
    tags: [ '*' ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run automatically at 8AM PST Monday-Friday
    - cron: '0 15 * * 1-5'
  workflow_dispatch:

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    timeout-minutes: 20
    strategy:
      matrix:
        php: [ 7.2, 7.3, 7.4, 8.0, 8.1, 8.2, 8.3, 8.4 ]
        dependencies:
          - "lowest"
          - "highest"
    steps:
      - name: Checkout twilio-php
        uses: actions/checkout@v3
        with:
          fetch-depth: 0  # Shallow clones should be disabled for a better relevancy of analysis

      - name: Setup PHP Action
        uses: shivammathur/setup-php@2.15.0
        with:
          php-version: ${{ matrix.php }}
          coverage: xdebug
        id: php

      - name: Composer webhook config
        run: composer config -g github-oauth.github.com ${{ secrets.GITHUB_TOKEN }}

      - name: Update Dependencies
        if: matrix.dependencies == 'lowest'
        run: composer update --prefer-lowest --prefer-stable -n

      - name: Run Tests
        run: make install test

      - name: Fix code coverage paths
        run: |
          if [ -f "coverage.xml" ]; then
            sed -i 's@'$GITHUB_WORKSPACE'@/github/workspace/@g' coverage.xml
          fi
      - name: Run Cluster Test
        if: (!github.event.pull_request.head.repo.fork)
        env:
          TWILIO_ACCOUNT_SID: ${{ secrets.TWILIO_ACCOUNT_SID }}
          TWILIO_API_KEY: ${{ secrets.TWILIO_CLUSTER_TEST_API_KEY}}
          TWILIO_API_SECRET: ${{ secrets.TWILIO_CLUSTER_TEST_API_KEY_SECRET }}
          TWILIO_FROM_NUMBER: ${{ secrets.TWILIO_FROM_NUMBER }}
          TWILIO_TO_NUMBER: ${{ secrets.TWILIO_TO_NUMBER }}
          TWILIO_ORGS_CLIENT_ID: ${{ secrets.TWILIO_ORGS_CLIENT_ID }}
          TWILIO_ORGS_CLIENT_SECRET: ${{ secrets.TWILIO_ORGS_CLIENT_SECRET }}
          TWILIO_ORG_SID: ${{ secrets.TWILIO_ORG_SID }}
          TWILIO_CLIENT_ID: ${{ secrets.TWILIO_CLIENT_ID }}
          TWILIO_CLIENT_SECRET: ${{ secrets.TWILIO_CLIENT_SECRET }}
          TWILIO_MESSAGE_SID: ${{ secrets.TWILIO_MESSAGE_SID }}
        run: make cluster-test

      - name: Install SonarCloud scanner and run analysis
        uses: SonarSource/sonarcloud-github-action@master
        if: (github.event_name == 'pull_request' || github.ref_type == 'branch') && !github.event.pull_request.head.repo.fork && matrix.php == '8.1' && matrix.dependencies == 'highest'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}  # Needed to get PR information, if any
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  deploy:
    name: Deploy
    if: success() && github.ref_type == 'tag'
    needs: [ test ]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout twilio-php
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Install dependencies
        run: composer install

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_AUTH_TOKEN }}

      # The expression strips off the shortest match from the front of the string to yield just the tag name as the output
      - name: Get tagged version
        run: echo "GITHUB_TAG=${GITHUB_REF#refs/*/}" >> $GITHUB_ENV

      - name: Create GitHub Release
        uses: sendgrid/dx-automator/actions/release@main
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Build & Push docker image
        run: make docker-build docker-push

      - name: Submit metric to Datadog
        uses: sendgrid/dx-automator/actions/datadog-release-metric@main
        env:
          DD_API_KEY: ${{ secrets.DATADOG_API_KEY }}

  notify-on-failure:
    name: Slack notify on failure
    if: failure() && github.event_name != 'pull_request' && (github.ref == 'refs/heads/main' || github.ref_type == 'tag')
    needs: [ test, deploy ]
    runs-on: ubuntu-latest
    steps:
      - uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_COLOR: failure
          SLACK_ICON_EMOJI: ':github:'
          SLACK_MESSAGE: ${{ format('Test *{0}*, Deploy *{1}*, {2}/{3}/actions/runs/{4}', needs.test.result, needs.deploy.result, github.server_url, github.repository, github.run_id) }}
          SLACK_TITLE: Action Failure - ${{ github.repository }}
          SLACK_USERNAME: GitHub Actions
          SLACK_MSG_AUTHOR: twilio-dx
          SLACK_FOOTER: Posted automatically using GitHub Actions
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          MSG_MINIMAL: true
