<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\FlexApi\V1\Interaction;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\Rest\FlexApi\V1\Interaction\InteractionChannel\InteractionChannelParticipantList;
use Twilio\Rest\FlexApi\V1\Interaction\InteractionChannel\InteractionChannelInviteList;
use Twilio\Rest\FlexApi\V1\Interaction\InteractionChannel\InteractionTransferList;


/**
 * @property string|null $sid
 * @property string|null $interactionSid
 * @property string $type
 * @property string $status
 * @property int|null $errorCode
 * @property string|null $errorMessage
 * @property string|null $url
 * @property array|null $links
 */
class InteractionChannelInstance extends InstanceResource
{
    protected $_participants;
    protected $_invites;
    protected $_transfers;

    /**
     * Initialize the InteractionChannelInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $interactionSid The unique string created by Twilio to identify an Interaction resource, prefixed with KD.
     * @param string $sid The unique string created by Twilio to identify an Interaction Channel resource, prefixed with UO.
     */
    public function __construct(Version $version, array $payload, string $interactionSid, ?string $sid = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'sid' => Values::array_get($payload, 'sid'),
            'interactionSid' => Values::array_get($payload, 'interaction_sid'),
            'type' => Values::array_get($payload, 'type'),
            'status' => Values::array_get($payload, 'status'),
            'errorCode' => Values::array_get($payload, 'error_code'),
            'errorMessage' => Values::array_get($payload, 'error_message'),
            'url' => Values::array_get($payload, 'url'),
            'links' => Values::array_get($payload, 'links'),
        ];

        $this->solution = ['interactionSid' => $interactionSid, 'sid' => $sid ?: $this->properties['sid'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return InteractionChannelContext Context for this InteractionChannelInstance
     */
    protected function proxy(): InteractionChannelContext
    {
        if (!$this->context) {
            $this->context = new InteractionChannelContext(
                $this->version,
                $this->solution['interactionSid'],
                $this->solution['sid']
            );
        }

        return $this->context;
    }

    /**
     * Fetch the InteractionChannelInstance
     *
     * @return InteractionChannelInstance Fetched InteractionChannelInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): InteractionChannelInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Update the InteractionChannelInstance
     *
     * @param string $status
     * @param array|Options $options Optional Arguments
     * @return InteractionChannelInstance Updated InteractionChannelInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(string $status, array $options = []): InteractionChannelInstance
    {

        return $this->proxy()->update($status, $options);
    }

    /**
     * Access the participants
     */
    protected function getParticipants(): InteractionChannelParticipantList
    {
        return $this->proxy()->participants;
    }

    /**
     * Access the invites
     */
    protected function getInvites(): InteractionChannelInviteList
    {
        return $this->proxy()->invites;
    }

    /**
     * Access the transfers
     */
    protected function getTransfers(): InteractionTransferList
    {
        return $this->proxy()->transfers;
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.FlexApi.V1.InteractionChannelInstance ' . \implode(' ', $context) . ']';
    }
}

