<?php

namespace Database\Seeders;

use App\Models\Utility;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // تم إصلاح المشكلة - إزالة التحقق من route
        $this->call(UsersTableSeeder::class);
        $this->call(NotificationSeeder::class);
        $this->call(AiTemplateSeeder::class);

        try {
            Artisan::call('module:migrate LandingPage');
            Artisan::call('module:seed LandingPage');
        } catch (\Exception $e) {
            // تجاهل أخطاء modules إذا لم تكن موجودة
        }

        Utility::languagecreate();
    }
}
