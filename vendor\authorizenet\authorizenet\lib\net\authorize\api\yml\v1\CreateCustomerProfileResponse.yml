net\authorize\api\contract\v1\CreateCustomerProfileResponse:
    xml_root_name: createCustomerProfileResponse
    xml_root_namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
    properties:
        customerProfileId:
            expose: true
            access_type: public_method
            serialized_name: customerProfileId
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getCustomerProfileId
                setter: setCustomerProfileId
            type: string
        customerPaymentProfileIdList:
            expose: true
            access_type: public_method
            serialized_name: customerPaymentProfileIdList
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getCustomerPaymentProfileIdList
                setter: setCustomerPaymentProfileIdList
            type: array<string>
            xml_list:
                inline: false
                skip_when_empty: false
                entry_name: numericString
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
        customerShippingAddressIdList:
            expose: true
            access_type: public_method
            serialized_name: customerShippingAddressIdList
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getCustomerShippingAddressIdList
                setter: setCustomerShippingAddressIdList
            type: array<string>
            xml_list:
                inline: false
                skip_when_empty: false
                entry_name: numericString
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
        validationDirectResponseList:
            expose: true
            access_type: public_method
            serialized_name: validationDirectResponseList
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getValidationDirectResponseList
                setter: setValidationDirectResponseList
            type: array<string>
            xml_list:
                inline: false
                skip_when_empty: false
                entry_name: string
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
