<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Numbers\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;


/**
 * @property string|null $portInRequestSid
 * @property string|null $phoneNumberSid
 * @property string|null $url
 * @property string|null $accountSid
 * @property string|null $phoneNumberType
 * @property \DateTime|null $dateCreated
 * @property string|null $country
 * @property bool|null $missingRequiredFields
 * @property \DateTime|null $lastUpdated
 * @property string|null $phoneNumber
 * @property bool|null $portable
 * @property string|null $notPortabilityReason
 * @property int|null $notPortabilityReasonCode
 * @property string|null $portInPhoneNumberStatus
 * @property int|null $portOutPin
 * @property string|null $rejectionReason
 * @property int|null $rejectionReasonCode
 * @property \DateTime|null $portDate
 */
class PortingPortInPhoneNumberInstance extends InstanceResource
{
    /**
     * Initialize the PortingPortInPhoneNumberInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $portInRequestSid The SID of the Port In request. This is a unique identifier of the port in request.
     * @param string $phoneNumberSid The SID of the Port In request phone number. This is a unique identifier of the phone number.
     */
    public function __construct(Version $version, array $payload, ?string $portInRequestSid = null, ?string $phoneNumberSid = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'portInRequestSid' => Values::array_get($payload, 'port_in_request_sid'),
            'phoneNumberSid' => Values::array_get($payload, 'phone_number_sid'),
            'url' => Values::array_get($payload, 'url'),
            'accountSid' => Values::array_get($payload, 'account_sid'),
            'phoneNumberType' => Values::array_get($payload, 'phone_number_type'),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'country' => Values::array_get($payload, 'country'),
            'missingRequiredFields' => Values::array_get($payload, 'missing_required_fields'),
            'lastUpdated' => Deserialize::dateTime(Values::array_get($payload, 'last_updated')),
            'phoneNumber' => Values::array_get($payload, 'phone_number'),
            'portable' => Values::array_get($payload, 'portable'),
            'notPortabilityReason' => Values::array_get($payload, 'not_portability_reason'),
            'notPortabilityReasonCode' => Values::array_get($payload, 'not_portability_reason_code'),
            'portInPhoneNumberStatus' => Values::array_get($payload, 'port_in_phone_number_status'),
            'portOutPin' => Values::array_get($payload, 'port_out_pin'),
            'rejectionReason' => Values::array_get($payload, 'rejection_reason'),
            'rejectionReasonCode' => Values::array_get($payload, 'rejection_reason_code'),
            'portDate' => Deserialize::dateTime(Values::array_get($payload, 'port_date')),
        ];

        $this->solution = ['portInRequestSid' => $portInRequestSid ?: $this->properties['portInRequestSid'], 'phoneNumberSid' => $phoneNumberSid ?: $this->properties['phoneNumberSid'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return PortingPortInPhoneNumberContext Context for this PortingPortInPhoneNumberInstance
     */
    protected function proxy(): PortingPortInPhoneNumberContext
    {
        if (!$this->context) {
            $this->context = new PortingPortInPhoneNumberContext(
                $this->version,
                $this->solution['portInRequestSid'],
                $this->solution['phoneNumberSid']
            );
        }

        return $this->context;
    }

    /**
     * Delete the PortingPortInPhoneNumberInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->proxy()->delete();
    }

    /**
     * Fetch the PortingPortInPhoneNumberInstance
     *
     * @return PortingPortInPhoneNumberInstance Fetched PortingPortInPhoneNumberInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): PortingPortInPhoneNumberInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Numbers.V1.PortingPortInPhoneNumberInstance ' . \implode(' ', $context) . ']';
    }
}

