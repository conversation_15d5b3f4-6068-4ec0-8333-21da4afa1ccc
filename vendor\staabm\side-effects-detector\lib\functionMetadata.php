<?php declare(strict_types = 1);

/**
 * Intially copied from PHPStan, but modified with some additional info about often used functions/methods, which PHPStan gets information about from other sources.
 *
 * https://github.com/phpstan/phpstan-src/blob/2.0.x/resources/functionMap.php
 */

return [
    // added data
    'ini_set' => ['hasSideEffects' => true],
    'trigger_error' => ['hasSideEffects' => true],
    'putenv' => ['hasSideEffects' => true],
    'version_compare' => ['hasSideEffects' => false],

    // Intially copied from PHPStan
	'BackedEnum::from' => ['hasSideEffects' => false],
	'BackedEnum::tryFrom' => ['hasSideEffects' => false],
	'CURLFile::getFilename' => ['hasSideEffects' => false],
	'CURLFile::getMimeType' => ['hasSideEffects' => false],
	'CURLFile::getPostFilename' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\AlreadyExistsException::__construct' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\AuthenticationException::__construct' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\ConfigurationException::__construct' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\DivideByZeroException::__construct' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\DomainException::__construct' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\ExecutionException::__construct' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\InvalidArgumentException::__construct' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\InvalidQueryException::__construct' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\InvalidSyntaxException::__construct' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\IsBootstrappingException::__construct' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\LogicException::__construct' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\OverloadedException::__construct' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\ProtocolException::__construct' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\RangeException::__construct' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\ReadTimeoutException::__construct' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\RuntimeException::__construct' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\ServerException::__construct' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\TimeoutException::__construct' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\TruncateException::__construct' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\UnauthorizedException::__construct' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\UnavailableException::__construct' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\UnpreparedException::__construct' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\ValidationException::__construct' => ['hasSideEffects' => false],
	'Cassandra\\Exception\\WriteTimeoutException::__construct' => ['hasSideEffects' => false],
	'Closure::bind' => ['hasSideEffects' => false],
	'Closure::bindTo' => ['hasSideEffects' => false],
	'Collator::__construct' => ['hasSideEffects' => false],
	'Collator::compare' => ['hasSideEffects' => false],
	'Collator::getAttribute' => ['hasSideEffects' => false],
	'Collator::getErrorCode' => ['hasSideEffects' => false],
	'Collator::getErrorMessage' => ['hasSideEffects' => false],
	'Collator::getLocale' => ['hasSideEffects' => false],
	'Collator::getSortKey' => ['hasSideEffects' => false],
	'Collator::getStrength' => ['hasSideEffects' => false],
	'DateTime::add' => ['hasSideEffects' => true],
	'DateTime::createFromFormat' => ['hasSideEffects' => false],
	'DateTime::createFromImmutable' => ['hasSideEffects' => false],
	'DateTime::diff' => ['hasSideEffects' => false],
	'DateTime::format' => ['hasSideEffects' => false],
	'DateTime::getLastErrors' => ['hasSideEffects' => false],
	'DateTime::getOffset' => ['hasSideEffects' => false],
	'DateTime::getTimestamp' => ['hasSideEffects' => false],
	'DateTime::getTimezone' => ['hasSideEffects' => false],
	'DateTime::modify' => ['hasSideEffects' => true],
	'DateTime::setDate' => ['hasSideEffects' => true],
	'DateTime::setISODate' => ['hasSideEffects' => true],
	'DateTime::setTime' => ['hasSideEffects' => true],
	'DateTime::setTimestamp' => ['hasSideEffects' => true],
	'DateTime::setTimezone' => ['hasSideEffects' => true],
	'DateTime::sub' => ['hasSideEffects' => true],
	'DateTimeImmutable::add' => ['hasSideEffects' => false],
	'DateTimeImmutable::createFromFormat' => ['hasSideEffects' => false],
	'DateTimeImmutable::createFromMutable' => ['hasSideEffects' => false],
	'DateTimeImmutable::diff' => ['hasSideEffects' => false],
	'DateTimeImmutable::format' => ['hasSideEffects' => false],
	'DateTimeImmutable::getLastErrors' => ['hasSideEffects' => false],
	'DateTimeImmutable::getOffset' => ['hasSideEffects' => false],
	'DateTimeImmutable::getTimestamp' => ['hasSideEffects' => false],
	'DateTimeImmutable::getTimezone' => ['hasSideEffects' => false],
	'DateTimeImmutable::modify' => ['hasSideEffects' => false],
	'DateTimeImmutable::setDate' => ['hasSideEffects' => false],
	'DateTimeImmutable::setISODate' => ['hasSideEffects' => false],
	'DateTimeImmutable::setTime' => ['hasSideEffects' => false],
	'DateTimeImmutable::setTimestamp' => ['hasSideEffects' => false],
	'DateTimeImmutable::setTimezone' => ['hasSideEffects' => false],
	'DateTimeImmutable::sub' => ['hasSideEffects' => false],
	'Error::__construct' => ['hasSideEffects' => false],
	'ErrorException::__construct' => ['hasSideEffects' => false],
	'Event::__construct' => ['hasSideEffects' => false],
	'EventBase::getFeatures' => ['hasSideEffects' => false],
	'EventBase::getMethod' => ['hasSideEffects' => false],
	'EventBase::getTimeOfDayCached' => ['hasSideEffects' => false],
	'EventBase::gotExit' => ['hasSideEffects' => false],
	'EventBase::gotStop' => ['hasSideEffects' => false],
	'EventBuffer::__construct' => ['hasSideEffects' => false],
	'EventBufferEvent::__construct' => ['hasSideEffects' => false],
	'EventBufferEvent::getDnsErrorString' => ['hasSideEffects' => false],
	'EventBufferEvent::getEnabled' => ['hasSideEffects' => false],
	'EventBufferEvent::getInput' => ['hasSideEffects' => false],
	'EventBufferEvent::getOutput' => ['hasSideEffects' => false],
	'EventConfig::__construct' => ['hasSideEffects' => false],
	'EventDnsBase::__construct' => ['hasSideEffects' => false],
	'EventHttpConnection::__construct' => ['hasSideEffects' => false],
	'EventHttpRequest::__construct' => ['hasSideEffects' => false],
	'EventHttpRequest::getCommand' => ['hasSideEffects' => false],
	'EventHttpRequest::getConnection' => ['hasSideEffects' => false],
	'EventHttpRequest::getHost' => ['hasSideEffects' => false],
	'EventHttpRequest::getInputBuffer' => ['hasSideEffects' => false],
	'EventHttpRequest::getInputHeaders' => ['hasSideEffects' => false],
	'EventHttpRequest::getOutputBuffer' => ['hasSideEffects' => false],
	'EventHttpRequest::getOutputHeaders' => ['hasSideEffects' => false],
	'EventHttpRequest::getResponseCode' => ['hasSideEffects' => false],
	'EventHttpRequest::getUri' => ['hasSideEffects' => false],
	'EventSslContext::__construct' => ['hasSideEffects' => false],
	'Exception::__construct' => ['hasSideEffects' => false],
	'Exception::getCode' => ['hasSideEffects' => false],
	'Exception::getFile' => ['hasSideEffects' => false],
	'Exception::getLine' => ['hasSideEffects' => false],
	'Exception::getMessage' => ['hasSideEffects' => false],
	'Exception::getPrevious' => ['hasSideEffects' => false],
	'Exception::getTrace' => ['hasSideEffects' => false],
	'Exception::getTraceAsString' => ['hasSideEffects' => false],
	'Gmagick::getcopyright' => ['hasSideEffects' => false],
	'Gmagick::getfilename' => ['hasSideEffects' => false],
	'Gmagick::getimagebackgroundcolor' => ['hasSideEffects' => false],
	'Gmagick::getimageblueprimary' => ['hasSideEffects' => false],
	'Gmagick::getimagebordercolor' => ['hasSideEffects' => false],
	'Gmagick::getimagechanneldepth' => ['hasSideEffects' => false],
	'Gmagick::getimagecolors' => ['hasSideEffects' => false],
	'Gmagick::getimagecolorspace' => ['hasSideEffects' => false],
	'Gmagick::getimagecompose' => ['hasSideEffects' => false],
	'Gmagick::getimagedelay' => ['hasSideEffects' => false],
	'Gmagick::getimagedepth' => ['hasSideEffects' => false],
	'Gmagick::getimagedispose' => ['hasSideEffects' => false],
	'Gmagick::getimageextrema' => ['hasSideEffects' => false],
	'Gmagick::getimagefilename' => ['hasSideEffects' => false],
	'Gmagick::getimageformat' => ['hasSideEffects' => false],
	'Gmagick::getimagegamma' => ['hasSideEffects' => false],
	'Gmagick::getimagegreenprimary' => ['hasSideEffects' => false],
	'Gmagick::getimageheight' => ['hasSideEffects' => false],
	'Gmagick::getimagehistogram' => ['hasSideEffects' => false],
	'Gmagick::getimageindex' => ['hasSideEffects' => false],
	'Gmagick::getimageinterlacescheme' => ['hasSideEffects' => false],
	'Gmagick::getimageiterations' => ['hasSideEffects' => false],
	'Gmagick::getimagematte' => ['hasSideEffects' => false],
	'Gmagick::getimagemattecolor' => ['hasSideEffects' => false],
	'Gmagick::getimageprofile' => ['hasSideEffects' => false],
	'Gmagick::getimageredprimary' => ['hasSideEffects' => false],
	'Gmagick::getimagerenderingintent' => ['hasSideEffects' => false],
	'Gmagick::getimageresolution' => ['hasSideEffects' => false],
	'Gmagick::getimagescene' => ['hasSideEffects' => false],
	'Gmagick::getimagesignature' => ['hasSideEffects' => false],
	'Gmagick::getimagetype' => ['hasSideEffects' => false],
	'Gmagick::getimageunits' => ['hasSideEffects' => false],
	'Gmagick::getimagewhitepoint' => ['hasSideEffects' => false],
	'Gmagick::getimagewidth' => ['hasSideEffects' => false],
	'Gmagick::getpackagename' => ['hasSideEffects' => false],
	'Gmagick::getquantumdepth' => ['hasSideEffects' => false],
	'Gmagick::getreleasedate' => ['hasSideEffects' => false],
	'Gmagick::getsamplingfactors' => ['hasSideEffects' => false],
	'Gmagick::getsize' => ['hasSideEffects' => false],
	'Gmagick::getversion' => ['hasSideEffects' => false],
	'GmagickDraw::getfillcolor' => ['hasSideEffects' => false],
	'GmagickDraw::getfillopacity' => ['hasSideEffects' => false],
	'GmagickDraw::getfont' => ['hasSideEffects' => false],
	'GmagickDraw::getfontsize' => ['hasSideEffects' => false],
	'GmagickDraw::getfontstyle' => ['hasSideEffects' => false],
	'GmagickDraw::getfontweight' => ['hasSideEffects' => false],
	'GmagickDraw::getstrokecolor' => ['hasSideEffects' => false],
	'GmagickDraw::getstrokeopacity' => ['hasSideEffects' => false],
	'GmagickDraw::getstrokewidth' => ['hasSideEffects' => false],
	'GmagickDraw::gettextdecoration' => ['hasSideEffects' => false],
	'GmagickDraw::gettextencoding' => ['hasSideEffects' => false],
	'GmagickPixel::getcolor' => ['hasSideEffects' => false],
	'GmagickPixel::getcolorcount' => ['hasSideEffects' => false],
	'GmagickPixel::getcolorvalue' => ['hasSideEffects' => false],
	'HttpMessage::getBody' => ['hasSideEffects' => false],
	'HttpMessage::getHeader' => ['hasSideEffects' => false],
	'HttpMessage::getHeaders' => ['hasSideEffects' => false],
	'HttpMessage::getHttpVersion' => ['hasSideEffects' => false],
	'HttpMessage::getInfo' => ['hasSideEffects' => false],
	'HttpMessage::getParentMessage' => ['hasSideEffects' => false],
	'HttpMessage::getRequestMethod' => ['hasSideEffects' => false],
	'HttpMessage::getRequestUrl' => ['hasSideEffects' => false],
	'HttpMessage::getResponseCode' => ['hasSideEffects' => false],
	'HttpMessage::getResponseStatus' => ['hasSideEffects' => false],
	'HttpMessage::getType' => ['hasSideEffects' => false],
	'HttpQueryString::get' => ['hasSideEffects' => false],
	'HttpQueryString::getArray' => ['hasSideEffects' => false],
	'HttpQueryString::getBool' => ['hasSideEffects' => false],
	'HttpQueryString::getFloat' => ['hasSideEffects' => false],
	'HttpQueryString::getInt' => ['hasSideEffects' => false],
	'HttpQueryString::getObject' => ['hasSideEffects' => false],
	'HttpQueryString::getString' => ['hasSideEffects' => false],
	'HttpRequest::getBody' => ['hasSideEffects' => false],
	'HttpRequest::getContentType' => ['hasSideEffects' => false],
	'HttpRequest::getCookies' => ['hasSideEffects' => false],
	'HttpRequest::getHeaders' => ['hasSideEffects' => false],
	'HttpRequest::getHistory' => ['hasSideEffects' => false],
	'HttpRequest::getMethod' => ['hasSideEffects' => false],
	'HttpRequest::getOptions' => ['hasSideEffects' => false],
	'HttpRequest::getPostFields' => ['hasSideEffects' => false],
	'HttpRequest::getPostFiles' => ['hasSideEffects' => false],
	'HttpRequest::getPutData' => ['hasSideEffects' => false],
	'HttpRequest::getPutFile' => ['hasSideEffects' => false],
	'HttpRequest::getQueryData' => ['hasSideEffects' => false],
	'HttpRequest::getRawPostData' => ['hasSideEffects' => false],
	'HttpRequest::getRawRequestMessage' => ['hasSideEffects' => false],
	'HttpRequest::getRawResponseMessage' => ['hasSideEffects' => false],
	'HttpRequest::getRequestMessage' => ['hasSideEffects' => false],
	'HttpRequest::getResponseBody' => ['hasSideEffects' => false],
	'HttpRequest::getResponseCode' => ['hasSideEffects' => false],
	'HttpRequest::getResponseCookies' => ['hasSideEffects' => false],
	'HttpRequest::getResponseData' => ['hasSideEffects' => false],
	'HttpRequest::getResponseHeader' => ['hasSideEffects' => false],
	'HttpRequest::getResponseInfo' => ['hasSideEffects' => false],
	'HttpRequest::getResponseMessage' => ['hasSideEffects' => false],
	'HttpRequest::getResponseStatus' => ['hasSideEffects' => false],
	'HttpRequest::getSslOptions' => ['hasSideEffects' => false],
	'HttpRequest::getUrl' => ['hasSideEffects' => false],
	'HttpRequestPool::getAttachedRequests' => ['hasSideEffects' => false],
	'HttpRequestPool::getFinishedRequests' => ['hasSideEffects' => false],
	'Imagick::getColorspace' => ['hasSideEffects' => false],
	'Imagick::getCompression' => ['hasSideEffects' => false],
	'Imagick::getCompressionQuality' => ['hasSideEffects' => false],
	'Imagick::getConfigureOptions' => ['hasSideEffects' => false],
	'Imagick::getFeatures' => ['hasSideEffects' => false],
	'Imagick::getFilename' => ['hasSideEffects' => false],
	'Imagick::getFont' => ['hasSideEffects' => false],
	'Imagick::getFormat' => ['hasSideEffects' => false],
	'Imagick::getGravity' => ['hasSideEffects' => false],
	'Imagick::getHDRIEnabled' => ['hasSideEffects' => false],
	'Imagick::getImage' => ['hasSideEffects' => false],
	'Imagick::getImageAlphaChannel' => ['hasSideEffects' => false],
	'Imagick::getImageArtifact' => ['hasSideEffects' => false],
	'Imagick::getImageAttribute' => ['hasSideEffects' => false],
	'Imagick::getImageBackgroundColor' => ['hasSideEffects' => false],
	'Imagick::getImageBlob' => ['hasSideEffects' => false],
	'Imagick::getImageBluePrimary' => ['hasSideEffects' => false],
	'Imagick::getImageBorderColor' => ['hasSideEffects' => false],
	'Imagick::getImageChannelDepth' => ['hasSideEffects' => false],
	'Imagick::getImageChannelDistortion' => ['hasSideEffects' => false],
	'Imagick::getImageChannelDistortions' => ['hasSideEffects' => false],
	'Imagick::getImageChannelExtrema' => ['hasSideEffects' => false],
	'Imagick::getImageChannelKurtosis' => ['hasSideEffects' => false],
	'Imagick::getImageChannelMean' => ['hasSideEffects' => false],
	'Imagick::getImageChannelRange' => ['hasSideEffects' => false],
	'Imagick::getImageChannelStatistics' => ['hasSideEffects' => false],
	'Imagick::getImageClipMask' => ['hasSideEffects' => false],
	'Imagick::getImageColormapColor' => ['hasSideEffects' => false],
	'Imagick::getImageColors' => ['hasSideEffects' => false],
	'Imagick::getImageColorspace' => ['hasSideEffects' => false],
	'Imagick::getImageCompose' => ['hasSideEffects' => false],
	'Imagick::getImageCompression' => ['hasSideEffects' => false],
	'Imagick::getImageCompressionQuality' => ['hasSideEffects' => false],
	'Imagick::getImageDelay' => ['hasSideEffects' => false],
	'Imagick::getImageDepth' => ['hasSideEffects' => false],
	'Imagick::getImageDispose' => ['hasSideEffects' => false],
	'Imagick::getImageDistortion' => ['hasSideEffects' => false],
	'Imagick::getImageExtrema' => ['hasSideEffects' => false],
	'Imagick::getImageFilename' => ['hasSideEffects' => false],
	'Imagick::getImageFormat' => ['hasSideEffects' => false],
	'Imagick::getImageGamma' => ['hasSideEffects' => false],
	'Imagick::getImageGeometry' => ['hasSideEffects' => false],
	'Imagick::getImageGravity' => ['hasSideEffects' => false],
	'Imagick::getImageGreenPrimary' => ['hasSideEffects' => false],
	'Imagick::getImageHeight' => ['hasSideEffects' => false],
	'Imagick::getImageHistogram' => ['hasSideEffects' => false],
	'Imagick::getImageIndex' => ['hasSideEffects' => false],
	'Imagick::getImageInterlaceScheme' => ['hasSideEffects' => false],
	'Imagick::getImageInterpolateMethod' => ['hasSideEffects' => false],
	'Imagick::getImageIterations' => ['hasSideEffects' => false],
	'Imagick::getImageLength' => ['hasSideEffects' => false],
	'Imagick::getImageMatte' => ['hasSideEffects' => false],
	'Imagick::getImageMatteColor' => ['hasSideEffects' => false],
	'Imagick::getImageMimeType' => ['hasSideEffects' => false],
	'Imagick::getImageOrientation' => ['hasSideEffects' => false],
	'Imagick::getImagePage' => ['hasSideEffects' => false],
	'Imagick::getImagePixelColor' => ['hasSideEffects' => false],
	'Imagick::getImageProfile' => ['hasSideEffects' => false],
	'Imagick::getImageProfiles' => ['hasSideEffects' => false],
	'Imagick::getImageProperties' => ['hasSideEffects' => false],
	'Imagick::getImageProperty' => ['hasSideEffects' => false],
	'Imagick::getImageRedPrimary' => ['hasSideEffects' => false],
	'Imagick::getImageRegion' => ['hasSideEffects' => false],
	'Imagick::getImageRenderingIntent' => ['hasSideEffects' => false],
	'Imagick::getImageResolution' => ['hasSideEffects' => false],
	'Imagick::getImageScene' => ['hasSideEffects' => false],
	'Imagick::getImageSignature' => ['hasSideEffects' => false],
	'Imagick::getImageSize' => ['hasSideEffects' => false],
	'Imagick::getImageTicksPerSecond' => ['hasSideEffects' => false],
	'Imagick::getImageTotalInkDensity' => ['hasSideEffects' => false],
	'Imagick::getImageType' => ['hasSideEffects' => false],
	'Imagick::getImageUnits' => ['hasSideEffects' => false],
	'Imagick::getImageVirtualPixelMethod' => ['hasSideEffects' => false],
	'Imagick::getImageWhitePoint' => ['hasSideEffects' => false],
	'Imagick::getImageWidth' => ['hasSideEffects' => false],
	'Imagick::getImagesBlob' => ['hasSideEffects' => false],
	'Imagick::getInterlaceScheme' => ['hasSideEffects' => false],
	'Imagick::getIteratorIndex' => ['hasSideEffects' => false],
	'Imagick::getNumberImages' => ['hasSideEffects' => false],
	'Imagick::getOption' => ['hasSideEffects' => false],
	'Imagick::getPage' => ['hasSideEffects' => false],
	'Imagick::getPixelIterator' => ['hasSideEffects' => false],
	'Imagick::getPixelRegionIterator' => ['hasSideEffects' => false],
	'Imagick::getPointSize' => ['hasSideEffects' => false],
	'Imagick::getSamplingFactors' => ['hasSideEffects' => false],
	'Imagick::getSize' => ['hasSideEffects' => false],
	'Imagick::getSizeOffset' => ['hasSideEffects' => false],
	'ImagickDraw::getBorderColor' => ['hasSideEffects' => false],
	'ImagickDraw::getClipPath' => ['hasSideEffects' => false],
	'ImagickDraw::getClipRule' => ['hasSideEffects' => false],
	'ImagickDraw::getClipUnits' => ['hasSideEffects' => false],
	'ImagickDraw::getDensity' => ['hasSideEffects' => false],
	'ImagickDraw::getFillColor' => ['hasSideEffects' => false],
	'ImagickDraw::getFillOpacity' => ['hasSideEffects' => false],
	'ImagickDraw::getFillRule' => ['hasSideEffects' => false],
	'ImagickDraw::getFont' => ['hasSideEffects' => false],
	'ImagickDraw::getFontFamily' => ['hasSideEffects' => false],
	'ImagickDraw::getFontResolution' => ['hasSideEffects' => false],
	'ImagickDraw::getFontSize' => ['hasSideEffects' => false],
	'ImagickDraw::getFontStretch' => ['hasSideEffects' => false],
	'ImagickDraw::getFontStyle' => ['hasSideEffects' => false],
	'ImagickDraw::getFontWeight' => ['hasSideEffects' => false],
	'ImagickDraw::getGravity' => ['hasSideEffects' => false],
	'ImagickDraw::getOpacity' => ['hasSideEffects' => false],
	'ImagickDraw::getStrokeAntialias' => ['hasSideEffects' => false],
	'ImagickDraw::getStrokeColor' => ['hasSideEffects' => false],
	'ImagickDraw::getStrokeDashArray' => ['hasSideEffects' => false],
	'ImagickDraw::getStrokeDashOffset' => ['hasSideEffects' => false],
	'ImagickDraw::getStrokeLineCap' => ['hasSideEffects' => false],
	'ImagickDraw::getStrokeLineJoin' => ['hasSideEffects' => false],
	'ImagickDraw::getStrokeMiterLimit' => ['hasSideEffects' => false],
	'ImagickDraw::getStrokeOpacity' => ['hasSideEffects' => false],
	'ImagickDraw::getStrokeWidth' => ['hasSideEffects' => false],
	'ImagickDraw::getTextAlignment' => ['hasSideEffects' => false],
	'ImagickDraw::getTextAntialias' => ['hasSideEffects' => false],
	'ImagickDraw::getTextDecoration' => ['hasSideEffects' => false],
	'ImagickDraw::getTextDirection' => ['hasSideEffects' => false],
	'ImagickDraw::getTextEncoding' => ['hasSideEffects' => false],
	'ImagickDraw::getTextInterLineSpacing' => ['hasSideEffects' => false],
	'ImagickDraw::getTextInterWordSpacing' => ['hasSideEffects' => false],
	'ImagickDraw::getTextKerning' => ['hasSideEffects' => false],
	'ImagickDraw::getTextUnderColor' => ['hasSideEffects' => false],
	'ImagickDraw::getVectorGraphics' => ['hasSideEffects' => false],
	'ImagickKernel::getMatrix' => ['hasSideEffects' => false],
	'ImagickPixel::getColor' => ['hasSideEffects' => false],
	'ImagickPixel::getColorAsString' => ['hasSideEffects' => false],
	'ImagickPixel::getColorCount' => ['hasSideEffects' => false],
	'ImagickPixel::getColorQuantum' => ['hasSideEffects' => false],
	'ImagickPixel::getColorValue' => ['hasSideEffects' => false],
	'ImagickPixel::getColorValueQuantum' => ['hasSideEffects' => false],
	'ImagickPixel::getHSL' => ['hasSideEffects' => false],
	'ImagickPixel::getIndex' => ['hasSideEffects' => false],
	'ImagickPixelIterator::getCurrentIteratorRow' => ['hasSideEffects' => false],
	'ImagickPixelIterator::getIteratorRow' => ['hasSideEffects' => false],
	'ImagickPixelIterator::getNextIteratorRow' => ['hasSideEffects' => false],
	'ImagickPixelIterator::getPreviousIteratorRow' => ['hasSideEffects' => false],
	'IntBackedEnum::from' => ['hasSideEffects' => false],
	'IntBackedEnum::tryFrom' => ['hasSideEffects' => false],
	'IntlBreakIterator::current' => ['hasSideEffects' => false],
	'IntlBreakIterator::getErrorCode' => ['hasSideEffects' => false],
	'IntlBreakIterator::getErrorMessage' => ['hasSideEffects' => false],
	'IntlBreakIterator::getIterator' => ['hasSideEffects' => false],
	'IntlBreakIterator::getLocale' => ['hasSideEffects' => false],
	'IntlBreakIterator::getPartsIterator' => ['hasSideEffects' => false],
	'IntlBreakIterator::getText' => ['hasSideEffects' => false],
	'IntlBreakIterator::isBoundary' => ['hasSideEffects' => false],
	'IntlCalendar::after' => ['hasSideEffects' => false],
	'IntlCalendar::before' => ['hasSideEffects' => false],
	'IntlCalendar::equals' => ['hasSideEffects' => false],
	'IntlCalendar::fieldDifference' => ['hasSideEffects' => false],
	'IntlCalendar::get' => ['hasSideEffects' => false],
	'IntlCalendar::getActualMaximum' => ['hasSideEffects' => false],
	'IntlCalendar::getActualMinimum' => ['hasSideEffects' => false],
	'IntlCalendar::getDayOfWeekType' => ['hasSideEffects' => false],
	'IntlCalendar::getErrorCode' => ['hasSideEffects' => false],
	'IntlCalendar::getErrorMessage' => ['hasSideEffects' => false],
	'IntlCalendar::getFirstDayOfWeek' => ['hasSideEffects' => false],
	'IntlCalendar::getGreatestMinimum' => ['hasSideEffects' => false],
	'IntlCalendar::getLeastMaximum' => ['hasSideEffects' => false],
	'IntlCalendar::getLocale' => ['hasSideEffects' => false],
	'IntlCalendar::getMaximum' => ['hasSideEffects' => false],
	'IntlCalendar::getMinimalDaysInFirstWeek' => ['hasSideEffects' => false],
	'IntlCalendar::getMinimum' => ['hasSideEffects' => false],
	'IntlCalendar::getRepeatedWallTimeOption' => ['hasSideEffects' => false],
	'IntlCalendar::getSkippedWallTimeOption' => ['hasSideEffects' => false],
	'IntlCalendar::getTime' => ['hasSideEffects' => false],
	'IntlCalendar::getTimeZone' => ['hasSideEffects' => false],
	'IntlCalendar::getType' => ['hasSideEffects' => false],
	'IntlCalendar::getWeekendTransition' => ['hasSideEffects' => false],
	'IntlCalendar::inDaylightTime' => ['hasSideEffects' => false],
	'IntlCalendar::isEquivalentTo' => ['hasSideEffects' => false],
	'IntlCalendar::isLenient' => ['hasSideEffects' => false],
	'IntlCalendar::isWeekend' => ['hasSideEffects' => false],
	'IntlCalendar::toDateTime' => ['hasSideEffects' => false],
	'IntlChar::hasBinaryProperty' => ['hasSideEffects' => false],
	'IntlCodePointBreakIterator::getLastCodePoint' => ['hasSideEffects' => false],
	'IntlDateFormatter::__construct' => ['hasSideEffects' => false],
	'IntlDateFormatter::getCalendar' => ['hasSideEffects' => false],
	'IntlDateFormatter::getCalendarObject' => ['hasSideEffects' => false],
	'IntlDateFormatter::getDateType' => ['hasSideEffects' => false],
	'IntlDateFormatter::getErrorCode' => ['hasSideEffects' => false],
	'IntlDateFormatter::getErrorMessage' => ['hasSideEffects' => false],
	'IntlDateFormatter::getLocale' => ['hasSideEffects' => false],
	'IntlDateFormatter::getPattern' => ['hasSideEffects' => false],
	'IntlDateFormatter::getTimeType' => ['hasSideEffects' => false],
	'IntlDateFormatter::getTimeZone' => ['hasSideEffects' => false],
	'IntlDateFormatter::getTimeZoneId' => ['hasSideEffects' => false],
	'IntlDateFormatter::isLenient' => ['hasSideEffects' => false],
	'IntlGregorianCalendar::getGregorianChange' => ['hasSideEffects' => false],
	'IntlGregorianCalendar::isLeapYear' => ['hasSideEffects' => false],
	'IntlPartsIterator::getBreakIterator' => ['hasSideEffects' => false],
	'IntlRuleBasedBreakIterator::__construct' => ['hasSideEffects' => false],
	'IntlRuleBasedBreakIterator::getBinaryRules' => ['hasSideEffects' => false],
	'IntlRuleBasedBreakIterator::getRuleStatus' => ['hasSideEffects' => false],
	'IntlRuleBasedBreakIterator::getRuleStatusVec' => ['hasSideEffects' => false],
	'IntlRuleBasedBreakIterator::getRules' => ['hasSideEffects' => false],
	'IntlTimeZone::getDSTSavings' => ['hasSideEffects' => false],
	'IntlTimeZone::getDisplayName' => ['hasSideEffects' => false],
	'IntlTimeZone::getErrorCode' => ['hasSideEffects' => false],
	'IntlTimeZone::getErrorMessage' => ['hasSideEffects' => false],
	'IntlTimeZone::getID' => ['hasSideEffects' => false],
	'IntlTimeZone::getRawOffset' => ['hasSideEffects' => false],
	'IntlTimeZone::hasSameRules' => ['hasSideEffects' => false],
	'IntlTimeZone::toDateTimeZone' => ['hasSideEffects' => false],
	'JsonIncrementalParser::__construct' => ['hasSideEffects' => false],
	'JsonIncrementalParser::get' => ['hasSideEffects' => false],
	'JsonIncrementalParser::getError' => ['hasSideEffects' => false],
	'MemcachedException::__construct' => ['hasSideEffects' => false],
	'MessageFormatter::__construct' => ['hasSideEffects' => false],
	'MessageFormatter::format' => ['hasSideEffects' => false],
	'MessageFormatter::getErrorCode' => ['hasSideEffects' => false],
	'MessageFormatter::getErrorMessage' => ['hasSideEffects' => false],
	'MessageFormatter::getLocale' => ['hasSideEffects' => false],
	'MessageFormatter::getPattern' => ['hasSideEffects' => false],
	'MessageFormatter::parse' => ['hasSideEffects' => false],
	'NumberFormatter::__construct' => ['hasSideEffects' => false],
	'NumberFormatter::format' => ['hasSideEffects' => false],
	'NumberFormatter::formatCurrency' => ['hasSideEffects' => false],
	'NumberFormatter::getAttribute' => ['hasSideEffects' => false],
	'NumberFormatter::getErrorCode' => ['hasSideEffects' => false],
	'NumberFormatter::getErrorMessage' => ['hasSideEffects' => false],
	'NumberFormatter::getLocale' => ['hasSideEffects' => false],
	'NumberFormatter::getPattern' => ['hasSideEffects' => false],
	'NumberFormatter::getSymbol' => ['hasSideEffects' => false],
	'NumberFormatter::getTextAttribute' => ['hasSideEffects' => false],
	'ReflectionAttribute::getArguments' => ['hasSideEffects' => false],
	'ReflectionAttribute::getName' => ['hasSideEffects' => false],
	'ReflectionAttribute::getTarget' => ['hasSideEffects' => false],
	'ReflectionAttribute::isRepeated' => ['hasSideEffects' => false],
	'ReflectionClass::getAttributes' => ['hasSideEffects' => false],
	'ReflectionClass::getConstant' => ['hasSideEffects' => false],
	'ReflectionClass::getConstants' => ['hasSideEffects' => false],
	'ReflectionClass::getConstructor' => ['hasSideEffects' => false],
	'ReflectionClass::getDefaultProperties' => ['hasSideEffects' => false],
	'ReflectionClass::getDocComment' => ['hasSideEffects' => false],
	'ReflectionClass::getEndLine' => ['hasSideEffects' => false],
	'ReflectionClass::getExtension' => ['hasSideEffects' => false],
	'ReflectionClass::getExtensionName' => ['hasSideEffects' => false],
	'ReflectionClass::getFileName' => ['hasSideEffects' => false],
	'ReflectionClass::getInterfaceNames' => ['hasSideEffects' => false],
	'ReflectionClass::getInterfaces' => ['hasSideEffects' => false],
	'ReflectionClass::getMethod' => ['hasSideEffects' => false],
	'ReflectionClass::getMethods' => ['hasSideEffects' => false],
	'ReflectionClass::getModifiers' => ['hasSideEffects' => false],
	'ReflectionClass::getName' => ['hasSideEffects' => false],
	'ReflectionClass::getNamespaceName' => ['hasSideEffects' => false],
	'ReflectionClass::getParentClass' => ['hasSideEffects' => false],
	'ReflectionClass::getProperties' => ['hasSideEffects' => false],
	'ReflectionClass::getProperty' => ['hasSideEffects' => false],
	'ReflectionClass::getReflectionConstant' => ['hasSideEffects' => false],
	'ReflectionClass::getReflectionConstants' => ['hasSideEffects' => false],
	'ReflectionClass::getShortName' => ['hasSideEffects' => false],
	'ReflectionClass::getStartLine' => ['hasSideEffects' => false],
	'ReflectionClass::getStaticProperties' => ['hasSideEffects' => false],
	'ReflectionClass::getStaticPropertyValue' => ['hasSideEffects' => false],
	'ReflectionClass::getTraitAliases' => ['hasSideEffects' => false],
	'ReflectionClass::getTraitNames' => ['hasSideEffects' => false],
	'ReflectionClass::getTraits' => ['hasSideEffects' => false],
	'ReflectionClass::isAbstract' => ['hasSideEffects' => false],
	'ReflectionClass::isAnonymous' => ['hasSideEffects' => false],
	'ReflectionClass::isCloneable' => ['hasSideEffects' => false],
	'ReflectionClass::isFinal' => ['hasSideEffects' => false],
	'ReflectionClass::isInstance' => ['hasSideEffects' => false],
	'ReflectionClass::isInstantiable' => ['hasSideEffects' => false],
	'ReflectionClass::isInterface' => ['hasSideEffects' => false],
	'ReflectionClass::isInternal' => ['hasSideEffects' => false],
	'ReflectionClass::isIterable' => ['hasSideEffects' => false],
	'ReflectionClass::isIterateable' => ['hasSideEffects' => false],
	'ReflectionClass::isReadOnly' => ['hasSideEffects' => false],
	'ReflectionClass::isSubclassOf' => ['hasSideEffects' => false],
	'ReflectionClass::isTrait' => ['hasSideEffects' => false],
	'ReflectionClass::isUserDefined' => ['hasSideEffects' => false],
	'ReflectionClassConstant::getAttributes' => ['hasSideEffects' => false],
	'ReflectionClassConstant::getDeclaringClass' => ['hasSideEffects' => false],
	'ReflectionClassConstant::getDocComment' => ['hasSideEffects' => false],
	'ReflectionClassConstant::getModifiers' => ['hasSideEffects' => false],
	'ReflectionClassConstant::getName' => ['hasSideEffects' => false],
	'ReflectionClassConstant::getValue' => ['hasSideEffects' => false],
	'ReflectionClassConstant::isPrivate' => ['hasSideEffects' => false],
	'ReflectionClassConstant::isProtected' => ['hasSideEffects' => false],
	'ReflectionClassConstant::isPublic' => ['hasSideEffects' => false],
	'ReflectionEnumBackedCase::getBackingValue' => ['hasSideEffects' => false],
	'ReflectionEnumUnitCase::getEnum' => ['hasSideEffects' => false],
	'ReflectionEnumUnitCase::getValue' => ['hasSideEffects' => false],
	'ReflectionExtension::getClassNames' => ['hasSideEffects' => false],
	'ReflectionExtension::getClasses' => ['hasSideEffects' => false],
	'ReflectionExtension::getConstants' => ['hasSideEffects' => false],
	'ReflectionExtension::getDependencies' => ['hasSideEffects' => false],
	'ReflectionExtension::getFunctions' => ['hasSideEffects' => false],
	'ReflectionExtension::getINIEntries' => ['hasSideEffects' => false],
	'ReflectionExtension::getName' => ['hasSideEffects' => false],
	'ReflectionExtension::getVersion' => ['hasSideEffects' => false],
	'ReflectionExtension::isPersistent' => ['hasSideEffects' => false],
	'ReflectionExtension::isTemporary' => ['hasSideEffects' => false],
	'ReflectionFunction::getClosure' => ['hasSideEffects' => false],
	'ReflectionFunction::isDisabled' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::getAttributes' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::getClosureCalledClass' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::getClosureScopeClass' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::getClosureThis' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::getClosureUsedVariables' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::getDocComment' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::getEndLine' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::getExtension' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::getExtensionName' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::getFileName' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::getName' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::getNamespaceName' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::getNumberOfParameters' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::getNumberOfRequiredParameters' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::getParameters' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::getReturnType' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::getShortName' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::getStartLine' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::getStaticVariables' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::getTentativeReturnType' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::hasTentativeReturnType' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::isClosure' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::isDeprecated' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::isGenerator' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::isInternal' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::isStatic' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::isUserDefined' => ['hasSideEffects' => false],
	'ReflectionFunctionAbstract::isVariadic' => ['hasSideEffects' => false],
	'ReflectionGenerator::getExecutingFile' => ['hasSideEffects' => false],
	'ReflectionGenerator::getExecutingGenerator' => ['hasSideEffects' => false],
	'ReflectionGenerator::getExecutingLine' => ['hasSideEffects' => false],
	'ReflectionGenerator::getFunction' => ['hasSideEffects' => false],
	'ReflectionGenerator::getThis' => ['hasSideEffects' => false],
	'ReflectionGenerator::getTrace' => ['hasSideEffects' => false],
	'ReflectionIntersectionType::getTypes' => ['hasSideEffects' => false],
	'ReflectionMethod::getClosure' => ['hasSideEffects' => false],
	'ReflectionMethod::getDeclaringClass' => ['hasSideEffects' => false],
	'ReflectionMethod::getModifiers' => ['hasSideEffects' => false],
	'ReflectionMethod::getPrototype' => ['hasSideEffects' => false],
	'ReflectionMethod::isAbstract' => ['hasSideEffects' => false],
	'ReflectionMethod::isConstructor' => ['hasSideEffects' => false],
	'ReflectionMethod::isDestructor' => ['hasSideEffects' => false],
	'ReflectionMethod::isFinal' => ['hasSideEffects' => false],
	'ReflectionMethod::isPrivate' => ['hasSideEffects' => false],
	'ReflectionMethod::isProtected' => ['hasSideEffects' => false],
	'ReflectionMethod::isPublic' => ['hasSideEffects' => false],
	'ReflectionMethod::isStatic' => ['hasSideEffects' => false],
	'ReflectionMethod::setAccessible' => ['hasSideEffects' => false],
	'ReflectionNamedType::getName' => ['hasSideEffects' => false],
	'ReflectionNamedType::isBuiltin' => ['hasSideEffects' => false],
	'ReflectionParameter::getAttributes' => ['hasSideEffects' => false],
	'ReflectionParameter::getClass' => ['hasSideEffects' => false],
	'ReflectionParameter::getDeclaringClass' => ['hasSideEffects' => false],
	'ReflectionParameter::getDeclaringFunction' => ['hasSideEffects' => false],
	'ReflectionParameter::getDefaultValue' => ['hasSideEffects' => false],
	'ReflectionParameter::getDefaultValueConstantName' => ['hasSideEffects' => false],
	'ReflectionParameter::getName' => ['hasSideEffects' => false],
	'ReflectionParameter::getPosition' => ['hasSideEffects' => false],
	'ReflectionParameter::getType' => ['hasSideEffects' => false],
	'ReflectionParameter::isArray' => ['hasSideEffects' => false],
	'ReflectionParameter::isCallable' => ['hasSideEffects' => false],
	'ReflectionParameter::isDefaultValueAvailable' => ['hasSideEffects' => false],
	'ReflectionParameter::isDefaultValueConstant' => ['hasSideEffects' => false],
	'ReflectionParameter::isOptional' => ['hasSideEffects' => false],
	'ReflectionParameter::isPassedByReference' => ['hasSideEffects' => false],
	'ReflectionParameter::isPromoted' => ['hasSideEffects' => false],
	'ReflectionParameter::isVariadic' => ['hasSideEffects' => false],
	'ReflectionProperty::getAttributes' => ['hasSideEffects' => false],
	'ReflectionProperty::getDeclaringClass' => ['hasSideEffects' => false],
	'ReflectionProperty::getDefaultValue' => ['hasSideEffects' => false],
	'ReflectionProperty::getDocComment' => ['hasSideEffects' => false],
	'ReflectionProperty::getModifiers' => ['hasSideEffects' => false],
	'ReflectionProperty::getName' => ['hasSideEffects' => false],
	'ReflectionProperty::getType' => ['hasSideEffects' => false],
	'ReflectionProperty::getValue' => ['hasSideEffects' => false],
	'ReflectionProperty::isDefault' => ['hasSideEffects' => false],
	'ReflectionProperty::isInitialized' => ['hasSideEffects' => false],
	'ReflectionProperty::isPrivate' => ['hasSideEffects' => false],
	'ReflectionProperty::isPromoted' => ['hasSideEffects' => false],
	'ReflectionProperty::isProtected' => ['hasSideEffects' => false],
	'ReflectionProperty::isPublic' => ['hasSideEffects' => false],
	'ReflectionProperty::isStatic' => ['hasSideEffects' => false],
	'ReflectionProperty::setAccessible' => ['hasSideEffects' => false],
	'ReflectionReference::getId' => ['hasSideEffects' => false],
	'ReflectionType::isBuiltin' => ['hasSideEffects' => false],
	'ReflectionUnionType::getTypes' => ['hasSideEffects' => false],
	'ReflectionZendExtension::getAuthor' => ['hasSideEffects' => false],
	'ReflectionZendExtension::getCopyright' => ['hasSideEffects' => false],
	'ReflectionZendExtension::getName' => ['hasSideEffects' => false],
	'ReflectionZendExtension::getURL' => ['hasSideEffects' => false],
	'ReflectionZendExtension::getVersion' => ['hasSideEffects' => false],
	'ResourceBundle::__construct' => ['hasSideEffects' => false],
	'ResourceBundle::count' => ['hasSideEffects' => false],
	'ResourceBundle::get' => ['hasSideEffects' => false],
	'ResourceBundle::getErrorCode' => ['hasSideEffects' => false],
	'ResourceBundle::getErrorMessage' => ['hasSideEffects' => false],
	'ResourceBundle::getIterator' => ['hasSideEffects' => false],
	'SQLiteException::__construct' => ['hasSideEffects' => false],
	'SimpleXMLElement::__construct' => ['hasSideEffects' => false],
	'SimpleXMLElement::children' => ['hasSideEffects' => false],
	'SimpleXMLElement::count' => ['hasSideEffects' => false],
	'SimpleXMLElement::current' => ['hasSideEffects' => false],
	'SimpleXMLElement::getChildren' => ['hasSideEffects' => false],
	'SimpleXMLElement::getDocNamespaces' => ['hasSideEffects' => false],
	'SimpleXMLElement::getName' => ['hasSideEffects' => false],
	'SimpleXMLElement::getNamespaces' => ['hasSideEffects' => false],
	'SimpleXMLElement::hasChildren' => ['hasSideEffects' => false],
	'SimpleXMLElement::offsetExists' => ['hasSideEffects' => false],
	'SimpleXMLElement::offsetGet' => ['hasSideEffects' => false],
	'SimpleXMLElement::valid' => ['hasSideEffects' => false],
	'SimpleXMLIterator::count' => ['hasSideEffects' => false],
	'SimpleXMLIterator::current' => ['hasSideEffects' => false],
	'SimpleXMLIterator::getChildren' => ['hasSideEffects' => false],
	'SimpleXMLIterator::hasChildren' => ['hasSideEffects' => false],
	'SimpleXMLIterator::valid' => ['hasSideEffects' => false],
	'SoapFault::__construct' => ['hasSideEffects' => false],
	'SplFileObject::fflush' => ['hasSideEffects' => true],
	'SplFileObject::fgetc' => ['hasSideEffects' => true],
	'SplFileObject::fgetcsv' => ['hasSideEffects' => true],
	'SplFileObject::fgets' => ['hasSideEffects' => true],
	'SplFileObject::fgetss' => ['hasSideEffects' => true],
	'SplFileObject::fpassthru' => ['hasSideEffects' => true],
	'SplFileObject::fputcsv' => ['hasSideEffects' => true],
	'SplFileObject::fread' => ['hasSideEffects' => true],
	'SplFileObject::fscanf' => ['hasSideEffects' => true],
	'SplFileObject::fseek' => ['hasSideEffects' => true],
	'SplFileObject::ftruncate' => ['hasSideEffects' => true],
	'SplFileObject::fwrite' => ['hasSideEffects' => true],
	'Spoofchecker::__construct' => ['hasSideEffects' => false],
	'StringBackedEnum::from' => ['hasSideEffects' => false],
	'StringBackedEnum::tryFrom' => ['hasSideEffects' => false],
	'StubTests\\CodeStyle\\BracesOneLineFixer::getDefinition' => ['hasSideEffects' => false],
	'StubTests\\Parsers\\ExpectedFunctionArgumentsInfo::__toString' => ['hasSideEffects' => false],
	'StubTests\\Parsers\\Visitors\\CoreStubASTVisitor::__construct' => ['hasSideEffects' => false],
	'StubTests\\StubsMetaExpectedArgumentsTest::getClassMemberFqn' => ['hasSideEffects' => false],
	'StubTests\\StubsParameterNamesTest::printParameters' => ['hasSideEffects' => false],
	'Transliterator::createInverse' => ['hasSideEffects' => false],
	'Transliterator::getErrorCode' => ['hasSideEffects' => false],
	'Transliterator::getErrorMessage' => ['hasSideEffects' => false],
	'Transliterator::transliterate' => ['hasSideEffects' => false],
	'UConverter::__construct' => ['hasSideEffects' => false],
	'UConverter::convert' => ['hasSideEffects' => false],
	'UConverter::getDestinationEncoding' => ['hasSideEffects' => false],
	'UConverter::getDestinationType' => ['hasSideEffects' => false],
	'UConverter::getErrorCode' => ['hasSideEffects' => false],
	'UConverter::getErrorMessage' => ['hasSideEffects' => false],
	'UConverter::getSourceEncoding' => ['hasSideEffects' => false],
	'UConverter::getSourceType' => ['hasSideEffects' => false],
	'UConverter::getStandards' => ['hasSideEffects' => false],
	'UConverter::getSubstChars' => ['hasSideEffects' => false],
	'UConverter::reasonText' => ['hasSideEffects' => false],
	'UnitEnum::cases' => ['hasSideEffects' => false],
	'WeakMap::count' => ['hasSideEffects' => false],
	'WeakMap::getIterator' => ['hasSideEffects' => false],
	'WeakMap::offsetExists' => ['hasSideEffects' => false],
	'WeakMap::offsetGet' => ['hasSideEffects' => false],
	'WeakReference::create' => ['hasSideEffects' => false],
	'WeakReference::get' => ['hasSideEffects' => false],
	'XmlReader::next' => ['hasSideEffects' => true],
	'XmlReader::read' => ['hasSideEffects' => true],
	'Zookeeper::getAcl' => ['hasSideEffects' => false],
	'Zookeeper::getChildren' => ['hasSideEffects' => false],
	'Zookeeper::getClientId' => ['hasSideEffects' => false],
	'Zookeeper::getRecvTimeout' => ['hasSideEffects' => false],
	'Zookeeper::getState' => ['hasSideEffects' => false],
	'_' => ['hasSideEffects' => false],
	'abs' => ['hasSideEffects' => false],
	'acos' => ['hasSideEffects' => false],
	'acosh' => ['hasSideEffects' => false],
	'addcslashes' => ['hasSideEffects' => false],
	'addslashes' => ['hasSideEffects' => false],
	'apache_get_modules' => ['hasSideEffects' => false],
	'apache_get_version' => ['hasSideEffects' => false],
	'apache_getenv' => ['hasSideEffects' => false],
	'apache_request_headers' => ['hasSideEffects' => false],
	'array_change_key_case' => ['hasSideEffects' => false],
	'array_chunk' => ['hasSideEffects' => false],
	'array_column' => ['hasSideEffects' => false],
	'array_combine' => ['hasSideEffects' => false],
	'array_count_values' => ['hasSideEffects' => false],
	'array_diff' => ['hasSideEffects' => false],
	'array_diff_assoc' => ['hasSideEffects' => false],
	'array_diff_key' => ['hasSideEffects' => false],
	'array_diff_uassoc' => ['hasSideEffects' => false],
	'array_diff_ukey' => ['hasSideEffects' => false],
	'array_fill' => ['hasSideEffects' => false],
	'array_fill_keys' => ['hasSideEffects' => false],
	'array_flip' => ['hasSideEffects' => false],
	'array_intersect' => ['hasSideEffects' => false],
	'array_intersect_assoc' => ['hasSideEffects' => false],
	'array_intersect_key' => ['hasSideEffects' => false],
	'array_intersect_uassoc' => ['hasSideEffects' => false],
	'array_intersect_ukey' => ['hasSideEffects' => false],
	'array_is_list' => ['hasSideEffects' => false],
	'array_key_exists' => ['hasSideEffects' => false],
	'array_key_first' => ['hasSideEffects' => false],
	'array_key_last' => ['hasSideEffects' => false],
	'array_keys' => ['hasSideEffects' => false],
	'array_merge' => ['hasSideEffects' => false],
	'array_merge_recursive' => ['hasSideEffects' => false],
	'array_pad' => ['hasSideEffects' => false],
	'array_pop' => ['hasSideEffects' => true],
	'array_product' => ['hasSideEffects' => false],
	'array_push' => ['hasSideEffects' => true],
	'array_rand' => ['hasSideEffects' => false],
	'array_replace' => ['hasSideEffects' => false],
	'array_replace_recursive' => ['hasSideEffects' => false],
	'array_reverse' => ['hasSideEffects' => false],
	'array_search' => ['hasSideEffects' => false],
	'array_shift' => ['hasSideEffects' => true],
	'array_slice' => ['hasSideEffects' => false],
	'array_sum' => ['hasSideEffects' => false],
	'array_udiff' => ['hasSideEffects' => false],
	'array_udiff_assoc' => ['hasSideEffects' => false],
	'array_udiff_uassoc' => ['hasSideEffects' => false],
	'array_uintersect' => ['hasSideEffects' => false],
	'array_uintersect_assoc' => ['hasSideEffects' => false],
	'array_uintersect_uassoc' => ['hasSideEffects' => false],
	'array_unique' => ['hasSideEffects' => false],
	'array_unshift' => ['hasSideEffects' => true],
	'array_values' => ['hasSideEffects' => false],
	'asin' => ['hasSideEffects' => false],
	'asinh' => ['hasSideEffects' => false],
	'atan' => ['hasSideEffects' => false],
	'atan2' => ['hasSideEffects' => false],
	'atanh' => ['hasSideEffects' => false],
	'base64_decode' => ['hasSideEffects' => false],
	'base64_encode' => ['hasSideEffects' => false],
	'base_convert' => ['hasSideEffects' => false],
	'basename' => ['hasSideEffects' => false],
	'bcadd' => ['hasSideEffects' => false],
	'bccomp' => ['hasSideEffects' => false],
	'bcdiv' => ['hasSideEffects' => false],
	'bcmod' => ['hasSideEffects' => false],
	'bcmul' => ['hasSideEffects' => false],
	'bcpow' => ['hasSideEffects' => false],
	'bcpowmod' => ['hasSideEffects' => false],
	'bcsqrt' => ['hasSideEffects' => false],
	'bcsub' => ['hasSideEffects' => false],
	'bin2hex' => ['hasSideEffects' => false],
	'bindec' => ['hasSideEffects' => false],
	'boolval' => ['hasSideEffects' => false],
	'bzcompress' => ['hasSideEffects' => false],
	'bzdecompress' => ['hasSideEffects' => false],
	'bzerrno' => ['hasSideEffects' => false],
	'bzerror' => ['hasSideEffects' => false],
	'bzerrstr' => ['hasSideEffects' => false],
	'bzopen' => ['hasSideEffects' => false],
	'ceil' => ['hasSideEffects' => false],
	'checkdate' => ['hasSideEffects' => false],
	'checkdnsrr' => ['hasSideEffects' => false],
	'chgrp' => ['hasSideEffects' => true],
	'chmod' => ['hasSideEffects' => true],
	'chop' => ['hasSideEffects' => false],
	'chown' => ['hasSideEffects' => true],
	'chr' => ['hasSideEffects' => false],
	'chunk_split' => ['hasSideEffects' => false],
	'class_implements' => ['hasSideEffects' => false],
	'class_parents' => ['hasSideEffects' => false],
	'cli_get_process_title' => ['hasSideEffects' => false],
	'collator_compare' => ['hasSideEffects' => false],
	'collator_create' => ['hasSideEffects' => false],
	'collator_get_attribute' => ['hasSideEffects' => false],
	'collator_get_error_code' => ['hasSideEffects' => false],
	'collator_get_error_message' => ['hasSideEffects' => false],
	'collator_get_locale' => ['hasSideEffects' => false],
	'collator_get_sort_key' => ['hasSideEffects' => false],
	'collator_get_strength' => ['hasSideEffects' => false],
	'compact' => ['hasSideEffects' => false],
	'connection_aborted' => ['hasSideEffects' => true],
	'connection_status' => ['hasSideEffects' => true],
	'constant' => ['hasSideEffects' => false],
	'convert_cyr_string' => ['hasSideEffects' => false],
	'convert_uudecode' => ['hasSideEffects' => false],
	'convert_uuencode' => ['hasSideEffects' => false],
	'copy' => ['hasSideEffects' => true],
	'cos' => ['hasSideEffects' => false],
	'cosh' => ['hasSideEffects' => false],
	'count' => ['hasSideEffects' => false],
	'count_chars' => ['hasSideEffects' => false],
	'crc32' => ['hasSideEffects' => false],
	'crypt' => ['hasSideEffects' => false],
	'ctype_alnum' => ['hasSideEffects' => false],
	'ctype_alpha' => ['hasSideEffects' => false],
	'ctype_cntrl' => ['hasSideEffects' => false],
	'ctype_digit' => ['hasSideEffects' => false],
	'ctype_graph' => ['hasSideEffects' => false],
	'ctype_lower' => ['hasSideEffects' => false],
	'ctype_print' => ['hasSideEffects' => false],
	'ctype_punct' => ['hasSideEffects' => false],
	'ctype_space' => ['hasSideEffects' => false],
	'ctype_upper' => ['hasSideEffects' => false],
	'ctype_xdigit' => ['hasSideEffects' => false],
	'curl_copy_handle' => ['hasSideEffects' => false],
	'curl_errno' => ['hasSideEffects' => false],
	'curl_error' => ['hasSideEffects' => false],
	'curl_escape' => ['hasSideEffects' => false],
	'curl_file_create' => ['hasSideEffects' => false],
	'curl_getinfo' => ['hasSideEffects' => false],
	'curl_multi_errno' => ['hasSideEffects' => false],
	'curl_multi_getcontent' => ['hasSideEffects' => false],
	'curl_multi_info_read' => ['hasSideEffects' => false],
	'curl_share_errno' => ['hasSideEffects' => false],
	'curl_share_strerror' => ['hasSideEffects' => false],
	'curl_strerror' => ['hasSideEffects' => false],
	'curl_unescape' => ['hasSideEffects' => false],
	'curl_version' => ['hasSideEffects' => false],
	'current' => ['hasSideEffects' => false],
	'date' => ['hasSideEffects' => false],
	'date_create' => ['hasSideEffects' => false],
	'date_create_from_format' => ['hasSideEffects' => false],
	'date_create_immutable' => ['hasSideEffects' => false],
	'date_create_immutable_from_format' => ['hasSideEffects' => false],
	'date_default_timezone_get' => ['hasSideEffects' => false],
	'date_diff' => ['hasSideEffects' => false],
	'date_format' => ['hasSideEffects' => false],
	'date_get_last_errors' => ['hasSideEffects' => false],
	'date_interval_create_from_date_string' => ['hasSideEffects' => false],
	'date_interval_format' => ['hasSideEffects' => false],
	'date_offset_get' => ['hasSideEffects' => false],
	'date_parse' => ['hasSideEffects' => false],
	'date_parse_from_format' => ['hasSideEffects' => false],
	'date_sun_info' => ['hasSideEffects' => false],
	'date_sunrise' => ['hasSideEffects' => false],
	'date_sunset' => ['hasSideEffects' => false],
	'date_timestamp_get' => ['hasSideEffects' => false],
	'date_timezone_get' => ['hasSideEffects' => false],
	'datefmt_create' => ['hasSideEffects' => false],
	'datefmt_format' => ['hasSideEffects' => false],
	'datefmt_format_object' => ['hasSideEffects' => false],
	'datefmt_get_calendar' => ['hasSideEffects' => false],
	'datefmt_get_calendar_object' => ['hasSideEffects' => false],
	'datefmt_get_datetype' => ['hasSideEffects' => false],
	'datefmt_get_error_code' => ['hasSideEffects' => false],
	'datefmt_get_error_message' => ['hasSideEffects' => false],
	'datefmt_get_locale' => ['hasSideEffects' => false],
	'datefmt_get_pattern' => ['hasSideEffects' => false],
	'datefmt_get_timetype' => ['hasSideEffects' => false],
	'datefmt_get_timezone' => ['hasSideEffects' => false],
	'datefmt_get_timezone_id' => ['hasSideEffects' => false],
	'datefmt_is_lenient' => ['hasSideEffects' => false],
	'dcngettext' => ['hasSideEffects' => false],
	'decbin' => ['hasSideEffects' => false],
	'dechex' => ['hasSideEffects' => false],
	'decoct' => ['hasSideEffects' => false],
	'defined' => ['hasSideEffects' => false],
	'deflate_init' => ['hasSideEffects' => false],
	'deg2rad' => ['hasSideEffects' => false],
	'dirname' => ['hasSideEffects' => false],
	'disk_free_space' => ['hasSideEffects' => false],
	'disk_total_space' => ['hasSideEffects' => false],
	'diskfreespace' => ['hasSideEffects' => false],
	'dngettext' => ['hasSideEffects' => false],
	'doubleval' => ['hasSideEffects' => false],
	'error_get_last' => ['hasSideEffects' => false],
	'error_log' => ['hasSideEffects' => true],
	'escapeshellarg' => ['hasSideEffects' => false],
	'escapeshellcmd' => ['hasSideEffects' => false],
	'exp' => ['hasSideEffects' => false],
	'explode' => ['hasSideEffects' => false],
	'expm1' => ['hasSideEffects' => false],
	'extension_loaded' => ['hasSideEffects' => false],
	'fclose' => ['hasSideEffects' => true],
	'fdiv' => ['hasSideEffects' => false],
	'feof' => ['hasSideEffects' => false],
	'fflush' => ['hasSideEffects' => true],
	'fgetc' => ['hasSideEffects' => true],
	'fgetcsv' => ['hasSideEffects' => true],
	'fgets' => ['hasSideEffects' => true],
	'fgetss' => ['hasSideEffects' => true],
	'file' => ['hasSideEffects' => false],
	'file_exists' => ['hasSideEffects' => false],
	'file_get_contents' => ['hasSideEffects' => true],
	'file_put_contents' => ['hasSideEffects' => true],
	'fileatime' => ['hasSideEffects' => false],
	'filectime' => ['hasSideEffects' => false],
	'filegroup' => ['hasSideEffects' => false],
	'fileinode' => ['hasSideEffects' => false],
	'filemtime' => ['hasSideEffects' => false],
	'fileowner' => ['hasSideEffects' => false],
	'fileperms' => ['hasSideEffects' => false],
	'filesize' => ['hasSideEffects' => false],
	'filetype' => ['hasSideEffects' => false],
	'filter_has_var' => ['hasSideEffects' => false],
	'filter_id' => ['hasSideEffects' => false],
	'filter_input' => ['hasSideEffects' => false],
	'filter_input_array' => ['hasSideEffects' => false],
	'filter_list' => ['hasSideEffects' => false],
	'filter_var' => ['hasSideEffects' => false],
	'filter_var_array' => ['hasSideEffects' => false],
	'finfo::buffer' => ['hasSideEffects' => false],
	'finfo::file' => ['hasSideEffects' => false],
	'floatval' => ['hasSideEffects' => false],
	'flock' => ['hasSideEffects' => true],
	'floor' => ['hasSideEffects' => false],
	'fmod' => ['hasSideEffects' => false],
	'fnmatch' => ['hasSideEffects' => false],
	'fopen' => ['hasSideEffects' => true],
	'fpassthru' => ['hasSideEffects' => true],
	'fputcsv' => ['hasSideEffects' => true],
	'fputs' => ['hasSideEffects' => true],
	'fread' => ['hasSideEffects' => true],
	'fscanf' => ['hasSideEffects' => true],
	'fseek' => ['hasSideEffects' => true],
	'fstat' => ['hasSideEffects' => false],
	'ftell' => ['hasSideEffects' => false],
	'ftok' => ['hasSideEffects' => false],
	'ftruncate' => ['hasSideEffects' => true],
	'func_get_arg' => ['hasSideEffects' => false],
	'func_get_args' => ['hasSideEffects' => false],
	'func_num_args' => ['hasSideEffects' => false],
	'function_exists' => ['hasSideEffects' => false],
	'fwrite' => ['hasSideEffects' => true],
	'gc_enabled' => ['hasSideEffects' => false],
	'gc_status' => ['hasSideEffects' => false],
	'gd_info' => ['hasSideEffects' => false],
	'geoip_continent_code_by_name' => ['hasSideEffects' => false],
	'geoip_country_code3_by_name' => ['hasSideEffects' => false],
	'geoip_country_code_by_name' => ['hasSideEffects' => false],
	'geoip_country_name_by_name' => ['hasSideEffects' => false],
	'geoip_database_info' => ['hasSideEffects' => false],
	'geoip_db_avail' => ['hasSideEffects' => false],
	'geoip_db_filename' => ['hasSideEffects' => false],
	'geoip_db_get_all_info' => ['hasSideEffects' => false],
	'geoip_id_by_name' => ['hasSideEffects' => false],
	'geoip_isp_by_name' => ['hasSideEffects' => false],
	'geoip_org_by_name' => ['hasSideEffects' => false],
	'geoip_record_by_name' => ['hasSideEffects' => false],
	'geoip_region_by_name' => ['hasSideEffects' => false],
	'geoip_region_name_by_code' => ['hasSideEffects' => false],
	'geoip_time_zone_by_country_and_region' => ['hasSideEffects' => false],
	'get_browser' => ['hasSideEffects' => false],
	'get_called_class' => ['hasSideEffects' => false],
	'get_cfg_var' => ['hasSideEffects' => false],
	'get_class' => ['hasSideEffects' => false],
	'get_class_methods' => ['hasSideEffects' => false],
	'get_class_vars' => ['hasSideEffects' => false],
	'get_current_user' => ['hasSideEffects' => false],
	'get_debug_type' => ['hasSideEffects' => false],
	'get_declared_classes' => ['hasSideEffects' => false],
	'get_declared_interfaces' => ['hasSideEffects' => false],
	'get_declared_traits' => ['hasSideEffects' => false],
	'get_defined_constants' => ['hasSideEffects' => false],
	'get_defined_functions' => ['hasSideEffects' => false],
	'get_defined_vars' => ['hasSideEffects' => false],
	'get_extension_funcs' => ['hasSideEffects' => false],
	'get_headers' => ['hasSideEffects' => false],
	'get_html_translation_table' => ['hasSideEffects' => false],
	'get_include_path' => ['hasSideEffects' => false],
	'get_included_files' => ['hasSideEffects' => false],
	'get_loaded_extensions' => ['hasSideEffects' => false],
	'get_meta_tags' => ['hasSideEffects' => false],
	'get_object_vars' => ['hasSideEffects' => false],
	'get_parent_class' => ['hasSideEffects' => false],
	'get_required_files' => ['hasSideEffects' => false],
	'get_resource_id' => ['hasSideEffects' => false],
	'get_resources' => ['hasSideEffects' => false],
	'getallheaders' => ['hasSideEffects' => false],
	'getcwd' => ['hasSideEffects' => false],
	'getdate' => ['hasSideEffects' => false],
	'getenv' => ['hasSideEffects' => false],
	'gethostbyaddr' => ['hasSideEffects' => false],
	'gethostbyname' => ['hasSideEffects' => false],
	'gethostbynamel' => ['hasSideEffects' => false],
	'gethostname' => ['hasSideEffects' => false],
	'getlastmod' => ['hasSideEffects' => false],
	'getmygid' => ['hasSideEffects' => false],
	'getmyinode' => ['hasSideEffects' => false],
	'getmypid' => ['hasSideEffects' => false],
	'getmyuid' => ['hasSideEffects' => false],
	'getprotobyname' => ['hasSideEffects' => false],
	'getprotobynumber' => ['hasSideEffects' => false],
	'getrandmax' => ['hasSideEffects' => false],
	'getrusage' => ['hasSideEffects' => false],
	'getservbyname' => ['hasSideEffects' => false],
	'getservbyport' => ['hasSideEffects' => false],
	'gettext' => ['hasSideEffects' => false],
	'gettimeofday' => ['hasSideEffects' => false],
	'gettype' => ['hasSideEffects' => false],
	'glob' => ['hasSideEffects' => false],
	'gmdate' => ['hasSideEffects' => false],
	'gmmktime' => ['hasSideEffects' => false],
	'gmp_abs' => ['hasSideEffects' => false],
	'gmp_add' => ['hasSideEffects' => false],
	'gmp_and' => ['hasSideEffects' => false],
	'gmp_binomial' => ['hasSideEffects' => false],
	'gmp_cmp' => ['hasSideEffects' => false],
	'gmp_com' => ['hasSideEffects' => false],
	'gmp_div' => ['hasSideEffects' => false],
	'gmp_div_q' => ['hasSideEffects' => false],
	'gmp_div_qr' => ['hasSideEffects' => false],
	'gmp_div_r' => ['hasSideEffects' => false],
	'gmp_divexact' => ['hasSideEffects' => false],
	'gmp_export' => ['hasSideEffects' => false],
	'gmp_fact' => ['hasSideEffects' => false],
	'gmp_gcd' => ['hasSideEffects' => false],
	'gmp_gcdext' => ['hasSideEffects' => false],
	'gmp_hamdist' => ['hasSideEffects' => false],
	'gmp_import' => ['hasSideEffects' => false],
	'gmp_init' => ['hasSideEffects' => false],
	'gmp_intval' => ['hasSideEffects' => false],
	'gmp_invert' => ['hasSideEffects' => false],
	'gmp_jacobi' => ['hasSideEffects' => false],
	'gmp_kronecker' => ['hasSideEffects' => false],
	'gmp_lcm' => ['hasSideEffects' => false],
	'gmp_legendre' => ['hasSideEffects' => false],
	'gmp_mod' => ['hasSideEffects' => false],
	'gmp_mul' => ['hasSideEffects' => false],
	'gmp_neg' => ['hasSideEffects' => false],
	'gmp_nextprime' => ['hasSideEffects' => false],
	'gmp_or' => ['hasSideEffects' => false],
	'gmp_perfect_power' => ['hasSideEffects' => false],
	'gmp_perfect_square' => ['hasSideEffects' => false],
	'gmp_popcount' => ['hasSideEffects' => false],
	'gmp_pow' => ['hasSideEffects' => false],
	'gmp_powm' => ['hasSideEffects' => false],
	'gmp_prob_prime' => ['hasSideEffects' => false],
	'gmp_root' => ['hasSideEffects' => false],
	'gmp_rootrem' => ['hasSideEffects' => false],
	'gmp_scan0' => ['hasSideEffects' => false],
	'gmp_scan1' => ['hasSideEffects' => false],
	'gmp_sign' => ['hasSideEffects' => false],
	'gmp_sqrt' => ['hasSideEffects' => false],
	'gmp_sqrtrem' => ['hasSideEffects' => false],
	'gmp_strval' => ['hasSideEffects' => false],
	'gmp_sub' => ['hasSideEffects' => false],
	'gmp_testbit' => ['hasSideEffects' => false],
	'gmp_xor' => ['hasSideEffects' => false],
	'grapheme_stripos' => ['hasSideEffects' => false],
	'grapheme_stristr' => ['hasSideEffects' => false],
	'grapheme_strlen' => ['hasSideEffects' => false],
	'grapheme_strpos' => ['hasSideEffects' => false],
	'grapheme_strripos' => ['hasSideEffects' => false],
	'grapheme_strrpos' => ['hasSideEffects' => false],
	'grapheme_strstr' => ['hasSideEffects' => false],
	'grapheme_substr' => ['hasSideEffects' => false],
	'gzcompress' => ['hasSideEffects' => false],
	'gzdecode' => ['hasSideEffects' => false],
	'gzdeflate' => ['hasSideEffects' => false],
	'gzencode' => ['hasSideEffects' => false],
	'gzinflate' => ['hasSideEffects' => false],
	'gzuncompress' => ['hasSideEffects' => false],
	'hash' => ['hasSideEffects' => false],
	'hash_algos' => ['hasSideEffects' => false],
	'hash_copy' => ['hasSideEffects' => false],
	'hash_equals' => ['hasSideEffects' => false],
	'hash_file' => ['hasSideEffects' => false],
	'hash_hkdf' => ['hasSideEffects' => false],
	'hash_hmac' => ['hasSideEffects' => false],
	'hash_hmac_algos' => ['hasSideEffects' => false],
	'hash_hmac_file' => ['hasSideEffects' => false],
	'hash_init' => ['hasSideEffects' => false],
	'hash_pbkdf2' => ['hasSideEffects' => false],
	'headers_list' => ['hasSideEffects' => false],
	'hebrev' => ['hasSideEffects' => false],
	'hexdec' => ['hasSideEffects' => false],
	'hrtime' => ['hasSideEffects' => false],
	'html_entity_decode' => ['hasSideEffects' => false],
	'htmlentities' => ['hasSideEffects' => false],
	'htmlspecialchars' => ['hasSideEffects' => false],
	'htmlspecialchars_decode' => ['hasSideEffects' => false],
	'http_build_cookie' => ['hasSideEffects' => false],
	'http_build_query' => ['hasSideEffects' => false],
	'http_build_str' => ['hasSideEffects' => false],
	'http_cache_etag' => ['hasSideEffects' => false],
	'http_cache_last_modified' => ['hasSideEffects' => false],
	'http_chunked_decode' => ['hasSideEffects' => false],
	'http_date' => ['hasSideEffects' => false],
	'http_deflate' => ['hasSideEffects' => false],
	'http_get_request_body' => ['hasSideEffects' => false],
	'http_get_request_body_stream' => ['hasSideEffects' => false],
	'http_get_request_headers' => ['hasSideEffects' => false],
	'http_inflate' => ['hasSideEffects' => false],
	'http_match_etag' => ['hasSideEffects' => false],
	'http_match_modified' => ['hasSideEffects' => false],
	'http_match_request_header' => ['hasSideEffects' => false],
	'http_parse_cookie' => ['hasSideEffects' => false],
	'http_parse_headers' => ['hasSideEffects' => false],
	'http_parse_message' => ['hasSideEffects' => false],
	'http_parse_params' => ['hasSideEffects' => false],
	'http_request_body_encode' => ['hasSideEffects' => false],
	'http_request_method_exists' => ['hasSideEffects' => false],
	'http_request_method_name' => ['hasSideEffects' => false],
	'http_support' => ['hasSideEffects' => false],
	'hypot' => ['hasSideEffects' => false],
	'iconv' => ['hasSideEffects' => false],
	'iconv_get_encoding' => ['hasSideEffects' => false],
	'iconv_mime_decode' => ['hasSideEffects' => false],
	'iconv_mime_decode_headers' => ['hasSideEffects' => false],
	'iconv_mime_encode' => ['hasSideEffects' => false],
	'iconv_strlen' => ['hasSideEffects' => false],
	'iconv_strpos' => ['hasSideEffects' => false],
	'iconv_strrpos' => ['hasSideEffects' => false],
	'iconv_substr' => ['hasSideEffects' => false],
	'idate' => ['hasSideEffects' => false],
	'image_type_to_extension' => ['hasSideEffects' => false],
	'image_type_to_mime_type' => ['hasSideEffects' => false],
	'imagecolorat' => ['hasSideEffects' => false],
	'imagecolorclosest' => ['hasSideEffects' => false],
	'imagecolorclosestalpha' => ['hasSideEffects' => false],
	'imagecolorclosesthwb' => ['hasSideEffects' => false],
	'imagecolorexact' => ['hasSideEffects' => false],
	'imagecolorexactalpha' => ['hasSideEffects' => false],
	'imagecolorresolve' => ['hasSideEffects' => false],
	'imagecolorresolvealpha' => ['hasSideEffects' => false],
	'imagecolorsforindex' => ['hasSideEffects' => false],
	'imagecolorstotal' => ['hasSideEffects' => false],
	'imagecreate' => ['hasSideEffects' => false],
	'imagecreatefromstring' => ['hasSideEffects' => false],
	'imagecreatetruecolor' => ['hasSideEffects' => false],
	'imagefontheight' => ['hasSideEffects' => false],
	'imagefontwidth' => ['hasSideEffects' => false],
	'imageftbbox' => ['hasSideEffects' => false],
	'imagegetinterpolation' => ['hasSideEffects' => false],
	'imagegrabscreen' => ['hasSideEffects' => false],
	'imagegrabwindow' => ['hasSideEffects' => false],
	'imageistruecolor' => ['hasSideEffects' => false],
	'imagesx' => ['hasSideEffects' => false],
	'imagesy' => ['hasSideEffects' => false],
	'imagettfbbox' => ['hasSideEffects' => false],
	'imagetypes' => ['hasSideEffects' => false],
	'implode' => ['hasSideEffects' => false],
	'in_array' => ['hasSideEffects' => false],
	'inet_ntop' => ['hasSideEffects' => false],
	'inet_pton' => ['hasSideEffects' => false],
	'inflate_get_read_len' => ['hasSideEffects' => false],
	'inflate_get_status' => ['hasSideEffects' => false],
	'inflate_init' => ['hasSideEffects' => false],
	'ini_get' => ['hasSideEffects' => false],
	'ini_get_all' => ['hasSideEffects' => false],
	'intcal_get_maximum' => ['hasSideEffects' => false],
	'intdiv' => ['hasSideEffects' => false],
	'intl_error_name' => ['hasSideEffects' => false],
	'intl_get' => ['hasSideEffects' => false],
	'intl_get_error_code' => ['hasSideEffects' => false],
	'intl_get_error_message' => ['hasSideEffects' => false],
	'intl_is_failure' => ['hasSideEffects' => false],
	'intlcal_after' => ['hasSideEffects' => false],
	'intlcal_before' => ['hasSideEffects' => false],
	'intlcal_create_instance' => ['hasSideEffects' => false],
	'intlcal_equals' => ['hasSideEffects' => false],
	'intlcal_field_difference' => ['hasSideEffects' => false],
	'intlcal_from_date_time' => ['hasSideEffects' => false],
	'intlcal_get' => ['hasSideEffects' => false],
	'intlcal_get_actual_maximum' => ['hasSideEffects' => false],
	'intlcal_get_actual_minimum' => ['hasSideEffects' => false],
	'intlcal_get_available_locales' => ['hasSideEffects' => false],
	'intlcal_get_day_of_week_type' => ['hasSideEffects' => false],
	'intlcal_get_error_code' => ['hasSideEffects' => false],
	'intlcal_get_error_message' => ['hasSideEffects' => false],
	'intlcal_get_first_day_of_week' => ['hasSideEffects' => false],
	'intlcal_get_greatest_minimum' => ['hasSideEffects' => false],
	'intlcal_get_keyword_values_for_locale' => ['hasSideEffects' => false],
	'intlcal_get_least_maximum' => ['hasSideEffects' => false],
	'intlcal_get_locale' => ['hasSideEffects' => false],
	'intlcal_get_maximum' => ['hasSideEffects' => false],
	'intlcal_get_minimal_days_in_first_week' => ['hasSideEffects' => false],
	'intlcal_get_minimum' => ['hasSideEffects' => false],
	'intlcal_get_now' => ['hasSideEffects' => false],
	'intlcal_get_repeated_wall_time_option' => ['hasSideEffects' => false],
	'intlcal_get_skipped_wall_time_option' => ['hasSideEffects' => false],
	'intlcal_get_time' => ['hasSideEffects' => false],
	'intlcal_get_time_zone' => ['hasSideEffects' => false],
	'intlcal_get_type' => ['hasSideEffects' => false],
	'intlcal_get_weekend_transition' => ['hasSideEffects' => false],
	'intlcal_greates_minimum' => ['hasSideEffects' => false],
	'intlcal_in_daylight_time' => ['hasSideEffects' => false],
	'intlcal_is_equivalent_to' => ['hasSideEffects' => false],
	'intlcal_is_lenient' => ['hasSideEffects' => false],
	'intlcal_is_set' => ['hasSideEffects' => false],
	'intlcal_is_weekend' => ['hasSideEffects' => false],
	'intlcal_to_date_time' => ['hasSideEffects' => false],
	'intlgregcal_create_instance' => ['hasSideEffects' => false],
	'intlgregcal_get_gregorian_change' => ['hasSideEffects' => false],
	'intlgregcal_is_leap_year' => ['hasSideEffects' => false],
	'intltz_count_equivalent_ids' => ['hasSideEffects' => false],
	'intltz_create_default' => ['hasSideEffects' => false],
	'intltz_create_enumeration' => ['hasSideEffects' => false],
	'intltz_create_time_zone' => ['hasSideEffects' => false],
	'intltz_create_time_zone_id_enumeration' => ['hasSideEffects' => false],
	'intltz_from_date_time_zone' => ['hasSideEffects' => false],
	'intltz_get_canonical_id' => ['hasSideEffects' => false],
	'intltz_get_display_name' => ['hasSideEffects' => false],
	'intltz_get_dst_savings' => ['hasSideEffects' => false],
	'intltz_get_equivalent_id' => ['hasSideEffects' => false],
	'intltz_get_error_code' => ['hasSideEffects' => false],
	'intltz_get_error_message' => ['hasSideEffects' => false],
	'intltz_get_gmt' => ['hasSideEffects' => false],
	'intltz_get_id' => ['hasSideEffects' => false],
	'intltz_get_offset' => ['hasSideEffects' => false],
	'intltz_get_raw_offset' => ['hasSideEffects' => false],
	'intltz_get_region' => ['hasSideEffects' => false],
	'intltz_get_tz_data_version' => ['hasSideEffects' => false],
	'intltz_get_unknown' => ['hasSideEffects' => false],
	'intltz_getgmt' => ['hasSideEffects' => false],
	'intltz_has_same_rules' => ['hasSideEffects' => false],
	'intltz_to_date_time_zone' => ['hasSideEffects' => false],
	'intltz_use_daylight_time' => ['hasSideEffects' => false],
	'intlz_create_default' => ['hasSideEffects' => false],
	'intval' => ['hasSideEffects' => false],
	'ip2long' => ['hasSideEffects' => false],
	'iptcparse' => ['hasSideEffects' => false],
	'is_a' => ['hasSideEffects' => false],
	'is_array' => ['hasSideEffects' => false],
	'is_bool' => ['hasSideEffects' => false],
	'is_countable' => ['hasSideEffects' => false],
	'is_dir' => ['hasSideEffects' => false],
	'is_double' => ['hasSideEffects' => false],
	'is_executable' => ['hasSideEffects' => false],
	'is_file' => ['hasSideEffects' => false],
	'is_finite' => ['hasSideEffects' => false],
	'is_float' => ['hasSideEffects' => false],
	'is_infinite' => ['hasSideEffects' => false],
	'is_int' => ['hasSideEffects' => false],
	'is_integer' => ['hasSideEffects' => false],
	'is_iterable' => ['hasSideEffects' => false],
	'is_link' => ['hasSideEffects' => false],
	'is_long' => ['hasSideEffects' => false],
	'is_nan' => ['hasSideEffects' => false],
	'is_null' => ['hasSideEffects' => false],
	'is_numeric' => ['hasSideEffects' => false],
	'is_object' => ['hasSideEffects' => false],
	'is_readable' => ['hasSideEffects' => false],
	'is_real' => ['hasSideEffects' => false],
	'is_resource' => ['hasSideEffects' => false],
	'is_scalar' => ['hasSideEffects' => false],
	'is_string' => ['hasSideEffects' => false],
	'is_subclass_of' => ['hasSideEffects' => false],
	'is_uploaded_file' => ['hasSideEffects' => false],
	'is_writable' => ['hasSideEffects' => false],
	'is_writeable' => ['hasSideEffects' => false],
	'iterator_count' => ['hasSideEffects' => false],
	'join' => ['hasSideEffects' => false],
	'json_last_error' => ['hasSideEffects' => false],
	'json_last_error_msg' => ['hasSideEffects' => false],
	'json_validate' => ['hasSideEffects' => false],
	'key' => ['hasSideEffects' => false],
	'key_exists' => ['hasSideEffects' => false],
	'lcfirst' => ['hasSideEffects' => false],
	'lchgrp' => ['hasSideEffects' => true],
	'lchown' => ['hasSideEffects' => true],
	'libxml_get_errors' => ['hasSideEffects' => false],
	'libxml_get_last_error' => ['hasSideEffects' => false],
	'link' => ['hasSideEffects' => true],
	'linkinfo' => ['hasSideEffects' => false],
	'locale_accept_from_http' => ['hasSideEffects' => false],
	'locale_canonicalize' => ['hasSideEffects' => false],
	'locale_compose' => ['hasSideEffects' => false],
	'locale_filter_matches' => ['hasSideEffects' => false],
	'locale_get_all_variants' => ['hasSideEffects' => false],
	'locale_get_default' => ['hasSideEffects' => false],
	'locale_get_display_language' => ['hasSideEffects' => false],
	'locale_get_display_name' => ['hasSideEffects' => false],
	'locale_get_display_region' => ['hasSideEffects' => false],
	'locale_get_display_script' => ['hasSideEffects' => false],
	'locale_get_display_variant' => ['hasSideEffects' => false],
	'locale_get_keywords' => ['hasSideEffects' => false],
	'locale_get_primary_language' => ['hasSideEffects' => false],
	'locale_get_region' => ['hasSideEffects' => false],
	'locale_get_script' => ['hasSideEffects' => false],
	'locale_lookup' => ['hasSideEffects' => false],
	'locale_parse' => ['hasSideEffects' => false],
	'localeconv' => ['hasSideEffects' => false],
	'localtime' => ['hasSideEffects' => false],
	'log' => ['hasSideEffects' => false],
	'log10' => ['hasSideEffects' => false],
	'log1p' => ['hasSideEffects' => false],
	'long2ip' => ['hasSideEffects' => false],
	'lstat' => ['hasSideEffects' => false],
	'ltrim' => ['hasSideEffects' => false],
	'max' => ['hasSideEffects' => false],
	'mb_check_encoding' => ['hasSideEffects' => false],
	'mb_chr' => ['hasSideEffects' => false],
	'mb_convert_case' => ['hasSideEffects' => false],
	'mb_convert_encoding' => ['hasSideEffects' => false],
	'mb_convert_kana' => ['hasSideEffects' => false],
	'mb_decode_mimeheader' => ['hasSideEffects' => false],
	'mb_decode_numericentity' => ['hasSideEffects' => false],
	'mb_detect_encoding' => ['hasSideEffects' => false],
	'mb_encode_mimeheader' => ['hasSideEffects' => false],
	'mb_encode_numericentity' => ['hasSideEffects' => false],
	'mb_encoding_aliases' => ['hasSideEffects' => false],
	'mb_ereg_match' => ['hasSideEffects' => false],
	'mb_ereg_replace' => ['hasSideEffects' => false],
	'mb_ereg_search' => ['hasSideEffects' => false],
	'mb_ereg_search_getpos' => ['hasSideEffects' => false],
	'mb_ereg_search_getregs' => ['hasSideEffects' => false],
	'mb_ereg_search_pos' => ['hasSideEffects' => false],
	'mb_ereg_search_regs' => ['hasSideEffects' => false],
	'mb_ereg_search_setpos' => ['hasSideEffects' => false],
	'mb_eregi_replace' => ['hasSideEffects' => false],
	'mb_get_info' => ['hasSideEffects' => false],
	'mb_http_input' => ['hasSideEffects' => false],
	'mb_list_encodings' => ['hasSideEffects' => false],
	'mb_ord' => ['hasSideEffects' => false],
	'mb_output_handler' => ['hasSideEffects' => false],
	'mb_preferred_mime_name' => ['hasSideEffects' => false],
	'mb_scrub' => ['hasSideEffects' => false],
	'mb_split' => ['hasSideEffects' => false],
	'mb_str_pad' => ['hasSideEffects' => false],
	'mb_str_split' => ['hasSideEffects' => false],
	'mb_strcut' => ['hasSideEffects' => false],
	'mb_strimwidth' => ['hasSideEffects' => false],
	'mb_stripos' => ['hasSideEffects' => false],
	'mb_stristr' => ['hasSideEffects' => false],
	'mb_strlen' => ['hasSideEffects' => false],
	'mb_strpos' => ['hasSideEffects' => false],
	'mb_strrchr' => ['hasSideEffects' => false],
	'mb_strrichr' => ['hasSideEffects' => false],
	'mb_strripos' => ['hasSideEffects' => false],
	'mb_strrpos' => ['hasSideEffects' => false],
	'mb_strstr' => ['hasSideEffects' => false],
	'mb_strtolower' => ['hasSideEffects' => false],
	'mb_strtoupper' => ['hasSideEffects' => false],
	'mb_strwidth' => ['hasSideEffects' => false],
	'mb_substr' => ['hasSideEffects' => false],
	'mb_substr_count' => ['hasSideEffects' => false],
	'mbereg_search_setpos' => ['hasSideEffects' => false],
	'md5' => ['hasSideEffects' => false],
	'md5_file' => ['hasSideEffects' => false],
	'memory_get_peak_usage' => ['hasSideEffects' => false],
	'memory_get_usage' => ['hasSideEffects' => false],
	'metaphone' => ['hasSideEffects' => false],
	'method_exists' => ['hasSideEffects' => false],
	'mhash' => ['hasSideEffects' => false],
	'mhash_count' => ['hasSideEffects' => false],
	'mhash_get_block_size' => ['hasSideEffects' => false],
	'mhash_get_hash_name' => ['hasSideEffects' => false],
	'mhash_keygen_s2k' => ['hasSideEffects' => false],
	'microtime' => ['hasSideEffects' => false],
	'min' => ['hasSideEffects' => false],
	'mkdir' => ['hasSideEffects' => true],
	'mktime' => ['hasSideEffects' => false],
	'move_uploaded_file' => ['hasSideEffects' => true],
	'msgfmt_create' => ['hasSideEffects' => false],
	'msgfmt_format' => ['hasSideEffects' => false],
	'msgfmt_format_message' => ['hasSideEffects' => false],
	'msgfmt_get_error_code' => ['hasSideEffects' => false],
	'msgfmt_get_error_message' => ['hasSideEffects' => false],
	'msgfmt_get_locale' => ['hasSideEffects' => false],
	'msgfmt_get_pattern' => ['hasSideEffects' => false],
	'msgfmt_parse' => ['hasSideEffects' => false],
	'msgfmt_parse_message' => ['hasSideEffects' => false],
	'mt_getrandmax' => ['hasSideEffects' => false],
	'mt_rand' => ['hasSideEffects' => true],
	'net_get_interfaces' => ['hasSideEffects' => false],
	'ngettext' => ['hasSideEffects' => false],
	'nl2br' => ['hasSideEffects' => false],
	'nl_langinfo' => ['hasSideEffects' => false],
	'normalizer_get_raw_decomposition' => ['hasSideEffects' => false],
	'normalizer_is_normalized' => ['hasSideEffects' => false],
	'normalizer_normalize' => ['hasSideEffects' => false],
	'number_format' => ['hasSideEffects' => false],
	'numfmt_create' => ['hasSideEffects' => false],
	'numfmt_format' => ['hasSideEffects' => false],
	'numfmt_format_currency' => ['hasSideEffects' => false],
	'numfmt_get_attribute' => ['hasSideEffects' => false],
	'numfmt_get_error_code' => ['hasSideEffects' => false],
	'numfmt_get_error_message' => ['hasSideEffects' => false],
	'numfmt_get_locale' => ['hasSideEffects' => false],
	'numfmt_get_pattern' => ['hasSideEffects' => false],
	'numfmt_get_symbol' => ['hasSideEffects' => false],
	'numfmt_get_text_attribute' => ['hasSideEffects' => false],
	'numfmt_parse' => ['hasSideEffects' => false],
	'ob_etaghandler' => ['hasSideEffects' => false],
	'ob_get_contents' => ['hasSideEffects' => false],
	'ob_iconv_handler' => ['hasSideEffects' => false],
	'octdec' => ['hasSideEffects' => false],
	'ord' => ['hasSideEffects' => false],
	'pack' => ['hasSideEffects' => false],
	'pam_auth' => ['hasSideEffects' => false],
	'pam_chpass' => ['hasSideEffects' => false],
	'parse_ini_file' => ['hasSideEffects' => false],
	'parse_ini_string' => ['hasSideEffects' => false],
	'parse_url' => ['hasSideEffects' => false],
	'pathinfo' => ['hasSideEffects' => false],
	'pclose' => ['hasSideEffects' => true],
	'pcntl_errno' => ['hasSideEffects' => false],
	'pcntl_get_last_error' => ['hasSideEffects' => false],
	'pcntl_getpriority' => ['hasSideEffects' => false],
	'pcntl_strerror' => ['hasSideEffects' => false],
	'pcntl_wexitstatus' => ['hasSideEffects' => false],
	'pcntl_wifcontinued' => ['hasSideEffects' => false],
	'pcntl_wifexited' => ['hasSideEffects' => false],
	'pcntl_wifsignaled' => ['hasSideEffects' => false],
	'pcntl_wifstopped' => ['hasSideEffects' => false],
	'pcntl_wstopsig' => ['hasSideEffects' => false],
	'pcntl_wtermsig' => ['hasSideEffects' => false],
	'pdo_drivers' => ['hasSideEffects' => false],
	'php_ini_loaded_file' => ['hasSideEffects' => false],
	'php_ini_scanned_files' => ['hasSideEffects' => false],
	'php_logo_guid' => ['hasSideEffects' => false],
	'php_sapi_name' => ['hasSideEffects' => false],
	'php_strip_whitespace' => ['hasSideEffects' => false],
	'php_uname' => ['hasSideEffects' => false],
	'phpversion' => ['hasSideEffects' => false],
	'pi' => ['hasSideEffects' => false],
	'popen' => ['hasSideEffects' => true],
	'pos' => ['hasSideEffects' => false],
	'posix_ctermid' => ['hasSideEffects' => false],
	'posix_errno' => ['hasSideEffects' => false],
	'posix_get_last_error' => ['hasSideEffects' => false],
	'posix_getcwd' => ['hasSideEffects' => false],
	'posix_getegid' => ['hasSideEffects' => false],
	'posix_geteuid' => ['hasSideEffects' => false],
	'posix_getgid' => ['hasSideEffects' => false],
	'posix_getgrgid' => ['hasSideEffects' => false],
	'posix_getgrnam' => ['hasSideEffects' => false],
	'posix_getgroups' => ['hasSideEffects' => false],
	'posix_getlogin' => ['hasSideEffects' => false],
	'posix_getpgid' => ['hasSideEffects' => false],
	'posix_getpgrp' => ['hasSideEffects' => false],
	'posix_getpid' => ['hasSideEffects' => false],
	'posix_getppid' => ['hasSideEffects' => false],
	'posix_getpwnam' => ['hasSideEffects' => false],
	'posix_getpwuid' => ['hasSideEffects' => false],
	'posix_getrlimit' => ['hasSideEffects' => false],
	'posix_getsid' => ['hasSideEffects' => false],
	'posix_getuid' => ['hasSideEffects' => false],
	'posix_initgroups' => ['hasSideEffects' => false],
	'posix_isatty' => ['hasSideEffects' => false],
	'posix_strerror' => ['hasSideEffects' => false],
	'posix_times' => ['hasSideEffects' => false],
	'posix_ttyname' => ['hasSideEffects' => false],
	'posix_uname' => ['hasSideEffects' => false],
	'pow' => ['hasSideEffects' => false],
	'preg_grep' => ['hasSideEffects' => false],
	'preg_last_error' => ['hasSideEffects' => false],
	'preg_last_error_msg' => ['hasSideEffects' => false],
	'preg_quote' => ['hasSideEffects' => false],
	'preg_split' => ['hasSideEffects' => false],
	'property_exists' => ['hasSideEffects' => false],
	'quoted_printable_decode' => ['hasSideEffects' => false],
	'quoted_printable_encode' => ['hasSideEffects' => false],
	'quotemeta' => ['hasSideEffects' => false],
	'rad2deg' => ['hasSideEffects' => false],
	'rand' => ['hasSideEffects' => true],
	'random_bytes' => ['hasSideEffects' => true],
	'random_int' => ['hasSideEffects' => true],
	'range' => ['hasSideEffects' => false],
	'rawurldecode' => ['hasSideEffects' => false],
	'rawurlencode' => ['hasSideEffects' => false],
	'readfile' => ['hasSideEffects' => true],
	'readlink' => ['hasSideEffects' => false],
	'realpath' => ['hasSideEffects' => false],
	'realpath_cache_get' => ['hasSideEffects' => false],
	'realpath_cache_size' => ['hasSideEffects' => false],
	'rename' => ['hasSideEffects' => true],
	'resourcebundle_count' => ['hasSideEffects' => false],
	'resourcebundle_create' => ['hasSideEffects' => false],
	'resourcebundle_get' => ['hasSideEffects' => false],
	'resourcebundle_get_error_code' => ['hasSideEffects' => false],
	'resourcebundle_get_error_message' => ['hasSideEffects' => false],
	'resourcebundle_locales' => ['hasSideEffects' => false],
	'rewind' => ['hasSideEffects' => true],
	'rmdir' => ['hasSideEffects' => true],
	'round' => ['hasSideEffects' => false],
	'rtrim' => ['hasSideEffects' => false],
	'sha1' => ['hasSideEffects' => false],
	'sha1_file' => ['hasSideEffects' => false],
	'sin' => ['hasSideEffects' => false],
	'sinh' => ['hasSideEffects' => false],
	'sizeof' => ['hasSideEffects' => false],
	'soundex' => ['hasSideEffects' => false],
	'spl_classes' => ['hasSideEffects' => false],
	'spl_object_hash' => ['hasSideEffects' => false],
	'sprintf' => ['hasSideEffects' => false],
	'sqrt' => ['hasSideEffects' => false],
	'stat' => ['hasSideEffects' => false],
	'str_contains' => ['hasSideEffects' => false],
	'str_decrement' => ['hasSideEffects' => false],
	'str_ends_with' => ['hasSideEffects' => false],
	'str_getcsv' => ['hasSideEffects' => false],
	'str_increment' => ['hasSideEffects' => false],
	'str_pad' => ['hasSideEffects' => false],
	'str_repeat' => ['hasSideEffects' => false],
	'str_rot13' => ['hasSideEffects' => false],
	'str_split' => ['hasSideEffects' => false],
	'str_starts_with' => ['hasSideEffects' => false],
	'str_word_count' => ['hasSideEffects' => false],
	'strcasecmp' => ['hasSideEffects' => false],
	'strchr' => ['hasSideEffects' => false],
	'strcmp' => ['hasSideEffects' => false],
	'strcoll' => ['hasSideEffects' => false],
	'strcspn' => ['hasSideEffects' => false],
	'stream_get_filters' => ['hasSideEffects' => false],
	'stream_get_transports' => ['hasSideEffects' => false],
	'stream_get_wrappers' => ['hasSideEffects' => false],
	'stream_is_local' => ['hasSideEffects' => false],
	'stream_isatty' => ['hasSideEffects' => false],
	'strip_tags' => ['hasSideEffects' => false],
	'stripcslashes' => ['hasSideEffects' => false],
	'stripos' => ['hasSideEffects' => false],
	'stripslashes' => ['hasSideEffects' => false],
	'stristr' => ['hasSideEffects' => false],
	'strlen' => ['hasSideEffects' => false],
	'strnatcasecmp' => ['hasSideEffects' => false],
	'strnatcmp' => ['hasSideEffects' => false],
	'strncasecmp' => ['hasSideEffects' => false],
	'strncmp' => ['hasSideEffects' => false],
	'strpbrk' => ['hasSideEffects' => false],
	'strpos' => ['hasSideEffects' => false],
	'strptime' => ['hasSideEffects' => false],
	'strrchr' => ['hasSideEffects' => false],
	'strrev' => ['hasSideEffects' => false],
	'strripos' => ['hasSideEffects' => false],
	'strrpos' => ['hasSideEffects' => false],
	'strspn' => ['hasSideEffects' => false],
	'strstr' => ['hasSideEffects' => false],
	'strtolower' => ['hasSideEffects' => false],
	'strtotime' => ['hasSideEffects' => false],
	'strtoupper' => ['hasSideEffects' => false],
	'strtr' => ['hasSideEffects' => false],
	'strval' => ['hasSideEffects' => false],
	'substr' => ['hasSideEffects' => false],
	'substr_compare' => ['hasSideEffects' => false],
	'substr_count' => ['hasSideEffects' => false],
	'substr_replace' => ['hasSideEffects' => false],
	'symlink' => ['hasSideEffects' => true],
	'sys_getloadavg' => ['hasSideEffects' => false],
	'tan' => ['hasSideEffects' => false],
	'tanh' => ['hasSideEffects' => false],
	'tempnam' => ['hasSideEffects' => true],
	'timezone_abbreviations_list' => ['hasSideEffects' => false],
	'timezone_identifiers_list' => ['hasSideEffects' => false],
	'timezone_location_get' => ['hasSideEffects' => false],
	'timezone_name_from_abbr' => ['hasSideEffects' => false],
	'timezone_name_get' => ['hasSideEffects' => false],
	'timezone_offset_get' => ['hasSideEffects' => false],
	'timezone_open' => ['hasSideEffects' => false],
	'timezone_transitions_get' => ['hasSideEffects' => false],
	'timezone_version_get' => ['hasSideEffects' => false],
	'tmpfile' => ['hasSideEffects' => true],
	'token_get_all' => ['hasSideEffects' => false],
	'token_name' => ['hasSideEffects' => false],
	'touch' => ['hasSideEffects' => true],
	'transliterator_create' => ['hasSideEffects' => false],
	'transliterator_create_from_rules' => ['hasSideEffects' => false],
	'transliterator_create_inverse' => ['hasSideEffects' => false],
	'transliterator_get_error_code' => ['hasSideEffects' => false],
	'transliterator_get_error_message' => ['hasSideEffects' => false],
	'transliterator_list_ids' => ['hasSideEffects' => false],
	'transliterator_transliterate' => ['hasSideEffects' => false],
	'trim' => ['hasSideEffects' => false],
	'ucfirst' => ['hasSideEffects' => false],
	'ucwords' => ['hasSideEffects' => false],
	'umask' => ['hasSideEffects' => true],
	'unlink' => ['hasSideEffects' => true],
	'unpack' => ['hasSideEffects' => false],
	'urldecode' => ['hasSideEffects' => false],
	'urlencode' => ['hasSideEffects' => false],
	'utf8_decode' => ['hasSideEffects' => false],
	'utf8_encode' => ['hasSideEffects' => false],
	'vsprintf' => ['hasSideEffects' => false],
	'wordwrap' => ['hasSideEffects' => false],
	'xml_error_string' => ['hasSideEffects' => false],
	'xml_get_current_byte_index' => ['hasSideEffects' => false],
	'xml_get_current_column_number' => ['hasSideEffects' => false],
	'xml_get_current_line_number' => ['hasSideEffects' => false],
	'xml_get_error_code' => ['hasSideEffects' => false],
	'xml_parser_create' => ['hasSideEffects' => false],
	'xml_parser_create_ns' => ['hasSideEffects' => false],
	'xml_parser_get_option' => ['hasSideEffects' => false],
	'zend_version' => ['hasSideEffects' => false],
	'zlib_decode' => ['hasSideEffects' => false],
	'zlib_encode' => ['hasSideEffects' => false],
	'zlib_get_coding_type' => ['hasSideEffects' => false],

];
