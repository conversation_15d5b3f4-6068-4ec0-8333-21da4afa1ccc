<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * إنشاء جدول أعضاء فريق المشروع
 * 
 * جدول الربط بين المشاريع والمستخدمين مع تحديد الأدوار والأسعار
 * 
 * @package Database\Migrations
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
return new class extends Migration
{
    /**
     * تشغيل الـ Migration
     * 
     * @return void
     */
    public function up(): void
    {
        Schema::create('project_team_members', function (Blueprint $table) {
            // المعرف الأساسي
            $table->id()->comment('معرف عضوية الفريق الفريد');
            
            // العلاقات الأساسية
            $table->foreignId('project_id')
                  ->constrained('projects')
                  ->onDelete('cascade')
                  ->comment('معرف المشروع');
                  
            $table->foreignId('user_id')
                  ->constrained('users')
                  ->onDelete('cascade')
                  ->comment('معرف المستخدم');
            
            // دور العضو في المشروع
            $table->enum('role', [
                'project_manager',
                'team_lead',
                'developer',
                'designer',
                'tester',
                'client'
            ])->comment('دور العضو في المشروع');
            
            // معلومات مالية
            $table->decimal('hourly_rate', 8, 2)->nullable()->comment('السعر بالساعة لهذا العضو في هذا المشروع');
            
            // تواريخ العضوية
            $table->timestamp('joined_at')->default(now())->comment('تاريخ انضمام العضو للمشروع');
            $table->timestamp('left_at')->nullable()->comment('تاريخ مغادرة العضو للمشروع');
            
            // حالة العضوية
            $table->boolean('is_active')->default(true)->comment('هل العضو نشط في المشروع');
            
            // صلاحيات خاصة
            $table->json('permissions')->nullable()->comment('صلاحيات خاصة للعضو في هذا المشروع');
            
            // بيانات إضافية
            $table->json('metadata')->nullable()->comment('بيانات إضافية مثل التخصص، المهارات، إلخ');
            
            // طوابع زمنية
            $table->timestamps();
            
            // فهرس فريد لمنع التكرار
            $table->unique(['project_id', 'user_id'], 'unique_project_user');
            
            // فهارس إضافية
            $table->index(['project_id', 'role'], 'idx_team_project_role');
            $table->index(['user_id', 'is_active'], 'idx_team_user_active');
            $table->index(['project_id', 'is_active'], 'idx_team_project_active');
            $table->index(['role', 'is_active'], 'idx_team_role_active');
            $table->index(['joined_at', 'left_at'], 'idx_team_membership_period');
            
            // قيود
            $table->check('hourly_rate IS NULL OR hourly_rate >= 0', 'chk_team_rate_positive');
            $table->check('left_at IS NULL OR left_at >= joined_at', 'chk_team_dates_order');
        });
        
        DB::statement("ALTER TABLE project_team_members COMMENT = 'جدول أعضاء فريق المشروع - ربط المستخدمين بالمشاريع مع الأدوار والصلاحيات'");
    }

    /**
     * التراجع عن الـ Migration
     * 
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('project_team_members');
    }
};
