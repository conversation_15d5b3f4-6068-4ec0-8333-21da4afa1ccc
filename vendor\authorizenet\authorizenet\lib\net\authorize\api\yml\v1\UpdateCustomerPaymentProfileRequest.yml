net\authorize\api\contract\v1\UpdateCustomerPaymentProfileRequest:
    xml_root_name: updateCustomerPaymentProfileRequest
    xml_root_namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
    properties:
        customerProfileId:
            expose: true
            access_type: public_method
            serialized_name: customerProfileId
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getCustomerProfileId
                setter: setCustomerProfileId
            type: string
        paymentProfile:
            expose: true
            access_type: public_method
            serialized_name: paymentProfile
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getPaymentProfile
                setter: setPaymentProfile
            type: net\authorize\api\contract\v1\CustomerPaymentProfileExType
        validationMode:
            expose: true
            access_type: public_method
            serialized_name: validationMode
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getValidationMode
                setter: setValidationMode
            type: string
