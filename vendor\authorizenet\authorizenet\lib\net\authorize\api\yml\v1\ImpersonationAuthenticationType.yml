net\authorize\api\contract\v1\ImpersonationAuthenticationType:
    properties:
        partnerLoginId:
            expose: true
            access_type: public_method
            serialized_name: partnerLoginId
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getPartnerLoginId
                setter: setPartnerLoginId
            type: string
        partnerTransactionKey:
            expose: true
            access_type: public_method
            serialized_name: partnerTransactionKey
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getPartnerTransactionKey
                setter: setPartnerTransactionKey
            type: string
