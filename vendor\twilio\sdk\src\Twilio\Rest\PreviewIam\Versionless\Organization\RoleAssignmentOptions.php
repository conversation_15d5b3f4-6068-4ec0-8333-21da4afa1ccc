<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Organization Public API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\PreviewIam\Versionless\Organization;

use Twilio\Options;
use Twilio\Values;

abstract class RoleAssignmentOptions
{


    /**
     * @param string $identity
     * @param string $scope
     * @return ReadRoleAssignmentOptions Options builder
     */
    public static function read(
        
        string $identity = Values::NONE,
        string $scope = Values::NONE

    ): ReadRoleAssignmentOptions
    {
        return new ReadRoleAssignmentOptions(
            $identity,
            $scope
        );
    }

}



class ReadRoleAssignmentOptions extends Options
    {
    /**
     * @param string $identity
     * @param string $scope
     */
    public function __construct(
        
        string $identity = Values::NONE,
        string $scope = Values::NONE

    ) {
        $this->options['identity'] = $identity;
        $this->options['scope'] = $scope;
    }

    /**
     * @param string $identity
     * @return $this Fluent Builder
     */
    public function setIdentity(string $identity): self
    {
        $this->options['identity'] = $identity;
        return $this;
    }

    /**
     * @param string $scope
     * @return $this Fluent Builder
     */
    public function setScope(string $scope): self
    {
        $this->options['scope'] = $scope;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.PreviewIam.Versionless.ReadRoleAssignmentOptions ' . $options . ']';
    }
}

