<?php

namespace App\Services\ProjectManagement;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use Exception;

/**
 * خدمة تحسين الأداء وإدارة قاعدة البيانات الكبيرة
 * 
 * هذه الخدمة مسؤولة عن تحسين أداء قاعدة البيانات مع البيانات الكبيرة
 * وإدارة الأرشفة والتخزين المؤقت للاستعلامات المعقدة
 * 
 * @package App\Services\ProjectManagement
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
class DatabaseOptimizationService
{
    /**
     * مدة التخزين المؤقت الافتراضية (بالدقائق)
     */
    const DEFAULT_CACHE_DURATION = 60;

    /**
     * حد الأرشفة الافتراضي (بالأشهر)
     */
    const DEFAULT_ARCHIVE_THRESHOLD = 12;

    /**
     * أرشفة المشاريع القديمة
     * 
     * @param int $monthsOld عدد الأشهر للاعتبار كقديم
     * @return array إحصائيات الأرشفة
     */
    public function archiveOldProjects(int $monthsOld = self::DEFAULT_ARCHIVE_THRESHOLD): array
    {
        try {
            DB::beginTransaction();

            $cutoffDate = Carbon::now()->subMonths($monthsOld);
            
            // البحث عن المشاريع المكتملة أو الملغية القديمة
            $projectsToArchive = DB::table('projects')
                ->whereIn('status', ['completed', 'cancelled'])
                ->where('updated_at', '<', $cutoffDate)
                ->whereNull('deleted_at')
                ->get();

            $archivedCount = 0;
            $errors = [];

            foreach ($projectsToArchive as $project) {
                try {
                    // نسخ المشروع إلى جدول الأرشيف
                    DB::table('projects_archive')->insert([
                        'original_id' => $project->id,
                        'name' => $project->name,
                        'slug' => $project->slug,
                        'description' => $project->description,
                        'type' => $project->type,
                        'status' => $project->status,
                        'priority' => $project->priority,
                        'budget' => $project->budget,
                        'spent_budget' => $project->spent_budget,
                        'start_date' => $project->start_date,
                        'end_date' => $project->end_date,
                        'actual_end_date' => $project->actual_end_date,
                        'progress_percentage' => $project->progress_percentage,
                        'client_id' => $project->client_id,
                        'manager_id' => $project->manager_id,
                        'created_by' => $project->created_by,
                        'settings' => $project->settings,
                        'archived_at' => now(),
                        'archived_by' => auth()->id() ?? 1,
                        'archive_reason' => "أرشفة تلقائية للمشاريع الأقدم من {$monthsOld} شهر",
                        'original_created_at' => $project->created_at,
                        'original_updated_at' => $project->updated_at,
                        'original_deleted_at' => $project->deleted_at,
                    ]);

                    // أرشفة المهام المرتبطة
                    $this->archiveProjectTasks($project->id);

                    // حذف المشروع من الجدول الأساسي (حذف ناعم)
                    DB::table('projects')->where('id', $project->id)->update([
                        'deleted_at' => now()
                    ]);

                    $archivedCount++;

                } catch (Exception $e) {
                    $errors[] = "خطأ في أرشفة المشروع {$project->id}: " . $e->getMessage();
                    Log::error("خطأ في أرشفة المشروع", [
                        'project_id' => $project->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            DB::commit();

            Log::info("تم أرشفة المشاريع القديمة", [
                'archived_count' => $archivedCount,
                'months_old' => $monthsOld,
                'errors_count' => count($errors)
            ]);

            return [
                'success' => true,
                'archived_count' => $archivedCount,
                'total_found' => $projectsToArchive->count(),
                'errors' => $errors,
                'cutoff_date' => $cutoffDate->toDateString()
            ];

        } catch (Exception $e) {
            DB::rollBack();
            Log::error("خطأ في عملية أرشفة المشاريع", ['error' => $e->getMessage()]);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'archived_count' => 0
            ];
        }
    }

    /**
     * أرشفة مهام مشروع معين
     * 
     * @param int $projectId معرف المشروع
     * @return int عدد المهام المؤرشفة
     */
    protected function archiveProjectTasks(int $projectId): int
    {
        $tasks = DB::table('tasks')
            ->where('project_id', $projectId)
            ->whereNull('deleted_at')
            ->get();

        $archivedCount = 0;

        foreach ($tasks as $task) {
            // نسخ المهمة إلى جدول الأرشيف
            DB::table('tasks_archive')->insert([
                'original_id' => $task->id,
                'original_project_id' => $task->project_id,
                'title' => $task->title,
                'description' => $task->description,
                'type' => $task->type,
                'status' => $task->status,
                'priority' => $task->priority,
                'parent_task_id' => $task->parent_task_id,
                'assigned_to' => $task->assigned_to,
                'estimated_hours' => $task->estimated_hours,
                'actual_hours' => $task->actual_hours,
                'start_date' => $task->start_date,
                'due_date' => $task->due_date,
                'completed_at' => $task->completed_at,
                'order_index' => $task->order_index,
                'metadata' => $task->metadata,
                'created_by' => $task->created_by,
                'archived_at' => now(),
                'archived_by' => auth()->id() ?? 1,
                'archive_reason' => 'أرشفة تلقائية مع المشروع',
                'original_created_at' => $task->created_at,
                'original_updated_at' => $task->updated_at,
            ]);

            // حذف المهمة من الجدول الأساسي
            DB::table('tasks')->where('id', $task->id)->update([
                'deleted_at' => now()
            ]);

            $archivedCount++;
        }

        return $archivedCount;
    }

    /**
     * أرشفة سجلات الوقت القديمة
     * 
     * @param int $monthsOld عدد الأشهر
     * @return array إحصائيات الأرشفة
     */
    public function archiveOldTimeEntries(int $monthsOld = 24): array
    {
        try {
            DB::beginTransaction();

            $cutoffDate = Carbon::now()->subMonths($monthsOld);
            
            // نقل السجلات القديمة إلى جدول الأرشيف
            $archivedCount = DB::statement("
                INSERT INTO time_entries_archive (
                    original_id, user_id, project_id, task_id, description,
                    hours, date, start_time, end_time, is_billable,
                    hourly_rate, total_cost, invoice_id, archived_at,
                    archive_reason, original_created_at
                )
                SELECT 
                    id, user_id, project_id, task_id, description,
                    hours, date, start_time, end_time, is_billable,
                    hourly_rate, total_cost, invoice_id, NOW(),
                    'أرشفة تلقائية للسجلات القديمة', created_at
                FROM time_entries 
                WHERE date < ? AND deleted_at IS NULL
            ", [$cutoffDate]);

            // حذف السجلات من الجدول الأساسي
            $deletedCount = DB::table('time_entries')
                ->where('date', '<', $cutoffDate)
                ->whereNull('deleted_at')
                ->update(['deleted_at' => now()]);

            DB::commit();

            Log::info("تم أرشفة سجلات الوقت القديمة", [
                'archived_count' => $deletedCount,
                'months_old' => $monthsOld,
                'cutoff_date' => $cutoffDate->toDateString()
            ]);

            return [
                'success' => true,
                'archived_count' => $deletedCount,
                'cutoff_date' => $cutoffDate->toDateString()
            ];

        } catch (Exception $e) {
            DB::rollBack();
            Log::error("خطأ في أرشفة سجلات الوقت", ['error' => $e->getMessage()]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * تحديث فهرس البحث
     * 
     * @param string|null $entityType نوع الكائن المحدد
     * @return array إحصائيات التحديث
     */
    public function updateSearchIndex(?string $entityType = null): array
    {
        try {
            $indexedCount = 0;
            $errors = [];

            // فهرسة المشاريع
            if (!$entityType || $entityType === 'projects') {
                $indexedCount += $this->indexProjects();
            }

            // فهرسة المهام
            if (!$entityType || $entityType === 'tasks') {
                $indexedCount += $this->indexTasks();
            }

            Log::info("تم تحديث فهرس البحث", [
                'indexed_count' => $indexedCount,
                'entity_type' => $entityType
            ]);

            return [
                'success' => true,
                'indexed_count' => $indexedCount,
                'entity_type' => $entityType
            ];

        } catch (Exception $e) {
            Log::error("خطأ في تحديث فهرس البحث", ['error' => $e->getMessage()]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * فهرسة المشاريع للبحث
     * 
     * @return int عدد المشاريع المفهرسة
     */
    protected function indexProjects(): int
    {
        $projects = DB::table('projects')
            ->whereNull('deleted_at')
            ->select('id', 'name', 'description', 'type', 'status')
            ->get();

        $indexedCount = 0;

        foreach ($projects as $project) {
            $searchableContent = implode(' ', array_filter([
                $project->name,
                $project->description,
                $project->type,
                $project->status
            ]));

            $keywords = $this->extractKeywords($searchableContent);
            $relevanceScore = $this->calculateRelevanceScore($project);

            DB::table('search_index')->updateOrInsert(
                [
                    'entity_type' => 'projects',
                    'entity_id' => $project->id
                ],
                [
                    'searchable_content' => $searchableContent,
                    'keywords' => json_encode($keywords),
                    'relevance_score' => $relevanceScore,
                    'indexed_at' => now(),
                    'updated_at' => now()
                ]
            );

            $indexedCount++;
        }

        return $indexedCount;
    }

    /**
     * فهرسة المهام للبحث
     * 
     * @return int عدد المهام المفهرسة
     */
    protected function indexTasks(): int
    {
        $tasks = DB::table('tasks')
            ->whereNull('deleted_at')
            ->select('id', 'title', 'description', 'type', 'status', 'priority')
            ->get();

        $indexedCount = 0;

        foreach ($tasks as $task) {
            $searchableContent = implode(' ', array_filter([
                $task->title,
                $task->description,
                $task->type,
                $task->status,
                $task->priority
            ]));

            $keywords = $this->extractKeywords($searchableContent);
            $relevanceScore = $this->calculateTaskRelevanceScore($task);

            DB::table('search_index')->updateOrInsert(
                [
                    'entity_type' => 'tasks',
                    'entity_id' => $task->id
                ],
                [
                    'searchable_content' => $searchableContent,
                    'keywords' => json_encode($keywords),
                    'relevance_score' => $relevanceScore,
                    'indexed_at' => now(),
                    'updated_at' => now()
                ]
            );

            $indexedCount++;
        }

        return $indexedCount;
    }

    /**
     * استخراج الكلمات المفتاحية من النص
     * 
     * @param string $text النص
     * @return array الكلمات المفتاحية
     */
    protected function extractKeywords(string $text): array
    {
        // إزالة علامات الترقيم والأرقام
        $cleanText = preg_replace('/[^\p{L}\s]/u', ' ', $text);
        
        // تقسيم النص إلى كلمات
        $words = preg_split('/\s+/', $cleanText, -1, PREG_SPLIT_NO_EMPTY);
        
        // إزالة الكلمات القصيرة والشائعة
        $stopWords = ['في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك', 'تلك'];
        $keywords = array_filter($words, function($word) use ($stopWords) {
            return strlen($word) > 2 && !in_array($word, $stopWords);
        });
        
        // إرجاع الكلمات الفريدة
        return array_unique(array_map('strtolower', $keywords));
    }

    /**
     * حساب درجة الصلة للمشروع
     * 
     * @param object $project المشروع
     * @return float درجة الصلة
     */
    protected function calculateRelevanceScore($project): float
    {
        $score = 1.0;
        
        // زيادة الدرجة للمشاريع النشطة
        if (in_array($project->status, ['in_progress', 'testing'])) {
            $score += 0.5;
        }
        
        // زيادة الدرجة للمشاريع عالية الأولوية
        if ($project->priority === 'urgent' || $project->priority === 'critical') {
            $score += 0.3;
        }
        
        return min($score, 5.0); // الحد الأقصى 5.0
    }

    /**
     * حساب درجة الصلة للمهمة
     * 
     * @param object $task المهمة
     * @return float درجة الصلة
     */
    protected function calculateTaskRelevanceScore($task): float
    {
        $score = 1.0;
        
        // زيادة الدرجة للمهام النشطة
        if (in_array($task->status, ['in_progress', 'review'])) {
            $score += 0.4;
        }
        
        // زيادة الدرجة للمهام عالية الأولوية
        if ($task->priority === 'urgent') {
            $score += 0.3;
        }
        
        return min($score, 5.0);
    }

    /**
     * تنظيف التخزين المؤقت المنتهي الصلاحية
     * 
     * @return array إحصائيات التنظيف
     */
    public function cleanExpiredCache(): array
    {
        try {
            // تنظيف تخزين الإحصائيات المؤقت
            $expiredStats = DB::table('statistics_cache')
                ->where('expires_at', '<', now())
                ->delete();

            // تنظيف تخزين التقارير المؤقت
            $expiredReports = DB::table('reports_cache')
                ->where('expires_at', '<', now())
                ->delete();

            // تنظيف فهرس البحث القديم
            $oldSearchIndex = DB::table('search_index')
                ->where('indexed_at', '<', now()->subDays(7))
                ->delete();

            Log::info("تم تنظيف التخزين المؤقت المنتهي الصلاحية", [
                'expired_stats' => $expiredStats,
                'expired_reports' => $expiredReports,
                'old_search_index' => $oldSearchIndex
            ]);

            return [
                'success' => true,
                'expired_stats' => $expiredStats,
                'expired_reports' => $expiredReports,
                'old_search_index' => $oldSearchIndex,
                'total_cleaned' => $expiredStats + $expiredReports + $oldSearchIndex
            ];

        } catch (Exception $e) {
            Log::error("خطأ في تنظيف التخزين المؤقت", ['error' => $e->getMessage()]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * تحليل أداء قاعدة البيانات
     * 
     * @return array تقرير الأداء
     */
    public function analyzeDatabasePerformance(): array
    {
        try {
            $analysis = [];

            // حجم الجداول
            $tableSizes = DB::select("
                SELECT 
                    table_name,
                    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb,
                    table_rows
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
                ORDER BY (data_length + index_length) DESC
            ");

            // الاستعلامات البطيئة
            $slowQueries = DB::select("
                SELECT 
                    query_time,
                    lock_time,
                    rows_sent,
                    rows_examined,
                    sql_text
                FROM mysql.slow_log 
                WHERE start_time >= DATE_SUB(NOW(), INTERVAL 1 DAY)
                ORDER BY query_time DESC 
                LIMIT 10
            ");

            // إحصائيات الفهارس
            $indexStats = DB::select("
                SELECT 
                    table_name,
                    index_name,
                    cardinality,
                    index_type
                FROM information_schema.statistics 
                WHERE table_schema = DATABASE()
                ORDER BY cardinality DESC
            ");

            $analysis = [
                'table_sizes' => $tableSizes,
                'slow_queries' => $slowQueries,
                'index_statistics' => $indexStats,
                'analysis_time' => now()->toDateTimeString()
            ];

            Log::info("تم تحليل أداء قاعدة البيانات", [
                'tables_analyzed' => count($tableSizes),
                'slow_queries_found' => count($slowQueries)
            ]);

            return [
                'success' => true,
                'analysis' => $analysis
            ];

        } catch (Exception $e) {
            Log::error("خطأ في تحليل أداء قاعدة البيانات", ['error' => $e->getMessage()]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
