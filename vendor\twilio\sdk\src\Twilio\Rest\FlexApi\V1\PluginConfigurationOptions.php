<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\FlexApi\V1;

use Twilio\Options;
use Twilio\Values;

abstract class PluginConfigurationOptions
{
    /**
     * @param object[] $plugins A list of objects that describe the plugin versions included in the configuration. Each object contains the sid of the plugin version.
     * @param string $description The Flex Plugin Configuration's description.
     * @param string $flexMetadata The Flex-Metadata HTTP request header
     * @return CreatePluginConfigurationOptions Options builder
     */
    public static function create(
        
        array $plugins = Values::ARRAY_NONE,
        string $description = Values::NONE,
        string $flexMetadata = Values::NONE

    ): CreatePluginConfigurationOptions
    {
        return new CreatePluginConfigurationOptions(
            $plugins,
            $description,
            $flexMetadata
        );
    }

    /**
     * @param string $flexMetadata The Flex-Metadata HTTP request header
     * @return FetchPluginConfigurationOptions Options builder
     */
    public static function fetch(
        
        string $flexMetadata = Values::NONE

    ): FetchPluginConfigurationOptions
    {
        return new FetchPluginConfigurationOptions(
            $flexMetadata
        );
    }

    /**
     * @param string $flexMetadata The Flex-Metadata HTTP request header
     * @return ReadPluginConfigurationOptions Options builder
     */
    public static function read(
        
        string $flexMetadata = Values::NONE

    ): ReadPluginConfigurationOptions
    {
        return new ReadPluginConfigurationOptions(
            $flexMetadata
        );
    }

}

class CreatePluginConfigurationOptions extends Options
    {
    /**
     * @param object[] $plugins A list of objects that describe the plugin versions included in the configuration. Each object contains the sid of the plugin version.
     * @param string $description The Flex Plugin Configuration's description.
     * @param string $flexMetadata The Flex-Metadata HTTP request header
     */
    public function __construct(
        
        array $plugins = Values::ARRAY_NONE,
        string $description = Values::NONE,
        string $flexMetadata = Values::NONE

    ) {
        $this->options['plugins'] = $plugins;
        $this->options['description'] = $description;
        $this->options['flexMetadata'] = $flexMetadata;
    }

    /**
     * A list of objects that describe the plugin versions included in the configuration. Each object contains the sid of the plugin version.
     *
     * @param object[] $plugins A list of objects that describe the plugin versions included in the configuration. Each object contains the sid of the plugin version.
     * @return $this Fluent Builder
     */
    public function setPlugins(array $plugins): self
    {
        $this->options['plugins'] = $plugins;
        return $this;
    }

    /**
     * The Flex Plugin Configuration's description.
     *
     * @param string $description The Flex Plugin Configuration's description.
     * @return $this Fluent Builder
     */
    public function setDescription(string $description): self
    {
        $this->options['description'] = $description;
        return $this;
    }

    /**
     * The Flex-Metadata HTTP request header
     *
     * @param string $flexMetadata The Flex-Metadata HTTP request header
     * @return $this Fluent Builder
     */
    public function setFlexMetadata(string $flexMetadata): self
    {
        $this->options['flexMetadata'] = $flexMetadata;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.FlexApi.V1.CreatePluginConfigurationOptions ' . $options . ']';
    }
}

class FetchPluginConfigurationOptions extends Options
    {
    /**
     * @param string $flexMetadata The Flex-Metadata HTTP request header
     */
    public function __construct(
        
        string $flexMetadata = Values::NONE

    ) {
        $this->options['flexMetadata'] = $flexMetadata;
    }

    /**
     * The Flex-Metadata HTTP request header
     *
     * @param string $flexMetadata The Flex-Metadata HTTP request header
     * @return $this Fluent Builder
     */
    public function setFlexMetadata(string $flexMetadata): self
    {
        $this->options['flexMetadata'] = $flexMetadata;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.FlexApi.V1.FetchPluginConfigurationOptions ' . $options . ']';
    }
}

class ReadPluginConfigurationOptions extends Options
    {
    /**
     * @param string $flexMetadata The Flex-Metadata HTTP request header
     */
    public function __construct(
        
        string $flexMetadata = Values::NONE

    ) {
        $this->options['flexMetadata'] = $flexMetadata;
    }

    /**
     * The Flex-Metadata HTTP request header
     *
     * @param string $flexMetadata The Flex-Metadata HTTP request header
     * @return $this Fluent Builder
     */
    public function setFlexMetadata(string $flexMetadata): self
    {
        $this->options['flexMetadata'] = $flexMetadata;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.FlexApi.V1.ReadPluginConfigurationOptions ' . $options . ']';
    }
}

