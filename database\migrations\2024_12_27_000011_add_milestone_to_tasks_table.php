<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * إضافة العلاقة مع المعالم إلى جدول المهام
 * 
 * هذا الـ Migration يضيف العمود المطلوب لربط المهام بالمعالم
 * بعد إنشاء جدول المعالم
 * 
 * @package Database\Migrations
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
return new class extends Migration
{
    /**
     * تشغيل الـ Migration
     * 
     * @return void
     */
    public function up(): void
    {
        Schema::table('tasks', function (Blueprint $table) {
            // إضافة العلاقة مع المعالم
            $table->foreignId('milestone_id')
                  ->nullable()
                  ->after('assigned_to')
                  ->constrained('milestones')
                  ->onDelete('set null')
                  ->comment('معرف المعلم المرتبط');
            
            // إضافة فهرس للعلاقة الجديدة
            $table->index(['milestone_id', 'status'], 'idx_tasks_milestone_status');
        });
    }

    /**
     * التراجع عن الـ Migration
     * 
     * @return void
     */
    public function down(): void
    {
        Schema::table('tasks', function (Blueprint $table) {
            $table->dropForeign(['milestone_id']);
            $table->dropIndex('idx_tasks_milestone_status');
            $table->dropColumn('milestone_id');
        });
    }
};
