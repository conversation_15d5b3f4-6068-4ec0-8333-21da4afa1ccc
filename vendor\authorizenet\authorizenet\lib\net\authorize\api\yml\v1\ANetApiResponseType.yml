net\authorize\api\contract\v1\ANetApiResponseType:
    properties:
        refId:
            expose: true
            access_type: public_method
            serialized_name: refId
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getRefId
                setter: setRefId
            type: string
        messages:
            expose: true
            access_type: public_method
            serialized_name: messages
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getMessages
                setter: setMessages
            type: net\authorize\api\contract\v1\MessagesType
        sessionToken:
            expose: true
            access_type: public_method
            serialized_name: sessionToken
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getSessionToken
                setter: setSessionToken
            type: string
