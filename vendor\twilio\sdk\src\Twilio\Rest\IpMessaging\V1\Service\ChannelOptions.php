<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Ip_messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\IpMessaging\V1\Service;

use Twilio\Options;
use Twilio\Values;

abstract class ChannelOptions
{
    /**
     * @param string $friendlyName 
     * @param string $uniqueName 
     * @param string $attributes 
     * @param string $type
     * @return CreateChannelOptions Options builder
     */
    public static function create(
        
        string $friendlyName = Values::NONE,
        string $uniqueName = Values::NONE,
        string $attributes = Values::NONE,
        string $type = Values::NONE

    ): CreateChannelOptions
    {
        return new CreateChannelOptions(
            $friendlyName,
            $uniqueName,
            $attributes,
            $type
        );
    }



    /**
     * @param string $type 
     * @return ReadChannelOptions Options builder
     */
    public static function read(
        
        array $type = Values::ARRAY_NONE

    ): ReadChannelOptions
    {
        return new ReadChannelOptions(
            $type
        );
    }

    /**
     * @param string $friendlyName 
     * @param string $uniqueName 
     * @param string $attributes 
     * @return UpdateChannelOptions Options builder
     */
    public static function update(
        
        string $friendlyName = Values::NONE,
        string $uniqueName = Values::NONE,
        string $attributes = Values::NONE

    ): UpdateChannelOptions
    {
        return new UpdateChannelOptions(
            $friendlyName,
            $uniqueName,
            $attributes
        );
    }

}

class CreateChannelOptions extends Options
    {
    /**
     * @param string $friendlyName 
     * @param string $uniqueName 
     * @param string $attributes 
     * @param string $type
     */
    public function __construct(
        
        string $friendlyName = Values::NONE,
        string $uniqueName = Values::NONE,
        string $attributes = Values::NONE,
        string $type = Values::NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
        $this->options['uniqueName'] = $uniqueName;
        $this->options['attributes'] = $attributes;
        $this->options['type'] = $type;
    }

    /**
     * 
     *
     * @param string $friendlyName 
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * 
     *
     * @param string $uniqueName 
     * @return $this Fluent Builder
     */
    public function setUniqueName(string $uniqueName): self
    {
        $this->options['uniqueName'] = $uniqueName;
        return $this;
    }

    /**
     * 
     *
     * @param string $attributes 
     * @return $this Fluent Builder
     */
    public function setAttributes(string $attributes): self
    {
        $this->options['attributes'] = $attributes;
        return $this;
    }

    /**
     * @param string $type
     * @return $this Fluent Builder
     */
    public function setType(string $type): self
    {
        $this->options['type'] = $type;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.IpMessaging.V1.CreateChannelOptions ' . $options . ']';
    }
}



class ReadChannelOptions extends Options
    {
    /**
     * @param string $type 
     */
    public function __construct(
        
        array $type = Values::ARRAY_NONE

    ) {
        $this->options['type'] = $type;
    }

    /**
     * 
     *
     * @param string $type 
     * @return $this Fluent Builder
     */
    public function setType(array $type): self
    {
        $this->options['type'] = $type;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.IpMessaging.V1.ReadChannelOptions ' . $options . ']';
    }
}

class UpdateChannelOptions extends Options
    {
    /**
     * @param string $friendlyName 
     * @param string $uniqueName 
     * @param string $attributes 
     */
    public function __construct(
        
        string $friendlyName = Values::NONE,
        string $uniqueName = Values::NONE,
        string $attributes = Values::NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
        $this->options['uniqueName'] = $uniqueName;
        $this->options['attributes'] = $attributes;
    }

    /**
     * 
     *
     * @param string $friendlyName 
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * 
     *
     * @param string $uniqueName 
     * @return $this Fluent Builder
     */
    public function setUniqueName(string $uniqueName): self
    {
        $this->options['uniqueName'] = $uniqueName;
        return $this;
    }

    /**
     * 
     *
     * @param string $attributes 
     * @return $this Fluent Builder
     */
    public function setAttributes(string $attributes): self
    {
        $this->options['attributes'] = $attributes;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.IpMessaging.V1.UpdateChannelOptions ' . $options . ']';
    }
}

