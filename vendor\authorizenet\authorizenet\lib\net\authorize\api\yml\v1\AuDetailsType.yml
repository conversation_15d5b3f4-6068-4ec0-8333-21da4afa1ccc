net\authorize\api\contract\v1\AuDetailsType:
    properties:
        customerProfileID:
            expose: true
            access_type: public_method
            serialized_name: customerProfileID
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getCustomerProfileID
                setter: setCustomerProfileID
            type: integer
        customerPaymentProfileID:
            expose: true
            access_type: public_method
            serialized_name: customerPaymentProfileID
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getCustomerPaymentProfileID
                setter: setCustomerPaymentProfileID
            type: integer
        firstName:
            expose: true
            access_type: public_method
            serialized_name: firstName
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getFirstName
                setter: setFirstName
            type: string
        lastName:
            expose: true
            access_type: public_method
            serialized_name: lastName
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getLastName
                setter: setLastName
            type: string
        updateTimeUTC:
            expose: true
            access_type: public_method
            serialized_name: updateTimeUTC
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getUpdateTimeUTC
                setter: setUpdateTimeUTC
            type: string
        auReasonCode:
            expose: true
            access_type: public_method
            serialized_name: auReasonCode
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getAuReasonCode
                setter: setAuReasonCode
            type: string
        reasonDescription:
            expose: true
            access_type: public_method
            serialized_name: reasonDescription
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getReasonDescription
                setter: setReasonDescription
            type: string
