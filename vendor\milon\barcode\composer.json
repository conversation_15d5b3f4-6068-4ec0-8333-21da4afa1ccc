{"name": "milon/barcode", "description": "Barcode generator like Qr Code, PDF417, C39, C39+, C39<PERSON>, C39<PERSON>+, C93, S25, S25+, I25, I25+, C128, C128A, C128B, C128C, 2-Digits UPC-Based Extention, 5-Digits UPC-Based Extention, EAN 8, EAN 13, UPC-A, UPC-E, MSI (Variation of Plessey code)", "keywords": ["barcode", "laravel", "qrcode", "QR Code", "PDF417", "Datamatrix", "CODE 39", "CODE 128", "EAN", "CODABAR"], "license": "LGPL-3.0", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.3 | ^8.0", "ext-gd": "*", "illuminate/support": "^7.0|^8.0|^9.0|^10.0 | ^11.0"}, "autoload": {"psr-0": {"Milon\\Barcode": "src/"}}, "minimum-stability": "stable", "config": {"sort-packages": true}, "extra": {"laravel": {"providers": ["Milon\\Barcode\\BarcodeServiceProvider"], "aliases": {"DNS1D": "Milon\\Barcode\\Facades\\DNS1DFacade", "DNS2D": "Milon\\Barcode\\Facades\\DNS2DFacade"}}}}