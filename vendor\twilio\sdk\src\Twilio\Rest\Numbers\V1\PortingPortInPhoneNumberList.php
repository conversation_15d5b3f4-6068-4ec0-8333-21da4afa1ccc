<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Numbers\V1;

use Twilio\ListResource;
use Twilio\Version;


class PortingPortInPhoneNumberList extends ListResource
    {
    /**
     * Construct the PortingPortInPhoneNumberList
     *
     * @param Version $version Version that contains the resource
     */
    public function __construct(
        Version $version
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        ];
    }

    /**
     * Constructs a PortingPortInPhoneNumberContext
     *
     * @param string $portInRequestSid The SID of the Port In request. This is a unique identifier of the port in request.
     *
     * @param string $phoneNumberSid The SID of the Port In request phone number. This is a unique identifier of the phone number.
     */
    public function getContext(
        string $portInRequestSid
        , string $phoneNumberSid
        
    ): PortingPortInPhoneNumberContext
    {
        return new PortingPortInPhoneNumberContext(
            $this->version,
            $portInRequestSid,
            $phoneNumberSid
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Numbers.V1.PortingPortInPhoneNumberList]';
    }
}
