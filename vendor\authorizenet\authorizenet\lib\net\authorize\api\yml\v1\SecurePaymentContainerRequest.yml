net\authorize\api\contract\v1\SecurePaymentContainerRequest:
    xml_root_name: securePaymentContainerRequest
    xml_root_namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
    properties:
        data:
            expose: true
            access_type: public_method
            serialized_name: data
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getData
                setter: setData
            type: net\authorize\api\contract\v1\WebCheckOutDataType
