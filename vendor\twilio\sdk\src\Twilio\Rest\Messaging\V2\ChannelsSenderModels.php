<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Messaging\V2;

use Twilio\Values;
abstract class ChannelsSenderModels
{
    /**
     * @property string|null $wabaId The ID of the WhatsApp Business Account to use for this sender.
     * @property string|null $verificationMethod The method to use for verification. Either \"sms\" or \"voice\".
     * @property string|null $verificationCode The verification code to use for this sender.
     * @property string|null $voiceApplicationSid The SID of the Twilio Voice application to use for this sender.
    */
    public static function createMessagingV2ChannelsSenderConfiguration(array $payload = []): MessagingV2ChannelsSenderConfiguration
    {
        return new MessagingV2ChannelsSenderConfiguration($payload);
    }

    /**
     * @property string|null $callbackUrl The URL to send the webhook to.
     * @property string|null $callbackMethod The HTTP method to use for the webhook. Either \"POST\" or \"PUT\".
     * @property string|null $fallbackUrl The URL to send the fallback webhook to.
     * @property string|null $fallbackMethod The HTTP method to use for the fallback webhook. Either \"POST\" or \"PUT\".
     * @property string|null $statusCallbackUrl The URL to send the status callback to.
     * @property string|null $statusCallbackMethod The HTTP method to use for the status callback.
    */
    public static function createMessagingV2ChannelsSenderWebhook(array $payload = []): MessagingV2ChannelsSenderWebhook
    {
        return new MessagingV2ChannelsSenderWebhook($payload);
    }

    /**
     * @property string|null $name The name of the sender.
     * @property string|null $about The about text of the sender.
     * @property string|null $address The address of the sender.
     * @property string|null $description The description of the sender.
     * @property array|null $emails The emails of the sender.
     * @property string|null $logoUrl The logo URL of the sender.
     * @property string|null $vertical The vertical of the sender. Allowed values are: - \"Automotive\" - \"Beauty, Spa and Salon\" - \"Clothing and Apparel\" - \"Education\" - \"Entertainment\" - \"Event Planning and Service\" - \"Finance and Banking\" - \"Food and Grocery\" - \"Public Service\" - \"Hotel and Lodging\" - \"Medical and Health\" - \"Non-profit\" - \"Professional Services\" - \"Shopping and Retail\" - \"Travel and Transportation\" - \"Restaurant\" - \"Other\"
     * @property array|null $websites The websites of the sender.
    */
    public static function createMessagingV2ChannelsSenderProfile(array $payload = []): MessagingV2ChannelsSenderProfile
    {
        return new MessagingV2ChannelsSenderProfile($payload);
    }

    /**
     * @property string $senderId The ID of this Sender prefixed with the channel, e.g., `whatsapp:E.164`
     * @property MessagingV2ChannelsSenderConfiguration $configuration
     * @property MessagingV2ChannelsSenderWebhook $webhook
     * @property MessagingV2ChannelsSenderProfile $profile
    */
    public static function createMessagingV2ChannelsSenderRequestsCreate(array $payload = []): MessagingV2ChannelsSenderRequestsCreate
    {
        return new MessagingV2ChannelsSenderRequestsCreate($payload);
    }

    /**
     * @property MessagingV2ChannelsSenderConfiguration $configuration
     * @property MessagingV2ChannelsSenderWebhook $webhook
     * @property MessagingV2ChannelsSenderProfile $profile
    */
    public static function createMessagingV2ChannelsSenderRequestsUpdate(array $payload = []): MessagingV2ChannelsSenderRequestsUpdate
    {
        return new MessagingV2ChannelsSenderRequestsUpdate($payload);
    }

}

class MessagingV2ChannelsSenderConfiguration implements \JsonSerializable
{
    /**
     * @property string|null $wabaId The ID of the WhatsApp Business Account to use for this sender.
     * @property string|null $verificationMethod The method to use for verification. Either \"sms\" or \"voice\".
     * @property string|null $verificationCode The verification code to use for this sender.
     * @property string|null $voiceApplicationSid The SID of the Twilio Voice application to use for this sender.
    */
        protected $wabaId;
        protected $verificationMethod;
        protected $verificationCode;
        protected $voiceApplicationSid;
    public function __construct(array $payload = []) {
        $this->wabaId = Values::array_get($payload, 'waba_id');
        $this->verificationMethod = Values::array_get($payload, 'verification_method');
        $this->verificationCode = Values::array_get($payload, 'verification_code');
        $this->voiceApplicationSid = Values::array_get($payload, 'voice_application_sid');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'waba_id' => $this->wabaId,
            'verification_method' => $this->verificationMethod,
            'verification_code' => $this->verificationCode,
            'voice_application_sid' => $this->voiceApplicationSid
        ];
    }
}

class MessagingV2ChannelsSenderWebhook implements \JsonSerializable
{
    /**
     * @property string|null $callbackUrl The URL to send the webhook to.
     * @property string|null $callbackMethod The HTTP method to use for the webhook. Either \"POST\" or \"PUT\".
     * @property string|null $fallbackUrl The URL to send the fallback webhook to.
     * @property string|null $fallbackMethod The HTTP method to use for the fallback webhook. Either \"POST\" or \"PUT\".
     * @property string|null $statusCallbackUrl The URL to send the status callback to.
     * @property string|null $statusCallbackMethod The HTTP method to use for the status callback.
    */
        protected $callbackUrl;
        protected $callbackMethod;
        protected $fallbackUrl;
        protected $fallbackMethod;
        protected $statusCallbackUrl;
        protected $statusCallbackMethod;
    public function __construct(array $payload = []) {
        $this->callbackUrl = Values::array_get($payload, 'callback_url');
        $this->callbackMethod = Values::array_get($payload, 'callback_method');
        $this->fallbackUrl = Values::array_get($payload, 'fallback_url');
        $this->fallbackMethod = Values::array_get($payload, 'fallback_method');
        $this->statusCallbackUrl = Values::array_get($payload, 'status_callback_url');
        $this->statusCallbackMethod = Values::array_get($payload, 'status_callback_method');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'callback_url' => $this->callbackUrl,
            'callback_method' => $this->callbackMethod,
            'fallback_url' => $this->fallbackUrl,
            'fallback_method' => $this->fallbackMethod,
            'status_callback_url' => $this->statusCallbackUrl,
            'status_callback_method' => $this->statusCallbackMethod
        ];
    }
}

class MessagingV2ChannelsSenderProfile implements \JsonSerializable
{
    /**
     * @property string|null $name The name of the sender.
     * @property string|null $about The about text of the sender.
     * @property string|null $address The address of the sender.
     * @property string|null $description The description of the sender.
     * @property array|null $emails The emails of the sender.
     * @property string|null $logoUrl The logo URL of the sender.
     * @property string|null $vertical The vertical of the sender. Allowed values are: - \"Automotive\" - \"Beauty, Spa and Salon\" - \"Clothing and Apparel\" - \"Education\" - \"Entertainment\" - \"Event Planning and Service\" - \"Finance and Banking\" - \"Food and Grocery\" - \"Public Service\" - \"Hotel and Lodging\" - \"Medical and Health\" - \"Non-profit\" - \"Professional Services\" - \"Shopping and Retail\" - \"Travel and Transportation\" - \"Restaurant\" - \"Other\"
     * @property array|null $websites The websites of the sender.
    */
        protected $name;
        protected $about;
        protected $address;
        protected $description;
        protected $emails;
        protected $logoUrl;
        protected $vertical;
        protected $websites;
    public function __construct(array $payload = []) {
        $this->name = Values::array_get($payload, 'name');
        $this->about = Values::array_get($payload, 'about');
        $this->address = Values::array_get($payload, 'address');
        $this->description = Values::array_get($payload, 'description');
        $this->emails = Values::array_get($payload, 'emails');
        $this->logoUrl = Values::array_get($payload, 'logo_url');
        $this->vertical = Values::array_get($payload, 'vertical');
        $this->websites = Values::array_get($payload, 'websites');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'name' => $this->name,
            'about' => $this->about,
            'address' => $this->address,
            'description' => $this->description,
            'emails' => $this->emails,
            'logo_url' => $this->logoUrl,
            'vertical' => $this->vertical,
            'websites' => $this->websites
        ];
    }
}

class MessagingV2ChannelsSenderRequestsCreate implements \JsonSerializable
{
    /**
     * @property string $senderId The ID of this Sender prefixed with the channel, e.g., `whatsapp:E.164`
     * @property MessagingV2ChannelsSenderConfiguration $configuration
     * @property MessagingV2ChannelsSenderWebhook $webhook
     * @property MessagingV2ChannelsSenderProfile $profile
    */
        protected $senderId;
        protected $configuration;
        protected $webhook;
        protected $profile;
    public function __construct(array $payload = []) {
        $this->senderId = Values::array_get($payload, 'sender_id');
        $this->configuration = Values::array_get($payload, 'configuration');
        $this->webhook = Values::array_get($payload, 'webhook');
        $this->profile = Values::array_get($payload, 'profile');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'sender_id' => $this->senderId,
            'configuration' => $this->configuration,
            'webhook' => $this->webhook,
            'profile' => $this->profile
        ];
    }
}

class MessagingV2ChannelsSenderRequestsUpdate implements \JsonSerializable
{
    /**
     * @property MessagingV2ChannelsSenderConfiguration $configuration
     * @property MessagingV2ChannelsSenderWebhook $webhook
     * @property MessagingV2ChannelsSenderProfile $profile
    */
        protected $configuration;
        protected $webhook;
        protected $profile;
    public function __construct(array $payload = []) {
        $this->configuration = Values::array_get($payload, 'configuration');
        $this->webhook = Values::array_get($payload, 'webhook');
        $this->profile = Values::array_get($payload, 'profile');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'configuration' => $this->configuration,
            'webhook' => $this->webhook,
            'profile' => $this->profile
        ];
    }
}

