<?php
// This file was auto-generated from sdk-root/src/data/mailmanager/2023-10-17/paginators-1.json
return [ 'pagination' => [ 'ListAddonInstances' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'PageSize', 'result_key' => 'AddonInstances', ], 'ListAddonSubscriptions' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'PageSize', 'result_key' => 'AddonSubscriptions', ], 'ListAddressListImportJobs' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'PageSize', 'result_key' => 'ImportJobs', ], 'ListAddressLists' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'PageSize', 'result_key' => 'AddressLists', ], 'ListArchiveExports' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'PageSize', 'result_key' => 'Exports', ], 'ListArchiveSearches' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'PageSize', 'result_key' => 'Searches', ], 'ListArchives' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'PageSize', 'result_key' => 'Archives', ], 'ListIngressPoints' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'PageSize', 'result_key' => 'IngressPoints', ], 'ListMembersOfAddressList' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'PageSize', 'result_key' => 'Addresses', ], 'ListRelays' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'PageSize', 'result_key' => 'Relays', ], 'ListRuleSets' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'PageSize', 'result_key' => 'RuleSets', ], 'ListTrafficPolicies' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'PageSize', 'result_key' => 'TrafficPolicies', ], ],];
