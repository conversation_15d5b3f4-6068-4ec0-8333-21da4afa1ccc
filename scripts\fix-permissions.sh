#!/bin/bash

# سكريبت إصلاح صلاحيات Laravel بشكل آمن
# مخصص لنظام إدارة المشاريع Hesabiai
# 
# الاستخدام: bash fix-permissions.sh
# أو: chmod +x fix-permissions.sh && ./fix-permissions.sh

echo "🔧 بدء إصلاح صلاحيات Laravel بشكل آمن..."

# التحقق من وجود مجلد المشروع
if [ ! -f "artisan" ]; then
    echo "❌ خطأ: هذا السكريبت يجب تشغيله من مجلد المشروع الرئيسي"
    exit 1
fi

# الحصول على معلومات المستخدم والخادم
WEB_USER="www-data"  # مستخدم الخادم الافتراضي
CURRENT_USER=$(whoami)

# محاولة تحديد مستخدم الخادم تلقائياً
if [ -f /etc/apache2/apache2.conf ]; then
    WEB_USER=$(grep -E "^User" /etc/apache2/apache2.conf | awk '{print $2}' | head -1)
elif [ -f /etc/nginx/nginx.conf ]; then
    WEB_USER=$(grep -E "^user" /etc/nginx/nginx.conf | awk '{print $2}' | sed 's/;//' | head -1)
elif [ -f /etc/httpd/conf/httpd.conf ]; then
    WEB_USER=$(grep -E "^User" /etc/httpd/conf/httpd.conf | awk '{print $2}' | head -1)
fi

echo "👤 المستخدم الحالي: $CURRENT_USER"
echo "🌐 مستخدم الخادم: $WEB_USER"

# التحقق من وجود مستخدم الخادم
if ! id "$WEB_USER" &>/dev/null; then
    echo "⚠️  تحذير: مستخدم الخادم '$WEB_USER' غير موجود"
    echo "📝 المستخدمين المتاحين:"
    echo "   - www-data (Apache/Nginx على Ubuntu/Debian)"
    echo "   - apache (Apache على CentOS/RHEL)"
    echo "   - nginx (Nginx على بعض التوزيعات)"
    echo "   - nobody (بعض الخوادم)"
    
    read -p "🔍 أدخل اسم مستخدم الخادم الصحيح: " WEB_USER
    
    if ! id "$WEB_USER" &>/dev/null; then
        echo "❌ المستخدم '$WEB_USER' غير موجود. الخروج..."
        exit 1
    fi
fi

echo ""
echo "🔒 تطبيق الصلاحيات الآمنة..."

# 1. تعيين مالك الملفات
echo "👥 تعيين مالك الملفات..."
sudo chown -R $CURRENT_USER:$WEB_USER .

# 2. صلاحيات المجلدات (755)
echo "📁 تعيين صلاحيات المجلدات (755)..."
find . -type d -exec chmod 755 {} \;

# 3. صلاحيات الملفات العادية (644)
echo "📄 تعيين صلاحيات الملفات (644)..."
find . -type f -exec chmod 644 {} \;

# 4. صلاحيات خاصة للمجلدات المهمة
echo "🔐 تعيين صلاحيات خاصة للمجلدات المهمة..."

# مجلد storage (775 للكتابة)
if [ -d "storage" ]; then
    chmod -R 775 storage
    echo "   ✓ storage: 775"
fi

# مجلد bootstrap/cache (775 للكتابة)
if [ -d "bootstrap/cache" ]; then
    chmod -R 775 bootstrap/cache
    echo "   ✓ bootstrap/cache: 775"
fi

# مجلد public (755 للقراءة العامة)
if [ -d "public" ]; then
    chmod -R 755 public
    echo "   ✓ public: 755"
fi

# مجلد config (644 للحماية)
if [ -d "config" ]; then
    chmod -R 644 config
    chmod 755 config  # المجلد نفسه
    echo "   ✓ config: 644"
fi

# 5. صلاحيات خاصة للملفات المهمة
echo "📋 تعيين صلاحيات خاصة للملفات المهمة..."

# ملف artisan (755 للتنفيذ)
if [ -f "artisan" ]; then
    chmod 755 artisan
    echo "   ✓ artisan: 755"
fi

# ملفات .env (600 للحماية القصوى)
find . -name ".env*" -type f -exec chmod 600 {} \;
echo "   ✓ ملفات .env: 600"

# ملفات composer (755 للتنفيذ)
if [ -f "composer.phar" ]; then
    chmod 755 composer.phar
    echo "   ✓ composer.phar: 755"
fi

# سكريبتات shell (755 للتنفيذ)
find . -name "*.sh" -type f -exec chmod 755 {} \;
echo "   ✓ سكريبتات shell: 755"

# 6. إنشاء المجلدات المطلوبة إذا لم تكن موجودة
echo "📂 إنشاء المجلدات المطلوبة..."

REQUIRED_DIRS=(
    "storage/app"
    "storage/app/public"
    "storage/framework"
    "storage/framework/cache"
    "storage/framework/cache/data"
    "storage/framework/sessions"
    "storage/framework/views"
    "storage/logs"
    "bootstrap/cache"
    "public/storage"
)

for dir in "${REQUIRED_DIRS[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        chmod 775 "$dir"
        chown $CURRENT_USER:$WEB_USER "$dir"
        echo "   ✓ تم إنشاء: $dir"
    fi
done

# 7. إنشاء الرابط الرمزي للتخزين
echo "🔗 إنشاء رابط التخزين العام..."
if [ ! -L "public/storage" ]; then
    php artisan storage:link 2>/dev/null || echo "   ⚠️  تعذر إنشاء رابط التخزين (قم بتشغيله يدوياً لاحقاً)"
else
    echo "   ✓ رابط التخزين موجود مسبقاً"
fi

# 8. تحسين Laravel
echo "⚡ تحسين Laravel..."

# تنظيف وإعادة بناء التخزين المؤقت
php artisan config:clear 2>/dev/null || echo "   ⚠️  تعذر مسح تخزين التكوين"
php artisan cache:clear 2>/dev/null || echo "   ⚠️  تعذر مسح التخزين المؤقت"
php artisan view:clear 2>/dev/null || echo "   ⚠️  تعذر مسح تخزين العروض"
php artisan route:clear 2>/dev/null || echo "   ⚠️  تعذر مسح تخزين المسارات"

# إعادة بناء التخزين المؤقت للإنتاج
if [ "$1" = "production" ]; then
    echo "🚀 تحسين للإنتاج..."
    php artisan config:cache 2>/dev/null || echo "   ⚠️  تعذر إنشاء تخزين التكوين"
    php artisan route:cache 2>/dev/null || echo "   ⚠️  تعذر إنشاء تخزين المسارات"
    php artisan view:cache 2>/dev/null || echo "   ⚠️  تعذر إنشاء تخزين العروض"
fi

# 9. فحص الصلاحيات النهائي
echo ""
echo "🔍 فحص الصلاحيات النهائي..."

# فحص المجلدات المهمة
CRITICAL_DIRS=("storage" "bootstrap/cache" "public")
for dir in "${CRITICAL_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        PERMS=$(stat -c "%a" "$dir" 2>/dev/null || stat -f "%A" "$dir" 2>/dev/null)
        OWNER=$(stat -c "%U:%G" "$dir" 2>/dev/null || stat -f "%Su:%Sg" "$dir" 2>/dev/null)
        echo "   📁 $dir: $PERMS ($OWNER)"
    fi
done

# فحص الملفات المهمة
CRITICAL_FILES=("artisan" ".env")
for file in "${CRITICAL_FILES[@]}"; do
    if [ -f "$file" ]; then
        PERMS=$(stat -c "%a" "$file" 2>/dev/null || stat -f "%A" "$file" 2>/dev/null)
        OWNER=$(stat -c "%U:%G" "$file" 2>/dev/null || stat -f "%Su:%Sg" "$file" 2>/dev/null)
        echo "   📄 $file: $PERMS ($OWNER)"
    fi
done

echo ""
echo "✅ تم إصلاح الصلاحيات بنجاح!"
echo ""
echo "📋 ملخص الصلاحيات المطبقة:"
echo "   📁 المجلدات العادية: 755"
echo "   📄 الملفات العادية: 644"
echo "   🔒 storage/: 775"
echo "   🔒 bootstrap/cache/: 775"
echo "   🔒 public/: 755"
echo "   🔐 .env: 600"
echo "   ⚡ artisan: 755"
echo ""
echo "🔧 أوامر إضافية قد تحتاجها:"
echo "   composer install --optimize-autoloader --no-dev"
echo "   php artisan migrate"
echo "   php artisan db:seed"
echo "   php artisan key:generate"
echo ""
echo "⚠️  ملاحظات مهمة:"
echo "   1. تأكد من تحديث ملف .env بإعدادات قاعدة البيانات"
echo "   2. قم بإعداد Virtual Host أو Nginx config"
echo "   3. تأكد من تشغيل PHP-FPM أو Apache mod_php"
echo "   4. راجع سجلات الأخطاء في /var/log/apache2/ أو /var/log/nginx/"
echo ""
echo "🎉 المشروع جاهز للاستخدام!"
