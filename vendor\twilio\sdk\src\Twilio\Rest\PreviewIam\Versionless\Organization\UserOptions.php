<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Organization Public API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\PreviewIam\Versionless\Organization;

use Twilio\Options;
use Twilio\Values;

abstract class UserOptions
{



    /**
     * @param string $filter
     * @return ReadUserOptions Options builder
     */
    public static function read(
        
        string $filter = Values::NONE

    ): ReadUserOptions
    {
        return new ReadUserOptions(
            $filter
        );
    }

    /**
     * @param string $ifMatch
     * @return UpdateUserOptions Options builder
     */
    public static function update(
        
        string $ifMatch = Values::NONE

    ): UpdateUserOptions
    {
        return new UpdateUserOptions(
            $ifMatch
        );
    }

}




class ReadUserOptions extends Options
    {
    /**
     * @param string $filter
     */
    public function __construct(
        
        string $filter = Values::NONE

    ) {
        $this->options['filter'] = $filter;
    }

    /**
     * @param string $filter
     * @return $this Fluent Builder
     */
    public function setFilter(string $filter): self
    {
        $this->options['filter'] = $filter;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.PreviewIam.Versionless.ReadUserOptions ' . $options . ']';
    }
}

class UpdateUserOptions extends Options
    {
    /**
     * @param string $ifMatch
     */
    public function __construct(
        
        string $ifMatch = Values::NONE

    ) {
        $this->options['ifMatch'] = $ifMatch;
    }

    /**
     * @param string $ifMatch
     * @return $this Fluent Builder
     */
    public function setIfMatch(string $ifMatch): self
    {
        $this->options['ifMatch'] = $ifMatch;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.PreviewIam.Versionless.UpdateUserOptions ' . $options . ']';
    }
}

