<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Api\V2010;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;
use Twilio\Rest\Api\V2010\Account\RecordingList;
use Twilio\Rest\Api\V2010\Account\UsageList;
use Twilio\Rest\Api\V2010\Account\MessageList;
use Twilio\Rest\Api\V2010\Account\KeyList;
use Twilio\Rest\Api\V2010\Account\NewKeyList;
use Twilio\Rest\Api\V2010\Account\ApplicationList;
use Twilio\Rest\Api\V2010\Account\IncomingPhoneNumberList;
use Twilio\Rest\Api\V2010\Account\ConferenceList;
use Twilio\Rest\Api\V2010\Account\CallList;
use Twilio\Rest\Api\V2010\Account\OutgoingCallerIdList;
use Twilio\Rest\Api\V2010\Account\ValidationRequestList;
use Twilio\Rest\Api\V2010\Account\TranscriptionList;
use Twilio\Rest\Api\V2010\Account\ConnectAppList;
use Twilio\Rest\Api\V2010\Account\AuthorizedConnectAppList;
use Twilio\Rest\Api\V2010\Account\TokenList;
use Twilio\Rest\Api\V2010\Account\BalanceList;
use Twilio\Rest\Api\V2010\Account\SipList;
use Twilio\Rest\Api\V2010\Account\NotificationList;
use Twilio\Rest\Api\V2010\Account\AvailablePhoneNumberCountryList;
use Twilio\Rest\Api\V2010\Account\AddressList;
use Twilio\Rest\Api\V2010\Account\QueueList;
use Twilio\Rest\Api\V2010\Account\ShortCodeList;
use Twilio\Rest\Api\V2010\Account\SigningKeyList;
use Twilio\Rest\Api\V2010\Account\NewSigningKeyList;


/**
 * @property string|null $authToken
 * @property \DateTime|null $dateCreated
 * @property \DateTime|null $dateUpdated
 * @property string|null $friendlyName
 * @property string|null $ownerAccountSid
 * @property string|null $sid
 * @property string $status
 * @property array|null $subresourceUris
 * @property string $type
 * @property string|null $uri
 */
class AccountInstance extends InstanceResource
{
    protected $_recordings;
    protected $_usage;
    protected $_messages;
    protected $_keys;
    protected $_newKeys;
    protected $_applications;
    protected $_incomingPhoneNumbers;
    protected $_conferences;
    protected $_calls;
    protected $_outgoingCallerIds;
    protected $_validationRequests;
    protected $_transcriptions;
    protected $_connectApps;
    protected $_authorizedConnectApps;
    protected $_tokens;
    protected $_balance;
    protected $_sip;
    protected $_notifications;
    protected $_availablePhoneNumbers;
    protected $_addresses;
    protected $_queues;
    protected $_shortCodes;
    protected $_signingKeys;
    protected $_newSigningKeys;

    /**
     * Initialize the AccountInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $sid The Account Sid that uniquely identifies the account to fetch
     */
    public function __construct(Version $version, array $payload, ?string $sid = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'authToken' => Values::array_get($payload, 'auth_token'),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'dateUpdated' => Deserialize::dateTime(Values::array_get($payload, 'date_updated')),
            'friendlyName' => Values::array_get($payload, 'friendly_name'),
            'ownerAccountSid' => Values::array_get($payload, 'owner_account_sid'),
            'sid' => Values::array_get($payload, 'sid'),
            'status' => Values::array_get($payload, 'status'),
            'subresourceUris' => Values::array_get($payload, 'subresource_uris'),
            'type' => Values::array_get($payload, 'type'),
            'uri' => Values::array_get($payload, 'uri'),
        ];

        $this->solution = ['sid' => $sid ?: $this->properties['sid'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return AccountContext Context for this AccountInstance
     */
    protected function proxy(): AccountContext
    {
        if (!$this->context) {
            $this->context = new AccountContext(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->context;
    }

    /**
     * Fetch the AccountInstance
     *
     * @return AccountInstance Fetched AccountInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): AccountInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Update the AccountInstance
     *
     * @param array|Options $options Optional Arguments
     * @return AccountInstance Updated AccountInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(array $options = []): AccountInstance
    {

        return $this->proxy()->update($options);
    }

    /**
     * Access the recordings
     */
    protected function getRecordings(): RecordingList
    {
        return $this->proxy()->recordings;
    }

    /**
     * Access the usage
     */
    protected function getUsage(): UsageList
    {
        return $this->proxy()->usage;
    }

    /**
     * Access the messages
     */
    protected function getMessages(): MessageList
    {
        return $this->proxy()->messages;
    }

    /**
     * Access the keys
     */
    protected function getKeys(): KeyList
    {
        return $this->proxy()->keys;
    }

    /**
     * Access the newKeys
     */
    protected function getNewKeys(): NewKeyList
    {
        return $this->proxy()->newKeys;
    }

    /**
     * Access the applications
     */
    protected function getApplications(): ApplicationList
    {
        return $this->proxy()->applications;
    }

    /**
     * Access the incomingPhoneNumbers
     */
    protected function getIncomingPhoneNumbers(): IncomingPhoneNumberList
    {
        return $this->proxy()->incomingPhoneNumbers;
    }

    /**
     * Access the conferences
     */
    protected function getConferences(): ConferenceList
    {
        return $this->proxy()->conferences;
    }

    /**
     * Access the calls
     */
    protected function getCalls(): CallList
    {
        return $this->proxy()->calls;
    }

    /**
     * Access the outgoingCallerIds
     */
    protected function getOutgoingCallerIds(): OutgoingCallerIdList
    {
        return $this->proxy()->outgoingCallerIds;
    }

    /**
     * Access the validationRequests
     */
    protected function getValidationRequests(): ValidationRequestList
    {
        return $this->proxy()->validationRequests;
    }

    /**
     * Access the transcriptions
     */
    protected function getTranscriptions(): TranscriptionList
    {
        return $this->proxy()->transcriptions;
    }

    /**
     * Access the connectApps
     */
    protected function getConnectApps(): ConnectAppList
    {
        return $this->proxy()->connectApps;
    }

    /**
     * Access the authorizedConnectApps
     */
    protected function getAuthorizedConnectApps(): AuthorizedConnectAppList
    {
        return $this->proxy()->authorizedConnectApps;
    }

    /**
     * Access the tokens
     */
    protected function getTokens(): TokenList
    {
        return $this->proxy()->tokens;
    }

    /**
     * Access the balance
     */
    protected function getBalance(): BalanceList
    {
        return $this->proxy()->balance;
    }

    /**
     * Access the sip
     */
    protected function getSip(): SipList
    {
        return $this->proxy()->sip;
    }

    /**
     * Access the notifications
     */
    protected function getNotifications(): NotificationList
    {
        return $this->proxy()->notifications;
    }

    /**
     * Access the availablePhoneNumbers
     */
    protected function getAvailablePhoneNumbers(): AvailablePhoneNumberCountryList
    {
        return $this->proxy()->availablePhoneNumbers;
    }

    /**
     * Access the addresses
     */
    protected function getAddresses(): AddressList
    {
        return $this->proxy()->addresses;
    }

    /**
     * Access the queues
     */
    protected function getQueues(): QueueList
    {
        return $this->proxy()->queues;
    }

    /**
     * Access the shortCodes
     */
    protected function getShortCodes(): ShortCodeList
    {
        return $this->proxy()->shortCodes;
    }

    /**
     * Access the signingKeys
     */
    protected function getSigningKeys(): SigningKeyList
    {
        return $this->proxy()->signingKeys;
    }

    /**
     * Access the newSigningKeys
     */
    protected function getNewSigningKeys(): NewSigningKeyList
    {
        return $this->proxy()->newSigningKeys;
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Api.V2010.AccountInstance ' . \implode(' ', $context) . ']';
    }
}

