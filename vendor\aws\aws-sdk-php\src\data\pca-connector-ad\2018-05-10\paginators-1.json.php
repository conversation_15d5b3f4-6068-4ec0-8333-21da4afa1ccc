<?php
// This file was auto-generated from sdk-root/src/data/pca-connector-ad/2018-05-10/paginators-1.json
return [ 'pagination' => [ 'ListConnectors' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'Connectors', ], 'ListDirectoryRegistrations' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'DirectoryRegistrations', ], 'ListServicePrincipalNames' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'ServicePrincipalNames', ], 'ListTemplateGroupAccessControlEntries' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'AccessControlEntries', ], 'ListTemplates' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'Templates', ], ],];
