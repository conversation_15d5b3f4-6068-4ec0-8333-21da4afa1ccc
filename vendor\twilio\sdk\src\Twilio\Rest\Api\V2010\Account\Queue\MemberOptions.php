<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Api\V2010\Account\Queue;

use Twilio\Options;
use Twilio\Values;

abstract class MemberOptions
{


    /**
     * @param string $method How to pass the update request data. Can be `GET` or `POST` and the default is `POST`. `POST` sends the data as encoded form data and `GET` sends the data as query parameters.
     * @return UpdateMemberOptions Options builder
     */
    public static function update(
        
        string $method = Values::NONE

    ): UpdateMemberOptions
    {
        return new UpdateMemberOptions(
            $method
        );
    }

}



class UpdateMemberOptions extends Options
    {
    /**
     * @param string $method How to pass the update request data. Can be `GET` or `POST` and the default is `POST`. `POST` sends the data as encoded form data and `GET` sends the data as query parameters.
     */
    public function __construct(
        
        string $method = Values::NONE

    ) {
        $this->options['method'] = $method;
    }

    /**
     * How to pass the update request data. Can be `GET` or `POST` and the default is `POST`. `POST` sends the data as encoded form data and `GET` sends the data as query parameters.
     *
     * @param string $method How to pass the update request data. Can be `GET` or `POST` and the default is `POST`. `POST` sends the data as encoded form data and `GET` sends the data as query parameters.
     * @return $this Fluent Builder
     */
    public function setMethod(string $method): self
    {
        $this->options['method'] = $method;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Api.V2010.UpdateMemberOptions ' . $options . ']';
    }
}

