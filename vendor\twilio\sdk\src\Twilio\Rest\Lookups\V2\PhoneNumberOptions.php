<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Lookups
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Lookups\V2;

use Twilio\Options;
use Twilio\Values;

abstract class PhoneNumberOptions
{
    /**
     * @param string $fields A comma-separated list of fields to return. Possible values are validation, caller_name, sim_swap, call_forwarding, line_status, line_type_intelligence, identity_match, reassigned_number, sms_pumping_risk, phone_number_quality_score, pre_fill.
     * @param string $countryCode The [country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) used if the phone number provided is in national format.
     * @param string $firstName User’s first name. This query parameter is only used (optionally) for identity_match package requests.
     * @param string $lastName User’s last name. This query parameter is only used (optionally) for identity_match package requests.
     * @param string $addressLine1 User’s first address line. This query parameter is only used (optionally) for identity_match package requests.
     * @param string $addressLine2 User’s second address line. This query parameter is only used (optionally) for identity_match package requests.
     * @param string $city User’s city. This query parameter is only used (optionally) for identity_match package requests.
     * @param string $state User’s country subdivision, such as state, province, or locality. This query parameter is only used (optionally) for identity_match package requests.
     * @param string $postalCode User’s postal zip code. This query parameter is only used (optionally) for identity_match package requests.
     * @param string $addressCountryCode User’s country, up to two characters. This query parameter is only used (optionally) for identity_match package requests.
     * @param string $nationalId User’s national ID, such as SSN or Passport ID. This query parameter is only used (optionally) for identity_match package requests.
     * @param string $dateOfBirth User’s date of birth, in YYYYMMDD format. This query parameter is only used (optionally) for identity_match package requests.
     * @param string $lastVerifiedDate The date you obtained consent to call or text the end-user of the phone number or a date on which you are reasonably certain that the end-user could still be reached at that number. This query parameter is only used (optionally) for reassigned_number package requests.
     * @param string $verificationSid The unique identifier associated with a verification process through verify API. This query parameter is only used (optionally) for pre_fill package requests.
     * @param string $partnerSubId The optional partnerSubId parameter to provide context for your sub-accounts, tenantIDs, sender IDs or other segmentation, enhancing the accuracy of the risk analysis.
     * @return FetchPhoneNumberOptions Options builder
     */
    public static function fetch(
        
        string $fields = Values::NONE,
        string $countryCode = Values::NONE,
        string $firstName = Values::NONE,
        string $lastName = Values::NONE,
        string $addressLine1 = Values::NONE,
        string $addressLine2 = Values::NONE,
        string $city = Values::NONE,
        string $state = Values::NONE,
        string $postalCode = Values::NONE,
        string $addressCountryCode = Values::NONE,
        string $nationalId = Values::NONE,
        string $dateOfBirth = Values::NONE,
        string $lastVerifiedDate = Values::NONE,
        string $verificationSid = Values::NONE,
        string $partnerSubId = Values::NONE

    ): FetchPhoneNumberOptions
    {
        return new FetchPhoneNumberOptions(
            $fields,
            $countryCode,
            $firstName,
            $lastName,
            $addressLine1,
            $addressLine2,
            $city,
            $state,
            $postalCode,
            $addressCountryCode,
            $nationalId,
            $dateOfBirth,
            $lastVerifiedDate,
            $verificationSid,
            $partnerSubId
        );
    }

}

class FetchPhoneNumberOptions extends Options
    {
    /**
     * @param string $fields A comma-separated list of fields to return. Possible values are validation, caller_name, sim_swap, call_forwarding, line_status, line_type_intelligence, identity_match, reassigned_number, sms_pumping_risk, phone_number_quality_score, pre_fill.
     * @param string $countryCode The [country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) used if the phone number provided is in national format.
     * @param string $firstName User’s first name. This query parameter is only used (optionally) for identity_match package requests.
     * @param string $lastName User’s last name. This query parameter is only used (optionally) for identity_match package requests.
     * @param string $addressLine1 User’s first address line. This query parameter is only used (optionally) for identity_match package requests.
     * @param string $addressLine2 User’s second address line. This query parameter is only used (optionally) for identity_match package requests.
     * @param string $city User’s city. This query parameter is only used (optionally) for identity_match package requests.
     * @param string $state User’s country subdivision, such as state, province, or locality. This query parameter is only used (optionally) for identity_match package requests.
     * @param string $postalCode User’s postal zip code. This query parameter is only used (optionally) for identity_match package requests.
     * @param string $addressCountryCode User’s country, up to two characters. This query parameter is only used (optionally) for identity_match package requests.
     * @param string $nationalId User’s national ID, such as SSN or Passport ID. This query parameter is only used (optionally) for identity_match package requests.
     * @param string $dateOfBirth User’s date of birth, in YYYYMMDD format. This query parameter is only used (optionally) for identity_match package requests.
     * @param string $lastVerifiedDate The date you obtained consent to call or text the end-user of the phone number or a date on which you are reasonably certain that the end-user could still be reached at that number. This query parameter is only used (optionally) for reassigned_number package requests.
     * @param string $verificationSid The unique identifier associated with a verification process through verify API. This query parameter is only used (optionally) for pre_fill package requests.
     * @param string $partnerSubId The optional partnerSubId parameter to provide context for your sub-accounts, tenantIDs, sender IDs or other segmentation, enhancing the accuracy of the risk analysis.
     */
    public function __construct(
        
        string $fields = Values::NONE,
        string $countryCode = Values::NONE,
        string $firstName = Values::NONE,
        string $lastName = Values::NONE,
        string $addressLine1 = Values::NONE,
        string $addressLine2 = Values::NONE,
        string $city = Values::NONE,
        string $state = Values::NONE,
        string $postalCode = Values::NONE,
        string $addressCountryCode = Values::NONE,
        string $nationalId = Values::NONE,
        string $dateOfBirth = Values::NONE,
        string $lastVerifiedDate = Values::NONE,
        string $verificationSid = Values::NONE,
        string $partnerSubId = Values::NONE

    ) {
        $this->options['fields'] = $fields;
        $this->options['countryCode'] = $countryCode;
        $this->options['firstName'] = $firstName;
        $this->options['lastName'] = $lastName;
        $this->options['addressLine1'] = $addressLine1;
        $this->options['addressLine2'] = $addressLine2;
        $this->options['city'] = $city;
        $this->options['state'] = $state;
        $this->options['postalCode'] = $postalCode;
        $this->options['addressCountryCode'] = $addressCountryCode;
        $this->options['nationalId'] = $nationalId;
        $this->options['dateOfBirth'] = $dateOfBirth;
        $this->options['lastVerifiedDate'] = $lastVerifiedDate;
        $this->options['verificationSid'] = $verificationSid;
        $this->options['partnerSubId'] = $partnerSubId;
    }

    /**
     * A comma-separated list of fields to return. Possible values are validation, caller_name, sim_swap, call_forwarding, line_status, line_type_intelligence, identity_match, reassigned_number, sms_pumping_risk, phone_number_quality_score, pre_fill.
     *
     * @param string $fields A comma-separated list of fields to return. Possible values are validation, caller_name, sim_swap, call_forwarding, line_status, line_type_intelligence, identity_match, reassigned_number, sms_pumping_risk, phone_number_quality_score, pre_fill.
     * @return $this Fluent Builder
     */
    public function setFields(string $fields): self
    {
        $this->options['fields'] = $fields;
        return $this;
    }

    /**
     * The [country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) used if the phone number provided is in national format.
     *
     * @param string $countryCode The [country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) used if the phone number provided is in national format.
     * @return $this Fluent Builder
     */
    public function setCountryCode(string $countryCode): self
    {
        $this->options['countryCode'] = $countryCode;
        return $this;
    }

    /**
     * User’s first name. This query parameter is only used (optionally) for identity_match package requests.
     *
     * @param string $firstName User’s first name. This query parameter is only used (optionally) for identity_match package requests.
     * @return $this Fluent Builder
     */
    public function setFirstName(string $firstName): self
    {
        $this->options['firstName'] = $firstName;
        return $this;
    }

    /**
     * User’s last name. This query parameter is only used (optionally) for identity_match package requests.
     *
     * @param string $lastName User’s last name. This query parameter is only used (optionally) for identity_match package requests.
     * @return $this Fluent Builder
     */
    public function setLastName(string $lastName): self
    {
        $this->options['lastName'] = $lastName;
        return $this;
    }

    /**
     * User’s first address line. This query parameter is only used (optionally) for identity_match package requests.
     *
     * @param string $addressLine1 User’s first address line. This query parameter is only used (optionally) for identity_match package requests.
     * @return $this Fluent Builder
     */
    public function setAddressLine1(string $addressLine1): self
    {
        $this->options['addressLine1'] = $addressLine1;
        return $this;
    }

    /**
     * User’s second address line. This query parameter is only used (optionally) for identity_match package requests.
     *
     * @param string $addressLine2 User’s second address line. This query parameter is only used (optionally) for identity_match package requests.
     * @return $this Fluent Builder
     */
    public function setAddressLine2(string $addressLine2): self
    {
        $this->options['addressLine2'] = $addressLine2;
        return $this;
    }

    /**
     * User’s city. This query parameter is only used (optionally) for identity_match package requests.
     *
     * @param string $city User’s city. This query parameter is only used (optionally) for identity_match package requests.
     * @return $this Fluent Builder
     */
    public function setCity(string $city): self
    {
        $this->options['city'] = $city;
        return $this;
    }

    /**
     * User’s country subdivision, such as state, province, or locality. This query parameter is only used (optionally) for identity_match package requests.
     *
     * @param string $state User’s country subdivision, such as state, province, or locality. This query parameter is only used (optionally) for identity_match package requests.
     * @return $this Fluent Builder
     */
    public function setState(string $state): self
    {
        $this->options['state'] = $state;
        return $this;
    }

    /**
     * User’s postal zip code. This query parameter is only used (optionally) for identity_match package requests.
     *
     * @param string $postalCode User’s postal zip code. This query parameter is only used (optionally) for identity_match package requests.
     * @return $this Fluent Builder
     */
    public function setPostalCode(string $postalCode): self
    {
        $this->options['postalCode'] = $postalCode;
        return $this;
    }

    /**
     * User’s country, up to two characters. This query parameter is only used (optionally) for identity_match package requests.
     *
     * @param string $addressCountryCode User’s country, up to two characters. This query parameter is only used (optionally) for identity_match package requests.
     * @return $this Fluent Builder
     */
    public function setAddressCountryCode(string $addressCountryCode): self
    {
        $this->options['addressCountryCode'] = $addressCountryCode;
        return $this;
    }

    /**
     * User’s national ID, such as SSN or Passport ID. This query parameter is only used (optionally) for identity_match package requests.
     *
     * @param string $nationalId User’s national ID, such as SSN or Passport ID. This query parameter is only used (optionally) for identity_match package requests.
     * @return $this Fluent Builder
     */
    public function setNationalId(string $nationalId): self
    {
        $this->options['nationalId'] = $nationalId;
        return $this;
    }

    /**
     * User’s date of birth, in YYYYMMDD format. This query parameter is only used (optionally) for identity_match package requests.
     *
     * @param string $dateOfBirth User’s date of birth, in YYYYMMDD format. This query parameter is only used (optionally) for identity_match package requests.
     * @return $this Fluent Builder
     */
    public function setDateOfBirth(string $dateOfBirth): self
    {
        $this->options['dateOfBirth'] = $dateOfBirth;
        return $this;
    }

    /**
     * The date you obtained consent to call or text the end-user of the phone number or a date on which you are reasonably certain that the end-user could still be reached at that number. This query parameter is only used (optionally) for reassigned_number package requests.
     *
     * @param string $lastVerifiedDate The date you obtained consent to call or text the end-user of the phone number or a date on which you are reasonably certain that the end-user could still be reached at that number. This query parameter is only used (optionally) for reassigned_number package requests.
     * @return $this Fluent Builder
     */
    public function setLastVerifiedDate(string $lastVerifiedDate): self
    {
        $this->options['lastVerifiedDate'] = $lastVerifiedDate;
        return $this;
    }

    /**
     * The unique identifier associated with a verification process through verify API. This query parameter is only used (optionally) for pre_fill package requests.
     *
     * @param string $verificationSid The unique identifier associated with a verification process through verify API. This query parameter is only used (optionally) for pre_fill package requests.
     * @return $this Fluent Builder
     */
    public function setVerificationSid(string $verificationSid): self
    {
        $this->options['verificationSid'] = $verificationSid;
        return $this;
    }

    /**
     * The optional partnerSubId parameter to provide context for your sub-accounts, tenantIDs, sender IDs or other segmentation, enhancing the accuracy of the risk analysis.
     *
     * @param string $partnerSubId The optional partnerSubId parameter to provide context for your sub-accounts, tenantIDs, sender IDs or other segmentation, enhancing the accuracy of the risk analysis.
     * @return $this Fluent Builder
     */
    public function setPartnerSubId(string $partnerSubId): self
    {
        $this->options['partnerSubId'] = $partnerSubId;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Lookups.V2.FetchPhoneNumberOptions ' . $options . ']';
    }
}

