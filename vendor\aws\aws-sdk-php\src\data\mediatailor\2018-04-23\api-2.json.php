<?php
// This file was auto-generated from sdk-root/src/data/mediatailor/2018-04-23/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-04-23', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'api.mediatailor', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceAbbreviation' => 'MediaTailor', 'serviceFullName' => 'AWS MediaTailor', 'serviceId' => 'MediaTailor', 'signatureVersion' => 'v4', 'signingName' => 'mediatailor', 'uid' => 'mediatailor-2018-04-23', ], 'operations' => [ 'ConfigureLogsForChannel' => [ 'name' => 'ConfigureLogsForChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/configureLogs/channel', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ConfigureLogsForChannelRequest', ], 'output' => [ 'shape' => 'ConfigureLogsForChannelResponse', ], ], 'ConfigureLogsForPlaybackConfiguration' => [ 'name' => 'ConfigureLogsForPlaybackConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/configureLogs/playbackConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ConfigureLogsForPlaybackConfigurationRequest', ], 'output' => [ 'shape' => 'ConfigureLogsForPlaybackConfigurationResponse', ], 'idempotent' => true, ], 'CreateChannel' => [ 'name' => 'CreateChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/channel/{ChannelName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateChannelRequest', ], 'output' => [ 'shape' => 'CreateChannelResponse', ], 'idempotent' => true, ], 'CreateLiveSource' => [ 'name' => 'CreateLiveSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/sourceLocation/{SourceLocationName}/liveSource/{LiveSourceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateLiveSourceRequest', ], 'output' => [ 'shape' => 'CreateLiveSourceResponse', ], 'idempotent' => true, ], 'CreatePrefetchSchedule' => [ 'name' => 'CreatePrefetchSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/prefetchSchedule/{PlaybackConfigurationName}/{Name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreatePrefetchScheduleRequest', ], 'output' => [ 'shape' => 'CreatePrefetchScheduleResponse', ], 'idempotent' => true, ], 'CreateProgram' => [ 'name' => 'CreateProgram', 'http' => [ 'method' => 'POST', 'requestUri' => '/channel/{ChannelName}/program/{ProgramName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateProgramRequest', ], 'output' => [ 'shape' => 'CreateProgramResponse', ], 'idempotent' => true, ], 'CreateSourceLocation' => [ 'name' => 'CreateSourceLocation', 'http' => [ 'method' => 'POST', 'requestUri' => '/sourceLocation/{SourceLocationName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSourceLocationRequest', ], 'output' => [ 'shape' => 'CreateSourceLocationResponse', ], 'idempotent' => true, ], 'CreateVodSource' => [ 'name' => 'CreateVodSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/sourceLocation/{SourceLocationName}/vodSource/{VodSourceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateVodSourceRequest', ], 'output' => [ 'shape' => 'CreateVodSourceResponse', ], 'idempotent' => true, ], 'DeleteChannel' => [ 'name' => 'DeleteChannel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channel/{ChannelName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteChannelRequest', ], 'output' => [ 'shape' => 'DeleteChannelResponse', ], 'idempotent' => true, ], 'DeleteChannelPolicy' => [ 'name' => 'DeleteChannelPolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channel/{ChannelName}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteChannelPolicyRequest', ], 'output' => [ 'shape' => 'DeleteChannelPolicyResponse', ], 'idempotent' => true, ], 'DeleteLiveSource' => [ 'name' => 'DeleteLiveSource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/sourceLocation/{SourceLocationName}/liveSource/{LiveSourceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteLiveSourceRequest', ], 'output' => [ 'shape' => 'DeleteLiveSourceResponse', ], 'idempotent' => true, ], 'DeletePlaybackConfiguration' => [ 'name' => 'DeletePlaybackConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/playbackConfiguration/{Name}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeletePlaybackConfigurationRequest', ], 'output' => [ 'shape' => 'DeletePlaybackConfigurationResponse', ], 'idempotent' => true, ], 'DeletePrefetchSchedule' => [ 'name' => 'DeletePrefetchSchedule', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/prefetchSchedule/{PlaybackConfigurationName}/{Name}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeletePrefetchScheduleRequest', ], 'output' => [ 'shape' => 'DeletePrefetchScheduleResponse', ], 'idempotent' => true, ], 'DeleteProgram' => [ 'name' => 'DeleteProgram', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/channel/{ChannelName}/program/{ProgramName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteProgramRequest', ], 'output' => [ 'shape' => 'DeleteProgramResponse', ], 'idempotent' => true, ], 'DeleteSourceLocation' => [ 'name' => 'DeleteSourceLocation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/sourceLocation/{SourceLocationName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteSourceLocationRequest', ], 'output' => [ 'shape' => 'DeleteSourceLocationResponse', ], 'idempotent' => true, ], 'DeleteVodSource' => [ 'name' => 'DeleteVodSource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/sourceLocation/{SourceLocationName}/vodSource/{VodSourceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteVodSourceRequest', ], 'output' => [ 'shape' => 'DeleteVodSourceResponse', ], 'idempotent' => true, ], 'DescribeChannel' => [ 'name' => 'DescribeChannel', 'http' => [ 'method' => 'GET', 'requestUri' => '/channel/{ChannelName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeChannelRequest', ], 'output' => [ 'shape' => 'DescribeChannelResponse', ], ], 'DescribeLiveSource' => [ 'name' => 'DescribeLiveSource', 'http' => [ 'method' => 'GET', 'requestUri' => '/sourceLocation/{SourceLocationName}/liveSource/{LiveSourceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeLiveSourceRequest', ], 'output' => [ 'shape' => 'DescribeLiveSourceResponse', ], ], 'DescribeProgram' => [ 'name' => 'DescribeProgram', 'http' => [ 'method' => 'GET', 'requestUri' => '/channel/{ChannelName}/program/{ProgramName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeProgramRequest', ], 'output' => [ 'shape' => 'DescribeProgramResponse', ], ], 'DescribeSourceLocation' => [ 'name' => 'DescribeSourceLocation', 'http' => [ 'method' => 'GET', 'requestUri' => '/sourceLocation/{SourceLocationName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeSourceLocationRequest', ], 'output' => [ 'shape' => 'DescribeSourceLocationResponse', ], ], 'DescribeVodSource' => [ 'name' => 'DescribeVodSource', 'http' => [ 'method' => 'GET', 'requestUri' => '/sourceLocation/{SourceLocationName}/vodSource/{VodSourceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeVodSourceRequest', ], 'output' => [ 'shape' => 'DescribeVodSourceResponse', ], ], 'GetChannelPolicy' => [ 'name' => 'GetChannelPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/channel/{ChannelName}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetChannelPolicyRequest', ], 'output' => [ 'shape' => 'GetChannelPolicyResponse', ], ], 'GetChannelSchedule' => [ 'name' => 'GetChannelSchedule', 'http' => [ 'method' => 'GET', 'requestUri' => '/channel/{ChannelName}/schedule', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetChannelScheduleRequest', ], 'output' => [ 'shape' => 'GetChannelScheduleResponse', ], ], 'GetPlaybackConfiguration' => [ 'name' => 'GetPlaybackConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/playbackConfiguration/{Name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPlaybackConfigurationRequest', ], 'output' => [ 'shape' => 'GetPlaybackConfigurationResponse', ], ], 'GetPrefetchSchedule' => [ 'name' => 'GetPrefetchSchedule', 'http' => [ 'method' => 'GET', 'requestUri' => '/prefetchSchedule/{PlaybackConfigurationName}/{Name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPrefetchScheduleRequest', ], 'output' => [ 'shape' => 'GetPrefetchScheduleResponse', ], ], 'ListAlerts' => [ 'name' => 'ListAlerts', 'http' => [ 'method' => 'GET', 'requestUri' => '/alerts', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAlertsRequest', ], 'output' => [ 'shape' => 'ListAlertsResponse', ], ], 'ListChannels' => [ 'name' => 'ListChannels', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelsRequest', ], 'output' => [ 'shape' => 'ListChannelsResponse', ], ], 'ListLiveSources' => [ 'name' => 'ListLiveSources', 'http' => [ 'method' => 'GET', 'requestUri' => '/sourceLocation/{SourceLocationName}/liveSources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListLiveSourcesRequest', ], 'output' => [ 'shape' => 'ListLiveSourcesResponse', ], ], 'ListPlaybackConfigurations' => [ 'name' => 'ListPlaybackConfigurations', 'http' => [ 'method' => 'GET', 'requestUri' => '/playbackConfigurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPlaybackConfigurationsRequest', ], 'output' => [ 'shape' => 'ListPlaybackConfigurationsResponse', ], ], 'ListPrefetchSchedules' => [ 'name' => 'ListPrefetchSchedules', 'http' => [ 'method' => 'POST', 'requestUri' => '/prefetchSchedule/{PlaybackConfigurationName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPrefetchSchedulesRequest', ], 'output' => [ 'shape' => 'ListPrefetchSchedulesResponse', ], ], 'ListSourceLocations' => [ 'name' => 'ListSourceLocations', 'http' => [ 'method' => 'GET', 'requestUri' => '/sourceLocations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSourceLocationsRequest', ], 'output' => [ 'shape' => 'ListSourceLocationsResponse', ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], ], ], 'ListVodSources' => [ 'name' => 'ListVodSources', 'http' => [ 'method' => 'GET', 'requestUri' => '/sourceLocation/{SourceLocationName}/vodSources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListVodSourcesRequest', ], 'output' => [ 'shape' => 'ListVodSourcesResponse', ], ], 'PutChannelPolicy' => [ 'name' => 'PutChannelPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channel/{ChannelName}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutChannelPolicyRequest', ], 'output' => [ 'shape' => 'PutChannelPolicyResponse', ], 'idempotent' => true, ], 'PutPlaybackConfiguration' => [ 'name' => 'PutPlaybackConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/playbackConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutPlaybackConfigurationRequest', ], 'output' => [ 'shape' => 'PutPlaybackConfigurationResponse', ], 'idempotent' => true, ], 'StartChannel' => [ 'name' => 'StartChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channel/{ChannelName}/start', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartChannelRequest', ], 'output' => [ 'shape' => 'StartChannelResponse', ], 'idempotent' => true, ], 'StopChannel' => [ 'name' => 'StopChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channel/{ChannelName}/stop', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopChannelRequest', ], 'output' => [ 'shape' => 'StopChannelResponse', ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], ], 'idempotent' => true, ], 'UpdateChannel' => [ 'name' => 'UpdateChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channel/{ChannelName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateChannelRequest', ], 'output' => [ 'shape' => 'UpdateChannelResponse', ], 'idempotent' => true, ], 'UpdateLiveSource' => [ 'name' => 'UpdateLiveSource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/sourceLocation/{SourceLocationName}/liveSource/{LiveSourceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateLiveSourceRequest', ], 'output' => [ 'shape' => 'UpdateLiveSourceResponse', ], 'idempotent' => true, ], 'UpdateProgram' => [ 'name' => 'UpdateProgram', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channel/{ChannelName}/program/{ProgramName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateProgramRequest', ], 'output' => [ 'shape' => 'UpdateProgramResponse', ], 'idempotent' => true, ], 'UpdateSourceLocation' => [ 'name' => 'UpdateSourceLocation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/sourceLocation/{SourceLocationName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSourceLocationRequest', ], 'output' => [ 'shape' => 'UpdateSourceLocationResponse', ], 'idempotent' => true, ], 'UpdateVodSource' => [ 'name' => 'UpdateVodSource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/sourceLocation/{SourceLocationName}/vodSource/{VodSourceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateVodSourceRequest', ], 'output' => [ 'shape' => 'UpdateVodSourceResponse', ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessConfiguration' => [ 'type' => 'structure', 'members' => [ 'AccessType' => [ 'shape' => 'AccessType', ], 'SecretsManagerAccessTokenConfiguration' => [ 'shape' => 'SecretsManagerAccessTokenConfiguration', ], ], ], 'AccessType' => [ 'type' => 'string', 'enum' => [ 'S3_SIGV4', 'SECRETS_MANAGER_ACCESS_TOKEN', 'AUTODETECT_SIGV4', ], ], 'AdBreak' => [ 'type' => 'structure', 'required' => [ 'OffsetMillis', ], 'members' => [ 'MessageType' => [ 'shape' => 'MessageType', ], 'OffsetMillis' => [ 'shape' => '__long', ], 'Slate' => [ 'shape' => 'SlateSource', ], 'SpliceInsertMessage' => [ 'shape' => 'SpliceInsertMessage', ], 'TimeSignalMessage' => [ 'shape' => 'TimeSignalMessage', ], 'AdBreakMetadata' => [ 'shape' => 'AdBreakMetadataList', ], ], ], 'AdBreakMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeyValuePair', ], ], 'AdBreakOpportunities' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdBreakOpportunity', ], ], 'AdBreakOpportunity' => [ 'type' => 'structure', 'required' => [ 'OffsetMillis', ], 'members' => [ 'OffsetMillis' => [ 'shape' => '__long', ], ], ], 'AdConditioningConfiguration' => [ 'type' => 'structure', 'required' => [ 'StreamingMediaFileConditioning', ], 'members' => [ 'StreamingMediaFileConditioning' => [ 'shape' => 'StreamingMediaFileConditioning', ], ], ], 'AdMarkerPassthrough' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => '__boolean', ], ], ], 'AdMarkupType' => [ 'type' => 'string', 'enum' => [ 'DATERANGE', 'SCTE35_ENHANCED', ], ], 'AdsInteractionExcludeEventType' => [ 'type' => 'string', 'enum' => [ 'AD_MARKER_FOUND', 'NON_AD_MARKER_FOUND', 'MAKING_ADS_REQUEST', 'MODIFIED_TARGET_URL', 'VAST_REDIRECT', 'EMPTY_VAST_RESPONSE', 'EMPTY_VMAP_RESPONSE', 'VAST_RESPONSE', 'REDIRECTED_VAST_RESPONSE', 'FILLED_AVAIL', 'FILLED_OVERLAY_AVAIL', 'BEACON_FIRED', 'WARNING_NO_ADVERTISEMENTS', 'WARNING_VPAID_AD_DROPPED', 'WARNING_URL_VARIABLE_SUBSTITUTION_FAILED', 'ERROR_UNKNOWN', 'ERROR_UNKNOWN_HOST', 'ERROR_DISALLOWED_HOST', 'ERROR_ADS_IO', 'ERROR_ADS_TIMEOUT', 'ERROR_ADS_RESPONSE_PARSE', 'ERROR_ADS_RESPONSE_UNKNOWN_ROOT_ELEMENT', 'ERROR_ADS_INVALID_RESPONSE', 'ERROR_VAST_REDIRECT_EMPTY_RESPONSE', 'ERROR_VAST_REDIRECT_MULTIPLE_VAST', 'ERROR_VAST_REDIRECT_FAILED', 'ERROR_VAST_MISSING_MEDIAFILES', 'ERROR_VAST_MISSING_CREATIVES', 'ERROR_VAST_MISSING_OVERLAYS', 'ERROR_VAST_MISSING_IMPRESSION', 'ERROR_VAST_INVALID_VAST_AD_TAG_URI', 'ERROR_VAST_MULTIPLE_TRACKING_EVENTS', 'ERROR_VAST_MULTIPLE_LINEAR', 'ERROR_VAST_INVALID_MEDIA_FILE', 'ERROR_FIRING_BEACON_FAILED', 'ERROR_PERSONALIZATION_DISABLED', 'VOD_TIME_BASED_AVAIL_PLAN_VAST_RESPONSE_FOR_OFFSET', 'VOD_TIME_BASED_AVAIL_PLAN_SUCCESS', 'VOD_TIME_BASED_AVAIL_PLAN_WARNING_NO_ADVERTISEMENTS', 'INTERSTITIAL_VOD_SUCCESS', 'INTERSTITIAL_VOD_FAILURE', ], ], 'AdsInteractionLog' => [ 'type' => 'structure', 'members' => [ 'PublishOptInEventTypes' => [ 'shape' => '__adsInteractionPublishOptInEventTypesList', ], 'ExcludeEventTypes' => [ 'shape' => '__adsInteractionExcludeEventTypesList', ], ], ], 'AdsInteractionPublishOptInEventType' => [ 'type' => 'string', 'enum' => [ 'RAW_ADS_RESPONSE', ], ], 'Alert' => [ 'type' => 'structure', 'required' => [ 'AlertCode', 'AlertMessage', 'LastModifiedTime', 'RelatedResourceArns', 'ResourceArn', ], 'members' => [ 'AlertCode' => [ 'shape' => '__string', ], 'AlertMessage' => [ 'shape' => '__string', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'RelatedResourceArns' => [ 'shape' => '__listOf__string', ], 'ResourceArn' => [ 'shape' => '__string', ], 'Category' => [ 'shape' => 'AlertCategory', ], ], ], 'AlertCategory' => [ 'type' => 'string', 'enum' => [ 'SCHEDULING_ERROR', 'PLAYBACK_WARNING', 'INFO', ], ], 'AlternateMedia' => [ 'type' => 'structure', 'members' => [ 'SourceLocationName' => [ 'shape' => '__string', ], 'LiveSourceName' => [ 'shape' => '__string', ], 'VodSourceName' => [ 'shape' => '__string', ], 'ClipRange' => [ 'shape' => 'ClipRange', ], 'ScheduledStartTimeMillis' => [ 'shape' => '__long', ], 'AdBreaks' => [ 'shape' => '__listOfAdBreak', ], 'DurationMillis' => [ 'shape' => '__long', ], ], ], 'AudienceMedia' => [ 'type' => 'structure', 'members' => [ 'Audience' => [ 'shape' => '__string', ], 'AlternateMedia' => [ 'shape' => '__listOfAlternateMedia', ], ], ], 'Audiences' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'AvailMatchingCriteria' => [ 'type' => 'structure', 'required' => [ 'DynamicVariable', 'Operator', ], 'members' => [ 'DynamicVariable' => [ 'shape' => '__string', ], 'Operator' => [ 'shape' => 'Operator', ], ], ], 'AvailSuppression' => [ 'type' => 'structure', 'members' => [ 'Mode' => [ 'shape' => 'Mode', ], 'Value' => [ 'shape' => '__string', ], 'FillPolicy' => [ 'shape' => 'FillPolicy', ], ], ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Bumper' => [ 'type' => 'structure', 'members' => [ 'EndUrl' => [ 'shape' => '__string', ], 'StartUrl' => [ 'shape' => '__string', ], ], ], 'CdnConfiguration' => [ 'type' => 'structure', 'members' => [ 'AdSegmentUrlPrefix' => [ 'shape' => '__string', ], 'ContentSegmentUrlPrefix' => [ 'shape' => '__string', ], ], ], 'Channel' => [ 'type' => 'structure', 'required' => [ 'Arn', 'ChannelName', 'ChannelState', 'Outputs', 'PlaybackMode', 'Tier', 'LogConfiguration', ], 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'ChannelName' => [ 'shape' => '__string', ], 'ChannelState' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'FillerSlate' => [ 'shape' => 'SlateSource', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'Outputs' => [ 'shape' => 'ResponseOutputs', ], 'PlaybackMode' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'Tier' => [ 'shape' => '__string', ], 'LogConfiguration' => [ 'shape' => 'LogConfigurationForChannel', ], 'Audiences' => [ 'shape' => 'Audiences', ], ], ], 'ChannelState' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'STOPPED', ], ], 'ClipRange' => [ 'type' => 'structure', 'members' => [ 'EndOffsetMillis' => [ 'shape' => '__long', 'box' => true, ], 'StartOffsetMillis' => [ 'shape' => '__long', ], ], ], 'ConfigurationAliasesRequest' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => '__mapOf__string', ], ], 'ConfigurationAliasesResponse' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => '__mapOf__string', ], ], 'ConfigureLogsForChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', 'LogTypes', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', ], 'LogTypes' => [ 'shape' => 'LogTypes', ], ], ], 'ConfigureLogsForChannelResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelName' => [ 'shape' => '__string', ], 'LogTypes' => [ 'shape' => 'LogTypes', ], ], ], 'ConfigureLogsForPlaybackConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'PercentEnabled', 'PlaybackConfigurationName', ], 'members' => [ 'PercentEnabled' => [ 'shape' => '__integer', ], 'PlaybackConfigurationName' => [ 'shape' => '__string', ], 'EnabledLoggingStrategies' => [ 'shape' => '__listOfLoggingStrategies', ], 'AdsInteractionLog' => [ 'shape' => 'AdsInteractionLog', ], 'ManifestServiceInteractionLog' => [ 'shape' => 'ManifestServiceInteractionLog', ], ], ], 'ConfigureLogsForPlaybackConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'PercentEnabled', ], 'members' => [ 'PercentEnabled' => [ 'shape' => '__integer', ], 'PlaybackConfigurationName' => [ 'shape' => '__string', ], 'EnabledLoggingStrategies' => [ 'shape' => '__listOfLoggingStrategies', ], 'AdsInteractionLog' => [ 'shape' => 'AdsInteractionLog', ], 'ManifestServiceInteractionLog' => [ 'shape' => 'ManifestServiceInteractionLog', ], ], ], 'CreateChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', 'Outputs', 'PlaybackMode', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'FillerSlate' => [ 'shape' => 'SlateSource', ], 'Outputs' => [ 'shape' => 'RequestOutputs', ], 'PlaybackMode' => [ 'shape' => 'PlaybackMode', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'Tier' => [ 'shape' => 'Tier', ], 'TimeShiftConfiguration' => [ 'shape' => 'TimeShiftConfiguration', ], 'Audiences' => [ 'shape' => 'Audiences', ], ], ], 'CreateChannelResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'ChannelName' => [ 'shape' => '__string', ], 'ChannelState' => [ 'shape' => 'ChannelState', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'FillerSlate' => [ 'shape' => 'SlateSource', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'Outputs' => [ 'shape' => 'ResponseOutputs', ], 'PlaybackMode' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'Tier' => [ 'shape' => '__string', ], 'TimeShiftConfiguration' => [ 'shape' => 'TimeShiftConfiguration', ], 'Audiences' => [ 'shape' => 'Audiences', ], ], ], 'CreateLiveSourceRequest' => [ 'type' => 'structure', 'required' => [ 'HttpPackageConfigurations', 'LiveSourceName', 'SourceLocationName', ], 'members' => [ 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'LiveSourceName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'LiveSourceName', ], 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'CreateLiveSourceResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'LiveSourceName' => [ 'shape' => '__string', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'CreatePrefetchScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'PlaybackConfigurationName', ], 'members' => [ 'Consumption' => [ 'shape' => 'PrefetchConsumption', ], 'Name' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'Name', ], 'PlaybackConfigurationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'PlaybackConfigurationName', ], 'Retrieval' => [ 'shape' => 'PrefetchRetrieval', ], 'RecurringPrefetchConfiguration' => [ 'shape' => 'RecurringPrefetchConfiguration', ], 'ScheduleType' => [ 'shape' => 'PrefetchScheduleType', ], 'StreamId' => [ 'shape' => '__string', ], ], ], 'CreatePrefetchScheduleResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'Consumption' => [ 'shape' => 'PrefetchConsumption', ], 'Name' => [ 'shape' => '__string', ], 'PlaybackConfigurationName' => [ 'shape' => '__string', ], 'Retrieval' => [ 'shape' => 'PrefetchRetrieval', ], 'RecurringPrefetchConfiguration' => [ 'shape' => 'RecurringPrefetchConfiguration', ], 'ScheduleType' => [ 'shape' => 'PrefetchScheduleType', ], 'StreamId' => [ 'shape' => '__string', ], ], ], 'CreateProgramRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', 'ProgramName', 'ScheduleConfiguration', 'SourceLocationName', ], 'members' => [ 'AdBreaks' => [ 'shape' => '__listOfAdBreak', ], 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'LiveSourceName' => [ 'shape' => '__string', ], 'ProgramName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ProgramName', ], 'ScheduleConfiguration' => [ 'shape' => 'ScheduleConfiguration', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'VodSourceName' => [ 'shape' => '__string', ], 'AudienceMedia' => [ 'shape' => '__listOfAudienceMedia', ], ], ], 'CreateProgramResponse' => [ 'type' => 'structure', 'members' => [ 'AdBreaks' => [ 'shape' => '__listOfAdBreak', ], 'Arn' => [ 'shape' => '__string', ], 'ChannelName' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'LiveSourceName' => [ 'shape' => '__string', ], 'ProgramName' => [ 'shape' => '__string', ], 'ScheduledStartTime' => [ 'shape' => '__timestampUnix', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'VodSourceName' => [ 'shape' => '__string', ], 'ClipRange' => [ 'shape' => 'ClipRange', ], 'DurationMillis' => [ 'shape' => '__long', ], 'AudienceMedia' => [ 'shape' => '__listOfAudienceMedia', ], ], ], 'CreateSourceLocationRequest' => [ 'type' => 'structure', 'required' => [ 'HttpConfiguration', 'SourceLocationName', ], 'members' => [ 'AccessConfiguration' => [ 'shape' => 'AccessConfiguration', ], 'DefaultSegmentDeliveryConfiguration' => [ 'shape' => 'DefaultSegmentDeliveryConfiguration', ], 'HttpConfiguration' => [ 'shape' => 'HttpConfiguration', ], 'SegmentDeliveryConfigurations' => [ 'shape' => '__listOfSegmentDeliveryConfiguration', ], 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'CreateSourceLocationResponse' => [ 'type' => 'structure', 'members' => [ 'AccessConfiguration' => [ 'shape' => 'AccessConfiguration', ], 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'DefaultSegmentDeliveryConfiguration' => [ 'shape' => 'DefaultSegmentDeliveryConfiguration', ], 'HttpConfiguration' => [ 'shape' => 'HttpConfiguration', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'SegmentDeliveryConfigurations' => [ 'shape' => '__listOfSegmentDeliveryConfiguration', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'CreateVodSourceRequest' => [ 'type' => 'structure', 'required' => [ 'HttpPackageConfigurations', 'SourceLocationName', 'VodSourceName', ], 'members' => [ 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'VodSourceName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'VodSourceName', ], ], ], 'CreateVodSourceResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'VodSourceName' => [ 'shape' => '__string', ], ], ], 'DashConfiguration' => [ 'type' => 'structure', 'members' => [ 'ManifestEndpointPrefix' => [ 'shape' => '__string', ], 'MpdLocation' => [ 'shape' => '__string', ], 'OriginManifestType' => [ 'shape' => 'OriginManifestType', ], ], ], 'DashConfigurationForPut' => [ 'type' => 'structure', 'members' => [ 'MpdLocation' => [ 'shape' => '__string', ], 'OriginManifestType' => [ 'shape' => 'OriginManifestType', ], ], ], 'DashPlaylistSettings' => [ 'type' => 'structure', 'members' => [ 'ManifestWindowSeconds' => [ 'shape' => '__integer', ], 'MinBufferTimeSeconds' => [ 'shape' => '__integer', ], 'MinUpdatePeriodSeconds' => [ 'shape' => '__integer', ], 'SuggestedPresentationDelaySeconds' => [ 'shape' => '__integer', ], ], ], 'DefaultSegmentDeliveryConfiguration' => [ 'type' => 'structure', 'members' => [ 'BaseUrl' => [ 'shape' => '__string', ], ], ], 'DeleteChannelPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], ], ], 'DeleteChannelPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], ], ], 'DeleteChannelResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteLiveSourceRequest' => [ 'type' => 'structure', 'required' => [ 'LiveSourceName', 'SourceLocationName', ], 'members' => [ 'LiveSourceName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'LiveSourceName', ], 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], ], ], 'DeleteLiveSourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePlaybackConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'DeletePlaybackConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePrefetchScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'PlaybackConfigurationName', ], 'members' => [ 'Name' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'Name', ], 'PlaybackConfigurationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'PlaybackConfigurationName', ], ], ], 'DeletePrefetchScheduleResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteProgramRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', 'ProgramName', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'ProgramName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ProgramName', ], ], ], 'DeleteProgramResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSourceLocationRequest' => [ 'type' => 'structure', 'required' => [ 'SourceLocationName', ], 'members' => [ 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], ], ], 'DeleteSourceLocationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteVodSourceRequest' => [ 'type' => 'structure', 'required' => [ 'SourceLocationName', 'VodSourceName', ], 'members' => [ 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], 'VodSourceName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'VodSourceName', ], ], ], 'DeleteVodSourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], ], ], 'DescribeChannelResponse' => [ 'type' => 'structure', 'required' => [ 'LogConfiguration', ], 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'ChannelName' => [ 'shape' => '__string', ], 'ChannelState' => [ 'shape' => 'ChannelState', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'FillerSlate' => [ 'shape' => 'SlateSource', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'Outputs' => [ 'shape' => 'ResponseOutputs', ], 'PlaybackMode' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'Tier' => [ 'shape' => '__string', ], 'LogConfiguration' => [ 'shape' => 'LogConfigurationForChannel', ], 'TimeShiftConfiguration' => [ 'shape' => 'TimeShiftConfiguration', ], 'Audiences' => [ 'shape' => 'Audiences', ], ], ], 'DescribeLiveSourceRequest' => [ 'type' => 'structure', 'required' => [ 'LiveSourceName', 'SourceLocationName', ], 'members' => [ 'LiveSourceName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'LiveSourceName', ], 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], ], ], 'DescribeLiveSourceResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'LiveSourceName' => [ 'shape' => '__string', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'DescribeProgramRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', 'ProgramName', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'ProgramName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ProgramName', ], ], ], 'DescribeProgramResponse' => [ 'type' => 'structure', 'members' => [ 'AdBreaks' => [ 'shape' => '__listOfAdBreak', ], 'Arn' => [ 'shape' => '__string', ], 'ChannelName' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'LiveSourceName' => [ 'shape' => '__string', ], 'ProgramName' => [ 'shape' => '__string', ], 'ScheduledStartTime' => [ 'shape' => '__timestampUnix', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'VodSourceName' => [ 'shape' => '__string', ], 'ClipRange' => [ 'shape' => 'ClipRange', ], 'DurationMillis' => [ 'shape' => 'Long', ], 'AudienceMedia' => [ 'shape' => '__listOfAudienceMedia', ], ], ], 'DescribeSourceLocationRequest' => [ 'type' => 'structure', 'required' => [ 'SourceLocationName', ], 'members' => [ 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], ], ], 'DescribeSourceLocationResponse' => [ 'type' => 'structure', 'members' => [ 'AccessConfiguration' => [ 'shape' => 'AccessConfiguration', ], 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'DefaultSegmentDeliveryConfiguration' => [ 'shape' => 'DefaultSegmentDeliveryConfiguration', ], 'HttpConfiguration' => [ 'shape' => 'HttpConfiguration', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'SegmentDeliveryConfigurations' => [ 'shape' => '__listOfSegmentDeliveryConfiguration', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'DescribeVodSourceRequest' => [ 'type' => 'structure', 'required' => [ 'SourceLocationName', 'VodSourceName', ], 'members' => [ 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], 'VodSourceName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'VodSourceName', ], ], ], 'DescribeVodSourceResponse' => [ 'type' => 'structure', 'members' => [ 'AdBreakOpportunities' => [ 'shape' => 'AdBreakOpportunities', ], 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'VodSourceName' => [ 'shape' => '__string', ], ], ], 'FillPolicy' => [ 'type' => 'string', 'enum' => [ 'FULL_AVAIL_ONLY', 'PARTIAL_AVAIL', ], ], 'GetChannelPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], ], ], 'GetChannelPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => '__string', ], ], ], 'GetChannelScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'DurationMinutes' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'durationMinutes', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'Audience' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'audience', ], ], ], 'GetChannelScheduleResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => '__listOfScheduleEntry', ], 'NextToken' => [ 'shape' => '__string', ], ], ], 'GetPlaybackConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'GetPlaybackConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'AdDecisionServerUrl' => [ 'shape' => '__string', ], 'AvailSuppression' => [ 'shape' => 'AvailSuppression', ], 'Bumper' => [ 'shape' => 'Bumper', ], 'CdnConfiguration' => [ 'shape' => 'CdnConfiguration', ], 'ConfigurationAliases' => [ 'shape' => 'ConfigurationAliasesResponse', ], 'DashConfiguration' => [ 'shape' => 'DashConfiguration', ], 'HlsConfiguration' => [ 'shape' => 'HlsConfiguration', ], 'InsertionMode' => [ 'shape' => 'InsertionMode', ], 'LivePreRollConfiguration' => [ 'shape' => 'LivePreRollConfiguration', ], 'LogConfiguration' => [ 'shape' => 'LogConfiguration', ], 'ManifestProcessingRules' => [ 'shape' => 'ManifestProcessingRules', ], 'Name' => [ 'shape' => '__string', ], 'PersonalizationThresholdSeconds' => [ 'shape' => '__integerMin1', ], 'PlaybackConfigurationArn' => [ 'shape' => '__string', ], 'PlaybackEndpointPrefix' => [ 'shape' => '__string', ], 'SessionInitializationEndpointPrefix' => [ 'shape' => '__string', ], 'SlateAdUrl' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'TranscodeProfileName' => [ 'shape' => '__string', ], 'VideoContentSourceUrl' => [ 'shape' => '__string', ], 'AdConditioningConfiguration' => [ 'shape' => 'AdConditioningConfiguration', ], ], ], 'GetPrefetchScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'PlaybackConfigurationName', ], 'members' => [ 'Name' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'Name', ], 'PlaybackConfigurationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'PlaybackConfigurationName', ], ], ], 'GetPrefetchScheduleResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'Consumption' => [ 'shape' => 'PrefetchConsumption', ], 'Name' => [ 'shape' => '__string', ], 'PlaybackConfigurationName' => [ 'shape' => '__string', ], 'Retrieval' => [ 'shape' => 'PrefetchRetrieval', ], 'ScheduleType' => [ 'shape' => 'PrefetchScheduleType', ], 'RecurringPrefetchConfiguration' => [ 'shape' => 'RecurringPrefetchConfiguration', ], 'StreamId' => [ 'shape' => '__string', ], ], ], 'HlsConfiguration' => [ 'type' => 'structure', 'members' => [ 'ManifestEndpointPrefix' => [ 'shape' => '__string', ], ], ], 'HlsPlaylistSettings' => [ 'type' => 'structure', 'members' => [ 'ManifestWindowSeconds' => [ 'shape' => '__integer', ], 'AdMarkupType' => [ 'shape' => 'adMarkupTypes', ], ], ], 'HttpConfiguration' => [ 'type' => 'structure', 'required' => [ 'BaseUrl', ], 'members' => [ 'BaseUrl' => [ 'shape' => '__string', ], ], ], 'HttpPackageConfiguration' => [ 'type' => 'structure', 'required' => [ 'Path', 'SourceGroup', 'Type', ], 'members' => [ 'Path' => [ 'shape' => '__string', ], 'SourceGroup' => [ 'shape' => '__string', ], 'Type' => [ 'shape' => 'Type', ], ], ], 'HttpPackageConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'HttpPackageConfiguration', ], ], 'InsertionMode' => [ 'type' => 'string', 'enum' => [ 'STITCHED_ONLY', 'PLAYER_SELECT', ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'KeyValuePair' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'ListAlertsRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'ResourceArn' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'resourceArn', ], ], ], 'ListAlertsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => '__listOfAlert', ], 'NextToken' => [ 'shape' => '__string', ], ], ], 'ListChannelsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListChannelsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => '__listOfChannel', ], 'NextToken' => [ 'shape' => '__string', ], ], ], 'ListLiveSourcesRequest' => [ 'type' => 'structure', 'required' => [ 'SourceLocationName', ], 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], ], ], 'ListLiveSourcesResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => '__listOfLiveSource', ], 'NextToken' => [ 'shape' => '__string', ], ], ], 'ListPlaybackConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListPlaybackConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => '__listOfPlaybackConfiguration', ], 'NextToken' => [ 'shape' => '__string', ], ], ], 'ListPrefetchScheduleType' => [ 'type' => 'string', 'enum' => [ 'SINGLE', 'RECURRING', 'ALL', ], ], 'ListPrefetchSchedulesRequest' => [ 'type' => 'structure', 'required' => [ 'PlaybackConfigurationName', ], 'members' => [ 'MaxResults' => [ 'shape' => '__integerMin1Max100', ], 'NextToken' => [ 'shape' => '__string', ], 'PlaybackConfigurationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'PlaybackConfigurationName', ], 'ScheduleType' => [ 'shape' => 'ListPrefetchScheduleType', ], 'StreamId' => [ 'shape' => '__string', ], ], ], 'ListPrefetchSchedulesResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => '__listOfPrefetchSchedule', ], 'NextToken' => [ 'shape' => '__string', ], ], ], 'ListSourceLocationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListSourceLocationsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => '__listOfSourceLocation', ], 'NextToken' => [ 'shape' => '__string', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'ListVodSourcesRequest' => [ 'type' => 'structure', 'required' => [ 'SourceLocationName', ], 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], ], ], 'ListVodSourcesResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => '__listOfVodSource', ], 'NextToken' => [ 'shape' => '__string', ], ], ], 'LivePreRollConfiguration' => [ 'type' => 'structure', 'members' => [ 'AdDecisionServerUrl' => [ 'shape' => '__string', ], 'MaxDurationSeconds' => [ 'shape' => '__integer', ], ], ], 'LiveSource' => [ 'type' => 'structure', 'required' => [ 'Arn', 'HttpPackageConfigurations', 'LiveSourceName', 'SourceLocationName', ], 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'LiveSourceName' => [ 'shape' => '__string', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'LogConfiguration' => [ 'type' => 'structure', 'required' => [ 'PercentEnabled', 'EnabledLoggingStrategies', ], 'members' => [ 'PercentEnabled' => [ 'shape' => '__integer', ], 'EnabledLoggingStrategies' => [ 'shape' => '__listOfLoggingStrategies', ], 'AdsInteractionLog' => [ 'shape' => 'AdsInteractionLog', ], 'ManifestServiceInteractionLog' => [ 'shape' => 'ManifestServiceInteractionLog', ], ], ], 'LogConfigurationForChannel' => [ 'type' => 'structure', 'members' => [ 'LogTypes' => [ 'shape' => 'LogTypes', ], ], ], 'LogType' => [ 'type' => 'string', 'enum' => [ 'AS_RUN', ], ], 'LogTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogType', ], ], 'LoggingStrategy' => [ 'type' => 'string', 'enum' => [ 'VENDED_LOGS', 'LEGACY_CLOUDWATCH', ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'ManifestProcessingRules' => [ 'type' => 'structure', 'members' => [ 'AdMarkerPassthrough' => [ 'shape' => 'AdMarkerPassthrough', ], ], ], 'ManifestServiceExcludeEventType' => [ 'type' => 'string', 'enum' => [ 'GENERATED_MANIFEST', 'ORIGIN_MANIFEST', 'SESSION_INITIALIZED', 'TRACKING_RESPONSE', 'CONFIG_SYNTAX_ERROR', 'CONFIG_SECURITY_ERROR', 'UNKNOWN_HOST', 'TIMEOUT_ERROR', 'CONNECTION_ERROR', 'IO_ERROR', 'UNKNOWN_ERROR', 'HOST_DISALLOWED', 'PARSING_ERROR', 'MANIFEST_ERROR', 'NO_MASTER_OR_MEDIA_PLAYLIST', 'NO_MASTER_PLAYLIST', 'NO_MEDIA_PLAYLIST', 'INCOMPATIBLE_HLS_VERSION', 'SCTE35_PARSING_ERROR', 'INVALID_SINGLE_PERIOD_DASH_MANIFEST', 'UNSUPPORTED_SINGLE_PERIOD_DASH_MANIFEST', 'LAST_PERIOD_MISSING_AUDIO', 'LAST_PERIOD_MISSING_AUDIO_WARNING', 'ERROR_ORIGIN_PREFIX_INTERPOLATION', 'ERROR_ADS_INTERPOLATION', 'ERROR_LIVE_PRE_ROLL_ADS_INTERPOLATION', 'ERROR_CDN_AD_SEGMENT_INTERPOLATION', 'ERROR_CDN_CONTENT_SEGMENT_INTERPOLATION', 'ERROR_SLATE_AD_URL_INTERPOLATION', 'ERROR_PROFILE_NAME_INTERPOLATION', 'ERROR_BUMPER_START_INTERPOLATION', 'ERROR_BUMPER_END_INTERPOLATION', ], ], 'ManifestServiceInteractionLog' => [ 'type' => 'structure', 'members' => [ 'ExcludeEventTypes' => [ 'shape' => '__manifestServiceExcludeEventTypesList', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MessageType' => [ 'type' => 'string', 'enum' => [ 'SPLICE_INSERT', 'TIME_SIGNAL', ], ], 'Mode' => [ 'type' => 'string', 'enum' => [ 'OFF', 'BEHIND_LIVE_EDGE', 'AFTER_LIVE_EDGE', ], ], 'Operator' => [ 'type' => 'string', 'enum' => [ 'EQUALS', ], ], 'OriginManifestType' => [ 'type' => 'string', 'enum' => [ 'SINGLE_PERIOD', 'MULTI_PERIOD', ], ], 'PlaybackConfiguration' => [ 'type' => 'structure', 'members' => [ 'AdDecisionServerUrl' => [ 'shape' => '__string', ], 'AvailSuppression' => [ 'shape' => 'AvailSuppression', ], 'Bumper' => [ 'shape' => 'Bumper', ], 'CdnConfiguration' => [ 'shape' => 'CdnConfiguration', ], 'ConfigurationAliases' => [ 'shape' => 'ConfigurationAliasesResponse', ], 'DashConfiguration' => [ 'shape' => 'DashConfiguration', ], 'HlsConfiguration' => [ 'shape' => 'HlsConfiguration', ], 'InsertionMode' => [ 'shape' => 'InsertionMode', ], 'LivePreRollConfiguration' => [ 'shape' => 'LivePreRollConfiguration', ], 'LogConfiguration' => [ 'shape' => 'LogConfiguration', ], 'ManifestProcessingRules' => [ 'shape' => 'ManifestProcessingRules', ], 'Name' => [ 'shape' => '__string', ], 'PersonalizationThresholdSeconds' => [ 'shape' => '__integerMin1', ], 'PlaybackConfigurationArn' => [ 'shape' => '__string', ], 'PlaybackEndpointPrefix' => [ 'shape' => '__string', ], 'SessionInitializationEndpointPrefix' => [ 'shape' => '__string', ], 'SlateAdUrl' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'TranscodeProfileName' => [ 'shape' => '__string', ], 'VideoContentSourceUrl' => [ 'shape' => '__string', ], 'AdConditioningConfiguration' => [ 'shape' => 'AdConditioningConfiguration', ], ], ], 'PlaybackMode' => [ 'type' => 'string', 'enum' => [ 'LOOP', 'LINEAR', ], ], 'PrefetchConsumption' => [ 'type' => 'structure', 'required' => [ 'EndTime', ], 'members' => [ 'AvailMatchingCriteria' => [ 'shape' => '__listOfAvailMatchingCriteria', ], 'EndTime' => [ 'shape' => '__timestampUnix', ], 'StartTime' => [ 'shape' => '__timestampUnix', ], ], ], 'PrefetchRetrieval' => [ 'type' => 'structure', 'required' => [ 'EndTime', ], 'members' => [ 'DynamicVariables' => [ 'shape' => '__mapOf__string', ], 'EndTime' => [ 'shape' => '__timestampUnix', ], 'StartTime' => [ 'shape' => '__timestampUnix', ], 'TrafficShapingType' => [ 'shape' => 'TrafficShapingType', ], 'TrafficShapingRetrievalWindow' => [ 'shape' => 'TrafficShapingRetrievalWindow', ], ], ], 'PrefetchSchedule' => [ 'type' => 'structure', 'required' => [ 'Arn', 'Name', 'PlaybackConfigurationName', ], 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'Consumption' => [ 'shape' => 'PrefetchConsumption', ], 'Name' => [ 'shape' => '__string', ], 'PlaybackConfigurationName' => [ 'shape' => '__string', ], 'Retrieval' => [ 'shape' => 'PrefetchRetrieval', ], 'ScheduleType' => [ 'shape' => 'PrefetchScheduleType', ], 'RecurringPrefetchConfiguration' => [ 'shape' => 'RecurringPrefetchConfiguration', ], 'StreamId' => [ 'shape' => '__string', ], ], ], 'PrefetchScheduleType' => [ 'type' => 'string', 'enum' => [ 'SINGLE', 'RECURRING', ], ], 'PutChannelPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', 'Policy', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'Policy' => [ 'shape' => '__string', ], ], ], 'PutChannelPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutPlaybackConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'AdDecisionServerUrl' => [ 'shape' => '__string', ], 'AvailSuppression' => [ 'shape' => 'AvailSuppression', ], 'Bumper' => [ 'shape' => 'Bumper', ], 'CdnConfiguration' => [ 'shape' => 'CdnConfiguration', ], 'ConfigurationAliases' => [ 'shape' => 'ConfigurationAliasesRequest', ], 'DashConfiguration' => [ 'shape' => 'DashConfigurationForPut', ], 'InsertionMode' => [ 'shape' => 'InsertionMode', ], 'LivePreRollConfiguration' => [ 'shape' => 'LivePreRollConfiguration', ], 'ManifestProcessingRules' => [ 'shape' => 'ManifestProcessingRules', ], 'Name' => [ 'shape' => '__string', ], 'PersonalizationThresholdSeconds' => [ 'shape' => '__integerMin1', ], 'SlateAdUrl' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'TranscodeProfileName' => [ 'shape' => '__string', ], 'VideoContentSourceUrl' => [ 'shape' => '__string', ], 'AdConditioningConfiguration' => [ 'shape' => 'AdConditioningConfiguration', ], ], ], 'PutPlaybackConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'AdDecisionServerUrl' => [ 'shape' => '__string', ], 'AvailSuppression' => [ 'shape' => 'AvailSuppression', ], 'Bumper' => [ 'shape' => 'Bumper', ], 'CdnConfiguration' => [ 'shape' => 'CdnConfiguration', ], 'ConfigurationAliases' => [ 'shape' => 'ConfigurationAliasesResponse', ], 'DashConfiguration' => [ 'shape' => 'DashConfiguration', ], 'HlsConfiguration' => [ 'shape' => 'HlsConfiguration', ], 'InsertionMode' => [ 'shape' => 'InsertionMode', ], 'LivePreRollConfiguration' => [ 'shape' => 'LivePreRollConfiguration', ], 'LogConfiguration' => [ 'shape' => 'LogConfiguration', ], 'ManifestProcessingRules' => [ 'shape' => 'ManifestProcessingRules', ], 'Name' => [ 'shape' => '__string', ], 'PersonalizationThresholdSeconds' => [ 'shape' => '__integerMin1', ], 'PlaybackConfigurationArn' => [ 'shape' => '__string', ], 'PlaybackEndpointPrefix' => [ 'shape' => '__string', ], 'SessionInitializationEndpointPrefix' => [ 'shape' => '__string', ], 'SlateAdUrl' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'TranscodeProfileName' => [ 'shape' => '__string', ], 'VideoContentSourceUrl' => [ 'shape' => '__string', ], 'AdConditioningConfiguration' => [ 'shape' => 'AdConditioningConfiguration', ], ], ], 'RecurringConsumption' => [ 'type' => 'structure', 'members' => [ 'RetrievedAdExpirationSeconds' => [ 'shape' => '__integer', ], 'AvailMatchingCriteria' => [ 'shape' => '__listOfAvailMatchingCriteria', ], ], ], 'RecurringPrefetchConfiguration' => [ 'type' => 'structure', 'required' => [ 'EndTime', 'RecurringConsumption', 'RecurringRetrieval', ], 'members' => [ 'StartTime' => [ 'shape' => '__timestampUnix', ], 'EndTime' => [ 'shape' => '__timestampUnix', ], 'RecurringConsumption' => [ 'shape' => 'RecurringConsumption', ], 'RecurringRetrieval' => [ 'shape' => 'RecurringRetrieval', ], ], ], 'RecurringRetrieval' => [ 'type' => 'structure', 'members' => [ 'DynamicVariables' => [ 'shape' => '__mapOf__string', ], 'DelayAfterAvailEndSeconds' => [ 'shape' => '__integer', ], 'TrafficShapingType' => [ 'shape' => 'TrafficShapingType', ], 'TrafficShapingRetrievalWindow' => [ 'shape' => 'TrafficShapingRetrievalWindow', ], ], ], 'RelativePosition' => [ 'type' => 'string', 'enum' => [ 'BEFORE_PROGRAM', 'AFTER_PROGRAM', ], ], 'RequestOutputItem' => [ 'type' => 'structure', 'required' => [ 'ManifestName', 'SourceGroup', ], 'members' => [ 'DashPlaylistSettings' => [ 'shape' => 'DashPlaylistSettings', ], 'HlsPlaylistSettings' => [ 'shape' => 'HlsPlaylistSettings', ], 'ManifestName' => [ 'shape' => '__string', ], 'SourceGroup' => [ 'shape' => '__string', ], ], ], 'RequestOutputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'RequestOutputItem', ], ], 'ResponseOutputItem' => [ 'type' => 'structure', 'required' => [ 'ManifestName', 'PlaybackUrl', 'SourceGroup', ], 'members' => [ 'DashPlaylistSettings' => [ 'shape' => 'DashPlaylistSettings', ], 'HlsPlaylistSettings' => [ 'shape' => 'HlsPlaylistSettings', ], 'ManifestName' => [ 'shape' => '__string', ], 'PlaybackUrl' => [ 'shape' => '__string', ], 'SourceGroup' => [ 'shape' => '__string', ], ], ], 'ResponseOutputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResponseOutputItem', ], ], 'ScheduleAdBreak' => [ 'type' => 'structure', 'members' => [ 'ApproximateDurationSeconds' => [ 'shape' => '__long', ], 'ApproximateStartTime' => [ 'shape' => '__timestampUnix', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'VodSourceName' => [ 'shape' => '__string', ], ], ], 'ScheduleConfiguration' => [ 'type' => 'structure', 'required' => [ 'Transition', ], 'members' => [ 'Transition' => [ 'shape' => 'Transition', ], 'ClipRange' => [ 'shape' => 'ClipRange', ], ], ], 'ScheduleEntry' => [ 'type' => 'structure', 'required' => [ 'Arn', 'ChannelName', 'ProgramName', 'SourceLocationName', ], 'members' => [ 'ApproximateDurationSeconds' => [ 'shape' => '__long', ], 'ApproximateStartTime' => [ 'shape' => '__timestampUnix', ], 'Arn' => [ 'shape' => '__string', ], 'ChannelName' => [ 'shape' => '__string', ], 'LiveSourceName' => [ 'shape' => '__string', ], 'ProgramName' => [ 'shape' => '__string', ], 'ScheduleAdBreaks' => [ 'shape' => '__listOfScheduleAdBreak', ], 'ScheduleEntryType' => [ 'shape' => 'ScheduleEntryType', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'VodSourceName' => [ 'shape' => '__string', ], 'Audiences' => [ 'shape' => 'Audiences', ], ], ], 'ScheduleEntryType' => [ 'type' => 'string', 'enum' => [ 'PROGRAM', 'FILLER_SLATE', 'ALTERNATE_MEDIA', ], ], 'SecretsManagerAccessTokenConfiguration' => [ 'type' => 'structure', 'members' => [ 'HeaderName' => [ 'shape' => '__string', ], 'SecretArn' => [ 'shape' => '__string', ], 'SecretStringKey' => [ 'shape' => '__string', ], ], ], 'SegmentDeliveryConfiguration' => [ 'type' => 'structure', 'members' => [ 'BaseUrl' => [ 'shape' => '__string', ], 'Name' => [ 'shape' => '__string', ], ], ], 'SegmentationDescriptor' => [ 'type' => 'structure', 'members' => [ 'SegmentationEventId' => [ 'shape' => 'Integer', ], 'SegmentationUpidType' => [ 'shape' => 'Integer', ], 'SegmentationUpid' => [ 'shape' => 'String', ], 'SegmentationTypeId' => [ 'shape' => 'Integer', ], 'SegmentNum' => [ 'shape' => 'Integer', ], 'SegmentsExpected' => [ 'shape' => 'Integer', ], 'SubSegmentNum' => [ 'shape' => 'Integer', ], 'SubSegmentsExpected' => [ 'shape' => 'Integer', ], ], ], 'SegmentationDescriptorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SegmentationDescriptor', ], ], 'SlateSource' => [ 'type' => 'structure', 'members' => [ 'SourceLocationName' => [ 'shape' => '__string', ], 'VodSourceName' => [ 'shape' => '__string', ], ], ], 'SourceLocation' => [ 'type' => 'structure', 'required' => [ 'Arn', 'HttpConfiguration', 'SourceLocationName', ], 'members' => [ 'AccessConfiguration' => [ 'shape' => 'AccessConfiguration', ], 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'DefaultSegmentDeliveryConfiguration' => [ 'shape' => 'DefaultSegmentDeliveryConfiguration', ], 'HttpConfiguration' => [ 'shape' => 'HttpConfiguration', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'SegmentDeliveryConfigurations' => [ 'shape' => '__listOfSegmentDeliveryConfiguration', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'SpliceInsertMessage' => [ 'type' => 'structure', 'members' => [ 'AvailNum' => [ 'shape' => '__integer', ], 'AvailsExpected' => [ 'shape' => '__integer', ], 'SpliceEventId' => [ 'shape' => '__integer', ], 'UniqueProgramId' => [ 'shape' => '__integer', ], ], ], 'StartChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], ], ], 'StartChannelResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], ], ], 'StopChannelResponse' => [ 'type' => 'structure', 'members' => [], ], 'StreamingMediaFileConditioning' => [ 'type' => 'string', 'enum' => [ 'TRANSCODE', 'NONE', ], ], 'String' => [ 'type' => 'string', ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'Tier' => [ 'type' => 'string', 'enum' => [ 'BASIC', 'STANDARD', ], ], 'TimeShiftConfiguration' => [ 'type' => 'structure', 'required' => [ 'MaxTimeDelaySeconds', ], 'members' => [ 'MaxTimeDelaySeconds' => [ 'shape' => '__integer', ], ], ], 'TimeSignalMessage' => [ 'type' => 'structure', 'members' => [ 'SegmentationDescriptors' => [ 'shape' => 'SegmentationDescriptorList', ], ], ], 'TrafficShapingRetrievalWindow' => [ 'type' => 'structure', 'members' => [ 'RetrievalWindowDurationSeconds' => [ 'shape' => '__integer', ], ], ], 'TrafficShapingType' => [ 'type' => 'string', 'enum' => [ 'RETRIEVAL_WINDOW', ], ], 'Transition' => [ 'type' => 'structure', 'required' => [ 'RelativePosition', 'Type', ], 'members' => [ 'DurationMillis' => [ 'shape' => '__long', ], 'RelativePosition' => [ 'shape' => 'RelativePosition', ], 'RelativeProgram' => [ 'shape' => '__string', ], 'ScheduledStartTimeMillis' => [ 'shape' => '__long', ], 'Type' => [ 'shape' => '__string', ], ], ], 'Type' => [ 'type' => 'string', 'enum' => [ 'DASH', 'HLS', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => '__listOf__string', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UpdateChannelRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', 'Outputs', ], 'members' => [ 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'FillerSlate' => [ 'shape' => 'SlateSource', ], 'Outputs' => [ 'shape' => 'RequestOutputs', ], 'TimeShiftConfiguration' => [ 'shape' => 'TimeShiftConfiguration', ], 'Audiences' => [ 'shape' => 'Audiences', ], ], ], 'UpdateChannelResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'ChannelName' => [ 'shape' => '__string', ], 'ChannelState' => [ 'shape' => 'ChannelState', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'FillerSlate' => [ 'shape' => 'SlateSource', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'Outputs' => [ 'shape' => 'ResponseOutputs', ], 'PlaybackMode' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'Tier' => [ 'shape' => '__string', ], 'TimeShiftConfiguration' => [ 'shape' => 'TimeShiftConfiguration', ], 'Audiences' => [ 'shape' => 'Audiences', ], ], ], 'UpdateLiveSourceRequest' => [ 'type' => 'structure', 'required' => [ 'HttpPackageConfigurations', 'LiveSourceName', 'SourceLocationName', ], 'members' => [ 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'LiveSourceName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'LiveSourceName', ], 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], ], ], 'UpdateLiveSourceResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'LiveSourceName' => [ 'shape' => '__string', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'UpdateProgramRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelName', 'ProgramName', 'ScheduleConfiguration', ], 'members' => [ 'AdBreaks' => [ 'shape' => '__listOfAdBreak', ], 'ChannelName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ChannelName', ], 'ProgramName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'ProgramName', ], 'ScheduleConfiguration' => [ 'shape' => 'UpdateProgramScheduleConfiguration', ], 'AudienceMedia' => [ 'shape' => '__listOfAudienceMedia', ], ], ], 'UpdateProgramResponse' => [ 'type' => 'structure', 'members' => [ 'AdBreaks' => [ 'shape' => '__listOfAdBreak', ], 'Arn' => [ 'shape' => '__string', ], 'ChannelName' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'ProgramName' => [ 'shape' => '__string', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'VodSourceName' => [ 'shape' => '__string', ], 'LiveSourceName' => [ 'shape' => '__string', ], 'ClipRange' => [ 'shape' => 'ClipRange', ], 'DurationMillis' => [ 'shape' => '__long', ], 'ScheduledStartTime' => [ 'shape' => '__timestampUnix', ], 'AudienceMedia' => [ 'shape' => '__listOfAudienceMedia', ], ], ], 'UpdateProgramScheduleConfiguration' => [ 'type' => 'structure', 'members' => [ 'Transition' => [ 'shape' => 'UpdateProgramTransition', ], 'ClipRange' => [ 'shape' => 'ClipRange', ], ], ], 'UpdateProgramTransition' => [ 'type' => 'structure', 'members' => [ 'ScheduledStartTimeMillis' => [ 'shape' => '__long', ], 'DurationMillis' => [ 'shape' => '__long', ], ], ], 'UpdateSourceLocationRequest' => [ 'type' => 'structure', 'required' => [ 'HttpConfiguration', 'SourceLocationName', ], 'members' => [ 'AccessConfiguration' => [ 'shape' => 'AccessConfiguration', ], 'DefaultSegmentDeliveryConfiguration' => [ 'shape' => 'DefaultSegmentDeliveryConfiguration', ], 'HttpConfiguration' => [ 'shape' => 'HttpConfiguration', ], 'SegmentDeliveryConfigurations' => [ 'shape' => '__listOfSegmentDeliveryConfiguration', ], 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], ], ], 'UpdateSourceLocationResponse' => [ 'type' => 'structure', 'members' => [ 'AccessConfiguration' => [ 'shape' => 'AccessConfiguration', ], 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'DefaultSegmentDeliveryConfiguration' => [ 'shape' => 'DefaultSegmentDeliveryConfiguration', ], 'HttpConfiguration' => [ 'shape' => 'HttpConfiguration', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'SegmentDeliveryConfigurations' => [ 'shape' => '__listOfSegmentDeliveryConfiguration', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'UpdateVodSourceRequest' => [ 'type' => 'structure', 'required' => [ 'HttpPackageConfigurations', 'SourceLocationName', 'VodSourceName', ], 'members' => [ 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'SourceLocationName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'SourceLocationName', ], 'VodSourceName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'VodSourceName', ], ], ], 'UpdateVodSourceResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'VodSourceName' => [ 'shape' => '__string', ], ], ], 'VodSource' => [ 'type' => 'structure', 'required' => [ 'Arn', 'HttpPackageConfigurations', 'SourceLocationName', 'VodSourceName', ], 'members' => [ 'Arn' => [ 'shape' => '__string', ], 'CreationTime' => [ 'shape' => '__timestampUnix', ], 'HttpPackageConfigurations' => [ 'shape' => 'HttpPackageConfigurations', ], 'LastModifiedTime' => [ 'shape' => '__timestampUnix', ], 'SourceLocationName' => [ 'shape' => '__string', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'VodSourceName' => [ 'shape' => '__string', ], ], ], '__adsInteractionExcludeEventTypesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdsInteractionExcludeEventType', ], ], '__adsInteractionPublishOptInEventTypesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdsInteractionPublishOptInEventType', ], ], '__boolean' => [ 'type' => 'boolean', 'box' => true, ], '__integer' => [ 'type' => 'integer', 'box' => true, ], '__integerMin1' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], '__integerMin1Max100' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], '__listOfAdBreak' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdBreak', ], ], '__listOfAlert' => [ 'type' => 'list', 'member' => [ 'shape' => 'Alert', ], ], '__listOfAlternateMedia' => [ 'type' => 'list', 'member' => [ 'shape' => 'AlternateMedia', ], ], '__listOfAudienceMedia' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudienceMedia', ], ], '__listOfAvailMatchingCriteria' => [ 'type' => 'list', 'member' => [ 'shape' => 'AvailMatchingCriteria', ], ], '__listOfChannel' => [ 'type' => 'list', 'member' => [ 'shape' => 'Channel', ], ], '__listOfLiveSource' => [ 'type' => 'list', 'member' => [ 'shape' => 'LiveSource', ], ], '__listOfLoggingStrategies' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoggingStrategy', ], ], '__listOfPlaybackConfiguration' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlaybackConfiguration', ], ], '__listOfPrefetchSchedule' => [ 'type' => 'list', 'member' => [ 'shape' => 'PrefetchSchedule', ], ], '__listOfScheduleAdBreak' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScheduleAdBreak', ], ], '__listOfScheduleEntry' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScheduleEntry', ], ], '__listOfSegmentDeliveryConfiguration' => [ 'type' => 'list', 'member' => [ 'shape' => 'SegmentDeliveryConfiguration', ], ], '__listOfSourceLocation' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceLocation', ], ], '__listOfVodSource' => [ 'type' => 'list', 'member' => [ 'shape' => 'VodSource', ], ], '__listOf__string' => [ 'type' => 'list', 'member' => [ 'shape' => '__string', ], ], '__long' => [ 'type' => 'long', 'box' => true, ], '__manifestServiceExcludeEventTypesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManifestServiceExcludeEventType', ], ], '__mapOf__string' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => '__string', ], ], '__string' => [ 'type' => 'string', ], '__timestampUnix' => [ 'type' => 'timestamp', 'timestampFormat' => 'unixTimestamp', ], 'adMarkupTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdMarkupType', ], ], ],];
