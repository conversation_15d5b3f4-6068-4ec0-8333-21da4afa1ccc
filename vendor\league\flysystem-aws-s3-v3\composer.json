{"name": "league/flysystem-aws-s3-v3", "description": "AWS S3 filesystem adapter for Flysystem.", "keywords": ["aws", "s3", "flysystem", "filesystem", "storage", "file", "files"], "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\AwsS3V3\\": ""}}, "require": {"php": "^8.0.2", "league/flysystem": "^3.10.0", "league/mime-type-detection": "^1.0.0", "aws/aws-sdk-php": "^3.295.10"}, "conflict": {"guzzlehttp/ringphp": "<1.1.1", "guzzlehttp/guzzle": "<7.0"}, "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}]}