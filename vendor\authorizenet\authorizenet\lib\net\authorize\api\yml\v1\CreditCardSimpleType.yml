net\authorize\api\contract\v1\CreditCardSimpleType:
    properties:
        cardNumber:
            expose: true
            access_type: public_method
            serialized_name: cardNumber
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getCardNumber
                setter: setCardNumber
            type: string
        expirationDate:
            expose: true
            access_type: public_method
            serialized_name: expirationDate
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getExpirationDate
                setter: setExpirationDate
            type: string
