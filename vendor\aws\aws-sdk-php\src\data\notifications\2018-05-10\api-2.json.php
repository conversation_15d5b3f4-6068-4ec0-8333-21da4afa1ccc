<?php
// This file was auto-generated from sdk-root/src/data/notifications/2018-05-10/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-10', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'notifications', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS User Notifications', 'serviceId' => 'Notifications', 'signatureVersion' => 'v4', 'signingName' => 'notifications', 'uid' => 'notifications-2018-05-10', ], 'operations' => [ 'AssociateChannel' => [ 'name' => 'AssociateChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/channels/associate/{arn}', 'responseCode' => 201, ], 'input' => [ 'shape' => 'AssociateChannelRequest', ], 'output' => [ 'shape' => 'AssociateChannelResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'AssociateManagedNotificationAccountContact' => [ 'name' => 'AssociateManagedNotificationAccountContact', 'http' => [ 'method' => 'PUT', 'requestUri' => '/contacts/associate-managed-notification/{contactIdentifier}', 'responseCode' => 201, ], 'input' => [ 'shape' => 'AssociateManagedNotificationAccountContactRequest', ], 'output' => [ 'shape' => 'AssociateManagedNotificationAccountContactResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'AssociateManagedNotificationAdditionalChannel' => [ 'name' => 'AssociateManagedNotificationAdditionalChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channels/associate-managed-notification/{channelArn}', 'responseCode' => 201, ], 'input' => [ 'shape' => 'AssociateManagedNotificationAdditionalChannelRequest', ], 'output' => [ 'shape' => 'AssociateManagedNotificationAdditionalChannelResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'CreateEventRule' => [ 'name' => 'CreateEventRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/event-rules', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateEventRuleRequest', ], 'output' => [ 'shape' => 'CreateEventRuleResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'CreateNotificationConfiguration' => [ 'name' => 'CreateNotificationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/notification-configurations', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateNotificationConfigurationRequest', ], 'output' => [ 'shape' => 'CreateNotificationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteEventRule' => [ 'name' => 'DeleteEventRule', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/event-rules/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteEventRuleRequest', ], 'output' => [ 'shape' => 'DeleteEventRuleResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteNotificationConfiguration' => [ 'name' => 'DeleteNotificationConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/notification-configurations/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteNotificationConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteNotificationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeregisterNotificationHub' => [ 'name' => 'DeregisterNotificationHub', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/notification-hubs/{notificationHubRegion}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeregisterNotificationHubRequest', ], 'output' => [ 'shape' => 'DeregisterNotificationHubResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DisableNotificationsAccessForOrganization' => [ 'name' => 'DisableNotificationsAccessForOrganization', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/organization/access', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisableNotificationsAccessForOrganizationRequest', ], 'output' => [ 'shape' => 'DisableNotificationsAccessForOrganizationResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DisassociateChannel' => [ 'name' => 'DisassociateChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/channels/disassociate/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateChannelRequest', ], 'output' => [ 'shape' => 'DisassociateChannelResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DisassociateManagedNotificationAccountContact' => [ 'name' => 'DisassociateManagedNotificationAccountContact', 'http' => [ 'method' => 'PUT', 'requestUri' => '/contacts/disassociate-managed-notification/{contactIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateManagedNotificationAccountContactRequest', ], 'output' => [ 'shape' => 'DisassociateManagedNotificationAccountContactResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DisassociateManagedNotificationAdditionalChannel' => [ 'name' => 'DisassociateManagedNotificationAdditionalChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/channels/disassociate-managed-notification/{channelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateManagedNotificationAdditionalChannelRequest', ], 'output' => [ 'shape' => 'DisassociateManagedNotificationAdditionalChannelResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'EnableNotificationsAccessForOrganization' => [ 'name' => 'EnableNotificationsAccessForOrganization', 'http' => [ 'method' => 'POST', 'requestUri' => '/organization/access', 'responseCode' => 200, ], 'input' => [ 'shape' => 'EnableNotificationsAccessForOrganizationRequest', ], 'output' => [ 'shape' => 'EnableNotificationsAccessForOrganizationResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'GetEventRule' => [ 'name' => 'GetEventRule', 'http' => [ 'method' => 'GET', 'requestUri' => '/event-rules/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEventRuleRequest', ], 'output' => [ 'shape' => 'GetEventRuleResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetManagedNotificationChildEvent' => [ 'name' => 'GetManagedNotificationChildEvent', 'http' => [ 'method' => 'GET', 'requestUri' => '/managed-notification-child-events/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetManagedNotificationChildEventRequest', ], 'output' => [ 'shape' => 'GetManagedNotificationChildEventResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetManagedNotificationConfiguration' => [ 'name' => 'GetManagedNotificationConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/managed-notification-configurations/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetManagedNotificationConfigurationRequest', ], 'output' => [ 'shape' => 'GetManagedNotificationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetManagedNotificationEvent' => [ 'name' => 'GetManagedNotificationEvent', 'http' => [ 'method' => 'GET', 'requestUri' => '/managed-notification-events/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetManagedNotificationEventRequest', ], 'output' => [ 'shape' => 'GetManagedNotificationEventResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetNotificationConfiguration' => [ 'name' => 'GetNotificationConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/notification-configurations/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetNotificationConfigurationRequest', ], 'output' => [ 'shape' => 'GetNotificationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetNotificationEvent' => [ 'name' => 'GetNotificationEvent', 'http' => [ 'method' => 'GET', 'requestUri' => '/notification-events/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetNotificationEventRequest', ], 'output' => [ 'shape' => 'GetNotificationEventResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetNotificationsAccessForOrganization' => [ 'name' => 'GetNotificationsAccessForOrganization', 'http' => [ 'method' => 'GET', 'requestUri' => '/organization/access', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetNotificationsAccessForOrganizationRequest', ], 'output' => [ 'shape' => 'GetNotificationsAccessForOrganizationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListChannels' => [ 'name' => 'ListChannels', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelsRequest', ], 'output' => [ 'shape' => 'ListChannelsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListEventRules' => [ 'name' => 'ListEventRules', 'http' => [ 'method' => 'GET', 'requestUri' => '/event-rules', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEventRulesRequest', ], 'output' => [ 'shape' => 'ListEventRulesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListManagedNotificationChannelAssociations' => [ 'name' => 'ListManagedNotificationChannelAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/channels/list-managed-notification-channel-associations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListManagedNotificationChannelAssociationsRequest', ], 'output' => [ 'shape' => 'ListManagedNotificationChannelAssociationsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListManagedNotificationChildEvents' => [ 'name' => 'ListManagedNotificationChildEvents', 'http' => [ 'method' => 'GET', 'requestUri' => '/list-managed-notification-child-events/{aggregateManagedNotificationEventArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListManagedNotificationChildEventsRequest', ], 'output' => [ 'shape' => 'ListManagedNotificationChildEventsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListManagedNotificationConfigurations' => [ 'name' => 'ListManagedNotificationConfigurations', 'http' => [ 'method' => 'GET', 'requestUri' => '/managed-notification-configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListManagedNotificationConfigurationsRequest', ], 'output' => [ 'shape' => 'ListManagedNotificationConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListManagedNotificationEvents' => [ 'name' => 'ListManagedNotificationEvents', 'http' => [ 'method' => 'GET', 'requestUri' => '/managed-notification-events', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListManagedNotificationEventsRequest', ], 'output' => [ 'shape' => 'ListManagedNotificationEventsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListNotificationConfigurations' => [ 'name' => 'ListNotificationConfigurations', 'http' => [ 'method' => 'GET', 'requestUri' => '/notification-configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListNotificationConfigurationsRequest', ], 'output' => [ 'shape' => 'ListNotificationConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListNotificationEvents' => [ 'name' => 'ListNotificationEvents', 'http' => [ 'method' => 'GET', 'requestUri' => '/notification-events', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListNotificationEventsRequest', ], 'output' => [ 'shape' => 'ListNotificationEventsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListNotificationHubs' => [ 'name' => 'ListNotificationHubs', 'http' => [ 'method' => 'GET', 'requestUri' => '/notification-hubs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListNotificationHubsRequest', ], 'output' => [ 'shape' => 'ListNotificationHubsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'RegisterNotificationHub' => [ 'name' => 'RegisterNotificationHub', 'http' => [ 'method' => 'POST', 'requestUri' => '/notification-hubs', 'responseCode' => 201, ], 'input' => [ 'shape' => 'RegisterNotificationHubRequest', ], 'output' => [ 'shape' => 'RegisterNotificationHubResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateEventRule' => [ 'name' => 'UpdateEventRule', 'http' => [ 'method' => 'PUT', 'requestUri' => '/event-rules/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateEventRuleRequest', ], 'output' => [ 'shape' => 'UpdateEventRuleResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateNotificationConfiguration' => [ 'name' => 'UpdateNotificationConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/notification-configurations/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateNotificationConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateNotificationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccessStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', 'PENDING', ], ], 'AccountContactType' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT_PRIMARY', 'ACCOUNT_ALTERNATE_BILLING', 'ACCOUNT_ALTERNATE_OPERATIONS', 'ACCOUNT_ALTERNATE_SECURITY', ], ], 'AccountId' => [ 'type' => 'string', 'pattern' => '\\d{12}', ], 'AggregatedNotificationRegions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Region', ], ], 'AggregationDetail' => [ 'type' => 'structure', 'members' => [ 'summarizationDimensions' => [ 'shape' => 'SummarizationDimensionDetails', ], ], ], 'AggregationDuration' => [ 'type' => 'string', 'enum' => [ 'LONG', 'SHORT', 'NONE', ], ], 'AggregationEventType' => [ 'type' => 'string', 'enum' => [ 'AGGREGATE', 'CHILD', 'NONE', ], ], 'AggregationKey' => [ 'type' => 'structure', 'required' => [ 'name', 'value', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], ], 'AggregationKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregationKey', ], ], 'AggregationSummary' => [ 'type' => 'structure', 'required' => [ 'eventCount', 'aggregatedBy', 'aggregatedAccounts', 'aggregatedRegions', ], 'members' => [ 'eventCount' => [ 'shape' => 'Integer', ], 'aggregatedBy' => [ 'shape' => 'AggregationKeys', ], 'aggregatedAccounts' => [ 'shape' => 'SummarizationDimensionOverview', ], 'aggregatedRegions' => [ 'shape' => 'SummarizationDimensionOverview', ], 'aggregatedOrganizationalUnits' => [ 'shape' => 'SummarizationDimensionOverview', ], 'additionalSummarizationDimensions' => [ 'shape' => 'SummarizationDimensionOverviews', ], ], ], 'Arn' => [ 'type' => 'string', 'pattern' => 'arn:[^:]*:[^:]*:[^:]*:.*', ], 'AssociateChannelRequest' => [ 'type' => 'structure', 'required' => [ 'arn', 'notificationConfigurationArn', ], 'members' => [ 'arn' => [ 'shape' => 'ChannelArn', 'location' => 'uri', 'locationName' => 'arn', ], 'notificationConfigurationArn' => [ 'shape' => 'NotificationConfigurationArn', ], ], ], 'AssociateChannelResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateManagedNotificationAccountContactRequest' => [ 'type' => 'structure', 'required' => [ 'contactIdentifier', 'managedNotificationConfigurationArn', ], 'members' => [ 'contactIdentifier' => [ 'shape' => 'AccountContactType', 'location' => 'uri', 'locationName' => 'contactIdentifier', ], 'managedNotificationConfigurationArn' => [ 'shape' => 'ManagedNotificationConfigurationOsArn', ], ], ], 'AssociateManagedNotificationAccountContactResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateManagedNotificationAdditionalChannelRequest' => [ 'type' => 'structure', 'required' => [ 'channelArn', 'managedNotificationConfigurationArn', ], 'members' => [ 'channelArn' => [ 'shape' => 'ChannelArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'managedNotificationConfigurationArn' => [ 'shape' => 'ManagedNotificationConfigurationOsArn', ], ], ], 'AssociateManagedNotificationAdditionalChannelResponse' => [ 'type' => 'structure', 'members' => [], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ChannelArn' => [ 'type' => 'string', 'pattern' => 'arn:aws:(chatbot|consoleapp|notifications-contacts):[a-zA-Z0-9-]*:[0-9]{12}:[a-zA-Z0-9-_.@]+/[a-zA-Z0-9/_.@:-]+', ], 'ChannelAssociationOverrideOption' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'ChannelIdentifier' => [ 'type' => 'string', 'pattern' => 'ACCOUNT_PRIMARY|ACCOUNT_ALTERNATE_BILLING|ACCOUNT_ALTERNATE_OPERATIONS|ACCOUNT_ALTERNATE_SECURITY|arn:aws:(chatbot|consoleapp|notifications-contacts):[a-zA-Z0-9-]*:[0-9]{12}:[a-zA-Z0-9-_.@]+/[a-zA-Z0-9/_.@:-]+', ], 'ChannelType' => [ 'type' => 'string', 'enum' => [ 'MOBILE', 'CHATBOT', 'EMAIL', 'ACCOUNT_CONTACT', ], ], 'Channels' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelArn', ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'ResourceId', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateEventRuleRequest' => [ 'type' => 'structure', 'required' => [ 'notificationConfigurationArn', 'source', 'eventType', 'regions', ], 'members' => [ 'notificationConfigurationArn' => [ 'shape' => 'NotificationConfigurationArn', ], 'source' => [ 'shape' => 'Source', ], 'eventType' => [ 'shape' => 'EventType', ], 'eventPattern' => [ 'shape' => 'EventRuleEventPattern', ], 'regions' => [ 'shape' => 'Regions', ], ], ], 'CreateEventRuleResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'notificationConfigurationArn', 'statusSummaryByRegion', ], 'members' => [ 'arn' => [ 'shape' => 'EventRuleArn', ], 'notificationConfigurationArn' => [ 'shape' => 'NotificationConfigurationArn', ], 'statusSummaryByRegion' => [ 'shape' => 'StatusSummaryByRegion', ], ], ], 'CreateNotificationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'description', ], 'members' => [ 'name' => [ 'shape' => 'NotificationConfigurationName', ], 'description' => [ 'shape' => 'NotificationConfigurationDescription', ], 'aggregationDuration' => [ 'shape' => 'AggregationDuration', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateNotificationConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'status', ], 'members' => [ 'arn' => [ 'shape' => 'NotificationConfigurationArn', ], 'status' => [ 'shape' => 'NotificationConfigurationStatus', ], ], ], 'CreationTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'DeleteEventRuleRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'EventRuleArn', 'location' => 'uri', 'locationName' => 'arn', ], ], ], 'DeleteEventRuleResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteNotificationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'NotificationConfigurationArn', 'location' => 'uri', 'locationName' => 'arn', ], ], ], 'DeleteNotificationConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeregisterNotificationHubRequest' => [ 'type' => 'structure', 'required' => [ 'notificationHubRegion', ], 'members' => [ 'notificationHubRegion' => [ 'shape' => 'Region', 'location' => 'uri', 'locationName' => 'notificationHubRegion', ], ], ], 'DeregisterNotificationHubResponse' => [ 'type' => 'structure', 'required' => [ 'notificationHubRegion', 'statusSummary', ], 'members' => [ 'notificationHubRegion' => [ 'shape' => 'Region', ], 'statusSummary' => [ 'shape' => 'NotificationHubStatusSummary', ], ], ], 'Dimension' => [ 'type' => 'structure', 'required' => [ 'name', 'value', ], 'members' => [ 'name' => [ 'shape' => 'TextPartReference', ], 'value' => [ 'shape' => 'TextPartReference', ], ], ], 'Dimensions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Dimension', ], 'max' => 10, 'min' => 0, ], 'DisableNotificationsAccessForOrganizationRequest' => [ 'type' => 'structure', 'members' => [], ], 'DisableNotificationsAccessForOrganizationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateChannelRequest' => [ 'type' => 'structure', 'required' => [ 'arn', 'notificationConfigurationArn', ], 'members' => [ 'arn' => [ 'shape' => 'ChannelArn', 'location' => 'uri', 'locationName' => 'arn', ], 'notificationConfigurationArn' => [ 'shape' => 'NotificationConfigurationArn', ], ], ], 'DisassociateChannelResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateManagedNotificationAccountContactRequest' => [ 'type' => 'structure', 'required' => [ 'contactIdentifier', 'managedNotificationConfigurationArn', ], 'members' => [ 'contactIdentifier' => [ 'shape' => 'AccountContactType', 'location' => 'uri', 'locationName' => 'contactIdentifier', ], 'managedNotificationConfigurationArn' => [ 'shape' => 'ManagedNotificationConfigurationOsArn', ], ], ], 'DisassociateManagedNotificationAccountContactResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateManagedNotificationAdditionalChannelRequest' => [ 'type' => 'structure', 'required' => [ 'channelArn', 'managedNotificationConfigurationArn', ], 'members' => [ 'channelArn' => [ 'shape' => 'ChannelArn', 'location' => 'uri', 'locationName' => 'channelArn', ], 'managedNotificationConfigurationArn' => [ 'shape' => 'ManagedNotificationConfigurationOsArn', ], ], ], 'DisassociateManagedNotificationAdditionalChannelResponse' => [ 'type' => 'structure', 'members' => [], ], 'EnableNotificationsAccessForOrganizationRequest' => [ 'type' => 'structure', 'members' => [], ], 'EnableNotificationsAccessForOrganizationResponse' => [ 'type' => 'structure', 'members' => [], ], 'ErrorMessage' => [ 'type' => 'string', ], 'EventRuleArn' => [ 'type' => 'string', 'pattern' => 'arn:aws:notifications::[0-9]{12}:configuration/[a-z0-9]{27}/rule/[a-z0-9]{27}', ], 'EventRuleEventPattern' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, ], 'EventRuleStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', 'CREATING', 'UPDATING', 'DELETING', ], ], 'EventRuleStatusReason' => [ 'type' => 'string', ], 'EventRuleStatusSummary' => [ 'type' => 'structure', 'required' => [ 'status', 'reason', ], 'members' => [ 'status' => [ 'shape' => 'EventRuleStatus', ], 'reason' => [ 'shape' => 'EventRuleStatusReason', ], ], ], 'EventRuleStructure' => [ 'type' => 'structure', 'required' => [ 'arn', 'notificationConfigurationArn', 'creationTime', 'source', 'eventType', 'eventPattern', 'regions', 'managedRules', 'statusSummaryByRegion', ], 'members' => [ 'arn' => [ 'shape' => 'EventRuleArn', ], 'notificationConfigurationArn' => [ 'shape' => 'NotificationConfigurationArn', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'source' => [ 'shape' => 'Source', ], 'eventType' => [ 'shape' => 'EventType', ], 'eventPattern' => [ 'shape' => 'EventRuleEventPattern', ], 'regions' => [ 'shape' => 'Regions', ], 'managedRules' => [ 'shape' => 'ManagedRuleArns', ], 'statusSummaryByRegion' => [ 'shape' => 'StatusSummaryByRegion', ], ], ], 'EventRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventRuleStructure', ], ], 'EventStatus' => [ 'type' => 'string', 'enum' => [ 'HEALTHY', 'UNHEALTHY', ], ], 'EventType' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '([a-zA-Z0-9 \\-\\(\\)])+', ], 'GetEventRuleRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'EventRuleArn', 'location' => 'uri', 'locationName' => 'arn', ], ], ], 'GetEventRuleResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'notificationConfigurationArn', 'creationTime', 'source', 'eventType', 'eventPattern', 'regions', 'managedRules', 'statusSummaryByRegion', ], 'members' => [ 'arn' => [ 'shape' => 'EventRuleArn', ], 'notificationConfigurationArn' => [ 'shape' => 'NotificationConfigurationArn', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'source' => [ 'shape' => 'Source', ], 'eventType' => [ 'shape' => 'EventType', ], 'eventPattern' => [ 'shape' => 'EventRuleEventPattern', ], 'regions' => [ 'shape' => 'Regions', ], 'managedRules' => [ 'shape' => 'ManagedRuleArns', ], 'statusSummaryByRegion' => [ 'shape' => 'StatusSummaryByRegion', ], ], ], 'GetManagedNotificationChildEventRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'ManagedNotificationChildEventArn', 'location' => 'uri', 'locationName' => 'arn', ], 'locale' => [ 'shape' => 'LocaleCode', 'location' => 'querystring', 'locationName' => 'locale', ], ], ], 'GetManagedNotificationChildEventResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'managedNotificationConfigurationArn', 'creationTime', 'content', ], 'members' => [ 'arn' => [ 'shape' => 'ManagedNotificationChildEventArn', ], 'managedNotificationConfigurationArn' => [ 'shape' => 'ManagedNotificationConfigurationOsArn', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'content' => [ 'shape' => 'ManagedNotificationChildEvent', ], ], ], 'GetManagedNotificationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'ManagedNotificationConfigurationOsArn', 'location' => 'uri', 'locationName' => 'arn', ], ], ], 'GetManagedNotificationConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'name', 'description', 'category', 'subCategory', ], 'members' => [ 'arn' => [ 'shape' => 'ManagedNotificationConfigurationOsArn', ], 'name' => [ 'shape' => 'ManagedNotificationConfigurationName', ], 'description' => [ 'shape' => 'ManagedNotificationConfigurationDescription', ], 'category' => [ 'shape' => 'String', ], 'subCategory' => [ 'shape' => 'String', ], ], ], 'GetManagedNotificationEventRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'ManagedNotificationEventArn', 'location' => 'uri', 'locationName' => 'arn', ], 'locale' => [ 'shape' => 'LocaleCode', 'location' => 'querystring', 'locationName' => 'locale', ], ], ], 'GetManagedNotificationEventResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'managedNotificationConfigurationArn', 'creationTime', 'content', ], 'members' => [ 'arn' => [ 'shape' => 'ManagedNotificationEventArn', ], 'managedNotificationConfigurationArn' => [ 'shape' => 'ManagedNotificationConfigurationOsArn', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'content' => [ 'shape' => 'ManagedNotificationEvent', ], ], ], 'GetNotificationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'NotificationConfigurationArn', 'location' => 'uri', 'locationName' => 'arn', ], ], ], 'GetNotificationConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'name', 'description', 'status', 'creationTime', ], 'members' => [ 'arn' => [ 'shape' => 'NotificationConfigurationArn', ], 'name' => [ 'shape' => 'NotificationConfigurationName', ], 'description' => [ 'shape' => 'NotificationConfigurationDescription', ], 'status' => [ 'shape' => 'NotificationConfigurationStatus', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'aggregationDuration' => [ 'shape' => 'AggregationDuration', ], ], ], 'GetNotificationEventRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'NotificationEventArn', 'location' => 'uri', 'locationName' => 'arn', ], 'locale' => [ 'shape' => 'LocaleCode', 'location' => 'querystring', 'locationName' => 'locale', ], ], ], 'GetNotificationEventResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'notificationConfigurationArn', 'creationTime', 'content', ], 'members' => [ 'arn' => [ 'shape' => 'NotificationEventArn', ], 'notificationConfigurationArn' => [ 'shape' => 'NotificationConfigurationArn', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'content' => [ 'shape' => 'NotificationEvent', ], ], ], 'GetNotificationsAccessForOrganizationRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetNotificationsAccessForOrganizationResponse' => [ 'type' => 'structure', 'required' => [ 'notificationsAccessForOrganization', ], 'members' => [ 'notificationsAccessForOrganization' => [ 'shape' => 'NotificationsAccessForOrganization', ], ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'LastActivationTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'ListChannelsRequest' => [ 'type' => 'structure', 'required' => [ 'notificationConfigurationArn', ], 'members' => [ 'notificationConfigurationArn' => [ 'shape' => 'NotificationConfigurationArn', 'location' => 'querystring', 'locationName' => 'notificationConfigurationArn', ], 'maxResults' => [ 'shape' => 'ListChannelsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListChannelsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListChannelsResponse' => [ 'type' => 'structure', 'required' => [ 'channels', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'channels' => [ 'shape' => 'Channels', ], ], ], 'ListEventRulesRequest' => [ 'type' => 'structure', 'required' => [ 'notificationConfigurationArn', ], 'members' => [ 'notificationConfigurationArn' => [ 'shape' => 'NotificationConfigurationArn', 'location' => 'querystring', 'locationName' => 'notificationConfigurationArn', ], 'maxResults' => [ 'shape' => 'ListEventRulesRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListEventRulesRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'ListEventRulesResponse' => [ 'type' => 'structure', 'required' => [ 'eventRules', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'eventRules' => [ 'shape' => 'EventRules', ], ], ], 'ListManagedNotificationChannelAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'managedNotificationConfigurationArn', ], 'members' => [ 'managedNotificationConfigurationArn' => [ 'shape' => 'ManagedNotificationConfigurationOsArn', 'location' => 'querystring', 'locationName' => 'managedNotificationConfigurationArn', ], 'maxResults' => [ 'shape' => 'ListManagedNotificationChannelAssociationsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListManagedNotificationChannelAssociationsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListManagedNotificationChannelAssociationsResponse' => [ 'type' => 'structure', 'required' => [ 'channelAssociations', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'channelAssociations' => [ 'shape' => 'ManagedNotificationChannelAssociations', ], ], ], 'ListManagedNotificationChildEventsRequest' => [ 'type' => 'structure', 'required' => [ 'aggregateManagedNotificationEventArn', ], 'members' => [ 'aggregateManagedNotificationEventArn' => [ 'shape' => 'ManagedNotificationEventArn', 'location' => 'uri', 'locationName' => 'aggregateManagedNotificationEventArn', ], 'startTime' => [ 'shape' => 'SyntheticTimestamp_date_time', 'location' => 'querystring', 'locationName' => 'startTime', ], 'endTime' => [ 'shape' => 'SyntheticTimestamp_date_time', 'location' => 'querystring', 'locationName' => 'endTime', ], 'locale' => [ 'shape' => 'LocaleCode', 'location' => 'querystring', 'locationName' => 'locale', ], 'maxResults' => [ 'shape' => 'ListManagedNotificationChildEventsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'relatedAccount' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'relatedAccount', ], 'organizationalUnitId' => [ 'shape' => 'OrganizationalUnitId', 'location' => 'querystring', 'locationName' => 'organizationalUnitId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListManagedNotificationChildEventsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListManagedNotificationChildEventsResponse' => [ 'type' => 'structure', 'required' => [ 'managedNotificationChildEvents', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'managedNotificationChildEvents' => [ 'shape' => 'ManagedNotificationChildEvents', ], ], ], 'ListManagedNotificationConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'channelIdentifier' => [ 'shape' => 'ChannelIdentifier', 'location' => 'querystring', 'locationName' => 'channelIdentifier', ], 'maxResults' => [ 'shape' => 'ListManagedNotificationConfigurationsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListManagedNotificationConfigurationsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListManagedNotificationConfigurationsResponse' => [ 'type' => 'structure', 'required' => [ 'managedNotificationConfigurations', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'managedNotificationConfigurations' => [ 'shape' => 'ManagedNotificationConfigurations', ], ], ], 'ListManagedNotificationEventsRequest' => [ 'type' => 'structure', 'members' => [ 'startTime' => [ 'shape' => 'SyntheticTimestamp_date_time', 'location' => 'querystring', 'locationName' => 'startTime', ], 'endTime' => [ 'shape' => 'SyntheticTimestamp_date_time', 'location' => 'querystring', 'locationName' => 'endTime', ], 'locale' => [ 'shape' => 'LocaleCode', 'location' => 'querystring', 'locationName' => 'locale', ], 'source' => [ 'shape' => 'Source', 'location' => 'querystring', 'locationName' => 'source', ], 'maxResults' => [ 'shape' => 'ListManagedNotificationEventsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'organizationalUnitId' => [ 'shape' => 'OrganizationalUnitId', 'location' => 'querystring', 'locationName' => 'organizationalUnitId', ], 'relatedAccount' => [ 'shape' => 'AccountId', 'location' => 'querystring', 'locationName' => 'relatedAccount', ], ], ], 'ListManagedNotificationEventsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListManagedNotificationEventsResponse' => [ 'type' => 'structure', 'required' => [ 'managedNotificationEvents', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'managedNotificationEvents' => [ 'shape' => 'ManagedNotificationEvents', ], ], ], 'ListNotificationConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'eventRuleSource' => [ 'shape' => 'Source', 'location' => 'querystring', 'locationName' => 'eventRuleSource', ], 'channelArn' => [ 'shape' => 'ChannelArn', 'location' => 'querystring', 'locationName' => 'channelArn', ], 'status' => [ 'shape' => 'NotificationConfigurationStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'maxResults' => [ 'shape' => 'ListNotificationConfigurationsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListNotificationConfigurationsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListNotificationConfigurationsResponse' => [ 'type' => 'structure', 'required' => [ 'notificationConfigurations', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'notificationConfigurations' => [ 'shape' => 'NotificationConfigurations', ], ], ], 'ListNotificationEventsRequest' => [ 'type' => 'structure', 'members' => [ 'startTime' => [ 'shape' => 'SyntheticTimestamp_date_time', 'location' => 'querystring', 'locationName' => 'startTime', ], 'endTime' => [ 'shape' => 'SyntheticTimestamp_date_time', 'location' => 'querystring', 'locationName' => 'endTime', ], 'locale' => [ 'shape' => 'LocaleCode', 'location' => 'querystring', 'locationName' => 'locale', ], 'source' => [ 'shape' => 'Source', 'location' => 'querystring', 'locationName' => 'source', ], 'includeChildEvents' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'includeChildEvents', ], 'aggregateNotificationEventArn' => [ 'shape' => 'NotificationEventArn', 'location' => 'querystring', 'locationName' => 'aggregateNotificationEventArn', ], 'maxResults' => [ 'shape' => 'ListNotificationEventsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListNotificationEventsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListNotificationEventsResponse' => [ 'type' => 'structure', 'required' => [ 'notificationEvents', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'notificationEvents' => [ 'shape' => 'NotificationEvents', ], ], ], 'ListNotificationHubsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListNotificationHubsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListNotificationHubsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 3, 'min' => 3, ], 'ListNotificationHubsResponse' => [ 'type' => 'structure', 'required' => [ 'notificationHubs', ], 'members' => [ 'notificationHubs' => [ 'shape' => 'NotificationHubs', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'NotificationConfigurationArn', 'location' => 'uri', 'locationName' => 'arn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'LocaleCode' => [ 'type' => 'string', 'enum' => [ 'de_DE', 'en_CA', 'en_US', 'en_UK', 'es_ES', 'fr_CA', 'fr_FR', 'id_ID', 'it_IT', 'ja_JP', 'ko_KR', 'pt_BR', 'tr_TR', 'zh_CN', 'zh_TW', ], ], 'ManagedNotificationChannelAssociationSummary' => [ 'type' => 'structure', 'required' => [ 'channelIdentifier', 'channelType', ], 'members' => [ 'channelIdentifier' => [ 'shape' => 'String', ], 'channelType' => [ 'shape' => 'ChannelType', ], 'overrideOption' => [ 'shape' => 'ChannelAssociationOverrideOption', ], ], ], 'ManagedNotificationChannelAssociations' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManagedNotificationChannelAssociationSummary', ], ], 'ManagedNotificationChildEvent' => [ 'type' => 'structure', 'required' => [ 'schemaVersion', 'id', 'messageComponents', 'notificationType', 'aggregateManagedNotificationEventArn', 'textParts', ], 'members' => [ 'schemaVersion' => [ 'shape' => 'SchemaVersion', ], 'id' => [ 'shape' => 'NotificationEventId', ], 'messageComponents' => [ 'shape' => 'MessageComponents', ], 'sourceEventDetailUrl' => [ 'shape' => 'Url', ], 'sourceEventDetailUrlDisplayText' => [ 'shape' => 'String', ], 'notificationType' => [ 'shape' => 'NotificationType', ], 'eventStatus' => [ 'shape' => 'EventStatus', ], 'aggregateManagedNotificationEventArn' => [ 'shape' => 'ManagedNotificationEventArn', ], 'startTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'endTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'textParts' => [ 'shape' => 'TextParts', ], 'organizationalUnitId' => [ 'shape' => 'OrganizationalUnitId', ], 'aggregationDetail' => [ 'shape' => 'AggregationDetail', ], ], ], 'ManagedNotificationChildEventArn' => [ 'type' => 'string', 'pattern' => 'arn:[-.a-z0-9]{1,63}:notifications::[0-9]{12}:managed-notification-configuration/category/[a-zA-Z0-9\\-]{3,64}/sub-category/[a-zA-Z0-9\\-]{3,64}/event/[a-z0-9]{27}/child-event/[a-z0-9]{27}', ], 'ManagedNotificationChildEventOverview' => [ 'type' => 'structure', 'required' => [ 'arn', 'managedNotificationConfigurationArn', 'relatedAccount', 'creationTime', 'childEvent', 'aggregateManagedNotificationEventArn', ], 'members' => [ 'arn' => [ 'shape' => 'ManagedNotificationEventArn', ], 'managedNotificationConfigurationArn' => [ 'shape' => 'ManagedNotificationConfigurationOsArn', ], 'relatedAccount' => [ 'shape' => 'AccountId', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'childEvent' => [ 'shape' => 'ManagedNotificationChildEventSummary', ], 'aggregateManagedNotificationEventArn' => [ 'shape' => 'ManagedNotificationEventArn', ], 'organizationalUnitId' => [ 'shape' => 'OrganizationalUnitId', ], ], ], 'ManagedNotificationChildEventSummary' => [ 'type' => 'structure', 'required' => [ 'schemaVersion', 'sourceEventMetadata', 'messageComponents', 'aggregationDetail', 'eventStatus', 'notificationType', ], 'members' => [ 'schemaVersion' => [ 'shape' => 'SchemaVersion', ], 'sourceEventMetadata' => [ 'shape' => 'ManagedSourceEventMetadataSummary', ], 'messageComponents' => [ 'shape' => 'MessageComponentsSummary', ], 'aggregationDetail' => [ 'shape' => 'AggregationDetail', ], 'eventStatus' => [ 'shape' => 'EventStatus', ], 'notificationType' => [ 'shape' => 'NotificationType', ], ], ], 'ManagedNotificationChildEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManagedNotificationChildEventOverview', ], ], 'ManagedNotificationConfigurationDescription' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[^\\u0001-\\u001F\\u007F-\\u009F]*', ], 'ManagedNotificationConfigurationName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[A-Za-z0-9\\-]+', ], 'ManagedNotificationConfigurationOsArn' => [ 'type' => 'string', 'pattern' => 'arn:[-.a-z0-9]{1,63}:notifications::[0-9]{12}:managed-notification-configuration/category/[a-zA-Z0-9\\-]{3,64}/sub-category/[a-zA-Z0-9\\-]{3,64}', ], 'ManagedNotificationConfigurationStructure' => [ 'type' => 'structure', 'required' => [ 'arn', 'name', 'description', ], 'members' => [ 'arn' => [ 'shape' => 'ManagedNotificationConfigurationOsArn', ], 'name' => [ 'shape' => 'ManagedNotificationConfigurationName', ], 'description' => [ 'shape' => 'ManagedNotificationConfigurationDescription', ], ], ], 'ManagedNotificationConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManagedNotificationConfigurationStructure', ], ], 'ManagedNotificationEvent' => [ 'type' => 'structure', 'required' => [ 'schemaVersion', 'id', 'messageComponents', 'notificationType', 'textParts', ], 'members' => [ 'schemaVersion' => [ 'shape' => 'SchemaVersion', ], 'id' => [ 'shape' => 'NotificationEventId', ], 'messageComponents' => [ 'shape' => 'MessageComponents', ], 'sourceEventDetailUrl' => [ 'shape' => 'Url', ], 'sourceEventDetailUrlDisplayText' => [ 'shape' => 'String', ], 'notificationType' => [ 'shape' => 'NotificationType', ], 'eventStatus' => [ 'shape' => 'EventStatus', ], 'aggregationEventType' => [ 'shape' => 'AggregationEventType', ], 'aggregationSummary' => [ 'shape' => 'AggregationSummary', ], 'startTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'endTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'textParts' => [ 'shape' => 'TextParts', ], 'organizationalUnitId' => [ 'shape' => 'OrganizationalUnitId', ], ], ], 'ManagedNotificationEventArn' => [ 'type' => 'string', 'pattern' => 'arn:[-.a-z0-9]{1,63}:notifications::[0-9]{12}:managed-notification-configuration/category/[a-zA-Z0-9\\-]{3,64}/sub-category/[a-zA-Z0-9\\-]{3,64}/event/[a-z0-9]{27}', ], 'ManagedNotificationEventOverview' => [ 'type' => 'structure', 'required' => [ 'arn', 'managedNotificationConfigurationArn', 'relatedAccount', 'creationTime', 'notificationEvent', ], 'members' => [ 'arn' => [ 'shape' => 'ManagedNotificationEventArn', ], 'managedNotificationConfigurationArn' => [ 'shape' => 'ManagedNotificationConfigurationOsArn', ], 'relatedAccount' => [ 'shape' => 'AccountId', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'notificationEvent' => [ 'shape' => 'ManagedNotificationEventSummary', ], 'aggregationEventType' => [ 'shape' => 'AggregationEventType', ], 'organizationalUnitId' => [ 'shape' => 'OrganizationalUnitId', ], 'aggregationSummary' => [ 'shape' => 'AggregationSummary', ], 'aggregatedNotificationRegions' => [ 'shape' => 'AggregatedNotificationRegions', ], ], ], 'ManagedNotificationEventSummary' => [ 'type' => 'structure', 'required' => [ 'schemaVersion', 'sourceEventMetadata', 'messageComponents', 'eventStatus', 'notificationType', ], 'members' => [ 'schemaVersion' => [ 'shape' => 'SchemaVersion', ], 'sourceEventMetadata' => [ 'shape' => 'ManagedSourceEventMetadataSummary', ], 'messageComponents' => [ 'shape' => 'MessageComponentsSummary', ], 'eventStatus' => [ 'shape' => 'EventStatus', ], 'notificationType' => [ 'shape' => 'NotificationType', ], ], ], 'ManagedNotificationEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManagedNotificationEventOverview', ], ], 'ManagedRuleArn' => [ 'type' => 'string', 'pattern' => 'arn:aws:events:[a-z-\\d]{2,25}:\\d{12}:rule\\/[a-zA-Z-\\d]{1,1024}', ], 'ManagedRuleArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManagedRuleArn', ], ], 'ManagedSourceEventMetadataSummary' => [ 'type' => 'structure', 'required' => [ 'source', 'eventType', ], 'members' => [ 'eventOriginRegion' => [ 'shape' => 'ManagedSourceEventMetadataSummaryEventOriginRegionString', ], 'source' => [ 'shape' => 'Source', ], 'eventType' => [ 'shape' => 'ManagedSourceEventMetadataSummaryEventTypeString', ], ], ], 'ManagedSourceEventMetadataSummaryEventOriginRegionString' => [ 'type' => 'string', 'max' => 32, 'min' => 0, 'pattern' => '([a-z]{1,2})-([a-z]{1,15}-)+([0-9])', ], 'ManagedSourceEventMetadataSummaryEventTypeString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '([a-zA-Z0-9 \\-\\(\\)])+', ], 'Media' => [ 'type' => 'list', 'member' => [ 'shape' => 'MediaElement', ], ], 'MediaElement' => [ 'type' => 'structure', 'required' => [ 'mediaId', 'type', 'url', 'caption', ], 'members' => [ 'mediaId' => [ 'shape' => 'MediaId', ], 'type' => [ 'shape' => 'MediaElementType', ], 'url' => [ 'shape' => 'Url', ], 'caption' => [ 'shape' => 'TextPartReference', ], ], ], 'MediaElementType' => [ 'type' => 'string', 'enum' => [ 'IMAGE', ], ], 'MediaId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'MessageComponents' => [ 'type' => 'structure', 'members' => [ 'headline' => [ 'shape' => 'TextPartReference', ], 'paragraphSummary' => [ 'shape' => 'TextPartReference', ], 'completeDescription' => [ 'shape' => 'TextPartReference', ], 'dimensions' => [ 'shape' => 'Dimensions', ], ], ], 'MessageComponentsSummary' => [ 'type' => 'structure', 'required' => [ 'headline', ], 'members' => [ 'headline' => [ 'shape' => 'MessageComponentsSummaryHeadlineString', ], ], ], 'MessageComponentsSummaryHeadlineString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'NextToken' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '[\\w+-/=]+', ], 'NotificationConfigurationArn' => [ 'type' => 'string', 'pattern' => 'arn:aws:notifications::[0-9]{12}:configuration/[a-z0-9]{27}', ], 'NotificationConfigurationDescription' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[^\\u0001-\\u001F\\u007F-\\u009F]*', ], 'NotificationConfigurationName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[A-Za-z0-9_\\-]+', ], 'NotificationConfigurationStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'PARTIALLY_ACTIVE', 'INACTIVE', 'DELETING', ], ], 'NotificationConfigurationStructure' => [ 'type' => 'structure', 'required' => [ 'arn', 'name', 'description', 'status', 'creationTime', ], 'members' => [ 'arn' => [ 'shape' => 'NotificationConfigurationArn', ], 'name' => [ 'shape' => 'NotificationConfigurationName', ], 'description' => [ 'shape' => 'NotificationConfigurationDescription', ], 'status' => [ 'shape' => 'NotificationConfigurationStatus', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'aggregationDuration' => [ 'shape' => 'AggregationDuration', ], ], ], 'NotificationConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'NotificationConfigurationStructure', ], ], 'NotificationEvent' => [ 'type' => 'structure', 'required' => [ 'schemaVersion', 'id', 'sourceEventMetadata', 'messageComponents', 'notificationType', 'textParts', 'media', ], 'members' => [ 'schemaVersion' => [ 'shape' => 'SchemaVersion', ], 'id' => [ 'shape' => 'NotificationEventId', ], 'sourceEventMetadata' => [ 'shape' => 'SourceEventMetadata', ], 'messageComponents' => [ 'shape' => 'MessageComponents', ], 'sourceEventDetailUrl' => [ 'shape' => 'Url', ], 'sourceEventDetailUrlDisplayText' => [ 'shape' => 'String', ], 'notificationType' => [ 'shape' => 'NotificationType', ], 'eventStatus' => [ 'shape' => 'EventStatus', ], 'aggregationEventType' => [ 'shape' => 'AggregationEventType', ], 'aggregateNotificationEventArn' => [ 'shape' => 'NotificationEventArn', ], 'aggregationSummary' => [ 'shape' => 'AggregationSummary', ], 'startTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'endTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'textParts' => [ 'shape' => 'TextParts', ], 'media' => [ 'shape' => 'Media', ], ], ], 'NotificationEventArn' => [ 'type' => 'string', 'pattern' => 'arn:[-.a-z0-9]{1,63}:notifications:[-.a-z0-9]{1,63}:[0-9]{12}:configuration/[a-z0-9]{27}/event/[a-z0-9]{27}', ], 'NotificationEventId' => [ 'type' => 'string', 'pattern' => '[a-z0-9]{27}', ], 'NotificationEventOverview' => [ 'type' => 'structure', 'required' => [ 'arn', 'notificationConfigurationArn', 'relatedAccount', 'creationTime', 'notificationEvent', ], 'members' => [ 'arn' => [ 'shape' => 'NotificationEventArn', ], 'notificationConfigurationArn' => [ 'shape' => 'NotificationConfigurationArn', ], 'relatedAccount' => [ 'shape' => 'AccountId', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'notificationEvent' => [ 'shape' => 'NotificationEventSummary', ], 'aggregationEventType' => [ 'shape' => 'AggregationEventType', ], 'aggregateNotificationEventArn' => [ 'shape' => 'NotificationEventArn', ], 'aggregationSummary' => [ 'shape' => 'AggregationSummary', ], ], ], 'NotificationEventSummary' => [ 'type' => 'structure', 'required' => [ 'schemaVersion', 'sourceEventMetadata', 'messageComponents', 'eventStatus', 'notificationType', ], 'members' => [ 'schemaVersion' => [ 'shape' => 'SchemaVersion', ], 'sourceEventMetadata' => [ 'shape' => 'SourceEventMetadataSummary', ], 'messageComponents' => [ 'shape' => 'MessageComponentsSummary', ], 'eventStatus' => [ 'shape' => 'EventStatus', ], 'notificationType' => [ 'shape' => 'NotificationType', ], ], ], 'NotificationEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'NotificationEventOverview', ], ], 'NotificationHubOverview' => [ 'type' => 'structure', 'required' => [ 'notificationHubRegion', 'statusSummary', 'creationTime', ], 'members' => [ 'notificationHubRegion' => [ 'shape' => 'Region', ], 'statusSummary' => [ 'shape' => 'NotificationHubStatusSummary', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'lastActivationTime' => [ 'shape' => 'LastActivationTime', ], ], ], 'NotificationHubStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'REGISTERING', 'DEREGISTERING', 'INACTIVE', ], ], 'NotificationHubStatusReason' => [ 'type' => 'string', ], 'NotificationHubStatusSummary' => [ 'type' => 'structure', 'required' => [ 'status', 'reason', ], 'members' => [ 'status' => [ 'shape' => 'NotificationHubStatus', ], 'reason' => [ 'shape' => 'NotificationHubStatusReason', ], ], ], 'NotificationHubs' => [ 'type' => 'list', 'member' => [ 'shape' => 'NotificationHubOverview', ], ], 'NotificationType' => [ 'type' => 'string', 'enum' => [ 'ALERT', 'WARNING', 'ANNOUNCEMENT', 'INFORMATIONAL', ], ], 'NotificationsAccessForOrganization' => [ 'type' => 'structure', 'required' => [ 'accessStatus', ], 'members' => [ 'accessStatus' => [ 'shape' => 'AccessStatus', ], ], ], 'OrganizationalUnitId' => [ 'type' => 'string', 'pattern' => 'Root|ou-[0-9a-z]{4,32}-[a-z0-9]{8,32}', ], 'QuotaCode' => [ 'type' => 'string', ], 'Region' => [ 'type' => 'string', 'max' => 25, 'min' => 2, 'pattern' => '([a-z]{1,2})-([a-z]{1,15}-)+([0-9])', ], 'Regions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Region', ], 'min' => 1, ], 'RegisterNotificationHubRequest' => [ 'type' => 'structure', 'required' => [ 'notificationHubRegion', ], 'members' => [ 'notificationHubRegion' => [ 'shape' => 'Region', ], ], ], 'RegisterNotificationHubResponse' => [ 'type' => 'structure', 'required' => [ 'notificationHubRegion', 'statusSummary', 'creationTime', ], 'members' => [ 'notificationHubRegion' => [ 'shape' => 'Region', ], 'statusSummary' => [ 'shape' => 'NotificationHubStatusSummary', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'lastActivationTime' => [ 'shape' => 'LastActivationTime', ], ], ], 'Resource' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', ], 'arn' => [ 'shape' => 'Arn', ], 'detailUrl' => [ 'shape' => 'Url', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'ResourceId' => [ 'type' => 'string', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'ResourceId', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', ], 'Resources' => [ 'type' => 'list', 'member' => [ 'shape' => 'Resource', ], ], 'SampleAggregationDimensionValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'SampleAggregationDimensionValuesMemberString', ], 'max' => 50, 'min' => 0, ], 'SampleAggregationDimensionValuesMemberString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'SchemaVersion' => [ 'type' => 'string', 'enum' => [ 'v1.0', ], ], 'ServiceCode' => [ 'type' => 'string', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'serviceCode' => [ 'shape' => 'ServiceCode', ], 'quotaCode' => [ 'shape' => 'QuotaCode', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'Source' => [ 'type' => 'string', 'max' => 36, 'min' => 1, 'pattern' => 'aws.([a-z0-9\\-])+', ], 'SourceEventMetadata' => [ 'type' => 'structure', 'required' => [ 'eventTypeVersion', 'sourceEventId', 'relatedAccount', 'source', 'eventOccurrenceTime', 'eventType', 'relatedResources', ], 'members' => [ 'eventTypeVersion' => [ 'shape' => 'SourceEventMetadataEventTypeVersionString', ], 'sourceEventId' => [ 'shape' => 'String', ], 'eventOriginRegion' => [ 'shape' => 'SourceEventMetadataEventOriginRegionString', ], 'relatedAccount' => [ 'shape' => 'SourceEventMetadataRelatedAccountString', ], 'source' => [ 'shape' => 'Source', ], 'eventOccurrenceTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'eventType' => [ 'shape' => 'SourceEventMetadataEventTypeString', ], 'relatedResources' => [ 'shape' => 'Resources', ], ], ], 'SourceEventMetadataEventOriginRegionString' => [ 'type' => 'string', 'max' => 32, 'min' => 0, 'pattern' => '([a-z]{1,2})-([a-z]{1,15}-)+([0-9])', ], 'SourceEventMetadataEventTypeString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'SourceEventMetadataEventTypeVersionString' => [ 'type' => 'string', 'max' => 3, 'min' => 1, 'pattern' => '[0-9.]+', ], 'SourceEventMetadataRelatedAccountString' => [ 'type' => 'string', 'pattern' => '[0-9]{12}', ], 'SourceEventMetadataSummary' => [ 'type' => 'structure', 'required' => [ 'source', 'eventType', ], 'members' => [ 'eventOriginRegion' => [ 'shape' => 'SourceEventMetadataSummaryEventOriginRegionString', ], 'source' => [ 'shape' => 'String', ], 'eventType' => [ 'shape' => 'SourceEventMetadataSummaryEventTypeString', ], ], ], 'SourceEventMetadataSummaryEventOriginRegionString' => [ 'type' => 'string', 'max' => 32, 'min' => 0, ], 'SourceEventMetadataSummaryEventTypeString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'StatusSummaryByRegion' => [ 'type' => 'map', 'key' => [ 'shape' => 'Region', ], 'value' => [ 'shape' => 'EventRuleStatusSummary', ], ], 'String' => [ 'type' => 'string', ], 'SummarizationDimensionDetail' => [ 'type' => 'structure', 'required' => [ 'name', 'value', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], ], 'SummarizationDimensionDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'SummarizationDimensionDetail', ], ], 'SummarizationDimensionOverview' => [ 'type' => 'structure', 'required' => [ 'name', 'count', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'count' => [ 'shape' => 'Integer', ], 'sampleValues' => [ 'shape' => 'SampleAggregationDimensionValues', ], ], ], 'SummarizationDimensionOverviews' => [ 'type' => 'list', 'member' => [ 'shape' => 'SummarizationDimensionOverview', ], ], 'SyntheticTimestamp_date_time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TagKey' => [ 'type' => 'string', 'pattern' => '(?!aws:).{1,128}', ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'arn', 'tags', ], 'members' => [ 'arn' => [ 'shape' => 'NotificationConfigurationArn', 'location' => 'uri', 'locationName' => 'arn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'Tags' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagsMemberString', ], 'max' => 50, 'min' => 0, ], 'TagsMemberString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'TextByLocale' => [ 'type' => 'map', 'key' => [ 'shape' => 'LocaleCode', ], 'value' => [ 'shape' => 'String', ], ], 'TextPartId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[A-Za-z0-9_]+', ], 'TextPartReference' => [ 'type' => 'string', ], 'TextPartType' => [ 'type' => 'string', 'enum' => [ 'LOCALIZED_TEXT', 'PLAIN_TEXT', 'URL', ], ], 'TextPartValue' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'TextPartType', ], 'displayText' => [ 'shape' => 'TextPartValueDisplayTextString', ], 'textByLocale' => [ 'shape' => 'TextByLocale', ], 'url' => [ 'shape' => 'Url', ], ], ], 'TextPartValueDisplayTextString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'TextParts' => [ 'type' => 'map', 'key' => [ 'shape' => 'TextPartId', ], 'value' => [ 'shape' => 'TextPartValue', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'serviceCode' => [ 'shape' => 'ServiceCode', ], 'quotaCode' => [ 'shape' => 'QuotaCode', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'arn', 'tagKeys', ], 'members' => [ 'arn' => [ 'shape' => 'NotificationConfigurationArn', 'location' => 'uri', 'locationName' => 'arn', ], 'tagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateEventRuleRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'EventRuleArn', 'location' => 'uri', 'locationName' => 'arn', ], 'eventPattern' => [ 'shape' => 'EventRuleEventPattern', ], 'regions' => [ 'shape' => 'Regions', ], ], ], 'UpdateEventRuleResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'notificationConfigurationArn', 'statusSummaryByRegion', ], 'members' => [ 'arn' => [ 'shape' => 'EventRuleArn', ], 'notificationConfigurationArn' => [ 'shape' => 'NotificationConfigurationArn', ], 'statusSummaryByRegion' => [ 'shape' => 'StatusSummaryByRegion', ], ], ], 'UpdateNotificationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'NotificationConfigurationArn', 'location' => 'uri', 'locationName' => 'arn', ], 'name' => [ 'shape' => 'NotificationConfigurationName', ], 'description' => [ 'shape' => 'NotificationConfigurationDescription', ], 'aggregationDuration' => [ 'shape' => 'AggregationDuration', ], ], ], 'UpdateNotificationConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'NotificationConfigurationArn', ], ], ], 'Url' => [ 'type' => 'string', 'max' => 2000, 'min' => 0, 'pattern' => '(https?)://.*', ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'name', 'message', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'fieldValidationFailed', 'other', ], ], ],];
