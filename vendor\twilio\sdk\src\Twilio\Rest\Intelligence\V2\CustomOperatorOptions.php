<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Intelligence
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Intelligence\V2;

use Twilio\Options;
use Twilio\Values;

abstract class CustomOperatorOptions
{



    /**
     * @param string $availability Returns Custom Operators with the provided availability type. Possible values: internal, beta, public, retired.
     * @param string $languageCode Returns Custom Operators that support the provided language code.
     * @return ReadCustomOperatorOptions Options builder
     */
    public static function read(
        
        string $availability = Values::NONE,
        string $languageCode = Values::NONE

    ): ReadCustomOperatorOptions
    {
        return new ReadCustomOperatorOptions(
            $availability,
            $languageCode
        );
    }

    /**
     * @param string $ifMatch The If-Match HTTP request header
     * @return UpdateCustomOperatorOptions Options builder
     */
    public static function update(
        
        string $ifMatch = Values::NONE

    ): UpdateCustomOperatorOptions
    {
        return new UpdateCustomOperatorOptions(
            $ifMatch
        );
    }

}




class ReadCustomOperatorOptions extends Options
    {
    /**
     * @param string $availability Returns Custom Operators with the provided availability type. Possible values: internal, beta, public, retired.
     * @param string $languageCode Returns Custom Operators that support the provided language code.
     */
    public function __construct(
        
        string $availability = Values::NONE,
        string $languageCode = Values::NONE

    ) {
        $this->options['availability'] = $availability;
        $this->options['languageCode'] = $languageCode;
    }

    /**
     * Returns Custom Operators with the provided availability type. Possible values: internal, beta, public, retired.
     *
     * @param string $availability Returns Custom Operators with the provided availability type. Possible values: internal, beta, public, retired.
     * @return $this Fluent Builder
     */
    public function setAvailability(string $availability): self
    {
        $this->options['availability'] = $availability;
        return $this;
    }

    /**
     * Returns Custom Operators that support the provided language code.
     *
     * @param string $languageCode Returns Custom Operators that support the provided language code.
     * @return $this Fluent Builder
     */
    public function setLanguageCode(string $languageCode): self
    {
        $this->options['languageCode'] = $languageCode;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Intelligence.V2.ReadCustomOperatorOptions ' . $options . ']';
    }
}

class UpdateCustomOperatorOptions extends Options
    {
    /**
     * @param string $ifMatch The If-Match HTTP request header
     */
    public function __construct(
        
        string $ifMatch = Values::NONE

    ) {
        $this->options['ifMatch'] = $ifMatch;
    }

    /**
     * The If-Match HTTP request header
     *
     * @param string $ifMatch The If-Match HTTP request header
     * @return $this Fluent Builder
     */
    public function setIfMatch(string $ifMatch): self
    {
        $this->options['ifMatch'] = $ifMatch;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Intelligence.V2.UpdateCustomOperatorOptions ' . $options . ']';
    }
}

