{"name": "spatie/backtrace", "description": "A better backtrace", "license": "MIT", "keywords": ["spatie", "backtrace"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "homepage": "https://github.com/spatie/backtrace", "funding": [{"type": "github", "url": "https://github.com/sponsors/spatie"}, {"type": "other", "url": "https://spatie.be/open-source/support-us"}], "require": {"php": "^7.3 || ^8.0"}, "require-dev": {"ext-json": "*", "laravel/serializable-closure": "^1.3 || ^2.0", "phpunit/phpunit": "^9.3 || ^11.4.3", "spatie/phpunit-snapshot-assertions": "^4.2 || ^5.1.6", "symfony/var-dumper": "^5.1 || ^6.0 || ^7.0"}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"Spatie\\Backtrace\\": "src"}}, "autoload-dev": {"psr-4": {"Spatie\\Backtrace\\Tests\\": "tests"}}, "config": {"sort-packages": true}, "scripts": {"format": "vendor/bin/php-cs-fixer fix --allow-risky=yes", "psalm": "vendor/bin/psalm", "test": "vendor/bin/phpunit", "test-coverage": "vendor/bin/phpunit --coverage-html coverage"}}