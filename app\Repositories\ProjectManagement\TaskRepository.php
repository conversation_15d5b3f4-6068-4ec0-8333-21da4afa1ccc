<?php

namespace App\Repositories\ProjectManagement;

use App\Models\ProjectManagement\Task;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;

/**
 * مستودع المهام المتقدم
 *
 * هذا المستودع يحتوي على جميع العمليات المتعلقة بالمهام
 * مع دعم المهام الفرعية والتبعيات والبحث المتقدم
 *
 * @package App\Repositories\ProjectManagement
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
class TaskRepository extends BaseRepository
{
    /**
     * منشئ مستودع المهام
     */
    public function __construct()
    {
        parent::__construct(new Task());
    }

    /**
     * الحصول على مهام مشروع معين
     *
     * @param int $projectId معرف المشروع
     * @param array $relations العلاقات المطلوب تحميلها
     * @return Collection
     */
    public function getProjectTasks(int $projectId, array $relations = []): Collection
    {
        return $this->findAllBy(['project_id' => $projectId], ['*'], $relations);
    }

    /**
     * الحصول على المهام الرئيسية لمشروع معين
     *
     * @param int $projectId معرف المشروع
     * @param array $relations العلاقات المطلوب تحميلها
     * @return Collection
     */
    public function getMainProjectTasks(int $projectId, array $relations = []): Collection
    {
        return $this->newQuery()
            ->where('project_id', $projectId)
            ->whereNull('parent_task_id')
            ->with($relations)
            ->orderBy('order_index')
            ->get();
    }

    /**
     * الحصول على المهام الفرعية لمهمة معينة
     *
     * @param int $parentTaskId معرف المهمة الأب
     * @param array $relations العلاقات المطلوب تحميلها
     * @return Collection
     */
    public function getSubTasks(int $parentTaskId, array $relations = []): Collection
    {
        return $this->findAllBy(['parent_task_id' => $parentTaskId], ['*'], $relations);
    }

    /**
     * الحصول على مهام مستخدم معين
     *
     * @param int $userId معرف المستخدم
     * @param array $statuses الحالات المطلوبة
     * @param array $relations العلاقات المطلوب تحميلها
     * @return Collection
     */
    public function getUserTasks(int $userId, array $statuses = [], array $relations = []): Collection
    {
        $query = $this->newQuery()
            ->where('assigned_to', $userId)
            ->with($relations);

        if (!empty($statuses)) {
            $query->whereIn('status', $statuses);
        }

        return $query->orderBy('priority', 'desc')
                    ->orderBy('due_date')
                    ->get();
    }

    /**
     * الحصول على المهام المتأخرة
     *
     * @param array $relations العلاقات المطلوب تحميلها
     * @return Collection
     */
    public function getOverdueTasks(array $relations = []): Collection
    {
        $query = $this->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->where('due_date', '<', Carbon::now())
                    ->where('status', '!=', 'completed')
                    ->get();
    }

    /**
     * الحصول على المهام المكتملة
     *
     * @param array $relations العلاقات المطلوب تحميلها
     * @return Collection
     */
    public function getCompletedTasks(array $relations = []): Collection
    {
        $query = $this->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->where('status', 'completed')->get();
    }

    /**
     * الحصول على المهام القادمة للاستحقاق
     *
     * @param int $days عدد الأيام
     * @param array $relations العلاقات المطلوب تحميلها
     * @return Collection
     */
    public function getTasksDueSoon(int $days = 7, array $relations = []): Collection
    {
        $dueDate = Carbon::now()->addDays($days);

        return $this->newQuery()
            ->where('due_date', '<=', $dueDate)
            ->where('due_date', '>=', Carbon::now())
            ->where('status', '!=', 'completed')
            ->with($relations)
            ->orderBy('due_date')
            ->get();
    }

    /**
     * البحث المتقدم في المهام
     *
     * @param array $filters مرشحات البحث
     * @param int $perPage عدد النتائج في الصفحة
     * @return LengthAwarePaginator
     */
    public function searchTasks(array $filters, int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->newQuery()->with(['project', 'assignedUser', 'creator', 'milestone']);

        // فلترة حسب النص
        if (!empty($filters['search'])) {
            $searchTerm = $filters['search'];
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('description', 'LIKE', "%{$searchTerm}%");
            });
        }

        // فلترة حسب المشروع
        if (!empty($filters['project_id'])) {
            $query->where('project_id', $filters['project_id']);
        }

        // فلترة حسب الحالة
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // فلترة حسب النوع
        if (!empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        // فلترة حسب الأولوية
        if (!empty($filters['priority'])) {
            $query->where('priority', $filters['priority']);
        }

        // فلترة حسب المكلف
        if (!empty($filters['assigned_to'])) {
            $query->where('assigned_to', $filters['assigned_to']);
        }

        // فلترة حسب المعلم
        if (!empty($filters['milestone_id'])) {
            $query->where('milestone_id', $filters['milestone_id']);
        }

        // فلترة حسب تاريخ الاستحقاق
        if (!empty($filters['due_date_from'])) {
            $query->where('due_date', '>=', $filters['due_date_from']);
        }

        if (!empty($filters['due_date_to'])) {
            $query->where('due_date', '<=', $filters['due_date_to']);
        }

        // فلترة المهام الرئيسية فقط
        if (!empty($filters['main_tasks_only'])) {
            $query->whereNull('parent_task_id');
        }

        // فلترة المهام الفرعية فقط
        if (!empty($filters['sub_tasks_only'])) {
            $query->whereNotNull('parent_task_id');
        }

        // ترتيب النتائج
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortBy, $sortDirection);

        return $query->paginate($perPage);
    }

    /**
     * الحصول على إحصائيات المهام
     *
     * @param int|null $projectId معرف المشروع (اختياري)
     * @return array
     */
    public function getTaskStatistics(?int $projectId = null): array
    {
        $query = $this->newQuery();

        if ($projectId) {
            $query->where('project_id', $projectId);
        }

        $total = $query->count();
        $completed = $query->where('status', 'completed')->count();
        $inProgress = $query->where('status', 'in_progress')->count();
        $todo = $query->where('status', 'todo')->count();
        $blocked = $query->where('status', 'blocked')->count();
        $overdueQuery = $this->newQuery();
        if ($projectId) {
            $overdueQuery->where('project_id', $projectId);
        }
        $overdue = $overdueQuery->where('due_date', '<', Carbon::now())
                                ->where('status', '!=', 'completed')
                                ->count();

        return [
            'total' => $total,
            'completed' => $completed,
            'in_progress' => $inProgress,
            'todo' => $todo,
            'blocked' => $blocked,
            'overdue' => $overdue,
            'completion_rate' => $total > 0 ? round(($completed / $total) * 100, 2) : 0,
        ];
    }

    /**
     * الحصول على إحصائيات المهام حسب الحالة
     *
     * @param int|null $projectId معرف المشروع
     * @return array
     */
    public function getTasksByStatus(?int $projectId = null): array
    {
        $query = $this->newQuery();

        if ($projectId) {
            $query->where('project_id', $projectId);
        }

        return $query->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();
    }

    /**
     * الحصول على إحصائيات المهام حسب النوع
     *
     * @param int|null $projectId معرف المشروع
     * @return array
     */
    public function getTasksByType(?int $projectId = null): array
    {
        $query = $this->newQuery();

        if ($projectId) {
            $query->where('project_id', $projectId);
        }

        return $query->selectRaw('type, COUNT(*) as count')
            ->groupBy('type')
            ->pluck('count', 'type')
            ->toArray();
    }

    /**
     * الحصول على أكثر المستخدمين نشاطاً
     *
     * @param int $limit عدد النتائج
     * @param int|null $projectId معرف المشروع
     * @return Collection
     */
    public function getMostActiveUsers(int $limit = 10, ?int $projectId = null): Collection
    {
        $query = $this->newQuery();

        if ($projectId) {
            $query->where('project_id', $projectId);
        }

        return $query->selectRaw('assigned_to, COUNT(*) as tasks_count')
            ->whereNotNull('assigned_to')
            ->groupBy('assigned_to')
            ->orderByDesc('tasks_count')
            ->limit($limit)
            ->with('assignedUser')
            ->get();
    }

    /**
     * إنشاء مهمة فرعية
     *
     * @param int $parentTaskId معرف المهمة الأب
     * @param array $data بيانات المهمة الجديدة
     * @return Task
     */
    public function createSubTask(int $parentTaskId, array $data): Task
    {
        $parentTask = $this->findOrFail($parentTaskId);

        $data['parent_task_id'] = $parentTaskId;
        $data['project_id'] = $parentTask->project_id;

        // تحديد ترتيب المهمة الفرعية
        if (!isset($data['order_index'])) {
            $maxOrder = $this->newQuery()
                ->where('parent_task_id', $parentTaskId)
                ->max('order_index');
            $data['order_index'] = ($maxOrder ?? 0) + 1;
        }

        return $this->create($data);
    }

    /**
     * إضافة تبعية بين مهمتين
     *
     * @param int $taskId معرف المهمة
     * @param int $dependsOnTaskId معرف المهمة التي تعتمد عليها
     * @param string $dependencyType نوع التبعية
     * @param int $lagDays أيام التأخير
     * @return bool
     */
    public function addTaskDependency(int $taskId, int $dependsOnTaskId, string $dependencyType = 'finish_to_start', int $lagDays = 0): bool
    {
        $task = $this->findOrFail($taskId);

        $task->dependencies()->syncWithoutDetaching([
            $dependsOnTaskId => [
                'dependency_type' => $dependencyType,
                'lag_days' => $lagDays,
                'created_by' => auth()->id(),
            ]
        ]);

        return true;
    }

    /**
     * إزالة تبعية بين مهمتين
     *
     * @param int $taskId معرف المهمة
     * @param int $dependsOnTaskId معرف المهمة التي تعتمد عليها
     * @return bool
     */
    public function removeTaskDependency(int $taskId, int $dependsOnTaskId): bool
    {
        $task = $this->findOrFail($taskId);
        $task->dependencies()->detach($dependsOnTaskId);
        return true;
    }

    /**
     * إضافة متابع للمهمة
     *
     * @param int $taskId معرف المهمة
     * @param int $userId معرف المستخدم
     * @param string $reason سبب المتابعة
     * @return bool
     */
    public function addWatcher(int $taskId, int $userId, string $reason = 'manual'): bool
    {
        $task = $this->findOrFail($taskId);
        $task->addWatcher($userId);
        return true;
    }

    /**
     * إزالة متابع من المهمة
     *
     * @param int $taskId معرف المهمة
     * @param int $userId معرف المستخدم
     * @return bool
     */
    public function removeWatcher(int $taskId, int $userId): bool
    {
        $task = $this->findOrFail($taskId);
        $task->removeWatcher($userId);
        return true;
    }

    /**
     * تحديث ترتيب المهام
     *
     * @param array $taskOrders مصفوفة [task_id => order_index]
     * @return bool
     */
    public function updateTasksOrder(array $taskOrders): bool
    {
        foreach ($taskOrders as $taskId => $orderIndex) {
            $this->update($taskId, ['order_index' => $orderIndex]);
        }

        return true;
    }

    /**
     * نسخ مهمة مع جميع خصائصها
     *
     * @param int $taskId معرف المهمة
     * @param array $overrides البيانات المراد تغييرها
     * @return Task
     */
    public function duplicateTask(int $taskId, array $overrides = []): Task
    {
        $originalTask = $this->findOrFail($taskId, ['*'], ['subTasks', 'watchers']);

        $taskData = $originalTask->toArray();
        unset($taskData['id'], $taskData['created_at'], $taskData['updated_at'], $taskData['completed_at']);

        // تطبيق التغييرات المطلوبة
        $taskData = array_merge($taskData, $overrides);

        // إنشاء المهمة الجديدة
        $newTask = $this->create($taskData);

        // نسخ المهام الفرعية
        foreach ($originalTask->subTasks as $subTask) {
            $this->duplicateTask($subTask->id, ['parent_task_id' => $newTask->id]);
        }

        // نسخ المتابعين
        foreach ($originalTask->watchers as $watcher) {
            $newTask->addWatcher($watcher->id);
        }

        return $newTask;
    }
}
