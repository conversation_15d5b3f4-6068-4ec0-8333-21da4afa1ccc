<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Organization Public API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\PreviewIam\Versionless\Organization;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;


/**
 * @property string $id
 * @property string $externalId
 * @property string $userName
 * @property string $displayName
 * @property string $name
 * @property string[] $emails
 * @property bool $active
 * @property string $locale
 * @property string $timezone
 * @property string[] $schemas
 * @property string $meta
 */
class UserInstance extends InstanceResource
{
    /**
     * Initialize the UserInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $organizationSid
     * @param string $id
     */
    public function __construct(Version $version, array $payload, string $organizationSid, ?string $id = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'id' => Values::array_get($payload, 'id'),
            'externalId' => Values::array_get($payload, 'external_id'),
            'userName' => Values::array_get($payload, 'user_name'),
            'displayName' => Values::array_get($payload, 'display_name'),
            'name' => Values::array_get($payload, 'name'),
            'emails' => Values::array_get($payload, 'emails'),
            'active' => Values::array_get($payload, 'active'),
            'locale' => Values::array_get($payload, 'locale'),
            'timezone' => Values::array_get($payload, 'timezone'),
            'schemas' => Values::array_get($payload, 'schemas'),
            'meta' => Values::array_get($payload, 'meta'),
        ];

        $this->solution = ['organizationSid' => $organizationSid, 'id' => $id ?: $this->properties['id'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return UserContext Context for this UserInstance
     */
    protected function proxy(): UserContext
    {
        if (!$this->context) {
            $this->context = new UserContext(
                $this->version,
                $this->solution['organizationSid'],
                $this->solution['id']
            );
        }

        return $this->context;
    }

    /**
     * Delete the UserInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->proxy()->delete();
    }

    /**
     * Fetch the UserInstance
     *
     * @return UserInstance Fetched UserInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): UserInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Update the UserInstance
     *
     * @param ScimUser $scimUser
     * @param array|Options $options Optional Arguments
     * @return UserInstance Updated UserInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(ScimUser $scimUser, array $options = []): UserInstance
    {

        return $this->proxy()->update($scimUser, $options);
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.PreviewIam.Versionless.UserInstance ' . \implode(' ', $context) . ']';
    }
}

