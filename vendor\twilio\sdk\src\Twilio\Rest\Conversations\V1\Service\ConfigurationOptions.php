<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Conversations
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Conversations\V1\Service;

use Twilio\Options;
use Twilio\Values;

abstract class ConfigurationOptions
{

    /**
     * @param string $defaultConversationCreatorRoleSid The conversation-level role assigned to a conversation creator when they join a new conversation. See [Conversation Role](https://www.twilio.com/docs/conversations/api/role-resource) for more info about roles.
     * @param string $defaultConversationRoleSid The conversation-level role assigned to users when they are added to a conversation. See [Conversation Role](https://www.twilio.com/docs/conversations/api/role-resource) for more info about roles.
     * @param string $defaultChatServiceRoleSid The service-level role assigned to users when they are added to the service. See [Conversation Role](https://www.twilio.com/docs/conversations/api/role-resource) for more info about roles.
     * @param bool $reachabilityEnabled Whether the [Reachability Indicator](https://www.twilio.com/docs/conversations/reachability) is enabled for this Conversations Service. The default is `false`.
     * @return UpdateConfigurationOptions Options builder
     */
    public static function update(
        
        string $defaultConversationCreatorRoleSid = Values::NONE,
        string $defaultConversationRoleSid = Values::NONE,
        string $defaultChatServiceRoleSid = Values::NONE,
        bool $reachabilityEnabled = Values::BOOL_NONE

    ): UpdateConfigurationOptions
    {
        return new UpdateConfigurationOptions(
            $defaultConversationCreatorRoleSid,
            $defaultConversationRoleSid,
            $defaultChatServiceRoleSid,
            $reachabilityEnabled
        );
    }

}


class UpdateConfigurationOptions extends Options
    {
    /**
     * @param string $defaultConversationCreatorRoleSid The conversation-level role assigned to a conversation creator when they join a new conversation. See [Conversation Role](https://www.twilio.com/docs/conversations/api/role-resource) for more info about roles.
     * @param string $defaultConversationRoleSid The conversation-level role assigned to users when they are added to a conversation. See [Conversation Role](https://www.twilio.com/docs/conversations/api/role-resource) for more info about roles.
     * @param string $defaultChatServiceRoleSid The service-level role assigned to users when they are added to the service. See [Conversation Role](https://www.twilio.com/docs/conversations/api/role-resource) for more info about roles.
     * @param bool $reachabilityEnabled Whether the [Reachability Indicator](https://www.twilio.com/docs/conversations/reachability) is enabled for this Conversations Service. The default is `false`.
     */
    public function __construct(
        
        string $defaultConversationCreatorRoleSid = Values::NONE,
        string $defaultConversationRoleSid = Values::NONE,
        string $defaultChatServiceRoleSid = Values::NONE,
        bool $reachabilityEnabled = Values::BOOL_NONE

    ) {
        $this->options['defaultConversationCreatorRoleSid'] = $defaultConversationCreatorRoleSid;
        $this->options['defaultConversationRoleSid'] = $defaultConversationRoleSid;
        $this->options['defaultChatServiceRoleSid'] = $defaultChatServiceRoleSid;
        $this->options['reachabilityEnabled'] = $reachabilityEnabled;
    }

    /**
     * The conversation-level role assigned to a conversation creator when they join a new conversation. See [Conversation Role](https://www.twilio.com/docs/conversations/api/role-resource) for more info about roles.
     *
     * @param string $defaultConversationCreatorRoleSid The conversation-level role assigned to a conversation creator when they join a new conversation. See [Conversation Role](https://www.twilio.com/docs/conversations/api/role-resource) for more info about roles.
     * @return $this Fluent Builder
     */
    public function setDefaultConversationCreatorRoleSid(string $defaultConversationCreatorRoleSid): self
    {
        $this->options['defaultConversationCreatorRoleSid'] = $defaultConversationCreatorRoleSid;
        return $this;
    }

    /**
     * The conversation-level role assigned to users when they are added to a conversation. See [Conversation Role](https://www.twilio.com/docs/conversations/api/role-resource) for more info about roles.
     *
     * @param string $defaultConversationRoleSid The conversation-level role assigned to users when they are added to a conversation. See [Conversation Role](https://www.twilio.com/docs/conversations/api/role-resource) for more info about roles.
     * @return $this Fluent Builder
     */
    public function setDefaultConversationRoleSid(string $defaultConversationRoleSid): self
    {
        $this->options['defaultConversationRoleSid'] = $defaultConversationRoleSid;
        return $this;
    }

    /**
     * The service-level role assigned to users when they are added to the service. See [Conversation Role](https://www.twilio.com/docs/conversations/api/role-resource) for more info about roles.
     *
     * @param string $defaultChatServiceRoleSid The service-level role assigned to users when they are added to the service. See [Conversation Role](https://www.twilio.com/docs/conversations/api/role-resource) for more info about roles.
     * @return $this Fluent Builder
     */
    public function setDefaultChatServiceRoleSid(string $defaultChatServiceRoleSid): self
    {
        $this->options['defaultChatServiceRoleSid'] = $defaultChatServiceRoleSid;
        return $this;
    }

    /**
     * Whether the [Reachability Indicator](https://www.twilio.com/docs/conversations/reachability) is enabled for this Conversations Service. The default is `false`.
     *
     * @param bool $reachabilityEnabled Whether the [Reachability Indicator](https://www.twilio.com/docs/conversations/reachability) is enabled for this Conversations Service. The default is `false`.
     * @return $this Fluent Builder
     */
    public function setReachabilityEnabled(bool $reachabilityEnabled): self
    {
        $this->options['reachabilityEnabled'] = $reachabilityEnabled;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Conversations.V1.UpdateConfigurationOptions ' . $options . ']';
    }
}

