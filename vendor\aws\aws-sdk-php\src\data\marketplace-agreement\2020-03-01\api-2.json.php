<?php
// This file was auto-generated from sdk-root/src/data/marketplace-agreement/2020-03-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-03-01', 'endpointPrefix' => 'agreement-marketplace', 'jsonVersion' => '1.0', 'protocol' => 'json', 'serviceAbbreviation' => 'Agreement Service', 'serviceFullName' => 'AWS Marketplace Agreement Service', 'serviceId' => 'Marketplace Agreement', 'signatureVersion' => 'v4', 'signingName' => 'aws-marketplace', 'targetPrefix' => 'AWSMPCommerceService_v20200301', 'uid' => 'marketplace-agreement-2020-03-01', ], 'operations' => [ 'DescribeAgreement' => [ 'name' => 'DescribeAgreement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAgreementInput', ], 'output' => [ 'shape' => 'DescribeAgreementOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetAgreementTerms' => [ 'name' => 'GetAgreementTerms', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAgreementTermsInput', ], 'output' => [ 'shape' => 'GetAgreementTermsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'SearchAgreements' => [ 'name' => 'SearchAgreements', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SearchAgreementsInput', ], 'output' => [ 'shape' => 'SearchAgreementsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'AWSAccountId' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^[0-9]+$', ], 'AcceptedTerm' => [ 'type' => 'structure', 'members' => [ 'byolPricingTerm' => [ 'shape' => 'ByolPricingTerm', ], 'configurableUpfrontPricingTerm' => [ 'shape' => 'ConfigurableUpfrontPricingTerm', ], 'fixedUpfrontPricingTerm' => [ 'shape' => 'FixedUpfrontPricingTerm', ], 'freeTrialPricingTerm' => [ 'shape' => 'FreeTrialPricingTerm', ], 'legalTerm' => [ 'shape' => 'LegalTerm', ], 'paymentScheduleTerm' => [ 'shape' => 'PaymentScheduleTerm', ], 'recurringPaymentTerm' => [ 'shape' => 'RecurringPaymentTerm', ], 'renewalTerm' => [ 'shape' => 'RenewalTerm', ], 'supportTerm' => [ 'shape' => 'SupportTerm', ], 'usageBasedPricingTerm' => [ 'shape' => 'UsageBasedPricingTerm', ], 'validityTerm' => [ 'shape' => 'ValidityTerm', ], ], 'union' => true, ], 'AcceptedTermList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AcceptedTerm', ], ], 'Acceptor' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AWSAccountId', ], ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'requestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'AgreementResourceType' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z]+$', ], 'AgreementStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'ARCHIVED', 'CANCELLED', 'EXPIRED', 'RENEWED', 'REPLACED', 'ROLLED_BACK', 'SUPERSEDED', 'TERMINATED', ], ], 'AgreementType' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[A-Za-z]+$', ], 'AgreementViewSummary' => [ 'type' => 'structure', 'members' => [ 'acceptanceTime' => [ 'shape' => 'Timestamp', ], 'acceptor' => [ 'shape' => 'Acceptor', ], 'agreementId' => [ 'shape' => 'ResourceId', ], 'agreementType' => [ 'shape' => 'AgreementType', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'proposalSummary' => [ 'shape' => 'ProposalSummary', ], 'proposer' => [ 'shape' => 'Proposer', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'AgreementStatus', ], ], ], 'AgreementViewSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgreementViewSummary', ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'BoundedString' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '^(.)+$', ], 'ByolPricingTerm' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'UnversionedTermType', ], ], ], 'Catalog' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z]+$', ], 'ConfigurableUpfrontPricingTerm' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'ConfigurableUpfrontPricingTermConfiguration', ], 'currencyCode' => [ 'shape' => 'CurrencyCode', ], 'rateCards' => [ 'shape' => 'ConfigurableUpfrontRateCardList', ], 'type' => [ 'shape' => 'UnversionedTermType', ], ], ], 'ConfigurableUpfrontPricingTermConfiguration' => [ 'type' => 'structure', 'required' => [ 'dimensions', 'selectorValue', ], 'members' => [ 'dimensions' => [ 'shape' => 'DimensionList', ], 'selectorValue' => [ 'shape' => 'BoundedString', ], ], ], 'ConfigurableUpfrontRateCardItem' => [ 'type' => 'structure', 'members' => [ 'constraints' => [ 'shape' => 'Constraints', ], 'rateCard' => [ 'shape' => 'RateCardList', ], 'selector' => [ 'shape' => 'Selector', ], ], ], 'ConfigurableUpfrontRateCardList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigurableUpfrontRateCardItem', ], ], 'Constraints' => [ 'type' => 'structure', 'members' => [ 'multipleDimensionSelection' => [ 'shape' => 'BoundedString', ], 'quantityConfiguration' => [ 'shape' => 'BoundedString', ], ], ], 'CurrencyCode' => [ 'type' => 'string', 'max' => 3, 'min' => 3, 'pattern' => '^[A-Z]+$', ], 'DescribeAgreementInput' => [ 'type' => 'structure', 'required' => [ 'agreementId', ], 'members' => [ 'agreementId' => [ 'shape' => 'ResourceId', ], ], ], 'DescribeAgreementOutput' => [ 'type' => 'structure', 'members' => [ 'acceptanceTime' => [ 'shape' => 'Timestamp', ], 'acceptor' => [ 'shape' => 'Acceptor', ], 'agreementId' => [ 'shape' => 'ResourceId', ], 'agreementType' => [ 'shape' => 'AgreementType', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'estimatedCharges' => [ 'shape' => 'EstimatedCharges', ], 'proposalSummary' => [ 'shape' => 'ProposalSummary', ], 'proposer' => [ 'shape' => 'Proposer', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'AgreementStatus', ], ], ], 'Dimension' => [ 'type' => 'structure', 'required' => [ 'dimensionKey', 'dimensionValue', ], 'members' => [ 'dimensionKey' => [ 'shape' => 'BoundedString', ], 'dimensionValue' => [ 'shape' => 'ZeroValueInteger', ], ], ], 'DimensionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Dimension', ], 'min' => 1, ], 'DocumentItem' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'BoundedString', ], 'url' => [ 'shape' => 'BoundedString', ], 'version' => [ 'shape' => 'BoundedString', ], ], ], 'DocumentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentItem', ], ], 'EstimatedCharges' => [ 'type' => 'structure', 'members' => [ 'agreementValue' => [ 'shape' => 'BoundedString', ], 'currencyCode' => [ 'shape' => 'CurrencyCode', ], ], ], 'ExceptionMessage' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'Filter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'FilterName', ], 'values' => [ 'shape' => 'FilterValueList', ], ], ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], 'max' => 10, 'min' => 1, ], 'FilterName' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^[A-Za-z_]+$', ], 'FilterValue' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[A-Za-z0-9+:_-]+$', ], 'FilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterValue', ], 'max' => 1, 'min' => 1, ], 'FixedUpfrontPricingTerm' => [ 'type' => 'structure', 'members' => [ 'currencyCode' => [ 'shape' => 'CurrencyCode', ], 'duration' => [ 'shape' => 'BoundedString', ], 'grants' => [ 'shape' => 'GrantList', ], 'price' => [ 'shape' => 'BoundedString', ], 'type' => [ 'shape' => 'UnversionedTermType', ], ], ], 'FreeTrialPricingTerm' => [ 'type' => 'structure', 'members' => [ 'duration' => [ 'shape' => 'BoundedString', ], 'grants' => [ 'shape' => 'GrantList', ], 'type' => [ 'shape' => 'UnversionedTermType', ], ], ], 'GetAgreementTermsInput' => [ 'type' => 'structure', 'required' => [ 'agreementId', ], 'members' => [ 'agreementId' => [ 'shape' => 'ResourceId', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetAgreementTermsOutput' => [ 'type' => 'structure', 'members' => [ 'acceptedTerms' => [ 'shape' => 'AcceptedTermList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GrantItem' => [ 'type' => 'structure', 'members' => [ 'dimensionKey' => [ 'shape' => 'BoundedString', ], 'maxQuantity' => [ 'shape' => 'PositiveIntegerWithDefaultValueOne', ], ], ], 'GrantList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GrantItem', ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'requestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, 'fault' => true, ], 'LegalTerm' => [ 'type' => 'structure', 'members' => [ 'documents' => [ 'shape' => 'DocumentList', ], 'type' => [ 'shape' => 'UnversionedTermType', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'NextToken' => [ 'type' => 'string', 'max' => 8192, 'min' => 0, 'pattern' => '^[a-zA-Z0-9+/=]+$', ], 'OfferId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^\\S{1,64}$', ], 'PaymentScheduleTerm' => [ 'type' => 'structure', 'members' => [ 'currencyCode' => [ 'shape' => 'CurrencyCode', ], 'schedule' => [ 'shape' => 'ScheduleList', ], 'type' => [ 'shape' => 'UnversionedTermType', ], ], ], 'PositiveIntegerWithDefaultValueOne' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'ProposalSummary' => [ 'type' => 'structure', 'members' => [ 'offerId' => [ 'shape' => 'OfferId', ], 'resources' => [ 'shape' => 'Resources', ], ], ], 'Proposer' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AWSAccountId', ], ], ], 'RateCardItem' => [ 'type' => 'structure', 'members' => [ 'dimensionKey' => [ 'shape' => 'BoundedString', ], 'price' => [ 'shape' => 'BoundedString', ], ], ], 'RateCardList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RateCardItem', ], ], 'RecurringPaymentTerm' => [ 'type' => 'structure', 'members' => [ 'billingPeriod' => [ 'shape' => 'BoundedString', ], 'currencyCode' => [ 'shape' => 'CurrencyCode', ], 'price' => [ 'shape' => 'BoundedString', ], 'type' => [ 'shape' => 'UnversionedTermType', ], ], ], 'RenewalTerm' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'RenewalTermConfiguration', ], 'type' => [ 'shape' => 'UnversionedTermType', ], ], ], 'RenewalTermConfiguration' => [ 'type' => 'structure', 'required' => [ 'enableAutoRenew', ], 'members' => [ 'enableAutoRenew' => [ 'shape' => 'Boolean', ], ], ], 'RequestId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[A-Za-z0-9-]+$', ], 'Resource' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'type' => [ 'shape' => 'AgreementResourceType', ], ], ], 'ResourceId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[A-Za-z0-9_/-]+$', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'requestId' => [ 'shape' => 'RequestId', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'Agreement', ], ], 'Resources' => [ 'type' => 'list', 'member' => [ 'shape' => 'Resource', ], ], 'ScheduleItem' => [ 'type' => 'structure', 'members' => [ 'chargeAmount' => [ 'shape' => 'BoundedString', ], 'chargeDate' => [ 'shape' => 'Timestamp', ], ], ], 'ScheduleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScheduleItem', ], ], 'SearchAgreementsInput' => [ 'type' => 'structure', 'members' => [ 'catalog' => [ 'shape' => 'Catalog', ], 'filters' => [ 'shape' => 'FilterList', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'sort' => [ 'shape' => 'Sort', ], ], ], 'SearchAgreementsOutput' => [ 'type' => 'structure', 'members' => [ 'agreementViewSummaries' => [ 'shape' => 'AgreementViewSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'Selector' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'BoundedString', ], 'value' => [ 'shape' => 'BoundedString', ], ], ], 'Sort' => [ 'type' => 'structure', 'members' => [ 'sortBy' => [ 'shape' => 'SortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'SortBy' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[A-Za-z_]+$', ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'SupportTerm' => [ 'type' => 'structure', 'members' => [ 'refundPolicy' => [ 'shape' => 'BoundedString', ], 'type' => [ 'shape' => 'UnversionedTermType', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'requestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UnversionedTermType' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '^[A-Za-z]+$', ], 'UsageBasedPricingTerm' => [ 'type' => 'structure', 'members' => [ 'currencyCode' => [ 'shape' => 'CurrencyCode', ], 'rateCards' => [ 'shape' => 'UsageBasedRateCardList', ], 'type' => [ 'shape' => 'UnversionedTermType', ], ], ], 'UsageBasedRateCardItem' => [ 'type' => 'structure', 'members' => [ 'rateCard' => [ 'shape' => 'RateCardList', ], ], ], 'UsageBasedRateCardList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageBasedRateCardItem', ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'fields' => [ 'shape' => 'ValidationExceptionFieldList', ], 'message' => [ 'shape' => 'ExceptionMessage', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], 'requestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'message', 'name', ], 'members' => [ 'message' => [ 'shape' => 'BoundedString', ], 'name' => [ 'shape' => 'BoundedString', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'INVALID_AGREEMENT_ID', 'MISSING_AGREEMENT_ID', 'INVALID_CATALOG', 'INVALID_FILTER_NAME', 'INVALID_FILTER_VALUES', 'INVALID_SORT_BY', 'INVALID_SORT_ORDER', 'INVALID_NEXT_TOKEN', 'INVALID_MAX_RESULTS', 'UNSUPPORTED_FILTERS', 'OTHER', ], ], 'ValidityTerm' => [ 'type' => 'structure', 'members' => [ 'agreementDuration' => [ 'shape' => 'BoundedString', ], 'agreementEndDate' => [ 'shape' => 'Timestamp', ], 'agreementStartDate' => [ 'shape' => 'Timestamp', ], 'type' => [ 'shape' => 'UnversionedTermType', ], ], ], 'ZeroValueInteger' => [ 'type' => 'integer', 'min' => 0, ], ],];
