<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Organization Public API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\PreviewIam\Versionless;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Version;
use Twilio\Rest\PreviewIam\Versionless\Organization\AccountList;
use Twilio\Rest\PreviewIam\Versionless\Organization\UserList;
use Twilio\Rest\PreviewIam\Versionless\Organization\RoleAssignmentList;
use Twilio\Rest\PreviewIam\Versionless\Organization\ResourceTypeList;


class OrganizationInstance extends InstanceResource
{
    protected $_accounts;
    protected $_users;
    protected $_roleAssignments;
    protected $_resourceTypes;

    /**
     * Initialize the OrganizationInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $organizationSid
     */
    public function __construct(Version $version, array $payload, ?string $organizationSid = null)
    {
        parent::__construct($version);

        $this->solution = ['organizationSid' => $organizationSid ?: $this->properties['organizationSid'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return OrganizationContext Context for this OrganizationInstance
     */
    protected function proxy(): OrganizationContext
    {
        if (!$this->context) {
            $this->context = new OrganizationContext(
                $this->version,
                $this->solution['organizationSid']
            );
        }

        return $this->context;
    }

    /**
     * Fetch the OrganizationInstance
     *
     * @return OrganizationInstance Fetched OrganizationInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): OrganizationInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Access the accounts
     */
    protected function getAccounts(): AccountList
    {
        return $this->proxy()->accounts;
    }

    /**
     * Access the users
     */
    protected function getUsers(): UserList
    {
        return $this->proxy()->users;
    }

    /**
     * Access the roleAssignments
     */
    protected function getRoleAssignments(): RoleAssignmentList
    {
        return $this->proxy()->roleAssignments;
    }

    /**
     * Access the resourceTypes
     */
    protected function getResourceTypes(): ResourceTypeList
    {
        return $this->proxy()->resourceTypes;
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.PreviewIam.Versionless.OrganizationInstance ' . \implode(' ', $context) . ']';
    }
}

