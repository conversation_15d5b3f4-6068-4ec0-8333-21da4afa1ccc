name: <PERSON><PERSON>al
on: [push, pull_request]
jobs:
  paypal:
    name: PHP ${{ matrix.php-versions }}
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        php-versions: ['8.0', '8.1', '8.2', '8.3']
        include:
          - php-versions: '8.0'
            continue-on-error: true
    steps:
    - name: Checkout
      uses: actions/checkout@v2
    #   with:
    #     lfs: true
    # - name: Checkout LFS objects
    #   run: git lfs checkout      
    - name: Setup PHP with Composer and extensions
      with:
        php-version: ${{ matrix.php-versions }}
      uses: shivammathur/setup-php@v2
    - name: Get Composer cache directory
      id: composercache
      run: echo "::set-output name=dir::$(composer config cache-files-dir)"
    - name: Cache Composer dependencies
      uses: actions/cache@v2
      with:
        php-version: ${{ matrix.php-versions }}
        path: ${{ steps.composercache.outputs.dir }}
        key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
        restore-keys: ${{ runner.os }}-composer-
    - name: Install Composer dependencies
      env:
        PHP_VERSION: ${{ matrix.php-versions }}
      run: composer install --no-progress --prefer-dist --optimize-autoloader $(if [ "$PHP_VERSION" == "8.0" || "$PHP_VERSION" == "8.1" ]; then echo "--ignore-platform-reqs"; fi;)    
    - name: Run tests for PHP 7.2 to 8.0 with code coverage
      if: matrix.php-versions == '7.2' || matrix.php-versions == '7.3' || matrix.php-versions == '7.4' || matrix.php-versions == '8.0'  
      env:
        PHP_VERSION: ${{ matrix.php-versions }}
      run: vendor/bin/phpunit $(if [ "$PHP_VERSION" == "7.2" ]; then echo "-c phpunit.xml.dist.php72"; fi;) $(if [ "$PHP_VERSION" == "7.3" ]; then echo "-c phpunit.xml.dist.php8"; fi;) $(if [ "$PHP_VERSION" == "7.4" ]; then echo "-c phpunit.xml.dist.php8"; fi;) $(if [ "$PHP_VERSION" == "8.0" ]; then echo "-c phpunit.xml.dist.php8"; fi;) --coverage-clover build/logs/clover.xml
    - name: Install PestPHP
      if: matrix.php-versions == '8.1' || matrix.php-versions == '8.2' || matrix.php-versions == '8.3'
      env:
        PHP_VERSION: ${{ matrix.php-versions }}
      run: composer require pestphp/pest --dev --with-all-dependencies
    - name: Run tests for PHP 8.1+ with code coverage
      if: matrix.php-versions == '8.1' || matrix.php-versions == '8.2' || matrix.php-versions == '8.3'
      env:
        PHP_VERSION: ${{ matrix.php-versions }}
      run: vendor/bin/pest --coverage-clover build/logs/clover.xml
    - name: Install PHP Coveralls library
      env:
        PHP_VERSION: ${{ matrix.php-versions }}
      run:
        composer global require --dev php-coveralls/php-coveralls
    - name: Upload coverage results to Coveralls
      env:
        PHP_VERSION: ${{ matrix.php-versions }}
        COVERALLS_REPO_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        COVERALLS_PARALLEL: true
        COVERALLS_FLAG_NAME: php-${{ matrix.php-versions }}
      run:
        php-coveralls -v
    - name: Upload coverage results to Codecov
      uses: codecov/codecov-action@v3
      env:
        CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
  coveralls-finish:
    needs: [paypal]
    runs-on: ubuntu-latest
    steps:
    - name: Coveralls Finished
      uses: coverallsapp/github-action@master
      with:
        github-token: ${{ secrets.github_token }}
        parallel-finished: true
