<?php
// This file was auto-generated from sdk-root/src/data/waf/2015-08-24/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2015-08-24', 'endpointPrefix' => 'waf', 'jsonVersion' => '1.1', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceAbbreviation' => 'WAF', 'serviceFullName' => 'AWS WAF', 'serviceId' => 'WAF', 'signatureVersion' => 'v4', 'targetPrefix' => 'AWSWAF_20150824', 'uid' => 'waf-2015-08-24', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'CreateByteMatchSet' => [ 'name' => 'CreateByteMatchSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateByteMatchSetRequest', ], 'output' => [ 'shape' => 'CreateByteMatchSetResponse', ], 'errors' => [ [ 'shape' => 'WAFDisallowedNameException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFLimitsExceededException', ], ], ], 'CreateGeoMatchSet' => [ 'name' => 'CreateGeoMatchSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateGeoMatchSetRequest', ], 'output' => [ 'shape' => 'CreateGeoMatchSetResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFDisallowedNameException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFLimitsExceededException', ], ], ], 'CreateIPSet' => [ 'name' => 'CreateIPSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateIPSetRequest', ], 'output' => [ 'shape' => 'CreateIPSetResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFDisallowedNameException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFLimitsExceededException', ], ], ], 'CreateRateBasedRule' => [ 'name' => 'CreateRateBasedRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRateBasedRuleRequest', ], 'output' => [ 'shape' => 'CreateRateBasedRuleResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFDisallowedNameException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFLimitsExceededException', ], [ 'shape' => 'WAFTagOperationException', ], [ 'shape' => 'WAFTagOperationInternalErrorException', ], [ 'shape' => 'WAFBadRequestException', ], ], ], 'CreateRegexMatchSet' => [ 'name' => 'CreateRegexMatchSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRegexMatchSetRequest', ], 'output' => [ 'shape' => 'CreateRegexMatchSetResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFDisallowedNameException', ], [ 'shape' => 'WAFLimitsExceededException', ], ], ], 'CreateRegexPatternSet' => [ 'name' => 'CreateRegexPatternSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRegexPatternSetRequest', ], 'output' => [ 'shape' => 'CreateRegexPatternSetResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFDisallowedNameException', ], [ 'shape' => 'WAFLimitsExceededException', ], ], ], 'CreateRule' => [ 'name' => 'CreateRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRuleRequest', ], 'output' => [ 'shape' => 'CreateRuleResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFDisallowedNameException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFLimitsExceededException', ], [ 'shape' => 'WAFTagOperationException', ], [ 'shape' => 'WAFTagOperationInternalErrorException', ], [ 'shape' => 'WAFBadRequestException', ], ], ], 'CreateRuleGroup' => [ 'name' => 'CreateRuleGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRuleGroupRequest', ], 'output' => [ 'shape' => 'CreateRuleGroupResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFDisallowedNameException', ], [ 'shape' => 'WAFLimitsExceededException', ], [ 'shape' => 'WAFTagOperationException', ], [ 'shape' => 'WAFTagOperationInternalErrorException', ], [ 'shape' => 'WAFBadRequestException', ], ], ], 'CreateSizeConstraintSet' => [ 'name' => 'CreateSizeConstraintSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSizeConstraintSetRequest', ], 'output' => [ 'shape' => 'CreateSizeConstraintSetResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFDisallowedNameException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFLimitsExceededException', ], ], ], 'CreateSqlInjectionMatchSet' => [ 'name' => 'CreateSqlInjectionMatchSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSqlInjectionMatchSetRequest', ], 'output' => [ 'shape' => 'CreateSqlInjectionMatchSetResponse', ], 'errors' => [ [ 'shape' => 'WAFDisallowedNameException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFLimitsExceededException', ], ], ], 'CreateWebACL' => [ 'name' => 'CreateWebACL', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateWebACLRequest', ], 'output' => [ 'shape' => 'CreateWebACLResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFDisallowedNameException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFLimitsExceededException', ], [ 'shape' => 'WAFTagOperationException', ], [ 'shape' => 'WAFTagOperationInternalErrorException', ], [ 'shape' => 'WAFBadRequestException', ], ], ], 'CreateWebACLMigrationStack' => [ 'name' => 'CreateWebACLMigrationStack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateWebACLMigrationStackRequest', ], 'output' => [ 'shape' => 'CreateWebACLMigrationStackResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFEntityMigrationException', ], ], ], 'CreateXssMatchSet' => [ 'name' => 'CreateXssMatchSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateXssMatchSetRequest', ], 'output' => [ 'shape' => 'CreateXssMatchSetResponse', ], 'errors' => [ [ 'shape' => 'WAFDisallowedNameException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFLimitsExceededException', ], ], ], 'DeleteByteMatchSet' => [ 'name' => 'DeleteByteMatchSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteByteMatchSetRequest', ], 'output' => [ 'shape' => 'DeleteByteMatchSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFReferencedItemException', ], [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFNonEmptyEntityException', ], ], ], 'DeleteGeoMatchSet' => [ 'name' => 'DeleteGeoMatchSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteGeoMatchSetRequest', ], 'output' => [ 'shape' => 'DeleteGeoMatchSetResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFReferencedItemException', ], [ 'shape' => 'WAFNonEmptyEntityException', ], ], ], 'DeleteIPSet' => [ 'name' => 'DeleteIPSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteIPSetRequest', ], 'output' => [ 'shape' => 'DeleteIPSetResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFReferencedItemException', ], [ 'shape' => 'WAFNonEmptyEntityException', ], ], ], 'DeleteLoggingConfiguration' => [ 'name' => 'DeleteLoggingConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteLoggingConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteLoggingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFStaleDataException', ], ], ], 'DeletePermissionPolicy' => [ 'name' => 'DeletePermissionPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePermissionPolicyRequest', ], 'output' => [ 'shape' => 'DeletePermissionPolicyResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFNonexistentItemException', ], ], ], 'DeleteRateBasedRule' => [ 'name' => 'DeleteRateBasedRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRateBasedRuleRequest', ], 'output' => [ 'shape' => 'DeleteRateBasedRuleResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFReferencedItemException', ], [ 'shape' => 'WAFNonEmptyEntityException', ], [ 'shape' => 'WAFTagOperationException', ], [ 'shape' => 'WAFTagOperationInternalErrorException', ], ], ], 'DeleteRegexMatchSet' => [ 'name' => 'DeleteRegexMatchSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRegexMatchSetRequest', ], 'output' => [ 'shape' => 'DeleteRegexMatchSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFReferencedItemException', ], [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFNonEmptyEntityException', ], ], ], 'DeleteRegexPatternSet' => [ 'name' => 'DeleteRegexPatternSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRegexPatternSetRequest', ], 'output' => [ 'shape' => 'DeleteRegexPatternSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFReferencedItemException', ], [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFNonEmptyEntityException', ], ], ], 'DeleteRule' => [ 'name' => 'DeleteRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRuleRequest', ], 'output' => [ 'shape' => 'DeleteRuleResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFReferencedItemException', ], [ 'shape' => 'WAFNonEmptyEntityException', ], [ 'shape' => 'WAFTagOperationException', ], [ 'shape' => 'WAFTagOperationInternalErrorException', ], ], ], 'DeleteRuleGroup' => [ 'name' => 'DeleteRuleGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRuleGroupRequest', ], 'output' => [ 'shape' => 'DeleteRuleGroupResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFReferencedItemException', ], [ 'shape' => 'WAFNonEmptyEntityException', ], [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFTagOperationException', ], [ 'shape' => 'WAFTagOperationInternalErrorException', ], ], ], 'DeleteSizeConstraintSet' => [ 'name' => 'DeleteSizeConstraintSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSizeConstraintSetRequest', ], 'output' => [ 'shape' => 'DeleteSizeConstraintSetResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFReferencedItemException', ], [ 'shape' => 'WAFNonEmptyEntityException', ], ], ], 'DeleteSqlInjectionMatchSet' => [ 'name' => 'DeleteSqlInjectionMatchSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSqlInjectionMatchSetRequest', ], 'output' => [ 'shape' => 'DeleteSqlInjectionMatchSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFReferencedItemException', ], [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFNonEmptyEntityException', ], ], ], 'DeleteWebACL' => [ 'name' => 'DeleteWebACL', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteWebACLRequest', ], 'output' => [ 'shape' => 'DeleteWebACLResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFReferencedItemException', ], [ 'shape' => 'WAFNonEmptyEntityException', ], [ 'shape' => 'WAFTagOperationException', ], [ 'shape' => 'WAFTagOperationInternalErrorException', ], ], ], 'DeleteXssMatchSet' => [ 'name' => 'DeleteXssMatchSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteXssMatchSetRequest', ], 'output' => [ 'shape' => 'DeleteXssMatchSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFReferencedItemException', ], [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFNonEmptyEntityException', ], ], ], 'GetByteMatchSet' => [ 'name' => 'GetByteMatchSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetByteMatchSetRequest', ], 'output' => [ 'shape' => 'GetByteMatchSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFNonexistentItemException', ], ], ], 'GetChangeToken' => [ 'name' => 'GetChangeToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetChangeTokenRequest', ], 'output' => [ 'shape' => 'GetChangeTokenResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], ], ], 'GetChangeTokenStatus' => [ 'name' => 'GetChangeTokenStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetChangeTokenStatusRequest', ], 'output' => [ 'shape' => 'GetChangeTokenStatusResponse', ], 'errors' => [ [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInternalErrorException', ], ], ], 'GetGeoMatchSet' => [ 'name' => 'GetGeoMatchSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetGeoMatchSetRequest', ], 'output' => [ 'shape' => 'GetGeoMatchSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFNonexistentItemException', ], ], ], 'GetIPSet' => [ 'name' => 'GetIPSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetIPSetRequest', ], 'output' => [ 'shape' => 'GetIPSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFNonexistentItemException', ], ], ], 'GetLoggingConfiguration' => [ 'name' => 'GetLoggingConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLoggingConfigurationRequest', ], 'output' => [ 'shape' => 'GetLoggingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFNonexistentItemException', ], ], ], 'GetPermissionPolicy' => [ 'name' => 'GetPermissionPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPermissionPolicyRequest', ], 'output' => [ 'shape' => 'GetPermissionPolicyResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFNonexistentItemException', ], ], ], 'GetRateBasedRule' => [ 'name' => 'GetRateBasedRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRateBasedRuleRequest', ], 'output' => [ 'shape' => 'GetRateBasedRuleResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFNonexistentItemException', ], ], ], 'GetRateBasedRuleManagedKeys' => [ 'name' => 'GetRateBasedRuleManagedKeys', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRateBasedRuleManagedKeysRequest', ], 'output' => [ 'shape' => 'GetRateBasedRuleManagedKeysResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInvalidParameterException', ], ], ], 'GetRegexMatchSet' => [ 'name' => 'GetRegexMatchSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRegexMatchSetRequest', ], 'output' => [ 'shape' => 'GetRegexMatchSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFNonexistentItemException', ], ], ], 'GetRegexPatternSet' => [ 'name' => 'GetRegexPatternSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRegexPatternSetRequest', ], 'output' => [ 'shape' => 'GetRegexPatternSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFNonexistentItemException', ], ], ], 'GetRule' => [ 'name' => 'GetRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRuleRequest', ], 'output' => [ 'shape' => 'GetRuleResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFNonexistentItemException', ], ], ], 'GetRuleGroup' => [ 'name' => 'GetRuleGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRuleGroupRequest', ], 'output' => [ 'shape' => 'GetRuleGroupResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFNonexistentItemException', ], ], ], 'GetSampledRequests' => [ 'name' => 'GetSampledRequests', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSampledRequestsRequest', ], 'output' => [ 'shape' => 'GetSampledRequestsResponse', ], 'errors' => [ [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInternalErrorException', ], ], ], 'GetSizeConstraintSet' => [ 'name' => 'GetSizeConstraintSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSizeConstraintSetRequest', ], 'output' => [ 'shape' => 'GetSizeConstraintSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFNonexistentItemException', ], ], ], 'GetSqlInjectionMatchSet' => [ 'name' => 'GetSqlInjectionMatchSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSqlInjectionMatchSetRequest', ], 'output' => [ 'shape' => 'GetSqlInjectionMatchSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFNonexistentItemException', ], ], ], 'GetWebACL' => [ 'name' => 'GetWebACL', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetWebACLRequest', ], 'output' => [ 'shape' => 'GetWebACLResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFNonexistentItemException', ], ], ], 'GetXssMatchSet' => [ 'name' => 'GetXssMatchSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetXssMatchSetRequest', ], 'output' => [ 'shape' => 'GetXssMatchSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFNonexistentItemException', ], ], ], 'ListActivatedRulesInRuleGroup' => [ 'name' => 'ListActivatedRulesInRuleGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListActivatedRulesInRuleGroupRequest', ], 'output' => [ 'shape' => 'ListActivatedRulesInRuleGroupResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInvalidParameterException', ], ], ], 'ListByteMatchSets' => [ 'name' => 'ListByteMatchSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListByteMatchSetsRequest', ], 'output' => [ 'shape' => 'ListByteMatchSetsResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], ], ], 'ListGeoMatchSets' => [ 'name' => 'ListGeoMatchSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListGeoMatchSetsRequest', ], 'output' => [ 'shape' => 'ListGeoMatchSetsResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], ], ], 'ListIPSets' => [ 'name' => 'ListIPSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListIPSetsRequest', ], 'output' => [ 'shape' => 'ListIPSetsResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], ], ], 'ListLoggingConfigurations' => [ 'name' => 'ListLoggingConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListLoggingConfigurationsRequest', ], 'output' => [ 'shape' => 'ListLoggingConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInvalidParameterException', ], ], ], 'ListRateBasedRules' => [ 'name' => 'ListRateBasedRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRateBasedRulesRequest', ], 'output' => [ 'shape' => 'ListRateBasedRulesResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], ], ], 'ListRegexMatchSets' => [ 'name' => 'ListRegexMatchSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRegexMatchSetsRequest', ], 'output' => [ 'shape' => 'ListRegexMatchSetsResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], ], ], 'ListRegexPatternSets' => [ 'name' => 'ListRegexPatternSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRegexPatternSetsRequest', ], 'output' => [ 'shape' => 'ListRegexPatternSetsResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], ], ], 'ListRuleGroups' => [ 'name' => 'ListRuleGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRuleGroupsRequest', ], 'output' => [ 'shape' => 'ListRuleGroupsResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], ], ], 'ListRules' => [ 'name' => 'ListRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRulesRequest', ], 'output' => [ 'shape' => 'ListRulesResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], ], ], 'ListSizeConstraintSets' => [ 'name' => 'ListSizeConstraintSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSizeConstraintSetsRequest', ], 'output' => [ 'shape' => 'ListSizeConstraintSetsResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], ], ], 'ListSqlInjectionMatchSets' => [ 'name' => 'ListSqlInjectionMatchSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSqlInjectionMatchSetsRequest', ], 'output' => [ 'shape' => 'ListSqlInjectionMatchSetsResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], ], ], 'ListSubscribedRuleGroups' => [ 'name' => 'ListSubscribedRuleGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSubscribedRuleGroupsRequest', ], 'output' => [ 'shape' => 'ListSubscribedRuleGroupsResponse', ], 'errors' => [ [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInternalErrorException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFBadRequestException', ], [ 'shape' => 'WAFTagOperationException', ], [ 'shape' => 'WAFTagOperationInternalErrorException', ], ], ], 'ListWebACLs' => [ 'name' => 'ListWebACLs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListWebACLsRequest', ], 'output' => [ 'shape' => 'ListWebACLsResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], ], ], 'ListXssMatchSets' => [ 'name' => 'ListXssMatchSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListXssMatchSetsRequest', ], 'output' => [ 'shape' => 'ListXssMatchSetsResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], ], ], 'PutLoggingConfiguration' => [ 'name' => 'PutLoggingConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutLoggingConfigurationRequest', ], 'output' => [ 'shape' => 'PutLoggingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFServiceLinkedRoleErrorException', ], ], ], 'PutPermissionPolicy' => [ 'name' => 'PutPermissionPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutPermissionPolicyRequest', ], 'output' => [ 'shape' => 'PutPermissionPolicyResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInvalidPermissionPolicyException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFLimitsExceededException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFBadRequestException', ], [ 'shape' => 'WAFTagOperationException', ], [ 'shape' => 'WAFTagOperationInternalErrorException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFBadRequestException', ], [ 'shape' => 'WAFTagOperationException', ], [ 'shape' => 'WAFTagOperationInternalErrorException', ], ], ], 'UpdateByteMatchSet' => [ 'name' => 'UpdateByteMatchSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateByteMatchSetRequest', ], 'output' => [ 'shape' => 'UpdateByteMatchSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentContainerException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFLimitsExceededException', ], ], ], 'UpdateGeoMatchSet' => [ 'name' => 'UpdateGeoMatchSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateGeoMatchSetRequest', ], 'output' => [ 'shape' => 'UpdateGeoMatchSetResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentContainerException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFReferencedItemException', ], [ 'shape' => 'WAFLimitsExceededException', ], ], ], 'UpdateIPSet' => [ 'name' => 'UpdateIPSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateIPSetRequest', ], 'output' => [ 'shape' => 'UpdateIPSetResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentContainerException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFReferencedItemException', ], [ 'shape' => 'WAFLimitsExceededException', ], ], ], 'UpdateRateBasedRule' => [ 'name' => 'UpdateRateBasedRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRateBasedRuleRequest', ], 'output' => [ 'shape' => 'UpdateRateBasedRuleResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentContainerException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFReferencedItemException', ], [ 'shape' => 'WAFLimitsExceededException', ], ], ], 'UpdateRegexMatchSet' => [ 'name' => 'UpdateRegexMatchSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRegexMatchSetRequest', ], 'output' => [ 'shape' => 'UpdateRegexMatchSetResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFDisallowedNameException', ], [ 'shape' => 'WAFLimitsExceededException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFNonexistentContainerException', ], [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFInvalidAccountException', ], ], ], 'UpdateRegexPatternSet' => [ 'name' => 'UpdateRegexPatternSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRegexPatternSetRequest', ], 'output' => [ 'shape' => 'UpdateRegexPatternSetResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFLimitsExceededException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFNonexistentContainerException', ], [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFInvalidRegexPatternException', ], ], ], 'UpdateRule' => [ 'name' => 'UpdateRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRuleRequest', ], 'output' => [ 'shape' => 'UpdateRuleResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentContainerException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFReferencedItemException', ], [ 'shape' => 'WAFLimitsExceededException', ], ], ], 'UpdateRuleGroup' => [ 'name' => 'UpdateRuleGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRuleGroupRequest', ], 'output' => [ 'shape' => 'UpdateRuleGroupResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFNonexistentContainerException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFLimitsExceededException', ], [ 'shape' => 'WAFInvalidParameterException', ], ], ], 'UpdateSizeConstraintSet' => [ 'name' => 'UpdateSizeConstraintSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateSizeConstraintSetRequest', ], 'output' => [ 'shape' => 'UpdateSizeConstraintSetResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentContainerException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFReferencedItemException', ], [ 'shape' => 'WAFLimitsExceededException', ], ], ], 'UpdateSqlInjectionMatchSet' => [ 'name' => 'UpdateSqlInjectionMatchSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateSqlInjectionMatchSetRequest', ], 'output' => [ 'shape' => 'UpdateSqlInjectionMatchSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentContainerException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFLimitsExceededException', ], ], ], 'UpdateWebACL' => [ 'name' => 'UpdateWebACL', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateWebACLRequest', ], 'output' => [ 'shape' => 'UpdateWebACLResponse', ], 'errors' => [ [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentContainerException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFReferencedItemException', ], [ 'shape' => 'WAFLimitsExceededException', ], [ 'shape' => 'WAFSubscriptionNotFoundException', ], ], ], 'UpdateXssMatchSet' => [ 'name' => 'UpdateXssMatchSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateXssMatchSetRequest', ], 'output' => [ 'shape' => 'UpdateXssMatchSetResponse', ], 'errors' => [ [ 'shape' => 'WAFInternalErrorException', ], [ 'shape' => 'WAFInvalidAccountException', ], [ 'shape' => 'WAFInvalidOperationException', ], [ 'shape' => 'WAFInvalidParameterException', ], [ 'shape' => 'WAFNonexistentContainerException', ], [ 'shape' => 'WAFNonexistentItemException', ], [ 'shape' => 'WAFStaleDataException', ], [ 'shape' => 'WAFLimitsExceededException', ], ], ], ], 'shapes' => [ 'Action' => [ 'type' => 'string', ], 'ActivatedRule' => [ 'type' => 'structure', 'required' => [ 'Priority', 'RuleId', ], 'members' => [ 'Priority' => [ 'shape' => 'RulePriority', ], 'RuleId' => [ 'shape' => 'ResourceId', ], 'Action' => [ 'shape' => 'WafAction', ], 'OverrideAction' => [ 'shape' => 'WafOverrideAction', ], 'Type' => [ 'shape' => 'WafRuleType', ], 'ExcludedRules' => [ 'shape' => 'ExcludedRules', ], ], ], 'ActivatedRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActivatedRule', ], ], 'ByteMatchSet' => [ 'type' => 'structure', 'required' => [ 'ByteMatchSetId', 'ByteMatchTuples', ], 'members' => [ 'ByteMatchSetId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], 'ByteMatchTuples' => [ 'shape' => 'ByteMatchTuples', ], ], ], 'ByteMatchSetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ByteMatchSetSummary', ], ], 'ByteMatchSetSummary' => [ 'type' => 'structure', 'required' => [ 'ByteMatchSetId', 'Name', ], 'members' => [ 'ByteMatchSetId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], ], ], 'ByteMatchSetUpdate' => [ 'type' => 'structure', 'required' => [ 'Action', 'ByteMatchTuple', ], 'members' => [ 'Action' => [ 'shape' => 'ChangeAction', ], 'ByteMatchTuple' => [ 'shape' => 'ByteMatchTuple', ], ], ], 'ByteMatchSetUpdates' => [ 'type' => 'list', 'member' => [ 'shape' => 'ByteMatchSetUpdate', ], 'min' => 1, ], 'ByteMatchTargetString' => [ 'type' => 'blob', ], 'ByteMatchTuple' => [ 'type' => 'structure', 'required' => [ 'FieldToMatch', 'TargetString', 'TextTransformation', 'PositionalConstraint', ], 'members' => [ 'FieldToMatch' => [ 'shape' => 'FieldToMatch', ], 'TargetString' => [ 'shape' => 'ByteMatchTargetString', ], 'TextTransformation' => [ 'shape' => 'TextTransformation', ], 'PositionalConstraint' => [ 'shape' => 'PositionalConstraint', ], ], ], 'ByteMatchTuples' => [ 'type' => 'list', 'member' => [ 'shape' => 'ByteMatchTuple', ], ], 'ChangeAction' => [ 'type' => 'string', 'enum' => [ 'INSERT', 'DELETE', ], ], 'ChangeToken' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '.*\\S.*', ], 'ChangeTokenStatus' => [ 'type' => 'string', 'enum' => [ 'PROVISIONED', 'PENDING', 'INSYNC', ], ], 'ComparisonOperator' => [ 'type' => 'string', 'enum' => [ 'EQ', 'NE', 'LE', 'LT', 'GE', 'GT', ], ], 'Country' => [ 'type' => 'string', ], 'CreateByteMatchSetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'ChangeToken', ], 'members' => [ 'Name' => [ 'shape' => 'ResourceName', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'CreateByteMatchSetResponse' => [ 'type' => 'structure', 'members' => [ 'ByteMatchSet' => [ 'shape' => 'ByteMatchSet', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'CreateGeoMatchSetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'ChangeToken', ], 'members' => [ 'Name' => [ 'shape' => 'ResourceName', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'CreateGeoMatchSetResponse' => [ 'type' => 'structure', 'members' => [ 'GeoMatchSet' => [ 'shape' => 'GeoMatchSet', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'CreateIPSetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'ChangeToken', ], 'members' => [ 'Name' => [ 'shape' => 'ResourceName', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'CreateIPSetResponse' => [ 'type' => 'structure', 'members' => [ 'IPSet' => [ 'shape' => 'IPSet', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'CreateRateBasedRuleRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'MetricName', 'RateKey', 'RateLimit', 'ChangeToken', ], 'members' => [ 'Name' => [ 'shape' => 'ResourceName', ], 'MetricName' => [ 'shape' => 'MetricName', ], 'RateKey' => [ 'shape' => 'RateKey', ], 'RateLimit' => [ 'shape' => 'RateLimit', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateRateBasedRuleResponse' => [ 'type' => 'structure', 'members' => [ 'Rule' => [ 'shape' => 'RateBasedRule', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'CreateRegexMatchSetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'ChangeToken', ], 'members' => [ 'Name' => [ 'shape' => 'ResourceName', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'CreateRegexMatchSetResponse' => [ 'type' => 'structure', 'members' => [ 'RegexMatchSet' => [ 'shape' => 'RegexMatchSet', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'CreateRegexPatternSetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'ChangeToken', ], 'members' => [ 'Name' => [ 'shape' => 'ResourceName', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'CreateRegexPatternSetResponse' => [ 'type' => 'structure', 'members' => [ 'RegexPatternSet' => [ 'shape' => 'RegexPatternSet', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'CreateRuleGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'MetricName', 'ChangeToken', ], 'members' => [ 'Name' => [ 'shape' => 'ResourceName', ], 'MetricName' => [ 'shape' => 'MetricName', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateRuleGroupResponse' => [ 'type' => 'structure', 'members' => [ 'RuleGroup' => [ 'shape' => 'RuleGroup', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'CreateRuleRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'MetricName', 'ChangeToken', ], 'members' => [ 'Name' => [ 'shape' => 'ResourceName', ], 'MetricName' => [ 'shape' => 'MetricName', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateRuleResponse' => [ 'type' => 'structure', 'members' => [ 'Rule' => [ 'shape' => 'Rule', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'CreateSizeConstraintSetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'ChangeToken', ], 'members' => [ 'Name' => [ 'shape' => 'ResourceName', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'CreateSizeConstraintSetResponse' => [ 'type' => 'structure', 'members' => [ 'SizeConstraintSet' => [ 'shape' => 'SizeConstraintSet', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'CreateSqlInjectionMatchSetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'ChangeToken', ], 'members' => [ 'Name' => [ 'shape' => 'ResourceName', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'CreateSqlInjectionMatchSetResponse' => [ 'type' => 'structure', 'members' => [ 'SqlInjectionMatchSet' => [ 'shape' => 'SqlInjectionMatchSet', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'CreateWebACLMigrationStackRequest' => [ 'type' => 'structure', 'required' => [ 'WebACLId', 'S3BucketName', 'IgnoreUnsupportedType', ], 'members' => [ 'WebACLId' => [ 'shape' => 'ResourceId', ], 'S3BucketName' => [ 'shape' => 'S3BucketName', ], 'IgnoreUnsupportedType' => [ 'shape' => 'IgnoreUnsupportedType', ], ], ], 'CreateWebACLMigrationStackResponse' => [ 'type' => 'structure', 'required' => [ 'S3ObjectUrl', ], 'members' => [ 'S3ObjectUrl' => [ 'shape' => 'S3ObjectUrl', ], ], ], 'CreateWebACLRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'MetricName', 'DefaultAction', 'ChangeToken', ], 'members' => [ 'Name' => [ 'shape' => 'ResourceName', ], 'MetricName' => [ 'shape' => 'MetricName', ], 'DefaultAction' => [ 'shape' => 'WafAction', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateWebACLResponse' => [ 'type' => 'structure', 'members' => [ 'WebACL' => [ 'shape' => 'WebACL', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'CreateXssMatchSetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'ChangeToken', ], 'members' => [ 'Name' => [ 'shape' => 'ResourceName', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'CreateXssMatchSetResponse' => [ 'type' => 'structure', 'members' => [ 'XssMatchSet' => [ 'shape' => 'XssMatchSet', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteByteMatchSetRequest' => [ 'type' => 'structure', 'required' => [ 'ByteMatchSetId', 'ChangeToken', ], 'members' => [ 'ByteMatchSetId' => [ 'shape' => 'ResourceId', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteByteMatchSetResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteGeoMatchSetRequest' => [ 'type' => 'structure', 'required' => [ 'GeoMatchSetId', 'ChangeToken', ], 'members' => [ 'GeoMatchSetId' => [ 'shape' => 'ResourceId', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteGeoMatchSetResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteIPSetRequest' => [ 'type' => 'structure', 'required' => [ 'IPSetId', 'ChangeToken', ], 'members' => [ 'IPSetId' => [ 'shape' => 'ResourceId', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteIPSetResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteLoggingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'DeleteLoggingConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePermissionPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'DeletePermissionPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRateBasedRuleRequest' => [ 'type' => 'structure', 'required' => [ 'RuleId', 'ChangeToken', ], 'members' => [ 'RuleId' => [ 'shape' => 'ResourceId', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteRateBasedRuleResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteRegexMatchSetRequest' => [ 'type' => 'structure', 'required' => [ 'RegexMatchSetId', 'ChangeToken', ], 'members' => [ 'RegexMatchSetId' => [ 'shape' => 'ResourceId', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteRegexMatchSetResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteRegexPatternSetRequest' => [ 'type' => 'structure', 'required' => [ 'RegexPatternSetId', 'ChangeToken', ], 'members' => [ 'RegexPatternSetId' => [ 'shape' => 'ResourceId', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteRegexPatternSetResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteRuleGroupRequest' => [ 'type' => 'structure', 'required' => [ 'RuleGroupId', 'ChangeToken', ], 'members' => [ 'RuleGroupId' => [ 'shape' => 'ResourceId', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteRuleGroupResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteRuleRequest' => [ 'type' => 'structure', 'required' => [ 'RuleId', 'ChangeToken', ], 'members' => [ 'RuleId' => [ 'shape' => 'ResourceId', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteRuleResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteSizeConstraintSetRequest' => [ 'type' => 'structure', 'required' => [ 'SizeConstraintSetId', 'ChangeToken', ], 'members' => [ 'SizeConstraintSetId' => [ 'shape' => 'ResourceId', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteSizeConstraintSetResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteSqlInjectionMatchSetRequest' => [ 'type' => 'structure', 'required' => [ 'SqlInjectionMatchSetId', 'ChangeToken', ], 'members' => [ 'SqlInjectionMatchSetId' => [ 'shape' => 'ResourceId', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteSqlInjectionMatchSetResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteWebACLRequest' => [ 'type' => 'structure', 'required' => [ 'WebACLId', 'ChangeToken', ], 'members' => [ 'WebACLId' => [ 'shape' => 'ResourceId', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteWebACLResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteXssMatchSetRequest' => [ 'type' => 'structure', 'required' => [ 'XssMatchSetId', 'ChangeToken', ], 'members' => [ 'XssMatchSetId' => [ 'shape' => 'ResourceId', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'DeleteXssMatchSetResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'ErrorReason' => [ 'type' => 'string', ], 'ExcludedRule' => [ 'type' => 'structure', 'required' => [ 'RuleId', ], 'members' => [ 'RuleId' => [ 'shape' => 'ResourceId', ], ], ], 'ExcludedRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExcludedRule', ], ], 'FieldToMatch' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Type' => [ 'shape' => 'MatchFieldType', ], 'Data' => [ 'shape' => 'MatchFieldData', ], ], ], 'GeoMatchConstraint' => [ 'type' => 'structure', 'required' => [ 'Type', 'Value', ], 'members' => [ 'Type' => [ 'shape' => 'GeoMatchConstraintType', ], 'Value' => [ 'shape' => 'GeoMatchConstraintValue', ], ], ], 'GeoMatchConstraintType' => [ 'type' => 'string', 'enum' => [ 'Country', ], ], 'GeoMatchConstraintValue' => [ 'type' => 'string', 'enum' => [ 'AF', 'AX', 'AL', 'DZ', 'AS', 'AD', 'AO', 'AI', 'AQ', 'AG', 'AR', 'AM', 'AW', 'AU', 'AT', 'AZ', 'BS', 'BH', 'BD', 'BB', 'BY', 'BE', 'BZ', 'BJ', 'BM', 'BT', 'BO', 'BQ', 'BA', 'BW', 'BV', 'BR', 'IO', 'BN', 'BG', 'BF', 'BI', 'KH', 'CM', 'CA', 'CV', 'KY', 'CF', 'TD', 'CL', 'CN', 'CX', 'CC', 'CO', 'KM', 'CG', 'CD', 'CK', 'CR', 'CI', 'HR', 'CU', 'CW', 'CY', 'CZ', 'DK', 'DJ', 'DM', 'DO', 'EC', 'EG', 'SV', 'GQ', 'ER', 'EE', 'ET', 'FK', 'FO', 'FJ', 'FI', 'FR', 'GF', 'PF', 'TF', 'GA', 'GM', 'GE', 'DE', 'GH', 'GI', 'GR', 'GL', 'GD', 'GP', 'GU', 'GT', 'GG', 'GN', 'GW', 'GY', 'HT', 'HM', 'VA', 'HN', 'HK', 'HU', 'IS', 'IN', 'ID', 'IR', 'IQ', 'IE', 'IM', 'IL', 'IT', 'JM', 'JP', 'JE', 'JO', 'KZ', 'KE', 'KI', 'KP', 'KR', 'KW', 'KG', 'LA', 'LV', 'LB', 'LS', 'LR', 'LY', 'LI', 'LT', 'LU', 'MO', 'MK', 'MG', 'MW', 'MY', 'MV', 'ML', 'MT', 'MH', 'MQ', 'MR', 'MU', 'YT', 'MX', 'FM', 'MD', 'MC', 'MN', 'ME', 'MS', 'MA', 'MZ', 'MM', 'NA', 'NR', 'NP', 'NL', 'NC', 'NZ', 'NI', 'NE', 'NG', 'NU', 'NF', 'MP', 'NO', 'OM', 'PK', 'PW', 'PS', 'PA', 'PG', 'PY', 'PE', 'PH', 'PN', 'PL', 'PT', 'PR', 'QA', 'RE', 'RO', 'RU', 'RW', 'BL', 'SH', 'KN', 'LC', 'MF', 'PM', 'VC', 'WS', 'SM', 'ST', 'SA', 'SN', 'RS', 'SC', 'SL', 'SG', 'SX', 'SK', 'SI', 'SB', 'SO', 'ZA', 'GS', 'SS', 'ES', 'LK', 'SD', 'SR', 'SJ', 'SZ', 'SE', 'CH', 'SY', 'TW', 'TJ', 'TZ', 'TH', 'TL', 'TG', 'TK', 'TO', 'TT', 'TN', 'TR', 'TM', 'TC', 'TV', 'UG', 'UA', 'AE', 'GB', 'US', 'UM', 'UY', 'UZ', 'VU', 'VE', 'VN', 'VG', 'VI', 'WF', 'EH', 'YE', 'ZM', 'ZW', ], ], 'GeoMatchConstraints' => [ 'type' => 'list', 'member' => [ 'shape' => 'GeoMatchConstraint', ], ], 'GeoMatchSet' => [ 'type' => 'structure', 'required' => [ 'GeoMatchSetId', 'GeoMatchConstraints', ], 'members' => [ 'GeoMatchSetId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], 'GeoMatchConstraints' => [ 'shape' => 'GeoMatchConstraints', ], ], ], 'GeoMatchSetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'GeoMatchSetSummary', ], ], 'GeoMatchSetSummary' => [ 'type' => 'structure', 'required' => [ 'GeoMatchSetId', 'Name', ], 'members' => [ 'GeoMatchSetId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], ], ], 'GeoMatchSetUpdate' => [ 'type' => 'structure', 'required' => [ 'Action', 'GeoMatchConstraint', ], 'members' => [ 'Action' => [ 'shape' => 'ChangeAction', ], 'GeoMatchConstraint' => [ 'shape' => 'GeoMatchConstraint', ], ], ], 'GeoMatchSetUpdates' => [ 'type' => 'list', 'member' => [ 'shape' => 'GeoMatchSetUpdate', ], 'min' => 1, ], 'GetByteMatchSetRequest' => [ 'type' => 'structure', 'required' => [ 'ByteMatchSetId', ], 'members' => [ 'ByteMatchSetId' => [ 'shape' => 'ResourceId', ], ], ], 'GetByteMatchSetResponse' => [ 'type' => 'structure', 'members' => [ 'ByteMatchSet' => [ 'shape' => 'ByteMatchSet', ], ], ], 'GetChangeTokenRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetChangeTokenResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'GetChangeTokenStatusRequest' => [ 'type' => 'structure', 'required' => [ 'ChangeToken', ], 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'GetChangeTokenStatusResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeTokenStatus' => [ 'shape' => 'ChangeTokenStatus', ], ], ], 'GetGeoMatchSetRequest' => [ 'type' => 'structure', 'required' => [ 'GeoMatchSetId', ], 'members' => [ 'GeoMatchSetId' => [ 'shape' => 'ResourceId', ], ], ], 'GetGeoMatchSetResponse' => [ 'type' => 'structure', 'members' => [ 'GeoMatchSet' => [ 'shape' => 'GeoMatchSet', ], ], ], 'GetIPSetRequest' => [ 'type' => 'structure', 'required' => [ 'IPSetId', ], 'members' => [ 'IPSetId' => [ 'shape' => 'ResourceId', ], ], ], 'GetIPSetResponse' => [ 'type' => 'structure', 'members' => [ 'IPSet' => [ 'shape' => 'IPSet', ], ], ], 'GetLoggingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'GetLoggingConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'LoggingConfiguration' => [ 'shape' => 'LoggingConfiguration', ], ], ], 'GetPermissionPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'GetPermissionPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'PolicyString', ], ], ], 'GetRateBasedRuleManagedKeysRequest' => [ 'type' => 'structure', 'required' => [ 'RuleId', ], 'members' => [ 'RuleId' => [ 'shape' => 'ResourceId', ], 'NextMarker' => [ 'shape' => 'NextMarker', ], ], ], 'GetRateBasedRuleManagedKeysResponse' => [ 'type' => 'structure', 'members' => [ 'ManagedKeys' => [ 'shape' => 'ManagedKeys', ], 'NextMarker' => [ 'shape' => 'NextMarker', ], ], ], 'GetRateBasedRuleRequest' => [ 'type' => 'structure', 'required' => [ 'RuleId', ], 'members' => [ 'RuleId' => [ 'shape' => 'ResourceId', ], ], ], 'GetRateBasedRuleResponse' => [ 'type' => 'structure', 'members' => [ 'Rule' => [ 'shape' => 'RateBasedRule', ], ], ], 'GetRegexMatchSetRequest' => [ 'type' => 'structure', 'required' => [ 'RegexMatchSetId', ], 'members' => [ 'RegexMatchSetId' => [ 'shape' => 'ResourceId', ], ], ], 'GetRegexMatchSetResponse' => [ 'type' => 'structure', 'members' => [ 'RegexMatchSet' => [ 'shape' => 'RegexMatchSet', ], ], ], 'GetRegexPatternSetRequest' => [ 'type' => 'structure', 'required' => [ 'RegexPatternSetId', ], 'members' => [ 'RegexPatternSetId' => [ 'shape' => 'ResourceId', ], ], ], 'GetRegexPatternSetResponse' => [ 'type' => 'structure', 'members' => [ 'RegexPatternSet' => [ 'shape' => 'RegexPatternSet', ], ], ], 'GetRuleGroupRequest' => [ 'type' => 'structure', 'required' => [ 'RuleGroupId', ], 'members' => [ 'RuleGroupId' => [ 'shape' => 'ResourceId', ], ], ], 'GetRuleGroupResponse' => [ 'type' => 'structure', 'members' => [ 'RuleGroup' => [ 'shape' => 'RuleGroup', ], ], ], 'GetRuleRequest' => [ 'type' => 'structure', 'required' => [ 'RuleId', ], 'members' => [ 'RuleId' => [ 'shape' => 'ResourceId', ], ], ], 'GetRuleResponse' => [ 'type' => 'structure', 'members' => [ 'Rule' => [ 'shape' => 'Rule', ], ], ], 'GetSampledRequestsMaxItems' => [ 'type' => 'long', 'max' => 500, 'min' => 1, ], 'GetSampledRequestsRequest' => [ 'type' => 'structure', 'required' => [ 'WebAclId', 'RuleId', 'TimeWindow', 'MaxItems', ], 'members' => [ 'WebAclId' => [ 'shape' => 'ResourceId', ], 'RuleId' => [ 'shape' => 'ResourceId', ], 'TimeWindow' => [ 'shape' => 'TimeWindow', ], 'MaxItems' => [ 'shape' => 'GetSampledRequestsMaxItems', ], ], ], 'GetSampledRequestsResponse' => [ 'type' => 'structure', 'members' => [ 'SampledRequests' => [ 'shape' => 'SampledHTTPRequests', ], 'PopulationSize' => [ 'shape' => 'PopulationSize', ], 'TimeWindow' => [ 'shape' => 'TimeWindow', ], ], ], 'GetSizeConstraintSetRequest' => [ 'type' => 'structure', 'required' => [ 'SizeConstraintSetId', ], 'members' => [ 'SizeConstraintSetId' => [ 'shape' => 'ResourceId', ], ], ], 'GetSizeConstraintSetResponse' => [ 'type' => 'structure', 'members' => [ 'SizeConstraintSet' => [ 'shape' => 'SizeConstraintSet', ], ], ], 'GetSqlInjectionMatchSetRequest' => [ 'type' => 'structure', 'required' => [ 'SqlInjectionMatchSetId', ], 'members' => [ 'SqlInjectionMatchSetId' => [ 'shape' => 'ResourceId', ], ], ], 'GetSqlInjectionMatchSetResponse' => [ 'type' => 'structure', 'members' => [ 'SqlInjectionMatchSet' => [ 'shape' => 'SqlInjectionMatchSet', ], ], ], 'GetWebACLRequest' => [ 'type' => 'structure', 'required' => [ 'WebACLId', ], 'members' => [ 'WebACLId' => [ 'shape' => 'ResourceId', ], ], ], 'GetWebACLResponse' => [ 'type' => 'structure', 'members' => [ 'WebACL' => [ 'shape' => 'WebACL', ], ], ], 'GetXssMatchSetRequest' => [ 'type' => 'structure', 'required' => [ 'XssMatchSetId', ], 'members' => [ 'XssMatchSetId' => [ 'shape' => 'ResourceId', ], ], ], 'GetXssMatchSetResponse' => [ 'type' => 'structure', 'members' => [ 'XssMatchSet' => [ 'shape' => 'XssMatchSet', ], ], ], 'HTTPHeader' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'HeaderName', ], 'Value' => [ 'shape' => 'HeaderValue', ], ], ], 'HTTPHeaders' => [ 'type' => 'list', 'member' => [ 'shape' => 'HTTPHeader', ], ], 'HTTPMethod' => [ 'type' => 'string', ], 'HTTPRequest' => [ 'type' => 'structure', 'members' => [ 'ClientIP' => [ 'shape' => 'IPString', ], 'Country' => [ 'shape' => 'Country', ], 'URI' => [ 'shape' => 'URIString', ], 'Method' => [ 'shape' => 'HTTPMethod', ], 'HTTPVersion' => [ 'shape' => 'HTTPVersion', ], 'Headers' => [ 'shape' => 'HTTPHeaders', ], ], ], 'HTTPVersion' => [ 'type' => 'string', ], 'HeaderName' => [ 'type' => 'string', ], 'HeaderValue' => [ 'type' => 'string', ], 'IPSet' => [ 'type' => 'structure', 'required' => [ 'IPSetId', 'IPSetDescriptors', ], 'members' => [ 'IPSetId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], 'IPSetDescriptors' => [ 'shape' => 'IPSetDescriptors', ], ], ], 'IPSetDescriptor' => [ 'type' => 'structure', 'required' => [ 'Type', 'Value', ], 'members' => [ 'Type' => [ 'shape' => 'IPSetDescriptorType', ], 'Value' => [ 'shape' => 'IPSetDescriptorValue', ], ], ], 'IPSetDescriptorType' => [ 'type' => 'string', 'enum' => [ 'IPV4', 'IPV6', ], ], 'IPSetDescriptorValue' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '.*\\S.*', ], 'IPSetDescriptors' => [ 'type' => 'list', 'member' => [ 'shape' => 'IPSetDescriptor', ], ], 'IPSetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'IPSetSummary', ], ], 'IPSetSummary' => [ 'type' => 'structure', 'required' => [ 'IPSetId', 'Name', ], 'members' => [ 'IPSetId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], ], ], 'IPSetUpdate' => [ 'type' => 'structure', 'required' => [ 'Action', 'IPSetDescriptor', ], 'members' => [ 'Action' => [ 'shape' => 'ChangeAction', ], 'IPSetDescriptor' => [ 'shape' => 'IPSetDescriptor', ], ], ], 'IPSetUpdates' => [ 'type' => 'list', 'member' => [ 'shape' => 'IPSetUpdate', ], 'min' => 1, ], 'IPString' => [ 'type' => 'string', ], 'IgnoreUnsupportedType' => [ 'type' => 'boolean', ], 'ListActivatedRulesInRuleGroupRequest' => [ 'type' => 'structure', 'members' => [ 'RuleGroupId' => [ 'shape' => 'ResourceId', ], 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListActivatedRulesInRuleGroupResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'ActivatedRules' => [ 'shape' => 'ActivatedRules', ], ], ], 'ListByteMatchSetsRequest' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListByteMatchSetsResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'ByteMatchSets' => [ 'shape' => 'ByteMatchSetSummaries', ], ], ], 'ListGeoMatchSetsRequest' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListGeoMatchSetsResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'GeoMatchSets' => [ 'shape' => 'GeoMatchSetSummaries', ], ], ], 'ListIPSetsRequest' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListIPSetsResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'IPSets' => [ 'shape' => 'IPSetSummaries', ], ], ], 'ListLoggingConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListLoggingConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'LoggingConfigurations' => [ 'shape' => 'LoggingConfigurations', ], 'NextMarker' => [ 'shape' => 'NextMarker', ], ], ], 'ListRateBasedRulesRequest' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListRateBasedRulesResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Rules' => [ 'shape' => 'RuleSummaries', ], ], ], 'ListRegexMatchSetsRequest' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListRegexMatchSetsResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'RegexMatchSets' => [ 'shape' => 'RegexMatchSetSummaries', ], ], ], 'ListRegexPatternSetsRequest' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListRegexPatternSetsResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'RegexPatternSets' => [ 'shape' => 'RegexPatternSetSummaries', ], ], ], 'ListRuleGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListRuleGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'RuleGroups' => [ 'shape' => 'RuleGroupSummaries', ], ], ], 'ListRulesRequest' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListRulesResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Rules' => [ 'shape' => 'RuleSummaries', ], ], ], 'ListSizeConstraintSetsRequest' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListSizeConstraintSetsResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'SizeConstraintSets' => [ 'shape' => 'SizeConstraintSetSummaries', ], ], ], 'ListSqlInjectionMatchSetsRequest' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListSqlInjectionMatchSetsResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'SqlInjectionMatchSets' => [ 'shape' => 'SqlInjectionMatchSetSummaries', ], ], ], 'ListSubscribedRuleGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListSubscribedRuleGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'RuleGroups' => [ 'shape' => 'SubscribedRuleGroupSummaries', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', ], 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], 'ResourceARN' => [ 'shape' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'TagInfoForResource' => [ 'shape' => 'TagInfoForResource', ], ], ], 'ListWebACLsRequest' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListWebACLsResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'WebACLs' => [ 'shape' => 'WebACLSummaries', ], ], ], 'ListXssMatchSetsRequest' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'Limit' => [ 'shape' => 'PaginationLimit', ], ], ], 'ListXssMatchSetsResponse' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'NextMarker', ], 'XssMatchSets' => [ 'shape' => 'XssMatchSetSummaries', ], ], ], 'LogDestinationConfigs' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceArn', ], 'max' => 1, 'min' => 1, ], 'LoggingConfiguration' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'LogDestinationConfigs', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'LogDestinationConfigs' => [ 'shape' => 'LogDestinationConfigs', ], 'RedactedFields' => [ 'shape' => 'RedactedFields', ], ], ], 'LoggingConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoggingConfiguration', ], ], 'ManagedKey' => [ 'type' => 'string', ], 'ManagedKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManagedKey', ], ], 'MatchFieldData' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '.*\\S.*', ], 'MatchFieldType' => [ 'type' => 'string', 'enum' => [ 'URI', 'QUERY_STRING', 'HEADER', 'METHOD', 'BODY', 'SINGLE_QUERY_ARG', 'ALL_QUERY_ARGS', ], ], 'MetricName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '.*\\S.*', ], 'MigrationErrorType' => [ 'type' => 'string', 'enum' => [ 'ENTITY_NOT_SUPPORTED', 'ENTITY_NOT_FOUND', 'S3_BUCKET_NO_PERMISSION', 'S3_BUCKET_NOT_ACCESSIBLE', 'S3_BUCKET_NOT_FOUND', 'S3_BUCKET_INVALID_REGION', 'S3_INTERNAL_ERROR', ], ], 'Negated' => [ 'type' => 'boolean', ], 'NextMarker' => [ 'type' => 'string', 'max' => 1224, 'min' => 1, 'pattern' => '.*\\S.*', ], 'PaginationLimit' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'ParameterExceptionField' => [ 'type' => 'string', 'enum' => [ 'CHANGE_ACTION', 'WAF_ACTION', 'WAF_OVERRIDE_ACTION', 'PREDICATE_TYPE', 'IPSET_TYPE', 'BYTE_MATCH_FIELD_TYPE', 'SQL_INJECTION_MATCH_FIELD_TYPE', 'BYTE_MATCH_TEXT_TRANSFORMATION', 'BYTE_MATCH_POSITIONAL_CONSTRAINT', 'SIZE_CONSTRAINT_COMPARISON_OPERATOR', 'GEO_MATCH_LOCATION_TYPE', 'GEO_MATCH_LOCATION_VALUE', 'RATE_KEY', 'RULE_TYPE', 'NEXT_MARKER', 'RESOURCE_ARN', 'TAGS', 'TAG_KEYS', ], ], 'ParameterExceptionParameter' => [ 'type' => 'string', 'min' => 1, ], 'ParameterExceptionReason' => [ 'type' => 'string', 'enum' => [ 'INVALID_OPTION', 'ILLEGAL_COMBINATION', 'ILLEGAL_ARGUMENT', 'INVALID_TAG_KEY', ], ], 'PolicyString' => [ 'type' => 'string', 'max' => 395000, 'min' => 1, 'pattern' => '.*\\S.*', ], 'PopulationSize' => [ 'type' => 'long', ], 'PositionalConstraint' => [ 'type' => 'string', 'enum' => [ 'EXACTLY', 'STARTS_WITH', 'ENDS_WITH', 'CONTAINS', 'CONTAINS_WORD', ], ], 'Predicate' => [ 'type' => 'structure', 'required' => [ 'Negated', 'Type', 'DataId', ], 'members' => [ 'Negated' => [ 'shape' => 'Negated', ], 'Type' => [ 'shape' => 'PredicateType', ], 'DataId' => [ 'shape' => 'ResourceId', ], ], ], 'PredicateType' => [ 'type' => 'string', 'enum' => [ 'IPMatch', 'ByteMatch', 'SqlInjectionMatch', 'GeoMatch', 'SizeConstraint', 'XssMatch', 'RegexMatch', ], ], 'Predicates' => [ 'type' => 'list', 'member' => [ 'shape' => 'Predicate', ], ], 'PutLoggingConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'LoggingConfiguration', ], 'members' => [ 'LoggingConfiguration' => [ 'shape' => 'LoggingConfiguration', ], ], ], 'PutLoggingConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'LoggingConfiguration' => [ 'shape' => 'LoggingConfiguration', ], ], ], 'PutPermissionPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Policy', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'Policy' => [ 'shape' => 'PolicyString', ], ], ], 'PutPermissionPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'RateBasedRule' => [ 'type' => 'structure', 'required' => [ 'RuleId', 'MatchPredicates', 'RateKey', 'RateLimit', ], 'members' => [ 'RuleId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], 'MetricName' => [ 'shape' => 'MetricName', ], 'MatchPredicates' => [ 'shape' => 'Predicates', ], 'RateKey' => [ 'shape' => 'RateKey', ], 'RateLimit' => [ 'shape' => 'RateLimit', ], ], ], 'RateKey' => [ 'type' => 'string', 'enum' => [ 'IP', ], ], 'RateLimit' => [ 'type' => 'long', 'max' => 2000000000, 'min' => 100, ], 'RedactedFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldToMatch', ], ], 'RegexMatchSet' => [ 'type' => 'structure', 'members' => [ 'RegexMatchSetId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], 'RegexMatchTuples' => [ 'shape' => 'RegexMatchTuples', ], ], ], 'RegexMatchSetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegexMatchSetSummary', ], ], 'RegexMatchSetSummary' => [ 'type' => 'structure', 'required' => [ 'RegexMatchSetId', 'Name', ], 'members' => [ 'RegexMatchSetId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], ], ], 'RegexMatchSetUpdate' => [ 'type' => 'structure', 'required' => [ 'Action', 'RegexMatchTuple', ], 'members' => [ 'Action' => [ 'shape' => 'ChangeAction', ], 'RegexMatchTuple' => [ 'shape' => 'RegexMatchTuple', ], ], ], 'RegexMatchSetUpdates' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegexMatchSetUpdate', ], 'min' => 1, ], 'RegexMatchTuple' => [ 'type' => 'structure', 'required' => [ 'FieldToMatch', 'TextTransformation', 'RegexPatternSetId', ], 'members' => [ 'FieldToMatch' => [ 'shape' => 'FieldToMatch', ], 'TextTransformation' => [ 'shape' => 'TextTransformation', ], 'RegexPatternSetId' => [ 'shape' => 'ResourceId', ], ], ], 'RegexMatchTuples' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegexMatchTuple', ], ], 'RegexPatternSet' => [ 'type' => 'structure', 'required' => [ 'RegexPatternSetId', 'RegexPatternStrings', ], 'members' => [ 'RegexPatternSetId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], 'RegexPatternStrings' => [ 'shape' => 'RegexPatternStrings', ], ], ], 'RegexPatternSetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegexPatternSetSummary', ], ], 'RegexPatternSetSummary' => [ 'type' => 'structure', 'required' => [ 'RegexPatternSetId', 'Name', ], 'members' => [ 'RegexPatternSetId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], ], ], 'RegexPatternSetUpdate' => [ 'type' => 'structure', 'required' => [ 'Action', 'RegexPatternString', ], 'members' => [ 'Action' => [ 'shape' => 'ChangeAction', ], 'RegexPatternString' => [ 'shape' => 'RegexPatternString', ], ], ], 'RegexPatternSetUpdates' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegexPatternSetUpdate', ], 'min' => 1, ], 'RegexPatternString' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '.*', ], 'RegexPatternStrings' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegexPatternString', ], 'max' => 10, ], 'ResourceArn' => [ 'type' => 'string', 'max' => 1224, 'min' => 1, 'pattern' => '.*\\S.*', ], 'ResourceId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '.*\\S.*', ], 'ResourceName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '.*\\S.*', ], 'Rule' => [ 'type' => 'structure', 'required' => [ 'RuleId', 'Predicates', ], 'members' => [ 'RuleId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], 'MetricName' => [ 'shape' => 'MetricName', ], 'Predicates' => [ 'shape' => 'Predicates', ], ], ], 'RuleGroup' => [ 'type' => 'structure', 'required' => [ 'RuleGroupId', ], 'members' => [ 'RuleGroupId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], 'MetricName' => [ 'shape' => 'MetricName', ], ], ], 'RuleGroupSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleGroupSummary', ], ], 'RuleGroupSummary' => [ 'type' => 'structure', 'required' => [ 'RuleGroupId', 'Name', ], 'members' => [ 'RuleGroupId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], ], ], 'RuleGroupUpdate' => [ 'type' => 'structure', 'required' => [ 'Action', 'ActivatedRule', ], 'members' => [ 'Action' => [ 'shape' => 'ChangeAction', ], 'ActivatedRule' => [ 'shape' => 'ActivatedRule', ], ], ], 'RuleGroupUpdates' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleGroupUpdate', ], 'min' => 1, ], 'RulePriority' => [ 'type' => 'integer', ], 'RuleSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleSummary', ], ], 'RuleSummary' => [ 'type' => 'structure', 'required' => [ 'RuleId', 'Name', ], 'members' => [ 'RuleId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], ], ], 'RuleUpdate' => [ 'type' => 'structure', 'required' => [ 'Action', 'Predicate', ], 'members' => [ 'Action' => [ 'shape' => 'ChangeAction', ], 'Predicate' => [ 'shape' => 'Predicate', ], ], ], 'RuleUpdates' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleUpdate', ], ], 'S3BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^aws-waf-migration-[0-9A-Za-z\\.\\-_]*', ], 'S3ObjectUrl' => [ 'type' => 'string', 'min' => 1, ], 'SampleWeight' => [ 'type' => 'long', 'min' => 0, ], 'SampledHTTPRequest' => [ 'type' => 'structure', 'required' => [ 'Request', 'Weight', ], 'members' => [ 'Request' => [ 'shape' => 'HTTPRequest', ], 'Weight' => [ 'shape' => 'SampleWeight', ], 'Timestamp' => [ 'shape' => 'Timestamp', ], 'Action' => [ 'shape' => 'Action', ], 'RuleWithinRuleGroup' => [ 'shape' => 'ResourceId', ], ], ], 'SampledHTTPRequests' => [ 'type' => 'list', 'member' => [ 'shape' => 'SampledHTTPRequest', ], ], 'Size' => [ 'type' => 'long', 'max' => 21474836480, 'min' => 0, ], 'SizeConstraint' => [ 'type' => 'structure', 'required' => [ 'FieldToMatch', 'TextTransformation', 'ComparisonOperator', 'Size', ], 'members' => [ 'FieldToMatch' => [ 'shape' => 'FieldToMatch', ], 'TextTransformation' => [ 'shape' => 'TextTransformation', ], 'ComparisonOperator' => [ 'shape' => 'ComparisonOperator', ], 'Size' => [ 'shape' => 'Size', ], ], ], 'SizeConstraintSet' => [ 'type' => 'structure', 'required' => [ 'SizeConstraintSetId', 'SizeConstraints', ], 'members' => [ 'SizeConstraintSetId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], 'SizeConstraints' => [ 'shape' => 'SizeConstraints', ], ], ], 'SizeConstraintSetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SizeConstraintSetSummary', ], ], 'SizeConstraintSetSummary' => [ 'type' => 'structure', 'required' => [ 'SizeConstraintSetId', 'Name', ], 'members' => [ 'SizeConstraintSetId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], ], ], 'SizeConstraintSetUpdate' => [ 'type' => 'structure', 'required' => [ 'Action', 'SizeConstraint', ], 'members' => [ 'Action' => [ 'shape' => 'ChangeAction', ], 'SizeConstraint' => [ 'shape' => 'SizeConstraint', ], ], ], 'SizeConstraintSetUpdates' => [ 'type' => 'list', 'member' => [ 'shape' => 'SizeConstraintSetUpdate', ], 'min' => 1, ], 'SizeConstraints' => [ 'type' => 'list', 'member' => [ 'shape' => 'SizeConstraint', ], ], 'SqlInjectionMatchSet' => [ 'type' => 'structure', 'required' => [ 'SqlInjectionMatchSetId', 'SqlInjectionMatchTuples', ], 'members' => [ 'SqlInjectionMatchSetId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], 'SqlInjectionMatchTuples' => [ 'shape' => 'SqlInjectionMatchTuples', ], ], ], 'SqlInjectionMatchSetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SqlInjectionMatchSetSummary', ], ], 'SqlInjectionMatchSetSummary' => [ 'type' => 'structure', 'required' => [ 'SqlInjectionMatchSetId', 'Name', ], 'members' => [ 'SqlInjectionMatchSetId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], ], ], 'SqlInjectionMatchSetUpdate' => [ 'type' => 'structure', 'required' => [ 'Action', 'SqlInjectionMatchTuple', ], 'members' => [ 'Action' => [ 'shape' => 'ChangeAction', ], 'SqlInjectionMatchTuple' => [ 'shape' => 'SqlInjectionMatchTuple', ], ], ], 'SqlInjectionMatchSetUpdates' => [ 'type' => 'list', 'member' => [ 'shape' => 'SqlInjectionMatchSetUpdate', ], 'min' => 1, ], 'SqlInjectionMatchTuple' => [ 'type' => 'structure', 'required' => [ 'FieldToMatch', 'TextTransformation', ], 'members' => [ 'FieldToMatch' => [ 'shape' => 'FieldToMatch', ], 'TextTransformation' => [ 'shape' => 'TextTransformation', ], ], ], 'SqlInjectionMatchTuples' => [ 'type' => 'list', 'member' => [ 'shape' => 'SqlInjectionMatchTuple', ], ], 'SubscribedRuleGroupSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubscribedRuleGroupSummary', ], ], 'SubscribedRuleGroupSummary' => [ 'type' => 'structure', 'required' => [ 'RuleGroupId', 'Name', 'MetricName', ], 'members' => [ 'RuleGroupId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], 'MetricName' => [ 'shape' => 'MetricName', ], ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagInfoForResource' => [ 'type' => 'structure', 'members' => [ 'ResourceARN' => [ 'shape' => 'ResourceArn', ], 'TagList' => [ 'shape' => 'TagList', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '.*\\S.*', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'min' => 1, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'Tags', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '.*', ], 'TextTransformation' => [ 'type' => 'string', 'enum' => [ 'NONE', 'COMPRESS_WHITE_SPACE', 'HTML_ENTITY_DECODE', 'LOWERCASE', 'CMD_LINE', 'URL_DECODE', ], ], 'TimeWindow' => [ 'type' => 'structure', 'required' => [ 'StartTime', 'EndTime', ], 'members' => [ 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'URIString' => [ 'type' => 'string', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'TagKeys', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateByteMatchSetRequest' => [ 'type' => 'structure', 'required' => [ 'ByteMatchSetId', 'ChangeToken', 'Updates', ], 'members' => [ 'ByteMatchSetId' => [ 'shape' => 'ResourceId', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], 'Updates' => [ 'shape' => 'ByteMatchSetUpdates', ], ], ], 'UpdateByteMatchSetResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'UpdateGeoMatchSetRequest' => [ 'type' => 'structure', 'required' => [ 'GeoMatchSetId', 'ChangeToken', 'Updates', ], 'members' => [ 'GeoMatchSetId' => [ 'shape' => 'ResourceId', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], 'Updates' => [ 'shape' => 'GeoMatchSetUpdates', ], ], ], 'UpdateGeoMatchSetResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'UpdateIPSetRequest' => [ 'type' => 'structure', 'required' => [ 'IPSetId', 'ChangeToken', 'Updates', ], 'members' => [ 'IPSetId' => [ 'shape' => 'ResourceId', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], 'Updates' => [ 'shape' => 'IPSetUpdates', ], ], ], 'UpdateIPSetResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'UpdateRateBasedRuleRequest' => [ 'type' => 'structure', 'required' => [ 'RuleId', 'ChangeToken', 'Updates', 'RateLimit', ], 'members' => [ 'RuleId' => [ 'shape' => 'ResourceId', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], 'Updates' => [ 'shape' => 'RuleUpdates', ], 'RateLimit' => [ 'shape' => 'RateLimit', ], ], ], 'UpdateRateBasedRuleResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'UpdateRegexMatchSetRequest' => [ 'type' => 'structure', 'required' => [ 'RegexMatchSetId', 'Updates', 'ChangeToken', ], 'members' => [ 'RegexMatchSetId' => [ 'shape' => 'ResourceId', ], 'Updates' => [ 'shape' => 'RegexMatchSetUpdates', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'UpdateRegexMatchSetResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'UpdateRegexPatternSetRequest' => [ 'type' => 'structure', 'required' => [ 'RegexPatternSetId', 'Updates', 'ChangeToken', ], 'members' => [ 'RegexPatternSetId' => [ 'shape' => 'ResourceId', ], 'Updates' => [ 'shape' => 'RegexPatternSetUpdates', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'UpdateRegexPatternSetResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'UpdateRuleGroupRequest' => [ 'type' => 'structure', 'required' => [ 'RuleGroupId', 'Updates', 'ChangeToken', ], 'members' => [ 'RuleGroupId' => [ 'shape' => 'ResourceId', ], 'Updates' => [ 'shape' => 'RuleGroupUpdates', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'UpdateRuleGroupResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'UpdateRuleRequest' => [ 'type' => 'structure', 'required' => [ 'RuleId', 'ChangeToken', 'Updates', ], 'members' => [ 'RuleId' => [ 'shape' => 'ResourceId', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], 'Updates' => [ 'shape' => 'RuleUpdates', ], ], ], 'UpdateRuleResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'UpdateSizeConstraintSetRequest' => [ 'type' => 'structure', 'required' => [ 'SizeConstraintSetId', 'ChangeToken', 'Updates', ], 'members' => [ 'SizeConstraintSetId' => [ 'shape' => 'ResourceId', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], 'Updates' => [ 'shape' => 'SizeConstraintSetUpdates', ], ], ], 'UpdateSizeConstraintSetResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'UpdateSqlInjectionMatchSetRequest' => [ 'type' => 'structure', 'required' => [ 'SqlInjectionMatchSetId', 'ChangeToken', 'Updates', ], 'members' => [ 'SqlInjectionMatchSetId' => [ 'shape' => 'ResourceId', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], 'Updates' => [ 'shape' => 'SqlInjectionMatchSetUpdates', ], ], ], 'UpdateSqlInjectionMatchSetResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'UpdateWebACLRequest' => [ 'type' => 'structure', 'required' => [ 'WebACLId', 'ChangeToken', ], 'members' => [ 'WebACLId' => [ 'shape' => 'ResourceId', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], 'Updates' => [ 'shape' => 'WebACLUpdates', ], 'DefaultAction' => [ 'shape' => 'WafAction', ], ], ], 'UpdateWebACLResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'UpdateXssMatchSetRequest' => [ 'type' => 'structure', 'required' => [ 'XssMatchSetId', 'ChangeToken', 'Updates', ], 'members' => [ 'XssMatchSetId' => [ 'shape' => 'ResourceId', ], 'ChangeToken' => [ 'shape' => 'ChangeToken', ], 'Updates' => [ 'shape' => 'XssMatchSetUpdates', ], ], ], 'UpdateXssMatchSetResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeToken' => [ 'shape' => 'ChangeToken', ], ], ], 'WAFBadRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'WAFDisallowedNameException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'WAFEntityMigrationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], 'MigrationErrorType' => [ 'shape' => 'MigrationErrorType', ], 'MigrationErrorReason' => [ 'shape' => 'ErrorReason', ], ], 'exception' => true, ], 'WAFInternalErrorException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, 'fault' => true, ], 'WAFInvalidAccountException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'WAFInvalidOperationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'WAFInvalidParameterException' => [ 'type' => 'structure', 'members' => [ 'field' => [ 'shape' => 'ParameterExceptionField', ], 'parameter' => [ 'shape' => 'ParameterExceptionParameter', ], 'reason' => [ 'shape' => 'ParameterExceptionReason', ], ], 'exception' => true, ], 'WAFInvalidPermissionPolicyException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'WAFInvalidRegexPatternException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'WAFLimitsExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'WAFNonEmptyEntityException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'WAFNonexistentContainerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'WAFNonexistentItemException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'WAFReferencedItemException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'WAFServiceLinkedRoleErrorException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'WAFStaleDataException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'WAFSubscriptionNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'WAFTagOperationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'WAFTagOperationInternalErrorException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, 'fault' => true, ], 'WafAction' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Type' => [ 'shape' => 'WafActionType', ], ], ], 'WafActionType' => [ 'type' => 'string', 'enum' => [ 'BLOCK', 'ALLOW', 'COUNT', ], ], 'WafOverrideAction' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Type' => [ 'shape' => 'WafOverrideActionType', ], ], ], 'WafOverrideActionType' => [ 'type' => 'string', 'enum' => [ 'NONE', 'COUNT', ], ], 'WafRuleType' => [ 'type' => 'string', 'enum' => [ 'REGULAR', 'RATE_BASED', 'GROUP', ], ], 'WebACL' => [ 'type' => 'structure', 'required' => [ 'WebACLId', 'DefaultAction', 'Rules', ], 'members' => [ 'WebACLId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], 'MetricName' => [ 'shape' => 'MetricName', ], 'DefaultAction' => [ 'shape' => 'WafAction', ], 'Rules' => [ 'shape' => 'ActivatedRules', ], 'WebACLArn' => [ 'shape' => 'ResourceArn', ], ], ], 'WebACLSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'WebACLSummary', ], ], 'WebACLSummary' => [ 'type' => 'structure', 'required' => [ 'WebACLId', 'Name', ], 'members' => [ 'WebACLId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], ], ], 'WebACLUpdate' => [ 'type' => 'structure', 'required' => [ 'Action', 'ActivatedRule', ], 'members' => [ 'Action' => [ 'shape' => 'ChangeAction', ], 'ActivatedRule' => [ 'shape' => 'ActivatedRule', ], ], ], 'WebACLUpdates' => [ 'type' => 'list', 'member' => [ 'shape' => 'WebACLUpdate', ], ], 'XssMatchSet' => [ 'type' => 'structure', 'required' => [ 'XssMatchSetId', 'XssMatchTuples', ], 'members' => [ 'XssMatchSetId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], 'XssMatchTuples' => [ 'shape' => 'XssMatchTuples', ], ], ], 'XssMatchSetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'XssMatchSetSummary', ], ], 'XssMatchSetSummary' => [ 'type' => 'structure', 'required' => [ 'XssMatchSetId', 'Name', ], 'members' => [ 'XssMatchSetId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'ResourceName', ], ], ], 'XssMatchSetUpdate' => [ 'type' => 'structure', 'required' => [ 'Action', 'XssMatchTuple', ], 'members' => [ 'Action' => [ 'shape' => 'ChangeAction', ], 'XssMatchTuple' => [ 'shape' => 'XssMatchTuple', ], ], ], 'XssMatchSetUpdates' => [ 'type' => 'list', 'member' => [ 'shape' => 'XssMatchSetUpdate', ], 'min' => 1, ], 'XssMatchTuple' => [ 'type' => 'structure', 'required' => [ 'FieldToMatch', 'TextTransformation', ], 'members' => [ 'FieldToMatch' => [ 'shape' => 'FieldToMatch', ], 'TextTransformation' => [ 'shape' => 'TextTransformation', ], ], ], 'XssMatchTuples' => [ 'type' => 'list', 'member' => [ 'shape' => 'XssMatchTuple', ], ], 'errorMessage' => [ 'type' => 'string', ], ],];
