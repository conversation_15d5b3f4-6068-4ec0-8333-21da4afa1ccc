net\authorize\api\contract\v1\OtherTaxType:
    properties:
        nationalTaxAmount:
            expose: true
            access_type: public_method
            serialized_name: nationalTaxAmount
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getNationalTaxAmount
                setter: setNationalTaxAmount
            type: float
        localTaxAmount:
            expose: true
            access_type: public_method
            serialized_name: localTaxAmount
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getLocalTaxAmount
                setter: setLocalTaxAmount
            type: float
        alternateTaxAmount:
            expose: true
            access_type: public_method
            serialized_name: alternateTaxAmount
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getAlternateTaxAmount
                setter: setAlternateTaxAmount
            type: float
        alternateTaxId:
            expose: true
            access_type: public_method
            serialized_name: alternateTaxId
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getAlternateTaxId
                setter: setAlternateTaxId
            type: string
        vatTaxRate:
            expose: true
            access_type: public_method
            serialized_name: vatTaxRate
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getVatTaxRate
                setter: setVatTaxRate
            type: float
        vatTaxAmount:
            expose: true
            access_type: public_method
            serialized_name: vatTaxAmount
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getVatTaxAmount
                setter: setVatTaxAmount
            type: float
