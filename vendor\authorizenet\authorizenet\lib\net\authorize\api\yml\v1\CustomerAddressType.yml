net\authorize\api\contract\v1\CustomerAddressType:
    properties:
        phoneNumber:
            expose: true
            access_type: public_method
            serialized_name: phoneNumber
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getPhoneNumber
                setter: setPhoneNumber
            type: string
        faxNumber:
            expose: true
            access_type: public_method
            serialized_name: faxNumber
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getFaxNumber
                setter: setFaxNumber
            type: string
        email:
            expose: true
            access_type: public_method
            serialized_name: email
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getEmail
                setter: setEmail
            type: string
