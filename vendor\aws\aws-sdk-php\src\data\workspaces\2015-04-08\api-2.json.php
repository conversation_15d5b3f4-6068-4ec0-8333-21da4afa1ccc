<?php
// This file was auto-generated from sdk-root/src/data/workspaces/2015-04-08/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2015-04-08', 'endpointPrefix' => 'workspaces', 'jsonVersion' => '1.1', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'Amazon WorkSpaces', 'serviceId' => 'WorkSpaces', 'signatureVersion' => 'v4', 'targetPrefix' => 'WorkspacesService', 'uid' => 'workspaces-2015-04-08', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AcceptAccountLinkInvitation' => [ 'name' => 'AcceptAccountLinkInvitation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AcceptAccountLinkInvitationRequest', ], 'output' => [ 'shape' => 'AcceptAccountLinkInvitationResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'AssociateConnectionAlias' => [ 'name' => 'AssociateConnectionAlias', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateConnectionAliasRequest', ], 'output' => [ 'shape' => 'AssociateConnectionAliasResult', ], 'errors' => [ [ 'shape' => 'ResourceAssociatedException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'AssociateIpGroups' => [ 'name' => 'AssociateIpGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateIpGroupsRequest', ], 'output' => [ 'shape' => 'AssociateIpGroupsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'AssociateWorkspaceApplication' => [ 'name' => 'AssociateWorkspaceApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateWorkspaceApplicationRequest', ], 'output' => [ 'shape' => 'AssociateWorkspaceApplicationResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ComputeNotCompatibleException', ], [ 'shape' => 'OperatingSystemNotCompatibleException', ], [ 'shape' => 'ApplicationNotSupportedException', ], [ 'shape' => 'IncompatibleApplicationsException', ], ], ], 'AuthorizeIpRules' => [ 'name' => 'AuthorizeIpRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AuthorizeIpRulesRequest', ], 'output' => [ 'shape' => 'AuthorizeIpRulesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CopyWorkspaceImage' => [ 'name' => 'CopyWorkspaceImage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CopyWorkspaceImageRequest', ], 'output' => [ 'shape' => 'CopyWorkspaceImageResult', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], ], ], 'CreateAccountLinkInvitation' => [ 'name' => 'CreateAccountLinkInvitation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAccountLinkInvitationRequest', ], 'output' => [ 'shape' => 'CreateAccountLinkInvitationResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateConnectClientAddIn' => [ 'name' => 'CreateConnectClientAddIn', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateConnectClientAddInRequest', ], 'output' => [ 'shape' => 'CreateConnectClientAddInResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceCreationFailedException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateConnectionAlias' => [ 'name' => 'CreateConnectionAlias', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateConnectionAliasRequest', ], 'output' => [ 'shape' => 'CreateConnectionAliasResult', ], 'errors' => [ [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'CreateIpGroup' => [ 'name' => 'CreateIpGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateIpGroupRequest', ], 'output' => [ 'shape' => 'CreateIpGroupResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceCreationFailedException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateStandbyWorkspaces' => [ 'name' => 'CreateStandbyWorkspaces', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateStandbyWorkspacesRequest', ], 'output' => [ 'shape' => 'CreateStandbyWorkspacesResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceLimitExceededException', ], ], ], 'CreateTags' => [ 'name' => 'CreateTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTagsRequest', ], 'output' => [ 'shape' => 'CreateTagsResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceLimitExceededException', ], ], ], 'CreateUpdatedWorkspaceImage' => [ 'name' => 'CreateUpdatedWorkspaceImage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUpdatedWorkspaceImageRequest', ], 'output' => [ 'shape' => 'CreateUpdatedWorkspaceImageResult', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], ], ], 'CreateWorkspaceBundle' => [ 'name' => 'CreateWorkspaceBundle', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateWorkspaceBundleRequest', ], 'output' => [ 'shape' => 'CreateWorkspaceBundleResult', ], 'errors' => [ [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateWorkspaceImage' => [ 'name' => 'CreateWorkspaceImage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateWorkspaceImageRequest', ], 'output' => [ 'shape' => 'CreateWorkspaceImageResult', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], ], ], 'CreateWorkspaces' => [ 'name' => 'CreateWorkspaces', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateWorkspacesRequest', ], 'output' => [ 'shape' => 'CreateWorkspacesResult', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'InvalidParameterValuesException', ], ], ], 'CreateWorkspacesPool' => [ 'name' => 'CreateWorkspacesPool', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateWorkspacesPoolRequest', ], 'output' => [ 'shape' => 'CreateWorkspacesPoolResult', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteAccountLinkInvitation' => [ 'name' => 'DeleteAccountLinkInvitation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAccountLinkInvitationRequest', ], 'output' => [ 'shape' => 'DeleteAccountLinkInvitationResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteClientBranding' => [ 'name' => 'DeleteClientBranding', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteClientBrandingRequest', ], 'output' => [ 'shape' => 'DeleteClientBrandingResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteConnectClientAddIn' => [ 'name' => 'DeleteConnectClientAddIn', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteConnectClientAddInRequest', ], 'output' => [ 'shape' => 'DeleteConnectClientAddInResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteConnectionAlias' => [ 'name' => 'DeleteConnectionAlias', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteConnectionAliasRequest', ], 'output' => [ 'shape' => 'DeleteConnectionAliasResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAssociatedException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'DeleteIpGroup' => [ 'name' => 'DeleteIpGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteIpGroupRequest', ], 'output' => [ 'shape' => 'DeleteIpGroupResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAssociatedException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteTags' => [ 'name' => 'DeleteTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTagsRequest', ], 'output' => [ 'shape' => 'DeleteTagsResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValuesException', ], ], ], 'DeleteWorkspaceBundle' => [ 'name' => 'DeleteWorkspaceBundle', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteWorkspaceBundleRequest', ], 'output' => [ 'shape' => 'DeleteWorkspaceBundleResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAssociatedException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteWorkspaceImage' => [ 'name' => 'DeleteWorkspaceImage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteWorkspaceImageRequest', ], 'output' => [ 'shape' => 'DeleteWorkspaceImageResult', ], 'errors' => [ [ 'shape' => 'ResourceAssociatedException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeployWorkspaceApplications' => [ 'name' => 'DeployWorkspaceApplications', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeployWorkspaceApplicationsRequest', ], 'output' => [ 'shape' => 'DeployWorkspaceApplicationsResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'IncompatibleApplicationsException', ], ], ], 'DeregisterWorkspaceDirectory' => [ 'name' => 'DeregisterWorkspaceDirectory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterWorkspaceDirectoryRequest', ], 'output' => [ 'shape' => 'DeregisterWorkspaceDirectoryResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidResourceStateException', ], ], ], 'DescribeAccount' => [ 'name' => 'DescribeAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAccountRequest', ], 'output' => [ 'shape' => 'DescribeAccountResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeAccountModifications' => [ 'name' => 'DescribeAccountModifications', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAccountModificationsRequest', ], 'output' => [ 'shape' => 'DescribeAccountModificationsResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeApplicationAssociations' => [ 'name' => 'DescribeApplicationAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeApplicationAssociationsRequest', ], 'output' => [ 'shape' => 'DescribeApplicationAssociationsResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeApplications' => [ 'name' => 'DescribeApplications', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeApplicationsRequest', ], 'output' => [ 'shape' => 'DescribeApplicationsResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeBundleAssociations' => [ 'name' => 'DescribeBundleAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeBundleAssociationsRequest', ], 'output' => [ 'shape' => 'DescribeBundleAssociationsResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeClientBranding' => [ 'name' => 'DescribeClientBranding', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeClientBrandingRequest', ], 'output' => [ 'shape' => 'DescribeClientBrandingResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeClientProperties' => [ 'name' => 'DescribeClientProperties', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeClientPropertiesRequest', ], 'output' => [ 'shape' => 'DescribeClientPropertiesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeConnectClientAddIns' => [ 'name' => 'DescribeConnectClientAddIns', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeConnectClientAddInsRequest', ], 'output' => [ 'shape' => 'DescribeConnectClientAddInsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeConnectionAliasPermissions' => [ 'name' => 'DescribeConnectionAliasPermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeConnectionAliasPermissionsRequest', ], 'output' => [ 'shape' => 'DescribeConnectionAliasPermissionsResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'DescribeConnectionAliases' => [ 'name' => 'DescribeConnectionAliases', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeConnectionAliasesRequest', ], 'output' => [ 'shape' => 'DescribeConnectionAliasesResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'DescribeImageAssociations' => [ 'name' => 'DescribeImageAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeImageAssociationsRequest', ], 'output' => [ 'shape' => 'DescribeImageAssociationsResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeIpGroups' => [ 'name' => 'DescribeIpGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeIpGroupsRequest', ], 'output' => [ 'shape' => 'DescribeIpGroupsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeTags' => [ 'name' => 'DescribeTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTagsRequest', ], 'output' => [ 'shape' => 'DescribeTagsResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeWorkspaceAssociations' => [ 'name' => 'DescribeWorkspaceAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeWorkspaceAssociationsRequest', ], 'output' => [ 'shape' => 'DescribeWorkspaceAssociationsResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeWorkspaceBundles' => [ 'name' => 'DescribeWorkspaceBundles', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeWorkspaceBundlesRequest', ], 'output' => [ 'shape' => 'DescribeWorkspaceBundlesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], ], ], 'DescribeWorkspaceDirectories' => [ 'name' => 'DescribeWorkspaceDirectories', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeWorkspaceDirectoriesRequest', ], 'output' => [ 'shape' => 'DescribeWorkspaceDirectoriesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], ], ], 'DescribeWorkspaceImagePermissions' => [ 'name' => 'DescribeWorkspaceImagePermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeWorkspaceImagePermissionsRequest', ], 'output' => [ 'shape' => 'DescribeWorkspaceImagePermissionsResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], ], ], 'DescribeWorkspaceImages' => [ 'name' => 'DescribeWorkspaceImages', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeWorkspaceImagesRequest', ], 'output' => [ 'shape' => 'DescribeWorkspaceImagesResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeWorkspaceSnapshots' => [ 'name' => 'DescribeWorkspaceSnapshots', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeWorkspaceSnapshotsRequest', ], 'output' => [ 'shape' => 'DescribeWorkspaceSnapshotsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeWorkspaces' => [ 'name' => 'DescribeWorkspaces', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeWorkspacesRequest', ], 'output' => [ 'shape' => 'DescribeWorkspacesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'DescribeWorkspacesConnectionStatus' => [ 'name' => 'DescribeWorkspacesConnectionStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeWorkspacesConnectionStatusRequest', ], 'output' => [ 'shape' => 'DescribeWorkspacesConnectionStatusResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], ], ], 'DescribeWorkspacesPoolSessions' => [ 'name' => 'DescribeWorkspacesPoolSessions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeWorkspacesPoolSessionsRequest', ], 'output' => [ 'shape' => 'DescribeWorkspacesPoolSessionsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeWorkspacesPools' => [ 'name' => 'DescribeWorkspacesPools', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeWorkspacesPoolsRequest', ], 'output' => [ 'shape' => 'DescribeWorkspacesPoolsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DisassociateConnectionAlias' => [ 'name' => 'DisassociateConnectionAlias', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateConnectionAliasRequest', ], 'output' => [ 'shape' => 'DisassociateConnectionAliasResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'DisassociateIpGroups' => [ 'name' => 'DisassociateIpGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateIpGroupsRequest', ], 'output' => [ 'shape' => 'DisassociateIpGroupsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'DisassociateWorkspaceApplication' => [ 'name' => 'DisassociateWorkspaceApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateWorkspaceApplicationRequest', ], 'output' => [ 'shape' => 'DisassociateWorkspaceApplicationResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAccountLink' => [ 'name' => 'GetAccountLink', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAccountLinkRequest', ], 'output' => [ 'shape' => 'GetAccountLinkResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ImportClientBranding' => [ 'name' => 'ImportClientBranding', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ImportClientBrandingRequest', ], 'output' => [ 'shape' => 'ImportClientBrandingResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ImportWorkspaceImage' => [ 'name' => 'ImportWorkspaceImage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ImportWorkspaceImageRequest', ], 'output' => [ 'shape' => 'ImportWorkspaceImageResult', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], ], ], 'ListAccountLinks' => [ 'name' => 'ListAccountLinks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAccountLinksRequest', ], 'output' => [ 'shape' => 'ListAccountLinksResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListAvailableManagementCidrRanges' => [ 'name' => 'ListAvailableManagementCidrRanges', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAvailableManagementCidrRangesRequest', ], 'output' => [ 'shape' => 'ListAvailableManagementCidrRangesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'MigrateWorkspace' => [ 'name' => 'MigrateWorkspace', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'MigrateWorkspaceRequest', ], 'output' => [ 'shape' => 'MigrateWorkspaceResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'OperationInProgressException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'ModifyAccount' => [ 'name' => 'ModifyAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyAccountRequest', ], 'output' => [ 'shape' => 'ModifyAccountResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ModifyCertificateBasedAuthProperties' => [ 'name' => 'ModifyCertificateBasedAuthProperties', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyCertificateBasedAuthPropertiesRequest', ], 'output' => [ 'shape' => 'ModifyCertificateBasedAuthPropertiesResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ModifyClientProperties' => [ 'name' => 'ModifyClientProperties', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyClientPropertiesRequest', ], 'output' => [ 'shape' => 'ModifyClientPropertiesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'ModifyEndpointEncryptionMode' => [ 'name' => 'ModifyEndpointEncryptionMode', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyEndpointEncryptionModeRequest', ], 'output' => [ 'shape' => 'ModifyEndpointEncryptionModeResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'ModifySamlProperties' => [ 'name' => 'ModifySamlProperties', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifySamlPropertiesRequest', ], 'output' => [ 'shape' => 'ModifySamlPropertiesResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ModifySelfservicePermissions' => [ 'name' => 'ModifySelfservicePermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifySelfservicePermissionsRequest', ], 'output' => [ 'shape' => 'ModifySelfservicePermissionsResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'ModifyStreamingProperties' => [ 'name' => 'ModifyStreamingProperties', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyStreamingPropertiesRequest', ], 'output' => [ 'shape' => 'ModifyStreamingPropertiesResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'ModifyWorkspaceAccessProperties' => [ 'name' => 'ModifyWorkspaceAccessProperties', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyWorkspaceAccessPropertiesRequest', ], 'output' => [ 'shape' => 'ModifyWorkspaceAccessPropertiesResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'ModifyWorkspaceCreationProperties' => [ 'name' => 'ModifyWorkspaceCreationProperties', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyWorkspaceCreationPropertiesRequest', ], 'output' => [ 'shape' => 'ModifyWorkspaceCreationPropertiesResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'ModifyWorkspaceProperties' => [ 'name' => 'ModifyWorkspaceProperties', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyWorkspacePropertiesRequest', ], 'output' => [ 'shape' => 'ModifyWorkspacePropertiesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'OperationInProgressException', ], [ 'shape' => 'UnsupportedWorkspaceConfigurationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'ModifyWorkspaceState' => [ 'name' => 'ModifyWorkspaceState', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyWorkspaceStateRequest', ], 'output' => [ 'shape' => 'ModifyWorkspaceStateResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'RebootWorkspaces' => [ 'name' => 'RebootWorkspaces', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RebootWorkspacesRequest', ], 'output' => [ 'shape' => 'RebootWorkspacesResult', ], 'errors' => [ [ 'shape' => 'OperationNotSupportedException', ], ], ], 'RebuildWorkspaces' => [ 'name' => 'RebuildWorkspaces', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RebuildWorkspacesRequest', ], 'output' => [ 'shape' => 'RebuildWorkspacesResult', ], 'errors' => [ [ 'shape' => 'OperationNotSupportedException', ], ], ], 'RegisterWorkspaceDirectory' => [ 'name' => 'RegisterWorkspaceDirectory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterWorkspaceDirectoryRequest', ], 'output' => [ 'shape' => 'RegisterWorkspaceDirectoryResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'WorkspacesDefaultRoleNotFoundException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'UnsupportedNetworkConfigurationException', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], ], ], 'RejectAccountLinkInvitation' => [ 'name' => 'RejectAccountLinkInvitation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RejectAccountLinkInvitationRequest', ], 'output' => [ 'shape' => 'RejectAccountLinkInvitationResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'RestoreWorkspace' => [ 'name' => 'RestoreWorkspace', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RestoreWorkspaceRequest', ], 'output' => [ 'shape' => 'RestoreWorkspaceResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'RevokeIpRules' => [ 'name' => 'RevokeIpRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RevokeIpRulesRequest', ], 'output' => [ 'shape' => 'RevokeIpRulesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StartWorkspaces' => [ 'name' => 'StartWorkspaces', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartWorkspacesRequest', ], 'output' => [ 'shape' => 'StartWorkspacesResult', ], ], 'StartWorkspacesPool' => [ 'name' => 'StartWorkspacesPool', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartWorkspacesPoolRequest', ], 'output' => [ 'shape' => 'StartWorkspacesPoolResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'OperationInProgressException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StopWorkspaces' => [ 'name' => 'StopWorkspaces', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopWorkspacesRequest', ], 'output' => [ 'shape' => 'StopWorkspacesResult', ], ], 'StopWorkspacesPool' => [ 'name' => 'StopWorkspacesPool', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopWorkspacesPoolRequest', ], 'output' => [ 'shape' => 'StopWorkspacesPoolResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationInProgressException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'TerminateWorkspaces' => [ 'name' => 'TerminateWorkspaces', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TerminateWorkspacesRequest', ], 'output' => [ 'shape' => 'TerminateWorkspacesResult', ], ], 'TerminateWorkspacesPool' => [ 'name' => 'TerminateWorkspacesPool', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TerminateWorkspacesPoolRequest', ], 'output' => [ 'shape' => 'TerminateWorkspacesPoolResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationInProgressException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'TerminateWorkspacesPoolSession' => [ 'name' => 'TerminateWorkspacesPoolSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TerminateWorkspacesPoolSessionRequest', ], 'output' => [ 'shape' => 'TerminateWorkspacesPoolSessionResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'OperationInProgressException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateConnectClientAddIn' => [ 'name' => 'UpdateConnectClientAddIn', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateConnectClientAddInRequest', ], 'output' => [ 'shape' => 'UpdateConnectClientAddInResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateConnectionAliasPermission' => [ 'name' => 'UpdateConnectionAliasPermission', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateConnectionAliasPermissionRequest', ], 'output' => [ 'shape' => 'UpdateConnectionAliasPermissionResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAssociatedException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'UpdateRulesOfIpGroup' => [ 'name' => 'UpdateRulesOfIpGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRulesOfIpGroupRequest', ], 'output' => [ 'shape' => 'UpdateRulesOfIpGroupResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateWorkspaceBundle' => [ 'name' => 'UpdateWorkspaceBundle', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateWorkspaceBundleRequest', ], 'output' => [ 'shape' => 'UpdateWorkspaceBundleResult', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'UpdateWorkspaceImagePermission' => [ 'name' => 'UpdateWorkspaceImagePermission', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateWorkspaceImagePermissionRequest', ], 'output' => [ 'shape' => 'UpdateWorkspaceImagePermissionResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'UpdateWorkspacesPool' => [ 'name' => 'UpdateWorkspacesPool', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateWorkspacesPoolRequest', ], 'output' => [ 'shape' => 'UpdateWorkspacesPoolResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValuesException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'OperationInProgressException', ], [ 'shape' => 'AccessDeniedException', ], ], ], ], 'shapes' => [ 'AGAModeForDirectoryEnum' => [ 'type' => 'string', 'enum' => [ 'ENABLED_AUTO', 'DISABLED', ], ], 'AGAModeForWorkSpaceEnum' => [ 'type' => 'string', 'enum' => [ 'ENABLED_AUTO', 'DISABLED', 'INHERITED', ], ], 'AGAPreferredProtocolForDirectory' => [ 'type' => 'string', 'enum' => [ 'TCP', 'NONE', ], ], 'AGAPreferredProtocolForWorkSpace' => [ 'type' => 'string', 'enum' => [ 'TCP', 'NONE', 'INHERITED', ], ], 'ARN' => [ 'type' => 'string', 'pattern' => '^arn:aws[a-z-]{0,7}:[A-Za-z0-9][A-za-z0-9_/.-]{0,62}:[A-za-z0-9_/.-]{0,63}:[A-za-z0-9_/.-]{0,63}:[A-Za-z0-9][A-Za-z0-9:_/+=,@.\\\\-]{0,1023}$', ], 'AcceptAccountLinkInvitationRequest' => [ 'type' => 'structure', 'required' => [ 'LinkId', ], 'members' => [ 'LinkId' => [ 'shape' => 'LinkId', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], ], ], 'AcceptAccountLinkInvitationResult' => [ 'type' => 'structure', 'members' => [ 'AccountLink' => [ 'shape' => 'AccountLink', ], ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'AccessEndpoint' => [ 'type' => 'structure', 'members' => [ 'AccessEndpointType' => [ 'shape' => 'AccessEndpointType', ], 'VpcEndpointId' => [ 'shape' => 'AlphanumericDashUnderscoreNonEmptyString', ], ], ], 'AccessEndpointConfig' => [ 'type' => 'structure', 'required' => [ 'AccessEndpoints', ], 'members' => [ 'AccessEndpoints' => [ 'shape' => 'AccessEndpointList', ], 'InternetFallbackProtocols' => [ 'shape' => 'InternetFallbackProtocolList', ], ], ], 'AccessEndpointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessEndpoint', ], ], 'AccessEndpointType' => [ 'type' => 'string', 'enum' => [ 'STREAMING_WSP', ], ], 'AccessPropertyValue' => [ 'type' => 'string', 'enum' => [ 'ALLOW', 'DENY', ], ], 'AccountLink' => [ 'type' => 'structure', 'members' => [ 'AccountLinkId' => [ 'shape' => 'LinkId', ], 'AccountLinkStatus' => [ 'shape' => 'AccountLinkStatusEnum', ], 'SourceAccountId' => [ 'shape' => 'AwsAccount', ], 'TargetAccountId' => [ 'shape' => 'AwsAccount', ], ], ], 'AccountLinkList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountLink', ], ], 'AccountLinkStatusEnum' => [ 'type' => 'string', 'enum' => [ 'LINKED', 'LINKING_FAILED', 'LINK_NOT_FOUND', 'PENDING_ACCEPTANCE_BY_TARGET_ACCOUNT', 'REJECTED', ], ], 'AccountModification' => [ 'type' => 'structure', 'members' => [ 'ModificationState' => [ 'shape' => 'DedicatedTenancyModificationStateEnum', ], 'DedicatedTenancySupport' => [ 'shape' => 'DedicatedTenancySupportResultEnum', ], 'DedicatedTenancyManagementCidrRange' => [ 'shape' => 'DedicatedTenancyManagementCidrRange', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'ErrorCode' => [ 'shape' => 'WorkspaceErrorCode', ], 'ErrorMessage' => [ 'shape' => 'Description', ], ], ], 'AccountModificationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountModification', ], ], 'ActiveDirectoryConfig' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'ServiceAccountSecretArn', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', ], 'ServiceAccountSecretArn' => [ 'shape' => 'SecretsManagerArn', ], ], ], 'ActiveUserSessions' => [ 'type' => 'integer', 'min' => 0, ], 'ActualUserSessions' => [ 'type' => 'integer', 'min' => 0, ], 'AddInName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^.*$', ], 'AddInUrl' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^(http|https)\\://\\S+', ], 'Alias' => [ 'type' => 'string', ], 'AlphanumericDashUnderscoreNonEmptyString' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9\\_\\-]{1,1000}$', ], 'AmazonUuid' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', ], 'Application' => [ 'type' => 'string', 'enum' => [ 'Microsoft_Office_2016', 'Microsoft_Office_2019', ], ], 'ApplicationAssociatedResourceType' => [ 'type' => 'string', 'enum' => [ 'WORKSPACE', 'BUNDLE', 'IMAGE', ], ], 'ApplicationAssociatedResourceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationAssociatedResourceType', ], ], 'ApplicationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Application', ], 'max' => 5, 'min' => 1, ], 'ApplicationNotSupportedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ApplicationResourceAssociation' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => 'WorkSpaceApplicationId', ], 'AssociatedResourceId' => [ 'shape' => 'NonEmptyString', ], 'AssociatedResourceType' => [ 'shape' => 'ApplicationAssociatedResourceType', ], 'Created' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'State' => [ 'shape' => 'AssociationState', ], 'StateReason' => [ 'shape' => 'AssociationStateReason', ], ], ], 'ApplicationResourceAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationResourceAssociation', ], ], 'ApplicationSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'Status', ], 'members' => [ 'Status' => [ 'shape' => 'ApplicationSettingsStatusEnum', ], 'SettingsGroup' => [ 'shape' => 'SettingsGroup', ], ], ], 'ApplicationSettingsResponse' => [ 'type' => 'structure', 'required' => [ 'Status', ], 'members' => [ 'Status' => [ 'shape' => 'ApplicationSettingsStatusEnum', ], 'SettingsGroup' => [ 'shape' => 'SettingsGroup', ], 'S3BucketName' => [ 'shape' => 'S3BucketName', ], ], ], 'ApplicationSettingsStatusEnum' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'AssociateConnectionAliasRequest' => [ 'type' => 'structure', 'required' => [ 'AliasId', 'ResourceId', ], 'members' => [ 'AliasId' => [ 'shape' => 'ConnectionAliasId', ], 'ResourceId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AssociateConnectionAliasResult' => [ 'type' => 'structure', 'members' => [ 'ConnectionIdentifier' => [ 'shape' => 'ConnectionIdentifier', ], ], ], 'AssociateIpGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'GroupIds', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'GroupIds' => [ 'shape' => 'IpGroupIdList', ], ], ], 'AssociateIpGroupsResult' => [ 'type' => 'structure', 'members' => [], ], 'AssociateWorkspaceApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'WorkspaceId', 'ApplicationId', ], 'members' => [ 'WorkspaceId' => [ 'shape' => 'WorkspaceId', ], 'ApplicationId' => [ 'shape' => 'WorkSpaceApplicationId', ], ], ], 'AssociateWorkspaceApplicationResult' => [ 'type' => 'structure', 'members' => [ 'Association' => [ 'shape' => 'WorkspaceResourceAssociation', ], ], ], 'AssociationErrorCode' => [ 'type' => 'string', 'enum' => [ 'ValidationError.InsufficientDiskSpace', 'ValidationError.InsufficientMemory', 'ValidationError.UnsupportedOperatingSystem', 'DeploymentError.InternalServerError', 'DeploymentError.WorkspaceUnreachable', ], ], 'AssociationState' => [ 'type' => 'string', 'enum' => [ 'PENDING_INSTALL', 'PENDING_INSTALL_DEPLOYMENT', 'PENDING_UNINSTALL', 'PENDING_UNINSTALL_DEPLOYMENT', 'INSTALLING', 'UNINSTALLING', 'ERROR', 'COMPLETED', 'REMOVED', ], ], 'AssociationStateReason' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'AssociationErrorCode', ], 'ErrorMessage' => [ 'shape' => 'String2048', ], ], ], 'AssociationStatus' => [ 'type' => 'string', 'enum' => [ 'NOT_ASSOCIATED', 'ASSOCIATED_WITH_OWNER_ACCOUNT', 'ASSOCIATED_WITH_SHARED_ACCOUNT', 'PENDING_ASSOCIATION', 'PENDING_DISASSOCIATION', ], ], 'AuthenticationType' => [ 'type' => 'string', 'enum' => [ 'SAML', ], ], 'AuthorizeIpRulesRequest' => [ 'type' => 'structure', 'required' => [ 'GroupId', 'UserRules', ], 'members' => [ 'GroupId' => [ 'shape' => 'IpGroupId', ], 'UserRules' => [ 'shape' => 'IpRuleList', ], ], ], 'AuthorizeIpRulesResult' => [ 'type' => 'structure', 'members' => [], ], 'AvailableUserSessions' => [ 'type' => 'integer', 'min' => 0, ], 'AwsAccount' => [ 'type' => 'string', 'pattern' => '^\\d{12}$', ], 'BooleanObject' => [ 'type' => 'boolean', ], 'BundleAssociatedResourceType' => [ 'type' => 'string', 'enum' => [ 'APPLICATION', ], ], 'BundleAssociatedResourceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BundleAssociatedResourceType', ], ], 'BundleId' => [ 'type' => 'string', 'pattern' => '^wsb-[0-9a-z]{8,63}$', ], 'BundleIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BundleId', ], 'max' => 25, 'min' => 1, ], 'BundleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkspaceBundle', ], ], 'BundleOwner' => [ 'type' => 'string', ], 'BundleResourceAssociation' => [ 'type' => 'structure', 'members' => [ 'AssociatedResourceId' => [ 'shape' => 'NonEmptyString', ], 'AssociatedResourceType' => [ 'shape' => 'BundleAssociatedResourceType', ], 'BundleId' => [ 'shape' => 'BundleId', ], 'Created' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'State' => [ 'shape' => 'AssociationState', ], 'StateReason' => [ 'shape' => 'AssociationStateReason', ], ], ], 'BundleResourceAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BundleResourceAssociation', ], ], 'BundleType' => [ 'type' => 'string', 'enum' => [ 'REGULAR', 'STANDBY', ], ], 'Capacity' => [ 'type' => 'structure', 'required' => [ 'DesiredUserSessions', ], 'members' => [ 'DesiredUserSessions' => [ 'shape' => 'DesiredUserSessions', ], ], ], 'CapacityStatus' => [ 'type' => 'structure', 'required' => [ 'AvailableUserSessions', 'DesiredUserSessions', 'ActualUserSessions', 'ActiveUserSessions', ], 'members' => [ 'AvailableUserSessions' => [ 'shape' => 'AvailableUserSessions', ], 'DesiredUserSessions' => [ 'shape' => 'DesiredUserSessions', ], 'ActualUserSessions' => [ 'shape' => 'ActualUserSessions', ], 'ActiveUserSessions' => [ 'shape' => 'ActiveUserSessions', ], ], ], 'CertificateAuthorityArn' => [ 'type' => 'string', 'max' => 200, 'min' => 5, 'pattern' => 'arn:[\\w+=/,.@-]+:[\\w+=/,.@-]+:[\\w+=/,.@-]*:[0-9]*:[\\w+=,.@-]+(/[\\w+=,.@-]+)*', ], 'CertificateBasedAuthProperties' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'CertificateBasedAuthStatusEnum', ], 'CertificateAuthorityArn' => [ 'shape' => 'CertificateAuthorityArn', ], ], ], 'CertificateBasedAuthStatusEnum' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'ClientDeviceType' => [ 'type' => 'string', 'enum' => [ 'DeviceTypeWindows', 'DeviceTypeOsx', 'DeviceTypeAndroid', 'DeviceTypeIos', 'DeviceTypeLinux', 'DeviceTypeWeb', ], ], 'ClientDeviceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ClientDeviceType', ], 'max' => 6, 'min' => 1, ], 'ClientEmail' => [ 'type' => 'string', 'max' => 64, 'min' => 6, 'pattern' => '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$', ], 'ClientLocale' => [ 'type' => 'string', 'max' => 5, 'min' => 5, 'pattern' => '^[a-z]{2}_[A-Z]{2}$', ], 'ClientLoginMessage' => [ 'type' => 'string', 'max' => 2000, 'min' => 0, 'pattern' => '^.*$', ], 'ClientProperties' => [ 'type' => 'structure', 'members' => [ 'ReconnectEnabled' => [ 'shape' => 'ReconnectEnum', ], 'LogUploadEnabled' => [ 'shape' => 'LogUploadEnum', ], ], ], 'ClientPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ClientPropertiesResult', ], ], 'ClientPropertiesResult' => [ 'type' => 'structure', 'members' => [ 'ResourceId' => [ 'shape' => 'NonEmptyString', ], 'ClientProperties' => [ 'shape' => 'ClientProperties', ], ], ], 'ClientToken' => [ 'type' => 'string', 'pattern' => '^.{1,64}$', ], 'ClientUrl' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^(http|https)\\://\\S+', ], 'Compute' => [ 'type' => 'string', 'enum' => [ 'VALUE', 'STANDARD', 'PERFORMANCE', 'POWER', 'GRAPHICS', 'POWERPRO', 'GENERALPURPOSE_4XLARGE', 'GENERALPURPOSE_8XLARGE', 'GRAPHICSPRO', 'GRAPHICS_G4DN', 'GRAPHICSPRO_G4DN', ], ], 'ComputeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Compute', ], ], 'ComputeNotCompatibleException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ComputeType' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'Compute', ], ], ], 'ComputerName' => [ 'type' => 'string', ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ConnectClientAddIn' => [ 'type' => 'structure', 'members' => [ 'AddInId' => [ 'shape' => 'AmazonUuid', ], 'ResourceId' => [ 'shape' => 'DirectoryId', ], 'Name' => [ 'shape' => 'AddInName', ], 'URL' => [ 'shape' => 'AddInUrl', ], ], ], 'ConnectClientAddInList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectClientAddIn', ], ], 'ConnectionAlias' => [ 'type' => 'structure', 'members' => [ 'ConnectionString' => [ 'shape' => 'ConnectionString', ], 'AliasId' => [ 'shape' => 'ConnectionAliasId', ], 'State' => [ 'shape' => 'ConnectionAliasState', ], 'OwnerAccountId' => [ 'shape' => 'AwsAccount', ], 'Associations' => [ 'shape' => 'ConnectionAliasAssociationList', ], ], ], 'ConnectionAliasAssociation' => [ 'type' => 'structure', 'members' => [ 'AssociationStatus' => [ 'shape' => 'AssociationStatus', ], 'AssociatedAccountId' => [ 'shape' => 'AwsAccount', ], 'ResourceId' => [ 'shape' => 'NonEmptyString', ], 'ConnectionIdentifier' => [ 'shape' => 'ConnectionIdentifier', ], ], ], 'ConnectionAliasAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectionAliasAssociation', ], 'max' => 25, 'min' => 1, ], 'ConnectionAliasId' => [ 'type' => 'string', 'max' => 68, 'min' => 13, 'pattern' => '^wsca-[0-9a-z]{8,63}$', ], 'ConnectionAliasIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectionAliasId', ], 'max' => 25, 'min' => 1, ], 'ConnectionAliasList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectionAlias', ], 'max' => 25, 'min' => 1, ], 'ConnectionAliasPermission' => [ 'type' => 'structure', 'required' => [ 'SharedAccountId', 'AllowAssociation', ], 'members' => [ 'SharedAccountId' => [ 'shape' => 'AwsAccount', ], 'AllowAssociation' => [ 'shape' => 'BooleanObject', ], ], ], 'ConnectionAliasPermissions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectionAliasPermission', ], 'max' => 25, 'min' => 1, ], 'ConnectionAliasState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATED', 'DELETING', ], ], 'ConnectionIdentifier' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '^[a-zA-Z0-9]+$', ], 'ConnectionState' => [ 'type' => 'string', 'enum' => [ 'CONNECTED', 'DISCONNECTED', 'UNKNOWN', ], ], 'ConnectionString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[.0-9a-zA-Z\\-]{1,255}$', ], 'CopyWorkspaceImageRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'SourceImageId', 'SourceRegion', ], 'members' => [ 'Name' => [ 'shape' => 'WorkspaceImageName', ], 'Description' => [ 'shape' => 'WorkspaceImageDescription', ], 'SourceImageId' => [ 'shape' => 'WorkspaceImageId', ], 'SourceRegion' => [ 'shape' => 'Region', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CopyWorkspaceImageResult' => [ 'type' => 'structure', 'members' => [ 'ImageId' => [ 'shape' => 'WorkspaceImageId', ], ], ], 'CreateAccountLinkInvitationRequest' => [ 'type' => 'structure', 'required' => [ 'TargetAccountId', ], 'members' => [ 'TargetAccountId' => [ 'shape' => 'AwsAccount', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], ], ], 'CreateAccountLinkInvitationResult' => [ 'type' => 'structure', 'members' => [ 'AccountLink' => [ 'shape' => 'AccountLink', ], ], ], 'CreateConnectClientAddInRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'Name', 'URL', ], 'members' => [ 'ResourceId' => [ 'shape' => 'DirectoryId', ], 'Name' => [ 'shape' => 'AddInName', ], 'URL' => [ 'shape' => 'AddInUrl', ], ], ], 'CreateConnectClientAddInResult' => [ 'type' => 'structure', 'members' => [ 'AddInId' => [ 'shape' => 'AmazonUuid', ], ], ], 'CreateConnectionAliasRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectionString', ], 'members' => [ 'ConnectionString' => [ 'shape' => 'ConnectionString', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateConnectionAliasResult' => [ 'type' => 'structure', 'members' => [ 'AliasId' => [ 'shape' => 'ConnectionAliasId', ], ], ], 'CreateIpGroupRequest' => [ 'type' => 'structure', 'required' => [ 'GroupName', ], 'members' => [ 'GroupName' => [ 'shape' => 'IpGroupName', ], 'GroupDesc' => [ 'shape' => 'IpGroupDesc', ], 'UserRules' => [ 'shape' => 'IpRuleList', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateIpGroupResult' => [ 'type' => 'structure', 'members' => [ 'GroupId' => [ 'shape' => 'IpGroupId', ], ], ], 'CreateStandbyWorkspacesRequest' => [ 'type' => 'structure', 'required' => [ 'PrimaryRegion', 'StandbyWorkspaces', ], 'members' => [ 'PrimaryRegion' => [ 'shape' => 'Region', ], 'StandbyWorkspaces' => [ 'shape' => 'StandbyWorkspacesList', ], ], ], 'CreateStandbyWorkspacesResult' => [ 'type' => 'structure', 'members' => [ 'FailedStandbyRequests' => [ 'shape' => 'FailedCreateStandbyWorkspacesRequestList', ], 'PendingStandbyRequests' => [ 'shape' => 'PendingCreateStandbyWorkspacesRequestList', ], ], ], 'CreateTagsRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'Tags', ], 'members' => [ 'ResourceId' => [ 'shape' => 'NonEmptyString', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateTagsResult' => [ 'type' => 'structure', 'members' => [], ], 'CreateUpdatedWorkspaceImageRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Description', 'SourceImageId', ], 'members' => [ 'Name' => [ 'shape' => 'WorkspaceImageName', ], 'Description' => [ 'shape' => 'WorkspaceImageDescription', ], 'SourceImageId' => [ 'shape' => 'WorkspaceImageId', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateUpdatedWorkspaceImageResult' => [ 'type' => 'structure', 'members' => [ 'ImageId' => [ 'shape' => 'WorkspaceImageId', ], ], ], 'CreateWorkspaceBundleRequest' => [ 'type' => 'structure', 'required' => [ 'BundleName', 'BundleDescription', 'ImageId', 'ComputeType', 'UserStorage', ], 'members' => [ 'BundleName' => [ 'shape' => 'WorkspaceBundleName', ], 'BundleDescription' => [ 'shape' => 'WorkspaceBundleDescription', ], 'ImageId' => [ 'shape' => 'WorkspaceImageId', ], 'ComputeType' => [ 'shape' => 'ComputeType', ], 'UserStorage' => [ 'shape' => 'UserStorage', ], 'RootStorage' => [ 'shape' => 'RootStorage', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateWorkspaceBundleResult' => [ 'type' => 'structure', 'members' => [ 'WorkspaceBundle' => [ 'shape' => 'WorkspaceBundle', ], ], ], 'CreateWorkspaceImageRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Description', 'WorkspaceId', ], 'members' => [ 'Name' => [ 'shape' => 'WorkspaceImageName', ], 'Description' => [ 'shape' => 'WorkspaceImageDescription', ], 'WorkspaceId' => [ 'shape' => 'WorkspaceId', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateWorkspaceImageResult' => [ 'type' => 'structure', 'members' => [ 'ImageId' => [ 'shape' => 'WorkspaceImageId', ], 'Name' => [ 'shape' => 'WorkspaceImageName', ], 'Description' => [ 'shape' => 'WorkspaceImageDescription', ], 'OperatingSystem' => [ 'shape' => 'OperatingSystem', ], 'State' => [ 'shape' => 'WorkspaceImageState', ], 'RequiredTenancy' => [ 'shape' => 'WorkspaceImageRequiredTenancy', ], 'Created' => [ 'shape' => 'Timestamp', ], 'OwnerAccountId' => [ 'shape' => 'AwsAccount', ], ], ], 'CreateWorkspacesPoolRequest' => [ 'type' => 'structure', 'required' => [ 'PoolName', 'Description', 'BundleId', 'DirectoryId', 'Capacity', ], 'members' => [ 'PoolName' => [ 'shape' => 'WorkspacesPoolName', ], 'Description' => [ 'shape' => 'UpdateDescription', ], 'BundleId' => [ 'shape' => 'BundleId', ], 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Capacity' => [ 'shape' => 'Capacity', ], 'Tags' => [ 'shape' => 'TagList', ], 'ApplicationSettings' => [ 'shape' => 'ApplicationSettingsRequest', ], 'TimeoutSettings' => [ 'shape' => 'TimeoutSettings', ], 'RunningMode' => [ 'shape' => 'PoolsRunningMode', ], ], ], 'CreateWorkspacesPoolResult' => [ 'type' => 'structure', 'members' => [ 'WorkspacesPool' => [ 'shape' => 'WorkspacesPool', ], ], ], 'CreateWorkspacesRequest' => [ 'type' => 'structure', 'required' => [ 'Workspaces', ], 'members' => [ 'Workspaces' => [ 'shape' => 'WorkspaceRequestList', ], ], ], 'CreateWorkspacesResult' => [ 'type' => 'structure', 'members' => [ 'FailedRequests' => [ 'shape' => 'FailedCreateWorkspaceRequests', ], 'PendingRequests' => [ 'shape' => 'WorkspaceList', ], ], ], 'DataReplication' => [ 'type' => 'string', 'enum' => [ 'NO_REPLICATION', 'PRIMARY_AS_SOURCE', ], ], 'DataReplicationSettings' => [ 'type' => 'structure', 'members' => [ 'DataReplication' => [ 'shape' => 'DataReplication', ], 'RecoverySnapshotTime' => [ 'shape' => 'Timestamp', ], ], ], 'DedicatedTenancyAccountType' => [ 'type' => 'string', 'enum' => [ 'SOURCE_ACCOUNT', 'TARGET_ACCOUNT', ], ], 'DedicatedTenancyCidrRangeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DedicatedTenancyManagementCidrRange', ], ], 'DedicatedTenancyManagementCidrRange' => [ 'type' => 'string', 'pattern' => '(^([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\.([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\.0\\.0)(\\/(16$))$', ], 'DedicatedTenancyModificationStateEnum' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'COMPLETED', 'FAILED', ], ], 'DedicatedTenancySupportEnum' => [ 'type' => 'string', 'enum' => [ 'ENABLED', ], ], 'DedicatedTenancySupportResultEnum' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'DefaultClientBrandingAttributes' => [ 'type' => 'structure', 'members' => [ 'LogoUrl' => [ 'shape' => 'ClientUrl', ], 'SupportEmail' => [ 'shape' => 'ClientEmail', ], 'SupportLink' => [ 'shape' => 'ClientUrl', ], 'ForgotPasswordLink' => [ 'shape' => 'ClientUrl', ], 'LoginMessage' => [ 'shape' => 'LoginMessage', ], ], ], 'DefaultImportClientBrandingAttributes' => [ 'type' => 'structure', 'members' => [ 'Logo' => [ 'shape' => 'DefaultLogo', ], 'SupportEmail' => [ 'shape' => 'ClientEmail', ], 'SupportLink' => [ 'shape' => 'ClientUrl', ], 'ForgotPasswordLink' => [ 'shape' => 'ClientUrl', ], 'LoginMessage' => [ 'shape' => 'LoginMessage', ], ], ], 'DefaultLogo' => [ 'type' => 'blob', 'max' => 1500000, 'min' => 1, ], 'DefaultOu' => [ 'type' => 'string', ], 'DefaultWorkspaceCreationProperties' => [ 'type' => 'structure', 'members' => [ 'EnableInternetAccess' => [ 'shape' => 'BooleanObject', ], 'DefaultOu' => [ 'shape' => 'DefaultOu', ], 'CustomSecurityGroupId' => [ 'shape' => 'SecurityGroupId', ], 'UserEnabledAsLocalAdministrator' => [ 'shape' => 'BooleanObject', ], 'EnableMaintenanceMode' => [ 'shape' => 'BooleanObject', ], 'InstanceIamRoleArn' => [ 'shape' => 'ARN', ], ], ], 'DeletableCertificateBasedAuthPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeletableCertificateBasedAuthProperty', ], ], 'DeletableCertificateBasedAuthProperty' => [ 'type' => 'string', 'enum' => [ 'CERTIFICATE_BASED_AUTH_PROPERTIES_CERTIFICATE_AUTHORITY_ARN', ], ], 'DeletableSamlPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeletableSamlProperty', ], ], 'DeletableSamlProperty' => [ 'type' => 'string', 'enum' => [ 'SAML_PROPERTIES_USER_ACCESS_URL', 'SAML_PROPERTIES_RELAY_STATE_PARAMETER_NAME', ], ], 'DeleteAccountLinkInvitationRequest' => [ 'type' => 'structure', 'required' => [ 'LinkId', ], 'members' => [ 'LinkId' => [ 'shape' => 'LinkId', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], ], ], 'DeleteAccountLinkInvitationResult' => [ 'type' => 'structure', 'members' => [ 'AccountLink' => [ 'shape' => 'AccountLink', ], ], ], 'DeleteClientBrandingRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'Platforms', ], 'members' => [ 'ResourceId' => [ 'shape' => 'DirectoryId', ], 'Platforms' => [ 'shape' => 'ClientDeviceTypeList', ], ], ], 'DeleteClientBrandingResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteConnectClientAddInRequest' => [ 'type' => 'structure', 'required' => [ 'AddInId', 'ResourceId', ], 'members' => [ 'AddInId' => [ 'shape' => 'AmazonUuid', ], 'ResourceId' => [ 'shape' => 'DirectoryId', ], ], ], 'DeleteConnectClientAddInResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteConnectionAliasRequest' => [ 'type' => 'structure', 'required' => [ 'AliasId', ], 'members' => [ 'AliasId' => [ 'shape' => 'ConnectionAliasId', ], ], ], 'DeleteConnectionAliasResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteIpGroupRequest' => [ 'type' => 'structure', 'required' => [ 'GroupId', ], 'members' => [ 'GroupId' => [ 'shape' => 'IpGroupId', ], ], ], 'DeleteIpGroupResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTagsRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'TagKeys', ], 'members' => [ 'ResourceId' => [ 'shape' => 'NonEmptyString', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'DeleteTagsResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWorkspaceBundleRequest' => [ 'type' => 'structure', 'members' => [ 'BundleId' => [ 'shape' => 'BundleId', ], ], ], 'DeleteWorkspaceBundleResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWorkspaceImageRequest' => [ 'type' => 'structure', 'required' => [ 'ImageId', ], 'members' => [ 'ImageId' => [ 'shape' => 'WorkspaceImageId', ], ], ], 'DeleteWorkspaceImageResult' => [ 'type' => 'structure', 'members' => [], ], 'DeployWorkspaceApplicationsRequest' => [ 'type' => 'structure', 'required' => [ 'WorkspaceId', ], 'members' => [ 'WorkspaceId' => [ 'shape' => 'WorkspaceId', ], 'Force' => [ 'shape' => 'BooleanObject', ], ], ], 'DeployWorkspaceApplicationsResult' => [ 'type' => 'structure', 'members' => [ 'Deployment' => [ 'shape' => 'WorkSpaceApplicationDeployment', ], ], ], 'DeregisterWorkspaceDirectoryRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'DeregisterWorkspaceDirectoryResult' => [ 'type' => 'structure', 'members' => [], ], 'DescribeAccountModificationsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeAccountModificationsResult' => [ 'type' => 'structure', 'members' => [ 'AccountModifications' => [ 'shape' => 'AccountModificationList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeAccountRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeAccountResult' => [ 'type' => 'structure', 'members' => [ 'DedicatedTenancySupport' => [ 'shape' => 'DedicatedTenancySupportResultEnum', ], 'DedicatedTenancyManagementCidrRange' => [ 'shape' => 'DedicatedTenancyManagementCidrRange', ], 'DedicatedTenancyAccountType' => [ 'shape' => 'DedicatedTenancyAccountType', ], ], ], 'DescribeApplicationAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationId', 'AssociatedResourceTypes', ], 'members' => [ 'MaxResults' => [ 'shape' => 'Limit', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'ApplicationId' => [ 'shape' => 'WorkSpaceApplicationId', ], 'AssociatedResourceTypes' => [ 'shape' => 'ApplicationAssociatedResourceTypeList', ], ], ], 'DescribeApplicationAssociationsResult' => [ 'type' => 'structure', 'members' => [ 'Associations' => [ 'shape' => 'ApplicationResourceAssociationList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeApplicationsRequest' => [ 'type' => 'structure', 'members' => [ 'ApplicationIds' => [ 'shape' => 'WorkSpaceApplicationIdList', ], 'ComputeTypeNames' => [ 'shape' => 'ComputeList', ], 'LicenseType' => [ 'shape' => 'WorkSpaceApplicationLicenseType', ], 'OperatingSystemNames' => [ 'shape' => 'OperatingSystemNameList', ], 'Owner' => [ 'shape' => 'WorkSpaceApplicationOwner', ], 'MaxResults' => [ 'shape' => 'Limit', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeApplicationsResult' => [ 'type' => 'structure', 'members' => [ 'Applications' => [ 'shape' => 'WorkSpaceApplicationList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeBundleAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'BundleId', 'AssociatedResourceTypes', ], 'members' => [ 'BundleId' => [ 'shape' => 'BundleId', ], 'AssociatedResourceTypes' => [ 'shape' => 'BundleAssociatedResourceTypeList', ], ], ], 'DescribeBundleAssociationsResult' => [ 'type' => 'structure', 'members' => [ 'Associations' => [ 'shape' => 'BundleResourceAssociationList', ], ], ], 'DescribeClientBrandingRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', ], 'members' => [ 'ResourceId' => [ 'shape' => 'DirectoryId', ], ], ], 'DescribeClientBrandingResult' => [ 'type' => 'structure', 'members' => [ 'DeviceTypeWindows' => [ 'shape' => 'DefaultClientBrandingAttributes', ], 'DeviceTypeOsx' => [ 'shape' => 'DefaultClientBrandingAttributes', ], 'DeviceTypeAndroid' => [ 'shape' => 'DefaultClientBrandingAttributes', ], 'DeviceTypeIos' => [ 'shape' => 'IosClientBrandingAttributes', ], 'DeviceTypeLinux' => [ 'shape' => 'DefaultClientBrandingAttributes', ], 'DeviceTypeWeb' => [ 'shape' => 'DefaultClientBrandingAttributes', ], ], ], 'DescribeClientPropertiesRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceIds', ], 'members' => [ 'ResourceIds' => [ 'shape' => 'ResourceIdList', ], ], ], 'DescribeClientPropertiesResult' => [ 'type' => 'structure', 'members' => [ 'ClientPropertiesList' => [ 'shape' => 'ClientPropertiesList', ], ], ], 'DescribeConnectClientAddInsRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', ], 'members' => [ 'ResourceId' => [ 'shape' => 'DirectoryId', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'Limit', ], ], ], 'DescribeConnectClientAddInsResult' => [ 'type' => 'structure', 'members' => [ 'AddIns' => [ 'shape' => 'ConnectClientAddInList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeConnectionAliasPermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'AliasId', ], 'members' => [ 'AliasId' => [ 'shape' => 'ConnectionAliasId', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'Limit', ], ], ], 'DescribeConnectionAliasPermissionsResult' => [ 'type' => 'structure', 'members' => [ 'AliasId' => [ 'shape' => 'ConnectionAliasId', ], 'ConnectionAliasPermissions' => [ 'shape' => 'ConnectionAliasPermissions', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeConnectionAliasesRequest' => [ 'type' => 'structure', 'members' => [ 'AliasIds' => [ 'shape' => 'ConnectionAliasIdList', ], 'ResourceId' => [ 'shape' => 'NonEmptyString', ], 'Limit' => [ 'shape' => 'Limit', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeConnectionAliasesResult' => [ 'type' => 'structure', 'members' => [ 'ConnectionAliases' => [ 'shape' => 'ConnectionAliasList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeImageAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'ImageId', 'AssociatedResourceTypes', ], 'members' => [ 'ImageId' => [ 'shape' => 'WorkspaceImageId', ], 'AssociatedResourceTypes' => [ 'shape' => 'ImageAssociatedResourceTypeList', ], ], ], 'DescribeImageAssociationsResult' => [ 'type' => 'structure', 'members' => [ 'Associations' => [ 'shape' => 'ImageResourceAssociationList', ], ], ], 'DescribeIpGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'GroupIds' => [ 'shape' => 'IpGroupIdList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'Limit', ], ], ], 'DescribeIpGroupsResult' => [ 'type' => 'structure', 'members' => [ 'Result' => [ 'shape' => 'WorkspacesIpGroupsList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeTagsRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', ], 'members' => [ 'ResourceId' => [ 'shape' => 'NonEmptyString', ], ], ], 'DescribeTagsResult' => [ 'type' => 'structure', 'members' => [ 'TagList' => [ 'shape' => 'TagList', ], ], ], 'DescribeWorkspaceAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'WorkspaceId', 'AssociatedResourceTypes', ], 'members' => [ 'WorkspaceId' => [ 'shape' => 'WorkspaceId', ], 'AssociatedResourceTypes' => [ 'shape' => 'WorkSpaceAssociatedResourceTypeList', ], ], ], 'DescribeWorkspaceAssociationsResult' => [ 'type' => 'structure', 'members' => [ 'Associations' => [ 'shape' => 'WorkspaceResourceAssociationList', ], ], ], 'DescribeWorkspaceBundlesRequest' => [ 'type' => 'structure', 'members' => [ 'BundleIds' => [ 'shape' => 'BundleIdList', ], 'Owner' => [ 'shape' => 'BundleOwner', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeWorkspaceBundlesResult' => [ 'type' => 'structure', 'members' => [ 'Bundles' => [ 'shape' => 'BundleList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeWorkspaceDirectoriesFilter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'DescribeWorkspaceDirectoriesFilterName', ], 'Values' => [ 'shape' => 'DescribeWorkspaceDirectoriesFilterValues', ], ], ], 'DescribeWorkspaceDirectoriesFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DescribeWorkspaceDirectoriesFilter', ], 'max' => 25, 'min' => 1, ], 'DescribeWorkspaceDirectoriesFilterName' => [ 'type' => 'string', 'enum' => [ 'USER_IDENTITY_TYPE', 'WORKSPACE_TYPE', ], ], 'DescribeWorkspaceDirectoriesFilterValue' => [ 'type' => 'string', 'pattern' => '^[0-9a-zA-Z\\*\\.\\\\/\\?-_]{0,64}$', ], 'DescribeWorkspaceDirectoriesFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'DescribeWorkspaceDirectoriesFilterValue', ], 'max' => 25, 'min' => 1, ], 'DescribeWorkspaceDirectoriesRequest' => [ 'type' => 'structure', 'members' => [ 'DirectoryIds' => [ 'shape' => 'DirectoryIdList', ], 'WorkspaceDirectoryNames' => [ 'shape' => 'WorkspaceDirectoryNameList', ], 'Limit' => [ 'shape' => 'Limit', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'Filters' => [ 'shape' => 'DescribeWorkspaceDirectoriesFilterList', ], ], ], 'DescribeWorkspaceDirectoriesResult' => [ 'type' => 'structure', 'members' => [ 'Directories' => [ 'shape' => 'DirectoryList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeWorkspaceImagePermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'ImageId', ], 'members' => [ 'ImageId' => [ 'shape' => 'WorkspaceImageId', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'Limit', ], ], ], 'DescribeWorkspaceImagePermissionsResult' => [ 'type' => 'structure', 'members' => [ 'ImageId' => [ 'shape' => 'WorkspaceImageId', ], 'ImagePermissions' => [ 'shape' => 'ImagePermissions', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeWorkspaceImagesRequest' => [ 'type' => 'structure', 'members' => [ 'ImageIds' => [ 'shape' => 'WorkspaceImageIdList', ], 'ImageType' => [ 'shape' => 'ImageType', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'Limit', ], ], ], 'DescribeWorkspaceImagesResult' => [ 'type' => 'structure', 'members' => [ 'Images' => [ 'shape' => 'WorkspaceImageList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeWorkspaceSnapshotsRequest' => [ 'type' => 'structure', 'required' => [ 'WorkspaceId', ], 'members' => [ 'WorkspaceId' => [ 'shape' => 'WorkspaceId', ], ], ], 'DescribeWorkspaceSnapshotsResult' => [ 'type' => 'structure', 'members' => [ 'RebuildSnapshots' => [ 'shape' => 'SnapshotList', ], 'RestoreSnapshots' => [ 'shape' => 'SnapshotList', ], ], ], 'DescribeWorkspacesConnectionStatusRequest' => [ 'type' => 'structure', 'members' => [ 'WorkspaceIds' => [ 'shape' => 'WorkspaceIdList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeWorkspacesConnectionStatusResult' => [ 'type' => 'structure', 'members' => [ 'WorkspacesConnectionStatus' => [ 'shape' => 'WorkspaceConnectionStatusList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeWorkspacesPoolSessionsRequest' => [ 'type' => 'structure', 'required' => [ 'PoolId', ], 'members' => [ 'PoolId' => [ 'shape' => 'WorkspacesPoolId', ], 'UserId' => [ 'shape' => 'WorkspacesPoolUserId', ], 'Limit' => [ 'shape' => 'Limit50', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeWorkspacesPoolSessionsResult' => [ 'type' => 'structure', 'members' => [ 'Sessions' => [ 'shape' => 'WorkspacesPoolSessions', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeWorkspacesPoolsFilter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', 'Operator', ], 'members' => [ 'Name' => [ 'shape' => 'DescribeWorkspacesPoolsFilterName', ], 'Values' => [ 'shape' => 'DescribeWorkspacesPoolsFilterValues', ], 'Operator' => [ 'shape' => 'DescribeWorkspacesPoolsFilterOperator', ], ], ], 'DescribeWorkspacesPoolsFilterName' => [ 'type' => 'string', 'enum' => [ 'PoolName', ], ], 'DescribeWorkspacesPoolsFilterOperator' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'NOTEQUALS', 'CONTAINS', 'NOTCONTAINS', ], ], 'DescribeWorkspacesPoolsFilterValue' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[A-Za-z0-9][A-Za-z0-9_.-]+$', ], 'DescribeWorkspacesPoolsFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'DescribeWorkspacesPoolsFilterValue', ], 'max' => 25, 'min' => 1, ], 'DescribeWorkspacesPoolsFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'DescribeWorkspacesPoolsFilter', ], 'max' => 25, 'min' => 1, ], 'DescribeWorkspacesPoolsRequest' => [ 'type' => 'structure', 'members' => [ 'PoolIds' => [ 'shape' => 'WorkspacesPoolIds', ], 'Filters' => [ 'shape' => 'DescribeWorkspacesPoolsFilters', ], 'Limit' => [ 'shape' => 'Limit', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeWorkspacesPoolsResult' => [ 'type' => 'structure', 'members' => [ 'WorkspacesPools' => [ 'shape' => 'WorkspacesPools', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeWorkspacesRequest' => [ 'type' => 'structure', 'members' => [ 'WorkspaceIds' => [ 'shape' => 'WorkspaceIdList', ], 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'UserName' => [ 'shape' => 'UserName', ], 'BundleId' => [ 'shape' => 'BundleId', ], 'Limit' => [ 'shape' => 'Limit', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'WorkspaceName' => [ 'shape' => 'WorkspaceName', ], ], ], 'DescribeWorkspacesResult' => [ 'type' => 'structure', 'members' => [ 'Workspaces' => [ 'shape' => 'WorkspaceList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'Description' => [ 'type' => 'string', ], 'DesiredUserSessions' => [ 'type' => 'integer', 'min' => 0, ], 'DirectoryId' => [ 'type' => 'string', 'max' => 65, 'min' => 10, 'pattern' => '^(d-[0-9a-f]{8,63}$)|(wsd-[0-9a-z]{8,63}$)', ], 'DirectoryIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DirectoryId', ], 'max' => 25, 'min' => 1, ], 'DirectoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkspaceDirectory', ], ], 'DirectoryName' => [ 'type' => 'string', ], 'DisassociateConnectionAliasRequest' => [ 'type' => 'structure', 'required' => [ 'AliasId', ], 'members' => [ 'AliasId' => [ 'shape' => 'ConnectionAliasId', ], ], ], 'DisassociateConnectionAliasResult' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateIpGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'GroupIds', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'GroupIds' => [ 'shape' => 'IpGroupIdList', ], ], ], 'DisassociateIpGroupsResult' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateWorkspaceApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'WorkspaceId', 'ApplicationId', ], 'members' => [ 'WorkspaceId' => [ 'shape' => 'WorkspaceId', ], 'ApplicationId' => [ 'shape' => 'WorkSpaceApplicationId', ], ], ], 'DisassociateWorkspaceApplicationResult' => [ 'type' => 'structure', 'members' => [ 'Association' => [ 'shape' => 'WorkspaceResourceAssociation', ], ], ], 'DisconnectTimeoutInSeconds' => [ 'type' => 'integer', 'max' => 36000, 'min' => 60, ], 'DnsIpAddresses' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpAddress', ], ], 'DomainName' => [ 'type' => 'string', 'pattern' => '^([a-zA-Z0-9]+[.-])+([a-zA-Z0-9])+$', ], 'Ec2ImageId' => [ 'type' => 'string', 'pattern' => '^ami\\-([a-f0-9]{8}|[a-f0-9]{17})$', ], 'EndpointEncryptionMode' => [ 'type' => 'string', 'enum' => [ 'STANDARD_TLS', 'FIPS_VALIDATED', ], ], 'ErrorDetails' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'WorkspaceImageErrorDetailCode', ], 'ErrorMessage' => [ 'shape' => 'Description', ], ], ], 'ErrorDetailsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ErrorDetails', ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'ErrorType' => [ 'type' => 'string', ], 'ExceptionErrorCode' => [ 'type' => 'string', ], 'ExceptionMessage' => [ 'type' => 'string', ], 'FailedCreateStandbyWorkspacesRequest' => [ 'type' => 'structure', 'members' => [ 'StandbyWorkspaceRequest' => [ 'shape' => 'StandbyWorkspace', ], 'ErrorCode' => [ 'shape' => 'WorkspaceErrorCode', ], 'ErrorMessage' => [ 'shape' => 'Description', ], ], ], 'FailedCreateStandbyWorkspacesRequestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedCreateStandbyWorkspacesRequest', ], ], 'FailedCreateWorkspaceRequest' => [ 'type' => 'structure', 'members' => [ 'WorkspaceRequest' => [ 'shape' => 'WorkspaceRequest', ], 'ErrorCode' => [ 'shape' => 'ErrorType', ], 'ErrorMessage' => [ 'shape' => 'Description', ], ], ], 'FailedCreateWorkspaceRequests' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedCreateWorkspaceRequest', ], ], 'FailedRebootWorkspaceRequests' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedWorkspaceChangeRequest', ], ], 'FailedRebuildWorkspaceRequests' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedWorkspaceChangeRequest', ], ], 'FailedStartWorkspaceRequests' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedWorkspaceChangeRequest', ], ], 'FailedStopWorkspaceRequests' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedWorkspaceChangeRequest', ], ], 'FailedTerminateWorkspaceRequests' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedWorkspaceChangeRequest', ], ], 'FailedWorkspaceChangeRequest' => [ 'type' => 'structure', 'members' => [ 'WorkspaceId' => [ 'shape' => 'WorkspaceId', ], 'ErrorCode' => [ 'shape' => 'ErrorType', ], 'ErrorMessage' => [ 'shape' => 'Description', ], ], ], 'GetAccountLinkRequest' => [ 'type' => 'structure', 'members' => [ 'LinkId' => [ 'shape' => 'LinkId', ], 'LinkedAccountId' => [ 'shape' => 'AwsAccount', ], ], ], 'GetAccountLinkResult' => [ 'type' => 'structure', 'members' => [ 'AccountLink' => [ 'shape' => 'AccountLink', ], ], ], 'GlobalAcceleratorForDirectory' => [ 'type' => 'structure', 'required' => [ 'Mode', ], 'members' => [ 'Mode' => [ 'shape' => 'AGAModeForDirectoryEnum', ], 'PreferredProtocol' => [ 'shape' => 'AGAPreferredProtocolForDirectory', ], ], ], 'GlobalAcceleratorForWorkSpace' => [ 'type' => 'structure', 'required' => [ 'Mode', ], 'members' => [ 'Mode' => [ 'shape' => 'AGAModeForWorkSpaceEnum', ], 'PreferredProtocol' => [ 'shape' => 'AGAPreferredProtocolForWorkSpace', ], ], ], 'IDCConfig' => [ 'type' => 'structure', 'members' => [ 'InstanceArn' => [ 'shape' => 'ARN', ], 'ApplicationArn' => [ 'shape' => 'ARN', ], ], ], 'IdleDisconnectTimeoutInSeconds' => [ 'type' => 'integer', 'max' => 36000, 'min' => 0, ], 'ImageAssociatedResourceType' => [ 'type' => 'string', 'enum' => [ 'APPLICATION', ], ], 'ImageAssociatedResourceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImageAssociatedResourceType', ], ], 'ImagePermission' => [ 'type' => 'structure', 'members' => [ 'SharedAccountId' => [ 'shape' => 'AwsAccount', ], ], ], 'ImagePermissions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImagePermission', ], ], 'ImageResourceAssociation' => [ 'type' => 'structure', 'members' => [ 'AssociatedResourceId' => [ 'shape' => 'NonEmptyString', ], 'AssociatedResourceType' => [ 'shape' => 'ImageAssociatedResourceType', ], 'Created' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'ImageId' => [ 'shape' => 'WorkspaceImageId', ], 'State' => [ 'shape' => 'AssociationState', ], 'StateReason' => [ 'shape' => 'AssociationStateReason', ], ], ], 'ImageResourceAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImageResourceAssociation', ], ], 'ImageType' => [ 'type' => 'string', 'enum' => [ 'OWNED', 'SHARED', ], ], 'ImportClientBrandingRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', ], 'members' => [ 'ResourceId' => [ 'shape' => 'DirectoryId', ], 'DeviceTypeWindows' => [ 'shape' => 'DefaultImportClientBrandingAttributes', ], 'DeviceTypeOsx' => [ 'shape' => 'DefaultImportClientBrandingAttributes', ], 'DeviceTypeAndroid' => [ 'shape' => 'DefaultImportClientBrandingAttributes', ], 'DeviceTypeIos' => [ 'shape' => 'IosImportClientBrandingAttributes', ], 'DeviceTypeLinux' => [ 'shape' => 'DefaultImportClientBrandingAttributes', ], 'DeviceTypeWeb' => [ 'shape' => 'DefaultImportClientBrandingAttributes', ], ], ], 'ImportClientBrandingResult' => [ 'type' => 'structure', 'members' => [ 'DeviceTypeWindows' => [ 'shape' => 'DefaultClientBrandingAttributes', ], 'DeviceTypeOsx' => [ 'shape' => 'DefaultClientBrandingAttributes', ], 'DeviceTypeAndroid' => [ 'shape' => 'DefaultClientBrandingAttributes', ], 'DeviceTypeIos' => [ 'shape' => 'IosClientBrandingAttributes', ], 'DeviceTypeLinux' => [ 'shape' => 'DefaultClientBrandingAttributes', ], 'DeviceTypeWeb' => [ 'shape' => 'DefaultClientBrandingAttributes', ], ], ], 'ImportWorkspaceImageRequest' => [ 'type' => 'structure', 'required' => [ 'Ec2ImageId', 'IngestionProcess', 'ImageName', 'ImageDescription', ], 'members' => [ 'Ec2ImageId' => [ 'shape' => 'Ec2ImageId', ], 'IngestionProcess' => [ 'shape' => 'WorkspaceImageIngestionProcess', ], 'ImageName' => [ 'shape' => 'WorkspaceImageName', ], 'ImageDescription' => [ 'shape' => 'WorkspaceImageDescription', ], 'Tags' => [ 'shape' => 'TagList', ], 'Applications' => [ 'shape' => 'ApplicationList', ], ], ], 'ImportWorkspaceImageResult' => [ 'type' => 'structure', 'members' => [ 'ImageId' => [ 'shape' => 'WorkspaceImageId', ], ], ], 'IncompatibleApplicationsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'InternetFallbackProtocol' => [ 'type' => 'string', 'enum' => [ 'PCOIP', ], ], 'InternetFallbackProtocolList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InternetFallbackProtocol', ], ], 'InvalidParameterCombinationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'InvalidParameterValuesException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'InvalidResourceStateException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'Ios2XLogo' => [ 'type' => 'blob', 'max' => 1770000, 'min' => 1, ], 'Ios3XLogo' => [ 'type' => 'blob', 'max' => 1770000, 'min' => 1, ], 'IosClientBrandingAttributes' => [ 'type' => 'structure', 'members' => [ 'LogoUrl' => [ 'shape' => 'ClientUrl', ], 'Logo2xUrl' => [ 'shape' => 'ClientUrl', ], 'Logo3xUrl' => [ 'shape' => 'ClientUrl', ], 'SupportEmail' => [ 'shape' => 'ClientEmail', ], 'SupportLink' => [ 'shape' => 'ClientUrl', ], 'ForgotPasswordLink' => [ 'shape' => 'ClientUrl', ], 'LoginMessage' => [ 'shape' => 'LoginMessage', ], ], ], 'IosImportClientBrandingAttributes' => [ 'type' => 'structure', 'members' => [ 'Logo' => [ 'shape' => 'IosLogo', ], 'Logo2x' => [ 'shape' => 'Ios2XLogo', ], 'Logo3x' => [ 'shape' => 'Ios3XLogo', ], 'SupportEmail' => [ 'shape' => 'ClientEmail', ], 'SupportLink' => [ 'shape' => 'ClientUrl', ], 'ForgotPasswordLink' => [ 'shape' => 'ClientUrl', ], 'LoginMessage' => [ 'shape' => 'LoginMessage', ], ], ], 'IosLogo' => [ 'type' => 'blob', 'max' => 447000, 'min' => 1, ], 'IpAddress' => [ 'type' => 'string', ], 'IpGroupDesc' => [ 'type' => 'string', ], 'IpGroupId' => [ 'type' => 'string', 'pattern' => 'wsipg-[0-9a-z]{8,63}$', ], 'IpGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpGroupId', ], ], 'IpGroupName' => [ 'type' => 'string', ], 'IpRevokedRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpRule', ], ], 'IpRule' => [ 'type' => 'string', ], 'IpRuleDesc' => [ 'type' => 'string', ], 'IpRuleItem' => [ 'type' => 'structure', 'members' => [ 'ipRule' => [ 'shape' => 'IpRule', ], 'ruleDesc' => [ 'shape' => 'IpRuleDesc', ], ], ], 'IpRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpRuleItem', ], ], 'Limit' => [ 'type' => 'integer', 'max' => 25, 'min' => 1, ], 'Limit50' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'LinkId' => [ 'type' => 'string', 'pattern' => '^link-.{8,24}$', ], 'LinkStatusFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountLinkStatusEnum', ], ], 'ListAccountLinksRequest' => [ 'type' => 'structure', 'members' => [ 'LinkStatusFilter' => [ 'shape' => 'LinkStatusFilterList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'Limit', ], ], ], 'ListAccountLinksResult' => [ 'type' => 'structure', 'members' => [ 'AccountLinks' => [ 'shape' => 'AccountLinkList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListAvailableManagementCidrRangesRequest' => [ 'type' => 'structure', 'required' => [ 'ManagementCidrRangeConstraint', ], 'members' => [ 'ManagementCidrRangeConstraint' => [ 'shape' => 'ManagementCidrRangeConstraint', ], 'MaxResults' => [ 'shape' => 'ManagementCidrRangeMaxResults', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListAvailableManagementCidrRangesResult' => [ 'type' => 'structure', 'members' => [ 'ManagementCidrRanges' => [ 'shape' => 'DedicatedTenancyCidrRangeList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'LogUploadEnum' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'LoginMessage' => [ 'type' => 'map', 'key' => [ 'shape' => 'ClientLocale', ], 'value' => [ 'shape' => 'ClientLoginMessage', ], ], 'ManagementCidrRangeConstraint' => [ 'type' => 'string', 'pattern' => '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\\/(3[0-2]|[1-2][0-9]|[0-9]))$', ], 'ManagementCidrRangeMaxResults' => [ 'type' => 'integer', 'max' => 5, 'min' => 1, ], 'MaxUserDurationInSeconds' => [ 'type' => 'integer', 'max' => 432000, 'min' => 600, ], 'MaximumLength' => [ 'type' => 'integer', 'min' => 0, ], 'MicrosoftEntraConfig' => [ 'type' => 'structure', 'members' => [ 'TenantId' => [ 'shape' => 'MicrosoftEntraConfigTenantId', ], 'ApplicationConfigSecretArn' => [ 'shape' => 'SecretsManagerArn', ], ], ], 'MicrosoftEntraConfigTenantId' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9-]{1,100}$', ], 'MigrateWorkspaceRequest' => [ 'type' => 'structure', 'required' => [ 'SourceWorkspaceId', 'BundleId', ], 'members' => [ 'SourceWorkspaceId' => [ 'shape' => 'WorkspaceId', ], 'BundleId' => [ 'shape' => 'BundleId', ], ], ], 'MigrateWorkspaceResult' => [ 'type' => 'structure', 'members' => [ 'SourceWorkspaceId' => [ 'shape' => 'WorkspaceId', ], 'TargetWorkspaceId' => [ 'shape' => 'WorkspaceId', ], ], ], 'ModificationResourceEnum' => [ 'type' => 'string', 'enum' => [ 'ROOT_VOLUME', 'USER_VOLUME', 'COMPUTE_TYPE', ], ], 'ModificationState' => [ 'type' => 'structure', 'members' => [ 'Resource' => [ 'shape' => 'ModificationResourceEnum', ], 'State' => [ 'shape' => 'ModificationStateEnum', ], ], ], 'ModificationStateEnum' => [ 'type' => 'string', 'enum' => [ 'UPDATE_INITIATED', 'UPDATE_IN_PROGRESS', ], ], 'ModificationStateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModificationState', ], ], 'ModifyAccountRequest' => [ 'type' => 'structure', 'members' => [ 'DedicatedTenancySupport' => [ 'shape' => 'DedicatedTenancySupportEnum', ], 'DedicatedTenancyManagementCidrRange' => [ 'shape' => 'DedicatedTenancyManagementCidrRange', ], ], ], 'ModifyAccountResult' => [ 'type' => 'structure', 'members' => [], ], 'ModifyCertificateBasedAuthPropertiesRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', ], 'members' => [ 'ResourceId' => [ 'shape' => 'DirectoryId', ], 'CertificateBasedAuthProperties' => [ 'shape' => 'CertificateBasedAuthProperties', ], 'PropertiesToDelete' => [ 'shape' => 'DeletableCertificateBasedAuthPropertiesList', ], ], ], 'ModifyCertificateBasedAuthPropertiesResult' => [ 'type' => 'structure', 'members' => [], ], 'ModifyClientPropertiesRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'ClientProperties', ], 'members' => [ 'ResourceId' => [ 'shape' => 'NonEmptyString', ], 'ClientProperties' => [ 'shape' => 'ClientProperties', ], ], ], 'ModifyClientPropertiesResult' => [ 'type' => 'structure', 'members' => [], ], 'ModifyEndpointEncryptionModeRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'EndpointEncryptionMode', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'EndpointEncryptionMode' => [ 'shape' => 'EndpointEncryptionMode', ], ], ], 'ModifyEndpointEncryptionModeResponse' => [ 'type' => 'structure', 'members' => [], ], 'ModifySamlPropertiesRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', ], 'members' => [ 'ResourceId' => [ 'shape' => 'DirectoryId', ], 'SamlProperties' => [ 'shape' => 'SamlProperties', ], 'PropertiesToDelete' => [ 'shape' => 'DeletableSamlPropertiesList', ], ], ], 'ModifySamlPropertiesResult' => [ 'type' => 'structure', 'members' => [], ], 'ModifySelfservicePermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'SelfservicePermissions', ], 'members' => [ 'ResourceId' => [ 'shape' => 'DirectoryId', ], 'SelfservicePermissions' => [ 'shape' => 'SelfservicePermissions', ], ], ], 'ModifySelfservicePermissionsResult' => [ 'type' => 'structure', 'members' => [], ], 'ModifyStreamingPropertiesRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', ], 'members' => [ 'ResourceId' => [ 'shape' => 'DirectoryId', ], 'StreamingProperties' => [ 'shape' => 'StreamingProperties', ], ], ], 'ModifyStreamingPropertiesResult' => [ 'type' => 'structure', 'members' => [], ], 'ModifyWorkspaceAccessPropertiesRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'WorkspaceAccessProperties', ], 'members' => [ 'ResourceId' => [ 'shape' => 'DirectoryId', ], 'WorkspaceAccessProperties' => [ 'shape' => 'WorkspaceAccessProperties', ], ], ], 'ModifyWorkspaceAccessPropertiesResult' => [ 'type' => 'structure', 'members' => [], ], 'ModifyWorkspaceCreationPropertiesRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'WorkspaceCreationProperties', ], 'members' => [ 'ResourceId' => [ 'shape' => 'DirectoryId', ], 'WorkspaceCreationProperties' => [ 'shape' => 'WorkspaceCreationProperties', ], ], ], 'ModifyWorkspaceCreationPropertiesResult' => [ 'type' => 'structure', 'members' => [], ], 'ModifyWorkspacePropertiesRequest' => [ 'type' => 'structure', 'required' => [ 'WorkspaceId', ], 'members' => [ 'WorkspaceId' => [ 'shape' => 'WorkspaceId', ], 'WorkspaceProperties' => [ 'shape' => 'WorkspaceProperties', ], 'DataReplication' => [ 'shape' => 'DataReplication', ], ], ], 'ModifyWorkspacePropertiesResult' => [ 'type' => 'structure', 'members' => [], ], 'ModifyWorkspaceStateRequest' => [ 'type' => 'structure', 'required' => [ 'WorkspaceId', 'WorkspaceState', ], 'members' => [ 'WorkspaceId' => [ 'shape' => 'WorkspaceId', ], 'WorkspaceState' => [ 'shape' => 'TargetWorkspaceState', ], ], ], 'ModifyWorkspaceStateResult' => [ 'type' => 'structure', 'members' => [], ], 'NetworkAccessConfiguration' => [ 'type' => 'structure', 'members' => [ 'EniPrivateIpAddress' => [ 'shape' => 'NonEmptyString', ], 'EniId' => [ 'shape' => 'NonEmptyString', ], ], ], 'NonEmptyString' => [ 'type' => 'string', 'min' => 1, ], 'OperatingSystem' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'OperatingSystemType', ], ], ], 'OperatingSystemName' => [ 'type' => 'string', 'enum' => [ 'AMAZON_LINUX_2', 'UBUNTU_18_04', 'UBUNTU_20_04', 'UBUNTU_22_04', 'UNKNOWN', 'WINDOWS_10', 'WINDOWS_11', 'WINDOWS_7', 'WINDOWS_SERVER_2016', 'WINDOWS_SERVER_2019', 'WINDOWS_SERVER_2022', 'RHEL_8', 'ROCKY_8', ], ], 'OperatingSystemNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OperatingSystemName', ], ], 'OperatingSystemNotCompatibleException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'OperatingSystemType' => [ 'type' => 'string', 'enum' => [ 'WINDOWS', 'LINUX', ], ], 'OperationInProgressException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'OperationNotSupportedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'reason' => [ 'shape' => 'ExceptionErrorCode', ], ], 'exception' => true, ], 'PaginationToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'PendingCreateStandbyWorkspacesRequest' => [ 'type' => 'structure', 'members' => [ 'UserName' => [ 'shape' => 'UserName', ], 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'State' => [ 'shape' => 'WorkspaceState', ], 'WorkspaceId' => [ 'shape' => 'WorkspaceId', ], ], ], 'PendingCreateStandbyWorkspacesRequestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PendingCreateStandbyWorkspacesRequest', ], ], 'PoolsRunningMode' => [ 'type' => 'string', 'enum' => [ 'AUTO_STOP', 'ALWAYS_ON', ], ], 'Protocol' => [ 'type' => 'string', 'enum' => [ 'PCOIP', 'WSP', ], ], 'ProtocolList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Protocol', ], ], 'RebootRequest' => [ 'type' => 'structure', 'required' => [ 'WorkspaceId', ], 'members' => [ 'WorkspaceId' => [ 'shape' => 'WorkspaceId', ], ], ], 'RebootWorkspaceRequests' => [ 'type' => 'list', 'member' => [ 'shape' => 'RebootRequest', ], 'max' => 25, 'min' => 1, ], 'RebootWorkspacesRequest' => [ 'type' => 'structure', 'required' => [ 'RebootWorkspaceRequests', ], 'members' => [ 'RebootWorkspaceRequests' => [ 'shape' => 'RebootWorkspaceRequests', ], ], ], 'RebootWorkspacesResult' => [ 'type' => 'structure', 'members' => [ 'FailedRequests' => [ 'shape' => 'FailedRebootWorkspaceRequests', ], ], ], 'RebuildRequest' => [ 'type' => 'structure', 'required' => [ 'WorkspaceId', ], 'members' => [ 'WorkspaceId' => [ 'shape' => 'WorkspaceId', ], ], ], 'RebuildWorkspaceRequests' => [ 'type' => 'list', 'member' => [ 'shape' => 'RebuildRequest', ], 'max' => 1, 'min' => 1, ], 'RebuildWorkspacesRequest' => [ 'type' => 'structure', 'required' => [ 'RebuildWorkspaceRequests', ], 'members' => [ 'RebuildWorkspaceRequests' => [ 'shape' => 'RebuildWorkspaceRequests', ], ], ], 'RebuildWorkspacesResult' => [ 'type' => 'structure', 'members' => [ 'FailedRequests' => [ 'shape' => 'FailedRebuildWorkspaceRequests', ], ], ], 'ReconnectEnum' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'Region' => [ 'type' => 'string', 'max' => 31, 'min' => 1, 'pattern' => '^[-0-9a-z]{1,31}$', ], 'RegisterWorkspaceDirectoryRequest' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'SubnetIds' => [ 'shape' => 'SubnetIds', ], 'EnableSelfService' => [ 'shape' => 'BooleanObject', ], 'Tenancy' => [ 'shape' => 'Tenancy', ], 'Tags' => [ 'shape' => 'TagList', ], 'WorkspaceDirectoryName' => [ 'shape' => 'WorkspaceDirectoryName', ], 'WorkspaceDirectoryDescription' => [ 'shape' => 'WorkspaceDirectoryDescription', ], 'UserIdentityType' => [ 'shape' => 'UserIdentityType', ], 'IdcInstanceArn' => [ 'shape' => 'ARN', ], 'MicrosoftEntraConfig' => [ 'shape' => 'MicrosoftEntraConfig', ], 'WorkspaceType' => [ 'shape' => 'WorkspaceType', ], 'ActiveDirectoryConfig' => [ 'shape' => 'ActiveDirectoryConfig', ], ], ], 'RegisterWorkspaceDirectoryResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'State' => [ 'shape' => 'WorkspaceDirectoryState', ], ], ], 'RegistrationCode' => [ 'type' => 'string', 'max' => 20, 'min' => 1, ], 'RejectAccountLinkInvitationRequest' => [ 'type' => 'structure', 'required' => [ 'LinkId', ], 'members' => [ 'LinkId' => [ 'shape' => 'LinkId', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], ], ], 'RejectAccountLinkInvitationResult' => [ 'type' => 'structure', 'members' => [ 'AccountLink' => [ 'shape' => 'AccountLink', ], ], ], 'RelatedWorkspaceProperties' => [ 'type' => 'structure', 'members' => [ 'WorkspaceId' => [ 'shape' => 'WorkspaceId', ], 'Region' => [ 'shape' => 'Region', ], 'State' => [ 'shape' => 'WorkspaceState', ], 'Type' => [ 'shape' => 'StandbyWorkspaceRelationshipType', ], ], ], 'RelatedWorkspaces' => [ 'type' => 'list', 'member' => [ 'shape' => 'RelatedWorkspaceProperties', ], ], 'ResourceAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ResourceAssociatedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ResourceCreationFailedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ResourceIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 25, 'min' => 1, ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'ResourceId' => [ 'shape' => 'NonEmptyString', ], ], 'exception' => true, ], 'ResourceLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'ResourceId' => [ 'shape' => 'NonEmptyString', ], ], 'exception' => true, ], 'ResourceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'ResourceId' => [ 'shape' => 'NonEmptyString', ], ], 'exception' => true, ], 'RestoreWorkspaceRequest' => [ 'type' => 'structure', 'required' => [ 'WorkspaceId', ], 'members' => [ 'WorkspaceId' => [ 'shape' => 'WorkspaceId', ], ], ], 'RestoreWorkspaceResult' => [ 'type' => 'structure', 'members' => [], ], 'RevokeIpRulesRequest' => [ 'type' => 'structure', 'required' => [ 'GroupId', 'UserRules', ], 'members' => [ 'GroupId' => [ 'shape' => 'IpGroupId', ], 'UserRules' => [ 'shape' => 'IpRevokedRuleList', ], ], ], 'RevokeIpRulesResult' => [ 'type' => 'structure', 'members' => [], ], 'RootStorage' => [ 'type' => 'structure', 'required' => [ 'Capacity', ], 'members' => [ 'Capacity' => [ 'shape' => 'NonEmptyString', ], ], ], 'RootVolumeSizeGib' => [ 'type' => 'integer', ], 'RunningMode' => [ 'type' => 'string', 'enum' => [ 'AUTO_STOP', 'ALWAYS_ON', 'MANUAL', ], ], 'RunningModeAutoStopTimeoutInMinutes' => [ 'type' => 'integer', ], 'S3BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9]$', ], 'SamlProperties' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'SamlStatusEnum', ], 'UserAccessUrl' => [ 'shape' => 'SamlUserAccessUrl', ], 'RelayStateParameterName' => [ 'shape' => 'NonEmptyString', ], ], ], 'SamlStatusEnum' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', 'ENABLED_WITH_DIRECTORY_LOGIN_FALLBACK', ], ], 'SamlUserAccessUrl' => [ 'type' => 'string', 'max' => 200, 'min' => 8, 'pattern' => '^(http|https)\\://\\S+$', ], 'SecretsManagerArn' => [ 'type' => 'string', 'pattern' => '^arn:aws[a-z-]{0,7}:secretsmanager:[A-za-z0-9_/.-]{0,63}:[A-za-z0-9_/.-]{0,63}:secret:[A-Za-z0-9][A-za-z0-9_/.-]{8,519}$', ], 'SecurityGroupId' => [ 'type' => 'string', 'max' => 20, 'min' => 11, 'pattern' => '^(sg-([0-9a-f]{8}|[0-9a-f]{17}))$', ], 'SelfservicePermissions' => [ 'type' => 'structure', 'members' => [ 'RestartWorkspace' => [ 'shape' => 'ReconnectEnum', ], 'IncreaseVolumeSize' => [ 'shape' => 'ReconnectEnum', ], 'ChangeComputeType' => [ 'shape' => 'ReconnectEnum', ], 'SwitchRunningMode' => [ 'shape' => 'ReconnectEnum', ], 'RebuildWorkspace' => [ 'shape' => 'ReconnectEnum', ], ], ], 'SessionConnectionState' => [ 'type' => 'string', 'enum' => [ 'CONNECTED', 'NOT_CONNECTED', ], ], 'SessionInstanceId' => [ 'type' => 'string', 'pattern' => '^i-[a-f0-9]{8}(?:[a-f0-9]{9})?$', ], 'SettingsGroup' => [ 'type' => 'string', 'max' => 100, 'pattern' => '^[A-Za-z0-9_./()!*\'-]+$', ], 'Snapshot' => [ 'type' => 'structure', 'members' => [ 'SnapshotTime' => [ 'shape' => 'Timestamp', ], ], ], 'SnapshotList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Snapshot', ], ], 'StandbyWorkspace' => [ 'type' => 'structure', 'required' => [ 'PrimaryWorkspaceId', 'DirectoryId', ], 'members' => [ 'PrimaryWorkspaceId' => [ 'shape' => 'WorkspaceId', ], 'VolumeEncryptionKey' => [ 'shape' => 'VolumeEncryptionKey', ], 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Tags' => [ 'shape' => 'TagList', ], 'DataReplication' => [ 'shape' => 'DataReplication', ], ], ], 'StandbyWorkspaceRelationshipType' => [ 'type' => 'string', 'enum' => [ 'PRIMARY', 'STANDBY', ], ], 'StandbyWorkspacesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StandbyWorkspace', ], ], 'StandbyWorkspacesProperties' => [ 'type' => 'structure', 'members' => [ 'StandbyWorkspaceId' => [ 'shape' => 'WorkspaceId', ], 'DataReplication' => [ 'shape' => 'DataReplication', ], 'RecoverySnapshotTime' => [ 'shape' => 'Timestamp', ], ], ], 'StandbyWorkspacesPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StandbyWorkspacesProperties', ], ], 'StartRequest' => [ 'type' => 'structure', 'members' => [ 'WorkspaceId' => [ 'shape' => 'WorkspaceId', ], ], ], 'StartWorkspaceRequests' => [ 'type' => 'list', 'member' => [ 'shape' => 'StartRequest', ], 'max' => 25, 'min' => 1, ], 'StartWorkspacesPoolRequest' => [ 'type' => 'structure', 'required' => [ 'PoolId', ], 'members' => [ 'PoolId' => [ 'shape' => 'WorkspacesPoolId', ], ], ], 'StartWorkspacesPoolResult' => [ 'type' => 'structure', 'members' => [], ], 'StartWorkspacesRequest' => [ 'type' => 'structure', 'required' => [ 'StartWorkspaceRequests', ], 'members' => [ 'StartWorkspaceRequests' => [ 'shape' => 'StartWorkspaceRequests', ], ], ], 'StartWorkspacesResult' => [ 'type' => 'structure', 'members' => [ 'FailedRequests' => [ 'shape' => 'FailedStartWorkspaceRequests', ], ], ], 'StopRequest' => [ 'type' => 'structure', 'members' => [ 'WorkspaceId' => [ 'shape' => 'WorkspaceId', ], ], ], 'StopWorkspaceRequests' => [ 'type' => 'list', 'member' => [ 'shape' => 'StopRequest', ], 'max' => 25, 'min' => 1, ], 'StopWorkspacesPoolRequest' => [ 'type' => 'structure', 'required' => [ 'PoolId', ], 'members' => [ 'PoolId' => [ 'shape' => 'WorkspacesPoolId', ], ], ], 'StopWorkspacesPoolResult' => [ 'type' => 'structure', 'members' => [], ], 'StopWorkspacesRequest' => [ 'type' => 'structure', 'required' => [ 'StopWorkspaceRequests', ], 'members' => [ 'StopWorkspaceRequests' => [ 'shape' => 'StopWorkspaceRequests', ], ], ], 'StopWorkspacesResult' => [ 'type' => 'structure', 'members' => [ 'FailedRequests' => [ 'shape' => 'FailedStopWorkspaceRequests', ], ], ], 'StorageConnector' => [ 'type' => 'structure', 'required' => [ 'ConnectorType', 'Status', ], 'members' => [ 'ConnectorType' => [ 'shape' => 'StorageConnectorTypeEnum', ], 'Status' => [ 'shape' => 'StorageConnectorStatusEnum', ], ], ], 'StorageConnectorStatusEnum' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'StorageConnectorTypeEnum' => [ 'type' => 'string', 'enum' => [ 'HOME_FOLDER', ], ], 'StorageConnectors' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorageConnector', ], 'min' => 1, ], 'StreamingExperiencePreferredProtocolEnum' => [ 'type' => 'string', 'enum' => [ 'TCP', 'UDP', ], ], 'StreamingProperties' => [ 'type' => 'structure', 'members' => [ 'StreamingExperiencePreferredProtocol' => [ 'shape' => 'StreamingExperiencePreferredProtocolEnum', ], 'UserSettings' => [ 'shape' => 'UserSettings', ], 'StorageConnectors' => [ 'shape' => 'StorageConnectors', ], 'GlobalAccelerator' => [ 'shape' => 'GlobalAcceleratorForDirectory', ], ], ], 'String2048' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'SubnetId' => [ 'type' => 'string', 'max' => 24, 'min' => 15, 'pattern' => '^(subnet-([0-9a-f]{8}|[0-9a-f]{17}))$', ], 'SubnetIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], 'max' => 2, ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 127, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TagValue' => [ 'type' => 'string', 'max' => 255, ], 'TargetWorkspaceState' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'ADMIN_MAINTENANCE', ], ], 'Tenancy' => [ 'type' => 'string', 'enum' => [ 'DEDICATED', 'SHARED', ], ], 'TerminateRequest' => [ 'type' => 'structure', 'required' => [ 'WorkspaceId', ], 'members' => [ 'WorkspaceId' => [ 'shape' => 'WorkspaceId', ], ], ], 'TerminateWorkspaceRequests' => [ 'type' => 'list', 'member' => [ 'shape' => 'TerminateRequest', ], 'max' => 25, 'min' => 1, ], 'TerminateWorkspacesPoolRequest' => [ 'type' => 'structure', 'required' => [ 'PoolId', ], 'members' => [ 'PoolId' => [ 'shape' => 'WorkspacesPoolId', ], ], ], 'TerminateWorkspacesPoolResult' => [ 'type' => 'structure', 'members' => [], ], 'TerminateWorkspacesPoolSessionRequest' => [ 'type' => 'structure', 'required' => [ 'SessionId', ], 'members' => [ 'SessionId' => [ 'shape' => 'AmazonUuid', ], ], ], 'TerminateWorkspacesPoolSessionResult' => [ 'type' => 'structure', 'members' => [], ], 'TerminateWorkspacesRequest' => [ 'type' => 'structure', 'required' => [ 'TerminateWorkspaceRequests', ], 'members' => [ 'TerminateWorkspaceRequests' => [ 'shape' => 'TerminateWorkspaceRequests', ], ], ], 'TerminateWorkspacesResult' => [ 'type' => 'structure', 'members' => [ 'FailedRequests' => [ 'shape' => 'FailedTerminateWorkspaceRequests', ], ], ], 'TimeoutSettings' => [ 'type' => 'structure', 'members' => [ 'DisconnectTimeoutInSeconds' => [ 'shape' => 'DisconnectTimeoutInSeconds', ], 'IdleDisconnectTimeoutInSeconds' => [ 'shape' => 'IdleDisconnectTimeoutInSeconds', ], 'MaxUserDurationInSeconds' => [ 'shape' => 'MaxUserDurationInSeconds', ], ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UnsupportedNetworkConfigurationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'UnsupportedWorkspaceConfigurationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'UpdateConnectClientAddInRequest' => [ 'type' => 'structure', 'required' => [ 'AddInId', 'ResourceId', ], 'members' => [ 'AddInId' => [ 'shape' => 'AmazonUuid', ], 'ResourceId' => [ 'shape' => 'DirectoryId', ], 'Name' => [ 'shape' => 'AddInName', ], 'URL' => [ 'shape' => 'AddInUrl', ], ], ], 'UpdateConnectClientAddInResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateConnectionAliasPermissionRequest' => [ 'type' => 'structure', 'required' => [ 'AliasId', 'ConnectionAliasPermission', ], 'members' => [ 'AliasId' => [ 'shape' => 'ConnectionAliasId', ], 'ConnectionAliasPermission' => [ 'shape' => 'ConnectionAliasPermission', ], ], ], 'UpdateConnectionAliasPermissionResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_./() -]+$', ], 'UpdateResult' => [ 'type' => 'structure', 'members' => [ 'UpdateAvailable' => [ 'shape' => 'BooleanObject', ], 'Description' => [ 'shape' => 'UpdateDescription', ], ], ], 'UpdateRulesOfIpGroupRequest' => [ 'type' => 'structure', 'required' => [ 'GroupId', 'UserRules', ], 'members' => [ 'GroupId' => [ 'shape' => 'IpGroupId', ], 'UserRules' => [ 'shape' => 'IpRuleList', ], ], ], 'UpdateRulesOfIpGroupResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateWorkspaceBundleRequest' => [ 'type' => 'structure', 'members' => [ 'BundleId' => [ 'shape' => 'BundleId', ], 'ImageId' => [ 'shape' => 'WorkspaceImageId', ], ], ], 'UpdateWorkspaceBundleResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateWorkspaceImagePermissionRequest' => [ 'type' => 'structure', 'required' => [ 'ImageId', 'AllowCopyImage', 'SharedAccountId', ], 'members' => [ 'ImageId' => [ 'shape' => 'WorkspaceImageId', ], 'AllowCopyImage' => [ 'shape' => 'BooleanObject', ], 'SharedAccountId' => [ 'shape' => 'AwsAccount', ], ], ], 'UpdateWorkspaceImagePermissionResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateWorkspacesPoolRequest' => [ 'type' => 'structure', 'required' => [ 'PoolId', ], 'members' => [ 'PoolId' => [ 'shape' => 'WorkspacesPoolId', ], 'Description' => [ 'shape' => 'UpdateDescription', ], 'BundleId' => [ 'shape' => 'BundleId', ], 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Capacity' => [ 'shape' => 'Capacity', ], 'ApplicationSettings' => [ 'shape' => 'ApplicationSettingsRequest', ], 'TimeoutSettings' => [ 'shape' => 'TimeoutSettings', ], 'RunningMode' => [ 'shape' => 'PoolsRunningMode', ], ], ], 'UpdateWorkspacesPoolResult' => [ 'type' => 'structure', 'members' => [ 'WorkspacesPool' => [ 'shape' => 'WorkspacesPool', ], ], ], 'UserIdentityType' => [ 'type' => 'string', 'enum' => [ 'CUSTOMER_MANAGED', 'AWS_DIRECTORY_SERVICE', 'AWS_IAM_IDENTITY_CENTER', ], ], 'UserName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, ], 'UserSetting' => [ 'type' => 'structure', 'required' => [ 'Action', 'Permission', ], 'members' => [ 'Action' => [ 'shape' => 'UserSettingActionEnum', ], 'Permission' => [ 'shape' => 'UserSettingPermissionEnum', ], 'MaximumLength' => [ 'shape' => 'MaximumLength', ], ], ], 'UserSettingActionEnum' => [ 'type' => 'string', 'enum' => [ 'CLIPBOARD_COPY_FROM_LOCAL_DEVICE', 'CLIPBOARD_COPY_TO_LOCAL_DEVICE', 'PRINTING_TO_LOCAL_DEVICE', 'SMART_CARD', ], ], 'UserSettingPermissionEnum' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'UserSettings' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserSetting', ], 'min' => 1, ], 'UserStorage' => [ 'type' => 'structure', 'required' => [ 'Capacity', ], 'members' => [ 'Capacity' => [ 'shape' => 'NonEmptyString', ], ], ], 'UserVolumeSizeGib' => [ 'type' => 'integer', ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'VolumeEncryptionKey' => [ 'type' => 'string', ], 'WorkSpaceApplication' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => 'WorkSpaceApplicationId', ], 'Created' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'String2048', ], 'LicenseType' => [ 'shape' => 'WorkSpaceApplicationLicenseType', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Owner' => [ 'shape' => 'WorkSpaceApplicationOwner', ], 'State' => [ 'shape' => 'WorkSpaceApplicationState', ], 'SupportedComputeTypeNames' => [ 'shape' => 'ComputeList', ], 'SupportedOperatingSystemNames' => [ 'shape' => 'OperatingSystemNameList', ], ], ], 'WorkSpaceApplicationDeployment' => [ 'type' => 'structure', 'members' => [ 'Associations' => [ 'shape' => 'WorkspaceResourceAssociationList', ], ], ], 'WorkSpaceApplicationId' => [ 'type' => 'string', 'pattern' => '^wsa-[0-9a-z]{8,63}$', ], 'WorkSpaceApplicationIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkSpaceApplicationId', ], 'max' => 25, 'min' => 1, ], 'WorkSpaceApplicationLicenseType' => [ 'type' => 'string', 'enum' => [ 'LICENSED', 'UNLICENSED', ], ], 'WorkSpaceApplicationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkSpaceApplication', ], ], 'WorkSpaceApplicationOwner' => [ 'type' => 'string', 'pattern' => '^\\d{12}|AMAZON$', ], 'WorkSpaceApplicationState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'ERROR', 'AVAILABLE', 'UNINSTALL_ONLY', ], ], 'WorkSpaceAssociatedResourceType' => [ 'type' => 'string', 'enum' => [ 'APPLICATION', ], ], 'WorkSpaceAssociatedResourceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkSpaceAssociatedResourceType', ], ], 'Workspace' => [ 'type' => 'structure', 'members' => [ 'WorkspaceId' => [ 'shape' => 'WorkspaceId', ], 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'UserName' => [ 'shape' => 'UserName', ], 'IpAddress' => [ 'shape' => 'IpAddress', ], 'State' => [ 'shape' => 'WorkspaceState', ], 'BundleId' => [ 'shape' => 'BundleId', ], 'SubnetId' => [ 'shape' => 'SubnetId', ], 'ErrorMessage' => [ 'shape' => 'Description', ], 'ErrorCode' => [ 'shape' => 'WorkspaceErrorCode', ], 'ComputerName' => [ 'shape' => 'ComputerName', ], 'VolumeEncryptionKey' => [ 'shape' => 'VolumeEncryptionKey', ], 'UserVolumeEncryptionEnabled' => [ 'shape' => 'BooleanObject', ], 'RootVolumeEncryptionEnabled' => [ 'shape' => 'BooleanObject', ], 'WorkspaceName' => [ 'shape' => 'WorkspaceName', ], 'WorkspaceProperties' => [ 'shape' => 'WorkspaceProperties', ], 'ModificationStates' => [ 'shape' => 'ModificationStateList', ], 'RelatedWorkspaces' => [ 'shape' => 'RelatedWorkspaces', ], 'DataReplicationSettings' => [ 'shape' => 'DataReplicationSettings', ], 'StandbyWorkspacesProperties' => [ 'shape' => 'StandbyWorkspacesPropertiesList', ], ], ], 'WorkspaceAccessProperties' => [ 'type' => 'structure', 'members' => [ 'DeviceTypeWindows' => [ 'shape' => 'AccessPropertyValue', ], 'DeviceTypeOsx' => [ 'shape' => 'AccessPropertyValue', ], 'DeviceTypeWeb' => [ 'shape' => 'AccessPropertyValue', ], 'DeviceTypeIos' => [ 'shape' => 'AccessPropertyValue', ], 'DeviceTypeAndroid' => [ 'shape' => 'AccessPropertyValue', ], 'DeviceTypeChromeOs' => [ 'shape' => 'AccessPropertyValue', ], 'DeviceTypeZeroClient' => [ 'shape' => 'AccessPropertyValue', ], 'DeviceTypeLinux' => [ 'shape' => 'AccessPropertyValue', ], 'DeviceTypeWorkSpacesThinClient' => [ 'shape' => 'AccessPropertyValue', ], 'AccessEndpointConfig' => [ 'shape' => 'AccessEndpointConfig', ], ], ], 'WorkspaceBundle' => [ 'type' => 'structure', 'members' => [ 'BundleId' => [ 'shape' => 'BundleId', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Owner' => [ 'shape' => 'BundleOwner', ], 'Description' => [ 'shape' => 'Description', ], 'ImageId' => [ 'shape' => 'WorkspaceImageId', ], 'RootStorage' => [ 'shape' => 'RootStorage', ], 'UserStorage' => [ 'shape' => 'UserStorage', ], 'ComputeType' => [ 'shape' => 'ComputeType', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'State' => [ 'shape' => 'WorkspaceBundleState', ], 'BundleType' => [ 'shape' => 'BundleType', ], ], ], 'WorkspaceBundleDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_./() -]+$', ], 'WorkspaceBundleName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_./()\\\\-]+$', ], 'WorkspaceBundleState' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'PENDING', 'ERROR', ], ], 'WorkspaceConnectionStatus' => [ 'type' => 'structure', 'members' => [ 'WorkspaceId' => [ 'shape' => 'WorkspaceId', ], 'ConnectionState' => [ 'shape' => 'ConnectionState', ], 'ConnectionStateCheckTimestamp' => [ 'shape' => 'Timestamp', ], 'LastKnownUserConnectionTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'WorkspaceConnectionStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkspaceConnectionStatus', ], ], 'WorkspaceCreationProperties' => [ 'type' => 'structure', 'members' => [ 'EnableInternetAccess' => [ 'shape' => 'BooleanObject', ], 'DefaultOu' => [ 'shape' => 'DefaultOu', ], 'CustomSecurityGroupId' => [ 'shape' => 'SecurityGroupId', ], 'UserEnabledAsLocalAdministrator' => [ 'shape' => 'BooleanObject', ], 'EnableMaintenanceMode' => [ 'shape' => 'BooleanObject', ], 'InstanceIamRoleArn' => [ 'shape' => 'ARN', ], ], ], 'WorkspaceDirectory' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Alias' => [ 'shape' => 'Alias', ], 'DirectoryName' => [ 'shape' => 'DirectoryName', ], 'RegistrationCode' => [ 'shape' => 'RegistrationCode', ], 'SubnetIds' => [ 'shape' => 'SubnetIds', ], 'DnsIpAddresses' => [ 'shape' => 'DnsIpAddresses', ], 'CustomerUserName' => [ 'shape' => 'UserName', ], 'IamRoleId' => [ 'shape' => 'ARN', ], 'DirectoryType' => [ 'shape' => 'WorkspaceDirectoryType', ], 'WorkspaceSecurityGroupId' => [ 'shape' => 'SecurityGroupId', ], 'State' => [ 'shape' => 'WorkspaceDirectoryState', ], 'WorkspaceCreationProperties' => [ 'shape' => 'DefaultWorkspaceCreationProperties', ], 'ipGroupIds' => [ 'shape' => 'IpGroupIdList', ], 'WorkspaceAccessProperties' => [ 'shape' => 'WorkspaceAccessProperties', ], 'Tenancy' => [ 'shape' => 'Tenancy', ], 'SelfservicePermissions' => [ 'shape' => 'SelfservicePermissions', ], 'SamlProperties' => [ 'shape' => 'SamlProperties', ], 'CertificateBasedAuthProperties' => [ 'shape' => 'CertificateBasedAuthProperties', ], 'EndpointEncryptionMode' => [ 'shape' => 'EndpointEncryptionMode', ], 'MicrosoftEntraConfig' => [ 'shape' => 'MicrosoftEntraConfig', ], 'WorkspaceDirectoryName' => [ 'shape' => 'WorkspaceDirectoryName', ], 'WorkspaceDirectoryDescription' => [ 'shape' => 'WorkspaceDirectoryDescription', ], 'UserIdentityType' => [ 'shape' => 'UserIdentityType', ], 'WorkspaceType' => [ 'shape' => 'WorkspaceType', ], 'IDCConfig' => [ 'shape' => 'IDCConfig', ], 'ActiveDirectoryConfig' => [ 'shape' => 'ActiveDirectoryConfig', ], 'StreamingProperties' => [ 'shape' => 'StreamingProperties', ], 'ErrorMessage' => [ 'shape' => 'Description', ], ], ], 'WorkspaceDirectoryDescription' => [ 'type' => 'string', 'pattern' => '^([a-zA-Z0-9_])[\\\\a-zA-Z0-9_@#%*+=:?./!\\s-]{1,255}$', ], 'WorkspaceDirectoryName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9_.\\s-]{1,64}$', ], 'WorkspaceDirectoryNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkspaceDirectoryName', ], 'max' => 25, 'min' => 1, ], 'WorkspaceDirectoryState' => [ 'type' => 'string', 'enum' => [ 'REGISTERING', 'REGISTERED', 'DEREGISTERING', 'DEREGISTERED', 'ERROR', ], ], 'WorkspaceDirectoryType' => [ 'type' => 'string', 'enum' => [ 'SIMPLE_AD', 'AD_CONNECTOR', 'CUSTOMER_MANAGED', 'AWS_IAM_IDENTITY_CENTER', ], ], 'WorkspaceErrorCode' => [ 'type' => 'string', ], 'WorkspaceId' => [ 'type' => 'string', 'pattern' => '^ws-[0-9a-z]{8,63}$', ], 'WorkspaceIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkspaceId', ], 'max' => 25, 'min' => 1, ], 'WorkspaceImage' => [ 'type' => 'structure', 'members' => [ 'ImageId' => [ 'shape' => 'WorkspaceImageId', ], 'Name' => [ 'shape' => 'WorkspaceImageName', ], 'Description' => [ 'shape' => 'WorkspaceImageDescription', ], 'OperatingSystem' => [ 'shape' => 'OperatingSystem', ], 'State' => [ 'shape' => 'WorkspaceImageState', ], 'RequiredTenancy' => [ 'shape' => 'WorkspaceImageRequiredTenancy', ], 'ErrorCode' => [ 'shape' => 'WorkspaceImageErrorCode', ], 'ErrorMessage' => [ 'shape' => 'Description', ], 'Created' => [ 'shape' => 'Timestamp', ], 'OwnerAccountId' => [ 'shape' => 'AwsAccount', ], 'Updates' => [ 'shape' => 'UpdateResult', ], 'ErrorDetails' => [ 'shape' => 'ErrorDetailsList', ], ], ], 'WorkspaceImageDescription' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_./() -]+$', ], 'WorkspaceImageErrorCode' => [ 'type' => 'string', ], 'WorkspaceImageErrorDetailCode' => [ 'type' => 'string', 'enum' => [ 'OutdatedPowershellVersion', 'OfficeInstalled', 'PCoIPAgentInstalled', 'WindowsUpdatesEnabled', 'AutoMountDisabled', 'WorkspacesBYOLAccountNotFound', 'WorkspacesBYOLAccountDisabled', 'DHCPDisabled', 'DiskFreeSpace', 'AdditionalDrivesAttached', 'OSNotSupported', 'DomainJoined', 'AzureDomainJoined', 'FirewallEnabled', 'VMWareToolsInstalled', 'DiskSizeExceeded', 'IncompatiblePartitioning', 'PendingReboot', 'AutoLogonEnabled', 'RealTimeUniversalDisabled', 'MultipleBootPartition', 'Requires64BitOS', 'ZeroRearmCount', 'InPlaceUpgrade', 'AntiVirusInstalled', 'UEFINotSupported', 'UnknownError', 'AppXPackagesInstalled', 'ReservedStorageInUse', 'AdditionalDrivesPresent', 'WindowsUpdatesRequired', 'SysPrepFileMissing', 'UserProfileMissing', 'InsufficientDiskSpace', 'EnvironmentVariablesPathMissingEntries', 'DomainAccountServicesFound', 'InvalidIp', 'RemoteDesktopServicesDisabled', 'WindowsModulesInstallerDisabled', 'AmazonSsmAgentEnabled', 'UnsupportedSecurityProtocol', 'MultipleUserProfiles', 'StagedAppxPackage', 'UnsupportedOsUpgrade', 'InsufficientRearmCount', 'ProtocolOSIncompatibility', 'MemoryIntegrityIncompatibility', 'RestrictedDriveLetterInUse', ], ], 'WorkspaceImageId' => [ 'type' => 'string', 'pattern' => 'wsi-[0-9a-z]{9,63}$', ], 'WorkspaceImageIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkspaceImageId', ], 'max' => 25, 'min' => 1, ], 'WorkspaceImageIngestionProcess' => [ 'type' => 'string', 'enum' => [ 'BYOL_REGULAR', 'BYOL_GRAPHICS', 'BYOL_GRAPHICSPRO', 'BYOL_GRAPHICS_G4DN', 'BYOL_REGULAR_WSP', 'BYOL_GRAPHICS_G4DN_WSP', 'BYOL_REGULAR_BYOP', 'BYOL_GRAPHICS_G4DN_BYOP', ], ], 'WorkspaceImageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkspaceImage', ], ], 'WorkspaceImageName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_./()\\\\-]+$', ], 'WorkspaceImageRequiredTenancy' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'DEDICATED', ], ], 'WorkspaceImageState' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'PENDING', 'ERROR', ], ], 'WorkspaceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Workspace', ], ], 'WorkspaceName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_()][a-zA-Z0-9_.()-]{1,63}$', ], 'WorkspaceProperties' => [ 'type' => 'structure', 'members' => [ 'RunningMode' => [ 'shape' => 'RunningMode', ], 'RunningModeAutoStopTimeoutInMinutes' => [ 'shape' => 'RunningModeAutoStopTimeoutInMinutes', ], 'RootVolumeSizeGib' => [ 'shape' => 'RootVolumeSizeGib', ], 'UserVolumeSizeGib' => [ 'shape' => 'UserVolumeSizeGib', ], 'ComputeTypeName' => [ 'shape' => 'Compute', ], 'Protocols' => [ 'shape' => 'ProtocolList', ], 'OperatingSystemName' => [ 'shape' => 'OperatingSystemName', ], 'GlobalAccelerator' => [ 'shape' => 'GlobalAcceleratorForWorkSpace', ], ], ], 'WorkspaceRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'UserName', 'BundleId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'UserName' => [ 'shape' => 'UserName', ], 'BundleId' => [ 'shape' => 'BundleId', ], 'VolumeEncryptionKey' => [ 'shape' => 'VolumeEncryptionKey', ], 'UserVolumeEncryptionEnabled' => [ 'shape' => 'BooleanObject', ], 'RootVolumeEncryptionEnabled' => [ 'shape' => 'BooleanObject', ], 'WorkspaceProperties' => [ 'shape' => 'WorkspaceProperties', ], 'Tags' => [ 'shape' => 'TagList', ], 'WorkspaceName' => [ 'shape' => 'WorkspaceName', ], ], ], 'WorkspaceRequestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkspaceRequest', ], 'max' => 25, 'min' => 1, ], 'WorkspaceResourceAssociation' => [ 'type' => 'structure', 'members' => [ 'AssociatedResourceId' => [ 'shape' => 'NonEmptyString', ], 'AssociatedResourceType' => [ 'shape' => 'WorkSpaceAssociatedResourceType', ], 'Created' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'State' => [ 'shape' => 'AssociationState', ], 'StateReason' => [ 'shape' => 'AssociationStateReason', ], 'WorkspaceId' => [ 'shape' => 'WorkspaceId', ], ], ], 'WorkspaceResourceAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkspaceResourceAssociation', ], ], 'WorkspaceState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'AVAILABLE', 'IMPAIRED', 'UNHEALTHY', 'REBOOTING', 'STARTING', 'REBUILDING', 'RESTORING', 'MAINTENANCE', 'ADMIN_MAINTENANCE', 'TERMINATING', 'TERMINATED', 'SUSPENDED', 'UPDATING', 'STOPPING', 'STOPPED', 'ERROR', ], ], 'WorkspaceType' => [ 'type' => 'string', 'enum' => [ 'PERSONAL', 'POOLS', ], ], 'WorkspacesDefaultRoleNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'WorkspacesIpGroup' => [ 'type' => 'structure', 'members' => [ 'groupId' => [ 'shape' => 'IpGroupId', ], 'groupName' => [ 'shape' => 'IpGroupName', ], 'groupDesc' => [ 'shape' => 'IpGroupDesc', ], 'userRules' => [ 'shape' => 'IpRuleList', ], ], ], 'WorkspacesIpGroupsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkspacesIpGroup', ], ], 'WorkspacesPool' => [ 'type' => 'structure', 'required' => [ 'PoolId', 'PoolArn', 'CapacityStatus', 'PoolName', 'State', 'CreatedAt', 'BundleId', 'DirectoryId', 'RunningMode', ], 'members' => [ 'PoolId' => [ 'shape' => 'WorkspacesPoolId', ], 'PoolArn' => [ 'shape' => 'ARN', ], 'CapacityStatus' => [ 'shape' => 'CapacityStatus', ], 'PoolName' => [ 'shape' => 'WorkspacesPoolName', ], 'Description' => [ 'shape' => 'UpdateDescription', ], 'State' => [ 'shape' => 'WorkspacesPoolState', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'BundleId' => [ 'shape' => 'BundleId', ], 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Errors' => [ 'shape' => 'WorkspacesPoolErrors', ], 'ApplicationSettings' => [ 'shape' => 'ApplicationSettingsResponse', ], 'TimeoutSettings' => [ 'shape' => 'TimeoutSettings', ], 'RunningMode' => [ 'shape' => 'PoolsRunningMode', ], ], ], 'WorkspacesPoolError' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'WorkspacesPoolErrorCode', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'WorkspacesPoolErrorCode' => [ 'type' => 'string', 'enum' => [ 'IAM_SERVICE_ROLE_IS_MISSING', 'IAM_SERVICE_ROLE_MISSING_ENI_DESCRIBE_ACTION', 'IAM_SERVICE_ROLE_MISSING_ENI_CREATE_ACTION', 'IAM_SERVICE_ROLE_MISSING_ENI_DELETE_ACTION', 'NETWORK_INTERFACE_LIMIT_EXCEEDED', 'INTERNAL_SERVICE_ERROR', 'MACHINE_ROLE_IS_MISSING', 'STS_DISABLED_IN_REGION', 'SUBNET_HAS_INSUFFICIENT_IP_ADDRESSES', 'IAM_SERVICE_ROLE_MISSING_DESCRIBE_SUBNET_ACTION', 'SUBNET_NOT_FOUND', 'IMAGE_NOT_FOUND', 'INVALID_SUBNET_CONFIGURATION', 'SECURITY_GROUPS_NOT_FOUND', 'IGW_NOT_ATTACHED', 'IAM_SERVICE_ROLE_MISSING_DESCRIBE_SECURITY_GROUPS_ACTION', 'WORKSPACES_POOL_STOPPED', 'WORKSPACES_POOL_INSTANCE_PROVISIONING_FAILURE', 'DOMAIN_JOIN_ERROR_FILE_NOT_FOUND', 'DOMAIN_JOIN_ERROR_ACCESS_DENIED', 'DOMAIN_JOIN_ERROR_LOGON_FAILURE', 'DOMAIN_JOIN_ERROR_INVALID_PARAMETER', 'DOMAIN_JOIN_ERROR_MORE_DATA', 'DOMAIN_JOIN_ERROR_NO_SUCH_DOMAIN', 'DOMAIN_JOIN_ERROR_NOT_SUPPORTED', 'DOMAIN_JOIN_NERR_INVALID_WORKGROUP_NAME', 'DOMAIN_JOIN_NERR_WORKSTATION_NOT_STARTED', 'DOMAIN_JOIN_ERROR_DS_MACHINE_ACCOUNT_QUOTA_EXCEEDED', 'DOMAIN_JOIN_NERR_PASSWORD_EXPIRED', 'DOMAIN_JOIN_INTERNAL_SERVICE_ERROR', 'DOMAIN_JOIN_ERROR_SECRET_ACTION_PERMISSION_IS_MISSING', 'DOMAIN_JOIN_ERROR_SECRET_DECRYPTION_FAILURE', 'DOMAIN_JOIN_ERROR_SECRET_STATE_INVALID', 'DOMAIN_JOIN_ERROR_SECRET_NOT_FOUND', 'DOMAIN_JOIN_ERROR_SECRET_VALUE_KEY_NOT_FOUND', 'DOMAIN_JOIN_ERROR_SECRET_INVALID', 'BUNDLE_NOT_FOUND', 'DIRECTORY_NOT_FOUND', 'INSUFFICIENT_PERMISSIONS_ERROR', 'DEFAULT_OU_IS_MISSING', ], ], 'WorkspacesPoolErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkspacesPoolError', ], ], 'WorkspacesPoolId' => [ 'type' => 'string', 'pattern' => '^wspool-[0-9a-z]{9}$', ], 'WorkspacesPoolIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkspacesPoolId', ], 'max' => 25, 'min' => 1, ], 'WorkspacesPoolName' => [ 'type' => 'string', 'pattern' => '^[A-Za-z0-9][A-Za-z0-9_.-]{0,63}$', ], 'WorkspacesPoolSession' => [ 'type' => 'structure', 'required' => [ 'SessionId', 'PoolId', 'UserId', ], 'members' => [ 'AuthenticationType' => [ 'shape' => 'AuthenticationType', ], 'ConnectionState' => [ 'shape' => 'SessionConnectionState', ], 'SessionId' => [ 'shape' => 'AmazonUuid', ], 'InstanceId' => [ 'shape' => 'SessionInstanceId', ], 'PoolId' => [ 'shape' => 'WorkspacesPoolId', ], 'ExpirationTime' => [ 'shape' => 'Timestamp', ], 'NetworkAccessConfiguration' => [ 'shape' => 'NetworkAccessConfiguration', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'UserId' => [ 'shape' => 'WorkspacesPoolUserId', ], ], ], 'WorkspacesPoolSessions' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkspacesPoolSession', ], ], 'WorkspacesPoolState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'DELETING', 'RUNNING', 'STARTING', 'STOPPED', 'STOPPING', 'UPDATING', ], ], 'WorkspacesPoolUserId' => [ 'type' => 'string', 'max' => 128, 'min' => 2, ], 'WorkspacesPools' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkspacesPool', ], ], ],];
