<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Preview
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Preview\HostedNumbers;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;
use Twilio\Base\PhoneNumberCapabilities;


/**
 * @property string|null $sid
 * @property string|null $accountSid
 * @property string|null $incomingPhoneNumberSid
 * @property string|null $addressSid
 * @property string|null $signingDocumentSid
 * @property string|null $phoneNumber
 * @property PhoneNumberCapabilities|null $capabilities
 * @property string|null $friendlyName
 * @property string|null $uniqueName
 * @property string $status
 * @property string|null $failureReason
 * @property \DateTime|null $dateCreated
 * @property \DateTime|null $dateUpdated
 * @property int $verificationAttempts
 * @property string|null $email
 * @property string[]|null $ccEmails
 * @property string|null $url
 * @property string $verificationType
 * @property string|null $verificationDocumentSid
 * @property string|null $extension
 * @property int $callDelay
 * @property string|null $verificationCode
 * @property string[]|null $verificationCallSids
 */
class HostedNumberOrderInstance extends InstanceResource
{
    /**
     * Initialize the HostedNumberOrderInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $sid A 34 character string that uniquely identifies this HostedNumberOrder.
     */
    public function __construct(Version $version, array $payload, ?string $sid = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'sid' => Values::array_get($payload, 'sid'),
            'accountSid' => Values::array_get($payload, 'account_sid'),
            'incomingPhoneNumberSid' => Values::array_get($payload, 'incoming_phone_number_sid'),
            'addressSid' => Values::array_get($payload, 'address_sid'),
            'signingDocumentSid' => Values::array_get($payload, 'signing_document_sid'),
            'phoneNumber' => Values::array_get($payload, 'phone_number'),
            'capabilities' => Deserialize::phoneNumberCapabilities(Values::array_get($payload, 'capabilities')),
            'friendlyName' => Values::array_get($payload, 'friendly_name'),
            'uniqueName' => Values::array_get($payload, 'unique_name'),
            'status' => Values::array_get($payload, 'status'),
            'failureReason' => Values::array_get($payload, 'failure_reason'),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'dateUpdated' => Deserialize::dateTime(Values::array_get($payload, 'date_updated')),
            'verificationAttempts' => Values::array_get($payload, 'verification_attempts'),
            'email' => Values::array_get($payload, 'email'),
            'ccEmails' => Values::array_get($payload, 'cc_emails'),
            'url' => Values::array_get($payload, 'url'),
            'verificationType' => Values::array_get($payload, 'verification_type'),
            'verificationDocumentSid' => Values::array_get($payload, 'verification_document_sid'),
            'extension' => Values::array_get($payload, 'extension'),
            'callDelay' => Values::array_get($payload, 'call_delay'),
            'verificationCode' => Values::array_get($payload, 'verification_code'),
            'verificationCallSids' => Values::array_get($payload, 'verification_call_sids'),
        ];

        $this->solution = ['sid' => $sid ?: $this->properties['sid'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return HostedNumberOrderContext Context for this HostedNumberOrderInstance
     */
    protected function proxy(): HostedNumberOrderContext
    {
        if (!$this->context) {
            $this->context = new HostedNumberOrderContext(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->context;
    }

    /**
     * Delete the HostedNumberOrderInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->proxy()->delete();
    }

    /**
     * Fetch the HostedNumberOrderInstance
     *
     * @return HostedNumberOrderInstance Fetched HostedNumberOrderInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): HostedNumberOrderInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Update the HostedNumberOrderInstance
     *
     * @param array|Options $options Optional Arguments
     * @return HostedNumberOrderInstance Updated HostedNumberOrderInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(array $options = []): HostedNumberOrderInstance
    {

        return $this->proxy()->update($options);
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Preview.HostedNumbers.HostedNumberOrderInstance ' . \implode(' ', $context) . ']';
    }
}

