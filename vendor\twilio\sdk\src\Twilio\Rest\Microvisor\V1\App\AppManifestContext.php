<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Microvisor
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Microvisor\V1\App;

use Twilio\Exceptions\TwilioException;
use Twilio\Values;
use Twilio\Version;
use Twilio\InstanceContext;


class AppManifestContext extends InstanceContext
    {
    /**
     * Initialize the AppManifestContext
     *
     * @param Version $version Version that contains the resource
     * @param string $appSid A 34-character string that uniquely identifies this App.
     */
    public function __construct(
        Version $version,
        $appSid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'appSid' =>
            $appSid,
        ];

        $this->uri = '/Apps/' . \rawurlencode($appSid)
        .'/Manifest';
    }

    /**
     * Fetch the AppManifestInstance
     *
     * @return AppManifestInstance Fetched AppManifestInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): AppManifestInstance
    {

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json' ]);
        $payload = $this->version->fetch('GET', $this->uri, [], [], $headers);

        return new AppManifestInstance(
            $this->version,
            $payload,
            $this->solution['appSid']
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Microvisor.V1.AppManifestContext ' . \implode(' ', $context) . ']';
    }
}
