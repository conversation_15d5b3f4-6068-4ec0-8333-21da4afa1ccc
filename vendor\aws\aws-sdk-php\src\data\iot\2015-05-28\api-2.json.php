<?php
// This file was auto-generated from sdk-root/src/data/iot/2015-05-28/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2015-05-28', 'endpointPrefix' => 'iot', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS IoT', 'serviceId' => 'IoT', 'signatureVersion' => 'v4', 'signingName' => 'iot', 'uid' => 'iot-2015-05-28', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AcceptCertificateTransfer' => [ 'name' => 'AcceptCertificateTransfer', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/accept-certificate-transfer/{certificateId}', ], 'input' => [ 'shape' => 'AcceptCertificateTransferRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TransferAlreadyCompletedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'AddThingToBillingGroup' => [ 'name' => 'AddThingToBillingGroup', 'http' => [ 'method' => 'PUT', 'requestUri' => '/billing-groups/addThingToBillingGroup', ], 'input' => [ 'shape' => 'AddThingToBillingGroupRequest', ], 'output' => [ 'shape' => 'AddThingToBillingGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'AddThingToThingGroup' => [ 'name' => 'AddThingToThingGroup', 'http' => [ 'method' => 'PUT', 'requestUri' => '/thing-groups/addThingToThingGroup', ], 'input' => [ 'shape' => 'AddThingToThingGroupRequest', ], 'output' => [ 'shape' => 'AddThingToThingGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'AssociateSbomWithPackageVersion' => [ 'name' => 'AssociateSbomWithPackageVersion', 'http' => [ 'method' => 'PUT', 'requestUri' => '/packages/{packageName}/versions/{versionName}/sbom', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateSbomWithPackageVersionRequest', ], 'output' => [ 'shape' => 'AssociateSbomWithPackageVersionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'AssociateTargetsWithJob' => [ 'name' => 'AssociateTargetsWithJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/jobs/{jobId}/targets', ], 'input' => [ 'shape' => 'AssociateTargetsWithJobRequest', ], 'output' => [ 'shape' => 'AssociateTargetsWithJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'AttachPolicy' => [ 'name' => 'AttachPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/target-policies/{policyName}', ], 'input' => [ 'shape' => 'AttachPolicyRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'AttachPrincipalPolicy' => [ 'name' => 'AttachPrincipalPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/principal-policies/{policyName}', ], 'input' => [ 'shape' => 'AttachPrincipalPolicyRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], ], 'deprecated' => true, ], 'AttachSecurityProfile' => [ 'name' => 'AttachSecurityProfile', 'http' => [ 'method' => 'PUT', 'requestUri' => '/security-profiles/{securityProfileName}/targets', ], 'input' => [ 'shape' => 'AttachSecurityProfileRequest', ], 'output' => [ 'shape' => 'AttachSecurityProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'VersionConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'AttachThingPrincipal' => [ 'name' => 'AttachThingPrincipal', 'http' => [ 'method' => 'PUT', 'requestUri' => '/things/{thingName}/principals', ], 'input' => [ 'shape' => 'AttachThingPrincipalRequest', ], 'output' => [ 'shape' => 'AttachThingPrincipalResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CancelAuditMitigationActionsTask' => [ 'name' => 'CancelAuditMitigationActionsTask', 'http' => [ 'method' => 'PUT', 'requestUri' => '/audit/mitigationactions/tasks/{taskId}/cancel', ], 'input' => [ 'shape' => 'CancelAuditMitigationActionsTaskRequest', ], 'output' => [ 'shape' => 'CancelAuditMitigationActionsTaskResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CancelAuditTask' => [ 'name' => 'CancelAuditTask', 'http' => [ 'method' => 'PUT', 'requestUri' => '/audit/tasks/{taskId}/cancel', ], 'input' => [ 'shape' => 'CancelAuditTaskRequest', ], 'output' => [ 'shape' => 'CancelAuditTaskResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CancelCertificateTransfer' => [ 'name' => 'CancelCertificateTransfer', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/cancel-certificate-transfer/{certificateId}', ], 'input' => [ 'shape' => 'CancelCertificateTransferRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TransferAlreadyCompletedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CancelDetectMitigationActionsTask' => [ 'name' => 'CancelDetectMitigationActionsTask', 'http' => [ 'method' => 'PUT', 'requestUri' => '/detect/mitigationactions/tasks/{taskId}/cancel', ], 'input' => [ 'shape' => 'CancelDetectMitigationActionsTaskRequest', ], 'output' => [ 'shape' => 'CancelDetectMitigationActionsTaskResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CancelJob' => [ 'name' => 'CancelJob', 'http' => [ 'method' => 'PUT', 'requestUri' => '/jobs/{jobId}/cancel', ], 'input' => [ 'shape' => 'CancelJobRequest', ], 'output' => [ 'shape' => 'CancelJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CancelJobExecution' => [ 'name' => 'CancelJobExecution', 'http' => [ 'method' => 'PUT', 'requestUri' => '/things/{thingName}/jobs/{jobId}/cancel', ], 'input' => [ 'shape' => 'CancelJobExecutionRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidStateTransitionException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'VersionConflictException', ], ], ], 'ClearDefaultAuthorizer' => [ 'name' => 'ClearDefaultAuthorizer', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/default-authorizer', ], 'input' => [ 'shape' => 'ClearDefaultAuthorizerRequest', ], 'output' => [ 'shape' => 'ClearDefaultAuthorizerResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ConfirmTopicRuleDestination' => [ 'name' => 'ConfirmTopicRuleDestination', 'http' => [ 'method' => 'GET', 'requestUri' => '/confirmdestination/{confirmationToken+}', ], 'input' => [ 'shape' => 'ConfirmTopicRuleDestinationRequest', ], 'output' => [ 'shape' => 'ConfirmTopicRuleDestinationResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ConflictingResourceUpdateException', ], ], ], 'CreateAuditSuppression' => [ 'name' => 'CreateAuditSuppression', 'http' => [ 'method' => 'POST', 'requestUri' => '/audit/suppressions/create', ], 'input' => [ 'shape' => 'CreateAuditSuppressionRequest', ], 'output' => [ 'shape' => 'CreateAuditSuppressionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateAuthorizer' => [ 'name' => 'CreateAuthorizer', 'http' => [ 'method' => 'POST', 'requestUri' => '/authorizer/{authorizerName}', ], 'input' => [ 'shape' => 'CreateAuthorizerRequest', ], 'output' => [ 'shape' => 'CreateAuthorizerResponse', ], 'errors' => [ [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateBillingGroup' => [ 'name' => 'CreateBillingGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/billing-groups/{billingGroupName}', ], 'input' => [ 'shape' => 'CreateBillingGroupRequest', ], 'output' => [ 'shape' => 'CreateBillingGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateCertificateFromCsr' => [ 'name' => 'CreateCertificateFromCsr', 'http' => [ 'method' => 'POST', 'requestUri' => '/certificates', ], 'input' => [ 'shape' => 'CreateCertificateFromCsrRequest', ], 'output' => [ 'shape' => 'CreateCertificateFromCsrResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateCertificateProvider' => [ 'name' => 'CreateCertificateProvider', 'http' => [ 'method' => 'POST', 'requestUri' => '/certificate-providers/{certificateProviderName}', ], 'input' => [ 'shape' => 'CreateCertificateProviderRequest', ], 'output' => [ 'shape' => 'CreateCertificateProviderResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateCommand' => [ 'name' => 'CreateCommand', 'http' => [ 'method' => 'PUT', 'requestUri' => '/commands/{commandId}', ], 'input' => [ 'shape' => 'CreateCommandRequest', ], 'output' => [ 'shape' => 'CreateCommandResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateCustomMetric' => [ 'name' => 'CreateCustomMetric', 'http' => [ 'method' => 'POST', 'requestUri' => '/custom-metric/{metricName}', ], 'input' => [ 'shape' => 'CreateCustomMetricRequest', ], 'output' => [ 'shape' => 'CreateCustomMetricResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateDimension' => [ 'name' => 'CreateDimension', 'http' => [ 'method' => 'POST', 'requestUri' => '/dimensions/{name}', ], 'input' => [ 'shape' => 'CreateDimensionRequest', ], 'output' => [ 'shape' => 'CreateDimensionResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateDomainConfiguration' => [ 'name' => 'CreateDomainConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/domainConfigurations/{domainConfigurationName}', ], 'input' => [ 'shape' => 'CreateDomainConfigurationRequest', ], 'output' => [ 'shape' => 'CreateDomainConfigurationResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'CertificateValidationException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateDynamicThingGroup' => [ 'name' => 'CreateDynamicThingGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/dynamic-thing-groups/{thingGroupName}', ], 'input' => [ 'shape' => 'CreateDynamicThingGroupRequest', ], 'output' => [ 'shape' => 'CreateDynamicThingGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidQueryException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateFleetMetric' => [ 'name' => 'CreateFleetMetric', 'http' => [ 'method' => 'PUT', 'requestUri' => '/fleet-metric/{metricName}', ], 'input' => [ 'shape' => 'CreateFleetMetricRequest', ], 'output' => [ 'shape' => 'CreateFleetMetricResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidQueryException', ], [ 'shape' => 'InvalidAggregationException', ], [ 'shape' => 'IndexNotReadyException', ], ], ], 'CreateJob' => [ 'name' => 'CreateJob', 'http' => [ 'method' => 'PUT', 'requestUri' => '/jobs/{jobId}', ], 'input' => [ 'shape' => 'CreateJobRequest', ], 'output' => [ 'shape' => 'CreateJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'CreateJobTemplate' => [ 'name' => 'CreateJobTemplate', 'http' => [ 'method' => 'PUT', 'requestUri' => '/job-templates/{jobTemplateId}', ], 'input' => [ 'shape' => 'CreateJobTemplateRequest', ], 'output' => [ 'shape' => 'CreateJobTemplateResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateKeysAndCertificate' => [ 'name' => 'CreateKeysAndCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/keys-and-certificate', ], 'input' => [ 'shape' => 'CreateKeysAndCertificateRequest', ], 'output' => [ 'shape' => 'CreateKeysAndCertificateResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateMitigationAction' => [ 'name' => 'CreateMitigationAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/mitigationactions/actions/{actionName}', ], 'input' => [ 'shape' => 'CreateMitigationActionRequest', ], 'output' => [ 'shape' => 'CreateMitigationActionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateOTAUpdate' => [ 'name' => 'CreateOTAUpdate', 'http' => [ 'method' => 'POST', 'requestUri' => '/otaUpdates/{otaUpdateId}', ], 'input' => [ 'shape' => 'CreateOTAUpdateRequest', ], 'output' => [ 'shape' => 'CreateOTAUpdateResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'CreatePackage' => [ 'name' => 'CreatePackage', 'http' => [ 'method' => 'PUT', 'requestUri' => '/packages/{packageName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreatePackageRequest', ], 'output' => [ 'shape' => 'CreatePackageResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreatePackageVersion' => [ 'name' => 'CreatePackageVersion', 'http' => [ 'method' => 'PUT', 'requestUri' => '/packages/{packageName}/versions/{versionName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreatePackageVersionRequest', ], 'output' => [ 'shape' => 'CreatePackageVersionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreatePolicy' => [ 'name' => 'CreatePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/policies/{policyName}', ], 'input' => [ 'shape' => 'CreatePolicyRequest', ], 'output' => [ 'shape' => 'CreatePolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'MalformedPolicyException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreatePolicyVersion' => [ 'name' => 'CreatePolicyVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/policies/{policyName}/version', ], 'input' => [ 'shape' => 'CreatePolicyVersionRequest', ], 'output' => [ 'shape' => 'CreatePolicyVersionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'MalformedPolicyException', ], [ 'shape' => 'VersionsLimitExceededException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateProvisioningClaim' => [ 'name' => 'CreateProvisioningClaim', 'http' => [ 'method' => 'POST', 'requestUri' => '/provisioning-templates/{templateName}/provisioning-claim', ], 'input' => [ 'shape' => 'CreateProvisioningClaimRequest', ], 'output' => [ 'shape' => 'CreateProvisioningClaimResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateProvisioningTemplate' => [ 'name' => 'CreateProvisioningTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/provisioning-templates', ], 'input' => [ 'shape' => 'CreateProvisioningTemplateRequest', ], 'output' => [ 'shape' => 'CreateProvisioningTemplateResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], ], ], 'CreateProvisioningTemplateVersion' => [ 'name' => 'CreateProvisioningTemplateVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/provisioning-templates/{templateName}/versions', ], 'input' => [ 'shape' => 'CreateProvisioningTemplateVersionRequest', ], 'output' => [ 'shape' => 'CreateProvisioningTemplateVersionResponse', ], 'errors' => [ [ 'shape' => 'VersionsLimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ConflictingResourceUpdateException', ], ], ], 'CreateRoleAlias' => [ 'name' => 'CreateRoleAlias', 'http' => [ 'method' => 'POST', 'requestUri' => '/role-aliases/{roleAlias}', ], 'input' => [ 'shape' => 'CreateRoleAliasRequest', ], 'output' => [ 'shape' => 'CreateRoleAliasResponse', ], 'errors' => [ [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateScheduledAudit' => [ 'name' => 'CreateScheduledAudit', 'http' => [ 'method' => 'POST', 'requestUri' => '/audit/scheduledaudits/{scheduledAuditName}', ], 'input' => [ 'shape' => 'CreateScheduledAuditRequest', ], 'output' => [ 'shape' => 'CreateScheduledAuditResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateSecurityProfile' => [ 'name' => 'CreateSecurityProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/security-profiles/{securityProfileName}', ], 'input' => [ 'shape' => 'CreateSecurityProfileRequest', ], 'output' => [ 'shape' => 'CreateSecurityProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateStream' => [ 'name' => 'CreateStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/streams/{streamId}', ], 'input' => [ 'shape' => 'CreateStreamRequest', ], 'output' => [ 'shape' => 'CreateStreamResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateThing' => [ 'name' => 'CreateThing', 'http' => [ 'method' => 'POST', 'requestUri' => '/things/{thingName}', ], 'input' => [ 'shape' => 'CreateThingRequest', ], 'output' => [ 'shape' => 'CreateThingResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateThingGroup' => [ 'name' => 'CreateThingGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/thing-groups/{thingGroupName}', ], 'input' => [ 'shape' => 'CreateThingGroupRequest', ], 'output' => [ 'shape' => 'CreateThingGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'CreateThingType' => [ 'name' => 'CreateThingType', 'http' => [ 'method' => 'POST', 'requestUri' => '/thing-types/{thingTypeName}', ], 'input' => [ 'shape' => 'CreateThingTypeRequest', ], 'output' => [ 'shape' => 'CreateThingTypeResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], ], ], 'CreateTopicRule' => [ 'name' => 'CreateTopicRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/rules/{ruleName}', ], 'input' => [ 'shape' => 'CreateTopicRuleRequest', ], 'errors' => [ [ 'shape' => 'SqlParseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ConflictingResourceUpdateException', ], ], ], 'CreateTopicRuleDestination' => [ 'name' => 'CreateTopicRuleDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/destinations', ], 'input' => [ 'shape' => 'CreateTopicRuleDestinationRequest', ], 'output' => [ 'shape' => 'CreateTopicRuleDestinationResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ConflictingResourceUpdateException', ], ], ], 'DeleteAccountAuditConfiguration' => [ 'name' => 'DeleteAccountAuditConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/audit/configuration', ], 'input' => [ 'shape' => 'DeleteAccountAuditConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteAccountAuditConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteAuditSuppression' => [ 'name' => 'DeleteAuditSuppression', 'http' => [ 'method' => 'POST', 'requestUri' => '/audit/suppressions/delete', ], 'input' => [ 'shape' => 'DeleteAuditSuppressionRequest', ], 'output' => [ 'shape' => 'DeleteAuditSuppressionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteAuthorizer' => [ 'name' => 'DeleteAuthorizer', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/authorizer/{authorizerName}', ], 'input' => [ 'shape' => 'DeleteAuthorizerRequest', ], 'output' => [ 'shape' => 'DeleteAuthorizerResponse', ], 'errors' => [ [ 'shape' => 'DeleteConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteBillingGroup' => [ 'name' => 'DeleteBillingGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/billing-groups/{billingGroupName}', ], 'input' => [ 'shape' => 'DeleteBillingGroupRequest', ], 'output' => [ 'shape' => 'DeleteBillingGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'VersionConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteCACertificate' => [ 'name' => 'DeleteCACertificate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/cacertificate/{caCertificateId}', ], 'input' => [ 'shape' => 'DeleteCACertificateRequest', ], 'output' => [ 'shape' => 'DeleteCACertificateResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'CertificateStateException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteCertificate' => [ 'name' => 'DeleteCertificate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/certificates/{certificateId}', ], 'input' => [ 'shape' => 'DeleteCertificateRequest', ], 'errors' => [ [ 'shape' => 'CertificateStateException', ], [ 'shape' => 'DeleteConflictException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteCertificateProvider' => [ 'name' => 'DeleteCertificateProvider', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/certificate-providers/{certificateProviderName}', ], 'input' => [ 'shape' => 'DeleteCertificateProviderRequest', ], 'output' => [ 'shape' => 'DeleteCertificateProviderResponse', ], 'errors' => [ [ 'shape' => 'DeleteConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteCommand' => [ 'name' => 'DeleteCommand', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/commands/{commandId}', ], 'input' => [ 'shape' => 'DeleteCommandRequest', ], 'output' => [ 'shape' => 'DeleteCommandResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteCommandExecution' => [ 'name' => 'DeleteCommandExecution', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/command-executions/{executionId}', ], 'input' => [ 'shape' => 'DeleteCommandExecutionRequest', ], 'output' => [ 'shape' => 'DeleteCommandExecutionResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteCustomMetric' => [ 'name' => 'DeleteCustomMetric', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/custom-metric/{metricName}', ], 'input' => [ 'shape' => 'DeleteCustomMetricRequest', ], 'output' => [ 'shape' => 'DeleteCustomMetricResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteDimension' => [ 'name' => 'DeleteDimension', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/dimensions/{name}', ], 'input' => [ 'shape' => 'DeleteDimensionRequest', ], 'output' => [ 'shape' => 'DeleteDimensionResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteDomainConfiguration' => [ 'name' => 'DeleteDomainConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/domainConfigurations/{domainConfigurationName}', ], 'input' => [ 'shape' => 'DeleteDomainConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteDomainConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DeleteDynamicThingGroup' => [ 'name' => 'DeleteDynamicThingGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/dynamic-thing-groups/{thingGroupName}', ], 'input' => [ 'shape' => 'DeleteDynamicThingGroupRequest', ], 'output' => [ 'shape' => 'DeleteDynamicThingGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'VersionConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteFleetMetric' => [ 'name' => 'DeleteFleetMetric', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/fleet-metric/{metricName}', ], 'input' => [ 'shape' => 'DeleteFleetMetricRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'VersionConflictException', ], ], ], 'DeleteJob' => [ 'name' => 'DeleteJob', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/jobs/{jobId}', ], 'input' => [ 'shape' => 'DeleteJobRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidStateTransitionException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteJobExecution' => [ 'name' => 'DeleteJobExecution', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/things/{thingName}/jobs/{jobId}/executionNumber/{executionNumber}', ], 'input' => [ 'shape' => 'DeleteJobExecutionRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidStateTransitionException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteJobTemplate' => [ 'name' => 'DeleteJobTemplate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/job-templates/{jobTemplateId}', ], 'input' => [ 'shape' => 'DeleteJobTemplateRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteMitigationAction' => [ 'name' => 'DeleteMitigationAction', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/mitigationactions/actions/{actionName}', ], 'input' => [ 'shape' => 'DeleteMitigationActionRequest', ], 'output' => [ 'shape' => 'DeleteMitigationActionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteOTAUpdate' => [ 'name' => 'DeleteOTAUpdate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/otaUpdates/{otaUpdateId}', ], 'input' => [ 'shape' => 'DeleteOTAUpdateRequest', ], 'output' => [ 'shape' => 'DeleteOTAUpdateResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'VersionConflictException', ], ], ], 'DeletePackage' => [ 'name' => 'DeletePackage', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/packages/{packageName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeletePackageRequest', ], 'output' => [ 'shape' => 'DeletePackageResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeletePackageVersion' => [ 'name' => 'DeletePackageVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/packages/{packageName}/versions/{versionName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeletePackageVersionRequest', ], 'output' => [ 'shape' => 'DeletePackageVersionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeletePolicy' => [ 'name' => 'DeletePolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/policies/{policyName}', ], 'input' => [ 'shape' => 'DeletePolicyRequest', ], 'errors' => [ [ 'shape' => 'DeleteConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeletePolicyVersion' => [ 'name' => 'DeletePolicyVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/policies/{policyName}/version/{policyVersionId}', ], 'input' => [ 'shape' => 'DeletePolicyVersionRequest', ], 'errors' => [ [ 'shape' => 'DeleteConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteProvisioningTemplate' => [ 'name' => 'DeleteProvisioningTemplate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/provisioning-templates/{templateName}', ], 'input' => [ 'shape' => 'DeleteProvisioningTemplateRequest', ], 'output' => [ 'shape' => 'DeleteProvisioningTemplateResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DeleteConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingResourceUpdateException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'DeleteProvisioningTemplateVersion' => [ 'name' => 'DeleteProvisioningTemplateVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/provisioning-templates/{templateName}/versions/{versionId}', ], 'input' => [ 'shape' => 'DeleteProvisioningTemplateVersionRequest', ], 'output' => [ 'shape' => 'DeleteProvisioningTemplateVersionResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ConflictingResourceUpdateException', ], [ 'shape' => 'DeleteConflictException', ], ], ], 'DeleteRegistrationCode' => [ 'name' => 'DeleteRegistrationCode', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/registrationcode', ], 'input' => [ 'shape' => 'DeleteRegistrationCodeRequest', ], 'output' => [ 'shape' => 'DeleteRegistrationCodeResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteRoleAlias' => [ 'name' => 'DeleteRoleAlias', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/role-aliases/{roleAlias}', ], 'input' => [ 'shape' => 'DeleteRoleAliasRequest', ], 'output' => [ 'shape' => 'DeleteRoleAliasResponse', ], 'errors' => [ [ 'shape' => 'DeleteConflictException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteScheduledAudit' => [ 'name' => 'DeleteScheduledAudit', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/audit/scheduledaudits/{scheduledAuditName}', ], 'input' => [ 'shape' => 'DeleteScheduledAuditRequest', ], 'output' => [ 'shape' => 'DeleteScheduledAuditResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteSecurityProfile' => [ 'name' => 'DeleteSecurityProfile', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/security-profiles/{securityProfileName}', ], 'input' => [ 'shape' => 'DeleteSecurityProfileRequest', ], 'output' => [ 'shape' => 'DeleteSecurityProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'VersionConflictException', ], ], ], 'DeleteStream' => [ 'name' => 'DeleteStream', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/streams/{streamId}', ], 'input' => [ 'shape' => 'DeleteStreamRequest', ], 'output' => [ 'shape' => 'DeleteStreamResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DeleteConflictException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteThing' => [ 'name' => 'DeleteThing', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/things/{thingName}', ], 'input' => [ 'shape' => 'DeleteThingRequest', ], 'output' => [ 'shape' => 'DeleteThingResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'VersionConflictException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteThingGroup' => [ 'name' => 'DeleteThingGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/thing-groups/{thingGroupName}', ], 'input' => [ 'shape' => 'DeleteThingGroupRequest', ], 'output' => [ 'shape' => 'DeleteThingGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'VersionConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteThingType' => [ 'name' => 'DeleteThingType', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/thing-types/{thingTypeName}', ], 'input' => [ 'shape' => 'DeleteThingTypeRequest', ], 'output' => [ 'shape' => 'DeleteThingTypeResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteTopicRule' => [ 'name' => 'DeleteTopicRule', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/rules/{ruleName}', ], 'input' => [ 'shape' => 'DeleteTopicRuleRequest', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ConflictingResourceUpdateException', ], ], ], 'DeleteTopicRuleDestination' => [ 'name' => 'DeleteTopicRuleDestination', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/destinations/{arn+}', ], 'input' => [ 'shape' => 'DeleteTopicRuleDestinationRequest', ], 'output' => [ 'shape' => 'DeleteTopicRuleDestinationResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ConflictingResourceUpdateException', ], ], ], 'DeleteV2LoggingLevel' => [ 'name' => 'DeleteV2LoggingLevel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2LoggingLevel', ], 'input' => [ 'shape' => 'DeleteV2LoggingLevelRequest', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeprecateThingType' => [ 'name' => 'DeprecateThingType', 'http' => [ 'method' => 'POST', 'requestUri' => '/thing-types/{thingTypeName}/deprecate', ], 'input' => [ 'shape' => 'DeprecateThingTypeRequest', ], 'output' => [ 'shape' => 'DeprecateThingTypeResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeAccountAuditConfiguration' => [ 'name' => 'DescribeAccountAuditConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/audit/configuration', ], 'input' => [ 'shape' => 'DescribeAccountAuditConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeAccountAuditConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeAuditFinding' => [ 'name' => 'DescribeAuditFinding', 'http' => [ 'method' => 'GET', 'requestUri' => '/audit/findings/{findingId}', ], 'input' => [ 'shape' => 'DescribeAuditFindingRequest', ], 'output' => [ 'shape' => 'DescribeAuditFindingResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeAuditMitigationActionsTask' => [ 'name' => 'DescribeAuditMitigationActionsTask', 'http' => [ 'method' => 'GET', 'requestUri' => '/audit/mitigationactions/tasks/{taskId}', ], 'input' => [ 'shape' => 'DescribeAuditMitigationActionsTaskRequest', ], 'output' => [ 'shape' => 'DescribeAuditMitigationActionsTaskResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeAuditSuppression' => [ 'name' => 'DescribeAuditSuppression', 'http' => [ 'method' => 'POST', 'requestUri' => '/audit/suppressions/describe', ], 'input' => [ 'shape' => 'DescribeAuditSuppressionRequest', ], 'output' => [ 'shape' => 'DescribeAuditSuppressionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeAuditTask' => [ 'name' => 'DescribeAuditTask', 'http' => [ 'method' => 'GET', 'requestUri' => '/audit/tasks/{taskId}', ], 'input' => [ 'shape' => 'DescribeAuditTaskRequest', ], 'output' => [ 'shape' => 'DescribeAuditTaskResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeAuthorizer' => [ 'name' => 'DescribeAuthorizer', 'http' => [ 'method' => 'GET', 'requestUri' => '/authorizer/{authorizerName}', ], 'input' => [ 'shape' => 'DescribeAuthorizerRequest', ], 'output' => [ 'shape' => 'DescribeAuthorizerResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeBillingGroup' => [ 'name' => 'DescribeBillingGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/billing-groups/{billingGroupName}', ], 'input' => [ 'shape' => 'DescribeBillingGroupRequest', ], 'output' => [ 'shape' => 'DescribeBillingGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeCACertificate' => [ 'name' => 'DescribeCACertificate', 'http' => [ 'method' => 'GET', 'requestUri' => '/cacertificate/{caCertificateId}', ], 'input' => [ 'shape' => 'DescribeCACertificateRequest', ], 'output' => [ 'shape' => 'DescribeCACertificateResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeCertificate' => [ 'name' => 'DescribeCertificate', 'http' => [ 'method' => 'GET', 'requestUri' => '/certificates/{certificateId}', ], 'input' => [ 'shape' => 'DescribeCertificateRequest', ], 'output' => [ 'shape' => 'DescribeCertificateResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeCertificateProvider' => [ 'name' => 'DescribeCertificateProvider', 'http' => [ 'method' => 'GET', 'requestUri' => '/certificate-providers/{certificateProviderName}', ], 'input' => [ 'shape' => 'DescribeCertificateProviderRequest', ], 'output' => [ 'shape' => 'DescribeCertificateProviderResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeCustomMetric' => [ 'name' => 'DescribeCustomMetric', 'http' => [ 'method' => 'GET', 'requestUri' => '/custom-metric/{metricName}', ], 'input' => [ 'shape' => 'DescribeCustomMetricRequest', ], 'output' => [ 'shape' => 'DescribeCustomMetricResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeDefaultAuthorizer' => [ 'name' => 'DescribeDefaultAuthorizer', 'http' => [ 'method' => 'GET', 'requestUri' => '/default-authorizer', ], 'input' => [ 'shape' => 'DescribeDefaultAuthorizerRequest', ], 'output' => [ 'shape' => 'DescribeDefaultAuthorizerResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeDetectMitigationActionsTask' => [ 'name' => 'DescribeDetectMitigationActionsTask', 'http' => [ 'method' => 'GET', 'requestUri' => '/detect/mitigationactions/tasks/{taskId}', ], 'input' => [ 'shape' => 'DescribeDetectMitigationActionsTaskRequest', ], 'output' => [ 'shape' => 'DescribeDetectMitigationActionsTaskResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeDimension' => [ 'name' => 'DescribeDimension', 'http' => [ 'method' => 'GET', 'requestUri' => '/dimensions/{name}', ], 'input' => [ 'shape' => 'DescribeDimensionRequest', ], 'output' => [ 'shape' => 'DescribeDimensionResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeDomainConfiguration' => [ 'name' => 'DescribeDomainConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/domainConfigurations/{domainConfigurationName}', ], 'input' => [ 'shape' => 'DescribeDomainConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeDomainConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeEndpoint' => [ 'name' => 'DescribeEndpoint', 'http' => [ 'method' => 'GET', 'requestUri' => '/endpoint', ], 'input' => [ 'shape' => 'DescribeEndpointRequest', ], 'output' => [ 'shape' => 'DescribeEndpointResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeEventConfigurations' => [ 'name' => 'DescribeEventConfigurations', 'http' => [ 'method' => 'GET', 'requestUri' => '/event-configurations', ], 'input' => [ 'shape' => 'DescribeEventConfigurationsRequest', ], 'output' => [ 'shape' => 'DescribeEventConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeFleetMetric' => [ 'name' => 'DescribeFleetMetric', 'http' => [ 'method' => 'GET', 'requestUri' => '/fleet-metric/{metricName}', ], 'input' => [ 'shape' => 'DescribeFleetMetricRequest', ], 'output' => [ 'shape' => 'DescribeFleetMetricResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeIndex' => [ 'name' => 'DescribeIndex', 'http' => [ 'method' => 'GET', 'requestUri' => '/indices/{indexName}', ], 'input' => [ 'shape' => 'DescribeIndexRequest', ], 'output' => [ 'shape' => 'DescribeIndexResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeJob' => [ 'name' => 'DescribeJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/jobs/{jobId}', ], 'input' => [ 'shape' => 'DescribeJobRequest', ], 'output' => [ 'shape' => 'DescribeJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeJobExecution' => [ 'name' => 'DescribeJobExecution', 'http' => [ 'method' => 'GET', 'requestUri' => '/things/{thingName}/jobs/{jobId}', ], 'input' => [ 'shape' => 'DescribeJobExecutionRequest', ], 'output' => [ 'shape' => 'DescribeJobExecutionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeJobTemplate' => [ 'name' => 'DescribeJobTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/job-templates/{jobTemplateId}', ], 'input' => [ 'shape' => 'DescribeJobTemplateRequest', ], 'output' => [ 'shape' => 'DescribeJobTemplateResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeManagedJobTemplate' => [ 'name' => 'DescribeManagedJobTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/managed-job-templates/{templateName}', ], 'input' => [ 'shape' => 'DescribeManagedJobTemplateRequest', ], 'output' => [ 'shape' => 'DescribeManagedJobTemplateResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeMitigationAction' => [ 'name' => 'DescribeMitigationAction', 'http' => [ 'method' => 'GET', 'requestUri' => '/mitigationactions/actions/{actionName}', ], 'input' => [ 'shape' => 'DescribeMitigationActionRequest', ], 'output' => [ 'shape' => 'DescribeMitigationActionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeProvisioningTemplate' => [ 'name' => 'DescribeProvisioningTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/provisioning-templates/{templateName}', ], 'input' => [ 'shape' => 'DescribeProvisioningTemplateRequest', ], 'output' => [ 'shape' => 'DescribeProvisioningTemplateResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'DescribeProvisioningTemplateVersion' => [ 'name' => 'DescribeProvisioningTemplateVersion', 'http' => [ 'method' => 'GET', 'requestUri' => '/provisioning-templates/{templateName}/versions/{versionId}', ], 'input' => [ 'shape' => 'DescribeProvisioningTemplateVersionRequest', ], 'output' => [ 'shape' => 'DescribeProvisioningTemplateVersionResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'DescribeRoleAlias' => [ 'name' => 'DescribeRoleAlias', 'http' => [ 'method' => 'GET', 'requestUri' => '/role-aliases/{roleAlias}', ], 'input' => [ 'shape' => 'DescribeRoleAliasRequest', ], 'output' => [ 'shape' => 'DescribeRoleAliasResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeScheduledAudit' => [ 'name' => 'DescribeScheduledAudit', 'http' => [ 'method' => 'GET', 'requestUri' => '/audit/scheduledaudits/{scheduledAuditName}', ], 'input' => [ 'shape' => 'DescribeScheduledAuditRequest', ], 'output' => [ 'shape' => 'DescribeScheduledAuditResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeSecurityProfile' => [ 'name' => 'DescribeSecurityProfile', 'http' => [ 'method' => 'GET', 'requestUri' => '/security-profiles/{securityProfileName}', ], 'input' => [ 'shape' => 'DescribeSecurityProfileRequest', ], 'output' => [ 'shape' => 'DescribeSecurityProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeStream' => [ 'name' => 'DescribeStream', 'http' => [ 'method' => 'GET', 'requestUri' => '/streams/{streamId}', ], 'input' => [ 'shape' => 'DescribeStreamRequest', ], 'output' => [ 'shape' => 'DescribeStreamResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeThing' => [ 'name' => 'DescribeThing', 'http' => [ 'method' => 'GET', 'requestUri' => '/things/{thingName}', ], 'input' => [ 'shape' => 'DescribeThingRequest', ], 'output' => [ 'shape' => 'DescribeThingResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeThingGroup' => [ 'name' => 'DescribeThingGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/thing-groups/{thingGroupName}', ], 'input' => [ 'shape' => 'DescribeThingGroupRequest', ], 'output' => [ 'shape' => 'DescribeThingGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeThingRegistrationTask' => [ 'name' => 'DescribeThingRegistrationTask', 'http' => [ 'method' => 'GET', 'requestUri' => '/thing-registration-tasks/{taskId}', ], 'input' => [ 'shape' => 'DescribeThingRegistrationTaskRequest', ], 'output' => [ 'shape' => 'DescribeThingRegistrationTaskResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeThingType' => [ 'name' => 'DescribeThingType', 'http' => [ 'method' => 'GET', 'requestUri' => '/thing-types/{thingTypeName}', ], 'input' => [ 'shape' => 'DescribeThingTypeRequest', ], 'output' => [ 'shape' => 'DescribeThingTypeResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DetachPolicy' => [ 'name' => 'DetachPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/target-policies/{policyName}', ], 'input' => [ 'shape' => 'DetachPolicyRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'DetachPrincipalPolicy' => [ 'name' => 'DetachPrincipalPolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/principal-policies/{policyName}', ], 'input' => [ 'shape' => 'DetachPrincipalPolicyRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], 'deprecated' => true, ], 'DetachSecurityProfile' => [ 'name' => 'DetachSecurityProfile', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/security-profiles/{securityProfileName}/targets', ], 'input' => [ 'shape' => 'DetachSecurityProfileRequest', ], 'output' => [ 'shape' => 'DetachSecurityProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DetachThingPrincipal' => [ 'name' => 'DetachThingPrincipal', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/things/{thingName}/principals', ], 'input' => [ 'shape' => 'DetachThingPrincipalRequest', ], 'output' => [ 'shape' => 'DetachThingPrincipalResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DisableTopicRule' => [ 'name' => 'DisableTopicRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/rules/{ruleName}/disable', ], 'input' => [ 'shape' => 'DisableTopicRuleRequest', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ConflictingResourceUpdateException', ], ], ], 'DisassociateSbomFromPackageVersion' => [ 'name' => 'DisassociateSbomFromPackageVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/packages/{packageName}/versions/{versionName}/sbom', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateSbomFromPackageVersionRequest', ], 'output' => [ 'shape' => 'DisassociateSbomFromPackageVersionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'EnableTopicRule' => [ 'name' => 'EnableTopicRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/rules/{ruleName}/enable', ], 'input' => [ 'shape' => 'EnableTopicRuleRequest', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ConflictingResourceUpdateException', ], ], ], 'GetBehaviorModelTrainingSummaries' => [ 'name' => 'GetBehaviorModelTrainingSummaries', 'http' => [ 'method' => 'GET', 'requestUri' => '/behavior-model-training/summaries', ], 'input' => [ 'shape' => 'GetBehaviorModelTrainingSummariesRequest', ], 'output' => [ 'shape' => 'GetBehaviorModelTrainingSummariesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetBucketsAggregation' => [ 'name' => 'GetBucketsAggregation', 'http' => [ 'method' => 'POST', 'requestUri' => '/indices/buckets', ], 'input' => [ 'shape' => 'GetBucketsAggregationRequest', ], 'output' => [ 'shape' => 'GetBucketsAggregationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidQueryException', ], [ 'shape' => 'InvalidAggregationException', ], [ 'shape' => 'IndexNotReadyException', ], ], ], 'GetCardinality' => [ 'name' => 'GetCardinality', 'http' => [ 'method' => 'POST', 'requestUri' => '/indices/cardinality', ], 'input' => [ 'shape' => 'GetCardinalityRequest', ], 'output' => [ 'shape' => 'GetCardinalityResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidQueryException', ], [ 'shape' => 'InvalidAggregationException', ], [ 'shape' => 'IndexNotReadyException', ], ], ], 'GetCommand' => [ 'name' => 'GetCommand', 'http' => [ 'method' => 'GET', 'requestUri' => '/commands/{commandId}', ], 'input' => [ 'shape' => 'GetCommandRequest', ], 'output' => [ 'shape' => 'GetCommandResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetCommandExecution' => [ 'name' => 'GetCommandExecution', 'http' => [ 'method' => 'GET', 'requestUri' => '/command-executions/{executionId}', ], 'input' => [ 'shape' => 'GetCommandExecutionRequest', ], 'output' => [ 'shape' => 'GetCommandExecutionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetEffectivePolicies' => [ 'name' => 'GetEffectivePolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/effective-policies', ], 'input' => [ 'shape' => 'GetEffectivePoliciesRequest', ], 'output' => [ 'shape' => 'GetEffectivePoliciesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'GetIndexingConfiguration' => [ 'name' => 'GetIndexingConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/indexing/config', ], 'input' => [ 'shape' => 'GetIndexingConfigurationRequest', ], 'output' => [ 'shape' => 'GetIndexingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'GetJobDocument' => [ 'name' => 'GetJobDocument', 'http' => [ 'method' => 'GET', 'requestUri' => '/jobs/{jobId}/job-document', ], 'input' => [ 'shape' => 'GetJobDocumentRequest', ], 'output' => [ 'shape' => 'GetJobDocumentResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'GetLoggingOptions' => [ 'name' => 'GetLoggingOptions', 'http' => [ 'method' => 'GET', 'requestUri' => '/loggingOptions', ], 'input' => [ 'shape' => 'GetLoggingOptionsRequest', ], 'output' => [ 'shape' => 'GetLoggingOptionsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'GetOTAUpdate' => [ 'name' => 'GetOTAUpdate', 'http' => [ 'method' => 'GET', 'requestUri' => '/otaUpdates/{otaUpdateId}', ], 'input' => [ 'shape' => 'GetOTAUpdateRequest', ], 'output' => [ 'shape' => 'GetOTAUpdateResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetPackage' => [ 'name' => 'GetPackage', 'http' => [ 'method' => 'GET', 'requestUri' => '/packages/{packageName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPackageRequest', ], 'output' => [ 'shape' => 'GetPackageResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetPackageConfiguration' => [ 'name' => 'GetPackageConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/package-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPackageConfigurationRequest', ], 'output' => [ 'shape' => 'GetPackageConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetPackageVersion' => [ 'name' => 'GetPackageVersion', 'http' => [ 'method' => 'GET', 'requestUri' => '/packages/{packageName}/versions/{versionName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPackageVersionRequest', ], 'output' => [ 'shape' => 'GetPackageVersionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetPercentiles' => [ 'name' => 'GetPercentiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/indices/percentiles', ], 'input' => [ 'shape' => 'GetPercentilesRequest', ], 'output' => [ 'shape' => 'GetPercentilesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidQueryException', ], [ 'shape' => 'InvalidAggregationException', ], [ 'shape' => 'IndexNotReadyException', ], ], ], 'GetPolicy' => [ 'name' => 'GetPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/policies/{policyName}', ], 'input' => [ 'shape' => 'GetPolicyRequest', ], 'output' => [ 'shape' => 'GetPolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'GetPolicyVersion' => [ 'name' => 'GetPolicyVersion', 'http' => [ 'method' => 'GET', 'requestUri' => '/policies/{policyName}/version/{policyVersionId}', ], 'input' => [ 'shape' => 'GetPolicyVersionRequest', ], 'output' => [ 'shape' => 'GetPolicyVersionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'GetRegistrationCode' => [ 'name' => 'GetRegistrationCode', 'http' => [ 'method' => 'GET', 'requestUri' => '/registrationcode', ], 'input' => [ 'shape' => 'GetRegistrationCodeRequest', ], 'output' => [ 'shape' => 'GetRegistrationCodeResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'GetStatistics' => [ 'name' => 'GetStatistics', 'http' => [ 'method' => 'POST', 'requestUri' => '/indices/statistics', ], 'input' => [ 'shape' => 'GetStatisticsRequest', ], 'output' => [ 'shape' => 'GetStatisticsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidQueryException', ], [ 'shape' => 'InvalidAggregationException', ], [ 'shape' => 'IndexNotReadyException', ], ], ], 'GetThingConnectivityData' => [ 'name' => 'GetThingConnectivityData', 'http' => [ 'method' => 'POST', 'requestUri' => '/things/{thingName}/connectivity-data', ], 'input' => [ 'shape' => 'GetThingConnectivityDataRequest', ], 'output' => [ 'shape' => 'GetThingConnectivityDataResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'IndexNotReadyException', ], ], ], 'GetTopicRule' => [ 'name' => 'GetTopicRule', 'http' => [ 'method' => 'GET', 'requestUri' => '/rules/{ruleName}', ], 'input' => [ 'shape' => 'GetTopicRuleRequest', ], 'output' => [ 'shape' => 'GetTopicRuleResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetTopicRuleDestination' => [ 'name' => 'GetTopicRuleDestination', 'http' => [ 'method' => 'GET', 'requestUri' => '/destinations/{arn+}', ], 'input' => [ 'shape' => 'GetTopicRuleDestinationRequest', ], 'output' => [ 'shape' => 'GetTopicRuleDestinationResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetV2LoggingOptions' => [ 'name' => 'GetV2LoggingOptions', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2LoggingOptions', ], 'input' => [ 'shape' => 'GetV2LoggingOptionsRequest', ], 'output' => [ 'shape' => 'GetV2LoggingOptionsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'NotConfiguredException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListActiveViolations' => [ 'name' => 'ListActiveViolations', 'http' => [ 'method' => 'GET', 'requestUri' => '/active-violations', ], 'input' => [ 'shape' => 'ListActiveViolationsRequest', ], 'output' => [ 'shape' => 'ListActiveViolationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListAttachedPolicies' => [ 'name' => 'ListAttachedPolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/attached-policies/{target}', ], 'input' => [ 'shape' => 'ListAttachedPoliciesRequest', ], 'output' => [ 'shape' => 'ListAttachedPoliciesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'ListAuditFindings' => [ 'name' => 'ListAuditFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/audit/findings', ], 'input' => [ 'shape' => 'ListAuditFindingsRequest', ], 'output' => [ 'shape' => 'ListAuditFindingsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListAuditMitigationActionsExecutions' => [ 'name' => 'ListAuditMitigationActionsExecutions', 'http' => [ 'method' => 'GET', 'requestUri' => '/audit/mitigationactions/executions', ], 'input' => [ 'shape' => 'ListAuditMitigationActionsExecutionsRequest', ], 'output' => [ 'shape' => 'ListAuditMitigationActionsExecutionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListAuditMitigationActionsTasks' => [ 'name' => 'ListAuditMitigationActionsTasks', 'http' => [ 'method' => 'GET', 'requestUri' => '/audit/mitigationactions/tasks', ], 'input' => [ 'shape' => 'ListAuditMitigationActionsTasksRequest', ], 'output' => [ 'shape' => 'ListAuditMitigationActionsTasksResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListAuditSuppressions' => [ 'name' => 'ListAuditSuppressions', 'http' => [ 'method' => 'POST', 'requestUri' => '/audit/suppressions/list', ], 'input' => [ 'shape' => 'ListAuditSuppressionsRequest', ], 'output' => [ 'shape' => 'ListAuditSuppressionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListAuditTasks' => [ 'name' => 'ListAuditTasks', 'http' => [ 'method' => 'GET', 'requestUri' => '/audit/tasks', ], 'input' => [ 'shape' => 'ListAuditTasksRequest', ], 'output' => [ 'shape' => 'ListAuditTasksResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListAuthorizers' => [ 'name' => 'ListAuthorizers', 'http' => [ 'method' => 'GET', 'requestUri' => '/authorizers/', ], 'input' => [ 'shape' => 'ListAuthorizersRequest', ], 'output' => [ 'shape' => 'ListAuthorizersResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListBillingGroups' => [ 'name' => 'ListBillingGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/billing-groups', ], 'input' => [ 'shape' => 'ListBillingGroupsRequest', ], 'output' => [ 'shape' => 'ListBillingGroupsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListCACertificates' => [ 'name' => 'ListCACertificates', 'http' => [ 'method' => 'GET', 'requestUri' => '/cacertificates', ], 'input' => [ 'shape' => 'ListCACertificatesRequest', ], 'output' => [ 'shape' => 'ListCACertificatesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListCertificateProviders' => [ 'name' => 'ListCertificateProviders', 'http' => [ 'method' => 'GET', 'requestUri' => '/certificate-providers/', ], 'input' => [ 'shape' => 'ListCertificateProvidersRequest', ], 'output' => [ 'shape' => 'ListCertificateProvidersResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListCertificates' => [ 'name' => 'ListCertificates', 'http' => [ 'method' => 'GET', 'requestUri' => '/certificates', ], 'input' => [ 'shape' => 'ListCertificatesRequest', ], 'output' => [ 'shape' => 'ListCertificatesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListCertificatesByCA' => [ 'name' => 'ListCertificatesByCA', 'http' => [ 'method' => 'GET', 'requestUri' => '/certificates-by-ca/{caCertificateId}', ], 'input' => [ 'shape' => 'ListCertificatesByCARequest', ], 'output' => [ 'shape' => 'ListCertificatesByCAResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListCommandExecutions' => [ 'name' => 'ListCommandExecutions', 'http' => [ 'method' => 'POST', 'requestUri' => '/command-executions', ], 'input' => [ 'shape' => 'ListCommandExecutionsRequest', ], 'output' => [ 'shape' => 'ListCommandExecutionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListCommands' => [ 'name' => 'ListCommands', 'http' => [ 'method' => 'GET', 'requestUri' => '/commands', ], 'input' => [ 'shape' => 'ListCommandsRequest', ], 'output' => [ 'shape' => 'ListCommandsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListCustomMetrics' => [ 'name' => 'ListCustomMetrics', 'http' => [ 'method' => 'GET', 'requestUri' => '/custom-metrics', ], 'input' => [ 'shape' => 'ListCustomMetricsRequest', ], 'output' => [ 'shape' => 'ListCustomMetricsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListDetectMitigationActionsExecutions' => [ 'name' => 'ListDetectMitigationActionsExecutions', 'http' => [ 'method' => 'GET', 'requestUri' => '/detect/mitigationactions/executions', ], 'input' => [ 'shape' => 'ListDetectMitigationActionsExecutionsRequest', ], 'output' => [ 'shape' => 'ListDetectMitigationActionsExecutionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListDetectMitigationActionsTasks' => [ 'name' => 'ListDetectMitigationActionsTasks', 'http' => [ 'method' => 'GET', 'requestUri' => '/detect/mitigationactions/tasks', ], 'input' => [ 'shape' => 'ListDetectMitigationActionsTasksRequest', ], 'output' => [ 'shape' => 'ListDetectMitigationActionsTasksResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListDimensions' => [ 'name' => 'ListDimensions', 'http' => [ 'method' => 'GET', 'requestUri' => '/dimensions', ], 'input' => [ 'shape' => 'ListDimensionsRequest', ], 'output' => [ 'shape' => 'ListDimensionsResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListDomainConfigurations' => [ 'name' => 'ListDomainConfigurations', 'http' => [ 'method' => 'GET', 'requestUri' => '/domainConfigurations', ], 'input' => [ 'shape' => 'ListDomainConfigurationsRequest', ], 'output' => [ 'shape' => 'ListDomainConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListFleetMetrics' => [ 'name' => 'ListFleetMetrics', 'http' => [ 'method' => 'GET', 'requestUri' => '/fleet-metrics', ], 'input' => [ 'shape' => 'ListFleetMetricsRequest', ], 'output' => [ 'shape' => 'ListFleetMetricsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListIndices' => [ 'name' => 'ListIndices', 'http' => [ 'method' => 'GET', 'requestUri' => '/indices', ], 'input' => [ 'shape' => 'ListIndicesRequest', ], 'output' => [ 'shape' => 'ListIndicesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListJobExecutionsForJob' => [ 'name' => 'ListJobExecutionsForJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/jobs/{jobId}/things', ], 'input' => [ 'shape' => 'ListJobExecutionsForJobRequest', ], 'output' => [ 'shape' => 'ListJobExecutionsForJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListJobExecutionsForThing' => [ 'name' => 'ListJobExecutionsForThing', 'http' => [ 'method' => 'GET', 'requestUri' => '/things/{thingName}/jobs', ], 'input' => [ 'shape' => 'ListJobExecutionsForThingRequest', ], 'output' => [ 'shape' => 'ListJobExecutionsForThingResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListJobTemplates' => [ 'name' => 'ListJobTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/job-templates', ], 'input' => [ 'shape' => 'ListJobTemplatesRequest', ], 'output' => [ 'shape' => 'ListJobTemplatesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListJobs' => [ 'name' => 'ListJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/jobs', ], 'input' => [ 'shape' => 'ListJobsRequest', ], 'output' => [ 'shape' => 'ListJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListManagedJobTemplates' => [ 'name' => 'ListManagedJobTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/managed-job-templates', ], 'input' => [ 'shape' => 'ListManagedJobTemplatesRequest', ], 'output' => [ 'shape' => 'ListManagedJobTemplatesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListMetricValues' => [ 'name' => 'ListMetricValues', 'http' => [ 'method' => 'GET', 'requestUri' => '/metric-values', ], 'input' => [ 'shape' => 'ListMetricValuesRequest', ], 'output' => [ 'shape' => 'ListMetricValuesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListMitigationActions' => [ 'name' => 'ListMitigationActions', 'http' => [ 'method' => 'GET', 'requestUri' => '/mitigationactions/actions', ], 'input' => [ 'shape' => 'ListMitigationActionsRequest', ], 'output' => [ 'shape' => 'ListMitigationActionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListOTAUpdates' => [ 'name' => 'ListOTAUpdates', 'http' => [ 'method' => 'GET', 'requestUri' => '/otaUpdates', ], 'input' => [ 'shape' => 'ListOTAUpdatesRequest', ], 'output' => [ 'shape' => 'ListOTAUpdatesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListOutgoingCertificates' => [ 'name' => 'ListOutgoingCertificates', 'http' => [ 'method' => 'GET', 'requestUri' => '/certificates-out-going', ], 'input' => [ 'shape' => 'ListOutgoingCertificatesRequest', ], 'output' => [ 'shape' => 'ListOutgoingCertificatesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListPackageVersions' => [ 'name' => 'ListPackageVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/packages/{packageName}/versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPackageVersionsRequest', ], 'output' => [ 'shape' => 'ListPackageVersionsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListPackages' => [ 'name' => 'ListPackages', 'http' => [ 'method' => 'GET', 'requestUri' => '/packages', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPackagesRequest', ], 'output' => [ 'shape' => 'ListPackagesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListPolicies' => [ 'name' => 'ListPolicies', 'http' => [ 'method' => 'GET', 'requestUri' => '/policies', ], 'input' => [ 'shape' => 'ListPoliciesRequest', ], 'output' => [ 'shape' => 'ListPoliciesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListPolicyPrincipals' => [ 'name' => 'ListPolicyPrincipals', 'http' => [ 'method' => 'GET', 'requestUri' => '/policy-principals', ], 'input' => [ 'shape' => 'ListPolicyPrincipalsRequest', ], 'output' => [ 'shape' => 'ListPolicyPrincipalsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], 'deprecated' => true, ], 'ListPolicyVersions' => [ 'name' => 'ListPolicyVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/policies/{policyName}/version', ], 'input' => [ 'shape' => 'ListPolicyVersionsRequest', ], 'output' => [ 'shape' => 'ListPolicyVersionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListPrincipalPolicies' => [ 'name' => 'ListPrincipalPolicies', 'http' => [ 'method' => 'GET', 'requestUri' => '/principal-policies', ], 'input' => [ 'shape' => 'ListPrincipalPoliciesRequest', ], 'output' => [ 'shape' => 'ListPrincipalPoliciesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], 'deprecated' => true, ], 'ListPrincipalThings' => [ 'name' => 'ListPrincipalThings', 'http' => [ 'method' => 'GET', 'requestUri' => '/principals/things', ], 'input' => [ 'shape' => 'ListPrincipalThingsRequest', ], 'output' => [ 'shape' => 'ListPrincipalThingsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListPrincipalThingsV2' => [ 'name' => 'ListPrincipalThingsV2', 'http' => [ 'method' => 'GET', 'requestUri' => '/principals/things-v2', ], 'input' => [ 'shape' => 'ListPrincipalThingsV2Request', ], 'output' => [ 'shape' => 'ListPrincipalThingsV2Response', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListProvisioningTemplateVersions' => [ 'name' => 'ListProvisioningTemplateVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/provisioning-templates/{templateName}/versions', ], 'input' => [ 'shape' => 'ListProvisioningTemplateVersionsRequest', ], 'output' => [ 'shape' => 'ListProvisioningTemplateVersionsResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'ListProvisioningTemplates' => [ 'name' => 'ListProvisioningTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/provisioning-templates', ], 'input' => [ 'shape' => 'ListProvisioningTemplatesRequest', ], 'output' => [ 'shape' => 'ListProvisioningTemplatesResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'ListRelatedResourcesForAuditFinding' => [ 'name' => 'ListRelatedResourcesForAuditFinding', 'http' => [ 'method' => 'GET', 'requestUri' => '/audit/relatedResources', ], 'input' => [ 'shape' => 'ListRelatedResourcesForAuditFindingRequest', ], 'output' => [ 'shape' => 'ListRelatedResourcesForAuditFindingResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListRoleAliases' => [ 'name' => 'ListRoleAliases', 'http' => [ 'method' => 'GET', 'requestUri' => '/role-aliases', ], 'input' => [ 'shape' => 'ListRoleAliasesRequest', ], 'output' => [ 'shape' => 'ListRoleAliasesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListSbomValidationResults' => [ 'name' => 'ListSbomValidationResults', 'http' => [ 'method' => 'GET', 'requestUri' => '/packages/{packageName}/versions/{versionName}/sbom-validation-results', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSbomValidationResultsRequest', ], 'output' => [ 'shape' => 'ListSbomValidationResultsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListScheduledAudits' => [ 'name' => 'ListScheduledAudits', 'http' => [ 'method' => 'GET', 'requestUri' => '/audit/scheduledaudits', ], 'input' => [ 'shape' => 'ListScheduledAuditsRequest', ], 'output' => [ 'shape' => 'ListScheduledAuditsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListSecurityProfiles' => [ 'name' => 'ListSecurityProfiles', 'http' => [ 'method' => 'GET', 'requestUri' => '/security-profiles', ], 'input' => [ 'shape' => 'ListSecurityProfilesRequest', ], 'output' => [ 'shape' => 'ListSecurityProfilesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListSecurityProfilesForTarget' => [ 'name' => 'ListSecurityProfilesForTarget', 'http' => [ 'method' => 'GET', 'requestUri' => '/security-profiles-for-target', ], 'input' => [ 'shape' => 'ListSecurityProfilesForTargetRequest', ], 'output' => [ 'shape' => 'ListSecurityProfilesForTargetResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListStreams' => [ 'name' => 'ListStreams', 'http' => [ 'method' => 'GET', 'requestUri' => '/streams', ], 'input' => [ 'shape' => 'ListStreamsRequest', ], 'output' => [ 'shape' => 'ListStreamsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListTargetsForPolicy' => [ 'name' => 'ListTargetsForPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/policy-targets/{policyName}', ], 'input' => [ 'shape' => 'ListTargetsForPolicyRequest', ], 'output' => [ 'shape' => 'ListTargetsForPolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'ListTargetsForSecurityProfile' => [ 'name' => 'ListTargetsForSecurityProfile', 'http' => [ 'method' => 'GET', 'requestUri' => '/security-profiles/{securityProfileName}/targets', ], 'input' => [ 'shape' => 'ListTargetsForSecurityProfileRequest', ], 'output' => [ 'shape' => 'ListTargetsForSecurityProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListThingGroups' => [ 'name' => 'ListThingGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/thing-groups', ], 'input' => [ 'shape' => 'ListThingGroupsRequest', ], 'output' => [ 'shape' => 'ListThingGroupsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListThingGroupsForThing' => [ 'name' => 'ListThingGroupsForThing', 'http' => [ 'method' => 'GET', 'requestUri' => '/things/{thingName}/thing-groups', ], 'input' => [ 'shape' => 'ListThingGroupsForThingRequest', ], 'output' => [ 'shape' => 'ListThingGroupsForThingResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListThingPrincipals' => [ 'name' => 'ListThingPrincipals', 'http' => [ 'method' => 'GET', 'requestUri' => '/things/{thingName}/principals', ], 'input' => [ 'shape' => 'ListThingPrincipalsRequest', ], 'output' => [ 'shape' => 'ListThingPrincipalsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListThingPrincipalsV2' => [ 'name' => 'ListThingPrincipalsV2', 'http' => [ 'method' => 'GET', 'requestUri' => '/things/{thingName}/principals-v2', ], 'input' => [ 'shape' => 'ListThingPrincipalsV2Request', ], 'output' => [ 'shape' => 'ListThingPrincipalsV2Response', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListThingRegistrationTaskReports' => [ 'name' => 'ListThingRegistrationTaskReports', 'http' => [ 'method' => 'GET', 'requestUri' => '/thing-registration-tasks/{taskId}/reports', ], 'input' => [ 'shape' => 'ListThingRegistrationTaskReportsRequest', ], 'output' => [ 'shape' => 'ListThingRegistrationTaskReportsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListThingRegistrationTasks' => [ 'name' => 'ListThingRegistrationTasks', 'http' => [ 'method' => 'GET', 'requestUri' => '/thing-registration-tasks', ], 'input' => [ 'shape' => 'ListThingRegistrationTasksRequest', ], 'output' => [ 'shape' => 'ListThingRegistrationTasksResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListThingTypes' => [ 'name' => 'ListThingTypes', 'http' => [ 'method' => 'GET', 'requestUri' => '/thing-types', ], 'input' => [ 'shape' => 'ListThingTypesRequest', ], 'output' => [ 'shape' => 'ListThingTypesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListThings' => [ 'name' => 'ListThings', 'http' => [ 'method' => 'GET', 'requestUri' => '/things', ], 'input' => [ 'shape' => 'ListThingsRequest', ], 'output' => [ 'shape' => 'ListThingsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListThingsInBillingGroup' => [ 'name' => 'ListThingsInBillingGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/billing-groups/{billingGroupName}/things', ], 'input' => [ 'shape' => 'ListThingsInBillingGroupRequest', ], 'output' => [ 'shape' => 'ListThingsInBillingGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListThingsInThingGroup' => [ 'name' => 'ListThingsInThingGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/thing-groups/{thingGroupName}/things', ], 'input' => [ 'shape' => 'ListThingsInThingGroupRequest', ], 'output' => [ 'shape' => 'ListThingsInThingGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListTopicRuleDestinations' => [ 'name' => 'ListTopicRuleDestinations', 'http' => [ 'method' => 'GET', 'requestUri' => '/destinations', ], 'input' => [ 'shape' => 'ListTopicRuleDestinationsRequest', ], 'output' => [ 'shape' => 'ListTopicRuleDestinationsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'ListTopicRules' => [ 'name' => 'ListTopicRules', 'http' => [ 'method' => 'GET', 'requestUri' => '/rules', ], 'input' => [ 'shape' => 'ListTopicRulesRequest', ], 'output' => [ 'shape' => 'ListTopicRulesResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListV2LoggingLevels' => [ 'name' => 'ListV2LoggingLevels', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2LoggingLevel', ], 'input' => [ 'shape' => 'ListV2LoggingLevelsRequest', ], 'output' => [ 'shape' => 'ListV2LoggingLevelsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'NotConfiguredException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListViolationEvents' => [ 'name' => 'ListViolationEvents', 'http' => [ 'method' => 'GET', 'requestUri' => '/violation-events', ], 'input' => [ 'shape' => 'ListViolationEventsRequest', ], 'output' => [ 'shape' => 'ListViolationEventsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'PutVerificationStateOnViolation' => [ 'name' => 'PutVerificationStateOnViolation', 'http' => [ 'method' => 'POST', 'requestUri' => '/violations/verification-state/{violationId}', ], 'input' => [ 'shape' => 'PutVerificationStateOnViolationRequest', ], 'output' => [ 'shape' => 'PutVerificationStateOnViolationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'RegisterCACertificate' => [ 'name' => 'RegisterCACertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/cacertificate', ], 'input' => [ 'shape' => 'RegisterCACertificateRequest', ], 'output' => [ 'shape' => 'RegisterCACertificateResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'RegistrationCodeValidationException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'CertificateValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'RegisterCertificate' => [ 'name' => 'RegisterCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/certificate/register', ], 'input' => [ 'shape' => 'RegisterCertificateRequest', ], 'output' => [ 'shape' => 'RegisterCertificateResponse', ], 'errors' => [ [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'CertificateValidationException', ], [ 'shape' => 'CertificateStateException', ], [ 'shape' => 'CertificateConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'RegisterCertificateWithoutCA' => [ 'name' => 'RegisterCertificateWithoutCA', 'http' => [ 'method' => 'POST', 'requestUri' => '/certificate/register-no-ca', ], 'input' => [ 'shape' => 'RegisterCertificateWithoutCARequest', ], 'output' => [ 'shape' => 'RegisterCertificateWithoutCAResponse', ], 'errors' => [ [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'CertificateStateException', ], [ 'shape' => 'CertificateValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'RegisterThing' => [ 'name' => 'RegisterThing', 'http' => [ 'method' => 'POST', 'requestUri' => '/things', ], 'input' => [ 'shape' => 'RegisterThingRequest', ], 'output' => [ 'shape' => 'RegisterThingResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingResourceUpdateException', ], [ 'shape' => 'ResourceRegistrationFailureException', ], ], ], 'RejectCertificateTransfer' => [ 'name' => 'RejectCertificateTransfer', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/reject-certificate-transfer/{certificateId}', ], 'input' => [ 'shape' => 'RejectCertificateTransferRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TransferAlreadyCompletedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'RemoveThingFromBillingGroup' => [ 'name' => 'RemoveThingFromBillingGroup', 'http' => [ 'method' => 'PUT', 'requestUri' => '/billing-groups/removeThingFromBillingGroup', ], 'input' => [ 'shape' => 'RemoveThingFromBillingGroupRequest', ], 'output' => [ 'shape' => 'RemoveThingFromBillingGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'RemoveThingFromThingGroup' => [ 'name' => 'RemoveThingFromThingGroup', 'http' => [ 'method' => 'PUT', 'requestUri' => '/thing-groups/removeThingFromThingGroup', ], 'input' => [ 'shape' => 'RemoveThingFromThingGroupRequest', ], 'output' => [ 'shape' => 'RemoveThingFromThingGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ReplaceTopicRule' => [ 'name' => 'ReplaceTopicRule', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/rules/{ruleName}', ], 'input' => [ 'shape' => 'ReplaceTopicRuleRequest', ], 'errors' => [ [ 'shape' => 'SqlParseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ConflictingResourceUpdateException', ], ], ], 'SearchIndex' => [ 'name' => 'SearchIndex', 'http' => [ 'method' => 'POST', 'requestUri' => '/indices/search', ], 'input' => [ 'shape' => 'SearchIndexRequest', ], 'output' => [ 'shape' => 'SearchIndexResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidQueryException', ], [ 'shape' => 'IndexNotReadyException', ], ], ], 'SetDefaultAuthorizer' => [ 'name' => 'SetDefaultAuthorizer', 'http' => [ 'method' => 'POST', 'requestUri' => '/default-authorizer', ], 'input' => [ 'shape' => 'SetDefaultAuthorizerRequest', ], 'output' => [ 'shape' => 'SetDefaultAuthorizerResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], ], ], 'SetDefaultPolicyVersion' => [ 'name' => 'SetDefaultPolicyVersion', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/policies/{policyName}/version/{policyVersionId}', ], 'input' => [ 'shape' => 'SetDefaultPolicyVersionRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'SetLoggingOptions' => [ 'name' => 'SetLoggingOptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/loggingOptions', ], 'input' => [ 'shape' => 'SetLoggingOptionsRequest', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'SetV2LoggingLevel' => [ 'name' => 'SetV2LoggingLevel', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2LoggingLevel', ], 'input' => [ 'shape' => 'SetV2LoggingLevelRequest', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'NotConfiguredException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'SetV2LoggingOptions' => [ 'name' => 'SetV2LoggingOptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2LoggingOptions', ], 'input' => [ 'shape' => 'SetV2LoggingOptionsRequest', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'StartAuditMitigationActionsTask' => [ 'name' => 'StartAuditMitigationActionsTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/audit/mitigationactions/tasks/{taskId}', ], 'input' => [ 'shape' => 'StartAuditMitigationActionsTaskRequest', ], 'output' => [ 'shape' => 'StartAuditMitigationActionsTaskResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TaskAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'StartDetectMitigationActionsTask' => [ 'name' => 'StartDetectMitigationActionsTask', 'http' => [ 'method' => 'PUT', 'requestUri' => '/detect/mitigationactions/tasks/{taskId}', ], 'input' => [ 'shape' => 'StartDetectMitigationActionsTaskRequest', ], 'output' => [ 'shape' => 'StartDetectMitigationActionsTaskResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TaskAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'StartOnDemandAuditTask' => [ 'name' => 'StartOnDemandAuditTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/audit/tasks', ], 'input' => [ 'shape' => 'StartOnDemandAuditTaskRequest', ], 'output' => [ 'shape' => 'StartOnDemandAuditTaskResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'StartThingRegistrationTask' => [ 'name' => 'StartThingRegistrationTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/thing-registration-tasks', ], 'input' => [ 'shape' => 'StartThingRegistrationTaskRequest', ], 'output' => [ 'shape' => 'StartThingRegistrationTaskResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'StopThingRegistrationTask' => [ 'name' => 'StopThingRegistrationTask', 'http' => [ 'method' => 'PUT', 'requestUri' => '/thing-registration-tasks/{taskId}/cancel', ], 'input' => [ 'shape' => 'StopThingRegistrationTaskRequest', ], 'output' => [ 'shape' => 'StopThingRegistrationTaskResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'TestAuthorization' => [ 'name' => 'TestAuthorization', 'http' => [ 'method' => 'POST', 'requestUri' => '/test-authorization', ], 'input' => [ 'shape' => 'TestAuthorizationRequest', ], 'output' => [ 'shape' => 'TestAuthorizationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'TestInvokeAuthorizer' => [ 'name' => 'TestInvokeAuthorizer', 'http' => [ 'method' => 'POST', 'requestUri' => '/authorizer/{authorizerName}/test', ], 'input' => [ 'shape' => 'TestInvokeAuthorizerRequest', ], 'output' => [ 'shape' => 'TestInvokeAuthorizerResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidResponseException', ], ], ], 'TransferCertificate' => [ 'name' => 'TransferCertificate', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/transfer-certificate/{certificateId}', ], 'input' => [ 'shape' => 'TransferCertificateRequest', ], 'output' => [ 'shape' => 'TransferCertificateResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'CertificateStateException', ], [ 'shape' => 'TransferConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/untag', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateAccountAuditConfiguration' => [ 'name' => 'UpdateAccountAuditConfiguration', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/audit/configuration', ], 'input' => [ 'shape' => 'UpdateAccountAuditConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateAccountAuditConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateAuditSuppression' => [ 'name' => 'UpdateAuditSuppression', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/audit/suppressions/update', ], 'input' => [ 'shape' => 'UpdateAuditSuppressionRequest', ], 'output' => [ 'shape' => 'UpdateAuditSuppressionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateAuthorizer' => [ 'name' => 'UpdateAuthorizer', 'http' => [ 'method' => 'PUT', 'requestUri' => '/authorizer/{authorizerName}', ], 'input' => [ 'shape' => 'UpdateAuthorizerRequest', ], 'output' => [ 'shape' => 'UpdateAuthorizerResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateBillingGroup' => [ 'name' => 'UpdateBillingGroup', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/billing-groups/{billingGroupName}', ], 'input' => [ 'shape' => 'UpdateBillingGroupRequest', ], 'output' => [ 'shape' => 'UpdateBillingGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'VersionConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateCACertificate' => [ 'name' => 'UpdateCACertificate', 'http' => [ 'method' => 'PUT', 'requestUri' => '/cacertificate/{caCertificateId}', ], 'input' => [ 'shape' => 'UpdateCACertificateRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateCertificate' => [ 'name' => 'UpdateCertificate', 'http' => [ 'method' => 'PUT', 'requestUri' => '/certificates/{certificateId}', ], 'input' => [ 'shape' => 'UpdateCertificateRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'CertificateStateException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateCertificateProvider' => [ 'name' => 'UpdateCertificateProvider', 'http' => [ 'method' => 'PUT', 'requestUri' => '/certificate-providers/{certificateProviderName}', ], 'input' => [ 'shape' => 'UpdateCertificateProviderRequest', ], 'output' => [ 'shape' => 'UpdateCertificateProviderResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateCommand' => [ 'name' => 'UpdateCommand', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/commands/{commandId}', ], 'input' => [ 'shape' => 'UpdateCommandRequest', ], 'output' => [ 'shape' => 'UpdateCommandResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateCustomMetric' => [ 'name' => 'UpdateCustomMetric', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/custom-metric/{metricName}', ], 'input' => [ 'shape' => 'UpdateCustomMetricRequest', ], 'output' => [ 'shape' => 'UpdateCustomMetricResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateDimension' => [ 'name' => 'UpdateDimension', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/dimensions/{name}', ], 'input' => [ 'shape' => 'UpdateDimensionRequest', ], 'output' => [ 'shape' => 'UpdateDimensionResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateDomainConfiguration' => [ 'name' => 'UpdateDomainConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/domainConfigurations/{domainConfigurationName}', ], 'input' => [ 'shape' => 'UpdateDomainConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateDomainConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'CertificateValidationException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateDynamicThingGroup' => [ 'name' => 'UpdateDynamicThingGroup', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/dynamic-thing-groups/{thingGroupName}', ], 'input' => [ 'shape' => 'UpdateDynamicThingGroupRequest', ], 'output' => [ 'shape' => 'UpdateDynamicThingGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'VersionConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidQueryException', ], ], ], 'UpdateEventConfigurations' => [ 'name' => 'UpdateEventConfigurations', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/event-configurations', ], 'input' => [ 'shape' => 'UpdateEventConfigurationsRequest', ], 'output' => [ 'shape' => 'UpdateEventConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateFleetMetric' => [ 'name' => 'UpdateFleetMetric', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/fleet-metric/{metricName}', ], 'input' => [ 'shape' => 'UpdateFleetMetricRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidQueryException', ], [ 'shape' => 'InvalidAggregationException', ], [ 'shape' => 'VersionConflictException', ], [ 'shape' => 'IndexNotReadyException', ], ], ], 'UpdateIndexingConfiguration' => [ 'name' => 'UpdateIndexingConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/indexing/config', ], 'input' => [ 'shape' => 'UpdateIndexingConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateIndexingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateJob' => [ 'name' => 'UpdateJob', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/jobs/{jobId}', ], 'input' => [ 'shape' => 'UpdateJobRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'UpdateMitigationAction' => [ 'name' => 'UpdateMitigationAction', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/mitigationactions/actions/{actionName}', ], 'input' => [ 'shape' => 'UpdateMitigationActionRequest', ], 'output' => [ 'shape' => 'UpdateMitigationActionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdatePackage' => [ 'name' => 'UpdatePackage', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/packages/{packageName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdatePackageRequest', ], 'output' => [ 'shape' => 'UpdatePackageResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdatePackageConfiguration' => [ 'name' => 'UpdatePackageConfiguration', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/package-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdatePackageConfigurationRequest', ], 'output' => [ 'shape' => 'UpdatePackageConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'UpdatePackageVersion' => [ 'name' => 'UpdatePackageVersion', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/packages/{packageName}/versions/{versionName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdatePackageVersionRequest', ], 'output' => [ 'shape' => 'UpdatePackageVersionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateProvisioningTemplate' => [ 'name' => 'UpdateProvisioningTemplate', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/provisioning-templates/{templateName}', ], 'input' => [ 'shape' => 'UpdateProvisioningTemplateRequest', ], 'output' => [ 'shape' => 'UpdateProvisioningTemplateResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ConflictingResourceUpdateException', ], ], ], 'UpdateRoleAlias' => [ 'name' => 'UpdateRoleAlias', 'http' => [ 'method' => 'PUT', 'requestUri' => '/role-aliases/{roleAlias}', ], 'input' => [ 'shape' => 'UpdateRoleAliasRequest', ], 'output' => [ 'shape' => 'UpdateRoleAliasResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateScheduledAudit' => [ 'name' => 'UpdateScheduledAudit', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/audit/scheduledaudits/{scheduledAuditName}', ], 'input' => [ 'shape' => 'UpdateScheduledAuditRequest', ], 'output' => [ 'shape' => 'UpdateScheduledAuditResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateSecurityProfile' => [ 'name' => 'UpdateSecurityProfile', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/security-profiles/{securityProfileName}', ], 'input' => [ 'shape' => 'UpdateSecurityProfileRequest', ], 'output' => [ 'shape' => 'UpdateSecurityProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'VersionConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateStream' => [ 'name' => 'UpdateStream', 'http' => [ 'method' => 'PUT', 'requestUri' => '/streams/{streamId}', ], 'input' => [ 'shape' => 'UpdateStreamRequest', ], 'output' => [ 'shape' => 'UpdateStreamResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateThing' => [ 'name' => 'UpdateThing', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/things/{thingName}', ], 'input' => [ 'shape' => 'UpdateThingRequest', ], 'output' => [ 'shape' => 'UpdateThingResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'VersionConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateThingGroup' => [ 'name' => 'UpdateThingGroup', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/thing-groups/{thingGroupName}', ], 'input' => [ 'shape' => 'UpdateThingGroupRequest', ], 'output' => [ 'shape' => 'UpdateThingGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'VersionConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateThingGroupsForThing' => [ 'name' => 'UpdateThingGroupsForThing', 'http' => [ 'method' => 'PUT', 'requestUri' => '/thing-groups/updateThingGroupsForThing', ], 'input' => [ 'shape' => 'UpdateThingGroupsForThingRequest', ], 'output' => [ 'shape' => 'UpdateThingGroupsForThingResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateThingType' => [ 'name' => 'UpdateThingType', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/thing-types/{thingTypeName}', ], 'input' => [ 'shape' => 'UpdateThingTypeRequest', ], 'output' => [ 'shape' => 'UpdateThingTypeResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateTopicRuleDestination' => [ 'name' => 'UpdateTopicRuleDestination', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/destinations', ], 'input' => [ 'shape' => 'UpdateTopicRuleDestinationRequest', ], 'output' => [ 'shape' => 'UpdateTopicRuleDestinationResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ConflictingResourceUpdateException', ], ], ], 'ValidateSecurityProfileBehaviors' => [ 'name' => 'ValidateSecurityProfileBehaviors', 'http' => [ 'method' => 'POST', 'requestUri' => '/security-profile-behaviors/validate', ], 'input' => [ 'shape' => 'ValidateSecurityProfileBehaviorsRequest', ], 'output' => [ 'shape' => 'ValidateSecurityProfileBehaviorsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], ], ], ], 'shapes' => [ 'AbortAction' => [ 'type' => 'string', 'enum' => [ 'CANCEL', ], ], 'AbortConfig' => [ 'type' => 'structure', 'required' => [ 'criteriaList', ], 'members' => [ 'criteriaList' => [ 'shape' => 'AbortCriteriaList', ], ], ], 'AbortCriteria' => [ 'type' => 'structure', 'required' => [ 'failureType', 'action', 'thresholdPercentage', 'minNumberOfExecutedThings', ], 'members' => [ 'failureType' => [ 'shape' => 'JobExecutionFailureType', ], 'action' => [ 'shape' => 'AbortAction', ], 'thresholdPercentage' => [ 'shape' => 'AbortThresholdPercentage', ], 'minNumberOfExecutedThings' => [ 'shape' => 'MinimumNumberOfExecutedThings', ], ], ], 'AbortCriteriaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AbortCriteria', ], 'min' => 1, ], 'AbortThresholdPercentage' => [ 'type' => 'double', 'max' => 100, ], 'AcceptCertificateTransferRequest' => [ 'type' => 'structure', 'required' => [ 'certificateId', ], 'members' => [ 'certificateId' => [ 'shape' => 'CertificateId', 'location' => 'uri', 'locationName' => 'certificateId', ], 'setAsActive' => [ 'shape' => 'SetAsActive', 'location' => 'querystring', 'locationName' => 'setAsActive', ], ], ], 'AcmCertificateArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:aws(-cn|-us-gov|-iso-b|-iso)?:acm:[a-z]{2}-(gov-|iso-|isob-)?[a-z]{4,9}-\\d{1}:\\d{12}:certificate/[a-zA-Z0-9/-]+', ], 'Action' => [ 'type' => 'structure', 'members' => [ 'dynamoDB' => [ 'shape' => 'DynamoDBAction', ], 'dynamoDBv2' => [ 'shape' => 'DynamoDBv2Action', ], 'lambda' => [ 'shape' => 'LambdaAction', ], 'sns' => [ 'shape' => 'SnsAction', ], 'sqs' => [ 'shape' => 'SqsAction', ], 'kinesis' => [ 'shape' => 'KinesisAction', ], 'republish' => [ 'shape' => 'RepublishAction', ], 's3' => [ 'shape' => 'S3Action', ], 'firehose' => [ 'shape' => 'FirehoseAction', ], 'cloudwatchMetric' => [ 'shape' => 'CloudwatchMetricAction', ], 'cloudwatchAlarm' => [ 'shape' => 'CloudwatchAlarmAction', ], 'cloudwatchLogs' => [ 'shape' => 'CloudwatchLogsAction', ], 'elasticsearch' => [ 'shape' => 'ElasticsearchAction', ], 'salesforce' => [ 'shape' => 'SalesforceAction', ], 'iotAnalytics' => [ 'shape' => 'IotAnalyticsAction', ], 'iotEvents' => [ 'shape' => 'IotEventsAction', ], 'iotSiteWise' => [ 'shape' => 'IotSiteWiseAction', ], 'stepFunctions' => [ 'shape' => 'StepFunctionsAction', ], 'timestream' => [ 'shape' => 'TimestreamAction', ], 'http' => [ 'shape' => 'HttpAction', ], 'kafka' => [ 'shape' => 'KafkaAction', ], 'openSearch' => [ 'shape' => 'OpenSearchAction', ], 'location' => [ 'shape' => 'LocationAction', ], ], ], 'ActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Action', ], 'max' => 10, 'min' => 0, ], 'ActionType' => [ 'type' => 'string', 'enum' => [ 'PUBLISH', 'SUBSCRIBE', 'RECEIVE', 'CONNECT', ], ], 'ActiveViolation' => [ 'type' => 'structure', 'members' => [ 'violationId' => [ 'shape' => 'ViolationId', ], 'thingName' => [ 'shape' => 'DeviceDefenderThingName', ], 'securityProfileName' => [ 'shape' => 'SecurityProfileName', ], 'behavior' => [ 'shape' => 'Behavior', ], 'lastViolationValue' => [ 'shape' => 'MetricValue', ], 'violationEventAdditionalInfo' => [ 'shape' => 'ViolationEventAdditionalInfo', ], 'verificationState' => [ 'shape' => 'VerificationState', ], 'verificationStateDescription' => [ 'shape' => 'VerificationStateDescription', ], 'lastViolationTime' => [ 'shape' => 'Timestamp', ], 'violationStartTime' => [ 'shape' => 'Timestamp', ], ], ], 'ActiveViolations' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActiveViolation', ], ], 'AddThingToBillingGroupRequest' => [ 'type' => 'structure', 'members' => [ 'billingGroupName' => [ 'shape' => 'BillingGroupName', ], 'billingGroupArn' => [ 'shape' => 'BillingGroupArn', ], 'thingName' => [ 'shape' => 'ThingName', ], 'thingArn' => [ 'shape' => 'ThingArn', ], ], ], 'AddThingToBillingGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'AddThingToThingGroupRequest' => [ 'type' => 'structure', 'members' => [ 'thingGroupName' => [ 'shape' => 'ThingGroupName', ], 'thingGroupArn' => [ 'shape' => 'ThingGroupArn', ], 'thingName' => [ 'shape' => 'ThingName', ], 'thingArn' => [ 'shape' => 'ThingArn', ], 'overrideDynamicGroups' => [ 'shape' => 'OverrideDynamicGroups', ], ], ], 'AddThingToThingGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'AddThingsToThingGroupParams' => [ 'type' => 'structure', 'required' => [ 'thingGroupNames', ], 'members' => [ 'thingGroupNames' => [ 'shape' => 'ThingGroupNames', ], 'overrideDynamicGroups' => [ 'shape' => 'NullableBoolean', ], ], ], 'AdditionalMetricsToRetainList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BehaviorMetric', ], ], 'AdditionalMetricsToRetainV2List' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricToRetain', ], ], 'AdditionalParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeKey', ], 'value' => [ 'shape' => 'Value', ], ], 'AggregationField' => [ 'type' => 'string', 'min' => 1, ], 'AggregationType' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'AggregationTypeName', ], 'values' => [ 'shape' => 'AggregationTypeValues', ], ], ], 'AggregationTypeName' => [ 'type' => 'string', 'enum' => [ 'Statistics', 'Percentiles', 'Cardinality', ], ], 'AggregationTypeValue' => [ 'type' => 'string', 'max' => 12, 'min' => 1, 'pattern' => '[a-zA-Z0-9]+', ], 'AggregationTypeValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregationTypeValue', ], ], 'AlarmName' => [ 'type' => 'string', ], 'AlertTarget' => [ 'type' => 'structure', 'required' => [ 'alertTargetArn', 'roleArn', ], 'members' => [ 'alertTargetArn' => [ 'shape' => 'AlertTargetArn', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'AlertTargetArn' => [ 'type' => 'string', ], 'AlertTargetType' => [ 'type' => 'string', 'enum' => [ 'SNS', ], ], 'AlertTargets' => [ 'type' => 'map', 'key' => [ 'shape' => 'AlertTargetType', ], 'value' => [ 'shape' => 'AlertTarget', ], ], 'AllowAuthorizerOverride' => [ 'type' => 'boolean', ], 'AllowAutoRegistration' => [ 'type' => 'boolean', ], 'Allowed' => [ 'type' => 'structure', 'members' => [ 'policies' => [ 'shape' => 'Policies', ], ], ], 'ApplicationProtocol' => [ 'type' => 'string', 'enum' => [ 'SECURE_MQTT', 'MQTT_WSS', 'HTTPS', 'DEFAULT', ], ], 'ApproximateSecondsBeforeTimedOut' => [ 'type' => 'long', ], 'AscendingOrder' => [ 'type' => 'boolean', ], 'AssetId' => [ 'type' => 'string', ], 'AssetPropertyAlias' => [ 'type' => 'string', 'min' => 1, ], 'AssetPropertyBooleanValue' => [ 'type' => 'string', ], 'AssetPropertyDoubleValue' => [ 'type' => 'string', ], 'AssetPropertyEntryId' => [ 'type' => 'string', ], 'AssetPropertyId' => [ 'type' => 'string', ], 'AssetPropertyIntegerValue' => [ 'type' => 'string', ], 'AssetPropertyOffsetInNanos' => [ 'type' => 'string', ], 'AssetPropertyQuality' => [ 'type' => 'string', ], 'AssetPropertyStringValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'AssetPropertyTimeInSeconds' => [ 'type' => 'string', ], 'AssetPropertyTimestamp' => [ 'type' => 'structure', 'required' => [ 'timeInSeconds', ], 'members' => [ 'timeInSeconds' => [ 'shape' => 'AssetPropertyTimeInSeconds', ], 'offsetInNanos' => [ 'shape' => 'AssetPropertyOffsetInNanos', ], ], ], 'AssetPropertyValue' => [ 'type' => 'structure', 'required' => [ 'value', 'timestamp', ], 'members' => [ 'value' => [ 'shape' => 'AssetPropertyVariant', ], 'timestamp' => [ 'shape' => 'AssetPropertyTimestamp', ], 'quality' => [ 'shape' => 'AssetPropertyQuality', ], ], ], 'AssetPropertyValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetPropertyValue', ], 'min' => 1, ], 'AssetPropertyVariant' => [ 'type' => 'structure', 'members' => [ 'stringValue' => [ 'shape' => 'AssetPropertyStringValue', ], 'integerValue' => [ 'shape' => 'AssetPropertyIntegerValue', ], 'doubleValue' => [ 'shape' => 'AssetPropertyDoubleValue', ], 'booleanValue' => [ 'shape' => 'AssetPropertyBooleanValue', ], ], ], 'AssociateSbomWithPackageVersionRequest' => [ 'type' => 'structure', 'required' => [ 'packageName', 'versionName', 'sbom', ], 'members' => [ 'packageName' => [ 'shape' => 'PackageName', 'location' => 'uri', 'locationName' => 'packageName', ], 'versionName' => [ 'shape' => 'VersionName', 'location' => 'uri', 'locationName' => 'versionName', ], 'sbom' => [ 'shape' => 'Sbom', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'AssociateSbomWithPackageVersionResponse' => [ 'type' => 'structure', 'members' => [ 'packageName' => [ 'shape' => 'PackageName', ], 'versionName' => [ 'shape' => 'VersionName', ], 'sbom' => [ 'shape' => 'Sbom', ], 'sbomValidationStatus' => [ 'shape' => 'SbomValidationStatus', ], ], ], 'AssociateTargetsWithJobRequest' => [ 'type' => 'structure', 'required' => [ 'targets', 'jobId', ], 'members' => [ 'targets' => [ 'shape' => 'JobTargets', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'comment' => [ 'shape' => 'Comment', ], 'namespaceId' => [ 'shape' => 'NamespaceId', 'location' => 'querystring', 'locationName' => 'namespaceId', ], ], ], 'AssociateTargetsWithJobResponse' => [ 'type' => 'structure', 'members' => [ 'jobArn' => [ 'shape' => 'JobArn', ], 'jobId' => [ 'shape' => 'JobId', ], 'description' => [ 'shape' => 'JobDescription', ], ], ], 'AttachPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', 'target', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'policyName', ], 'target' => [ 'shape' => 'PolicyTarget', ], ], ], 'AttachPrincipalPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', 'principal', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'policyName', ], 'principal' => [ 'shape' => 'Principal', 'location' => 'header', 'locationName' => 'x-amzn-iot-principal', ], ], ], 'AttachSecurityProfileRequest' => [ 'type' => 'structure', 'required' => [ 'securityProfileName', 'securityProfileTargetArn', ], 'members' => [ 'securityProfileName' => [ 'shape' => 'SecurityProfileName', 'location' => 'uri', 'locationName' => 'securityProfileName', ], 'securityProfileTargetArn' => [ 'shape' => 'SecurityProfileTargetArn', 'location' => 'querystring', 'locationName' => 'securityProfileTargetArn', ], ], ], 'AttachSecurityProfileResponse' => [ 'type' => 'structure', 'members' => [], ], 'AttachThingPrincipalRequest' => [ 'type' => 'structure', 'required' => [ 'thingName', 'principal', ], 'members' => [ 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], 'principal' => [ 'shape' => 'Principal', 'location' => 'header', 'locationName' => 'x-amzn-principal', ], 'thingPrincipalType' => [ 'shape' => 'ThingPrincipalType', 'location' => 'querystring', 'locationName' => 'thingPrincipalType', ], ], ], 'AttachThingPrincipalResponse' => [ 'type' => 'structure', 'members' => [], ], 'AttributeKey' => [ 'type' => 'string', ], 'AttributeName' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[a-zA-Z0-9_.,@/:#-]+', ], 'AttributePayload' => [ 'type' => 'structure', 'members' => [ 'attributes' => [ 'shape' => 'Attributes', ], 'merge' => [ 'shape' => 'Flag', ], ], ], 'AttributeValue' => [ 'type' => 'string', 'max' => 800, 'pattern' => '[a-zA-Z0-9_.,@/:#=\\[\\]-]*', ], 'Attributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeName', ], 'value' => [ 'shape' => 'AttributeValue', ], ], 'AttributesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeKey', ], 'value' => [ 'shape' => 'Value', ], ], 'AuditCheckConfiguration' => [ 'type' => 'structure', 'members' => [ 'enabled' => [ 'shape' => 'Enabled', ], 'configuration' => [ 'shape' => 'CheckCustomConfiguration', ], ], ], 'AuditCheckConfigurations' => [ 'type' => 'map', 'key' => [ 'shape' => 'AuditCheckName', ], 'value' => [ 'shape' => 'AuditCheckConfiguration', ], ], 'AuditCheckDetails' => [ 'type' => 'structure', 'members' => [ 'checkRunStatus' => [ 'shape' => 'AuditCheckRunStatus', ], 'checkCompliant' => [ 'shape' => 'CheckCompliant', ], 'totalResourcesCount' => [ 'shape' => 'TotalResourcesCount', ], 'nonCompliantResourcesCount' => [ 'shape' => 'NonCompliantResourcesCount', ], 'suppressedNonCompliantResourcesCount' => [ 'shape' => 'SuppressedNonCompliantResourcesCount', ], 'errorCode' => [ 'shape' => 'ErrorCode', ], 'message' => [ 'shape' => 'ErrorMessage', ], ], ], 'AuditCheckName' => [ 'type' => 'string', ], 'AuditCheckRunStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'WAITING_FOR_DATA_COLLECTION', 'CANCELED', 'COMPLETED_COMPLIANT', 'COMPLETED_NON_COMPLIANT', 'FAILED', ], ], 'AuditCheckToActionsMapping' => [ 'type' => 'map', 'key' => [ 'shape' => 'AuditCheckName', ], 'value' => [ 'shape' => 'MitigationActionNameList', ], ], 'AuditCheckToReasonCodeFilter' => [ 'type' => 'map', 'key' => [ 'shape' => 'AuditCheckName', ], 'value' => [ 'shape' => 'ReasonForNonComplianceCodes', ], ], 'AuditDescription' => [ 'type' => 'string', 'max' => 1000, 'pattern' => '[\\p{Graph}\\x20]*', ], 'AuditDetails' => [ 'type' => 'map', 'key' => [ 'shape' => 'AuditCheckName', ], 'value' => [ 'shape' => 'AuditCheckDetails', ], ], 'AuditFinding' => [ 'type' => 'structure', 'members' => [ 'findingId' => [ 'shape' => 'FindingId', ], 'taskId' => [ 'shape' => 'AuditTaskId', ], 'checkName' => [ 'shape' => 'AuditCheckName', ], 'taskStartTime' => [ 'shape' => 'Timestamp', ], 'findingTime' => [ 'shape' => 'Timestamp', ], 'severity' => [ 'shape' => 'AuditFindingSeverity', ], 'nonCompliantResource' => [ 'shape' => 'NonCompliantResource', ], 'relatedResources' => [ 'shape' => 'RelatedResources', ], 'reasonForNonCompliance' => [ 'shape' => 'ReasonForNonCompliance', ], 'reasonForNonComplianceCode' => [ 'shape' => 'ReasonForNonComplianceCode', ], 'isSuppressed' => [ 'shape' => 'IsSuppressed', ], ], ], 'AuditFindingSeverity' => [ 'type' => 'string', 'enum' => [ 'CRITICAL', 'HIGH', 'MEDIUM', 'LOW', ], ], 'AuditFindings' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuditFinding', ], ], 'AuditFrequency' => [ 'type' => 'string', 'enum' => [ 'DAILY', 'WEEKLY', 'BIWEEKLY', 'MONTHLY', ], ], 'AuditMitigationActionExecutionMetadata' => [ 'type' => 'structure', 'members' => [ 'taskId' => [ 'shape' => 'MitigationActionsTaskId', ], 'findingId' => [ 'shape' => 'FindingId', ], 'actionName' => [ 'shape' => 'MitigationActionName', ], 'actionId' => [ 'shape' => 'MitigationActionId', ], 'status' => [ 'shape' => 'AuditMitigationActionsExecutionStatus', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'errorCode' => [ 'shape' => 'ErrorCode', ], 'message' => [ 'shape' => 'ErrorMessage', ], ], ], 'AuditMitigationActionExecutionMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuditMitigationActionExecutionMetadata', ], ], 'AuditMitigationActionsExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'CANCELED', 'SKIPPED', 'PENDING', ], ], 'AuditMitigationActionsTaskMetadata' => [ 'type' => 'structure', 'members' => [ 'taskId' => [ 'shape' => 'MitigationActionsTaskId', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'taskStatus' => [ 'shape' => 'AuditMitigationActionsTaskStatus', ], ], ], 'AuditMitigationActionsTaskMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuditMitigationActionsTaskMetadata', ], ], 'AuditMitigationActionsTaskStatistics' => [ 'type' => 'map', 'key' => [ 'shape' => 'AuditCheckName', ], 'value' => [ 'shape' => 'TaskStatisticsForAuditCheck', ], ], 'AuditMitigationActionsTaskStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'CANCELED', ], ], 'AuditMitigationActionsTaskTarget' => [ 'type' => 'structure', 'members' => [ 'auditTaskId' => [ 'shape' => 'AuditTaskId', ], 'findingIds' => [ 'shape' => 'FindingIds', ], 'auditCheckToReasonCodeFilter' => [ 'shape' => 'AuditCheckToReasonCodeFilter', ], ], ], 'AuditNotificationTarget' => [ 'type' => 'structure', 'members' => [ 'targetArn' => [ 'shape' => 'TargetArn', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'enabled' => [ 'shape' => 'Enabled', ], ], ], 'AuditNotificationTargetConfigurations' => [ 'type' => 'map', 'key' => [ 'shape' => 'AuditNotificationType', ], 'value' => [ 'shape' => 'AuditNotificationTarget', ], ], 'AuditNotificationType' => [ 'type' => 'string', 'enum' => [ 'SNS', ], ], 'AuditSuppression' => [ 'type' => 'structure', 'required' => [ 'checkName', 'resourceIdentifier', ], 'members' => [ 'checkName' => [ 'shape' => 'AuditCheckName', ], 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'expirationDate' => [ 'shape' => 'Timestamp', ], 'suppressIndefinitely' => [ 'shape' => 'SuppressIndefinitely', ], 'description' => [ 'shape' => 'AuditDescription', ], ], ], 'AuditSuppressionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuditSuppression', ], ], 'AuditTaskId' => [ 'type' => 'string', 'max' => 40, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\-]+', ], 'AuditTaskMetadata' => [ 'type' => 'structure', 'members' => [ 'taskId' => [ 'shape' => 'AuditTaskId', ], 'taskStatus' => [ 'shape' => 'AuditTaskStatus', ], 'taskType' => [ 'shape' => 'AuditTaskType', ], ], ], 'AuditTaskMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuditTaskMetadata', ], ], 'AuditTaskStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'CANCELED', ], ], 'AuditTaskType' => [ 'type' => 'string', 'enum' => [ 'ON_DEMAND_AUDIT_TASK', 'SCHEDULED_AUDIT_TASK', ], ], 'AuthDecision' => [ 'type' => 'string', 'enum' => [ 'ALLOWED', 'EXPLICIT_DENY', 'IMPLICIT_DENY', ], ], 'AuthInfo' => [ 'type' => 'structure', 'required' => [ 'resources', ], 'members' => [ 'actionType' => [ 'shape' => 'ActionType', ], 'resources' => [ 'shape' => 'Resources', ], ], ], 'AuthInfos' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuthInfo', ], 'max' => 10, 'min' => 1, ], 'AuthResult' => [ 'type' => 'structure', 'members' => [ 'authInfo' => [ 'shape' => 'AuthInfo', ], 'allowed' => [ 'shape' => 'Allowed', ], 'denied' => [ 'shape' => 'Denied', ], 'authDecision' => [ 'shape' => 'AuthDecision', ], 'missingContextValues' => [ 'shape' => 'MissingContextValues', ], ], ], 'AuthResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuthResult', ], ], 'AuthenticationType' => [ 'type' => 'string', 'enum' => [ 'CUSTOM_AUTH_X509', 'CUSTOM_AUTH', 'AWS_X509', 'AWS_SIGV4', 'DEFAULT', ], ], 'AuthorizerArn' => [ 'type' => 'string', 'max' => 2048, ], 'AuthorizerConfig' => [ 'type' => 'structure', 'members' => [ 'defaultAuthorizerName' => [ 'shape' => 'AuthorizerName', ], 'allowAuthorizerOverride' => [ 'shape' => 'AllowAuthorizerOverride', 'box' => true, ], ], ], 'AuthorizerDescription' => [ 'type' => 'structure', 'members' => [ 'authorizerName' => [ 'shape' => 'AuthorizerName', ], 'authorizerArn' => [ 'shape' => 'AuthorizerArn', ], 'authorizerFunctionArn' => [ 'shape' => 'AuthorizerFunctionArn', ], 'tokenKeyName' => [ 'shape' => 'TokenKeyName', ], 'tokenSigningPublicKeys' => [ 'shape' => 'PublicKeyMap', ], 'status' => [ 'shape' => 'AuthorizerStatus', ], 'creationDate' => [ 'shape' => 'DateType', ], 'lastModifiedDate' => [ 'shape' => 'DateType', ], 'signingDisabled' => [ 'shape' => 'BooleanKey', ], 'enableCachingForHttp' => [ 'shape' => 'EnableCachingForHttp', ], ], ], 'AuthorizerFunctionArn' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '[\\s\\S]*', ], 'AuthorizerName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\w=,@-]+', ], 'AuthorizerStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'AuthorizerSummary' => [ 'type' => 'structure', 'members' => [ 'authorizerName' => [ 'shape' => 'AuthorizerName', ], 'authorizerArn' => [ 'shape' => 'AuthorizerArn', ], ], ], 'Authorizers' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuthorizerSummary', ], ], 'AutoRegistrationStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLE', 'DISABLE', ], ], 'Average' => [ 'type' => 'double', ], 'AwsAccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '[0-9]+', ], 'AwsArn' => [ 'type' => 'string', ], 'AwsIotJobArn' => [ 'type' => 'string', ], 'AwsIotJobId' => [ 'type' => 'string', ], 'AwsIotSqlVersion' => [ 'type' => 'string', ], 'AwsJobAbortConfig' => [ 'type' => 'structure', 'required' => [ 'abortCriteriaList', ], 'members' => [ 'abortCriteriaList' => [ 'shape' => 'AwsJobAbortCriteriaList', ], ], ], 'AwsJobAbortCriteria' => [ 'type' => 'structure', 'required' => [ 'failureType', 'action', 'thresholdPercentage', 'minNumberOfExecutedThings', ], 'members' => [ 'failureType' => [ 'shape' => 'AwsJobAbortCriteriaFailureType', ], 'action' => [ 'shape' => 'AwsJobAbortCriteriaAbortAction', ], 'thresholdPercentage' => [ 'shape' => 'AwsJobAbortCriteriaAbortThresholdPercentage', ], 'minNumberOfExecutedThings' => [ 'shape' => 'AwsJobAbortCriteriaMinimumNumberOfExecutedThings', ], ], ], 'AwsJobAbortCriteriaAbortAction' => [ 'type' => 'string', 'enum' => [ 'CANCEL', ], ], 'AwsJobAbortCriteriaAbortThresholdPercentage' => [ 'type' => 'double', 'max' => 100, ], 'AwsJobAbortCriteriaFailureType' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'REJECTED', 'TIMED_OUT', 'ALL', ], ], 'AwsJobAbortCriteriaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsJobAbortCriteria', ], 'min' => 1, ], 'AwsJobAbortCriteriaMinimumNumberOfExecutedThings' => [ 'type' => 'integer', 'min' => 1, ], 'AwsJobExecutionsRolloutConfig' => [ 'type' => 'structure', 'members' => [ 'maximumPerMinute' => [ 'shape' => 'MaximumPerMinute', ], 'exponentialRate' => [ 'shape' => 'AwsJobExponentialRolloutRate', ], ], ], 'AwsJobExponentialRolloutRate' => [ 'type' => 'structure', 'required' => [ 'baseRatePerMinute', 'incrementFactor', 'rateIncreaseCriteria', ], 'members' => [ 'baseRatePerMinute' => [ 'shape' => 'AwsJobRolloutRatePerMinute', ], 'incrementFactor' => [ 'shape' => 'AwsJobRolloutIncrementFactor', ], 'rateIncreaseCriteria' => [ 'shape' => 'AwsJobRateIncreaseCriteria', ], ], ], 'AwsJobPresignedUrlConfig' => [ 'type' => 'structure', 'members' => [ 'expiresInSec' => [ 'shape' => 'ExpiresInSeconds', ], ], ], 'AwsJobRateIncreaseCriteria' => [ 'type' => 'structure', 'members' => [ 'numberOfNotifiedThings' => [ 'shape' => 'AwsJobRateIncreaseCriteriaNumberOfThings', ], 'numberOfSucceededThings' => [ 'shape' => 'AwsJobRateIncreaseCriteriaNumberOfThings', ], ], ], 'AwsJobRateIncreaseCriteriaNumberOfThings' => [ 'type' => 'integer', 'min' => 1, ], 'AwsJobRolloutIncrementFactor' => [ 'type' => 'double', ], 'AwsJobRolloutRatePerMinute' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'AwsJobTimeoutConfig' => [ 'type' => 'structure', 'members' => [ 'inProgressTimeoutInMinutes' => [ 'shape' => 'AwsJobTimeoutInProgressTimeoutInMinutes', ], ], ], 'AwsJobTimeoutInProgressTimeoutInMinutes' => [ 'type' => 'long', ], 'BatchMode' => [ 'type' => 'boolean', ], 'BeforeSubstitutionFlag' => [ 'type' => 'boolean', ], 'Behavior' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'BehaviorName', ], 'metric' => [ 'shape' => 'BehaviorMetric', ], 'metricDimension' => [ 'shape' => 'MetricDimension', ], 'criteria' => [ 'shape' => 'BehaviorCriteria', ], 'suppressAlerts' => [ 'shape' => 'SuppressAlerts', ], 'exportMetric' => [ 'shape' => 'ExportMetric', ], ], ], 'BehaviorCriteria' => [ 'type' => 'structure', 'members' => [ 'comparisonOperator' => [ 'shape' => 'ComparisonOperator', ], 'value' => [ 'shape' => 'MetricValue', ], 'durationSeconds' => [ 'shape' => 'DurationSeconds', ], 'consecutiveDatapointsToAlarm' => [ 'shape' => 'ConsecutiveDatapointsToAlarm', ], 'consecutiveDatapointsToClear' => [ 'shape' => 'ConsecutiveDatapointsToClear', ], 'statisticalThreshold' => [ 'shape' => 'StatisticalThreshold', ], 'mlDetectionConfig' => [ 'shape' => 'MachineLearningDetectionConfig', ], ], ], 'BehaviorCriteriaType' => [ 'type' => 'string', 'enum' => [ 'STATIC', 'STATISTICAL', 'MACHINE_LEARNING', ], ], 'BehaviorMetric' => [ 'type' => 'string', ], 'BehaviorModelTrainingSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BehaviorModelTrainingSummary', ], ], 'BehaviorModelTrainingSummary' => [ 'type' => 'structure', 'members' => [ 'securityProfileName' => [ 'shape' => 'SecurityProfileName', ], 'behaviorName' => [ 'shape' => 'BehaviorName', ], 'trainingDataCollectionStartDate' => [ 'shape' => 'Timestamp', ], 'modelStatus' => [ 'shape' => 'ModelStatus', ], 'datapointsCollectionPercentage' => [ 'shape' => 'DataCollectionPercentage', ], 'lastModelRefreshDate' => [ 'shape' => 'Timestamp', ], ], ], 'BehaviorName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9:_-]+', ], 'Behaviors' => [ 'type' => 'list', 'member' => [ 'shape' => 'Behavior', ], 'max' => 100, ], 'BillingGroupArn' => [ 'type' => 'string', ], 'BillingGroupDescription' => [ 'type' => 'string', 'max' => 2028, 'pattern' => '[\\p{Graph}\\x20]*', ], 'BillingGroupId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\-]+', ], 'BillingGroupMetadata' => [ 'type' => 'structure', 'members' => [ 'creationDate' => [ 'shape' => 'CreationDate', ], ], ], 'BillingGroupName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9:_-]+', ], 'BillingGroupNameAndArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupNameAndArn', ], ], 'BillingGroupProperties' => [ 'type' => 'structure', 'members' => [ 'billingGroupDescription' => [ 'shape' => 'BillingGroupDescription', ], ], ], 'BinaryCommandExecutionResult' => [ 'type' => 'blob', 'min' => 1, ], 'BinaryParameterValue' => [ 'type' => 'blob', 'min' => 1, ], 'Boolean' => [ 'type' => 'boolean', ], 'BooleanCommandExecutionResult' => [ 'type' => 'boolean', ], 'BooleanKey' => [ 'type' => 'boolean', ], 'BooleanParameterValue' => [ 'type' => 'boolean', ], 'BooleanWrapperObject' => [ 'type' => 'boolean', ], 'Bucket' => [ 'type' => 'structure', 'members' => [ 'keyValue' => [ 'shape' => 'BucketKeyValue', ], 'count' => [ 'shape' => 'Count', ], ], ], 'BucketKeyValue' => [ 'type' => 'string', ], 'BucketName' => [ 'type' => 'string', ], 'Buckets' => [ 'type' => 'list', 'member' => [ 'shape' => 'Bucket', ], ], 'BucketsAggregationType' => [ 'type' => 'structure', 'members' => [ 'termsAggregation' => [ 'shape' => 'TermsAggregation', ], ], ], 'CACertificate' => [ 'type' => 'structure', 'members' => [ 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'certificateId' => [ 'shape' => 'CertificateId', ], 'status' => [ 'shape' => 'CACertificateStatus', ], 'creationDate' => [ 'shape' => 'DateType', ], ], ], 'CACertificateDescription' => [ 'type' => 'structure', 'members' => [ 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'certificateId' => [ 'shape' => 'CertificateId', ], 'status' => [ 'shape' => 'CACertificateStatus', ], 'certificatePem' => [ 'shape' => 'CertificatePem', ], 'ownedBy' => [ 'shape' => 'AwsAccountId', ], 'creationDate' => [ 'shape' => 'DateType', ], 'autoRegistrationStatus' => [ 'shape' => 'AutoRegistrationStatus', ], 'lastModifiedDate' => [ 'shape' => 'DateType', ], 'customerVersion' => [ 'shape' => 'CustomerVersion', ], 'generationId' => [ 'shape' => 'GenerationId', ], 'validity' => [ 'shape' => 'CertificateValidity', ], 'certificateMode' => [ 'shape' => 'CertificateMode', ], ], ], 'CACertificateStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'CACertificateUpdateAction' => [ 'type' => 'string', 'enum' => [ 'DEACTIVATE', ], ], 'CACertificates' => [ 'type' => 'list', 'member' => [ 'shape' => 'CACertificate', ], ], 'CancelAuditMitigationActionsTaskRequest' => [ 'type' => 'structure', 'required' => [ 'taskId', ], 'members' => [ 'taskId' => [ 'shape' => 'MitigationActionsTaskId', 'location' => 'uri', 'locationName' => 'taskId', ], ], ], 'CancelAuditMitigationActionsTaskResponse' => [ 'type' => 'structure', 'members' => [], ], 'CancelAuditTaskRequest' => [ 'type' => 'structure', 'required' => [ 'taskId', ], 'members' => [ 'taskId' => [ 'shape' => 'AuditTaskId', 'location' => 'uri', 'locationName' => 'taskId', ], ], ], 'CancelAuditTaskResponse' => [ 'type' => 'structure', 'members' => [], ], 'CancelCertificateTransferRequest' => [ 'type' => 'structure', 'required' => [ 'certificateId', ], 'members' => [ 'certificateId' => [ 'shape' => 'CertificateId', 'location' => 'uri', 'locationName' => 'certificateId', ], ], ], 'CancelDetectMitigationActionsTaskRequest' => [ 'type' => 'structure', 'required' => [ 'taskId', ], 'members' => [ 'taskId' => [ 'shape' => 'MitigationActionsTaskId', 'location' => 'uri', 'locationName' => 'taskId', ], ], ], 'CancelDetectMitigationActionsTaskResponse' => [ 'type' => 'structure', 'members' => [], ], 'CancelJobExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', 'thingName', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], 'force' => [ 'shape' => 'ForceFlag', 'location' => 'querystring', 'locationName' => 'force', ], 'expectedVersion' => [ 'shape' => 'ExpectedVersion', ], 'statusDetails' => [ 'shape' => 'DetailsMap', ], ], ], 'CancelJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'reasonCode' => [ 'shape' => 'ReasonCode', ], 'comment' => [ 'shape' => 'Comment', ], 'force' => [ 'shape' => 'ForceFlag', 'location' => 'querystring', 'locationName' => 'force', ], ], ], 'CancelJobResponse' => [ 'type' => 'structure', 'members' => [ 'jobArn' => [ 'shape' => 'JobArn', ], 'jobId' => [ 'shape' => 'JobId', ], 'description' => [ 'shape' => 'JobDescription', ], ], ], 'CanceledChecksCount' => [ 'type' => 'integer', ], 'CanceledFindingsCount' => [ 'type' => 'long', ], 'CanceledThings' => [ 'type' => 'integer', ], 'CannedAccessControlList' => [ 'type' => 'string', 'enum' => [ 'private', 'public-read', 'public-read-write', 'aws-exec-read', 'authenticated-read', 'bucket-owner-read', 'bucket-owner-full-control', 'log-delivery-write', ], ], 'Certificate' => [ 'type' => 'structure', 'members' => [ 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'certificateId' => [ 'shape' => 'CertificateId', ], 'status' => [ 'shape' => 'CertificateStatus', ], 'certificateMode' => [ 'shape' => 'CertificateMode', ], 'creationDate' => [ 'shape' => 'DateType', ], ], ], 'CertificateArn' => [ 'type' => 'string', ], 'CertificateConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'CertificateDescription' => [ 'type' => 'structure', 'members' => [ 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'certificateId' => [ 'shape' => 'CertificateId', ], 'caCertificateId' => [ 'shape' => 'CertificateId', ], 'status' => [ 'shape' => 'CertificateStatus', ], 'certificatePem' => [ 'shape' => 'CertificatePem', ], 'ownedBy' => [ 'shape' => 'AwsAccountId', ], 'previousOwnedBy' => [ 'shape' => 'AwsAccountId', ], 'creationDate' => [ 'shape' => 'DateType', ], 'lastModifiedDate' => [ 'shape' => 'DateType', ], 'customerVersion' => [ 'shape' => 'CustomerVersion', ], 'transferData' => [ 'shape' => 'TransferData', ], 'generationId' => [ 'shape' => 'GenerationId', ], 'validity' => [ 'shape' => 'CertificateValidity', ], 'certificateMode' => [ 'shape' => 'CertificateMode', ], ], ], 'CertificateId' => [ 'type' => 'string', 'max' => 64, 'min' => 64, 'pattern' => '(0x)?[a-fA-F0-9]+', ], 'CertificateMode' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'SNI_ONLY', ], ], 'CertificateName' => [ 'type' => 'string', ], 'CertificatePathOnDevice' => [ 'type' => 'string', ], 'CertificatePem' => [ 'type' => 'string', 'max' => 65536, 'min' => 1, 'pattern' => '[\\s\\S]*', ], 'CertificateProviderAccountDefaultForOperations' => [ 'type' => 'list', 'member' => [ 'shape' => 'CertificateProviderOperation', ], 'max' => 1, 'min' => 1, ], 'CertificateProviderArn' => [ 'type' => 'string', 'max' => 2048, ], 'CertificateProviderFunctionArn' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '[\\s\\S]*', ], 'CertificateProviderName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\w=,@-]+', ], 'CertificateProviderOperation' => [ 'type' => 'string', 'enum' => [ 'CreateCertificateFromCsr', ], ], 'CertificateProviderSummary' => [ 'type' => 'structure', 'members' => [ 'certificateProviderName' => [ 'shape' => 'CertificateProviderName', ], 'certificateProviderArn' => [ 'shape' => 'CertificateProviderArn', ], ], ], 'CertificateProviders' => [ 'type' => 'list', 'member' => [ 'shape' => 'CertificateProviderSummary', ], ], 'CertificateSigningRequest' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '[\\s\\S]*', ], 'CertificateStateException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 406, ], 'exception' => true, ], 'CertificateStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', 'REVOKED', 'PENDING_TRANSFER', 'REGISTER_INACTIVE', 'PENDING_ACTIVATION', ], ], 'CertificateValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'CertificateValidity' => [ 'type' => 'structure', 'members' => [ 'notBefore' => [ 'shape' => 'DateType', ], 'notAfter' => [ 'shape' => 'DateType', ], ], ], 'Certificates' => [ 'type' => 'list', 'member' => [ 'shape' => 'Certificate', ], ], 'ChannelName' => [ 'type' => 'string', ], 'CheckCompliant' => [ 'type' => 'boolean', ], 'CheckCustomConfiguration' => [ 'type' => 'map', 'key' => [ 'shape' => 'ConfigName', ], 'value' => [ 'shape' => 'ConfigValue', ], ], 'Cidr' => [ 'type' => 'string', 'max' => 43, 'min' => 2, 'pattern' => '[a-fA-F0-9:\\.\\/]+', ], 'Cidrs' => [ 'type' => 'list', 'member' => [ 'shape' => 'Cidr', ], ], 'ClearDefaultAuthorizerRequest' => [ 'type' => 'structure', 'members' => [], ], 'ClearDefaultAuthorizerResponse' => [ 'type' => 'structure', 'members' => [], ], 'ClientCertificateCallbackArn' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '[\\s\\S]*', ], 'ClientCertificateConfig' => [ 'type' => 'structure', 'members' => [ 'clientCertificateCallbackArn' => [ 'shape' => 'ClientCertificateCallbackArn', ], ], ], 'ClientId' => [ 'type' => 'string', ], 'ClientProperties' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'ClientRequestToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-_]+$', ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 36, 'pattern' => '\\S{36,64}', ], 'CloudwatchAlarmAction' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'alarmName', 'stateReason', 'stateValue', ], 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'alarmName' => [ 'shape' => 'AlarmName', ], 'stateReason' => [ 'shape' => 'StateReason', ], 'stateValue' => [ 'shape' => 'StateValue', ], ], ], 'CloudwatchLogsAction' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'logGroupName', ], 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'batchMode' => [ 'shape' => 'BatchMode', ], ], ], 'CloudwatchMetricAction' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'metricNamespace', 'metricName', 'metricValue', 'metricUnit', ], 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'metricNamespace' => [ 'shape' => 'String', ], 'metricName' => [ 'shape' => 'String', ], 'metricValue' => [ 'shape' => 'String', ], 'metricUnit' => [ 'shape' => 'String', ], 'metricTimestamp' => [ 'shape' => 'String', ], ], ], 'Code' => [ 'type' => 'string', ], 'CodeSigning' => [ 'type' => 'structure', 'members' => [ 'awsSignerJobId' => [ 'shape' => 'SigningJobId', ], 'startSigningJobParameter' => [ 'shape' => 'StartSigningJobParameter', ], 'customCodeSigning' => [ 'shape' => 'CustomCodeSigning', ], ], ], 'CodeSigningCertificateChain' => [ 'type' => 'structure', 'members' => [ 'certificateName' => [ 'shape' => 'CertificateName', ], 'inlineDocument' => [ 'shape' => 'InlineDocument', ], ], ], 'CodeSigningSignature' => [ 'type' => 'structure', 'members' => [ 'inlineDocument' => [ 'shape' => 'Signature', ], ], ], 'CognitoIdentityPoolId' => [ 'type' => 'string', ], 'CommandArn' => [ 'type' => 'string', ], 'CommandDescription' => [ 'type' => 'string', 'max' => 2028, 'pattern' => '[^\\p{C}]*', ], 'CommandExecutionId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'CommandExecutionParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'CommandParameterName', ], 'value' => [ 'shape' => 'CommandParameterValue', ], 'min' => 1, ], 'CommandExecutionResult' => [ 'type' => 'structure', 'members' => [ 'S' => [ 'shape' => 'StringCommandExecutionResult', ], 'B' => [ 'shape' => 'BooleanCommandExecutionResult', ], 'BIN' => [ 'shape' => 'BinaryCommandExecutionResult', ], ], ], 'CommandExecutionResultMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'CommandExecutionResultName', ], 'value' => [ 'shape' => 'CommandExecutionResult', ], 'min' => 1, ], 'CommandExecutionResultName' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'CommandExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'IN_PROGRESS', 'SUCCEEDED', 'FAILED', 'REJECTED', 'TIMED_OUT', ], ], 'CommandExecutionSummary' => [ 'type' => 'structure', 'members' => [ 'commandArn' => [ 'shape' => 'CommandArn', ], 'executionId' => [ 'shape' => 'CommandExecutionId', ], 'targetArn' => [ 'shape' => 'TargetArn', ], 'status' => [ 'shape' => 'CommandExecutionStatus', ], 'createdAt' => [ 'shape' => 'DateType', ], 'startedAt' => [ 'shape' => 'DateType', ], 'completedAt' => [ 'shape' => 'DateType', ], ], ], 'CommandExecutionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CommandExecutionSummary', ], ], 'CommandExecutionTimeoutInSeconds' => [ 'type' => 'long', 'min' => 1, ], 'CommandId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'CommandMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'CommandNamespace' => [ 'type' => 'string', 'enum' => [ 'AWS-IoT', 'AWS-IoT-FleetWise', ], ], 'CommandParameter' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'CommandParameterName', ], 'value' => [ 'shape' => 'CommandParameterValue', ], 'defaultValue' => [ 'shape' => 'CommandParameterValue', ], 'description' => [ 'shape' => 'CommandParameterDescription', ], ], ], 'CommandParameterDescription' => [ 'type' => 'string', 'max' => 2028, 'pattern' => '[^\\p{C}]*', ], 'CommandParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CommandParameter', ], 'min' => 1, ], 'CommandParameterName' => [ 'type' => 'string', 'max' => 192, 'min' => 1, 'pattern' => '^[.$a-zA-Z0-9_-]+$', ], 'CommandParameterValue' => [ 'type' => 'structure', 'members' => [ 'S' => [ 'shape' => 'StringParameterValue', ], 'B' => [ 'shape' => 'BooleanParameterValue', ], 'I' => [ 'shape' => 'IntegerParameterValue', ], 'L' => [ 'shape' => 'LongParameterValue', ], 'D' => [ 'shape' => 'DoubleParameterValue', ], 'BIN' => [ 'shape' => 'BinaryParameterValue', ], 'UL' => [ 'shape' => 'UnsignedLongParameterValue', ], ], ], 'CommandPayload' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'CommandPayloadBlob', ], 'contentType' => [ 'shape' => 'MimeType', ], ], ], 'CommandPayloadBlob' => [ 'type' => 'blob', ], 'CommandSummary' => [ 'type' => 'structure', 'members' => [ 'commandArn' => [ 'shape' => 'CommandArn', ], 'commandId' => [ 'shape' => 'CommandId', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'deprecated' => [ 'shape' => 'DeprecationFlag', ], 'createdAt' => [ 'shape' => 'DateType', ], 'lastUpdatedAt' => [ 'shape' => 'DateType', ], 'pendingDeletion' => [ 'shape' => 'BooleanWrapperObject', ], ], ], 'CommandSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CommandSummary', ], ], 'Comment' => [ 'type' => 'string', 'max' => 2028, 'pattern' => '[^\\p{C}]+', ], 'ComparisonOperator' => [ 'type' => 'string', 'enum' => [ 'less-than', 'less-than-equals', 'greater-than', 'greater-than-equals', 'in-cidr-set', 'not-in-cidr-set', 'in-port-set', 'not-in-port-set', 'in-set', 'not-in-set', ], ], 'CompliantChecksCount' => [ 'type' => 'integer', ], 'ConfidenceLevel' => [ 'type' => 'string', 'enum' => [ 'LOW', 'MEDIUM', 'HIGH', ], ], 'ConfigName' => [ 'type' => 'string', 'enum' => [ 'CERT_AGE_THRESHOLD_IN_DAYS', 'CERT_EXPIRATION_THRESHOLD_IN_DAYS', ], ], 'ConfigValue' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'Configuration' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Enabled', ], ], ], 'ConfirmTopicRuleDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'confirmationToken', ], 'members' => [ 'confirmationToken' => [ 'shape' => 'ConfirmationToken', 'location' => 'uri', 'locationName' => 'confirmationToken', ], ], ], 'ConfirmTopicRuleDestinationResponse' => [ 'type' => 'structure', 'members' => [], ], 'ConfirmationToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], 'resourceId' => [ 'shape' => 'resourceId', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ConflictingResourceUpdateException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ConnectionAttributeName' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[a-zA-Z0-9:.]+', ], 'ConnectivityApiThingName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9:_-]+', 'sensitive' => true, ], 'ConnectivityTimestamp' => [ 'type' => 'long', ], 'ConsecutiveDatapointsToAlarm' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'ConsecutiveDatapointsToClear' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'ContentType' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'CorrelationData' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'Count' => [ 'type' => 'integer', ], 'CreateAuditSuppressionRequest' => [ 'type' => 'structure', 'required' => [ 'checkName', 'resourceIdentifier', 'clientRequestToken', ], 'members' => [ 'checkName' => [ 'shape' => 'AuditCheckName', ], 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'expirationDate' => [ 'shape' => 'Timestamp', ], 'suppressIndefinitely' => [ 'shape' => 'SuppressIndefinitely', ], 'description' => [ 'shape' => 'AuditDescription', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'CreateAuditSuppressionResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateAuthorizerRequest' => [ 'type' => 'structure', 'required' => [ 'authorizerName', 'authorizerFunctionArn', ], 'members' => [ 'authorizerName' => [ 'shape' => 'AuthorizerName', 'location' => 'uri', 'locationName' => 'authorizerName', ], 'authorizerFunctionArn' => [ 'shape' => 'AuthorizerFunctionArn', ], 'tokenKeyName' => [ 'shape' => 'TokenKeyName', ], 'tokenSigningPublicKeys' => [ 'shape' => 'PublicKeyMap', ], 'status' => [ 'shape' => 'AuthorizerStatus', ], 'tags' => [ 'shape' => 'TagList', ], 'signingDisabled' => [ 'shape' => 'BooleanKey', ], 'enableCachingForHttp' => [ 'shape' => 'EnableCachingForHttp', ], ], ], 'CreateAuthorizerResponse' => [ 'type' => 'structure', 'members' => [ 'authorizerName' => [ 'shape' => 'AuthorizerName', ], 'authorizerArn' => [ 'shape' => 'AuthorizerArn', ], ], ], 'CreateBillingGroupRequest' => [ 'type' => 'structure', 'required' => [ 'billingGroupName', ], 'members' => [ 'billingGroupName' => [ 'shape' => 'BillingGroupName', 'location' => 'uri', 'locationName' => 'billingGroupName', ], 'billingGroupProperties' => [ 'shape' => 'BillingGroupProperties', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateBillingGroupResponse' => [ 'type' => 'structure', 'members' => [ 'billingGroupName' => [ 'shape' => 'BillingGroupName', ], 'billingGroupArn' => [ 'shape' => 'BillingGroupArn', ], 'billingGroupId' => [ 'shape' => 'BillingGroupId', ], ], ], 'CreateCertificateFromCsrRequest' => [ 'type' => 'structure', 'required' => [ 'certificateSigningRequest', ], 'members' => [ 'certificateSigningRequest' => [ 'shape' => 'CertificateSigningRequest', ], 'setAsActive' => [ 'shape' => 'SetAsActive', 'location' => 'querystring', 'locationName' => 'setAsActive', ], ], ], 'CreateCertificateFromCsrResponse' => [ 'type' => 'structure', 'members' => [ 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'certificateId' => [ 'shape' => 'CertificateId', ], 'certificatePem' => [ 'shape' => 'CertificatePem', ], ], ], 'CreateCertificateProviderRequest' => [ 'type' => 'structure', 'required' => [ 'certificateProviderName', 'lambdaFunctionArn', 'accountDefaultForOperations', ], 'members' => [ 'certificateProviderName' => [ 'shape' => 'CertificateProviderName', 'location' => 'uri', 'locationName' => 'certificateProviderName', ], 'lambdaFunctionArn' => [ 'shape' => 'CertificateProviderFunctionArn', ], 'accountDefaultForOperations' => [ 'shape' => 'CertificateProviderAccountDefaultForOperations', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateCertificateProviderResponse' => [ 'type' => 'structure', 'members' => [ 'certificateProviderName' => [ 'shape' => 'CertificateProviderName', ], 'certificateProviderArn' => [ 'shape' => 'CertificateProviderArn', ], ], ], 'CreateCommandRequest' => [ 'type' => 'structure', 'required' => [ 'commandId', ], 'members' => [ 'commandId' => [ 'shape' => 'CommandId', 'location' => 'uri', 'locationName' => 'commandId', ], 'namespace' => [ 'shape' => 'CommandNamespace', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'description' => [ 'shape' => 'CommandDescription', ], 'payload' => [ 'shape' => 'CommandPayload', ], 'mandatoryParameters' => [ 'shape' => 'CommandParameterList', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateCommandResponse' => [ 'type' => 'structure', 'members' => [ 'commandId' => [ 'shape' => 'CommandId', ], 'commandArn' => [ 'shape' => 'CommandArn', ], ], ], 'CreateCustomMetricRequest' => [ 'type' => 'structure', 'required' => [ 'metricName', 'metricType', 'clientRequestToken', ], 'members' => [ 'metricName' => [ 'shape' => 'MetricName', 'location' => 'uri', 'locationName' => 'metricName', ], 'displayName' => [ 'shape' => 'CustomMetricDisplayName', ], 'metricType' => [ 'shape' => 'CustomMetricType', ], 'tags' => [ 'shape' => 'TagList', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'CreateCustomMetricResponse' => [ 'type' => 'structure', 'members' => [ 'metricName' => [ 'shape' => 'MetricName', ], 'metricArn' => [ 'shape' => 'CustomMetricArn', ], ], ], 'CreateDimensionRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'type', 'stringValues', 'clientRequestToken', ], 'members' => [ 'name' => [ 'shape' => 'DimensionName', 'location' => 'uri', 'locationName' => 'name', ], 'type' => [ 'shape' => 'DimensionType', ], 'stringValues' => [ 'shape' => 'DimensionStringValues', ], 'tags' => [ 'shape' => 'TagList', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'CreateDimensionResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'DimensionName', ], 'arn' => [ 'shape' => 'DimensionArn', ], ], ], 'CreateDomainConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'domainConfigurationName', ], 'members' => [ 'domainConfigurationName' => [ 'shape' => 'DomainConfigurationName', 'location' => 'uri', 'locationName' => 'domainConfigurationName', ], 'domainName' => [ 'shape' => 'DomainName', ], 'serverCertificateArns' => [ 'shape' => 'ServerCertificateArns', ], 'validationCertificateArn' => [ 'shape' => 'AcmCertificateArn', ], 'authorizerConfig' => [ 'shape' => 'AuthorizerConfig', ], 'serviceType' => [ 'shape' => 'ServiceType', ], 'tags' => [ 'shape' => 'TagList', ], 'tlsConfig' => [ 'shape' => 'TlsConfig', ], 'serverCertificateConfig' => [ 'shape' => 'ServerCertificateConfig', ], 'authenticationType' => [ 'shape' => 'AuthenticationType', ], 'applicationProtocol' => [ 'shape' => 'ApplicationProtocol', ], 'clientCertificateConfig' => [ 'shape' => 'ClientCertificateConfig', ], ], ], 'CreateDomainConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'domainConfigurationName' => [ 'shape' => 'DomainConfigurationName', ], 'domainConfigurationArn' => [ 'shape' => 'DomainConfigurationArn', ], ], ], 'CreateDynamicThingGroupRequest' => [ 'type' => 'structure', 'required' => [ 'thingGroupName', 'queryString', ], 'members' => [ 'thingGroupName' => [ 'shape' => 'ThingGroupName', 'location' => 'uri', 'locationName' => 'thingGroupName', ], 'thingGroupProperties' => [ 'shape' => 'ThingGroupProperties', ], 'indexName' => [ 'shape' => 'IndexName', ], 'queryString' => [ 'shape' => 'QueryString', ], 'queryVersion' => [ 'shape' => 'QueryVersion', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDynamicThingGroupResponse' => [ 'type' => 'structure', 'members' => [ 'thingGroupName' => [ 'shape' => 'ThingGroupName', ], 'thingGroupArn' => [ 'shape' => 'ThingGroupArn', ], 'thingGroupId' => [ 'shape' => 'ThingGroupId', ], 'indexName' => [ 'shape' => 'IndexName', ], 'queryString' => [ 'shape' => 'QueryString', ], 'queryVersion' => [ 'shape' => 'QueryVersion', ], ], ], 'CreateFleetMetricRequest' => [ 'type' => 'structure', 'required' => [ 'metricName', 'queryString', 'aggregationType', 'period', 'aggregationField', ], 'members' => [ 'metricName' => [ 'shape' => 'FleetMetricName', 'location' => 'uri', 'locationName' => 'metricName', ], 'queryString' => [ 'shape' => 'QueryString', ], 'aggregationType' => [ 'shape' => 'AggregationType', ], 'period' => [ 'shape' => 'FleetMetricPeriod', ], 'aggregationField' => [ 'shape' => 'AggregationField', ], 'description' => [ 'shape' => 'FleetMetricDescription', ], 'queryVersion' => [ 'shape' => 'QueryVersion', ], 'indexName' => [ 'shape' => 'IndexName', ], 'unit' => [ 'shape' => 'FleetMetricUnit', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateFleetMetricResponse' => [ 'type' => 'structure', 'members' => [ 'metricName' => [ 'shape' => 'FleetMetricName', ], 'metricArn' => [ 'shape' => 'FleetMetricArn', ], ], ], 'CreateJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', 'targets', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'targets' => [ 'shape' => 'JobTargets', ], 'documentSource' => [ 'shape' => 'JobDocumentSource', ], 'document' => [ 'shape' => 'JobDocument', ], 'description' => [ 'shape' => 'JobDescription', ], 'presignedUrlConfig' => [ 'shape' => 'PresignedUrlConfig', ], 'targetSelection' => [ 'shape' => 'TargetSelection', ], 'jobExecutionsRolloutConfig' => [ 'shape' => 'JobExecutionsRolloutConfig', ], 'abortConfig' => [ 'shape' => 'AbortConfig', ], 'timeoutConfig' => [ 'shape' => 'TimeoutConfig', ], 'tags' => [ 'shape' => 'TagList', ], 'namespaceId' => [ 'shape' => 'NamespaceId', ], 'jobTemplateArn' => [ 'shape' => 'JobTemplateArn', ], 'jobExecutionsRetryConfig' => [ 'shape' => 'JobExecutionsRetryConfig', ], 'documentParameters' => [ 'shape' => 'ParameterMap', ], 'schedulingConfig' => [ 'shape' => 'SchedulingConfig', ], 'destinationPackageVersions' => [ 'shape' => 'DestinationPackageVersions', ], ], ], 'CreateJobResponse' => [ 'type' => 'structure', 'members' => [ 'jobArn' => [ 'shape' => 'JobArn', ], 'jobId' => [ 'shape' => 'JobId', ], 'description' => [ 'shape' => 'JobDescription', ], ], ], 'CreateJobTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'jobTemplateId', 'description', ], 'members' => [ 'jobTemplateId' => [ 'shape' => 'JobTemplateId', 'location' => 'uri', 'locationName' => 'jobTemplateId', ], 'jobArn' => [ 'shape' => 'JobArn', ], 'documentSource' => [ 'shape' => 'JobDocumentSource', ], 'document' => [ 'shape' => 'JobDocument', ], 'description' => [ 'shape' => 'JobDescription', ], 'presignedUrlConfig' => [ 'shape' => 'PresignedUrlConfig', ], 'jobExecutionsRolloutConfig' => [ 'shape' => 'JobExecutionsRolloutConfig', ], 'abortConfig' => [ 'shape' => 'AbortConfig', ], 'timeoutConfig' => [ 'shape' => 'TimeoutConfig', ], 'tags' => [ 'shape' => 'TagList', ], 'jobExecutionsRetryConfig' => [ 'shape' => 'JobExecutionsRetryConfig', ], 'maintenanceWindows' => [ 'shape' => 'MaintenanceWindows', ], 'destinationPackageVersions' => [ 'shape' => 'DestinationPackageVersions', ], ], ], 'CreateJobTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'jobTemplateArn' => [ 'shape' => 'JobTemplateArn', ], 'jobTemplateId' => [ 'shape' => 'JobTemplateId', ], ], ], 'CreateKeysAndCertificateRequest' => [ 'type' => 'structure', 'members' => [ 'setAsActive' => [ 'shape' => 'SetAsActive', 'location' => 'querystring', 'locationName' => 'setAsActive', ], ], ], 'CreateKeysAndCertificateResponse' => [ 'type' => 'structure', 'members' => [ 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'certificateId' => [ 'shape' => 'CertificateId', ], 'certificatePem' => [ 'shape' => 'CertificatePem', ], 'keyPair' => [ 'shape' => 'KeyPair', ], ], ], 'CreateMitigationActionRequest' => [ 'type' => 'structure', 'required' => [ 'actionName', 'roleArn', 'actionParams', ], 'members' => [ 'actionName' => [ 'shape' => 'MitigationActionName', 'location' => 'uri', 'locationName' => 'actionName', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'actionParams' => [ 'shape' => 'MitigationActionParams', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateMitigationActionResponse' => [ 'type' => 'structure', 'members' => [ 'actionArn' => [ 'shape' => 'MitigationActionArn', ], 'actionId' => [ 'shape' => 'MitigationActionId', ], ], ], 'CreateOTAUpdateRequest' => [ 'type' => 'structure', 'required' => [ 'otaUpdateId', 'targets', 'files', 'roleArn', ], 'members' => [ 'otaUpdateId' => [ 'shape' => 'OTAUpdateId', 'location' => 'uri', 'locationName' => 'otaUpdateId', ], 'description' => [ 'shape' => 'OTAUpdateDescription', ], 'targets' => [ 'shape' => 'Targets', ], 'protocols' => [ 'shape' => 'Protocols', ], 'targetSelection' => [ 'shape' => 'TargetSelection', ], 'awsJobExecutionsRolloutConfig' => [ 'shape' => 'AwsJobExecutionsRolloutConfig', ], 'awsJobPresignedUrlConfig' => [ 'shape' => 'AwsJobPresignedUrlConfig', ], 'awsJobAbortConfig' => [ 'shape' => 'AwsJobAbortConfig', ], 'awsJobTimeoutConfig' => [ 'shape' => 'AwsJobTimeoutConfig', ], 'files' => [ 'shape' => 'OTAUpdateFiles', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'additionalParameters' => [ 'shape' => 'AdditionalParameterMap', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateOTAUpdateResponse' => [ 'type' => 'structure', 'members' => [ 'otaUpdateId' => [ 'shape' => 'OTAUpdateId', ], 'awsIotJobId' => [ 'shape' => 'AwsIotJobId', ], 'otaUpdateArn' => [ 'shape' => 'OTAUpdateArn', ], 'awsIotJobArn' => [ 'shape' => 'AwsIotJobArn', ], 'otaUpdateStatus' => [ 'shape' => 'OTAUpdateStatus', ], ], ], 'CreatePackageRequest' => [ 'type' => 'structure', 'required' => [ 'packageName', ], 'members' => [ 'packageName' => [ 'shape' => 'PackageName', 'location' => 'uri', 'locationName' => 'packageName', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'CreatePackageResponse' => [ 'type' => 'structure', 'members' => [ 'packageName' => [ 'shape' => 'PackageName', ], 'packageArn' => [ 'shape' => 'PackageArn', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'CreatePackageVersionRequest' => [ 'type' => 'structure', 'required' => [ 'packageName', 'versionName', ], 'members' => [ 'packageName' => [ 'shape' => 'PackageName', 'location' => 'uri', 'locationName' => 'packageName', ], 'versionName' => [ 'shape' => 'VersionName', 'location' => 'uri', 'locationName' => 'versionName', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'attributes' => [ 'shape' => 'ResourceAttributes', ], 'artifact' => [ 'shape' => 'PackageVersionArtifact', ], 'recipe' => [ 'shape' => 'PackageVersionRecipe', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'CreatePackageVersionResponse' => [ 'type' => 'structure', 'members' => [ 'packageVersionArn' => [ 'shape' => 'PackageVersionArn', ], 'packageName' => [ 'shape' => 'PackageName', ], 'versionName' => [ 'shape' => 'VersionName', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'attributes' => [ 'shape' => 'ResourceAttributes', ], 'status' => [ 'shape' => 'PackageVersionStatus', ], 'errorReason' => [ 'shape' => 'PackageVersionErrorReason', ], ], ], 'CreatePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', 'policyDocument', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'policyName', ], 'policyDocument' => [ 'shape' => 'PolicyDocument', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreatePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', ], 'policyArn' => [ 'shape' => 'PolicyArn', ], 'policyDocument' => [ 'shape' => 'PolicyDocument', ], 'policyVersionId' => [ 'shape' => 'PolicyVersionId', ], ], ], 'CreatePolicyVersionRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', 'policyDocument', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'policyName', ], 'policyDocument' => [ 'shape' => 'PolicyDocument', ], 'setAsDefault' => [ 'shape' => 'SetAsDefault', 'location' => 'querystring', 'locationName' => 'setAsDefault', ], ], ], 'CreatePolicyVersionResponse' => [ 'type' => 'structure', 'members' => [ 'policyArn' => [ 'shape' => 'PolicyArn', ], 'policyDocument' => [ 'shape' => 'PolicyDocument', ], 'policyVersionId' => [ 'shape' => 'PolicyVersionId', ], 'isDefaultVersion' => [ 'shape' => 'IsDefaultVersion', ], ], ], 'CreateProvisioningClaimRequest' => [ 'type' => 'structure', 'required' => [ 'templateName', ], 'members' => [ 'templateName' => [ 'shape' => 'TemplateName', 'location' => 'uri', 'locationName' => 'templateName', ], ], ], 'CreateProvisioningClaimResponse' => [ 'type' => 'structure', 'members' => [ 'certificateId' => [ 'shape' => 'CertificateId', ], 'certificatePem' => [ 'shape' => 'CertificatePem', ], 'keyPair' => [ 'shape' => 'KeyPair', ], 'expiration' => [ 'shape' => 'DateType', ], ], ], 'CreateProvisioningTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'templateName', 'templateBody', 'provisioningRoleArn', ], 'members' => [ 'templateName' => [ 'shape' => 'TemplateName', ], 'description' => [ 'shape' => 'TemplateDescription', ], 'templateBody' => [ 'shape' => 'TemplateBody', ], 'enabled' => [ 'shape' => 'Enabled', ], 'provisioningRoleArn' => [ 'shape' => 'RoleArn', ], 'preProvisioningHook' => [ 'shape' => 'ProvisioningHook', ], 'tags' => [ 'shape' => 'TagList', ], 'type' => [ 'shape' => 'TemplateType', ], ], ], 'CreateProvisioningTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'templateArn' => [ 'shape' => 'TemplateArn', ], 'templateName' => [ 'shape' => 'TemplateName', ], 'defaultVersionId' => [ 'shape' => 'TemplateVersionId', ], ], ], 'CreateProvisioningTemplateVersionRequest' => [ 'type' => 'structure', 'required' => [ 'templateName', 'templateBody', ], 'members' => [ 'templateName' => [ 'shape' => 'TemplateName', 'location' => 'uri', 'locationName' => 'templateName', ], 'templateBody' => [ 'shape' => 'TemplateBody', ], 'setAsDefault' => [ 'shape' => 'SetAsDefault', 'location' => 'querystring', 'locationName' => 'setAsDefault', ], ], ], 'CreateProvisioningTemplateVersionResponse' => [ 'type' => 'structure', 'members' => [ 'templateArn' => [ 'shape' => 'TemplateArn', ], 'templateName' => [ 'shape' => 'TemplateName', ], 'versionId' => [ 'shape' => 'TemplateVersionId', ], 'isDefaultVersion' => [ 'shape' => 'IsDefaultVersion', ], ], ], 'CreateRoleAliasRequest' => [ 'type' => 'structure', 'required' => [ 'roleAlias', 'roleArn', ], 'members' => [ 'roleAlias' => [ 'shape' => 'RoleAlias', 'location' => 'uri', 'locationName' => 'roleAlias', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'credentialDurationSeconds' => [ 'shape' => 'CredentialDurationSeconds', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateRoleAliasResponse' => [ 'type' => 'structure', 'members' => [ 'roleAlias' => [ 'shape' => 'RoleAlias', ], 'roleAliasArn' => [ 'shape' => 'RoleAliasArn', ], ], ], 'CreateScheduledAuditRequest' => [ 'type' => 'structure', 'required' => [ 'frequency', 'targetCheckNames', 'scheduledAuditName', ], 'members' => [ 'frequency' => [ 'shape' => 'AuditFrequency', ], 'dayOfMonth' => [ 'shape' => 'DayOfMonth', ], 'dayOfWeek' => [ 'shape' => 'DayOfWeek', ], 'targetCheckNames' => [ 'shape' => 'TargetAuditCheckNames', ], 'scheduledAuditName' => [ 'shape' => 'ScheduledAuditName', 'location' => 'uri', 'locationName' => 'scheduledAuditName', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateScheduledAuditResponse' => [ 'type' => 'structure', 'members' => [ 'scheduledAuditArn' => [ 'shape' => 'ScheduledAuditArn', ], ], ], 'CreateSecurityProfileRequest' => [ 'type' => 'structure', 'required' => [ 'securityProfileName', ], 'members' => [ 'securityProfileName' => [ 'shape' => 'SecurityProfileName', 'location' => 'uri', 'locationName' => 'securityProfileName', ], 'securityProfileDescription' => [ 'shape' => 'SecurityProfileDescription', ], 'behaviors' => [ 'shape' => 'Behaviors', ], 'alertTargets' => [ 'shape' => 'AlertTargets', ], 'additionalMetricsToRetain' => [ 'shape' => 'AdditionalMetricsToRetainList', 'deprecated' => true, 'deprecatedMessage' => 'Use additionalMetricsToRetainV2.', ], 'additionalMetricsToRetainV2' => [ 'shape' => 'AdditionalMetricsToRetainV2List', ], 'tags' => [ 'shape' => 'TagList', ], 'metricsExportConfig' => [ 'shape' => 'MetricsExportConfig', ], ], ], 'CreateSecurityProfileResponse' => [ 'type' => 'structure', 'members' => [ 'securityProfileName' => [ 'shape' => 'SecurityProfileName', ], 'securityProfileArn' => [ 'shape' => 'SecurityProfileArn', ], ], ], 'CreateStreamRequest' => [ 'type' => 'structure', 'required' => [ 'streamId', 'files', 'roleArn', ], 'members' => [ 'streamId' => [ 'shape' => 'StreamId', 'location' => 'uri', 'locationName' => 'streamId', ], 'description' => [ 'shape' => 'StreamDescription', ], 'files' => [ 'shape' => 'StreamFiles', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateStreamResponse' => [ 'type' => 'structure', 'members' => [ 'streamId' => [ 'shape' => 'StreamId', ], 'streamArn' => [ 'shape' => 'StreamArn', ], 'description' => [ 'shape' => 'StreamDescription', ], 'streamVersion' => [ 'shape' => 'StreamVersion', ], ], ], 'CreateThingGroupRequest' => [ 'type' => 'structure', 'required' => [ 'thingGroupName', ], 'members' => [ 'thingGroupName' => [ 'shape' => 'ThingGroupName', 'location' => 'uri', 'locationName' => 'thingGroupName', ], 'parentGroupName' => [ 'shape' => 'ThingGroupName', ], 'thingGroupProperties' => [ 'shape' => 'ThingGroupProperties', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateThingGroupResponse' => [ 'type' => 'structure', 'members' => [ 'thingGroupName' => [ 'shape' => 'ThingGroupName', ], 'thingGroupArn' => [ 'shape' => 'ThingGroupArn', ], 'thingGroupId' => [ 'shape' => 'ThingGroupId', ], ], ], 'CreateThingRequest' => [ 'type' => 'structure', 'required' => [ 'thingName', ], 'members' => [ 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], 'thingTypeName' => [ 'shape' => 'ThingTypeName', ], 'attributePayload' => [ 'shape' => 'AttributePayload', ], 'billingGroupName' => [ 'shape' => 'BillingGroupName', ], ], ], 'CreateThingResponse' => [ 'type' => 'structure', 'members' => [ 'thingName' => [ 'shape' => 'ThingName', ], 'thingArn' => [ 'shape' => 'ThingArn', ], 'thingId' => [ 'shape' => 'ThingId', ], ], ], 'CreateThingTypeRequest' => [ 'type' => 'structure', 'required' => [ 'thingTypeName', ], 'members' => [ 'thingTypeName' => [ 'shape' => 'ThingTypeName', 'location' => 'uri', 'locationName' => 'thingTypeName', ], 'thingTypeProperties' => [ 'shape' => 'ThingTypeProperties', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateThingTypeResponse' => [ 'type' => 'structure', 'members' => [ 'thingTypeName' => [ 'shape' => 'ThingTypeName', ], 'thingTypeArn' => [ 'shape' => 'ThingTypeArn', ], 'thingTypeId' => [ 'shape' => 'ThingTypeId', ], ], ], 'CreateTopicRuleDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'destinationConfiguration', ], 'members' => [ 'destinationConfiguration' => [ 'shape' => 'TopicRuleDestinationConfiguration', ], ], ], 'CreateTopicRuleDestinationResponse' => [ 'type' => 'structure', 'members' => [ 'topicRuleDestination' => [ 'shape' => 'TopicRuleDestination', ], ], ], 'CreateTopicRuleRequest' => [ 'type' => 'structure', 'required' => [ 'ruleName', 'topicRulePayload', ], 'members' => [ 'ruleName' => [ 'shape' => 'RuleName', 'location' => 'uri', 'locationName' => 'ruleName', ], 'topicRulePayload' => [ 'shape' => 'TopicRulePayload', ], 'tags' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amz-tagging', ], ], 'payload' => 'topicRulePayload', ], 'CreatedAtDate' => [ 'type' => 'timestamp', ], 'CreationDate' => [ 'type' => 'timestamp', ], 'CredentialDurationSeconds' => [ 'type' => 'integer', 'max' => 43200, 'min' => 900, ], 'CronExpression' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'CustomCodeSigning' => [ 'type' => 'structure', 'members' => [ 'signature' => [ 'shape' => 'CodeSigningSignature', ], 'certificateChain' => [ 'shape' => 'CodeSigningCertificateChain', ], 'hashAlgorithm' => [ 'shape' => 'HashAlgorithm', ], 'signatureAlgorithm' => [ 'shape' => 'SignatureAlgorithm', ], ], ], 'CustomMetricArn' => [ 'type' => 'string', ], 'CustomMetricDisplayName' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[\\p{Graph}\\x20]*', ], 'CustomMetricType' => [ 'type' => 'string', 'enum' => [ 'string-list', 'ip-address-list', 'number-list', 'number', ], ], 'CustomerVersion' => [ 'type' => 'integer', 'min' => 1, ], 'DataCollectionPercentage' => [ 'type' => 'double', 'max' => 100, 'min' => 0, ], 'DateType' => [ 'type' => 'timestamp', ], 'DayOfMonth' => [ 'type' => 'string', 'pattern' => '^([1-9]|[12][0-9]|3[01])$|^LAST$', ], 'DayOfWeek' => [ 'type' => 'string', 'enum' => [ 'SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', ], ], 'DeleteAccountAuditConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'deleteScheduledAudits' => [ 'shape' => 'DeleteScheduledAudits', 'location' => 'querystring', 'locationName' => 'deleteScheduledAudits', ], ], ], 'DeleteAccountAuditConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAdditionalMetricsToRetain' => [ 'type' => 'boolean', ], 'DeleteAlertTargets' => [ 'type' => 'boolean', ], 'DeleteAuditSuppressionRequest' => [ 'type' => 'structure', 'required' => [ 'checkName', 'resourceIdentifier', ], 'members' => [ 'checkName' => [ 'shape' => 'AuditCheckName', ], 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], ], ], 'DeleteAuditSuppressionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAuthorizerRequest' => [ 'type' => 'structure', 'required' => [ 'authorizerName', ], 'members' => [ 'authorizerName' => [ 'shape' => 'AuthorizerName', 'location' => 'uri', 'locationName' => 'authorizerName', ], ], ], 'DeleteAuthorizerResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteBehaviors' => [ 'type' => 'boolean', ], 'DeleteBillingGroupRequest' => [ 'type' => 'structure', 'required' => [ 'billingGroupName', ], 'members' => [ 'billingGroupName' => [ 'shape' => 'BillingGroupName', 'location' => 'uri', 'locationName' => 'billingGroupName', ], 'expectedVersion' => [ 'shape' => 'OptionalVersion', 'location' => 'querystring', 'locationName' => 'expectedVersion', ], ], ], 'DeleteBillingGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteCACertificateRequest' => [ 'type' => 'structure', 'required' => [ 'certificateId', ], 'members' => [ 'certificateId' => [ 'shape' => 'CertificateId', 'location' => 'uri', 'locationName' => 'caCertificateId', ], ], ], 'DeleteCACertificateResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteCertificateProviderRequest' => [ 'type' => 'structure', 'required' => [ 'certificateProviderName', ], 'members' => [ 'certificateProviderName' => [ 'shape' => 'CertificateProviderName', 'location' => 'uri', 'locationName' => 'certificateProviderName', ], ], ], 'DeleteCertificateProviderResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'certificateId', ], 'members' => [ 'certificateId' => [ 'shape' => 'CertificateId', 'location' => 'uri', 'locationName' => 'certificateId', ], 'forceDelete' => [ 'shape' => 'ForceDelete', 'location' => 'querystring', 'locationName' => 'forceDelete', ], ], ], 'DeleteCommandExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'executionId', 'targetArn', ], 'members' => [ 'executionId' => [ 'shape' => 'CommandExecutionId', 'location' => 'uri', 'locationName' => 'executionId', ], 'targetArn' => [ 'shape' => 'TargetArn', 'location' => 'querystring', 'locationName' => 'targetArn', ], ], ], 'DeleteCommandExecutionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteCommandRequest' => [ 'type' => 'structure', 'required' => [ 'commandId', ], 'members' => [ 'commandId' => [ 'shape' => 'CommandId', 'location' => 'uri', 'locationName' => 'commandId', ], ], ], 'DeleteCommandResponse' => [ 'type' => 'structure', 'members' => [ 'statusCode' => [ 'shape' => 'StatusCode', 'location' => 'statusCode', ], ], ], 'DeleteConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'DeleteCustomMetricRequest' => [ 'type' => 'structure', 'required' => [ 'metricName', ], 'members' => [ 'metricName' => [ 'shape' => 'MetricName', 'location' => 'uri', 'locationName' => 'metricName', ], ], ], 'DeleteCustomMetricResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDimensionRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'DimensionName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteDimensionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDomainConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'domainConfigurationName', ], 'members' => [ 'domainConfigurationName' => [ 'shape' => 'DomainConfigurationName', 'location' => 'uri', 'locationName' => 'domainConfigurationName', ], ], ], 'DeleteDomainConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDynamicThingGroupRequest' => [ 'type' => 'structure', 'required' => [ 'thingGroupName', ], 'members' => [ 'thingGroupName' => [ 'shape' => 'ThingGroupName', 'location' => 'uri', 'locationName' => 'thingGroupName', ], 'expectedVersion' => [ 'shape' => 'OptionalVersion', 'location' => 'querystring', 'locationName' => 'expectedVersion', ], ], ], 'DeleteDynamicThingGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteFleetMetricRequest' => [ 'type' => 'structure', 'required' => [ 'metricName', ], 'members' => [ 'metricName' => [ 'shape' => 'FleetMetricName', 'location' => 'uri', 'locationName' => 'metricName', ], 'expectedVersion' => [ 'shape' => 'OptionalVersion', 'location' => 'querystring', 'locationName' => 'expectedVersion', ], ], ], 'DeleteJobExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', 'thingName', 'executionNumber', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], 'executionNumber' => [ 'shape' => 'ExecutionNumber', 'location' => 'uri', 'locationName' => 'executionNumber', ], 'force' => [ 'shape' => 'ForceFlag', 'location' => 'querystring', 'locationName' => 'force', ], 'namespaceId' => [ 'shape' => 'NamespaceId', 'location' => 'querystring', 'locationName' => 'namespaceId', ], ], ], 'DeleteJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'force' => [ 'shape' => 'ForceFlag', 'location' => 'querystring', 'locationName' => 'force', ], 'namespaceId' => [ 'shape' => 'NamespaceId', 'location' => 'querystring', 'locationName' => 'namespaceId', ], ], ], 'DeleteJobTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'jobTemplateId', ], 'members' => [ 'jobTemplateId' => [ 'shape' => 'JobTemplateId', 'location' => 'uri', 'locationName' => 'jobTemplateId', ], ], ], 'DeleteMetricsExportConfig' => [ 'type' => 'boolean', ], 'DeleteMitigationActionRequest' => [ 'type' => 'structure', 'required' => [ 'actionName', ], 'members' => [ 'actionName' => [ 'shape' => 'MitigationActionName', 'location' => 'uri', 'locationName' => 'actionName', ], ], ], 'DeleteMitigationActionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteOTAUpdateRequest' => [ 'type' => 'structure', 'required' => [ 'otaUpdateId', ], 'members' => [ 'otaUpdateId' => [ 'shape' => 'OTAUpdateId', 'location' => 'uri', 'locationName' => 'otaUpdateId', ], 'deleteStream' => [ 'shape' => 'DeleteStream', 'location' => 'querystring', 'locationName' => 'deleteStream', ], 'forceDeleteAWSJob' => [ 'shape' => 'ForceDeleteAWSJob', 'location' => 'querystring', 'locationName' => 'forceDeleteAWSJob', ], ], ], 'DeleteOTAUpdateResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePackageRequest' => [ 'type' => 'structure', 'required' => [ 'packageName', ], 'members' => [ 'packageName' => [ 'shape' => 'PackageName', 'location' => 'uri', 'locationName' => 'packageName', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeletePackageResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePackageVersionRequest' => [ 'type' => 'structure', 'required' => [ 'packageName', 'versionName', ], 'members' => [ 'packageName' => [ 'shape' => 'PackageName', 'location' => 'uri', 'locationName' => 'packageName', ], 'versionName' => [ 'shape' => 'VersionName', 'location' => 'uri', 'locationName' => 'versionName', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeletePackageVersionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'policyName', ], ], ], 'DeletePolicyVersionRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', 'policyVersionId', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'policyName', ], 'policyVersionId' => [ 'shape' => 'PolicyVersionId', 'location' => 'uri', 'locationName' => 'policyVersionId', ], ], ], 'DeleteProvisioningTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'templateName', ], 'members' => [ 'templateName' => [ 'shape' => 'TemplateName', 'location' => 'uri', 'locationName' => 'templateName', ], ], ], 'DeleteProvisioningTemplateResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteProvisioningTemplateVersionRequest' => [ 'type' => 'structure', 'required' => [ 'templateName', 'versionId', ], 'members' => [ 'templateName' => [ 'shape' => 'TemplateName', 'location' => 'uri', 'locationName' => 'templateName', ], 'versionId' => [ 'shape' => 'TemplateVersionId', 'location' => 'uri', 'locationName' => 'versionId', ], ], ], 'DeleteProvisioningTemplateVersionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRegistrationCodeRequest' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRegistrationCodeResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRoleAliasRequest' => [ 'type' => 'structure', 'required' => [ 'roleAlias', ], 'members' => [ 'roleAlias' => [ 'shape' => 'RoleAlias', 'location' => 'uri', 'locationName' => 'roleAlias', ], ], ], 'DeleteRoleAliasResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteScheduledAuditRequest' => [ 'type' => 'structure', 'required' => [ 'scheduledAuditName', ], 'members' => [ 'scheduledAuditName' => [ 'shape' => 'ScheduledAuditName', 'location' => 'uri', 'locationName' => 'scheduledAuditName', ], ], ], 'DeleteScheduledAuditResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteScheduledAudits' => [ 'type' => 'boolean', ], 'DeleteSecurityProfileRequest' => [ 'type' => 'structure', 'required' => [ 'securityProfileName', ], 'members' => [ 'securityProfileName' => [ 'shape' => 'SecurityProfileName', 'location' => 'uri', 'locationName' => 'securityProfileName', ], 'expectedVersion' => [ 'shape' => 'OptionalVersion', 'location' => 'querystring', 'locationName' => 'expectedVersion', ], ], ], 'DeleteSecurityProfileResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteStream' => [ 'type' => 'boolean', ], 'DeleteStreamRequest' => [ 'type' => 'structure', 'required' => [ 'streamId', ], 'members' => [ 'streamId' => [ 'shape' => 'StreamId', 'location' => 'uri', 'locationName' => 'streamId', ], ], ], 'DeleteStreamResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteThingGroupRequest' => [ 'type' => 'structure', 'required' => [ 'thingGroupName', ], 'members' => [ 'thingGroupName' => [ 'shape' => 'ThingGroupName', 'location' => 'uri', 'locationName' => 'thingGroupName', ], 'expectedVersion' => [ 'shape' => 'OptionalVersion', 'location' => 'querystring', 'locationName' => 'expectedVersion', ], ], ], 'DeleteThingGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteThingRequest' => [ 'type' => 'structure', 'required' => [ 'thingName', ], 'members' => [ 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], 'expectedVersion' => [ 'shape' => 'OptionalVersion', 'location' => 'querystring', 'locationName' => 'expectedVersion', ], ], ], 'DeleteThingResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteThingTypeRequest' => [ 'type' => 'structure', 'required' => [ 'thingTypeName', ], 'members' => [ 'thingTypeName' => [ 'shape' => 'ThingTypeName', 'location' => 'uri', 'locationName' => 'thingTypeName', ], ], ], 'DeleteThingTypeResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTopicRuleDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AwsArn', 'location' => 'uri', 'locationName' => 'arn', ], ], ], 'DeleteTopicRuleDestinationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTopicRuleRequest' => [ 'type' => 'structure', 'required' => [ 'ruleName', ], 'members' => [ 'ruleName' => [ 'shape' => 'RuleName', 'location' => 'uri', 'locationName' => 'ruleName', ], ], ], 'DeleteV2LoggingLevelRequest' => [ 'type' => 'structure', 'required' => [ 'targetType', 'targetName', ], 'members' => [ 'targetType' => [ 'shape' => 'LogTargetType', 'location' => 'querystring', 'locationName' => 'targetType', ], 'targetName' => [ 'shape' => 'LogTargetName', 'location' => 'querystring', 'locationName' => 'targetName', ], ], ], 'DeliveryStreamName' => [ 'type' => 'string', ], 'Denied' => [ 'type' => 'structure', 'members' => [ 'implicitDeny' => [ 'shape' => 'ImplicitDeny', ], 'explicitDeny' => [ 'shape' => 'ExplicitDeny', ], ], ], 'DeprecateThingTypeRequest' => [ 'type' => 'structure', 'required' => [ 'thingTypeName', ], 'members' => [ 'thingTypeName' => [ 'shape' => 'ThingTypeName', 'location' => 'uri', 'locationName' => 'thingTypeName', ], 'undoDeprecate' => [ 'shape' => 'UndoDeprecate', ], ], ], 'DeprecateThingTypeResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeprecationDate' => [ 'type' => 'timestamp', ], 'DeprecationFlag' => [ 'type' => 'boolean', ], 'DescribeAccountAuditConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeAccountAuditConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'roleArn' => [ 'shape' => 'RoleArn', ], 'auditNotificationTargetConfigurations' => [ 'shape' => 'AuditNotificationTargetConfigurations', ], 'auditCheckConfigurations' => [ 'shape' => 'AuditCheckConfigurations', ], ], ], 'DescribeAuditFindingRequest' => [ 'type' => 'structure', 'required' => [ 'findingId', ], 'members' => [ 'findingId' => [ 'shape' => 'FindingId', 'location' => 'uri', 'locationName' => 'findingId', ], ], ], 'DescribeAuditFindingResponse' => [ 'type' => 'structure', 'members' => [ 'finding' => [ 'shape' => 'AuditFinding', ], ], ], 'DescribeAuditMitigationActionsTaskRequest' => [ 'type' => 'structure', 'required' => [ 'taskId', ], 'members' => [ 'taskId' => [ 'shape' => 'MitigationActionsTaskId', 'location' => 'uri', 'locationName' => 'taskId', ], ], ], 'DescribeAuditMitigationActionsTaskResponse' => [ 'type' => 'structure', 'members' => [ 'taskStatus' => [ 'shape' => 'AuditMitigationActionsTaskStatus', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'taskStatistics' => [ 'shape' => 'AuditMitigationActionsTaskStatistics', ], 'target' => [ 'shape' => 'AuditMitigationActionsTaskTarget', ], 'auditCheckToActionsMapping' => [ 'shape' => 'AuditCheckToActionsMapping', ], 'actionsDefinition' => [ 'shape' => 'MitigationActionList', ], ], ], 'DescribeAuditSuppressionRequest' => [ 'type' => 'structure', 'required' => [ 'checkName', 'resourceIdentifier', ], 'members' => [ 'checkName' => [ 'shape' => 'AuditCheckName', ], 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], ], ], 'DescribeAuditSuppressionResponse' => [ 'type' => 'structure', 'members' => [ 'checkName' => [ 'shape' => 'AuditCheckName', ], 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'expirationDate' => [ 'shape' => 'Timestamp', ], 'suppressIndefinitely' => [ 'shape' => 'SuppressIndefinitely', ], 'description' => [ 'shape' => 'AuditDescription', ], ], ], 'DescribeAuditTaskRequest' => [ 'type' => 'structure', 'required' => [ 'taskId', ], 'members' => [ 'taskId' => [ 'shape' => 'AuditTaskId', 'location' => 'uri', 'locationName' => 'taskId', ], ], ], 'DescribeAuditTaskResponse' => [ 'type' => 'structure', 'members' => [ 'taskStatus' => [ 'shape' => 'AuditTaskStatus', ], 'taskType' => [ 'shape' => 'AuditTaskType', ], 'taskStartTime' => [ 'shape' => 'Timestamp', ], 'taskStatistics' => [ 'shape' => 'TaskStatistics', ], 'scheduledAuditName' => [ 'shape' => 'ScheduledAuditName', ], 'auditDetails' => [ 'shape' => 'AuditDetails', ], ], ], 'DescribeAuthorizerRequest' => [ 'type' => 'structure', 'required' => [ 'authorizerName', ], 'members' => [ 'authorizerName' => [ 'shape' => 'AuthorizerName', 'location' => 'uri', 'locationName' => 'authorizerName', ], ], ], 'DescribeAuthorizerResponse' => [ 'type' => 'structure', 'members' => [ 'authorizerDescription' => [ 'shape' => 'AuthorizerDescription', ], ], ], 'DescribeBillingGroupRequest' => [ 'type' => 'structure', 'required' => [ 'billingGroupName', ], 'members' => [ 'billingGroupName' => [ 'shape' => 'BillingGroupName', 'location' => 'uri', 'locationName' => 'billingGroupName', ], ], ], 'DescribeBillingGroupResponse' => [ 'type' => 'structure', 'members' => [ 'billingGroupName' => [ 'shape' => 'BillingGroupName', ], 'billingGroupId' => [ 'shape' => 'BillingGroupId', ], 'billingGroupArn' => [ 'shape' => 'BillingGroupArn', ], 'version' => [ 'shape' => 'Version', ], 'billingGroupProperties' => [ 'shape' => 'BillingGroupProperties', ], 'billingGroupMetadata' => [ 'shape' => 'BillingGroupMetadata', ], ], ], 'DescribeCACertificateRequest' => [ 'type' => 'structure', 'required' => [ 'certificateId', ], 'members' => [ 'certificateId' => [ 'shape' => 'CertificateId', 'location' => 'uri', 'locationName' => 'caCertificateId', ], ], ], 'DescribeCACertificateResponse' => [ 'type' => 'structure', 'members' => [ 'certificateDescription' => [ 'shape' => 'CACertificateDescription', ], 'registrationConfig' => [ 'shape' => 'RegistrationConfig', ], ], ], 'DescribeCertificateProviderRequest' => [ 'type' => 'structure', 'required' => [ 'certificateProviderName', ], 'members' => [ 'certificateProviderName' => [ 'shape' => 'CertificateProviderName', 'location' => 'uri', 'locationName' => 'certificateProviderName', ], ], ], 'DescribeCertificateProviderResponse' => [ 'type' => 'structure', 'members' => [ 'certificateProviderName' => [ 'shape' => 'CertificateProviderName', ], 'certificateProviderArn' => [ 'shape' => 'CertificateProviderArn', ], 'lambdaFunctionArn' => [ 'shape' => 'CertificateProviderFunctionArn', ], 'accountDefaultForOperations' => [ 'shape' => 'CertificateProviderAccountDefaultForOperations', ], 'creationDate' => [ 'shape' => 'DateType', ], 'lastModifiedDate' => [ 'shape' => 'DateType', ], ], ], 'DescribeCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'certificateId', ], 'members' => [ 'certificateId' => [ 'shape' => 'CertificateId', 'location' => 'uri', 'locationName' => 'certificateId', ], ], ], 'DescribeCertificateResponse' => [ 'type' => 'structure', 'members' => [ 'certificateDescription' => [ 'shape' => 'CertificateDescription', ], ], ], 'DescribeCustomMetricRequest' => [ 'type' => 'structure', 'required' => [ 'metricName', ], 'members' => [ 'metricName' => [ 'shape' => 'MetricName', 'location' => 'uri', 'locationName' => 'metricName', ], ], ], 'DescribeCustomMetricResponse' => [ 'type' => 'structure', 'members' => [ 'metricName' => [ 'shape' => 'MetricName', ], 'metricArn' => [ 'shape' => 'CustomMetricArn', ], 'metricType' => [ 'shape' => 'CustomMetricType', ], 'displayName' => [ 'shape' => 'CustomMetricDisplayName', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastModifiedDate' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeDefaultAuthorizerRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeDefaultAuthorizerResponse' => [ 'type' => 'structure', 'members' => [ 'authorizerDescription' => [ 'shape' => 'AuthorizerDescription', ], ], ], 'DescribeDetectMitigationActionsTaskRequest' => [ 'type' => 'structure', 'required' => [ 'taskId', ], 'members' => [ 'taskId' => [ 'shape' => 'MitigationActionsTaskId', 'location' => 'uri', 'locationName' => 'taskId', ], ], ], 'DescribeDetectMitigationActionsTaskResponse' => [ 'type' => 'structure', 'members' => [ 'taskSummary' => [ 'shape' => 'DetectMitigationActionsTaskSummary', ], ], ], 'DescribeDimensionRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'DimensionName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DescribeDimensionResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'DimensionName', ], 'arn' => [ 'shape' => 'DimensionArn', ], 'type' => [ 'shape' => 'DimensionType', ], 'stringValues' => [ 'shape' => 'DimensionStringValues', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastModifiedDate' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeDomainConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'domainConfigurationName', ], 'members' => [ 'domainConfigurationName' => [ 'shape' => 'ReservedDomainConfigurationName', 'location' => 'uri', 'locationName' => 'domainConfigurationName', ], ], ], 'DescribeDomainConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'domainConfigurationName' => [ 'shape' => 'ReservedDomainConfigurationName', ], 'domainConfigurationArn' => [ 'shape' => 'DomainConfigurationArn', ], 'domainName' => [ 'shape' => 'DomainName', ], 'serverCertificates' => [ 'shape' => 'ServerCertificates', ], 'authorizerConfig' => [ 'shape' => 'AuthorizerConfig', ], 'domainConfigurationStatus' => [ 'shape' => 'DomainConfigurationStatus', ], 'serviceType' => [ 'shape' => 'ServiceType', ], 'domainType' => [ 'shape' => 'DomainType', ], 'lastStatusChangeDate' => [ 'shape' => 'DateType', ], 'tlsConfig' => [ 'shape' => 'TlsConfig', ], 'serverCertificateConfig' => [ 'shape' => 'ServerCertificateConfig', ], 'authenticationType' => [ 'shape' => 'AuthenticationType', ], 'applicationProtocol' => [ 'shape' => 'ApplicationProtocol', ], 'clientCertificateConfig' => [ 'shape' => 'ClientCertificateConfig', ], ], ], 'DescribeEndpointRequest' => [ 'type' => 'structure', 'members' => [ 'endpointType' => [ 'shape' => 'EndpointType', 'location' => 'querystring', 'locationName' => 'endpointType', ], ], ], 'DescribeEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'endpointAddress' => [ 'shape' => 'EndpointAddress', ], ], ], 'DescribeEventConfigurationsRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeEventConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'eventConfigurations' => [ 'shape' => 'EventConfigurations', ], 'creationDate' => [ 'shape' => 'CreationDate', ], 'lastModifiedDate' => [ 'shape' => 'LastModifiedDate', ], ], ], 'DescribeFleetMetricRequest' => [ 'type' => 'structure', 'required' => [ 'metricName', ], 'members' => [ 'metricName' => [ 'shape' => 'FleetMetricName', 'location' => 'uri', 'locationName' => 'metricName', ], ], ], 'DescribeFleetMetricResponse' => [ 'type' => 'structure', 'members' => [ 'metricName' => [ 'shape' => 'FleetMetricName', ], 'queryString' => [ 'shape' => 'QueryString', ], 'aggregationType' => [ 'shape' => 'AggregationType', ], 'period' => [ 'shape' => 'FleetMetricPeriod', ], 'aggregationField' => [ 'shape' => 'AggregationField', ], 'description' => [ 'shape' => 'FleetMetricDescription', ], 'queryVersion' => [ 'shape' => 'QueryVersion', ], 'indexName' => [ 'shape' => 'IndexName', ], 'creationDate' => [ 'shape' => 'CreationDate', ], 'lastModifiedDate' => [ 'shape' => 'LastModifiedDate', ], 'unit' => [ 'shape' => 'FleetMetricUnit', ], 'version' => [ 'shape' => 'Version', ], 'metricArn' => [ 'shape' => 'FleetMetricArn', ], ], ], 'DescribeIndexRequest' => [ 'type' => 'structure', 'required' => [ 'indexName', ], 'members' => [ 'indexName' => [ 'shape' => 'IndexName', 'location' => 'uri', 'locationName' => 'indexName', ], ], ], 'DescribeIndexResponse' => [ 'type' => 'structure', 'members' => [ 'indexName' => [ 'shape' => 'IndexName', ], 'indexStatus' => [ 'shape' => 'IndexStatus', ], 'schema' => [ 'shape' => 'IndexSchema', ], ], ], 'DescribeJobExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', 'thingName', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], 'executionNumber' => [ 'shape' => 'ExecutionNumber', 'location' => 'querystring', 'locationName' => 'executionNumber', ], ], ], 'DescribeJobExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'execution' => [ 'shape' => 'JobExecution', ], ], ], 'DescribeJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'beforeSubstitution' => [ 'shape' => 'BeforeSubstitutionFlag', 'location' => 'querystring', 'locationName' => 'beforeSubstitution', ], ], ], 'DescribeJobResponse' => [ 'type' => 'structure', 'members' => [ 'documentSource' => [ 'shape' => 'JobDocumentSource', ], 'job' => [ 'shape' => 'Job', ], ], ], 'DescribeJobTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'jobTemplateId', ], 'members' => [ 'jobTemplateId' => [ 'shape' => 'JobTemplateId', 'location' => 'uri', 'locationName' => 'jobTemplateId', ], ], ], 'DescribeJobTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'jobTemplateArn' => [ 'shape' => 'JobTemplateArn', ], 'jobTemplateId' => [ 'shape' => 'JobTemplateId', ], 'description' => [ 'shape' => 'JobDescription', ], 'documentSource' => [ 'shape' => 'JobDocumentSource', ], 'document' => [ 'shape' => 'JobDocument', ], 'createdAt' => [ 'shape' => 'DateType', ], 'presignedUrlConfig' => [ 'shape' => 'PresignedUrlConfig', ], 'jobExecutionsRolloutConfig' => [ 'shape' => 'JobExecutionsRolloutConfig', ], 'abortConfig' => [ 'shape' => 'AbortConfig', ], 'timeoutConfig' => [ 'shape' => 'TimeoutConfig', ], 'jobExecutionsRetryConfig' => [ 'shape' => 'JobExecutionsRetryConfig', ], 'maintenanceWindows' => [ 'shape' => 'MaintenanceWindows', ], 'destinationPackageVersions' => [ 'shape' => 'DestinationPackageVersions', ], ], ], 'DescribeManagedJobTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'templateName', ], 'members' => [ 'templateName' => [ 'shape' => 'ManagedJobTemplateName', 'location' => 'uri', 'locationName' => 'templateName', ], 'templateVersion' => [ 'shape' => 'ManagedTemplateVersion', 'location' => 'querystring', 'locationName' => 'templateVersion', ], ], ], 'DescribeManagedJobTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'templateName' => [ 'shape' => 'ManagedJobTemplateName', ], 'templateArn' => [ 'shape' => 'JobTemplateArn', ], 'description' => [ 'shape' => 'JobDescription', ], 'templateVersion' => [ 'shape' => 'ManagedTemplateVersion', ], 'environments' => [ 'shape' => 'Environments', ], 'documentParameters' => [ 'shape' => 'DocumentParameters', ], 'document' => [ 'shape' => 'JobDocument', ], ], ], 'DescribeMitigationActionRequest' => [ 'type' => 'structure', 'required' => [ 'actionName', ], 'members' => [ 'actionName' => [ 'shape' => 'MitigationActionName', 'location' => 'uri', 'locationName' => 'actionName', ], ], ], 'DescribeMitigationActionResponse' => [ 'type' => 'structure', 'members' => [ 'actionName' => [ 'shape' => 'MitigationActionName', ], 'actionType' => [ 'shape' => 'MitigationActionType', ], 'actionArn' => [ 'shape' => 'MitigationActionArn', ], 'actionId' => [ 'shape' => 'MitigationActionId', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'actionParams' => [ 'shape' => 'MitigationActionParams', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastModifiedDate' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeProvisioningTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'templateName', ], 'members' => [ 'templateName' => [ 'shape' => 'TemplateName', 'location' => 'uri', 'locationName' => 'templateName', ], ], ], 'DescribeProvisioningTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'templateArn' => [ 'shape' => 'TemplateArn', ], 'templateName' => [ 'shape' => 'TemplateName', ], 'description' => [ 'shape' => 'TemplateDescription', ], 'creationDate' => [ 'shape' => 'DateType', ], 'lastModifiedDate' => [ 'shape' => 'DateType', ], 'defaultVersionId' => [ 'shape' => 'TemplateVersionId', ], 'templateBody' => [ 'shape' => 'TemplateBody', ], 'enabled' => [ 'shape' => 'Enabled', ], 'provisioningRoleArn' => [ 'shape' => 'RoleArn', ], 'preProvisioningHook' => [ 'shape' => 'ProvisioningHook', ], 'type' => [ 'shape' => 'TemplateType', ], ], ], 'DescribeProvisioningTemplateVersionRequest' => [ 'type' => 'structure', 'required' => [ 'templateName', 'versionId', ], 'members' => [ 'templateName' => [ 'shape' => 'TemplateName', 'location' => 'uri', 'locationName' => 'templateName', ], 'versionId' => [ 'shape' => 'TemplateVersionId', 'location' => 'uri', 'locationName' => 'versionId', ], ], ], 'DescribeProvisioningTemplateVersionResponse' => [ 'type' => 'structure', 'members' => [ 'versionId' => [ 'shape' => 'TemplateVersionId', ], 'creationDate' => [ 'shape' => 'DateType', ], 'templateBody' => [ 'shape' => 'TemplateBody', ], 'isDefaultVersion' => [ 'shape' => 'IsDefaultVersion', ], ], ], 'DescribeRoleAliasRequest' => [ 'type' => 'structure', 'required' => [ 'roleAlias', ], 'members' => [ 'roleAlias' => [ 'shape' => 'RoleAlias', 'location' => 'uri', 'locationName' => 'roleAlias', ], ], ], 'DescribeRoleAliasResponse' => [ 'type' => 'structure', 'members' => [ 'roleAliasDescription' => [ 'shape' => 'RoleAliasDescription', ], ], ], 'DescribeScheduledAuditRequest' => [ 'type' => 'structure', 'required' => [ 'scheduledAuditName', ], 'members' => [ 'scheduledAuditName' => [ 'shape' => 'ScheduledAuditName', 'location' => 'uri', 'locationName' => 'scheduledAuditName', ], ], ], 'DescribeScheduledAuditResponse' => [ 'type' => 'structure', 'members' => [ 'frequency' => [ 'shape' => 'AuditFrequency', ], 'dayOfMonth' => [ 'shape' => 'DayOfMonth', ], 'dayOfWeek' => [ 'shape' => 'DayOfWeek', ], 'targetCheckNames' => [ 'shape' => 'TargetAuditCheckNames', ], 'scheduledAuditName' => [ 'shape' => 'ScheduledAuditName', ], 'scheduledAuditArn' => [ 'shape' => 'ScheduledAuditArn', ], ], ], 'DescribeSecurityProfileRequest' => [ 'type' => 'structure', 'required' => [ 'securityProfileName', ], 'members' => [ 'securityProfileName' => [ 'shape' => 'SecurityProfileName', 'location' => 'uri', 'locationName' => 'securityProfileName', ], ], ], 'DescribeSecurityProfileResponse' => [ 'type' => 'structure', 'members' => [ 'securityProfileName' => [ 'shape' => 'SecurityProfileName', ], 'securityProfileArn' => [ 'shape' => 'SecurityProfileArn', ], 'securityProfileDescription' => [ 'shape' => 'SecurityProfileDescription', ], 'behaviors' => [ 'shape' => 'Behaviors', ], 'alertTargets' => [ 'shape' => 'AlertTargets', ], 'additionalMetricsToRetain' => [ 'shape' => 'AdditionalMetricsToRetainList', 'deprecated' => true, 'deprecatedMessage' => 'Use additionalMetricsToRetainV2.', ], 'additionalMetricsToRetainV2' => [ 'shape' => 'AdditionalMetricsToRetainV2List', ], 'version' => [ 'shape' => 'Version', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastModifiedDate' => [ 'shape' => 'Timestamp', ], 'metricsExportConfig' => [ 'shape' => 'MetricsExportConfig', ], ], ], 'DescribeStreamRequest' => [ 'type' => 'structure', 'required' => [ 'streamId', ], 'members' => [ 'streamId' => [ 'shape' => 'StreamId', 'location' => 'uri', 'locationName' => 'streamId', ], ], ], 'DescribeStreamResponse' => [ 'type' => 'structure', 'members' => [ 'streamInfo' => [ 'shape' => 'StreamInfo', ], ], ], 'DescribeThingGroupRequest' => [ 'type' => 'structure', 'required' => [ 'thingGroupName', ], 'members' => [ 'thingGroupName' => [ 'shape' => 'ThingGroupName', 'location' => 'uri', 'locationName' => 'thingGroupName', ], ], ], 'DescribeThingGroupResponse' => [ 'type' => 'structure', 'members' => [ 'thingGroupName' => [ 'shape' => 'ThingGroupName', ], 'thingGroupId' => [ 'shape' => 'ThingGroupId', ], 'thingGroupArn' => [ 'shape' => 'ThingGroupArn', ], 'version' => [ 'shape' => 'Version', ], 'thingGroupProperties' => [ 'shape' => 'ThingGroupProperties', ], 'thingGroupMetadata' => [ 'shape' => 'ThingGroupMetadata', ], 'indexName' => [ 'shape' => 'IndexName', ], 'queryString' => [ 'shape' => 'QueryString', ], 'queryVersion' => [ 'shape' => 'QueryVersion', ], 'status' => [ 'shape' => 'DynamicGroupStatus', ], ], ], 'DescribeThingRegistrationTaskRequest' => [ 'type' => 'structure', 'required' => [ 'taskId', ], 'members' => [ 'taskId' => [ 'shape' => 'TaskId', 'location' => 'uri', 'locationName' => 'taskId', ], ], ], 'DescribeThingRegistrationTaskResponse' => [ 'type' => 'structure', 'members' => [ 'taskId' => [ 'shape' => 'TaskId', ], 'creationDate' => [ 'shape' => 'CreationDate', ], 'lastModifiedDate' => [ 'shape' => 'LastModifiedDate', ], 'templateBody' => [ 'shape' => 'TemplateBody', ], 'inputFileBucket' => [ 'shape' => 'RegistryS3BucketName', ], 'inputFileKey' => [ 'shape' => 'RegistryS3KeyName', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'status' => [ 'shape' => 'Status', ], 'message' => [ 'shape' => 'ErrorMessage', ], 'successCount' => [ 'shape' => 'Count', ], 'failureCount' => [ 'shape' => 'Count', ], 'percentageProgress' => [ 'shape' => 'Percentage', ], ], ], 'DescribeThingRequest' => [ 'type' => 'structure', 'required' => [ 'thingName', ], 'members' => [ 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], ], ], 'DescribeThingResponse' => [ 'type' => 'structure', 'members' => [ 'defaultClientId' => [ 'shape' => 'ClientId', ], 'thingName' => [ 'shape' => 'ThingName', ], 'thingId' => [ 'shape' => 'ThingId', ], 'thingArn' => [ 'shape' => 'ThingArn', ], 'thingTypeName' => [ 'shape' => 'ThingTypeName', ], 'attributes' => [ 'shape' => 'Attributes', ], 'version' => [ 'shape' => 'Version', ], 'billingGroupName' => [ 'shape' => 'BillingGroupName', ], ], ], 'DescribeThingTypeRequest' => [ 'type' => 'structure', 'required' => [ 'thingTypeName', ], 'members' => [ 'thingTypeName' => [ 'shape' => 'ThingTypeName', 'location' => 'uri', 'locationName' => 'thingTypeName', ], ], ], 'DescribeThingTypeResponse' => [ 'type' => 'structure', 'members' => [ 'thingTypeName' => [ 'shape' => 'ThingTypeName', ], 'thingTypeId' => [ 'shape' => 'ThingTypeId', ], 'thingTypeArn' => [ 'shape' => 'ThingTypeArn', ], 'thingTypeProperties' => [ 'shape' => 'ThingTypeProperties', ], 'thingTypeMetadata' => [ 'shape' => 'ThingTypeMetadata', ], ], ], 'Description' => [ 'type' => 'string', ], 'Destination' => [ 'type' => 'structure', 'members' => [ 's3Destination' => [ 'shape' => 'S3Destination', ], ], ], 'DestinationPackageVersions' => [ 'type' => 'list', 'member' => [ 'shape' => 'PackageVersionArn', ], ], 'DetachPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', 'target', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'policyName', ], 'target' => [ 'shape' => 'PolicyTarget', ], ], ], 'DetachPrincipalPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', 'principal', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'policyName', ], 'principal' => [ 'shape' => 'Principal', 'location' => 'header', 'locationName' => 'x-amzn-iot-principal', ], ], ], 'DetachSecurityProfileRequest' => [ 'type' => 'structure', 'required' => [ 'securityProfileName', 'securityProfileTargetArn', ], 'members' => [ 'securityProfileName' => [ 'shape' => 'SecurityProfileName', 'location' => 'uri', 'locationName' => 'securityProfileName', ], 'securityProfileTargetArn' => [ 'shape' => 'SecurityProfileTargetArn', 'location' => 'querystring', 'locationName' => 'securityProfileTargetArn', ], ], ], 'DetachSecurityProfileResponse' => [ 'type' => 'structure', 'members' => [], ], 'DetachThingPrincipalRequest' => [ 'type' => 'structure', 'required' => [ 'thingName', 'principal', ], 'members' => [ 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], 'principal' => [ 'shape' => 'Principal', 'location' => 'header', 'locationName' => 'x-amzn-principal', ], ], ], 'DetachThingPrincipalResponse' => [ 'type' => 'structure', 'members' => [], ], 'DetailsKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9:_-]+', ], 'DetailsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'DetailsKey', ], 'value' => [ 'shape' => 'DetailsValue', ], ], 'DetailsValue' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[^\\p{C}]+', ], 'DetectMitigationActionExecution' => [ 'type' => 'structure', 'members' => [ 'taskId' => [ 'shape' => 'MitigationActionsTaskId', ], 'violationId' => [ 'shape' => 'ViolationId', ], 'actionName' => [ 'shape' => 'MitigationActionName', ], 'thingName' => [ 'shape' => 'DeviceDefenderThingName', ], 'executionStartDate' => [ 'shape' => 'Timestamp', ], 'executionEndDate' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'DetectMitigationActionExecutionStatus', ], 'errorCode' => [ 'shape' => 'DetectMitigationActionExecutionErrorCode', ], 'message' => [ 'shape' => 'ErrorMessage', ], ], ], 'DetectMitigationActionExecutionErrorCode' => [ 'type' => 'string', ], 'DetectMitigationActionExecutionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DetectMitigationActionExecution', ], ], 'DetectMitigationActionExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'SUCCESSFUL', 'FAILED', 'SKIPPED', ], ], 'DetectMitigationActionsTaskStatistics' => [ 'type' => 'structure', 'members' => [ 'actionsExecuted' => [ 'shape' => 'GenericLongValue', ], 'actionsSkipped' => [ 'shape' => 'GenericLongValue', ], 'actionsFailed' => [ 'shape' => 'GenericLongValue', ], ], ], 'DetectMitigationActionsTaskStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'SUCCESSFUL', 'FAILED', 'CANCELED', ], ], 'DetectMitigationActionsTaskSummary' => [ 'type' => 'structure', 'members' => [ 'taskId' => [ 'shape' => 'MitigationActionsTaskId', ], 'taskStatus' => [ 'shape' => 'DetectMitigationActionsTaskStatus', ], 'taskStartTime' => [ 'shape' => 'Timestamp', ], 'taskEndTime' => [ 'shape' => 'Timestamp', ], 'target' => [ 'shape' => 'DetectMitigationActionsTaskTarget', ], 'violationEventOccurrenceRange' => [ 'shape' => 'ViolationEventOccurrenceRange', ], 'onlyActiveViolationsIncluded' => [ 'shape' => 'PrimitiveBoolean', ], 'suppressedAlertsIncluded' => [ 'shape' => 'PrimitiveBoolean', ], 'actionsDefinition' => [ 'shape' => 'MitigationActionList', ], 'taskStatistics' => [ 'shape' => 'DetectMitigationActionsTaskStatistics', ], ], ], 'DetectMitigationActionsTaskSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DetectMitigationActionsTaskSummary', ], ], 'DetectMitigationActionsTaskTarget' => [ 'type' => 'structure', 'members' => [ 'violationIds' => [ 'shape' => 'TargetViolationIdsForDetectMitigationActions', ], 'securityProfileName' => [ 'shape' => 'SecurityProfileName', ], 'behaviorName' => [ 'shape' => 'BehaviorName', ], ], ], 'DetectMitigationActionsToExecuteList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MitigationActionName', ], 'max' => 5, 'min' => 1, ], 'DeviceCertificateUpdateAction' => [ 'type' => 'string', 'enum' => [ 'DEACTIVATE', ], ], 'DeviceDefenderIndexingMode' => [ 'type' => 'string', 'enum' => [ 'OFF', 'VIOLATIONS', ], ], 'DeviceDefenderThingName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'DimensionArn' => [ 'type' => 'string', ], 'DimensionName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9:_-]+', ], 'DimensionNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'DimensionName', ], ], 'DimensionStringValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'DimensionStringValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'DimensionStringValue', ], 'max' => 100, 'min' => 1, ], 'DimensionType' => [ 'type' => 'string', 'enum' => [ 'TOPIC_FILTER', ], ], 'DimensionValueOperator' => [ 'type' => 'string', 'enum' => [ 'IN', 'NOT_IN', ], ], 'DisableAllLogs' => [ 'type' => 'boolean', ], 'DisableTopicRuleRequest' => [ 'type' => 'structure', 'required' => [ 'ruleName', ], 'members' => [ 'ruleName' => [ 'shape' => 'RuleName', 'location' => 'uri', 'locationName' => 'ruleName', ], ], ], 'DisassociateSbomFromPackageVersionRequest' => [ 'type' => 'structure', 'required' => [ 'packageName', 'versionName', ], 'members' => [ 'packageName' => [ 'shape' => 'PackageName', 'location' => 'uri', 'locationName' => 'packageName', ], 'versionName' => [ 'shape' => 'VersionName', 'location' => 'uri', 'locationName' => 'versionName', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DisassociateSbomFromPackageVersionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisconnectReason' => [ 'type' => 'string', ], 'DisconnectReasonValue' => [ 'type' => 'string', 'enum' => [ 'AUTH_ERROR', 'CLIENT_INITIATED_DISCONNECT', 'CLIENT_ERROR', 'CONNECTION_LOST', 'DUPLICATE_CLIENTID', 'FORBIDDEN_ACCESS', 'MQTT_KEEP_ALIVE_TIMEOUT', 'SERVER_ERROR', 'SERVER_INITIATED_DISCONNECT', 'THROTTLED', 'WEBSOCKET_TTL_EXPIRATION', 'CUSTOMAUTH_TTL_EXPIRATION', 'UNKNOWN', 'NONE', ], ], 'DisplayName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[^\\p{C}]*', ], 'DocumentParameter' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'ParameterKey', ], 'description' => [ 'shape' => 'JobDescription', ], 'regex' => [ 'shape' => 'Regex', ], 'example' => [ 'shape' => 'Example', ], 'optional' => [ 'shape' => 'Optional', ], ], ], 'DocumentParameters' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentParameter', ], ], 'DomainConfigurationArn' => [ 'type' => 'string', ], 'DomainConfigurationName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\w.-]+', ], 'DomainConfigurationStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'DomainConfigurationSummary' => [ 'type' => 'structure', 'members' => [ 'domainConfigurationName' => [ 'shape' => 'ReservedDomainConfigurationName', ], 'domainConfigurationArn' => [ 'shape' => 'DomainConfigurationArn', ], 'serviceType' => [ 'shape' => 'ServiceType', ], ], ], 'DomainConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainConfigurationSummary', ], ], 'DomainName' => [ 'type' => 'string', 'max' => 253, 'min' => 1, 'pattern' => '[\\s\\S]*', ], 'DomainType' => [ 'type' => 'string', 'enum' => [ 'ENDPOINT', 'AWS_MANAGED', 'CUSTOMER_MANAGED', ], ], 'DoubleParameterValue' => [ 'type' => 'double', ], 'DurationInMinutes' => [ 'type' => 'integer', 'max' => 1430, 'min' => 1, ], 'DurationSeconds' => [ 'type' => 'integer', ], 'DynamicGroupStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'BUILDING', 'REBUILDING', ], ], 'DynamoDBAction' => [ 'type' => 'structure', 'required' => [ 'tableName', 'roleArn', 'hashKeyField', 'hashKeyValue', ], 'members' => [ 'tableName' => [ 'shape' => 'TableName', ], 'roleArn' => [ 'shape' => 'AwsArn', ], 'operation' => [ 'shape' => 'DynamoOperation', ], 'hashKeyField' => [ 'shape' => 'HashKeyField', ], 'hashKeyValue' => [ 'shape' => 'HashKeyValue', ], 'hashKeyType' => [ 'shape' => 'DynamoKeyType', ], 'rangeKeyField' => [ 'shape' => 'RangeKeyField', ], 'rangeKeyValue' => [ 'shape' => 'RangeKeyValue', ], 'rangeKeyType' => [ 'shape' => 'DynamoKeyType', ], 'payloadField' => [ 'shape' => 'PayloadField', ], ], ], 'DynamoDBv2Action' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'putItem', ], 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'putItem' => [ 'shape' => 'PutItemInput', ], ], ], 'DynamoKeyType' => [ 'type' => 'string', 'enum' => [ 'STRING', 'NUMBER', ], ], 'DynamoOperation' => [ 'type' => 'string', ], 'EffectivePolicies' => [ 'type' => 'list', 'member' => [ 'shape' => 'EffectivePolicy', ], ], 'EffectivePolicy' => [ 'type' => 'structure', 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', ], 'policyArn' => [ 'shape' => 'PolicyArn', ], 'policyDocument' => [ 'shape' => 'PolicyDocument', ], ], ], 'ElasticsearchAction' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'endpoint', 'index', 'type', 'id', ], 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'endpoint' => [ 'shape' => 'ElasticsearchEndpoint', ], 'index' => [ 'shape' => 'ElasticsearchIndex', ], 'type' => [ 'shape' => 'ElasticsearchType', ], 'id' => [ 'shape' => 'ElasticsearchId', ], ], ], 'ElasticsearchEndpoint' => [ 'type' => 'string', 'pattern' => 'https?://.*', ], 'ElasticsearchId' => [ 'type' => 'string', ], 'ElasticsearchIndex' => [ 'type' => 'string', ], 'ElasticsearchType' => [ 'type' => 'string', ], 'EnableCachingForHttp' => [ 'type' => 'boolean', ], 'EnableIoTLoggingParams' => [ 'type' => 'structure', 'required' => [ 'roleArnForLogging', 'logLevel', ], 'members' => [ 'roleArnForLogging' => [ 'shape' => 'RoleArn', ], 'logLevel' => [ 'shape' => 'LogLevel', ], ], ], 'EnableOCSPCheck' => [ 'type' => 'boolean', ], 'EnableTopicRuleRequest' => [ 'type' => 'structure', 'required' => [ 'ruleName', ], 'members' => [ 'ruleName' => [ 'shape' => 'RuleName', 'location' => 'uri', 'locationName' => 'ruleName', ], ], ], 'Enabled' => [ 'type' => 'boolean', ], 'EnabledBoolean' => [ 'type' => 'boolean', 'box' => true, ], 'EndpointAddress' => [ 'type' => 'string', ], 'EndpointType' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[\\s\\S]*', ], 'Environment' => [ 'type' => 'string', 'pattern' => '[^\\p{C}]+', ], 'Environments' => [ 'type' => 'list', 'member' => [ 'shape' => 'Environment', ], ], 'ErrorCode' => [ 'type' => 'string', ], 'ErrorInfo' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'Code', ], 'message' => [ 'shape' => 'OTAUpdateErrorMessage', ], ], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 2048, ], 'EvaluationStatistic' => [ 'type' => 'string', 'pattern' => '(p0|p0\\.1|p0\\.01|p1|p10|p50|p90|p99|p99\\.9|p99\\.99|p100)', ], 'EventConfigurations' => [ 'type' => 'map', 'key' => [ 'shape' => 'EventType', ], 'value' => [ 'shape' => 'Configuration', ], ], 'EventType' => [ 'type' => 'string', 'enum' => [ 'THING', 'THING_GROUP', 'THING_TYPE', 'THING_GROUP_MEMBERSHIP', 'THING_GROUP_HIERARCHY', 'THING_TYPE_ASSOCIATION', 'JOB', 'JOB_EXECUTION', 'POLICY', 'CERTIFICATE', 'CA_CERTIFICATE', ], ], 'Example' => [ 'type' => 'string', 'pattern' => '[^\\p{C}]+', ], 'ExecutionNamePrefix' => [ 'type' => 'string', ], 'ExecutionNumber' => [ 'type' => 'long', ], 'ExpectedVersion' => [ 'type' => 'long', ], 'ExpiresInSec' => [ 'type' => 'long', 'max' => 3600, 'min' => 60, ], 'ExpiresInSeconds' => [ 'type' => 'long', ], 'ExplicitDeny' => [ 'type' => 'structure', 'members' => [ 'policies' => [ 'shape' => 'Policies', ], ], ], 'ExponentialRolloutRate' => [ 'type' => 'structure', 'required' => [ 'baseRatePerMinute', 'incrementFactor', 'rateIncreaseCriteria', ], 'members' => [ 'baseRatePerMinute' => [ 'shape' => 'RolloutRatePerMinute', ], 'incrementFactor' => [ 'shape' => 'IncrementFactor', ], 'rateIncreaseCriteria' => [ 'shape' => 'RateIncreaseCriteria', ], ], ], 'ExportMetric' => [ 'type' => 'boolean', ], 'FailedChecksCount' => [ 'type' => 'integer', ], 'FailedFindingsCount' => [ 'type' => 'long', ], 'FailedThings' => [ 'type' => 'integer', ], 'Field' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'FieldName', ], 'type' => [ 'shape' => 'FieldType', ], ], ], 'FieldName' => [ 'type' => 'string', ], 'FieldType' => [ 'type' => 'string', 'enum' => [ 'Number', 'String', 'Boolean', ], ], 'Fields' => [ 'type' => 'list', 'member' => [ 'shape' => 'Field', ], ], 'FileId' => [ 'type' => 'integer', 'max' => 255, 'min' => 0, ], 'FileLocation' => [ 'type' => 'structure', 'members' => [ 'stream' => [ 'shape' => 'Stream', ], 's3Location' => [ 'shape' => 'S3Location', ], ], ], 'FileName' => [ 'type' => 'string', ], 'FileType' => [ 'type' => 'integer', 'max' => 255, 'min' => 0, ], 'FindingId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'FindingIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingId', ], 'max' => 25, 'min' => 1, ], 'FirehoseAction' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'deliveryStreamName', ], 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'deliveryStreamName' => [ 'shape' => 'DeliveryStreamName', ], 'separator' => [ 'shape' => 'FirehoseSeparator', ], 'batchMode' => [ 'shape' => 'BatchMode', ], ], ], 'FirehoseSeparator' => [ 'type' => 'string', 'pattern' => '([\\n\\t])|(\\r\\n)|(,)', ], 'Flag' => [ 'type' => 'boolean', ], 'FleetMetricArn' => [ 'type' => 'string', ], 'FleetMetricDescription' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '[\\p{Graph}\\x20]*', ], 'FleetMetricName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9_\\-\\.]+', ], 'FleetMetricNameAndArn' => [ 'type' => 'structure', 'members' => [ 'metricName' => [ 'shape' => 'FleetMetricName', ], 'metricArn' => [ 'shape' => 'FleetMetricArn', ], ], ], 'FleetMetricNameAndArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetMetricNameAndArn', ], ], 'FleetMetricPeriod' => [ 'type' => 'integer', 'max' => 86400, 'min' => 60, ], 'FleetMetricUnit' => [ 'type' => 'string', 'enum' => [ 'Seconds', 'Microseconds', 'Milliseconds', 'Bytes', 'Kilobytes', 'Megabytes', 'Gigabytes', 'Terabytes', 'Bits', 'Kilobits', 'Megabits', 'Gigabits', 'Terabits', 'Percent', 'Count', 'Bytes/Second', 'Kilobytes/Second', 'Megabytes/Second', 'Gigabytes/Second', 'Terabytes/Second', 'Bits/Second', 'Kilobits/Second', 'Megabits/Second', 'Gigabits/Second', 'Terabits/Second', 'Count/Second', 'None', ], ], 'ForceDelete' => [ 'type' => 'boolean', ], 'ForceDeleteAWSJob' => [ 'type' => 'boolean', ], 'ForceFlag' => [ 'type' => 'boolean', ], 'Forced' => [ 'type' => 'boolean', ], 'FunctionArn' => [ 'type' => 'string', ], 'GenerationId' => [ 'type' => 'string', ], 'GenericLongValue' => [ 'type' => 'long', ], 'GeoLocationTarget' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'TargetFieldName', ], 'order' => [ 'shape' => 'TargetFieldOrder', ], ], ], 'GeoLocationsFilter' => [ 'type' => 'list', 'member' => [ 'shape' => 'GeoLocationTarget', ], ], 'GetBehaviorModelTrainingSummariesRequest' => [ 'type' => 'structure', 'members' => [ 'securityProfileName' => [ 'shape' => 'SecurityProfileName', 'location' => 'querystring', 'locationName' => 'securityProfileName', ], 'maxResults' => [ 'shape' => 'TinyMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetBehaviorModelTrainingSummariesResponse' => [ 'type' => 'structure', 'members' => [ 'summaries' => [ 'shape' => 'BehaviorModelTrainingSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetBucketsAggregationRequest' => [ 'type' => 'structure', 'required' => [ 'queryString', 'aggregationField', 'bucketsAggregationType', ], 'members' => [ 'indexName' => [ 'shape' => 'IndexName', ], 'queryString' => [ 'shape' => 'QueryString', ], 'aggregationField' => [ 'shape' => 'AggregationField', ], 'queryVersion' => [ 'shape' => 'QueryVersion', ], 'bucketsAggregationType' => [ 'shape' => 'BucketsAggregationType', ], ], ], 'GetBucketsAggregationResponse' => [ 'type' => 'structure', 'members' => [ 'totalCount' => [ 'shape' => 'Count', ], 'buckets' => [ 'shape' => 'Buckets', ], ], ], 'GetCardinalityRequest' => [ 'type' => 'structure', 'required' => [ 'queryString', ], 'members' => [ 'indexName' => [ 'shape' => 'IndexName', ], 'queryString' => [ 'shape' => 'QueryString', ], 'aggregationField' => [ 'shape' => 'AggregationField', ], 'queryVersion' => [ 'shape' => 'QueryVersion', ], ], ], 'GetCardinalityResponse' => [ 'type' => 'structure', 'members' => [ 'cardinality' => [ 'shape' => 'Count', ], ], ], 'GetCommandExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'executionId', 'targetArn', ], 'members' => [ 'executionId' => [ 'shape' => 'CommandExecutionId', 'location' => 'uri', 'locationName' => 'executionId', ], 'targetArn' => [ 'shape' => 'TargetArn', 'location' => 'querystring', 'locationName' => 'targetArn', ], 'includeResult' => [ 'shape' => 'BooleanWrapperObject', 'location' => 'querystring', 'locationName' => 'includeResult', ], ], ], 'GetCommandExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'executionId' => [ 'shape' => 'CommandExecutionId', ], 'commandArn' => [ 'shape' => 'CommandArn', ], 'targetArn' => [ 'shape' => 'TargetArn', ], 'status' => [ 'shape' => 'CommandExecutionStatus', ], 'statusReason' => [ 'shape' => 'StatusReason', ], 'result' => [ 'shape' => 'CommandExecutionResultMap', ], 'parameters' => [ 'shape' => 'CommandExecutionParameterMap', ], 'executionTimeoutSeconds' => [ 'shape' => 'CommandExecutionTimeoutInSeconds', ], 'createdAt' => [ 'shape' => 'DateType', ], 'lastUpdatedAt' => [ 'shape' => 'DateType', ], 'startedAt' => [ 'shape' => 'DateType', ], 'completedAt' => [ 'shape' => 'DateType', ], 'timeToLive' => [ 'shape' => 'DateType', ], ], ], 'GetCommandRequest' => [ 'type' => 'structure', 'required' => [ 'commandId', ], 'members' => [ 'commandId' => [ 'shape' => 'CommandId', 'location' => 'uri', 'locationName' => 'commandId', ], ], ], 'GetCommandResponse' => [ 'type' => 'structure', 'members' => [ 'commandId' => [ 'shape' => 'CommandId', ], 'commandArn' => [ 'shape' => 'CommandArn', ], 'namespace' => [ 'shape' => 'CommandNamespace', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'description' => [ 'shape' => 'CommandDescription', ], 'mandatoryParameters' => [ 'shape' => 'CommandParameterList', ], 'payload' => [ 'shape' => 'CommandPayload', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'createdAt' => [ 'shape' => 'DateType', ], 'lastUpdatedAt' => [ 'shape' => 'DateType', ], 'deprecated' => [ 'shape' => 'DeprecationFlag', ], 'pendingDeletion' => [ 'shape' => 'BooleanWrapperObject', ], ], ], 'GetEffectivePoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'principal' => [ 'shape' => 'Principal', ], 'cognitoIdentityPoolId' => [ 'shape' => 'CognitoIdentityPoolId', ], 'thingName' => [ 'shape' => 'ThingName', 'location' => 'querystring', 'locationName' => 'thingName', ], ], ], 'GetEffectivePoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'effectivePolicies' => [ 'shape' => 'EffectivePolicies', ], ], ], 'GetIndexingConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetIndexingConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'thingIndexingConfiguration' => [ 'shape' => 'ThingIndexingConfiguration', ], 'thingGroupIndexingConfiguration' => [ 'shape' => 'ThingGroupIndexingConfiguration', ], ], ], 'GetJobDocumentRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'beforeSubstitution' => [ 'shape' => 'BeforeSubstitutionFlag', 'location' => 'querystring', 'locationName' => 'beforeSubstitution', ], ], ], 'GetJobDocumentResponse' => [ 'type' => 'structure', 'members' => [ 'document' => [ 'shape' => 'JobDocument', ], ], ], 'GetLoggingOptionsRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetLoggingOptionsResponse' => [ 'type' => 'structure', 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'logLevel' => [ 'shape' => 'LogLevel', ], ], ], 'GetOTAUpdateRequest' => [ 'type' => 'structure', 'required' => [ 'otaUpdateId', ], 'members' => [ 'otaUpdateId' => [ 'shape' => 'OTAUpdateId', 'location' => 'uri', 'locationName' => 'otaUpdateId', ], ], ], 'GetOTAUpdateResponse' => [ 'type' => 'structure', 'members' => [ 'otaUpdateInfo' => [ 'shape' => 'OTAUpdateInfo', ], ], ], 'GetPackageConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetPackageConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'versionUpdateByJobsConfig' => [ 'shape' => 'VersionUpdateByJobsConfig', ], ], ], 'GetPackageRequest' => [ 'type' => 'structure', 'required' => [ 'packageName', ], 'members' => [ 'packageName' => [ 'shape' => 'PackageName', 'location' => 'uri', 'locationName' => 'packageName', ], ], ], 'GetPackageResponse' => [ 'type' => 'structure', 'members' => [ 'packageName' => [ 'shape' => 'PackageName', ], 'packageArn' => [ 'shape' => 'PackageArn', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'defaultVersionName' => [ 'shape' => 'VersionName', ], 'creationDate' => [ 'shape' => 'CreationDate', ], 'lastModifiedDate' => [ 'shape' => 'LastModifiedDate', ], ], ], 'GetPackageVersionRequest' => [ 'type' => 'structure', 'required' => [ 'packageName', 'versionName', ], 'members' => [ 'packageName' => [ 'shape' => 'PackageName', 'location' => 'uri', 'locationName' => 'packageName', ], 'versionName' => [ 'shape' => 'VersionName', 'location' => 'uri', 'locationName' => 'versionName', ], ], ], 'GetPackageVersionResponse' => [ 'type' => 'structure', 'members' => [ 'packageVersionArn' => [ 'shape' => 'PackageVersionArn', ], 'packageName' => [ 'shape' => 'PackageName', ], 'versionName' => [ 'shape' => 'VersionName', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'attributes' => [ 'shape' => 'ResourceAttributes', ], 'artifact' => [ 'shape' => 'PackageVersionArtifact', ], 'status' => [ 'shape' => 'PackageVersionStatus', ], 'errorReason' => [ 'shape' => 'PackageVersionErrorReason', ], 'creationDate' => [ 'shape' => 'CreationDate', ], 'lastModifiedDate' => [ 'shape' => 'LastModifiedDate', ], 'sbom' => [ 'shape' => 'Sbom', ], 'sbomValidationStatus' => [ 'shape' => 'SbomValidationStatus', ], 'recipe' => [ 'shape' => 'PackageVersionRecipe', ], ], ], 'GetPercentilesRequest' => [ 'type' => 'structure', 'required' => [ 'queryString', ], 'members' => [ 'indexName' => [ 'shape' => 'IndexName', ], 'queryString' => [ 'shape' => 'QueryString', ], 'aggregationField' => [ 'shape' => 'AggregationField', ], 'queryVersion' => [ 'shape' => 'QueryVersion', ], 'percents' => [ 'shape' => 'PercentList', ], ], ], 'GetPercentilesResponse' => [ 'type' => 'structure', 'members' => [ 'percentiles' => [ 'shape' => 'Percentiles', ], ], ], 'GetPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'policyName', ], ], ], 'GetPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', ], 'policyArn' => [ 'shape' => 'PolicyArn', ], 'policyDocument' => [ 'shape' => 'PolicyDocument', ], 'defaultVersionId' => [ 'shape' => 'PolicyVersionId', ], 'creationDate' => [ 'shape' => 'DateType', ], 'lastModifiedDate' => [ 'shape' => 'DateType', ], 'generationId' => [ 'shape' => 'GenerationId', ], ], ], 'GetPolicyVersionRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', 'policyVersionId', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'policyName', ], 'policyVersionId' => [ 'shape' => 'PolicyVersionId', 'location' => 'uri', 'locationName' => 'policyVersionId', ], ], ], 'GetPolicyVersionResponse' => [ 'type' => 'structure', 'members' => [ 'policyArn' => [ 'shape' => 'PolicyArn', ], 'policyName' => [ 'shape' => 'PolicyName', ], 'policyDocument' => [ 'shape' => 'PolicyDocument', ], 'policyVersionId' => [ 'shape' => 'PolicyVersionId', ], 'isDefaultVersion' => [ 'shape' => 'IsDefaultVersion', ], 'creationDate' => [ 'shape' => 'DateType', ], 'lastModifiedDate' => [ 'shape' => 'DateType', ], 'generationId' => [ 'shape' => 'GenerationId', ], ], ], 'GetRegistrationCodeRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetRegistrationCodeResponse' => [ 'type' => 'structure', 'members' => [ 'registrationCode' => [ 'shape' => 'RegistrationCode', ], ], ], 'GetStatisticsRequest' => [ 'type' => 'structure', 'required' => [ 'queryString', ], 'members' => [ 'indexName' => [ 'shape' => 'IndexName', ], 'queryString' => [ 'shape' => 'QueryString', ], 'aggregationField' => [ 'shape' => 'AggregationField', ], 'queryVersion' => [ 'shape' => 'QueryVersion', ], ], ], 'GetStatisticsResponse' => [ 'type' => 'structure', 'members' => [ 'statistics' => [ 'shape' => 'Statistics', ], ], ], 'GetThingConnectivityDataRequest' => [ 'type' => 'structure', 'required' => [ 'thingName', ], 'members' => [ 'thingName' => [ 'shape' => 'ConnectivityApiThingName', 'location' => 'uri', 'locationName' => 'thingName', ], ], ], 'GetThingConnectivityDataResponse' => [ 'type' => 'structure', 'members' => [ 'thingName' => [ 'shape' => 'ConnectivityApiThingName', ], 'connected' => [ 'shape' => 'Boolean', ], 'timestamp' => [ 'shape' => 'Timestamp', ], 'disconnectReason' => [ 'shape' => 'DisconnectReasonValue', ], ], ], 'GetTopicRuleDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'AwsArn', 'location' => 'uri', 'locationName' => 'arn', ], ], ], 'GetTopicRuleDestinationResponse' => [ 'type' => 'structure', 'members' => [ 'topicRuleDestination' => [ 'shape' => 'TopicRuleDestination', ], ], ], 'GetTopicRuleRequest' => [ 'type' => 'structure', 'required' => [ 'ruleName', ], 'members' => [ 'ruleName' => [ 'shape' => 'RuleName', 'location' => 'uri', 'locationName' => 'ruleName', ], ], ], 'GetTopicRuleResponse' => [ 'type' => 'structure', 'members' => [ 'ruleArn' => [ 'shape' => 'RuleArn', ], 'rule' => [ 'shape' => 'TopicRule', ], ], ], 'GetV2LoggingOptionsRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetV2LoggingOptionsResponse' => [ 'type' => 'structure', 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'defaultLogLevel' => [ 'shape' => 'LogLevel', ], 'disableAllLogs' => [ 'shape' => 'DisableAllLogs', ], ], ], 'GroupNameAndArn' => [ 'type' => 'structure', 'members' => [ 'groupName' => [ 'shape' => 'ThingGroupName', ], 'groupArn' => [ 'shape' => 'ThingGroupArn', ], ], ], 'HashAlgorithm' => [ 'type' => 'string', ], 'HashKeyField' => [ 'type' => 'string', ], 'HashKeyValue' => [ 'type' => 'string', ], 'HeaderKey' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'HeaderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HttpActionHeader', ], 'max' => 100, 'min' => 0, ], 'HeaderValue' => [ 'type' => 'string', ], 'HttpAction' => [ 'type' => 'structure', 'required' => [ 'url', ], 'members' => [ 'url' => [ 'shape' => 'Url', ], 'confirmationUrl' => [ 'shape' => 'Url', ], 'headers' => [ 'shape' => 'HeaderList', ], 'auth' => [ 'shape' => 'HttpAuthorization', ], ], ], 'HttpActionHeader' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'HeaderKey', ], 'value' => [ 'shape' => 'HeaderValue', ], ], ], 'HttpAuthorization' => [ 'type' => 'structure', 'members' => [ 'sigv4' => [ 'shape' => 'SigV4Authorization', ], ], ], 'HttpContext' => [ 'type' => 'structure', 'members' => [ 'headers' => [ 'shape' => 'HttpHeaders', ], 'queryString' => [ 'shape' => 'HttpQueryString', ], ], ], 'HttpHeaderName' => [ 'type' => 'string', 'max' => 8192, 'min' => 1, 'pattern' => '[\\s\\S]*', ], 'HttpHeaderValue' => [ 'type' => 'string', 'max' => 8192, 'min' => 1, 'pattern' => '[\\s\\S]*', ], 'HttpHeaders' => [ 'type' => 'map', 'key' => [ 'shape' => 'HttpHeaderName', ], 'value' => [ 'shape' => 'HttpHeaderValue', ], ], 'HttpQueryString' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '[\\s\\S]*', ], 'HttpUrlDestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'confirmationUrl', ], 'members' => [ 'confirmationUrl' => [ 'shape' => 'Url', ], ], ], 'HttpUrlDestinationProperties' => [ 'type' => 'structure', 'members' => [ 'confirmationUrl' => [ 'shape' => 'Url', ], ], ], 'HttpUrlDestinationSummary' => [ 'type' => 'structure', 'members' => [ 'confirmationUrl' => [ 'shape' => 'Url', ], ], ], 'ImplicitDeny' => [ 'type' => 'structure', 'members' => [ 'policies' => [ 'shape' => 'Policies', ], ], ], 'InProgressChecksCount' => [ 'type' => 'integer', ], 'InProgressThings' => [ 'type' => 'integer', ], 'InProgressTimeoutInMinutes' => [ 'type' => 'long', ], 'IncrementFactor' => [ 'type' => 'double', 'max' => 5, 'min' => 1.1, ], 'IndexName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9:_-]+', ], 'IndexNamesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IndexName', ], ], 'IndexNotReadyException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'IndexSchema' => [ 'type' => 'string', ], 'IndexStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'BUILDING', 'REBUILDING', ], ], 'IndexingFilter' => [ 'type' => 'structure', 'members' => [ 'namedShadowNames' => [ 'shape' => 'NamedShadowNamesFilter', ], 'geoLocations' => [ 'shape' => 'GeoLocationsFilter', ], ], ], 'InlineDocument' => [ 'type' => 'string', ], 'InputName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'IntegerParameterValue' => [ 'type' => 'integer', ], 'InternalException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InternalFailureException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InvalidAggregationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidQueryException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidResponseException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidStateTransitionException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'IotAnalyticsAction' => [ 'type' => 'structure', 'members' => [ 'channelArn' => [ 'shape' => 'AwsArn', ], 'channelName' => [ 'shape' => 'ChannelName', ], 'batchMode' => [ 'shape' => 'BatchMode', ], 'roleArn' => [ 'shape' => 'AwsArn', ], ], ], 'IotEventsAction' => [ 'type' => 'structure', 'required' => [ 'inputName', 'roleArn', ], 'members' => [ 'inputName' => [ 'shape' => 'InputName', ], 'messageId' => [ 'shape' => 'MessageId', ], 'batchMode' => [ 'shape' => 'BatchMode', ], 'roleArn' => [ 'shape' => 'AwsArn', ], ], ], 'IotSiteWiseAction' => [ 'type' => 'structure', 'required' => [ 'putAssetPropertyValueEntries', 'roleArn', ], 'members' => [ 'putAssetPropertyValueEntries' => [ 'shape' => 'PutAssetPropertyValueEntryList', ], 'roleArn' => [ 'shape' => 'AwsArn', ], ], ], 'IsAuthenticated' => [ 'type' => 'boolean', ], 'IsDefaultVersion' => [ 'type' => 'boolean', ], 'IsDisabled' => [ 'type' => 'boolean', ], 'IsSuppressed' => [ 'type' => 'boolean', ], 'IssuerCertificateIdentifier' => [ 'type' => 'structure', 'members' => [ 'issuerCertificateSubject' => [ 'shape' => 'IssuerCertificateSubject', ], 'issuerId' => [ 'shape' => 'IssuerId', ], 'issuerCertificateSerialNumber' => [ 'shape' => 'IssuerCertificateSerialNumber', ], ], ], 'IssuerCertificateSerialNumber' => [ 'type' => 'string', 'max' => 20, 'pattern' => '[a-fA-F0-9:]+', ], 'IssuerCertificateSubject' => [ 'type' => 'string', 'max' => 1000, 'pattern' => '[\\p{Graph}\\x20]*', ], 'IssuerId' => [ 'type' => 'string', 'max' => 64, 'pattern' => '(0x)?[a-fA-F0-9]+', ], 'Job' => [ 'type' => 'structure', 'members' => [ 'jobArn' => [ 'shape' => 'JobArn', ], 'jobId' => [ 'shape' => 'JobId', ], 'targetSelection' => [ 'shape' => 'TargetSelection', ], 'status' => [ 'shape' => 'JobStatus', ], 'forceCanceled' => [ 'shape' => 'Forced', ], 'reasonCode' => [ 'shape' => 'ReasonCode', ], 'comment' => [ 'shape' => 'Comment', ], 'targets' => [ 'shape' => 'JobTargets', ], 'description' => [ 'shape' => 'JobDescription', ], 'presignedUrlConfig' => [ 'shape' => 'PresignedUrlConfig', ], 'jobExecutionsRolloutConfig' => [ 'shape' => 'JobExecutionsRolloutConfig', ], 'abortConfig' => [ 'shape' => 'AbortConfig', ], 'createdAt' => [ 'shape' => 'DateType', ], 'lastUpdatedAt' => [ 'shape' => 'DateType', ], 'completedAt' => [ 'shape' => 'DateType', ], 'jobProcessDetails' => [ 'shape' => 'JobProcessDetails', ], 'timeoutConfig' => [ 'shape' => 'TimeoutConfig', ], 'namespaceId' => [ 'shape' => 'NamespaceId', ], 'jobTemplateArn' => [ 'shape' => 'JobTemplateArn', ], 'jobExecutionsRetryConfig' => [ 'shape' => 'JobExecutionsRetryConfig', ], 'documentParameters' => [ 'shape' => 'ParameterMap', ], 'isConcurrent' => [ 'shape' => 'BooleanWrapperObject', ], 'schedulingConfig' => [ 'shape' => 'SchedulingConfig', ], 'scheduledJobRollouts' => [ 'shape' => 'ScheduledJobRolloutList', ], 'destinationPackageVersions' => [ 'shape' => 'DestinationPackageVersions', ], ], ], 'JobArn' => [ 'type' => 'string', ], 'JobDescription' => [ 'type' => 'string', 'max' => 2028, 'pattern' => '[^\\p{C}]+', ], 'JobDocument' => [ 'type' => 'string', 'max' => 32768, ], 'JobDocumentSource' => [ 'type' => 'string', 'max' => 1350, 'min' => 1, ], 'JobEndBehavior' => [ 'type' => 'string', 'enum' => [ 'STOP_ROLLOUT', 'CANCEL', 'FORCE_CANCEL', ], ], 'JobExecution' => [ 'type' => 'structure', 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'status' => [ 'shape' => 'JobExecutionStatus', ], 'forceCanceled' => [ 'shape' => 'Forced', ], 'statusDetails' => [ 'shape' => 'JobExecutionStatusDetails', ], 'thingArn' => [ 'shape' => 'ThingArn', ], 'queuedAt' => [ 'shape' => 'DateType', ], 'startedAt' => [ 'shape' => 'DateType', ], 'lastUpdatedAt' => [ 'shape' => 'DateType', ], 'executionNumber' => [ 'shape' => 'ExecutionNumber', ], 'versionNumber' => [ 'shape' => 'VersionNumber', ], 'approximateSecondsBeforeTimedOut' => [ 'shape' => 'ApproximateSecondsBeforeTimedOut', ], ], ], 'JobExecutionFailureType' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'REJECTED', 'TIMED_OUT', 'ALL', ], ], 'JobExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'QUEUED', 'IN_PROGRESS', 'SUCCEEDED', 'FAILED', 'TIMED_OUT', 'REJECTED', 'REMOVED', 'CANCELED', ], ], 'JobExecutionStatusDetails' => [ 'type' => 'structure', 'members' => [ 'detailsMap' => [ 'shape' => 'DetailsMap', ], ], ], 'JobExecutionSummary' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'JobExecutionStatus', ], 'queuedAt' => [ 'shape' => 'DateType', ], 'startedAt' => [ 'shape' => 'DateType', ], 'lastUpdatedAt' => [ 'shape' => 'DateType', ], 'executionNumber' => [ 'shape' => 'ExecutionNumber', ], 'retryAttempt' => [ 'shape' => 'RetryAttempt', ], ], ], 'JobExecutionSummaryForJob' => [ 'type' => 'structure', 'members' => [ 'thingArn' => [ 'shape' => 'ThingArn', ], 'jobExecutionSummary' => [ 'shape' => 'JobExecutionSummary', ], ], ], 'JobExecutionSummaryForJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobExecutionSummaryForJob', ], ], 'JobExecutionSummaryForThing' => [ 'type' => 'structure', 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'jobExecutionSummary' => [ 'shape' => 'JobExecutionSummary', ], ], ], 'JobExecutionSummaryForThingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobExecutionSummaryForThing', ], ], 'JobExecutionsRetryConfig' => [ 'type' => 'structure', 'required' => [ 'criteriaList', ], 'members' => [ 'criteriaList' => [ 'shape' => 'RetryCriteriaList', ], ], ], 'JobExecutionsRolloutConfig' => [ 'type' => 'structure', 'members' => [ 'maximumPerMinute' => [ 'shape' => 'MaxJobExecutionsPerMin', ], 'exponentialRate' => [ 'shape' => 'ExponentialRolloutRate', ], ], ], 'JobId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'JobProcessDetails' => [ 'type' => 'structure', 'members' => [ 'processingTargets' => [ 'shape' => 'ProcessingTargetNameList', ], 'numberOfCanceledThings' => [ 'shape' => 'CanceledThings', ], 'numberOfSucceededThings' => [ 'shape' => 'SucceededThings', ], 'numberOfFailedThings' => [ 'shape' => 'FailedThings', ], 'numberOfRejectedThings' => [ 'shape' => 'RejectedThings', ], 'numberOfQueuedThings' => [ 'shape' => 'QueuedThings', ], 'numberOfInProgressThings' => [ 'shape' => 'InProgressThings', ], 'numberOfRemovedThings' => [ 'shape' => 'RemovedThings', ], 'numberOfTimedOutThings' => [ 'shape' => 'TimedOutThings', ], ], ], 'JobStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'CANCELED', 'COMPLETED', 'DELETION_IN_PROGRESS', 'SCHEDULED', ], ], 'JobSummary' => [ 'type' => 'structure', 'members' => [ 'jobArn' => [ 'shape' => 'JobArn', ], 'jobId' => [ 'shape' => 'JobId', ], 'thingGroupId' => [ 'shape' => 'ThingGroupId', ], 'targetSelection' => [ 'shape' => 'TargetSelection', ], 'status' => [ 'shape' => 'JobStatus', ], 'createdAt' => [ 'shape' => 'DateType', ], 'lastUpdatedAt' => [ 'shape' => 'DateType', ], 'completedAt' => [ 'shape' => 'DateType', ], 'isConcurrent' => [ 'shape' => 'BooleanWrapperObject', ], ], ], 'JobSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobSummary', ], ], 'JobTargets' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetArn', ], 'min' => 1, ], 'JobTemplateArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '^arn:[!-~]+$', ], 'JobTemplateId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'JobTemplateSummary' => [ 'type' => 'structure', 'members' => [ 'jobTemplateArn' => [ 'shape' => 'JobTemplateArn', ], 'jobTemplateId' => [ 'shape' => 'JobTemplateId', ], 'description' => [ 'shape' => 'JobDescription', ], 'createdAt' => [ 'shape' => 'DateType', ], ], ], 'JobTemplateSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobTemplateSummary', ], ], 'JsonDocument' => [ 'type' => 'string', ], 'KafkaAction' => [ 'type' => 'structure', 'required' => [ 'destinationArn', 'topic', 'clientProperties', ], 'members' => [ 'destinationArn' => [ 'shape' => 'AwsArn', ], 'topic' => [ 'shape' => 'String', ], 'key' => [ 'shape' => 'String', ], 'partition' => [ 'shape' => 'String', ], 'clientProperties' => [ 'shape' => 'ClientProperties', ], 'headers' => [ 'shape' => 'KafkaHeaders', ], ], ], 'KafkaActionHeader' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'KafkaHeaderKey', ], 'value' => [ 'shape' => 'KafkaHeaderValue', ], ], ], 'KafkaHeaderKey' => [ 'type' => 'string', 'max' => 16384, 'min' => 0, ], 'KafkaHeaderValue' => [ 'type' => 'string', 'max' => 16384, 'min' => 0, ], 'KafkaHeaders' => [ 'type' => 'list', 'member' => [ 'shape' => 'KafkaActionHeader', ], 'max' => 100, 'min' => 1, ], 'Key' => [ 'type' => 'string', ], 'KeyName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9:_-]+', ], 'KeyPair' => [ 'type' => 'structure', 'members' => [ 'PublicKey' => [ 'shape' => 'PublicKey', ], 'PrivateKey' => [ 'shape' => 'PrivateKey', ], ], ], 'KeyValue' => [ 'type' => 'string', 'max' => 5120, 'pattern' => '[\\s\\S]*', ], 'KinesisAction' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'streamName', ], 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'streamName' => [ 'shape' => 'StreamName', ], 'partitionKey' => [ 'shape' => 'PartitionKey', ], ], ], 'LambdaAction' => [ 'type' => 'structure', 'required' => [ 'functionArn', ], 'members' => [ 'functionArn' => [ 'shape' => 'FunctionArn', ], ], ], 'LaserMaxResults' => [ 'type' => 'integer', 'max' => 250, 'min' => 1, ], 'LastModifiedDate' => [ 'type' => 'timestamp', ], 'LastUpdatedAtDate' => [ 'type' => 'timestamp', ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 410, ], 'exception' => true, ], 'ListActiveViolationsRequest' => [ 'type' => 'structure', 'members' => [ 'thingName' => [ 'shape' => 'DeviceDefenderThingName', 'location' => 'querystring', 'locationName' => 'thingName', ], 'securityProfileName' => [ 'shape' => 'SecurityProfileName', 'location' => 'querystring', 'locationName' => 'securityProfileName', ], 'behaviorCriteriaType' => [ 'shape' => 'BehaviorCriteriaType', 'location' => 'querystring', 'locationName' => 'behaviorCriteriaType', ], 'listSuppressedAlerts' => [ 'shape' => 'ListSuppressedAlerts', 'location' => 'querystring', 'locationName' => 'listSuppressedAlerts', ], 'verificationState' => [ 'shape' => 'VerificationState', 'location' => 'querystring', 'locationName' => 'verificationState', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListActiveViolationsResponse' => [ 'type' => 'structure', 'members' => [ 'activeViolations' => [ 'shape' => 'ActiveViolations', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAttachedPoliciesRequest' => [ 'type' => 'structure', 'required' => [ 'target', ], 'members' => [ 'target' => [ 'shape' => 'PolicyTarget', 'location' => 'uri', 'locationName' => 'target', ], 'recursive' => [ 'shape' => 'Recursive', 'location' => 'querystring', 'locationName' => 'recursive', ], 'marker' => [ 'shape' => 'Marker', 'location' => 'querystring', 'locationName' => 'marker', ], 'pageSize' => [ 'shape' => 'PageSize', 'location' => 'querystring', 'locationName' => 'pageSize', ], ], ], 'ListAttachedPoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'policies' => [ 'shape' => 'Policies', ], 'nextMarker' => [ 'shape' => 'Marker', ], ], ], 'ListAuditFindingsRequest' => [ 'type' => 'structure', 'members' => [ 'taskId' => [ 'shape' => 'AuditTaskId', ], 'checkName' => [ 'shape' => 'AuditCheckName', ], 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'listSuppressedFindings' => [ 'shape' => 'ListSuppressedFindings', ], ], ], 'ListAuditFindingsResponse' => [ 'type' => 'structure', 'members' => [ 'findings' => [ 'shape' => 'AuditFindings', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAuditMitigationActionsExecutionsRequest' => [ 'type' => 'structure', 'required' => [ 'taskId', 'findingId', ], 'members' => [ 'taskId' => [ 'shape' => 'MitigationActionsTaskId', 'location' => 'querystring', 'locationName' => 'taskId', ], 'actionStatus' => [ 'shape' => 'AuditMitigationActionsExecutionStatus', 'location' => 'querystring', 'locationName' => 'actionStatus', ], 'findingId' => [ 'shape' => 'FindingId', 'location' => 'querystring', 'locationName' => 'findingId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListAuditMitigationActionsExecutionsResponse' => [ 'type' => 'structure', 'members' => [ 'actionsExecutions' => [ 'shape' => 'AuditMitigationActionExecutionMetadataList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAuditMitigationActionsTasksRequest' => [ 'type' => 'structure', 'required' => [ 'startTime', 'endTime', ], 'members' => [ 'auditTaskId' => [ 'shape' => 'AuditTaskId', 'location' => 'querystring', 'locationName' => 'auditTaskId', ], 'findingId' => [ 'shape' => 'FindingId', 'location' => 'querystring', 'locationName' => 'findingId', ], 'taskStatus' => [ 'shape' => 'AuditMitigationActionsTaskStatus', 'location' => 'querystring', 'locationName' => 'taskStatus', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'startTime' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'startTime', ], 'endTime' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'endTime', ], ], ], 'ListAuditMitigationActionsTasksResponse' => [ 'type' => 'structure', 'members' => [ 'tasks' => [ 'shape' => 'AuditMitigationActionsTaskMetadataList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAuditSuppressionsRequest' => [ 'type' => 'structure', 'members' => [ 'checkName' => [ 'shape' => 'AuditCheckName', ], 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'ascendingOrder' => [ 'shape' => 'AscendingOrder', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListAuditSuppressionsResponse' => [ 'type' => 'structure', 'members' => [ 'suppressions' => [ 'shape' => 'AuditSuppressionList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAuditTasksRequest' => [ 'type' => 'structure', 'required' => [ 'startTime', 'endTime', ], 'members' => [ 'startTime' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'startTime', ], 'endTime' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'endTime', ], 'taskType' => [ 'shape' => 'AuditTaskType', 'location' => 'querystring', 'locationName' => 'taskType', ], 'taskStatus' => [ 'shape' => 'AuditTaskStatus', 'location' => 'querystring', 'locationName' => 'taskStatus', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAuditTasksResponse' => [ 'type' => 'structure', 'members' => [ 'tasks' => [ 'shape' => 'AuditTaskMetadataList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAuthorizersRequest' => [ 'type' => 'structure', 'members' => [ 'pageSize' => [ 'shape' => 'PageSize', 'location' => 'querystring', 'locationName' => 'pageSize', ], 'marker' => [ 'shape' => 'Marker', 'location' => 'querystring', 'locationName' => 'marker', ], 'ascendingOrder' => [ 'shape' => 'AscendingOrder', 'location' => 'querystring', 'locationName' => 'isAscendingOrder', ], 'status' => [ 'shape' => 'AuthorizerStatus', 'location' => 'querystring', 'locationName' => 'status', ], ], ], 'ListAuthorizersResponse' => [ 'type' => 'structure', 'members' => [ 'authorizers' => [ 'shape' => 'Authorizers', ], 'nextMarker' => [ 'shape' => 'Marker', ], ], ], 'ListBillingGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'RegistryMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'namePrefixFilter' => [ 'shape' => 'BillingGroupName', 'location' => 'querystring', 'locationName' => 'namePrefixFilter', ], ], ], 'ListBillingGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'billingGroups' => [ 'shape' => 'BillingGroupNameAndArnList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCACertificatesRequest' => [ 'type' => 'structure', 'members' => [ 'pageSize' => [ 'shape' => 'PageSize', 'location' => 'querystring', 'locationName' => 'pageSize', ], 'marker' => [ 'shape' => 'Marker', 'location' => 'querystring', 'locationName' => 'marker', ], 'ascendingOrder' => [ 'shape' => 'AscendingOrder', 'location' => 'querystring', 'locationName' => 'isAscendingOrder', ], 'templateName' => [ 'shape' => 'TemplateName', 'location' => 'querystring', 'locationName' => 'templateName', ], ], ], 'ListCACertificatesResponse' => [ 'type' => 'structure', 'members' => [ 'certificates' => [ 'shape' => 'CACertificates', ], 'nextMarker' => [ 'shape' => 'Marker', ], ], ], 'ListCertificateProvidersRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'Marker', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'ascendingOrder' => [ 'shape' => 'AscendingOrder', 'location' => 'querystring', 'locationName' => 'isAscendingOrder', ], ], ], 'ListCertificateProvidersResponse' => [ 'type' => 'structure', 'members' => [ 'certificateProviders' => [ 'shape' => 'CertificateProviders', ], 'nextToken' => [ 'shape' => 'Marker', ], ], ], 'ListCertificatesByCARequest' => [ 'type' => 'structure', 'required' => [ 'caCertificateId', ], 'members' => [ 'caCertificateId' => [ 'shape' => 'CertificateId', 'location' => 'uri', 'locationName' => 'caCertificateId', ], 'pageSize' => [ 'shape' => 'PageSize', 'location' => 'querystring', 'locationName' => 'pageSize', ], 'marker' => [ 'shape' => 'Marker', 'location' => 'querystring', 'locationName' => 'marker', ], 'ascendingOrder' => [ 'shape' => 'AscendingOrder', 'location' => 'querystring', 'locationName' => 'isAscendingOrder', ], ], ], 'ListCertificatesByCAResponse' => [ 'type' => 'structure', 'members' => [ 'certificates' => [ 'shape' => 'Certificates', ], 'nextMarker' => [ 'shape' => 'Marker', ], ], ], 'ListCertificatesRequest' => [ 'type' => 'structure', 'members' => [ 'pageSize' => [ 'shape' => 'PageSize', 'location' => 'querystring', 'locationName' => 'pageSize', ], 'marker' => [ 'shape' => 'Marker', 'location' => 'querystring', 'locationName' => 'marker', ], 'ascendingOrder' => [ 'shape' => 'AscendingOrder', 'location' => 'querystring', 'locationName' => 'isAscendingOrder', ], ], ], 'ListCertificatesResponse' => [ 'type' => 'structure', 'members' => [ 'certificates' => [ 'shape' => 'Certificates', ], 'nextMarker' => [ 'shape' => 'Marker', ], ], ], 'ListCommandExecutionsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'CommandMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'namespace' => [ 'shape' => 'CommandNamespace', ], 'status' => [ 'shape' => 'CommandExecutionStatus', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], 'startedTimeFilter' => [ 'shape' => 'TimeFilter', ], 'completedTimeFilter' => [ 'shape' => 'TimeFilter', ], 'targetArn' => [ 'shape' => 'TargetArn', ], 'commandArn' => [ 'shape' => 'CommandArn', ], ], ], 'ListCommandExecutionsResponse' => [ 'type' => 'structure', 'members' => [ 'commandExecutions' => [ 'shape' => 'CommandExecutionSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCommandsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'CommandMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'namespace' => [ 'shape' => 'CommandNamespace', 'location' => 'querystring', 'locationName' => 'namespace', ], 'commandParameterName' => [ 'shape' => 'CommandParameterName', 'location' => 'querystring', 'locationName' => 'commandParameterName', ], 'sortOrder' => [ 'shape' => 'SortOrder', 'location' => 'querystring', 'locationName' => 'sortOrder', ], ], ], 'ListCommandsResponse' => [ 'type' => 'structure', 'members' => [ 'commands' => [ 'shape' => 'CommandSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCustomMetricsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListCustomMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'metricNames' => [ 'shape' => 'MetricNames', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDetectMitigationActionsExecutionsRequest' => [ 'type' => 'structure', 'members' => [ 'taskId' => [ 'shape' => 'MitigationActionsTaskId', 'location' => 'querystring', 'locationName' => 'taskId', ], 'violationId' => [ 'shape' => 'ViolationId', 'location' => 'querystring', 'locationName' => 'violationId', ], 'thingName' => [ 'shape' => 'DeviceDefenderThingName', 'location' => 'querystring', 'locationName' => 'thingName', ], 'startTime' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'startTime', ], 'endTime' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'endTime', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListDetectMitigationActionsExecutionsResponse' => [ 'type' => 'structure', 'members' => [ 'actionsExecutions' => [ 'shape' => 'DetectMitigationActionExecutionList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDetectMitigationActionsTasksRequest' => [ 'type' => 'structure', 'required' => [ 'startTime', 'endTime', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'startTime' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'startTime', ], 'endTime' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'endTime', ], ], ], 'ListDetectMitigationActionsTasksResponse' => [ 'type' => 'structure', 'members' => [ 'tasks' => [ 'shape' => 'DetectMitigationActionsTaskSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDimensionsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListDimensionsResponse' => [ 'type' => 'structure', 'members' => [ 'dimensionNames' => [ 'shape' => 'DimensionNames', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDomainConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'marker' => [ 'shape' => 'Marker', 'location' => 'querystring', 'locationName' => 'marker', ], 'pageSize' => [ 'shape' => 'PageSize', 'location' => 'querystring', 'locationName' => 'pageSize', ], 'serviceType' => [ 'shape' => 'ServiceType', 'location' => 'querystring', 'locationName' => 'serviceType', ], ], ], 'ListDomainConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'domainConfigurations' => [ 'shape' => 'DomainConfigurations', ], 'nextMarker' => [ 'shape' => 'Marker', ], ], ], 'ListFleetMetricsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListFleetMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'fleetMetrics' => [ 'shape' => 'FleetMetricNameAndArnList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListIndicesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'QueryMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListIndicesResponse' => [ 'type' => 'structure', 'members' => [ 'indexNames' => [ 'shape' => 'IndexNamesList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListJobExecutionsForJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'status' => [ 'shape' => 'JobExecutionStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'maxResults' => [ 'shape' => 'LaserMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListJobExecutionsForJobResponse' => [ 'type' => 'structure', 'members' => [ 'executionSummaries' => [ 'shape' => 'JobExecutionSummaryForJobList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListJobExecutionsForThingRequest' => [ 'type' => 'structure', 'required' => [ 'thingName', ], 'members' => [ 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], 'status' => [ 'shape' => 'JobExecutionStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'namespaceId' => [ 'shape' => 'NamespaceId', 'location' => 'querystring', 'locationName' => 'namespaceId', ], 'maxResults' => [ 'shape' => 'LaserMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'querystring', 'locationName' => 'jobId', ], ], ], 'ListJobExecutionsForThingResponse' => [ 'type' => 'structure', 'members' => [ 'executionSummaries' => [ 'shape' => 'JobExecutionSummaryForThingList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListJobTemplatesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'LaserMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListJobTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'jobTemplates' => [ 'shape' => 'JobTemplateSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListJobsRequest' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'JobStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'targetSelection' => [ 'shape' => 'TargetSelection', 'location' => 'querystring', 'locationName' => 'targetSelection', ], 'maxResults' => [ 'shape' => 'LaserMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'thingGroupName' => [ 'shape' => 'ThingGroupName', 'location' => 'querystring', 'locationName' => 'thingGroupName', ], 'thingGroupId' => [ 'shape' => 'ThingGroupId', 'location' => 'querystring', 'locationName' => 'thingGroupId', ], 'namespaceId' => [ 'shape' => 'NamespaceId', 'location' => 'querystring', 'locationName' => 'namespaceId', ], ], ], 'ListJobsResponse' => [ 'type' => 'structure', 'members' => [ 'jobs' => [ 'shape' => 'JobSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListManagedJobTemplatesRequest' => [ 'type' => 'structure', 'members' => [ 'templateName' => [ 'shape' => 'ManagedJobTemplateName', 'location' => 'querystring', 'locationName' => 'templateName', ], 'maxResults' => [ 'shape' => 'LaserMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListManagedJobTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'managedJobTemplates' => [ 'shape' => 'ManagedJobTemplatesSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMetricValuesRequest' => [ 'type' => 'structure', 'required' => [ 'thingName', 'metricName', 'startTime', 'endTime', ], 'members' => [ 'thingName' => [ 'shape' => 'DeviceDefenderThingName', 'location' => 'querystring', 'locationName' => 'thingName', ], 'metricName' => [ 'shape' => 'BehaviorMetric', 'location' => 'querystring', 'locationName' => 'metricName', ], 'dimensionName' => [ 'shape' => 'DimensionName', 'location' => 'querystring', 'locationName' => 'dimensionName', ], 'dimensionValueOperator' => [ 'shape' => 'DimensionValueOperator', 'location' => 'querystring', 'locationName' => 'dimensionValueOperator', ], 'startTime' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'startTime', ], 'endTime' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'endTime', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListMetricValuesResponse' => [ 'type' => 'structure', 'members' => [ 'metricDatumList' => [ 'shape' => 'MetricDatumList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMitigationActionsRequest' => [ 'type' => 'structure', 'members' => [ 'actionType' => [ 'shape' => 'MitigationActionType', 'location' => 'querystring', 'locationName' => 'actionType', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListMitigationActionsResponse' => [ 'type' => 'structure', 'members' => [ 'actionIdentifiers' => [ 'shape' => 'MitigationActionIdentifierList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListOTAUpdatesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'otaUpdateStatus' => [ 'shape' => 'OTAUpdateStatus', 'location' => 'querystring', 'locationName' => 'otaUpdateStatus', ], ], ], 'ListOTAUpdatesResponse' => [ 'type' => 'structure', 'members' => [ 'otaUpdates' => [ 'shape' => 'OTAUpdatesSummary', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListOutgoingCertificatesRequest' => [ 'type' => 'structure', 'members' => [ 'pageSize' => [ 'shape' => 'PageSize', 'location' => 'querystring', 'locationName' => 'pageSize', ], 'marker' => [ 'shape' => 'Marker', 'location' => 'querystring', 'locationName' => 'marker', ], 'ascendingOrder' => [ 'shape' => 'AscendingOrder', 'location' => 'querystring', 'locationName' => 'isAscendingOrder', ], ], ], 'ListOutgoingCertificatesResponse' => [ 'type' => 'structure', 'members' => [ 'outgoingCertificates' => [ 'shape' => 'OutgoingCertificates', ], 'nextMarker' => [ 'shape' => 'Marker', ], ], ], 'ListPackageVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'packageName', ], 'members' => [ 'packageName' => [ 'shape' => 'PackageName', 'location' => 'uri', 'locationName' => 'packageName', ], 'status' => [ 'shape' => 'PackageVersionStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'maxResults' => [ 'shape' => 'PackageCatalogMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListPackageVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'packageVersionSummaries' => [ 'shape' => 'PackageVersionSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPackagesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'PackageCatalogMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListPackagesResponse' => [ 'type' => 'structure', 'members' => [ 'packageSummaries' => [ 'shape' => 'PackageSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'marker' => [ 'shape' => 'Marker', 'location' => 'querystring', 'locationName' => 'marker', ], 'pageSize' => [ 'shape' => 'PageSize', 'location' => 'querystring', 'locationName' => 'pageSize', ], 'ascendingOrder' => [ 'shape' => 'AscendingOrder', 'location' => 'querystring', 'locationName' => 'isAscendingOrder', ], ], ], 'ListPoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'policies' => [ 'shape' => 'Policies', ], 'nextMarker' => [ 'shape' => 'Marker', ], ], ], 'ListPolicyPrincipalsRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'header', 'locationName' => 'x-amzn-iot-policy', ], 'marker' => [ 'shape' => 'Marker', 'location' => 'querystring', 'locationName' => 'marker', ], 'pageSize' => [ 'shape' => 'PageSize', 'location' => 'querystring', 'locationName' => 'pageSize', ], 'ascendingOrder' => [ 'shape' => 'AscendingOrder', 'location' => 'querystring', 'locationName' => 'isAscendingOrder', ], ], ], 'ListPolicyPrincipalsResponse' => [ 'type' => 'structure', 'members' => [ 'principals' => [ 'shape' => 'Principals', ], 'nextMarker' => [ 'shape' => 'Marker', ], ], ], 'ListPolicyVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'policyName', ], ], ], 'ListPolicyVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'policyVersions' => [ 'shape' => 'PolicyVersions', ], ], ], 'ListPrincipalPoliciesRequest' => [ 'type' => 'structure', 'required' => [ 'principal', ], 'members' => [ 'principal' => [ 'shape' => 'Principal', 'location' => 'header', 'locationName' => 'x-amzn-iot-principal', ], 'marker' => [ 'shape' => 'Marker', 'location' => 'querystring', 'locationName' => 'marker', ], 'pageSize' => [ 'shape' => 'PageSize', 'location' => 'querystring', 'locationName' => 'pageSize', ], 'ascendingOrder' => [ 'shape' => 'AscendingOrder', 'location' => 'querystring', 'locationName' => 'isAscendingOrder', ], ], ], 'ListPrincipalPoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'policies' => [ 'shape' => 'Policies', ], 'nextMarker' => [ 'shape' => 'Marker', ], ], ], 'ListPrincipalThingsRequest' => [ 'type' => 'structure', 'required' => [ 'principal', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'RegistryMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'principal' => [ 'shape' => 'Principal', 'location' => 'header', 'locationName' => 'x-amzn-principal', ], ], ], 'ListPrincipalThingsResponse' => [ 'type' => 'structure', 'members' => [ 'things' => [ 'shape' => 'ThingNameList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPrincipalThingsV2Request' => [ 'type' => 'structure', 'required' => [ 'principal', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'RegistryMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'principal' => [ 'shape' => 'Principal', 'location' => 'header', 'locationName' => 'x-amzn-principal', ], 'thingPrincipalType' => [ 'shape' => 'ThingPrincipalType', 'location' => 'querystring', 'locationName' => 'thingPrincipalType', ], ], ], 'ListPrincipalThingsV2Response' => [ 'type' => 'structure', 'members' => [ 'principalThingObjects' => [ 'shape' => 'PrincipalThingObjects', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListProvisioningTemplateVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'templateName', ], 'members' => [ 'templateName' => [ 'shape' => 'TemplateName', 'location' => 'uri', 'locationName' => 'templateName', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListProvisioningTemplateVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'versions' => [ 'shape' => 'ProvisioningTemplateVersionListing', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListProvisioningTemplatesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListProvisioningTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'templates' => [ 'shape' => 'ProvisioningTemplateListing', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRelatedResourcesForAuditFindingRequest' => [ 'type' => 'structure', 'required' => [ 'findingId', ], 'members' => [ 'findingId' => [ 'shape' => 'FindingId', 'location' => 'querystring', 'locationName' => 'findingId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListRelatedResourcesForAuditFindingResponse' => [ 'type' => 'structure', 'members' => [ 'relatedResources' => [ 'shape' => 'RelatedResources', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRoleAliasesRequest' => [ 'type' => 'structure', 'members' => [ 'pageSize' => [ 'shape' => 'PageSize', 'location' => 'querystring', 'locationName' => 'pageSize', ], 'marker' => [ 'shape' => 'Marker', 'location' => 'querystring', 'locationName' => 'marker', ], 'ascendingOrder' => [ 'shape' => 'AscendingOrder', 'location' => 'querystring', 'locationName' => 'isAscendingOrder', ], ], ], 'ListRoleAliasesResponse' => [ 'type' => 'structure', 'members' => [ 'roleAliases' => [ 'shape' => 'RoleAliases', ], 'nextMarker' => [ 'shape' => 'Marker', ], ], ], 'ListSbomValidationResultsRequest' => [ 'type' => 'structure', 'required' => [ 'packageName', 'versionName', ], 'members' => [ 'packageName' => [ 'shape' => 'PackageName', 'location' => 'uri', 'locationName' => 'packageName', ], 'versionName' => [ 'shape' => 'VersionName', 'location' => 'uri', 'locationName' => 'versionName', ], 'validationResult' => [ 'shape' => 'SbomValidationResult', 'location' => 'querystring', 'locationName' => 'validationResult', ], 'maxResults' => [ 'shape' => 'PackageCatalogMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListSbomValidationResultsResponse' => [ 'type' => 'structure', 'members' => [ 'validationResultSummaries' => [ 'shape' => 'SbomValidationResultSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListScheduledAuditsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListScheduledAuditsResponse' => [ 'type' => 'structure', 'members' => [ 'scheduledAudits' => [ 'shape' => 'ScheduledAuditMetadataList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSecurityProfilesForTargetRequest' => [ 'type' => 'structure', 'required' => [ 'securityProfileTargetArn', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'recursive' => [ 'shape' => 'Recursive', 'location' => 'querystring', 'locationName' => 'recursive', ], 'securityProfileTargetArn' => [ 'shape' => 'SecurityProfileTargetArn', 'location' => 'querystring', 'locationName' => 'securityProfileTargetArn', ], ], ], 'ListSecurityProfilesForTargetResponse' => [ 'type' => 'structure', 'members' => [ 'securityProfileTargetMappings' => [ 'shape' => 'SecurityProfileTargetMappings', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSecurityProfilesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'dimensionName' => [ 'shape' => 'DimensionName', 'location' => 'querystring', 'locationName' => 'dimensionName', ], 'metricName' => [ 'shape' => 'MetricName', 'location' => 'querystring', 'locationName' => 'metricName', ], ], ], 'ListSecurityProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'securityProfileIdentifiers' => [ 'shape' => 'SecurityProfileIdentifiers', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStreamsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'ascendingOrder' => [ 'shape' => 'AscendingOrder', 'location' => 'querystring', 'locationName' => 'isAscendingOrder', ], ], ], 'ListStreamsResponse' => [ 'type' => 'structure', 'members' => [ 'streams' => [ 'shape' => 'StreamsSummary', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSuppressedAlerts' => [ 'type' => 'boolean', ], 'ListSuppressedFindings' => [ 'type' => 'boolean', ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'querystring', 'locationName' => 'resourceArn', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTargetsForPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'policyName', ], 'marker' => [ 'shape' => 'Marker', 'location' => 'querystring', 'locationName' => 'marker', ], 'pageSize' => [ 'shape' => 'PageSize', 'location' => 'querystring', 'locationName' => 'pageSize', ], ], ], 'ListTargetsForPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'targets' => [ 'shape' => 'PolicyTargets', ], 'nextMarker' => [ 'shape' => 'Marker', ], ], ], 'ListTargetsForSecurityProfileRequest' => [ 'type' => 'structure', 'required' => [ 'securityProfileName', ], 'members' => [ 'securityProfileName' => [ 'shape' => 'SecurityProfileName', 'location' => 'uri', 'locationName' => 'securityProfileName', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListTargetsForSecurityProfileResponse' => [ 'type' => 'structure', 'members' => [ 'securityProfileTargets' => [ 'shape' => 'SecurityProfileTargets', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListThingGroupsForThingRequest' => [ 'type' => 'structure', 'required' => [ 'thingName', ], 'members' => [ 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'RegistryMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListThingGroupsForThingResponse' => [ 'type' => 'structure', 'members' => [ 'thingGroups' => [ 'shape' => 'ThingGroupNameAndArnList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListThingGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'RegistryMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'parentGroup' => [ 'shape' => 'ThingGroupName', 'location' => 'querystring', 'locationName' => 'parentGroup', ], 'namePrefixFilter' => [ 'shape' => 'ThingGroupName', 'location' => 'querystring', 'locationName' => 'namePrefixFilter', ], 'recursive' => [ 'shape' => 'RecursiveWithoutDefault', 'location' => 'querystring', 'locationName' => 'recursive', ], ], ], 'ListThingGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'thingGroups' => [ 'shape' => 'ThingGroupNameAndArnList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListThingPrincipalsRequest' => [ 'type' => 'structure', 'required' => [ 'thingName', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'RegistryMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], ], ], 'ListThingPrincipalsResponse' => [ 'type' => 'structure', 'members' => [ 'principals' => [ 'shape' => 'Principals', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListThingPrincipalsV2Request' => [ 'type' => 'structure', 'required' => [ 'thingName', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'RegistryMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], 'thingPrincipalType' => [ 'shape' => 'ThingPrincipalType', 'location' => 'querystring', 'locationName' => 'thingPrincipalType', ], ], ], 'ListThingPrincipalsV2Response' => [ 'type' => 'structure', 'members' => [ 'thingPrincipalObjects' => [ 'shape' => 'ThingPrincipalObjects', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListThingRegistrationTaskReportsRequest' => [ 'type' => 'structure', 'required' => [ 'taskId', 'reportType', ], 'members' => [ 'taskId' => [ 'shape' => 'TaskId', 'location' => 'uri', 'locationName' => 'taskId', ], 'reportType' => [ 'shape' => 'ReportType', 'location' => 'querystring', 'locationName' => 'reportType', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'RegistryMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListThingRegistrationTaskReportsResponse' => [ 'type' => 'structure', 'members' => [ 'resourceLinks' => [ 'shape' => 'S3FileUrlList', ], 'reportType' => [ 'shape' => 'ReportType', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListThingRegistrationTasksRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'RegistryMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'status' => [ 'shape' => 'Status', 'location' => 'querystring', 'locationName' => 'status', ], ], ], 'ListThingRegistrationTasksResponse' => [ 'type' => 'structure', 'members' => [ 'taskIds' => [ 'shape' => 'TaskIdList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListThingTypesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'RegistryMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'thingTypeName' => [ 'shape' => 'ThingTypeName', 'location' => 'querystring', 'locationName' => 'thingTypeName', ], ], ], 'ListThingTypesResponse' => [ 'type' => 'structure', 'members' => [ 'thingTypes' => [ 'shape' => 'ThingTypeList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListThingsInBillingGroupRequest' => [ 'type' => 'structure', 'required' => [ 'billingGroupName', ], 'members' => [ 'billingGroupName' => [ 'shape' => 'BillingGroupName', 'location' => 'uri', 'locationName' => 'billingGroupName', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'RegistryMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListThingsInBillingGroupResponse' => [ 'type' => 'structure', 'members' => [ 'things' => [ 'shape' => 'ThingNameList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListThingsInThingGroupRequest' => [ 'type' => 'structure', 'required' => [ 'thingGroupName', ], 'members' => [ 'thingGroupName' => [ 'shape' => 'ThingGroupName', 'location' => 'uri', 'locationName' => 'thingGroupName', ], 'recursive' => [ 'shape' => 'Recursive', 'location' => 'querystring', 'locationName' => 'recursive', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'RegistryMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListThingsInThingGroupResponse' => [ 'type' => 'structure', 'members' => [ 'things' => [ 'shape' => 'ThingNameList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListThingsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'RegistryMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'attributeName' => [ 'shape' => 'AttributeName', 'location' => 'querystring', 'locationName' => 'attributeName', ], 'attributeValue' => [ 'shape' => 'AttributeValue', 'location' => 'querystring', 'locationName' => 'attributeValue', ], 'thingTypeName' => [ 'shape' => 'ThingTypeName', 'location' => 'querystring', 'locationName' => 'thingTypeName', ], 'usePrefixAttributeValue' => [ 'shape' => 'usePrefixAttributeValue', 'location' => 'querystring', 'locationName' => 'usePrefixAttributeValue', ], ], ], 'ListThingsResponse' => [ 'type' => 'structure', 'members' => [ 'things' => [ 'shape' => 'ThingAttributeList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTopicRuleDestinationsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'TopicRuleDestinationMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListTopicRuleDestinationsResponse' => [ 'type' => 'structure', 'members' => [ 'destinationSummaries' => [ 'shape' => 'TopicRuleDestinationSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTopicRulesRequest' => [ 'type' => 'structure', 'members' => [ 'topic' => [ 'shape' => 'Topic', 'location' => 'querystring', 'locationName' => 'topic', ], 'maxResults' => [ 'shape' => 'TopicRuleMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'ruleDisabled' => [ 'shape' => 'IsDisabled', 'location' => 'querystring', 'locationName' => 'ruleDisabled', ], ], ], 'ListTopicRulesResponse' => [ 'type' => 'structure', 'members' => [ 'rules' => [ 'shape' => 'TopicRuleList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListV2LoggingLevelsRequest' => [ 'type' => 'structure', 'members' => [ 'targetType' => [ 'shape' => 'LogTargetType', 'location' => 'querystring', 'locationName' => 'targetType', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'SkyfallMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListV2LoggingLevelsResponse' => [ 'type' => 'structure', 'members' => [ 'logTargetConfigurations' => [ 'shape' => 'LogTargetConfigurations', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListViolationEventsRequest' => [ 'type' => 'structure', 'required' => [ 'startTime', 'endTime', ], 'members' => [ 'startTime' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'startTime', ], 'endTime' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'endTime', ], 'thingName' => [ 'shape' => 'DeviceDefenderThingName', 'location' => 'querystring', 'locationName' => 'thingName', ], 'securityProfileName' => [ 'shape' => 'SecurityProfileName', 'location' => 'querystring', 'locationName' => 'securityProfileName', ], 'behaviorCriteriaType' => [ 'shape' => 'BehaviorCriteriaType', 'location' => 'querystring', 'locationName' => 'behaviorCriteriaType', ], 'listSuppressedAlerts' => [ 'shape' => 'ListSuppressedAlerts', 'location' => 'querystring', 'locationName' => 'listSuppressedAlerts', ], 'verificationState' => [ 'shape' => 'VerificationState', 'location' => 'querystring', 'locationName' => 'verificationState', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListViolationEventsResponse' => [ 'type' => 'structure', 'members' => [ 'violationEvents' => [ 'shape' => 'ViolationEvents', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'LocationAction' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'trackerName', 'deviceId', 'latitude', 'longitude', ], 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'trackerName' => [ 'shape' => 'String', ], 'deviceId' => [ 'shape' => 'String', ], 'timestamp' => [ 'shape' => 'LocationTimestamp', ], 'latitude' => [ 'shape' => 'String', ], 'longitude' => [ 'shape' => 'String', ], ], ], 'LocationTimestamp' => [ 'type' => 'structure', 'required' => [ 'value', ], 'members' => [ 'value' => [ 'shape' => 'String', ], 'unit' => [ 'shape' => 'String', ], ], ], 'LogGroupName' => [ 'type' => 'string', ], 'LogLevel' => [ 'type' => 'string', 'enum' => [ 'DEBUG', 'INFO', 'ERROR', 'WARN', 'DISABLED', ], ], 'LogTarget' => [ 'type' => 'structure', 'required' => [ 'targetType', ], 'members' => [ 'targetType' => [ 'shape' => 'LogTargetType', ], 'targetName' => [ 'shape' => 'LogTargetName', ], ], ], 'LogTargetConfiguration' => [ 'type' => 'structure', 'members' => [ 'logTarget' => [ 'shape' => 'LogTarget', ], 'logLevel' => [ 'shape' => 'LogLevel', ], ], ], 'LogTargetConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogTargetConfiguration', ], ], 'LogTargetName' => [ 'type' => 'string', ], 'LogTargetType' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'THING_GROUP', 'CLIENT_ID', 'SOURCE_IP', 'PRINCIPAL_ID', ], ], 'LoggingOptionsPayload' => [ 'type' => 'structure', 'required' => [ 'roleArn', ], 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'logLevel' => [ 'shape' => 'LogLevel', ], ], ], 'LongParameterValue' => [ 'type' => 'long', ], 'MachineLearningDetectionConfig' => [ 'type' => 'structure', 'required' => [ 'confidenceLevel', ], 'members' => [ 'confidenceLevel' => [ 'shape' => 'ConfidenceLevel', ], ], ], 'MaintenanceWindow' => [ 'type' => 'structure', 'required' => [ 'startTime', 'durationInMinutes', ], 'members' => [ 'startTime' => [ 'shape' => 'CronExpression', ], 'durationInMinutes' => [ 'shape' => 'DurationInMinutes', ], ], ], 'MaintenanceWindows' => [ 'type' => 'list', 'member' => [ 'shape' => 'MaintenanceWindow', ], ], 'MalformedPolicyException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ManagedJobTemplateName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ManagedJobTemplateSummary' => [ 'type' => 'structure', 'members' => [ 'templateArn' => [ 'shape' => 'JobTemplateArn', ], 'templateName' => [ 'shape' => 'ManagedJobTemplateName', ], 'description' => [ 'shape' => 'JobDescription', ], 'environments' => [ 'shape' => 'Environments', ], 'templateVersion' => [ 'shape' => 'ManagedTemplateVersion', ], ], ], 'ManagedJobTemplatesSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManagedJobTemplateSummary', ], ], 'ManagedTemplateVersion' => [ 'type' => 'string', 'pattern' => '^[1-9]+.[0-9]+', ], 'Marker' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '[A-Za-z0-9+/]+={0,2}', ], 'MaxBuckets' => [ 'type' => 'integer', 'max' => 10000, 'min' => 1, ], 'MaxJobExecutionsPerMin' => [ 'type' => 'integer', 'min' => 1, ], 'MaxResults' => [ 'type' => 'integer', 'max' => 250, 'min' => 1, ], 'Maximum' => [ 'type' => 'double', ], 'MaximumPerMinute' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'Message' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[\\s\\S]*', ], 'MessageExpiry' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'MessageFormat' => [ 'type' => 'string', 'enum' => [ 'RAW', 'JSON', ], ], 'MessageId' => [ 'type' => 'string', 'max' => 128, ], 'MetricDatum' => [ 'type' => 'structure', 'members' => [ 'timestamp' => [ 'shape' => 'Timestamp', ], 'value' => [ 'shape' => 'MetricValue', ], ], ], 'MetricDatumList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricDatum', ], ], 'MetricDimension' => [ 'type' => 'structure', 'required' => [ 'dimensionName', ], 'members' => [ 'dimensionName' => [ 'shape' => 'DimensionName', ], 'operator' => [ 'shape' => 'DimensionValueOperator', ], ], ], 'MetricName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9:_-]+', ], 'MetricNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricName', ], ], 'MetricToRetain' => [ 'type' => 'structure', 'required' => [ 'metric', ], 'members' => [ 'metric' => [ 'shape' => 'BehaviorMetric', ], 'metricDimension' => [ 'shape' => 'MetricDimension', ], 'exportMetric' => [ 'shape' => 'ExportMetric', ], ], ], 'MetricValue' => [ 'type' => 'structure', 'members' => [ 'count' => [ 'shape' => 'UnsignedLong', ], 'cidrs' => [ 'shape' => 'Cidrs', ], 'ports' => [ 'shape' => 'Ports', ], 'number' => [ 'shape' => 'Number', ], 'numbers' => [ 'shape' => 'NumberList', ], 'strings' => [ 'shape' => 'StringList', ], ], ], 'MetricsExportConfig' => [ 'type' => 'structure', 'required' => [ 'mqttTopic', 'roleArn', ], 'members' => [ 'mqttTopic' => [ 'shape' => 'MqttTopic', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'MimeType' => [ 'type' => 'string', 'min' => 1, ], 'Minimum' => [ 'type' => 'double', ], 'MinimumNumberOfExecutedThings' => [ 'type' => 'integer', 'min' => 1, ], 'MissingContextValue' => [ 'type' => 'string', ], 'MissingContextValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'MissingContextValue', ], ], 'MitigationAction' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'MitigationActionName', ], 'id' => [ 'shape' => 'MitigationActionId', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'actionParams' => [ 'shape' => 'MitigationActionParams', ], ], ], 'MitigationActionArn' => [ 'type' => 'string', ], 'MitigationActionId' => [ 'type' => 'string', ], 'MitigationActionIdentifier' => [ 'type' => 'structure', 'members' => [ 'actionName' => [ 'shape' => 'MitigationActionName', ], 'actionArn' => [ 'shape' => 'MitigationActionArn', ], 'creationDate' => [ 'shape' => 'Timestamp', ], ], ], 'MitigationActionIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MitigationActionIdentifier', ], ], 'MitigationActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MitigationAction', ], ], 'MitigationActionName' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[a-zA-Z0-9_-]+', ], 'MitigationActionNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MitigationActionName', ], 'max' => 5, 'min' => 1, ], 'MitigationActionParams' => [ 'type' => 'structure', 'members' => [ 'updateDeviceCertificateParams' => [ 'shape' => 'UpdateDeviceCertificateParams', ], 'updateCACertificateParams' => [ 'shape' => 'UpdateCACertificateParams', ], 'addThingsToThingGroupParams' => [ 'shape' => 'AddThingsToThingGroupParams', ], 'replaceDefaultPolicyVersionParams' => [ 'shape' => 'ReplaceDefaultPolicyVersionParams', ], 'enableIoTLoggingParams' => [ 'shape' => 'EnableIoTLoggingParams', ], 'publishFindingToSnsParams' => [ 'shape' => 'PublishFindingToSnsParams', ], ], ], 'MitigationActionType' => [ 'type' => 'string', 'enum' => [ 'UPDATE_DEVICE_CERTIFICATE', 'UPDATE_CA_CERTIFICATE', 'ADD_THINGS_TO_THING_GROUP', 'REPLACE_DEFAULT_POLICY_VERSION', 'ENABLE_IOT_LOGGING', 'PUBLISH_FINDING_TO_SNS', ], ], 'MitigationActionsTaskId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'ModelStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING_BUILD', 'ACTIVE', 'EXPIRED', ], ], 'Mqtt5Configuration' => [ 'type' => 'structure', 'members' => [ 'propagatingAttributes' => [ 'shape' => 'PropagatingAttributeList', ], ], ], 'MqttClientId' => [ 'type' => 'string', 'max' => 65535, 'min' => 1, 'pattern' => '[\\s\\S]*', ], 'MqttContext' => [ 'type' => 'structure', 'members' => [ 'username' => [ 'shape' => 'MqttUsername', ], 'password' => [ 'shape' => 'MqttPassword', ], 'clientId' => [ 'shape' => 'MqttClientId', ], ], ], 'MqttHeaders' => [ 'type' => 'structure', 'members' => [ 'payloadFormatIndicator' => [ 'shape' => 'PayloadFormatIndicator', ], 'contentType' => [ 'shape' => 'ContentType', ], 'responseTopic' => [ 'shape' => 'ResponseTopic', ], 'correlationData' => [ 'shape' => 'CorrelationData', ], 'messageExpiry' => [ 'shape' => 'MessageExpiry', ], 'userProperties' => [ 'shape' => 'UserProperties', ], ], ], 'MqttPassword' => [ 'type' => 'blob', 'max' => 65535, 'min' => 1, ], 'MqttTopic' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'MqttUsername' => [ 'type' => 'string', 'max' => 65535, 'min' => 1, 'pattern' => '[\\s\\S]*', ], 'NamedShadowIndexingMode' => [ 'type' => 'string', 'enum' => [ 'OFF', 'ON', ], ], 'NamedShadowNamesFilter' => [ 'type' => 'list', 'member' => [ 'shape' => 'ShadowName', ], ], 'NamespaceId' => [ 'type' => 'string', 'pattern' => '[a-zA-Z0-9_-]+', ], 'NextToken' => [ 'type' => 'string', ], 'NonCompliantChecksCount' => [ 'type' => 'integer', ], 'NonCompliantResource' => [ 'type' => 'structure', 'members' => [ 'resourceType' => [ 'shape' => 'ResourceType', ], 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'additionalInfo' => [ 'shape' => 'StringMap', ], ], ], 'NonCompliantResourcesCount' => [ 'type' => 'long', ], 'NotConfiguredException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'NullableBoolean' => [ 'type' => 'boolean', ], 'Number' => [ 'type' => 'double', ], 'NumberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Number', ], ], 'NumberOfRetries' => [ 'type' => 'integer', 'max' => 10, 'min' => 0, ], 'NumberOfThings' => [ 'type' => 'integer', 'min' => 1, ], 'OCSPLambdaArn' => [ 'type' => 'string', 'max' => 140, ], 'OTAUpdateArn' => [ 'type' => 'string', ], 'OTAUpdateDescription' => [ 'type' => 'string', 'max' => 2028, 'pattern' => '[^\\p{C}]+', ], 'OTAUpdateErrorMessage' => [ 'type' => 'string', ], 'OTAUpdateFile' => [ 'type' => 'structure', 'members' => [ 'fileName' => [ 'shape' => 'FileName', ], 'fileType' => [ 'shape' => 'FileType', ], 'fileVersion' => [ 'shape' => 'OTAUpdateFileVersion', ], 'fileLocation' => [ 'shape' => 'FileLocation', ], 'codeSigning' => [ 'shape' => 'CodeSigning', ], 'attributes' => [ 'shape' => 'AttributesMap', ], ], ], 'OTAUpdateFileVersion' => [ 'type' => 'string', ], 'OTAUpdateFiles' => [ 'type' => 'list', 'member' => [ 'shape' => 'OTAUpdateFile', ], 'min' => 1, ], 'OTAUpdateId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'OTAUpdateInfo' => [ 'type' => 'structure', 'members' => [ 'otaUpdateId' => [ 'shape' => 'OTAUpdateId', ], 'otaUpdateArn' => [ 'shape' => 'OTAUpdateArn', ], 'creationDate' => [ 'shape' => 'DateType', ], 'lastModifiedDate' => [ 'shape' => 'DateType', ], 'description' => [ 'shape' => 'OTAUpdateDescription', ], 'targets' => [ 'shape' => 'Targets', ], 'protocols' => [ 'shape' => 'Protocols', ], 'awsJobExecutionsRolloutConfig' => [ 'shape' => 'AwsJobExecutionsRolloutConfig', ], 'awsJobPresignedUrlConfig' => [ 'shape' => 'AwsJobPresignedUrlConfig', ], 'targetSelection' => [ 'shape' => 'TargetSelection', ], 'otaUpdateFiles' => [ 'shape' => 'OTAUpdateFiles', ], 'otaUpdateStatus' => [ 'shape' => 'OTAUpdateStatus', ], 'awsIotJobId' => [ 'shape' => 'AwsIotJobId', ], 'awsIotJobArn' => [ 'shape' => 'AwsIotJobArn', ], 'errorInfo' => [ 'shape' => 'ErrorInfo', ], 'additionalParameters' => [ 'shape' => 'AdditionalParameterMap', ], ], ], 'OTAUpdateStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_PENDING', 'CREATE_IN_PROGRESS', 'CREATE_COMPLETE', 'CREATE_FAILED', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', ], ], 'OTAUpdateSummary' => [ 'type' => 'structure', 'members' => [ 'otaUpdateId' => [ 'shape' => 'OTAUpdateId', ], 'otaUpdateArn' => [ 'shape' => 'OTAUpdateArn', ], 'creationDate' => [ 'shape' => 'DateType', ], ], ], 'OTAUpdatesSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'OTAUpdateSummary', ], ], 'OpenSearchAction' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'endpoint', 'index', 'type', 'id', ], 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'endpoint' => [ 'shape' => 'ElasticsearchEndpoint', ], 'index' => [ 'shape' => 'ElasticsearchIndex', ], 'type' => [ 'shape' => 'ElasticsearchType', ], 'id' => [ 'shape' => 'ElasticsearchId', ], ], ], 'Optional' => [ 'type' => 'boolean', ], 'OptionalVersion' => [ 'type' => 'long', ], 'OutgoingCertificate' => [ 'type' => 'structure', 'members' => [ 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'certificateId' => [ 'shape' => 'CertificateId', ], 'transferredTo' => [ 'shape' => 'AwsAccountId', ], 'transferDate' => [ 'shape' => 'DateType', ], 'transferMessage' => [ 'shape' => 'Message', ], 'creationDate' => [ 'shape' => 'DateType', ], ], ], 'OutgoingCertificates' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutgoingCertificate', ], ], 'OverrideDynamicGroups' => [ 'type' => 'boolean', ], 'PackageArn' => [ 'type' => 'string', ], 'PackageCatalogMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'PackageName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9-_.]+', ], 'PackageSummary' => [ 'type' => 'structure', 'members' => [ 'packageName' => [ 'shape' => 'PackageName', ], 'defaultVersionName' => [ 'shape' => 'VersionName', ], 'creationDate' => [ 'shape' => 'CreationDate', ], 'lastModifiedDate' => [ 'shape' => 'LastModifiedDate', ], ], ], 'PackageSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PackageSummary', ], ], 'PackageVersionAction' => [ 'type' => 'string', 'enum' => [ 'PUBLISH', 'DEPRECATE', ], ], 'PackageVersionArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '^arn:[!-~]+$', ], 'PackageVersionArtifact' => [ 'type' => 'structure', 'members' => [ 's3Location' => [ 'shape' => 'S3Location', ], ], ], 'PackageVersionErrorReason' => [ 'type' => 'string', ], 'PackageVersionRecipe' => [ 'type' => 'string', 'max' => 3072, 'sensitive' => true, ], 'PackageVersionStatus' => [ 'type' => 'string', 'enum' => [ 'DRAFT', 'PUBLISHED', 'DEPRECATED', ], ], 'PackageVersionSummary' => [ 'type' => 'structure', 'members' => [ 'packageName' => [ 'shape' => 'PackageName', ], 'versionName' => [ 'shape' => 'VersionName', ], 'status' => [ 'shape' => 'PackageVersionStatus', ], 'creationDate' => [ 'shape' => 'CreationDate', ], 'lastModifiedDate' => [ 'shape' => 'LastModifiedDate', ], ], ], 'PackageVersionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PackageVersionSummary', ], ], 'PageSize' => [ 'type' => 'integer', 'max' => 250, 'min' => 1, ], 'Parameter' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'ParameterKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'ParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ParameterKey', ], 'value' => [ 'shape' => 'ParameterValue', ], ], 'ParameterValue' => [ 'type' => 'string', 'max' => 30720, 'min' => 1, 'pattern' => '[^\\p{C}]+', ], 'Parameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'Parameter', ], 'value' => [ 'shape' => 'Value', ], ], 'PartitionKey' => [ 'type' => 'string', ], 'PayloadField' => [ 'type' => 'string', ], 'PayloadFormatIndicator' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'PayloadVersion' => [ 'type' => 'string', 'max' => 32, 'min' => 10, 'pattern' => '^[0-9-]+$', ], 'Percent' => [ 'type' => 'double', 'max' => 100, 'min' => 0, ], 'PercentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Percent', ], ], 'PercentPair' => [ 'type' => 'structure', 'members' => [ 'percent' => [ 'shape' => 'Percent', ], 'value' => [ 'shape' => 'PercentValue', ], ], ], 'PercentValue' => [ 'type' => 'double', ], 'Percentage' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'Percentiles' => [ 'type' => 'list', 'member' => [ 'shape' => 'PercentPair', ], ], 'Platform' => [ 'type' => 'string', ], 'Policies' => [ 'type' => 'list', 'member' => [ 'shape' => 'Policy', ], ], 'Policy' => [ 'type' => 'structure', 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', ], 'policyArn' => [ 'shape' => 'PolicyArn', ], ], ], 'PolicyArn' => [ 'type' => 'string', ], 'PolicyDocument' => [ 'type' => 'string', 'max' => 404600, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'PolicyDocuments' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolicyDocument', ], ], 'PolicyName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\w+=,.@-]+', ], 'PolicyNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolicyName', ], ], 'PolicyTarget' => [ 'type' => 'string', ], 'PolicyTargets' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolicyTarget', ], ], 'PolicyTemplateName' => [ 'type' => 'string', 'enum' => [ 'BLANK_POLICY', ], ], 'PolicyVersion' => [ 'type' => 'structure', 'members' => [ 'versionId' => [ 'shape' => 'PolicyVersionId', ], 'isDefaultVersion' => [ 'shape' => 'IsDefaultVersion', ], 'createDate' => [ 'shape' => 'DateType', ], ], ], 'PolicyVersionId' => [ 'type' => 'string', 'pattern' => '[0-9]+', ], 'PolicyVersionIdentifier' => [ 'type' => 'structure', 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', ], 'policyVersionId' => [ 'shape' => 'PolicyVersionId', ], ], ], 'PolicyVersions' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolicyVersion', ], ], 'Port' => [ 'type' => 'integer', 'max' => 65535, 'min' => 0, ], 'Ports' => [ 'type' => 'list', 'member' => [ 'shape' => 'Port', ], ], 'Prefix' => [ 'type' => 'string', ], 'PresignedUrlConfig' => [ 'type' => 'structure', 'members' => [ 'roleArn' => [ 'shape' => 'RoleArn', ], 'expiresInSec' => [ 'shape' => 'ExpiresInSec', ], ], ], 'PrimitiveBoolean' => [ 'type' => 'boolean', ], 'Principal' => [ 'type' => 'string', ], 'PrincipalArn' => [ 'type' => 'string', ], 'PrincipalId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9]+', ], 'PrincipalThingObject' => [ 'type' => 'structure', 'required' => [ 'thingName', ], 'members' => [ 'thingName' => [ 'shape' => 'ThingName', ], 'thingPrincipalType' => [ 'shape' => 'ThingPrincipalType', ], ], ], 'PrincipalThingObjects' => [ 'type' => 'list', 'member' => [ 'shape' => 'PrincipalThingObject', ], ], 'Principals' => [ 'type' => 'list', 'member' => [ 'shape' => 'PrincipalArn', ], ], 'PrivateKey' => [ 'type' => 'string', 'min' => 1, 'sensitive' => true, ], 'ProcessingTargetName' => [ 'type' => 'string', ], 'ProcessingTargetNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProcessingTargetName', ], ], 'PropagatingAttribute' => [ 'type' => 'structure', 'members' => [ 'userPropertyKey' => [ 'shape' => 'UserPropertyKeyName', ], 'thingAttribute' => [ 'shape' => 'AttributeName', ], 'connectionAttribute' => [ 'shape' => 'ConnectionAttributeName', ], ], ], 'PropagatingAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PropagatingAttribute', ], ], 'Protocol' => [ 'type' => 'string', 'enum' => [ 'MQTT', 'HTTP', ], ], 'Protocols' => [ 'type' => 'list', 'member' => [ 'shape' => 'Protocol', ], 'max' => 2, 'min' => 1, ], 'ProvisioningHook' => [ 'type' => 'structure', 'required' => [ 'targetArn', ], 'members' => [ 'payloadVersion' => [ 'shape' => 'PayloadVersion', ], 'targetArn' => [ 'shape' => 'TargetArn', ], ], ], 'ProvisioningTemplateListing' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProvisioningTemplateSummary', ], ], 'ProvisioningTemplateSummary' => [ 'type' => 'structure', 'members' => [ 'templateArn' => [ 'shape' => 'TemplateArn', ], 'templateName' => [ 'shape' => 'TemplateName', ], 'description' => [ 'shape' => 'TemplateDescription', ], 'creationDate' => [ 'shape' => 'DateType', ], 'lastModifiedDate' => [ 'shape' => 'DateType', ], 'enabled' => [ 'shape' => 'Enabled', ], 'type' => [ 'shape' => 'TemplateType', ], ], ], 'ProvisioningTemplateVersionListing' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProvisioningTemplateVersionSummary', ], ], 'ProvisioningTemplateVersionSummary' => [ 'type' => 'structure', 'members' => [ 'versionId' => [ 'shape' => 'TemplateVersionId', ], 'creationDate' => [ 'shape' => 'DateType', ], 'isDefaultVersion' => [ 'shape' => 'IsDefaultVersion', ], ], ], 'PublicKey' => [ 'type' => 'string', 'min' => 1, ], 'PublicKeyMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'KeyName', ], 'value' => [ 'shape' => 'KeyValue', ], ], 'PublishFindingToSnsParams' => [ 'type' => 'structure', 'required' => [ 'topicArn', ], 'members' => [ 'topicArn' => [ 'shape' => 'SnsTopicArn', ], ], ], 'PutAssetPropertyValueEntry' => [ 'type' => 'structure', 'required' => [ 'propertyValues', ], 'members' => [ 'entryId' => [ 'shape' => 'AssetPropertyEntryId', ], 'assetId' => [ 'shape' => 'AssetId', ], 'propertyId' => [ 'shape' => 'AssetPropertyId', ], 'propertyAlias' => [ 'shape' => 'AssetPropertyAlias', ], 'propertyValues' => [ 'shape' => 'AssetPropertyValueList', ], ], ], 'PutAssetPropertyValueEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PutAssetPropertyValueEntry', ], 'min' => 1, ], 'PutItemInput' => [ 'type' => 'structure', 'required' => [ 'tableName', ], 'members' => [ 'tableName' => [ 'shape' => 'TableName', ], ], ], 'PutVerificationStateOnViolationRequest' => [ 'type' => 'structure', 'required' => [ 'violationId', 'verificationState', ], 'members' => [ 'violationId' => [ 'shape' => 'ViolationId', 'location' => 'uri', 'locationName' => 'violationId', ], 'verificationState' => [ 'shape' => 'VerificationState', ], 'verificationStateDescription' => [ 'shape' => 'VerificationStateDescription', ], ], ], 'PutVerificationStateOnViolationResponse' => [ 'type' => 'structure', 'members' => [], ], 'Qos' => [ 'type' => 'integer', 'max' => 1, 'min' => 0, ], 'QueryMaxResults' => [ 'type' => 'integer', 'max' => 500, 'min' => 1, ], 'QueryString' => [ 'type' => 'string', 'min' => 1, ], 'QueryVersion' => [ 'type' => 'string', ], 'QueueUrl' => [ 'type' => 'string', ], 'QueuedThings' => [ 'type' => 'integer', ], 'RangeKeyField' => [ 'type' => 'string', ], 'RangeKeyValue' => [ 'type' => 'string', ], 'RateIncreaseCriteria' => [ 'type' => 'structure', 'members' => [ 'numberOfNotifiedThings' => [ 'shape' => 'NumberOfThings', ], 'numberOfSucceededThings' => [ 'shape' => 'NumberOfThings', ], ], ], 'ReasonCode' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[\\p{Upper}\\p{Digit}_]+', ], 'ReasonForNonCompliance' => [ 'type' => 'string', ], 'ReasonForNonComplianceCode' => [ 'type' => 'string', ], 'ReasonForNonComplianceCodes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReasonForNonComplianceCode', ], 'max' => 25, 'min' => 1, ], 'Recursive' => [ 'type' => 'boolean', ], 'RecursiveWithoutDefault' => [ 'type' => 'boolean', ], 'Regex' => [ 'type' => 'string', ], 'RegisterCACertificateRequest' => [ 'type' => 'structure', 'required' => [ 'caCertificate', ], 'members' => [ 'caCertificate' => [ 'shape' => 'CertificatePem', ], 'verificationCertificate' => [ 'shape' => 'CertificatePem', ], 'setAsActive' => [ 'shape' => 'SetAsActive', 'location' => 'querystring', 'locationName' => 'setAsActive', ], 'allowAutoRegistration' => [ 'shape' => 'AllowAutoRegistration', 'location' => 'querystring', 'locationName' => 'allowAutoRegistration', ], 'registrationConfig' => [ 'shape' => 'RegistrationConfig', ], 'tags' => [ 'shape' => 'TagList', ], 'certificateMode' => [ 'shape' => 'CertificateMode', ], ], ], 'RegisterCACertificateResponse' => [ 'type' => 'structure', 'members' => [ 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'certificateId' => [ 'shape' => 'CertificateId', ], ], ], 'RegisterCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'certificatePem', ], 'members' => [ 'certificatePem' => [ 'shape' => 'CertificatePem', ], 'caCertificatePem' => [ 'shape' => 'CertificatePem', ], 'setAsActive' => [ 'shape' => 'SetAsActiveFlag', 'deprecated' => true, 'location' => 'querystring', 'locationName' => 'setAsActive', ], 'status' => [ 'shape' => 'CertificateStatus', ], ], ], 'RegisterCertificateResponse' => [ 'type' => 'structure', 'members' => [ 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'certificateId' => [ 'shape' => 'CertificateId', ], ], ], 'RegisterCertificateWithoutCARequest' => [ 'type' => 'structure', 'required' => [ 'certificatePem', ], 'members' => [ 'certificatePem' => [ 'shape' => 'CertificatePem', ], 'status' => [ 'shape' => 'CertificateStatus', ], ], ], 'RegisterCertificateWithoutCAResponse' => [ 'type' => 'structure', 'members' => [ 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'certificateId' => [ 'shape' => 'CertificateId', ], ], ], 'RegisterThingRequest' => [ 'type' => 'structure', 'required' => [ 'templateBody', ], 'members' => [ 'templateBody' => [ 'shape' => 'TemplateBody', ], 'parameters' => [ 'shape' => 'Parameters', ], ], ], 'RegisterThingResponse' => [ 'type' => 'structure', 'members' => [ 'certificatePem' => [ 'shape' => 'CertificatePem', ], 'resourceArns' => [ 'shape' => 'ResourceArns', ], ], ], 'RegistrationCode' => [ 'type' => 'string', 'max' => 64, 'min' => 64, 'pattern' => '(0x)?[a-fA-F0-9]+', ], 'RegistrationCodeValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'RegistrationConfig' => [ 'type' => 'structure', 'members' => [ 'templateBody' => [ 'shape' => 'TemplateBody', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'templateName' => [ 'shape' => 'TemplateName', ], ], ], 'RegistryMaxResults' => [ 'type' => 'integer', 'max' => 250, 'min' => 1, ], 'RegistryS3BucketName' => [ 'type' => 'string', 'max' => 256, 'min' => 3, 'pattern' => '[a-zA-Z0-9._-]+', ], 'RegistryS3KeyName' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[a-zA-Z0-9!_.*\'()-\\/]+', ], 'RejectCertificateTransferRequest' => [ 'type' => 'structure', 'required' => [ 'certificateId', ], 'members' => [ 'certificateId' => [ 'shape' => 'CertificateId', 'location' => 'uri', 'locationName' => 'certificateId', ], 'rejectReason' => [ 'shape' => 'Message', ], ], ], 'RejectedThings' => [ 'type' => 'integer', ], 'RelatedResource' => [ 'type' => 'structure', 'members' => [ 'resourceType' => [ 'shape' => 'ResourceType', ], 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'additionalInfo' => [ 'shape' => 'StringMap', ], ], ], 'RelatedResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'RelatedResource', ], ], 'RemoveAuthorizerConfig' => [ 'type' => 'boolean', ], 'RemoveAutoRegistration' => [ 'type' => 'boolean', ], 'RemoveHook' => [ 'type' => 'boolean', ], 'RemoveThingFromBillingGroupRequest' => [ 'type' => 'structure', 'members' => [ 'billingGroupName' => [ 'shape' => 'BillingGroupName', ], 'billingGroupArn' => [ 'shape' => 'BillingGroupArn', ], 'thingName' => [ 'shape' => 'ThingName', ], 'thingArn' => [ 'shape' => 'ThingArn', ], ], ], 'RemoveThingFromBillingGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'RemoveThingFromThingGroupRequest' => [ 'type' => 'structure', 'members' => [ 'thingGroupName' => [ 'shape' => 'ThingGroupName', ], 'thingGroupArn' => [ 'shape' => 'ThingGroupArn', ], 'thingName' => [ 'shape' => 'ThingName', ], 'thingArn' => [ 'shape' => 'ThingArn', ], ], ], 'RemoveThingFromThingGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'RemoveThingType' => [ 'type' => 'boolean', ], 'RemovedThings' => [ 'type' => 'integer', ], 'ReplaceDefaultPolicyVersionParams' => [ 'type' => 'structure', 'required' => [ 'templateName', ], 'members' => [ 'templateName' => [ 'shape' => 'PolicyTemplateName', ], ], ], 'ReplaceTopicRuleRequest' => [ 'type' => 'structure', 'required' => [ 'ruleName', 'topicRulePayload', ], 'members' => [ 'ruleName' => [ 'shape' => 'RuleName', 'location' => 'uri', 'locationName' => 'ruleName', ], 'topicRulePayload' => [ 'shape' => 'TopicRulePayload', ], ], 'payload' => 'topicRulePayload', ], 'ReportType' => [ 'type' => 'string', 'enum' => [ 'ERRORS', 'RESULTS', ], ], 'RepublishAction' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'topic', ], 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'topic' => [ 'shape' => 'TopicPattern', ], 'qos' => [ 'shape' => 'Qos', ], 'headers' => [ 'shape' => 'MqttHeaders', ], ], ], 'ReservedDomainConfigurationName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\w.:-]+', ], 'Resource' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '[\\s\\S]*', ], 'ResourceAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], 'resourceId' => [ 'shape' => 'resourceId', ], 'resourceArn' => [ 'shape' => 'resourceArn', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResourceArn' => [ 'type' => 'string', ], 'ResourceArns' => [ 'type' => 'map', 'key' => [ 'shape' => 'ResourceLogicalId', ], 'value' => [ 'shape' => 'ResourceArn', ], ], 'ResourceAttributeKey' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[a-zA-Z0-9:_-]+', ], 'ResourceAttributeValue' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[^\\p{C}]+', ], 'ResourceAttributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'ResourceAttributeKey', ], 'value' => [ 'shape' => 'ResourceAttributeValue', ], 'sensitive' => true, ], 'ResourceDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '[^\\p{C}]+', 'sensitive' => true, ], 'ResourceIdentifier' => [ 'type' => 'structure', 'members' => [ 'deviceCertificateId' => [ 'shape' => 'CertificateId', ], 'caCertificateId' => [ 'shape' => 'CertificateId', ], 'cognitoIdentityPoolId' => [ 'shape' => 'CognitoIdentityPoolId', ], 'clientId' => [ 'shape' => 'ClientId', ], 'policyVersionIdentifier' => [ 'shape' => 'PolicyVersionIdentifier', ], 'account' => [ 'shape' => 'AwsAccountId', ], 'iamRoleArn' => [ 'shape' => 'RoleArn', ], 'roleAliasArn' => [ 'shape' => 'RoleAliasArn', ], 'issuerCertificateIdentifier' => [ 'shape' => 'IssuerCertificateIdentifier', ], 'deviceCertificateArn' => [ 'shape' => 'CertificateArn', ], ], ], 'ResourceLogicalId' => [ 'type' => 'string', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ResourceRegistrationFailureException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'DEVICE_CERTIFICATE', 'CA_CERTIFICATE', 'IOT_POLICY', 'COGNITO_IDENTITY_POOL', 'CLIENT_ID', 'ACCOUNT_SETTINGS', 'ROLE_ALIAS', 'IAM_ROLE', 'ISSUER_CERTIFICATE', ], ], 'Resources' => [ 'type' => 'list', 'member' => [ 'shape' => 'Resource', ], ], 'ResponseTopic' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'RetryAttempt' => [ 'type' => 'integer', ], 'RetryCriteria' => [ 'type' => 'structure', 'required' => [ 'failureType', 'numberOfRetries', ], 'members' => [ 'failureType' => [ 'shape' => 'RetryableFailureType', ], 'numberOfRetries' => [ 'shape' => 'NumberOfRetries', ], ], ], 'RetryCriteriaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RetryCriteria', ], 'max' => 2, 'min' => 1, ], 'RetryableFailureType' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'TIMED_OUT', 'ALL', ], ], 'RoleAlias' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\w=,@-]+', ], 'RoleAliasArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'RoleAliasDescription' => [ 'type' => 'structure', 'members' => [ 'roleAlias' => [ 'shape' => 'RoleAlias', ], 'roleAliasArn' => [ 'shape' => 'RoleAliasArn', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'owner' => [ 'shape' => 'AwsAccountId', ], 'credentialDurationSeconds' => [ 'shape' => 'CredentialDurationSeconds', ], 'creationDate' => [ 'shape' => 'DateType', ], 'lastModifiedDate' => [ 'shape' => 'DateType', ], ], ], 'RoleAliases' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoleAlias', ], ], 'RoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, ], 'RolloutRatePerMinute' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'RuleArn' => [ 'type' => 'string', ], 'RuleName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_]+$', ], 'S3Action' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'bucketName', 'key', ], 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'bucketName' => [ 'shape' => 'BucketName', ], 'key' => [ 'shape' => 'Key', ], 'cannedAcl' => [ 'shape' => 'CannedAccessControlList', ], ], ], 'S3Bucket' => [ 'type' => 'string', 'min' => 1, ], 'S3Destination' => [ 'type' => 'structure', 'members' => [ 'bucket' => [ 'shape' => 'S3Bucket', ], 'prefix' => [ 'shape' => 'Prefix', ], ], ], 'S3FileUrl' => [ 'type' => 'string', 'max' => 65535, ], 'S3FileUrlList' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3FileUrl', ], ], 'S3Key' => [ 'type' => 'string', 'min' => 1, ], 'S3Location' => [ 'type' => 'structure', 'members' => [ 'bucket' => [ 'shape' => 'S3Bucket', ], 'key' => [ 'shape' => 'S3Key', ], 'version' => [ 'shape' => 'S3Version', ], ], ], 'S3Version' => [ 'type' => 'string', ], 'SQL' => [ 'type' => 'string', ], 'SalesforceAction' => [ 'type' => 'structure', 'required' => [ 'token', 'url', ], 'members' => [ 'token' => [ 'shape' => 'SalesforceToken', ], 'url' => [ 'shape' => 'SalesforceEndpoint', ], ], ], 'SalesforceEndpoint' => [ 'type' => 'string', 'max' => 2000, 'pattern' => 'https://ingestion-[a-zA-Z0-9]{1,12}\\.[a-zA-Z0-9]+\\.((sfdc-matrix\\.net)|(sfdcnow\\.com))/streams/\\w{1,20}/\\w{1,20}/event', ], 'SalesforceToken' => [ 'type' => 'string', 'min' => 40, ], 'Sbom' => [ 'type' => 'structure', 'members' => [ 's3Location' => [ 'shape' => 'S3Location', ], ], ], 'SbomValidationErrorCode' => [ 'type' => 'string', 'enum' => [ 'INCOMPATIBLE_FORMAT', 'FILE_SIZE_LIMIT_EXCEEDED', ], ], 'SbomValidationErrorMessage' => [ 'type' => 'string', ], 'SbomValidationResult' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'SUCCEEDED', ], ], 'SbomValidationResultSummary' => [ 'type' => 'structure', 'members' => [ 'fileName' => [ 'shape' => 'FileName', ], 'validationResult' => [ 'shape' => 'SbomValidationResult', ], 'errorCode' => [ 'shape' => 'SbomValidationErrorCode', ], 'errorMessage' => [ 'shape' => 'SbomValidationErrorMessage', ], ], ], 'SbomValidationResultSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SbomValidationResultSummary', ], ], 'SbomValidationStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'FAILED', 'SUCCEEDED', ], ], 'ScheduledAuditArn' => [ 'type' => 'string', ], 'ScheduledAuditMetadata' => [ 'type' => 'structure', 'members' => [ 'scheduledAuditName' => [ 'shape' => 'ScheduledAuditName', ], 'scheduledAuditArn' => [ 'shape' => 'ScheduledAuditArn', ], 'frequency' => [ 'shape' => 'AuditFrequency', ], 'dayOfMonth' => [ 'shape' => 'DayOfMonth', ], 'dayOfWeek' => [ 'shape' => 'DayOfWeek', ], ], ], 'ScheduledAuditMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScheduledAuditMetadata', ], ], 'ScheduledAuditName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'ScheduledJobRollout' => [ 'type' => 'structure', 'members' => [ 'startTime' => [ 'shape' => 'StringDateTime', ], ], ], 'ScheduledJobRolloutList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScheduledJobRollout', ], ], 'SchedulingConfig' => [ 'type' => 'structure', 'members' => [ 'startTime' => [ 'shape' => 'StringDateTime', ], 'endTime' => [ 'shape' => 'StringDateTime', ], 'endBehavior' => [ 'shape' => 'JobEndBehavior', ], 'maintenanceWindows' => [ 'shape' => 'MaintenanceWindows', ], ], ], 'SearchIndexRequest' => [ 'type' => 'structure', 'required' => [ 'queryString', ], 'members' => [ 'indexName' => [ 'shape' => 'IndexName', ], 'queryString' => [ 'shape' => 'QueryString', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'SearchQueryMaxResults', ], 'queryVersion' => [ 'shape' => 'QueryVersion', ], ], ], 'SearchIndexResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'things' => [ 'shape' => 'ThingDocumentList', ], 'thingGroups' => [ 'shape' => 'ThingGroupDocumentList', ], ], ], 'SearchQueryMaxResults' => [ 'type' => 'integer', 'min' => 1, ], 'SearchableAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeName', ], ], 'Seconds' => [ 'type' => 'integer', ], 'SecurityGroupId' => [ 'type' => 'string', ], 'SecurityGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], ], 'SecurityPolicy' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[\\s\\S]*', ], 'SecurityProfileArn' => [ 'type' => 'string', ], 'SecurityProfileDescription' => [ 'type' => 'string', 'max' => 1000, 'pattern' => '[\\p{Graph}\\x20]*', ], 'SecurityProfileIdentifier' => [ 'type' => 'structure', 'required' => [ 'name', 'arn', ], 'members' => [ 'name' => [ 'shape' => 'SecurityProfileName', ], 'arn' => [ 'shape' => 'SecurityProfileArn', ], ], ], 'SecurityProfileIdentifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityProfileIdentifier', ], ], 'SecurityProfileName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9:_-]+', ], 'SecurityProfileTarget' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'SecurityProfileTargetArn', ], ], ], 'SecurityProfileTargetArn' => [ 'type' => 'string', ], 'SecurityProfileTargetMapping' => [ 'type' => 'structure', 'members' => [ 'securityProfileIdentifier' => [ 'shape' => 'SecurityProfileIdentifier', ], 'target' => [ 'shape' => 'SecurityProfileTarget', ], ], ], 'SecurityProfileTargetMappings' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityProfileTargetMapping', ], ], 'SecurityProfileTargets' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityProfileTarget', ], ], 'ServerCertificateArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'AcmCertificateArn', ], 'max' => 1, 'min' => 0, ], 'ServerCertificateConfig' => [ 'type' => 'structure', 'members' => [ 'enableOCSPCheck' => [ 'shape' => 'EnableOCSPCheck', ], 'ocspLambdaArn' => [ 'shape' => 'OCSPLambdaArn', ], 'ocspAuthorizedResponderArn' => [ 'shape' => 'AcmCertificateArn', ], ], ], 'ServerCertificateStatus' => [ 'type' => 'string', 'enum' => [ 'INVALID', 'VALID', ], ], 'ServerCertificateStatusDetail' => [ 'type' => 'string', ], 'ServerCertificateSummary' => [ 'type' => 'structure', 'members' => [ 'serverCertificateArn' => [ 'shape' => 'AcmCertificateArn', ], 'serverCertificateStatus' => [ 'shape' => 'ServerCertificateStatus', ], 'serverCertificateStatusDetail' => [ 'shape' => 'ServerCertificateStatusDetail', ], ], ], 'ServerCertificates' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServerCertificateSummary', ], ], 'ServerName' => [ 'type' => 'string', 'max' => 253, 'min' => 1, 'pattern' => '[\\s\\S]*', ], 'ServiceName' => [ 'type' => 'string', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 402, ], 'exception' => true, ], 'ServiceType' => [ 'type' => 'string', 'enum' => [ 'DATA', 'CREDENTIAL_PROVIDER', 'JOBS', ], ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], 'SetAsActive' => [ 'type' => 'boolean', ], 'SetAsActiveFlag' => [ 'type' => 'boolean', ], 'SetAsDefault' => [ 'type' => 'boolean', ], 'SetDefaultAuthorizerRequest' => [ 'type' => 'structure', 'required' => [ 'authorizerName', ], 'members' => [ 'authorizerName' => [ 'shape' => 'AuthorizerName', ], ], ], 'SetDefaultAuthorizerResponse' => [ 'type' => 'structure', 'members' => [ 'authorizerName' => [ 'shape' => 'AuthorizerName', ], 'authorizerArn' => [ 'shape' => 'AuthorizerArn', ], ], ], 'SetDefaultPolicyVersionRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', 'policyVersionId', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'policyName', ], 'policyVersionId' => [ 'shape' => 'PolicyVersionId', 'location' => 'uri', 'locationName' => 'policyVersionId', ], ], ], 'SetLoggingOptionsRequest' => [ 'type' => 'structure', 'required' => [ 'loggingOptionsPayload', ], 'members' => [ 'loggingOptionsPayload' => [ 'shape' => 'LoggingOptionsPayload', ], ], 'payload' => 'loggingOptionsPayload', ], 'SetV2LoggingLevelRequest' => [ 'type' => 'structure', 'required' => [ 'logTarget', 'logLevel', ], 'members' => [ 'logTarget' => [ 'shape' => 'LogTarget', ], 'logLevel' => [ 'shape' => 'LogLevel', ], ], ], 'SetV2LoggingOptionsRequest' => [ 'type' => 'structure', 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'defaultLogLevel' => [ 'shape' => 'LogLevel', ], 'disableAllLogs' => [ 'shape' => 'DisableAllLogs', ], ], ], 'ShadowName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[$a-zA-Z0-9:_-]+', ], 'SigV4Authorization' => [ 'type' => 'structure', 'required' => [ 'signingRegion', 'serviceName', 'roleArn', ], 'members' => [ 'signingRegion' => [ 'shape' => 'SigningRegion', ], 'serviceName' => [ 'shape' => 'ServiceName', ], 'roleArn' => [ 'shape' => 'AwsArn', ], ], ], 'Signature' => [ 'type' => 'blob', ], 'SignatureAlgorithm' => [ 'type' => 'string', ], 'SigningJobId' => [ 'type' => 'string', ], 'SigningProfileName' => [ 'type' => 'string', ], 'SigningProfileParameter' => [ 'type' => 'structure', 'members' => [ 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'platform' => [ 'shape' => 'Platform', ], 'certificatePathOnDevice' => [ 'shape' => 'CertificatePathOnDevice', ], ], ], 'SigningRegion' => [ 'type' => 'string', ], 'SkippedFindingsCount' => [ 'type' => 'long', ], 'SkyfallMaxResults' => [ 'type' => 'integer', 'max' => 250, 'min' => 1, ], 'SnsAction' => [ 'type' => 'structure', 'required' => [ 'targetArn', 'roleArn', ], 'members' => [ 'targetArn' => [ 'shape' => 'AwsArn', ], 'roleArn' => [ 'shape' => 'AwsArn', ], 'messageFormat' => [ 'shape' => 'MessageFormat', ], ], ], 'SnsTopicArn' => [ 'type' => 'string', 'max' => 350, ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'SqlParseException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'SqsAction' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'queueUrl', ], 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'queueUrl' => [ 'shape' => 'QueueUrl', ], 'useBase64' => [ 'shape' => 'UseBase64', ], ], ], 'StartAuditMitigationActionsTaskRequest' => [ 'type' => 'structure', 'required' => [ 'taskId', 'target', 'auditCheckToActionsMapping', 'clientRequestToken', ], 'members' => [ 'taskId' => [ 'shape' => 'MitigationActionsTaskId', 'location' => 'uri', 'locationName' => 'taskId', ], 'target' => [ 'shape' => 'AuditMitigationActionsTaskTarget', ], 'auditCheckToActionsMapping' => [ 'shape' => 'AuditCheckToActionsMapping', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'StartAuditMitigationActionsTaskResponse' => [ 'type' => 'structure', 'members' => [ 'taskId' => [ 'shape' => 'MitigationActionsTaskId', ], ], ], 'StartDetectMitigationActionsTaskRequest' => [ 'type' => 'structure', 'required' => [ 'taskId', 'target', 'actions', 'clientRequestToken', ], 'members' => [ 'taskId' => [ 'shape' => 'MitigationActionsTaskId', 'location' => 'uri', 'locationName' => 'taskId', ], 'target' => [ 'shape' => 'DetectMitigationActionsTaskTarget', ], 'actions' => [ 'shape' => 'DetectMitigationActionsToExecuteList', ], 'violationEventOccurrenceRange' => [ 'shape' => 'ViolationEventOccurrenceRange', ], 'includeOnlyActiveViolations' => [ 'shape' => 'NullableBoolean', ], 'includeSuppressedAlerts' => [ 'shape' => 'NullableBoolean', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'StartDetectMitigationActionsTaskResponse' => [ 'type' => 'structure', 'members' => [ 'taskId' => [ 'shape' => 'MitigationActionsTaskId', ], ], ], 'StartOnDemandAuditTaskRequest' => [ 'type' => 'structure', 'required' => [ 'targetCheckNames', ], 'members' => [ 'targetCheckNames' => [ 'shape' => 'TargetAuditCheckNames', ], ], ], 'StartOnDemandAuditTaskResponse' => [ 'type' => 'structure', 'members' => [ 'taskId' => [ 'shape' => 'AuditTaskId', ], ], ], 'StartSigningJobParameter' => [ 'type' => 'structure', 'members' => [ 'signingProfileParameter' => [ 'shape' => 'SigningProfileParameter', ], 'signingProfileName' => [ 'shape' => 'SigningProfileName', ], 'destination' => [ 'shape' => 'Destination', ], ], ], 'StartThingRegistrationTaskRequest' => [ 'type' => 'structure', 'required' => [ 'templateBody', 'inputFileBucket', 'inputFileKey', 'roleArn', ], 'members' => [ 'templateBody' => [ 'shape' => 'TemplateBody', ], 'inputFileBucket' => [ 'shape' => 'RegistryS3BucketName', ], 'inputFileKey' => [ 'shape' => 'RegistryS3KeyName', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'StartThingRegistrationTaskResponse' => [ 'type' => 'structure', 'members' => [ 'taskId' => [ 'shape' => 'TaskId', ], ], ], 'StateMachineName' => [ 'type' => 'string', ], 'StateReason' => [ 'type' => 'string', ], 'StateValue' => [ 'type' => 'string', ], 'StatisticalThreshold' => [ 'type' => 'structure', 'members' => [ 'statistic' => [ 'shape' => 'EvaluationStatistic', ], ], ], 'Statistics' => [ 'type' => 'structure', 'members' => [ 'count' => [ 'shape' => 'Count', ], 'average' => [ 'shape' => 'Average', 'box' => true, ], 'sum' => [ 'shape' => 'Sum', 'box' => true, ], 'minimum' => [ 'shape' => 'Minimum', 'box' => true, ], 'maximum' => [ 'shape' => 'Maximum', 'box' => true, ], 'sumOfSquares' => [ 'shape' => 'SumOfSquares', 'box' => true, ], 'variance' => [ 'shape' => 'Variance', 'box' => true, ], 'stdDeviation' => [ 'shape' => 'StdDeviation', 'box' => true, ], ], ], 'Status' => [ 'type' => 'string', 'enum' => [ 'InProgress', 'Completed', 'Failed', 'Cancelled', 'Cancelling', ], ], 'StatusCode' => [ 'type' => 'integer', ], 'StatusReason' => [ 'type' => 'structure', 'required' => [ 'reasonCode', ], 'members' => [ 'reasonCode' => [ 'shape' => 'StatusReasonCode', ], 'reasonDescription' => [ 'shape' => 'StatusReasonDescription', ], ], ], 'StatusReasonCode' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[A-Z0-9_-]+', ], 'StatusReasonDescription' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '[^\\p{C}]*', ], 'StdDeviation' => [ 'type' => 'double', ], 'StepFunctionsAction' => [ 'type' => 'structure', 'required' => [ 'stateMachineName', 'roleArn', ], 'members' => [ 'executionNamePrefix' => [ 'shape' => 'ExecutionNamePrefix', ], 'stateMachineName' => [ 'shape' => 'StateMachineName', ], 'roleArn' => [ 'shape' => 'AwsArn', ], ], ], 'StopThingRegistrationTaskRequest' => [ 'type' => 'structure', 'required' => [ 'taskId', ], 'members' => [ 'taskId' => [ 'shape' => 'TaskId', 'location' => 'uri', 'locationName' => 'taskId', ], ], ], 'StopThingRegistrationTaskResponse' => [ 'type' => 'structure', 'members' => [], ], 'Stream' => [ 'type' => 'structure', 'members' => [ 'streamId' => [ 'shape' => 'StreamId', ], 'fileId' => [ 'shape' => 'FileId', ], ], ], 'StreamArn' => [ 'type' => 'string', ], 'StreamDescription' => [ 'type' => 'string', 'max' => 2028, 'pattern' => '[^\\p{C}]+', ], 'StreamFile' => [ 'type' => 'structure', 'members' => [ 'fileId' => [ 'shape' => 'FileId', ], 's3Location' => [ 'shape' => 'S3Location', ], ], ], 'StreamFiles' => [ 'type' => 'list', 'member' => [ 'shape' => 'StreamFile', ], 'max' => 50, 'min' => 1, ], 'StreamId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'StreamInfo' => [ 'type' => 'structure', 'members' => [ 'streamId' => [ 'shape' => 'StreamId', ], 'streamArn' => [ 'shape' => 'StreamArn', ], 'streamVersion' => [ 'shape' => 'StreamVersion', ], 'description' => [ 'shape' => 'StreamDescription', ], 'files' => [ 'shape' => 'StreamFiles', ], 'createdAt' => [ 'shape' => 'DateType', ], 'lastUpdatedAt' => [ 'shape' => 'DateType', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'StreamName' => [ 'type' => 'string', ], 'StreamSummary' => [ 'type' => 'structure', 'members' => [ 'streamId' => [ 'shape' => 'StreamId', ], 'streamArn' => [ 'shape' => 'StreamArn', ], 'streamVersion' => [ 'shape' => 'StreamVersion', ], 'description' => [ 'shape' => 'StreamDescription', ], ], ], 'StreamVersion' => [ 'type' => 'integer', 'max' => 65535, 'min' => 0, ], 'StreamsSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'StreamSummary', ], ], 'String' => [ 'type' => 'string', ], 'StringCommandExecutionResult' => [ 'type' => 'string', 'min' => 1, ], 'StringDateTime' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'stringValue', ], ], 'StringMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'StringParameterValue' => [ 'type' => 'string', 'min' => 1, ], 'SubnetId' => [ 'type' => 'string', ], 'SubnetIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], ], 'SucceededFindingsCount' => [ 'type' => 'long', ], 'SucceededThings' => [ 'type' => 'integer', ], 'Sum' => [ 'type' => 'double', ], 'SumOfSquares' => [ 'type' => 'double', ], 'SuppressAlerts' => [ 'type' => 'boolean', ], 'SuppressIndefinitely' => [ 'type' => 'boolean', ], 'SuppressedNonCompliantResourcesCount' => [ 'type' => 'long', ], 'TableName' => [ 'type' => 'string', ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'Target' => [ 'type' => 'string', ], 'TargetArn' => [ 'type' => 'string', 'max' => 2048, ], 'TargetAuditCheckNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuditCheckName', ], ], 'TargetFieldName' => [ 'type' => 'string', ], 'TargetFieldOrder' => [ 'type' => 'string', 'enum' => [ 'LatLon', 'LonLat', ], ], 'TargetSelection' => [ 'type' => 'string', 'enum' => [ 'CONTINUOUS', 'SNAPSHOT', ], ], 'TargetViolationIdsForDetectMitigationActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ViolationId', ], 'max' => 25, 'min' => 1, ], 'Targets' => [ 'type' => 'list', 'member' => [ 'shape' => 'Target', ], 'min' => 1, ], 'TaskAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TaskId' => [ 'type' => 'string', 'max' => 40, ], 'TaskIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaskId', ], ], 'TaskStatistics' => [ 'type' => 'structure', 'members' => [ 'totalChecks' => [ 'shape' => 'TotalChecksCount', ], 'inProgressChecks' => [ 'shape' => 'InProgressChecksCount', ], 'waitingForDataCollectionChecks' => [ 'shape' => 'WaitingForDataCollectionChecksCount', ], 'compliantChecks' => [ 'shape' => 'CompliantChecksCount', ], 'nonCompliantChecks' => [ 'shape' => 'NonCompliantChecksCount', ], 'failedChecks' => [ 'shape' => 'FailedChecksCount', ], 'canceledChecks' => [ 'shape' => 'CanceledChecksCount', ], ], ], 'TaskStatisticsForAuditCheck' => [ 'type' => 'structure', 'members' => [ 'totalFindingsCount' => [ 'shape' => 'TotalFindingsCount', ], 'failedFindingsCount' => [ 'shape' => 'FailedFindingsCount', ], 'succeededFindingsCount' => [ 'shape' => 'SucceededFindingsCount', ], 'skippedFindingsCount' => [ 'shape' => 'SkippedFindingsCount', ], 'canceledFindingsCount' => [ 'shape' => 'CanceledFindingsCount', ], ], ], 'TemplateArn' => [ 'type' => 'string', ], 'TemplateBody' => [ 'type' => 'string', 'max' => 10240, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'TemplateDescription' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '[^\\p{C}]*', ], 'TemplateName' => [ 'type' => 'string', 'max' => 36, 'min' => 1, 'pattern' => '^[0-9A-Za-z_-]+$', ], 'TemplateType' => [ 'type' => 'string', 'enum' => [ 'FLEET_PROVISIONING', 'JITP', ], ], 'TemplateVersionId' => [ 'type' => 'integer', ], 'TermsAggregation' => [ 'type' => 'structure', 'members' => [ 'maxBuckets' => [ 'shape' => 'MaxBuckets', ], ], ], 'TestAuthorizationRequest' => [ 'type' => 'structure', 'required' => [ 'authInfos', ], 'members' => [ 'principal' => [ 'shape' => 'Principal', ], 'cognitoIdentityPoolId' => [ 'shape' => 'CognitoIdentityPoolId', ], 'authInfos' => [ 'shape' => 'AuthInfos', ], 'clientId' => [ 'shape' => 'ClientId', 'location' => 'querystring', 'locationName' => 'clientId', ], 'policyNamesToAdd' => [ 'shape' => 'PolicyNames', ], 'policyNamesToSkip' => [ 'shape' => 'PolicyNames', ], ], ], 'TestAuthorizationResponse' => [ 'type' => 'structure', 'members' => [ 'authResults' => [ 'shape' => 'AuthResults', ], ], ], 'TestInvokeAuthorizerRequest' => [ 'type' => 'structure', 'required' => [ 'authorizerName', ], 'members' => [ 'authorizerName' => [ 'shape' => 'AuthorizerName', 'location' => 'uri', 'locationName' => 'authorizerName', ], 'token' => [ 'shape' => 'Token', ], 'tokenSignature' => [ 'shape' => 'TokenSignature', ], 'httpContext' => [ 'shape' => 'HttpContext', ], 'mqttContext' => [ 'shape' => 'MqttContext', ], 'tlsContext' => [ 'shape' => 'TlsContext', ], ], ], 'TestInvokeAuthorizerResponse' => [ 'type' => 'structure', 'members' => [ 'isAuthenticated' => [ 'shape' => 'IsAuthenticated', ], 'principalId' => [ 'shape' => 'PrincipalId', ], 'policyDocuments' => [ 'shape' => 'PolicyDocuments', ], 'refreshAfterInSeconds' => [ 'shape' => 'Seconds', ], 'disconnectAfterInSeconds' => [ 'shape' => 'Seconds', ], ], ], 'ThingArn' => [ 'type' => 'string', ], 'ThingAttribute' => [ 'type' => 'structure', 'members' => [ 'thingName' => [ 'shape' => 'ThingName', ], 'thingTypeName' => [ 'shape' => 'ThingTypeName', ], 'thingArn' => [ 'shape' => 'ThingArn', ], 'attributes' => [ 'shape' => 'Attributes', ], 'version' => [ 'shape' => 'Version', ], ], ], 'ThingAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThingAttribute', ], ], 'ThingConnectivity' => [ 'type' => 'structure', 'members' => [ 'connected' => [ 'shape' => 'Boolean', ], 'timestamp' => [ 'shape' => 'ConnectivityTimestamp', ], 'disconnectReason' => [ 'shape' => 'DisconnectReason', ], ], ], 'ThingConnectivityIndexingMode' => [ 'type' => 'string', 'enum' => [ 'OFF', 'STATUS', ], ], 'ThingDocument' => [ 'type' => 'structure', 'members' => [ 'thingName' => [ 'shape' => 'ThingName', ], 'thingId' => [ 'shape' => 'ThingId', ], 'thingTypeName' => [ 'shape' => 'ThingTypeName', ], 'thingGroupNames' => [ 'shape' => 'ThingGroupNameList', ], 'attributes' => [ 'shape' => 'Attributes', ], 'shadow' => [ 'shape' => 'JsonDocument', ], 'deviceDefender' => [ 'shape' => 'JsonDocument', ], 'connectivity' => [ 'shape' => 'ThingConnectivity', ], ], ], 'ThingDocumentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThingDocument', ], ], 'ThingGroupArn' => [ 'type' => 'string', ], 'ThingGroupDescription' => [ 'type' => 'string', 'max' => 2028, 'pattern' => '[\\p{Graph}\\x20]*', ], 'ThingGroupDocument' => [ 'type' => 'structure', 'members' => [ 'thingGroupName' => [ 'shape' => 'ThingGroupName', ], 'thingGroupId' => [ 'shape' => 'ThingGroupId', ], 'thingGroupDescription' => [ 'shape' => 'ThingGroupDescription', ], 'attributes' => [ 'shape' => 'Attributes', ], 'parentGroupNames' => [ 'shape' => 'ThingGroupNameList', ], ], ], 'ThingGroupDocumentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThingGroupDocument', ], ], 'ThingGroupId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\-]+', ], 'ThingGroupIndexingConfiguration' => [ 'type' => 'structure', 'required' => [ 'thingGroupIndexingMode', ], 'members' => [ 'thingGroupIndexingMode' => [ 'shape' => 'ThingGroupIndexingMode', ], 'managedFields' => [ 'shape' => 'Fields', ], 'customFields' => [ 'shape' => 'Fields', ], ], ], 'ThingGroupIndexingMode' => [ 'type' => 'string', 'enum' => [ 'OFF', 'ON', ], ], 'ThingGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThingGroupName', ], ], 'ThingGroupMetadata' => [ 'type' => 'structure', 'members' => [ 'parentGroupName' => [ 'shape' => 'ThingGroupName', ], 'rootToParentThingGroups' => [ 'shape' => 'ThingGroupNameAndArnList', ], 'creationDate' => [ 'shape' => 'CreationDate', ], ], ], 'ThingGroupName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9:_-]+', ], 'ThingGroupNameAndArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupNameAndArn', ], ], 'ThingGroupNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThingGroupName', ], ], 'ThingGroupNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThingGroupName', ], 'max' => 10, 'min' => 1, ], 'ThingGroupProperties' => [ 'type' => 'structure', 'members' => [ 'thingGroupDescription' => [ 'shape' => 'ThingGroupDescription', ], 'attributePayload' => [ 'shape' => 'AttributePayload', ], ], ], 'ThingId' => [ 'type' => 'string', ], 'ThingIndexingConfiguration' => [ 'type' => 'structure', 'required' => [ 'thingIndexingMode', ], 'members' => [ 'thingIndexingMode' => [ 'shape' => 'ThingIndexingMode', ], 'thingConnectivityIndexingMode' => [ 'shape' => 'ThingConnectivityIndexingMode', ], 'deviceDefenderIndexingMode' => [ 'shape' => 'DeviceDefenderIndexingMode', ], 'namedShadowIndexingMode' => [ 'shape' => 'NamedShadowIndexingMode', ], 'managedFields' => [ 'shape' => 'Fields', ], 'customFields' => [ 'shape' => 'Fields', ], 'filter' => [ 'shape' => 'IndexingFilter', ], ], ], 'ThingIndexingMode' => [ 'type' => 'string', 'enum' => [ 'OFF', 'REGISTRY', 'REGISTRY_AND_SHADOW', ], ], 'ThingName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9:_-]+', ], 'ThingNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThingName', ], ], 'ThingPrincipalObject' => [ 'type' => 'structure', 'required' => [ 'principal', ], 'members' => [ 'principal' => [ 'shape' => 'Principal', ], 'thingPrincipalType' => [ 'shape' => 'ThingPrincipalType', ], ], ], 'ThingPrincipalObjects' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThingPrincipalObject', ], ], 'ThingPrincipalType' => [ 'type' => 'string', 'enum' => [ 'EXCLUSIVE_THING', 'NON_EXCLUSIVE_THING', ], ], 'ThingTypeArn' => [ 'type' => 'string', ], 'ThingTypeDefinition' => [ 'type' => 'structure', 'members' => [ 'thingTypeName' => [ 'shape' => 'ThingTypeName', ], 'thingTypeArn' => [ 'shape' => 'ThingTypeArn', ], 'thingTypeProperties' => [ 'shape' => 'ThingTypeProperties', ], 'thingTypeMetadata' => [ 'shape' => 'ThingTypeMetadata', ], ], ], 'ThingTypeDescription' => [ 'type' => 'string', 'max' => 2028, 'pattern' => '[\\p{Graph}\\x20]*', ], 'ThingTypeId' => [ 'type' => 'string', ], 'ThingTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThingTypeDefinition', ], ], 'ThingTypeMetadata' => [ 'type' => 'structure', 'members' => [ 'deprecated' => [ 'shape' => 'Boolean', ], 'deprecationDate' => [ 'shape' => 'DeprecationDate', ], 'creationDate' => [ 'shape' => 'CreationDate', ], ], ], 'ThingTypeName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9:_-]+', ], 'ThingTypeProperties' => [ 'type' => 'structure', 'members' => [ 'thingTypeDescription' => [ 'shape' => 'ThingTypeDescription', ], 'searchableAttributes' => [ 'shape' => 'SearchableAttributes', ], 'mqtt5Configuration' => [ 'shape' => 'Mqtt5Configuration', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TimeFilter' => [ 'type' => 'structure', 'members' => [ 'after' => [ 'shape' => 'StringDateTime', ], 'before' => [ 'shape' => 'StringDateTime', ], ], ], 'TimedOutThings' => [ 'type' => 'integer', ], 'TimeoutConfig' => [ 'type' => 'structure', 'members' => [ 'inProgressTimeoutInMinutes' => [ 'shape' => 'InProgressTimeoutInMinutes', ], ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TimestreamAction' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'databaseName', 'tableName', 'dimensions', ], 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'databaseName' => [ 'shape' => 'TimestreamDatabaseName', ], 'tableName' => [ 'shape' => 'TimestreamTableName', ], 'dimensions' => [ 'shape' => 'TimestreamDimensionList', ], 'timestamp' => [ 'shape' => 'TimestreamTimestamp', ], ], ], 'TimestreamDatabaseName' => [ 'type' => 'string', ], 'TimestreamDimension' => [ 'type' => 'structure', 'required' => [ 'name', 'value', ], 'members' => [ 'name' => [ 'shape' => 'TimestreamDimensionName', ], 'value' => [ 'shape' => 'TimestreamDimensionValue', ], ], ], 'TimestreamDimensionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TimestreamDimension', ], 'max' => 128, 'min' => 1, ], 'TimestreamDimensionName' => [ 'type' => 'string', ], 'TimestreamDimensionValue' => [ 'type' => 'string', ], 'TimestreamTableName' => [ 'type' => 'string', ], 'TimestreamTimestamp' => [ 'type' => 'structure', 'required' => [ 'value', 'unit', ], 'members' => [ 'value' => [ 'shape' => 'TimestreamTimestampValue', ], 'unit' => [ 'shape' => 'TimestreamTimestampUnit', ], ], ], 'TimestreamTimestampUnit' => [ 'type' => 'string', ], 'TimestreamTimestampValue' => [ 'type' => 'string', ], 'TinyMaxResults' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'TlsConfig' => [ 'type' => 'structure', 'members' => [ 'securityPolicy' => [ 'shape' => 'SecurityPolicy', ], ], ], 'TlsContext' => [ 'type' => 'structure', 'members' => [ 'serverName' => [ 'shape' => 'ServerName', ], ], ], 'Token' => [ 'type' => 'string', 'max' => 6144, 'min' => 1, 'pattern' => '[\\s\\S]*', ], 'TokenKeyName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'TokenSignature' => [ 'type' => 'string', 'max' => 2560, 'min' => 1, 'pattern' => '[A-Za-z0-9+/]+={0,2}', ], 'Topic' => [ 'type' => 'string', ], 'TopicPattern' => [ 'type' => 'string', ], 'TopicRule' => [ 'type' => 'structure', 'members' => [ 'ruleName' => [ 'shape' => 'RuleName', ], 'sql' => [ 'shape' => 'SQL', ], 'description' => [ 'shape' => 'Description', ], 'createdAt' => [ 'shape' => 'CreatedAtDate', ], 'actions' => [ 'shape' => 'ActionList', ], 'ruleDisabled' => [ 'shape' => 'IsDisabled', ], 'awsIotSqlVersion' => [ 'shape' => 'AwsIotSqlVersion', ], 'errorAction' => [ 'shape' => 'Action', ], ], ], 'TopicRuleDestination' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AwsArn', ], 'status' => [ 'shape' => 'TopicRuleDestinationStatus', ], 'createdAt' => [ 'shape' => 'CreatedAtDate', ], 'lastUpdatedAt' => [ 'shape' => 'LastUpdatedAtDate', ], 'statusReason' => [ 'shape' => 'String', ], 'httpUrlProperties' => [ 'shape' => 'HttpUrlDestinationProperties', ], 'vpcProperties' => [ 'shape' => 'VpcDestinationProperties', ], ], ], 'TopicRuleDestinationConfiguration' => [ 'type' => 'structure', 'members' => [ 'httpUrlConfiguration' => [ 'shape' => 'HttpUrlDestinationConfiguration', ], 'vpcConfiguration' => [ 'shape' => 'VpcDestinationConfiguration', ], ], ], 'TopicRuleDestinationMaxResults' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'TopicRuleDestinationStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'IN_PROGRESS', 'DISABLED', 'ERROR', 'DELETING', ], ], 'TopicRuleDestinationSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'TopicRuleDestinationSummary', ], ], 'TopicRuleDestinationSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AwsArn', ], 'status' => [ 'shape' => 'TopicRuleDestinationStatus', ], 'createdAt' => [ 'shape' => 'CreatedAtDate', ], 'lastUpdatedAt' => [ 'shape' => 'LastUpdatedAtDate', ], 'statusReason' => [ 'shape' => 'String', ], 'httpUrlSummary' => [ 'shape' => 'HttpUrlDestinationSummary', ], 'vpcDestinationSummary' => [ 'shape' => 'VpcDestinationSummary', ], ], ], 'TopicRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TopicRuleListItem', ], ], 'TopicRuleListItem' => [ 'type' => 'structure', 'members' => [ 'ruleArn' => [ 'shape' => 'RuleArn', ], 'ruleName' => [ 'shape' => 'RuleName', ], 'topicPattern' => [ 'shape' => 'TopicPattern', ], 'createdAt' => [ 'shape' => 'CreatedAtDate', ], 'ruleDisabled' => [ 'shape' => 'IsDisabled', ], ], ], 'TopicRuleMaxResults' => [ 'type' => 'integer', 'max' => 10000, 'min' => 1, ], 'TopicRulePayload' => [ 'type' => 'structure', 'required' => [ 'sql', 'actions', ], 'members' => [ 'sql' => [ 'shape' => 'SQL', ], 'description' => [ 'shape' => 'Description', ], 'actions' => [ 'shape' => 'ActionList', ], 'ruleDisabled' => [ 'shape' => 'IsDisabled', ], 'awsIotSqlVersion' => [ 'shape' => 'AwsIotSqlVersion', ], 'errorAction' => [ 'shape' => 'Action', ], ], ], 'TotalChecksCount' => [ 'type' => 'integer', ], 'TotalFindingsCount' => [ 'type' => 'long', ], 'TotalResourcesCount' => [ 'type' => 'long', ], 'TransferAlreadyCompletedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 410, ], 'exception' => true, ], 'TransferCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'certificateId', 'targetAwsAccount', ], 'members' => [ 'certificateId' => [ 'shape' => 'CertificateId', 'location' => 'uri', 'locationName' => 'certificateId', ], 'targetAwsAccount' => [ 'shape' => 'AwsAccountId', 'location' => 'querystring', 'locationName' => 'targetAwsAccount', ], 'transferMessage' => [ 'shape' => 'Message', ], ], ], 'TransferCertificateResponse' => [ 'type' => 'structure', 'members' => [ 'transferredCertificateArn' => [ 'shape' => 'CertificateArn', ], ], ], 'TransferConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'TransferData' => [ 'type' => 'structure', 'members' => [ 'transferMessage' => [ 'shape' => 'Message', ], 'rejectReason' => [ 'shape' => 'Message', ], 'transferDate' => [ 'shape' => 'DateType', ], 'acceptDate' => [ 'shape' => 'DateType', ], 'rejectDate' => [ 'shape' => 'DateType', ], ], ], 'UnauthorizedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], 'UndoDeprecate' => [ 'type' => 'boolean', ], 'UnsetDefaultVersion' => [ 'type' => 'boolean', 'box' => true, ], 'UnsignedLong' => [ 'type' => 'long', 'min' => 0, ], 'UnsignedLongParameterValue' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '^[0-9]*$', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAccountAuditConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'roleArn' => [ 'shape' => 'RoleArn', ], 'auditNotificationTargetConfigurations' => [ 'shape' => 'AuditNotificationTargetConfigurations', ], 'auditCheckConfigurations' => [ 'shape' => 'AuditCheckConfigurations', ], ], ], 'UpdateAccountAuditConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAuditSuppressionRequest' => [ 'type' => 'structure', 'required' => [ 'checkName', 'resourceIdentifier', ], 'members' => [ 'checkName' => [ 'shape' => 'AuditCheckName', ], 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'expirationDate' => [ 'shape' => 'Timestamp', ], 'suppressIndefinitely' => [ 'shape' => 'SuppressIndefinitely', ], 'description' => [ 'shape' => 'AuditDescription', ], ], ], 'UpdateAuditSuppressionResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAuthorizerRequest' => [ 'type' => 'structure', 'required' => [ 'authorizerName', ], 'members' => [ 'authorizerName' => [ 'shape' => 'AuthorizerName', 'location' => 'uri', 'locationName' => 'authorizerName', ], 'authorizerFunctionArn' => [ 'shape' => 'AuthorizerFunctionArn', ], 'tokenKeyName' => [ 'shape' => 'TokenKeyName', ], 'tokenSigningPublicKeys' => [ 'shape' => 'PublicKeyMap', ], 'status' => [ 'shape' => 'AuthorizerStatus', ], 'enableCachingForHttp' => [ 'shape' => 'EnableCachingForHttp', ], ], ], 'UpdateAuthorizerResponse' => [ 'type' => 'structure', 'members' => [ 'authorizerName' => [ 'shape' => 'AuthorizerName', ], 'authorizerArn' => [ 'shape' => 'AuthorizerArn', ], ], ], 'UpdateBillingGroupRequest' => [ 'type' => 'structure', 'required' => [ 'billingGroupName', 'billingGroupProperties', ], 'members' => [ 'billingGroupName' => [ 'shape' => 'BillingGroupName', 'location' => 'uri', 'locationName' => 'billingGroupName', ], 'billingGroupProperties' => [ 'shape' => 'BillingGroupProperties', ], 'expectedVersion' => [ 'shape' => 'OptionalVersion', ], ], ], 'UpdateBillingGroupResponse' => [ 'type' => 'structure', 'members' => [ 'version' => [ 'shape' => 'Version', ], ], ], 'UpdateCACertificateParams' => [ 'type' => 'structure', 'required' => [ 'action', ], 'members' => [ 'action' => [ 'shape' => 'CACertificateUpdateAction', ], ], ], 'UpdateCACertificateRequest' => [ 'type' => 'structure', 'required' => [ 'certificateId', ], 'members' => [ 'certificateId' => [ 'shape' => 'CertificateId', 'location' => 'uri', 'locationName' => 'caCertificateId', ], 'newStatus' => [ 'shape' => 'CACertificateStatus', 'location' => 'querystring', 'locationName' => 'newStatus', ], 'newAutoRegistrationStatus' => [ 'shape' => 'AutoRegistrationStatus', 'location' => 'querystring', 'locationName' => 'newAutoRegistrationStatus', ], 'registrationConfig' => [ 'shape' => 'RegistrationConfig', ], 'removeAutoRegistration' => [ 'shape' => 'RemoveAutoRegistration', ], ], ], 'UpdateCertificateProviderRequest' => [ 'type' => 'structure', 'required' => [ 'certificateProviderName', ], 'members' => [ 'certificateProviderName' => [ 'shape' => 'CertificateProviderName', 'location' => 'uri', 'locationName' => 'certificateProviderName', ], 'lambdaFunctionArn' => [ 'shape' => 'CertificateProviderFunctionArn', ], 'accountDefaultForOperations' => [ 'shape' => 'CertificateProviderAccountDefaultForOperations', ], ], ], 'UpdateCertificateProviderResponse' => [ 'type' => 'structure', 'members' => [ 'certificateProviderName' => [ 'shape' => 'CertificateProviderName', ], 'certificateProviderArn' => [ 'shape' => 'CertificateProviderArn', ], ], ], 'UpdateCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'certificateId', 'newStatus', ], 'members' => [ 'certificateId' => [ 'shape' => 'CertificateId', 'location' => 'uri', 'locationName' => 'certificateId', ], 'newStatus' => [ 'shape' => 'CertificateStatus', 'location' => 'querystring', 'locationName' => 'newStatus', ], ], ], 'UpdateCommandRequest' => [ 'type' => 'structure', 'required' => [ 'commandId', ], 'members' => [ 'commandId' => [ 'shape' => 'CommandId', 'location' => 'uri', 'locationName' => 'commandId', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'description' => [ 'shape' => 'CommandDescription', ], 'deprecated' => [ 'shape' => 'DeprecationFlag', ], ], ], 'UpdateCommandResponse' => [ 'type' => 'structure', 'members' => [ 'commandId' => [ 'shape' => 'CommandId', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'description' => [ 'shape' => 'CommandDescription', ], 'deprecated' => [ 'shape' => 'DeprecationFlag', ], 'lastUpdatedAt' => [ 'shape' => 'DateType', ], ], ], 'UpdateCustomMetricRequest' => [ 'type' => 'structure', 'required' => [ 'metricName', 'displayName', ], 'members' => [ 'metricName' => [ 'shape' => 'MetricName', 'location' => 'uri', 'locationName' => 'metricName', ], 'displayName' => [ 'shape' => 'CustomMetricDisplayName', ], ], ], 'UpdateCustomMetricResponse' => [ 'type' => 'structure', 'members' => [ 'metricName' => [ 'shape' => 'MetricName', ], 'metricArn' => [ 'shape' => 'CustomMetricArn', ], 'metricType' => [ 'shape' => 'CustomMetricType', ], 'displayName' => [ 'shape' => 'CustomMetricDisplayName', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastModifiedDate' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateDeviceCertificateParams' => [ 'type' => 'structure', 'required' => [ 'action', ], 'members' => [ 'action' => [ 'shape' => 'DeviceCertificateUpdateAction', ], ], ], 'UpdateDimensionRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'stringValues', ], 'members' => [ 'name' => [ 'shape' => 'DimensionName', 'location' => 'uri', 'locationName' => 'name', ], 'stringValues' => [ 'shape' => 'DimensionStringValues', ], ], ], 'UpdateDimensionResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'DimensionName', ], 'arn' => [ 'shape' => 'DimensionArn', ], 'type' => [ 'shape' => 'DimensionType', ], 'stringValues' => [ 'shape' => 'DimensionStringValues', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastModifiedDate' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateDomainConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'domainConfigurationName', ], 'members' => [ 'domainConfigurationName' => [ 'shape' => 'ReservedDomainConfigurationName', 'location' => 'uri', 'locationName' => 'domainConfigurationName', ], 'authorizerConfig' => [ 'shape' => 'AuthorizerConfig', ], 'domainConfigurationStatus' => [ 'shape' => 'DomainConfigurationStatus', ], 'removeAuthorizerConfig' => [ 'shape' => 'RemoveAuthorizerConfig', ], 'tlsConfig' => [ 'shape' => 'TlsConfig', ], 'serverCertificateConfig' => [ 'shape' => 'ServerCertificateConfig', ], 'authenticationType' => [ 'shape' => 'AuthenticationType', ], 'applicationProtocol' => [ 'shape' => 'ApplicationProtocol', ], 'clientCertificateConfig' => [ 'shape' => 'ClientCertificateConfig', ], ], ], 'UpdateDomainConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'domainConfigurationName' => [ 'shape' => 'ReservedDomainConfigurationName', ], 'domainConfigurationArn' => [ 'shape' => 'DomainConfigurationArn', ], ], ], 'UpdateDynamicThingGroupRequest' => [ 'type' => 'structure', 'required' => [ 'thingGroupName', 'thingGroupProperties', ], 'members' => [ 'thingGroupName' => [ 'shape' => 'ThingGroupName', 'location' => 'uri', 'locationName' => 'thingGroupName', ], 'thingGroupProperties' => [ 'shape' => 'ThingGroupProperties', ], 'expectedVersion' => [ 'shape' => 'OptionalVersion', ], 'indexName' => [ 'shape' => 'IndexName', ], 'queryString' => [ 'shape' => 'QueryString', ], 'queryVersion' => [ 'shape' => 'QueryVersion', ], ], ], 'UpdateDynamicThingGroupResponse' => [ 'type' => 'structure', 'members' => [ 'version' => [ 'shape' => 'Version', ], ], ], 'UpdateEventConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'eventConfigurations' => [ 'shape' => 'EventConfigurations', ], ], ], 'UpdateEventConfigurationsResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateFleetMetricRequest' => [ 'type' => 'structure', 'required' => [ 'metricName', 'indexName', ], 'members' => [ 'metricName' => [ 'shape' => 'FleetMetricName', 'location' => 'uri', 'locationName' => 'metricName', ], 'queryString' => [ 'shape' => 'QueryString', ], 'aggregationType' => [ 'shape' => 'AggregationType', ], 'period' => [ 'shape' => 'FleetMetricPeriod', ], 'aggregationField' => [ 'shape' => 'AggregationField', ], 'description' => [ 'shape' => 'FleetMetricDescription', ], 'queryVersion' => [ 'shape' => 'QueryVersion', ], 'indexName' => [ 'shape' => 'IndexName', ], 'unit' => [ 'shape' => 'FleetMetricUnit', ], 'expectedVersion' => [ 'shape' => 'OptionalVersion', ], ], ], 'UpdateIndexingConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'thingIndexingConfiguration' => [ 'shape' => 'ThingIndexingConfiguration', ], 'thingGroupIndexingConfiguration' => [ 'shape' => 'ThingGroupIndexingConfiguration', ], ], ], 'UpdateIndexingConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'description' => [ 'shape' => 'JobDescription', ], 'presignedUrlConfig' => [ 'shape' => 'PresignedUrlConfig', ], 'jobExecutionsRolloutConfig' => [ 'shape' => 'JobExecutionsRolloutConfig', ], 'abortConfig' => [ 'shape' => 'AbortConfig', ], 'timeoutConfig' => [ 'shape' => 'TimeoutConfig', ], 'namespaceId' => [ 'shape' => 'NamespaceId', 'location' => 'querystring', 'locationName' => 'namespaceId', ], 'jobExecutionsRetryConfig' => [ 'shape' => 'JobExecutionsRetryConfig', ], ], ], 'UpdateMitigationActionRequest' => [ 'type' => 'structure', 'required' => [ 'actionName', ], 'members' => [ 'actionName' => [ 'shape' => 'MitigationActionName', 'location' => 'uri', 'locationName' => 'actionName', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'actionParams' => [ 'shape' => 'MitigationActionParams', ], ], ], 'UpdateMitigationActionResponse' => [ 'type' => 'structure', 'members' => [ 'actionArn' => [ 'shape' => 'MitigationActionArn', ], 'actionId' => [ 'shape' => 'MitigationActionId', ], ], ], 'UpdatePackageConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'versionUpdateByJobsConfig' => [ 'shape' => 'VersionUpdateByJobsConfig', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'UpdatePackageConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdatePackageRequest' => [ 'type' => 'structure', 'required' => [ 'packageName', ], 'members' => [ 'packageName' => [ 'shape' => 'PackageName', 'location' => 'uri', 'locationName' => 'packageName', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'defaultVersionName' => [ 'shape' => 'VersionName', ], 'unsetDefaultVersion' => [ 'shape' => 'UnsetDefaultVersion', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'UpdatePackageResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdatePackageVersionRequest' => [ 'type' => 'structure', 'required' => [ 'packageName', 'versionName', ], 'members' => [ 'packageName' => [ 'shape' => 'PackageName', 'location' => 'uri', 'locationName' => 'packageName', ], 'versionName' => [ 'shape' => 'VersionName', 'location' => 'uri', 'locationName' => 'versionName', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'attributes' => [ 'shape' => 'ResourceAttributes', ], 'artifact' => [ 'shape' => 'PackageVersionArtifact', ], 'action' => [ 'shape' => 'PackageVersionAction', ], 'recipe' => [ 'shape' => 'PackageVersionRecipe', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'UpdatePackageVersionResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateProvisioningTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'templateName', ], 'members' => [ 'templateName' => [ 'shape' => 'TemplateName', 'location' => 'uri', 'locationName' => 'templateName', ], 'description' => [ 'shape' => 'TemplateDescription', ], 'enabled' => [ 'shape' => 'Enabled', ], 'defaultVersionId' => [ 'shape' => 'TemplateVersionId', ], 'provisioningRoleArn' => [ 'shape' => 'RoleArn', ], 'preProvisioningHook' => [ 'shape' => 'ProvisioningHook', ], 'removePreProvisioningHook' => [ 'shape' => 'RemoveHook', ], ], ], 'UpdateProvisioningTemplateResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateRoleAliasRequest' => [ 'type' => 'structure', 'required' => [ 'roleAlias', ], 'members' => [ 'roleAlias' => [ 'shape' => 'RoleAlias', 'location' => 'uri', 'locationName' => 'roleAlias', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'credentialDurationSeconds' => [ 'shape' => 'CredentialDurationSeconds', ], ], ], 'UpdateRoleAliasResponse' => [ 'type' => 'structure', 'members' => [ 'roleAlias' => [ 'shape' => 'RoleAlias', ], 'roleAliasArn' => [ 'shape' => 'RoleAliasArn', ], ], ], 'UpdateScheduledAuditRequest' => [ 'type' => 'structure', 'required' => [ 'scheduledAuditName', ], 'members' => [ 'frequency' => [ 'shape' => 'AuditFrequency', ], 'dayOfMonth' => [ 'shape' => 'DayOfMonth', ], 'dayOfWeek' => [ 'shape' => 'DayOfWeek', ], 'targetCheckNames' => [ 'shape' => 'TargetAuditCheckNames', ], 'scheduledAuditName' => [ 'shape' => 'ScheduledAuditName', 'location' => 'uri', 'locationName' => 'scheduledAuditName', ], ], ], 'UpdateScheduledAuditResponse' => [ 'type' => 'structure', 'members' => [ 'scheduledAuditArn' => [ 'shape' => 'ScheduledAuditArn', ], ], ], 'UpdateSecurityProfileRequest' => [ 'type' => 'structure', 'required' => [ 'securityProfileName', ], 'members' => [ 'securityProfileName' => [ 'shape' => 'SecurityProfileName', 'location' => 'uri', 'locationName' => 'securityProfileName', ], 'securityProfileDescription' => [ 'shape' => 'SecurityProfileDescription', ], 'behaviors' => [ 'shape' => 'Behaviors', ], 'alertTargets' => [ 'shape' => 'AlertTargets', ], 'additionalMetricsToRetain' => [ 'shape' => 'AdditionalMetricsToRetainList', 'deprecated' => true, 'deprecatedMessage' => 'Use additionalMetricsToRetainV2.', ], 'additionalMetricsToRetainV2' => [ 'shape' => 'AdditionalMetricsToRetainV2List', ], 'deleteBehaviors' => [ 'shape' => 'DeleteBehaviors', ], 'deleteAlertTargets' => [ 'shape' => 'DeleteAlertTargets', ], 'deleteAdditionalMetricsToRetain' => [ 'shape' => 'DeleteAdditionalMetricsToRetain', ], 'expectedVersion' => [ 'shape' => 'OptionalVersion', 'location' => 'querystring', 'locationName' => 'expectedVersion', ], 'metricsExportConfig' => [ 'shape' => 'MetricsExportConfig', ], 'deleteMetricsExportConfig' => [ 'shape' => 'DeleteMetricsExportConfig', ], ], ], 'UpdateSecurityProfileResponse' => [ 'type' => 'structure', 'members' => [ 'securityProfileName' => [ 'shape' => 'SecurityProfileName', ], 'securityProfileArn' => [ 'shape' => 'SecurityProfileArn', ], 'securityProfileDescription' => [ 'shape' => 'SecurityProfileDescription', ], 'behaviors' => [ 'shape' => 'Behaviors', ], 'alertTargets' => [ 'shape' => 'AlertTargets', ], 'additionalMetricsToRetain' => [ 'shape' => 'AdditionalMetricsToRetainList', 'deprecated' => true, 'deprecatedMessage' => 'Use additionalMetricsToRetainV2.', ], 'additionalMetricsToRetainV2' => [ 'shape' => 'AdditionalMetricsToRetainV2List', ], 'version' => [ 'shape' => 'Version', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastModifiedDate' => [ 'shape' => 'Timestamp', ], 'metricsExportConfig' => [ 'shape' => 'MetricsExportConfig', ], ], ], 'UpdateStreamRequest' => [ 'type' => 'structure', 'required' => [ 'streamId', ], 'members' => [ 'streamId' => [ 'shape' => 'StreamId', 'location' => 'uri', 'locationName' => 'streamId', ], 'description' => [ 'shape' => 'StreamDescription', ], 'files' => [ 'shape' => 'StreamFiles', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'UpdateStreamResponse' => [ 'type' => 'structure', 'members' => [ 'streamId' => [ 'shape' => 'StreamId', ], 'streamArn' => [ 'shape' => 'StreamArn', ], 'description' => [ 'shape' => 'StreamDescription', ], 'streamVersion' => [ 'shape' => 'StreamVersion', ], ], ], 'UpdateThingGroupRequest' => [ 'type' => 'structure', 'required' => [ 'thingGroupName', 'thingGroupProperties', ], 'members' => [ 'thingGroupName' => [ 'shape' => 'ThingGroupName', 'location' => 'uri', 'locationName' => 'thingGroupName', ], 'thingGroupProperties' => [ 'shape' => 'ThingGroupProperties', ], 'expectedVersion' => [ 'shape' => 'OptionalVersion', ], ], ], 'UpdateThingGroupResponse' => [ 'type' => 'structure', 'members' => [ 'version' => [ 'shape' => 'Version', ], ], ], 'UpdateThingGroupsForThingRequest' => [ 'type' => 'structure', 'members' => [ 'thingName' => [ 'shape' => 'ThingName', ], 'thingGroupsToAdd' => [ 'shape' => 'ThingGroupList', ], 'thingGroupsToRemove' => [ 'shape' => 'ThingGroupList', ], 'overrideDynamicGroups' => [ 'shape' => 'OverrideDynamicGroups', ], ], ], 'UpdateThingGroupsForThingResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateThingRequest' => [ 'type' => 'structure', 'required' => [ 'thingName', ], 'members' => [ 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], 'thingTypeName' => [ 'shape' => 'ThingTypeName', ], 'attributePayload' => [ 'shape' => 'AttributePayload', ], 'expectedVersion' => [ 'shape' => 'OptionalVersion', ], 'removeThingType' => [ 'shape' => 'RemoveThingType', ], ], ], 'UpdateThingResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateThingTypeRequest' => [ 'type' => 'structure', 'required' => [ 'thingTypeName', ], 'members' => [ 'thingTypeName' => [ 'shape' => 'ThingTypeName', 'location' => 'uri', 'locationName' => 'thingTypeName', ], 'thingTypeProperties' => [ 'shape' => 'ThingTypeProperties', ], ], ], 'UpdateThingTypeResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateTopicRuleDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'arn', 'status', ], 'members' => [ 'arn' => [ 'shape' => 'AwsArn', ], 'status' => [ 'shape' => 'TopicRuleDestinationStatus', ], ], ], 'UpdateTopicRuleDestinationResponse' => [ 'type' => 'structure', 'members' => [], ], 'Url' => [ 'type' => 'string', 'max' => 2000, ], 'UseBase64' => [ 'type' => 'boolean', ], 'UserProperties' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserProperty', ], 'max' => 100, 'min' => 1, ], 'UserProperty' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'UserPropertyKey', ], 'value' => [ 'shape' => 'UserPropertyValue', ], ], ], 'UserPropertyKey' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'UserPropertyKeyName' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[a-zA-Z0-9:$.]+', ], 'UserPropertyValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'Valid' => [ 'type' => 'boolean', ], 'ValidateSecurityProfileBehaviorsRequest' => [ 'type' => 'structure', 'required' => [ 'behaviors', ], 'members' => [ 'behaviors' => [ 'shape' => 'Behaviors', ], ], ], 'ValidateSecurityProfileBehaviorsResponse' => [ 'type' => 'structure', 'members' => [ 'valid' => [ 'shape' => 'Valid', ], 'validationErrors' => [ 'shape' => 'ValidationErrors', ], ], ], 'ValidationError' => [ 'type' => 'structure', 'members' => [ 'errorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'ValidationErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationError', ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Value' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'Variance' => [ 'type' => 'double', ], 'VerificationState' => [ 'type' => 'string', 'enum' => [ 'FALSE_POSITIVE', 'BENIGN_POSITIVE', 'TRUE_POSITIVE', 'UNKNOWN', ], ], 'VerificationStateDescription' => [ 'type' => 'string', 'max' => 1000, 'pattern' => '[^\\p{Cntrl}]*', ], 'Version' => [ 'type' => 'long', ], 'VersionConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'VersionName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9-_.]+', ], 'VersionNumber' => [ 'type' => 'long', ], 'VersionUpdateByJobsConfig' => [ 'type' => 'structure', 'members' => [ 'enabled' => [ 'shape' => 'EnabledBoolean', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'VersionsLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ViolationEvent' => [ 'type' => 'structure', 'members' => [ 'violationId' => [ 'shape' => 'ViolationId', ], 'thingName' => [ 'shape' => 'DeviceDefenderThingName', ], 'securityProfileName' => [ 'shape' => 'SecurityProfileName', ], 'behavior' => [ 'shape' => 'Behavior', ], 'metricValue' => [ 'shape' => 'MetricValue', ], 'violationEventAdditionalInfo' => [ 'shape' => 'ViolationEventAdditionalInfo', ], 'violationEventType' => [ 'shape' => 'ViolationEventType', ], 'verificationState' => [ 'shape' => 'VerificationState', ], 'verificationStateDescription' => [ 'shape' => 'VerificationStateDescription', ], 'violationEventTime' => [ 'shape' => 'Timestamp', ], ], ], 'ViolationEventAdditionalInfo' => [ 'type' => 'structure', 'members' => [ 'confidenceLevel' => [ 'shape' => 'ConfidenceLevel', ], ], ], 'ViolationEventOccurrenceRange' => [ 'type' => 'structure', 'required' => [ 'startTime', 'endTime', ], 'members' => [ 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], ], ], 'ViolationEventType' => [ 'type' => 'string', 'enum' => [ 'in-alarm', 'alarm-cleared', 'alarm-invalidated', ], ], 'ViolationEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'ViolationEvent', ], ], 'ViolationId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\-]+', ], 'VpcDestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'subnetIds', 'vpcId', 'roleArn', ], 'members' => [ 'subnetIds' => [ 'shape' => 'SubnetIdList', ], 'securityGroups' => [ 'shape' => 'SecurityGroupList', ], 'vpcId' => [ 'shape' => 'VpcId', ], 'roleArn' => [ 'shape' => 'AwsArn', ], ], ], 'VpcDestinationProperties' => [ 'type' => 'structure', 'members' => [ 'subnetIds' => [ 'shape' => 'SubnetIdList', ], 'securityGroups' => [ 'shape' => 'SecurityGroupList', ], 'vpcId' => [ 'shape' => 'VpcId', ], 'roleArn' => [ 'shape' => 'AwsArn', ], ], ], 'VpcDestinationSummary' => [ 'type' => 'structure', 'members' => [ 'subnetIds' => [ 'shape' => 'SubnetIdList', ], 'securityGroups' => [ 'shape' => 'SecurityGroupList', ], 'vpcId' => [ 'shape' => 'VpcId', ], 'roleArn' => [ 'shape' => 'AwsArn', ], ], ], 'VpcId' => [ 'type' => 'string', ], 'WaitingForDataCollectionChecksCount' => [ 'type' => 'integer', ], 'errorMessage' => [ 'type' => 'string', ], 'resourceArn' => [ 'type' => 'string', ], 'resourceId' => [ 'type' => 'string', ], 'stringValue' => [ 'type' => 'string', ], 'usePrefixAttributeValue' => [ 'type' => 'boolean', ], ],];
