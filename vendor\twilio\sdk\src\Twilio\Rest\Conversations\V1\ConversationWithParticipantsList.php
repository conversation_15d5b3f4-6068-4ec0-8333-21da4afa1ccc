<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Conversations
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Conversations\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\ListResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\Serialize;


class ConversationWithParticipantsList extends ListResource
    {
    /**
     * Construct the ConversationWithParticipantsList
     *
     * @param Version $version Version that contains the resource
     */
    public function __construct(
        Version $version
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        ];

        $this->uri = '/ConversationWithParticipants';
    }

    /**
     * Create the ConversationWithParticipantsInstance
     *
     * @param array|Options $options Optional Arguments
     * @return ConversationWithParticipantsInstance Created ConversationWithParticipantsInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function create(array $options = []): ConversationWithParticipantsInstance
    {

        $options = new Values($options);

        $data = Values::of([
            'FriendlyName' =>
                $options['friendlyName'],
            'UniqueName' =>
                $options['uniqueName'],
            'DateCreated' =>
                Serialize::iso8601DateTime($options['dateCreated']),
            'DateUpdated' =>
                Serialize::iso8601DateTime($options['dateUpdated']),
            'MessagingServiceSid' =>
                $options['messagingServiceSid'],
            'Attributes' =>
                $options['attributes'],
            'State' =>
                $options['state'],
            'Timers.Inactive' =>
                $options['timersInactive'],
            'Timers.Closed' =>
                $options['timersClosed'],
            'Bindings.Email.Address' =>
                $options['bindingsEmailAddress'],
            'Bindings.Email.Name' =>
                $options['bindingsEmailName'],
            'Participant' =>
                Serialize::map($options['participant'], function ($e) { return $e; }),
        ]);

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json' , 'X-Twilio-Webhook-Enabled' => $options['xTwilioWebhookEnabled']]);
        $payload = $this->version->create('POST', $this->uri, [], $data, $headers);

        return new ConversationWithParticipantsInstance(
            $this->version,
            $payload
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Conversations.V1.ConversationWithParticipantsList]';
    }
}
