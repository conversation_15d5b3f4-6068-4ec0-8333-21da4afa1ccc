<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Messaging\V1;

use Twilio\Options;
use Twilio\Values;

abstract class DeactivationsOptions
{
    /**
     * @param \DateTime $date The request will return a list of all United States Phone Numbers that were deactivated on the day specified by this parameter. This date should be specified in YYYY-MM-DD format.
     * @return FetchDeactivationsOptions Options builder
     */
    public static function fetch(
        
        ?\DateTime $date = null

    ): FetchDeactivationsOptions
    {
        return new FetchDeactivationsOptions(
            $date
        );
    }

}

class FetchDeactivationsOptions extends Options
    {
    /**
     * @param \DateTime $date The request will return a list of all United States Phone Numbers that were deactivated on the day specified by this parameter. This date should be specified in YYYY-MM-DD format.
     */
    public function __construct(
        
        ?\DateTime $date = null

    ) {
        $this->options['date'] = $date;
    }

    /**
     * The request will return a list of all United States Phone Numbers that were deactivated on the day specified by this parameter. This date should be specified in YYYY-MM-DD format.
     *
     * @param \DateTime $date The request will return a list of all United States Phone Numbers that were deactivated on the day specified by this parameter. This date should be specified in YYYY-MM-DD format.
     * @return $this Fluent Builder
     */
    public function setDate(\DateTime $date): self
    {
        $this->options['date'] = $date;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Messaging.V1.FetchDeactivationsOptions ' . $options . ']';
    }
}

