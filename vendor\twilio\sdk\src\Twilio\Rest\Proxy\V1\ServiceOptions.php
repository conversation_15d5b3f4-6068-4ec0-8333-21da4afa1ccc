<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Proxy
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Proxy\V1;

use Twilio\Options;
use Twilio\Values;

abstract class ServiceOptions
{
    /**
     * @param int $defaultTtl The default `ttl` value to set for Sessions created in the Service. The TTL (time to live) is measured in seconds after the Session's last create or last Interaction. The default value of `0` indicates an unlimited Session length. You can override a Session's default TTL value by setting its `ttl` value.
     * @param string $callbackUrl The URL we should call when the interaction status changes.
     * @param string $geoMatchLevel
     * @param string $numberSelectionBehavior
     * @param string $interceptCallbackUrl The URL we call on each interaction. If we receive a 403 status, we block the interaction; otherwise the interaction continues.
     * @param string $outOfSessionCallbackUrl The URL we should call when an inbound call or SMS action occurs on a closed or non-existent Session. If your server (or a Twilio [function](https://www.twilio.com/en-us/serverless/functions)) responds with valid [TwiML](https://www.twilio.com/docs/voice/twiml), we will process it. This means it is possible, for example, to play a message for a call, send an automated text message response, or redirect a call to another Phone Number. See [Out-of-Session Callback Response Guide](https://www.twilio.com/docs/proxy/out-session-callback-response-guide) for more information.
     * @param string $chatInstanceSid The SID of the Chat Service Instance managed by Proxy Service. The Chat Service enables Proxy to forward SMS and channel messages to this chat instance. This is a one-to-one relationship.
     * @return CreateServiceOptions Options builder
     */
    public static function create(
        
        int $defaultTtl = Values::INT_NONE,
        string $callbackUrl = Values::NONE,
        string $geoMatchLevel = Values::NONE,
        string $numberSelectionBehavior = Values::NONE,
        string $interceptCallbackUrl = Values::NONE,
        string $outOfSessionCallbackUrl = Values::NONE,
        string $chatInstanceSid = Values::NONE

    ): CreateServiceOptions
    {
        return new CreateServiceOptions(
            $defaultTtl,
            $callbackUrl,
            $geoMatchLevel,
            $numberSelectionBehavior,
            $interceptCallbackUrl,
            $outOfSessionCallbackUrl,
            $chatInstanceSid
        );
    }




    /**
     * @param string $uniqueName An application-defined string that uniquely identifies the resource. This value must be 191 characters or fewer in length and be unique. **This value should not have PII.**
     * @param int $defaultTtl The default `ttl` value to set for Sessions created in the Service. The TTL (time to live) is measured in seconds after the Session's last create or last Interaction. The default value of `0` indicates an unlimited Session length. You can override a Session's default TTL value by setting its `ttl` value.
     * @param string $callbackUrl The URL we should call when the interaction status changes.
     * @param string $geoMatchLevel
     * @param string $numberSelectionBehavior
     * @param string $interceptCallbackUrl The URL we call on each interaction. If we receive a 403 status, we block the interaction; otherwise the interaction continues.
     * @param string $outOfSessionCallbackUrl The URL we should call when an inbound call or SMS action occurs on a closed or non-existent Session. If your server (or a Twilio [function](https://www.twilio.com/en-us/serverless/functions)) responds with valid [TwiML](https://www.twilio.com/docs/voice/twiml), we will process it. This means it is possible, for example, to play a message for a call, send an automated text message response, or redirect a call to another Phone Number. See [Out-of-Session Callback Response Guide](https://www.twilio.com/docs/proxy/out-session-callback-response-guide) for more information.
     * @param string $chatInstanceSid The SID of the Chat Service Instance managed by Proxy Service. The Chat Service enables Proxy to forward SMS and channel messages to this chat instance. This is a one-to-one relationship.
     * @return UpdateServiceOptions Options builder
     */
    public static function update(
        
        string $uniqueName = Values::NONE,
        int $defaultTtl = Values::INT_NONE,
        string $callbackUrl = Values::NONE,
        string $geoMatchLevel = Values::NONE,
        string $numberSelectionBehavior = Values::NONE,
        string $interceptCallbackUrl = Values::NONE,
        string $outOfSessionCallbackUrl = Values::NONE,
        string $chatInstanceSid = Values::NONE

    ): UpdateServiceOptions
    {
        return new UpdateServiceOptions(
            $uniqueName,
            $defaultTtl,
            $callbackUrl,
            $geoMatchLevel,
            $numberSelectionBehavior,
            $interceptCallbackUrl,
            $outOfSessionCallbackUrl,
            $chatInstanceSid
        );
    }

}

class CreateServiceOptions extends Options
    {
    /**
     * @param int $defaultTtl The default `ttl` value to set for Sessions created in the Service. The TTL (time to live) is measured in seconds after the Session's last create or last Interaction. The default value of `0` indicates an unlimited Session length. You can override a Session's default TTL value by setting its `ttl` value.
     * @param string $callbackUrl The URL we should call when the interaction status changes.
     * @param string $geoMatchLevel
     * @param string $numberSelectionBehavior
     * @param string $interceptCallbackUrl The URL we call on each interaction. If we receive a 403 status, we block the interaction; otherwise the interaction continues.
     * @param string $outOfSessionCallbackUrl The URL we should call when an inbound call or SMS action occurs on a closed or non-existent Session. If your server (or a Twilio [function](https://www.twilio.com/en-us/serverless/functions)) responds with valid [TwiML](https://www.twilio.com/docs/voice/twiml), we will process it. This means it is possible, for example, to play a message for a call, send an automated text message response, or redirect a call to another Phone Number. See [Out-of-Session Callback Response Guide](https://www.twilio.com/docs/proxy/out-session-callback-response-guide) for more information.
     * @param string $chatInstanceSid The SID of the Chat Service Instance managed by Proxy Service. The Chat Service enables Proxy to forward SMS and channel messages to this chat instance. This is a one-to-one relationship.
     */
    public function __construct(
        
        int $defaultTtl = Values::INT_NONE,
        string $callbackUrl = Values::NONE,
        string $geoMatchLevel = Values::NONE,
        string $numberSelectionBehavior = Values::NONE,
        string $interceptCallbackUrl = Values::NONE,
        string $outOfSessionCallbackUrl = Values::NONE,
        string $chatInstanceSid = Values::NONE

    ) {
        $this->options['defaultTtl'] = $defaultTtl;
        $this->options['callbackUrl'] = $callbackUrl;
        $this->options['geoMatchLevel'] = $geoMatchLevel;
        $this->options['numberSelectionBehavior'] = $numberSelectionBehavior;
        $this->options['interceptCallbackUrl'] = $interceptCallbackUrl;
        $this->options['outOfSessionCallbackUrl'] = $outOfSessionCallbackUrl;
        $this->options['chatInstanceSid'] = $chatInstanceSid;
    }

    /**
     * The default `ttl` value to set for Sessions created in the Service. The TTL (time to live) is measured in seconds after the Session's last create or last Interaction. The default value of `0` indicates an unlimited Session length. You can override a Session's default TTL value by setting its `ttl` value.
     *
     * @param int $defaultTtl The default `ttl` value to set for Sessions created in the Service. The TTL (time to live) is measured in seconds after the Session's last create or last Interaction. The default value of `0` indicates an unlimited Session length. You can override a Session's default TTL value by setting its `ttl` value.
     * @return $this Fluent Builder
     */
    public function setDefaultTtl(int $defaultTtl): self
    {
        $this->options['defaultTtl'] = $defaultTtl;
        return $this;
    }

    /**
     * The URL we should call when the interaction status changes.
     *
     * @param string $callbackUrl The URL we should call when the interaction status changes.
     * @return $this Fluent Builder
     */
    public function setCallbackUrl(string $callbackUrl): self
    {
        $this->options['callbackUrl'] = $callbackUrl;
        return $this;
    }

    /**
     * @param string $geoMatchLevel
     * @return $this Fluent Builder
     */
    public function setGeoMatchLevel(string $geoMatchLevel): self
    {
        $this->options['geoMatchLevel'] = $geoMatchLevel;
        return $this;
    }

    /**
     * @param string $numberSelectionBehavior
     * @return $this Fluent Builder
     */
    public function setNumberSelectionBehavior(string $numberSelectionBehavior): self
    {
        $this->options['numberSelectionBehavior'] = $numberSelectionBehavior;
        return $this;
    }

    /**
     * The URL we call on each interaction. If we receive a 403 status, we block the interaction; otherwise the interaction continues.
     *
     * @param string $interceptCallbackUrl The URL we call on each interaction. If we receive a 403 status, we block the interaction; otherwise the interaction continues.
     * @return $this Fluent Builder
     */
    public function setInterceptCallbackUrl(string $interceptCallbackUrl): self
    {
        $this->options['interceptCallbackUrl'] = $interceptCallbackUrl;
        return $this;
    }

    /**
     * The URL we should call when an inbound call or SMS action occurs on a closed or non-existent Session. If your server (or a Twilio [function](https://www.twilio.com/en-us/serverless/functions)) responds with valid [TwiML](https://www.twilio.com/docs/voice/twiml), we will process it. This means it is possible, for example, to play a message for a call, send an automated text message response, or redirect a call to another Phone Number. See [Out-of-Session Callback Response Guide](https://www.twilio.com/docs/proxy/out-session-callback-response-guide) for more information.
     *
     * @param string $outOfSessionCallbackUrl The URL we should call when an inbound call or SMS action occurs on a closed or non-existent Session. If your server (or a Twilio [function](https://www.twilio.com/en-us/serverless/functions)) responds with valid [TwiML](https://www.twilio.com/docs/voice/twiml), we will process it. This means it is possible, for example, to play a message for a call, send an automated text message response, or redirect a call to another Phone Number. See [Out-of-Session Callback Response Guide](https://www.twilio.com/docs/proxy/out-session-callback-response-guide) for more information.
     * @return $this Fluent Builder
     */
    public function setOutOfSessionCallbackUrl(string $outOfSessionCallbackUrl): self
    {
        $this->options['outOfSessionCallbackUrl'] = $outOfSessionCallbackUrl;
        return $this;
    }

    /**
     * The SID of the Chat Service Instance managed by Proxy Service. The Chat Service enables Proxy to forward SMS and channel messages to this chat instance. This is a one-to-one relationship.
     *
     * @param string $chatInstanceSid The SID of the Chat Service Instance managed by Proxy Service. The Chat Service enables Proxy to forward SMS and channel messages to this chat instance. This is a one-to-one relationship.
     * @return $this Fluent Builder
     */
    public function setChatInstanceSid(string $chatInstanceSid): self
    {
        $this->options['chatInstanceSid'] = $chatInstanceSid;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Proxy.V1.CreateServiceOptions ' . $options . ']';
    }
}




class UpdateServiceOptions extends Options
    {
    /**
     * @param string $uniqueName An application-defined string that uniquely identifies the resource. This value must be 191 characters or fewer in length and be unique. **This value should not have PII.**
     * @param int $defaultTtl The default `ttl` value to set for Sessions created in the Service. The TTL (time to live) is measured in seconds after the Session's last create or last Interaction. The default value of `0` indicates an unlimited Session length. You can override a Session's default TTL value by setting its `ttl` value.
     * @param string $callbackUrl The URL we should call when the interaction status changes.
     * @param string $geoMatchLevel
     * @param string $numberSelectionBehavior
     * @param string $interceptCallbackUrl The URL we call on each interaction. If we receive a 403 status, we block the interaction; otherwise the interaction continues.
     * @param string $outOfSessionCallbackUrl The URL we should call when an inbound call or SMS action occurs on a closed or non-existent Session. If your server (or a Twilio [function](https://www.twilio.com/en-us/serverless/functions)) responds with valid [TwiML](https://www.twilio.com/docs/voice/twiml), we will process it. This means it is possible, for example, to play a message for a call, send an automated text message response, or redirect a call to another Phone Number. See [Out-of-Session Callback Response Guide](https://www.twilio.com/docs/proxy/out-session-callback-response-guide) for more information.
     * @param string $chatInstanceSid The SID of the Chat Service Instance managed by Proxy Service. The Chat Service enables Proxy to forward SMS and channel messages to this chat instance. This is a one-to-one relationship.
     */
    public function __construct(
        
        string $uniqueName = Values::NONE,
        int $defaultTtl = Values::INT_NONE,
        string $callbackUrl = Values::NONE,
        string $geoMatchLevel = Values::NONE,
        string $numberSelectionBehavior = Values::NONE,
        string $interceptCallbackUrl = Values::NONE,
        string $outOfSessionCallbackUrl = Values::NONE,
        string $chatInstanceSid = Values::NONE

    ) {
        $this->options['uniqueName'] = $uniqueName;
        $this->options['defaultTtl'] = $defaultTtl;
        $this->options['callbackUrl'] = $callbackUrl;
        $this->options['geoMatchLevel'] = $geoMatchLevel;
        $this->options['numberSelectionBehavior'] = $numberSelectionBehavior;
        $this->options['interceptCallbackUrl'] = $interceptCallbackUrl;
        $this->options['outOfSessionCallbackUrl'] = $outOfSessionCallbackUrl;
        $this->options['chatInstanceSid'] = $chatInstanceSid;
    }

    /**
     * An application-defined string that uniquely identifies the resource. This value must be 191 characters or fewer in length and be unique. **This value should not have PII.**
     *
     * @param string $uniqueName An application-defined string that uniquely identifies the resource. This value must be 191 characters or fewer in length and be unique. **This value should not have PII.**
     * @return $this Fluent Builder
     */
    public function setUniqueName(string $uniqueName): self
    {
        $this->options['uniqueName'] = $uniqueName;
        return $this;
    }

    /**
     * The default `ttl` value to set for Sessions created in the Service. The TTL (time to live) is measured in seconds after the Session's last create or last Interaction. The default value of `0` indicates an unlimited Session length. You can override a Session's default TTL value by setting its `ttl` value.
     *
     * @param int $defaultTtl The default `ttl` value to set for Sessions created in the Service. The TTL (time to live) is measured in seconds after the Session's last create or last Interaction. The default value of `0` indicates an unlimited Session length. You can override a Session's default TTL value by setting its `ttl` value.
     * @return $this Fluent Builder
     */
    public function setDefaultTtl(int $defaultTtl): self
    {
        $this->options['defaultTtl'] = $defaultTtl;
        return $this;
    }

    /**
     * The URL we should call when the interaction status changes.
     *
     * @param string $callbackUrl The URL we should call when the interaction status changes.
     * @return $this Fluent Builder
     */
    public function setCallbackUrl(string $callbackUrl): self
    {
        $this->options['callbackUrl'] = $callbackUrl;
        return $this;
    }

    /**
     * @param string $geoMatchLevel
     * @return $this Fluent Builder
     */
    public function setGeoMatchLevel(string $geoMatchLevel): self
    {
        $this->options['geoMatchLevel'] = $geoMatchLevel;
        return $this;
    }

    /**
     * @param string $numberSelectionBehavior
     * @return $this Fluent Builder
     */
    public function setNumberSelectionBehavior(string $numberSelectionBehavior): self
    {
        $this->options['numberSelectionBehavior'] = $numberSelectionBehavior;
        return $this;
    }

    /**
     * The URL we call on each interaction. If we receive a 403 status, we block the interaction; otherwise the interaction continues.
     *
     * @param string $interceptCallbackUrl The URL we call on each interaction. If we receive a 403 status, we block the interaction; otherwise the interaction continues.
     * @return $this Fluent Builder
     */
    public function setInterceptCallbackUrl(string $interceptCallbackUrl): self
    {
        $this->options['interceptCallbackUrl'] = $interceptCallbackUrl;
        return $this;
    }

    /**
     * The URL we should call when an inbound call or SMS action occurs on a closed or non-existent Session. If your server (or a Twilio [function](https://www.twilio.com/en-us/serverless/functions)) responds with valid [TwiML](https://www.twilio.com/docs/voice/twiml), we will process it. This means it is possible, for example, to play a message for a call, send an automated text message response, or redirect a call to another Phone Number. See [Out-of-Session Callback Response Guide](https://www.twilio.com/docs/proxy/out-session-callback-response-guide) for more information.
     *
     * @param string $outOfSessionCallbackUrl The URL we should call when an inbound call or SMS action occurs on a closed or non-existent Session. If your server (or a Twilio [function](https://www.twilio.com/en-us/serverless/functions)) responds with valid [TwiML](https://www.twilio.com/docs/voice/twiml), we will process it. This means it is possible, for example, to play a message for a call, send an automated text message response, or redirect a call to another Phone Number. See [Out-of-Session Callback Response Guide](https://www.twilio.com/docs/proxy/out-session-callback-response-guide) for more information.
     * @return $this Fluent Builder
     */
    public function setOutOfSessionCallbackUrl(string $outOfSessionCallbackUrl): self
    {
        $this->options['outOfSessionCallbackUrl'] = $outOfSessionCallbackUrl;
        return $this;
    }

    /**
     * The SID of the Chat Service Instance managed by Proxy Service. The Chat Service enables Proxy to forward SMS and channel messages to this chat instance. This is a one-to-one relationship.
     *
     * @param string $chatInstanceSid The SID of the Chat Service Instance managed by Proxy Service. The Chat Service enables Proxy to forward SMS and channel messages to this chat instance. This is a one-to-one relationship.
     * @return $this Fluent Builder
     */
    public function setChatInstanceSid(string $chatInstanceSid): self
    {
        $this->options['chatInstanceSid'] = $chatInstanceSid;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Proxy.V1.UpdateServiceOptions ' . $options . ']';
    }
}

