name: PHP Pipeline

on: [push, pull_request]

jobs:
  build:

    runs-on: ubuntu-latest

    strategy:
      max-parallel: 2
      matrix:
        php-versions: ['8.2', '8.3', '8.4']

    name: PHP ${{ matrix.php-versions }}

    steps:
    - uses: actions/checkout@v4

    - name: Setup PHP
      uses: shivammathur/setup-php@master
      with:
        php-version: ${{ matrix.php-versions }}
        coverage: xdebug

    - name: Validate composer.json and composer.lock
      run: composer validate

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress --no-suggest

    - name: Run test suite
      run: composer run-script test
  phplint:
      permissions:
          contents: write
      runs-on: ubuntu-latest
      steps:
          - uses: actions/checkout@v3
          - name: "laravel-pint"
            uses: aglipanci/laravel-pint-action@latest
            with:
                configPath: './pint.json'

          - name: Commit changes
            uses: stefanzweifel/git-auto-commit-action@v4
            with:
                commit_message: PHP Linting (Pint)
                skip_fetch: true
