net\authorize\api\contract\v1\ARBGetSubscriptionStatusResponse:
    xml_root_name: ARBGetSubscriptionStatusResponse
    xml_root_namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
    properties:
        status:
            expose: true
            access_type: public_method
            serialized_name: status
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getStatus
                setter: setStatus
            type: string
