<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Services\SmartCacheService;
use Symfony\Component\HttpFoundation\Response;

class CacheCleanup
{
    protected $cache;

    public function __construct(SmartCacheService $cache)
    {
        $this->cache = $cache;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // تنظيف Cache بعد العمليات المهمة
        if ($this->shouldCleanupCache($request)) {
            $this->performCacheCleanup($request);
        }

        return $response;
    }

    /**
     * تحديد ما إذا كان يجب تنظيف Cache
     */
    protected function shouldCleanupCache(Request $request): bool
    {
        // تنظيف Cache بعد العمليات التي تؤثر على البيانات
        $cleanupRoutes = [
            'invoice.store',
            'invoice.update',
            'invoice.destroy',
            'customer.store',
            'customer.update',
            'customer.destroy',
            'payment.store',
            'payment.update',
        ];

        $currentRoute = $request->route() ? $request->route()->getName() : '';

        return in_array($currentRoute, $cleanupRoutes);
    }

    /**
     * تنفيذ تنظيف Cache
     */
    protected function performCacheCleanup(Request $request)
    {
        $route = $request->route() ? $request->route()->getName() : '';

        // تنظيف Cache حسب نوع العملية
        if (str_contains($route, 'invoice')) {
            $this->cache->forgetByTags(['invoices', 'stats']);
        }

        if (str_contains($route, 'customer')) {
            $this->cache->forgetByTags(['customers', 'stats']);
        }

        if (str_contains($route, 'payment')) {
            $this->cache->forgetByTags(['payments', 'stats']);
        }

        // تنظيف عام للبحث
        $this->cache->forgetByTags(['search']);
    }
}
