<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Marketplace
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Marketplace\V1;

use Twilio\Values;
abstract class ReferralConversionModels
{
    /**
     * @property string $referralAccountSid
    */
    public static function createCreateReferralConversionRequest(array $payload = []): CreateReferralConversionRequest
    {
        return new CreateReferralConversionRequest($payload);
    }

}

class CreateReferralConversionRequest implements \JsonSerializable
{
    /**
     * @property string $referralAccountSid
    */
        protected $referralAccountSid;
    public function __construct(array $payload = []) {
        $this->referralAccountSid = Values::array_get($payload, 'referral_account_sid');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'referral_account_sid' => $this->referralAccountSid
        ];
    }
}

