<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Assistants
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Assistants\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;
use Twilio\Rest\Assistants\V1\Assistant\AssistantsKnowledgeList;
use Twilio\Rest\Assistants\V1\Assistant\AssistantsToolList;
use Twilio\Rest\Assistants\V1\Assistant\FeedbackList;
use Twilio\Rest\Assistants\V1\Assistant\MessageList;


/**
 * @property string $accountSid
 * @property array $customerAi
 * @property string $id
 * @property string $model
 * @property string $name
 * @property string $owner
 * @property string $url
 * @property string $personalityPrompt
 * @property \DateTime $dateCreated
 * @property \DateTime $dateUpdated
 * @property string[] $knowledge
 * @property string[] $tools
 */
class AssistantInstance extends InstanceResource
{
    protected $_assistantsKnowledge;
    protected $_assistantsTools;
    protected $_feedbacks;
    protected $_messages;

    /**
     * Initialize the AssistantInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $id
     */
    public function __construct(Version $version, array $payload, ?string $id = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'accountSid' => Values::array_get($payload, 'account_sid'),
            'customerAi' => Values::array_get($payload, 'customer_ai'),
            'id' => Values::array_get($payload, 'id'),
            'model' => Values::array_get($payload, 'model'),
            'name' => Values::array_get($payload, 'name'),
            'owner' => Values::array_get($payload, 'owner'),
            'url' => Values::array_get($payload, 'url'),
            'personalityPrompt' => Values::array_get($payload, 'personality_prompt'),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'dateUpdated' => Deserialize::dateTime(Values::array_get($payload, 'date_updated')),
            'knowledge' => Values::array_get($payload, 'knowledge'),
            'tools' => Values::array_get($payload, 'tools'),
        ];

        $this->solution = ['id' => $id ?: $this->properties['id'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return AssistantContext Context for this AssistantInstance
     */
    protected function proxy(): AssistantContext
    {
        if (!$this->context) {
            $this->context = new AssistantContext(
                $this->version,
                $this->solution['id']
            );
        }

        return $this->context;
    }

    /**
     * Delete the AssistantInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->proxy()->delete();
    }

    /**
     * Fetch the AssistantInstance
     *
     * @return AssistantInstance Fetched AssistantInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): AssistantInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Update the AssistantInstance
     *
     * @return AssistantInstance Updated AssistantInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(): AssistantInstance
    {

        return $this->proxy()->update();
    }

    /**
     * Access the assistantsKnowledge
     */
    protected function getAssistantsKnowledge(): AssistantsKnowledgeList
    {
        return $this->proxy()->assistantsKnowledge;
    }

    /**
     * Access the assistantsTools
     */
    protected function getAssistantsTools(): AssistantsToolList
    {
        return $this->proxy()->assistantsTools;
    }

    /**
     * Access the feedbacks
     */
    protected function getFeedbacks(): FeedbackList
    {
        return $this->proxy()->feedbacks;
    }

    /**
     * Access the messages
     */
    protected function getMessages(): MessageList
    {
        return $this->proxy()->messages;
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Assistants.V1.AssistantInstance ' . \implode(' ', $context) . ']';
    }
}

