<?php
// This file was auto-generated from sdk-root/src/data/s3tables/2018-05-10/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-10', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 's3tables', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Amazon S3 Tables', 'serviceId' => 'S3Tables', 'signatureVersion' => 'v4', 'signingName' => 's3tables', 'uid' => 's3tables-2018-05-10', ], 'operations' => [ 'CreateNamespace' => [ 'name' => 'CreateNamespace', 'http' => [ 'method' => 'PUT', 'requestUri' => '/namespaces/{tableBucketARN}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateNamespaceRequest', ], 'output' => [ 'shape' => 'CreateNamespaceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], ], 'CreateTable' => [ 'name' => 'CreateTable', 'http' => [ 'method' => 'PUT', 'requestUri' => '/tables/{tableBucketARN}/{namespace}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateTableRequest', ], 'output' => [ 'shape' => 'CreateTableResponse', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], ], 'CreateTableBucket' => [ 'name' => 'CreateTableBucket', 'http' => [ 'method' => 'PUT', 'requestUri' => '/buckets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateTableBucketRequest', ], 'output' => [ 'shape' => 'CreateTableBucketResponse', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], ], 'DeleteNamespace' => [ 'name' => 'DeleteNamespace', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/namespaces/{tableBucketARN}/{namespace}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteNamespaceRequest', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], 'idempotent' => true, ], 'DeleteTable' => [ 'name' => 'DeleteTable', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tables/{tableBucketARN}/{namespace}/{name}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteTableRequest', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], 'idempotent' => true, ], 'DeleteTableBucket' => [ 'name' => 'DeleteTableBucket', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/buckets/{tableBucketARN}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteTableBucketRequest', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], 'idempotent' => true, ], 'DeleteTableBucketEncryption' => [ 'name' => 'DeleteTableBucketEncryption', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/buckets/{tableBucketARN}/encryption', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteTableBucketEncryptionRequest', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], 'idempotent' => true, ], 'DeleteTableBucketPolicy' => [ 'name' => 'DeleteTableBucketPolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/buckets/{tableBucketARN}/policy', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteTableBucketPolicyRequest', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], 'idempotent' => true, ], 'DeleteTablePolicy' => [ 'name' => 'DeleteTablePolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tables/{tableBucketARN}/{namespace}/{name}/policy', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteTablePolicyRequest', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], 'idempotent' => true, ], 'GetNamespace' => [ 'name' => 'GetNamespace', 'http' => [ 'method' => 'GET', 'requestUri' => '/namespaces/{tableBucketARN}/{namespace}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetNamespaceRequest', ], 'output' => [ 'shape' => 'GetNamespaceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetTable' => [ 'name' => 'GetTable', 'http' => [ 'method' => 'GET', 'requestUri' => '/get-table', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTableRequest', ], 'output' => [ 'shape' => 'GetTableResponse', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetTableBucket' => [ 'name' => 'GetTableBucket', 'http' => [ 'method' => 'GET', 'requestUri' => '/buckets/{tableBucketARN}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTableBucketRequest', ], 'output' => [ 'shape' => 'GetTableBucketResponse', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetTableBucketEncryption' => [ 'name' => 'GetTableBucketEncryption', 'http' => [ 'method' => 'GET', 'requestUri' => '/buckets/{tableBucketARN}/encryption', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTableBucketEncryptionRequest', ], 'output' => [ 'shape' => 'GetTableBucketEncryptionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetTableBucketMaintenanceConfiguration' => [ 'name' => 'GetTableBucketMaintenanceConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/buckets/{tableBucketARN}/maintenance', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTableBucketMaintenanceConfigurationRequest', ], 'output' => [ 'shape' => 'GetTableBucketMaintenanceConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetTableBucketPolicy' => [ 'name' => 'GetTableBucketPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/buckets/{tableBucketARN}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTableBucketPolicyRequest', ], 'output' => [ 'shape' => 'GetTableBucketPolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetTableEncryption' => [ 'name' => 'GetTableEncryption', 'http' => [ 'method' => 'GET', 'requestUri' => '/tables/{tableBucketARN}/{namespace}/{name}/encryption', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTableEncryptionRequest', ], 'output' => [ 'shape' => 'GetTableEncryptionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetTableMaintenanceConfiguration' => [ 'name' => 'GetTableMaintenanceConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/tables/{tableBucketARN}/{namespace}/{name}/maintenance', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTableMaintenanceConfigurationRequest', ], 'output' => [ 'shape' => 'GetTableMaintenanceConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetTableMaintenanceJobStatus' => [ 'name' => 'GetTableMaintenanceJobStatus', 'http' => [ 'method' => 'GET', 'requestUri' => '/tables/{tableBucketARN}/{namespace}/{name}/maintenance-job-status', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTableMaintenanceJobStatusRequest', ], 'output' => [ 'shape' => 'GetTableMaintenanceJobStatusResponse', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetTableMetadataLocation' => [ 'name' => 'GetTableMetadataLocation', 'http' => [ 'method' => 'GET', 'requestUri' => '/tables/{tableBucketARN}/{namespace}/{name}/metadata-location', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTableMetadataLocationRequest', ], 'output' => [ 'shape' => 'GetTableMetadataLocationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetTablePolicy' => [ 'name' => 'GetTablePolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/tables/{tableBucketARN}/{namespace}/{name}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTablePolicyRequest', ], 'output' => [ 'shape' => 'GetTablePolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], ], 'ListNamespaces' => [ 'name' => 'ListNamespaces', 'http' => [ 'method' => 'GET', 'requestUri' => '/namespaces/{tableBucketARN}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListNamespacesRequest', ], 'output' => [ 'shape' => 'ListNamespacesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], ], 'ListTableBuckets' => [ 'name' => 'ListTableBuckets', 'http' => [ 'method' => 'GET', 'requestUri' => '/buckets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTableBucketsRequest', ], 'output' => [ 'shape' => 'ListTableBucketsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], ], 'ListTables' => [ 'name' => 'ListTables', 'http' => [ 'method' => 'GET', 'requestUri' => '/tables/{tableBucketARN}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTablesRequest', ], 'output' => [ 'shape' => 'ListTablesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], ], 'PutTableBucketEncryption' => [ 'name' => 'PutTableBucketEncryption', 'http' => [ 'method' => 'PUT', 'requestUri' => '/buckets/{tableBucketARN}/encryption', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutTableBucketEncryptionRequest', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], 'idempotent' => true, ], 'PutTableBucketMaintenanceConfiguration' => [ 'name' => 'PutTableBucketMaintenanceConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/buckets/{tableBucketARN}/maintenance/{type}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'PutTableBucketMaintenanceConfigurationRequest', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], ], 'PutTableBucketPolicy' => [ 'name' => 'PutTableBucketPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/buckets/{tableBucketARN}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutTableBucketPolicyRequest', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], 'idempotent' => true, ], 'PutTableMaintenanceConfiguration' => [ 'name' => 'PutTableMaintenanceConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/tables/{tableBucketARN}/{namespace}/{name}/maintenance/{type}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'PutTableMaintenanceConfigurationRequest', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], ], 'PutTablePolicy' => [ 'name' => 'PutTablePolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/tables/{tableBucketARN}/{namespace}/{name}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutTablePolicyRequest', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], 'idempotent' => true, ], 'RenameTable' => [ 'name' => 'RenameTable', 'http' => [ 'method' => 'PUT', 'requestUri' => '/tables/{tableBucketARN}/{namespace}/{name}/rename', 'responseCode' => 204, ], 'input' => [ 'shape' => 'RenameTableRequest', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], ], 'UpdateTableMetadataLocation' => [ 'name' => 'UpdateTableMetadataLocation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/tables/{tableBucketARN}/{namespace}/{name}/metadata-location', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateTableMetadataLocationRequest', ], 'output' => [ 'shape' => 'UpdateTableMetadataLocationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BadRequestException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '[0-9].*', ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateNamespaceRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', 'namespace', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], 'namespace' => [ 'shape' => 'CreateNamespaceRequestNamespaceList', ], ], ], 'CreateNamespaceRequestNamespaceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NamespaceName', ], 'max' => 1, 'min' => 1, ], 'CreateNamespaceResponse' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', 'namespace', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', ], 'namespace' => [ 'shape' => 'NamespaceList', ], ], ], 'CreateTableBucketRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'TableBucketName', ], 'encryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], ], ], 'CreateTableBucketResponse' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'TableBucketARN', ], ], ], 'CreateTableRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', 'namespace', 'name', 'format', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], 'namespace' => [ 'shape' => 'NamespaceName', 'location' => 'uri', 'locationName' => 'namespace', ], 'name' => [ 'shape' => 'TableName', ], 'format' => [ 'shape' => 'OpenTableFormat', ], 'metadata' => [ 'shape' => 'TableMetadata', ], 'encryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], ], ], 'CreateTableResponse' => [ 'type' => 'structure', 'required' => [ 'tableARN', 'versionToken', ], 'members' => [ 'tableARN' => [ 'shape' => 'TableARN', ], 'versionToken' => [ 'shape' => 'VersionToken', ], ], ], 'DeleteNamespaceRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', 'namespace', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], 'namespace' => [ 'shape' => 'NamespaceName', 'location' => 'uri', 'locationName' => 'namespace', ], ], ], 'DeleteTableBucketEncryptionRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], ], ], 'DeleteTableBucketPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], ], ], 'DeleteTableBucketRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], ], ], 'DeleteTablePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', 'namespace', 'name', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], 'namespace' => [ 'shape' => 'NamespaceName', 'location' => 'uri', 'locationName' => 'namespace', ], 'name' => [ 'shape' => 'TableName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteTableRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', 'namespace', 'name', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], 'namespace' => [ 'shape' => 'NamespaceName', 'location' => 'uri', 'locationName' => 'namespace', ], 'name' => [ 'shape' => 'TableName', 'location' => 'uri', 'locationName' => 'name', ], 'versionToken' => [ 'shape' => 'VersionToken', 'location' => 'querystring', 'locationName' => 'versionToken', ], ], ], 'EncryptionConfiguration' => [ 'type' => 'structure', 'required' => [ 'sseAlgorithm', ], 'members' => [ 'sseAlgorithm' => [ 'shape' => 'SSEAlgorithm', ], 'kmsKeyArn' => [ 'shape' => 'EncryptionConfigurationKmsKeyArnString', ], ], ], 'EncryptionConfigurationKmsKeyArnString' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '(arn:aws[-a-z0-9]*:kms:[-a-z0-9]*:[0-9]{12}:key/.+)', ], 'ErrorMessage' => [ 'type' => 'string', ], 'ForbiddenException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'GetNamespaceRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', 'namespace', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], 'namespace' => [ 'shape' => 'NamespaceName', 'location' => 'uri', 'locationName' => 'namespace', ], ], ], 'GetNamespaceResponse' => [ 'type' => 'structure', 'required' => [ 'namespace', 'createdAt', 'createdBy', 'ownerAccountId', ], 'members' => [ 'namespace' => [ 'shape' => 'NamespaceList', ], 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBy' => [ 'shape' => 'AccountId', ], 'ownerAccountId' => [ 'shape' => 'AccountId', ], 'namespaceId' => [ 'shape' => 'NamespaceId', ], 'tableBucketId' => [ 'shape' => 'TableBucketId', ], ], ], 'GetTableBucketEncryptionRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], ], ], 'GetTableBucketEncryptionResponse' => [ 'type' => 'structure', 'required' => [ 'encryptionConfiguration', ], 'members' => [ 'encryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], ], ], 'GetTableBucketMaintenanceConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], ], ], 'GetTableBucketMaintenanceConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', 'configuration', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', ], 'configuration' => [ 'shape' => 'TableBucketMaintenanceConfiguration', ], ], ], 'GetTableBucketPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], ], ], 'GetTableBucketPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'resourcePolicy', ], 'members' => [ 'resourcePolicy' => [ 'shape' => 'ResourcePolicy', ], ], ], 'GetTableBucketRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], ], ], 'GetTableBucketResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'name', 'ownerAccountId', 'createdAt', ], 'members' => [ 'arn' => [ 'shape' => 'TableBucketARN', ], 'name' => [ 'shape' => 'TableBucketName', ], 'ownerAccountId' => [ 'shape' => 'AccountId', ], 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'tableBucketId' => [ 'shape' => 'TableBucketId', ], ], ], 'GetTableEncryptionRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', 'namespace', 'name', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], 'namespace' => [ 'shape' => 'NamespaceName', 'location' => 'uri', 'locationName' => 'namespace', ], 'name' => [ 'shape' => 'TableName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetTableEncryptionResponse' => [ 'type' => 'structure', 'required' => [ 'encryptionConfiguration', ], 'members' => [ 'encryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], ], ], 'GetTableMaintenanceConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', 'namespace', 'name', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], 'namespace' => [ 'shape' => 'NamespaceName', 'location' => 'uri', 'locationName' => 'namespace', ], 'name' => [ 'shape' => 'TableName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetTableMaintenanceConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'tableARN', 'configuration', ], 'members' => [ 'tableARN' => [ 'shape' => 'TableARN', ], 'configuration' => [ 'shape' => 'TableMaintenanceConfiguration', ], ], ], 'GetTableMaintenanceJobStatusRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', 'namespace', 'name', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], 'namespace' => [ 'shape' => 'NamespaceName', 'location' => 'uri', 'locationName' => 'namespace', ], 'name' => [ 'shape' => 'TableName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetTableMaintenanceJobStatusResponse' => [ 'type' => 'structure', 'required' => [ 'tableARN', 'status', ], 'members' => [ 'tableARN' => [ 'shape' => 'TableARN', ], 'status' => [ 'shape' => 'TableMaintenanceJobStatus', ], ], ], 'GetTableMetadataLocationRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', 'namespace', 'name', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], 'namespace' => [ 'shape' => 'NamespaceName', 'location' => 'uri', 'locationName' => 'namespace', ], 'name' => [ 'shape' => 'TableName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetTableMetadataLocationResponse' => [ 'type' => 'structure', 'required' => [ 'versionToken', 'warehouseLocation', ], 'members' => [ 'versionToken' => [ 'shape' => 'VersionToken', ], 'metadataLocation' => [ 'shape' => 'MetadataLocation', ], 'warehouseLocation' => [ 'shape' => 'WarehouseLocation', ], ], ], 'GetTablePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', 'namespace', 'name', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], 'namespace' => [ 'shape' => 'NamespaceName', 'location' => 'uri', 'locationName' => 'namespace', ], 'name' => [ 'shape' => 'TableName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetTablePolicyResponse' => [ 'type' => 'structure', 'required' => [ 'resourcePolicy', ], 'members' => [ 'resourcePolicy' => [ 'shape' => 'ResourcePolicy', ], ], ], 'GetTableRequest' => [ 'type' => 'structure', 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'querystring', 'locationName' => 'tableBucketARN', ], 'namespace' => [ 'shape' => 'NamespaceName', 'location' => 'querystring', 'locationName' => 'namespace', ], 'name' => [ 'shape' => 'TableName', 'location' => 'querystring', 'locationName' => 'name', ], 'tableArn' => [ 'shape' => 'TableARN', 'location' => 'querystring', 'locationName' => 'tableArn', ], ], ], 'GetTableResponse' => [ 'type' => 'structure', 'required' => [ 'name', 'type', 'tableARN', 'namespace', 'versionToken', 'warehouseLocation', 'createdAt', 'createdBy', 'modifiedAt', 'modifiedBy', 'ownerAccountId', 'format', ], 'members' => [ 'name' => [ 'shape' => 'TableName', ], 'type' => [ 'shape' => 'TableType', ], 'tableARN' => [ 'shape' => 'TableARN', ], 'namespace' => [ 'shape' => 'NamespaceList', ], 'namespaceId' => [ 'shape' => 'NamespaceId', ], 'versionToken' => [ 'shape' => 'VersionToken', ], 'metadataLocation' => [ 'shape' => 'MetadataLocation', ], 'warehouseLocation' => [ 'shape' => 'WarehouseLocation', ], 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBy' => [ 'shape' => 'AccountId', ], 'managedByService' => [ 'shape' => 'String', ], 'modifiedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'modifiedBy' => [ 'shape' => 'AccountId', ], 'ownerAccountId' => [ 'shape' => 'AccountId', ], 'format' => [ 'shape' => 'OpenTableFormat', ], 'tableBucketId' => [ 'shape' => 'TableBucketId', ], ], ], 'IcebergCompactionSettings' => [ 'type' => 'structure', 'members' => [ 'targetFileSizeMB' => [ 'shape' => 'PositiveInteger', ], 'strategy' => [ 'shape' => 'IcebergCompactionStrategy', ], ], ], 'IcebergCompactionStrategy' => [ 'type' => 'string', 'enum' => [ 'auto', 'binpack', 'sort', 'z-order', ], ], 'IcebergMetadata' => [ 'type' => 'structure', 'required' => [ 'schema', ], 'members' => [ 'schema' => [ 'shape' => 'IcebergSchema', ], ], ], 'IcebergSchema' => [ 'type' => 'structure', 'required' => [ 'fields', ], 'members' => [ 'fields' => [ 'shape' => 'SchemaFieldList', ], ], ], 'IcebergSnapshotManagementSettings' => [ 'type' => 'structure', 'members' => [ 'minSnapshotsToKeep' => [ 'shape' => 'PositiveInteger', ], 'maxSnapshotAgeHours' => [ 'shape' => 'PositiveInteger', ], ], ], 'IcebergUnreferencedFileRemovalSettings' => [ 'type' => 'structure', 'members' => [ 'unreferencedDays' => [ 'shape' => 'PositiveInteger', ], 'nonCurrentDays' => [ 'shape' => 'PositiveInteger', ], ], ], 'InternalServerErrorException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'JobStatus' => [ 'type' => 'string', 'enum' => [ 'Not_Yet_Run', 'Successful', 'Failed', 'Disabled', ], ], 'ListNamespacesLimit' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'ListNamespacesRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], 'prefix' => [ 'shape' => 'ListNamespacesRequestPrefixString', 'location' => 'querystring', 'locationName' => 'prefix', ], 'continuationToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'continuationToken', ], 'maxNamespaces' => [ 'shape' => 'ListNamespacesLimit', 'location' => 'querystring', 'locationName' => 'maxNamespaces', ], ], ], 'ListNamespacesRequestPrefixString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ListNamespacesResponse' => [ 'type' => 'structure', 'required' => [ 'namespaces', ], 'members' => [ 'namespaces' => [ 'shape' => 'NamespaceSummaryList', ], 'continuationToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTableBucketsLimit' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'ListTableBucketsRequest' => [ 'type' => 'structure', 'members' => [ 'prefix' => [ 'shape' => 'ListTableBucketsRequestPrefixString', 'location' => 'querystring', 'locationName' => 'prefix', ], 'continuationToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'continuationToken', ], 'maxBuckets' => [ 'shape' => 'ListTableBucketsLimit', 'location' => 'querystring', 'locationName' => 'maxBuckets', ], ], ], 'ListTableBucketsRequestPrefixString' => [ 'type' => 'string', 'max' => 63, 'min' => 1, ], 'ListTableBucketsResponse' => [ 'type' => 'structure', 'required' => [ 'tableBuckets', ], 'members' => [ 'tableBuckets' => [ 'shape' => 'TableBucketSummaryList', ], 'continuationToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTablesLimit' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'ListTablesRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], 'namespace' => [ 'shape' => 'NamespaceName', 'location' => 'querystring', 'locationName' => 'namespace', ], 'prefix' => [ 'shape' => 'ListTablesRequestPrefixString', 'location' => 'querystring', 'locationName' => 'prefix', ], 'continuationToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'continuationToken', ], 'maxTables' => [ 'shape' => 'ListTablesLimit', 'location' => 'querystring', 'locationName' => 'maxTables', ], ], ], 'ListTablesRequestPrefixString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ListTablesResponse' => [ 'type' => 'structure', 'required' => [ 'tables', ], 'members' => [ 'tables' => [ 'shape' => 'TableSummaryList', ], 'continuationToken' => [ 'shape' => 'NextToken', ], ], ], 'MaintenanceStatus' => [ 'type' => 'string', 'enum' => [ 'enabled', 'disabled', ], ], 'MetadataLocation' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'NamespaceId' => [ 'type' => 'string', ], 'NamespaceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NamespaceName', ], ], 'NamespaceName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[0-9a-z_]*', ], 'NamespaceSummary' => [ 'type' => 'structure', 'required' => [ 'namespace', 'createdAt', 'createdBy', 'ownerAccountId', ], 'members' => [ 'namespace' => [ 'shape' => 'NamespaceList', ], 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'createdBy' => [ 'shape' => 'AccountId', ], 'ownerAccountId' => [ 'shape' => 'AccountId', ], 'namespaceId' => [ 'shape' => 'NamespaceId', ], 'tableBucketId' => [ 'shape' => 'TableBucketId', ], ], ], 'NamespaceSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NamespaceSummary', ], ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'OpenTableFormat' => [ 'type' => 'string', 'enum' => [ 'ICEBERG', ], ], 'PositiveInteger' => [ 'type' => 'integer', 'box' => true, 'max' => **********, 'min' => 1, ], 'PutTableBucketEncryptionRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', 'encryptionConfiguration', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], 'encryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], ], ], 'PutTableBucketMaintenanceConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', 'type', 'value', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], 'type' => [ 'shape' => 'TableBucketMaintenanceType', 'location' => 'uri', 'locationName' => 'type', ], 'value' => [ 'shape' => 'TableBucketMaintenanceConfigurationValue', ], ], ], 'PutTableBucketPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', 'resourcePolicy', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], 'resourcePolicy' => [ 'shape' => 'ResourcePolicy', ], ], ], 'PutTableMaintenanceConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', 'namespace', 'name', 'type', 'value', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], 'namespace' => [ 'shape' => 'NamespaceName', 'location' => 'uri', 'locationName' => 'namespace', ], 'name' => [ 'shape' => 'TableName', 'location' => 'uri', 'locationName' => 'name', ], 'type' => [ 'shape' => 'TableMaintenanceType', 'location' => 'uri', 'locationName' => 'type', ], 'value' => [ 'shape' => 'TableMaintenanceConfigurationValue', ], ], ], 'PutTablePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', 'namespace', 'name', 'resourcePolicy', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], 'namespace' => [ 'shape' => 'NamespaceName', 'location' => 'uri', 'locationName' => 'namespace', ], 'name' => [ 'shape' => 'TableName', 'location' => 'uri', 'locationName' => 'name', ], 'resourcePolicy' => [ 'shape' => 'ResourcePolicy', ], ], ], 'RenameTableRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', 'namespace', 'name', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], 'namespace' => [ 'shape' => 'NamespaceName', 'location' => 'uri', 'locationName' => 'namespace', ], 'name' => [ 'shape' => 'TableName', 'location' => 'uri', 'locationName' => 'name', ], 'newNamespaceName' => [ 'shape' => 'NamespaceName', ], 'newName' => [ 'shape' => 'TableName', ], 'versionToken' => [ 'shape' => 'VersionToken', ], ], ], 'ResourcePolicy' => [ 'type' => 'string', 'max' => 20480, 'min' => 1, ], 'SSEAlgorithm' => [ 'type' => 'string', 'enum' => [ 'AES256', 'aws:kms', ], ], 'SchemaField' => [ 'type' => 'structure', 'required' => [ 'name', 'type', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'String', ], 'required' => [ 'shape' => 'Boolean', ], ], ], 'SchemaFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaField', ], ], 'String' => [ 'type' => 'string', ], 'SyntheticTimestamp_date_time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TableARN' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '(arn:aws[-a-z0-9]*:[a-z0-9]+:[-a-z0-9]*:[0-9]{12}:bucket/[a-z0-9_-]{3,63}/table/[a-zA-Z0-9-_]{1,255})', ], 'TableBucketARN' => [ 'type' => 'string', 'pattern' => '(arn:aws[-a-z0-9]*:[a-z0-9]+:[-a-z0-9]*:[0-9]{12}:bucket/[a-z0-9_-]{3,63})', ], 'TableBucketId' => [ 'type' => 'string', ], 'TableBucketMaintenanceConfiguration' => [ 'type' => 'map', 'key' => [ 'shape' => 'TableBucketMaintenanceType', ], 'value' => [ 'shape' => 'TableBucketMaintenanceConfigurationValue', ], ], 'TableBucketMaintenanceConfigurationValue' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'MaintenanceStatus', ], 'settings' => [ 'shape' => 'TableBucketMaintenanceSettings', ], ], ], 'TableBucketMaintenanceSettings' => [ 'type' => 'structure', 'members' => [ 'icebergUnreferencedFileRemoval' => [ 'shape' => 'IcebergUnreferencedFileRemovalSettings', ], ], 'union' => true, ], 'TableBucketMaintenanceType' => [ 'type' => 'string', 'enum' => [ 'icebergUnreferencedFileRemoval', ], ], 'TableBucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '[0-9a-z-]*', ], 'TableBucketSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'name', 'ownerAccountId', 'createdAt', ], 'members' => [ 'arn' => [ 'shape' => 'TableBucketARN', ], 'name' => [ 'shape' => 'TableBucketName', ], 'ownerAccountId' => [ 'shape' => 'AccountId', ], 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'tableBucketId' => [ 'shape' => 'TableBucketId', ], ], ], 'TableBucketSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableBucketSummary', ], ], 'TableMaintenanceConfiguration' => [ 'type' => 'map', 'key' => [ 'shape' => 'TableMaintenanceType', ], 'value' => [ 'shape' => 'TableMaintenanceConfigurationValue', ], ], 'TableMaintenanceConfigurationValue' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'MaintenanceStatus', ], 'settings' => [ 'shape' => 'TableMaintenanceSettings', ], ], ], 'TableMaintenanceJobStatus' => [ 'type' => 'map', 'key' => [ 'shape' => 'TableMaintenanceJobType', ], 'value' => [ 'shape' => 'TableMaintenanceJobStatusValue', ], ], 'TableMaintenanceJobStatusValue' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'JobStatus', ], 'lastRunTimestamp' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'failureMessage' => [ 'shape' => 'String', ], ], ], 'TableMaintenanceJobType' => [ 'type' => 'string', 'enum' => [ 'icebergCompaction', 'icebergSnapshotManagement', 'icebergUnreferencedFileRemoval', ], ], 'TableMaintenanceSettings' => [ 'type' => 'structure', 'members' => [ 'icebergCompaction' => [ 'shape' => 'IcebergCompactionSettings', ], 'icebergSnapshotManagement' => [ 'shape' => 'IcebergSnapshotManagementSettings', ], ], 'union' => true, ], 'TableMaintenanceType' => [ 'type' => 'string', 'enum' => [ 'icebergCompaction', 'icebergSnapshotManagement', ], ], 'TableMetadata' => [ 'type' => 'structure', 'members' => [ 'iceberg' => [ 'shape' => 'IcebergMetadata', ], ], 'union' => true, ], 'TableName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[0-9a-z_]*', ], 'TableSummary' => [ 'type' => 'structure', 'required' => [ 'namespace', 'name', 'type', 'tableARN', 'createdAt', 'modifiedAt', ], 'members' => [ 'namespace' => [ 'shape' => 'NamespaceList', ], 'name' => [ 'shape' => 'TableName', ], 'type' => [ 'shape' => 'TableType', ], 'tableARN' => [ 'shape' => 'TableARN', ], 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'modifiedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'namespaceId' => [ 'shape' => 'NamespaceId', ], 'tableBucketId' => [ 'shape' => 'TableBucketId', ], ], ], 'TableSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableSummary', ], ], 'TableType' => [ 'type' => 'string', 'enum' => [ 'customer', 'aws', ], ], 'TooManyRequestsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'UpdateTableMetadataLocationRequest' => [ 'type' => 'structure', 'required' => [ 'tableBucketARN', 'namespace', 'name', 'versionToken', 'metadataLocation', ], 'members' => [ 'tableBucketARN' => [ 'shape' => 'TableBucketARN', 'location' => 'uri', 'locationName' => 'tableBucketARN', ], 'namespace' => [ 'shape' => 'NamespaceName', 'location' => 'uri', 'locationName' => 'namespace', ], 'name' => [ 'shape' => 'TableName', 'location' => 'uri', 'locationName' => 'name', ], 'versionToken' => [ 'shape' => 'VersionToken', ], 'metadataLocation' => [ 'shape' => 'MetadataLocation', ], ], ], 'UpdateTableMetadataLocationResponse' => [ 'type' => 'structure', 'required' => [ 'name', 'tableARN', 'namespace', 'versionToken', 'metadataLocation', ], 'members' => [ 'name' => [ 'shape' => 'TableName', ], 'tableARN' => [ 'shape' => 'TableARN', ], 'namespace' => [ 'shape' => 'NamespaceList', ], 'versionToken' => [ 'shape' => 'VersionToken', ], 'metadataLocation' => [ 'shape' => 'MetadataLocation', ], ], ], 'VersionToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'WarehouseLocation' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], ],];
