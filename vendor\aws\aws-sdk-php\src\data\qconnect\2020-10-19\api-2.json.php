<?php
// This file was auto-generated from sdk-root/src/data/qconnect/2020-10-19/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-10-19', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'wisdom', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Amazon Q Connect', 'serviceId' => 'QConnect', 'signatureVersion' => 'v4', 'signingName' => 'wisdom', 'uid' => 'qconnect-2020-10-19', ], 'operations' => [ 'ActivateMessageTemplate' => [ 'name' => 'ActivateMessageTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/messageTemplates/{messageTemplateId}/activate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ActivateMessageTemplateRequest', ], 'output' => [ 'shape' => 'ActivateMessageTemplateResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateAIAgent' => [ 'name' => 'CreateAIAgent', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants/{assistantId}/aiagents', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAIAgentRequest', ], 'output' => [ 'shape' => 'CreateAIAgentResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'CreateAIAgentVersion' => [ 'name' => 'CreateAIAgentVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants/{assistantId}/aiagents/{aiAgentId}/versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAIAgentVersionRequest', ], 'output' => [ 'shape' => 'CreateAIAgentVersionResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'CreateAIGuardrail' => [ 'name' => 'CreateAIGuardrail', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants/{assistantId}/aiguardrails', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAIGuardrailRequest', ], 'output' => [ 'shape' => 'CreateAIGuardrailResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'CreateAIGuardrailVersion' => [ 'name' => 'CreateAIGuardrailVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants/{assistantId}/aiguardrails/{aiGuardrailId}/versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAIGuardrailVersionRequest', ], 'output' => [ 'shape' => 'CreateAIGuardrailVersionResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'CreateAIPrompt' => [ 'name' => 'CreateAIPrompt', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants/{assistantId}/aiprompts', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAIPromptRequest', ], 'output' => [ 'shape' => 'CreateAIPromptResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'CreateAIPromptVersion' => [ 'name' => 'CreateAIPromptVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants/{assistantId}/aiprompts/{aiPromptId}/versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAIPromptVersionRequest', ], 'output' => [ 'shape' => 'CreateAIPromptVersionResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'CreateAssistant' => [ 'name' => 'CreateAssistant', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAssistantRequest', ], 'output' => [ 'shape' => 'CreateAssistantResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateAssistantAssociation' => [ 'name' => 'CreateAssistantAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants/{assistantId}/associations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAssistantAssociationRequest', ], 'output' => [ 'shape' => 'CreateAssistantAssociationResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'CreateContent' => [ 'name' => 'CreateContent', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/contents', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateContentRequest', ], 'output' => [ 'shape' => 'CreateContentResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'CreateContentAssociation' => [ 'name' => 'CreateContentAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/contents/{contentId}/associations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateContentAssociationRequest', ], 'output' => [ 'shape' => 'CreateContentAssociationResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateKnowledgeBase' => [ 'name' => 'CreateKnowledgeBase', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateKnowledgeBaseRequest', ], 'output' => [ 'shape' => 'CreateKnowledgeBaseResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateMessageTemplate' => [ 'name' => 'CreateMessageTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/messageTemplates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMessageTemplateRequest', ], 'output' => [ 'shape' => 'CreateMessageTemplateResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'CreateMessageTemplateAttachment' => [ 'name' => 'CreateMessageTemplateAttachment', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/messageTemplates/{messageTemplateId}/attachments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMessageTemplateAttachmentRequest', ], 'output' => [ 'shape' => 'CreateMessageTemplateAttachmentResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'CreateMessageTemplateVersion' => [ 'name' => 'CreateMessageTemplateVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/messageTemplates/{messageTemplateId}/versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMessageTemplateVersionRequest', ], 'output' => [ 'shape' => 'CreateMessageTemplateVersionResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateQuickResponse' => [ 'name' => 'CreateQuickResponse', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/quickResponses', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateQuickResponseRequest', ], 'output' => [ 'shape' => 'CreateQuickResponseResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'CreateSession' => [ 'name' => 'CreateSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants/{assistantId}/sessions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSessionRequest', ], 'output' => [ 'shape' => 'CreateSessionResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeactivateMessageTemplate' => [ 'name' => 'DeactivateMessageTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/messageTemplates/{messageTemplateId}/deactivate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeactivateMessageTemplateRequest', ], 'output' => [ 'shape' => 'DeactivateMessageTemplateResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteAIAgent' => [ 'name' => 'DeleteAIAgent', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/assistants/{assistantId}/aiagents/{aiAgentId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAIAgentRequest', ], 'output' => [ 'shape' => 'DeleteAIAgentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteAIAgentVersion' => [ 'name' => 'DeleteAIAgentVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/assistants/{assistantId}/aiagents/{aiAgentId}/versions/{versionNumber}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAIAgentVersionRequest', ], 'output' => [ 'shape' => 'DeleteAIAgentVersionResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteAIGuardrail' => [ 'name' => 'DeleteAIGuardrail', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/assistants/{assistantId}/aiguardrails/{aiGuardrailId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteAIGuardrailRequest', ], 'output' => [ 'shape' => 'DeleteAIGuardrailResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteAIGuardrailVersion' => [ 'name' => 'DeleteAIGuardrailVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/assistants/{assistantId}/aiguardrails/{aiGuardrailId}/versions/{versionNumber}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteAIGuardrailVersionRequest', ], 'output' => [ 'shape' => 'DeleteAIGuardrailVersionResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteAIPrompt' => [ 'name' => 'DeleteAIPrompt', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/assistants/{assistantId}/aiprompts/{aiPromptId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAIPromptRequest', ], 'output' => [ 'shape' => 'DeleteAIPromptResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteAIPromptVersion' => [ 'name' => 'DeleteAIPromptVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/assistants/{assistantId}/aiprompts/{aiPromptId}/versions/{versionNumber}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAIPromptVersionRequest', ], 'output' => [ 'shape' => 'DeleteAIPromptVersionResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteAssistant' => [ 'name' => 'DeleteAssistant', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/assistants/{assistantId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAssistantRequest', ], 'output' => [ 'shape' => 'DeleteAssistantResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteAssistantAssociation' => [ 'name' => 'DeleteAssistantAssociation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/assistants/{assistantId}/associations/{assistantAssociationId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAssistantAssociationRequest', ], 'output' => [ 'shape' => 'DeleteAssistantAssociationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteContent' => [ 'name' => 'DeleteContent', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/contents/{contentId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteContentRequest', ], 'output' => [ 'shape' => 'DeleteContentResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteContentAssociation' => [ 'name' => 'DeleteContentAssociation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/contents/{contentId}/associations/{contentAssociationId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteContentAssociationRequest', ], 'output' => [ 'shape' => 'DeleteContentAssociationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteImportJob' => [ 'name' => 'DeleteImportJob', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/importJobs/{importJobId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteImportJobRequest', ], 'output' => [ 'shape' => 'DeleteImportJobResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteKnowledgeBase' => [ 'name' => 'DeleteKnowledgeBase', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteKnowledgeBaseRequest', ], 'output' => [ 'shape' => 'DeleteKnowledgeBaseResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteMessageTemplate' => [ 'name' => 'DeleteMessageTemplate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/messageTemplates/{messageTemplateId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteMessageTemplateRequest', ], 'output' => [ 'shape' => 'DeleteMessageTemplateResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteMessageTemplateAttachment' => [ 'name' => 'DeleteMessageTemplateAttachment', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/messageTemplates/{messageTemplateId}/attachments/{attachmentId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteMessageTemplateAttachmentRequest', ], 'output' => [ 'shape' => 'DeleteMessageTemplateAttachmentResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteQuickResponse' => [ 'name' => 'DeleteQuickResponse', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/quickResponses/{quickResponseId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteQuickResponseRequest', ], 'output' => [ 'shape' => 'DeleteQuickResponseResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'GetAIAgent' => [ 'name' => 'GetAIAgent', 'http' => [ 'method' => 'GET', 'requestUri' => '/assistants/{assistantId}/aiagents/{aiAgentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAIAgentRequest', ], 'output' => [ 'shape' => 'GetAIAgentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetAIGuardrail' => [ 'name' => 'GetAIGuardrail', 'http' => [ 'method' => 'GET', 'requestUri' => '/assistants/{assistantId}/aiguardrails/{aiGuardrailId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAIGuardrailRequest', ], 'output' => [ 'shape' => 'GetAIGuardrailResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetAIPrompt' => [ 'name' => 'GetAIPrompt', 'http' => [ 'method' => 'GET', 'requestUri' => '/assistants/{assistantId}/aiprompts/{aiPromptId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAIPromptRequest', ], 'output' => [ 'shape' => 'GetAIPromptResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetAssistant' => [ 'name' => 'GetAssistant', 'http' => [ 'method' => 'GET', 'requestUri' => '/assistants/{assistantId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAssistantRequest', ], 'output' => [ 'shape' => 'GetAssistantResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAssistantAssociation' => [ 'name' => 'GetAssistantAssociation', 'http' => [ 'method' => 'GET', 'requestUri' => '/assistants/{assistantId}/associations/{assistantAssociationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAssistantAssociationRequest', ], 'output' => [ 'shape' => 'GetAssistantAssociationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetContent' => [ 'name' => 'GetContent', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/contents/{contentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetContentRequest', ], 'output' => [ 'shape' => 'GetContentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetContentAssociation' => [ 'name' => 'GetContentAssociation', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/contents/{contentId}/associations/{contentAssociationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetContentAssociationRequest', ], 'output' => [ 'shape' => 'GetContentAssociationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetContentSummary' => [ 'name' => 'GetContentSummary', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/contents/{contentId}/summary', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetContentSummaryRequest', ], 'output' => [ 'shape' => 'GetContentSummaryResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetImportJob' => [ 'name' => 'GetImportJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/importJobs/{importJobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetImportJobRequest', ], 'output' => [ 'shape' => 'GetImportJobResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetKnowledgeBase' => [ 'name' => 'GetKnowledgeBase', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetKnowledgeBaseRequest', ], 'output' => [ 'shape' => 'GetKnowledgeBaseResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetMessageTemplate' => [ 'name' => 'GetMessageTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/messageTemplates/{messageTemplateId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMessageTemplateRequest', ], 'output' => [ 'shape' => 'GetMessageTemplateResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetNextMessage' => [ 'name' => 'GetNextMessage', 'http' => [ 'method' => 'GET', 'requestUri' => '/assistants/{assistantId}/sessions/{sessionId}/messages/next', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetNextMessageRequest', ], 'output' => [ 'shape' => 'GetNextMessageResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetQuickResponse' => [ 'name' => 'GetQuickResponse', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/quickResponses/{quickResponseId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetQuickResponseRequest', ], 'output' => [ 'shape' => 'GetQuickResponseResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetRecommendations' => [ 'name' => 'GetRecommendations', 'http' => [ 'method' => 'GET', 'requestUri' => '/assistants/{assistantId}/sessions/{sessionId}/recommendations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRecommendationsRequest', ], 'output' => [ 'shape' => 'GetRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'GetRecommendations API will be discontinued starting June 1, 2024. To receive generative responses after March 1, 2024 you will need to create a new Assistant in the Connect console and integrate the Amazon Q in Connect JavaScript library (amazon-q-connectjs) into your applications.', ], 'GetSession' => [ 'name' => 'GetSession', 'http' => [ 'method' => 'GET', 'requestUri' => '/assistants/{assistantId}/sessions/{sessionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSessionRequest', ], 'output' => [ 'shape' => 'GetSessionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListAIAgentVersions' => [ 'name' => 'ListAIAgentVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/assistants/{assistantId}/aiagents/{aiAgentId}/versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAIAgentVersionsRequest', ], 'output' => [ 'shape' => 'ListAIAgentVersionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListAIAgents' => [ 'name' => 'ListAIAgents', 'http' => [ 'method' => 'GET', 'requestUri' => '/assistants/{assistantId}/aiagents', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAIAgentsRequest', ], 'output' => [ 'shape' => 'ListAIAgentsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListAIGuardrailVersions' => [ 'name' => 'ListAIGuardrailVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/assistants/{assistantId}/aiguardrails/{aiGuardrailId}/versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAIGuardrailVersionsRequest', ], 'output' => [ 'shape' => 'ListAIGuardrailVersionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListAIGuardrails' => [ 'name' => 'ListAIGuardrails', 'http' => [ 'method' => 'GET', 'requestUri' => '/assistants/{assistantId}/aiguardrails', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAIGuardrailsRequest', ], 'output' => [ 'shape' => 'ListAIGuardrailsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListAIPromptVersions' => [ 'name' => 'ListAIPromptVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/assistants/{assistantId}/aiprompts/{aiPromptId}/versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAIPromptVersionsRequest', ], 'output' => [ 'shape' => 'ListAIPromptVersionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListAIPrompts' => [ 'name' => 'ListAIPrompts', 'http' => [ 'method' => 'GET', 'requestUri' => '/assistants/{assistantId}/aiprompts', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAIPromptsRequest', ], 'output' => [ 'shape' => 'ListAIPromptsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListAssistantAssociations' => [ 'name' => 'ListAssistantAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/assistants/{assistantId}/associations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAssistantAssociationsRequest', ], 'output' => [ 'shape' => 'ListAssistantAssociationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListAssistants' => [ 'name' => 'ListAssistants', 'http' => [ 'method' => 'GET', 'requestUri' => '/assistants', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAssistantsRequest', ], 'output' => [ 'shape' => 'ListAssistantsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListContentAssociations' => [ 'name' => 'ListContentAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/contents/{contentId}/associations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListContentAssociationsRequest', ], 'output' => [ 'shape' => 'ListContentAssociationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListContents' => [ 'name' => 'ListContents', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/contents', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListContentsRequest', ], 'output' => [ 'shape' => 'ListContentsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListImportJobs' => [ 'name' => 'ListImportJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/importJobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListImportJobsRequest', ], 'output' => [ 'shape' => 'ListImportJobsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListKnowledgeBases' => [ 'name' => 'ListKnowledgeBases', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgeBases', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListKnowledgeBasesRequest', ], 'output' => [ 'shape' => 'ListKnowledgeBasesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListMessageTemplateVersions' => [ 'name' => 'ListMessageTemplateVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/messageTemplates/{messageTemplateId}/versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMessageTemplateVersionsRequest', ], 'output' => [ 'shape' => 'ListMessageTemplateVersionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListMessageTemplates' => [ 'name' => 'ListMessageTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/messageTemplates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMessageTemplatesRequest', ], 'output' => [ 'shape' => 'ListMessageTemplatesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListMessages' => [ 'name' => 'ListMessages', 'http' => [ 'method' => 'GET', 'requestUri' => '/assistants/{assistantId}/sessions/{sessionId}/messages', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMessagesRequest', ], 'output' => [ 'shape' => 'ListMessagesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListQuickResponses' => [ 'name' => 'ListQuickResponses', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/quickResponses', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListQuickResponsesRequest', ], 'output' => [ 'shape' => 'ListQuickResponsesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], ], ], 'NotifyRecommendationsReceived' => [ 'name' => 'NotifyRecommendationsReceived', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants/{assistantId}/sessions/{sessionId}/recommendations/notify', 'responseCode' => 200, ], 'input' => [ 'shape' => 'NotifyRecommendationsReceivedRequest', ], 'output' => [ 'shape' => 'NotifyRecommendationsReceivedResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'PutFeedback' => [ 'name' => 'PutFeedback', 'http' => [ 'method' => 'PUT', 'requestUri' => '/assistants/{assistantId}/feedback', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutFeedbackRequest', ], 'output' => [ 'shape' => 'PutFeedbackResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'QueryAssistant' => [ 'name' => 'QueryAssistant', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants/{assistantId}/query', 'responseCode' => 200, ], 'input' => [ 'shape' => 'QueryAssistantRequest', ], 'output' => [ 'shape' => 'QueryAssistantResponse', ], 'errors' => [ [ 'shape' => 'RequestTimeoutException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'QueryAssistant API will be discontinued starting June 1, 2024. To receive generative responses after March 1, 2024 you will need to create a new Assistant in the Connect console and integrate the Amazon Q in Connect JavaScript library (amazon-q-connectjs) into your applications.', ], 'RemoveAssistantAIAgent' => [ 'name' => 'RemoveAssistantAIAgent', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/assistants/{assistantId}/aiagentConfiguration', 'responseCode' => 204, ], 'input' => [ 'shape' => 'RemoveAssistantAIAgentRequest', ], 'output' => [ 'shape' => 'RemoveAssistantAIAgentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'RemoveKnowledgeBaseTemplateUri' => [ 'name' => 'RemoveKnowledgeBaseTemplateUri', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/templateUri', 'responseCode' => 204, ], 'input' => [ 'shape' => 'RemoveKnowledgeBaseTemplateUriRequest', ], 'output' => [ 'shape' => 'RemoveKnowledgeBaseTemplateUriResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'RenderMessageTemplate' => [ 'name' => 'RenderMessageTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/messageTemplates/{messageTemplateId}/render', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RenderMessageTemplateRequest', ], 'output' => [ 'shape' => 'RenderMessageTemplateResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'SearchContent' => [ 'name' => 'SearchContent', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/search', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchContentRequest', ], 'output' => [ 'shape' => 'SearchContentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'SearchMessageTemplates' => [ 'name' => 'SearchMessageTemplates', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/search/messageTemplates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchMessageTemplatesRequest', ], 'output' => [ 'shape' => 'SearchMessageTemplatesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'SearchQuickResponses' => [ 'name' => 'SearchQuickResponses', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/search/quickResponses', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchQuickResponsesRequest', ], 'output' => [ 'shape' => 'SearchQuickResponsesResponse', ], 'errors' => [ [ 'shape' => 'RequestTimeoutException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'SearchSessions' => [ 'name' => 'SearchSessions', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants/{assistantId}/searchSessions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchSessionsRequest', ], 'output' => [ 'shape' => 'SearchSessionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'SendMessage' => [ 'name' => 'SendMessage', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants/{assistantId}/sessions/{sessionId}/message', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SendMessageRequest', ], 'output' => [ 'shape' => 'SendMessageResponse', ], 'errors' => [ [ 'shape' => 'RequestTimeoutException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'StartContentUpload' => [ 'name' => 'StartContentUpload', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/upload', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartContentUploadRequest', ], 'output' => [ 'shape' => 'StartContentUploadResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StartImportJob' => [ 'name' => 'StartImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/importJobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartImportJobRequest', ], 'output' => [ 'shape' => 'StartImportJobResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateAIAgent' => [ 'name' => 'UpdateAIAgent', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants/{assistantId}/aiagents/{aiAgentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAIAgentRequest', ], 'output' => [ 'shape' => 'UpdateAIAgentResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdateAIGuardrail' => [ 'name' => 'UpdateAIGuardrail', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants/{assistantId}/aiguardrails/{aiGuardrailId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAIGuardrailRequest', ], 'output' => [ 'shape' => 'UpdateAIGuardrailResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdateAIPrompt' => [ 'name' => 'UpdateAIPrompt', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants/{assistantId}/aiprompts/{aiPromptId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAIPromptRequest', ], 'output' => [ 'shape' => 'UpdateAIPromptResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdateAssistantAIAgent' => [ 'name' => 'UpdateAssistantAIAgent', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants/{assistantId}/aiagentConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAssistantAIAgentRequest', ], 'output' => [ 'shape' => 'UpdateAssistantAIAgentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateContent' => [ 'name' => 'UpdateContent', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/contents/{contentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateContentRequest', ], 'output' => [ 'shape' => 'UpdateContentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateKnowledgeBaseTemplateUri' => [ 'name' => 'UpdateKnowledgeBaseTemplateUri', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/templateUri', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateKnowledgeBaseTemplateUriRequest', ], 'output' => [ 'shape' => 'UpdateKnowledgeBaseTemplateUriResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateMessageTemplate' => [ 'name' => 'UpdateMessageTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/messageTemplates/{messageTemplateId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMessageTemplateRequest', ], 'output' => [ 'shape' => 'UpdateMessageTemplateResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateMessageTemplateMetadata' => [ 'name' => 'UpdateMessageTemplateMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/messageTemplates/{messageTemplateId}/metadata', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMessageTemplateMetadataRequest', ], 'output' => [ 'shape' => 'UpdateMessageTemplateMetadataResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateQuickResponse' => [ 'name' => 'UpdateQuickResponse', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgeBases/{knowledgeBaseId}/quickResponses/{quickResponseId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateQuickResponseRequest', ], 'output' => [ 'shape' => 'UpdateQuickResponseResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateSession' => [ 'name' => 'UpdateSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistants/{assistantId}/sessions/{sessionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSessionRequest', ], 'output' => [ 'shape' => 'UpdateSessionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateSessionData' => [ 'name' => 'UpdateSessionData', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/assistants/{assistantId}/sessions/{sessionId}/data', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSessionDataRequest', ], 'output' => [ 'shape' => 'UpdateSessionDataResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], ], 'shapes' => [ 'AIAgentAssociationConfigurationType' => [ 'type' => 'string', 'enum' => [ 'KNOWLEDGE_BASE', ], ], 'AIAgentConfiguration' => [ 'type' => 'structure', 'members' => [ 'manualSearchAIAgentConfiguration' => [ 'shape' => 'ManualSearchAIAgentConfiguration', ], 'answerRecommendationAIAgentConfiguration' => [ 'shape' => 'AnswerRecommendationAIAgentConfiguration', ], 'selfServiceAIAgentConfiguration' => [ 'shape' => 'SelfServiceAIAgentConfiguration', ], ], 'union' => true, ], 'AIAgentConfigurationData' => [ 'type' => 'structure', 'required' => [ 'aiAgentId', ], 'members' => [ 'aiAgentId' => [ 'shape' => 'UuidWithQualifier', ], ], ], 'AIAgentConfigurationMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'AIAgentType', ], 'value' => [ 'shape' => 'AIAgentConfigurationData', ], ], 'AIAgentData' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'assistantArn', 'aiAgentId', 'aiAgentArn', 'name', 'type', 'configuration', 'visibilityStatus', ], 'members' => [ 'assistantId' => [ 'shape' => 'Uuid', ], 'assistantArn' => [ 'shape' => 'Arn', ], 'aiAgentId' => [ 'shape' => 'Uuid', ], 'aiAgentArn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'Name', ], 'type' => [ 'shape' => 'AIAgentType', ], 'configuration' => [ 'shape' => 'AIAgentConfiguration', ], 'modifiedTime' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'visibilityStatus' => [ 'shape' => 'VisibilityStatus', ], 'tags' => [ 'shape' => 'Tags', ], 'origin' => [ 'shape' => 'Origin', ], 'status' => [ 'shape' => 'Status', ], ], ], 'AIAgentSummary' => [ 'type' => 'structure', 'required' => [ 'name', 'assistantId', 'assistantArn', 'aiAgentId', 'type', 'aiAgentArn', 'visibilityStatus', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'assistantId' => [ 'shape' => 'Uuid', ], 'assistantArn' => [ 'shape' => 'Arn', ], 'aiAgentId' => [ 'shape' => 'Uuid', ], 'type' => [ 'shape' => 'AIAgentType', ], 'aiAgentArn' => [ 'shape' => 'Arn', ], 'modifiedTime' => [ 'shape' => 'Timestamp', ], 'visibilityStatus' => [ 'shape' => 'VisibilityStatus', ], 'configuration' => [ 'shape' => 'AIAgentConfiguration', ], 'origin' => [ 'shape' => 'Origin', ], 'description' => [ 'shape' => 'Description', ], 'status' => [ 'shape' => 'Status', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'AIAgentSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AIAgentSummary', ], ], 'AIAgentType' => [ 'type' => 'string', 'enum' => [ 'MANUAL_SEARCH', 'ANSWER_RECOMMENDATION', 'SELF_SERVICE', ], ], 'AIAgentVersionSummariesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AIAgentVersionSummary', ], ], 'AIAgentVersionSummary' => [ 'type' => 'structure', 'members' => [ 'aiAgentSummary' => [ 'shape' => 'AIAgentSummary', ], 'versionNumber' => [ 'shape' => 'Version', ], ], ], 'AIGuardrailBlockedMessaging' => [ 'type' => 'string', 'max' => 500, 'min' => 1, 'sensitive' => true, ], 'AIGuardrailContentPolicyConfig' => [ 'type' => 'structure', 'required' => [ 'filtersConfig', ], 'members' => [ 'filtersConfig' => [ 'shape' => 'GuardrailContentFiltersConfig', ], ], ], 'AIGuardrailContextualGroundingPolicyConfig' => [ 'type' => 'structure', 'required' => [ 'filtersConfig', ], 'members' => [ 'filtersConfig' => [ 'shape' => 'GuardrailContextualGroundingFiltersConfig', ], ], ], 'AIGuardrailData' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'assistantArn', 'aiGuardrailArn', 'aiGuardrailId', 'name', 'visibilityStatus', 'blockedInputMessaging', 'blockedOutputsMessaging', ], 'members' => [ 'assistantId' => [ 'shape' => 'Uuid', ], 'assistantArn' => [ 'shape' => 'Arn', ], 'aiGuardrailArn' => [ 'shape' => 'Arn', ], 'aiGuardrailId' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'Name', ], 'visibilityStatus' => [ 'shape' => 'VisibilityStatus', ], 'blockedInputMessaging' => [ 'shape' => 'AIGuardrailBlockedMessaging', ], 'blockedOutputsMessaging' => [ 'shape' => 'AIGuardrailBlockedMessaging', ], 'description' => [ 'shape' => 'AIGuardrailDescription', ], 'topicPolicyConfig' => [ 'shape' => 'AIGuardrailTopicPolicyConfig', ], 'contentPolicyConfig' => [ 'shape' => 'AIGuardrailContentPolicyConfig', ], 'wordPolicyConfig' => [ 'shape' => 'AIGuardrailWordPolicyConfig', ], 'sensitiveInformationPolicyConfig' => [ 'shape' => 'AIGuardrailSensitiveInformationPolicyConfig', ], 'contextualGroundingPolicyConfig' => [ 'shape' => 'AIGuardrailContextualGroundingPolicyConfig', ], 'tags' => [ 'shape' => 'Tags', ], 'status' => [ 'shape' => 'Status', ], 'modifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'AIGuardrailDescription' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'sensitive' => true, ], 'AIGuardrailSensitiveInformationPolicyConfig' => [ 'type' => 'structure', 'members' => [ 'piiEntitiesConfig' => [ 'shape' => 'GuardrailPiiEntitiesConfig', ], 'regexesConfig' => [ 'shape' => 'GuardrailRegexesConfig', ], ], ], 'AIGuardrailSummariesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AIGuardrailSummary', ], ], 'AIGuardrailSummary' => [ 'type' => 'structure', 'required' => [ 'name', 'assistantId', 'assistantArn', 'aiGuardrailId', 'aiGuardrailArn', 'visibilityStatus', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'assistantId' => [ 'shape' => 'Uuid', ], 'assistantArn' => [ 'shape' => 'Arn', ], 'aiGuardrailId' => [ 'shape' => 'Uuid', ], 'aiGuardrailArn' => [ 'shape' => 'Arn', ], 'modifiedTime' => [ 'shape' => 'Timestamp', ], 'visibilityStatus' => [ 'shape' => 'VisibilityStatus', ], 'description' => [ 'shape' => 'AIGuardrailDescription', ], 'status' => [ 'shape' => 'Status', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'AIGuardrailTopicPolicyConfig' => [ 'type' => 'structure', 'required' => [ 'topicsConfig', ], 'members' => [ 'topicsConfig' => [ 'shape' => 'GuardrailTopicsConfig', ], ], ], 'AIGuardrailVersionSummariesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AIGuardrailVersionSummary', ], ], 'AIGuardrailVersionSummary' => [ 'type' => 'structure', 'members' => [ 'aiGuardrailSummary' => [ 'shape' => 'AIGuardrailSummary', ], 'versionNumber' => [ 'shape' => 'Version', ], ], ], 'AIGuardrailWordPolicyConfig' => [ 'type' => 'structure', 'members' => [ 'wordsConfig' => [ 'shape' => 'GuardrailWordsConfig', ], 'managedWordListsConfig' => [ 'shape' => 'GuardrailManagedWordListsConfig', ], ], ], 'AIPromptAPIFormat' => [ 'type' => 'string', 'enum' => [ 'ANTHROPIC_CLAUDE_MESSAGES', 'ANTHROPIC_CLAUDE_TEXT_COMPLETIONS', 'MESSAGES', 'TEXT_COMPLETIONS', ], ], 'AIPromptData' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'assistantArn', 'aiPromptId', 'aiPromptArn', 'name', 'type', 'templateType', 'modelId', 'apiFormat', 'templateConfiguration', 'visibilityStatus', ], 'members' => [ 'assistantId' => [ 'shape' => 'Uuid', ], 'assistantArn' => [ 'shape' => 'Arn', ], 'aiPromptId' => [ 'shape' => 'Uuid', ], 'aiPromptArn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'Name', ], 'type' => [ 'shape' => 'AIPromptType', ], 'templateType' => [ 'shape' => 'AIPromptTemplateType', ], 'modelId' => [ 'shape' => 'AIPromptModelIdentifier', ], 'apiFormat' => [ 'shape' => 'AIPromptAPIFormat', ], 'templateConfiguration' => [ 'shape' => 'AIPromptTemplateConfiguration', ], 'modifiedTime' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'visibilityStatus' => [ 'shape' => 'VisibilityStatus', ], 'tags' => [ 'shape' => 'Tags', ], 'origin' => [ 'shape' => 'Origin', ], 'status' => [ 'shape' => 'Status', ], ], ], 'AIPromptModelIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'AIPromptSummary' => [ 'type' => 'structure', 'required' => [ 'name', 'assistantId', 'assistantArn', 'aiPromptId', 'type', 'aiPromptArn', 'templateType', 'modelId', 'apiFormat', 'visibilityStatus', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'assistantId' => [ 'shape' => 'Uuid', ], 'assistantArn' => [ 'shape' => 'Arn', ], 'aiPromptId' => [ 'shape' => 'Uuid', ], 'type' => [ 'shape' => 'AIPromptType', ], 'aiPromptArn' => [ 'shape' => 'Arn', ], 'modifiedTime' => [ 'shape' => 'Timestamp', ], 'templateType' => [ 'shape' => 'AIPromptTemplateType', ], 'modelId' => [ 'shape' => 'AIPromptModelIdentifier', ], 'apiFormat' => [ 'shape' => 'AIPromptAPIFormat', ], 'visibilityStatus' => [ 'shape' => 'VisibilityStatus', ], 'origin' => [ 'shape' => 'Origin', ], 'description' => [ 'shape' => 'Description', ], 'status' => [ 'shape' => 'Status', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'AIPromptSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AIPromptSummary', ], ], 'AIPromptTemplateConfiguration' => [ 'type' => 'structure', 'members' => [ 'textFullAIPromptEditTemplateConfiguration' => [ 'shape' => 'TextFullAIPromptEditTemplateConfiguration', ], ], 'union' => true, ], 'AIPromptTemplateType' => [ 'type' => 'string', 'enum' => [ 'TEXT', ], ], 'AIPromptType' => [ 'type' => 'string', 'enum' => [ 'ANSWER_GENERATION', 'INTENT_LABELING_GENERATION', 'QUERY_REFORMULATION', 'SELF_SERVICE_PRE_PROCESSING', 'SELF_SERVICE_ANSWER_GENERATION', ], ], 'AIPromptVersionSummariesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AIPromptVersionSummary', ], ], 'AIPromptVersionSummary' => [ 'type' => 'structure', 'members' => [ 'aiPromptSummary' => [ 'shape' => 'AIPromptSummary', ], 'versionNumber' => [ 'shape' => 'Version', ], ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'ActivateMessageTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'messageTemplateId', 'versionNumber', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'messageTemplateId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'messageTemplateId', ], 'versionNumber' => [ 'shape' => 'Version', ], ], ], 'ActivateMessageTemplateResponse' => [ 'type' => 'structure', 'required' => [ 'messageTemplateArn', 'messageTemplateId', 'versionNumber', ], 'members' => [ 'messageTemplateArn' => [ 'shape' => 'ArnWithQualifier', ], 'messageTemplateId' => [ 'shape' => 'Uuid', ], 'versionNumber' => [ 'shape' => 'Version', ], ], ], 'AgentAttributes' => [ 'type' => 'structure', 'members' => [ 'firstName' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'lastName' => [ 'shape' => 'MessageTemplateAttributeValue', ], ], ], 'AmazonConnectGuideAssociationData' => [ 'type' => 'structure', 'members' => [ 'flowId' => [ 'shape' => 'GenericArn', ], ], ], 'AndConditions' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagCondition', ], ], 'AnswerRecommendationAIAgentConfiguration' => [ 'type' => 'structure', 'members' => [ 'intentLabelingGenerationAIPromptId' => [ 'shape' => 'UuidWithQualifier', ], 'queryReformulationAIPromptId' => [ 'shape' => 'UuidWithQualifier', ], 'answerGenerationAIPromptId' => [ 'shape' => 'UuidWithQualifier', ], 'answerGenerationAIGuardrailId' => [ 'shape' => 'UuidWithQualifier', ], 'associationConfigurations' => [ 'shape' => 'AssociationConfigurationList', ], 'locale' => [ 'shape' => 'NonEmptyString', ], ], ], 'AppIntegrationsConfiguration' => [ 'type' => 'structure', 'required' => [ 'appIntegrationArn', ], 'members' => [ 'appIntegrationArn' => [ 'shape' => 'GenericArn', ], 'objectFields' => [ 'shape' => 'ObjectFieldsList', ], ], ], 'Arn' => [ 'type' => 'string', 'pattern' => 'arn:[a-z-]*?:wisdom:[a-z0-9-]*?:[0-9]{12}:[a-z-]*?/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}(?:/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}){0,2}', ], 'ArnWithQualifier' => [ 'type' => 'string', 'pattern' => 'arn:[a-z-]*?:wisdom:[a-z0-9-]*?:[0-9]{12}:[a-z-]*?/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}(?:/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}){0,2}(:[A-Z0-9_$]+){0,1}', ], 'AssistantAssociationData' => [ 'type' => 'structure', 'required' => [ 'assistantAssociationId', 'assistantAssociationArn', 'assistantId', 'assistantArn', 'associationType', 'associationData', ], 'members' => [ 'assistantAssociationId' => [ 'shape' => 'Uuid', ], 'assistantAssociationArn' => [ 'shape' => 'Arn', ], 'assistantId' => [ 'shape' => 'Uuid', ], 'assistantArn' => [ 'shape' => 'Arn', ], 'associationType' => [ 'shape' => 'AssociationType', ], 'associationData' => [ 'shape' => 'AssistantAssociationOutputData', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'AssistantAssociationInputData' => [ 'type' => 'structure', 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], ], 'union' => true, ], 'AssistantAssociationOutputData' => [ 'type' => 'structure', 'members' => [ 'knowledgeBaseAssociation' => [ 'shape' => 'KnowledgeBaseAssociationData', ], ], 'union' => true, ], 'AssistantAssociationSummary' => [ 'type' => 'structure', 'required' => [ 'assistantAssociationId', 'assistantAssociationArn', 'assistantId', 'assistantArn', 'associationType', 'associationData', ], 'members' => [ 'assistantAssociationId' => [ 'shape' => 'Uuid', ], 'assistantAssociationArn' => [ 'shape' => 'Arn', ], 'assistantId' => [ 'shape' => 'Uuid', ], 'assistantArn' => [ 'shape' => 'Arn', ], 'associationType' => [ 'shape' => 'AssociationType', ], 'associationData' => [ 'shape' => 'AssistantAssociationOutputData', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'AssistantAssociationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssistantAssociationSummary', ], ], 'AssistantCapabilityConfiguration' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'AssistantCapabilityType', ], ], ], 'AssistantCapabilityType' => [ 'type' => 'string', 'enum' => [ 'V1', 'V2', ], ], 'AssistantData' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'assistantArn', 'name', 'type', 'status', ], 'members' => [ 'assistantId' => [ 'shape' => 'Uuid', ], 'assistantArn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'Name', ], 'type' => [ 'shape' => 'AssistantType', ], 'status' => [ 'shape' => 'AssistantStatus', ], 'description' => [ 'shape' => 'Description', ], 'tags' => [ 'shape' => 'Tags', ], 'serverSideEncryptionConfiguration' => [ 'shape' => 'ServerSideEncryptionConfiguration', ], 'integrationConfiguration' => [ 'shape' => 'AssistantIntegrationConfiguration', ], 'capabilityConfiguration' => [ 'shape' => 'AssistantCapabilityConfiguration', ], 'aiAgentConfiguration' => [ 'shape' => 'AIAgentConfigurationMap', ], ], ], 'AssistantIntegrationConfiguration' => [ 'type' => 'structure', 'members' => [ 'topicIntegrationArn' => [ 'shape' => 'GenericArn', ], ], ], 'AssistantList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssistantSummary', ], ], 'AssistantStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'ACTIVE', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', 'DELETED', ], ], 'AssistantSummary' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'assistantArn', 'name', 'type', 'status', ], 'members' => [ 'assistantId' => [ 'shape' => 'Uuid', ], 'assistantArn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'Name', ], 'type' => [ 'shape' => 'AssistantType', ], 'status' => [ 'shape' => 'AssistantStatus', ], 'description' => [ 'shape' => 'Description', ], 'tags' => [ 'shape' => 'Tags', ], 'serverSideEncryptionConfiguration' => [ 'shape' => 'ServerSideEncryptionConfiguration', ], 'integrationConfiguration' => [ 'shape' => 'AssistantIntegrationConfiguration', ], 'capabilityConfiguration' => [ 'shape' => 'AssistantCapabilityConfiguration', ], 'aiAgentConfiguration' => [ 'shape' => 'AIAgentConfigurationMap', ], ], ], 'AssistantType' => [ 'type' => 'string', 'enum' => [ 'AGENT', ], ], 'AssociationConfiguration' => [ 'type' => 'structure', 'members' => [ 'associationId' => [ 'shape' => 'Uuid', ], 'associationType' => [ 'shape' => 'AIAgentAssociationConfigurationType', ], 'associationConfigurationData' => [ 'shape' => 'AssociationConfigurationData', ], ], ], 'AssociationConfigurationData' => [ 'type' => 'structure', 'members' => [ 'knowledgeBaseAssociationConfigurationData' => [ 'shape' => 'KnowledgeBaseAssociationConfigurationData', ], ], 'union' => true, ], 'AssociationConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociationConfiguration', ], ], 'AssociationType' => [ 'type' => 'string', 'enum' => [ 'KNOWLEDGE_BASE', ], ], 'AttachmentFileName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\p{L}\\p{M}\\p{N}_\\s&@()+,;=\\-]+\\.[A-Za-z0-9]+', 'sensitive' => true, ], 'BedrockFoundationModelConfigurationForParsing' => [ 'type' => 'structure', 'required' => [ 'modelArn', ], 'members' => [ 'modelArn' => [ 'shape' => 'BedrockModelArnForParsing', ], 'parsingPrompt' => [ 'shape' => 'ParsingPrompt', ], ], ], 'BedrockModelArnForParsing' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}::foundation-model/anthropic.claude-3-haiku-20240307-v1:0', ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'Channel' => [ 'type' => 'string', 'max' => 10, 'min' => 1, 'sensitive' => true, ], 'ChannelSubtype' => [ 'type' => 'string', 'enum' => [ 'EMAIL', 'SMS', ], ], 'Channels' => [ 'type' => 'list', 'member' => [ 'shape' => 'Channel', ], ], 'ChunkingConfiguration' => [ 'type' => 'structure', 'required' => [ 'chunkingStrategy', ], 'members' => [ 'chunkingStrategy' => [ 'shape' => 'ChunkingStrategy', ], 'fixedSizeChunkingConfiguration' => [ 'shape' => 'FixedSizeChunkingConfiguration', ], 'hierarchicalChunkingConfiguration' => [ 'shape' => 'HierarchicalChunkingConfiguration', ], 'semanticChunkingConfiguration' => [ 'shape' => 'SemanticChunkingConfiguration', ], ], ], 'ChunkingStrategy' => [ 'type' => 'string', 'enum' => [ 'FIXED_SIZE', 'NONE', 'HIERARCHICAL', 'SEMANTIC', ], ], 'CitationSpan' => [ 'type' => 'structure', 'members' => [ 'beginOffsetInclusive' => [ 'shape' => 'CitationSpanOffset', ], 'endOffsetExclusive' => [ 'shape' => 'CitationSpanOffset', ], ], ], 'CitationSpanOffset' => [ 'type' => 'integer', ], 'ClientToken' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'Configuration' => [ 'type' => 'structure', 'members' => [ 'connectConfiguration' => [ 'shape' => 'ConnectConfiguration', ], ], 'union' => true, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ConnectConfiguration' => [ 'type' => 'structure', 'members' => [ 'instanceId' => [ 'shape' => 'NonEmptyString', ], ], ], 'ContactAttributeKey' => [ 'type' => 'string', ], 'ContactAttributeKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactAttributeKey', ], 'sensitive' => true, ], 'ContactAttributeValue' => [ 'type' => 'string', ], 'ContactAttributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'ContactAttributeKey', ], 'value' => [ 'shape' => 'ContactAttributeValue', ], 'sensitive' => true, ], 'ContentAssociationContents' => [ 'type' => 'structure', 'members' => [ 'amazonConnectGuideAssociation' => [ 'shape' => 'AmazonConnectGuideAssociationData', ], ], 'union' => true, ], 'ContentAssociationData' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'knowledgeBaseArn', 'contentId', 'contentArn', 'contentAssociationId', 'contentAssociationArn', 'associationType', 'associationData', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'contentId' => [ 'shape' => 'Uuid', ], 'contentArn' => [ 'shape' => 'Arn', ], 'contentAssociationId' => [ 'shape' => 'Uuid', ], 'contentAssociationArn' => [ 'shape' => 'Arn', ], 'associationType' => [ 'shape' => 'ContentAssociationType', ], 'associationData' => [ 'shape' => 'ContentAssociationContents', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'ContentAssociationSummary' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'knowledgeBaseArn', 'contentId', 'contentArn', 'contentAssociationId', 'contentAssociationArn', 'associationType', 'associationData', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'contentId' => [ 'shape' => 'Uuid', ], 'contentArn' => [ 'shape' => 'Arn', ], 'contentAssociationId' => [ 'shape' => 'Uuid', ], 'contentAssociationArn' => [ 'shape' => 'Arn', ], 'associationType' => [ 'shape' => 'ContentAssociationType', ], 'associationData' => [ 'shape' => 'ContentAssociationContents', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'ContentAssociationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContentAssociationSummary', ], ], 'ContentAssociationType' => [ 'type' => 'string', 'enum' => [ 'AMAZON_CONNECT_GUIDE', ], ], 'ContentData' => [ 'type' => 'structure', 'required' => [ 'contentArn', 'contentId', 'knowledgeBaseArn', 'knowledgeBaseId', 'name', 'revisionId', 'title', 'contentType', 'status', 'metadata', 'url', 'urlExpiry', ], 'members' => [ 'contentArn' => [ 'shape' => 'Arn', ], 'contentId' => [ 'shape' => 'Uuid', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'Name', ], 'revisionId' => [ 'shape' => 'NonEmptyString', ], 'title' => [ 'shape' => 'ContentTitle', ], 'contentType' => [ 'shape' => 'ContentType', ], 'status' => [ 'shape' => 'ContentStatus', ], 'metadata' => [ 'shape' => 'ContentMetadata', ], 'tags' => [ 'shape' => 'Tags', ], 'linkOutUri' => [ 'shape' => 'Uri', ], 'url' => [ 'shape' => 'Url', ], 'urlExpiry' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], ], ], 'ContentDataDetails' => [ 'type' => 'structure', 'required' => [ 'textData', 'rankingData', ], 'members' => [ 'textData' => [ 'shape' => 'TextData', ], 'rankingData' => [ 'shape' => 'RankingData', ], ], ], 'ContentDisposition' => [ 'type' => 'string', 'enum' => [ 'ATTACHMENT', ], ], 'ContentFeedbackData' => [ 'type' => 'structure', 'members' => [ 'generativeContentFeedbackData' => [ 'shape' => 'GenerativeContentFeedbackData', ], ], 'union' => true, ], 'ContentMetadata' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'NonEmptyString', ], 'max' => 10, 'min' => 0, ], 'ContentReference' => [ 'type' => 'structure', 'members' => [ 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'contentArn' => [ 'shape' => 'Arn', ], 'contentId' => [ 'shape' => 'Uuid', ], 'sourceURL' => [ 'shape' => 'String', ], 'referenceType' => [ 'shape' => 'ReferenceType', ], ], ], 'ContentStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'ACTIVE', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', 'DELETED', 'UPDATE_FAILED', ], ], 'ContentSummary' => [ 'type' => 'structure', 'required' => [ 'contentArn', 'contentId', 'knowledgeBaseArn', 'knowledgeBaseId', 'name', 'revisionId', 'title', 'contentType', 'status', 'metadata', ], 'members' => [ 'contentArn' => [ 'shape' => 'Arn', ], 'contentId' => [ 'shape' => 'Uuid', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'Name', ], 'revisionId' => [ 'shape' => 'NonEmptyString', ], 'title' => [ 'shape' => 'ContentTitle', ], 'contentType' => [ 'shape' => 'ContentType', ], 'status' => [ 'shape' => 'ContentStatus', ], 'metadata' => [ 'shape' => 'ContentMetadata', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'ContentSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContentSummary', ], ], 'ContentTitle' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ContentType' => [ 'type' => 'string', 'pattern' => '(text/(plain|html|csv))|(application/(pdf|vnd\\.openxmlformats-officedocument\\.wordprocessingml\\.document))|(application/x\\.wisdom-json;source=(salesforce|servicenow|zendesk))', ], 'ConversationContext' => [ 'type' => 'structure', 'required' => [ 'selfServiceConversationHistory', ], 'members' => [ 'selfServiceConversationHistory' => [ 'shape' => 'SelfServiceConversationHistoryList', ], ], ], 'ConversationState' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'ConversationStatus', ], 'reason' => [ 'shape' => 'ConversationStatusReason', ], ], ], 'ConversationStatus' => [ 'type' => 'string', 'enum' => [ 'CLOSED', 'READY', 'PROCESSING', ], ], 'ConversationStatusReason' => [ 'type' => 'string', 'enum' => [ 'SUCCESS', 'FAILED', 'REJECTED', ], ], 'CreateAIAgentRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'name', 'type', 'configuration', 'visibilityStatus', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'name' => [ 'shape' => 'Name', ], 'type' => [ 'shape' => 'AIAgentType', ], 'configuration' => [ 'shape' => 'AIAgentConfiguration', ], 'visibilityStatus' => [ 'shape' => 'VisibilityStatus', ], 'tags' => [ 'shape' => 'Tags', ], 'description' => [ 'shape' => 'Description', ], ], ], 'CreateAIAgentResponse' => [ 'type' => 'structure', 'members' => [ 'aiAgent' => [ 'shape' => 'AIAgentData', ], ], ], 'CreateAIAgentVersionRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'aiAgentId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'aiAgentId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'aiAgentId', ], 'modifiedTime' => [ 'shape' => 'Timestamp', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateAIAgentVersionResponse' => [ 'type' => 'structure', 'members' => [ 'aiAgent' => [ 'shape' => 'AIAgentData', ], 'versionNumber' => [ 'shape' => 'Version', ], ], ], 'CreateAIGuardrailRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'name', 'blockedInputMessaging', 'blockedOutputsMessaging', 'visibilityStatus', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'name' => [ 'shape' => 'Name', ], 'blockedInputMessaging' => [ 'shape' => 'AIGuardrailBlockedMessaging', ], 'blockedOutputsMessaging' => [ 'shape' => 'AIGuardrailBlockedMessaging', ], 'visibilityStatus' => [ 'shape' => 'VisibilityStatus', ], 'description' => [ 'shape' => 'AIGuardrailDescription', ], 'topicPolicyConfig' => [ 'shape' => 'AIGuardrailTopicPolicyConfig', ], 'contentPolicyConfig' => [ 'shape' => 'AIGuardrailContentPolicyConfig', ], 'wordPolicyConfig' => [ 'shape' => 'AIGuardrailWordPolicyConfig', ], 'sensitiveInformationPolicyConfig' => [ 'shape' => 'AIGuardrailSensitiveInformationPolicyConfig', ], 'contextualGroundingPolicyConfig' => [ 'shape' => 'AIGuardrailContextualGroundingPolicyConfig', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateAIGuardrailResponse' => [ 'type' => 'structure', 'members' => [ 'aiGuardrail' => [ 'shape' => 'AIGuardrailData', ], ], ], 'CreateAIGuardrailVersionRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'aiGuardrailId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'aiGuardrailId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'aiGuardrailId', ], 'modifiedTime' => [ 'shape' => 'Timestamp', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateAIGuardrailVersionResponse' => [ 'type' => 'structure', 'members' => [ 'aiGuardrail' => [ 'shape' => 'AIGuardrailData', ], 'versionNumber' => [ 'shape' => 'Version', ], ], ], 'CreateAIPromptRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'name', 'type', 'templateConfiguration', 'visibilityStatus', 'templateType', 'modelId', 'apiFormat', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'name' => [ 'shape' => 'Name', ], 'type' => [ 'shape' => 'AIPromptType', ], 'templateConfiguration' => [ 'shape' => 'AIPromptTemplateConfiguration', ], 'visibilityStatus' => [ 'shape' => 'VisibilityStatus', ], 'templateType' => [ 'shape' => 'AIPromptTemplateType', ], 'modelId' => [ 'shape' => 'AIPromptModelIdentifier', ], 'apiFormat' => [ 'shape' => 'AIPromptAPIFormat', ], 'tags' => [ 'shape' => 'Tags', ], 'description' => [ 'shape' => 'Description', ], ], ], 'CreateAIPromptResponse' => [ 'type' => 'structure', 'members' => [ 'aiPrompt' => [ 'shape' => 'AIPromptData', ], ], ], 'CreateAIPromptVersionRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'aiPromptId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'aiPromptId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'aiPromptId', ], 'modifiedTime' => [ 'shape' => 'Timestamp', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateAIPromptVersionResponse' => [ 'type' => 'structure', 'members' => [ 'aiPrompt' => [ 'shape' => 'AIPromptData', ], 'versionNumber' => [ 'shape' => 'Version', ], ], ], 'CreateAssistantAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'associationType', 'association', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'associationType' => [ 'shape' => 'AssociationType', ], 'association' => [ 'shape' => 'AssistantAssociationInputData', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateAssistantAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'assistantAssociation' => [ 'shape' => 'AssistantAssociationData', ], ], ], 'CreateAssistantRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'type', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'name' => [ 'shape' => 'Name', ], 'type' => [ 'shape' => 'AssistantType', ], 'description' => [ 'shape' => 'Description', ], 'tags' => [ 'shape' => 'Tags', ], 'serverSideEncryptionConfiguration' => [ 'shape' => 'ServerSideEncryptionConfiguration', ], ], ], 'CreateAssistantResponse' => [ 'type' => 'structure', 'members' => [ 'assistant' => [ 'shape' => 'AssistantData', ], ], ], 'CreateContentAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'contentId', 'associationType', 'association', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'contentId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'contentId', ], 'associationType' => [ 'shape' => 'ContentAssociationType', ], 'association' => [ 'shape' => 'ContentAssociationContents', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateContentAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'contentAssociation' => [ 'shape' => 'ContentAssociationData', ], ], ], 'CreateContentRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'name', 'uploadId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'name' => [ 'shape' => 'Name', ], 'title' => [ 'shape' => 'ContentTitle', ], 'overrideLinkOutUri' => [ 'shape' => 'Uri', ], 'metadata' => [ 'shape' => 'ContentMetadata', ], 'uploadId' => [ 'shape' => 'UploadId', ], 'clientToken' => [ 'shape' => 'NonEmptyString', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateContentResponse' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'ContentData', ], ], ], 'CreateKnowledgeBaseRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'knowledgeBaseType', ], 'members' => [ 'clientToken' => [ 'shape' => 'NonEmptyString', 'idempotencyToken' => true, ], 'name' => [ 'shape' => 'Name', ], 'knowledgeBaseType' => [ 'shape' => 'KnowledgeBaseType', ], 'sourceConfiguration' => [ 'shape' => 'SourceConfiguration', ], 'renderingConfiguration' => [ 'shape' => 'RenderingConfiguration', ], 'vectorIngestionConfiguration' => [ 'shape' => 'VectorIngestionConfiguration', ], 'serverSideEncryptionConfiguration' => [ 'shape' => 'ServerSideEncryptionConfiguration', ], 'description' => [ 'shape' => 'Description', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateKnowledgeBaseResponse' => [ 'type' => 'structure', 'members' => [ 'knowledgeBase' => [ 'shape' => 'KnowledgeBaseData', ], ], ], 'CreateMessageTemplateAttachmentRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'messageTemplateId', 'contentDisposition', 'name', 'body', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'messageTemplateId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'messageTemplateId', ], 'contentDisposition' => [ 'shape' => 'ContentDisposition', ], 'name' => [ 'shape' => 'AttachmentFileName', ], 'body' => [ 'shape' => 'NonEmptyUnlimitedString', ], 'clientToken' => [ 'shape' => 'ClientToken', ], ], ], 'CreateMessageTemplateAttachmentResponse' => [ 'type' => 'structure', 'members' => [ 'attachment' => [ 'shape' => 'MessageTemplateAttachment', ], ], ], 'CreateMessageTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'name', 'content', 'channelSubtype', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'name' => [ 'shape' => 'Name', ], 'content' => [ 'shape' => 'MessageTemplateContentProvider', ], 'description' => [ 'shape' => 'Description', ], 'channelSubtype' => [ 'shape' => 'ChannelSubtype', ], 'language' => [ 'shape' => 'LanguageCode', ], 'defaultAttributes' => [ 'shape' => 'MessageTemplateAttributes', ], 'groupingConfiguration' => [ 'shape' => 'GroupingConfiguration', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateMessageTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'messageTemplate' => [ 'shape' => 'MessageTemplateData', ], ], ], 'CreateMessageTemplateVersionRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'messageTemplateId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'messageTemplateId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'messageTemplateId', ], 'messageTemplateContentSha256' => [ 'shape' => 'MessageTemplateContentSha256', ], ], ], 'CreateMessageTemplateVersionResponse' => [ 'type' => 'structure', 'members' => [ 'messageTemplate' => [ 'shape' => 'ExtendedMessageTemplateData', ], ], ], 'CreateQuickResponseRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'name', 'content', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'name' => [ 'shape' => 'QuickResponseName', ], 'content' => [ 'shape' => 'QuickResponseDataProvider', ], 'contentType' => [ 'shape' => 'QuickResponseType', ], 'groupingConfiguration' => [ 'shape' => 'GroupingConfiguration', ], 'description' => [ 'shape' => 'QuickResponseDescription', ], 'shortcutKey' => [ 'shape' => 'ShortCutKey', ], 'isActive' => [ 'shape' => 'Boolean', ], 'channels' => [ 'shape' => 'Channels', ], 'language' => [ 'shape' => 'LanguageCode', ], 'clientToken' => [ 'shape' => 'NonEmptyString', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateQuickResponseResponse' => [ 'type' => 'structure', 'members' => [ 'quickResponse' => [ 'shape' => 'QuickResponseData', ], ], ], 'CreateSessionRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'name', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'tags' => [ 'shape' => 'Tags', ], 'tagFilter' => [ 'shape' => 'TagFilter', ], 'aiAgentConfiguration' => [ 'shape' => 'AIAgentConfigurationMap', ], ], ], 'CreateSessionResponse' => [ 'type' => 'structure', 'members' => [ 'session' => [ 'shape' => 'SessionData', ], ], ], 'CustomAttributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'MessageTemplateAttributeKey', ], 'value' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'sensitive' => true, ], 'CustomerProfileAttributes' => [ 'type' => 'structure', 'members' => [ 'profileId' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'profileARN' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'firstName' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'middleName' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'lastName' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'accountNumber' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'emailAddress' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'phoneNumber' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'additionalInformation' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'partyType' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'businessName' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'birthDate' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'gender' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'mobilePhoneNumber' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'homePhoneNumber' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'businessPhoneNumber' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'businessEmailAddress' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'address1' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'address2' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'address3' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'address4' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'city' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'county' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'country' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'postalCode' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'province' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'state' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'shippingAddress1' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'shippingAddress2' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'shippingAddress3' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'shippingAddress4' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'shippingCity' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'shippingCounty' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'shippingCountry' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'shippingPostalCode' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'shippingProvince' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'shippingState' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'mailingAddress1' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'mailingAddress2' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'mailingAddress3' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'mailingAddress4' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'mailingCity' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'mailingCounty' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'mailingCountry' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'mailingPostalCode' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'mailingProvince' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'mailingState' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'billingAddress1' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'billingAddress2' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'billingAddress3' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'billingAddress4' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'billingCity' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'billingCounty' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'billingCountry' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'billingPostalCode' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'billingProvince' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'billingState' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'custom' => [ 'shape' => 'CustomAttributes', ], ], ], 'DataDetails' => [ 'type' => 'structure', 'members' => [ 'contentData' => [ 'shape' => 'ContentDataDetails', ], 'generativeData' => [ 'shape' => 'GenerativeDataDetails', ], 'intentDetectedData' => [ 'shape' => 'IntentDetectedDataDetails', ], 'sourceContentData' => [ 'shape' => 'SourceContentDataDetails', ], 'generativeChunkData' => [ 'shape' => 'GenerativeChunkDataDetails', ], ], 'union' => true, ], 'DataReference' => [ 'type' => 'structure', 'members' => [ 'contentReference' => [ 'shape' => 'ContentReference', ], 'generativeReference' => [ 'shape' => 'GenerativeReference', ], ], 'union' => true, ], 'DataSummary' => [ 'type' => 'structure', 'required' => [ 'reference', 'details', ], 'members' => [ 'reference' => [ 'shape' => 'DataReference', ], 'details' => [ 'shape' => 'DataDetails', ], ], ], 'DataSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSummary', ], ], 'DeactivateMessageTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'messageTemplateId', 'versionNumber', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'messageTemplateId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'messageTemplateId', ], 'versionNumber' => [ 'shape' => 'Version', ], ], ], 'DeactivateMessageTemplateResponse' => [ 'type' => 'structure', 'required' => [ 'messageTemplateArn', 'messageTemplateId', 'versionNumber', ], 'members' => [ 'messageTemplateArn' => [ 'shape' => 'ArnWithQualifier', ], 'messageTemplateId' => [ 'shape' => 'Uuid', ], 'versionNumber' => [ 'shape' => 'Version', ], ], ], 'DeleteAIAgentRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'aiAgentId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'aiAgentId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'aiAgentId', ], ], ], 'DeleteAIAgentResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAIAgentVersionRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'aiAgentId', 'versionNumber', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'aiAgentId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'aiAgentId', ], 'versionNumber' => [ 'shape' => 'Version', 'location' => 'uri', 'locationName' => 'versionNumber', ], ], ], 'DeleteAIAgentVersionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAIGuardrailRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'aiGuardrailId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'aiGuardrailId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'aiGuardrailId', ], ], ], 'DeleteAIGuardrailResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAIGuardrailVersionRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'aiGuardrailId', 'versionNumber', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'aiGuardrailId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'aiGuardrailId', ], 'versionNumber' => [ 'shape' => 'Version', 'location' => 'uri', 'locationName' => 'versionNumber', ], ], ], 'DeleteAIGuardrailVersionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAIPromptRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'aiPromptId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'aiPromptId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'aiPromptId', ], ], ], 'DeleteAIPromptResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAIPromptVersionRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'aiPromptId', 'versionNumber', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'aiPromptId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'aiPromptId', ], 'versionNumber' => [ 'shape' => 'Version', 'location' => 'uri', 'locationName' => 'versionNumber', ], ], ], 'DeleteAIPromptVersionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAssistantAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'assistantAssociationId', 'assistantId', ], 'members' => [ 'assistantAssociationId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantAssociationId', ], 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], ], ], 'DeleteAssistantAssociationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAssistantRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], ], ], 'DeleteAssistantResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteContentAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'contentId', 'contentAssociationId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'contentId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'contentId', ], 'contentAssociationId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'contentAssociationId', ], ], ], 'DeleteContentAssociationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteContentRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'contentId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'contentId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'contentId', ], ], ], 'DeleteContentResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'importJobId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'importJobId' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'importJobId', ], ], ], 'DeleteImportJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteKnowledgeBaseRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'DeleteKnowledgeBaseResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteMessageTemplateAttachmentRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'messageTemplateId', 'attachmentId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'messageTemplateId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'messageTemplateId', ], 'attachmentId' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'attachmentId', ], ], ], 'DeleteMessageTemplateAttachmentResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteMessageTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'messageTemplateId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'messageTemplateId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'messageTemplateId', ], ], ], 'DeleteMessageTemplateResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteQuickResponseRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'quickResponseId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'quickResponseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'quickResponseId', ], ], ], 'DeleteQuickResponseResponse' => [ 'type' => 'structure', 'members' => [], ], 'Description' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\s_.,-]+.*', ], 'Document' => [ 'type' => 'structure', 'required' => [ 'contentReference', ], 'members' => [ 'contentReference' => [ 'shape' => 'ContentReference', ], 'title' => [ 'shape' => 'DocumentText', ], 'excerpt' => [ 'shape' => 'DocumentText', ], ], ], 'DocumentText' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'SensitiveString', ], 'highlights' => [ 'shape' => 'Highlights', ], ], ], 'EmailHeader' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'EmailHeaderKey', ], 'value' => [ 'shape' => 'EmailHeaderValue', ], ], ], 'EmailHeaderKey' => [ 'type' => 'string', 'max' => 126, 'min' => 1, 'pattern' => '[!-9;-@A-~]+', ], 'EmailHeaderValue' => [ 'type' => 'string', 'max' => 870, 'min' => 1, 'pattern' => '.*[ -~]*.*', 'sensitive' => true, ], 'EmailHeaders' => [ 'type' => 'list', 'member' => [ 'shape' => 'EmailHeader', ], 'max' => 15, 'min' => 0, ], 'EmailMessageTemplateContent' => [ 'type' => 'structure', 'members' => [ 'subject' => [ 'shape' => 'NonEmptyUnlimitedString', ], 'body' => [ 'shape' => 'EmailMessageTemplateContentBody', ], 'headers' => [ 'shape' => 'EmailHeaders', ], ], ], 'EmailMessageTemplateContentBody' => [ 'type' => 'structure', 'members' => [ 'plainText' => [ 'shape' => 'MessageTemplateBodyContentProvider', ], 'html' => [ 'shape' => 'MessageTemplateBodyContentProvider', ], ], ], 'ExtendedMessageTemplateData' => [ 'type' => 'structure', 'required' => [ 'messageTemplateArn', 'messageTemplateId', 'knowledgeBaseArn', 'knowledgeBaseId', 'name', 'channelSubtype', 'createdTime', 'lastModifiedTime', 'lastModifiedBy', 'content', 'messageTemplateContentSha256', ], 'members' => [ 'messageTemplateArn' => [ 'shape' => 'ArnWithQualifier', ], 'messageTemplateId' => [ 'shape' => 'Uuid', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'Name', ], 'channelSubtype' => [ 'shape' => 'ChannelSubtype', ], 'createdTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'lastModifiedTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'lastModifiedBy' => [ 'shape' => 'GenericArn', ], 'content' => [ 'shape' => 'MessageTemplateContentProvider', ], 'description' => [ 'shape' => 'Description', ], 'language' => [ 'shape' => 'LanguageCode', ], 'groupingConfiguration' => [ 'shape' => 'GroupingConfiguration', ], 'defaultAttributes' => [ 'shape' => 'MessageTemplateAttributes', ], 'attributeTypes' => [ 'shape' => 'MessageTemplateAttributeTypeList', ], 'attachments' => [ 'shape' => 'MessageTemplateAttachmentList', ], 'isActive' => [ 'shape' => 'Boolean', ], 'versionNumber' => [ 'shape' => 'Version', ], 'messageTemplateContentSha256' => [ 'shape' => 'MessageTemplateContentSha256', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'ExternalSource' => [ 'type' => 'string', 'enum' => [ 'AMAZON_CONNECT', ], ], 'ExternalSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'source', 'configuration', ], 'members' => [ 'source' => [ 'shape' => 'ExternalSource', ], 'configuration' => [ 'shape' => 'Configuration', ], ], ], 'FailureReason' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'Filter' => [ 'type' => 'structure', 'required' => [ 'field', 'operator', 'value', ], 'members' => [ 'field' => [ 'shape' => 'FilterField', ], 'operator' => [ 'shape' => 'FilterOperator', ], 'value' => [ 'shape' => 'NonEmptyString', ], ], ], 'FilterField' => [ 'type' => 'string', 'enum' => [ 'NAME', ], ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], ], 'FilterOperator' => [ 'type' => 'string', 'enum' => [ 'EQUALS', ], ], 'FixedSizeChunkingConfiguration' => [ 'type' => 'structure', 'required' => [ 'maxTokens', 'overlapPercentage', ], 'members' => [ 'maxTokens' => [ 'shape' => 'FixedSizeChunkingConfigurationMaxTokensInteger', ], 'overlapPercentage' => [ 'shape' => 'FixedSizeChunkingConfigurationOverlapPercentageInteger', ], ], ], 'FixedSizeChunkingConfigurationMaxTokensInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'FixedSizeChunkingConfigurationOverlapPercentageInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 99, 'min' => 1, ], 'GenerativeChunkDataDetails' => [ 'type' => 'structure', 'members' => [ 'completion' => [ 'shape' => 'SensitiveString', ], 'references' => [ 'shape' => 'DataSummaryList', ], 'nextChunkToken' => [ 'shape' => 'NextToken', ], ], ], 'GenerativeContentFeedbackData' => [ 'type' => 'structure', 'required' => [ 'relevance', ], 'members' => [ 'relevance' => [ 'shape' => 'Relevance', ], ], ], 'GenerativeDataDetails' => [ 'type' => 'structure', 'required' => [ 'completion', 'references', 'rankingData', ], 'members' => [ 'completion' => [ 'shape' => 'SensitiveString', ], 'references' => [ 'shape' => 'DataSummaryList', ], 'rankingData' => [ 'shape' => 'RankingData', ], ], ], 'GenerativeReference' => [ 'type' => 'structure', 'members' => [ 'modelId' => [ 'shape' => 'LlmModelId', ], 'generationId' => [ 'shape' => 'Uuid', ], ], ], 'GenericArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:[a-z-]+?:[a-z-]+?:[a-z0-9-]*?:([0-9]{12})?:[a-zA-Z0-9-:/]+', ], 'GetAIAgentRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'aiAgentId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'aiAgentId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'aiAgentId', ], ], ], 'GetAIAgentResponse' => [ 'type' => 'structure', 'members' => [ 'aiAgent' => [ 'shape' => 'AIAgentData', ], 'versionNumber' => [ 'shape' => 'Version', ], ], ], 'GetAIGuardrailRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'aiGuardrailId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'aiGuardrailId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'aiGuardrailId', ], ], ], 'GetAIGuardrailResponse' => [ 'type' => 'structure', 'members' => [ 'aiGuardrail' => [ 'shape' => 'AIGuardrailData', ], 'versionNumber' => [ 'shape' => 'Version', ], ], ], 'GetAIPromptRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'aiPromptId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'aiPromptId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'aiPromptId', ], ], ], 'GetAIPromptResponse' => [ 'type' => 'structure', 'members' => [ 'aiPrompt' => [ 'shape' => 'AIPromptData', ], 'versionNumber' => [ 'shape' => 'Version', ], ], ], 'GetAssistantAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'assistantAssociationId', 'assistantId', ], 'members' => [ 'assistantAssociationId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantAssociationId', ], 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], ], ], 'GetAssistantAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'assistantAssociation' => [ 'shape' => 'AssistantAssociationData', ], ], ], 'GetAssistantRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], ], ], 'GetAssistantResponse' => [ 'type' => 'structure', 'members' => [ 'assistant' => [ 'shape' => 'AssistantData', ], ], ], 'GetContentAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'contentId', 'contentAssociationId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'contentId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'contentId', ], 'contentAssociationId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'contentAssociationId', ], ], ], 'GetContentAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'contentAssociation' => [ 'shape' => 'ContentAssociationData', ], ], ], 'GetContentRequest' => [ 'type' => 'structure', 'required' => [ 'contentId', 'knowledgeBaseId', ], 'members' => [ 'contentId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'contentId', ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'GetContentResponse' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'ContentData', ], ], ], 'GetContentSummaryRequest' => [ 'type' => 'structure', 'required' => [ 'contentId', 'knowledgeBaseId', ], 'members' => [ 'contentId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'contentId', ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'GetContentSummaryResponse' => [ 'type' => 'structure', 'members' => [ 'contentSummary' => [ 'shape' => 'ContentSummary', ], ], ], 'GetImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'importJobId', 'knowledgeBaseId', ], 'members' => [ 'importJobId' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'importJobId', ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'GetImportJobResponse' => [ 'type' => 'structure', 'members' => [ 'importJob' => [ 'shape' => 'ImportJobData', ], ], ], 'GetKnowledgeBaseRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'GetKnowledgeBaseResponse' => [ 'type' => 'structure', 'members' => [ 'knowledgeBase' => [ 'shape' => 'KnowledgeBaseData', ], ], ], 'GetMessageTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'messageTemplateId', 'knowledgeBaseId', ], 'members' => [ 'messageTemplateId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'messageTemplateId', ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'GetMessageTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'messageTemplate' => [ 'shape' => 'ExtendedMessageTemplateData', ], ], ], 'GetNextMessageRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'sessionId', 'nextMessageToken', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'sessionId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'sessionId', ], 'nextMessageToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextMessageToken', ], ], ], 'GetNextMessageResponse' => [ 'type' => 'structure', 'required' => [ 'type', 'response', 'requestMessageId', 'conversationState', ], 'members' => [ 'type' => [ 'shape' => 'MessageType', ], 'response' => [ 'shape' => 'MessageOutput', ], 'requestMessageId' => [ 'shape' => 'Uuid', ], 'conversationState' => [ 'shape' => 'ConversationState', ], 'nextMessageToken' => [ 'shape' => 'NextToken', ], 'conversationSessionData' => [ 'shape' => 'RuntimeSessionDataList', ], ], ], 'GetQuickResponseRequest' => [ 'type' => 'structure', 'required' => [ 'quickResponseId', 'knowledgeBaseId', ], 'members' => [ 'quickResponseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'quickResponseId', ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'GetQuickResponseResponse' => [ 'type' => 'structure', 'members' => [ 'quickResponse' => [ 'shape' => 'QuickResponseData', ], ], ], 'GetRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'sessionId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'sessionId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'sessionId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'waitTimeSeconds' => [ 'shape' => 'WaitTimeSeconds', 'location' => 'querystring', 'locationName' => 'waitTimeSeconds', ], 'nextChunkToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextChunkToken', ], ], ], 'GetRecommendationsResponse' => [ 'type' => 'structure', 'required' => [ 'recommendations', ], 'members' => [ 'recommendations' => [ 'shape' => 'RecommendationList', ], 'triggers' => [ 'shape' => 'RecommendationTriggerList', ], ], ], 'GetSessionRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'sessionId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'sessionId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'sessionId', ], ], ], 'GetSessionResponse' => [ 'type' => 'structure', 'members' => [ 'session' => [ 'shape' => 'SessionData', ], ], ], 'GroupingConfiguration' => [ 'type' => 'structure', 'members' => [ 'criteria' => [ 'shape' => 'GroupingCriteria', ], 'values' => [ 'shape' => 'GroupingValues', ], ], ], 'GroupingCriteria' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'sensitive' => true, ], 'GroupingValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'sensitive' => true, ], 'GroupingValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupingValue', ], ], 'GuardrailContentFilterConfig' => [ 'type' => 'structure', 'required' => [ 'type', 'inputStrength', 'outputStrength', ], 'members' => [ 'type' => [ 'shape' => 'GuardrailContentFilterType', ], 'inputStrength' => [ 'shape' => 'GuardrailFilterStrength', ], 'outputStrength' => [ 'shape' => 'GuardrailFilterStrength', ], ], ], 'GuardrailContentFilterType' => [ 'type' => 'string', 'enum' => [ 'SEXUAL', 'VIOLENCE', 'HATE', 'INSULTS', 'MISCONDUCT', 'PROMPT_ATTACK', ], 'sensitive' => true, ], 'GuardrailContentFiltersConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailContentFilterConfig', ], 'max' => 6, 'min' => 1, ], 'GuardrailContextualGroundingFilterConfig' => [ 'type' => 'structure', 'required' => [ 'type', 'threshold', ], 'members' => [ 'type' => [ 'shape' => 'GuardrailContextualGroundingFilterType', ], 'threshold' => [ 'shape' => 'GuardrailContextualGroundingFilterThreshold', ], ], ], 'GuardrailContextualGroundingFilterThreshold' => [ 'type' => 'double', 'min' => 0, 'sensitive' => true, ], 'GuardrailContextualGroundingFilterType' => [ 'type' => 'string', 'enum' => [ 'GROUNDING', 'RELEVANCE', ], 'sensitive' => true, ], 'GuardrailContextualGroundingFiltersConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailContextualGroundingFilterConfig', ], 'min' => 1, ], 'GuardrailFilterStrength' => [ 'type' => 'string', 'enum' => [ 'NONE', 'LOW', 'MEDIUM', 'HIGH', ], 'sensitive' => true, ], 'GuardrailManagedWordListsConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailManagedWordsConfig', ], ], 'GuardrailManagedWordsConfig' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'GuardrailManagedWordsType', ], ], ], 'GuardrailManagedWordsType' => [ 'type' => 'string', 'enum' => [ 'PROFANITY', ], 'sensitive' => true, ], 'GuardrailPiiEntitiesConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailPiiEntityConfig', ], 'min' => 1, ], 'GuardrailPiiEntityConfig' => [ 'type' => 'structure', 'required' => [ 'type', 'action', ], 'members' => [ 'type' => [ 'shape' => 'GuardrailPiiEntityType', ], 'action' => [ 'shape' => 'GuardrailSensitiveInformationAction', ], ], ], 'GuardrailPiiEntityType' => [ 'type' => 'string', 'enum' => [ 'ADDRESS', 'AGE', 'AWS_ACCESS_KEY', 'AWS_SECRET_KEY', 'CA_HEALTH_NUMBER', 'CA_SOCIAL_INSURANCE_NUMBER', 'CREDIT_DEBIT_CARD_CVV', 'CREDIT_DEBIT_CARD_EXPIRY', 'CREDIT_DEBIT_CARD_NUMBER', 'DRIVER_ID', 'EMAIL', 'INTERNATIONAL_BANK_ACCOUNT_NUMBER', 'IP_ADDRESS', 'LICENSE_PLATE', 'MAC_ADDRESS', 'NAME', 'PASSWORD', 'PHONE', 'PIN', 'SWIFT_CODE', 'UK_NATIONAL_HEALTH_SERVICE_NUMBER', 'UK_NATIONAL_INSURANCE_NUMBER', 'UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER', 'URL', 'USERNAME', 'US_BANK_ACCOUNT_NUMBER', 'US_BANK_ROUTING_NUMBER', 'US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER', 'US_PASSPORT_NUMBER', 'US_SOCIAL_SECURITY_NUMBER', 'VEHICLE_IDENTIFICATION_NUMBER', ], 'sensitive' => true, ], 'GuardrailRegexConfig' => [ 'type' => 'structure', 'required' => [ 'name', 'pattern', 'action', ], 'members' => [ 'name' => [ 'shape' => 'GuardrailRegexName', ], 'description' => [ 'shape' => 'GuardrailRegexDescription', ], 'pattern' => [ 'shape' => 'GuardrailRegexPattern', ], 'action' => [ 'shape' => 'GuardrailSensitiveInformationAction', ], ], ], 'GuardrailRegexDescription' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'sensitive' => true, ], 'GuardrailRegexName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'sensitive' => true, ], 'GuardrailRegexPattern' => [ 'type' => 'string', 'min' => 1, 'sensitive' => true, ], 'GuardrailRegexesConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailRegexConfig', ], 'min' => 1, ], 'GuardrailSensitiveInformationAction' => [ 'type' => 'string', 'enum' => [ 'BLOCK', 'ANONYMIZE', ], 'sensitive' => true, ], 'GuardrailTopicConfig' => [ 'type' => 'structure', 'required' => [ 'name', 'definition', 'type', ], 'members' => [ 'name' => [ 'shape' => 'GuardrailTopicName', ], 'definition' => [ 'shape' => 'GuardrailTopicDefinition', ], 'examples' => [ 'shape' => 'GuardrailTopicExamples', ], 'type' => [ 'shape' => 'GuardrailTopicType', ], ], ], 'GuardrailTopicDefinition' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'sensitive' => true, ], 'GuardrailTopicExample' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'sensitive' => true, ], 'GuardrailTopicExamples' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailTopicExample', ], 'min' => 0, ], 'GuardrailTopicName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[0-9a-zA-Z-_ !?.]+', 'sensitive' => true, ], 'GuardrailTopicType' => [ 'type' => 'string', 'enum' => [ 'DENY', ], 'sensitive' => true, ], 'GuardrailTopicsConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailTopicConfig', ], 'min' => 1, ], 'GuardrailWordConfig' => [ 'type' => 'structure', 'required' => [ 'text', ], 'members' => [ 'text' => [ 'shape' => 'GuardrailWordText', ], ], ], 'GuardrailWordText' => [ 'type' => 'string', 'min' => 1, 'sensitive' => true, ], 'GuardrailWordsConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailWordConfig', ], 'min' => 1, ], 'Headers' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'NonEmptyString', ], ], 'HierarchicalChunkingConfiguration' => [ 'type' => 'structure', 'required' => [ 'levelConfigurations', 'overlapTokens', ], 'members' => [ 'levelConfigurations' => [ 'shape' => 'HierarchicalChunkingLevelConfigurations', ], 'overlapTokens' => [ 'shape' => 'HierarchicalChunkingConfigurationOverlapTokensInteger', ], ], ], 'HierarchicalChunkingConfigurationOverlapTokensInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'HierarchicalChunkingLevelConfiguration' => [ 'type' => 'structure', 'required' => [ 'maxTokens', ], 'members' => [ 'maxTokens' => [ 'shape' => 'HierarchicalChunkingLevelConfigurationMaxTokensInteger', ], ], ], 'HierarchicalChunkingLevelConfigurationMaxTokensInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 8192, 'min' => 1, ], 'HierarchicalChunkingLevelConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'HierarchicalChunkingLevelConfiguration', ], 'max' => 2, 'min' => 2, ], 'Highlight' => [ 'type' => 'structure', 'members' => [ 'beginOffsetInclusive' => [ 'shape' => 'HighlightOffset', ], 'endOffsetExclusive' => [ 'shape' => 'HighlightOffset', ], ], ], 'HighlightOffset' => [ 'type' => 'integer', ], 'Highlights' => [ 'type' => 'list', 'member' => [ 'shape' => 'Highlight', ], ], 'ImportJobData' => [ 'type' => 'structure', 'required' => [ 'importJobId', 'knowledgeBaseId', 'uploadId', 'knowledgeBaseArn', 'importJobType', 'status', 'url', 'urlExpiry', 'createdTime', 'lastModifiedTime', ], 'members' => [ 'importJobId' => [ 'shape' => 'Uuid', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'uploadId' => [ 'shape' => 'UploadId', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'importJobType' => [ 'shape' => 'ImportJobType', ], 'status' => [ 'shape' => 'ImportJobStatus', ], 'url' => [ 'shape' => 'Url', ], 'failedRecordReport' => [ 'shape' => 'Url', ], 'urlExpiry' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'createdTime' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'lastModifiedTime' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'metadata' => [ 'shape' => 'ContentMetadata', ], 'externalSourceConfiguration' => [ 'shape' => 'ExternalSourceConfiguration', ], ], ], 'ImportJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImportJobSummary', ], ], 'ImportJobStatus' => [ 'type' => 'string', 'enum' => [ 'START_IN_PROGRESS', 'FAILED', 'COMPLETE', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', 'DELETED', ], ], 'ImportJobSummary' => [ 'type' => 'structure', 'required' => [ 'importJobId', 'knowledgeBaseId', 'uploadId', 'knowledgeBaseArn', 'importJobType', 'status', 'createdTime', 'lastModifiedTime', ], 'members' => [ 'importJobId' => [ 'shape' => 'Uuid', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'uploadId' => [ 'shape' => 'UploadId', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'importJobType' => [ 'shape' => 'ImportJobType', ], 'status' => [ 'shape' => 'ImportJobStatus', ], 'createdTime' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'lastModifiedTime' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'metadata' => [ 'shape' => 'ContentMetadata', ], 'externalSourceConfiguration' => [ 'shape' => 'ExternalSourceConfiguration', ], ], ], 'ImportJobType' => [ 'type' => 'string', 'enum' => [ 'QUICK_RESPONSES', ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'IntentDetectedDataDetails' => [ 'type' => 'structure', 'required' => [ 'intent', 'intentId', ], 'members' => [ 'intent' => [ 'shape' => 'SensitiveString', ], 'intentId' => [ 'shape' => 'Uuid', ], ], ], 'IntentInputData' => [ 'type' => 'structure', 'required' => [ 'intentId', ], 'members' => [ 'intentId' => [ 'shape' => 'Uuid', ], ], ], 'KnowledgeBaseAssociationConfigurationData' => [ 'type' => 'structure', 'members' => [ 'contentTagFilter' => [ 'shape' => 'TagFilter', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'overrideKnowledgeBaseSearchType' => [ 'shape' => 'KnowledgeBaseSearchType', ], ], ], 'KnowledgeBaseAssociationData' => [ 'type' => 'structure', 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], ], ], 'KnowledgeBaseData' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'knowledgeBaseArn', 'name', 'knowledgeBaseType', 'status', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'Name', ], 'knowledgeBaseType' => [ 'shape' => 'KnowledgeBaseType', ], 'status' => [ 'shape' => 'KnowledgeBaseStatus', ], 'lastContentModificationTime' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'vectorIngestionConfiguration' => [ 'shape' => 'VectorIngestionConfiguration', ], 'sourceConfiguration' => [ 'shape' => 'SourceConfiguration', ], 'renderingConfiguration' => [ 'shape' => 'RenderingConfiguration', ], 'serverSideEncryptionConfiguration' => [ 'shape' => 'ServerSideEncryptionConfiguration', ], 'description' => [ 'shape' => 'Description', ], 'tags' => [ 'shape' => 'Tags', ], 'ingestionStatus' => [ 'shape' => 'SyncStatus', ], 'ingestionFailureReasons' => [ 'shape' => 'FailureReason', ], ], ], 'KnowledgeBaseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KnowledgeBaseSummary', ], ], 'KnowledgeBaseSearchType' => [ 'type' => 'string', 'enum' => [ 'HYBRID', 'SEMANTIC', ], ], 'KnowledgeBaseStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'ACTIVE', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', 'DELETED', ], ], 'KnowledgeBaseSummary' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'knowledgeBaseArn', 'name', 'knowledgeBaseType', 'status', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'Name', ], 'knowledgeBaseType' => [ 'shape' => 'KnowledgeBaseType', ], 'status' => [ 'shape' => 'KnowledgeBaseStatus', ], 'sourceConfiguration' => [ 'shape' => 'SourceConfiguration', ], 'vectorIngestionConfiguration' => [ 'shape' => 'VectorIngestionConfiguration', ], 'renderingConfiguration' => [ 'shape' => 'RenderingConfiguration', ], 'serverSideEncryptionConfiguration' => [ 'shape' => 'ServerSideEncryptionConfiguration', ], 'description' => [ 'shape' => 'Description', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'KnowledgeBaseType' => [ 'type' => 'string', 'enum' => [ 'EXTERNAL', 'CUSTOM', 'QUICK_RESPONSES', 'MESSAGE_TEMPLATES', 'MANAGED', ], ], 'LanguageCode' => [ 'type' => 'string', 'max' => 5, 'min' => 2, ], 'ListAIAgentVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'aiAgentId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'aiAgentId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'aiAgentId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'origin' => [ 'shape' => 'Origin', 'location' => 'querystring', 'locationName' => 'origin', ], ], ], 'ListAIAgentVersionsResponse' => [ 'type' => 'structure', 'required' => [ 'aiAgentVersionSummaries', ], 'members' => [ 'aiAgentVersionSummaries' => [ 'shape' => 'AIAgentVersionSummariesList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAIAgentsRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'origin' => [ 'shape' => 'Origin', 'location' => 'querystring', 'locationName' => 'origin', ], ], ], 'ListAIAgentsResponse' => [ 'type' => 'structure', 'required' => [ 'aiAgentSummaries', ], 'members' => [ 'aiAgentSummaries' => [ 'shape' => 'AIAgentSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAIGuardrailVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'aiGuardrailId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'aiGuardrailId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'aiGuardrailId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAIGuardrailVersionsResponse' => [ 'type' => 'structure', 'required' => [ 'aiGuardrailVersionSummaries', ], 'members' => [ 'aiGuardrailVersionSummaries' => [ 'shape' => 'AIGuardrailVersionSummariesList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAIGuardrailsRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAIGuardrailsResponse' => [ 'type' => 'structure', 'required' => [ 'aiGuardrailSummaries', ], 'members' => [ 'aiGuardrailSummaries' => [ 'shape' => 'AIGuardrailSummariesList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAIPromptVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'aiPromptId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'aiPromptId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'aiPromptId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'origin' => [ 'shape' => 'Origin', 'location' => 'querystring', 'locationName' => 'origin', ], ], ], 'ListAIPromptVersionsResponse' => [ 'type' => 'structure', 'required' => [ 'aiPromptVersionSummaries', ], 'members' => [ 'aiPromptVersionSummaries' => [ 'shape' => 'AIPromptVersionSummariesList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAIPromptsRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'origin' => [ 'shape' => 'Origin', 'location' => 'querystring', 'locationName' => 'origin', ], ], ], 'ListAIPromptsResponse' => [ 'type' => 'structure', 'required' => [ 'aiPromptSummaries', ], 'members' => [ 'aiPromptSummaries' => [ 'shape' => 'AIPromptSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAssistantAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], ], ], 'ListAssistantAssociationsResponse' => [ 'type' => 'structure', 'required' => [ 'assistantAssociationSummaries', ], 'members' => [ 'assistantAssociationSummaries' => [ 'shape' => 'AssistantAssociationSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAssistantsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAssistantsResponse' => [ 'type' => 'structure', 'required' => [ 'assistantSummaries', ], 'members' => [ 'assistantSummaries' => [ 'shape' => 'AssistantList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListContentAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'contentId', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'contentId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'contentId', ], ], ], 'ListContentAssociationsResponse' => [ 'type' => 'structure', 'required' => [ 'contentAssociationSummaries', ], 'members' => [ 'contentAssociationSummaries' => [ 'shape' => 'ContentAssociationSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListContentsRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'ListContentsResponse' => [ 'type' => 'structure', 'required' => [ 'contentSummaries', ], 'members' => [ 'contentSummaries' => [ 'shape' => 'ContentSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListImportJobsRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', ], 'members' => [ 'nextToken' => [ 'shape' => 'NonEmptyString', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'ListImportJobsResponse' => [ 'type' => 'structure', 'required' => [ 'importJobSummaries', ], 'members' => [ 'importJobSummaries' => [ 'shape' => 'ImportJobList', ], 'nextToken' => [ 'shape' => 'NonEmptyString', ], ], ], 'ListKnowledgeBasesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NonEmptyString', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListKnowledgeBasesResponse' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseSummaries', ], 'members' => [ 'knowledgeBaseSummaries' => [ 'shape' => 'KnowledgeBaseList', ], 'nextToken' => [ 'shape' => 'NonEmptyString', ], ], ], 'ListMessageTemplateVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'messageTemplateId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'messageTemplateId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'messageTemplateId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListMessageTemplateVersionsResponse' => [ 'type' => 'structure', 'required' => [ 'messageTemplateVersionSummaries', ], 'members' => [ 'messageTemplateVersionSummaries' => [ 'shape' => 'MessageTemplateVersionSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMessageTemplatesRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'ListMessageTemplatesResponse' => [ 'type' => 'structure', 'required' => [ 'messageTemplateSummaries', ], 'members' => [ 'messageTemplateSummaries' => [ 'shape' => 'MessageTemplateSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMessagesRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'sessionId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'sessionId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'sessionId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListMessagesResponse' => [ 'type' => 'structure', 'required' => [ 'messages', ], 'members' => [ 'messages' => [ 'shape' => 'MessageList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListQuickResponsesRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', ], 'members' => [ 'nextToken' => [ 'shape' => 'NonEmptyString', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'ListQuickResponsesResponse' => [ 'type' => 'structure', 'required' => [ 'quickResponseSummaries', ], 'members' => [ 'quickResponseSummaries' => [ 'shape' => 'QuickResponseSummaryList', ], 'nextToken' => [ 'shape' => 'NonEmptyString', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'Tags', ], ], ], 'LlmModelId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'ManagedSourceConfiguration' => [ 'type' => 'structure', 'members' => [ 'webCrawlerConfiguration' => [ 'shape' => 'WebCrawlerConfiguration', ], ], 'union' => true, ], 'ManualSearchAIAgentConfiguration' => [ 'type' => 'structure', 'members' => [ 'answerGenerationAIPromptId' => [ 'shape' => 'UuidWithQualifier', ], 'answerGenerationAIGuardrailId' => [ 'shape' => 'UuidWithQualifier', ], 'associationConfigurations' => [ 'shape' => 'AssociationConfigurationList', ], 'locale' => [ 'shape' => 'NonEmptyString', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MessageConfiguration' => [ 'type' => 'structure', 'members' => [ 'generateFillerMessage' => [ 'shape' => 'Boolean', ], ], ], 'MessageData' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'TextMessage', ], ], 'union' => true, ], 'MessageInput' => [ 'type' => 'structure', 'required' => [ 'value', ], 'members' => [ 'value' => [ 'shape' => 'MessageData', ], ], ], 'MessageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MessageOutput', ], ], 'MessageOutput' => [ 'type' => 'structure', 'required' => [ 'value', 'messageId', 'participant', 'timestamp', ], 'members' => [ 'value' => [ 'shape' => 'MessageData', ], 'messageId' => [ 'shape' => 'Uuid', ], 'participant' => [ 'shape' => 'Participant', ], 'timestamp' => [ 'shape' => 'Timestamp', ], ], ], 'MessageTemplateAttachment' => [ 'type' => 'structure', 'required' => [ 'contentDisposition', 'name', 'uploadedTime', 'url', 'urlExpiry', 'attachmentId', ], 'members' => [ 'contentDisposition' => [ 'shape' => 'ContentDisposition', ], 'name' => [ 'shape' => 'AttachmentFileName', ], 'uploadedTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'url' => [ 'shape' => 'Url', ], 'urlExpiry' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'attachmentId' => [ 'shape' => 'Uuid', ], ], ], 'MessageTemplateAttachmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MessageTemplateAttachment', ], ], 'MessageTemplateAttributeKey' => [ 'type' => 'string', 'max' => 32767, 'min' => 1, ], 'MessageTemplateAttributeKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MessageTemplateAttributeKey', ], 'sensitive' => true, ], 'MessageTemplateAttributeType' => [ 'type' => 'string', 'enum' => [ 'SYSTEM', 'AGENT', 'CUSTOMER_PROFILE', 'CUSTOM', ], ], 'MessageTemplateAttributeTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MessageTemplateAttributeType', ], ], 'MessageTemplateAttributeValue' => [ 'type' => 'string', 'max' => 32767, 'min' => 1, 'sensitive' => true, ], 'MessageTemplateAttributes' => [ 'type' => 'structure', 'members' => [ 'systemAttributes' => [ 'shape' => 'SystemAttributes', ], 'agentAttributes' => [ 'shape' => 'AgentAttributes', ], 'customerProfileAttributes' => [ 'shape' => 'CustomerProfileAttributes', ], 'customAttributes' => [ 'shape' => 'CustomAttributes', ], ], ], 'MessageTemplateBodyContentProvider' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'NonEmptyUnlimitedString', ], ], 'union' => true, ], 'MessageTemplateContentProvider' => [ 'type' => 'structure', 'members' => [ 'email' => [ 'shape' => 'EmailMessageTemplateContent', ], 'sms' => [ 'shape' => 'SMSMessageTemplateContent', ], ], 'union' => true, ], 'MessageTemplateContentSha256' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9]+', ], 'MessageTemplateData' => [ 'type' => 'structure', 'required' => [ 'messageTemplateArn', 'messageTemplateId', 'knowledgeBaseArn', 'knowledgeBaseId', 'name', 'channelSubtype', 'createdTime', 'lastModifiedTime', 'lastModifiedBy', 'content', 'messageTemplateContentSha256', ], 'members' => [ 'messageTemplateArn' => [ 'shape' => 'ArnWithQualifier', ], 'messageTemplateId' => [ 'shape' => 'Uuid', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'Name', ], 'channelSubtype' => [ 'shape' => 'ChannelSubtype', ], 'createdTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'lastModifiedTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'lastModifiedBy' => [ 'shape' => 'GenericArn', ], 'content' => [ 'shape' => 'MessageTemplateContentProvider', ], 'description' => [ 'shape' => 'Description', ], 'language' => [ 'shape' => 'LanguageCode', ], 'groupingConfiguration' => [ 'shape' => 'GroupingConfiguration', ], 'defaultAttributes' => [ 'shape' => 'MessageTemplateAttributes', ], 'attributeTypes' => [ 'shape' => 'MessageTemplateAttributeTypeList', ], 'messageTemplateContentSha256' => [ 'shape' => 'MessageTemplateContentSha256', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'MessageTemplateFilterField' => [ 'type' => 'structure', 'required' => [ 'name', 'operator', ], 'members' => [ 'name' => [ 'shape' => 'NonEmptyString', ], 'values' => [ 'shape' => 'MessageTemplateFilterValueList', ], 'operator' => [ 'shape' => 'MessageTemplateFilterOperator', ], 'includeNoExistence' => [ 'shape' => 'Boolean', ], ], ], 'MessageTemplateFilterFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MessageTemplateFilterField', ], 'max' => 10, 'min' => 0, ], 'MessageTemplateFilterOperator' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'PREFIX', ], ], 'MessageTemplateFilterValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'MessageTemplateFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MessageTemplateFilterValue', ], 'max' => 5, 'min' => 1, ], 'MessageTemplateOrderField' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'NonEmptyString', ], 'order' => [ 'shape' => 'Order', ], ], ], 'MessageTemplateQueryField' => [ 'type' => 'structure', 'required' => [ 'name', 'values', 'operator', ], 'members' => [ 'name' => [ 'shape' => 'NonEmptyString', ], 'values' => [ 'shape' => 'MessageTemplateQueryValueList', ], 'operator' => [ 'shape' => 'MessageTemplateQueryOperator', ], 'allowFuzziness' => [ 'shape' => 'Boolean', ], 'priority' => [ 'shape' => 'Priority', ], ], ], 'MessageTemplateQueryFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MessageTemplateQueryField', ], 'max' => 4, 'min' => 0, ], 'MessageTemplateQueryOperator' => [ 'type' => 'string', 'enum' => [ 'CONTAINS', 'CONTAINS_AND_PREFIX', ], ], 'MessageTemplateQueryValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'MessageTemplateQueryValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MessageTemplateQueryValue', ], 'max' => 5, 'min' => 1, ], 'MessageTemplateSearchExpression' => [ 'type' => 'structure', 'members' => [ 'queries' => [ 'shape' => 'MessageTemplateQueryFieldList', ], 'filters' => [ 'shape' => 'MessageTemplateFilterFieldList', ], 'orderOnField' => [ 'shape' => 'MessageTemplateOrderField', ], ], ], 'MessageTemplateSearchResultData' => [ 'type' => 'structure', 'required' => [ 'messageTemplateArn', 'messageTemplateId', 'knowledgeBaseArn', 'knowledgeBaseId', 'name', 'channelSubtype', 'createdTime', 'lastModifiedTime', 'lastModifiedBy', ], 'members' => [ 'messageTemplateArn' => [ 'shape' => 'ArnWithQualifier', ], 'messageTemplateId' => [ 'shape' => 'Uuid', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'Name', ], 'channelSubtype' => [ 'shape' => 'ChannelSubtype', ], 'createdTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'lastModifiedTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'lastModifiedBy' => [ 'shape' => 'GenericArn', ], 'isActive' => [ 'shape' => 'Boolean', ], 'versionNumber' => [ 'shape' => 'Version', ], 'description' => [ 'shape' => 'Description', ], 'groupingConfiguration' => [ 'shape' => 'GroupingConfiguration', ], 'language' => [ 'shape' => 'LanguageCode', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'MessageTemplateSearchResultsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MessageTemplateSearchResultData', ], ], 'MessageTemplateSummary' => [ 'type' => 'structure', 'required' => [ 'messageTemplateArn', 'messageTemplateId', 'knowledgeBaseArn', 'knowledgeBaseId', 'name', 'channelSubtype', 'createdTime', 'lastModifiedTime', 'lastModifiedBy', ], 'members' => [ 'messageTemplateArn' => [ 'shape' => 'ArnWithQualifier', ], 'messageTemplateId' => [ 'shape' => 'Uuid', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'Name', ], 'channelSubtype' => [ 'shape' => 'ChannelSubtype', ], 'createdTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'lastModifiedTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'lastModifiedBy' => [ 'shape' => 'GenericArn', ], 'activeVersionNumber' => [ 'shape' => 'Version', ], 'description' => [ 'shape' => 'Description', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'MessageTemplateSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MessageTemplateSummary', ], ], 'MessageTemplateVersionSummary' => [ 'type' => 'structure', 'required' => [ 'messageTemplateArn', 'messageTemplateId', 'knowledgeBaseArn', 'knowledgeBaseId', 'name', 'channelSubtype', 'isActive', 'versionNumber', ], 'members' => [ 'messageTemplateArn' => [ 'shape' => 'ArnWithQualifier', ], 'messageTemplateId' => [ 'shape' => 'Uuid', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'Name', ], 'channelSubtype' => [ 'shape' => 'ChannelSubtype', ], 'isActive' => [ 'shape' => 'Boolean', ], 'versionNumber' => [ 'shape' => 'Version', ], ], ], 'MessageTemplateVersionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MessageTemplateVersionSummary', ], ], 'MessageType' => [ 'type' => 'string', 'enum' => [ 'TEXT', ], ], 'Name' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\s_.,-]+.*', ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'NonEmptySensitiveString' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'sensitive' => true, ], 'NonEmptyString' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'NonEmptyUnlimitedString' => [ 'type' => 'string', 'min' => 1, 'sensitive' => true, ], 'NotifyRecommendationsReceivedError' => [ 'type' => 'structure', 'members' => [ 'recommendationId' => [ 'shape' => 'RecommendationId', ], 'message' => [ 'shape' => 'NotifyRecommendationsReceivedErrorMessage', ], ], ], 'NotifyRecommendationsReceivedErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NotifyRecommendationsReceivedError', ], ], 'NotifyRecommendationsReceivedErrorMessage' => [ 'type' => 'string', ], 'NotifyRecommendationsReceivedRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'sessionId', 'recommendationIds', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'sessionId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'sessionId', ], 'recommendationIds' => [ 'shape' => 'RecommendationIdList', ], ], ], 'NotifyRecommendationsReceivedResponse' => [ 'type' => 'structure', 'members' => [ 'recommendationIds' => [ 'shape' => 'RecommendationIdList', ], 'errors' => [ 'shape' => 'NotifyRecommendationsReceivedErrorList', ], ], ], 'ObjectFieldsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 100, 'min' => 1, ], 'OrCondition' => [ 'type' => 'structure', 'members' => [ 'andConditions' => [ 'shape' => 'AndConditions', ], 'tagCondition' => [ 'shape' => 'TagCondition', ], ], 'union' => true, ], 'OrConditions' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrCondition', ], ], 'Order' => [ 'type' => 'string', 'enum' => [ 'ASC', 'DESC', ], ], 'Origin' => [ 'type' => 'string', 'enum' => [ 'SYSTEM', 'CUSTOMER', ], ], 'ParsingConfiguration' => [ 'type' => 'structure', 'required' => [ 'parsingStrategy', ], 'members' => [ 'parsingStrategy' => [ 'shape' => 'ParsingStrategy', ], 'bedrockFoundationModelConfiguration' => [ 'shape' => 'BedrockFoundationModelConfigurationForParsing', ], ], ], 'ParsingPrompt' => [ 'type' => 'structure', 'required' => [ 'parsingPromptText', ], 'members' => [ 'parsingPromptText' => [ 'shape' => 'ParsingPromptText', ], ], ], 'ParsingPromptText' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, ], 'ParsingStrategy' => [ 'type' => 'string', 'enum' => [ 'BEDROCK_FOUNDATION_MODEL', ], ], 'Participant' => [ 'type' => 'string', 'enum' => [ 'CUSTOMER', 'AGENT', 'BOT', ], ], 'PreconditionFailedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 412, 'senderFault' => true, ], 'exception' => true, ], 'Priority' => [ 'type' => 'string', 'enum' => [ 'HIGH', 'MEDIUM', 'LOW', ], ], 'PutFeedbackRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'targetId', 'targetType', 'contentFeedback', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'targetId' => [ 'shape' => 'Uuid', ], 'targetType' => [ 'shape' => 'TargetType', ], 'contentFeedback' => [ 'shape' => 'ContentFeedbackData', ], ], ], 'PutFeedbackResponse' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'assistantArn', 'targetId', 'targetType', 'contentFeedback', ], 'members' => [ 'assistantId' => [ 'shape' => 'Uuid', ], 'assistantArn' => [ 'shape' => 'UuidOrArn', ], 'targetId' => [ 'shape' => 'Uuid', ], 'targetType' => [ 'shape' => 'TargetType', ], 'contentFeedback' => [ 'shape' => 'ContentFeedbackData', ], ], ], 'QueryAssistantRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'queryText' => [ 'shape' => 'QueryText', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'sessionId' => [ 'shape' => 'UuidOrArn', ], 'queryCondition' => [ 'shape' => 'QueryConditionExpression', ], 'queryInputData' => [ 'shape' => 'QueryInputData', ], 'overrideKnowledgeBaseSearchType' => [ 'shape' => 'KnowledgeBaseSearchType', ], ], ], 'QueryAssistantResponse' => [ 'type' => 'structure', 'required' => [ 'results', ], 'members' => [ 'results' => [ 'shape' => 'QueryResultsList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'QueryCondition' => [ 'type' => 'structure', 'members' => [ 'single' => [ 'shape' => 'QueryConditionItem', ], ], 'union' => true, ], 'QueryConditionComparisonOperator' => [ 'type' => 'string', 'enum' => [ 'EQUALS', ], ], 'QueryConditionExpression' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueryCondition', ], 'max' => 1, 'min' => 0, ], 'QueryConditionFieldName' => [ 'type' => 'string', 'enum' => [ 'RESULT_TYPE', ], ], 'QueryConditionItem' => [ 'type' => 'structure', 'required' => [ 'field', 'comparator', 'value', ], 'members' => [ 'field' => [ 'shape' => 'QueryConditionFieldName', ], 'comparator' => [ 'shape' => 'QueryConditionComparisonOperator', ], 'value' => [ 'shape' => 'NonEmptyString', ], ], ], 'QueryInputData' => [ 'type' => 'structure', 'members' => [ 'queryTextInputData' => [ 'shape' => 'QueryTextInputData', ], 'intentInputData' => [ 'shape' => 'IntentInputData', ], ], 'union' => true, ], 'QueryRecommendationTriggerData' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'QueryText', ], ], ], 'QueryResultType' => [ 'type' => 'string', 'enum' => [ 'KNOWLEDGE_CONTENT', 'INTENT_ANSWER', 'GENERATIVE_ANSWER', 'GENERATIVE_ANSWER_CHUNK', 'BLOCKED_GENERATIVE_ANSWER_CHUNK', 'INTENT_ANSWER_CHUNK', 'BLOCKED_INTENT_ANSWER_CHUNK', ], ], 'QueryResultsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResultData', ], ], 'QueryText' => [ 'type' => 'string', 'max' => 512, 'min' => 0, 'sensitive' => true, ], 'QueryTextInputData' => [ 'type' => 'structure', 'required' => [ 'text', ], 'members' => [ 'text' => [ 'shape' => 'QueryText', ], ], ], 'QuickResponseContent' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'sensitive' => true, ], 'QuickResponseContentProvider' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'QuickResponseContent', ], ], 'union' => true, ], 'QuickResponseContents' => [ 'type' => 'structure', 'members' => [ 'plainText' => [ 'shape' => 'QuickResponseContentProvider', ], 'markdown' => [ 'shape' => 'QuickResponseContentProvider', ], ], ], 'QuickResponseData' => [ 'type' => 'structure', 'required' => [ 'quickResponseArn', 'quickResponseId', 'knowledgeBaseArn', 'knowledgeBaseId', 'name', 'contentType', 'status', 'createdTime', 'lastModifiedTime', ], 'members' => [ 'quickResponseArn' => [ 'shape' => 'Arn', ], 'quickResponseId' => [ 'shape' => 'Uuid', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'QuickResponseName', ], 'contentType' => [ 'shape' => 'QuickResponseType', ], 'status' => [ 'shape' => 'QuickResponseStatus', ], 'createdTime' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'lastModifiedTime' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'contents' => [ 'shape' => 'QuickResponseContents', ], 'description' => [ 'shape' => 'QuickResponseDescription', ], 'groupingConfiguration' => [ 'shape' => 'GroupingConfiguration', ], 'shortcutKey' => [ 'shape' => 'ShortCutKey', ], 'lastModifiedBy' => [ 'shape' => 'GenericArn', ], 'isActive' => [ 'shape' => 'Boolean', ], 'channels' => [ 'shape' => 'Channels', ], 'language' => [ 'shape' => 'LanguageCode', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'QuickResponseDataProvider' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'QuickResponseContent', ], ], 'union' => true, ], 'QuickResponseDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'QuickResponseFilterField' => [ 'type' => 'structure', 'required' => [ 'name', 'operator', ], 'members' => [ 'name' => [ 'shape' => 'NonEmptyString', ], 'values' => [ 'shape' => 'QuickResponseFilterValueList', ], 'operator' => [ 'shape' => 'QuickResponseFilterOperator', ], 'includeNoExistence' => [ 'shape' => 'Boolean', ], ], ], 'QuickResponseFilterFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QuickResponseFilterField', ], 'max' => 10, 'min' => 0, ], 'QuickResponseFilterOperator' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'PREFIX', ], ], 'QuickResponseFilterValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'QuickResponseFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QuickResponseFilterValue', ], 'max' => 5, 'min' => 1, ], 'QuickResponseName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'QuickResponseOrderField' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'NonEmptyString', ], 'order' => [ 'shape' => 'Order', ], ], ], 'QuickResponseQueryField' => [ 'type' => 'structure', 'required' => [ 'name', 'values', 'operator', ], 'members' => [ 'name' => [ 'shape' => 'NonEmptyString', ], 'values' => [ 'shape' => 'QuickResponseQueryValueList', ], 'operator' => [ 'shape' => 'QuickResponseQueryOperator', ], 'allowFuzziness' => [ 'shape' => 'Boolean', ], 'priority' => [ 'shape' => 'Priority', ], ], ], 'QuickResponseQueryFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QuickResponseQueryField', ], 'max' => 4, 'min' => 0, ], 'QuickResponseQueryOperator' => [ 'type' => 'string', 'enum' => [ 'CONTAINS', 'CONTAINS_AND_PREFIX', ], ], 'QuickResponseQueryValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'QuickResponseQueryValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QuickResponseQueryValue', ], 'max' => 5, 'min' => 1, ], 'QuickResponseSearchExpression' => [ 'type' => 'structure', 'members' => [ 'queries' => [ 'shape' => 'QuickResponseQueryFieldList', ], 'filters' => [ 'shape' => 'QuickResponseFilterFieldList', ], 'orderOnField' => [ 'shape' => 'QuickResponseOrderField', ], ], ], 'QuickResponseSearchResultData' => [ 'type' => 'structure', 'required' => [ 'quickResponseArn', 'quickResponseId', 'knowledgeBaseArn', 'knowledgeBaseId', 'name', 'contentType', 'status', 'contents', 'createdTime', 'lastModifiedTime', 'isActive', ], 'members' => [ 'quickResponseArn' => [ 'shape' => 'Arn', ], 'quickResponseId' => [ 'shape' => 'Uuid', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'QuickResponseName', ], 'contentType' => [ 'shape' => 'QuickResponseType', ], 'status' => [ 'shape' => 'QuickResponseStatus', ], 'contents' => [ 'shape' => 'QuickResponseContents', ], 'createdTime' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'lastModifiedTime' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'isActive' => [ 'shape' => 'Boolean', ], 'description' => [ 'shape' => 'QuickResponseDescription', ], 'groupingConfiguration' => [ 'shape' => 'GroupingConfiguration', ], 'shortcutKey' => [ 'shape' => 'ShortCutKey', ], 'lastModifiedBy' => [ 'shape' => 'GenericArn', ], 'channels' => [ 'shape' => 'Channels', ], 'language' => [ 'shape' => 'LanguageCode', ], 'attributesNotInterpolated' => [ 'shape' => 'ContactAttributeKeys', ], 'attributesInterpolated' => [ 'shape' => 'ContactAttributeKeys', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'QuickResponseSearchResultsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QuickResponseSearchResultData', ], ], 'QuickResponseStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'CREATED', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', 'DELETED', 'UPDATE_IN_PROGRESS', 'UPDATE_FAILED', ], ], 'QuickResponseSummary' => [ 'type' => 'structure', 'required' => [ 'quickResponseArn', 'quickResponseId', 'knowledgeBaseArn', 'knowledgeBaseId', 'name', 'contentType', 'status', 'createdTime', 'lastModifiedTime', ], 'members' => [ 'quickResponseArn' => [ 'shape' => 'Arn', ], 'quickResponseId' => [ 'shape' => 'Uuid', ], 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], 'knowledgeBaseId' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'QuickResponseName', ], 'contentType' => [ 'shape' => 'QuickResponseType', ], 'status' => [ 'shape' => 'QuickResponseStatus', ], 'createdTime' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'lastModifiedTime' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'description' => [ 'shape' => 'QuickResponseDescription', ], 'lastModifiedBy' => [ 'shape' => 'GenericArn', ], 'isActive' => [ 'shape' => 'Boolean', ], 'channels' => [ 'shape' => 'Channels', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'QuickResponseSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QuickResponseSummary', ], ], 'QuickResponseType' => [ 'type' => 'string', 'pattern' => '(application/x\\.quickresponse;format=(plain|markdown))', ], 'RankingData' => [ 'type' => 'structure', 'members' => [ 'relevanceScore' => [ 'shape' => 'RelevanceScore', ], 'relevanceLevel' => [ 'shape' => 'RelevanceLevel', ], ], ], 'RecommendationData' => [ 'type' => 'structure', 'required' => [ 'recommendationId', ], 'members' => [ 'recommendationId' => [ 'shape' => 'RecommendationId', ], 'document' => [ 'shape' => 'Document', ], 'relevanceScore' => [ 'shape' => 'RelevanceScore', ], 'relevanceLevel' => [ 'shape' => 'RelevanceLevel', ], 'type' => [ 'shape' => 'RecommendationType', ], 'data' => [ 'shape' => 'DataSummary', ], ], ], 'RecommendationId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'RecommendationIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationId', ], 'max' => 25, 'min' => 0, ], 'RecommendationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationData', ], ], 'RecommendationSourceType' => [ 'type' => 'string', 'enum' => [ 'ISSUE_DETECTION', 'RULE_EVALUATION', 'OTHER', ], ], 'RecommendationTrigger' => [ 'type' => 'structure', 'required' => [ 'id', 'type', 'source', 'data', 'recommendationIds', ], 'members' => [ 'id' => [ 'shape' => 'Uuid', ], 'type' => [ 'shape' => 'RecommendationTriggerType', ], 'source' => [ 'shape' => 'RecommendationSourceType', ], 'data' => [ 'shape' => 'RecommendationTriggerData', ], 'recommendationIds' => [ 'shape' => 'RecommendationIdList', ], ], ], 'RecommendationTriggerData' => [ 'type' => 'structure', 'members' => [ 'query' => [ 'shape' => 'QueryRecommendationTriggerData', ], ], 'union' => true, ], 'RecommendationTriggerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationTrigger', ], ], 'RecommendationTriggerType' => [ 'type' => 'string', 'enum' => [ 'QUERY', 'GENERATIVE', ], ], 'RecommendationType' => [ 'type' => 'string', 'enum' => [ 'KNOWLEDGE_CONTENT', 'GENERATIVE_RESPONSE', 'GENERATIVE_ANSWER', 'DETECTED_INTENT', 'GENERATIVE_ANSWER_CHUNK', 'BLOCKED_GENERATIVE_ANSWER_CHUNK', 'INTENT_ANSWER_CHUNK', 'BLOCKED_INTENT_ANSWER_CHUNK', ], ], 'ReferenceType' => [ 'type' => 'string', 'enum' => [ 'WEB_CRAWLER', 'KNOWLEDGE_BASE', ], ], 'Relevance' => [ 'type' => 'string', 'enum' => [ 'HELPFUL', 'NOT_HELPFUL', ], ], 'RelevanceLevel' => [ 'type' => 'string', 'enum' => [ 'HIGH', 'MEDIUM', 'LOW', ], ], 'RelevanceScore' => [ 'type' => 'double', 'min' => 0.0, ], 'RemoveAssistantAIAgentRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'aiAgentType', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'aiAgentType' => [ 'shape' => 'AIAgentType', 'location' => 'querystring', 'locationName' => 'aiAgentType', ], ], ], 'RemoveAssistantAIAgentResponse' => [ 'type' => 'structure', 'members' => [], ], 'RemoveKnowledgeBaseTemplateUriRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'RemoveKnowledgeBaseTemplateUriResponse' => [ 'type' => 'structure', 'members' => [], ], 'RenderMessageTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'messageTemplateId', 'attributes', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'messageTemplateId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'messageTemplateId', ], 'attributes' => [ 'shape' => 'MessageTemplateAttributes', ], ], ], 'RenderMessageTemplateResponse' => [ 'type' => 'structure', 'required' => [ 'content', ], 'members' => [ 'content' => [ 'shape' => 'MessageTemplateContentProvider', ], 'attributesNotInterpolated' => [ 'shape' => 'MessageTemplateAttributeKeyList', ], 'attachments' => [ 'shape' => 'MessageTemplateAttachmentList', ], ], ], 'RenderingConfiguration' => [ 'type' => 'structure', 'members' => [ 'templateUri' => [ 'shape' => 'Uri', ], ], ], 'RequestTimeoutException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 408, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceName' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResultData' => [ 'type' => 'structure', 'required' => [ 'resultId', ], 'members' => [ 'resultId' => [ 'shape' => 'Uuid', ], 'document' => [ 'shape' => 'Document', ], 'relevanceScore' => [ 'shape' => 'RelevanceScore', ], 'data' => [ 'shape' => 'DataSummary', ], 'type' => [ 'shape' => 'QueryResultType', ], ], ], 'RuntimeSessionData' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'NonEmptySensitiveString', ], 'value' => [ 'shape' => 'RuntimeSessionDataValue', ], ], ], 'RuntimeSessionDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuntimeSessionData', ], 'max' => 50, 'min' => 0, ], 'RuntimeSessionDataValue' => [ 'type' => 'structure', 'members' => [ 'stringValue' => [ 'shape' => 'NonEmptySensitiveString', ], ], 'union' => true, ], 'SMSMessageTemplateContent' => [ 'type' => 'structure', 'members' => [ 'body' => [ 'shape' => 'SMSMessageTemplateContentBody', ], ], ], 'SMSMessageTemplateContentBody' => [ 'type' => 'structure', 'members' => [ 'plainText' => [ 'shape' => 'MessageTemplateBodyContentProvider', ], ], ], 'SearchContentRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'searchExpression', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'searchExpression' => [ 'shape' => 'SearchExpression', ], ], ], 'SearchContentResponse' => [ 'type' => 'structure', 'required' => [ 'contentSummaries', ], 'members' => [ 'contentSummaries' => [ 'shape' => 'ContentSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'SearchExpression' => [ 'type' => 'structure', 'required' => [ 'filters', ], 'members' => [ 'filters' => [ 'shape' => 'FilterList', ], ], ], 'SearchMessageTemplatesRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'searchExpression', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'searchExpression' => [ 'shape' => 'MessageTemplateSearchExpression', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'SearchMessageTemplatesResponse' => [ 'type' => 'structure', 'required' => [ 'results', ], 'members' => [ 'results' => [ 'shape' => 'MessageTemplateSearchResultsList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'SearchQuickResponsesRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'searchExpression', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'searchExpression' => [ 'shape' => 'QuickResponseSearchExpression', ], 'nextToken' => [ 'shape' => 'NonEmptyString', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'attributes' => [ 'shape' => 'ContactAttributes', ], ], ], 'SearchQuickResponsesResponse' => [ 'type' => 'structure', 'required' => [ 'results', ], 'members' => [ 'results' => [ 'shape' => 'QuickResponseSearchResultsList', ], 'nextToken' => [ 'shape' => 'NonEmptyString', ], ], ], 'SearchSessionsRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'searchExpression', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'searchExpression' => [ 'shape' => 'SearchExpression', ], ], ], 'SearchSessionsResponse' => [ 'type' => 'structure', 'required' => [ 'sessionSummaries', ], 'members' => [ 'sessionSummaries' => [ 'shape' => 'SessionSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'SeedUrl' => [ 'type' => 'structure', 'members' => [ 'url' => [ 'shape' => 'WebUrl', ], ], ], 'SeedUrls' => [ 'type' => 'list', 'member' => [ 'shape' => 'SeedUrl', ], 'max' => 100, 'min' => 1, ], 'SelfServiceAIAgentConfiguration' => [ 'type' => 'structure', 'members' => [ 'selfServicePreProcessingAIPromptId' => [ 'shape' => 'UuidWithQualifier', ], 'selfServiceAnswerGenerationAIPromptId' => [ 'shape' => 'UuidWithQualifier', ], 'selfServiceAIGuardrailId' => [ 'shape' => 'UuidWithQualifier', ], 'associationConfigurations' => [ 'shape' => 'AssociationConfigurationList', ], ], ], 'SelfServiceConversationHistory' => [ 'type' => 'structure', 'required' => [ 'turnNumber', ], 'members' => [ 'turnNumber' => [ 'shape' => 'Integer', ], 'inputTranscript' => [ 'shape' => 'SensitiveString', ], 'botResponse' => [ 'shape' => 'SensitiveString', ], ], ], 'SelfServiceConversationHistoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SelfServiceConversationHistory', ], 'max' => 10, 'min' => 0, ], 'SemanticChunkingConfiguration' => [ 'type' => 'structure', 'required' => [ 'maxTokens', 'bufferSize', 'breakpointPercentileThreshold', ], 'members' => [ 'maxTokens' => [ 'shape' => 'SemanticChunkingConfigurationMaxTokensInteger', ], 'bufferSize' => [ 'shape' => 'SemanticChunkingConfigurationBufferSizeInteger', ], 'breakpointPercentileThreshold' => [ 'shape' => 'SemanticChunkingConfigurationBreakpointPercentileThresholdInteger', ], ], ], 'SemanticChunkingConfigurationBreakpointPercentileThresholdInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 99, 'min' => 50, ], 'SemanticChunkingConfigurationBufferSizeInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1, 'min' => 0, ], 'SemanticChunkingConfigurationMaxTokensInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'SendMessageRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'sessionId', 'type', 'message', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'sessionId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'sessionId', ], 'type' => [ 'shape' => 'MessageType', ], 'message' => [ 'shape' => 'MessageInput', ], 'conversationContext' => [ 'shape' => 'ConversationContext', ], 'configuration' => [ 'shape' => 'MessageConfiguration', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'SendMessageResponse' => [ 'type' => 'structure', 'required' => [ 'requestMessageId', 'nextMessageToken', ], 'members' => [ 'requestMessageId' => [ 'shape' => 'Uuid', ], 'configuration' => [ 'shape' => 'MessageConfiguration', ], 'nextMessageToken' => [ 'shape' => 'NextToken', ], ], ], 'SensitiveString' => [ 'type' => 'string', 'sensitive' => true, ], 'ServerSideEncryptionConfiguration' => [ 'type' => 'structure', 'members' => [ 'kmsKeyId' => [ 'shape' => 'NonEmptyString', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SessionData' => [ 'type' => 'structure', 'required' => [ 'sessionArn', 'sessionId', 'name', ], 'members' => [ 'sessionArn' => [ 'shape' => 'Arn', ], 'sessionId' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'tags' => [ 'shape' => 'Tags', ], 'integrationConfiguration' => [ 'shape' => 'SessionIntegrationConfiguration', ], 'tagFilter' => [ 'shape' => 'TagFilter', ], 'aiAgentConfiguration' => [ 'shape' => 'AIAgentConfigurationMap', ], 'origin' => [ 'shape' => 'Origin', ], ], ], 'SessionDataNamespace' => [ 'type' => 'string', 'enum' => [ 'Custom', ], ], 'SessionIntegrationConfiguration' => [ 'type' => 'structure', 'members' => [ 'topicIntegrationArn' => [ 'shape' => 'GenericArn', ], ], ], 'SessionSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SessionSummary', ], ], 'SessionSummary' => [ 'type' => 'structure', 'required' => [ 'sessionId', 'sessionArn', 'assistantId', 'assistantArn', ], 'members' => [ 'sessionId' => [ 'shape' => 'Uuid', ], 'sessionArn' => [ 'shape' => 'Arn', ], 'assistantId' => [ 'shape' => 'Uuid', ], 'assistantArn' => [ 'shape' => 'Arn', ], ], ], 'ShortCutKey' => [ 'type' => 'string', 'max' => 10, 'min' => 1, ], 'SourceConfiguration' => [ 'type' => 'structure', 'members' => [ 'appIntegrations' => [ 'shape' => 'AppIntegrationsConfiguration', ], 'managedSourceConfiguration' => [ 'shape' => 'ManagedSourceConfiguration', ], ], 'union' => true, ], 'SourceContentDataDetails' => [ 'type' => 'structure', 'required' => [ 'id', 'type', 'textData', 'rankingData', ], 'members' => [ 'id' => [ 'shape' => 'Uuid', ], 'type' => [ 'shape' => 'SourceContentType', ], 'textData' => [ 'shape' => 'TextData', ], 'rankingData' => [ 'shape' => 'RankingData', ], 'citationSpan' => [ 'shape' => 'CitationSpan', ], ], ], 'SourceContentType' => [ 'type' => 'string', 'enum' => [ 'KNOWLEDGE_CONTENT', ], ], 'StartContentUploadRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'contentType', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'contentType' => [ 'shape' => 'ContentType', ], 'presignedUrlTimeToLive' => [ 'shape' => 'TimeToLive', ], ], ], 'StartContentUploadResponse' => [ 'type' => 'structure', 'required' => [ 'uploadId', 'url', 'urlExpiry', 'headersToInclude', ], 'members' => [ 'uploadId' => [ 'shape' => 'UploadId', ], 'url' => [ 'shape' => 'Url', ], 'urlExpiry' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'headersToInclude' => [ 'shape' => 'Headers', ], ], ], 'StartImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'importJobType', 'uploadId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'importJobType' => [ 'shape' => 'ImportJobType', ], 'uploadId' => [ 'shape' => 'UploadId', ], 'clientToken' => [ 'shape' => 'NonEmptyString', 'idempotencyToken' => true, ], 'metadata' => [ 'shape' => 'ContentMetadata', ], 'externalSourceConfiguration' => [ 'shape' => 'ExternalSourceConfiguration', ], ], ], 'StartImportJobResponse' => [ 'type' => 'structure', 'members' => [ 'importJob' => [ 'shape' => 'ImportJobData', ], ], ], 'Status' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'ACTIVE', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', 'DELETED', ], ], 'String' => [ 'type' => 'string', ], 'SyncStatus' => [ 'type' => 'string', 'enum' => [ 'SYNC_FAILED', 'SYNCING_IN_PROGRESS', 'SYNC_SUCCESS', 'CREATE_IN_PROGRESS', ], ], 'SyntheticTimestamp_date_time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'SyntheticTimestamp_epoch_seconds' => [ 'type' => 'timestamp', 'timestampFormat' => 'unixTimestamp', ], 'SystemAttributes' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'MessageTemplateAttributeValue', ], 'customerEndpoint' => [ 'shape' => 'SystemEndpointAttributes', ], 'systemEndpoint' => [ 'shape' => 'SystemEndpointAttributes', ], ], ], 'SystemEndpointAttributes' => [ 'type' => 'structure', 'members' => [ 'address' => [ 'shape' => 'MessageTemplateAttributeValue', ], ], ], 'TagCondition' => [ 'type' => 'structure', 'required' => [ 'key', ], 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'TagFilter' => [ 'type' => 'structure', 'members' => [ 'tagCondition' => [ 'shape' => 'TagCondition', ], 'andConditions' => [ 'shape' => 'AndConditions', ], 'orConditions' => [ 'shape' => 'OrConditions', ], ], 'union' => true, ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '(?!aws:)[a-zA-Z+-=._:/]+', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], 'TargetType' => [ 'type' => 'string', 'enum' => [ 'RECOMMENDATION', 'RESULT', ], ], 'TextAIPrompt' => [ 'type' => 'string', 'max' => 1000000, 'min' => 1, 'sensitive' => true, ], 'TextData' => [ 'type' => 'structure', 'members' => [ 'title' => [ 'shape' => 'DocumentText', ], 'excerpt' => [ 'shape' => 'DocumentText', ], ], ], 'TextFullAIPromptEditTemplateConfiguration' => [ 'type' => 'structure', 'required' => [ 'text', ], 'members' => [ 'text' => [ 'shape' => 'TextAIPrompt', ], ], ], 'TextMessage' => [ 'type' => 'structure', 'members' => [ 'value' => [ 'shape' => 'TextMessageValueString', ], ], ], 'TextMessageValueString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'sensitive' => true, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'TimeToLive' => [ 'type' => 'integer', 'box' => true, 'max' => 60, 'min' => 1, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceName' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAIAgentRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'aiAgentId', 'visibilityStatus', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'aiAgentId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'aiAgentId', ], 'visibilityStatus' => [ 'shape' => 'VisibilityStatus', ], 'configuration' => [ 'shape' => 'AIAgentConfiguration', ], 'description' => [ 'shape' => 'Description', ], ], ], 'UpdateAIAgentResponse' => [ 'type' => 'structure', 'members' => [ 'aiAgent' => [ 'shape' => 'AIAgentData', ], ], ], 'UpdateAIGuardrailRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'aiGuardrailId', 'visibilityStatus', 'blockedInputMessaging', 'blockedOutputsMessaging', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'aiGuardrailId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'aiGuardrailId', ], 'visibilityStatus' => [ 'shape' => 'VisibilityStatus', ], 'blockedInputMessaging' => [ 'shape' => 'AIGuardrailBlockedMessaging', ], 'blockedOutputsMessaging' => [ 'shape' => 'AIGuardrailBlockedMessaging', ], 'description' => [ 'shape' => 'AIGuardrailDescription', ], 'topicPolicyConfig' => [ 'shape' => 'AIGuardrailTopicPolicyConfig', ], 'contentPolicyConfig' => [ 'shape' => 'AIGuardrailContentPolicyConfig', ], 'wordPolicyConfig' => [ 'shape' => 'AIGuardrailWordPolicyConfig', ], 'sensitiveInformationPolicyConfig' => [ 'shape' => 'AIGuardrailSensitiveInformationPolicyConfig', ], 'contextualGroundingPolicyConfig' => [ 'shape' => 'AIGuardrailContextualGroundingPolicyConfig', ], ], ], 'UpdateAIGuardrailResponse' => [ 'type' => 'structure', 'members' => [ 'aiGuardrail' => [ 'shape' => 'AIGuardrailData', ], ], ], 'UpdateAIPromptRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'aiPromptId', 'visibilityStatus', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'aiPromptId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'aiPromptId', ], 'visibilityStatus' => [ 'shape' => 'VisibilityStatus', ], 'templateConfiguration' => [ 'shape' => 'AIPromptTemplateConfiguration', ], 'description' => [ 'shape' => 'Description', ], ], ], 'UpdateAIPromptResponse' => [ 'type' => 'structure', 'members' => [ 'aiPrompt' => [ 'shape' => 'AIPromptData', ], ], ], 'UpdateAssistantAIAgentRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'aiAgentType', 'configuration', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'aiAgentType' => [ 'shape' => 'AIAgentType', ], 'configuration' => [ 'shape' => 'AIAgentConfigurationData', ], ], ], 'UpdateAssistantAIAgentResponse' => [ 'type' => 'structure', 'members' => [ 'assistant' => [ 'shape' => 'AssistantData', ], ], ], 'UpdateContentRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'contentId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'contentId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'contentId', ], 'revisionId' => [ 'shape' => 'NonEmptyString', ], 'title' => [ 'shape' => 'ContentTitle', ], 'overrideLinkOutUri' => [ 'shape' => 'Uri', ], 'removeOverrideLinkOutUri' => [ 'shape' => 'Boolean', ], 'metadata' => [ 'shape' => 'ContentMetadata', ], 'uploadId' => [ 'shape' => 'UploadId', ], ], ], 'UpdateContentResponse' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'ContentData', ], ], ], 'UpdateKnowledgeBaseTemplateUriRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'templateUri', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'templateUri' => [ 'shape' => 'Uri', ], ], ], 'UpdateKnowledgeBaseTemplateUriResponse' => [ 'type' => 'structure', 'members' => [ 'knowledgeBase' => [ 'shape' => 'KnowledgeBaseData', ], ], ], 'UpdateMessageTemplateMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'messageTemplateId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'messageTemplateId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'messageTemplateId', ], 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'groupingConfiguration' => [ 'shape' => 'GroupingConfiguration', ], ], ], 'UpdateMessageTemplateMetadataResponse' => [ 'type' => 'structure', 'members' => [ 'messageTemplate' => [ 'shape' => 'MessageTemplateData', ], ], ], 'UpdateMessageTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'messageTemplateId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'messageTemplateId' => [ 'shape' => 'UuidOrArnOrEitherWithQualifier', 'location' => 'uri', 'locationName' => 'messageTemplateId', ], 'content' => [ 'shape' => 'MessageTemplateContentProvider', ], 'language' => [ 'shape' => 'LanguageCode', ], 'defaultAttributes' => [ 'shape' => 'MessageTemplateAttributes', ], ], ], 'UpdateMessageTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'messageTemplate' => [ 'shape' => 'MessageTemplateData', ], ], ], 'UpdateQuickResponseRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'quickResponseId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'quickResponseId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'quickResponseId', ], 'name' => [ 'shape' => 'QuickResponseName', ], 'content' => [ 'shape' => 'QuickResponseDataProvider', ], 'contentType' => [ 'shape' => 'QuickResponseType', ], 'groupingConfiguration' => [ 'shape' => 'GroupingConfiguration', ], 'removeGroupingConfiguration' => [ 'shape' => 'Boolean', ], 'description' => [ 'shape' => 'QuickResponseDescription', ], 'removeDescription' => [ 'shape' => 'Boolean', ], 'shortcutKey' => [ 'shape' => 'ShortCutKey', ], 'removeShortcutKey' => [ 'shape' => 'Boolean', ], 'isActive' => [ 'shape' => 'Boolean', ], 'channels' => [ 'shape' => 'Channels', ], 'language' => [ 'shape' => 'LanguageCode', ], ], ], 'UpdateQuickResponseResponse' => [ 'type' => 'structure', 'members' => [ 'quickResponse' => [ 'shape' => 'QuickResponseData', ], ], ], 'UpdateSessionDataRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'sessionId', 'data', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'sessionId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'sessionId', ], 'namespace' => [ 'shape' => 'SessionDataNamespace', ], 'data' => [ 'shape' => 'RuntimeSessionDataList', ], ], ], 'UpdateSessionDataResponse' => [ 'type' => 'structure', 'required' => [ 'sessionArn', 'sessionId', 'namespace', 'data', ], 'members' => [ 'sessionArn' => [ 'shape' => 'Arn', ], 'sessionId' => [ 'shape' => 'Uuid', ], 'namespace' => [ 'shape' => 'SessionDataNamespace', ], 'data' => [ 'shape' => 'RuntimeSessionDataList', ], ], ], 'UpdateSessionRequest' => [ 'type' => 'structure', 'required' => [ 'assistantId', 'sessionId', ], 'members' => [ 'assistantId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'assistantId', ], 'sessionId' => [ 'shape' => 'UuidOrArn', 'location' => 'uri', 'locationName' => 'sessionId', ], 'description' => [ 'shape' => 'Description', ], 'tagFilter' => [ 'shape' => 'TagFilter', ], 'aiAgentConfiguration' => [ 'shape' => 'AIAgentConfigurationMap', ], ], ], 'UpdateSessionResponse' => [ 'type' => 'structure', 'members' => [ 'session' => [ 'shape' => 'SessionData', ], ], ], 'UploadId' => [ 'type' => 'string', 'max' => 1200, 'min' => 1, ], 'Uri' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'Url' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'sensitive' => true, ], 'UrlConfiguration' => [ 'type' => 'structure', 'members' => [ 'seedUrls' => [ 'shape' => 'SeedUrls', ], ], ], 'UrlFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UrlFilterPattern', ], 'max' => 25, 'min' => 1, 'sensitive' => true, ], 'UrlFilterPattern' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'sensitive' => true, ], 'Uuid' => [ 'type' => 'string', 'pattern' => '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', ], 'UuidOrArn' => [ 'type' => 'string', 'pattern' => '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$|^arn:[a-z-]*?:wisdom:[a-z0-9-]*?:[0-9]{12}:[a-z-]*?/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}(?:/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}){0,2}', ], 'UuidOrArnOrEitherWithQualifier' => [ 'type' => 'string', 'pattern' => '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}(:[A-Z0-9_$]+){0,1}$|^arn:[a-z-]*?:wisdom:[a-z0-9-]*?:[0-9]{12}:[a-z-]*?/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}(?:/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}){0,2}(:[A-Z0-9_$]+){0,1}', ], 'UuidWithQualifier' => [ 'type' => 'string', 'pattern' => '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}(:[A-Z0-9_$]+){0,1}', ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'VectorIngestionConfiguration' => [ 'type' => 'structure', 'members' => [ 'chunkingConfiguration' => [ 'shape' => 'ChunkingConfiguration', ], 'parsingConfiguration' => [ 'shape' => 'ParsingConfiguration', ], ], ], 'Version' => [ 'type' => 'long', 'box' => true, 'min' => 1, ], 'VisibilityStatus' => [ 'type' => 'string', 'enum' => [ 'SAVED', 'PUBLISHED', ], ], 'WaitTimeSeconds' => [ 'type' => 'integer', 'max' => 20, 'min' => 0, ], 'WebCrawlerConfiguration' => [ 'type' => 'structure', 'required' => [ 'urlConfiguration', ], 'members' => [ 'urlConfiguration' => [ 'shape' => 'UrlConfiguration', ], 'crawlerLimits' => [ 'shape' => 'WebCrawlerLimits', ], 'inclusionFilters' => [ 'shape' => 'UrlFilterList', ], 'exclusionFilters' => [ 'shape' => 'UrlFilterList', ], 'scope' => [ 'shape' => 'WebScopeType', ], ], ], 'WebCrawlerLimits' => [ 'type' => 'structure', 'members' => [ 'rateLimit' => [ 'shape' => 'WebCrawlerLimitsRateLimitInteger', ], ], ], 'WebCrawlerLimitsRateLimitInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 300, 'min' => 1, ], 'WebScopeType' => [ 'type' => 'string', 'enum' => [ 'HOST_ONLY', 'SUBDOMAINS', ], ], 'WebUrl' => [ 'type' => 'string', 'pattern' => 'https?://[A-Za-z0-9][^\\s]*', ], ],];
