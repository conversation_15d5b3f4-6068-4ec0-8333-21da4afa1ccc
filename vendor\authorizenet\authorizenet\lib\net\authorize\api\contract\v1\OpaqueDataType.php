<?php

namespace net\authorize\api\contract\v1;

/**
 * Class representing OpaqueDataType
 *
 * 
 * XSD Type: opaqueDataType
 */
class OpaqueDataType implements \JsonSerializable
{

    /**
     * @property string $dataDescriptor
     */
    private $dataDescriptor = null;

    /**
     * @property string $dataValue
     */
    private $dataValue = null;

    /**
     * @property string $dataKey
     */
    private $dataKey = null;

    /**
     * @property \DateTime $expirationTimeStamp
     */
    private $expirationTimeStamp = null;

    /**
     * Gets as dataDescriptor
     *
     * @return string
     */
    public function getDataDescriptor()
    {
        return $this->dataDescriptor;
    }

    /**
     * Sets a new dataDescriptor
     *
     * @param string $dataDescriptor
     * @return self
     */
    public function setDataDescriptor($dataDescriptor)
    {
        $this->dataDescriptor = $dataDescriptor;
        return $this;
    }

    /**
     * Gets as dataValue
     *
     * @return string
     */
    public function getDataValue()
    {
        return $this->dataValue;
    }

    /**
     * Sets a new dataValue
     *
     * @param string $dataValue
     * @return self
     */
    public function setDataValue($dataValue)
    {
        $this->dataValue = $dataValue;
        return $this;
    }

    /**
     * Gets as dataKey
     *
     * @return string
     */
    public function getDataKey()
    {
        return $this->dataKey;
    }

    /**
     * Sets a new dataKey
     *
     * @param string $dataKey
     * @return self
     */
    public function setDataKey($dataKey)
    {
        $this->dataKey = $dataKey;
        return $this;
    }

    /**
     * Gets as expirationTimeStamp
     *
     * @return \DateTime
     */
    public function getExpirationTimeStamp()
    {
        return $this->expirationTimeStamp;
    }

    /**
     * Sets a new expirationTimeStamp
     *
     * @param \DateTime $expirationTimeStamp
     * @return self
     */
    public function setExpirationTimeStamp(\DateTime $expirationTimeStamp)
    {
        $this->expirationTimeStamp = $expirationTimeStamp;
        return $this;
    }


    // Json Serialize Code
    #[\ReturnTypeWillChange]
    public function jsonSerialize(){
        $values = array_filter((array)get_object_vars($this),
        function ($val){
            return !is_null($val);
        });
        $mapper = \net\authorize\util\Mapper::Instance();
        foreach($values as $key => $value){
            $classDetails = $mapper->getClass(get_class($this) , $key);
            if (isset($value)){
                if ($classDetails->className === 'Date'){
                    $dateTime = $value->format('Y-m-d');
                    $values[$key] = $dateTime;
                }
                else if ($classDetails->className === 'DateTime'){
                    $dateTime = $value->format('Y-m-d\TH:i:s\Z');
                    $values[$key] = $dateTime;
                }
                if (is_array($value)){
                    if (!$classDetails->isInlineArray){
                        $subKey = $classDetails->arrayEntryname;
                        $subArray = [$subKey => $value];
                        $values[$key] = $subArray;
                    }
                }
            }
        }
        return $values;
    }
    
    // Json Set Code
    public function set($data)
    {
        if(is_array($data) || is_object($data)) {
			$mapper = \net\authorize\util\Mapper::Instance();
			foreach($data AS $key => $value) {
				$classDetails = $mapper->getClass(get_class($this) , $key);
	 
				if($classDetails !== NULL ) {
					if ($classDetails->isArray) {
						if ($classDetails->isCustomDefined) {
							foreach($value AS $keyChild => $valueChild) {
								$type = new $classDetails->className;
								$type->set($valueChild);
								$this->{'addTo' . $key}($type);
							}
						}
						else if ($classDetails->className === 'DateTime' || $classDetails->className === 'Date' ) {
							foreach($value AS $keyChild => $valueChild) {
								$type = new \DateTime($valueChild);
								$this->{'addTo' . $key}($type);
							}
						}
						else {
							foreach($value AS $keyChild => $valueChild) {
								$this->{'addTo' . $key}($valueChild);
							}
						}
					}
					else {
						if ($classDetails->isCustomDefined){
							$type = new $classDetails->className;
							$type->set($value);
							$this->{'set' . $key}($type);
						}
						else if ($classDetails->className === 'DateTime' || $classDetails->className === 'Date' ) {
							$type = new \DateTime($value);
							$this->{'set' . $key}($type);
						}
						else {
							$this->{'set' . $key}($value);
						}
					}
				}
			}
		}
    }
    
}

