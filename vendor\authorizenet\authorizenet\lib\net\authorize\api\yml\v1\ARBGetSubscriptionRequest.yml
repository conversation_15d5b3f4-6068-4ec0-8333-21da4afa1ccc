net\authorize\api\contract\v1\ARBGetSubscriptionRequest:
    xml_root_name: ARBGetSubscriptionRequest
    xml_root_namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
    properties:
        subscriptionId:
            expose: true
            access_type: public_method
            serialized_name: subscriptionId
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getSubscriptionId
                setter: setSubscriptionId
            type: string
        includeTransactions:
            expose: true
            access_type: public_method
            serialized_name: includeTransactions
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getIncludeTransactions
                setter: setIncludeTransactions
            type: boolean
