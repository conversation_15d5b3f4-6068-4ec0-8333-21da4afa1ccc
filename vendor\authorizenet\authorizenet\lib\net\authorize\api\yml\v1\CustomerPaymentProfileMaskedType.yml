net\authorize\api\contract\v1\CustomerPaymentProfileMaskedType:
    properties:
        customerProfileId:
            expose: true
            access_type: public_method
            serialized_name: customerProfileId
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getCustomerProfileId
                setter: setCustomerProfileId
            type: string
        customerPaymentProfileId:
            expose: true
            access_type: public_method
            serialized_name: customerPaymentProfileId
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getCustomerPaymentProfileId
                setter: setCustomerPaymentProfileId
            type: string
        defaultPaymentProfile:
            expose: true
            access_type: public_method
            serialized_name: defaultPaymentProfile
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getDefaultPaymentProfile
                setter: setDefaultPaymentProfile
            type: boolean
        payment:
            expose: true
            access_type: public_method
            serialized_name: payment
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getPayment
                setter: setPayment
            type: net\authorize\api\contract\v1\PaymentMaskedType
        driversLicense:
            expose: true
            access_type: public_method
            serialized_name: driversLicense
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getDriversLicense
                setter: setDriversLicense
            type: net\authorize\api\contract\v1\DriversLicenseMaskedType
        taxId:
            expose: true
            access_type: public_method
            serialized_name: taxId
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getTaxId
                setter: setTaxId
            type: string
        subscriptionIds:
            expose: true
            access_type: public_method
            serialized_name: subscriptionIds
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getSubscriptionIds
                setter: setSubscriptionIds
            type: array<string>
            xml_list:
                inline: false
                skip_when_empty: true
                entry_name: subscriptionId
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
        originalNetworkTransId:
            expose: true
            access_type: public_method
            serialized_name: originalNetworkTransId
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getOriginalNetworkTransId
                setter: setOriginalNetworkTransId
            type: string
        originalAuthAmount:
            expose: true
            access_type: public_method
            serialized_name: originalAuthAmount
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getOriginalAuthAmount
                setter: setOriginalAuthAmount
            type: float
        excludeFromAccountUpdater:
            expose: true
            access_type: public_method
            serialized_name: excludeFromAccountUpdater
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getExcludeFromAccountUpdater
                setter: setExcludeFromAccountUpdater
            type: boolean
