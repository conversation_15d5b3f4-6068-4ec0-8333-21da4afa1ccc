# حماية ملفات البيانات الحساسة
<Files "*.csv">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.log">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.encrypted">
    Order Deny,Allow
    Deny from all
</Files>

# منع الوصول المباشر لملفات PHP
<Files "*.php">
    Order Deny,Allow
    Deny from all
</Files>

# منع عرض محتويات المجلد
Options -Indexes

# حماية من hotlinking
RewriteEngine On
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?yourdomain.com [NC]
RewriteRule \.(csv|log|encrypted)$ - [F]
