<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Insights
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Insights\V1;

use Twilio\Options;
use Twilio\Values;

abstract class CallSummariesOptions
{
    /**
     * @param string $from A calling party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
     * @param string $to A called party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
     * @param string $fromCarrier An origination carrier.
     * @param string $toCarrier A destination carrier.
     * @param string $fromCountryCode A source country code based on phone number in From.
     * @param string $toCountryCode A destination country code. Based on phone number in To.
     * @param bool $verifiedCaller A boolean flag indicating whether or not the caller was verified using SHAKEN/STIR.One of 'true' or 'false'.
     * @param bool $hasTag A boolean flag indicating the presence of one or more [Voice Insights Call Tags](https://www.twilio.com/docs/voice/voice-insights/api/call/details-call-tags).
     * @param string $startTime A Start time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 4h.
     * @param string $endTime An End Time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 0m.
     * @param string $callType A Call Type of the calls. One of `carrier`, `sip`, `trunking` or `client`.
     * @param string $callState A Call State of the calls. One of `ringing`, `completed`, `busy`, `fail`, `noanswer`, `canceled`, `answered`, `undialed`.
     * @param string $direction A Direction of the calls. One of `outbound_api`, `outbound_dial`, `inbound`, `trunking_originating`, `trunking_terminating`.
     * @param string $processingState A Processing State of the Call Summaries. One of `completed`, `partial` or `all`.
     * @param string $sortBy A Sort By criterion for the returned list of Call Summaries. One of `start_time` or `end_time`.
     * @param string $subaccount A unique SID identifier of a Subaccount.
     * @param bool $abnormalSession A boolean flag indicating an abnormal session where the last SIP response was not 200 OK.
     * @param string $answeredBy An Answered By value for the calls based on `Answering Machine Detection (AMD)`. One of `unknown`, `machine_start`, `machine_end_beep`, `machine_end_silence`, `machine_end_other`, `human` or `fax`.
     * @param string $answeredByAnnotation Either machine or human.
     * @param string $connectivityIssueAnnotation A Connectivity Issue with the calls. One of `no_connectivity_issue`, `invalid_number`, `caller_id`, `dropped_call`, or `number_reachability`.
     * @param string $qualityIssueAnnotation A subjective Quality Issue with the calls. One of `no_quality_issue`, `low_volume`, `choppy_robotic`, `echo`, `dtmf`, `latency`, `owa`, `static_noise`.
     * @param bool $spamAnnotation A boolean flag indicating spam calls.
     * @param string $callScoreAnnotation A Call Score of the calls. Use a range of 1-5 to indicate the call experience score, with the following mapping as a reference for the rated call [5: Excellent, 4: Good, 3 : Fair, 2 : Poor, 1: Bad].
     * @param bool $brandedEnabled A boolean flag indicating whether or not the calls were branded using Twilio Branded Calls. One of 'true' or 'false'
     * @param bool $voiceIntegrityEnabled A boolean flag indicating whether or not the phone number had voice integrity enabled.One of 'true' or 'false'
     * @param string $brandedBundleSid A unique SID identifier of the Branded Call.
     * @param string $voiceIntegrityBundleSid A unique SID identifier of the Voice Integrity Profile.
     * @param string $voiceIntegrityUseCase A Voice Integrity Use Case . Is of type enum. One of 'abandoned_cart', 'appointment_reminders', 'appointment_scheduling', 'asset_management', 'automated_support', 'call_tracking', 'click_to_call', 'contact_tracing', 'contactless_delivery', 'customer_support', 'dating/social', 'delivery_notifications', 'distance_learning', 'emergency_notifications', 'employee_notifications', 'exam_proctoring', 'field_notifications', 'first_responder', 'fraud_alerts', 'group_messaging', 'identify_&_verification', 'intelligent_routing', 'lead_alerts', 'lead_distribution', 'lead_generation', 'lead_management', 'lead_nurturing', 'marketing_events', 'mass_alerts', 'meetings/collaboration', 'order_notifications', 'outbound_dialer', 'pharmacy', 'phone_system', 'purchase_confirmation', 'remote_appointments', 'rewards_program', 'self-service', 'service_alerts', 'shift_management', 'survey/research', 'telehealth', 'telemarketing', 'therapy_(individual+group)'.
     * @param string $businessProfileIdentity A Business Identity of the calls. Is of type enum. One of 'direct_customer', 'isv_reseller_or_partner'.
     * @param string $businessProfileIndustry A Business Industry of the calls. Is of type enum. One of 'automotive', 'agriculture', 'banking', 'consumer', 'construction', 'education', 'engineering', 'energy', 'oil_and_gas', 'fast_moving_consumer_goods', 'financial', 'fintech', 'food_and_beverage', 'government', 'healthcare', 'hospitality', 'insurance', 'legal', 'manufacturing', 'media', 'online', 'professional_services', 'raw_materials', 'real_estate', 'religion', 'retail', 'jewelry', 'technology', 'telecommunications', 'transportation', 'travel', 'electronics', 'not_for_profit'
     * @param string $businessProfileBundleSid A unique SID identifier of the Business Profile.
     * @param string $businessProfileType A Business Profile Type of the calls. Is of type enum. One of 'primary', 'secondary'.
     * @return ReadCallSummariesOptions Options builder
     */
    public static function read(
        
        string $from = Values::NONE,
        string $to = Values::NONE,
        string $fromCarrier = Values::NONE,
        string $toCarrier = Values::NONE,
        string $fromCountryCode = Values::NONE,
        string $toCountryCode = Values::NONE,
        bool $verifiedCaller = Values::BOOL_NONE,
        bool $hasTag = Values::BOOL_NONE,
        string $startTime = Values::NONE,
        string $endTime = Values::NONE,
        string $callType = Values::NONE,
        string $callState = Values::NONE,
        string $direction = Values::NONE,
        string $processingState = Values::NONE,
        string $sortBy = Values::NONE,
        string $subaccount = Values::NONE,
        bool $abnormalSession = Values::BOOL_NONE,
        string $answeredBy = Values::NONE,
        string $answeredByAnnotation = Values::NONE,
        string $connectivityIssueAnnotation = Values::NONE,
        string $qualityIssueAnnotation = Values::NONE,
        bool $spamAnnotation = Values::BOOL_NONE,
        string $callScoreAnnotation = Values::NONE,
        bool $brandedEnabled = Values::BOOL_NONE,
        bool $voiceIntegrityEnabled = Values::BOOL_NONE,
        string $brandedBundleSid = Values::NONE,
        string $voiceIntegrityBundleSid = Values::NONE,
        string $voiceIntegrityUseCase = Values::NONE,
        string $businessProfileIdentity = Values::NONE,
        string $businessProfileIndustry = Values::NONE,
        string $businessProfileBundleSid = Values::NONE,
        string $businessProfileType = Values::NONE

    ): ReadCallSummariesOptions
    {
        return new ReadCallSummariesOptions(
            $from,
            $to,
            $fromCarrier,
            $toCarrier,
            $fromCountryCode,
            $toCountryCode,
            $verifiedCaller,
            $hasTag,
            $startTime,
            $endTime,
            $callType,
            $callState,
            $direction,
            $processingState,
            $sortBy,
            $subaccount,
            $abnormalSession,
            $answeredBy,
            $answeredByAnnotation,
            $connectivityIssueAnnotation,
            $qualityIssueAnnotation,
            $spamAnnotation,
            $callScoreAnnotation,
            $brandedEnabled,
            $voiceIntegrityEnabled,
            $brandedBundleSid,
            $voiceIntegrityBundleSid,
            $voiceIntegrityUseCase,
            $businessProfileIdentity,
            $businessProfileIndustry,
            $businessProfileBundleSid,
            $businessProfileType
        );
    }

}

class ReadCallSummariesOptions extends Options
    {
    /**
     * @param string $from A calling party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
     * @param string $to A called party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
     * @param string $fromCarrier An origination carrier.
     * @param string $toCarrier A destination carrier.
     * @param string $fromCountryCode A source country code based on phone number in From.
     * @param string $toCountryCode A destination country code. Based on phone number in To.
     * @param bool $verifiedCaller A boolean flag indicating whether or not the caller was verified using SHAKEN/STIR.One of 'true' or 'false'.
     * @param bool $hasTag A boolean flag indicating the presence of one or more [Voice Insights Call Tags](https://www.twilio.com/docs/voice/voice-insights/api/call/details-call-tags).
     * @param string $startTime A Start time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 4h.
     * @param string $endTime An End Time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 0m.
     * @param string $callType A Call Type of the calls. One of `carrier`, `sip`, `trunking` or `client`.
     * @param string $callState A Call State of the calls. One of `ringing`, `completed`, `busy`, `fail`, `noanswer`, `canceled`, `answered`, `undialed`.
     * @param string $direction A Direction of the calls. One of `outbound_api`, `outbound_dial`, `inbound`, `trunking_originating`, `trunking_terminating`.
     * @param string $processingState A Processing State of the Call Summaries. One of `completed`, `partial` or `all`.
     * @param string $sortBy A Sort By criterion for the returned list of Call Summaries. One of `start_time` or `end_time`.
     * @param string $subaccount A unique SID identifier of a Subaccount.
     * @param bool $abnormalSession A boolean flag indicating an abnormal session where the last SIP response was not 200 OK.
     * @param string $answeredBy An Answered By value for the calls based on `Answering Machine Detection (AMD)`. One of `unknown`, `machine_start`, `machine_end_beep`, `machine_end_silence`, `machine_end_other`, `human` or `fax`.
     * @param string $answeredByAnnotation Either machine or human.
     * @param string $connectivityIssueAnnotation A Connectivity Issue with the calls. One of `no_connectivity_issue`, `invalid_number`, `caller_id`, `dropped_call`, or `number_reachability`.
     * @param string $qualityIssueAnnotation A subjective Quality Issue with the calls. One of `no_quality_issue`, `low_volume`, `choppy_robotic`, `echo`, `dtmf`, `latency`, `owa`, `static_noise`.
     * @param bool $spamAnnotation A boolean flag indicating spam calls.
     * @param string $callScoreAnnotation A Call Score of the calls. Use a range of 1-5 to indicate the call experience score, with the following mapping as a reference for the rated call [5: Excellent, 4: Good, 3 : Fair, 2 : Poor, 1: Bad].
     * @param bool $brandedEnabled A boolean flag indicating whether or not the calls were branded using Twilio Branded Calls. One of 'true' or 'false'
     * @param bool $voiceIntegrityEnabled A boolean flag indicating whether or not the phone number had voice integrity enabled.One of 'true' or 'false'
     * @param string $brandedBundleSid A unique SID identifier of the Branded Call.
     * @param string $voiceIntegrityBundleSid A unique SID identifier of the Voice Integrity Profile.
     * @param string $voiceIntegrityUseCase A Voice Integrity Use Case . Is of type enum. One of 'abandoned_cart', 'appointment_reminders', 'appointment_scheduling', 'asset_management', 'automated_support', 'call_tracking', 'click_to_call', 'contact_tracing', 'contactless_delivery', 'customer_support', 'dating/social', 'delivery_notifications', 'distance_learning', 'emergency_notifications', 'employee_notifications', 'exam_proctoring', 'field_notifications', 'first_responder', 'fraud_alerts', 'group_messaging', 'identify_&_verification', 'intelligent_routing', 'lead_alerts', 'lead_distribution', 'lead_generation', 'lead_management', 'lead_nurturing', 'marketing_events', 'mass_alerts', 'meetings/collaboration', 'order_notifications', 'outbound_dialer', 'pharmacy', 'phone_system', 'purchase_confirmation', 'remote_appointments', 'rewards_program', 'self-service', 'service_alerts', 'shift_management', 'survey/research', 'telehealth', 'telemarketing', 'therapy_(individual+group)'.
     * @param string $businessProfileIdentity A Business Identity of the calls. Is of type enum. One of 'direct_customer', 'isv_reseller_or_partner'.
     * @param string $businessProfileIndustry A Business Industry of the calls. Is of type enum. One of 'automotive', 'agriculture', 'banking', 'consumer', 'construction', 'education', 'engineering', 'energy', 'oil_and_gas', 'fast_moving_consumer_goods', 'financial', 'fintech', 'food_and_beverage', 'government', 'healthcare', 'hospitality', 'insurance', 'legal', 'manufacturing', 'media', 'online', 'professional_services', 'raw_materials', 'real_estate', 'religion', 'retail', 'jewelry', 'technology', 'telecommunications', 'transportation', 'travel', 'electronics', 'not_for_profit'
     * @param string $businessProfileBundleSid A unique SID identifier of the Business Profile.
     * @param string $businessProfileType A Business Profile Type of the calls. Is of type enum. One of 'primary', 'secondary'.
     */
    public function __construct(
        
        string $from = Values::NONE,
        string $to = Values::NONE,
        string $fromCarrier = Values::NONE,
        string $toCarrier = Values::NONE,
        string $fromCountryCode = Values::NONE,
        string $toCountryCode = Values::NONE,
        bool $verifiedCaller = Values::BOOL_NONE,
        bool $hasTag = Values::BOOL_NONE,
        string $startTime = Values::NONE,
        string $endTime = Values::NONE,
        string $callType = Values::NONE,
        string $callState = Values::NONE,
        string $direction = Values::NONE,
        string $processingState = Values::NONE,
        string $sortBy = Values::NONE,
        string $subaccount = Values::NONE,
        bool $abnormalSession = Values::BOOL_NONE,
        string $answeredBy = Values::NONE,
        string $answeredByAnnotation = Values::NONE,
        string $connectivityIssueAnnotation = Values::NONE,
        string $qualityIssueAnnotation = Values::NONE,
        bool $spamAnnotation = Values::BOOL_NONE,
        string $callScoreAnnotation = Values::NONE,
        bool $brandedEnabled = Values::BOOL_NONE,
        bool $voiceIntegrityEnabled = Values::BOOL_NONE,
        string $brandedBundleSid = Values::NONE,
        string $voiceIntegrityBundleSid = Values::NONE,
        string $voiceIntegrityUseCase = Values::NONE,
        string $businessProfileIdentity = Values::NONE,
        string $businessProfileIndustry = Values::NONE,
        string $businessProfileBundleSid = Values::NONE,
        string $businessProfileType = Values::NONE

    ) {
        $this->options['from'] = $from;
        $this->options['to'] = $to;
        $this->options['fromCarrier'] = $fromCarrier;
        $this->options['toCarrier'] = $toCarrier;
        $this->options['fromCountryCode'] = $fromCountryCode;
        $this->options['toCountryCode'] = $toCountryCode;
        $this->options['verifiedCaller'] = $verifiedCaller;
        $this->options['hasTag'] = $hasTag;
        $this->options['startTime'] = $startTime;
        $this->options['endTime'] = $endTime;
        $this->options['callType'] = $callType;
        $this->options['callState'] = $callState;
        $this->options['direction'] = $direction;
        $this->options['processingState'] = $processingState;
        $this->options['sortBy'] = $sortBy;
        $this->options['subaccount'] = $subaccount;
        $this->options['abnormalSession'] = $abnormalSession;
        $this->options['answeredBy'] = $answeredBy;
        $this->options['answeredByAnnotation'] = $answeredByAnnotation;
        $this->options['connectivityIssueAnnotation'] = $connectivityIssueAnnotation;
        $this->options['qualityIssueAnnotation'] = $qualityIssueAnnotation;
        $this->options['spamAnnotation'] = $spamAnnotation;
        $this->options['callScoreAnnotation'] = $callScoreAnnotation;
        $this->options['brandedEnabled'] = $brandedEnabled;
        $this->options['voiceIntegrityEnabled'] = $voiceIntegrityEnabled;
        $this->options['brandedBundleSid'] = $brandedBundleSid;
        $this->options['voiceIntegrityBundleSid'] = $voiceIntegrityBundleSid;
        $this->options['voiceIntegrityUseCase'] = $voiceIntegrityUseCase;
        $this->options['businessProfileIdentity'] = $businessProfileIdentity;
        $this->options['businessProfileIndustry'] = $businessProfileIndustry;
        $this->options['businessProfileBundleSid'] = $businessProfileBundleSid;
        $this->options['businessProfileType'] = $businessProfileType;
    }

    /**
     * A calling party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
     *
     * @param string $from A calling party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
     * @return $this Fluent Builder
     */
    public function setFrom(string $from): self
    {
        $this->options['from'] = $from;
        return $this;
    }

    /**
     * A called party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
     *
     * @param string $to A called party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
     * @return $this Fluent Builder
     */
    public function setTo(string $to): self
    {
        $this->options['to'] = $to;
        return $this;
    }

    /**
     * An origination carrier.
     *
     * @param string $fromCarrier An origination carrier.
     * @return $this Fluent Builder
     */
    public function setFromCarrier(string $fromCarrier): self
    {
        $this->options['fromCarrier'] = $fromCarrier;
        return $this;
    }

    /**
     * A destination carrier.
     *
     * @param string $toCarrier A destination carrier.
     * @return $this Fluent Builder
     */
    public function setToCarrier(string $toCarrier): self
    {
        $this->options['toCarrier'] = $toCarrier;
        return $this;
    }

    /**
     * A source country code based on phone number in From.
     *
     * @param string $fromCountryCode A source country code based on phone number in From.
     * @return $this Fluent Builder
     */
    public function setFromCountryCode(string $fromCountryCode): self
    {
        $this->options['fromCountryCode'] = $fromCountryCode;
        return $this;
    }

    /**
     * A destination country code. Based on phone number in To.
     *
     * @param string $toCountryCode A destination country code. Based on phone number in To.
     * @return $this Fluent Builder
     */
    public function setToCountryCode(string $toCountryCode): self
    {
        $this->options['toCountryCode'] = $toCountryCode;
        return $this;
    }

    /**
     * A boolean flag indicating whether or not the caller was verified using SHAKEN/STIR.One of 'true' or 'false'.
     *
     * @param bool $verifiedCaller A boolean flag indicating whether or not the caller was verified using SHAKEN/STIR.One of 'true' or 'false'.
     * @return $this Fluent Builder
     */
    public function setVerifiedCaller(bool $verifiedCaller): self
    {
        $this->options['verifiedCaller'] = $verifiedCaller;
        return $this;
    }

    /**
     * A boolean flag indicating the presence of one or more [Voice Insights Call Tags](https://www.twilio.com/docs/voice/voice-insights/api/call/details-call-tags).
     *
     * @param bool $hasTag A boolean flag indicating the presence of one or more [Voice Insights Call Tags](https://www.twilio.com/docs/voice/voice-insights/api/call/details-call-tags).
     * @return $this Fluent Builder
     */
    public function setHasTag(bool $hasTag): self
    {
        $this->options['hasTag'] = $hasTag;
        return $this;
    }

    /**
     * A Start time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 4h.
     *
     * @param string $startTime A Start time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 4h.
     * @return $this Fluent Builder
     */
    public function setStartTime(string $startTime): self
    {
        $this->options['startTime'] = $startTime;
        return $this;
    }

    /**
     * An End Time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 0m.
     *
     * @param string $endTime An End Time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 0m.
     * @return $this Fluent Builder
     */
    public function setEndTime(string $endTime): self
    {
        $this->options['endTime'] = $endTime;
        return $this;
    }

    /**
     * A Call Type of the calls. One of `carrier`, `sip`, `trunking` or `client`.
     *
     * @param string $callType A Call Type of the calls. One of `carrier`, `sip`, `trunking` or `client`.
     * @return $this Fluent Builder
     */
    public function setCallType(string $callType): self
    {
        $this->options['callType'] = $callType;
        return $this;
    }

    /**
     * A Call State of the calls. One of `ringing`, `completed`, `busy`, `fail`, `noanswer`, `canceled`, `answered`, `undialed`.
     *
     * @param string $callState A Call State of the calls. One of `ringing`, `completed`, `busy`, `fail`, `noanswer`, `canceled`, `answered`, `undialed`.
     * @return $this Fluent Builder
     */
    public function setCallState(string $callState): self
    {
        $this->options['callState'] = $callState;
        return $this;
    }

    /**
     * A Direction of the calls. One of `outbound_api`, `outbound_dial`, `inbound`, `trunking_originating`, `trunking_terminating`.
     *
     * @param string $direction A Direction of the calls. One of `outbound_api`, `outbound_dial`, `inbound`, `trunking_originating`, `trunking_terminating`.
     * @return $this Fluent Builder
     */
    public function setDirection(string $direction): self
    {
        $this->options['direction'] = $direction;
        return $this;
    }

    /**
     * A Processing State of the Call Summaries. One of `completed`, `partial` or `all`.
     *
     * @param string $processingState A Processing State of the Call Summaries. One of `completed`, `partial` or `all`.
     * @return $this Fluent Builder
     */
    public function setProcessingState(string $processingState): self
    {
        $this->options['processingState'] = $processingState;
        return $this;
    }

    /**
     * A Sort By criterion for the returned list of Call Summaries. One of `start_time` or `end_time`.
     *
     * @param string $sortBy A Sort By criterion for the returned list of Call Summaries. One of `start_time` or `end_time`.
     * @return $this Fluent Builder
     */
    public function setSortBy(string $sortBy): self
    {
        $this->options['sortBy'] = $sortBy;
        return $this;
    }

    /**
     * A unique SID identifier of a Subaccount.
     *
     * @param string $subaccount A unique SID identifier of a Subaccount.
     * @return $this Fluent Builder
     */
    public function setSubaccount(string $subaccount): self
    {
        $this->options['subaccount'] = $subaccount;
        return $this;
    }

    /**
     * A boolean flag indicating an abnormal session where the last SIP response was not 200 OK.
     *
     * @param bool $abnormalSession A boolean flag indicating an abnormal session where the last SIP response was not 200 OK.
     * @return $this Fluent Builder
     */
    public function setAbnormalSession(bool $abnormalSession): self
    {
        $this->options['abnormalSession'] = $abnormalSession;
        return $this;
    }

    /**
     * An Answered By value for the calls based on `Answering Machine Detection (AMD)`. One of `unknown`, `machine_start`, `machine_end_beep`, `machine_end_silence`, `machine_end_other`, `human` or `fax`.
     *
     * @param string $answeredBy An Answered By value for the calls based on `Answering Machine Detection (AMD)`. One of `unknown`, `machine_start`, `machine_end_beep`, `machine_end_silence`, `machine_end_other`, `human` or `fax`.
     * @return $this Fluent Builder
     */
    public function setAnsweredBy(string $answeredBy): self
    {
        $this->options['answeredBy'] = $answeredBy;
        return $this;
    }

    /**
     * Either machine or human.
     *
     * @param string $answeredByAnnotation Either machine or human.
     * @return $this Fluent Builder
     */
    public function setAnsweredByAnnotation(string $answeredByAnnotation): self
    {
        $this->options['answeredByAnnotation'] = $answeredByAnnotation;
        return $this;
    }

    /**
     * A Connectivity Issue with the calls. One of `no_connectivity_issue`, `invalid_number`, `caller_id`, `dropped_call`, or `number_reachability`.
     *
     * @param string $connectivityIssueAnnotation A Connectivity Issue with the calls. One of `no_connectivity_issue`, `invalid_number`, `caller_id`, `dropped_call`, or `number_reachability`.
     * @return $this Fluent Builder
     */
    public function setConnectivityIssueAnnotation(string $connectivityIssueAnnotation): self
    {
        $this->options['connectivityIssueAnnotation'] = $connectivityIssueAnnotation;
        return $this;
    }

    /**
     * A subjective Quality Issue with the calls. One of `no_quality_issue`, `low_volume`, `choppy_robotic`, `echo`, `dtmf`, `latency`, `owa`, `static_noise`.
     *
     * @param string $qualityIssueAnnotation A subjective Quality Issue with the calls. One of `no_quality_issue`, `low_volume`, `choppy_robotic`, `echo`, `dtmf`, `latency`, `owa`, `static_noise`.
     * @return $this Fluent Builder
     */
    public function setQualityIssueAnnotation(string $qualityIssueAnnotation): self
    {
        $this->options['qualityIssueAnnotation'] = $qualityIssueAnnotation;
        return $this;
    }

    /**
     * A boolean flag indicating spam calls.
     *
     * @param bool $spamAnnotation A boolean flag indicating spam calls.
     * @return $this Fluent Builder
     */
    public function setSpamAnnotation(bool $spamAnnotation): self
    {
        $this->options['spamAnnotation'] = $spamAnnotation;
        return $this;
    }

    /**
     * A Call Score of the calls. Use a range of 1-5 to indicate the call experience score, with the following mapping as a reference for the rated call [5: Excellent, 4: Good, 3 : Fair, 2 : Poor, 1: Bad].
     *
     * @param string $callScoreAnnotation A Call Score of the calls. Use a range of 1-5 to indicate the call experience score, with the following mapping as a reference for the rated call [5: Excellent, 4: Good, 3 : Fair, 2 : Poor, 1: Bad].
     * @return $this Fluent Builder
     */
    public function setCallScoreAnnotation(string $callScoreAnnotation): self
    {
        $this->options['callScoreAnnotation'] = $callScoreAnnotation;
        return $this;
    }

    /**
     * A boolean flag indicating whether or not the calls were branded using Twilio Branded Calls. One of 'true' or 'false'
     *
     * @param bool $brandedEnabled A boolean flag indicating whether or not the calls were branded using Twilio Branded Calls. One of 'true' or 'false'
     * @return $this Fluent Builder
     */
    public function setBrandedEnabled(bool $brandedEnabled): self
    {
        $this->options['brandedEnabled'] = $brandedEnabled;
        return $this;
    }

    /**
     * A boolean flag indicating whether or not the phone number had voice integrity enabled.One of 'true' or 'false'
     *
     * @param bool $voiceIntegrityEnabled A boolean flag indicating whether or not the phone number had voice integrity enabled.One of 'true' or 'false'
     * @return $this Fluent Builder
     */
    public function setVoiceIntegrityEnabled(bool $voiceIntegrityEnabled): self
    {
        $this->options['voiceIntegrityEnabled'] = $voiceIntegrityEnabled;
        return $this;
    }

    /**
     * A unique SID identifier of the Branded Call.
     *
     * @param string $brandedBundleSid A unique SID identifier of the Branded Call.
     * @return $this Fluent Builder
     */
    public function setBrandedBundleSid(string $brandedBundleSid): self
    {
        $this->options['brandedBundleSid'] = $brandedBundleSid;
        return $this;
    }

    /**
     * A unique SID identifier of the Voice Integrity Profile.
     *
     * @param string $voiceIntegrityBundleSid A unique SID identifier of the Voice Integrity Profile.
     * @return $this Fluent Builder
     */
    public function setVoiceIntegrityBundleSid(string $voiceIntegrityBundleSid): self
    {
        $this->options['voiceIntegrityBundleSid'] = $voiceIntegrityBundleSid;
        return $this;
    }

    /**
     * A Voice Integrity Use Case . Is of type enum. One of 'abandoned_cart', 'appointment_reminders', 'appointment_scheduling', 'asset_management', 'automated_support', 'call_tracking', 'click_to_call', 'contact_tracing', 'contactless_delivery', 'customer_support', 'dating/social', 'delivery_notifications', 'distance_learning', 'emergency_notifications', 'employee_notifications', 'exam_proctoring', 'field_notifications', 'first_responder', 'fraud_alerts', 'group_messaging', 'identify_&_verification', 'intelligent_routing', 'lead_alerts', 'lead_distribution', 'lead_generation', 'lead_management', 'lead_nurturing', 'marketing_events', 'mass_alerts', 'meetings/collaboration', 'order_notifications', 'outbound_dialer', 'pharmacy', 'phone_system', 'purchase_confirmation', 'remote_appointments', 'rewards_program', 'self-service', 'service_alerts', 'shift_management', 'survey/research', 'telehealth', 'telemarketing', 'therapy_(individual+group)'.
     *
     * @param string $voiceIntegrityUseCase A Voice Integrity Use Case . Is of type enum. One of 'abandoned_cart', 'appointment_reminders', 'appointment_scheduling', 'asset_management', 'automated_support', 'call_tracking', 'click_to_call', 'contact_tracing', 'contactless_delivery', 'customer_support', 'dating/social', 'delivery_notifications', 'distance_learning', 'emergency_notifications', 'employee_notifications', 'exam_proctoring', 'field_notifications', 'first_responder', 'fraud_alerts', 'group_messaging', 'identify_&_verification', 'intelligent_routing', 'lead_alerts', 'lead_distribution', 'lead_generation', 'lead_management', 'lead_nurturing', 'marketing_events', 'mass_alerts', 'meetings/collaboration', 'order_notifications', 'outbound_dialer', 'pharmacy', 'phone_system', 'purchase_confirmation', 'remote_appointments', 'rewards_program', 'self-service', 'service_alerts', 'shift_management', 'survey/research', 'telehealth', 'telemarketing', 'therapy_(individual+group)'.
     * @return $this Fluent Builder
     */
    public function setVoiceIntegrityUseCase(string $voiceIntegrityUseCase): self
    {
        $this->options['voiceIntegrityUseCase'] = $voiceIntegrityUseCase;
        return $this;
    }

    /**
     * A Business Identity of the calls. Is of type enum. One of 'direct_customer', 'isv_reseller_or_partner'.
     *
     * @param string $businessProfileIdentity A Business Identity of the calls. Is of type enum. One of 'direct_customer', 'isv_reseller_or_partner'.
     * @return $this Fluent Builder
     */
    public function setBusinessProfileIdentity(string $businessProfileIdentity): self
    {
        $this->options['businessProfileIdentity'] = $businessProfileIdentity;
        return $this;
    }

    /**
     * A Business Industry of the calls. Is of type enum. One of 'automotive', 'agriculture', 'banking', 'consumer', 'construction', 'education', 'engineering', 'energy', 'oil_and_gas', 'fast_moving_consumer_goods', 'financial', 'fintech', 'food_and_beverage', 'government', 'healthcare', 'hospitality', 'insurance', 'legal', 'manufacturing', 'media', 'online', 'professional_services', 'raw_materials', 'real_estate', 'religion', 'retail', 'jewelry', 'technology', 'telecommunications', 'transportation', 'travel', 'electronics', 'not_for_profit'
     *
     * @param string $businessProfileIndustry A Business Industry of the calls. Is of type enum. One of 'automotive', 'agriculture', 'banking', 'consumer', 'construction', 'education', 'engineering', 'energy', 'oil_and_gas', 'fast_moving_consumer_goods', 'financial', 'fintech', 'food_and_beverage', 'government', 'healthcare', 'hospitality', 'insurance', 'legal', 'manufacturing', 'media', 'online', 'professional_services', 'raw_materials', 'real_estate', 'religion', 'retail', 'jewelry', 'technology', 'telecommunications', 'transportation', 'travel', 'electronics', 'not_for_profit'
     * @return $this Fluent Builder
     */
    public function setBusinessProfileIndustry(string $businessProfileIndustry): self
    {
        $this->options['businessProfileIndustry'] = $businessProfileIndustry;
        return $this;
    }

    /**
     * A unique SID identifier of the Business Profile.
     *
     * @param string $businessProfileBundleSid A unique SID identifier of the Business Profile.
     * @return $this Fluent Builder
     */
    public function setBusinessProfileBundleSid(string $businessProfileBundleSid): self
    {
        $this->options['businessProfileBundleSid'] = $businessProfileBundleSid;
        return $this;
    }

    /**
     * A Business Profile Type of the calls. Is of type enum. One of 'primary', 'secondary'.
     *
     * @param string $businessProfileType A Business Profile Type of the calls. Is of type enum. One of 'primary', 'secondary'.
     * @return $this Fluent Builder
     */
    public function setBusinessProfileType(string $businessProfileType): self
    {
        $this->options['businessProfileType'] = $businessProfileType;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Insights.V1.ReadCallSummariesOptions ' . $options . ']';
    }
}

