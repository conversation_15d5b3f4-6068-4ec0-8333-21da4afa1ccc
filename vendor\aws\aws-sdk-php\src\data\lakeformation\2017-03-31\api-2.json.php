<?php
// This file was auto-generated from sdk-root/src/data/lakeformation/2017-03-31/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-03-31', 'endpointPrefix' => 'lakeformation', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS Lake Formation', 'serviceId' => 'LakeFormation', 'signatureVersion' => 'v4', 'signingName' => 'lakeformation', 'uid' => 'lakeformation-2017-03-31', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AddLFTagsToResource' => [ 'name' => 'AddLFTagsToResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/AddLFTagsToResource', ], 'input' => [ 'shape' => 'AddLFTagsToResourceRequest', ], 'output' => [ 'shape' => 'AddLFTagsToResourceResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'AssumeDecoratedRoleWithSAML' => [ 'name' => 'AssumeDecoratedRoleWithSAML', 'http' => [ 'method' => 'POST', 'requestUri' => '/AssumeDecoratedRoleWithSAML', ], 'input' => [ 'shape' => 'AssumeDecoratedRoleWithSAMLRequest', ], 'output' => [ 'shape' => 'AssumeDecoratedRoleWithSAMLResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'BatchGrantPermissions' => [ 'name' => 'BatchGrantPermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/BatchGrantPermissions', ], 'input' => [ 'shape' => 'BatchGrantPermissionsRequest', ], 'output' => [ 'shape' => 'BatchGrantPermissionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'BatchRevokePermissions' => [ 'name' => 'BatchRevokePermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/BatchRevokePermissions', ], 'input' => [ 'shape' => 'BatchRevokePermissionsRequest', ], 'output' => [ 'shape' => 'BatchRevokePermissionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'CancelTransaction' => [ 'name' => 'CancelTransaction', 'http' => [ 'method' => 'POST', 'requestUri' => '/CancelTransaction', ], 'input' => [ 'shape' => 'CancelTransactionRequest', ], 'output' => [ 'shape' => 'CancelTransactionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'TransactionCommittedException', ], [ 'shape' => 'TransactionCommitInProgressException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'CommitTransaction' => [ 'name' => 'CommitTransaction', 'http' => [ 'method' => 'POST', 'requestUri' => '/CommitTransaction', ], 'input' => [ 'shape' => 'CommitTransactionRequest', ], 'output' => [ 'shape' => 'CommitTransactionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'TransactionCanceledException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'CreateDataCellsFilter' => [ 'name' => 'CreateDataCellsFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateDataCellsFilter', ], 'input' => [ 'shape' => 'CreateDataCellsFilterRequest', ], 'output' => [ 'shape' => 'CreateDataCellsFilterResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateLFTag' => [ 'name' => 'CreateLFTag', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateLFTag', ], 'input' => [ 'shape' => 'CreateLFTagRequest', ], 'output' => [ 'shape' => 'CreateLFTagResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateLFTagExpression' => [ 'name' => 'CreateLFTagExpression', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateLFTagExpression', ], 'input' => [ 'shape' => 'CreateLFTagExpressionRequest', ], 'output' => [ 'shape' => 'CreateLFTagExpressionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'CreateLakeFormationIdentityCenterConfiguration' => [ 'name' => 'CreateLakeFormationIdentityCenterConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateLakeFormationIdentityCenterConfiguration', ], 'input' => [ 'shape' => 'CreateLakeFormationIdentityCenterConfigurationRequest', ], 'output' => [ 'shape' => 'CreateLakeFormationIdentityCenterConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'CreateLakeFormationOptIn' => [ 'name' => 'CreateLakeFormationOptIn', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateLakeFormationOptIn', ], 'input' => [ 'shape' => 'CreateLakeFormationOptInRequest', ], 'output' => [ 'shape' => 'CreateLakeFormationOptInResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], ], ], 'DeleteDataCellsFilter' => [ 'name' => 'DeleteDataCellsFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteDataCellsFilter', ], 'input' => [ 'shape' => 'DeleteDataCellsFilterRequest', ], 'output' => [ 'shape' => 'DeleteDataCellsFilterResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteLFTag' => [ 'name' => 'DeleteLFTag', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteLFTag', ], 'input' => [ 'shape' => 'DeleteLFTagRequest', ], 'output' => [ 'shape' => 'DeleteLFTagResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteLFTagExpression' => [ 'name' => 'DeleteLFTagExpression', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteLFTagExpression', ], 'input' => [ 'shape' => 'DeleteLFTagExpressionRequest', ], 'output' => [ 'shape' => 'DeleteLFTagExpressionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteLakeFormationIdentityCenterConfiguration' => [ 'name' => 'DeleteLakeFormationIdentityCenterConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteLakeFormationIdentityCenterConfiguration', ], 'input' => [ 'shape' => 'DeleteLakeFormationIdentityCenterConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteLakeFormationIdentityCenterConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteLakeFormationOptIn' => [ 'name' => 'DeleteLakeFormationOptIn', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteLakeFormationOptIn', ], 'input' => [ 'shape' => 'DeleteLakeFormationOptInRequest', ], 'output' => [ 'shape' => 'DeleteLakeFormationOptInResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteObjectsOnCancel' => [ 'name' => 'DeleteObjectsOnCancel', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteObjectsOnCancel', ], 'input' => [ 'shape' => 'DeleteObjectsOnCancelRequest', ], 'output' => [ 'shape' => 'DeleteObjectsOnCancelResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'TransactionCommittedException', ], [ 'shape' => 'TransactionCanceledException', ], [ 'shape' => 'ResourceNotReadyException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeregisterResource' => [ 'name' => 'DeregisterResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeregisterResource', ], 'input' => [ 'shape' => 'DeregisterResourceRequest', ], 'output' => [ 'shape' => 'DeregisterResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'DescribeLakeFormationIdentityCenterConfiguration' => [ 'name' => 'DescribeLakeFormationIdentityCenterConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeLakeFormationIdentityCenterConfiguration', ], 'input' => [ 'shape' => 'DescribeLakeFormationIdentityCenterConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeLakeFormationIdentityCenterConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeResource' => [ 'name' => 'DescribeResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeResource', ], 'input' => [ 'shape' => 'DescribeResourceRequest', ], 'output' => [ 'shape' => 'DescribeResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'DescribeTransaction' => [ 'name' => 'DescribeTransaction', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeTransaction', ], 'input' => [ 'shape' => 'DescribeTransactionRequest', ], 'output' => [ 'shape' => 'DescribeTransactionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'ExtendTransaction' => [ 'name' => 'ExtendTransaction', 'http' => [ 'method' => 'POST', 'requestUri' => '/ExtendTransaction', ], 'input' => [ 'shape' => 'ExtendTransactionRequest', ], 'output' => [ 'shape' => 'ExtendTransactionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'TransactionCommittedException', ], [ 'shape' => 'TransactionCanceledException', ], [ 'shape' => 'TransactionCommitInProgressException', ], ], ], 'GetDataCellsFilter' => [ 'name' => 'GetDataCellsFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetDataCellsFilter', ], 'input' => [ 'shape' => 'GetDataCellsFilterRequest', ], 'output' => [ 'shape' => 'GetDataCellsFilterResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetDataLakePrincipal' => [ 'name' => 'GetDataLakePrincipal', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetDataLakePrincipal', ], 'input' => [ 'shape' => 'GetDataLakePrincipalRequest', ], 'output' => [ 'shape' => 'GetDataLakePrincipalResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetDataLakeSettings' => [ 'name' => 'GetDataLakeSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetDataLakeSettings', ], 'input' => [ 'shape' => 'GetDataLakeSettingsRequest', ], 'output' => [ 'shape' => 'GetDataLakeSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'GetEffectivePermissionsForPath' => [ 'name' => 'GetEffectivePermissionsForPath', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetEffectivePermissionsForPath', ], 'input' => [ 'shape' => 'GetEffectivePermissionsForPathRequest', ], 'output' => [ 'shape' => 'GetEffectivePermissionsForPathResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetLFTag' => [ 'name' => 'GetLFTag', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetLFTag', ], 'input' => [ 'shape' => 'GetLFTagRequest', ], 'output' => [ 'shape' => 'GetLFTagResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetLFTagExpression' => [ 'name' => 'GetLFTagExpression', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetLFTagExpression', ], 'input' => [ 'shape' => 'GetLFTagExpressionRequest', ], 'output' => [ 'shape' => 'GetLFTagExpressionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetQueryState' => [ 'name' => 'GetQueryState', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetQueryState', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetQueryStateRequest', ], 'output' => [ 'shape' => 'GetQueryStateResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'query-', ], ], 'GetQueryStatistics' => [ 'name' => 'GetQueryStatistics', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetQueryStatistics', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetQueryStatisticsRequest', ], 'output' => [ 'shape' => 'GetQueryStatisticsResponse', ], 'errors' => [ [ 'shape' => 'StatisticsNotReadyYetException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ExpiredException', ], [ 'shape' => 'ThrottledException', ], ], 'endpoint' => [ 'hostPrefix' => 'query-', ], ], 'GetResourceLFTags' => [ 'name' => 'GetResourceLFTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetResourceLFTags', ], 'input' => [ 'shape' => 'GetResourceLFTagsRequest', ], 'output' => [ 'shape' => 'GetResourceLFTagsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetTableObjects' => [ 'name' => 'GetTableObjects', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetTableObjects', ], 'input' => [ 'shape' => 'GetTableObjectsRequest', ], 'output' => [ 'shape' => 'GetTableObjectsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'TransactionCommittedException', ], [ 'shape' => 'TransactionCanceledException', ], [ 'shape' => 'ResourceNotReadyException', ], ], ], 'GetTemporaryGluePartitionCredentials' => [ 'name' => 'GetTemporaryGluePartitionCredentials', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetTemporaryGluePartitionCredentials', ], 'input' => [ 'shape' => 'GetTemporaryGluePartitionCredentialsRequest', ], 'output' => [ 'shape' => 'GetTemporaryGluePartitionCredentialsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'PermissionTypeMismatchException', ], ], ], 'GetTemporaryGlueTableCredentials' => [ 'name' => 'GetTemporaryGlueTableCredentials', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetTemporaryGlueTableCredentials', ], 'input' => [ 'shape' => 'GetTemporaryGlueTableCredentialsRequest', ], 'output' => [ 'shape' => 'GetTemporaryGlueTableCredentialsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'PermissionTypeMismatchException', ], ], ], 'GetWorkUnitResults' => [ 'name' => 'GetWorkUnitResults', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetWorkUnitResults', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetWorkUnitResultsRequest', ], 'output' => [ 'shape' => 'GetWorkUnitResultsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ExpiredException', ], [ 'shape' => 'ThrottledException', ], ], 'endpoint' => [ 'hostPrefix' => 'data-', ], ], 'GetWorkUnits' => [ 'name' => 'GetWorkUnits', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetWorkUnits', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetWorkUnitsRequest', ], 'output' => [ 'shape' => 'GetWorkUnitsResponse', ], 'errors' => [ [ 'shape' => 'WorkUnitsNotReadyYetException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ExpiredException', ], ], 'endpoint' => [ 'hostPrefix' => 'query-', ], ], 'GrantPermissions' => [ 'name' => 'GrantPermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/GrantPermissions', ], 'input' => [ 'shape' => 'GrantPermissionsRequest', ], 'output' => [ 'shape' => 'GrantPermissionsResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'ListDataCellsFilter' => [ 'name' => 'ListDataCellsFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListDataCellsFilter', ], 'input' => [ 'shape' => 'ListDataCellsFilterRequest', ], 'output' => [ 'shape' => 'ListDataCellsFilterResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListLFTagExpressions' => [ 'name' => 'ListLFTagExpressions', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListLFTagExpressions', ], 'input' => [ 'shape' => 'ListLFTagExpressionsRequest', ], 'output' => [ 'shape' => 'ListLFTagExpressionsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListLFTags' => [ 'name' => 'ListLFTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListLFTags', ], 'input' => [ 'shape' => 'ListLFTagsRequest', ], 'output' => [ 'shape' => 'ListLFTagsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListLakeFormationOptIns' => [ 'name' => 'ListLakeFormationOptIns', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListLakeFormationOptIns', ], 'input' => [ 'shape' => 'ListLakeFormationOptInsRequest', ], 'output' => [ 'shape' => 'ListLakeFormationOptInsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListPermissions' => [ 'name' => 'ListPermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListPermissions', ], 'input' => [ 'shape' => 'ListPermissionsRequest', ], 'output' => [ 'shape' => 'ListPermissionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListResources' => [ 'name' => 'ListResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListResources', ], 'input' => [ 'shape' => 'ListResourcesRequest', ], 'output' => [ 'shape' => 'ListResourcesResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'ListTableStorageOptimizers' => [ 'name' => 'ListTableStorageOptimizers', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListTableStorageOptimizers', ], 'input' => [ 'shape' => 'ListTableStorageOptimizersRequest', ], 'output' => [ 'shape' => 'ListTableStorageOptimizersResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListTransactions' => [ 'name' => 'ListTransactions', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListTransactions', ], 'input' => [ 'shape' => 'ListTransactionsRequest', ], 'output' => [ 'shape' => 'ListTransactionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'PutDataLakeSettings' => [ 'name' => 'PutDataLakeSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/PutDataLakeSettings', ], 'input' => [ 'shape' => 'PutDataLakeSettingsRequest', ], 'output' => [ 'shape' => 'PutDataLakeSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'RegisterResource' => [ 'name' => 'RegisterResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/RegisterResource', ], 'input' => [ 'shape' => 'RegisterResourceRequest', ], 'output' => [ 'shape' => 'RegisterResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'RemoveLFTagsFromResource' => [ 'name' => 'RemoveLFTagsFromResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/RemoveLFTagsFromResource', ], 'input' => [ 'shape' => 'RemoveLFTagsFromResourceRequest', ], 'output' => [ 'shape' => 'RemoveLFTagsFromResourceResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'RevokePermissions' => [ 'name' => 'RevokePermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/RevokePermissions', ], 'input' => [ 'shape' => 'RevokePermissionsRequest', ], 'output' => [ 'shape' => 'RevokePermissionsResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'SearchDatabasesByLFTags' => [ 'name' => 'SearchDatabasesByLFTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/SearchDatabasesByLFTags', ], 'input' => [ 'shape' => 'SearchDatabasesByLFTagsRequest', ], 'output' => [ 'shape' => 'SearchDatabasesByLFTagsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'SearchTablesByLFTags' => [ 'name' => 'SearchTablesByLFTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/SearchTablesByLFTags', ], 'input' => [ 'shape' => 'SearchTablesByLFTagsRequest', ], 'output' => [ 'shape' => 'SearchTablesByLFTagsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StartQueryPlanning' => [ 'name' => 'StartQueryPlanning', 'http' => [ 'method' => 'POST', 'requestUri' => '/StartQueryPlanning', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartQueryPlanningRequest', ], 'output' => [ 'shape' => 'StartQueryPlanningResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottledException', ], ], 'endpoint' => [ 'hostPrefix' => 'query-', ], ], 'StartTransaction' => [ 'name' => 'StartTransaction', 'http' => [ 'method' => 'POST', 'requestUri' => '/StartTransaction', ], 'input' => [ 'shape' => 'StartTransactionRequest', ], 'output' => [ 'shape' => 'StartTransactionResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'UpdateDataCellsFilter' => [ 'name' => 'UpdateDataCellsFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateDataCellsFilter', ], 'input' => [ 'shape' => 'UpdateDataCellsFilterRequest', ], 'output' => [ 'shape' => 'UpdateDataCellsFilterResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateLFTag' => [ 'name' => 'UpdateLFTag', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateLFTag', ], 'input' => [ 'shape' => 'UpdateLFTagRequest', ], 'output' => [ 'shape' => 'UpdateLFTagResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateLFTagExpression' => [ 'name' => 'UpdateLFTagExpression', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateLFTagExpression', ], 'input' => [ 'shape' => 'UpdateLFTagExpressionRequest', ], 'output' => [ 'shape' => 'UpdateLFTagExpressionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateLakeFormationIdentityCenterConfiguration' => [ 'name' => 'UpdateLakeFormationIdentityCenterConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateLakeFormationIdentityCenterConfiguration', ], 'input' => [ 'shape' => 'UpdateLakeFormationIdentityCenterConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateLakeFormationIdentityCenterConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'UpdateResource' => [ 'name' => 'UpdateResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateResource', ], 'input' => [ 'shape' => 'UpdateResourceRequest', ], 'output' => [ 'shape' => 'UpdateResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'UpdateTableObjects' => [ 'name' => 'UpdateTableObjects', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateTableObjects', ], 'input' => [ 'shape' => 'UpdateTableObjectsRequest', ], 'output' => [ 'shape' => 'UpdateTableObjectsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'TransactionCommittedException', ], [ 'shape' => 'TransactionCanceledException', ], [ 'shape' => 'TransactionCommitInProgressException', ], [ 'shape' => 'ResourceNotReadyException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'UpdateTableStorageOptimizer' => [ 'name' => 'UpdateTableStorageOptimizer', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateTableStorageOptimizer', ], 'input' => [ 'shape' => 'UpdateTableStorageOptimizerRequest', ], 'output' => [ 'shape' => 'UpdateTableStorageOptimizerResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccessKeyIdString' => [ 'type' => 'string', ], 'AddLFTagsToResourceRequest' => [ 'type' => 'structure', 'required' => [ 'Resource', 'LFTags', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Resource' => [ 'shape' => 'Resource', ], 'LFTags' => [ 'shape' => 'LFTagsList', ], ], ], 'AddLFTagsToResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Failures' => [ 'shape' => 'LFTagErrors', ], ], ], 'AddObjectInput' => [ 'type' => 'structure', 'required' => [ 'Uri', 'ETag', 'Size', ], 'members' => [ 'Uri' => [ 'shape' => 'URI', ], 'ETag' => [ 'shape' => 'ETagString', ], 'Size' => [ 'shape' => 'ObjectSize', ], 'PartitionValues' => [ 'shape' => 'PartitionValuesList', ], ], ], 'AdditionalContextMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ContextKey', ], 'value' => [ 'shape' => 'ContextValue', ], ], 'AllRowsWildcard' => [ 'type' => 'structure', 'members' => [], ], 'AlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ApplicationArn' => [ 'type' => 'string', ], 'ApplicationStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'AssumeDecoratedRoleWithSAMLRequest' => [ 'type' => 'structure', 'required' => [ 'SAMLAssertion', 'RoleArn', 'PrincipalArn', ], 'members' => [ 'SAMLAssertion' => [ 'shape' => 'SAMLAssertionString', ], 'RoleArn' => [ 'shape' => 'IAMRoleArn', ], 'PrincipalArn' => [ 'shape' => 'IAMSAMLProviderArn', ], 'DurationSeconds' => [ 'shape' => 'CredentialTimeoutDurationSecondInteger', ], ], ], 'AssumeDecoratedRoleWithSAMLResponse' => [ 'type' => 'structure', 'members' => [ 'AccessKeyId' => [ 'shape' => 'AccessKeyIdString', ], 'SecretAccessKey' => [ 'shape' => 'SecretAccessKeyString', ], 'SessionToken' => [ 'shape' => 'SessionTokenString', ], 'Expiration' => [ 'shape' => 'ExpirationTimestamp', ], ], ], 'AuditContext' => [ 'type' => 'structure', 'members' => [ 'AdditionalAuditContext' => [ 'shape' => 'AuditContextString', ], ], ], 'AuditContextString' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'AuthorizedSessionTagValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'BatchGrantPermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Entries' => [ 'shape' => 'BatchPermissionsRequestEntryList', ], ], ], 'BatchGrantPermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'Failures' => [ 'shape' => 'BatchPermissionsFailureList', ], ], ], 'BatchPermissionsFailureEntry' => [ 'type' => 'structure', 'members' => [ 'RequestEntry' => [ 'shape' => 'BatchPermissionsRequestEntry', ], 'Error' => [ 'shape' => 'ErrorDetail', ], ], ], 'BatchPermissionsFailureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPermissionsFailureEntry', ], ], 'BatchPermissionsRequestEntry' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'Identifier', ], 'Principal' => [ 'shape' => 'DataLakePrincipal', ], 'Resource' => [ 'shape' => 'Resource', ], 'Permissions' => [ 'shape' => 'PermissionList', ], 'Condition' => [ 'shape' => 'Condition', ], 'PermissionsWithGrantOption' => [ 'shape' => 'PermissionList', ], ], ], 'BatchPermissionsRequestEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPermissionsRequestEntry', ], ], 'BatchRevokePermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Entries' => [ 'shape' => 'BatchPermissionsRequestEntryList', ], ], ], 'BatchRevokePermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'Failures' => [ 'shape' => 'BatchPermissionsFailureList', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BooleanNullable' => [ 'type' => 'boolean', ], 'CancelTransactionRequest' => [ 'type' => 'structure', 'required' => [ 'TransactionId', ], 'members' => [ 'TransactionId' => [ 'shape' => 'TransactionIdString', ], ], ], 'CancelTransactionResponse' => [ 'type' => 'structure', 'members' => [], ], 'CatalogIdString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'CatalogResource' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'CatalogIdString', ], ], ], 'ColumnLFTag' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'LFTags' => [ 'shape' => 'LFTagsList', ], ], ], 'ColumnLFTagsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnLFTag', ], ], 'ColumnNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'ColumnWildcard' => [ 'type' => 'structure', 'members' => [ 'ExcludedColumnNames' => [ 'shape' => 'ColumnNames', ], ], ], 'CommitTransactionRequest' => [ 'type' => 'structure', 'required' => [ 'TransactionId', ], 'members' => [ 'TransactionId' => [ 'shape' => 'TransactionIdString', ], ], ], 'CommitTransactionResponse' => [ 'type' => 'structure', 'members' => [ 'TransactionStatus' => [ 'shape' => 'TransactionStatus', ], ], ], 'ComparisonOperator' => [ 'type' => 'string', 'enum' => [ 'EQ', 'NE', 'LE', 'LT', 'GE', 'GT', 'CONTAINS', 'NOT_CONTAINS', 'BEGINS_WITH', 'IN', 'BETWEEN', ], ], 'ConcurrentModificationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'Condition' => [ 'type' => 'structure', 'members' => [ 'Expression' => [ 'shape' => 'ExpressionString', ], ], ], 'ContextKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'ContextValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'CreateDataCellsFilterRequest' => [ 'type' => 'structure', 'required' => [ 'TableData', ], 'members' => [ 'TableData' => [ 'shape' => 'DataCellsFilter', ], ], ], 'CreateDataCellsFilterResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateLFTagExpressionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Expression', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Expression' => [ 'shape' => 'Expression', ], ], ], 'CreateLFTagExpressionResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateLFTagRequest' => [ 'type' => 'structure', 'required' => [ 'TagKey', 'TagValues', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'TagKey' => [ 'shape' => 'LFTagKey', ], 'TagValues' => [ 'shape' => 'TagValueList', ], ], ], 'CreateLFTagResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateLakeFormationIdentityCenterConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'InstanceArn' => [ 'shape' => 'IdentityCenterInstanceArn', ], 'ExternalFiltering' => [ 'shape' => 'ExternalFilteringConfiguration', ], 'ShareRecipients' => [ 'shape' => 'DataLakePrincipalList', ], ], ], 'CreateLakeFormationIdentityCenterConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], ], ], 'CreateLakeFormationOptInRequest' => [ 'type' => 'structure', 'required' => [ 'Principal', 'Resource', ], 'members' => [ 'Principal' => [ 'shape' => 'DataLakePrincipal', ], 'Resource' => [ 'shape' => 'Resource', ], 'Condition' => [ 'shape' => 'Condition', ], ], ], 'CreateLakeFormationOptInResponse' => [ 'type' => 'structure', 'members' => [], ], 'CredentialTimeoutDurationSecondInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 43200, 'min' => 900, ], 'DataCellsFilter' => [ 'type' => 'structure', 'required' => [ 'TableCatalogId', 'DatabaseName', 'TableName', 'Name', ], 'members' => [ 'TableCatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'Name' => [ 'shape' => 'NameString', ], 'RowFilter' => [ 'shape' => 'RowFilter', ], 'ColumnNames' => [ 'shape' => 'ColumnNames', ], 'ColumnWildcard' => [ 'shape' => 'ColumnWildcard', ], 'VersionId' => [ 'shape' => 'VersionString', ], ], ], 'DataCellsFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataCellsFilter', ], ], 'DataCellsFilterResource' => [ 'type' => 'structure', 'members' => [ 'TableCatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'Name' => [ 'shape' => 'NameString', ], ], ], 'DataLakePrincipal' => [ 'type' => 'structure', 'members' => [ 'DataLakePrincipalIdentifier' => [ 'shape' => 'DataLakePrincipalString', ], ], ], 'DataLakePrincipalList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataLakePrincipal', ], 'max' => 30, 'min' => 0, ], 'DataLakePrincipalString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'DataLakeResourceType' => [ 'type' => 'string', 'enum' => [ 'CATALOG', 'DATABASE', 'TABLE', 'DATA_LOCATION', 'LF_TAG', 'LF_TAG_POLICY', 'LF_TAG_POLICY_DATABASE', 'LF_TAG_POLICY_TABLE', 'LF_NAMED_TAG_EXPRESSION', ], ], 'DataLakeSettings' => [ 'type' => 'structure', 'members' => [ 'DataLakeAdmins' => [ 'shape' => 'DataLakePrincipalList', ], 'ReadOnlyAdmins' => [ 'shape' => 'DataLakePrincipalList', ], 'CreateDatabaseDefaultPermissions' => [ 'shape' => 'PrincipalPermissionsList', ], 'CreateTableDefaultPermissions' => [ 'shape' => 'PrincipalPermissionsList', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], 'TrustedResourceOwners' => [ 'shape' => 'TrustedResourceOwners', ], 'AllowExternalDataFiltering' => [ 'shape' => 'NullableBoolean', ], 'AllowFullTableExternalDataAccess' => [ 'shape' => 'NullableBoolean', ], 'ExternalDataFilteringAllowList' => [ 'shape' => 'DataLakePrincipalList', ], 'AuthorizedSessionTagValueList' => [ 'shape' => 'AuthorizedSessionTagValueList', ], ], ], 'DataLocationResource' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], ], ], 'DatabaseLFTagsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaggedDatabase', ], ], 'DatabaseResource' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Name' => [ 'shape' => 'NameString', ], ], ], 'DateTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'DeleteDataCellsFilterRequest' => [ 'type' => 'structure', 'members' => [ 'TableCatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteDataCellsFilterResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteLFTagExpressionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], ], ], 'DeleteLFTagExpressionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteLFTagRequest' => [ 'type' => 'structure', 'required' => [ 'TagKey', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'TagKey' => [ 'shape' => 'LFTagKey', ], ], ], 'DeleteLFTagResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteLakeFormationIdentityCenterConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], ], ], 'DeleteLakeFormationIdentityCenterConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteLakeFormationOptInRequest' => [ 'type' => 'structure', 'required' => [ 'Principal', 'Resource', ], 'members' => [ 'Principal' => [ 'shape' => 'DataLakePrincipal', ], 'Resource' => [ 'shape' => 'Resource', ], 'Condition' => [ 'shape' => 'Condition', ], ], ], 'DeleteLakeFormationOptInResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteObjectInput' => [ 'type' => 'structure', 'required' => [ 'Uri', ], 'members' => [ 'Uri' => [ 'shape' => 'URI', ], 'ETag' => [ 'shape' => 'ETagString', ], 'PartitionValues' => [ 'shape' => 'PartitionValuesList', ], ], ], 'DeleteObjectsOnCancelRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'TransactionId', 'Objects', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'TransactionId' => [ 'shape' => 'TransactionIdString', ], 'Objects' => [ 'shape' => 'VirtualObjectList', ], ], ], 'DeleteObjectsOnCancelResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeregisterResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], ], ], 'DeregisterResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeLakeFormationIdentityCenterConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], ], ], 'DescribeLakeFormationIdentityCenterConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'InstanceArn' => [ 'shape' => 'IdentityCenterInstanceArn', ], 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'ExternalFiltering' => [ 'shape' => 'ExternalFilteringConfiguration', ], 'ShareRecipients' => [ 'shape' => 'DataLakePrincipalList', ], 'ResourceShare' => [ 'shape' => 'RAMResourceShareArn', ], ], ], 'DescribeResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], ], ], 'DescribeResourceResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceInfo' => [ 'shape' => 'ResourceInfo', ], ], ], 'DescribeTransactionRequest' => [ 'type' => 'structure', 'required' => [ 'TransactionId', ], 'members' => [ 'TransactionId' => [ 'shape' => 'TransactionIdString', ], ], ], 'DescribeTransactionResponse' => [ 'type' => 'structure', 'members' => [ 'TransactionDescription' => [ 'shape' => 'TransactionDescription', ], ], ], 'DescriptionString' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'DetailsMap' => [ 'type' => 'structure', 'members' => [ 'ResourceShare' => [ 'shape' => 'ResourceShareList', ], ], ], 'ETagString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\p{L}\\p{N}\\p{P}]*', ], 'EnableStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'EntityNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ErrorDetail' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'NameString', ], 'ErrorMessage' => [ 'shape' => 'DescriptionString', ], ], ], 'ErrorMessageString' => [ 'type' => 'string', ], 'ExecutionStatistics' => [ 'type' => 'structure', 'members' => [ 'AverageExecutionTimeMillis' => [ 'shape' => 'NumberOfMilliseconds', ], 'DataScannedBytes' => [ 'shape' => 'NumberOfBytes', ], 'WorkUnitsExecutedCount' => [ 'shape' => 'NumberOfItems', ], ], ], 'ExpirationTimestamp' => [ 'type' => 'timestamp', ], 'ExpiredException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'error' => [ 'httpStatusCode' => 410, 'senderFault' => true, ], 'exception' => true, ], 'Expression' => [ 'type' => 'list', 'member' => [ 'shape' => 'LFTag', ], ], 'ExpressionString' => [ 'type' => 'string', 'max' => 3000, ], 'ExtendTransactionRequest' => [ 'type' => 'structure', 'members' => [ 'TransactionId' => [ 'shape' => 'TransactionIdString', ], ], ], 'ExtendTransactionResponse' => [ 'type' => 'structure', 'members' => [], ], 'ExternalFilteringConfiguration' => [ 'type' => 'structure', 'required' => [ 'Status', 'AuthorizedTargets', ], 'members' => [ 'Status' => [ 'shape' => 'EnableStatus', ], 'AuthorizedTargets' => [ 'shape' => 'ScopeTargets', ], ], ], 'FieldNameString' => [ 'type' => 'string', 'enum' => [ 'RESOURCE_ARN', 'ROLE_ARN', 'LAST_MODIFIED', ], ], 'FilterCondition' => [ 'type' => 'structure', 'members' => [ 'Field' => [ 'shape' => 'FieldNameString', ], 'ComparisonOperator' => [ 'shape' => 'ComparisonOperator', ], 'StringValueList' => [ 'shape' => 'StringValueList', ], ], ], 'FilterConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterCondition', ], 'max' => 20, 'min' => 1, ], 'GetDataCellsFilterRequest' => [ 'type' => 'structure', 'required' => [ 'TableCatalogId', 'DatabaseName', 'TableName', 'Name', ], 'members' => [ 'TableCatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'Name' => [ 'shape' => 'NameString', ], ], ], 'GetDataCellsFilterResponse' => [ 'type' => 'structure', 'members' => [ 'DataCellsFilter' => [ 'shape' => 'DataCellsFilter', ], ], ], 'GetDataLakePrincipalRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetDataLakePrincipalResponse' => [ 'type' => 'structure', 'members' => [ 'Identity' => [ 'shape' => 'IdentityString', ], ], ], 'GetDataLakeSettingsRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], ], ], 'GetDataLakeSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'DataLakeSettings' => [ 'shape' => 'DataLakeSettings', ], ], ], 'GetEffectivePermissionsForPathRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetEffectivePermissionsForPathResponse' => [ 'type' => 'structure', 'members' => [ 'Permissions' => [ 'shape' => 'PrincipalResourcePermissionsList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetLFTagExpressionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], ], ], 'GetLFTagExpressionResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Expression' => [ 'shape' => 'Expression', ], ], ], 'GetLFTagRequest' => [ 'type' => 'structure', 'required' => [ 'TagKey', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'TagKey' => [ 'shape' => 'LFTagKey', ], ], ], 'GetLFTagResponse' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'TagKey' => [ 'shape' => 'LFTagKey', ], 'TagValues' => [ 'shape' => 'TagValueList', ], ], ], 'GetQueryStateRequest' => [ 'type' => 'structure', 'required' => [ 'QueryId', ], 'members' => [ 'QueryId' => [ 'shape' => 'GetQueryStateRequestQueryIdString', ], ], ], 'GetQueryStateRequestQueryIdString' => [ 'type' => 'string', 'max' => 36, 'min' => 36, ], 'GetQueryStateResponse' => [ 'type' => 'structure', 'required' => [ 'State', ], 'members' => [ 'Error' => [ 'shape' => 'ErrorMessageString', ], 'State' => [ 'shape' => 'QueryStateString', ], ], ], 'GetQueryStatisticsRequest' => [ 'type' => 'structure', 'required' => [ 'QueryId', ], 'members' => [ 'QueryId' => [ 'shape' => 'GetQueryStatisticsRequestQueryIdString', ], ], ], 'GetQueryStatisticsRequestQueryIdString' => [ 'type' => 'string', 'max' => 36, 'min' => 36, ], 'GetQueryStatisticsResponse' => [ 'type' => 'structure', 'members' => [ 'ExecutionStatistics' => [ 'shape' => 'ExecutionStatistics', ], 'PlanningStatistics' => [ 'shape' => 'PlanningStatistics', ], 'QuerySubmissionTime' => [ 'shape' => 'DateTime', ], ], ], 'GetResourceLFTagsRequest' => [ 'type' => 'structure', 'required' => [ 'Resource', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Resource' => [ 'shape' => 'Resource', ], 'ShowAssignedLFTags' => [ 'shape' => 'BooleanNullable', ], ], ], 'GetResourceLFTagsResponse' => [ 'type' => 'structure', 'members' => [ 'LFTagOnDatabase' => [ 'shape' => 'LFTagsList', ], 'LFTagsOnTable' => [ 'shape' => 'LFTagsList', ], 'LFTagsOnColumns' => [ 'shape' => 'ColumnLFTagsList', ], ], ], 'GetTableObjectsRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'TransactionId' => [ 'shape' => 'TransactionIdString', ], 'QueryAsOfTime' => [ 'shape' => 'Timestamp', ], 'PartitionPredicate' => [ 'shape' => 'PredicateString', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'TokenString', ], ], ], 'GetTableObjectsResponse' => [ 'type' => 'structure', 'members' => [ 'Objects' => [ 'shape' => 'PartitionedTableObjectsList', ], 'NextToken' => [ 'shape' => 'TokenString', ], ], ], 'GetTemporaryGluePartitionCredentialsRequest' => [ 'type' => 'structure', 'required' => [ 'TableArn', 'Partition', ], 'members' => [ 'TableArn' => [ 'shape' => 'ResourceArnString', ], 'Partition' => [ 'shape' => 'PartitionValueList', ], 'Permissions' => [ 'shape' => 'PermissionList', ], 'DurationSeconds' => [ 'shape' => 'CredentialTimeoutDurationSecondInteger', ], 'AuditContext' => [ 'shape' => 'AuditContext', ], 'SupportedPermissionTypes' => [ 'shape' => 'PermissionTypeList', ], ], ], 'GetTemporaryGluePartitionCredentialsResponse' => [ 'type' => 'structure', 'members' => [ 'AccessKeyId' => [ 'shape' => 'AccessKeyIdString', ], 'SecretAccessKey' => [ 'shape' => 'SecretAccessKeyString', ], 'SessionToken' => [ 'shape' => 'SessionTokenString', ], 'Expiration' => [ 'shape' => 'ExpirationTimestamp', ], ], ], 'GetTemporaryGlueTableCredentialsRequest' => [ 'type' => 'structure', 'required' => [ 'TableArn', ], 'members' => [ 'TableArn' => [ 'shape' => 'ResourceArnString', ], 'Permissions' => [ 'shape' => 'PermissionList', ], 'DurationSeconds' => [ 'shape' => 'CredentialTimeoutDurationSecondInteger', ], 'AuditContext' => [ 'shape' => 'AuditContext', ], 'SupportedPermissionTypes' => [ 'shape' => 'PermissionTypeList', ], 'S3Path' => [ 'shape' => 'PathString', ], 'QuerySessionContext' => [ 'shape' => 'QuerySessionContext', ], ], ], 'GetTemporaryGlueTableCredentialsResponse' => [ 'type' => 'structure', 'members' => [ 'AccessKeyId' => [ 'shape' => 'AccessKeyIdString', ], 'SecretAccessKey' => [ 'shape' => 'SecretAccessKeyString', ], 'SessionToken' => [ 'shape' => 'SessionTokenString', ], 'Expiration' => [ 'shape' => 'ExpirationTimestamp', ], 'VendedS3Path' => [ 'shape' => 'PathStringList', ], ], ], 'GetWorkUnitResultsRequest' => [ 'type' => 'structure', 'required' => [ 'QueryId', 'WorkUnitId', 'WorkUnitToken', ], 'members' => [ 'QueryId' => [ 'shape' => 'GetWorkUnitResultsRequestQueryIdString', ], 'WorkUnitId' => [ 'shape' => 'GetWorkUnitResultsRequestWorkUnitIdLong', ], 'WorkUnitToken' => [ 'shape' => 'SyntheticGetWorkUnitResultsRequestWorkUnitTokenString', ], ], ], 'GetWorkUnitResultsRequestQueryIdString' => [ 'type' => 'string', 'max' => 36, 'min' => 36, ], 'GetWorkUnitResultsRequestWorkUnitIdLong' => [ 'type' => 'long', 'min' => 0, ], 'GetWorkUnitResultsResponse' => [ 'type' => 'structure', 'members' => [ 'ResultStream' => [ 'shape' => 'ResultStream', ], ], 'payload' => 'ResultStream', ], 'GetWorkUnitsRequest' => [ 'type' => 'structure', 'required' => [ 'QueryId', ], 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'PageSize' => [ 'shape' => 'Integer', ], 'QueryId' => [ 'shape' => 'GetWorkUnitsRequestQueryIdString', ], ], ], 'GetWorkUnitsRequestQueryIdString' => [ 'type' => 'string', 'max' => 36, 'min' => 36, ], 'GetWorkUnitsResponse' => [ 'type' => 'structure', 'required' => [ 'QueryId', 'WorkUnitRanges', ], 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'QueryId' => [ 'shape' => 'QueryIdString', ], 'WorkUnitRanges' => [ 'shape' => 'WorkUnitRangeList', ], ], ], 'GlueEncryptionException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'GrantPermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'Principal', 'Resource', 'Permissions', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Principal' => [ 'shape' => 'DataLakePrincipal', ], 'Resource' => [ 'shape' => 'Resource', ], 'Permissions' => [ 'shape' => 'PermissionList', ], 'Condition' => [ 'shape' => 'Condition', ], 'PermissionsWithGrantOption' => [ 'shape' => 'PermissionList', ], ], ], 'GrantPermissionsResponse' => [ 'type' => 'structure', 'members' => [], ], 'HashString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'IAMRoleArn' => [ 'type' => 'string', 'pattern' => 'arn:aws:iam::[0-9]*:role/.*', ], 'IAMSAMLProviderArn' => [ 'type' => 'string', 'pattern' => 'arn:aws:iam::[0-9]*:saml-provider/.*', ], 'Identifier' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'IdentityCenterInstanceArn' => [ 'type' => 'string', ], 'IdentityString' => [ 'type' => 'string', ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServiceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InvalidInputException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'KeyString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'LFTag' => [ 'type' => 'structure', 'required' => [ 'TagKey', 'TagValues', ], 'members' => [ 'TagKey' => [ 'shape' => 'LFTagKey', ], 'TagValues' => [ 'shape' => 'TagValueList', ], ], ], 'LFTagError' => [ 'type' => 'structure', 'members' => [ 'LFTag' => [ 'shape' => 'LFTagPair', ], 'Error' => [ 'shape' => 'ErrorDetail', ], ], ], 'LFTagErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'LFTagError', ], ], 'LFTagExpression' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Expression' => [ 'shape' => 'Expression', ], ], ], 'LFTagExpressionResource' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Name' => [ 'shape' => 'NameString', ], ], ], 'LFTagExpressionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LFTagExpression', ], ], 'LFTagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:\\/=+\\-@%]*)$', ], 'LFTagKeyResource' => [ 'type' => 'structure', 'required' => [ 'TagKey', 'TagValues', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'TagKey' => [ 'shape' => 'NameString', ], 'TagValues' => [ 'shape' => 'TagValueList', ], ], ], 'LFTagPair' => [ 'type' => 'structure', 'required' => [ 'TagKey', 'TagValues', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'TagKey' => [ 'shape' => 'LFTagKey', ], 'TagValues' => [ 'shape' => 'TagValueList', ], ], ], 'LFTagPolicyResource' => [ 'type' => 'structure', 'required' => [ 'ResourceType', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'Expression' => [ 'shape' => 'Expression', ], 'ExpressionName' => [ 'shape' => 'NameString', ], ], ], 'LFTagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:\\*\\/=+\\-@%]*)$', ], 'LFTagsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LFTagPair', ], 'max' => 50, 'min' => 1, ], 'LakeFormationOptInsInfo' => [ 'type' => 'structure', 'members' => [ 'Resource' => [ 'shape' => 'Resource', ], 'Principal' => [ 'shape' => 'DataLakePrincipal', ], 'Condition' => [ 'shape' => 'Condition', ], 'LastModified' => [ 'shape' => 'LastModifiedTimestamp', ], 'LastUpdatedBy' => [ 'shape' => 'NameString', ], ], ], 'LakeFormationOptInsInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LakeFormationOptInsInfo', ], ], 'LastModifiedTimestamp' => [ 'type' => 'timestamp', ], 'ListDataCellsFilterRequest' => [ 'type' => 'structure', 'members' => [ 'Table' => [ 'shape' => 'TableResource', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'ListDataCellsFilterResponse' => [ 'type' => 'structure', 'members' => [ 'DataCellsFilters' => [ 'shape' => 'DataCellsFilterList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListLFTagExpressionsRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListLFTagExpressionsResponse' => [ 'type' => 'structure', 'members' => [ 'LFTagExpressions' => [ 'shape' => 'LFTagExpressionsList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListLFTagsRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'ResourceShareType' => [ 'shape' => 'ResourceShareType', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListLFTagsResponse' => [ 'type' => 'structure', 'members' => [ 'LFTags' => [ 'shape' => 'LFTagsList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListLakeFormationOptInsRequest' => [ 'type' => 'structure', 'members' => [ 'Principal' => [ 'shape' => 'DataLakePrincipal', ], 'Resource' => [ 'shape' => 'Resource', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListLakeFormationOptInsResponse' => [ 'type' => 'structure', 'members' => [ 'LakeFormationOptInsInfoList' => [ 'shape' => 'LakeFormationOptInsInfoList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListPermissionsRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Principal' => [ 'shape' => 'DataLakePrincipal', ], 'ResourceType' => [ 'shape' => 'DataLakeResourceType', ], 'Resource' => [ 'shape' => 'Resource', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'IncludeRelated' => [ 'shape' => 'TrueFalseString', ], ], ], 'ListPermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'PrincipalResourcePermissions' => [ 'shape' => 'PrincipalResourcePermissionsList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListResourcesRequest' => [ 'type' => 'structure', 'members' => [ 'FilterConditionList' => [ 'shape' => 'FilterConditionList', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListResourcesResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceInfoList' => [ 'shape' => 'ResourceInfoList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListTableStorageOptimizersRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'StorageOptimizerType' => [ 'shape' => 'OptimizerType', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListTableStorageOptimizersResponse' => [ 'type' => 'structure', 'members' => [ 'StorageOptimizerList' => [ 'shape' => 'StorageOptimizerList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListTransactionsRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'StatusFilter' => [ 'shape' => 'TransactionStatusFilter', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'TokenString', ], ], ], 'ListTransactionsResponse' => [ 'type' => 'structure', 'members' => [ 'Transactions' => [ 'shape' => 'TransactionDescriptionList', ], 'NextToken' => [ 'shape' => 'TokenString', ], ], ], 'MessageString' => [ 'type' => 'string', ], 'NameString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'NullableBoolean' => [ 'type' => 'boolean', 'box' => true, ], 'NullableString' => [ 'type' => 'string', 'box' => true, ], 'NumberOfBytes' => [ 'type' => 'long', ], 'NumberOfItems' => [ 'type' => 'long', ], 'NumberOfMilliseconds' => [ 'type' => 'long', ], 'ObjectSize' => [ 'type' => 'long', ], 'OperationTimeoutException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'OptimizerType' => [ 'type' => 'string', 'enum' => [ 'COMPACTION', 'GARBAGE_COLLECTION', 'ALL', ], ], 'PageSize' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'ParametersMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'KeyString', ], 'value' => [ 'shape' => 'ParametersMapValue', ], ], 'ParametersMapValue' => [ 'type' => 'string', 'max' => 512000, ], 'PartitionObjects' => [ 'type' => 'structure', 'members' => [ 'PartitionValues' => [ 'shape' => 'PartitionValuesList', ], 'Objects' => [ 'shape' => 'TableObjectList', ], ], ], 'PartitionValueList' => [ 'type' => 'structure', 'required' => [ 'Values', ], 'members' => [ 'Values' => [ 'shape' => 'ValueStringList', ], ], ], 'PartitionValueString' => [ 'type' => 'string', 'max' => 1024, ], 'PartitionValuesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartitionValueString', ], 'max' => 100, 'min' => 1, ], 'PartitionedTableObjectsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartitionObjects', ], ], 'PathString' => [ 'type' => 'string', ], 'PathStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PathString', ], ], 'Permission' => [ 'type' => 'string', 'enum' => [ 'ALL', 'SELECT', 'ALTER', 'DROP', 'DELETE', 'INSERT', 'DESCRIBE', 'CREATE_DATABASE', 'CREATE_TABLE', 'DATA_LOCATION_ACCESS', 'CREATE_LF_TAG', 'ASSOCIATE', 'GRANT_WITH_LF_TAG_EXPRESSION', 'CREATE_LF_TAG_EXPRESSION', 'CREATE_CATALOG', 'SUPER_USER', ], ], 'PermissionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Permission', ], ], 'PermissionType' => [ 'type' => 'string', 'enum' => [ 'COLUMN_PERMISSION', 'CELL_FILTER_PERMISSION', 'NESTED_PERMISSION', 'NESTED_CELL_PERMISSION', ], ], 'PermissionTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PermissionType', ], 'max' => 255, 'min' => 1, ], 'PermissionTypeMismatchException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'PlanningStatistics' => [ 'type' => 'structure', 'members' => [ 'EstimatedDataToScanBytes' => [ 'shape' => 'NumberOfBytes', ], 'PlanningTimeMillis' => [ 'shape' => 'NumberOfMilliseconds', ], 'QueueTimeMillis' => [ 'shape' => 'NumberOfMilliseconds', ], 'WorkUnitsGeneratedCount' => [ 'shape' => 'NumberOfItems', ], ], ], 'PredicateString' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'PrincipalPermissions' => [ 'type' => 'structure', 'members' => [ 'Principal' => [ 'shape' => 'DataLakePrincipal', ], 'Permissions' => [ 'shape' => 'PermissionList', ], ], ], 'PrincipalPermissionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PrincipalPermissions', ], ], 'PrincipalResourcePermissions' => [ 'type' => 'structure', 'members' => [ 'Principal' => [ 'shape' => 'DataLakePrincipal', ], 'Resource' => [ 'shape' => 'Resource', ], 'Condition' => [ 'shape' => 'Condition', ], 'Permissions' => [ 'shape' => 'PermissionList', ], 'PermissionsWithGrantOption' => [ 'shape' => 'PermissionList', ], 'AdditionalDetails' => [ 'shape' => 'DetailsMap', ], 'LastUpdated' => [ 'shape' => 'LastModifiedTimestamp', ], 'LastUpdatedBy' => [ 'shape' => 'NameString', ], ], ], 'PrincipalResourcePermissionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PrincipalResourcePermissions', ], ], 'PutDataLakeSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'DataLakeSettings', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DataLakeSettings' => [ 'shape' => 'DataLakeSettings', ], ], ], 'PutDataLakeSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'QueryIdString' => [ 'type' => 'string', ], 'QueryParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'QueryPlanningContext' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'QueryPlanningContextDatabaseNameString', ], 'QueryAsOfTime' => [ 'shape' => 'Timestamp', ], 'QueryParameters' => [ 'shape' => 'QueryParameterMap', ], 'TransactionId' => [ 'shape' => 'TransactionIdString', ], ], ], 'QueryPlanningContextDatabaseNameString' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'QuerySessionContext' => [ 'type' => 'structure', 'members' => [ 'QueryId' => [ 'shape' => 'HashString', ], 'QueryStartTime' => [ 'shape' => 'Timestamp', ], 'ClusterId' => [ 'shape' => 'NullableString', ], 'QueryAuthorizationId' => [ 'shape' => 'HashString', ], 'AdditionalContext' => [ 'shape' => 'AdditionalContextMap', ], ], ], 'QueryStateString' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'WORKUNITS_AVAILABLE', 'ERROR', 'FINISHED', 'EXPIRED', ], ], 'RAMResourceShareArn' => [ 'type' => 'string', ], 'RegisterResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], 'UseServiceLinkedRole' => [ 'shape' => 'NullableBoolean', ], 'RoleArn' => [ 'shape' => 'IAMRoleArn', ], 'WithFederation' => [ 'shape' => 'NullableBoolean', ], 'HybridAccessEnabled' => [ 'shape' => 'NullableBoolean', ], 'WithPrivilegedAccess' => [ 'shape' => 'Boolean', ], ], ], 'RegisterResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'RemoveLFTagsFromResourceRequest' => [ 'type' => 'structure', 'required' => [ 'Resource', 'LFTags', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Resource' => [ 'shape' => 'Resource', ], 'LFTags' => [ 'shape' => 'LFTagsList', ], ], ], 'RemoveLFTagsFromResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Failures' => [ 'shape' => 'LFTagErrors', ], ], ], 'Resource' => [ 'type' => 'structure', 'members' => [ 'Catalog' => [ 'shape' => 'CatalogResource', ], 'Database' => [ 'shape' => 'DatabaseResource', ], 'Table' => [ 'shape' => 'TableResource', ], 'TableWithColumns' => [ 'shape' => 'TableWithColumnsResource', ], 'DataLocation' => [ 'shape' => 'DataLocationResource', ], 'DataCellsFilter' => [ 'shape' => 'DataCellsFilterResource', ], 'LFTag' => [ 'shape' => 'LFTagKeyResource', ], 'LFTagPolicy' => [ 'shape' => 'LFTagPolicyResource', ], 'LFTagExpression' => [ 'shape' => 'LFTagExpressionResource', ], ], ], 'ResourceArnString' => [ 'type' => 'string', ], 'ResourceInfo' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], 'RoleArn' => [ 'shape' => 'IAMRoleArn', ], 'LastModified' => [ 'shape' => 'LastModifiedTimestamp', ], 'WithFederation' => [ 'shape' => 'NullableBoolean', ], 'HybridAccessEnabled' => [ 'shape' => 'NullableBoolean', ], 'WithPrivilegedAccess' => [ 'shape' => 'NullableBoolean', ], ], ], 'ResourceInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceInfo', ], ], 'ResourceNotReadyException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ResourceNumberLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ResourceShareList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RAMResourceShareArn', ], ], 'ResourceShareType' => [ 'type' => 'string', 'enum' => [ 'FOREIGN', 'ALL', ], ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'DATABASE', 'TABLE', ], ], 'Result' => [ 'type' => 'string', ], 'ResultStream' => [ 'type' => 'blob', 'streaming' => true, ], 'RevokePermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'Principal', 'Resource', 'Permissions', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Principal' => [ 'shape' => 'DataLakePrincipal', ], 'Resource' => [ 'shape' => 'Resource', ], 'Permissions' => [ 'shape' => 'PermissionList', ], 'Condition' => [ 'shape' => 'Condition', ], 'PermissionsWithGrantOption' => [ 'shape' => 'PermissionList', ], ], ], 'RevokePermissionsResponse' => [ 'type' => 'structure', 'members' => [], ], 'RowFilter' => [ 'type' => 'structure', 'members' => [ 'FilterExpression' => [ 'shape' => 'PredicateString', ], 'AllRowsWildcard' => [ 'shape' => 'AllRowsWildcard', ], ], ], 'SAMLAssertionString' => [ 'type' => 'string', 'max' => 100000, 'min' => 4, ], 'ScopeTarget' => [ 'type' => 'string', ], 'ScopeTargets' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScopeTarget', ], ], 'SearchDatabasesByLFTagsRequest' => [ 'type' => 'structure', 'required' => [ 'Expression', ], 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'SearchPageSize', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Expression' => [ 'shape' => 'Expression', ], ], ], 'SearchDatabasesByLFTagsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'DatabaseList' => [ 'shape' => 'DatabaseLFTagsList', ], ], ], 'SearchPageSize' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'SearchTablesByLFTagsRequest' => [ 'type' => 'structure', 'required' => [ 'Expression', ], 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'SearchPageSize', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Expression' => [ 'shape' => 'Expression', ], ], ], 'SearchTablesByLFTagsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'TableList' => [ 'shape' => 'TableLFTagsList', ], ], ], 'SecretAccessKeyString' => [ 'type' => 'string', ], 'SessionTokenString' => [ 'type' => 'string', ], 'StartQueryPlanningRequest' => [ 'type' => 'structure', 'required' => [ 'QueryPlanningContext', 'QueryString', ], 'members' => [ 'QueryPlanningContext' => [ 'shape' => 'QueryPlanningContext', ], 'QueryString' => [ 'shape' => 'SyntheticStartQueryPlanningRequestQueryString', ], ], ], 'StartQueryPlanningResponse' => [ 'type' => 'structure', 'required' => [ 'QueryId', ], 'members' => [ 'QueryId' => [ 'shape' => 'QueryIdString', ], ], ], 'StartTransactionRequest' => [ 'type' => 'structure', 'members' => [ 'TransactionType' => [ 'shape' => 'TransactionType', ], ], ], 'StartTransactionResponse' => [ 'type' => 'structure', 'members' => [ 'TransactionId' => [ 'shape' => 'TransactionIdString', ], ], ], 'StatisticsNotReadyYetException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'error' => [ 'httpStatusCode' => 420, 'senderFault' => true, ], 'exception' => true, ], 'StorageOptimizer' => [ 'type' => 'structure', 'members' => [ 'StorageOptimizerType' => [ 'shape' => 'OptimizerType', ], 'Config' => [ 'shape' => 'StorageOptimizerConfig', ], 'ErrorMessage' => [ 'shape' => 'MessageString', ], 'Warnings' => [ 'shape' => 'MessageString', ], 'LastRunDetails' => [ 'shape' => 'MessageString', ], ], ], 'StorageOptimizerConfig' => [ 'type' => 'map', 'key' => [ 'shape' => 'StorageOptimizerConfigKey', ], 'value' => [ 'shape' => 'StorageOptimizerConfigValue', ], ], 'StorageOptimizerConfigKey' => [ 'type' => 'string', ], 'StorageOptimizerConfigMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'OptimizerType', ], 'value' => [ 'shape' => 'StorageOptimizerConfig', ], ], 'StorageOptimizerConfigValue' => [ 'type' => 'string', ], 'StorageOptimizerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorageOptimizer', ], ], 'String' => [ 'type' => 'string', ], 'StringValue' => [ 'type' => 'string', ], 'StringValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringValue', ], ], 'SyntheticGetWorkUnitResultsRequestWorkUnitTokenString' => [ 'type' => 'string', 'min' => 1, 'sensitive' => true, ], 'SyntheticStartQueryPlanningRequestQueryString' => [ 'type' => 'string', 'min' => 1, 'sensitive' => true, ], 'TableLFTagsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaggedTable', ], ], 'TableObject' => [ 'type' => 'structure', 'members' => [ 'Uri' => [ 'shape' => 'URI', ], 'ETag' => [ 'shape' => 'ETagString', ], 'Size' => [ 'shape' => 'ObjectSize', ], ], ], 'TableObjectList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableObject', ], ], 'TableResource' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'Name' => [ 'shape' => 'NameString', ], 'TableWildcard' => [ 'shape' => 'TableWildcard', ], ], ], 'TableWildcard' => [ 'type' => 'structure', 'members' => [], ], 'TableWithColumnsResource' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'Name', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'Name' => [ 'shape' => 'NameString', ], 'ColumnNames' => [ 'shape' => 'ColumnNames', ], 'ColumnWildcard' => [ 'shape' => 'ColumnWildcard', ], ], ], 'TagValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LFTagValue', ], 'max' => 50, 'min' => 1, ], 'TaggedDatabase' => [ 'type' => 'structure', 'members' => [ 'Database' => [ 'shape' => 'DatabaseResource', ], 'LFTags' => [ 'shape' => 'LFTagsList', ], ], ], 'TaggedTable' => [ 'type' => 'structure', 'members' => [ 'Table' => [ 'shape' => 'TableResource', ], 'LFTagOnDatabase' => [ 'shape' => 'LFTagsList', ], 'LFTagsOnTable' => [ 'shape' => 'LFTagsList', ], 'LFTagsOnColumns' => [ 'shape' => 'ColumnLFTagsList', ], ], ], 'ThrottledException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'Token' => [ 'type' => 'string', ], 'TokenString' => [ 'type' => 'string', 'max' => 4096, ], 'TransactionCanceledException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TransactionCommitInProgressException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TransactionCommittedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TransactionDescription' => [ 'type' => 'structure', 'members' => [ 'TransactionId' => [ 'shape' => 'TransactionIdString', ], 'TransactionStatus' => [ 'shape' => 'TransactionStatus', ], 'TransactionStartTime' => [ 'shape' => 'Timestamp', ], 'TransactionEndTime' => [ 'shape' => 'Timestamp', ], ], ], 'TransactionDescriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TransactionDescription', ], ], 'TransactionIdString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\p{L}\\p{N}\\p{P}]*', ], 'TransactionStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'COMMITTED', 'ABORTED', 'COMMIT_IN_PROGRESS', ], ], 'TransactionStatusFilter' => [ 'type' => 'string', 'enum' => [ 'ALL', 'COMPLETED', 'ACTIVE', 'COMMITTED', 'ABORTED', ], ], 'TransactionType' => [ 'type' => 'string', 'enum' => [ 'READ_AND_WRITE', 'READ_ONLY', ], ], 'TrueFalseString' => [ 'type' => 'string', 'max' => 5, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'TrustedResourceOwners' => [ 'type' => 'list', 'member' => [ 'shape' => 'CatalogIdString', ], ], 'URI' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'UpdateDataCellsFilterRequest' => [ 'type' => 'structure', 'required' => [ 'TableData', ], 'members' => [ 'TableData' => [ 'shape' => 'DataCellsFilter', ], ], ], 'UpdateDataCellsFilterResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateLFTagExpressionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Expression', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Expression' => [ 'shape' => 'Expression', ], ], ], 'UpdateLFTagExpressionResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateLFTagRequest' => [ 'type' => 'structure', 'required' => [ 'TagKey', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'TagKey' => [ 'shape' => 'LFTagKey', ], 'TagValuesToDelete' => [ 'shape' => 'TagValueList', ], 'TagValuesToAdd' => [ 'shape' => 'TagValueList', ], ], ], 'UpdateLFTagResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateLakeFormationIdentityCenterConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'ShareRecipients' => [ 'shape' => 'DataLakePrincipalList', ], 'ApplicationStatus' => [ 'shape' => 'ApplicationStatus', ], 'ExternalFiltering' => [ 'shape' => 'ExternalFilteringConfiguration', ], ], ], 'UpdateLakeFormationIdentityCenterConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateResourceRequest' => [ 'type' => 'structure', 'required' => [ 'RoleArn', 'ResourceArn', ], 'members' => [ 'RoleArn' => [ 'shape' => 'IAMRoleArn', ], 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], 'WithFederation' => [ 'shape' => 'NullableBoolean', ], 'HybridAccessEnabled' => [ 'shape' => 'NullableBoolean', ], ], ], 'UpdateResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateTableObjectsRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'WriteOperations', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'TransactionId' => [ 'shape' => 'TransactionIdString', ], 'WriteOperations' => [ 'shape' => 'WriteOperationList', ], ], ], 'UpdateTableObjectsResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateTableStorageOptimizerRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'StorageOptimizerConfig', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'StorageOptimizerConfig' => [ 'shape' => 'StorageOptimizerConfigMap', ], ], ], 'UpdateTableStorageOptimizerResponse' => [ 'type' => 'structure', 'members' => [ 'Result' => [ 'shape' => 'Result', ], ], ], 'ValueString' => [ 'type' => 'string', ], 'ValueStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValueString', ], 'min' => 1, ], 'VersionString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'VirtualObject' => [ 'type' => 'structure', 'required' => [ 'Uri', ], 'members' => [ 'Uri' => [ 'shape' => 'URI', ], 'ETag' => [ 'shape' => 'ETagString', ], ], ], 'VirtualObjectList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VirtualObject', ], 'max' => 100, 'min' => 1, ], 'WorkUnitIdLong' => [ 'type' => 'long', ], 'WorkUnitRange' => [ 'type' => 'structure', 'required' => [ 'WorkUnitIdMax', 'WorkUnitIdMin', 'WorkUnitToken', ], 'members' => [ 'WorkUnitIdMax' => [ 'shape' => 'WorkUnitIdLong', ], 'WorkUnitIdMin' => [ 'shape' => 'WorkUnitIdLong', ], 'WorkUnitToken' => [ 'shape' => 'WorkUnitTokenString', ], ], ], 'WorkUnitRangeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkUnitRange', ], ], 'WorkUnitTokenString' => [ 'type' => 'string', ], 'WorkUnitsNotReadyYetException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'error' => [ 'httpStatusCode' => 420, 'senderFault' => true, ], 'exception' => true, ], 'WriteOperation' => [ 'type' => 'structure', 'members' => [ 'AddObject' => [ 'shape' => 'AddObjectInput', ], 'DeleteObject' => [ 'shape' => 'DeleteObjectInput', ], ], ], 'WriteOperationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WriteOperation', ], 'max' => 100, 'min' => 1, ], ],];
