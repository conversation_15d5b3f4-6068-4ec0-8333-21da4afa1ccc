<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * إنشاء جدول المهام المتقدم
 *
 * هذا الـ Migration ينشئ جدول المهام مع دعم المهام الفرعية
 * والتبعيات والتتبع المتقدم للوقت والتقدم
 *
 * @package Database\Migrations
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
return new class extends Migration
{
    /**
     * تشغيل الـ Migration
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('tasks', function (Blueprint $table) {
            // المعرف الأساسي
            $table->id()->comment('معرف المهمة الفريد');

            // معلومات المهمة الأساسية
            $table->string('title', 255)->comment('عنوان المهمة');
            $table->text('description')->nullable()->comment('وصف المهمة التفصيلي');

            // تصنيف المهمة
            $table->enum('type', [
                'development',
                'design',
                'testing',
                'documentation',
                'review',
                'deployment',
                'maintenance',
                'meeting',
                'research',
                'other'
            ])->default('other')->comment('نوع المهمة');

            // حالة المهمة
            $table->enum('status', [
                'todo',
                'in_progress',
                'review',
                'completed',
                'blocked'
            ])->default('todo')->comment('حالة المهمة');

            // أولوية المهمة
            $table->enum('priority', [
                'low',
                'normal',
                'high',
                'urgent'
            ])->default('normal')->comment('أولوية المهمة');

            // العلاقات
            $table->foreignId('project_id')
                  ->constrained('projects')
                  ->onDelete('cascade')
                  ->comment('معرف المشروع');

            $table->foreignId('parent_task_id')
                  ->nullable()
                  ->constrained('tasks')
                  ->onDelete('cascade')
                  ->comment('معرف المهمة الأب للمهام الفرعية');

            $table->foreignId('assigned_to')
                  ->nullable()
                  ->constrained('users')
                  ->onDelete('set null')
                  ->comment('معرف المستخدم المكلف بالمهمة');

            // ملاحظة: سيتم إضافة العلاقة مع المعالم لاحقاً بعد إنشاء جدول المعالم

            // تتبع الوقت
            $table->decimal('estimated_hours', 8, 2)->default(0)->comment('الساعات المقدرة');
            $table->decimal('actual_hours', 8, 2)->default(0)->comment('الساعات الفعلية المستغرقة');

            // التواريخ
            $table->date('start_date')->nullable()->comment('تاريخ بداية المهمة');
            $table->date('due_date')->nullable()->comment('تاريخ الاستحقاق');
            $table->timestamp('completed_at')->nullable()->comment('تاريخ ووقت الإنجاز');

            // ترتيب المهام
            $table->integer('order_index')->default(0)->comment('ترتيب المهمة ضمن المشروع');

            // بيانات إضافية
            $table->json('metadata')->nullable()->comment('بيانات إضافية ومخصصة');

            // معلومات الإنشاء
            $table->foreignId('created_by')
                  ->constrained('users')
                  ->onDelete('cascade')
                  ->comment('معرف منشئ المهمة');

            // طوابع زمنية
            $table->timestamps();
            $table->softDeletes();

            // الفهارس المركبة لتحسين الأداء
            $table->index(['project_id', 'status'], 'idx_tasks_project_status');
            $table->index(['assigned_to', 'status'], 'idx_tasks_assignee_status');
            $table->index(['due_date', 'status'], 'idx_tasks_due_status');
            $table->index(['parent_task_id', 'order_index'], 'idx_tasks_parent_order');
            $table->index(['milestone_id', 'status'], 'idx_tasks_milestone_status');
            $table->index(['priority', 'status'], 'idx_tasks_priority_status');
            $table->index(['type', 'status'], 'idx_tasks_type_status');
            $table->index(['created_by', 'created_at'], 'idx_tasks_creator_date');

            // فهرس نصي للبحث
            $table->fullText(['title', 'description'], 'idx_tasks_search');

            // قيود إضافية
            $table->check('estimated_hours >= 0', 'chk_tasks_estimated_hours_positive');
            $table->check('actual_hours >= 0', 'chk_tasks_actual_hours_positive');
            $table->check('order_index >= 0', 'chk_tasks_order_index_positive');
        });

        // إضافة تعليق على الجدول
        DB::statement("ALTER TABLE tasks COMMENT = 'جدول المهام - يحتوي على مهام المشاريع مع دعم التسلسل الهرمي والتبعيات'");
    }

    /**
     * التراجع عن الـ Migration
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('tasks');
    }
};
