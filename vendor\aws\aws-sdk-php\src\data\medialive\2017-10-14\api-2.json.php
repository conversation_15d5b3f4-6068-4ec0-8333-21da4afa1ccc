<?php
// This file was auto-generated from sdk-root/src/data/medialive/2017-10-14/api-2.json
return [ 'metadata' => [ 'apiVersion' => '2017-10-14', 'endpointPrefix' => 'medialive', 'signingName' => 'medialive', 'serviceFullName' => 'AWS Elemental MediaLive', 'serviceId' => 'MediaLive', 'protocol' => 'rest-json', 'uid' => 'medialive-2017-10-14', 'signatureVersion' => 'v4', 'serviceAbbreviation' => 'MediaLive', 'jsonVersion' => '1.1', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AcceptInputDeviceTransfer' => [ 'name' => 'AcceptInputDeviceTransfer', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/inputDevices/{inputDeviceId}/accept', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AcceptInputDeviceTransferRequest', ], 'output' => [ 'shape' => 'AcceptInputDeviceTransferResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'BatchDelete' => [ 'name' => 'BatchDelete', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/batch/delete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchDeleteRequest', ], 'output' => [ 'shape' => 'BatchDeleteResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'BatchStart' => [ 'name' => 'BatchStart', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/batch/start', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchStartRequest', ], 'output' => [ 'shape' => 'BatchStartResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'BatchStop' => [ 'name' => 'BatchStop', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/batch/stop', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchStopRequest', ], 'output' => [ 'shape' => 'BatchStopResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'BatchUpdateSchedule' => [ 'name' => 'BatchUpdateSchedule', 'http' => [ 'method' => 'PUT', 'requestUri' => '/prod/channels/{channelId}/schedule', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchUpdateScheduleRequest', ], 'output' => [ 'shape' => 'BatchUpdateScheduleResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'CancelInputDeviceTransfer' => [ 'name' => 'CancelInputDeviceTransfer', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/inputDevices/{inputDeviceId}/cancel', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelInputDeviceTransferRequest', ], 'output' => [ 'shape' => 'CancelInputDeviceTransferResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'ClaimDevice' => [ 'name' => 'ClaimDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/claimDevice', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ClaimDeviceRequest', ], 'output' => [ 'shape' => 'ClaimDeviceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'CreateChannel' => [ 'name' => 'CreateChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/channels', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateChannelRequest', ], 'output' => [ 'shape' => 'CreateChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateInput' => [ 'name' => 'CreateInput', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/inputs', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateInputRequest', ], 'output' => [ 'shape' => 'CreateInputResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'CreateInputSecurityGroup' => [ 'name' => 'CreateInputSecurityGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/inputSecurityGroups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateInputSecurityGroupRequest', ], 'output' => [ 'shape' => 'CreateInputSecurityGroupResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'CreateMultiplex' => [ 'name' => 'CreateMultiplex', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/multiplexes', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateMultiplexRequest', ], 'output' => [ 'shape' => 'CreateMultiplexResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateMultiplexProgram' => [ 'name' => 'CreateMultiplexProgram', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/multiplexes/{multiplexId}/programs', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateMultiplexProgramRequest', ], 'output' => [ 'shape' => 'CreateMultiplexProgramResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreatePartnerInput' => [ 'name' => 'CreatePartnerInput', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/inputs/{inputId}/partners', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreatePartnerInputRequest', ], 'output' => [ 'shape' => 'CreatePartnerInputResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'CreateTags' => [ 'name' => 'CreateTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/tags/{resource-arn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'CreateTagsRequest', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'DeleteChannel' => [ 'name' => 'DeleteChannel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/prod/channels/{channelId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteChannelRequest', ], 'output' => [ 'shape' => 'DeleteChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteInput' => [ 'name' => 'DeleteInput', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/prod/inputs/{inputId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteInputRequest', ], 'output' => [ 'shape' => 'DeleteInputResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteInputSecurityGroup' => [ 'name' => 'DeleteInputSecurityGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/prod/inputSecurityGroups/{inputSecurityGroupId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteInputSecurityGroupRequest', ], 'output' => [ 'shape' => 'DeleteInputSecurityGroupResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteMultiplex' => [ 'name' => 'DeleteMultiplex', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/prod/multiplexes/{multiplexId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteMultiplexRequest', ], 'output' => [ 'shape' => 'DeleteMultiplexResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteMultiplexProgram' => [ 'name' => 'DeleteMultiplexProgram', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/prod/multiplexes/{multiplexId}/programs/{programName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteMultiplexProgramRequest', ], 'output' => [ 'shape' => 'DeleteMultiplexProgramResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteReservation' => [ 'name' => 'DeleteReservation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/prod/reservations/{reservationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteReservationRequest', ], 'output' => [ 'shape' => 'DeleteReservationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteSchedule' => [ 'name' => 'DeleteSchedule', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/prod/channels/{channelId}/schedule', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteScheduleRequest', ], 'output' => [ 'shape' => 'DeleteScheduleResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DeleteTags' => [ 'name' => 'DeleteTags', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/prod/tags/{resource-arn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteTagsRequest', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'DescribeAccountConfiguration' => [ 'name' => 'DescribeAccountConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/accountConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeAccountConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeAccountConfigurationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DescribeChannel' => [ 'name' => 'DescribeChannel', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/channels/{channelId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeChannelRequest', ], 'output' => [ 'shape' => 'DescribeChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DescribeInput' => [ 'name' => 'DescribeInput', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/inputs/{inputId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeInputRequest', ], 'output' => [ 'shape' => 'DescribeInputResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DescribeInputDevice' => [ 'name' => 'DescribeInputDevice', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/inputDevices/{inputDeviceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeInputDeviceRequest', ], 'output' => [ 'shape' => 'DescribeInputDeviceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DescribeInputDeviceThumbnail' => [ 'name' => 'DescribeInputDeviceThumbnail', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/inputDevices/{inputDeviceId}/thumbnailData', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeInputDeviceThumbnailRequest', ], 'output' => [ 'shape' => 'DescribeInputDeviceThumbnailResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DescribeInputSecurityGroup' => [ 'name' => 'DescribeInputSecurityGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/inputSecurityGroups/{inputSecurityGroupId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeInputSecurityGroupRequest', ], 'output' => [ 'shape' => 'DescribeInputSecurityGroupResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DescribeMultiplex' => [ 'name' => 'DescribeMultiplex', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/multiplexes/{multiplexId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeMultiplexRequest', ], 'output' => [ 'shape' => 'DescribeMultiplexResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DescribeMultiplexProgram' => [ 'name' => 'DescribeMultiplexProgram', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/multiplexes/{multiplexId}/programs/{programName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeMultiplexProgramRequest', ], 'output' => [ 'shape' => 'DescribeMultiplexProgramResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DescribeOffering' => [ 'name' => 'DescribeOffering', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/offerings/{offeringId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeOfferingRequest', ], 'output' => [ 'shape' => 'DescribeOfferingResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DescribeReservation' => [ 'name' => 'DescribeReservation', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/reservations/{reservationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeReservationRequest', ], 'output' => [ 'shape' => 'DescribeReservationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DescribeSchedule' => [ 'name' => 'DescribeSchedule', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/channels/{channelId}/schedule', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeScheduleRequest', ], 'output' => [ 'shape' => 'DescribeScheduleResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DescribeThumbnails' => [ 'name' => 'DescribeThumbnails', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/channels/{channelId}/thumbnails', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeThumbnailsRequest', ], 'output' => [ 'shape' => 'DescribeThumbnailsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListChannels' => [ 'name' => 'ListChannels', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/channels', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelsRequest', ], 'output' => [ 'shape' => 'ListChannelsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListInputDeviceTransfers' => [ 'name' => 'ListInputDeviceTransfers', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/inputDeviceTransfers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListInputDeviceTransfersRequest', ], 'output' => [ 'shape' => 'ListInputDeviceTransfersResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListInputDevices' => [ 'name' => 'ListInputDevices', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/inputDevices', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListInputDevicesRequest', ], 'output' => [ 'shape' => 'ListInputDevicesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListInputSecurityGroups' => [ 'name' => 'ListInputSecurityGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/inputSecurityGroups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListInputSecurityGroupsRequest', ], 'output' => [ 'shape' => 'ListInputSecurityGroupsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListInputs' => [ 'name' => 'ListInputs', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/inputs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListInputsRequest', ], 'output' => [ 'shape' => 'ListInputsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListMultiplexPrograms' => [ 'name' => 'ListMultiplexPrograms', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/multiplexes/{multiplexId}/programs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMultiplexProgramsRequest', ], 'output' => [ 'shape' => 'ListMultiplexProgramsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListMultiplexes' => [ 'name' => 'ListMultiplexes', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/multiplexes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMultiplexesRequest', ], 'output' => [ 'shape' => 'ListMultiplexesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListOfferings' => [ 'name' => 'ListOfferings', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/offerings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListOfferingsRequest', ], 'output' => [ 'shape' => 'ListOfferingsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListReservations' => [ 'name' => 'ListReservations', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/reservations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListReservationsRequest', ], 'output' => [ 'shape' => 'ListReservationsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/tags/{resource-arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'PurchaseOffering' => [ 'name' => 'PurchaseOffering', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/offerings/{offeringId}/purchase', 'responseCode' => 201, ], 'input' => [ 'shape' => 'PurchaseOfferingRequest', ], 'output' => [ 'shape' => 'PurchaseOfferingResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'RebootInputDevice' => [ 'name' => 'RebootInputDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/inputDevices/{inputDeviceId}/reboot', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RebootInputDeviceRequest', ], 'output' => [ 'shape' => 'RebootInputDeviceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'RejectInputDeviceTransfer' => [ 'name' => 'RejectInputDeviceTransfer', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/inputDevices/{inputDeviceId}/reject', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RejectInputDeviceTransferRequest', ], 'output' => [ 'shape' => 'RejectInputDeviceTransferResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'StartChannel' => [ 'name' => 'StartChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/channels/{channelId}/start', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartChannelRequest', ], 'output' => [ 'shape' => 'StartChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'StartInputDevice' => [ 'name' => 'StartInputDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/inputDevices/{inputDeviceId}/start', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartInputDeviceRequest', ], 'output' => [ 'shape' => 'StartInputDeviceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'StartInputDeviceMaintenanceWindow' => [ 'name' => 'StartInputDeviceMaintenanceWindow', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/inputDevices/{inputDeviceId}/startInputDeviceMaintenanceWindow', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartInputDeviceMaintenanceWindowRequest', ], 'output' => [ 'shape' => 'StartInputDeviceMaintenanceWindowResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'StartMultiplex' => [ 'name' => 'StartMultiplex', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/multiplexes/{multiplexId}/start', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StartMultiplexRequest', ], 'output' => [ 'shape' => 'StartMultiplexResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'StopChannel' => [ 'name' => 'StopChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/channels/{channelId}/stop', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopChannelRequest', ], 'output' => [ 'shape' => 'StopChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'StopInputDevice' => [ 'name' => 'StopInputDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/inputDevices/{inputDeviceId}/stop', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopInputDeviceRequest', ], 'output' => [ 'shape' => 'StopInputDeviceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'StopMultiplex' => [ 'name' => 'StopMultiplex', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/multiplexes/{multiplexId}/stop', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StopMultiplexRequest', ], 'output' => [ 'shape' => 'StopMultiplexResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'TransferInputDevice' => [ 'name' => 'TransferInputDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/inputDevices/{inputDeviceId}/transfer', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TransferInputDeviceRequest', ], 'output' => [ 'shape' => 'TransferInputDeviceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateAccountConfiguration' => [ 'name' => 'UpdateAccountConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/prod/accountConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAccountConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateAccountConfigurationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateChannel' => [ 'name' => 'UpdateChannel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/prod/channels/{channelId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateChannelRequest', ], 'output' => [ 'shape' => 'UpdateChannelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateChannelClass' => [ 'name' => 'UpdateChannelClass', 'http' => [ 'method' => 'PUT', 'requestUri' => '/prod/channels/{channelId}/channelClass', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateChannelClassRequest', ], 'output' => [ 'shape' => 'UpdateChannelClassResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateInput' => [ 'name' => 'UpdateInput', 'http' => [ 'method' => 'PUT', 'requestUri' => '/prod/inputs/{inputId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateInputRequest', ], 'output' => [ 'shape' => 'UpdateInputResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateInputDevice' => [ 'name' => 'UpdateInputDevice', 'http' => [ 'method' => 'PUT', 'requestUri' => '/prod/inputDevices/{inputDeviceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateInputDeviceRequest', ], 'output' => [ 'shape' => 'UpdateInputDeviceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateInputSecurityGroup' => [ 'name' => 'UpdateInputSecurityGroup', 'http' => [ 'method' => 'PUT', 'requestUri' => '/prod/inputSecurityGroups/{inputSecurityGroupId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateInputSecurityGroupRequest', ], 'output' => [ 'shape' => 'UpdateInputSecurityGroupResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateMultiplex' => [ 'name' => 'UpdateMultiplex', 'http' => [ 'method' => 'PUT', 'requestUri' => '/prod/multiplexes/{multiplexId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMultiplexRequest', ], 'output' => [ 'shape' => 'UpdateMultiplexResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateMultiplexProgram' => [ 'name' => 'UpdateMultiplexProgram', 'http' => [ 'method' => 'PUT', 'requestUri' => '/prod/multiplexes/{multiplexId}/programs/{programName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMultiplexProgramRequest', ], 'output' => [ 'shape' => 'UpdateMultiplexProgramResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateReservation' => [ 'name' => 'UpdateReservation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/prod/reservations/{reservationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateReservationRequest', ], 'output' => [ 'shape' => 'UpdateReservationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'RestartChannelPipelines' => [ 'name' => 'RestartChannelPipelines', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/channels/{channelId}/restartChannelPipelines', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RestartChannelPipelinesRequest', ], 'output' => [ 'shape' => 'RestartChannelPipelinesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateCloudWatchAlarmTemplate' => [ 'name' => 'CreateCloudWatchAlarmTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/cloudwatch-alarm-templates', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateCloudWatchAlarmTemplateRequest', ], 'output' => [ 'shape' => 'CreateCloudWatchAlarmTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateCloudWatchAlarmTemplateGroup' => [ 'name' => 'CreateCloudWatchAlarmTemplateGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/cloudwatch-alarm-template-groups', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateCloudWatchAlarmTemplateGroupRequest', ], 'output' => [ 'shape' => 'CreateCloudWatchAlarmTemplateGroupResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateEventBridgeRuleTemplate' => [ 'name' => 'CreateEventBridgeRuleTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/eventbridge-rule-templates', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateEventBridgeRuleTemplateRequest', ], 'output' => [ 'shape' => 'CreateEventBridgeRuleTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateEventBridgeRuleTemplateGroup' => [ 'name' => 'CreateEventBridgeRuleTemplateGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/eventbridge-rule-template-groups', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateEventBridgeRuleTemplateGroupRequest', ], 'output' => [ 'shape' => 'CreateEventBridgeRuleTemplateGroupResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateSignalMap' => [ 'name' => 'CreateSignalMap', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/signal-maps', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateSignalMapRequest', ], 'output' => [ 'shape' => 'CreateSignalMapResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteCloudWatchAlarmTemplate' => [ 'name' => 'DeleteCloudWatchAlarmTemplate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/prod/cloudwatch-alarm-templates/{identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteCloudWatchAlarmTemplateRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteCloudWatchAlarmTemplateGroup' => [ 'name' => 'DeleteCloudWatchAlarmTemplateGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/prod/cloudwatch-alarm-template-groups/{identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteCloudWatchAlarmTemplateGroupRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteEventBridgeRuleTemplate' => [ 'name' => 'DeleteEventBridgeRuleTemplate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/prod/eventbridge-rule-templates/{identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteEventBridgeRuleTemplateRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteEventBridgeRuleTemplateGroup' => [ 'name' => 'DeleteEventBridgeRuleTemplateGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/prod/eventbridge-rule-template-groups/{identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteEventBridgeRuleTemplateGroupRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteSignalMap' => [ 'name' => 'DeleteSignalMap', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/prod/signal-maps/{identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSignalMapRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetCloudWatchAlarmTemplate' => [ 'name' => 'GetCloudWatchAlarmTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/cloudwatch-alarm-templates/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCloudWatchAlarmTemplateRequest', ], 'output' => [ 'shape' => 'GetCloudWatchAlarmTemplateResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'GetCloudWatchAlarmTemplateGroup' => [ 'name' => 'GetCloudWatchAlarmTemplateGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/cloudwatch-alarm-template-groups/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCloudWatchAlarmTemplateGroupRequest', ], 'output' => [ 'shape' => 'GetCloudWatchAlarmTemplateGroupResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'GetEventBridgeRuleTemplate' => [ 'name' => 'GetEventBridgeRuleTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/eventbridge-rule-templates/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEventBridgeRuleTemplateRequest', ], 'output' => [ 'shape' => 'GetEventBridgeRuleTemplateResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'GetEventBridgeRuleTemplateGroup' => [ 'name' => 'GetEventBridgeRuleTemplateGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/eventbridge-rule-template-groups/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEventBridgeRuleTemplateGroupRequest', ], 'output' => [ 'shape' => 'GetEventBridgeRuleTemplateGroupResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'GetSignalMap' => [ 'name' => 'GetSignalMap', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/signal-maps/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSignalMapRequest', ], 'output' => [ 'shape' => 'GetSignalMapResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'ListCloudWatchAlarmTemplateGroups' => [ 'name' => 'ListCloudWatchAlarmTemplateGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/cloudwatch-alarm-template-groups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCloudWatchAlarmTemplateGroupsRequest', ], 'output' => [ 'shape' => 'ListCloudWatchAlarmTemplateGroupsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'ListCloudWatchAlarmTemplates' => [ 'name' => 'ListCloudWatchAlarmTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/cloudwatch-alarm-templates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCloudWatchAlarmTemplatesRequest', ], 'output' => [ 'shape' => 'ListCloudWatchAlarmTemplatesResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'ListEventBridgeRuleTemplateGroups' => [ 'name' => 'ListEventBridgeRuleTemplateGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/eventbridge-rule-template-groups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEventBridgeRuleTemplateGroupsRequest', ], 'output' => [ 'shape' => 'ListEventBridgeRuleTemplateGroupsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'ListEventBridgeRuleTemplates' => [ 'name' => 'ListEventBridgeRuleTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/eventbridge-rule-templates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEventBridgeRuleTemplatesRequest', ], 'output' => [ 'shape' => 'ListEventBridgeRuleTemplatesResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'ListSignalMaps' => [ 'name' => 'ListSignalMaps', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/signal-maps', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSignalMapsRequest', ], 'output' => [ 'shape' => 'ListSignalMapsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], ], ], 'StartDeleteMonitorDeployment' => [ 'name' => 'StartDeleteMonitorDeployment', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/prod/signal-maps/{identifier}/monitor-deployment', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StartDeleteMonitorDeploymentRequest', ], 'output' => [ 'shape' => 'StartDeleteMonitorDeploymentResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'StartMonitorDeployment' => [ 'name' => 'StartMonitorDeployment', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/signal-maps/{identifier}/monitor-deployment', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StartMonitorDeploymentRequest', ], 'output' => [ 'shape' => 'StartMonitorDeploymentResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'StartUpdateSignalMap' => [ 'name' => 'StartUpdateSignalMap', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/prod/signal-maps/{identifier}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StartUpdateSignalMapRequest', ], 'output' => [ 'shape' => 'StartUpdateSignalMapResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateCloudWatchAlarmTemplate' => [ 'name' => 'UpdateCloudWatchAlarmTemplate', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/prod/cloudwatch-alarm-templates/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateCloudWatchAlarmTemplateRequest', ], 'output' => [ 'shape' => 'UpdateCloudWatchAlarmTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateCloudWatchAlarmTemplateGroup' => [ 'name' => 'UpdateCloudWatchAlarmTemplateGroup', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/prod/cloudwatch-alarm-template-groups/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateCloudWatchAlarmTemplateGroupRequest', ], 'output' => [ 'shape' => 'UpdateCloudWatchAlarmTemplateGroupResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateEventBridgeRuleTemplate' => [ 'name' => 'UpdateEventBridgeRuleTemplate', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/prod/eventbridge-rule-templates/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateEventBridgeRuleTemplateRequest', ], 'output' => [ 'shape' => 'UpdateEventBridgeRuleTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateEventBridgeRuleTemplateGroup' => [ 'name' => 'UpdateEventBridgeRuleTemplateGroup', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/prod/eventbridge-rule-template-groups/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateEventBridgeRuleTemplateGroupRequest', ], 'output' => [ 'shape' => 'UpdateEventBridgeRuleTemplateGroupResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateChannelPlacementGroup' => [ 'name' => 'CreateChannelPlacementGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/clusters/{clusterId}/channelplacementgroups', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateChannelPlacementGroupRequest', ], 'output' => [ 'shape' => 'CreateChannelPlacementGroupResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'CreateCluster' => [ 'name' => 'CreateCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/clusters', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateClusterRequest', ], 'output' => [ 'shape' => 'CreateClusterResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateNetwork' => [ 'name' => 'CreateNetwork', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/networks', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateNetworkRequest', ], 'output' => [ 'shape' => 'CreateNetworkResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateNode' => [ 'name' => 'CreateNode', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/clusters/{clusterId}/nodes', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateNodeRequest', ], 'output' => [ 'shape' => 'CreateNodeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'CreateNodeRegistrationScript' => [ 'name' => 'CreateNodeRegistrationScript', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/clusters/{clusterId}/nodeRegistrationScript', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateNodeRegistrationScriptRequest', ], 'output' => [ 'shape' => 'CreateNodeRegistrationScriptResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteChannelPlacementGroup' => [ 'name' => 'DeleteChannelPlacementGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/prod/clusters/{clusterId}/channelplacementgroups/{channelPlacementGroupId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteChannelPlacementGroupRequest', ], 'output' => [ 'shape' => 'DeleteChannelPlacementGroupResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteCluster' => [ 'name' => 'DeleteCluster', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/prod/clusters/{clusterId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteClusterRequest', ], 'output' => [ 'shape' => 'DeleteClusterResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteNetwork' => [ 'name' => 'DeleteNetwork', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/prod/networks/{networkId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteNetworkRequest', ], 'output' => [ 'shape' => 'DeleteNetworkResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteNode' => [ 'name' => 'DeleteNode', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/prod/clusters/{clusterId}/nodes/{nodeId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteNodeRequest', ], 'output' => [ 'shape' => 'DeleteNodeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'DescribeChannelPlacementGroup' => [ 'name' => 'DescribeChannelPlacementGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/clusters/{clusterId}/channelplacementgroups/{channelPlacementGroupId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeChannelPlacementGroupRequest', ], 'output' => [ 'shape' => 'DescribeChannelPlacementGroupResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DescribeCluster' => [ 'name' => 'DescribeCluster', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/clusters/{clusterId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeClusterRequest', ], 'output' => [ 'shape' => 'DescribeClusterResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DescribeNetwork' => [ 'name' => 'DescribeNetwork', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/networks/{networkId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeNetworkRequest', ], 'output' => [ 'shape' => 'DescribeNetworkResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DescribeNode' => [ 'name' => 'DescribeNode', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/clusters/{clusterId}/nodes/{nodeId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeNodeRequest', ], 'output' => [ 'shape' => 'DescribeNodeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListChannelPlacementGroups' => [ 'name' => 'ListChannelPlacementGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/clusters/{clusterId}/channelplacementgroups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListChannelPlacementGroupsRequest', ], 'output' => [ 'shape' => 'ListChannelPlacementGroupsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListClusters' => [ 'name' => 'ListClusters', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/clusters', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListClustersRequest', ], 'output' => [ 'shape' => 'ListClustersResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListNetworks' => [ 'name' => 'ListNetworks', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/networks', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListNetworksRequest', ], 'output' => [ 'shape' => 'ListNetworksResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListNodes' => [ 'name' => 'ListNodes', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/clusters/{clusterId}/nodes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListNodesRequest', ], 'output' => [ 'shape' => 'ListNodesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateChannelPlacementGroup' => [ 'name' => 'UpdateChannelPlacementGroup', 'http' => [ 'method' => 'PUT', 'requestUri' => '/prod/clusters/{clusterId}/channelplacementgroups/{channelPlacementGroupId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateChannelPlacementGroupRequest', ], 'output' => [ 'shape' => 'UpdateChannelPlacementGroupResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateCluster' => [ 'name' => 'UpdateCluster', 'http' => [ 'method' => 'PUT', 'requestUri' => '/prod/clusters/{clusterId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateClusterRequest', ], 'output' => [ 'shape' => 'UpdateClusterResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateNetwork' => [ 'name' => 'UpdateNetwork', 'http' => [ 'method' => 'PUT', 'requestUri' => '/prod/networks/{networkId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateNetworkRequest', ], 'output' => [ 'shape' => 'UpdateNetworkResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateNode' => [ 'name' => 'UpdateNode', 'http' => [ 'method' => 'PUT', 'requestUri' => '/prod/clusters/{clusterId}/nodes/{nodeId}', 'responseCode' => 201, ], 'input' => [ 'shape' => 'UpdateNodeRequest', ], 'output' => [ 'shape' => 'UpdateNodeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateNodeState' => [ 'name' => 'UpdateNodeState', 'http' => [ 'method' => 'PUT', 'requestUri' => '/prod/clusters/{clusterId}/nodes/{nodeId}/state', 'responseCode' => 201, ], 'input' => [ 'shape' => 'UpdateNodeStateRequest', ], 'output' => [ 'shape' => 'UpdateNodeStateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListVersions' => [ 'name' => 'ListVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListVersionsRequest', ], 'output' => [ 'shape' => 'ListVersionsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateSdiSource' => [ 'name' => 'CreateSdiSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/prod/sdiSources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSdiSourceRequest', ], 'output' => [ 'shape' => 'CreateSdiSourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteSdiSource' => [ 'name' => 'DeleteSdiSource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/prod/sdiSources/{sdiSourceId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteSdiSourceRequest', ], 'output' => [ 'shape' => 'DeleteSdiSourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'DescribeSdiSource' => [ 'name' => 'DescribeSdiSource', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/sdiSources/{sdiSourceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeSdiSourceRequest', ], 'output' => [ 'shape' => 'DescribeSdiSourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListSdiSources' => [ 'name' => 'ListSdiSources', 'http' => [ 'method' => 'GET', 'requestUri' => '/prod/sdiSources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSdiSourcesRequest', ], 'output' => [ 'shape' => 'ListSdiSourcesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'UpdateSdiSource' => [ 'name' => 'UpdateSdiSource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/prod/sdiSources/{sdiSourceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSdiSourceRequest', ], 'output' => [ 'shape' => 'UpdateSdiSourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], ], 'shapes' => [ 'AacCodingMode' => [ 'type' => 'string', 'enum' => [ 'AD_RECEIVER_MIX', 'CODING_MODE_1_0', 'CODING_MODE_1_1', 'CODING_MODE_2_0', 'CODING_MODE_5_1', ], ], 'AacInputType' => [ 'type' => 'string', 'enum' => [ 'BROADCASTER_MIXED_AD', 'NORMAL', ], ], 'AacProfile' => [ 'type' => 'string', 'enum' => [ 'HEV1', 'HEV2', 'LC', ], ], 'AacRateControlMode' => [ 'type' => 'string', 'enum' => [ 'CBR', 'VBR', ], ], 'AacRawFormat' => [ 'type' => 'string', 'enum' => [ 'LATM_LOAS', 'NONE', ], ], 'AacSettings' => [ 'type' => 'structure', 'members' => [ 'Bitrate' => [ 'shape' => '__double', 'locationName' => 'bitrate', ], 'CodingMode' => [ 'shape' => 'AacCodingMode', 'locationName' => 'codingMode', ], 'InputType' => [ 'shape' => 'AacInputType', 'locationName' => 'inputType', ], 'Profile' => [ 'shape' => 'AacProfile', 'locationName' => 'profile', ], 'RateControlMode' => [ 'shape' => 'AacRateControlMode', 'locationName' => 'rateControlMode', ], 'RawFormat' => [ 'shape' => 'AacRawFormat', 'locationName' => 'rawFormat', ], 'SampleRate' => [ 'shape' => '__double', 'locationName' => 'sampleRate', ], 'Spec' => [ 'shape' => 'AacSpec', 'locationName' => 'spec', ], 'VbrQuality' => [ 'shape' => 'AacVbrQuality', 'locationName' => 'vbrQuality', ], ], ], 'AacSpec' => [ 'type' => 'string', 'enum' => [ 'MPEG2', 'MPEG4', ], ], 'AacVbrQuality' => [ 'type' => 'string', 'enum' => [ 'HIGH', 'LOW', 'MEDIUM_HIGH', 'MEDIUM_LOW', ], ], 'Ac3AttenuationControl' => [ 'type' => 'string', 'enum' => [ 'ATTENUATE_3_DB', 'NONE', ], ], 'Ac3BitstreamMode' => [ 'type' => 'string', 'enum' => [ 'COMMENTARY', 'COMPLETE_MAIN', 'DIALOGUE', 'EMERGENCY', 'HEARING_IMPAIRED', 'MUSIC_AND_EFFECTS', 'VISUALLY_IMPAIRED', 'VOICE_OVER', ], ], 'Ac3CodingMode' => [ 'type' => 'string', 'enum' => [ 'CODING_MODE_1_0', 'CODING_MODE_1_1', 'CODING_MODE_2_0', 'CODING_MODE_3_2_LFE', ], ], 'Ac3DrcProfile' => [ 'type' => 'string', 'enum' => [ 'FILM_STANDARD', 'NONE', ], ], 'Ac3LfeFilter' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'Ac3MetadataControl' => [ 'type' => 'string', 'enum' => [ 'FOLLOW_INPUT', 'USE_CONFIGURED', ], ], 'Ac3Settings' => [ 'type' => 'structure', 'members' => [ 'Bitrate' => [ 'shape' => '__double', 'locationName' => 'bitrate', ], 'BitstreamMode' => [ 'shape' => 'Ac3BitstreamMode', 'locationName' => 'bitstreamMode', ], 'CodingMode' => [ 'shape' => 'Ac3CodingMode', 'locationName' => 'codingMode', ], 'Dialnorm' => [ 'shape' => '__integerMin1Max31', 'locationName' => 'dialnorm', ], 'DrcProfile' => [ 'shape' => 'Ac3DrcProfile', 'locationName' => 'drcProfile', ], 'LfeFilter' => [ 'shape' => 'Ac3LfeFilter', 'locationName' => 'lfeFilter', ], 'MetadataControl' => [ 'shape' => 'Ac3MetadataControl', 'locationName' => 'metadataControl', ], 'AttenuationControl' => [ 'shape' => 'Ac3AttenuationControl', 'locationName' => 'attenuationControl', ], ], ], 'AcceptInputDeviceTransferRequest' => [ 'type' => 'structure', 'members' => [ 'InputDeviceId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'inputDeviceId', ], ], 'required' => [ 'InputDeviceId', ], ], 'AcceptInputDeviceTransferResponse' => [ 'type' => 'structure', 'members' => [], ], 'AccessDenied' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], ], 'AccessibilityType' => [ 'type' => 'string', 'enum' => [ 'DOES_NOT_IMPLEMENT_ACCESSIBILITY_FEATURES', 'IMPLEMENTS_ACCESSIBILITY_FEATURES', ], ], 'AccountConfiguration' => [ 'type' => 'structure', 'members' => [ 'KmsKeyId' => [ 'shape' => '__string', 'locationName' => 'kmsKeyId', ], ], ], 'AfdSignaling' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'FIXED', 'NONE', ], ], 'AncillarySourceSettings' => [ 'type' => 'structure', 'members' => [ 'SourceAncillaryChannelNumber' => [ 'shape' => '__integerMin1Max4', 'locationName' => 'sourceAncillaryChannelNumber', ], ], ], 'ArchiveCdnSettings' => [ 'type' => 'structure', 'members' => [ 'ArchiveS3Settings' => [ 'shape' => 'ArchiveS3Settings', 'locationName' => 'archiveS3Settings', ], ], ], 'ArchiveContainerSettings' => [ 'type' => 'structure', 'members' => [ 'M2tsSettings' => [ 'shape' => 'M2tsSettings', 'locationName' => 'm2tsSettings', ], 'RawSettings' => [ 'shape' => 'RawSettings', 'locationName' => 'rawSettings', ], ], ], 'ArchiveGroupSettings' => [ 'type' => 'structure', 'members' => [ 'ArchiveCdnSettings' => [ 'shape' => 'ArchiveCdnSettings', 'locationName' => 'archiveCdnSettings', ], 'Destination' => [ 'shape' => 'OutputLocationRef', 'locationName' => 'destination', ], 'RolloverInterval' => [ 'shape' => '__integerMin1', 'locationName' => 'rolloverInterval', ], ], 'required' => [ 'Destination', ], ], 'ArchiveOutputSettings' => [ 'type' => 'structure', 'members' => [ 'ContainerSettings' => [ 'shape' => 'ArchiveContainerSettings', 'locationName' => 'containerSettings', ], 'Extension' => [ 'shape' => '__string', 'locationName' => 'extension', ], 'NameModifier' => [ 'shape' => '__string', 'locationName' => 'nameModifier', ], ], 'required' => [ 'ContainerSettings', ], ], 'ArchiveS3LogUploads' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'ArchiveS3Settings' => [ 'type' => 'structure', 'members' => [ 'CannedAcl' => [ 'shape' => 'S3CannedAcl', 'locationName' => 'cannedAcl', ], ], ], 'AribDestinationSettings' => [ 'type' => 'structure', 'members' => [], ], 'AribSourceSettings' => [ 'type' => 'structure', 'members' => [], ], 'AudioChannelMapping' => [ 'type' => 'structure', 'members' => [ 'InputChannelLevels' => [ 'shape' => '__listOfInputChannelLevel', 'locationName' => 'inputChannelLevels', ], 'OutputChannel' => [ 'shape' => '__integerMin0Max7', 'locationName' => 'outputChannel', ], ], 'required' => [ 'OutputChannel', 'InputChannelLevels', ], ], 'AudioCodecSettings' => [ 'type' => 'structure', 'members' => [ 'AacSettings' => [ 'shape' => 'AacSettings', 'locationName' => 'aacSettings', ], 'Ac3Settings' => [ 'shape' => 'Ac3Settings', 'locationName' => 'ac3Settings', ], 'Eac3AtmosSettings' => [ 'shape' => 'Eac3AtmosSettings', 'locationName' => 'eac3AtmosSettings', ], 'Eac3Settings' => [ 'shape' => 'Eac3Settings', 'locationName' => 'eac3Settings', ], 'Mp2Settings' => [ 'shape' => 'Mp2Settings', 'locationName' => 'mp2Settings', ], 'PassThroughSettings' => [ 'shape' => 'PassThroughSettings', 'locationName' => 'passThroughSettings', ], 'WavSettings' => [ 'shape' => 'WavSettings', 'locationName' => 'wavSettings', ], ], ], 'AudioDescription' => [ 'type' => 'structure', 'members' => [ 'AudioNormalizationSettings' => [ 'shape' => 'AudioNormalizationSettings', 'locationName' => 'audioNormalizationSettings', ], 'AudioSelectorName' => [ 'shape' => '__string', 'locationName' => 'audioSelectorName', ], 'AudioType' => [ 'shape' => 'AudioType', 'locationName' => 'audioType', ], 'AudioTypeControl' => [ 'shape' => 'AudioDescriptionAudioTypeControl', 'locationName' => 'audioTypeControl', ], 'AudioWatermarkingSettings' => [ 'shape' => 'AudioWatermarkSettings', 'locationName' => 'audioWatermarkingSettings', ], 'CodecSettings' => [ 'shape' => 'AudioCodecSettings', 'locationName' => 'codecSettings', ], 'LanguageCode' => [ 'shape' => '__stringMin1Max35', 'locationName' => 'languageCode', ], 'LanguageCodeControl' => [ 'shape' => 'AudioDescriptionLanguageCodeControl', 'locationName' => 'languageCodeControl', ], 'Name' => [ 'shape' => '__stringMax255', 'locationName' => 'name', ], 'RemixSettings' => [ 'shape' => 'RemixSettings', 'locationName' => 'remixSettings', ], 'StreamName' => [ 'shape' => '__string', 'locationName' => 'streamName', ], 'AudioDashRoles' => [ 'shape' => '__listOfDashRoleAudio', 'locationName' => 'audioDashRoles', ], 'DvbDashAccessibility' => [ 'shape' => 'DvbDashAccessibility', 'locationName' => 'dvbDashAccessibility', ], ], 'required' => [ 'AudioSelectorName', 'Name', ], ], 'AudioDescriptionAudioTypeControl' => [ 'type' => 'string', 'enum' => [ 'FOLLOW_INPUT', 'USE_CONFIGURED', ], ], 'AudioDescriptionLanguageCodeControl' => [ 'type' => 'string', 'enum' => [ 'FOLLOW_INPUT', 'USE_CONFIGURED', ], ], 'AudioDolbyEDecode' => [ 'type' => 'structure', 'members' => [ 'ProgramSelection' => [ 'shape' => 'DolbyEProgramSelection', 'locationName' => 'programSelection', ], ], 'required' => [ 'ProgramSelection', ], ], 'AudioHlsRenditionSelection' => [ 'type' => 'structure', 'members' => [ 'GroupId' => [ 'shape' => '__stringMin1', 'locationName' => 'groupId', ], 'Name' => [ 'shape' => '__stringMin1', 'locationName' => 'name', ], ], 'required' => [ 'Name', 'GroupId', ], ], 'AudioLanguageSelection' => [ 'type' => 'structure', 'members' => [ 'LanguageCode' => [ 'shape' => '__string', 'locationName' => 'languageCode', ], 'LanguageSelectionPolicy' => [ 'shape' => 'AudioLanguageSelectionPolicy', 'locationName' => 'languageSelectionPolicy', ], ], 'required' => [ 'LanguageCode', ], ], 'AudioLanguageSelectionPolicy' => [ 'type' => 'string', 'enum' => [ 'LOOSE', 'STRICT', ], ], 'AudioNormalizationAlgorithm' => [ 'type' => 'string', 'enum' => [ 'ITU_1770_1', 'ITU_1770_2', ], ], 'AudioNormalizationAlgorithmControl' => [ 'type' => 'string', 'enum' => [ 'CORRECT_AUDIO', ], ], 'AudioNormalizationSettings' => [ 'type' => 'structure', 'members' => [ 'Algorithm' => [ 'shape' => 'AudioNormalizationAlgorithm', 'locationName' => 'algorithm', ], 'AlgorithmControl' => [ 'shape' => 'AudioNormalizationAlgorithmControl', 'locationName' => 'algorithmControl', ], 'TargetLkfs' => [ 'shape' => '__doubleMinNegative59Max0', 'locationName' => 'targetLkfs', ], ], ], 'AudioOnlyHlsSegmentType' => [ 'type' => 'string', 'enum' => [ 'AAC', 'FMP4', ], ], 'AudioOnlyHlsSettings' => [ 'type' => 'structure', 'members' => [ 'AudioGroupId' => [ 'shape' => '__string', 'locationName' => 'audioGroupId', ], 'AudioOnlyImage' => [ 'shape' => 'InputLocation', 'locationName' => 'audioOnlyImage', ], 'AudioTrackType' => [ 'shape' => 'AudioOnlyHlsTrackType', 'locationName' => 'audioTrackType', ], 'SegmentType' => [ 'shape' => 'AudioOnlyHlsSegmentType', 'locationName' => 'segmentType', ], ], ], 'AudioOnlyHlsTrackType' => [ 'type' => 'string', 'enum' => [ 'ALTERNATE_AUDIO_AUTO_SELECT', 'ALTERNATE_AUDIO_AUTO_SELECT_DEFAULT', 'ALTERNATE_AUDIO_NOT_AUTO_SELECT', 'AUDIO_ONLY_VARIANT_STREAM', ], ], 'AudioPidSelection' => [ 'type' => 'structure', 'members' => [ 'Pid' => [ 'shape' => '__integerMin0Max8191', 'locationName' => 'pid', ], ], 'required' => [ 'Pid', ], ], 'AudioSelector' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => '__stringMin1', 'locationName' => 'name', ], 'SelectorSettings' => [ 'shape' => 'AudioSelectorSettings', 'locationName' => 'selectorSettings', ], ], 'required' => [ 'Name', ], ], 'AudioSelectorSettings' => [ 'type' => 'structure', 'members' => [ 'AudioHlsRenditionSelection' => [ 'shape' => 'AudioHlsRenditionSelection', 'locationName' => 'audioHlsRenditionSelection', ], 'AudioLanguageSelection' => [ 'shape' => 'AudioLanguageSelection', 'locationName' => 'audioLanguageSelection', ], 'AudioPidSelection' => [ 'shape' => 'AudioPidSelection', 'locationName' => 'audioPidSelection', ], 'AudioTrackSelection' => [ 'shape' => 'AudioTrackSelection', 'locationName' => 'audioTrackSelection', ], ], ], 'AudioSilenceFailoverSettings' => [ 'type' => 'structure', 'members' => [ 'AudioSelectorName' => [ 'shape' => '__string', 'locationName' => 'audioSelectorName', ], 'AudioSilenceThresholdMsec' => [ 'shape' => '__integerMin1000', 'locationName' => 'audioSilenceThresholdMsec', ], ], 'required' => [ 'AudioSelectorName', ], ], 'AudioTrack' => [ 'type' => 'structure', 'members' => [ 'Track' => [ 'shape' => '__integerMin1', 'locationName' => 'track', ], ], 'required' => [ 'Track', ], ], 'AudioTrackSelection' => [ 'type' => 'structure', 'members' => [ 'Tracks' => [ 'shape' => '__listOfAudioTrack', 'locationName' => 'tracks', ], 'DolbyEDecode' => [ 'shape' => 'AudioDolbyEDecode', 'locationName' => 'dolbyEDecode', ], ], 'required' => [ 'Tracks', ], ], 'AudioType' => [ 'type' => 'string', 'enum' => [ 'CLEAN_EFFECTS', 'HEARING_IMPAIRED', 'UNDEFINED', 'VISUAL_IMPAIRED_COMMENTARY', ], ], 'AudioWatermarkSettings' => [ 'type' => 'structure', 'members' => [ 'NielsenWatermarksSettings' => [ 'shape' => 'NielsenWatermarksSettings', 'locationName' => 'nielsenWatermarksSettings', ], ], ], 'AuthenticationScheme' => [ 'type' => 'string', 'enum' => [ 'AKAMAI', 'COMMON', ], ], 'AutomaticInputFailoverSettings' => [ 'type' => 'structure', 'members' => [ 'ErrorClearTimeMsec' => [ 'shape' => '__integerMin1', 'locationName' => 'errorClearTimeMsec', ], 'FailoverConditions' => [ 'shape' => '__listOfFailoverCondition', 'locationName' => 'failoverConditions', ], 'InputPreference' => [ 'shape' => 'InputPreference', 'locationName' => 'inputPreference', ], 'SecondaryInputId' => [ 'shape' => '__string', 'locationName' => 'secondaryInputId', ], ], 'required' => [ 'SecondaryInputId', ], ], 'AvailBlanking' => [ 'type' => 'structure', 'members' => [ 'AvailBlankingImage' => [ 'shape' => 'InputLocation', 'locationName' => 'availBlankingImage', ], 'State' => [ 'shape' => 'AvailBlankingState', 'locationName' => 'state', ], ], ], 'AvailBlankingState' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'AvailConfiguration' => [ 'type' => 'structure', 'members' => [ 'AvailSettings' => [ 'shape' => 'AvailSettings', 'locationName' => 'availSettings', ], 'Scte35SegmentationScope' => [ 'shape' => 'Scte35SegmentationScope', 'locationName' => 'scte35SegmentationScope', ], ], ], 'AvailSettings' => [ 'type' => 'structure', 'members' => [ 'Esam' => [ 'shape' => 'Esam', 'locationName' => 'esam', ], 'Scte35SpliceInsert' => [ 'shape' => 'Scte35SpliceInsert', 'locationName' => 'scte35SpliceInsert', ], 'Scte35TimeSignalApos' => [ 'shape' => 'Scte35TimeSignalApos', 'locationName' => 'scte35TimeSignalApos', ], ], ], 'BadGatewayException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 502, ], ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 400, ], ], 'BatchDelete' => [ 'type' => 'structure', 'members' => [ 'ChannelIds' => [ 'shape' => '__listOf__string', 'locationName' => 'channelIds', ], 'InputIds' => [ 'shape' => '__listOf__string', 'locationName' => 'inputIds', ], 'InputSecurityGroupIds' => [ 'shape' => '__listOf__string', 'locationName' => 'inputSecurityGroupIds', ], 'MultiplexIds' => [ 'shape' => '__listOf__string', 'locationName' => 'multiplexIds', ], ], ], 'BatchDeleteRequest' => [ 'type' => 'structure', 'members' => [ 'ChannelIds' => [ 'shape' => '__listOf__string', 'locationName' => 'channelIds', ], 'InputIds' => [ 'shape' => '__listOf__string', 'locationName' => 'inputIds', ], 'InputSecurityGroupIds' => [ 'shape' => '__listOf__string', 'locationName' => 'inputSecurityGroupIds', ], 'MultiplexIds' => [ 'shape' => '__listOf__string', 'locationName' => 'multiplexIds', ], ], ], 'BatchDeleteResponse' => [ 'type' => 'structure', 'members' => [ 'Failed' => [ 'shape' => '__listOfBatchFailedResultModel', 'locationName' => 'failed', ], 'Successful' => [ 'shape' => '__listOfBatchSuccessfulResultModel', 'locationName' => 'successful', ], ], ], 'BatchDeleteResultModel' => [ 'type' => 'structure', 'members' => [ 'Failed' => [ 'shape' => '__listOfBatchFailedResultModel', 'locationName' => 'failed', ], 'Successful' => [ 'shape' => '__listOfBatchSuccessfulResultModel', 'locationName' => 'successful', ], ], ], 'BatchFailedResultModel' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'Code' => [ 'shape' => '__string', 'locationName' => 'code', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], ], 'BatchScheduleActionCreateRequest' => [ 'type' => 'structure', 'members' => [ 'ScheduleActions' => [ 'shape' => '__listOfScheduleAction', 'locationName' => 'scheduleActions', ], ], 'required' => [ 'ScheduleActions', ], ], 'BatchScheduleActionCreateResult' => [ 'type' => 'structure', 'members' => [ 'ScheduleActions' => [ 'shape' => '__listOfScheduleAction', 'locationName' => 'scheduleActions', ], ], 'required' => [ 'ScheduleActions', ], ], 'BatchScheduleActionDeleteRequest' => [ 'type' => 'structure', 'members' => [ 'ActionNames' => [ 'shape' => '__listOf__string', 'locationName' => 'actionNames', ], ], 'required' => [ 'ActionNames', ], ], 'BatchScheduleActionDeleteResult' => [ 'type' => 'structure', 'members' => [ 'ScheduleActions' => [ 'shape' => '__listOfScheduleAction', 'locationName' => 'scheduleActions', ], ], 'required' => [ 'ScheduleActions', ], ], 'BatchStart' => [ 'type' => 'structure', 'members' => [ 'ChannelIds' => [ 'shape' => '__listOf__string', 'locationName' => 'channelIds', ], 'MultiplexIds' => [ 'shape' => '__listOf__string', 'locationName' => 'multiplexIds', ], ], ], 'BatchStartRequest' => [ 'type' => 'structure', 'members' => [ 'ChannelIds' => [ 'shape' => '__listOf__string', 'locationName' => 'channelIds', ], 'MultiplexIds' => [ 'shape' => '__listOf__string', 'locationName' => 'multiplexIds', ], ], ], 'BatchStartResponse' => [ 'type' => 'structure', 'members' => [ 'Failed' => [ 'shape' => '__listOfBatchFailedResultModel', 'locationName' => 'failed', ], 'Successful' => [ 'shape' => '__listOfBatchSuccessfulResultModel', 'locationName' => 'successful', ], ], ], 'BatchStartResultModel' => [ 'type' => 'structure', 'members' => [ 'Failed' => [ 'shape' => '__listOfBatchFailedResultModel', 'locationName' => 'failed', ], 'Successful' => [ 'shape' => '__listOfBatchSuccessfulResultModel', 'locationName' => 'successful', ], ], ], 'BatchStop' => [ 'type' => 'structure', 'members' => [ 'ChannelIds' => [ 'shape' => '__listOf__string', 'locationName' => 'channelIds', ], 'MultiplexIds' => [ 'shape' => '__listOf__string', 'locationName' => 'multiplexIds', ], ], ], 'BatchStopRequest' => [ 'type' => 'structure', 'members' => [ 'ChannelIds' => [ 'shape' => '__listOf__string', 'locationName' => 'channelIds', ], 'MultiplexIds' => [ 'shape' => '__listOf__string', 'locationName' => 'multiplexIds', ], ], ], 'BatchStopResponse' => [ 'type' => 'structure', 'members' => [ 'Failed' => [ 'shape' => '__listOfBatchFailedResultModel', 'locationName' => 'failed', ], 'Successful' => [ 'shape' => '__listOfBatchSuccessfulResultModel', 'locationName' => 'successful', ], ], ], 'BatchStopResultModel' => [ 'type' => 'structure', 'members' => [ 'Failed' => [ 'shape' => '__listOfBatchFailedResultModel', 'locationName' => 'failed', ], 'Successful' => [ 'shape' => '__listOfBatchSuccessfulResultModel', 'locationName' => 'successful', ], ], ], 'BatchSuccessfulResultModel' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'State' => [ 'shape' => '__string', 'locationName' => 'state', ], ], ], 'BatchUpdateScheduleRequest' => [ 'type' => 'structure', 'members' => [ 'ChannelId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'channelId', ], 'Creates' => [ 'shape' => 'BatchScheduleActionCreateRequest', 'locationName' => 'creates', ], 'Deletes' => [ 'shape' => 'BatchScheduleActionDeleteRequest', 'locationName' => 'deletes', ], ], 'required' => [ 'ChannelId', ], ], 'BatchUpdateScheduleResponse' => [ 'type' => 'structure', 'members' => [ 'Creates' => [ 'shape' => 'BatchScheduleActionCreateResult', 'locationName' => 'creates', ], 'Deletes' => [ 'shape' => 'BatchScheduleActionDeleteResult', 'locationName' => 'deletes', ], ], ], 'BatchUpdateScheduleResult' => [ 'type' => 'structure', 'members' => [ 'Creates' => [ 'shape' => 'BatchScheduleActionCreateResult', 'locationName' => 'creates', ], 'Deletes' => [ 'shape' => 'BatchScheduleActionDeleteResult', 'locationName' => 'deletes', ], ], ], 'BlackoutSlate' => [ 'type' => 'structure', 'members' => [ 'BlackoutSlateImage' => [ 'shape' => 'InputLocation', 'locationName' => 'blackoutSlateImage', ], 'NetworkEndBlackout' => [ 'shape' => 'BlackoutSlateNetworkEndBlackout', 'locationName' => 'networkEndBlackout', ], 'NetworkEndBlackoutImage' => [ 'shape' => 'InputLocation', 'locationName' => 'networkEndBlackoutImage', ], 'NetworkId' => [ 'shape' => '__stringMin34Max34', 'locationName' => 'networkId', ], 'State' => [ 'shape' => 'BlackoutSlateState', 'locationName' => 'state', ], ], ], 'BlackoutSlateNetworkEndBlackout' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'BlackoutSlateState' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'BurnInAlignment' => [ 'type' => 'string', 'enum' => [ 'CENTERED', 'LEFT', 'SMART', ], ], 'BurnInBackgroundColor' => [ 'type' => 'string', 'enum' => [ 'BLACK', 'NONE', 'WHITE', ], ], 'BurnInDestinationSettings' => [ 'type' => 'structure', 'members' => [ 'Alignment' => [ 'shape' => 'BurnInAlignment', 'locationName' => 'alignment', ], 'BackgroundColor' => [ 'shape' => 'BurnInBackgroundColor', 'locationName' => 'backgroundColor', ], 'BackgroundOpacity' => [ 'shape' => '__integerMin0Max255', 'locationName' => 'backgroundOpacity', ], 'Font' => [ 'shape' => 'InputLocation', 'locationName' => 'font', ], 'FontColor' => [ 'shape' => 'BurnInFontColor', 'locationName' => 'fontColor', ], 'FontOpacity' => [ 'shape' => '__integerMin0Max255', 'locationName' => 'fontOpacity', ], 'FontResolution' => [ 'shape' => '__integerMin96Max600', 'locationName' => 'fontResolution', ], 'FontSize' => [ 'shape' => '__string', 'locationName' => 'fontSize', ], 'OutlineColor' => [ 'shape' => 'BurnInOutlineColor', 'locationName' => 'outlineColor', ], 'OutlineSize' => [ 'shape' => '__integerMin0Max10', 'locationName' => 'outlineSize', ], 'ShadowColor' => [ 'shape' => 'BurnInShadowColor', 'locationName' => 'shadowColor', ], 'ShadowOpacity' => [ 'shape' => '__integerMin0Max255', 'locationName' => 'shadowOpacity', ], 'ShadowXOffset' => [ 'shape' => '__integer', 'locationName' => 'shadowXOffset', ], 'ShadowYOffset' => [ 'shape' => '__integer', 'locationName' => 'shadowYOffset', ], 'TeletextGridControl' => [ 'shape' => 'BurnInTeletextGridControl', 'locationName' => 'teletextGridControl', ], 'XPosition' => [ 'shape' => '__integerMin0', 'locationName' => 'xPosition', ], 'YPosition' => [ 'shape' => '__integerMin0', 'locationName' => 'yPosition', ], ], ], 'BurnInFontColor' => [ 'type' => 'string', 'enum' => [ 'BLACK', 'BLUE', 'GREEN', 'RED', 'WHITE', 'YELLOW', ], ], 'BurnInOutlineColor' => [ 'type' => 'string', 'enum' => [ 'BLACK', 'BLUE', 'GREEN', 'RED', 'WHITE', 'YELLOW', ], ], 'BurnInShadowColor' => [ 'type' => 'string', 'enum' => [ 'BLACK', 'NONE', 'WHITE', ], ], 'BurnInTeletextGridControl' => [ 'type' => 'string', 'enum' => [ 'FIXED', 'SCALED', ], ], 'CancelInputDeviceTransferRequest' => [ 'type' => 'structure', 'members' => [ 'InputDeviceId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'inputDeviceId', ], ], 'required' => [ 'InputDeviceId', ], ], 'CancelInputDeviceTransferResponse' => [ 'type' => 'structure', 'members' => [], ], 'CaptionDescription' => [ 'type' => 'structure', 'members' => [ 'Accessibility' => [ 'shape' => 'AccessibilityType', 'locationName' => 'accessibility', ], 'CaptionSelectorName' => [ 'shape' => '__string', 'locationName' => 'captionSelectorName', ], 'DestinationSettings' => [ 'shape' => 'CaptionDestinationSettings', 'locationName' => 'destinationSettings', ], 'LanguageCode' => [ 'shape' => '__string', 'locationName' => 'languageCode', ], 'LanguageDescription' => [ 'shape' => '__string', 'locationName' => 'languageDescription', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'CaptionDashRoles' => [ 'shape' => '__listOfDashRoleCaption', 'locationName' => 'captionDashRoles', ], 'DvbDashAccessibility' => [ 'shape' => 'DvbDashAccessibility', 'locationName' => 'dvbDashAccessibility', ], ], 'required' => [ 'CaptionSelectorName', 'Name', ], ], 'CaptionDestinationSettings' => [ 'type' => 'structure', 'members' => [ 'AribDestinationSettings' => [ 'shape' => 'AribDestinationSettings', 'locationName' => 'aribDestinationSettings', ], 'BurnInDestinationSettings' => [ 'shape' => 'BurnInDestinationSettings', 'locationName' => 'burnInDestinationSettings', ], 'DvbSubDestinationSettings' => [ 'shape' => 'DvbSubDestinationSettings', 'locationName' => 'dvbSubDestinationSettings', ], 'EbuTtDDestinationSettings' => [ 'shape' => 'EbuTtDDestinationSettings', 'locationName' => 'ebuTtDDestinationSettings', ], 'EmbeddedDestinationSettings' => [ 'shape' => 'EmbeddedDestinationSettings', 'locationName' => 'embeddedDestinationSettings', ], 'EmbeddedPlusScte20DestinationSettings' => [ 'shape' => 'EmbeddedPlusScte20DestinationSettings', 'locationName' => 'embeddedPlusScte20DestinationSettings', ], 'RtmpCaptionInfoDestinationSettings' => [ 'shape' => 'RtmpCaptionInfoDestinationSettings', 'locationName' => 'rtmpCaptionInfoDestinationSettings', ], 'Scte20PlusEmbeddedDestinationSettings' => [ 'shape' => 'Scte20PlusEmbeddedDestinationSettings', 'locationName' => 'scte20PlusEmbeddedDestinationSettings', ], 'Scte27DestinationSettings' => [ 'shape' => 'Scte27DestinationSettings', 'locationName' => 'scte27DestinationSettings', ], 'SmpteTtDestinationSettings' => [ 'shape' => 'SmpteTtDestinationSettings', 'locationName' => 'smpteTtDestinationSettings', ], 'TeletextDestinationSettings' => [ 'shape' => 'TeletextDestinationSettings', 'locationName' => 'teletextDestinationSettings', ], 'TtmlDestinationSettings' => [ 'shape' => 'TtmlDestinationSettings', 'locationName' => 'ttmlDestinationSettings', ], 'WebvttDestinationSettings' => [ 'shape' => 'WebvttDestinationSettings', 'locationName' => 'webvttDestinationSettings', ], ], ], 'CaptionLanguageMapping' => [ 'type' => 'structure', 'members' => [ 'CaptionChannel' => [ 'shape' => '__integerMin1Max4', 'locationName' => 'captionChannel', ], 'LanguageCode' => [ 'shape' => '__stringMin3Max3', 'locationName' => 'languageCode', ], 'LanguageDescription' => [ 'shape' => '__stringMin1', 'locationName' => 'languageDescription', ], ], 'required' => [ 'LanguageCode', 'LanguageDescription', 'CaptionChannel', ], ], 'CaptionRectangle' => [ 'type' => 'structure', 'members' => [ 'Height' => [ 'shape' => '__doubleMin0Max100', 'locationName' => 'height', ], 'LeftOffset' => [ 'shape' => '__doubleMin0Max100', 'locationName' => 'leftOffset', ], 'TopOffset' => [ 'shape' => '__doubleMin0Max100', 'locationName' => 'topOffset', ], 'Width' => [ 'shape' => '__doubleMin0Max100', 'locationName' => 'width', ], ], 'required' => [ 'TopOffset', 'Height', 'Width', 'LeftOffset', ], ], 'CaptionSelector' => [ 'type' => 'structure', 'members' => [ 'LanguageCode' => [ 'shape' => '__string', 'locationName' => 'languageCode', ], 'Name' => [ 'shape' => '__stringMin1', 'locationName' => 'name', ], 'SelectorSettings' => [ 'shape' => 'CaptionSelectorSettings', 'locationName' => 'selectorSettings', ], ], 'required' => [ 'Name', ], ], 'CaptionSelectorSettings' => [ 'type' => 'structure', 'members' => [ 'AncillarySourceSettings' => [ 'shape' => 'AncillarySourceSettings', 'locationName' => 'ancillarySourceSettings', ], 'AribSourceSettings' => [ 'shape' => 'AribSourceSettings', 'locationName' => 'aribSourceSettings', ], 'DvbSubSourceSettings' => [ 'shape' => 'DvbSubSourceSettings', 'locationName' => 'dvbSubSourceSettings', ], 'EmbeddedSourceSettings' => [ 'shape' => 'EmbeddedSourceSettings', 'locationName' => 'embeddedSourceSettings', ], 'Scte20SourceSettings' => [ 'shape' => 'Scte20SourceSettings', 'locationName' => 'scte20SourceSettings', ], 'Scte27SourceSettings' => [ 'shape' => 'Scte27SourceSettings', 'locationName' => 'scte27SourceSettings', ], 'TeletextSourceSettings' => [ 'shape' => 'TeletextSourceSettings', 'locationName' => 'teletextSourceSettings', ], ], ], 'CdiInputResolution' => [ 'type' => 'string', 'enum' => [ 'SD', 'HD', 'FHD', 'UHD', ], ], 'CdiInputSpecification' => [ 'type' => 'structure', 'members' => [ 'Resolution' => [ 'shape' => 'CdiInputResolution', 'locationName' => 'resolution', ], ], ], 'Channel' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'CdiInputSpecification' => [ 'shape' => 'CdiInputSpecification', 'locationName' => 'cdiInputSpecification', ], 'ChannelClass' => [ 'shape' => 'ChannelClass', 'locationName' => 'channelClass', ], 'Destinations' => [ 'shape' => '__listOfOutputDestination', 'locationName' => 'destinations', ], 'EgressEndpoints' => [ 'shape' => '__listOfChannelEgressEndpoint', 'locationName' => 'egressEndpoints', ], 'EncoderSettings' => [ 'shape' => 'EncoderSettings', 'locationName' => 'encoderSettings', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'InputAttachments' => [ 'shape' => '__listOfInputAttachment', 'locationName' => 'inputAttachments', ], 'InputSpecification' => [ 'shape' => 'InputSpecification', 'locationName' => 'inputSpecification', ], 'LogLevel' => [ 'shape' => 'LogLevel', 'locationName' => 'logLevel', ], 'Maintenance' => [ 'shape' => 'MaintenanceStatus', 'locationName' => 'maintenance', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'PipelineDetails' => [ 'shape' => '__listOfPipelineDetail', 'locationName' => 'pipelineDetails', ], 'PipelinesRunningCount' => [ 'shape' => '__integer', 'locationName' => 'pipelinesRunningCount', ], 'RoleArn' => [ 'shape' => '__string', 'locationName' => 'roleArn', ], 'State' => [ 'shape' => 'ChannelState', 'locationName' => 'state', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'Vpc' => [ 'shape' => 'VpcOutputSettingsDescription', 'locationName' => 'vpc', ], 'AnywhereSettings' => [ 'shape' => 'DescribeAnywhereSettings', 'locationName' => 'anywhereSettings', ], 'ChannelEngineVersion' => [ 'shape' => 'ChannelEngineVersionResponse', 'locationName' => 'channelEngineVersion', ], ], ], 'ChannelClass' => [ 'type' => 'string', 'enum' => [ 'STANDARD', 'SINGLE_PIPELINE', ], ], 'ChannelConfigurationValidationError' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], 'ValidationErrors' => [ 'shape' => '__listOfValidationError', 'locationName' => 'validationErrors', ], ], ], 'ChannelEgressEndpoint' => [ 'type' => 'structure', 'members' => [ 'SourceIp' => [ 'shape' => '__string', 'locationName' => 'sourceIp', ], ], ], 'ChannelState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATE_FAILED', 'IDLE', 'STARTING', 'RUNNING', 'RECOVERING', 'STOPPING', 'DELETING', 'DELETED', 'UPDATING', 'UPDATE_FAILED', ], ], 'ChannelSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'CdiInputSpecification' => [ 'shape' => 'CdiInputSpecification', 'locationName' => 'cdiInputSpecification', ], 'ChannelClass' => [ 'shape' => 'ChannelClass', 'locationName' => 'channelClass', ], 'Destinations' => [ 'shape' => '__listOfOutputDestination', 'locationName' => 'destinations', ], 'EgressEndpoints' => [ 'shape' => '__listOfChannelEgressEndpoint', 'locationName' => 'egressEndpoints', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'InputAttachments' => [ 'shape' => '__listOfInputAttachment', 'locationName' => 'inputAttachments', ], 'InputSpecification' => [ 'shape' => 'InputSpecification', 'locationName' => 'inputSpecification', ], 'LogLevel' => [ 'shape' => 'LogLevel', 'locationName' => 'logLevel', ], 'Maintenance' => [ 'shape' => 'MaintenanceStatus', 'locationName' => 'maintenance', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'PipelinesRunningCount' => [ 'shape' => '__integer', 'locationName' => 'pipelinesRunningCount', ], 'RoleArn' => [ 'shape' => '__string', 'locationName' => 'roleArn', ], 'State' => [ 'shape' => 'ChannelState', 'locationName' => 'state', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'Vpc' => [ 'shape' => 'VpcOutputSettingsDescription', 'locationName' => 'vpc', ], 'AnywhereSettings' => [ 'shape' => 'DescribeAnywhereSettings', 'locationName' => 'anywhereSettings', ], 'ChannelEngineVersion' => [ 'shape' => 'ChannelEngineVersionResponse', 'locationName' => 'channelEngineVersion', ], 'UsedChannelEngineVersions' => [ 'shape' => '__listOfChannelEngineVersionResponse', 'locationName' => 'usedChannelEngineVersions', ], ], ], 'ClaimDeviceRequest' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], ], ], 'ClaimDeviceResponse' => [ 'type' => 'structure', 'members' => [], ], 'ColorCorrection' => [ 'type' => 'structure', 'members' => [ 'InputColorSpace' => [ 'shape' => 'ColorSpace', 'locationName' => 'inputColorSpace', ], 'OutputColorSpace' => [ 'shape' => 'ColorSpace', 'locationName' => 'outputColorSpace', ], 'Uri' => [ 'shape' => '__string', 'locationName' => 'uri', ], ], 'required' => [ 'OutputColorSpace', 'InputColorSpace', 'Uri', ], ], 'ColorCorrectionSettings' => [ 'type' => 'structure', 'members' => [ 'GlobalColorCorrections' => [ 'shape' => '__listOfColorCorrection', 'locationName' => 'globalColorCorrections', ], ], 'required' => [ 'GlobalColorCorrections', ], ], 'ColorSpace' => [ 'type' => 'string', 'enum' => [ 'HDR10', 'HLG_2020', 'REC_601', 'REC_709', ], ], 'ColorSpacePassthroughSettings' => [ 'type' => 'structure', 'members' => [], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 409, ], ], 'CreateChannel' => [ 'type' => 'structure', 'members' => [ 'CdiInputSpecification' => [ 'shape' => 'CdiInputSpecification', 'locationName' => 'cdiInputSpecification', ], 'ChannelClass' => [ 'shape' => 'ChannelClass', 'locationName' => 'channelClass', ], 'Destinations' => [ 'shape' => '__listOfOutputDestination', 'locationName' => 'destinations', ], 'EncoderSettings' => [ 'shape' => 'EncoderSettings', 'locationName' => 'encoderSettings', ], 'InputAttachments' => [ 'shape' => '__listOfInputAttachment', 'locationName' => 'inputAttachments', ], 'InputSpecification' => [ 'shape' => 'InputSpecification', 'locationName' => 'inputSpecification', ], 'LogLevel' => [ 'shape' => 'LogLevel', 'locationName' => 'logLevel', ], 'Maintenance' => [ 'shape' => 'MaintenanceCreateSettings', 'locationName' => 'maintenance', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'RequestId' => [ 'shape' => '__string', 'locationName' => 'requestId', 'idempotencyToken' => true, ], 'Reserved' => [ 'shape' => '__string', 'locationName' => 'reserved', 'deprecated' => true, ], 'RoleArn' => [ 'shape' => '__string', 'locationName' => 'roleArn', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'Vpc' => [ 'shape' => 'VpcOutputSettings', 'locationName' => 'vpc', ], 'AnywhereSettings' => [ 'shape' => 'AnywhereSettings', 'locationName' => 'anywhereSettings', ], 'ChannelEngineVersion' => [ 'shape' => 'ChannelEngineVersionRequest', 'locationName' => 'channelEngineVersion', ], 'DryRun' => [ 'shape' => '__boolean', 'locationName' => 'dryRun', ], ], ], 'CreateChannelRequest' => [ 'type' => 'structure', 'members' => [ 'CdiInputSpecification' => [ 'shape' => 'CdiInputSpecification', 'locationName' => 'cdiInputSpecification', ], 'ChannelClass' => [ 'shape' => 'ChannelClass', 'locationName' => 'channelClass', ], 'Destinations' => [ 'shape' => '__listOfOutputDestination', 'locationName' => 'destinations', ], 'EncoderSettings' => [ 'shape' => 'EncoderSettings', 'locationName' => 'encoderSettings', ], 'InputAttachments' => [ 'shape' => '__listOfInputAttachment', 'locationName' => 'inputAttachments', ], 'InputSpecification' => [ 'shape' => 'InputSpecification', 'locationName' => 'inputSpecification', ], 'LogLevel' => [ 'shape' => 'LogLevel', 'locationName' => 'logLevel', ], 'Maintenance' => [ 'shape' => 'MaintenanceCreateSettings', 'locationName' => 'maintenance', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'RequestId' => [ 'shape' => '__string', 'locationName' => 'requestId', 'idempotencyToken' => true, ], 'Reserved' => [ 'shape' => '__string', 'locationName' => 'reserved', 'deprecated' => true, ], 'RoleArn' => [ 'shape' => '__string', 'locationName' => 'roleArn', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'Vpc' => [ 'shape' => 'VpcOutputSettings', 'locationName' => 'vpc', ], 'AnywhereSettings' => [ 'shape' => 'AnywhereSettings', 'locationName' => 'anywhereSettings', ], 'ChannelEngineVersion' => [ 'shape' => 'ChannelEngineVersionRequest', 'locationName' => 'channelEngineVersion', ], 'DryRun' => [ 'shape' => '__boolean', 'locationName' => 'dryRun', ], ], ], 'CreateChannelResponse' => [ 'type' => 'structure', 'members' => [ 'Channel' => [ 'shape' => 'Channel', 'locationName' => 'channel', ], ], ], 'CreateChannelResultModel' => [ 'type' => 'structure', 'members' => [ 'Channel' => [ 'shape' => 'Channel', 'locationName' => 'channel', ], ], ], 'CreateInput' => [ 'type' => 'structure', 'members' => [ 'Destinations' => [ 'shape' => '__listOfInputDestinationRequest', 'locationName' => 'destinations', ], 'InputDevices' => [ 'shape' => '__listOfInputDeviceSettings', 'locationName' => 'inputDevices', ], 'InputSecurityGroups' => [ 'shape' => '__listOf__string', 'locationName' => 'inputSecurityGroups', ], 'MediaConnectFlows' => [ 'shape' => '__listOfMediaConnectFlowRequest', 'locationName' => 'mediaConnectFlows', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'RequestId' => [ 'shape' => '__string', 'locationName' => 'requestId', 'idempotencyToken' => true, ], 'RoleArn' => [ 'shape' => '__string', 'locationName' => 'roleArn', ], 'Sources' => [ 'shape' => '__listOfInputSourceRequest', 'locationName' => 'sources', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'Type' => [ 'shape' => 'InputType', 'locationName' => 'type', ], 'Vpc' => [ 'shape' => 'InputVpcRequest', 'locationName' => 'vpc', ], 'SrtSettings' => [ 'shape' => 'SrtSettingsRequest', 'locationName' => 'srtSettings', ], 'InputNetworkLocation' => [ 'shape' => 'InputNetworkLocation', 'locationName' => 'inputNetworkLocation', ], 'MulticastSettings' => [ 'shape' => 'MulticastSettingsCreateRequest', 'locationName' => 'multicastSettings', ], 'Smpte2110ReceiverGroupSettings' => [ 'shape' => 'Smpte2110ReceiverGroupSettings', 'locationName' => 'smpte2110ReceiverGroupSettings', ], 'SdiSources' => [ 'shape' => 'InputSdiSources', 'locationName' => 'sdiSources', ], ], ], 'CreateInputRequest' => [ 'type' => 'structure', 'members' => [ 'Destinations' => [ 'shape' => '__listOfInputDestinationRequest', 'locationName' => 'destinations', ], 'InputDevices' => [ 'shape' => '__listOfInputDeviceSettings', 'locationName' => 'inputDevices', ], 'InputSecurityGroups' => [ 'shape' => '__listOf__string', 'locationName' => 'inputSecurityGroups', ], 'MediaConnectFlows' => [ 'shape' => '__listOfMediaConnectFlowRequest', 'locationName' => 'mediaConnectFlows', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'RequestId' => [ 'shape' => '__string', 'locationName' => 'requestId', 'idempotencyToken' => true, ], 'RoleArn' => [ 'shape' => '__string', 'locationName' => 'roleArn', ], 'Sources' => [ 'shape' => '__listOfInputSourceRequest', 'locationName' => 'sources', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'Type' => [ 'shape' => 'InputType', 'locationName' => 'type', ], 'Vpc' => [ 'shape' => 'InputVpcRequest', 'locationName' => 'vpc', ], 'SrtSettings' => [ 'shape' => 'SrtSettingsRequest', 'locationName' => 'srtSettings', ], 'InputNetworkLocation' => [ 'shape' => 'InputNetworkLocation', 'locationName' => 'inputNetworkLocation', ], 'MulticastSettings' => [ 'shape' => 'MulticastSettingsCreateRequest', 'locationName' => 'multicastSettings', ], 'Smpte2110ReceiverGroupSettings' => [ 'shape' => 'Smpte2110ReceiverGroupSettings', 'locationName' => 'smpte2110ReceiverGroupSettings', ], 'SdiSources' => [ 'shape' => 'InputSdiSources', 'locationName' => 'sdiSources', ], ], ], 'CreateInputResponse' => [ 'type' => 'structure', 'members' => [ 'Input' => [ 'shape' => 'Input', 'locationName' => 'input', ], ], ], 'CreateInputResultModel' => [ 'type' => 'structure', 'members' => [ 'Input' => [ 'shape' => 'Input', 'locationName' => 'input', ], ], ], 'CreateInputSecurityGroupRequest' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'WhitelistRules' => [ 'shape' => '__listOfInputWhitelistRuleCidr', 'locationName' => 'whitelistRules', ], ], ], 'CreateInputSecurityGroupResponse' => [ 'type' => 'structure', 'members' => [ 'SecurityGroup' => [ 'shape' => 'InputSecurityGroup', 'locationName' => 'securityGroup', ], ], ], 'CreateInputSecurityGroupResultModel' => [ 'type' => 'structure', 'members' => [ 'SecurityGroup' => [ 'shape' => 'InputSecurityGroup', 'locationName' => 'securityGroup', ], ], ], 'CreateMultiplex' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZones' => [ 'shape' => '__listOf__string', 'locationName' => 'availabilityZones', ], 'MultiplexSettings' => [ 'shape' => 'MultiplexSettings', 'locationName' => 'multiplexSettings', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'RequestId' => [ 'shape' => '__string', 'locationName' => 'requestId', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], ], 'required' => [ 'RequestId', 'MultiplexSettings', 'AvailabilityZones', 'Name', ], ], 'CreateMultiplexProgram' => [ 'type' => 'structure', 'members' => [ 'MultiplexProgramSettings' => [ 'shape' => 'MultiplexProgramSettings', 'locationName' => 'multiplexProgramSettings', ], 'ProgramName' => [ 'shape' => '__string', 'locationName' => 'programName', ], 'RequestId' => [ 'shape' => '__string', 'locationName' => 'requestId', 'idempotencyToken' => true, ], ], 'required' => [ 'RequestId', 'MultiplexProgramSettings', 'ProgramName', ], ], 'CreateMultiplexProgramRequest' => [ 'type' => 'structure', 'members' => [ 'MultiplexId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'multiplexId', ], 'MultiplexProgramSettings' => [ 'shape' => 'MultiplexProgramSettings', 'locationName' => 'multiplexProgramSettings', ], 'ProgramName' => [ 'shape' => '__string', 'locationName' => 'programName', ], 'RequestId' => [ 'shape' => '__string', 'locationName' => 'requestId', 'idempotencyToken' => true, ], ], 'required' => [ 'MultiplexId', 'RequestId', 'MultiplexProgramSettings', 'ProgramName', ], ], 'CreateMultiplexProgramResponse' => [ 'type' => 'structure', 'members' => [ 'MultiplexProgram' => [ 'shape' => 'MultiplexProgram', 'locationName' => 'multiplexProgram', ], ], ], 'CreateMultiplexProgramResultModel' => [ 'type' => 'structure', 'members' => [ 'MultiplexProgram' => [ 'shape' => 'MultiplexProgram', 'locationName' => 'multiplexProgram', ], ], ], 'CreateMultiplexRequest' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZones' => [ 'shape' => '__listOf__string', 'locationName' => 'availabilityZones', ], 'MultiplexSettings' => [ 'shape' => 'MultiplexSettings', 'locationName' => 'multiplexSettings', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'RequestId' => [ 'shape' => '__string', 'locationName' => 'requestId', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], ], 'required' => [ 'RequestId', 'MultiplexSettings', 'AvailabilityZones', 'Name', ], ], 'CreateMultiplexResponse' => [ 'type' => 'structure', 'members' => [ 'Multiplex' => [ 'shape' => 'Multiplex', 'locationName' => 'multiplex', ], ], ], 'CreateMultiplexResultModel' => [ 'type' => 'structure', 'members' => [ 'Multiplex' => [ 'shape' => 'Multiplex', 'locationName' => 'multiplex', ], ], ], 'CreatePartnerInput' => [ 'type' => 'structure', 'members' => [ 'RequestId' => [ 'shape' => '__string', 'locationName' => 'requestId', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], ], ], 'CreatePartnerInputRequest' => [ 'type' => 'structure', 'members' => [ 'InputId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'inputId', ], 'RequestId' => [ 'shape' => '__string', 'locationName' => 'requestId', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], ], 'required' => [ 'InputId', ], ], 'CreatePartnerInputResponse' => [ 'type' => 'structure', 'members' => [ 'Input' => [ 'shape' => 'Input', 'locationName' => 'input', ], ], ], 'CreatePartnerInputResultModel' => [ 'type' => 'structure', 'members' => [ 'Input' => [ 'shape' => 'Input', 'locationName' => 'input', ], ], ], 'CreateTagsRequest' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'resource-arn', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], ], 'required' => [ 'ResourceArn', ], ], 'DeleteChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ChannelId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'channelId', ], ], 'required' => [ 'ChannelId', ], ], 'DeleteChannelResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'CdiInputSpecification' => [ 'shape' => 'CdiInputSpecification', 'locationName' => 'cdiInputSpecification', ], 'ChannelClass' => [ 'shape' => 'ChannelClass', 'locationName' => 'channelClass', ], 'Destinations' => [ 'shape' => '__listOfOutputDestination', 'locationName' => 'destinations', ], 'EgressEndpoints' => [ 'shape' => '__listOfChannelEgressEndpoint', 'locationName' => 'egressEndpoints', ], 'EncoderSettings' => [ 'shape' => 'EncoderSettings', 'locationName' => 'encoderSettings', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'InputAttachments' => [ 'shape' => '__listOfInputAttachment', 'locationName' => 'inputAttachments', ], 'InputSpecification' => [ 'shape' => 'InputSpecification', 'locationName' => 'inputSpecification', ], 'LogLevel' => [ 'shape' => 'LogLevel', 'locationName' => 'logLevel', ], 'Maintenance' => [ 'shape' => 'MaintenanceStatus', 'locationName' => 'maintenance', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'PipelineDetails' => [ 'shape' => '__listOfPipelineDetail', 'locationName' => 'pipelineDetails', ], 'PipelinesRunningCount' => [ 'shape' => '__integer', 'locationName' => 'pipelinesRunningCount', ], 'RoleArn' => [ 'shape' => '__string', 'locationName' => 'roleArn', ], 'State' => [ 'shape' => 'ChannelState', 'locationName' => 'state', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'Vpc' => [ 'shape' => 'VpcOutputSettingsDescription', 'locationName' => 'vpc', ], 'AnywhereSettings' => [ 'shape' => 'DescribeAnywhereSettings', 'locationName' => 'anywhereSettings', ], 'ChannelEngineVersion' => [ 'shape' => 'ChannelEngineVersionResponse', 'locationName' => 'channelEngineVersion', ], ], ], 'DeleteInputRequest' => [ 'type' => 'structure', 'members' => [ 'InputId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'inputId', ], ], 'required' => [ 'InputId', ], ], 'DeleteInputResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteInputSecurityGroupRequest' => [ 'type' => 'structure', 'members' => [ 'InputSecurityGroupId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'inputSecurityGroupId', ], ], 'required' => [ 'InputSecurityGroupId', ], ], 'DeleteInputSecurityGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteMultiplexProgramRequest' => [ 'type' => 'structure', 'members' => [ 'MultiplexId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'multiplexId', ], 'ProgramName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'programName', ], ], 'required' => [ 'MultiplexId', 'ProgramName', ], ], 'DeleteMultiplexProgramResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelId' => [ 'shape' => '__string', 'locationName' => 'channelId', ], 'MultiplexProgramSettings' => [ 'shape' => 'MultiplexProgramSettings', 'locationName' => 'multiplexProgramSettings', ], 'PacketIdentifiersMap' => [ 'shape' => 'MultiplexProgramPacketIdentifiersMap', 'locationName' => 'packetIdentifiersMap', ], 'PipelineDetails' => [ 'shape' => '__listOfMultiplexProgramPipelineDetail', 'locationName' => 'pipelineDetails', ], 'ProgramName' => [ 'shape' => '__string', 'locationName' => 'programName', ], ], ], 'DeleteMultiplexRequest' => [ 'type' => 'structure', 'members' => [ 'MultiplexId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'multiplexId', ], ], 'required' => [ 'MultiplexId', ], ], 'DeleteMultiplexResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'AvailabilityZones' => [ 'shape' => '__listOf__string', 'locationName' => 'availabilityZones', ], 'Destinations' => [ 'shape' => '__listOfMultiplexOutputDestination', 'locationName' => 'destinations', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'MultiplexSettings' => [ 'shape' => 'MultiplexSettings', 'locationName' => 'multiplexSettings', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'PipelinesRunningCount' => [ 'shape' => '__integer', 'locationName' => 'pipelinesRunningCount', ], 'ProgramCount' => [ 'shape' => '__integer', 'locationName' => 'programCount', ], 'State' => [ 'shape' => 'MultiplexState', 'locationName' => 'state', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], ], ], 'DeleteReservationRequest' => [ 'type' => 'structure', 'members' => [ 'ReservationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'reservationId', ], ], 'required' => [ 'ReservationId', ], ], 'DeleteReservationResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'Count' => [ 'shape' => '__integer', 'locationName' => 'count', ], 'CurrencyCode' => [ 'shape' => '__string', 'locationName' => 'currencyCode', ], 'Duration' => [ 'shape' => '__integer', 'locationName' => 'duration', ], 'DurationUnits' => [ 'shape' => 'OfferingDurationUnits', 'locationName' => 'durationUnits', ], 'End' => [ 'shape' => '__string', 'locationName' => 'end', ], 'FixedPrice' => [ 'shape' => '__double', 'locationName' => 'fixedPrice', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'OfferingDescription' => [ 'shape' => '__string', 'locationName' => 'offeringDescription', ], 'OfferingId' => [ 'shape' => '__string', 'locationName' => 'offeringId', ], 'OfferingType' => [ 'shape' => 'OfferingType', 'locationName' => 'offeringType', ], 'Region' => [ 'shape' => '__string', 'locationName' => 'region', ], 'RenewalSettings' => [ 'shape' => 'RenewalSettings', 'locationName' => 'renewalSettings', ], 'ReservationId' => [ 'shape' => '__string', 'locationName' => 'reservationId', ], 'ResourceSpecification' => [ 'shape' => 'ReservationResourceSpecification', 'locationName' => 'resourceSpecification', ], 'Start' => [ 'shape' => '__string', 'locationName' => 'start', ], 'State' => [ 'shape' => 'ReservationState', 'locationName' => 'state', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'UsagePrice' => [ 'shape' => '__double', 'locationName' => 'usagePrice', ], ], ], 'DeleteScheduleRequest' => [ 'type' => 'structure', 'members' => [ 'ChannelId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'channelId', ], ], 'required' => [ 'ChannelId', ], ], 'DeleteScheduleResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTagsRequest' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'resource-arn', ], 'TagKeys' => [ 'shape' => '__listOf__string', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], 'required' => [ 'TagKeys', 'ResourceArn', ], ], 'DescribeAccountConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeAccountConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'AccountConfiguration' => [ 'shape' => 'AccountConfiguration', 'locationName' => 'accountConfiguration', ], ], ], 'DescribeAccountConfigurationResultModel' => [ 'type' => 'structure', 'members' => [ 'AccountConfiguration' => [ 'shape' => 'AccountConfiguration', 'locationName' => 'accountConfiguration', ], ], ], 'DescribeChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ChannelId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'channelId', ], ], 'required' => [ 'ChannelId', ], ], 'DescribeChannelResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'CdiInputSpecification' => [ 'shape' => 'CdiInputSpecification', 'locationName' => 'cdiInputSpecification', ], 'ChannelClass' => [ 'shape' => 'ChannelClass', 'locationName' => 'channelClass', ], 'Destinations' => [ 'shape' => '__listOfOutputDestination', 'locationName' => 'destinations', ], 'EgressEndpoints' => [ 'shape' => '__listOfChannelEgressEndpoint', 'locationName' => 'egressEndpoints', ], 'EncoderSettings' => [ 'shape' => 'EncoderSettings', 'locationName' => 'encoderSettings', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'InputAttachments' => [ 'shape' => '__listOfInputAttachment', 'locationName' => 'inputAttachments', ], 'InputSpecification' => [ 'shape' => 'InputSpecification', 'locationName' => 'inputSpecification', ], 'LogLevel' => [ 'shape' => 'LogLevel', 'locationName' => 'logLevel', ], 'Maintenance' => [ 'shape' => 'MaintenanceStatus', 'locationName' => 'maintenance', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'PipelineDetails' => [ 'shape' => '__listOfPipelineDetail', 'locationName' => 'pipelineDetails', ], 'PipelinesRunningCount' => [ 'shape' => '__integer', 'locationName' => 'pipelinesRunningCount', ], 'RoleArn' => [ 'shape' => '__string', 'locationName' => 'roleArn', ], 'State' => [ 'shape' => 'ChannelState', 'locationName' => 'state', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'Vpc' => [ 'shape' => 'VpcOutputSettingsDescription', 'locationName' => 'vpc', ], 'AnywhereSettings' => [ 'shape' => 'DescribeAnywhereSettings', 'locationName' => 'anywhereSettings', ], 'ChannelEngineVersion' => [ 'shape' => 'ChannelEngineVersionResponse', 'locationName' => 'channelEngineVersion', ], ], ], 'DescribeInputDeviceRequest' => [ 'type' => 'structure', 'members' => [ 'InputDeviceId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'inputDeviceId', ], ], 'required' => [ 'InputDeviceId', ], ], 'DescribeInputDeviceResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'ConnectionState' => [ 'shape' => 'InputDeviceConnectionState', 'locationName' => 'connectionState', ], 'DeviceSettingsSyncState' => [ 'shape' => 'DeviceSettingsSyncState', 'locationName' => 'deviceSettingsSyncState', ], 'DeviceUpdateStatus' => [ 'shape' => 'DeviceUpdateStatus', 'locationName' => 'deviceUpdateStatus', ], 'HdDeviceSettings' => [ 'shape' => 'InputDeviceHdSettings', 'locationName' => 'hdDeviceSettings', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'MacAddress' => [ 'shape' => '__string', 'locationName' => 'macAddress', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NetworkSettings' => [ 'shape' => 'InputDeviceNetworkSettings', 'locationName' => 'networkSettings', ], 'SerialNumber' => [ 'shape' => '__string', 'locationName' => 'serialNumber', ], 'Type' => [ 'shape' => 'InputDeviceType', 'locationName' => 'type', ], 'UhdDeviceSettings' => [ 'shape' => 'InputDeviceUhdSettings', 'locationName' => 'uhdDeviceSettings', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'AvailabilityZone' => [ 'shape' => '__string', 'locationName' => 'availabilityZone', ], 'MedialiveInputArns' => [ 'shape' => '__listOf__string', 'locationName' => 'medialiveInputArns', ], 'OutputType' => [ 'shape' => 'InputDeviceOutputType', 'locationName' => 'outputType', ], ], ], 'DescribeInputDeviceThumbnailRequest' => [ 'type' => 'structure', 'members' => [ 'InputDeviceId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'inputDeviceId', ], 'Accept' => [ 'shape' => 'AcceptHeader', 'location' => 'header', 'locationName' => 'accept', ], ], 'required' => [ 'InputDeviceId', 'Accept', ], ], 'DescribeInputDeviceThumbnailResponse' => [ 'type' => 'structure', 'members' => [ 'Body' => [ 'shape' => 'InputDeviceThumbnail', 'locationName' => 'body', ], 'ContentType' => [ 'shape' => 'ContentType', 'location' => 'header', 'locationName' => 'Content-Type', ], 'ContentLength' => [ 'shape' => '__long', 'location' => 'header', 'locationName' => 'Content-Length', ], 'ETag' => [ 'shape' => '__string', 'location' => 'header', 'locationName' => 'ETag', ], 'LastModified' => [ 'shape' => '__timestamp', 'location' => 'header', 'locationName' => 'Last-Modified', ], ], 'payload' => 'Body', ], 'DescribeInputRequest' => [ 'type' => 'structure', 'members' => [ 'InputId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'inputId', ], ], 'required' => [ 'InputId', ], ], 'DescribeInputResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'AttachedChannels' => [ 'shape' => '__listOf__string', 'locationName' => 'attachedChannels', ], 'Destinations' => [ 'shape' => '__listOfInputDestination', 'locationName' => 'destinations', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'InputClass' => [ 'shape' => 'InputClass', 'locationName' => 'inputClass', ], 'InputDevices' => [ 'shape' => '__listOfInputDeviceSettings', 'locationName' => 'inputDevices', ], 'InputPartnerIds' => [ 'shape' => '__listOf__string', 'locationName' => 'inputPartnerIds', ], 'InputSourceType' => [ 'shape' => 'InputSourceType', 'locationName' => 'inputSourceType', ], 'MediaConnectFlows' => [ 'shape' => '__listOfMediaConnectFlow', 'locationName' => 'mediaConnectFlows', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'RoleArn' => [ 'shape' => '__string', 'locationName' => 'roleArn', ], 'SecurityGroups' => [ 'shape' => '__listOf__string', 'locationName' => 'securityGroups', ], 'Sources' => [ 'shape' => '__listOfInputSource', 'locationName' => 'sources', ], 'State' => [ 'shape' => 'InputState', 'locationName' => 'state', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'Type' => [ 'shape' => 'InputType', 'locationName' => 'type', ], 'SrtSettings' => [ 'shape' => 'SrtSettings', 'locationName' => 'srtSettings', ], 'InputNetworkLocation' => [ 'shape' => 'InputNetworkLocation', 'locationName' => 'inputNetworkLocation', ], 'MulticastSettings' => [ 'shape' => 'MulticastSettings', 'locationName' => 'multicastSettings', ], 'Smpte2110ReceiverGroupSettings' => [ 'shape' => 'Smpte2110ReceiverGroupSettings', 'locationName' => 'smpte2110ReceiverGroupSettings', ], 'SdiSources' => [ 'shape' => 'InputSdiSources', 'locationName' => 'sdiSources', ], ], ], 'DescribeInputSecurityGroupRequest' => [ 'type' => 'structure', 'members' => [ 'InputSecurityGroupId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'inputSecurityGroupId', ], ], 'required' => [ 'InputSecurityGroupId', ], ], 'DescribeInputSecurityGroupResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'Inputs' => [ 'shape' => '__listOf__string', 'locationName' => 'inputs', ], 'State' => [ 'shape' => 'InputSecurityGroupState', 'locationName' => 'state', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'WhitelistRules' => [ 'shape' => '__listOfInputWhitelistRule', 'locationName' => 'whitelistRules', ], ], ], 'DescribeMultiplexProgramRequest' => [ 'type' => 'structure', 'members' => [ 'MultiplexId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'multiplexId', ], 'ProgramName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'programName', ], ], 'required' => [ 'MultiplexId', 'ProgramName', ], ], 'DescribeMultiplexProgramResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelId' => [ 'shape' => '__string', 'locationName' => 'channelId', ], 'MultiplexProgramSettings' => [ 'shape' => 'MultiplexProgramSettings', 'locationName' => 'multiplexProgramSettings', ], 'PacketIdentifiersMap' => [ 'shape' => 'MultiplexProgramPacketIdentifiersMap', 'locationName' => 'packetIdentifiersMap', ], 'PipelineDetails' => [ 'shape' => '__listOfMultiplexProgramPipelineDetail', 'locationName' => 'pipelineDetails', ], 'ProgramName' => [ 'shape' => '__string', 'locationName' => 'programName', ], ], ], 'DescribeMultiplexRequest' => [ 'type' => 'structure', 'members' => [ 'MultiplexId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'multiplexId', ], ], 'required' => [ 'MultiplexId', ], ], 'DescribeMultiplexResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'AvailabilityZones' => [ 'shape' => '__listOf__string', 'locationName' => 'availabilityZones', ], 'Destinations' => [ 'shape' => '__listOfMultiplexOutputDestination', 'locationName' => 'destinations', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'MultiplexSettings' => [ 'shape' => 'MultiplexSettings', 'locationName' => 'multiplexSettings', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'PipelinesRunningCount' => [ 'shape' => '__integer', 'locationName' => 'pipelinesRunningCount', ], 'ProgramCount' => [ 'shape' => '__integer', 'locationName' => 'programCount', ], 'State' => [ 'shape' => 'MultiplexState', 'locationName' => 'state', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], ], ], 'DescribeOfferingRequest' => [ 'type' => 'structure', 'members' => [ 'OfferingId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'offeringId', ], ], 'required' => [ 'OfferingId', ], ], 'DescribeOfferingResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'CurrencyCode' => [ 'shape' => '__string', 'locationName' => 'currencyCode', ], 'Duration' => [ 'shape' => '__integer', 'locationName' => 'duration', ], 'DurationUnits' => [ 'shape' => 'OfferingDurationUnits', 'locationName' => 'durationUnits', ], 'FixedPrice' => [ 'shape' => '__double', 'locationName' => 'fixedPrice', ], 'OfferingDescription' => [ 'shape' => '__string', 'locationName' => 'offeringDescription', ], 'OfferingId' => [ 'shape' => '__string', 'locationName' => 'offeringId', ], 'OfferingType' => [ 'shape' => 'OfferingType', 'locationName' => 'offeringType', ], 'Region' => [ 'shape' => '__string', 'locationName' => 'region', ], 'ResourceSpecification' => [ 'shape' => 'ReservationResourceSpecification', 'locationName' => 'resourceSpecification', ], 'UsagePrice' => [ 'shape' => '__double', 'locationName' => 'usagePrice', ], ], ], 'DescribeReservationRequest' => [ 'type' => 'structure', 'members' => [ 'ReservationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'reservationId', ], ], 'required' => [ 'ReservationId', ], ], 'DescribeReservationResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'Count' => [ 'shape' => '__integer', 'locationName' => 'count', ], 'CurrencyCode' => [ 'shape' => '__string', 'locationName' => 'currencyCode', ], 'Duration' => [ 'shape' => '__integer', 'locationName' => 'duration', ], 'DurationUnits' => [ 'shape' => 'OfferingDurationUnits', 'locationName' => 'durationUnits', ], 'End' => [ 'shape' => '__string', 'locationName' => 'end', ], 'FixedPrice' => [ 'shape' => '__double', 'locationName' => 'fixedPrice', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'OfferingDescription' => [ 'shape' => '__string', 'locationName' => 'offeringDescription', ], 'OfferingId' => [ 'shape' => '__string', 'locationName' => 'offeringId', ], 'OfferingType' => [ 'shape' => 'OfferingType', 'locationName' => 'offeringType', ], 'Region' => [ 'shape' => '__string', 'locationName' => 'region', ], 'RenewalSettings' => [ 'shape' => 'RenewalSettings', 'locationName' => 'renewalSettings', ], 'ReservationId' => [ 'shape' => '__string', 'locationName' => 'reservationId', ], 'ResourceSpecification' => [ 'shape' => 'ReservationResourceSpecification', 'locationName' => 'resourceSpecification', ], 'Start' => [ 'shape' => '__string', 'locationName' => 'start', ], 'State' => [ 'shape' => 'ReservationState', 'locationName' => 'state', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'UsagePrice' => [ 'shape' => '__double', 'locationName' => 'usagePrice', ], ], ], 'DescribeScheduleRequest' => [ 'type' => 'structure', 'members' => [ 'ChannelId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'channelId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], 'required' => [ 'ChannelId', ], ], 'DescribeScheduleResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'ScheduleActions' => [ 'shape' => '__listOfScheduleAction', 'locationName' => 'scheduleActions', ], ], ], 'DescribeThumbnailsRequest' => [ 'type' => 'structure', 'members' => [ 'ChannelId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'channelId', ], 'PipelineId' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'pipelineId', ], 'ThumbnailType' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'thumbnailType', ], ], 'required' => [ 'ThumbnailType', 'PipelineId', 'ChannelId', ], ], 'DescribeThumbnailsResponse' => [ 'type' => 'structure', 'members' => [ 'ThumbnailDetails' => [ 'shape' => '__listOfThumbnailDetail', 'locationName' => 'thumbnailDetails', ], ], ], 'DescribeThumbnailsResultModel' => [ 'type' => 'structure', 'members' => [ 'ThumbnailDetails' => [ 'shape' => '__listOfThumbnailDetail', 'locationName' => 'thumbnailDetails', ], ], ], 'DeviceSettingsSyncState' => [ 'type' => 'string', 'enum' => [ 'SYNCED', 'SYNCING', ], ], 'DeviceUpdateStatus' => [ 'type' => 'string', 'enum' => [ 'UP_TO_DATE', 'NOT_UP_TO_DATE', 'UPDATING', ], ], 'DolbyEProgramSelection' => [ 'type' => 'string', 'enum' => [ 'ALL_CHANNELS', 'PROGRAM_1', 'PROGRAM_2', 'PROGRAM_3', 'PROGRAM_4', 'PROGRAM_5', 'PROGRAM_6', 'PROGRAM_7', 'PROGRAM_8', ], ], 'DolbyVision81Settings' => [ 'type' => 'structure', 'members' => [], ], 'DvbNitSettings' => [ 'type' => 'structure', 'members' => [ 'NetworkId' => [ 'shape' => '__integerMin0Max65536', 'locationName' => 'networkId', ], 'NetworkName' => [ 'shape' => '__stringMin1Max256', 'locationName' => 'networkName', ], 'RepInterval' => [ 'shape' => '__integerMin25Max10000', 'locationName' => 'repInterval', ], ], 'required' => [ 'NetworkName', 'NetworkId', ], ], 'DvbSdtOutputSdt' => [ 'type' => 'string', 'enum' => [ 'SDT_FOLLOW', 'SDT_FOLLOW_IF_PRESENT', 'SDT_MANUAL', 'SDT_NONE', ], ], 'DvbSdtSettings' => [ 'type' => 'structure', 'members' => [ 'OutputSdt' => [ 'shape' => 'DvbSdtOutputSdt', 'locationName' => 'outputSdt', ], 'RepInterval' => [ 'shape' => '__integerMin25Max2000', 'locationName' => 'repInterval', ], 'ServiceName' => [ 'shape' => '__stringMin1Max256', 'locationName' => 'serviceName', ], 'ServiceProviderName' => [ 'shape' => '__stringMin1Max256', 'locationName' => 'serviceProviderName', ], ], ], 'DvbSubDestinationAlignment' => [ 'type' => 'string', 'enum' => [ 'CENTERED', 'LEFT', 'SMART', ], ], 'DvbSubDestinationBackgroundColor' => [ 'type' => 'string', 'enum' => [ 'BLACK', 'NONE', 'WHITE', ], ], 'DvbSubDestinationFontColor' => [ 'type' => 'string', 'enum' => [ 'BLACK', 'BLUE', 'GREEN', 'RED', 'WHITE', 'YELLOW', ], ], 'DvbSubDestinationOutlineColor' => [ 'type' => 'string', 'enum' => [ 'BLACK', 'BLUE', 'GREEN', 'RED', 'WHITE', 'YELLOW', ], ], 'DvbSubDestinationSettings' => [ 'type' => 'structure', 'members' => [ 'Alignment' => [ 'shape' => 'DvbSubDestinationAlignment', 'locationName' => 'alignment', ], 'BackgroundColor' => [ 'shape' => 'DvbSubDestinationBackgroundColor', 'locationName' => 'backgroundColor', ], 'BackgroundOpacity' => [ 'shape' => '__integerMin0Max255', 'locationName' => 'backgroundOpacity', ], 'Font' => [ 'shape' => 'InputLocation', 'locationName' => 'font', ], 'FontColor' => [ 'shape' => 'DvbSubDestinationFontColor', 'locationName' => 'fontColor', ], 'FontOpacity' => [ 'shape' => '__integerMin0Max255', 'locationName' => 'fontOpacity', ], 'FontResolution' => [ 'shape' => '__integerMin96Max600', 'locationName' => 'fontResolution', ], 'FontSize' => [ 'shape' => '__string', 'locationName' => 'fontSize', ], 'OutlineColor' => [ 'shape' => 'DvbSubDestinationOutlineColor', 'locationName' => 'outlineColor', ], 'OutlineSize' => [ 'shape' => '__integerMin0Max10', 'locationName' => 'outlineSize', ], 'ShadowColor' => [ 'shape' => 'DvbSubDestinationShadowColor', 'locationName' => 'shadowColor', ], 'ShadowOpacity' => [ 'shape' => '__integerMin0Max255', 'locationName' => 'shadowOpacity', ], 'ShadowXOffset' => [ 'shape' => '__integer', 'locationName' => 'shadowXOffset', ], 'ShadowYOffset' => [ 'shape' => '__integer', 'locationName' => 'shadowYOffset', ], 'TeletextGridControl' => [ 'shape' => 'DvbSubDestinationTeletextGridControl', 'locationName' => 'teletextGridControl', ], 'XPosition' => [ 'shape' => '__integerMin0', 'locationName' => 'xPosition', ], 'YPosition' => [ 'shape' => '__integerMin0', 'locationName' => 'yPosition', ], ], ], 'DvbSubDestinationShadowColor' => [ 'type' => 'string', 'enum' => [ 'BLACK', 'NONE', 'WHITE', ], ], 'DvbSubDestinationTeletextGridControl' => [ 'type' => 'string', 'enum' => [ 'FIXED', 'SCALED', ], ], 'DvbSubOcrLanguage' => [ 'type' => 'string', 'enum' => [ 'DEU', 'ENG', 'FRA', 'NLD', 'POR', 'SPA', ], ], 'DvbSubSourceSettings' => [ 'type' => 'structure', 'members' => [ 'OcrLanguage' => [ 'shape' => 'DvbSubOcrLanguage', 'locationName' => 'ocrLanguage', ], 'Pid' => [ 'shape' => '__integerMin1', 'locationName' => 'pid', ], ], ], 'DvbTdtSettings' => [ 'type' => 'structure', 'members' => [ 'RepInterval' => [ 'shape' => '__integerMin1000Max30000', 'locationName' => 'repInterval', ], ], ], 'Eac3AtmosCodingMode' => [ 'type' => 'string', 'enum' => [ 'CODING_MODE_5_1_4', 'CODING_MODE_7_1_4', 'CODING_MODE_9_1_6', ], ], 'Eac3AtmosDrcLine' => [ 'type' => 'string', 'enum' => [ 'FILM_LIGHT', 'FILM_STANDARD', 'MUSIC_LIGHT', 'MUSIC_STANDARD', 'NONE', 'SPEECH', ], ], 'Eac3AtmosDrcRf' => [ 'type' => 'string', 'enum' => [ 'FILM_LIGHT', 'FILM_STANDARD', 'MUSIC_LIGHT', 'MUSIC_STANDARD', 'NONE', 'SPEECH', ], ], 'Eac3AtmosSettings' => [ 'type' => 'structure', 'members' => [ 'Bitrate' => [ 'shape' => '__double', 'locationName' => 'bitrate', ], 'CodingMode' => [ 'shape' => 'Eac3AtmosCodingMode', 'locationName' => 'codingMode', ], 'Dialnorm' => [ 'shape' => '__integerMin1Max31', 'locationName' => 'dialnorm', ], 'DrcLine' => [ 'shape' => 'Eac3AtmosDrcLine', 'locationName' => 'drcLine', ], 'DrcRf' => [ 'shape' => 'Eac3AtmosDrcRf', 'locationName' => 'drcRf', ], 'HeightTrim' => [ 'shape' => '__double', 'locationName' => 'heightTrim', ], 'SurroundTrim' => [ 'shape' => '__double', 'locationName' => 'surroundTrim', ], ], ], 'Eac3AttenuationControl' => [ 'type' => 'string', 'enum' => [ 'ATTENUATE_3_DB', 'NONE', ], ], 'Eac3BitstreamMode' => [ 'type' => 'string', 'enum' => [ 'COMMENTARY', 'COMPLETE_MAIN', 'EMERGENCY', 'HEARING_IMPAIRED', 'VISUALLY_IMPAIRED', ], ], 'Eac3CodingMode' => [ 'type' => 'string', 'enum' => [ 'CODING_MODE_1_0', 'CODING_MODE_2_0', 'CODING_MODE_3_2', ], ], 'Eac3DcFilter' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'Eac3DrcLine' => [ 'type' => 'string', 'enum' => [ 'FILM_LIGHT', 'FILM_STANDARD', 'MUSIC_LIGHT', 'MUSIC_STANDARD', 'NONE', 'SPEECH', ], ], 'Eac3DrcRf' => [ 'type' => 'string', 'enum' => [ 'FILM_LIGHT', 'FILM_STANDARD', 'MUSIC_LIGHT', 'MUSIC_STANDARD', 'NONE', 'SPEECH', ], ], 'Eac3LfeControl' => [ 'type' => 'string', 'enum' => [ 'LFE', 'NO_LFE', ], ], 'Eac3LfeFilter' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'Eac3MetadataControl' => [ 'type' => 'string', 'enum' => [ 'FOLLOW_INPUT', 'USE_CONFIGURED', ], ], 'Eac3PassthroughControl' => [ 'type' => 'string', 'enum' => [ 'NO_PASSTHROUGH', 'WHEN_POSSIBLE', ], ], 'Eac3PhaseControl' => [ 'type' => 'string', 'enum' => [ 'NO_SHIFT', 'SHIFT_90_DEGREES', ], ], 'Eac3Settings' => [ 'type' => 'structure', 'members' => [ 'AttenuationControl' => [ 'shape' => 'Eac3AttenuationControl', 'locationName' => 'attenuationControl', ], 'Bitrate' => [ 'shape' => '__double', 'locationName' => 'bitrate', ], 'BitstreamMode' => [ 'shape' => 'Eac3BitstreamMode', 'locationName' => 'bitstreamMode', ], 'CodingMode' => [ 'shape' => 'Eac3CodingMode', 'locationName' => 'codingMode', ], 'DcFilter' => [ 'shape' => 'Eac3DcFilter', 'locationName' => 'dcFilter', ], 'Dialnorm' => [ 'shape' => '__integerMin1Max31', 'locationName' => 'dialnorm', ], 'DrcLine' => [ 'shape' => 'Eac3DrcLine', 'locationName' => 'drcLine', ], 'DrcRf' => [ 'shape' => 'Eac3DrcRf', 'locationName' => 'drcRf', ], 'LfeControl' => [ 'shape' => 'Eac3LfeControl', 'locationName' => 'lfeControl', ], 'LfeFilter' => [ 'shape' => 'Eac3LfeFilter', 'locationName' => 'lfeFilter', ], 'LoRoCenterMixLevel' => [ 'shape' => '__double', 'locationName' => 'loRoCenterMixLevel', ], 'LoRoSurroundMixLevel' => [ 'shape' => '__double', 'locationName' => 'loRoSurroundMixLevel', ], 'LtRtCenterMixLevel' => [ 'shape' => '__double', 'locationName' => 'ltRtCenterMixLevel', ], 'LtRtSurroundMixLevel' => [ 'shape' => '__double', 'locationName' => 'ltRtSurroundMixLevel', ], 'MetadataControl' => [ 'shape' => 'Eac3MetadataControl', 'locationName' => 'metadataControl', ], 'PassthroughControl' => [ 'shape' => 'Eac3PassthroughControl', 'locationName' => 'passthroughControl', ], 'PhaseControl' => [ 'shape' => 'Eac3PhaseControl', 'locationName' => 'phaseControl', ], 'StereoDownmix' => [ 'shape' => 'Eac3StereoDownmix', 'locationName' => 'stereoDownmix', ], 'SurroundExMode' => [ 'shape' => 'Eac3SurroundExMode', 'locationName' => 'surroundExMode', ], 'SurroundMode' => [ 'shape' => 'Eac3SurroundMode', 'locationName' => 'surroundMode', ], ], ], 'Eac3StereoDownmix' => [ 'type' => 'string', 'enum' => [ 'DPL2', 'LO_RO', 'LT_RT', 'NOT_INDICATED', ], ], 'Eac3SurroundExMode' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', 'NOT_INDICATED', ], ], 'Eac3SurroundMode' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', 'NOT_INDICATED', ], ], 'EbuTtDDestinationSettings' => [ 'type' => 'structure', 'members' => [ 'CopyrightHolder' => [ 'shape' => '__stringMax1000', 'locationName' => 'copyrightHolder', ], 'FillLineGap' => [ 'shape' => 'EbuTtDFillLineGapControl', 'locationName' => 'fillLineGap', ], 'FontFamily' => [ 'shape' => '__string', 'locationName' => 'fontFamily', ], 'StyleControl' => [ 'shape' => 'EbuTtDDestinationStyleControl', 'locationName' => 'styleControl', ], 'DefaultFontSize' => [ 'shape' => '__integerMin1Max800', 'locationName' => 'defaultFontSize', ], 'DefaultLineHeight' => [ 'shape' => '__integerMin80Max800', 'locationName' => 'defaultLineHeight', ], ], ], 'EbuTtDDestinationStyleControl' => [ 'type' => 'string', 'enum' => [ 'EXCLUDE', 'INCLUDE', ], ], 'EbuTtDFillLineGapControl' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'EmbeddedConvert608To708' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'UPCONVERT', ], ], 'EmbeddedDestinationSettings' => [ 'type' => 'structure', 'members' => [], ], 'EmbeddedPlusScte20DestinationSettings' => [ 'type' => 'structure', 'members' => [], ], 'EmbeddedScte20Detection' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'OFF', ], ], 'EmbeddedSourceSettings' => [ 'type' => 'structure', 'members' => [ 'Convert608To708' => [ 'shape' => 'EmbeddedConvert608To708', 'locationName' => 'convert608To708', ], 'Scte20Detection' => [ 'shape' => 'EmbeddedScte20Detection', 'locationName' => 'scte20Detection', ], 'Source608ChannelNumber' => [ 'shape' => '__integerMin1Max4', 'locationName' => 'source608ChannelNumber', ], 'Source608TrackNumber' => [ 'shape' => '__integerMin1Max5', 'locationName' => 'source608TrackNumber', ], ], ], 'Empty' => [ 'type' => 'structure', 'members' => [], ], 'EncoderSettings' => [ 'type' => 'structure', 'members' => [ 'AudioDescriptions' => [ 'shape' => '__listOfAudioDescription', 'locationName' => 'audioDescriptions', ], 'AvailBlanking' => [ 'shape' => 'AvailBlanking', 'locationName' => 'availBlanking', ], 'AvailConfiguration' => [ 'shape' => 'AvailConfiguration', 'locationName' => 'availConfiguration', ], 'BlackoutSlate' => [ 'shape' => 'BlackoutSlate', 'locationName' => 'blackoutSlate', ], 'CaptionDescriptions' => [ 'shape' => '__listOfCaptionDescription', 'locationName' => 'captionDescriptions', ], 'FeatureActivations' => [ 'shape' => 'FeatureActivations', 'locationName' => 'featureActivations', ], 'GlobalConfiguration' => [ 'shape' => 'GlobalConfiguration', 'locationName' => 'globalConfiguration', ], 'MotionGraphicsConfiguration' => [ 'shape' => 'MotionGraphicsConfiguration', 'locationName' => 'motionGraphicsConfiguration', ], 'NielsenConfiguration' => [ 'shape' => 'NielsenConfiguration', 'locationName' => 'nielsenConfiguration', ], 'OutputGroups' => [ 'shape' => '__listOfOutputGroup', 'locationName' => 'outputGroups', ], 'TimecodeConfig' => [ 'shape' => 'TimecodeConfig', 'locationName' => 'timecodeConfig', ], 'VideoDescriptions' => [ 'shape' => '__listOfVideoDescription', 'locationName' => 'videoDescriptions', ], 'ThumbnailConfiguration' => [ 'shape' => 'ThumbnailConfiguration', 'locationName' => 'thumbnailConfiguration', ], 'ColorCorrectionSettings' => [ 'shape' => 'ColorCorrectionSettings', 'locationName' => 'colorCorrectionSettings', ], ], 'required' => [ 'VideoDescriptions', 'AudioDescriptions', 'OutputGroups', 'TimecodeConfig', ], ], 'EpochLockingSettings' => [ 'type' => 'structure', 'members' => [ 'CustomEpoch' => [ 'shape' => '__string', 'locationName' => 'customEpoch', ], 'JamSyncTime' => [ 'shape' => '__string', 'locationName' => 'jamSyncTime', ], ], ], 'Esam' => [ 'type' => 'structure', 'members' => [ 'AcquisitionPointId' => [ 'shape' => '__stringMax256', 'locationName' => 'acquisitionPointId', ], 'AdAvailOffset' => [ 'shape' => '__integerMinNegative1000Max1000', 'locationName' => 'adAvailOffset', ], 'PasswordParam' => [ 'shape' => '__string', 'locationName' => 'passwordParam', ], 'PoisEndpoint' => [ 'shape' => '__stringMax2048', 'locationName' => 'poisEndpoint', ], 'Username' => [ 'shape' => '__string', 'locationName' => 'username', ], 'ZoneIdentity' => [ 'shape' => '__stringMax256', 'locationName' => 'zoneIdentity', ], ], 'required' => [ 'AcquisitionPointId', 'PoisEndpoint', ], ], 'FailoverCondition' => [ 'type' => 'structure', 'members' => [ 'FailoverConditionSettings' => [ 'shape' => 'FailoverConditionSettings', 'locationName' => 'failoverConditionSettings', ], ], ], 'FailoverConditionSettings' => [ 'type' => 'structure', 'members' => [ 'AudioSilenceSettings' => [ 'shape' => 'AudioSilenceFailoverSettings', 'locationName' => 'audioSilenceSettings', ], 'InputLossSettings' => [ 'shape' => 'InputLossFailoverSettings', 'locationName' => 'inputLossSettings', ], 'VideoBlackSettings' => [ 'shape' => 'VideoBlackFailoverSettings', 'locationName' => 'videoBlackSettings', ], ], ], 'FeatureActivations' => [ 'type' => 'structure', 'members' => [ 'InputPrepareScheduleActions' => [ 'shape' => 'FeatureActivationsInputPrepareScheduleActions', 'locationName' => 'inputPrepareScheduleActions', ], 'OutputStaticImageOverlayScheduleActions' => [ 'shape' => 'FeatureActivationsOutputStaticImageOverlayScheduleActions', 'locationName' => 'outputStaticImageOverlayScheduleActions', ], ], ], 'FeatureActivationsInputPrepareScheduleActions' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'FeatureActivationsOutputStaticImageOverlayScheduleActions' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'FecOutputIncludeFec' => [ 'type' => 'string', 'enum' => [ 'COLUMN', 'COLUMN_AND_ROW', ], ], 'FecOutputSettings' => [ 'type' => 'structure', 'members' => [ 'ColumnDepth' => [ 'shape' => '__integerMin4Max20', 'locationName' => 'columnDepth', ], 'IncludeFec' => [ 'shape' => 'FecOutputIncludeFec', 'locationName' => 'includeFec', ], 'RowLength' => [ 'shape' => '__integerMin1Max20', 'locationName' => 'rowLength', ], ], ], 'FixedAfd' => [ 'type' => 'string', 'enum' => [ 'AFD_0000', 'AFD_0010', 'AFD_0011', 'AFD_0100', 'AFD_1000', 'AFD_1001', 'AFD_1010', 'AFD_1011', 'AFD_1101', 'AFD_1110', 'AFD_1111', ], ], 'FixedModeScheduleActionStartSettings' => [ 'type' => 'structure', 'members' => [ 'Time' => [ 'shape' => '__string', 'locationName' => 'time', ], ], 'required' => [ 'Time', ], ], 'Fmp4HlsSettings' => [ 'type' => 'structure', 'members' => [ 'AudioRenditionSets' => [ 'shape' => '__string', 'locationName' => 'audioRenditionSets', ], 'NielsenId3Behavior' => [ 'shape' => 'Fmp4NielsenId3Behavior', 'locationName' => 'nielsenId3Behavior', ], 'TimedMetadataBehavior' => [ 'shape' => 'Fmp4TimedMetadataBehavior', 'locationName' => 'timedMetadataBehavior', ], ], ], 'Fmp4NielsenId3Behavior' => [ 'type' => 'string', 'enum' => [ 'NO_PASSTHROUGH', 'PASSTHROUGH', ], ], 'Fmp4TimedMetadataBehavior' => [ 'type' => 'string', 'enum' => [ 'NO_PASSTHROUGH', 'PASSTHROUGH', ], ], 'FollowModeScheduleActionStartSettings' => [ 'type' => 'structure', 'members' => [ 'FollowPoint' => [ 'shape' => 'FollowPoint', 'locationName' => 'followPoint', ], 'ReferenceActionName' => [ 'shape' => '__string', 'locationName' => 'referenceActionName', ], ], 'required' => [ 'ReferenceActionName', 'FollowPoint', ], ], 'FollowPoint' => [ 'type' => 'string', 'enum' => [ 'END', 'START', ], ], 'ForbiddenException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 403, ], ], 'FrameCaptureCdnSettings' => [ 'type' => 'structure', 'members' => [ 'FrameCaptureS3Settings' => [ 'shape' => 'FrameCaptureS3Settings', 'locationName' => 'frameCaptureS3Settings', ], ], ], 'FrameCaptureGroupSettings' => [ 'type' => 'structure', 'members' => [ 'Destination' => [ 'shape' => 'OutputLocationRef', 'locationName' => 'destination', ], 'FrameCaptureCdnSettings' => [ 'shape' => 'FrameCaptureCdnSettings', 'locationName' => 'frameCaptureCdnSettings', ], ], 'required' => [ 'Destination', ], ], 'FrameCaptureHlsSettings' => [ 'type' => 'structure', 'members' => [], ], 'FrameCaptureIntervalUnit' => [ 'type' => 'string', 'enum' => [ 'MILLISECONDS', 'SECONDS', ], ], 'FrameCaptureOutputSettings' => [ 'type' => 'structure', 'members' => [ 'NameModifier' => [ 'shape' => '__string', 'locationName' => 'nameModifier', ], ], ], 'FrameCaptureS3LogUploads' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'FrameCaptureS3Settings' => [ 'type' => 'structure', 'members' => [ 'CannedAcl' => [ 'shape' => 'S3CannedAcl', 'locationName' => 'cannedAcl', ], ], ], 'FrameCaptureSettings' => [ 'type' => 'structure', 'members' => [ 'CaptureInterval' => [ 'shape' => '__integerMin1Max3600000', 'locationName' => 'captureInterval', ], 'CaptureIntervalUnits' => [ 'shape' => 'FrameCaptureIntervalUnit', 'locationName' => 'captureIntervalUnits', ], 'TimecodeBurninSettings' => [ 'shape' => 'TimecodeBurninSettings', 'locationName' => 'timecodeBurninSettings', ], ], ], 'GatewayTimeoutException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 504, ], ], 'GlobalConfiguration' => [ 'type' => 'structure', 'members' => [ 'InitialAudioGain' => [ 'shape' => '__integerMinNegative60Max60', 'locationName' => 'initialAudioGain', ], 'InputEndAction' => [ 'shape' => 'GlobalConfigurationInputEndAction', 'locationName' => 'inputEndAction', ], 'InputLossBehavior' => [ 'shape' => 'InputLossBehavior', 'locationName' => 'inputLossBehavior', ], 'OutputLockingMode' => [ 'shape' => 'GlobalConfigurationOutputLockingMode', 'locationName' => 'outputLockingMode', ], 'OutputTimingSource' => [ 'shape' => 'GlobalConfigurationOutputTimingSource', 'locationName' => 'outputTimingSource', ], 'SupportLowFramerateInputs' => [ 'shape' => 'GlobalConfigurationLowFramerateInputs', 'locationName' => 'supportLowFramerateInputs', ], 'OutputLockingSettings' => [ 'shape' => 'OutputLockingSettings', 'locationName' => 'outputLockingSettings', ], ], ], 'GlobalConfigurationInputEndAction' => [ 'type' => 'string', 'enum' => [ 'NONE', 'SWITCH_AND_LOOP_INPUTS', ], ], 'GlobalConfigurationLowFramerateInputs' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'GlobalConfigurationOutputLockingMode' => [ 'type' => 'string', 'enum' => [ 'EPOCH_LOCKING', 'PIPELINE_LOCKING', 'DISABLED', ], ], 'GlobalConfigurationOutputTimingSource' => [ 'type' => 'string', 'enum' => [ 'INPUT_CLOCK', 'SYSTEM_CLOCK', ], ], 'H264AdaptiveQuantization' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'HIGH', 'HIGHER', 'LOW', 'MAX', 'MEDIUM', 'OFF', ], ], 'H264ColorMetadata' => [ 'type' => 'string', 'enum' => [ 'IGNORE', 'INSERT', ], ], 'H264ColorSpaceSettings' => [ 'type' => 'structure', 'members' => [ 'ColorSpacePassthroughSettings' => [ 'shape' => 'ColorSpacePassthroughSettings', 'locationName' => 'colorSpacePassthroughSettings', ], 'Rec601Settings' => [ 'shape' => 'Rec601Settings', 'locationName' => 'rec601Settings', ], 'Rec709Settings' => [ 'shape' => 'Rec709Settings', 'locationName' => 'rec709Settings', ], ], ], 'H264EntropyEncoding' => [ 'type' => 'string', 'enum' => [ 'CABAC', 'CAVLC', ], ], 'H264FilterSettings' => [ 'type' => 'structure', 'members' => [ 'TemporalFilterSettings' => [ 'shape' => 'TemporalFilterSettings', 'locationName' => 'temporalFilterSettings', ], 'BandwidthReductionFilterSettings' => [ 'shape' => 'BandwidthReductionFilterSettings', 'locationName' => 'bandwidthReductionFilterSettings', ], ], ], 'H264FlickerAq' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H264ForceFieldPictures' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H264FramerateControl' => [ 'type' => 'string', 'enum' => [ 'INITIALIZE_FROM_SOURCE', 'SPECIFIED', ], ], 'H264GopBReference' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H264GopSizeUnits' => [ 'type' => 'string', 'enum' => [ 'FRAMES', 'SECONDS', ], ], 'H264Level' => [ 'type' => 'string', 'enum' => [ 'H264_LEVEL_1', 'H264_LEVEL_1_1', 'H264_LEVEL_1_2', 'H264_LEVEL_1_3', 'H264_LEVEL_2', 'H264_LEVEL_2_1', 'H264_LEVEL_2_2', 'H264_LEVEL_3', 'H264_LEVEL_3_1', 'H264_LEVEL_3_2', 'H264_LEVEL_4', 'H264_LEVEL_4_1', 'H264_LEVEL_4_2', 'H264_LEVEL_5', 'H264_LEVEL_5_1', 'H264_LEVEL_5_2', 'H264_LEVEL_AUTO', ], ], 'H264LookAheadRateControl' => [ 'type' => 'string', 'enum' => [ 'HIGH', 'LOW', 'MEDIUM', ], ], 'H264ParControl' => [ 'type' => 'string', 'enum' => [ 'INITIALIZE_FROM_SOURCE', 'SPECIFIED', ], ], 'H264Profile' => [ 'type' => 'string', 'enum' => [ 'BASELINE', 'HIGH', 'HIGH_10BIT', 'HIGH_422', 'HIGH_422_10BIT', 'MAIN', ], ], 'H264QualityLevel' => [ 'type' => 'string', 'enum' => [ 'ENHANCED_QUALITY', 'STANDARD_QUALITY', ], ], 'H264RateControlMode' => [ 'type' => 'string', 'enum' => [ 'CBR', 'MULTIPLEX', 'QVBR', 'VBR', ], ], 'H264ScanType' => [ 'type' => 'string', 'enum' => [ 'INTERLACED', 'PROGRESSIVE', ], ], 'H264SceneChangeDetect' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H264Settings' => [ 'type' => 'structure', 'members' => [ 'AdaptiveQuantization' => [ 'shape' => 'H264AdaptiveQuantization', 'locationName' => 'adaptiveQuantization', ], 'AfdSignaling' => [ 'shape' => 'AfdSignaling', 'locationName' => 'afdSignaling', ], 'Bitrate' => [ 'shape' => '__integerMin1000', 'locationName' => 'bitrate', ], 'BufFillPct' => [ 'shape' => '__integerMin0Max100', 'locationName' => 'bufFillPct', ], 'BufSize' => [ 'shape' => '__integerMin0', 'locationName' => 'bufSize', ], 'ColorMetadata' => [ 'shape' => 'H264ColorMetadata', 'locationName' => 'colorMetadata', ], 'ColorSpaceSettings' => [ 'shape' => 'H264ColorSpaceSettings', 'locationName' => 'colorSpaceSettings', ], 'EntropyEncoding' => [ 'shape' => 'H264EntropyEncoding', 'locationName' => 'entropyEncoding', ], 'FilterSettings' => [ 'shape' => 'H264FilterSettings', 'locationName' => 'filterSettings', ], 'FixedAfd' => [ 'shape' => 'FixedAfd', 'locationName' => 'fixedAfd', ], 'FlickerAq' => [ 'shape' => 'H264FlickerAq', 'locationName' => 'flickerAq', ], 'ForceFieldPictures' => [ 'shape' => 'H264ForceFieldPictures', 'locationName' => 'forceFieldPictures', ], 'FramerateControl' => [ 'shape' => 'H264FramerateControl', 'locationName' => 'framerateControl', ], 'FramerateDenominator' => [ 'shape' => '__integerMin1', 'locationName' => 'framerateDenominator', ], 'FramerateNumerator' => [ 'shape' => '__integerMin1', 'locationName' => 'framerateNumerator', ], 'GopBReference' => [ 'shape' => 'H264GopBReference', 'locationName' => 'gopBReference', ], 'GopClosedCadence' => [ 'shape' => '__integerMin0', 'locationName' => 'gopClosedCadence', ], 'GopNumBFrames' => [ 'shape' => '__integerMin0Max7', 'locationName' => 'gopNumBFrames', ], 'GopSize' => [ 'shape' => '__double', 'locationName' => 'gopSize', ], 'GopSizeUnits' => [ 'shape' => 'H264GopSizeUnits', 'locationName' => 'gopSizeUnits', ], 'Level' => [ 'shape' => 'H264Level', 'locationName' => 'level', ], 'LookAheadRateControl' => [ 'shape' => 'H264LookAheadRateControl', 'locationName' => 'lookAheadRateControl', ], 'MaxBitrate' => [ 'shape' => '__integerMin1000', 'locationName' => 'maxBitrate', ], 'MinIInterval' => [ 'shape' => '__integerMin0Max30', 'locationName' => 'minIInterval', ], 'NumRefFrames' => [ 'shape' => '__integerMin1Max6', 'locationName' => 'numRefFrames', ], 'ParControl' => [ 'shape' => 'H264ParControl', 'locationName' => 'parControl', ], 'ParDenominator' => [ 'shape' => '__integerMin1', 'locationName' => 'parDenominator', ], 'ParNumerator' => [ 'shape' => '__integerMin1', 'locationName' => 'parNumerator', ], 'Profile' => [ 'shape' => 'H264Profile', 'locationName' => 'profile', ], 'QualityLevel' => [ 'shape' => 'H264QualityLevel', 'locationName' => 'qualityLevel', ], 'QvbrQualityLevel' => [ 'shape' => '__integerMin1Max10', 'locationName' => 'qvbrQualityLevel', ], 'RateControlMode' => [ 'shape' => 'H264RateControlMode', 'locationName' => 'rateControlMode', ], 'ScanType' => [ 'shape' => 'H264ScanType', 'locationName' => 'scanType', ], 'SceneChangeDetect' => [ 'shape' => 'H264SceneChangeDetect', 'locationName' => 'sceneChangeDetect', ], 'Slices' => [ 'shape' => '__integerMin1Max32', 'locationName' => 'slices', ], 'Softness' => [ 'shape' => '__integerMin0Max128', 'locationName' => 'softness', ], 'SpatialAq' => [ 'shape' => 'H264SpatialAq', 'locationName' => 'spatialAq', ], 'SubgopLength' => [ 'shape' => 'H264SubGopLength', 'locationName' => 'subgopLength', ], 'Syntax' => [ 'shape' => 'H264Syntax', 'locationName' => 'syntax', ], 'TemporalAq' => [ 'shape' => 'H264TemporalAq', 'locationName' => 'temporalAq', ], 'TimecodeInsertion' => [ 'shape' => 'H264TimecodeInsertionBehavior', 'locationName' => 'timecodeInsertion', ], 'TimecodeBurninSettings' => [ 'shape' => 'TimecodeBurninSettings', 'locationName' => 'timecodeBurninSettings', ], 'MinQp' => [ 'shape' => '__integerMin1Max51', 'locationName' => 'minQp', ], ], ], 'H264SpatialAq' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H264SubGopLength' => [ 'type' => 'string', 'enum' => [ 'DYNAMIC', 'FIXED', ], ], 'H264Syntax' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'RP2027', ], ], 'H264TemporalAq' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H264TimecodeInsertionBehavior' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'PIC_TIMING_SEI', ], ], 'H265AdaptiveQuantization' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'HIGH', 'HIGHER', 'LOW', 'MAX', 'MEDIUM', 'OFF', ], ], 'H265AlternativeTransferFunction' => [ 'type' => 'string', 'enum' => [ 'INSERT', 'OMIT', ], ], 'H265ColorMetadata' => [ 'type' => 'string', 'enum' => [ 'IGNORE', 'INSERT', ], ], 'H265ColorSpaceSettings' => [ 'type' => 'structure', 'members' => [ 'ColorSpacePassthroughSettings' => [ 'shape' => 'ColorSpacePassthroughSettings', 'locationName' => 'colorSpacePassthroughSettings', ], 'DolbyVision81Settings' => [ 'shape' => 'DolbyVision81Settings', 'locationName' => 'dolbyVision81Settings', ], 'Hdr10Settings' => [ 'shape' => 'Hdr10Settings', 'locationName' => 'hdr10Settings', ], 'Rec601Settings' => [ 'shape' => 'Rec601Settings', 'locationName' => 'rec601Settings', ], 'Rec709Settings' => [ 'shape' => 'Rec709Settings', 'locationName' => 'rec709Settings', ], ], ], 'H265FilterSettings' => [ 'type' => 'structure', 'members' => [ 'TemporalFilterSettings' => [ 'shape' => 'TemporalFilterSettings', 'locationName' => 'temporalFilterSettings', ], 'BandwidthReductionFilterSettings' => [ 'shape' => 'BandwidthReductionFilterSettings', 'locationName' => 'bandwidthReductionFilterSettings', ], ], ], 'H265FlickerAq' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H265GopSizeUnits' => [ 'type' => 'string', 'enum' => [ 'FRAMES', 'SECONDS', ], ], 'H265Level' => [ 'type' => 'string', 'enum' => [ 'H265_LEVEL_1', 'H265_LEVEL_2', 'H265_LEVEL_2_1', 'H265_LEVEL_3', 'H265_LEVEL_3_1', 'H265_LEVEL_4', 'H265_LEVEL_4_1', 'H265_LEVEL_5', 'H265_LEVEL_5_1', 'H265_LEVEL_5_2', 'H265_LEVEL_6', 'H265_LEVEL_6_1', 'H265_LEVEL_6_2', 'H265_LEVEL_AUTO', ], ], 'H265LookAheadRateControl' => [ 'type' => 'string', 'enum' => [ 'HIGH', 'LOW', 'MEDIUM', ], ], 'H265Profile' => [ 'type' => 'string', 'enum' => [ 'MAIN', 'MAIN_10BIT', ], ], 'H265RateControlMode' => [ 'type' => 'string', 'enum' => [ 'CBR', 'MULTIPLEX', 'QVBR', ], ], 'H265ScanType' => [ 'type' => 'string', 'enum' => [ 'INTERLACED', 'PROGRESSIVE', ], ], 'H265SceneChangeDetect' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H265Settings' => [ 'type' => 'structure', 'members' => [ 'AdaptiveQuantization' => [ 'shape' => 'H265AdaptiveQuantization', 'locationName' => 'adaptiveQuantization', ], 'AfdSignaling' => [ 'shape' => 'AfdSignaling', 'locationName' => 'afdSignaling', ], 'AlternativeTransferFunction' => [ 'shape' => 'H265AlternativeTransferFunction', 'locationName' => 'alternativeTransferFunction', ], 'Bitrate' => [ 'shape' => '__integerMin100000Max40000000', 'locationName' => 'bitrate', ], 'BufSize' => [ 'shape' => '__integerMin100000Max80000000', 'locationName' => 'bufSize', ], 'ColorMetadata' => [ 'shape' => 'H265ColorMetadata', 'locationName' => 'colorMetadata', ], 'ColorSpaceSettings' => [ 'shape' => 'H265ColorSpaceSettings', 'locationName' => 'colorSpaceSettings', ], 'FilterSettings' => [ 'shape' => 'H265FilterSettings', 'locationName' => 'filterSettings', ], 'FixedAfd' => [ 'shape' => 'FixedAfd', 'locationName' => 'fixedAfd', ], 'FlickerAq' => [ 'shape' => 'H265FlickerAq', 'locationName' => 'flickerAq', ], 'FramerateDenominator' => [ 'shape' => '__integerMin1Max3003', 'locationName' => 'framerateDenominator', ], 'FramerateNumerator' => [ 'shape' => '__integerMin1', 'locationName' => 'framerateNumerator', ], 'GopClosedCadence' => [ 'shape' => '__integerMin0', 'locationName' => 'gopClosedCadence', ], 'GopSize' => [ 'shape' => '__double', 'locationName' => 'gopSize', ], 'GopSizeUnits' => [ 'shape' => 'H265GopSizeUnits', 'locationName' => 'gopSizeUnits', ], 'Level' => [ 'shape' => 'H265Level', 'locationName' => 'level', ], 'LookAheadRateControl' => [ 'shape' => 'H265LookAheadRateControl', 'locationName' => 'lookAheadRateControl', ], 'MaxBitrate' => [ 'shape' => '__integerMin100000Max40000000', 'locationName' => 'maxBitrate', ], 'MinIInterval' => [ 'shape' => '__integerMin0Max30', 'locationName' => 'minIInterval', ], 'ParDenominator' => [ 'shape' => '__integerMin1', 'locationName' => 'parDenominator', ], 'ParNumerator' => [ 'shape' => '__integerMin1', 'locationName' => 'parNumerator', ], 'Profile' => [ 'shape' => 'H265Profile', 'locationName' => 'profile', ], 'QvbrQualityLevel' => [ 'shape' => '__integerMin1Max10', 'locationName' => 'qvbrQualityLevel', ], 'RateControlMode' => [ 'shape' => 'H265RateControlMode', 'locationName' => 'rateControlMode', ], 'ScanType' => [ 'shape' => 'H265ScanType', 'locationName' => 'scanType', ], 'SceneChangeDetect' => [ 'shape' => 'H265SceneChangeDetect', 'locationName' => 'sceneChangeDetect', ], 'Slices' => [ 'shape' => '__integerMin1Max16', 'locationName' => 'slices', ], 'Tier' => [ 'shape' => 'H265Tier', 'locationName' => 'tier', ], 'TimecodeInsertion' => [ 'shape' => 'H265TimecodeInsertionBehavior', 'locationName' => 'timecodeInsertion', ], 'TimecodeBurninSettings' => [ 'shape' => 'TimecodeBurninSettings', 'locationName' => 'timecodeBurninSettings', ], 'MvOverPictureBoundaries' => [ 'shape' => 'H265MvOverPictureBoundaries', 'locationName' => 'mvOverPictureBoundaries', ], 'MvTemporalPredictor' => [ 'shape' => 'H265MvTemporalPredictor', 'locationName' => 'mvTemporalPredictor', ], 'TileHeight' => [ 'shape' => '__integerMin64Max2160', 'locationName' => 'tileHeight', ], 'TilePadding' => [ 'shape' => 'H265TilePadding', 'locationName' => 'tilePadding', ], 'TileWidth' => [ 'shape' => '__integerMin256Max3840', 'locationName' => 'tileWidth', ], 'TreeblockSize' => [ 'shape' => 'H265TreeblockSize', 'locationName' => 'treeblockSize', ], 'MinQp' => [ 'shape' => '__integerMin1Max51', 'locationName' => 'minQp', ], 'Deblocking' => [ 'shape' => 'H265Deblocking', 'locationName' => 'deblocking', ], ], 'required' => [ 'FramerateNumerator', 'FramerateDenominator', ], ], 'H265Tier' => [ 'type' => 'string', 'enum' => [ 'HIGH', 'MAIN', ], ], 'H265TimecodeInsertionBehavior' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'PIC_TIMING_SEI', ], ], 'Hdr10Settings' => [ 'type' => 'structure', 'members' => [ 'MaxCll' => [ 'shape' => '__integerMin0Max32768', 'locationName' => 'maxCll', ], 'MaxFall' => [ 'shape' => '__integerMin0Max32768', 'locationName' => 'maxFall', ], ], ], 'HlsAdMarkers' => [ 'type' => 'string', 'enum' => [ 'ADOBE', 'ELEMENTAL', 'ELEMENTAL_SCTE35', ], ], 'HlsAkamaiHttpTransferMode' => [ 'type' => 'string', 'enum' => [ 'CHUNKED', 'NON_CHUNKED', ], ], 'HlsAkamaiSettings' => [ 'type' => 'structure', 'members' => [ 'ConnectionRetryInterval' => [ 'shape' => '__integerMin0', 'locationName' => 'connectionRetryInterval', ], 'FilecacheDuration' => [ 'shape' => '__integerMin0Max600', 'locationName' => 'filecacheDuration', ], 'HttpTransferMode' => [ 'shape' => 'HlsAkamaiHttpTransferMode', 'locationName' => 'httpTransferMode', ], 'NumRetries' => [ 'shape' => '__integerMin0', 'locationName' => 'numRetries', ], 'RestartDelay' => [ 'shape' => '__integerMin0Max15', 'locationName' => 'restartDelay', ], 'Salt' => [ 'shape' => '__string', 'locationName' => 'salt', ], 'Token' => [ 'shape' => '__string', 'locationName' => 'token', ], ], ], 'HlsBasicPutSettings' => [ 'type' => 'structure', 'members' => [ 'ConnectionRetryInterval' => [ 'shape' => '__integerMin0', 'locationName' => 'connectionRetryInterval', ], 'FilecacheDuration' => [ 'shape' => '__integerMin0Max600', 'locationName' => 'filecacheDuration', ], 'NumRetries' => [ 'shape' => '__integerMin0', 'locationName' => 'numRetries', ], 'RestartDelay' => [ 'shape' => '__integerMin0Max15', 'locationName' => 'restartDelay', ], ], ], 'HlsCaptionLanguageSetting' => [ 'type' => 'string', 'enum' => [ 'INSERT', 'NONE', 'OMIT', ], ], 'HlsCdnSettings' => [ 'type' => 'structure', 'members' => [ 'HlsAkamaiSettings' => [ 'shape' => 'HlsAkamaiSettings', 'locationName' => 'hlsAkamaiSettings', ], 'HlsBasicPutSettings' => [ 'shape' => 'HlsBasicPutSettings', 'locationName' => 'hlsBasicPutSettings', ], 'HlsMediaStoreSettings' => [ 'shape' => 'HlsMediaStoreSettings', 'locationName' => 'hlsMediaStoreSettings', ], 'HlsS3Settings' => [ 'shape' => 'HlsS3Settings', 'locationName' => 'hlsS3Settings', ], 'HlsWebdavSettings' => [ 'shape' => 'HlsWebdavSettings', 'locationName' => 'hlsWebdavSettings', ], ], ], 'HlsClientCache' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'HlsCodecSpecification' => [ 'type' => 'string', 'enum' => [ 'RFC_4281', 'RFC_6381', ], ], 'HlsDirectoryStructure' => [ 'type' => 'string', 'enum' => [ 'SINGLE_DIRECTORY', 'SUBDIRECTORY_PER_STREAM', ], ], 'HlsDiscontinuityTags' => [ 'type' => 'string', 'enum' => [ 'INSERT', 'NEVER_INSERT', ], ], 'HlsEncryptionType' => [ 'type' => 'string', 'enum' => [ 'AES128', 'SAMPLE_AES', ], ], 'HlsGroupSettings' => [ 'type' => 'structure', 'members' => [ 'AdMarkers' => [ 'shape' => '__listOfHlsAdMarkers', 'locationName' => 'adMarkers', ], 'BaseUrlContent' => [ 'shape' => '__string', 'locationName' => 'baseUrlContent', ], 'BaseUrlContent1' => [ 'shape' => '__string', 'locationName' => 'baseUrlContent1', ], 'BaseUrlManifest' => [ 'shape' => '__string', 'locationName' => 'baseUrlManifest', ], 'BaseUrlManifest1' => [ 'shape' => '__string', 'locationName' => 'baseUrlManifest1', ], 'CaptionLanguageMappings' => [ 'shape' => '__listOfCaptionLanguageMapping', 'locationName' => 'captionLanguageMappings', ], 'CaptionLanguageSetting' => [ 'shape' => 'HlsCaptionLanguageSetting', 'locationName' => 'captionLanguageSetting', ], 'ClientCache' => [ 'shape' => 'HlsClientCache', 'locationName' => 'clientCache', ], 'CodecSpecification' => [ 'shape' => 'HlsCodecSpecification', 'locationName' => 'codecSpecification', ], 'ConstantIv' => [ 'shape' => '__stringMin32Max32', 'locationName' => 'constantIv', ], 'Destination' => [ 'shape' => 'OutputLocationRef', 'locationName' => 'destination', ], 'DirectoryStructure' => [ 'shape' => 'HlsDirectoryStructure', 'locationName' => 'directoryStructure', ], 'DiscontinuityTags' => [ 'shape' => 'HlsDiscontinuityTags', 'locationName' => 'discontinuityTags', ], 'EncryptionType' => [ 'shape' => 'HlsEncryptionType', 'locationName' => 'encryptionType', ], 'HlsCdnSettings' => [ 'shape' => 'HlsCdnSettings', 'locationName' => 'hlsCdnSettings', ], 'HlsId3SegmentTagging' => [ 'shape' => 'HlsId3SegmentTaggingState', 'locationName' => 'hlsId3SegmentTagging', ], 'IFrameOnlyPlaylists' => [ 'shape' => 'IFrameOnlyPlaylistType', 'locationName' => 'iFrameOnlyPlaylists', ], 'IncompleteSegmentBehavior' => [ 'shape' => 'HlsIncompleteSegmentBehavior', 'locationName' => 'incompleteSegmentBehavior', ], 'IndexNSegments' => [ 'shape' => '__integerMin3', 'locationName' => 'indexNSegments', ], 'InputLossAction' => [ 'shape' => 'InputLossActionForHlsOut', 'locationName' => 'inputLossAction', ], 'IvInManifest' => [ 'shape' => 'HlsIvInManifest', 'locationName' => 'ivInManifest', ], 'IvSource' => [ 'shape' => 'HlsIvSource', 'locationName' => 'ivSource', ], 'KeepSegments' => [ 'shape' => '__integerMin1', 'locationName' => 'keepSegments', ], 'KeyFormat' => [ 'shape' => '__string', 'locationName' => 'keyFormat', ], 'KeyFormatVersions' => [ 'shape' => '__string', 'locationName' => 'keyFormatVersions', ], 'KeyProviderSettings' => [ 'shape' => 'KeyProviderSettings', 'locationName' => 'keyProviderSettings', ], 'ManifestCompression' => [ 'shape' => 'HlsManifestCompression', 'locationName' => 'manifestCompression', ], 'ManifestDurationFormat' => [ 'shape' => 'HlsManifestDurationFormat', 'locationName' => 'manifestDurationFormat', ], 'MinSegmentLength' => [ 'shape' => '__integerMin0', 'locationName' => 'minSegmentLength', ], 'Mode' => [ 'shape' => 'HlsMode', 'locationName' => 'mode', ], 'OutputSelection' => [ 'shape' => 'HlsOutputSelection', 'locationName' => 'outputSelection', ], 'ProgramDateTime' => [ 'shape' => 'HlsProgramDateTime', 'locationName' => 'programDateTime', ], 'ProgramDateTimeClock' => [ 'shape' => 'HlsProgramDateTimeClock', 'locationName' => 'programDateTimeClock', ], 'ProgramDateTimePeriod' => [ 'shape' => '__integerMin0Max3600', 'locationName' => 'programDateTimePeriod', ], 'RedundantManifest' => [ 'shape' => 'HlsRedundantManifest', 'locationName' => 'redundantManifest', ], 'SegmentLength' => [ 'shape' => '__integerMin1', 'locationName' => 'segmentLength', ], 'SegmentationMode' => [ 'shape' => 'HlsSegmentationMode', 'locationName' => 'segmentationMode', ], 'SegmentsPerSubdirectory' => [ 'shape' => '__integerMin1', 'locationName' => 'segmentsPerSubdirectory', ], 'StreamInfResolution' => [ 'shape' => 'HlsStreamInfResolution', 'locationName' => 'streamInfResolution', ], 'TimedMetadataId3Frame' => [ 'shape' => 'HlsTimedMetadataId3Frame', 'locationName' => 'timedMetadataId3Frame', ], 'TimedMetadataId3Period' => [ 'shape' => '__integerMin0', 'locationName' => 'timedMetadataId3Period', ], 'TimestampDeltaMilliseconds' => [ 'shape' => '__integerMin0', 'locationName' => 'timestampDeltaMilliseconds', ], 'TsFileMode' => [ 'shape' => 'HlsTsFileMode', 'locationName' => 'tsFileMode', ], ], 'required' => [ 'Destination', ], ], 'HlsH265PackagingType' => [ 'type' => 'string', 'enum' => [ 'HEV1', 'HVC1', ], ], 'HlsId3SegmentTaggingScheduleActionSettings' => [ 'type' => 'structure', 'members' => [ 'Tag' => [ 'shape' => '__string', 'locationName' => 'tag', ], 'Id3' => [ 'shape' => '__string', 'locationName' => 'id3', ], ], ], 'HlsId3SegmentTaggingState' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'HlsIncompleteSegmentBehavior' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'SUPPRESS', ], ], 'HlsInputSettings' => [ 'type' => 'structure', 'members' => [ 'Bandwidth' => [ 'shape' => '__integerMin0', 'locationName' => 'bandwidth', ], 'BufferSegments' => [ 'shape' => '__integerMin0', 'locationName' => 'bufferSegments', ], 'Retries' => [ 'shape' => '__integerMin0', 'locationName' => 'retries', ], 'RetryInterval' => [ 'shape' => '__integerMin0', 'locationName' => 'retryInterval', ], 'Scte35Source' => [ 'shape' => 'HlsScte35SourceType', 'locationName' => 'scte35Source', ], ], ], 'HlsIvInManifest' => [ 'type' => 'string', 'enum' => [ 'EXCLUDE', 'INCLUDE', ], ], 'HlsIvSource' => [ 'type' => 'string', 'enum' => [ 'EXPLICIT', 'FOLLOWS_SEGMENT_NUMBER', ], ], 'HlsManifestCompression' => [ 'type' => 'string', 'enum' => [ 'GZIP', 'NONE', ], ], 'HlsManifestDurationFormat' => [ 'type' => 'string', 'enum' => [ 'FLOATING_POINT', 'INTEGER', ], ], 'HlsMediaStoreSettings' => [ 'type' => 'structure', 'members' => [ 'ConnectionRetryInterval' => [ 'shape' => '__integerMin0', 'locationName' => 'connectionRetryInterval', ], 'FilecacheDuration' => [ 'shape' => '__integerMin0Max600', 'locationName' => 'filecacheDuration', ], 'MediaStoreStorageClass' => [ 'shape' => 'HlsMediaStoreStorageClass', 'locationName' => 'mediaStoreStorageClass', ], 'NumRetries' => [ 'shape' => '__integerMin0', 'locationName' => 'numRetries', ], 'RestartDelay' => [ 'shape' => '__integerMin0Max15', 'locationName' => 'restartDelay', ], ], ], 'HlsMediaStoreStorageClass' => [ 'type' => 'string', 'enum' => [ 'TEMPORAL', ], ], 'HlsMode' => [ 'type' => 'string', 'enum' => [ 'LIVE', 'VOD', ], ], 'HlsOutputSelection' => [ 'type' => 'string', 'enum' => [ 'MANIFESTS_AND_SEGMENTS', 'SEGMENTS_ONLY', 'VARIANT_MANIFESTS_AND_SEGMENTS', ], ], 'HlsOutputSettings' => [ 'type' => 'structure', 'members' => [ 'H265PackagingType' => [ 'shape' => 'HlsH265PackagingType', 'locationName' => 'h265PackagingType', ], 'HlsSettings' => [ 'shape' => 'HlsSettings', 'locationName' => 'hlsSettings', ], 'NameModifier' => [ 'shape' => '__stringMin1', 'locationName' => 'nameModifier', ], 'SegmentModifier' => [ 'shape' => '__string', 'locationName' => 'segmentModifier', ], ], 'required' => [ 'HlsSettings', ], ], 'HlsProgramDateTime' => [ 'type' => 'string', 'enum' => [ 'EXCLUDE', 'INCLUDE', ], ], 'HlsProgramDateTimeClock' => [ 'type' => 'string', 'enum' => [ 'INITIALIZE_FROM_OUTPUT_TIMECODE', 'SYSTEM_CLOCK', ], ], 'HlsRedundantManifest' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'HlsS3LogUploads' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'HlsS3Settings' => [ 'type' => 'structure', 'members' => [ 'CannedAcl' => [ 'shape' => 'S3CannedAcl', 'locationName' => 'cannedAcl', ], ], ], 'HlsScte35SourceType' => [ 'type' => 'string', 'enum' => [ 'MANIFEST', 'SEGMENTS', ], ], 'HlsSegmentationMode' => [ 'type' => 'string', 'enum' => [ 'USE_INPUT_SEGMENTATION', 'USE_SEGMENT_DURATION', ], ], 'HlsSettings' => [ 'type' => 'structure', 'members' => [ 'AudioOnlyHlsSettings' => [ 'shape' => 'AudioOnlyHlsSettings', 'locationName' => 'audioOnlyHlsSettings', ], 'Fmp4HlsSettings' => [ 'shape' => 'Fmp4HlsSettings', 'locationName' => 'fmp4HlsSettings', ], 'FrameCaptureHlsSettings' => [ 'shape' => 'FrameCaptureHlsSettings', 'locationName' => 'frameCaptureHlsSettings', ], 'StandardHlsSettings' => [ 'shape' => 'StandardHlsSettings', 'locationName' => 'standardHlsSettings', ], ], ], 'HlsStreamInfResolution' => [ 'type' => 'string', 'enum' => [ 'EXCLUDE', 'INCLUDE', ], ], 'HlsTimedMetadataId3Frame' => [ 'type' => 'string', 'enum' => [ 'NONE', 'PRIV', 'TDRL', ], ], 'HlsTimedMetadataScheduleActionSettings' => [ 'type' => 'structure', 'members' => [ 'Id3' => [ 'shape' => '__string', 'locationName' => 'id3', ], ], 'required' => [ 'Id3', ], ], 'HlsTsFileMode' => [ 'type' => 'string', 'enum' => [ 'SEGMENTED_FILES', 'SINGLE_FILE', ], ], 'HlsWebdavHttpTransferMode' => [ 'type' => 'string', 'enum' => [ 'CHUNKED', 'NON_CHUNKED', ], ], 'HlsWebdavSettings' => [ 'type' => 'structure', 'members' => [ 'ConnectionRetryInterval' => [ 'shape' => '__integerMin0', 'locationName' => 'connectionRetryInterval', ], 'FilecacheDuration' => [ 'shape' => '__integerMin0Max600', 'locationName' => 'filecacheDuration', ], 'HttpTransferMode' => [ 'shape' => 'HlsWebdavHttpTransferMode', 'locationName' => 'httpTransferMode', ], 'NumRetries' => [ 'shape' => '__integerMin0', 'locationName' => 'numRetries', ], 'RestartDelay' => [ 'shape' => '__integerMin0Max15', 'locationName' => 'restartDelay', ], ], ], 'HtmlMotionGraphicsSettings' => [ 'type' => 'structure', 'members' => [], ], 'IFrameOnlyPlaylistType' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'STANDARD', ], ], 'ImmediateModeScheduleActionStartSettings' => [ 'type' => 'structure', 'members' => [], ], 'IncludeFillerNalUnits' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'DROP', 'INCLUDE', ], ], 'Input' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'AttachedChannels' => [ 'shape' => '__listOf__string', 'locationName' => 'attachedChannels', ], 'Destinations' => [ 'shape' => '__listOfInputDestination', 'locationName' => 'destinations', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'InputClass' => [ 'shape' => 'InputClass', 'locationName' => 'inputClass', ], 'InputDevices' => [ 'shape' => '__listOfInputDeviceSettings', 'locationName' => 'inputDevices', ], 'InputPartnerIds' => [ 'shape' => '__listOf__string', 'locationName' => 'inputPartnerIds', ], 'InputSourceType' => [ 'shape' => 'InputSourceType', 'locationName' => 'inputSourceType', ], 'MediaConnectFlows' => [ 'shape' => '__listOfMediaConnectFlow', 'locationName' => 'mediaConnectFlows', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'RoleArn' => [ 'shape' => '__string', 'locationName' => 'roleArn', ], 'SecurityGroups' => [ 'shape' => '__listOf__string', 'locationName' => 'securityGroups', ], 'Sources' => [ 'shape' => '__listOfInputSource', 'locationName' => 'sources', ], 'State' => [ 'shape' => 'InputState', 'locationName' => 'state', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'Type' => [ 'shape' => 'InputType', 'locationName' => 'type', ], 'SrtSettings' => [ 'shape' => 'SrtSettings', 'locationName' => 'srtSettings', ], 'InputNetworkLocation' => [ 'shape' => 'InputNetworkLocation', 'locationName' => 'inputNetworkLocation', ], 'MulticastSettings' => [ 'shape' => 'MulticastSettings', 'locationName' => 'multicastSettings', ], 'Smpte2110ReceiverGroupSettings' => [ 'shape' => 'Smpte2110ReceiverGroupSettings', 'locationName' => 'smpte2110ReceiverGroupSettings', ], 'SdiSources' => [ 'shape' => 'InputSdiSources', 'locationName' => 'sdiSources', ], ], ], 'InputAttachment' => [ 'type' => 'structure', 'members' => [ 'AutomaticInputFailoverSettings' => [ 'shape' => 'AutomaticInputFailoverSettings', 'locationName' => 'automaticInputFailoverSettings', ], 'InputAttachmentName' => [ 'shape' => '__string', 'locationName' => 'inputAttachmentName', ], 'InputId' => [ 'shape' => '__string', 'locationName' => 'inputId', ], 'InputSettings' => [ 'shape' => 'InputSettings', 'locationName' => 'inputSettings', ], 'LogicalInterfaceNames' => [ 'shape' => '__listOf__string', 'locationName' => 'logicalInterfaceNames', ], ], ], 'InputChannelLevel' => [ 'type' => 'structure', 'members' => [ 'Gain' => [ 'shape' => '__integerMinNegative60Max6', 'locationName' => 'gain', ], 'InputChannel' => [ 'shape' => '__integerMin0Max15', 'locationName' => 'inputChannel', ], ], 'required' => [ 'InputChannel', 'Gain', ], ], 'InputClass' => [ 'type' => 'string', 'enum' => [ 'STANDARD', 'SINGLE_PIPELINE', ], ], 'InputClippingSettings' => [ 'type' => 'structure', 'members' => [ 'InputTimecodeSource' => [ 'shape' => 'InputTimecodeSource', 'locationName' => 'inputTimecodeSource', ], 'StartTimecode' => [ 'shape' => 'StartTimecode', 'locationName' => 'startTimecode', ], 'StopTimecode' => [ 'shape' => 'StopTimecode', 'locationName' => 'stopTimecode', ], ], 'required' => [ 'InputTimecodeSource', ], ], 'InputCodec' => [ 'type' => 'string', 'enum' => [ 'MPEG2', 'AVC', 'HEVC', ], ], 'InputDeblockFilter' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'InputDenoiseFilter' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'InputDestination' => [ 'type' => 'structure', 'members' => [ 'Ip' => [ 'shape' => '__string', 'locationName' => 'ip', ], 'Port' => [ 'shape' => '__string', 'locationName' => 'port', ], 'Url' => [ 'shape' => '__string', 'locationName' => 'url', ], 'Vpc' => [ 'shape' => 'InputDestinationVpc', 'locationName' => 'vpc', ], 'Network' => [ 'shape' => '__string', 'locationName' => 'network', ], 'NetworkRoutes' => [ 'shape' => '__listOfInputDestinationRoute', 'locationName' => 'networkRoutes', ], ], ], 'InputDestinationRequest' => [ 'type' => 'structure', 'members' => [ 'StreamName' => [ 'shape' => '__string', 'locationName' => 'streamName', ], 'Network' => [ 'shape' => '__string', 'locationName' => 'network', ], 'NetworkRoutes' => [ 'shape' => '__listOfInputRequestDestinationRoute', 'locationName' => 'networkRoutes', ], 'StaticIpAddress' => [ 'shape' => '__string', 'locationName' => 'staticIpAddress', ], ], ], 'InputDestinationVpc' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZone' => [ 'shape' => '__string', 'locationName' => 'availabilityZone', ], 'NetworkInterfaceId' => [ 'shape' => '__string', 'locationName' => 'networkInterfaceId', ], ], ], 'InputDevice' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'ConnectionState' => [ 'shape' => 'InputDeviceConnectionState', 'locationName' => 'connectionState', ], 'DeviceSettingsSyncState' => [ 'shape' => 'DeviceSettingsSyncState', 'locationName' => 'deviceSettingsSyncState', ], 'DeviceUpdateStatus' => [ 'shape' => 'DeviceUpdateStatus', 'locationName' => 'deviceUpdateStatus', ], 'HdDeviceSettings' => [ 'shape' => 'InputDeviceHdSettings', 'locationName' => 'hdDeviceSettings', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'MacAddress' => [ 'shape' => '__string', 'locationName' => 'macAddress', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NetworkSettings' => [ 'shape' => 'InputDeviceNetworkSettings', 'locationName' => 'networkSettings', ], 'SerialNumber' => [ 'shape' => '__string', 'locationName' => 'serialNumber', ], 'Type' => [ 'shape' => 'InputDeviceType', 'locationName' => 'type', ], 'UhdDeviceSettings' => [ 'shape' => 'InputDeviceUhdSettings', 'locationName' => 'uhdDeviceSettings', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'AvailabilityZone' => [ 'shape' => '__string', 'locationName' => 'availabilityZone', ], 'MedialiveInputArns' => [ 'shape' => '__listOf__string', 'locationName' => 'medialiveInputArns', ], 'OutputType' => [ 'shape' => 'InputDeviceOutputType', 'locationName' => 'outputType', ], ], ], 'InputDeviceActiveInput' => [ 'type' => 'string', 'enum' => [ 'HDMI', 'SDI', ], ], 'InputDeviceCodec' => [ 'type' => 'string', 'enum' => [ 'HEVC', 'AVC', ], ], 'InputDeviceConfigurableSettings' => [ 'type' => 'structure', 'members' => [ 'ConfiguredInput' => [ 'shape' => 'InputDeviceConfiguredInput', 'locationName' => 'configuredInput', ], 'MaxBitrate' => [ 'shape' => '__integer', 'locationName' => 'maxBitrate', ], 'LatencyMs' => [ 'shape' => '__integer', 'locationName' => 'latencyMs', ], 'Codec' => [ 'shape' => 'InputDeviceCodec', 'locationName' => 'codec', ], 'MediaconnectSettings' => [ 'shape' => 'InputDeviceMediaConnectConfigurableSettings', 'locationName' => 'mediaconnectSettings', ], 'AudioChannelPairs' => [ 'shape' => '__listOfInputDeviceConfigurableAudioChannelPairConfig', 'locationName' => 'audioChannelPairs', ], 'InputResolution' => [ 'shape' => '__string', 'locationName' => 'inputResolution', ], ], ], 'InputDeviceConfigurationValidationError' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], 'ValidationErrors' => [ 'shape' => '__listOfValidationError', 'locationName' => 'validationErrors', ], ], ], 'InputDeviceConfiguredInput' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'HDMI', 'SDI', ], ], 'InputDeviceConnectionState' => [ 'type' => 'string', 'enum' => [ 'DISCONNECTED', 'CONNECTED', ], ], 'InputDeviceHdSettings' => [ 'type' => 'structure', 'members' => [ 'ActiveInput' => [ 'shape' => 'InputDeviceActiveInput', 'locationName' => 'activeInput', ], 'ConfiguredInput' => [ 'shape' => 'InputDeviceConfiguredInput', 'locationName' => 'configuredInput', ], 'DeviceState' => [ 'shape' => 'InputDeviceState', 'locationName' => 'deviceState', ], 'Framerate' => [ 'shape' => '__double', 'locationName' => 'framerate', ], 'Height' => [ 'shape' => '__integer', 'locationName' => 'height', ], 'MaxBitrate' => [ 'shape' => '__integer', 'locationName' => 'maxBitrate', ], 'ScanType' => [ 'shape' => 'InputDeviceScanType', 'locationName' => 'scanType', ], 'Width' => [ 'shape' => '__integer', 'locationName' => 'width', ], 'LatencyMs' => [ 'shape' => '__integer', 'locationName' => 'latencyMs', ], ], ], 'InputDeviceIpScheme' => [ 'type' => 'string', 'enum' => [ 'STATIC', 'DHCP', ], ], 'InputDeviceMediaConnectConfigurableSettings' => [ 'type' => 'structure', 'members' => [ 'FlowArn' => [ 'shape' => '__string', 'locationName' => 'flowArn', ], 'RoleArn' => [ 'shape' => '__string', 'locationName' => 'roleArn', ], 'SecretArn' => [ 'shape' => '__string', 'locationName' => 'secretArn', ], 'SourceName' => [ 'shape' => '__string', 'locationName' => 'sourceName', ], ], ], 'InputDeviceMediaConnectSettings' => [ 'type' => 'structure', 'members' => [ 'FlowArn' => [ 'shape' => '__string', 'locationName' => 'flowArn', ], 'RoleArn' => [ 'shape' => '__string', 'locationName' => 'roleArn', ], 'SecretArn' => [ 'shape' => '__string', 'locationName' => 'secretArn', ], 'SourceName' => [ 'shape' => '__string', 'locationName' => 'sourceName', ], ], ], 'InputDeviceNetworkSettings' => [ 'type' => 'structure', 'members' => [ 'DnsAddresses' => [ 'shape' => '__listOf__string', 'locationName' => 'dnsAddresses', ], 'Gateway' => [ 'shape' => '__string', 'locationName' => 'gateway', ], 'IpAddress' => [ 'shape' => '__string', 'locationName' => 'ipAddress', ], 'IpScheme' => [ 'shape' => 'InputDeviceIpScheme', 'locationName' => 'ipScheme', ], 'SubnetMask' => [ 'shape' => '__string', 'locationName' => 'subnetMask', ], ], ], 'InputDeviceOutputType' => [ 'type' => 'string', 'enum' => [ 'NONE', 'MEDIALIVE_INPUT', 'MEDIACONNECT_FLOW', ], ], 'InputDeviceRequest' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], ], ], 'InputDeviceScanType' => [ 'type' => 'string', 'enum' => [ 'INTERLACED', 'PROGRESSIVE', ], ], 'InputDeviceSettings' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], ], ], 'InputDeviceState' => [ 'type' => 'string', 'enum' => [ 'IDLE', 'STREAMING', ], ], 'InputDeviceSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'ConnectionState' => [ 'shape' => 'InputDeviceConnectionState', 'locationName' => 'connectionState', ], 'DeviceSettingsSyncState' => [ 'shape' => 'DeviceSettingsSyncState', 'locationName' => 'deviceSettingsSyncState', ], 'DeviceUpdateStatus' => [ 'shape' => 'DeviceUpdateStatus', 'locationName' => 'deviceUpdateStatus', ], 'HdDeviceSettings' => [ 'shape' => 'InputDeviceHdSettings', 'locationName' => 'hdDeviceSettings', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'MacAddress' => [ 'shape' => '__string', 'locationName' => 'macAddress', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NetworkSettings' => [ 'shape' => 'InputDeviceNetworkSettings', 'locationName' => 'networkSettings', ], 'SerialNumber' => [ 'shape' => '__string', 'locationName' => 'serialNumber', ], 'Type' => [ 'shape' => 'InputDeviceType', 'locationName' => 'type', ], 'UhdDeviceSettings' => [ 'shape' => 'InputDeviceUhdSettings', 'locationName' => 'uhdDeviceSettings', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'AvailabilityZone' => [ 'shape' => '__string', 'locationName' => 'availabilityZone', ], 'MedialiveInputArns' => [ 'shape' => '__listOf__string', 'locationName' => 'medialiveInputArns', ], 'OutputType' => [ 'shape' => 'InputDeviceOutputType', 'locationName' => 'outputType', ], ], ], 'InputDeviceTransferType' => [ 'type' => 'string', 'enum' => [ 'OUTGOING', 'INCOMING', ], ], 'InputDeviceType' => [ 'type' => 'string', 'enum' => [ 'HD', 'UHD', ], ], 'InputDeviceUhdSettings' => [ 'type' => 'structure', 'members' => [ 'ActiveInput' => [ 'shape' => 'InputDeviceActiveInput', 'locationName' => 'activeInput', ], 'ConfiguredInput' => [ 'shape' => 'InputDeviceConfiguredInput', 'locationName' => 'configuredInput', ], 'DeviceState' => [ 'shape' => 'InputDeviceState', 'locationName' => 'deviceState', ], 'Framerate' => [ 'shape' => '__double', 'locationName' => 'framerate', ], 'Height' => [ 'shape' => '__integer', 'locationName' => 'height', ], 'MaxBitrate' => [ 'shape' => '__integer', 'locationName' => 'maxBitrate', ], 'ScanType' => [ 'shape' => 'InputDeviceScanType', 'locationName' => 'scanType', ], 'Width' => [ 'shape' => '__integer', 'locationName' => 'width', ], 'LatencyMs' => [ 'shape' => '__integer', 'locationName' => 'latencyMs', ], 'Codec' => [ 'shape' => 'InputDeviceCodec', 'locationName' => 'codec', ], 'MediaconnectSettings' => [ 'shape' => 'InputDeviceMediaConnectSettings', 'locationName' => 'mediaconnectSettings', ], 'AudioChannelPairs' => [ 'shape' => '__listOfInputDeviceUhdAudioChannelPairConfig', 'locationName' => 'audioChannelPairs', ], 'InputResolution' => [ 'shape' => '__string', 'locationName' => 'inputResolution', ], ], ], 'InputFilter' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'DISABLED', 'FORCED', ], ], 'InputLocation' => [ 'type' => 'structure', 'members' => [ 'PasswordParam' => [ 'shape' => '__string', 'locationName' => 'passwordParam', ], 'Uri' => [ 'shape' => '__stringMax2048', 'locationName' => 'uri', ], 'Username' => [ 'shape' => '__string', 'locationName' => 'username', ], ], 'required' => [ 'Uri', ], ], 'InputLossActionForHlsOut' => [ 'type' => 'string', 'enum' => [ 'EMIT_OUTPUT', 'PAUSE_OUTPUT', ], ], 'InputLossActionForMsSmoothOut' => [ 'type' => 'string', 'enum' => [ 'EMIT_OUTPUT', 'PAUSE_OUTPUT', ], ], 'InputLossActionForRtmpOut' => [ 'type' => 'string', 'enum' => [ 'EMIT_OUTPUT', 'PAUSE_OUTPUT', ], ], 'InputLossActionForUdpOut' => [ 'type' => 'string', 'enum' => [ 'DROP_PROGRAM', 'DROP_TS', 'EMIT_PROGRAM', ], ], 'InputLossBehavior' => [ 'type' => 'structure', 'members' => [ 'BlackFrameMsec' => [ 'shape' => '__integerMin0Max1000000', 'locationName' => 'blackFrameMsec', ], 'InputLossImageColor' => [ 'shape' => '__stringMin6Max6', 'locationName' => 'inputLossImageColor', ], 'InputLossImageSlate' => [ 'shape' => 'InputLocation', 'locationName' => 'inputLossImageSlate', ], 'InputLossImageType' => [ 'shape' => 'InputLossImageType', 'locationName' => 'inputLossImageType', ], 'RepeatFrameMsec' => [ 'shape' => '__integerMin0Max1000000', 'locationName' => 'repeatFrameMsec', ], ], ], 'InputLossFailoverSettings' => [ 'type' => 'structure', 'members' => [ 'InputLossThresholdMsec' => [ 'shape' => '__integerMin100', 'locationName' => 'inputLossThresholdMsec', ], ], ], 'InputLossImageType' => [ 'type' => 'string', 'enum' => [ 'COLOR', 'SLATE', ], ], 'InputMaximumBitrate' => [ 'type' => 'string', 'enum' => [ 'MAX_10_MBPS', 'MAX_20_MBPS', 'MAX_50_MBPS', ], ], 'InputPreference' => [ 'type' => 'string', 'enum' => [ 'EQUAL_INPUT_PREFERENCE', 'PRIMARY_INPUT_PREFERRED', ], ], 'InputPrepareScheduleActionSettings' => [ 'type' => 'structure', 'members' => [ 'InputAttachmentNameReference' => [ 'shape' => '__string', 'locationName' => 'inputAttachmentNameReference', ], 'InputClippingSettings' => [ 'shape' => 'InputClippingSettings', 'locationName' => 'inputClippingSettings', ], 'UrlPath' => [ 'shape' => '__listOf__string', 'locationName' => 'urlPath', ], ], ], 'InputResolution' => [ 'type' => 'string', 'enum' => [ 'SD', 'HD', 'UHD', ], ], 'InputSecurityGroup' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'Inputs' => [ 'shape' => '__listOf__string', 'locationName' => 'inputs', ], 'State' => [ 'shape' => 'InputSecurityGroupState', 'locationName' => 'state', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'WhitelistRules' => [ 'shape' => '__listOfInputWhitelistRule', 'locationName' => 'whitelistRules', ], ], ], 'InputSecurityGroupState' => [ 'type' => 'string', 'enum' => [ 'IDLE', 'IN_USE', 'UPDATING', 'DELETED', ], ], 'InputSecurityGroupWhitelistRequest' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'WhitelistRules' => [ 'shape' => '__listOfInputWhitelistRuleCidr', 'locationName' => 'whitelistRules', ], ], ], 'InputSettings' => [ 'type' => 'structure', 'members' => [ 'AudioSelectors' => [ 'shape' => '__listOfAudioSelector', 'locationName' => 'audioSelectors', ], 'CaptionSelectors' => [ 'shape' => '__listOfCaptionSelector', 'locationName' => 'captionSelectors', ], 'DeblockFilter' => [ 'shape' => 'InputDeblockFilter', 'locationName' => 'deblockFilter', ], 'DenoiseFilter' => [ 'shape' => 'InputDenoiseFilter', 'locationName' => 'denoiseFilter', ], 'FilterStrength' => [ 'shape' => '__integerMin1Max5', 'locationName' => 'filterStrength', ], 'InputFilter' => [ 'shape' => 'InputFilter', 'locationName' => 'inputFilter', ], 'NetworkInputSettings' => [ 'shape' => 'NetworkInputSettings', 'locationName' => 'networkInputSettings', ], 'Scte35Pid' => [ 'shape' => '__integerMin32Max8191', 'locationName' => 'scte35Pid', ], 'Smpte2038DataPreference' => [ 'shape' => 'Smpte2038DataPreference', 'locationName' => 'smpte2038DataPreference', ], 'SourceEndBehavior' => [ 'shape' => 'InputSourceEndBehavior', 'locationName' => 'sourceEndBehavior', ], 'VideoSelector' => [ 'shape' => 'VideoSelector', 'locationName' => 'videoSelector', ], ], ], 'InputSource' => [ 'type' => 'structure', 'members' => [ 'PasswordParam' => [ 'shape' => '__string', 'locationName' => 'passwordParam', ], 'Url' => [ 'shape' => '__string', 'locationName' => 'url', ], 'Username' => [ 'shape' => '__string', 'locationName' => 'username', ], ], ], 'InputSourceEndBehavior' => [ 'type' => 'string', 'enum' => [ 'CONTINUE', 'LOOP', ], ], 'InputSourceRequest' => [ 'type' => 'structure', 'members' => [ 'PasswordParam' => [ 'shape' => '__string', 'locationName' => 'passwordParam', ], 'Url' => [ 'shape' => '__string', 'locationName' => 'url', ], 'Username' => [ 'shape' => '__string', 'locationName' => 'username', ], ], ], 'InputSourceType' => [ 'type' => 'string', 'enum' => [ 'STATIC', 'DYNAMIC', ], ], 'InputSpecification' => [ 'type' => 'structure', 'members' => [ 'Codec' => [ 'shape' => 'InputCodec', 'locationName' => 'codec', ], 'MaximumBitrate' => [ 'shape' => 'InputMaximumBitrate', 'locationName' => 'maximumBitrate', ], 'Resolution' => [ 'shape' => 'InputResolution', 'locationName' => 'resolution', ], ], ], 'InputState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'DETACHED', 'ATTACHED', 'DELETING', 'DELETED', ], ], 'InputSwitchScheduleActionSettings' => [ 'type' => 'structure', 'members' => [ 'InputAttachmentNameReference' => [ 'shape' => '__string', 'locationName' => 'inputAttachmentNameReference', ], 'InputClippingSettings' => [ 'shape' => 'InputClippingSettings', 'locationName' => 'inputClippingSettings', ], 'UrlPath' => [ 'shape' => '__listOf__string', 'locationName' => 'urlPath', ], ], 'required' => [ 'InputAttachmentNameReference', ], ], 'InputTimecodeSource' => [ 'type' => 'string', 'enum' => [ 'ZEROBASED', 'EMBEDDED', ], ], 'InputType' => [ 'type' => 'string', 'enum' => [ 'UDP_PUSH', 'RTP_PUSH', 'RTMP_PUSH', 'RTMP_PULL', 'URL_PULL', 'MP4_FILE', 'MEDIACONNECT', 'INPUT_DEVICE', 'AWS_CDI', 'TS_FILE', 'SRT_CALLER', 'MULTICAST', 'SMPTE_2110_RECEIVER_GROUP', 'SDI', ], ], 'InputVpcRequest' => [ 'type' => 'structure', 'members' => [ 'SecurityGroupIds' => [ 'shape' => '__listOf__string', 'locationName' => 'securityGroupIds', ], 'SubnetIds' => [ 'shape' => '__listOf__string', 'locationName' => 'subnetIds', ], ], 'required' => [ 'SubnetIds', ], ], 'InputWhitelistRule' => [ 'type' => 'structure', 'members' => [ 'Cidr' => [ 'shape' => '__string', 'locationName' => 'cidr', ], ], ], 'InputWhitelistRuleCidr' => [ 'type' => 'structure', 'members' => [ 'Cidr' => [ 'shape' => '__string', 'locationName' => 'cidr', ], ], ], 'InternalServerErrorException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 500, ], ], 'InternalServiceError' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], ], 'InvalidRequest' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], ], 'KeyProviderSettings' => [ 'type' => 'structure', 'members' => [ 'StaticKeySettings' => [ 'shape' => 'StaticKeySettings', 'locationName' => 'staticKeySettings', ], ], ], 'LastFrameClippingBehavior' => [ 'type' => 'string', 'enum' => [ 'EXCLUDE_LAST_FRAME', 'INCLUDE_LAST_FRAME', ], ], 'LimitExceeded' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], ], 'ListChannelsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListChannelsResponse' => [ 'type' => 'structure', 'members' => [ 'Channels' => [ 'shape' => '__listOfChannelSummary', 'locationName' => 'channels', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListChannelsResultModel' => [ 'type' => 'structure', 'members' => [ 'Channels' => [ 'shape' => '__listOfChannelSummary', 'locationName' => 'channels', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListInputDeviceTransfersRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'TransferType' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'transferType', ], ], 'required' => [ 'TransferType', ], ], 'ListInputDeviceTransfersResponse' => [ 'type' => 'structure', 'members' => [ 'InputDeviceTransfers' => [ 'shape' => '__listOfTransferringInputDeviceSummary', 'locationName' => 'inputDeviceTransfers', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListInputDeviceTransfersResultModel' => [ 'type' => 'structure', 'members' => [ 'InputDeviceTransfers' => [ 'shape' => '__listOfTransferringInputDeviceSummary', 'locationName' => 'inputDeviceTransfers', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListInputDevicesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListInputDevicesResponse' => [ 'type' => 'structure', 'members' => [ 'InputDevices' => [ 'shape' => '__listOfInputDeviceSummary', 'locationName' => 'inputDevices', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListInputDevicesResultModel' => [ 'type' => 'structure', 'members' => [ 'InputDevices' => [ 'shape' => '__listOfInputDeviceSummary', 'locationName' => 'inputDevices', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListInputSecurityGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListInputSecurityGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'InputSecurityGroups' => [ 'shape' => '__listOfInputSecurityGroup', 'locationName' => 'inputSecurityGroups', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListInputSecurityGroupsResultModel' => [ 'type' => 'structure', 'members' => [ 'InputSecurityGroups' => [ 'shape' => '__listOfInputSecurityGroup', 'locationName' => 'inputSecurityGroups', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListInputsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListInputsResponse' => [ 'type' => 'structure', 'members' => [ 'Inputs' => [ 'shape' => '__listOfInput', 'locationName' => 'inputs', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListInputsResultModel' => [ 'type' => 'structure', 'members' => [ 'Inputs' => [ 'shape' => '__listOfInput', 'locationName' => 'inputs', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListMultiplexProgramsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'MultiplexId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'multiplexId', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], 'required' => [ 'MultiplexId', ], ], 'ListMultiplexProgramsResponse' => [ 'type' => 'structure', 'members' => [ 'MultiplexPrograms' => [ 'shape' => '__listOfMultiplexProgramSummary', 'locationName' => 'multiplexPrograms', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListMultiplexProgramsResultModel' => [ 'type' => 'structure', 'members' => [ 'MultiplexPrograms' => [ 'shape' => '__listOfMultiplexProgramSummary', 'locationName' => 'multiplexPrograms', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListMultiplexesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListMultiplexesResponse' => [ 'type' => 'structure', 'members' => [ 'Multiplexes' => [ 'shape' => '__listOfMultiplexSummary', 'locationName' => 'multiplexes', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListMultiplexesResultModel' => [ 'type' => 'structure', 'members' => [ 'Multiplexes' => [ 'shape' => '__listOfMultiplexSummary', 'locationName' => 'multiplexes', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListOfferingsRequest' => [ 'type' => 'structure', 'members' => [ 'ChannelClass' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'channelClass', ], 'ChannelConfiguration' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'channelConfiguration', ], 'Codec' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'codec', ], 'Duration' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'duration', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'MaximumBitrate' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'maximumBitrate', ], 'MaximumFramerate' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'maximumFramerate', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'Resolution' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'resolution', ], 'ResourceType' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'SpecialFeature' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'specialFeature', ], 'VideoQuality' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'videoQuality', ], ], ], 'ListOfferingsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'Offerings' => [ 'shape' => '__listOfOffering', 'locationName' => 'offerings', ], ], ], 'ListOfferingsResultModel' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'Offerings' => [ 'shape' => '__listOfOffering', 'locationName' => 'offerings', ], ], ], 'ListReservationsRequest' => [ 'type' => 'structure', 'members' => [ 'ChannelClass' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'channelClass', ], 'Codec' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'codec', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'MaximumBitrate' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'maximumBitrate', ], 'MaximumFramerate' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'maximumFramerate', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'Resolution' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'resolution', ], 'ResourceType' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'SpecialFeature' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'specialFeature', ], 'VideoQuality' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'videoQuality', ], ], ], 'ListReservationsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'Reservations' => [ 'shape' => '__listOfReservation', 'locationName' => 'reservations', ], ], ], 'ListReservationsResultModel' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'Reservations' => [ 'shape' => '__listOfReservation', 'locationName' => 'reservations', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'resource-arn', ], ], 'required' => [ 'ResourceArn', ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], ], ], 'LogLevel' => [ 'type' => 'string', 'enum' => [ 'ERROR', 'WARNING', 'INFO', 'DEBUG', 'DISABLED', ], ], 'M2tsAbsentInputAudioBehavior' => [ 'type' => 'string', 'enum' => [ 'DROP', 'ENCODE_SILENCE', ], ], 'M2tsArib' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'M2tsAribCaptionsPidControl' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'USE_CONFIGURED', ], ], 'M2tsAudioBufferModel' => [ 'type' => 'string', 'enum' => [ 'ATSC', 'DVB', ], ], 'M2tsAudioInterval' => [ 'type' => 'string', 'enum' => [ 'VIDEO_AND_FIXED_INTERVALS', 'VIDEO_INTERVAL', ], ], 'M2tsAudioStreamType' => [ 'type' => 'string', 'enum' => [ 'ATSC', 'DVB', ], ], 'M2tsBufferModel' => [ 'type' => 'string', 'enum' => [ 'MULTIPLEX', 'NONE', ], ], 'M2tsCcDescriptor' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'M2tsEbifControl' => [ 'type' => 'string', 'enum' => [ 'NONE', 'PASSTHROUGH', ], ], 'M2tsEbpPlacement' => [ 'type' => 'string', 'enum' => [ 'VIDEO_AND_AUDIO_PIDS', 'VIDEO_PID', ], ], 'M2tsEsRateInPes' => [ 'type' => 'string', 'enum' => [ 'EXCLUDE', 'INCLUDE', ], ], 'M2tsKlv' => [ 'type' => 'string', 'enum' => [ 'NONE', 'PASSTHROUGH', ], ], 'M2tsNielsenId3Behavior' => [ 'type' => 'string', 'enum' => [ 'NO_PASSTHROUGH', 'PASSTHROUGH', ], ], 'M2tsPcrControl' => [ 'type' => 'string', 'enum' => [ 'CONFIGURED_PCR_PERIOD', 'PCR_EVERY_PES_PACKET', ], ], 'M2tsRateMode' => [ 'type' => 'string', 'enum' => [ 'CBR', 'VBR', ], ], 'M2tsScte35Control' => [ 'type' => 'string', 'enum' => [ 'NONE', 'PASSTHROUGH', ], ], 'M2tsSegmentationMarkers' => [ 'type' => 'string', 'enum' => [ 'EBP', 'EBP_LEGACY', 'NONE', 'PSI_SEGSTART', 'RAI_ADAPT', 'RAI_SEGSTART', ], ], 'M2tsSegmentationStyle' => [ 'type' => 'string', 'enum' => [ 'MAINTAIN_CADENCE', 'RESET_CADENCE', ], ], 'M2tsSettings' => [ 'type' => 'structure', 'members' => [ 'AbsentInputAudioBehavior' => [ 'shape' => 'M2tsAbsentInputAudioBehavior', 'locationName' => 'absentInputAudioBehavior', ], 'Arib' => [ 'shape' => 'M2tsArib', 'locationName' => 'arib', ], 'AribCaptionsPid' => [ 'shape' => '__string', 'locationName' => 'aribCaptionsPid', ], 'AribCaptionsPidControl' => [ 'shape' => 'M2tsAribCaptionsPidControl', 'locationName' => 'aribCaptionsPidControl', ], 'AudioBufferModel' => [ 'shape' => 'M2tsAudioBufferModel', 'locationName' => 'audioBufferModel', ], 'AudioFramesPerPes' => [ 'shape' => '__integerMin0', 'locationName' => 'audioFramesPerPes', ], 'AudioPids' => [ 'shape' => '__string', 'locationName' => 'audioPids', ], 'AudioStreamType' => [ 'shape' => 'M2tsAudioStreamType', 'locationName' => 'audioStreamType', ], 'Bitrate' => [ 'shape' => '__integerMin0', 'locationName' => 'bitrate', ], 'BufferModel' => [ 'shape' => 'M2tsBufferModel', 'locationName' => 'bufferModel', ], 'CcDescriptor' => [ 'shape' => 'M2tsCcDescriptor', 'locationName' => 'ccDescriptor', ], 'DvbNitSettings' => [ 'shape' => 'DvbNitSettings', 'locationName' => 'dvbNitSettings', ], 'DvbSdtSettings' => [ 'shape' => 'DvbSdtSettings', 'locationName' => 'dvbSdtSettings', ], 'DvbSubPids' => [ 'shape' => '__string', 'locationName' => 'dvbSubPids', ], 'DvbTdtSettings' => [ 'shape' => 'DvbTdtSettings', 'locationName' => 'dvbTdtSettings', ], 'DvbTeletextPid' => [ 'shape' => '__string', 'locationName' => 'dvbTeletextPid', ], 'Ebif' => [ 'shape' => 'M2tsEbifControl', 'locationName' => 'ebif', ], 'EbpAudioInterval' => [ 'shape' => 'M2tsAudioInterval', 'locationName' => 'ebpAudioInterval', ], 'EbpLookaheadMs' => [ 'shape' => '__integerMin0Max10000', 'locationName' => 'ebpLookaheadMs', ], 'EbpPlacement' => [ 'shape' => 'M2tsEbpPlacement', 'locationName' => 'ebpPlacement', ], 'EcmPid' => [ 'shape' => '__string', 'locationName' => 'ecmPid', ], 'EsRateInPes' => [ 'shape' => 'M2tsEsRateInPes', 'locationName' => 'esRateInPes', ], 'EtvPlatformPid' => [ 'shape' => '__string', 'locationName' => 'etvPlatformPid', ], 'EtvSignalPid' => [ 'shape' => '__string', 'locationName' => 'etvSignalPid', ], 'FragmentTime' => [ 'shape' => '__doubleMin0', 'locationName' => 'fragmentTime', ], 'Klv' => [ 'shape' => 'M2tsKlv', 'locationName' => 'klv', ], 'KlvDataPids' => [ 'shape' => '__string', 'locationName' => 'klvDataPids', ], 'NielsenId3Behavior' => [ 'shape' => 'M2tsNielsenId3Behavior', 'locationName' => 'nielsenId3Behavior', ], 'NullPacketBitrate' => [ 'shape' => '__doubleMin0', 'locationName' => 'nullPacketBitrate', ], 'PatInterval' => [ 'shape' => '__integerMin0Max1000', 'locationName' => 'patInterval', ], 'PcrControl' => [ 'shape' => 'M2tsPcrControl', 'locationName' => 'pcrControl', ], 'PcrPeriod' => [ 'shape' => '__integerMin0Max500', 'locationName' => 'pcrPeriod', ], 'PcrPid' => [ 'shape' => '__string', 'locationName' => 'pcrPid', ], 'PmtInterval' => [ 'shape' => '__integerMin0Max1000', 'locationName' => 'pmtInterval', ], 'PmtPid' => [ 'shape' => '__string', 'locationName' => 'pmtPid', ], 'ProgramNum' => [ 'shape' => '__integerMin0Max65535', 'locationName' => 'programNum', ], 'RateMode' => [ 'shape' => 'M2tsRateMode', 'locationName' => 'rateMode', ], 'Scte27Pids' => [ 'shape' => '__string', 'locationName' => 'scte27Pids', ], 'Scte35Control' => [ 'shape' => 'M2tsScte35Control', 'locationName' => 'scte35Control', ], 'Scte35Pid' => [ 'shape' => '__string', 'locationName' => 'scte35Pid', ], 'Scte35PrerollPullupMilliseconds' => [ 'shape' => '__doubleMin0Max5000', 'locationName' => 'scte35PrerollPullupMilliseconds', ], 'SegmentationMarkers' => [ 'shape' => 'M2tsSegmentationMarkers', 'locationName' => 'segmentationMarkers', ], 'SegmentationStyle' => [ 'shape' => 'M2tsSegmentationStyle', 'locationName' => 'segmentationStyle', ], 'SegmentationTime' => [ 'shape' => '__doubleMin1', 'locationName' => 'segmentationTime', ], 'TimedMetadataBehavior' => [ 'shape' => 'M2tsTimedMetadataBehavior', 'locationName' => 'timedMetadataBehavior', ], 'TimedMetadataPid' => [ 'shape' => '__string', 'locationName' => 'timedMetadataPid', ], 'TransportStreamId' => [ 'shape' => '__integerMin0Max65535', 'locationName' => 'transportStreamId', ], 'VideoPid' => [ 'shape' => '__string', 'locationName' => 'videoPid', ], ], ], 'M2tsTimedMetadataBehavior' => [ 'type' => 'string', 'enum' => [ 'NO_PASSTHROUGH', 'PASSTHROUGH', ], ], 'M3u8KlvBehavior' => [ 'type' => 'string', 'enum' => [ 'NO_PASSTHROUGH', 'PASSTHROUGH', ], ], 'M3u8NielsenId3Behavior' => [ 'type' => 'string', 'enum' => [ 'NO_PASSTHROUGH', 'PASSTHROUGH', ], ], 'M3u8PcrControl' => [ 'type' => 'string', 'enum' => [ 'CONFIGURED_PCR_PERIOD', 'PCR_EVERY_PES_PACKET', ], ], 'M3u8Scte35Behavior' => [ 'type' => 'string', 'enum' => [ 'NO_PASSTHROUGH', 'PASSTHROUGH', ], ], 'M3u8Settings' => [ 'type' => 'structure', 'members' => [ 'AudioFramesPerPes' => [ 'shape' => '__integerMin0', 'locationName' => 'audioFramesPerPes', ], 'AudioPids' => [ 'shape' => '__string', 'locationName' => 'audioPids', ], 'EcmPid' => [ 'shape' => '__string', 'locationName' => 'ecmPid', ], 'NielsenId3Behavior' => [ 'shape' => 'M3u8NielsenId3Behavior', 'locationName' => 'nielsenId3Behavior', ], 'PatInterval' => [ 'shape' => '__integerMin0Max1000', 'locationName' => 'patInterval', ], 'PcrControl' => [ 'shape' => 'M3u8PcrControl', 'locationName' => 'pcrControl', ], 'PcrPeriod' => [ 'shape' => '__integerMin0Max500', 'locationName' => 'pcrPeriod', ], 'PcrPid' => [ 'shape' => '__string', 'locationName' => 'pcrPid', ], 'PmtInterval' => [ 'shape' => '__integerMin0Max1000', 'locationName' => 'pmtInterval', ], 'PmtPid' => [ 'shape' => '__string', 'locationName' => 'pmtPid', ], 'ProgramNum' => [ 'shape' => '__integerMin0Max65535', 'locationName' => 'programNum', ], 'Scte35Behavior' => [ 'shape' => 'M3u8Scte35Behavior', 'locationName' => 'scte35Behavior', ], 'Scte35Pid' => [ 'shape' => '__string', 'locationName' => 'scte35Pid', ], 'TimedMetadataBehavior' => [ 'shape' => 'M3u8TimedMetadataBehavior', 'locationName' => 'timedMetadataBehavior', ], 'TimedMetadataPid' => [ 'shape' => '__string', 'locationName' => 'timedMetadataPid', ], 'TransportStreamId' => [ 'shape' => '__integerMin0Max65535', 'locationName' => 'transportStreamId', ], 'VideoPid' => [ 'shape' => '__string', 'locationName' => 'videoPid', ], 'KlvBehavior' => [ 'shape' => 'M3u8KlvBehavior', 'locationName' => 'klvBehavior', ], 'KlvDataPids' => [ 'shape' => '__string', 'locationName' => 'klvDataPids', ], ], ], 'M3u8TimedMetadataBehavior' => [ 'type' => 'string', 'enum' => [ 'NO_PASSTHROUGH', 'PASSTHROUGH', ], ], 'MaintenanceCreateSettings' => [ 'type' => 'structure', 'members' => [ 'MaintenanceDay' => [ 'shape' => 'MaintenanceDay', 'locationName' => 'maintenanceDay', ], 'MaintenanceStartTime' => [ 'shape' => '__stringPattern010920300', 'locationName' => 'maintenanceStartTime', ], ], ], 'MaintenanceDay' => [ 'type' => 'string', 'enum' => [ 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY', ], ], 'MaintenanceStatus' => [ 'type' => 'structure', 'members' => [ 'MaintenanceDay' => [ 'shape' => 'MaintenanceDay', 'locationName' => 'maintenanceDay', ], 'MaintenanceDeadline' => [ 'shape' => '__string', 'locationName' => 'maintenanceDeadline', ], 'MaintenanceScheduledDate' => [ 'shape' => '__string', 'locationName' => 'maintenanceScheduledDate', ], 'MaintenanceStartTime' => [ 'shape' => '__string', 'locationName' => 'maintenanceStartTime', ], ], ], 'MaintenanceUpdateSettings' => [ 'type' => 'structure', 'members' => [ 'MaintenanceDay' => [ 'shape' => 'MaintenanceDay', 'locationName' => 'maintenanceDay', ], 'MaintenanceScheduledDate' => [ 'shape' => '__string', 'locationName' => 'maintenanceScheduledDate', ], 'MaintenanceStartTime' => [ 'shape' => '__stringPattern010920300', 'locationName' => 'maintenanceStartTime', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'min' => 1, 'max' => 1000, ], 'MediaConnectFlow' => [ 'type' => 'structure', 'members' => [ 'FlowArn' => [ 'shape' => '__string', 'locationName' => 'flowArn', ], ], ], 'MediaConnectFlowRequest' => [ 'type' => 'structure', 'members' => [ 'FlowArn' => [ 'shape' => '__string', 'locationName' => 'flowArn', ], ], ], 'MediaPackageGroupSettings' => [ 'type' => 'structure', 'members' => [ 'Destination' => [ 'shape' => 'OutputLocationRef', 'locationName' => 'destination', ], ], 'required' => [ 'Destination', ], ], 'MediaPackageOutputDestinationSettings' => [ 'type' => 'structure', 'members' => [ 'ChannelId' => [ 'shape' => '__stringMin1', 'locationName' => 'channelId', ], 'ChannelGroup' => [ 'shape' => '__stringMin1', 'locationName' => 'channelGroup', ], 'ChannelName' => [ 'shape' => '__stringMin1', 'locationName' => 'channelName', ], ], ], 'MediaPackageOutputSettings' => [ 'type' => 'structure', 'members' => [], ], 'MotionGraphicsActivateScheduleActionSettings' => [ 'type' => 'structure', 'members' => [ 'Duration' => [ 'shape' => '__longMin0Max86400000', 'locationName' => 'duration', ], 'PasswordParam' => [ 'shape' => '__string', 'locationName' => 'passwordParam', ], 'Url' => [ 'shape' => '__string', 'locationName' => 'url', ], 'Username' => [ 'shape' => '__string', 'locationName' => 'username', ], ], ], 'MotionGraphicsConfiguration' => [ 'type' => 'structure', 'members' => [ 'MotionGraphicsInsertion' => [ 'shape' => 'MotionGraphicsInsertion', 'locationName' => 'motionGraphicsInsertion', ], 'MotionGraphicsSettings' => [ 'shape' => 'MotionGraphicsSettings', 'locationName' => 'motionGraphicsSettings', ], ], 'required' => [ 'MotionGraphicsSettings', ], ], 'MotionGraphicsDeactivateScheduleActionSettings' => [ 'type' => 'structure', 'members' => [], ], 'MotionGraphicsInsertion' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'MotionGraphicsSettings' => [ 'type' => 'structure', 'members' => [ 'HtmlMotionGraphicsSettings' => [ 'shape' => 'HtmlMotionGraphicsSettings', 'locationName' => 'htmlMotionGraphicsSettings', ], ], ], 'Mp2CodingMode' => [ 'type' => 'string', 'enum' => [ 'CODING_MODE_1_0', 'CODING_MODE_2_0', ], ], 'Mp2Settings' => [ 'type' => 'structure', 'members' => [ 'Bitrate' => [ 'shape' => '__double', 'locationName' => 'bitrate', ], 'CodingMode' => [ 'shape' => 'Mp2CodingMode', 'locationName' => 'codingMode', ], 'SampleRate' => [ 'shape' => '__double', 'locationName' => 'sampleRate', ], ], ], 'Mpeg2AdaptiveQuantization' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'HIGH', 'LOW', 'MEDIUM', 'OFF', ], ], 'Mpeg2ColorMetadata' => [ 'type' => 'string', 'enum' => [ 'IGNORE', 'INSERT', ], ], 'Mpeg2ColorSpace' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'PASSTHROUGH', ], ], 'Mpeg2DisplayRatio' => [ 'type' => 'string', 'enum' => [ 'DISPLAYRATIO16X9', 'DISPLAYRATIO4X3', ], ], 'Mpeg2FilterSettings' => [ 'type' => 'structure', 'members' => [ 'TemporalFilterSettings' => [ 'shape' => 'TemporalFilterSettings', 'locationName' => 'temporalFilterSettings', ], ], ], 'Mpeg2GopSizeUnits' => [ 'type' => 'string', 'enum' => [ 'FRAMES', 'SECONDS', ], ], 'Mpeg2ScanType' => [ 'type' => 'string', 'enum' => [ 'INTERLACED', 'PROGRESSIVE', ], ], 'Mpeg2Settings' => [ 'type' => 'structure', 'members' => [ 'AdaptiveQuantization' => [ 'shape' => 'Mpeg2AdaptiveQuantization', 'locationName' => 'adaptiveQuantization', ], 'AfdSignaling' => [ 'shape' => 'AfdSignaling', 'locationName' => 'afdSignaling', ], 'ColorMetadata' => [ 'shape' => 'Mpeg2ColorMetadata', 'locationName' => 'colorMetadata', ], 'ColorSpace' => [ 'shape' => 'Mpeg2ColorSpace', 'locationName' => 'colorSpace', ], 'DisplayAspectRatio' => [ 'shape' => 'Mpeg2DisplayRatio', 'locationName' => 'displayAspectRatio', ], 'FilterSettings' => [ 'shape' => 'Mpeg2FilterSettings', 'locationName' => 'filterSettings', ], 'FixedAfd' => [ 'shape' => 'FixedAfd', 'locationName' => 'fixedAfd', ], 'FramerateDenominator' => [ 'shape' => '__integerMin1', 'locationName' => 'framerateDenominator', ], 'FramerateNumerator' => [ 'shape' => '__integerMin1', 'locationName' => 'framerateNumerator', ], 'GopClosedCadence' => [ 'shape' => '__integerMin0', 'locationName' => 'gopClosedCadence', ], 'GopNumBFrames' => [ 'shape' => '__integerMin0Max7', 'locationName' => 'gopNumBFrames', ], 'GopSize' => [ 'shape' => '__double', 'locationName' => 'gopSize', ], 'GopSizeUnits' => [ 'shape' => 'Mpeg2GopSizeUnits', 'locationName' => 'gopSizeUnits', ], 'ScanType' => [ 'shape' => 'Mpeg2ScanType', 'locationName' => 'scanType', ], 'SubgopLength' => [ 'shape' => 'Mpeg2SubGopLength', 'locationName' => 'subgopLength', ], 'TimecodeInsertion' => [ 'shape' => 'Mpeg2TimecodeInsertionBehavior', 'locationName' => 'timecodeInsertion', ], 'TimecodeBurninSettings' => [ 'shape' => 'TimecodeBurninSettings', 'locationName' => 'timecodeBurninSettings', ], ], 'required' => [ 'FramerateNumerator', 'FramerateDenominator', ], ], 'Mpeg2SubGopLength' => [ 'type' => 'string', 'enum' => [ 'DYNAMIC', 'FIXED', ], ], 'Mpeg2TimecodeInsertionBehavior' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'GOP_TIMECODE', ], ], 'MsSmoothGroupSettings' => [ 'type' => 'structure', 'members' => [ 'AcquisitionPointId' => [ 'shape' => '__string', 'locationName' => 'acquisitionPointId', ], 'AudioOnlyTimecodeControl' => [ 'shape' => 'SmoothGroupAudioOnlyTimecodeControl', 'locationName' => 'audioOnlyTimecodeControl', ], 'CertificateMode' => [ 'shape' => 'SmoothGroupCertificateMode', 'locationName' => 'certificateMode', ], 'ConnectionRetryInterval' => [ 'shape' => '__integerMin0', 'locationName' => 'connectionRetryInterval', ], 'Destination' => [ 'shape' => 'OutputLocationRef', 'locationName' => 'destination', ], 'EventId' => [ 'shape' => '__string', 'locationName' => 'eventId', ], 'EventIdMode' => [ 'shape' => 'SmoothGroupEventIdMode', 'locationName' => 'eventIdMode', ], 'EventStopBehavior' => [ 'shape' => 'SmoothGroupEventStopBehavior', 'locationName' => 'eventStopBehavior', ], 'FilecacheDuration' => [ 'shape' => '__integerMin0', 'locationName' => 'filecacheDuration', ], 'FragmentLength' => [ 'shape' => '__integerMin1', 'locationName' => 'fragmentLength', ], 'InputLossAction' => [ 'shape' => 'InputLossActionForMsSmoothOut', 'locationName' => 'inputLossAction', ], 'NumRetries' => [ 'shape' => '__integerMin0', 'locationName' => 'numRetries', ], 'RestartDelay' => [ 'shape' => '__integerMin0', 'locationName' => 'restartDelay', ], 'SegmentationMode' => [ 'shape' => 'SmoothGroupSegmentationMode', 'locationName' => 'segmentationMode', ], 'SendDelayMs' => [ 'shape' => '__integerMin0Max10000', 'locationName' => 'sendDelayMs', ], 'SparseTrackType' => [ 'shape' => 'SmoothGroupSparseTrackType', 'locationName' => 'sparseTrackType', ], 'StreamManifestBehavior' => [ 'shape' => 'SmoothGroupStreamManifestBehavior', 'locationName' => 'streamManifestBehavior', ], 'TimestampOffset' => [ 'shape' => '__string', 'locationName' => 'timestampOffset', ], 'TimestampOffsetMode' => [ 'shape' => 'SmoothGroupTimestampOffsetMode', 'locationName' => 'timestampOffsetMode', ], ], 'required' => [ 'Destination', ], ], 'MsSmoothH265PackagingType' => [ 'type' => 'string', 'enum' => [ 'HEV1', 'HVC1', ], ], 'MsSmoothOutputSettings' => [ 'type' => 'structure', 'members' => [ 'H265PackagingType' => [ 'shape' => 'MsSmoothH265PackagingType', 'locationName' => 'h265PackagingType', ], 'NameModifier' => [ 'shape' => '__string', 'locationName' => 'nameModifier', ], ], ], 'Multiplex' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'AvailabilityZones' => [ 'shape' => '__listOf__string', 'locationName' => 'availabilityZones', ], 'Destinations' => [ 'shape' => '__listOfMultiplexOutputDestination', 'locationName' => 'destinations', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'MultiplexSettings' => [ 'shape' => 'MultiplexSettings', 'locationName' => 'multiplexSettings', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'PipelinesRunningCount' => [ 'shape' => '__integer', 'locationName' => 'pipelinesRunningCount', ], 'ProgramCount' => [ 'shape' => '__integer', 'locationName' => 'programCount', ], 'State' => [ 'shape' => 'MultiplexState', 'locationName' => 'state', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], ], ], 'MultiplexConfigurationValidationError' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], 'ValidationErrors' => [ 'shape' => '__listOfValidationError', 'locationName' => 'validationErrors', ], ], ], 'MultiplexGroupSettings' => [ 'type' => 'structure', 'members' => [], ], 'MultiplexMediaConnectOutputDestinationSettings' => [ 'type' => 'structure', 'members' => [ 'EntitlementArn' => [ 'shape' => '__stringMin1', 'locationName' => 'entitlementArn', ], ], ], 'MultiplexOutputDestination' => [ 'type' => 'structure', 'members' => [ 'MediaConnectSettings' => [ 'shape' => 'MultiplexMediaConnectOutputDestinationSettings', 'locationName' => 'mediaConnectSettings', ], ], ], 'MultiplexOutputSettings' => [ 'type' => 'structure', 'members' => [ 'Destination' => [ 'shape' => 'OutputLocationRef', 'locationName' => 'destination', ], 'ContainerSettings' => [ 'shape' => 'MultiplexContainerSettings', 'locationName' => 'containerSettings', ], ], 'required' => [ 'Destination', ], ], 'MultiplexProgram' => [ 'type' => 'structure', 'members' => [ 'ChannelId' => [ 'shape' => '__string', 'locationName' => 'channelId', ], 'MultiplexProgramSettings' => [ 'shape' => 'MultiplexProgramSettings', 'locationName' => 'multiplexProgramSettings', ], 'PacketIdentifiersMap' => [ 'shape' => 'MultiplexProgramPacketIdentifiersMap', 'locationName' => 'packetIdentifiersMap', ], 'PipelineDetails' => [ 'shape' => '__listOfMultiplexProgramPipelineDetail', 'locationName' => 'pipelineDetails', ], 'ProgramName' => [ 'shape' => '__string', 'locationName' => 'programName', ], ], ], 'MultiplexProgramChannelDestinationSettings' => [ 'type' => 'structure', 'members' => [ 'MultiplexId' => [ 'shape' => '__stringMin1', 'locationName' => 'multiplexId', ], 'ProgramName' => [ 'shape' => '__stringMin1', 'locationName' => 'programName', ], ], ], 'MultiplexProgramPacketIdentifiersMap' => [ 'type' => 'structure', 'members' => [ 'AudioPids' => [ 'shape' => '__listOf__integer', 'locationName' => 'audioPids', ], 'DvbSubPids' => [ 'shape' => '__listOf__integer', 'locationName' => 'dvbSubPids', ], 'DvbTeletextPid' => [ 'shape' => '__integer', 'locationName' => 'dvbTeletextPid', ], 'EtvPlatformPid' => [ 'shape' => '__integer', 'locationName' => 'etvPlatformPid', ], 'EtvSignalPid' => [ 'shape' => '__integer', 'locationName' => 'etvSignalPid', ], 'KlvDataPids' => [ 'shape' => '__listOf__integer', 'locationName' => 'klvDataPids', ], 'PcrPid' => [ 'shape' => '__integer', 'locationName' => 'pcrPid', ], 'PmtPid' => [ 'shape' => '__integer', 'locationName' => 'pmtPid', ], 'PrivateMetadataPid' => [ 'shape' => '__integer', 'locationName' => 'privateMetadataPid', ], 'Scte27Pids' => [ 'shape' => '__listOf__integer', 'locationName' => 'scte27Pids', ], 'Scte35Pid' => [ 'shape' => '__integer', 'locationName' => 'scte35Pid', ], 'TimedMetadataPid' => [ 'shape' => '__integer', 'locationName' => 'timedMetadataPid', ], 'VideoPid' => [ 'shape' => '__integer', 'locationName' => 'videoPid', ], 'AribCaptionsPid' => [ 'shape' => '__integer', 'locationName' => 'aribCaptionsPid', ], 'DvbTeletextPids' => [ 'shape' => '__listOf__integer', 'locationName' => 'dvbTeletextPids', ], 'EcmPid' => [ 'shape' => '__integer', 'locationName' => 'ecmPid', ], 'Smpte2038Pid' => [ 'shape' => '__integer', 'locationName' => 'smpte2038Pid', ], ], ], 'MultiplexProgramPipelineDetail' => [ 'type' => 'structure', 'members' => [ 'ActiveChannelPipeline' => [ 'shape' => '__string', 'locationName' => 'activeChannelPipeline', ], 'PipelineId' => [ 'shape' => '__string', 'locationName' => 'pipelineId', ], ], ], 'MultiplexProgramServiceDescriptor' => [ 'type' => 'structure', 'members' => [ 'ProviderName' => [ 'shape' => '__stringMax256', 'locationName' => 'providerName', ], 'ServiceName' => [ 'shape' => '__stringMax256', 'locationName' => 'serviceName', ], ], 'required' => [ 'ProviderName', 'ServiceName', ], ], 'MultiplexProgramSettings' => [ 'type' => 'structure', 'members' => [ 'PreferredChannelPipeline' => [ 'shape' => 'PreferredChannelPipeline', 'locationName' => 'preferredChannelPipeline', ], 'ProgramNumber' => [ 'shape' => '__integerMin0Max65535', 'locationName' => 'programNumber', ], 'ServiceDescriptor' => [ 'shape' => 'MultiplexProgramServiceDescriptor', 'locationName' => 'serviceDescriptor', ], 'VideoSettings' => [ 'shape' => 'MultiplexVideoSettings', 'locationName' => 'videoSettings', ], ], 'required' => [ 'ProgramNumber', ], ], 'MultiplexProgramSummary' => [ 'type' => 'structure', 'members' => [ 'ChannelId' => [ 'shape' => '__string', 'locationName' => 'channelId', ], 'ProgramName' => [ 'shape' => '__string', 'locationName' => 'programName', ], ], ], 'MultiplexSettings' => [ 'type' => 'structure', 'members' => [ 'MaximumVideoBufferDelayMilliseconds' => [ 'shape' => '__integerMin800Max3000', 'locationName' => 'maximumVideoBufferDelayMilliseconds', ], 'TransportStreamBitrate' => [ 'shape' => '__integerMin1000000Max100000000', 'locationName' => 'transportStreamBitrate', ], 'TransportStreamId' => [ 'shape' => '__integerMin0Max65535', 'locationName' => 'transportStreamId', ], 'TransportStreamReservedBitrate' => [ 'shape' => '__integerMin0Max100000000', 'locationName' => 'transportStreamReservedBitrate', ], ], 'required' => [ 'TransportStreamBitrate', 'TransportStreamId', ], ], 'MultiplexSettingsSummary' => [ 'type' => 'structure', 'members' => [ 'TransportStreamBitrate' => [ 'shape' => '__integerMin1000000Max100000000', 'locationName' => 'transportStreamBitrate', ], ], ], 'MultiplexState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATE_FAILED', 'IDLE', 'STARTING', 'RUNNING', 'RECOVERING', 'STOPPING', 'DELETING', 'DELETED', ], ], 'MultiplexStatmuxVideoSettings' => [ 'type' => 'structure', 'members' => [ 'MaximumBitrate' => [ 'shape' => '__integerMin100000Max100000000', 'locationName' => 'maximumBitrate', ], 'MinimumBitrate' => [ 'shape' => '__integerMin100000Max100000000', 'locationName' => 'minimumBitrate', ], 'Priority' => [ 'shape' => '__integerMinNegative5Max5', 'locationName' => 'priority', ], ], ], 'MultiplexSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'AvailabilityZones' => [ 'shape' => '__listOf__string', 'locationName' => 'availabilityZones', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'MultiplexSettings' => [ 'shape' => 'MultiplexSettingsSummary', 'locationName' => 'multiplexSettings', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'PipelinesRunningCount' => [ 'shape' => '__integer', 'locationName' => 'pipelinesRunningCount', ], 'ProgramCount' => [ 'shape' => '__integer', 'locationName' => 'programCount', ], 'State' => [ 'shape' => 'MultiplexState', 'locationName' => 'state', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], ], ], 'MultiplexVideoSettings' => [ 'type' => 'structure', 'members' => [ 'ConstantBitrate' => [ 'shape' => '__integerMin100000Max100000000', 'locationName' => 'constantBitrate', ], 'StatmuxSettings' => [ 'shape' => 'MultiplexStatmuxVideoSettings', 'locationName' => 'statmuxSettings', ], ], ], 'NetworkInputServerValidation' => [ 'type' => 'string', 'enum' => [ 'CHECK_CRYPTOGRAPHY_AND_VALIDATE_NAME', 'CHECK_CRYPTOGRAPHY_ONLY', ], ], 'NetworkInputSettings' => [ 'type' => 'structure', 'members' => [ 'HlsInputSettings' => [ 'shape' => 'HlsInputSettings', 'locationName' => 'hlsInputSettings', ], 'ServerValidation' => [ 'shape' => 'NetworkInputServerValidation', 'locationName' => 'serverValidation', ], 'MulticastInputSettings' => [ 'shape' => 'MulticastInputSettings', 'locationName' => 'multicastInputSettings', ], ], ], 'NielsenCBET' => [ 'type' => 'structure', 'members' => [ 'CbetCheckDigitString' => [ 'shape' => '__stringMin2Max2', 'locationName' => 'cbetCheckDigitString', ], 'CbetStepaside' => [ 'shape' => 'NielsenWatermarksCbetStepaside', 'locationName' => 'cbetStepaside', ], 'Csid' => [ 'shape' => '__stringMin1Max7', 'locationName' => 'csid', ], ], 'required' => [ 'CbetCheckDigitString', 'CbetStepaside', 'Csid', ], ], 'NielsenConfiguration' => [ 'type' => 'structure', 'members' => [ 'DistributorId' => [ 'shape' => '__string', 'locationName' => 'distributorId', ], 'NielsenPcmToId3Tagging' => [ 'shape' => 'NielsenPcmToId3TaggingState', 'locationName' => 'nielsenPcmToId3Tagging', ], ], ], 'NielsenNaesIiNw' => [ 'type' => 'structure', 'members' => [ 'CheckDigitString' => [ 'shape' => '__stringMin2Max2', 'locationName' => 'checkDigitString', ], 'Sid' => [ 'shape' => '__doubleMin1Max65535', 'locationName' => 'sid', ], 'Timezone' => [ 'shape' => 'NielsenWatermarkTimezones', 'locationName' => 'timezone', ], ], 'required' => [ 'CheckDigitString', 'Sid', ], ], 'NielsenPcmToId3TaggingState' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'NielsenWatermarkTimezones' => [ 'type' => 'string', 'enum' => [ 'AMERICA_PUERTO_RICO', 'US_ALASKA', 'US_ARIZONA', 'US_CENTRAL', 'US_EASTERN', 'US_HAWAII', 'US_MOUNTAIN', 'US_PACIFIC', 'US_SAMOA', 'UTC', ], ], 'NielsenWatermarksCbetStepaside' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'NielsenWatermarksDistributionTypes' => [ 'type' => 'string', 'enum' => [ 'FINAL_DISTRIBUTOR', 'PROGRAM_CONTENT', ], ], 'NielsenWatermarksSettings' => [ 'type' => 'structure', 'members' => [ 'NielsenCbetSettings' => [ 'shape' => 'NielsenCBET', 'locationName' => 'nielsenCbetSettings', ], 'NielsenDistributionType' => [ 'shape' => 'NielsenWatermarksDistributionTypes', 'locationName' => 'nielsenDistributionType', ], 'NielsenNaesIiNwSettings' => [ 'shape' => 'NielsenNaesIiNw', 'locationName' => 'nielsenNaesIiNwSettings', ], ], ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 404, ], ], 'Offering' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'CurrencyCode' => [ 'shape' => '__string', 'locationName' => 'currencyCode', ], 'Duration' => [ 'shape' => '__integer', 'locationName' => 'duration', ], 'DurationUnits' => [ 'shape' => 'OfferingDurationUnits', 'locationName' => 'durationUnits', ], 'FixedPrice' => [ 'shape' => '__double', 'locationName' => 'fixedPrice', ], 'OfferingDescription' => [ 'shape' => '__string', 'locationName' => 'offeringDescription', ], 'OfferingId' => [ 'shape' => '__string', 'locationName' => 'offeringId', ], 'OfferingType' => [ 'shape' => 'OfferingType', 'locationName' => 'offeringType', ], 'Region' => [ 'shape' => '__string', 'locationName' => 'region', ], 'ResourceSpecification' => [ 'shape' => 'ReservationResourceSpecification', 'locationName' => 'resourceSpecification', ], 'UsagePrice' => [ 'shape' => '__double', 'locationName' => 'usagePrice', ], ], ], 'OfferingDurationUnits' => [ 'type' => 'string', 'enum' => [ 'MONTHS', ], ], 'OfferingType' => [ 'type' => 'string', 'enum' => [ 'NO_UPFRONT', ], ], 'Output' => [ 'type' => 'structure', 'members' => [ 'AudioDescriptionNames' => [ 'shape' => '__listOf__string', 'locationName' => 'audioDescriptionNames', ], 'CaptionDescriptionNames' => [ 'shape' => '__listOf__string', 'locationName' => 'captionDescriptionNames', ], 'OutputName' => [ 'shape' => '__stringMin1Max255', 'locationName' => 'outputName', ], 'OutputSettings' => [ 'shape' => 'OutputSettings', 'locationName' => 'outputSettings', ], 'VideoDescriptionName' => [ 'shape' => '__string', 'locationName' => 'videoDescriptionName', ], ], 'required' => [ 'OutputSettings', ], ], 'OutputDestination' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'MediaPackageSettings' => [ 'shape' => '__listOfMediaPackageOutputDestinationSettings', 'locationName' => 'mediaPackageSettings', ], 'MultiplexSettings' => [ 'shape' => 'MultiplexProgramChannelDestinationSettings', 'locationName' => 'multiplexSettings', ], 'Settings' => [ 'shape' => '__listOfOutputDestinationSettings', 'locationName' => 'settings', ], 'SrtSettings' => [ 'shape' => '__listOfSrtOutputDestinationSettings', 'locationName' => 'srtSettings', ], 'LogicalInterfaceNames' => [ 'shape' => '__listOf__string', 'locationName' => 'logicalInterfaceNames', ], ], ], 'OutputDestinationSettings' => [ 'type' => 'structure', 'members' => [ 'PasswordParam' => [ 'shape' => '__string', 'locationName' => 'passwordParam', ], 'StreamName' => [ 'shape' => '__string', 'locationName' => 'streamName', ], 'Url' => [ 'shape' => '__string', 'locationName' => 'url', ], 'Username' => [ 'shape' => '__string', 'locationName' => 'username', ], ], ], 'OutputGroup' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => '__stringMax32', 'locationName' => 'name', ], 'OutputGroupSettings' => [ 'shape' => 'OutputGroupSettings', 'locationName' => 'outputGroupSettings', ], 'Outputs' => [ 'shape' => '__listOfOutput', 'locationName' => 'outputs', ], ], 'required' => [ 'Outputs', 'OutputGroupSettings', ], ], 'OutputGroupSettings' => [ 'type' => 'structure', 'members' => [ 'ArchiveGroupSettings' => [ 'shape' => 'ArchiveGroupSettings', 'locationName' => 'archiveGroupSettings', ], 'FrameCaptureGroupSettings' => [ 'shape' => 'FrameCaptureGroupSettings', 'locationName' => 'frameCaptureGroupSettings', ], 'HlsGroupSettings' => [ 'shape' => 'HlsGroupSettings', 'locationName' => 'hlsGroupSettings', ], 'MediaPackageGroupSettings' => [ 'shape' => 'MediaPackageGroupSettings', 'locationName' => 'mediaPackageGroupSettings', ], 'MsSmoothGroupSettings' => [ 'shape' => 'MsSmoothGroupSettings', 'locationName' => 'msSmoothGroupSettings', ], 'MultiplexGroupSettings' => [ 'shape' => 'MultiplexGroupSettings', 'locationName' => 'multiplexGroupSettings', ], 'RtmpGroupSettings' => [ 'shape' => 'RtmpGroupSettings', 'locationName' => 'rtmpGroupSettings', ], 'UdpGroupSettings' => [ 'shape' => 'UdpGroupSettings', 'locationName' => 'udpGroupSettings', ], 'CmafIngestGroupSettings' => [ 'shape' => 'CmafIngestGroupSettings', 'locationName' => 'cmafIngestGroupSettings', ], 'SrtGroupSettings' => [ 'shape' => 'SrtGroupSettings', 'locationName' => 'srtGroupSettings', ], ], ], 'OutputLocationRef' => [ 'type' => 'structure', 'members' => [ 'DestinationRefId' => [ 'shape' => '__string', 'locationName' => 'destinationRefId', ], ], ], 'OutputLockingSettings' => [ 'type' => 'structure', 'members' => [ 'EpochLockingSettings' => [ 'shape' => 'EpochLockingSettings', 'locationName' => 'epochLockingSettings', ], 'PipelineLockingSettings' => [ 'shape' => 'PipelineLockingSettings', 'locationName' => 'pipelineLockingSettings', ], ], ], 'OutputSettings' => [ 'type' => 'structure', 'members' => [ 'ArchiveOutputSettings' => [ 'shape' => 'ArchiveOutputSettings', 'locationName' => 'archiveOutputSettings', ], 'FrameCaptureOutputSettings' => [ 'shape' => 'FrameCaptureOutputSettings', 'locationName' => 'frameCaptureOutputSettings', ], 'HlsOutputSettings' => [ 'shape' => 'HlsOutputSettings', 'locationName' => 'hlsOutputSettings', ], 'MediaPackageOutputSettings' => [ 'shape' => 'MediaPackageOutputSettings', 'locationName' => 'mediaPackageOutputSettings', ], 'MsSmoothOutputSettings' => [ 'shape' => 'MsSmoothOutputSettings', 'locationName' => 'msSmoothOutputSettings', ], 'MultiplexOutputSettings' => [ 'shape' => 'MultiplexOutputSettings', 'locationName' => 'multiplexOutputSettings', ], 'RtmpOutputSettings' => [ 'shape' => 'RtmpOutputSettings', 'locationName' => 'rtmpOutputSettings', ], 'UdpOutputSettings' => [ 'shape' => 'UdpOutputSettings', 'locationName' => 'udpOutputSettings', ], 'CmafIngestOutputSettings' => [ 'shape' => 'CmafIngestOutputSettings', 'locationName' => 'cmafIngestOutputSettings', ], 'SrtOutputSettings' => [ 'shape' => 'SrtOutputSettings', 'locationName' => 'srtOutputSettings', ], ], ], 'PassThroughSettings' => [ 'type' => 'structure', 'members' => [], ], 'PauseStateScheduleActionSettings' => [ 'type' => 'structure', 'members' => [ 'Pipelines' => [ 'shape' => '__listOfPipelinePauseStateSettings', 'locationName' => 'pipelines', ], ], ], 'PipelineDetail' => [ 'type' => 'structure', 'members' => [ 'ActiveInputAttachmentName' => [ 'shape' => '__string', 'locationName' => 'activeInputAttachmentName', ], 'ActiveInputSwitchActionName' => [ 'shape' => '__string', 'locationName' => 'activeInputSwitchActionName', ], 'ActiveMotionGraphicsActionName' => [ 'shape' => '__string', 'locationName' => 'activeMotionGraphicsActionName', ], 'ActiveMotionGraphicsUri' => [ 'shape' => '__string', 'locationName' => 'activeMotionGraphicsUri', ], 'PipelineId' => [ 'shape' => '__string', 'locationName' => 'pipelineId', ], 'ChannelEngineVersion' => [ 'shape' => 'ChannelEngineVersionResponse', 'locationName' => 'channelEngineVersion', ], ], ], 'PipelineId' => [ 'type' => 'string', 'enum' => [ 'PIPELINE_0', 'PIPELINE_1', ], ], 'PipelineLockingSettings' => [ 'type' => 'structure', 'members' => [], ], 'PipelinePauseStateSettings' => [ 'type' => 'structure', 'members' => [ 'PipelineId' => [ 'shape' => 'PipelineId', 'locationName' => 'pipelineId', ], ], 'required' => [ 'PipelineId', ], ], 'PreferredChannelPipeline' => [ 'type' => 'string', 'enum' => [ 'CURRENTLY_ACTIVE', 'PIPELINE_0', 'PIPELINE_1', ], ], 'PurchaseOffering' => [ 'type' => 'structure', 'members' => [ 'Count' => [ 'shape' => '__integerMin1', 'locationName' => 'count', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'RenewalSettings' => [ 'shape' => 'RenewalSettings', 'locationName' => 'renewalSettings', ], 'RequestId' => [ 'shape' => '__string', 'locationName' => 'requestId', 'idempotencyToken' => true, ], 'Start' => [ 'shape' => '__string', 'locationName' => 'start', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], ], 'required' => [ 'Count', ], ], 'PurchaseOfferingRequest' => [ 'type' => 'structure', 'members' => [ 'Count' => [ 'shape' => '__integerMin1', 'locationName' => 'count', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'OfferingId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'offeringId', ], 'RenewalSettings' => [ 'shape' => 'RenewalSettings', 'locationName' => 'renewalSettings', ], 'RequestId' => [ 'shape' => '__string', 'locationName' => 'requestId', 'idempotencyToken' => true, ], 'Start' => [ 'shape' => '__string', 'locationName' => 'start', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], ], 'required' => [ 'OfferingId', 'Count', ], ], 'PurchaseOfferingResponse' => [ 'type' => 'structure', 'members' => [ 'Reservation' => [ 'shape' => 'Reservation', 'locationName' => 'reservation', ], ], ], 'PurchaseOfferingResultModel' => [ 'type' => 'structure', 'members' => [ 'Reservation' => [ 'shape' => 'Reservation', 'locationName' => 'reservation', ], ], ], 'RawSettings' => [ 'type' => 'structure', 'members' => [], ], 'RebootInputDevice' => [ 'type' => 'structure', 'members' => [ 'Force' => [ 'shape' => 'RebootInputDeviceForce', 'locationName' => 'force', ], ], ], 'RebootInputDeviceForce' => [ 'type' => 'string', 'enum' => [ 'NO', 'YES', ], ], 'RebootInputDeviceRequest' => [ 'type' => 'structure', 'members' => [ 'Force' => [ 'shape' => 'RebootInputDeviceForce', 'locationName' => 'force', ], 'InputDeviceId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'inputDeviceId', ], ], 'required' => [ 'InputDeviceId', ], ], 'RebootInputDeviceResponse' => [ 'type' => 'structure', 'members' => [], ], 'Rec601Settings' => [ 'type' => 'structure', 'members' => [], ], 'Rec709Settings' => [ 'type' => 'structure', 'members' => [], ], 'RejectInputDeviceTransferRequest' => [ 'type' => 'structure', 'members' => [ 'InputDeviceId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'inputDeviceId', ], ], 'required' => [ 'InputDeviceId', ], ], 'RejectInputDeviceTransferResponse' => [ 'type' => 'structure', 'members' => [], ], 'RemixSettings' => [ 'type' => 'structure', 'members' => [ 'ChannelMappings' => [ 'shape' => '__listOfAudioChannelMapping', 'locationName' => 'channelMappings', ], 'ChannelsIn' => [ 'shape' => '__integerMin1Max16', 'locationName' => 'channelsIn', ], 'ChannelsOut' => [ 'shape' => '__integerMin1Max8', 'locationName' => 'channelsOut', ], ], 'required' => [ 'ChannelMappings', ], ], 'RenewalSettings' => [ 'type' => 'structure', 'members' => [ 'AutomaticRenewal' => [ 'shape' => 'ReservationAutomaticRenewal', 'locationName' => 'automaticRenewal', ], 'RenewalCount' => [ 'shape' => '__integerMin1', 'locationName' => 'renewalCount', ], ], ], 'Reservation' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'Count' => [ 'shape' => '__integer', 'locationName' => 'count', ], 'CurrencyCode' => [ 'shape' => '__string', 'locationName' => 'currencyCode', ], 'Duration' => [ 'shape' => '__integer', 'locationName' => 'duration', ], 'DurationUnits' => [ 'shape' => 'OfferingDurationUnits', 'locationName' => 'durationUnits', ], 'End' => [ 'shape' => '__string', 'locationName' => 'end', ], 'FixedPrice' => [ 'shape' => '__double', 'locationName' => 'fixedPrice', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'OfferingDescription' => [ 'shape' => '__string', 'locationName' => 'offeringDescription', ], 'OfferingId' => [ 'shape' => '__string', 'locationName' => 'offeringId', ], 'OfferingType' => [ 'shape' => 'OfferingType', 'locationName' => 'offeringType', ], 'Region' => [ 'shape' => '__string', 'locationName' => 'region', ], 'RenewalSettings' => [ 'shape' => 'RenewalSettings', 'locationName' => 'renewalSettings', ], 'ReservationId' => [ 'shape' => '__string', 'locationName' => 'reservationId', ], 'ResourceSpecification' => [ 'shape' => 'ReservationResourceSpecification', 'locationName' => 'resourceSpecification', ], 'Start' => [ 'shape' => '__string', 'locationName' => 'start', ], 'State' => [ 'shape' => 'ReservationState', 'locationName' => 'state', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'UsagePrice' => [ 'shape' => '__double', 'locationName' => 'usagePrice', ], ], ], 'ReservationAutomaticRenewal' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', 'UNAVAILABLE', ], ], 'ReservationCodec' => [ 'type' => 'string', 'enum' => [ 'MPEG2', 'AVC', 'HEVC', 'AUDIO', 'LINK', 'AV1', ], ], 'ReservationMaximumBitrate' => [ 'type' => 'string', 'enum' => [ 'MAX_10_MBPS', 'MAX_20_MBPS', 'MAX_50_MBPS', ], ], 'ReservationMaximumFramerate' => [ 'type' => 'string', 'enum' => [ 'MAX_30_FPS', 'MAX_60_FPS', ], ], 'ReservationResolution' => [ 'type' => 'string', 'enum' => [ 'SD', 'HD', 'FHD', 'UHD', ], ], 'ReservationResourceSpecification' => [ 'type' => 'structure', 'members' => [ 'ChannelClass' => [ 'shape' => 'ChannelClass', 'locationName' => 'channelClass', ], 'Codec' => [ 'shape' => 'ReservationCodec', 'locationName' => 'codec', ], 'MaximumBitrate' => [ 'shape' => 'ReservationMaximumBitrate', 'locationName' => 'maximumBitrate', ], 'MaximumFramerate' => [ 'shape' => 'ReservationMaximumFramerate', 'locationName' => 'maximumFramerate', ], 'Resolution' => [ 'shape' => 'ReservationResolution', 'locationName' => 'resolution', ], 'ResourceType' => [ 'shape' => 'ReservationResourceType', 'locationName' => 'resourceType', ], 'SpecialFeature' => [ 'shape' => 'ReservationSpecialFeature', 'locationName' => 'specialFeature', ], 'VideoQuality' => [ 'shape' => 'ReservationVideoQuality', 'locationName' => 'videoQuality', ], ], ], 'ReservationResourceType' => [ 'type' => 'string', 'enum' => [ 'INPUT', 'OUTPUT', 'MULTIPLEX', 'CHANNEL', ], ], 'ReservationSpecialFeature' => [ 'type' => 'string', 'enum' => [ 'ADVANCED_AUDIO', 'AUDIO_NORMALIZATION', 'MGHD', 'MGUHD', ], ], 'ReservationState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'EXPIRED', 'CANCELED', 'DELETED', ], ], 'ReservationVideoQuality' => [ 'type' => 'string', 'enum' => [ 'STANDARD', 'ENHANCED', 'PREMIUM', ], ], 'ResourceConflict' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], ], 'ResourceNotFound' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], ], 'RtmpAdMarkers' => [ 'type' => 'string', 'enum' => [ 'ON_CUE_POINT_SCTE35', ], ], 'RtmpCacheFullBehavior' => [ 'type' => 'string', 'enum' => [ 'DISCONNECT_IMMEDIATELY', 'WAIT_FOR_SERVER', ], ], 'RtmpCaptionData' => [ 'type' => 'string', 'enum' => [ 'ALL', 'FIELD1_608', 'FIELD1_AND_FIELD2_608', ], ], 'RtmpCaptionInfoDestinationSettings' => [ 'type' => 'structure', 'members' => [], ], 'RtmpGroupSettings' => [ 'type' => 'structure', 'members' => [ 'AdMarkers' => [ 'shape' => '__listOfRtmpAdMarkers', 'locationName' => 'adMarkers', ], 'AuthenticationScheme' => [ 'shape' => 'AuthenticationScheme', 'locationName' => 'authenticationScheme', ], 'CacheFullBehavior' => [ 'shape' => 'RtmpCacheFullBehavior', 'locationName' => 'cacheFullBehavior', ], 'CacheLength' => [ 'shape' => '__integerMin30', 'locationName' => 'cacheLength', ], 'CaptionData' => [ 'shape' => 'RtmpCaptionData', 'locationName' => 'captionData', ], 'InputLossAction' => [ 'shape' => 'InputLossActionForRtmpOut', 'locationName' => 'inputLossAction', ], 'RestartDelay' => [ 'shape' => '__integerMin0', 'locationName' => 'restartDelay', ], 'IncludeFillerNalUnits' => [ 'shape' => 'IncludeFillerNalUnits', 'locationName' => 'includeFillerNalUnits', ], ], ], 'RtmpOutputCertificateMode' => [ 'type' => 'string', 'enum' => [ 'SELF_SIGNED', 'VERIFY_AUTHENTICITY', ], ], 'RtmpOutputSettings' => [ 'type' => 'structure', 'members' => [ 'CertificateMode' => [ 'shape' => 'RtmpOutputCertificateMode', 'locationName' => 'certificateMode', ], 'ConnectionRetryInterval' => [ 'shape' => '__integerMin1', 'locationName' => 'connectionRetryInterval', ], 'Destination' => [ 'shape' => 'OutputLocationRef', 'locationName' => 'destination', ], 'NumRetries' => [ 'shape' => '__integerMin0', 'locationName' => 'numRetries', ], ], 'required' => [ 'Destination', ], ], 'S3CannedAcl' => [ 'type' => 'string', 'enum' => [ 'AUTHENTICATED_READ', 'BUCKET_OWNER_FULL_CONTROL', 'BUCKET_OWNER_READ', 'PUBLIC_READ', ], ], 'ScheduleAction' => [ 'type' => 'structure', 'members' => [ 'ActionName' => [ 'shape' => '__string', 'locationName' => 'actionName', ], 'ScheduleActionSettings' => [ 'shape' => 'ScheduleActionSettings', 'locationName' => 'scheduleActionSettings', ], 'ScheduleActionStartSettings' => [ 'shape' => 'ScheduleActionStartSettings', 'locationName' => 'scheduleActionStartSettings', ], ], 'required' => [ 'ActionName', 'ScheduleActionStartSettings', 'ScheduleActionSettings', ], ], 'ScheduleActionSettings' => [ 'type' => 'structure', 'members' => [ 'HlsId3SegmentTaggingSettings' => [ 'shape' => 'HlsId3SegmentTaggingScheduleActionSettings', 'locationName' => 'hlsId3SegmentTaggingSettings', ], 'HlsTimedMetadataSettings' => [ 'shape' => 'HlsTimedMetadataScheduleActionSettings', 'locationName' => 'hlsTimedMetadataSettings', ], 'InputPrepareSettings' => [ 'shape' => 'InputPrepareScheduleActionSettings', 'locationName' => 'inputPrepareSettings', ], 'InputSwitchSettings' => [ 'shape' => 'InputSwitchScheduleActionSettings', 'locationName' => 'inputSwitchSettings', ], 'MotionGraphicsImageActivateSettings' => [ 'shape' => 'MotionGraphicsActivateScheduleActionSettings', 'locationName' => 'motionGraphicsImageActivateSettings', ], 'MotionGraphicsImageDeactivateSettings' => [ 'shape' => 'MotionGraphicsDeactivateScheduleActionSettings', 'locationName' => 'motionGraphicsImageDeactivateSettings', ], 'PauseStateSettings' => [ 'shape' => 'PauseStateScheduleActionSettings', 'locationName' => 'pauseStateSettings', ], 'Scte35InputSettings' => [ 'shape' => 'Scte35InputScheduleActionSettings', 'locationName' => 'scte35InputSettings', ], 'Scte35ReturnToNetworkSettings' => [ 'shape' => 'Scte35ReturnToNetworkScheduleActionSettings', 'locationName' => 'scte35ReturnToNetworkSettings', ], 'Scte35SpliceInsertSettings' => [ 'shape' => 'Scte35SpliceInsertScheduleActionSettings', 'locationName' => 'scte35SpliceInsertSettings', ], 'Scte35TimeSignalSettings' => [ 'shape' => 'Scte35TimeSignalScheduleActionSettings', 'locationName' => 'scte35TimeSignalSettings', ], 'StaticImageActivateSettings' => [ 'shape' => 'StaticImageActivateScheduleActionSettings', 'locationName' => 'staticImageActivateSettings', ], 'StaticImageDeactivateSettings' => [ 'shape' => 'StaticImageDeactivateScheduleActionSettings', 'locationName' => 'staticImageDeactivateSettings', ], 'StaticImageOutputActivateSettings' => [ 'shape' => 'StaticImageOutputActivateScheduleActionSettings', 'locationName' => 'staticImageOutputActivateSettings', ], 'StaticImageOutputDeactivateSettings' => [ 'shape' => 'StaticImageOutputDeactivateScheduleActionSettings', 'locationName' => 'staticImageOutputDeactivateSettings', ], 'Id3SegmentTaggingSettings' => [ 'shape' => 'Id3SegmentTaggingScheduleActionSettings', 'locationName' => 'id3SegmentTaggingSettings', ], 'TimedMetadataSettings' => [ 'shape' => 'TimedMetadataScheduleActionSettings', 'locationName' => 'timedMetadataSettings', ], ], ], 'ScheduleActionStartSettings' => [ 'type' => 'structure', 'members' => [ 'FixedModeScheduleActionStartSettings' => [ 'shape' => 'FixedModeScheduleActionStartSettings', 'locationName' => 'fixedModeScheduleActionStartSettings', ], 'FollowModeScheduleActionStartSettings' => [ 'shape' => 'FollowModeScheduleActionStartSettings', 'locationName' => 'followModeScheduleActionStartSettings', ], 'ImmediateModeScheduleActionStartSettings' => [ 'shape' => 'ImmediateModeScheduleActionStartSettings', 'locationName' => 'immediateModeScheduleActionStartSettings', ], ], ], 'ScheduleDeleteResultModel' => [ 'type' => 'structure', 'members' => [], ], 'ScheduleDescribeResultModel' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'ScheduleActions' => [ 'shape' => '__listOfScheduleAction', 'locationName' => 'scheduleActions', ], ], 'required' => [ 'ScheduleActions', ], ], 'Scte20Convert608To708' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'UPCONVERT', ], ], 'Scte20PlusEmbeddedDestinationSettings' => [ 'type' => 'structure', 'members' => [], ], 'Scte20SourceSettings' => [ 'type' => 'structure', 'members' => [ 'Convert608To708' => [ 'shape' => 'Scte20Convert608To708', 'locationName' => 'convert608To708', ], 'Source608ChannelNumber' => [ 'shape' => '__integerMin1Max4', 'locationName' => 'source608ChannelNumber', ], ], ], 'Scte27DestinationSettings' => [ 'type' => 'structure', 'members' => [], ], 'Scte27OcrLanguage' => [ 'type' => 'string', 'enum' => [ 'DEU', 'ENG', 'FRA', 'NLD', 'POR', 'SPA', ], ], 'Scte27SourceSettings' => [ 'type' => 'structure', 'members' => [ 'OcrLanguage' => [ 'shape' => 'Scte27OcrLanguage', 'locationName' => 'ocrLanguage', ], 'Pid' => [ 'shape' => '__integerMin1', 'locationName' => 'pid', ], ], ], 'Scte35AposNoRegionalBlackoutBehavior' => [ 'type' => 'string', 'enum' => [ 'FOLLOW', 'IGNORE', ], ], 'Scte35AposWebDeliveryAllowedBehavior' => [ 'type' => 'string', 'enum' => [ 'FOLLOW', 'IGNORE', ], ], 'Scte35ArchiveAllowedFlag' => [ 'type' => 'string', 'enum' => [ 'ARCHIVE_NOT_ALLOWED', 'ARCHIVE_ALLOWED', ], ], 'Scte35DeliveryRestrictions' => [ 'type' => 'structure', 'members' => [ 'ArchiveAllowedFlag' => [ 'shape' => 'Scte35ArchiveAllowedFlag', 'locationName' => 'archiveAllowedFlag', ], 'DeviceRestrictions' => [ 'shape' => 'Scte35DeviceRestrictions', 'locationName' => 'deviceRestrictions', ], 'NoRegionalBlackoutFlag' => [ 'shape' => 'Scte35NoRegionalBlackoutFlag', 'locationName' => 'noRegionalBlackoutFlag', ], 'WebDeliveryAllowedFlag' => [ 'shape' => 'Scte35WebDeliveryAllowedFlag', 'locationName' => 'webDeliveryAllowedFlag', ], ], 'required' => [ 'DeviceRestrictions', 'ArchiveAllowedFlag', 'WebDeliveryAllowedFlag', 'NoRegionalBlackoutFlag', ], ], 'Scte35Descriptor' => [ 'type' => 'structure', 'members' => [ 'Scte35DescriptorSettings' => [ 'shape' => 'Scte35DescriptorSettings', 'locationName' => 'scte35DescriptorSettings', ], ], 'required' => [ 'Scte35DescriptorSettings', ], ], 'Scte35DescriptorSettings' => [ 'type' => 'structure', 'members' => [ 'SegmentationDescriptorScte35DescriptorSettings' => [ 'shape' => 'Scte35SegmentationDescriptor', 'locationName' => 'segmentationDescriptorScte35DescriptorSettings', ], ], 'required' => [ 'SegmentationDescriptorScte35DescriptorSettings', ], ], 'Scte35DeviceRestrictions' => [ 'type' => 'string', 'enum' => [ 'NONE', 'RESTRICT_GROUP0', 'RESTRICT_GROUP1', 'RESTRICT_GROUP2', ], ], 'Scte35InputMode' => [ 'type' => 'string', 'enum' => [ 'FIXED', 'FOLLOW_ACTIVE', ], ], 'Scte35InputScheduleActionSettings' => [ 'type' => 'structure', 'members' => [ 'InputAttachmentNameReference' => [ 'shape' => '__string', 'locationName' => 'inputAttachmentNameReference', ], 'Mode' => [ 'shape' => 'Scte35InputMode', 'locationName' => 'mode', ], ], 'required' => [ 'Mode', ], ], 'Scte35NoRegionalBlackoutFlag' => [ 'type' => 'string', 'enum' => [ 'REGIONAL_BLACKOUT', 'NO_REGIONAL_BLACKOUT', ], ], 'Scte35ReturnToNetworkScheduleActionSettings' => [ 'type' => 'structure', 'members' => [ 'SpliceEventId' => [ 'shape' => '__longMin0Max4294967295', 'locationName' => 'spliceEventId', ], ], 'required' => [ 'SpliceEventId', ], ], 'Scte35SegmentationCancelIndicator' => [ 'type' => 'string', 'enum' => [ 'SEGMENTATION_EVENT_NOT_CANCELED', 'SEGMENTATION_EVENT_CANCELED', ], ], 'Scte35SegmentationDescriptor' => [ 'type' => 'structure', 'members' => [ 'DeliveryRestrictions' => [ 'shape' => 'Scte35DeliveryRestrictions', 'locationName' => 'deliveryRestrictions', ], 'SegmentNum' => [ 'shape' => '__integerMin0Max255', 'locationName' => 'segmentNum', ], 'SegmentationCancelIndicator' => [ 'shape' => 'Scte35SegmentationCancelIndicator', 'locationName' => 'segmentationCancelIndicator', ], 'SegmentationDuration' => [ 'shape' => '__longMin0Max1099511627775', 'locationName' => 'segmentationDuration', ], 'SegmentationEventId' => [ 'shape' => '__longMin0Max4294967295', 'locationName' => 'segmentationEventId', ], 'SegmentationTypeId' => [ 'shape' => '__integerMin0Max255', 'locationName' => 'segmentationTypeId', ], 'SegmentationUpid' => [ 'shape' => '__string', 'locationName' => 'segmentationUpid', ], 'SegmentationUpidType' => [ 'shape' => '__integerMin0Max255', 'locationName' => 'segmentationUpidType', ], 'SegmentsExpected' => [ 'shape' => '__integerMin0Max255', 'locationName' => 'segmentsExpected', ], 'SubSegmentNum' => [ 'shape' => '__integerMin0Max255', 'locationName' => 'subSegmentNum', ], 'SubSegmentsExpected' => [ 'shape' => '__integerMin0Max255', 'locationName' => 'subSegmentsExpected', ], ], 'required' => [ 'SegmentationEventId', 'SegmentationCancelIndicator', ], ], 'Scte35SpliceInsert' => [ 'type' => 'structure', 'members' => [ 'AdAvailOffset' => [ 'shape' => '__integerMinNegative1000Max1000', 'locationName' => 'adAvailOffset', ], 'NoRegionalBlackoutFlag' => [ 'shape' => 'Scte35SpliceInsertNoRegionalBlackoutBehavior', 'locationName' => 'noRegionalBlackoutFlag', ], 'WebDeliveryAllowedFlag' => [ 'shape' => 'Scte35SpliceInsertWebDeliveryAllowedBehavior', 'locationName' => 'webDeliveryAllowedFlag', ], ], ], 'Scte35SpliceInsertNoRegionalBlackoutBehavior' => [ 'type' => 'string', 'enum' => [ 'FOLLOW', 'IGNORE', ], ], 'Scte35SpliceInsertScheduleActionSettings' => [ 'type' => 'structure', 'members' => [ 'Duration' => [ 'shape' => '__longMin0Max8589934591', 'locationName' => 'duration', ], 'SpliceEventId' => [ 'shape' => '__longMin0Max4294967295', 'locationName' => 'spliceEventId', ], ], 'required' => [ 'SpliceEventId', ], ], 'Scte35SpliceInsertWebDeliveryAllowedBehavior' => [ 'type' => 'string', 'enum' => [ 'FOLLOW', 'IGNORE', ], ], 'Scte35TimeSignalApos' => [ 'type' => 'structure', 'members' => [ 'AdAvailOffset' => [ 'shape' => '__integerMinNegative1000Max1000', 'locationName' => 'adAvailOffset', ], 'NoRegionalBlackoutFlag' => [ 'shape' => 'Scte35AposNoRegionalBlackoutBehavior', 'locationName' => 'noRegionalBlackoutFlag', ], 'WebDeliveryAllowedFlag' => [ 'shape' => 'Scte35AposWebDeliveryAllowedBehavior', 'locationName' => 'webDeliveryAllowedFlag', ], ], ], 'Scte35TimeSignalScheduleActionSettings' => [ 'type' => 'structure', 'members' => [ 'Scte35Descriptors' => [ 'shape' => '__listOfScte35Descriptor', 'locationName' => 'scte35Descriptors', ], ], 'required' => [ 'Scte35Descriptors', ], ], 'Scte35WebDeliveryAllowedFlag' => [ 'type' => 'string', 'enum' => [ 'WEB_DELIVERY_NOT_ALLOWED', 'WEB_DELIVERY_ALLOWED', ], ], 'SmoothGroupAudioOnlyTimecodeControl' => [ 'type' => 'string', 'enum' => [ 'PASSTHROUGH', 'USE_CONFIGURED_CLOCK', ], ], 'SmoothGroupCertificateMode' => [ 'type' => 'string', 'enum' => [ 'SELF_SIGNED', 'VERIFY_AUTHENTICITY', ], ], 'SmoothGroupEventIdMode' => [ 'type' => 'string', 'enum' => [ 'NO_EVENT_ID', 'USE_CONFIGURED', 'USE_TIMESTAMP', ], ], 'SmoothGroupEventStopBehavior' => [ 'type' => 'string', 'enum' => [ 'NONE', 'SEND_EOS', ], ], 'SmoothGroupSegmentationMode' => [ 'type' => 'string', 'enum' => [ 'USE_INPUT_SEGMENTATION', 'USE_SEGMENT_DURATION', ], ], 'SmoothGroupSparseTrackType' => [ 'type' => 'string', 'enum' => [ 'NONE', 'SCTE_35', 'SCTE_35_WITHOUT_SEGMENTATION', ], ], 'SmoothGroupStreamManifestBehavior' => [ 'type' => 'string', 'enum' => [ 'DO_NOT_SEND', 'SEND', ], ], 'SmoothGroupTimestampOffsetMode' => [ 'type' => 'string', 'enum' => [ 'USE_CONFIGURED_OFFSET', 'USE_EVENT_START_DATE', ], ], 'Smpte2038DataPreference' => [ 'type' => 'string', 'enum' => [ 'IGNORE', 'PREFER', ], ], 'SmpteTtDestinationSettings' => [ 'type' => 'structure', 'members' => [], ], 'StandardHlsSettings' => [ 'type' => 'structure', 'members' => [ 'AudioRenditionSets' => [ 'shape' => '__string', 'locationName' => 'audioRenditionSets', ], 'M3u8Settings' => [ 'shape' => 'M3u8Settings', 'locationName' => 'm3u8Settings', ], ], 'required' => [ 'M3u8Settings', ], ], 'StartChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ChannelId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'channelId', ], ], 'required' => [ 'ChannelId', ], ], 'StartChannelResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'CdiInputSpecification' => [ 'shape' => 'CdiInputSpecification', 'locationName' => 'cdiInputSpecification', ], 'ChannelClass' => [ 'shape' => 'ChannelClass', 'locationName' => 'channelClass', ], 'Destinations' => [ 'shape' => '__listOfOutputDestination', 'locationName' => 'destinations', ], 'EgressEndpoints' => [ 'shape' => '__listOfChannelEgressEndpoint', 'locationName' => 'egressEndpoints', ], 'EncoderSettings' => [ 'shape' => 'EncoderSettings', 'locationName' => 'encoderSettings', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'InputAttachments' => [ 'shape' => '__listOfInputAttachment', 'locationName' => 'inputAttachments', ], 'InputSpecification' => [ 'shape' => 'InputSpecification', 'locationName' => 'inputSpecification', ], 'LogLevel' => [ 'shape' => 'LogLevel', 'locationName' => 'logLevel', ], 'Maintenance' => [ 'shape' => 'MaintenanceStatus', 'locationName' => 'maintenance', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'PipelineDetails' => [ 'shape' => '__listOfPipelineDetail', 'locationName' => 'pipelineDetails', ], 'PipelinesRunningCount' => [ 'shape' => '__integer', 'locationName' => 'pipelinesRunningCount', ], 'RoleArn' => [ 'shape' => '__string', 'locationName' => 'roleArn', ], 'State' => [ 'shape' => 'ChannelState', 'locationName' => 'state', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'Vpc' => [ 'shape' => 'VpcOutputSettingsDescription', 'locationName' => 'vpc', ], 'AnywhereSettings' => [ 'shape' => 'DescribeAnywhereSettings', 'locationName' => 'anywhereSettings', ], 'ChannelEngineVersion' => [ 'shape' => 'ChannelEngineVersionResponse', 'locationName' => 'channelEngineVersion', ], ], ], 'StartInputDeviceMaintenanceWindowRequest' => [ 'type' => 'structure', 'members' => [ 'InputDeviceId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'inputDeviceId', ], ], 'required' => [ 'InputDeviceId', ], ], 'StartInputDeviceMaintenanceWindowResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartInputDeviceRequest' => [ 'type' => 'structure', 'members' => [ 'InputDeviceId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'inputDeviceId', ], ], 'required' => [ 'InputDeviceId', ], ], 'StartInputDeviceResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartMultiplexRequest' => [ 'type' => 'structure', 'members' => [ 'MultiplexId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'multiplexId', ], ], 'required' => [ 'MultiplexId', ], ], 'StartMultiplexResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'AvailabilityZones' => [ 'shape' => '__listOf__string', 'locationName' => 'availabilityZones', ], 'Destinations' => [ 'shape' => '__listOfMultiplexOutputDestination', 'locationName' => 'destinations', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'MultiplexSettings' => [ 'shape' => 'MultiplexSettings', 'locationName' => 'multiplexSettings', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'PipelinesRunningCount' => [ 'shape' => '__integer', 'locationName' => 'pipelinesRunningCount', ], 'ProgramCount' => [ 'shape' => '__integer', 'locationName' => 'programCount', ], 'State' => [ 'shape' => 'MultiplexState', 'locationName' => 'state', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], ], ], 'StartTimecode' => [ 'type' => 'structure', 'members' => [ 'Timecode' => [ 'shape' => '__string', 'locationName' => 'timecode', ], ], ], 'StaticImageActivateScheduleActionSettings' => [ 'type' => 'structure', 'members' => [ 'Duration' => [ 'shape' => '__integerMin0', 'locationName' => 'duration', ], 'FadeIn' => [ 'shape' => '__integerMin0', 'locationName' => 'fadeIn', ], 'FadeOut' => [ 'shape' => '__integerMin0', 'locationName' => 'fadeOut', ], 'Height' => [ 'shape' => '__integerMin1', 'locationName' => 'height', ], 'Image' => [ 'shape' => 'InputLocation', 'locationName' => 'image', ], 'ImageX' => [ 'shape' => '__integerMin0', 'locationName' => 'imageX', ], 'ImageY' => [ 'shape' => '__integerMin0', 'locationName' => 'imageY', ], 'Layer' => [ 'shape' => '__integerMin0Max7', 'locationName' => 'layer', ], 'Opacity' => [ 'shape' => '__integerMin0Max100', 'locationName' => 'opacity', ], 'Width' => [ 'shape' => '__integerMin1', 'locationName' => 'width', ], ], 'required' => [ 'Image', ], ], 'StaticImageDeactivateScheduleActionSettings' => [ 'type' => 'structure', 'members' => [ 'FadeOut' => [ 'shape' => '__integerMin0', 'locationName' => 'fadeOut', ], 'Layer' => [ 'shape' => '__integerMin0Max7', 'locationName' => 'layer', ], ], ], 'StaticImageOutputActivateScheduleActionSettings' => [ 'type' => 'structure', 'members' => [ 'Duration' => [ 'shape' => '__integerMin0', 'locationName' => 'duration', ], 'FadeIn' => [ 'shape' => '__integerMin0', 'locationName' => 'fadeIn', ], 'FadeOut' => [ 'shape' => '__integerMin0', 'locationName' => 'fadeOut', ], 'Height' => [ 'shape' => '__integerMin1', 'locationName' => 'height', ], 'Image' => [ 'shape' => 'InputLocation', 'locationName' => 'image', ], 'ImageX' => [ 'shape' => '__integerMin0', 'locationName' => 'imageX', ], 'ImageY' => [ 'shape' => '__integerMin0', 'locationName' => 'imageY', ], 'Layer' => [ 'shape' => '__integerMin0Max7', 'locationName' => 'layer', ], 'Opacity' => [ 'shape' => '__integerMin0Max100', 'locationName' => 'opacity', ], 'OutputNames' => [ 'shape' => '__listOf__string', 'locationName' => 'outputNames', ], 'Width' => [ 'shape' => '__integerMin1', 'locationName' => 'width', ], ], 'required' => [ 'OutputNames', 'Image', ], ], 'StaticImageOutputDeactivateScheduleActionSettings' => [ 'type' => 'structure', 'members' => [ 'FadeOut' => [ 'shape' => '__integerMin0', 'locationName' => 'fadeOut', ], 'Layer' => [ 'shape' => '__integerMin0Max7', 'locationName' => 'layer', ], 'OutputNames' => [ 'shape' => '__listOf__string', 'locationName' => 'outputNames', ], ], 'required' => [ 'OutputNames', ], ], 'StaticKeySettings' => [ 'type' => 'structure', 'members' => [ 'KeyProviderServer' => [ 'shape' => 'InputLocation', 'locationName' => 'keyProviderServer', ], 'StaticKeyValue' => [ 'shape' => '__stringMin32Max32', 'locationName' => 'staticKeyValue', ], ], 'required' => [ 'StaticKeyValue', ], ], 'StopChannelRequest' => [ 'type' => 'structure', 'members' => [ 'ChannelId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'channelId', ], ], 'required' => [ 'ChannelId', ], ], 'StopChannelResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'CdiInputSpecification' => [ 'shape' => 'CdiInputSpecification', 'locationName' => 'cdiInputSpecification', ], 'ChannelClass' => [ 'shape' => 'ChannelClass', 'locationName' => 'channelClass', ], 'Destinations' => [ 'shape' => '__listOfOutputDestination', 'locationName' => 'destinations', ], 'EgressEndpoints' => [ 'shape' => '__listOfChannelEgressEndpoint', 'locationName' => 'egressEndpoints', ], 'EncoderSettings' => [ 'shape' => 'EncoderSettings', 'locationName' => 'encoderSettings', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'InputAttachments' => [ 'shape' => '__listOfInputAttachment', 'locationName' => 'inputAttachments', ], 'InputSpecification' => [ 'shape' => 'InputSpecification', 'locationName' => 'inputSpecification', ], 'LogLevel' => [ 'shape' => 'LogLevel', 'locationName' => 'logLevel', ], 'Maintenance' => [ 'shape' => 'MaintenanceStatus', 'locationName' => 'maintenance', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'PipelineDetails' => [ 'shape' => '__listOfPipelineDetail', 'locationName' => 'pipelineDetails', ], 'PipelinesRunningCount' => [ 'shape' => '__integer', 'locationName' => 'pipelinesRunningCount', ], 'RoleArn' => [ 'shape' => '__string', 'locationName' => 'roleArn', ], 'State' => [ 'shape' => 'ChannelState', 'locationName' => 'state', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'Vpc' => [ 'shape' => 'VpcOutputSettingsDescription', 'locationName' => 'vpc', ], 'AnywhereSettings' => [ 'shape' => 'DescribeAnywhereSettings', 'locationName' => 'anywhereSettings', ], 'ChannelEngineVersion' => [ 'shape' => 'ChannelEngineVersionResponse', 'locationName' => 'channelEngineVersion', ], ], ], 'StopInputDeviceRequest' => [ 'type' => 'structure', 'members' => [ 'InputDeviceId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'inputDeviceId', ], ], 'required' => [ 'InputDeviceId', ], ], 'StopInputDeviceResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopMultiplexRequest' => [ 'type' => 'structure', 'members' => [ 'MultiplexId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'multiplexId', ], ], 'required' => [ 'MultiplexId', ], ], 'StopMultiplexResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'AvailabilityZones' => [ 'shape' => '__listOf__string', 'locationName' => 'availabilityZones', ], 'Destinations' => [ 'shape' => '__listOfMultiplexOutputDestination', 'locationName' => 'destinations', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'MultiplexSettings' => [ 'shape' => 'MultiplexSettings', 'locationName' => 'multiplexSettings', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'PipelinesRunningCount' => [ 'shape' => '__integer', 'locationName' => 'pipelinesRunningCount', ], 'ProgramCount' => [ 'shape' => '__integer', 'locationName' => 'programCount', ], 'State' => [ 'shape' => 'MultiplexState', 'locationName' => 'state', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], ], ], 'StopTimecode' => [ 'type' => 'structure', 'members' => [ 'LastFrameClippingBehavior' => [ 'shape' => 'LastFrameClippingBehavior', 'locationName' => 'lastFrameClippingBehavior', ], 'Timecode' => [ 'shape' => '__string', 'locationName' => 'timecode', ], ], ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => '__string', ], ], 'TagsModel' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], ], ], 'TeletextDestinationSettings' => [ 'type' => 'structure', 'members' => [], ], 'TeletextSourceSettings' => [ 'type' => 'structure', 'members' => [ 'OutputRectangle' => [ 'shape' => 'CaptionRectangle', 'locationName' => 'outputRectangle', ], 'PageNumber' => [ 'shape' => '__string', 'locationName' => 'pageNumber', ], ], ], 'TemporalFilterPostFilterSharpening' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'DISABLED', 'ENABLED', ], ], 'TemporalFilterSettings' => [ 'type' => 'structure', 'members' => [ 'PostFilterSharpening' => [ 'shape' => 'TemporalFilterPostFilterSharpening', 'locationName' => 'postFilterSharpening', ], 'Strength' => [ 'shape' => 'TemporalFilterStrength', 'locationName' => 'strength', ], ], ], 'TemporalFilterStrength' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'STRENGTH_1', 'STRENGTH_2', 'STRENGTH_3', 'STRENGTH_4', 'STRENGTH_5', 'STRENGTH_6', 'STRENGTH_7', 'STRENGTH_8', 'STRENGTH_9', 'STRENGTH_10', 'STRENGTH_11', 'STRENGTH_12', 'STRENGTH_13', 'STRENGTH_14', 'STRENGTH_15', 'STRENGTH_16', ], ], 'Thumbnail' => [ 'type' => 'structure', 'members' => [ 'Body' => [ 'shape' => '__string', 'locationName' => 'body', ], 'ContentType' => [ 'shape' => '__string', 'locationName' => 'contentType', ], 'ThumbnailType' => [ 'shape' => 'ThumbnailType', 'locationName' => 'thumbnailType', ], 'TimeStamp' => [ 'shape' => '__timestampIso8601', 'locationName' => 'timeStamp', ], ], ], 'ThumbnailConfiguration' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'ThumbnailState', 'locationName' => 'state', ], ], 'required' => [ 'State', ], ], 'ThumbnailData' => [ 'type' => 'structure', 'members' => [ 'Body' => [ 'shape' => '__string', 'locationName' => 'body', ], ], ], 'ThumbnailDetail' => [ 'type' => 'structure', 'members' => [ 'PipelineId' => [ 'shape' => '__string', 'locationName' => 'pipelineId', ], 'Thumbnails' => [ 'shape' => '__listOfThumbnail', 'locationName' => 'thumbnails', ], ], ], 'ThumbnailNoData' => [ 'type' => 'structure', 'members' => [], ], 'ThumbnailState' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'DISABLED', ], ], 'ThumbnailType' => [ 'type' => 'string', 'enum' => [ 'UNSPECIFIED', 'CURRENT_ACTIVE', ], ], 'TimecodeBurninFontSize' => [ 'type' => 'string', 'enum' => [ 'EXTRA_SMALL_10', 'LARGE_48', 'MEDIUM_32', 'SMALL_16', ], ], 'TimecodeBurninPosition' => [ 'type' => 'string', 'enum' => [ 'BOTTOM_CENTER', 'BOTTOM_LEFT', 'BOTTOM_RIGHT', 'MIDDLE_CENTER', 'MIDDLE_LEFT', 'MIDDLE_RIGHT', 'TOP_CENTER', 'TOP_LEFT', 'TOP_RIGHT', ], ], 'TimecodeBurninSettings' => [ 'type' => 'structure', 'members' => [ 'FontSize' => [ 'shape' => 'TimecodeBurninFontSize', 'locationName' => 'fontSize', ], 'Position' => [ 'shape' => 'TimecodeBurninPosition', 'locationName' => 'position', ], 'Prefix' => [ 'shape' => '__stringMax255', 'locationName' => 'prefix', ], ], 'required' => [ 'Position', 'FontSize', ], ], 'TimecodeConfig' => [ 'type' => 'structure', 'members' => [ 'Source' => [ 'shape' => 'TimecodeConfigSource', 'locationName' => 'source', ], 'SyncThreshold' => [ 'shape' => '__integerMin1Max1000000', 'locationName' => 'syncThreshold', ], ], 'required' => [ 'Source', ], ], 'TimecodeConfigSource' => [ 'type' => 'string', 'enum' => [ 'EMBEDDED', 'SYSTEMCLOCK', 'ZEROBASED', ], ], 'TooManyRequestsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 429, ], ], 'TransferInputDevice' => [ 'type' => 'structure', 'members' => [ 'TargetCustomerId' => [ 'shape' => '__string', 'locationName' => 'targetCustomerId', ], 'TargetRegion' => [ 'shape' => '__string', 'locationName' => 'targetRegion', ], 'TransferMessage' => [ 'shape' => '__string', 'locationName' => 'transferMessage', ], ], ], 'TransferInputDeviceRequest' => [ 'type' => 'structure', 'members' => [ 'InputDeviceId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'inputDeviceId', ], 'TargetCustomerId' => [ 'shape' => '__string', 'locationName' => 'targetCustomerId', ], 'TargetRegion' => [ 'shape' => '__string', 'locationName' => 'targetRegion', ], 'TransferMessage' => [ 'shape' => '__string', 'locationName' => 'transferMessage', ], ], 'required' => [ 'InputDeviceId', ], ], 'TransferInputDeviceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TransferringInputDeviceSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], 'TargetCustomerId' => [ 'shape' => '__string', 'locationName' => 'targetCustomerId', ], 'TransferType' => [ 'shape' => 'InputDeviceTransferType', 'locationName' => 'transferType', ], ], ], 'TtmlDestinationSettings' => [ 'type' => 'structure', 'members' => [ 'StyleControl' => [ 'shape' => 'TtmlDestinationStyleControl', 'locationName' => 'styleControl', ], ], ], 'TtmlDestinationStyleControl' => [ 'type' => 'string', 'enum' => [ 'PASSTHROUGH', 'USE_CONFIGURED', ], ], 'UdpContainerSettings' => [ 'type' => 'structure', 'members' => [ 'M2tsSettings' => [ 'shape' => 'M2tsSettings', 'locationName' => 'm2tsSettings', ], ], ], 'UdpGroupSettings' => [ 'type' => 'structure', 'members' => [ 'InputLossAction' => [ 'shape' => 'InputLossActionForUdpOut', 'locationName' => 'inputLossAction', ], 'TimedMetadataId3Frame' => [ 'shape' => 'UdpTimedMetadataId3Frame', 'locationName' => 'timedMetadataId3Frame', ], 'TimedMetadataId3Period' => [ 'shape' => '__integerMin0', 'locationName' => 'timedMetadataId3Period', ], ], ], 'UdpOutputSettings' => [ 'type' => 'structure', 'members' => [ 'BufferMsec' => [ 'shape' => '__integerMin0Max10000', 'locationName' => 'bufferMsec', ], 'ContainerSettings' => [ 'shape' => 'UdpContainerSettings', 'locationName' => 'containerSettings', ], 'Destination' => [ 'shape' => 'OutputLocationRef', 'locationName' => 'destination', ], 'FecOutputSettings' => [ 'shape' => 'FecOutputSettings', 'locationName' => 'fecOutputSettings', ], ], 'required' => [ 'Destination', 'ContainerSettings', ], ], 'UdpTimedMetadataId3Frame' => [ 'type' => 'string', 'enum' => [ 'NONE', 'PRIV', 'TDRL', ], ], 'UnprocessableEntityException' => [ 'type' => 'structure', 'members' => [ 'ElementPath' => [ 'shape' => '__string', 'locationName' => 'elementPath', ], 'ErrorMessage' => [ 'shape' => '__string', 'locationName' => 'errorMessage', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 422, ], ], 'UpdateAccountConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'AccountConfiguration' => [ 'shape' => 'AccountConfiguration', 'locationName' => 'accountConfiguration', ], ], ], 'UpdateAccountConfigurationRequestModel' => [ 'type' => 'structure', 'members' => [ 'AccountConfiguration' => [ 'shape' => 'AccountConfiguration', 'locationName' => 'accountConfiguration', ], ], ], 'UpdateAccountConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'AccountConfiguration' => [ 'shape' => 'AccountConfiguration', 'locationName' => 'accountConfiguration', ], ], ], 'UpdateAccountConfigurationResultModel' => [ 'type' => 'structure', 'members' => [ 'AccountConfiguration' => [ 'shape' => 'AccountConfiguration', 'locationName' => 'accountConfiguration', ], ], ], 'UpdateChannel' => [ 'type' => 'structure', 'members' => [ 'CdiInputSpecification' => [ 'shape' => 'CdiInputSpecification', 'locationName' => 'cdiInputSpecification', ], 'Destinations' => [ 'shape' => '__listOfOutputDestination', 'locationName' => 'destinations', ], 'EncoderSettings' => [ 'shape' => 'EncoderSettings', 'locationName' => 'encoderSettings', ], 'InputAttachments' => [ 'shape' => '__listOfInputAttachment', 'locationName' => 'inputAttachments', ], 'InputSpecification' => [ 'shape' => 'InputSpecification', 'locationName' => 'inputSpecification', ], 'LogLevel' => [ 'shape' => 'LogLevel', 'locationName' => 'logLevel', ], 'Maintenance' => [ 'shape' => 'MaintenanceUpdateSettings', 'locationName' => 'maintenance', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'RoleArn' => [ 'shape' => '__string', 'locationName' => 'roleArn', ], 'ChannelEngineVersion' => [ 'shape' => 'ChannelEngineVersionRequest', 'locationName' => 'channelEngineVersion', ], 'DryRun' => [ 'shape' => '__boolean', 'locationName' => 'dryRun', ], 'AnywhereSettings' => [ 'shape' => 'AnywhereSettings', 'locationName' => 'anywhereSettings', ], ], ], 'UpdateChannelClass' => [ 'type' => 'structure', 'members' => [ 'ChannelClass' => [ 'shape' => 'ChannelClass', 'locationName' => 'channelClass', ], 'Destinations' => [ 'shape' => '__listOfOutputDestination', 'locationName' => 'destinations', ], ], 'required' => [ 'ChannelClass', ], ], 'UpdateChannelClassRequest' => [ 'type' => 'structure', 'members' => [ 'ChannelClass' => [ 'shape' => 'ChannelClass', 'locationName' => 'channelClass', ], 'ChannelId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'channelId', ], 'Destinations' => [ 'shape' => '__listOfOutputDestination', 'locationName' => 'destinations', ], ], 'required' => [ 'ChannelId', 'ChannelClass', ], ], 'UpdateChannelClassResponse' => [ 'type' => 'structure', 'members' => [ 'Channel' => [ 'shape' => 'Channel', 'locationName' => 'channel', ], ], ], 'UpdateChannelRequest' => [ 'type' => 'structure', 'members' => [ 'CdiInputSpecification' => [ 'shape' => 'CdiInputSpecification', 'locationName' => 'cdiInputSpecification', ], 'ChannelId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'channelId', ], 'Destinations' => [ 'shape' => '__listOfOutputDestination', 'locationName' => 'destinations', ], 'EncoderSettings' => [ 'shape' => 'EncoderSettings', 'locationName' => 'encoderSettings', ], 'InputAttachments' => [ 'shape' => '__listOfInputAttachment', 'locationName' => 'inputAttachments', ], 'InputSpecification' => [ 'shape' => 'InputSpecification', 'locationName' => 'inputSpecification', ], 'LogLevel' => [ 'shape' => 'LogLevel', 'locationName' => 'logLevel', ], 'Maintenance' => [ 'shape' => 'MaintenanceUpdateSettings', 'locationName' => 'maintenance', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'RoleArn' => [ 'shape' => '__string', 'locationName' => 'roleArn', ], 'ChannelEngineVersion' => [ 'shape' => 'ChannelEngineVersionRequest', 'locationName' => 'channelEngineVersion', ], 'DryRun' => [ 'shape' => '__boolean', 'locationName' => 'dryRun', ], 'AnywhereSettings' => [ 'shape' => 'AnywhereSettings', 'locationName' => 'anywhereSettings', ], ], 'required' => [ 'ChannelId', ], ], 'UpdateChannelResponse' => [ 'type' => 'structure', 'members' => [ 'Channel' => [ 'shape' => 'Channel', 'locationName' => 'channel', ], ], ], 'UpdateChannelResultModel' => [ 'type' => 'structure', 'members' => [ 'Channel' => [ 'shape' => 'Channel', 'locationName' => 'channel', ], ], ], 'UpdateInput' => [ 'type' => 'structure', 'members' => [ 'Destinations' => [ 'shape' => '__listOfInputDestinationRequest', 'locationName' => 'destinations', ], 'InputDevices' => [ 'shape' => '__listOfInputDeviceRequest', 'locationName' => 'inputDevices', ], 'InputSecurityGroups' => [ 'shape' => '__listOf__string', 'locationName' => 'inputSecurityGroups', ], 'MediaConnectFlows' => [ 'shape' => '__listOfMediaConnectFlowRequest', 'locationName' => 'mediaConnectFlows', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'RoleArn' => [ 'shape' => '__string', 'locationName' => 'roleArn', ], 'Sources' => [ 'shape' => '__listOfInputSourceRequest', 'locationName' => 'sources', ], 'SrtSettings' => [ 'shape' => 'SrtSettingsRequest', 'locationName' => 'srtSettings', ], 'MulticastSettings' => [ 'shape' => 'MulticastSettingsUpdateRequest', 'locationName' => 'multicastSettings', ], 'Smpte2110ReceiverGroupSettings' => [ 'shape' => 'Smpte2110ReceiverGroupSettings', 'locationName' => 'smpte2110ReceiverGroupSettings', ], 'SdiSources' => [ 'shape' => 'InputSdiSources', 'locationName' => 'sdiSources', ], ], ], 'UpdateInputDevice' => [ 'type' => 'structure', 'members' => [ 'HdDeviceSettings' => [ 'shape' => 'InputDeviceConfigurableSettings', 'locationName' => 'hdDeviceSettings', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'UhdDeviceSettings' => [ 'shape' => 'InputDeviceConfigurableSettings', 'locationName' => 'uhdDeviceSettings', ], 'AvailabilityZone' => [ 'shape' => '__string', 'locationName' => 'availabilityZone', ], ], ], 'UpdateInputDeviceRequest' => [ 'type' => 'structure', 'members' => [ 'HdDeviceSettings' => [ 'shape' => 'InputDeviceConfigurableSettings', 'locationName' => 'hdDeviceSettings', ], 'InputDeviceId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'inputDeviceId', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'UhdDeviceSettings' => [ 'shape' => 'InputDeviceConfigurableSettings', 'locationName' => 'uhdDeviceSettings', ], 'AvailabilityZone' => [ 'shape' => '__string', 'locationName' => 'availabilityZone', ], ], 'required' => [ 'InputDeviceId', ], ], 'UpdateInputDeviceResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'ConnectionState' => [ 'shape' => 'InputDeviceConnectionState', 'locationName' => 'connectionState', ], 'DeviceSettingsSyncState' => [ 'shape' => 'DeviceSettingsSyncState', 'locationName' => 'deviceSettingsSyncState', ], 'DeviceUpdateStatus' => [ 'shape' => 'DeviceUpdateStatus', 'locationName' => 'deviceUpdateStatus', ], 'HdDeviceSettings' => [ 'shape' => 'InputDeviceHdSettings', 'locationName' => 'hdDeviceSettings', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'MacAddress' => [ 'shape' => '__string', 'locationName' => 'macAddress', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NetworkSettings' => [ 'shape' => 'InputDeviceNetworkSettings', 'locationName' => 'networkSettings', ], 'SerialNumber' => [ 'shape' => '__string', 'locationName' => 'serialNumber', ], 'Type' => [ 'shape' => 'InputDeviceType', 'locationName' => 'type', ], 'UhdDeviceSettings' => [ 'shape' => 'InputDeviceUhdSettings', 'locationName' => 'uhdDeviceSettings', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'AvailabilityZone' => [ 'shape' => '__string', 'locationName' => 'availabilityZone', ], 'MedialiveInputArns' => [ 'shape' => '__listOf__string', 'locationName' => 'medialiveInputArns', ], 'OutputType' => [ 'shape' => 'InputDeviceOutputType', 'locationName' => 'outputType', ], ], ], 'UpdateInputRequest' => [ 'type' => 'structure', 'members' => [ 'Destinations' => [ 'shape' => '__listOfInputDestinationRequest', 'locationName' => 'destinations', ], 'InputDevices' => [ 'shape' => '__listOfInputDeviceRequest', 'locationName' => 'inputDevices', ], 'InputId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'inputId', ], 'InputSecurityGroups' => [ 'shape' => '__listOf__string', 'locationName' => 'inputSecurityGroups', ], 'MediaConnectFlows' => [ 'shape' => '__listOfMediaConnectFlowRequest', 'locationName' => 'mediaConnectFlows', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'RoleArn' => [ 'shape' => '__string', 'locationName' => 'roleArn', ], 'Sources' => [ 'shape' => '__listOfInputSourceRequest', 'locationName' => 'sources', ], 'SrtSettings' => [ 'shape' => 'SrtSettingsRequest', 'locationName' => 'srtSettings', ], 'MulticastSettings' => [ 'shape' => 'MulticastSettingsUpdateRequest', 'locationName' => 'multicastSettings', ], 'Smpte2110ReceiverGroupSettings' => [ 'shape' => 'Smpte2110ReceiverGroupSettings', 'locationName' => 'smpte2110ReceiverGroupSettings', ], 'SdiSources' => [ 'shape' => 'InputSdiSources', 'locationName' => 'sdiSources', ], ], 'required' => [ 'InputId', ], ], 'UpdateInputResponse' => [ 'type' => 'structure', 'members' => [ 'Input' => [ 'shape' => 'Input', 'locationName' => 'input', ], ], ], 'UpdateInputResultModel' => [ 'type' => 'structure', 'members' => [ 'Input' => [ 'shape' => 'Input', 'locationName' => 'input', ], ], ], 'UpdateInputSecurityGroupRequest' => [ 'type' => 'structure', 'members' => [ 'InputSecurityGroupId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'inputSecurityGroupId', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', 'deprecated' => true, 'deprecatedMessage' => 'This API is deprecated. You must use UpdateTagsForResource instead.', 'deprecatedSince' => '2024-11-20', ], 'WhitelistRules' => [ 'shape' => '__listOfInputWhitelistRuleCidr', 'locationName' => 'whitelistRules', ], ], 'required' => [ 'InputSecurityGroupId', ], ], 'UpdateInputSecurityGroupResponse' => [ 'type' => 'structure', 'members' => [ 'SecurityGroup' => [ 'shape' => 'InputSecurityGroup', 'locationName' => 'securityGroup', ], ], ], 'UpdateInputSecurityGroupResultModel' => [ 'type' => 'structure', 'members' => [ 'SecurityGroup' => [ 'shape' => 'InputSecurityGroup', 'locationName' => 'securityGroup', ], ], ], 'UpdateMultiplex' => [ 'type' => 'structure', 'members' => [ 'MultiplexSettings' => [ 'shape' => 'MultiplexSettings', 'locationName' => 'multiplexSettings', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'PacketIdentifiersMapping' => [ 'shape' => 'MultiplexPacketIdentifiersMapping', 'locationName' => 'packetIdentifiersMapping', ], ], ], 'UpdateMultiplexProgram' => [ 'type' => 'structure', 'members' => [ 'MultiplexProgramSettings' => [ 'shape' => 'MultiplexProgramSettings', 'locationName' => 'multiplexProgramSettings', ], ], ], 'UpdateMultiplexProgramRequest' => [ 'type' => 'structure', 'members' => [ 'MultiplexId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'multiplexId', ], 'MultiplexProgramSettings' => [ 'shape' => 'MultiplexProgramSettings', 'locationName' => 'multiplexProgramSettings', ], 'ProgramName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'programName', ], ], 'required' => [ 'MultiplexId', 'ProgramName', ], ], 'UpdateMultiplexProgramResponse' => [ 'type' => 'structure', 'members' => [ 'MultiplexProgram' => [ 'shape' => 'MultiplexProgram', 'locationName' => 'multiplexProgram', ], ], ], 'UpdateMultiplexProgramResultModel' => [ 'type' => 'structure', 'members' => [ 'MultiplexProgram' => [ 'shape' => 'MultiplexProgram', 'locationName' => 'multiplexProgram', ], ], ], 'UpdateMultiplexRequest' => [ 'type' => 'structure', 'members' => [ 'MultiplexId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'multiplexId', ], 'MultiplexSettings' => [ 'shape' => 'MultiplexSettings', 'locationName' => 'multiplexSettings', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'PacketIdentifiersMapping' => [ 'shape' => 'MultiplexPacketIdentifiersMapping', 'locationName' => 'packetIdentifiersMapping', ], ], 'required' => [ 'MultiplexId', ], ], 'UpdateMultiplexResponse' => [ 'type' => 'structure', 'members' => [ 'Multiplex' => [ 'shape' => 'Multiplex', 'locationName' => 'multiplex', ], ], ], 'UpdateMultiplexResultModel' => [ 'type' => 'structure', 'members' => [ 'Multiplex' => [ 'shape' => 'Multiplex', 'locationName' => 'multiplex', ], ], ], 'UpdateReservation' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'RenewalSettings' => [ 'shape' => 'RenewalSettings', 'locationName' => 'renewalSettings', ], ], ], 'UpdateReservationRequest' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'RenewalSettings' => [ 'shape' => 'RenewalSettings', 'locationName' => 'renewalSettings', ], 'ReservationId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'reservationId', ], ], 'required' => [ 'ReservationId', ], ], 'UpdateReservationResponse' => [ 'type' => 'structure', 'members' => [ 'Reservation' => [ 'shape' => 'Reservation', 'locationName' => 'reservation', ], ], ], 'UpdateReservationResultModel' => [ 'type' => 'structure', 'members' => [ 'Reservation' => [ 'shape' => 'Reservation', 'locationName' => 'reservation', ], ], ], 'ValidationError' => [ 'type' => 'structure', 'members' => [ 'ElementPath' => [ 'shape' => '__string', 'locationName' => 'elementPath', ], 'ErrorMessage' => [ 'shape' => '__string', 'locationName' => 'errorMessage', ], ], ], 'VideoBlackFailoverSettings' => [ 'type' => 'structure', 'members' => [ 'BlackDetectThreshold' => [ 'shape' => '__doubleMin0Max1', 'locationName' => 'blackDetectThreshold', ], 'VideoBlackThresholdMsec' => [ 'shape' => '__integerMin1000', 'locationName' => 'videoBlackThresholdMsec', ], ], ], 'VideoCodecSettings' => [ 'type' => 'structure', 'members' => [ 'FrameCaptureSettings' => [ 'shape' => 'FrameCaptureSettings', 'locationName' => 'frameCaptureSettings', ], 'H264Settings' => [ 'shape' => 'H264Settings', 'locationName' => 'h264Settings', ], 'H265Settings' => [ 'shape' => 'H265Settings', 'locationName' => 'h265Settings', ], 'Mpeg2Settings' => [ 'shape' => 'Mpeg2Settings', 'locationName' => 'mpeg2Settings', ], 'Av1Settings' => [ 'shape' => 'Av1Settings', 'locationName' => 'av1Settings', ], ], ], 'VideoDescription' => [ 'type' => 'structure', 'members' => [ 'CodecSettings' => [ 'shape' => 'VideoCodecSettings', 'locationName' => 'codecSettings', ], 'Height' => [ 'shape' => '__integer', 'locationName' => 'height', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'RespondToAfd' => [ 'shape' => 'VideoDescriptionRespondToAfd', 'locationName' => 'respondToAfd', ], 'ScalingBehavior' => [ 'shape' => 'VideoDescriptionScalingBehavior', 'locationName' => 'scalingBehavior', ], 'Sharpness' => [ 'shape' => '__integerMin0Max100', 'locationName' => 'sharpness', ], 'Width' => [ 'shape' => '__integer', 'locationName' => 'width', ], ], 'required' => [ 'Name', ], ], 'VideoDescriptionRespondToAfd' => [ 'type' => 'string', 'enum' => [ 'NONE', 'PASSTHROUGH', 'RESPOND', ], ], 'VideoDescriptionScalingBehavior' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'STRETCH_TO_OUTPUT', ], ], 'VideoSelector' => [ 'type' => 'structure', 'members' => [ 'ColorSpace' => [ 'shape' => 'VideoSelectorColorSpace', 'locationName' => 'colorSpace', ], 'ColorSpaceSettings' => [ 'shape' => 'VideoSelectorColorSpaceSettings', 'locationName' => 'colorSpaceSettings', ], 'ColorSpaceUsage' => [ 'shape' => 'VideoSelectorColorSpaceUsage', 'locationName' => 'colorSpaceUsage', ], 'SelectorSettings' => [ 'shape' => 'VideoSelectorSettings', 'locationName' => 'selectorSettings', ], ], ], 'VideoSelectorColorSpace' => [ 'type' => 'string', 'enum' => [ 'FOLLOW', 'HDR10', 'HLG_2020', 'REC_601', 'REC_709', ], ], 'VideoSelectorColorSpaceSettings' => [ 'type' => 'structure', 'members' => [ 'Hdr10Settings' => [ 'shape' => 'Hdr10Settings', 'locationName' => 'hdr10Settings', ], ], ], 'VideoSelectorColorSpaceUsage' => [ 'type' => 'string', 'enum' => [ 'FALLBACK', 'FORCE', ], ], 'VideoSelectorPid' => [ 'type' => 'structure', 'members' => [ 'Pid' => [ 'shape' => '__integerMin0Max8191', 'locationName' => 'pid', ], ], ], 'VideoSelectorProgramId' => [ 'type' => 'structure', 'members' => [ 'ProgramId' => [ 'shape' => '__integerMin0Max65536', 'locationName' => 'programId', ], ], ], 'VideoSelectorSettings' => [ 'type' => 'structure', 'members' => [ 'VideoSelectorPid' => [ 'shape' => 'VideoSelectorPid', 'locationName' => 'videoSelectorPid', ], 'VideoSelectorProgramId' => [ 'shape' => 'VideoSelectorProgramId', 'locationName' => 'videoSelectorProgramId', ], ], ], 'VpcOutputSettings' => [ 'type' => 'structure', 'members' => [ 'PublicAddressAllocationIds' => [ 'shape' => '__listOf__string', 'locationName' => 'publicAddressAllocationIds', ], 'SecurityGroupIds' => [ 'shape' => '__listOf__string', 'locationName' => 'securityGroupIds', ], 'SubnetIds' => [ 'shape' => '__listOf__string', 'locationName' => 'subnetIds', ], ], 'required' => [ 'SubnetIds', ], ], 'VpcOutputSettingsDescription' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZones' => [ 'shape' => '__listOf__string', 'locationName' => 'availabilityZones', ], 'NetworkInterfaceIds' => [ 'shape' => '__listOf__string', 'locationName' => 'networkInterfaceIds', ], 'SecurityGroupIds' => [ 'shape' => '__listOf__string', 'locationName' => 'securityGroupIds', ], 'SubnetIds' => [ 'shape' => '__listOf__string', 'locationName' => 'subnetIds', ], ], ], 'WavCodingMode' => [ 'type' => 'string', 'enum' => [ 'CODING_MODE_1_0', 'CODING_MODE_2_0', 'CODING_MODE_4_0', 'CODING_MODE_8_0', ], ], 'WavSettings' => [ 'type' => 'structure', 'members' => [ 'BitDepth' => [ 'shape' => '__double', 'locationName' => 'bitDepth', ], 'CodingMode' => [ 'shape' => 'WavCodingMode', 'locationName' => 'codingMode', ], 'SampleRate' => [ 'shape' => '__double', 'locationName' => 'sampleRate', ], ], ], 'WebvttDestinationSettings' => [ 'type' => 'structure', 'members' => [ 'StyleControl' => [ 'shape' => 'WebvttDestinationStyleControl', 'locationName' => 'styleControl', ], ], ], 'WebvttDestinationStyleControl' => [ 'type' => 'string', 'enum' => [ 'NO_STYLE_DATA', 'PASSTHROUGH', ], ], '__boolean' => [ 'type' => 'boolean', ], '__double' => [ 'type' => 'double', ], '__doubleMin0' => [ 'type' => 'double', ], '__doubleMin0Max1' => [ 'type' => 'double', ], '__doubleMin0Max100' => [ 'type' => 'double', ], '__doubleMin0Max5000' => [ 'type' => 'double', ], '__doubleMin1' => [ 'type' => 'double', ], '__doubleMin1Max65535' => [ 'type' => 'double', ], '__doubleMin250Max5000' => [ 'type' => 'double', ], '__doubleMin32Max46' => [ 'type' => 'double', ], '__doubleMinNegative1Max5' => [ 'type' => 'double', ], '__doubleMinNegative59Max0' => [ 'type' => 'double', ], '__integer' => [ 'type' => 'integer', ], '__integerMin0' => [ 'type' => 'integer', 'min' => 0, ], '__integerMin0Max10' => [ 'type' => 'integer', 'min' => 0, 'max' => 10, ], '__integerMin0Max100' => [ 'type' => 'integer', 'min' => 0, 'max' => 100, ], '__integerMin0Max1000' => [ 'type' => 'integer', 'min' => 0, 'max' => 1000, ], '__integerMin0Max10000' => [ 'type' => 'integer', 'min' => 0, 'max' => 10000, ], '__integerMin0Max1000000' => [ 'type' => 'integer', 'min' => 0, 'max' => 1000000, ], '__integerMin0Max100000000' => [ 'type' => 'integer', 'min' => 0, 'max' => 100000000, ], '__integerMin0Max128' => [ 'type' => 'integer', 'min' => 0, 'max' => 128, ], '__integerMin0Max15' => [ 'type' => 'integer', 'min' => 0, 'max' => 15, ], '__integerMin0Max2000' => [ 'type' => 'integer', 'min' => 0, 'max' => 2000, ], '__integerMin0Max255' => [ 'type' => 'integer', 'min' => 0, 'max' => 255, ], '__integerMin0Max30' => [ 'type' => 'integer', 'min' => 0, 'max' => 30, ], '__integerMin0Max32768' => [ 'type' => 'integer', 'min' => 0, 'max' => 32768, ], '__integerMin0Max3600' => [ 'type' => 'integer', 'min' => 0, 'max' => 3600, ], '__integerMin0Max500' => [ 'type' => 'integer', 'min' => 0, 'max' => 500, ], '__integerMin0Max600' => [ 'type' => 'integer', 'min' => 0, 'max' => 600, ], '__integerMin0Max65535' => [ 'type' => 'integer', 'min' => 0, 'max' => 65535, ], '__integerMin0Max65536' => [ 'type' => 'integer', 'min' => 0, 'max' => 65536, ], '__integerMin0Max7' => [ 'type' => 'integer', 'min' => 0, 'max' => 7, ], '__integerMin0Max8191' => [ 'type' => 'integer', 'min' => 0, 'max' => 8191, ], '__integerMin1' => [ 'type' => 'integer', 'min' => 1, ], '__integerMin100' => [ 'type' => 'integer', 'min' => 100, ], '__integerMin1000' => [ 'type' => 'integer', 'min' => 1000, ], '__integerMin1000000Max100000000' => [ 'type' => 'integer', 'min' => 1000000, 'max' => 100000000, ], '__integerMin100000Max100000000' => [ 'type' => 'integer', 'min' => 100000, 'max' => 100000000, ], '__integerMin100000Max40000000' => [ 'type' => 'integer', 'min' => 100000, 'max' => 40000000, ], '__integerMin100000Max80000000' => [ 'type' => 'integer', 'min' => 100000, 'max' => 80000000, ], '__integerMin1000Max30000' => [ 'type' => 'integer', 'min' => 1000, 'max' => 30000, ], '__integerMin1Max10' => [ 'type' => 'integer', 'min' => 1, 'max' => 10, ], '__integerMin1Max1000000' => [ 'type' => 'integer', 'min' => 1, 'max' => 1000000, ], '__integerMin1Max16' => [ 'type' => 'integer', 'min' => 1, 'max' => 16, ], '__integerMin1Max20' => [ 'type' => 'integer', 'min' => 1, 'max' => 20, ], '__integerMin1Max3003' => [ 'type' => 'integer', 'min' => 1, 'max' => 3003, ], '__integerMin1Max31' => [ 'type' => 'integer', 'min' => 1, 'max' => 31, ], '__integerMin1Max32' => [ 'type' => 'integer', 'min' => 1, 'max' => 32, ], '__integerMin1Max3600000' => [ 'type' => 'integer', 'min' => 1, 'max' => 3600000, ], '__integerMin1Max4' => [ 'type' => 'integer', 'min' => 1, 'max' => 4, ], '__integerMin1Max5' => [ 'type' => 'integer', 'min' => 1, 'max' => 5, ], '__integerMin1Max6' => [ 'type' => 'integer', 'min' => 1, 'max' => 6, ], '__integerMin1Max8' => [ 'type' => 'integer', 'min' => 1, 'max' => 8, ], '__integerMin25Max10000' => [ 'type' => 'integer', 'min' => 25, 'max' => 10000, ], '__integerMin25Max2000' => [ 'type' => 'integer', 'min' => 25, 'max' => 2000, ], '__integerMin3' => [ 'type' => 'integer', 'min' => 3, ], '__integerMin30' => [ 'type' => 'integer', 'min' => 30, ], '__integerMin32Max8191' => [ 'type' => 'integer', 'min' => 32, 'max' => 8191, ], '__integerMin4Max20' => [ 'type' => 'integer', 'min' => 4, 'max' => 20, ], '__integerMin800Max3000' => [ 'type' => 'integer', 'min' => 800, 'max' => 3000, ], '__integerMin96Max600' => [ 'type' => 'integer', 'min' => 96, 'max' => 600, ], '__integerMinNegative1000Max1000' => [ 'type' => 'integer', 'min' => -1000, 'max' => 1000, ], '__integerMinNegative5Max5' => [ 'type' => 'integer', 'min' => -5, 'max' => 5, ], '__integerMinNegative60Max6' => [ 'type' => 'integer', 'min' => -60, 'max' => 6, ], '__integerMinNegative60Max60' => [ 'type' => 'integer', 'min' => -60, 'max' => 60, ], '__listOfAudioChannelMapping' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudioChannelMapping', ], ], '__listOfAudioDescription' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudioDescription', ], ], '__listOfAudioSelector' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudioSelector', ], ], '__listOfAudioTrack' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudioTrack', ], ], '__listOfBatchFailedResultModel' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchFailedResultModel', ], ], '__listOfBatchSuccessfulResultModel' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchSuccessfulResultModel', ], ], '__listOfCaptionDescription' => [ 'type' => 'list', 'member' => [ 'shape' => 'CaptionDescription', ], ], '__listOfCaptionLanguageMapping' => [ 'type' => 'list', 'member' => [ 'shape' => 'CaptionLanguageMapping', ], ], '__listOfCaptionSelector' => [ 'type' => 'list', 'member' => [ 'shape' => 'CaptionSelector', ], ], '__listOfChannelEgressEndpoint' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelEgressEndpoint', ], ], '__listOfChannelSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelSummary', ], ], '__listOfColorCorrection' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColorCorrection', ], ], '__listOfFailoverCondition' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailoverCondition', ], ], '__listOfHlsAdMarkers' => [ 'type' => 'list', 'member' => [ 'shape' => 'HlsAdMarkers', ], ], '__listOfInput' => [ 'type' => 'list', 'member' => [ 'shape' => 'Input', ], ], '__listOfInputAttachment' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputAttachment', ], ], '__listOfInputChannelLevel' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputChannelLevel', ], ], '__listOfInputDestination' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputDestination', ], ], '__listOfInputDestinationRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputDestinationRequest', ], ], '__listOfInputDeviceRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputDeviceRequest', ], ], '__listOfInputDeviceSettings' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputDeviceSettings', ], ], '__listOfInputDeviceSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputDeviceSummary', ], ], '__listOfInputSecurityGroup' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputSecurityGroup', ], ], '__listOfInputSource' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputSource', ], ], '__listOfInputSourceRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputSourceRequest', ], ], '__listOfInputWhitelistRule' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputWhitelistRule', ], ], '__listOfInputWhitelistRuleCidr' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputWhitelistRuleCidr', ], ], '__listOfMediaConnectFlow' => [ 'type' => 'list', 'member' => [ 'shape' => 'MediaConnectFlow', ], ], '__listOfMediaConnectFlowRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'MediaConnectFlowRequest', ], ], '__listOfMediaPackageOutputDestinationSettings' => [ 'type' => 'list', 'member' => [ 'shape' => 'MediaPackageOutputDestinationSettings', ], ], '__listOfMultiplexOutputDestination' => [ 'type' => 'list', 'member' => [ 'shape' => 'MultiplexOutputDestination', ], ], '__listOfMultiplexProgramPipelineDetail' => [ 'type' => 'list', 'member' => [ 'shape' => 'MultiplexProgramPipelineDetail', ], ], '__listOfMultiplexProgramSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'MultiplexProgramSummary', ], ], '__listOfMultiplexSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'MultiplexSummary', ], ], '__listOfOffering' => [ 'type' => 'list', 'member' => [ 'shape' => 'Offering', ], ], '__listOfOutput' => [ 'type' => 'list', 'member' => [ 'shape' => 'Output', ], ], '__listOfOutputDestination' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputDestination', ], ], '__listOfOutputDestinationSettings' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputDestinationSettings', ], ], '__listOfOutputGroup' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputGroup', ], ], '__listOfPipelineDetail' => [ 'type' => 'list', 'member' => [ 'shape' => 'PipelineDetail', ], ], '__listOfPipelinePauseStateSettings' => [ 'type' => 'list', 'member' => [ 'shape' => 'PipelinePauseStateSettings', ], ], '__listOfReservation' => [ 'type' => 'list', 'member' => [ 'shape' => 'Reservation', ], ], '__listOfRtmpAdMarkers' => [ 'type' => 'list', 'member' => [ 'shape' => 'RtmpAdMarkers', ], ], '__listOfScheduleAction' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScheduleAction', ], ], '__listOfScte35Descriptor' => [ 'type' => 'list', 'member' => [ 'shape' => 'Scte35Descriptor', ], ], '__listOfThumbnail' => [ 'type' => 'list', 'member' => [ 'shape' => 'Thumbnail', ], ], '__listOfThumbnailDetail' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThumbnailDetail', ], ], '__listOfTransferringInputDeviceSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'TransferringInputDeviceSummary', ], ], '__listOfValidationError' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationError', ], ], '__listOfVideoDescription' => [ 'type' => 'list', 'member' => [ 'shape' => 'VideoDescription', ], ], '__listOf__integer' => [ 'type' => 'list', 'member' => [ 'shape' => '__integer', ], ], '__listOf__string' => [ 'type' => 'list', 'member' => [ 'shape' => '__string', ], ], '__long' => [ 'type' => 'long', ], '__longMin0Max1099511627775' => [ 'type' => 'long', 'min' => 0, 'max' => 1099511627775, ], '__longMin0Max4294967295' => [ 'type' => 'long', 'min' => 0, 'max' => 4294967295, ], '__longMin0Max8589934591' => [ 'type' => 'long', 'min' => 0, 'max' => 8589934591, ], '__longMin0Max86400000' => [ 'type' => 'long', 'min' => 0, 'max' => 86400000, ], '__string' => [ 'type' => 'string', ], '__stringMax1000' => [ 'type' => 'string', 'max' => 1000, ], '__stringMax2048' => [ 'type' => 'string', 'max' => 2048, ], '__stringMax255' => [ 'type' => 'string', 'max' => 255, ], '__stringMax256' => [ 'type' => 'string', 'max' => 256, ], '__stringMax32' => [ 'type' => 'string', 'max' => 32, ], '__stringMin1' => [ 'type' => 'string', 'min' => 1, ], '__stringMin1Max255' => [ 'type' => 'string', 'min' => 1, 'max' => 255, ], '__stringMin1Max256' => [ 'type' => 'string', 'min' => 1, 'max' => 256, ], '__stringMin1Max35' => [ 'type' => 'string', 'min' => 1, 'max' => 35, ], '__stringMin1Max7' => [ 'type' => 'string', 'min' => 1, 'max' => 7, ], '__stringMin2Max2' => [ 'type' => 'string', 'min' => 2, 'max' => 2, ], '__stringMin32Max32' => [ 'type' => 'string', 'min' => 32, 'max' => 32, ], '__stringMin34Max34' => [ 'type' => 'string', 'min' => 34, 'max' => 34, ], '__stringMin3Max3' => [ 'type' => 'string', 'min' => 3, 'max' => 3, ], '__stringMin6Max6' => [ 'type' => 'string', 'min' => 6, 'max' => 6, ], '__stringPattern010920300' => [ 'type' => 'string', 'pattern' => '^([0,1]?[0-9]|2[0-3]):00$', ], '__timestampIso8601' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], '__timestampUnix' => [ 'type' => 'timestamp', 'timestampFormat' => 'unixTimestamp', ], 'InputDeviceThumbnail' => [ 'type' => 'blob', 'streaming' => true, 'documentation' => 'The binary data for the thumbnail that the Link device has most recently sent to MediaLive.', ], 'AcceptHeader' => [ 'type' => 'string', 'enum' => [ 'image/jpeg', ], 'documentation' => 'The HTTP Accept header. Indicates the requested type fothe thumbnail.', ], 'ContentType' => [ 'type' => 'string', 'enum' => [ 'image/jpeg', ], 'documentation' => 'Specifies the media type of the thumbnail.', ], '__timestamp' => [ 'type' => 'timestamp', 'documentation' => 'Placeholder documentation for __timestamp', ], 'InputDeviceConfigurableAudioChannelPairConfig' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => '__integer', 'locationName' => 'id', ], 'Profile' => [ 'shape' => 'InputDeviceConfigurableAudioChannelPairProfile', 'locationName' => 'profile', ], ], ], 'InputDeviceConfigurableAudioChannelPairProfile' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'VBR-AAC_HHE-16000', 'VBR-AAC_HE-64000', 'VBR-AAC_LC-128000', 'CBR-AAC_HQ-192000', 'CBR-AAC_HQ-256000', 'CBR-AAC_HQ-384000', 'CBR-AAC_HQ-512000', ], ], 'InputDeviceUhdAudioChannelPairConfig' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => '__integer', 'locationName' => 'id', ], 'Profile' => [ 'shape' => 'InputDeviceUhdAudioChannelPairProfile', 'locationName' => 'profile', ], ], ], 'InputDeviceUhdAudioChannelPairProfile' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'VBR-AAC_HHE-16000', 'VBR-AAC_HE-64000', 'VBR-AAC_LC-128000', 'CBR-AAC_HQ-192000', 'CBR-AAC_HQ-256000', 'CBR-AAC_HQ-384000', 'CBR-AAC_HQ-512000', ], ], '__listOfInputDeviceConfigurableAudioChannelPairConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputDeviceConfigurableAudioChannelPairConfig', ], ], '__listOfInputDeviceUhdAudioChannelPairConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputDeviceUhdAudioChannelPairConfig', ], ], 'ChannelPipelineIdToRestart' => [ 'type' => 'string', 'enum' => [ 'PIPELINE_0', 'PIPELINE_1', ], ], 'RestartChannelPipelinesRequest' => [ 'type' => 'structure', 'members' => [ 'ChannelId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'channelId', ], 'PipelineIds' => [ 'shape' => '__listOfChannelPipelineIdToRestart', 'locationName' => 'pipelineIds', ], ], 'required' => [ 'ChannelId', ], ], 'RestartChannelPipelinesResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'CdiInputSpecification' => [ 'shape' => 'CdiInputSpecification', 'locationName' => 'cdiInputSpecification', ], 'ChannelClass' => [ 'shape' => 'ChannelClass', 'locationName' => 'channelClass', ], 'Destinations' => [ 'shape' => '__listOfOutputDestination', 'locationName' => 'destinations', ], 'EgressEndpoints' => [ 'shape' => '__listOfChannelEgressEndpoint', 'locationName' => 'egressEndpoints', ], 'EncoderSettings' => [ 'shape' => 'EncoderSettings', 'locationName' => 'encoderSettings', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'InputAttachments' => [ 'shape' => '__listOfInputAttachment', 'locationName' => 'inputAttachments', ], 'InputSpecification' => [ 'shape' => 'InputSpecification', 'locationName' => 'inputSpecification', ], 'LogLevel' => [ 'shape' => 'LogLevel', 'locationName' => 'logLevel', ], 'Maintenance' => [ 'shape' => 'MaintenanceStatus', 'locationName' => 'maintenance', ], 'MaintenanceStatus' => [ 'shape' => '__string', 'locationName' => 'maintenanceStatus', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'PipelineDetails' => [ 'shape' => '__listOfPipelineDetail', 'locationName' => 'pipelineDetails', ], 'PipelinesRunningCount' => [ 'shape' => '__integer', 'locationName' => 'pipelinesRunningCount', ], 'RoleArn' => [ 'shape' => '__string', 'locationName' => 'roleArn', ], 'State' => [ 'shape' => 'ChannelState', 'locationName' => 'state', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'Vpc' => [ 'shape' => 'VpcOutputSettingsDescription', 'locationName' => 'vpc', ], 'AnywhereSettings' => [ 'shape' => 'DescribeAnywhereSettings', 'locationName' => 'anywhereSettings', ], 'ChannelEngineVersion' => [ 'shape' => 'ChannelEngineVersionResponse', 'locationName' => 'channelEngineVersion', ], ], ], '__listOfChannelPipelineIdToRestart' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelPipelineIdToRestart', ], ], 'H265MvOverPictureBoundaries' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H265MvTemporalPredictor' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H265TilePadding' => [ 'type' => 'string', 'enum' => [ 'NONE', 'PADDED', ], ], 'H265TreeblockSize' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'TREE_SIZE_32X32', ], ], '__integerMin256Max3840' => [ 'type' => 'integer', 'min' => 256, 'max' => 3840, ], '__integerMin64Max2160' => [ 'type' => 'integer', 'min' => 64, 'max' => 2160, ], 'CmafIngestGroupSettings' => [ 'type' => 'structure', 'members' => [ 'Destination' => [ 'shape' => 'OutputLocationRef', 'locationName' => 'destination', ], 'NielsenId3Behavior' => [ 'shape' => 'CmafNielsenId3Behavior', 'locationName' => 'nielsenId3Behavior', ], 'Scte35Type' => [ 'shape' => 'Scte35Type', 'locationName' => 'scte35Type', ], 'SegmentLength' => [ 'shape' => '__integerMin1', 'locationName' => 'segmentLength', ], 'SegmentLengthUnits' => [ 'shape' => 'CmafIngestSegmentLengthUnits', 'locationName' => 'segmentLengthUnits', ], 'SendDelayMs' => [ 'shape' => '__integerMin0Max2000', 'locationName' => 'sendDelayMs', ], 'KlvBehavior' => [ 'shape' => 'CmafKLVBehavior', 'locationName' => 'klvBehavior', ], 'KlvNameModifier' => [ 'shape' => '__stringMax100', 'locationName' => 'klvNameModifier', ], 'NielsenId3NameModifier' => [ 'shape' => '__stringMax100', 'locationName' => 'nielsenId3NameModifier', ], 'Scte35NameModifier' => [ 'shape' => '__stringMax100', 'locationName' => 'scte35NameModifier', ], 'Id3Behavior' => [ 'shape' => 'CmafId3Behavior', 'locationName' => 'id3Behavior', ], 'Id3NameModifier' => [ 'shape' => '__stringMax100', 'locationName' => 'id3NameModifier', ], 'CaptionLanguageMappings' => [ 'shape' => '__listOfCmafIngestCaptionLanguageMapping', 'locationName' => 'captionLanguageMappings', ], 'TimedMetadataId3Frame' => [ 'shape' => 'CmafTimedMetadataId3Frame', 'locationName' => 'timedMetadataId3Frame', ], 'TimedMetadataId3Period' => [ 'shape' => '__integerMin0Max10000', 'locationName' => 'timedMetadataId3Period', ], 'TimedMetadataPassthrough' => [ 'shape' => 'CmafTimedMetadataPassthrough', 'locationName' => 'timedMetadataPassthrough', ], ], 'required' => [ 'Destination', ], ], 'CmafIngestOutputSettings' => [ 'type' => 'structure', 'members' => [ 'NameModifier' => [ 'shape' => '__string', 'locationName' => 'nameModifier', ], ], ], 'CmafIngestSegmentLengthUnits' => [ 'type' => 'string', 'enum' => [ 'MILLISECONDS', 'SECONDS', ], ], 'CmafNielsenId3Behavior' => [ 'type' => 'string', 'enum' => [ 'NO_PASSTHROUGH', 'PASSTHROUGH', ], ], 'DashRoleAudio' => [ 'type' => 'string', 'enum' => [ 'ALTERNATE', 'COMMENTARY', 'DESCRIPTION', 'DUB', 'EMERGENCY', 'ENHANCED-AUDIO-INTELLIGIBILITY', 'KARAOKE', 'MAIN', 'SUPPLEMENTARY', ], ], 'DashRoleCaption' => [ 'type' => 'string', 'enum' => [ 'ALTERNATE', 'CAPTION', 'COMMENTARY', 'DESCRIPTION', 'DUB', 'EASYREADER', 'EMERGENCY', 'FORCED-SUBTITLE', 'KARAOKE', 'MAIN', 'METADATA', 'SUBTITLE', 'SUPPLEMENTARY', ], ], 'DvbDashAccessibility' => [ 'type' => 'string', 'enum' => [ 'DVBDASH_1_VISUALLY_IMPAIRED', 'DVBDASH_2_HARD_OF_HEARING', 'DVBDASH_3_SUPPLEMENTAL_COMMENTARY', 'DVBDASH_4_DIRECTORS_COMMENTARY', 'DVBDASH_5_EDUCATIONAL_NOTES', 'DVBDASH_6_MAIN_PROGRAM', 'DVBDASH_7_CLEAN_FEED', ], ], '__listOfDashRoleAudio' => [ 'type' => 'list', 'member' => [ 'shape' => 'DashRoleAudio', ], ], '__listOfDashRoleCaption' => [ 'type' => 'list', 'member' => [ 'shape' => 'DashRoleCaption', ], ], 'Scte35Type' => [ 'type' => 'string', 'enum' => [ 'NONE', 'SCTE_35_WITHOUT_SEGMENTATION', ], ], 'BadRequestExceptionResponseContent' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], ], 'CloudWatchAlarmTemplateComparisonOperator' => [ 'type' => 'string', 'enum' => [ 'GreaterThanOrEqualToThreshold', 'GreaterThanThreshold', 'LessThanThreshold', 'LessThanOrEqualToThreshold', ], ], 'CloudWatchAlarmTemplateGroupSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveCloudwatchAlarmTemplateGroup', 'locationName' => 'arn', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'TemplateCount' => [ 'shape' => '__integer', 'locationName' => 'templateCount', ], ], 'required' => [ 'TemplateCount', 'CreatedAt', 'Id', 'Arn', 'Name', ], ], 'CloudWatchAlarmTemplateStatistic' => [ 'type' => 'string', 'enum' => [ 'SampleCount', 'Average', 'Sum', 'Minimum', 'Maximum', ], ], 'CloudWatchAlarmTemplateSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveCloudwatchAlarmTemplate', 'locationName' => 'arn', ], 'ComparisonOperator' => [ 'shape' => 'CloudWatchAlarmTemplateComparisonOperator', 'locationName' => 'comparisonOperator', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'DatapointsToAlarm' => [ 'shape' => '__integerMin1', 'locationName' => 'datapointsToAlarm', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'EvaluationPeriods' => [ 'shape' => '__integerMin1', 'locationName' => 'evaluationPeriods', ], 'GroupId' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'groupId', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'MetricName' => [ 'shape' => '__stringMax64', 'locationName' => 'metricName', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Period' => [ 'shape' => '__integerMin10Max86400', 'locationName' => 'period', ], 'Statistic' => [ 'shape' => 'CloudWatchAlarmTemplateStatistic', 'locationName' => 'statistic', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'TargetResourceType' => [ 'shape' => 'CloudWatchAlarmTemplateTargetResourceType', 'locationName' => 'targetResourceType', ], 'Threshold' => [ 'shape' => '__double', 'locationName' => 'threshold', ], 'TreatMissingData' => [ 'shape' => 'CloudWatchAlarmTemplateTreatMissingData', 'locationName' => 'treatMissingData', ], ], 'required' => [ 'TargetResourceType', 'TreatMissingData', 'ComparisonOperator', 'CreatedAt', 'Period', 'EvaluationPeriods', 'Name', 'GroupId', 'MetricName', 'Statistic', 'Id', 'Arn', 'Threshold', ], ], 'CloudWatchAlarmTemplateTargetResourceType' => [ 'type' => 'string', 'enum' => [ 'CLOUDFRONT_DISTRIBUTION', 'MEDIALIVE_MULTIPLEX', 'MEDIALIVE_CHANNEL', 'MEDIALIVE_INPUT_DEVICE', 'MEDIAPACKAGE_CHANNEL', 'MEDIAPACKAGE_ORIGIN_ENDPOINT', 'MEDIACONNECT_FLOW', 'S3_BUCKET', 'MEDIATAILOR_PLAYBACK_CONFIGURATION', ], ], 'CloudWatchAlarmTemplateTreatMissingData' => [ 'type' => 'string', 'enum' => [ 'notBreaching', 'breaching', 'ignore', 'missing', ], ], 'ConflictExceptionResponseContent' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], ], 'CreateCloudWatchAlarmTemplateGroupRequest' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'RequestId' => [ 'shape' => '__stringMin1Max256PatternS', 'locationName' => 'requestId', 'idempotencyToken' => true, ], ], 'required' => [ 'Name', ], ], 'CreateCloudWatchAlarmTemplateGroupRequestContent' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'RequestId' => [ 'shape' => '__stringMin1Max256PatternS', 'locationName' => 'requestId', 'idempotencyToken' => true, ], ], 'required' => [ 'Name', ], ], 'CreateCloudWatchAlarmTemplateGroupResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveCloudwatchAlarmTemplateGroup', 'locationName' => 'arn', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'CreateCloudWatchAlarmTemplateGroupResponseContent' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveCloudwatchAlarmTemplateGroup', 'locationName' => 'arn', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], 'required' => [ 'CreatedAt', 'Id', 'Arn', 'Name', ], ], 'CreateCloudWatchAlarmTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'ComparisonOperator' => [ 'shape' => 'CloudWatchAlarmTemplateComparisonOperator', 'locationName' => 'comparisonOperator', ], 'DatapointsToAlarm' => [ 'shape' => '__integerMin1', 'locationName' => 'datapointsToAlarm', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'EvaluationPeriods' => [ 'shape' => '__integerMin1', 'locationName' => 'evaluationPeriods', ], 'GroupIdentifier' => [ 'shape' => '__stringPatternS', 'locationName' => 'groupIdentifier', ], 'MetricName' => [ 'shape' => '__stringMax64', 'locationName' => 'metricName', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Period' => [ 'shape' => '__integerMin10Max86400', 'locationName' => 'period', ], 'Statistic' => [ 'shape' => 'CloudWatchAlarmTemplateStatistic', 'locationName' => 'statistic', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'TargetResourceType' => [ 'shape' => 'CloudWatchAlarmTemplateTargetResourceType', 'locationName' => 'targetResourceType', ], 'Threshold' => [ 'shape' => '__double', 'locationName' => 'threshold', ], 'TreatMissingData' => [ 'shape' => 'CloudWatchAlarmTemplateTreatMissingData', 'locationName' => 'treatMissingData', ], 'RequestId' => [ 'shape' => '__stringMin1Max256PatternS', 'locationName' => 'requestId', 'idempotencyToken' => true, ], ], 'required' => [ 'TargetResourceType', 'MetricName', 'TreatMissingData', 'ComparisonOperator', 'Statistic', 'Period', 'EvaluationPeriods', 'Threshold', 'Name', 'GroupIdentifier', ], ], 'CreateCloudWatchAlarmTemplateRequestContent' => [ 'type' => 'structure', 'members' => [ 'ComparisonOperator' => [ 'shape' => 'CloudWatchAlarmTemplateComparisonOperator', 'locationName' => 'comparisonOperator', ], 'DatapointsToAlarm' => [ 'shape' => '__integerMin1', 'locationName' => 'datapointsToAlarm', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'EvaluationPeriods' => [ 'shape' => '__integerMin1', 'locationName' => 'evaluationPeriods', ], 'GroupIdentifier' => [ 'shape' => '__stringPatternS', 'locationName' => 'groupIdentifier', ], 'MetricName' => [ 'shape' => '__stringMax64', 'locationName' => 'metricName', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Period' => [ 'shape' => '__integerMin10Max86400', 'locationName' => 'period', ], 'Statistic' => [ 'shape' => 'CloudWatchAlarmTemplateStatistic', 'locationName' => 'statistic', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'TargetResourceType' => [ 'shape' => 'CloudWatchAlarmTemplateTargetResourceType', 'locationName' => 'targetResourceType', ], 'Threshold' => [ 'shape' => '__double', 'locationName' => 'threshold', ], 'TreatMissingData' => [ 'shape' => 'CloudWatchAlarmTemplateTreatMissingData', 'locationName' => 'treatMissingData', ], 'RequestId' => [ 'shape' => '__stringMin1Max256PatternS', 'locationName' => 'requestId', 'idempotencyToken' => true, ], ], 'required' => [ 'TargetResourceType', 'MetricName', 'TreatMissingData', 'ComparisonOperator', 'Statistic', 'Period', 'EvaluationPeriods', 'Threshold', 'Name', 'GroupIdentifier', ], ], 'CreateCloudWatchAlarmTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveCloudwatchAlarmTemplate', 'locationName' => 'arn', ], 'ComparisonOperator' => [ 'shape' => 'CloudWatchAlarmTemplateComparisonOperator', 'locationName' => 'comparisonOperator', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'DatapointsToAlarm' => [ 'shape' => '__integerMin1', 'locationName' => 'datapointsToAlarm', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'EvaluationPeriods' => [ 'shape' => '__integerMin1', 'locationName' => 'evaluationPeriods', ], 'GroupId' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'groupId', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'MetricName' => [ 'shape' => '__stringMax64', 'locationName' => 'metricName', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Period' => [ 'shape' => '__integerMin10Max86400', 'locationName' => 'period', ], 'Statistic' => [ 'shape' => 'CloudWatchAlarmTemplateStatistic', 'locationName' => 'statistic', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'TargetResourceType' => [ 'shape' => 'CloudWatchAlarmTemplateTargetResourceType', 'locationName' => 'targetResourceType', ], 'Threshold' => [ 'shape' => '__double', 'locationName' => 'threshold', ], 'TreatMissingData' => [ 'shape' => 'CloudWatchAlarmTemplateTreatMissingData', 'locationName' => 'treatMissingData', ], ], ], 'CreateCloudWatchAlarmTemplateResponseContent' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveCloudwatchAlarmTemplate', 'locationName' => 'arn', ], 'ComparisonOperator' => [ 'shape' => 'CloudWatchAlarmTemplateComparisonOperator', 'locationName' => 'comparisonOperator', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'DatapointsToAlarm' => [ 'shape' => '__integerMin1', 'locationName' => 'datapointsToAlarm', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'EvaluationPeriods' => [ 'shape' => '__integerMin1', 'locationName' => 'evaluationPeriods', ], 'GroupId' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'groupId', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'MetricName' => [ 'shape' => '__stringMax64', 'locationName' => 'metricName', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Period' => [ 'shape' => '__integerMin10Max86400', 'locationName' => 'period', ], 'Statistic' => [ 'shape' => 'CloudWatchAlarmTemplateStatistic', 'locationName' => 'statistic', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'TargetResourceType' => [ 'shape' => 'CloudWatchAlarmTemplateTargetResourceType', 'locationName' => 'targetResourceType', ], 'Threshold' => [ 'shape' => '__double', 'locationName' => 'threshold', ], 'TreatMissingData' => [ 'shape' => 'CloudWatchAlarmTemplateTreatMissingData', 'locationName' => 'treatMissingData', ], ], 'required' => [ 'TargetResourceType', 'TreatMissingData', 'ComparisonOperator', 'CreatedAt', 'Period', 'EvaluationPeriods', 'Name', 'GroupId', 'MetricName', 'Statistic', 'Id', 'Arn', 'Threshold', ], ], 'CreateEventBridgeRuleTemplateGroupRequest' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'RequestId' => [ 'shape' => '__stringMin1Max256PatternS', 'locationName' => 'requestId', 'idempotencyToken' => true, ], ], 'required' => [ 'Name', ], ], 'CreateEventBridgeRuleTemplateGroupRequestContent' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'RequestId' => [ 'shape' => '__stringMin1Max256PatternS', 'locationName' => 'requestId', 'idempotencyToken' => true, ], ], 'required' => [ 'Name', ], ], 'CreateEventBridgeRuleTemplateGroupResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveEventbridgeRuleTemplateGroup', 'locationName' => 'arn', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'CreateEventBridgeRuleTemplateGroupResponseContent' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveEventbridgeRuleTemplateGroup', 'locationName' => 'arn', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], 'required' => [ 'CreatedAt', 'Id', 'Arn', 'Name', ], ], 'CreateEventBridgeRuleTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'EventTargets' => [ 'shape' => '__listOfEventBridgeRuleTemplateTarget', 'locationName' => 'eventTargets', ], 'EventType' => [ 'shape' => 'EventBridgeRuleTemplateEventType', 'locationName' => 'eventType', ], 'GroupIdentifier' => [ 'shape' => '__stringPatternS', 'locationName' => 'groupIdentifier', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'RequestId' => [ 'shape' => '__stringMin1Max256PatternS', 'locationName' => 'requestId', 'idempotencyToken' => true, ], ], 'required' => [ 'EventType', 'Name', 'GroupIdentifier', ], ], 'CreateEventBridgeRuleTemplateRequestContent' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'EventTargets' => [ 'shape' => '__listOfEventBridgeRuleTemplateTarget', 'locationName' => 'eventTargets', ], 'EventType' => [ 'shape' => 'EventBridgeRuleTemplateEventType', 'locationName' => 'eventType', ], 'GroupIdentifier' => [ 'shape' => '__stringPatternS', 'locationName' => 'groupIdentifier', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'RequestId' => [ 'shape' => '__stringMin1Max256PatternS', 'locationName' => 'requestId', 'idempotencyToken' => true, ], ], 'required' => [ 'EventType', 'Name', 'GroupIdentifier', ], ], 'CreateEventBridgeRuleTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveEventbridgeRuleTemplate', 'locationName' => 'arn', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'EventTargets' => [ 'shape' => '__listOfEventBridgeRuleTemplateTarget', 'locationName' => 'eventTargets', ], 'EventType' => [ 'shape' => 'EventBridgeRuleTemplateEventType', 'locationName' => 'eventType', ], 'GroupId' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'groupId', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'CreateEventBridgeRuleTemplateResponseContent' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveEventbridgeRuleTemplate', 'locationName' => 'arn', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'EventTargets' => [ 'shape' => '__listOfEventBridgeRuleTemplateTarget', 'locationName' => 'eventTargets', ], 'EventType' => [ 'shape' => 'EventBridgeRuleTemplateEventType', 'locationName' => 'eventType', ], 'GroupId' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'groupId', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], 'required' => [ 'EventType', 'CreatedAt', 'Id', 'Arn', 'Name', 'GroupId', ], ], 'CreateSignalMapRequest' => [ 'type' => 'structure', 'members' => [ 'CloudWatchAlarmTemplateGroupIdentifiers' => [ 'shape' => '__listOf__stringPatternS', 'locationName' => 'cloudWatchAlarmTemplateGroupIdentifiers', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'DiscoveryEntryPointArn' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'discoveryEntryPointArn', ], 'EventBridgeRuleTemplateGroupIdentifiers' => [ 'shape' => '__listOf__stringPatternS', 'locationName' => 'eventBridgeRuleTemplateGroupIdentifiers', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'RequestId' => [ 'shape' => '__stringMin1Max256PatternS', 'locationName' => 'requestId', 'idempotencyToken' => true, ], ], 'required' => [ 'DiscoveryEntryPointArn', 'Name', ], ], 'CreateSignalMapRequestContent' => [ 'type' => 'structure', 'members' => [ 'CloudWatchAlarmTemplateGroupIdentifiers' => [ 'shape' => '__listOf__stringPatternS', 'locationName' => 'cloudWatchAlarmTemplateGroupIdentifiers', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'DiscoveryEntryPointArn' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'discoveryEntryPointArn', ], 'EventBridgeRuleTemplateGroupIdentifiers' => [ 'shape' => '__listOf__stringPatternS', 'locationName' => 'eventBridgeRuleTemplateGroupIdentifiers', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'RequestId' => [ 'shape' => '__stringMin1Max256PatternS', 'locationName' => 'requestId', 'idempotencyToken' => true, ], ], 'required' => [ 'DiscoveryEntryPointArn', 'Name', ], ], 'CreateSignalMapResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveSignalMap', 'locationName' => 'arn', ], 'CloudWatchAlarmTemplateGroupIds' => [ 'shape' => '__listOf__stringMin7Max11PatternAws097', 'locationName' => 'cloudWatchAlarmTemplateGroupIds', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'DiscoveryEntryPointArn' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'discoveryEntryPointArn', ], 'ErrorMessage' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'errorMessage', ], 'EventBridgeRuleTemplateGroupIds' => [ 'shape' => '__listOf__stringMin7Max11PatternAws097', 'locationName' => 'eventBridgeRuleTemplateGroupIds', ], 'FailedMediaResourceMap' => [ 'shape' => 'FailedMediaResourceMap', 'locationName' => 'failedMediaResourceMap', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'LastDiscoveredAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'lastDiscoveredAt', ], 'LastSuccessfulMonitorDeployment' => [ 'shape' => 'SuccessfulMonitorDeployment', 'locationName' => 'lastSuccessfulMonitorDeployment', ], 'MediaResourceMap' => [ 'shape' => 'MediaResourceMap', 'locationName' => 'mediaResourceMap', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'MonitorChangesPendingDeployment' => [ 'shape' => '__boolean', 'locationName' => 'monitorChangesPendingDeployment', ], 'MonitorDeployment' => [ 'shape' => 'MonitorDeployment', 'locationName' => 'monitorDeployment', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Status' => [ 'shape' => 'SignalMapStatus', 'locationName' => 'status', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'CreateSignalMapResponseContent' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveSignalMap', 'locationName' => 'arn', ], 'CloudWatchAlarmTemplateGroupIds' => [ 'shape' => '__listOf__stringMin7Max11PatternAws097', 'locationName' => 'cloudWatchAlarmTemplateGroupIds', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'DiscoveryEntryPointArn' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'discoveryEntryPointArn', ], 'ErrorMessage' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'errorMessage', ], 'EventBridgeRuleTemplateGroupIds' => [ 'shape' => '__listOf__stringMin7Max11PatternAws097', 'locationName' => 'eventBridgeRuleTemplateGroupIds', ], 'FailedMediaResourceMap' => [ 'shape' => 'FailedMediaResourceMap', 'locationName' => 'failedMediaResourceMap', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'LastDiscoveredAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'lastDiscoveredAt', ], 'LastSuccessfulMonitorDeployment' => [ 'shape' => 'SuccessfulMonitorDeployment', 'locationName' => 'lastSuccessfulMonitorDeployment', ], 'MediaResourceMap' => [ 'shape' => 'MediaResourceMap', 'locationName' => 'mediaResourceMap', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'MonitorChangesPendingDeployment' => [ 'shape' => '__boolean', 'locationName' => 'monitorChangesPendingDeployment', ], 'MonitorDeployment' => [ 'shape' => 'MonitorDeployment', 'locationName' => 'monitorDeployment', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Status' => [ 'shape' => 'SignalMapStatus', 'locationName' => 'status', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], 'required' => [ 'Status', 'CreatedAt', 'Name', 'Id', 'Arn', 'DiscoveryEntryPointArn', 'MonitorChangesPendingDeployment', ], ], 'DeleteCloudWatchAlarmTemplateGroupRequest' => [ 'type' => 'structure', 'members' => [ 'Identifier' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'identifier', ], ], 'required' => [ 'Identifier', ], ], 'DeleteCloudWatchAlarmTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'Identifier' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'identifier', ], ], 'required' => [ 'Identifier', ], ], 'DeleteEventBridgeRuleTemplateGroupRequest' => [ 'type' => 'structure', 'members' => [ 'Identifier' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'identifier', ], ], 'required' => [ 'Identifier', ], ], 'DeleteEventBridgeRuleTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'Identifier' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'identifier', ], ], 'required' => [ 'Identifier', ], ], 'DeleteSignalMapRequest' => [ 'type' => 'structure', 'members' => [ 'Identifier' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'identifier', ], ], 'required' => [ 'Identifier', ], ], 'EventBridgeRuleTemplateEventType' => [ 'type' => 'string', 'enum' => [ 'MEDIALIVE_MULTIPLEX_ALERT', 'MEDIALIVE_MULTIPLEX_STATE_CHANGE', 'MEDIALIVE_CHANNEL_ALERT', 'MEDIALIVE_CHANNEL_INPUT_CHANGE', 'MEDIALIVE_CHANNEL_STATE_CHANGE', 'MEDIAPACKAGE_INPUT_NOTIFICATION', 'MEDIAPACKAGE_KEY_PROVIDER_NOTIFICATION', 'MEDIAPACKAGE_HARVEST_JOB_NOTIFICATION', 'SIGNAL_MAP_ACTIVE_ALARM', 'MEDIACONNECT_ALERT', 'MEDIACONNECT_SOURCE_HEALTH', 'MEDIACONNECT_OUTPUT_HEALTH', 'MEDIACONNECT_FLOW_STATUS_CHANGE', ], ], 'EventBridgeRuleTemplateGroupSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveEventbridgeRuleTemplateGroup', 'locationName' => 'arn', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'TemplateCount' => [ 'shape' => '__integer', 'locationName' => 'templateCount', ], ], 'required' => [ 'TemplateCount', 'CreatedAt', 'Id', 'Arn', 'Name', ], ], 'EventBridgeRuleTemplateSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveEventbridgeRuleTemplate', 'locationName' => 'arn', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'EventTargetCount' => [ 'shape' => '__integerMax5', 'locationName' => 'eventTargetCount', ], 'EventType' => [ 'shape' => 'EventBridgeRuleTemplateEventType', 'locationName' => 'eventType', ], 'GroupId' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'groupId', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], 'required' => [ 'EventType', 'CreatedAt', 'EventTargetCount', 'Id', 'Arn', 'Name', 'GroupId', ], ], 'EventBridgeRuleTemplateTarget' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringMin1Max2048PatternArn', 'locationName' => 'arn', ], ], 'required' => [ 'Arn', ], ], 'FailedMediaResourceMap' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => 'MediaResource', ], ], 'ForbiddenExceptionResponseContent' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], ], 'GetCloudWatchAlarmTemplateGroupRequest' => [ 'type' => 'structure', 'members' => [ 'Identifier' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'identifier', ], ], 'required' => [ 'Identifier', ], ], 'GetCloudWatchAlarmTemplateGroupResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveCloudwatchAlarmTemplateGroup', 'locationName' => 'arn', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'GetCloudWatchAlarmTemplateGroupResponseContent' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveCloudwatchAlarmTemplateGroup', 'locationName' => 'arn', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], 'required' => [ 'CreatedAt', 'Id', 'Arn', 'Name', ], ], 'GetCloudWatchAlarmTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'Identifier' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'identifier', ], ], 'required' => [ 'Identifier', ], ], 'GetCloudWatchAlarmTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveCloudwatchAlarmTemplate', 'locationName' => 'arn', ], 'ComparisonOperator' => [ 'shape' => 'CloudWatchAlarmTemplateComparisonOperator', 'locationName' => 'comparisonOperator', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'DatapointsToAlarm' => [ 'shape' => '__integerMin1', 'locationName' => 'datapointsToAlarm', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'EvaluationPeriods' => [ 'shape' => '__integerMin1', 'locationName' => 'evaluationPeriods', ], 'GroupId' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'groupId', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'MetricName' => [ 'shape' => '__stringMax64', 'locationName' => 'metricName', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Period' => [ 'shape' => '__integerMin10Max86400', 'locationName' => 'period', ], 'Statistic' => [ 'shape' => 'CloudWatchAlarmTemplateStatistic', 'locationName' => 'statistic', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'TargetResourceType' => [ 'shape' => 'CloudWatchAlarmTemplateTargetResourceType', 'locationName' => 'targetResourceType', ], 'Threshold' => [ 'shape' => '__double', 'locationName' => 'threshold', ], 'TreatMissingData' => [ 'shape' => 'CloudWatchAlarmTemplateTreatMissingData', 'locationName' => 'treatMissingData', ], ], ], 'GetCloudWatchAlarmTemplateResponseContent' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveCloudwatchAlarmTemplate', 'locationName' => 'arn', ], 'ComparisonOperator' => [ 'shape' => 'CloudWatchAlarmTemplateComparisonOperator', 'locationName' => 'comparisonOperator', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'DatapointsToAlarm' => [ 'shape' => '__integerMin1', 'locationName' => 'datapointsToAlarm', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'EvaluationPeriods' => [ 'shape' => '__integerMin1', 'locationName' => 'evaluationPeriods', ], 'GroupId' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'groupId', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'MetricName' => [ 'shape' => '__stringMax64', 'locationName' => 'metricName', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Period' => [ 'shape' => '__integerMin10Max86400', 'locationName' => 'period', ], 'Statistic' => [ 'shape' => 'CloudWatchAlarmTemplateStatistic', 'locationName' => 'statistic', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'TargetResourceType' => [ 'shape' => 'CloudWatchAlarmTemplateTargetResourceType', 'locationName' => 'targetResourceType', ], 'Threshold' => [ 'shape' => '__double', 'locationName' => 'threshold', ], 'TreatMissingData' => [ 'shape' => 'CloudWatchAlarmTemplateTreatMissingData', 'locationName' => 'treatMissingData', ], ], 'required' => [ 'TargetResourceType', 'TreatMissingData', 'ComparisonOperator', 'CreatedAt', 'Period', 'EvaluationPeriods', 'Name', 'GroupId', 'MetricName', 'Statistic', 'Id', 'Arn', 'Threshold', ], ], 'GetEventBridgeRuleTemplateGroupRequest' => [ 'type' => 'structure', 'members' => [ 'Identifier' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'identifier', ], ], 'required' => [ 'Identifier', ], ], 'GetEventBridgeRuleTemplateGroupResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveEventbridgeRuleTemplateGroup', 'locationName' => 'arn', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'GetEventBridgeRuleTemplateGroupResponseContent' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveEventbridgeRuleTemplateGroup', 'locationName' => 'arn', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], 'required' => [ 'CreatedAt', 'Id', 'Arn', 'Name', ], ], 'GetEventBridgeRuleTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'Identifier' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'identifier', ], ], 'required' => [ 'Identifier', ], ], 'GetEventBridgeRuleTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveEventbridgeRuleTemplate', 'locationName' => 'arn', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'EventTargets' => [ 'shape' => '__listOfEventBridgeRuleTemplateTarget', 'locationName' => 'eventTargets', ], 'EventType' => [ 'shape' => 'EventBridgeRuleTemplateEventType', 'locationName' => 'eventType', ], 'GroupId' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'groupId', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'GetEventBridgeRuleTemplateResponseContent' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveEventbridgeRuleTemplate', 'locationName' => 'arn', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'EventTargets' => [ 'shape' => '__listOfEventBridgeRuleTemplateTarget', 'locationName' => 'eventTargets', ], 'EventType' => [ 'shape' => 'EventBridgeRuleTemplateEventType', 'locationName' => 'eventType', ], 'GroupId' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'groupId', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], 'required' => [ 'EventType', 'CreatedAt', 'Id', 'Arn', 'Name', 'GroupId', ], ], 'GetSignalMapRequest' => [ 'type' => 'structure', 'members' => [ 'Identifier' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'identifier', ], ], 'required' => [ 'Identifier', ], ], 'GetSignalMapResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveSignalMap', 'locationName' => 'arn', ], 'CloudWatchAlarmTemplateGroupIds' => [ 'shape' => '__listOf__stringMin7Max11PatternAws097', 'locationName' => 'cloudWatchAlarmTemplateGroupIds', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'DiscoveryEntryPointArn' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'discoveryEntryPointArn', ], 'ErrorMessage' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'errorMessage', ], 'EventBridgeRuleTemplateGroupIds' => [ 'shape' => '__listOf__stringMin7Max11PatternAws097', 'locationName' => 'eventBridgeRuleTemplateGroupIds', ], 'FailedMediaResourceMap' => [ 'shape' => 'FailedMediaResourceMap', 'locationName' => 'failedMediaResourceMap', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'LastDiscoveredAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'lastDiscoveredAt', ], 'LastSuccessfulMonitorDeployment' => [ 'shape' => 'SuccessfulMonitorDeployment', 'locationName' => 'lastSuccessfulMonitorDeployment', ], 'MediaResourceMap' => [ 'shape' => 'MediaResourceMap', 'locationName' => 'mediaResourceMap', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'MonitorChangesPendingDeployment' => [ 'shape' => '__boolean', 'locationName' => 'monitorChangesPendingDeployment', ], 'MonitorDeployment' => [ 'shape' => 'MonitorDeployment', 'locationName' => 'monitorDeployment', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Status' => [ 'shape' => 'SignalMapStatus', 'locationName' => 'status', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'GetSignalMapResponseContent' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveSignalMap', 'locationName' => 'arn', ], 'CloudWatchAlarmTemplateGroupIds' => [ 'shape' => '__listOf__stringMin7Max11PatternAws097', 'locationName' => 'cloudWatchAlarmTemplateGroupIds', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'DiscoveryEntryPointArn' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'discoveryEntryPointArn', ], 'ErrorMessage' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'errorMessage', ], 'EventBridgeRuleTemplateGroupIds' => [ 'shape' => '__listOf__stringMin7Max11PatternAws097', 'locationName' => 'eventBridgeRuleTemplateGroupIds', ], 'FailedMediaResourceMap' => [ 'shape' => 'FailedMediaResourceMap', 'locationName' => 'failedMediaResourceMap', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'LastDiscoveredAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'lastDiscoveredAt', ], 'LastSuccessfulMonitorDeployment' => [ 'shape' => 'SuccessfulMonitorDeployment', 'locationName' => 'lastSuccessfulMonitorDeployment', ], 'MediaResourceMap' => [ 'shape' => 'MediaResourceMap', 'locationName' => 'mediaResourceMap', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'MonitorChangesPendingDeployment' => [ 'shape' => '__boolean', 'locationName' => 'monitorChangesPendingDeployment', ], 'MonitorDeployment' => [ 'shape' => 'MonitorDeployment', 'locationName' => 'monitorDeployment', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Status' => [ 'shape' => 'SignalMapStatus', 'locationName' => 'status', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], 'required' => [ 'Status', 'CreatedAt', 'Name', 'Id', 'Arn', 'DiscoveryEntryPointArn', 'MonitorChangesPendingDeployment', ], ], 'InternalServerErrorExceptionResponseContent' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], ], 'ListCloudWatchAlarmTemplateGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'Scope' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'scope', ], 'SignalMapIdentifier' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'signalMapIdentifier', ], ], ], 'ListCloudWatchAlarmTemplateGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'CloudWatchAlarmTemplateGroups' => [ 'shape' => '__listOfCloudWatchAlarmTemplateGroupSummary', 'locationName' => 'cloudWatchAlarmTemplateGroups', ], 'NextToken' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'nextToken', ], ], ], 'ListCloudWatchAlarmTemplateGroupsResponseContent' => [ 'type' => 'structure', 'members' => [ 'CloudWatchAlarmTemplateGroups' => [ 'shape' => '__listOfCloudWatchAlarmTemplateGroupSummary', 'locationName' => 'cloudWatchAlarmTemplateGroups', ], 'NextToken' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'nextToken', ], ], 'required' => [ 'CloudWatchAlarmTemplateGroups', ], ], 'ListCloudWatchAlarmTemplatesRequest' => [ 'type' => 'structure', 'members' => [ 'GroupIdentifier' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'groupIdentifier', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'Scope' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'scope', ], 'SignalMapIdentifier' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'signalMapIdentifier', ], ], ], 'ListCloudWatchAlarmTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'CloudWatchAlarmTemplates' => [ 'shape' => '__listOfCloudWatchAlarmTemplateSummary', 'locationName' => 'cloudWatchAlarmTemplates', ], 'NextToken' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'nextToken', ], ], ], 'ListCloudWatchAlarmTemplatesResponseContent' => [ 'type' => 'structure', 'members' => [ 'CloudWatchAlarmTemplates' => [ 'shape' => '__listOfCloudWatchAlarmTemplateSummary', 'locationName' => 'cloudWatchAlarmTemplates', ], 'NextToken' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'nextToken', ], ], 'required' => [ 'CloudWatchAlarmTemplates', ], ], 'ListEventBridgeRuleTemplateGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'SignalMapIdentifier' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'signalMapIdentifier', ], ], ], 'ListEventBridgeRuleTemplateGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'EventBridgeRuleTemplateGroups' => [ 'shape' => '__listOfEventBridgeRuleTemplateGroupSummary', 'locationName' => 'eventBridgeRuleTemplateGroups', ], 'NextToken' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'nextToken', ], ], ], 'ListEventBridgeRuleTemplateGroupsResponseContent' => [ 'type' => 'structure', 'members' => [ 'EventBridgeRuleTemplateGroups' => [ 'shape' => '__listOfEventBridgeRuleTemplateGroupSummary', 'locationName' => 'eventBridgeRuleTemplateGroups', ], 'NextToken' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'nextToken', ], ], 'required' => [ 'EventBridgeRuleTemplateGroups', ], ], 'ListEventBridgeRuleTemplatesRequest' => [ 'type' => 'structure', 'members' => [ 'GroupIdentifier' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'groupIdentifier', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'SignalMapIdentifier' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'signalMapIdentifier', ], ], ], 'ListEventBridgeRuleTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'EventBridgeRuleTemplates' => [ 'shape' => '__listOfEventBridgeRuleTemplateSummary', 'locationName' => 'eventBridgeRuleTemplates', ], 'NextToken' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'nextToken', ], ], ], 'ListEventBridgeRuleTemplatesResponseContent' => [ 'type' => 'structure', 'members' => [ 'EventBridgeRuleTemplates' => [ 'shape' => '__listOfEventBridgeRuleTemplateSummary', 'locationName' => 'eventBridgeRuleTemplates', ], 'NextToken' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'nextToken', ], ], 'required' => [ 'EventBridgeRuleTemplates', ], ], 'ListSignalMapsRequest' => [ 'type' => 'structure', 'members' => [ 'CloudWatchAlarmTemplateGroupIdentifier' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'cloudWatchAlarmTemplateGroupIdentifier', ], 'EventBridgeRuleTemplateGroupIdentifier' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'eventBridgeRuleTemplateGroupIdentifier', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListSignalMapsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'nextToken', ], 'SignalMaps' => [ 'shape' => '__listOfSignalMapSummary', 'locationName' => 'signalMaps', ], ], ], 'ListSignalMapsResponseContent' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'nextToken', ], 'SignalMaps' => [ 'shape' => '__listOfSignalMapSummary', 'locationName' => 'signalMaps', ], ], 'required' => [ 'SignalMaps', ], ], 'MediaResource' => [ 'type' => 'structure', 'members' => [ 'Destinations' => [ 'shape' => '__listOfMediaResourceNeighbor', 'locationName' => 'destinations', ], 'Name' => [ 'shape' => '__stringMin1Max256', 'locationName' => 'name', ], 'Sources' => [ 'shape' => '__listOfMediaResourceNeighbor', 'locationName' => 'sources', ], ], ], 'MediaResourceMap' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => 'MediaResource', ], ], 'MediaResourceNeighbor' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringMin1Max2048PatternArn', 'locationName' => 'arn', ], 'Name' => [ 'shape' => '__stringMin1Max256', 'locationName' => 'name', ], ], 'required' => [ 'Arn', ], ], 'MonitorDeployment' => [ 'type' => 'structure', 'members' => [ 'DetailsUri' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'detailsUri', ], 'ErrorMessage' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'errorMessage', ], 'Status' => [ 'shape' => 'SignalMapMonitorDeploymentStatus', 'locationName' => 'status', ], ], 'required' => [ 'Status', ], ], 'NotFoundExceptionResponseContent' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], ], 'SignalMapMonitorDeploymentStatus' => [ 'type' => 'string', 'enum' => [ 'NOT_DEPLOYED', 'DRY_RUN_DEPLOYMENT_COMPLETE', 'DRY_RUN_DEPLOYMENT_FAILED', 'DRY_RUN_DEPLOYMENT_IN_PROGRESS', 'DEPLOYMENT_COMPLETE', 'DEPLOYMENT_FAILED', 'DEPLOYMENT_IN_PROGRESS', 'DELETE_COMPLETE', 'DELETE_FAILED', 'DELETE_IN_PROGRESS', ], ], 'SignalMapStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'CREATE_COMPLETE', 'CREATE_FAILED', 'UPDATE_IN_PROGRESS', 'UPDATE_COMPLETE', 'UPDATE_REVERTED', 'UPDATE_FAILED', 'READY', 'NOT_READY', ], ], 'SignalMapSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveSignalMap', 'locationName' => 'arn', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'MonitorDeploymentStatus' => [ 'shape' => 'SignalMapMonitorDeploymentStatus', 'locationName' => 'monitorDeploymentStatus', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Status' => [ 'shape' => 'SignalMapStatus', 'locationName' => 'status', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], 'required' => [ 'Status', 'MonitorDeploymentStatus', 'CreatedAt', 'Id', 'Arn', 'Name', ], ], 'StartDeleteMonitorDeploymentRequest' => [ 'type' => 'structure', 'members' => [ 'Identifier' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'identifier', ], ], 'required' => [ 'Identifier', ], ], 'StartDeleteMonitorDeploymentResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveSignalMap', 'locationName' => 'arn', ], 'CloudWatchAlarmTemplateGroupIds' => [ 'shape' => '__listOf__stringMin7Max11PatternAws097', 'locationName' => 'cloudWatchAlarmTemplateGroupIds', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'DiscoveryEntryPointArn' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'discoveryEntryPointArn', ], 'ErrorMessage' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'errorMessage', ], 'EventBridgeRuleTemplateGroupIds' => [ 'shape' => '__listOf__stringMin7Max11PatternAws097', 'locationName' => 'eventBridgeRuleTemplateGroupIds', ], 'FailedMediaResourceMap' => [ 'shape' => 'FailedMediaResourceMap', 'locationName' => 'failedMediaResourceMap', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'LastDiscoveredAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'lastDiscoveredAt', ], 'LastSuccessfulMonitorDeployment' => [ 'shape' => 'SuccessfulMonitorDeployment', 'locationName' => 'lastSuccessfulMonitorDeployment', ], 'MediaResourceMap' => [ 'shape' => 'MediaResourceMap', 'locationName' => 'mediaResourceMap', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'MonitorChangesPendingDeployment' => [ 'shape' => '__boolean', 'locationName' => 'monitorChangesPendingDeployment', ], 'MonitorDeployment' => [ 'shape' => 'MonitorDeployment', 'locationName' => 'monitorDeployment', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Status' => [ 'shape' => 'SignalMapStatus', 'locationName' => 'status', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'StartDeleteMonitorDeploymentResponseContent' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveSignalMap', 'locationName' => 'arn', ], 'CloudWatchAlarmTemplateGroupIds' => [ 'shape' => '__listOf__stringMin7Max11PatternAws097', 'locationName' => 'cloudWatchAlarmTemplateGroupIds', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'DiscoveryEntryPointArn' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'discoveryEntryPointArn', ], 'ErrorMessage' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'errorMessage', ], 'EventBridgeRuleTemplateGroupIds' => [ 'shape' => '__listOf__stringMin7Max11PatternAws097', 'locationName' => 'eventBridgeRuleTemplateGroupIds', ], 'FailedMediaResourceMap' => [ 'shape' => 'FailedMediaResourceMap', 'locationName' => 'failedMediaResourceMap', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'LastDiscoveredAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'lastDiscoveredAt', ], 'LastSuccessfulMonitorDeployment' => [ 'shape' => 'SuccessfulMonitorDeployment', 'locationName' => 'lastSuccessfulMonitorDeployment', ], 'MediaResourceMap' => [ 'shape' => 'MediaResourceMap', 'locationName' => 'mediaResourceMap', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'MonitorChangesPendingDeployment' => [ 'shape' => '__boolean', 'locationName' => 'monitorChangesPendingDeployment', ], 'MonitorDeployment' => [ 'shape' => 'MonitorDeployment', 'locationName' => 'monitorDeployment', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Status' => [ 'shape' => 'SignalMapStatus', 'locationName' => 'status', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], 'required' => [ 'Status', 'CreatedAt', 'Name', 'Id', 'Arn', 'DiscoveryEntryPointArn', 'MonitorChangesPendingDeployment', ], ], 'StartMonitorDeploymentRequest' => [ 'type' => 'structure', 'members' => [ 'DryRun' => [ 'shape' => '__boolean', 'locationName' => 'dryRun', ], 'Identifier' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'identifier', ], ], 'required' => [ 'Identifier', ], ], 'StartMonitorDeploymentRequestContent' => [ 'type' => 'structure', 'members' => [ 'DryRun' => [ 'shape' => '__boolean', 'locationName' => 'dryRun', ], ], ], 'StartMonitorDeploymentResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveSignalMap', 'locationName' => 'arn', ], 'CloudWatchAlarmTemplateGroupIds' => [ 'shape' => '__listOf__stringMin7Max11PatternAws097', 'locationName' => 'cloudWatchAlarmTemplateGroupIds', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'DiscoveryEntryPointArn' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'discoveryEntryPointArn', ], 'ErrorMessage' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'errorMessage', ], 'EventBridgeRuleTemplateGroupIds' => [ 'shape' => '__listOf__stringMin7Max11PatternAws097', 'locationName' => 'eventBridgeRuleTemplateGroupIds', ], 'FailedMediaResourceMap' => [ 'shape' => 'FailedMediaResourceMap', 'locationName' => 'failedMediaResourceMap', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'LastDiscoveredAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'lastDiscoveredAt', ], 'LastSuccessfulMonitorDeployment' => [ 'shape' => 'SuccessfulMonitorDeployment', 'locationName' => 'lastSuccessfulMonitorDeployment', ], 'MediaResourceMap' => [ 'shape' => 'MediaResourceMap', 'locationName' => 'mediaResourceMap', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'MonitorChangesPendingDeployment' => [ 'shape' => '__boolean', 'locationName' => 'monitorChangesPendingDeployment', ], 'MonitorDeployment' => [ 'shape' => 'MonitorDeployment', 'locationName' => 'monitorDeployment', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Status' => [ 'shape' => 'SignalMapStatus', 'locationName' => 'status', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'StartMonitorDeploymentResponseContent' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveSignalMap', 'locationName' => 'arn', ], 'CloudWatchAlarmTemplateGroupIds' => [ 'shape' => '__listOf__stringMin7Max11PatternAws097', 'locationName' => 'cloudWatchAlarmTemplateGroupIds', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'DiscoveryEntryPointArn' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'discoveryEntryPointArn', ], 'ErrorMessage' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'errorMessage', ], 'EventBridgeRuleTemplateGroupIds' => [ 'shape' => '__listOf__stringMin7Max11PatternAws097', 'locationName' => 'eventBridgeRuleTemplateGroupIds', ], 'FailedMediaResourceMap' => [ 'shape' => 'FailedMediaResourceMap', 'locationName' => 'failedMediaResourceMap', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'LastDiscoveredAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'lastDiscoveredAt', ], 'LastSuccessfulMonitorDeployment' => [ 'shape' => 'SuccessfulMonitorDeployment', 'locationName' => 'lastSuccessfulMonitorDeployment', ], 'MediaResourceMap' => [ 'shape' => 'MediaResourceMap', 'locationName' => 'mediaResourceMap', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'MonitorChangesPendingDeployment' => [ 'shape' => '__boolean', 'locationName' => 'monitorChangesPendingDeployment', ], 'MonitorDeployment' => [ 'shape' => 'MonitorDeployment', 'locationName' => 'monitorDeployment', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Status' => [ 'shape' => 'SignalMapStatus', 'locationName' => 'status', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], 'required' => [ 'Status', 'CreatedAt', 'Name', 'Id', 'Arn', 'DiscoveryEntryPointArn', 'MonitorChangesPendingDeployment', ], ], 'StartUpdateSignalMapRequest' => [ 'type' => 'structure', 'members' => [ 'CloudWatchAlarmTemplateGroupIdentifiers' => [ 'shape' => '__listOf__stringPatternS', 'locationName' => 'cloudWatchAlarmTemplateGroupIdentifiers', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'DiscoveryEntryPointArn' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'discoveryEntryPointArn', ], 'EventBridgeRuleTemplateGroupIdentifiers' => [ 'shape' => '__listOf__stringPatternS', 'locationName' => 'eventBridgeRuleTemplateGroupIdentifiers', ], 'ForceRediscovery' => [ 'shape' => '__boolean', 'locationName' => 'forceRediscovery', ], 'Identifier' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'identifier', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], ], 'required' => [ 'Identifier', ], ], 'StartUpdateSignalMapRequestContent' => [ 'type' => 'structure', 'members' => [ 'CloudWatchAlarmTemplateGroupIdentifiers' => [ 'shape' => '__listOf__stringPatternS', 'locationName' => 'cloudWatchAlarmTemplateGroupIdentifiers', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'DiscoveryEntryPointArn' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'discoveryEntryPointArn', ], 'EventBridgeRuleTemplateGroupIdentifiers' => [ 'shape' => '__listOf__stringPatternS', 'locationName' => 'eventBridgeRuleTemplateGroupIdentifiers', ], 'ForceRediscovery' => [ 'shape' => '__boolean', 'locationName' => 'forceRediscovery', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], ], ], 'StartUpdateSignalMapResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveSignalMap', 'locationName' => 'arn', ], 'CloudWatchAlarmTemplateGroupIds' => [ 'shape' => '__listOf__stringMin7Max11PatternAws097', 'locationName' => 'cloudWatchAlarmTemplateGroupIds', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'DiscoveryEntryPointArn' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'discoveryEntryPointArn', ], 'ErrorMessage' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'errorMessage', ], 'EventBridgeRuleTemplateGroupIds' => [ 'shape' => '__listOf__stringMin7Max11PatternAws097', 'locationName' => 'eventBridgeRuleTemplateGroupIds', ], 'FailedMediaResourceMap' => [ 'shape' => 'FailedMediaResourceMap', 'locationName' => 'failedMediaResourceMap', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'LastDiscoveredAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'lastDiscoveredAt', ], 'LastSuccessfulMonitorDeployment' => [ 'shape' => 'SuccessfulMonitorDeployment', 'locationName' => 'lastSuccessfulMonitorDeployment', ], 'MediaResourceMap' => [ 'shape' => 'MediaResourceMap', 'locationName' => 'mediaResourceMap', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'MonitorChangesPendingDeployment' => [ 'shape' => '__boolean', 'locationName' => 'monitorChangesPendingDeployment', ], 'MonitorDeployment' => [ 'shape' => 'MonitorDeployment', 'locationName' => 'monitorDeployment', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Status' => [ 'shape' => 'SignalMapStatus', 'locationName' => 'status', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'StartUpdateSignalMapResponseContent' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveSignalMap', 'locationName' => 'arn', ], 'CloudWatchAlarmTemplateGroupIds' => [ 'shape' => '__listOf__stringMin7Max11PatternAws097', 'locationName' => 'cloudWatchAlarmTemplateGroupIds', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'DiscoveryEntryPointArn' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'discoveryEntryPointArn', ], 'ErrorMessage' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'errorMessage', ], 'EventBridgeRuleTemplateGroupIds' => [ 'shape' => '__listOf__stringMin7Max11PatternAws097', 'locationName' => 'eventBridgeRuleTemplateGroupIds', ], 'FailedMediaResourceMap' => [ 'shape' => 'FailedMediaResourceMap', 'locationName' => 'failedMediaResourceMap', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'LastDiscoveredAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'lastDiscoveredAt', ], 'LastSuccessfulMonitorDeployment' => [ 'shape' => 'SuccessfulMonitorDeployment', 'locationName' => 'lastSuccessfulMonitorDeployment', ], 'MediaResourceMap' => [ 'shape' => 'MediaResourceMap', 'locationName' => 'mediaResourceMap', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'MonitorChangesPendingDeployment' => [ 'shape' => '__boolean', 'locationName' => 'monitorChangesPendingDeployment', ], 'MonitorDeployment' => [ 'shape' => 'MonitorDeployment', 'locationName' => 'monitorDeployment', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Status' => [ 'shape' => 'SignalMapStatus', 'locationName' => 'status', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], 'required' => [ 'Status', 'CreatedAt', 'Name', 'Id', 'Arn', 'DiscoveryEntryPointArn', 'MonitorChangesPendingDeployment', ], ], 'SuccessfulMonitorDeployment' => [ 'type' => 'structure', 'members' => [ 'DetailsUri' => [ 'shape' => '__stringMin1Max2048', 'locationName' => 'detailsUri', ], 'Status' => [ 'shape' => 'SignalMapMonitorDeploymentStatus', 'locationName' => 'status', ], ], 'required' => [ 'DetailsUri', 'Status', ], ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => '__string', ], ], 'TooManyRequestsExceptionResponseContent' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], ], 'UpdateCloudWatchAlarmTemplateGroupRequest' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'Identifier' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'identifier', ], ], 'required' => [ 'Identifier', ], ], 'UpdateCloudWatchAlarmTemplateGroupRequestContent' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], ], ], 'UpdateCloudWatchAlarmTemplateGroupResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveCloudwatchAlarmTemplateGroup', 'locationName' => 'arn', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'UpdateCloudWatchAlarmTemplateGroupResponseContent' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveCloudwatchAlarmTemplateGroup', 'locationName' => 'arn', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], 'required' => [ 'CreatedAt', 'Id', 'Arn', 'Name', ], ], 'UpdateCloudWatchAlarmTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'ComparisonOperator' => [ 'shape' => 'CloudWatchAlarmTemplateComparisonOperator', 'locationName' => 'comparisonOperator', ], 'DatapointsToAlarm' => [ 'shape' => '__integerMin1', 'locationName' => 'datapointsToAlarm', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'EvaluationPeriods' => [ 'shape' => '__integerMin1', 'locationName' => 'evaluationPeriods', ], 'GroupIdentifier' => [ 'shape' => '__stringPatternS', 'locationName' => 'groupIdentifier', ], 'Identifier' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'identifier', ], 'MetricName' => [ 'shape' => '__stringMax64', 'locationName' => 'metricName', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Period' => [ 'shape' => '__integerMin10Max86400', 'locationName' => 'period', ], 'Statistic' => [ 'shape' => 'CloudWatchAlarmTemplateStatistic', 'locationName' => 'statistic', ], 'TargetResourceType' => [ 'shape' => 'CloudWatchAlarmTemplateTargetResourceType', 'locationName' => 'targetResourceType', ], 'Threshold' => [ 'shape' => '__double', 'locationName' => 'threshold', ], 'TreatMissingData' => [ 'shape' => 'CloudWatchAlarmTemplateTreatMissingData', 'locationName' => 'treatMissingData', ], ], 'required' => [ 'Identifier', ], ], 'UpdateCloudWatchAlarmTemplateRequestContent' => [ 'type' => 'structure', 'members' => [ 'ComparisonOperator' => [ 'shape' => 'CloudWatchAlarmTemplateComparisonOperator', 'locationName' => 'comparisonOperator', ], 'DatapointsToAlarm' => [ 'shape' => '__integerMin1', 'locationName' => 'datapointsToAlarm', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'EvaluationPeriods' => [ 'shape' => '__integerMin1', 'locationName' => 'evaluationPeriods', ], 'GroupIdentifier' => [ 'shape' => '__stringPatternS', 'locationName' => 'groupIdentifier', ], 'MetricName' => [ 'shape' => '__stringMax64', 'locationName' => 'metricName', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Period' => [ 'shape' => '__integerMin10Max86400', 'locationName' => 'period', ], 'Statistic' => [ 'shape' => 'CloudWatchAlarmTemplateStatistic', 'locationName' => 'statistic', ], 'TargetResourceType' => [ 'shape' => 'CloudWatchAlarmTemplateTargetResourceType', 'locationName' => 'targetResourceType', ], 'Threshold' => [ 'shape' => '__double', 'locationName' => 'threshold', ], 'TreatMissingData' => [ 'shape' => 'CloudWatchAlarmTemplateTreatMissingData', 'locationName' => 'treatMissingData', ], ], ], 'UpdateCloudWatchAlarmTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveCloudwatchAlarmTemplate', 'locationName' => 'arn', ], 'ComparisonOperator' => [ 'shape' => 'CloudWatchAlarmTemplateComparisonOperator', 'locationName' => 'comparisonOperator', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'DatapointsToAlarm' => [ 'shape' => '__integerMin1', 'locationName' => 'datapointsToAlarm', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'EvaluationPeriods' => [ 'shape' => '__integerMin1', 'locationName' => 'evaluationPeriods', ], 'GroupId' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'groupId', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'MetricName' => [ 'shape' => '__stringMax64', 'locationName' => 'metricName', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Period' => [ 'shape' => '__integerMin10Max86400', 'locationName' => 'period', ], 'Statistic' => [ 'shape' => 'CloudWatchAlarmTemplateStatistic', 'locationName' => 'statistic', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'TargetResourceType' => [ 'shape' => 'CloudWatchAlarmTemplateTargetResourceType', 'locationName' => 'targetResourceType', ], 'Threshold' => [ 'shape' => '__double', 'locationName' => 'threshold', ], 'TreatMissingData' => [ 'shape' => 'CloudWatchAlarmTemplateTreatMissingData', 'locationName' => 'treatMissingData', ], ], ], 'UpdateCloudWatchAlarmTemplateResponseContent' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveCloudwatchAlarmTemplate', 'locationName' => 'arn', ], 'ComparisonOperator' => [ 'shape' => 'CloudWatchAlarmTemplateComparisonOperator', 'locationName' => 'comparisonOperator', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'DatapointsToAlarm' => [ 'shape' => '__integerMin1', 'locationName' => 'datapointsToAlarm', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'EvaluationPeriods' => [ 'shape' => '__integerMin1', 'locationName' => 'evaluationPeriods', ], 'GroupId' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'groupId', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'MetricName' => [ 'shape' => '__stringMax64', 'locationName' => 'metricName', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Period' => [ 'shape' => '__integerMin10Max86400', 'locationName' => 'period', ], 'Statistic' => [ 'shape' => 'CloudWatchAlarmTemplateStatistic', 'locationName' => 'statistic', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], 'TargetResourceType' => [ 'shape' => 'CloudWatchAlarmTemplateTargetResourceType', 'locationName' => 'targetResourceType', ], 'Threshold' => [ 'shape' => '__double', 'locationName' => 'threshold', ], 'TreatMissingData' => [ 'shape' => 'CloudWatchAlarmTemplateTreatMissingData', 'locationName' => 'treatMissingData', ], ], 'required' => [ 'TargetResourceType', 'TreatMissingData', 'ComparisonOperator', 'CreatedAt', 'Period', 'EvaluationPeriods', 'Name', 'GroupId', 'MetricName', 'Statistic', 'Id', 'Arn', 'Threshold', ], ], 'UpdateEventBridgeRuleTemplateGroupRequest' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'Identifier' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'identifier', ], ], 'required' => [ 'Identifier', ], ], 'UpdateEventBridgeRuleTemplateGroupRequestContent' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], ], ], 'UpdateEventBridgeRuleTemplateGroupResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveEventbridgeRuleTemplateGroup', 'locationName' => 'arn', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'UpdateEventBridgeRuleTemplateGroupResponseContent' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveEventbridgeRuleTemplateGroup', 'locationName' => 'arn', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], 'required' => [ 'CreatedAt', 'Id', 'Arn', 'Name', ], ], 'UpdateEventBridgeRuleTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'EventTargets' => [ 'shape' => '__listOfEventBridgeRuleTemplateTarget', 'locationName' => 'eventTargets', ], 'EventType' => [ 'shape' => 'EventBridgeRuleTemplateEventType', 'locationName' => 'eventType', ], 'GroupIdentifier' => [ 'shape' => '__stringPatternS', 'locationName' => 'groupIdentifier', ], 'Identifier' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'identifier', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], ], 'required' => [ 'Identifier', ], ], 'UpdateEventBridgeRuleTemplateRequestContent' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'EventTargets' => [ 'shape' => '__listOfEventBridgeRuleTemplateTarget', 'locationName' => 'eventTargets', ], 'EventType' => [ 'shape' => 'EventBridgeRuleTemplateEventType', 'locationName' => 'eventType', ], 'GroupIdentifier' => [ 'shape' => '__stringPatternS', 'locationName' => 'groupIdentifier', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], ], ], 'UpdateEventBridgeRuleTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveEventbridgeRuleTemplate', 'locationName' => 'arn', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'EventTargets' => [ 'shape' => '__listOfEventBridgeRuleTemplateTarget', 'locationName' => 'eventTargets', ], 'EventType' => [ 'shape' => 'EventBridgeRuleTemplateEventType', 'locationName' => 'eventType', ], 'GroupId' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'groupId', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], ], 'UpdateEventBridgeRuleTemplateResponseContent' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__stringPatternArnMedialiveEventbridgeRuleTemplate', 'locationName' => 'arn', ], 'CreatedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__stringMin0Max1024', 'locationName' => 'description', ], 'EventTargets' => [ 'shape' => '__listOfEventBridgeRuleTemplateTarget', 'locationName' => 'eventTargets', ], 'EventType' => [ 'shape' => 'EventBridgeRuleTemplateEventType', 'locationName' => 'eventType', ], 'GroupId' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'groupId', ], 'Id' => [ 'shape' => '__stringMin7Max11PatternAws097', 'locationName' => 'id', ], 'ModifiedAt' => [ 'shape' => '__timestampIso8601', 'locationName' => 'modifiedAt', ], 'Name' => [ 'shape' => '__stringMin1Max255PatternS', 'locationName' => 'name', ], 'Tags' => [ 'shape' => 'TagMap', 'locationName' => 'tags', ], ], 'required' => [ 'EventType', 'CreatedAt', 'Id', 'Arn', 'Name', 'GroupId', ], ], '__integerMax5' => [ 'type' => 'integer', 'max' => 5, ], '__integerMin10Max86400' => [ 'type' => 'integer', 'min' => 10, 'max' => 86400, ], '__listOfCloudWatchAlarmTemplateGroupSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'CloudWatchAlarmTemplateGroupSummary', ], ], '__listOfCloudWatchAlarmTemplateSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'CloudWatchAlarmTemplateSummary', ], ], '__listOfEventBridgeRuleTemplateGroupSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventBridgeRuleTemplateGroupSummary', ], ], '__listOfEventBridgeRuleTemplateSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventBridgeRuleTemplateSummary', ], ], '__listOfEventBridgeRuleTemplateTarget' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventBridgeRuleTemplateTarget', ], ], '__listOfMediaResourceNeighbor' => [ 'type' => 'list', 'member' => [ 'shape' => 'MediaResourceNeighbor', ], ], '__listOfSignalMapSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'SignalMapSummary', ], ], '__listOf__stringMin7Max11PatternAws097' => [ 'type' => 'list', 'member' => [ 'shape' => '__stringMin7Max11PatternAws097', ], ], '__listOf__stringPatternS' => [ 'type' => 'list', 'member' => [ 'shape' => '__stringPatternS', ], ], '__stringMax64' => [ 'type' => 'string', 'max' => 64, ], '__stringMin0Max1024' => [ 'type' => 'string', 'min' => 0, 'max' => 1024, ], '__stringMin1Max2048' => [ 'type' => 'string', 'min' => 1, 'max' => 2048, ], '__stringMin1Max2048PatternArn' => [ 'type' => 'string', 'min' => 1, 'max' => 2048, 'pattern' => '^arn.+$', ], '__stringMin1Max255PatternS' => [ 'type' => 'string', 'min' => 1, 'max' => 255, 'pattern' => '^[^\\s]+$', ], '__stringMin7Max11PatternAws097' => [ 'type' => 'string', 'min' => 7, 'max' => 11, 'pattern' => '^(aws-)?[0-9]{7}$', ], '__stringPatternArnMedialiveCloudwatchAlarmTemplate' => [ 'type' => 'string', 'pattern' => '^arn:.+:medialive:.+:cloudwatch-alarm-template:.+$', ], '__stringPatternArnMedialiveCloudwatchAlarmTemplateGroup' => [ 'type' => 'string', 'pattern' => '^arn:.+:medialive:.+:cloudwatch-alarm-template-group:.+$', ], '__stringPatternArnMedialiveEventbridgeRuleTemplate' => [ 'type' => 'string', 'pattern' => '^arn:.+:medialive:.+:eventbridge-rule-template:.+$', ], '__stringPatternArnMedialiveEventbridgeRuleTemplateGroup' => [ 'type' => 'string', 'pattern' => '^arn:.+:medialive:.+:eventbridge-rule-template-group:.+$', ], '__stringPatternArnMedialiveSignalMap' => [ 'type' => 'string', 'pattern' => '^arn:.+:medialive:.+:signal-map:.+$', ], '__stringPatternS' => [ 'type' => 'string', 'pattern' => '^[^\\s]+$', ], 'Scte35SegmentationScope' => [ 'type' => 'string', 'enum' => [ 'ALL_OUTPUT_GROUPS', 'SCTE35_ENABLED_OUTPUT_GROUPS', ], ], 'Algorithm' => [ 'type' => 'string', 'enum' => [ 'AES128', 'AES192', 'AES256', ], ], 'SrtCallerDecryption' => [ 'type' => 'structure', 'members' => [ 'Algorithm' => [ 'shape' => 'Algorithm', 'locationName' => 'algorithm', ], 'PassphraseSecretArn' => [ 'shape' => '__string', 'locationName' => 'passphraseSecretArn', ], ], ], 'SrtCallerDecryptionRequest' => [ 'type' => 'structure', 'members' => [ 'Algorithm' => [ 'shape' => 'Algorithm', 'locationName' => 'algorithm', ], 'PassphraseSecretArn' => [ 'shape' => '__string', 'locationName' => 'passphraseSecretArn', ], ], ], 'SrtCallerSource' => [ 'type' => 'structure', 'members' => [ 'Decryption' => [ 'shape' => 'SrtCallerDecryption', 'locationName' => 'decryption', ], 'MinimumLatency' => [ 'shape' => '__integer', 'locationName' => 'minimumLatency', ], 'SrtListenerAddress' => [ 'shape' => '__string', 'locationName' => 'srtListenerAddress', ], 'SrtListenerPort' => [ 'shape' => '__string', 'locationName' => 'srtListenerPort', ], 'StreamId' => [ 'shape' => '__string', 'locationName' => 'streamId', ], ], ], 'SrtCallerSourceRequest' => [ 'type' => 'structure', 'members' => [ 'Decryption' => [ 'shape' => 'SrtCallerDecryptionRequest', 'locationName' => 'decryption', ], 'MinimumLatency' => [ 'shape' => '__integer', 'locationName' => 'minimumLatency', ], 'SrtListenerAddress' => [ 'shape' => '__string', 'locationName' => 'srtListenerAddress', ], 'SrtListenerPort' => [ 'shape' => '__string', 'locationName' => 'srtListenerPort', ], 'StreamId' => [ 'shape' => '__string', 'locationName' => 'streamId', ], ], ], 'SrtSettings' => [ 'type' => 'structure', 'members' => [ 'SrtCallerSources' => [ 'shape' => '__listOfSrtCallerSource', 'locationName' => 'srtCallerSources', ], ], ], 'SrtSettingsRequest' => [ 'type' => 'structure', 'members' => [ 'SrtCallerSources' => [ 'shape' => '__listOfSrtCallerSourceRequest', 'locationName' => 'srtCallerSources', ], ], ], '__listOfSrtCallerSource' => [ 'type' => 'list', 'member' => [ 'shape' => 'SrtCallerSource', ], ], '__listOfSrtCallerSourceRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'SrtCallerSourceRequest', ], ], 'MultiplexPacketIdentifiersMapping' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => 'MultiplexProgramPacketIdentifiersMap', ], ], '__integerMin1Max51' => [ 'type' => 'integer', 'min' => 1, 'max' => 51, ], 'AnywhereSettings' => [ 'type' => 'structure', 'members' => [ 'ChannelPlacementGroupId' => [ 'shape' => '__string', 'locationName' => 'channelPlacementGroupId', ], 'ClusterId' => [ 'shape' => '__string', 'locationName' => 'clusterId', ], ], ], 'Av1ColorSpaceSettings' => [ 'type' => 'structure', 'members' => [ 'ColorSpacePassthroughSettings' => [ 'shape' => 'ColorSpacePassthroughSettings', 'locationName' => 'colorSpacePassthroughSettings', ], 'Hdr10Settings' => [ 'shape' => 'Hdr10Settings', 'locationName' => 'hdr10Settings', ], 'Rec601Settings' => [ 'shape' => 'Rec601Settings', 'locationName' => 'rec601Settings', ], 'Rec709Settings' => [ 'shape' => 'Rec709Settings', 'locationName' => 'rec709Settings', ], ], ], 'Av1GopSizeUnits' => [ 'type' => 'string', 'enum' => [ 'FRAMES', 'SECONDS', ], ], 'Av1Level' => [ 'type' => 'string', 'enum' => [ 'AV1_LEVEL_2', 'AV1_LEVEL_2_1', 'AV1_LEVEL_3', 'AV1_LEVEL_3_1', 'AV1_LEVEL_4', 'AV1_LEVEL_4_1', 'AV1_LEVEL_5', 'AV1_LEVEL_5_1', 'AV1_LEVEL_5_2', 'AV1_LEVEL_5_3', 'AV1_LEVEL_6', 'AV1_LEVEL_6_1', 'AV1_LEVEL_6_2', 'AV1_LEVEL_6_3', 'AV1_LEVEL_AUTO', ], ], 'Av1LookAheadRateControl' => [ 'type' => 'string', 'enum' => [ 'HIGH', 'LOW', 'MEDIUM', ], ], 'Av1SceneChangeDetect' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'Av1Settings' => [ 'type' => 'structure', 'members' => [ 'AfdSignaling' => [ 'shape' => 'AfdSignaling', 'locationName' => 'afdSignaling', ], 'BufSize' => [ 'shape' => '__integerMin50000Max16000000', 'locationName' => 'bufSize', ], 'ColorSpaceSettings' => [ 'shape' => 'Av1ColorSpaceSettings', 'locationName' => 'colorSpaceSettings', ], 'FixedAfd' => [ 'shape' => 'FixedAfd', 'locationName' => 'fixedAfd', ], 'FramerateDenominator' => [ 'shape' => '__integerMin1Max3003', 'locationName' => 'framerateDenominator', ], 'FramerateNumerator' => [ 'shape' => '__integerMin1', 'locationName' => 'framerateNumerator', ], 'GopSize' => [ 'shape' => '__doubleMin0', 'locationName' => 'gopSize', ], 'GopSizeUnits' => [ 'shape' => 'Av1GopSizeUnits', 'locationName' => 'gopSizeUnits', ], 'Level' => [ 'shape' => 'Av1Level', 'locationName' => 'level', ], 'LookAheadRateControl' => [ 'shape' => 'Av1LookAheadRateControl', 'locationName' => 'lookAheadRateControl', ], 'MaxBitrate' => [ 'shape' => '__integerMin50000Max8000000', 'locationName' => 'maxBitrate', ], 'MinIInterval' => [ 'shape' => '__integerMin0Max30', 'locationName' => 'minIInterval', ], 'ParDenominator' => [ 'shape' => '__integerMin1', 'locationName' => 'parDenominator', ], 'ParNumerator' => [ 'shape' => '__integerMin1', 'locationName' => 'parNumerator', ], 'QvbrQualityLevel' => [ 'shape' => '__integerMin1Max10', 'locationName' => 'qvbrQualityLevel', ], 'SceneChangeDetect' => [ 'shape' => 'Av1SceneChangeDetect', 'locationName' => 'sceneChangeDetect', ], 'TimecodeBurninSettings' => [ 'shape' => 'TimecodeBurninSettings', 'locationName' => 'timecodeBurninSettings', ], 'Bitrate' => [ 'shape' => '__integerMin50000Max8000000', 'locationName' => 'bitrate', ], 'RateControlMode' => [ 'shape' => 'Av1RateControlMode', 'locationName' => 'rateControlMode', ], ], 'required' => [ 'FramerateNumerator', 'FramerateDenominator', ], ], 'ChannelPlacementGroupState' => [ 'type' => 'string', 'enum' => [ 'UNASSIGNED', 'ASSIGNING', 'ASSIGNED', 'DELETING', 'DELETE_FAILED', 'DELETED', 'UNASSIGNING', ], ], 'ClusterNetworkSettings' => [ 'type' => 'structure', 'members' => [ 'DefaultRoute' => [ 'shape' => '__string', 'locationName' => 'defaultRoute', ], 'InterfaceMappings' => [ 'shape' => '__listOfInterfaceMapping', 'locationName' => 'interfaceMappings', ], ], ], 'ClusterNetworkSettingsCreateRequest' => [ 'type' => 'structure', 'members' => [ 'DefaultRoute' => [ 'shape' => '__string', 'locationName' => 'defaultRoute', ], 'InterfaceMappings' => [ 'shape' => '__listOfInterfaceMappingCreateRequest', 'locationName' => 'interfaceMappings', ], ], ], 'ClusterNetworkSettingsUpdateRequest' => [ 'type' => 'structure', 'members' => [ 'DefaultRoute' => [ 'shape' => '__string', 'locationName' => 'defaultRoute', ], 'InterfaceMappings' => [ 'shape' => '__listOfInterfaceMappingUpdateRequest', 'locationName' => 'interfaceMappings', ], ], ], 'ClusterState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATE_FAILED', 'ACTIVE', 'DELETING', 'DELETE_FAILED', 'DELETED', ], ], 'ClusterType' => [ 'type' => 'string', 'enum' => [ 'ON_PREMISES', ], ], 'CreateChannelPlacementGroupRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterId', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'Nodes' => [ 'shape' => '__listOf__string', 'locationName' => 'nodes', ], 'RequestId' => [ 'shape' => '__string', 'locationName' => 'requestId', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], ], 'required' => [ 'ClusterId', ], ], 'CreateChannelPlacementGroupResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'Channels' => [ 'shape' => '__listOf__string', 'locationName' => 'channels', ], 'ClusterId' => [ 'shape' => '__string', 'locationName' => 'clusterId', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'Nodes' => [ 'shape' => '__listOf__string', 'locationName' => 'nodes', ], 'State' => [ 'shape' => 'ChannelPlacementGroupState', 'locationName' => 'state', ], ], ], 'CreateClusterRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterType' => [ 'shape' => 'ClusterType', 'locationName' => 'clusterType', ], 'InstanceRoleArn' => [ 'shape' => '__string', 'locationName' => 'instanceRoleArn', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NetworkSettings' => [ 'shape' => 'ClusterNetworkSettingsCreateRequest', 'locationName' => 'networkSettings', ], 'RequestId' => [ 'shape' => '__string', 'locationName' => 'requestId', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], ], ], 'CreateClusterResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'ChannelIds' => [ 'shape' => '__listOf__string', 'locationName' => 'channelIds', ], 'ClusterType' => [ 'shape' => 'ClusterType', 'locationName' => 'clusterType', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'InstanceRoleArn' => [ 'shape' => '__string', 'locationName' => 'instanceRoleArn', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NetworkSettings' => [ 'shape' => 'ClusterNetworkSettings', 'locationName' => 'networkSettings', ], 'State' => [ 'shape' => 'ClusterState', 'locationName' => 'state', ], ], ], 'CreateNetworkRequest' => [ 'type' => 'structure', 'members' => [ 'IpPools' => [ 'shape' => '__listOfIpPoolCreateRequest', 'locationName' => 'ipPools', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'RequestId' => [ 'shape' => '__string', 'locationName' => 'requestId', 'idempotencyToken' => true, ], 'Routes' => [ 'shape' => '__listOfRouteCreateRequest', 'locationName' => 'routes', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], ], ], 'CreateNetworkResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'AssociatedClusterIds' => [ 'shape' => '__listOf__string', 'locationName' => 'associatedClusterIds', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'IpPools' => [ 'shape' => '__listOfIpPool', 'locationName' => 'ipPools', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'Routes' => [ 'shape' => '__listOfRoute', 'locationName' => 'routes', ], 'State' => [ 'shape' => 'NetworkState', 'locationName' => 'state', ], ], ], 'CreateNodeRegistrationScriptRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterId', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NodeInterfaceMappings' => [ 'shape' => '__listOfNodeInterfaceMapping', 'locationName' => 'nodeInterfaceMappings', ], 'RequestId' => [ 'shape' => '__string', 'locationName' => 'requestId', 'idempotencyToken' => true, ], 'Role' => [ 'shape' => 'NodeRole', 'locationName' => 'role', ], ], 'required' => [ 'ClusterId', ], ], 'CreateNodeRegistrationScriptResponse' => [ 'type' => 'structure', 'members' => [ 'NodeRegistrationScript' => [ 'shape' => '__string', 'locationName' => 'nodeRegistrationScript', ], ], ], 'CreateNodeRegistrationScriptResult' => [ 'type' => 'structure', 'members' => [ 'NodeRegistrationScript' => [ 'shape' => '__string', 'locationName' => 'nodeRegistrationScript', ], ], ], 'CreateNodeRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterId', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NodeInterfaceMappings' => [ 'shape' => '__listOfNodeInterfaceMappingCreateRequest', 'locationName' => 'nodeInterfaceMappings', ], 'RequestId' => [ 'shape' => '__string', 'locationName' => 'requestId', 'idempotencyToken' => true, ], 'Role' => [ 'shape' => 'NodeRole', 'locationName' => 'role', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], ], 'required' => [ 'ClusterId', ], ], 'CreateNodeResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'ChannelPlacementGroups' => [ 'shape' => '__listOf__string', 'locationName' => 'channelPlacementGroups', ], 'ClusterId' => [ 'shape' => '__string', 'locationName' => 'clusterId', ], 'ConnectionState' => [ 'shape' => 'NodeConnectionState', 'locationName' => 'connectionState', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'InstanceArn' => [ 'shape' => '__string', 'locationName' => 'instanceArn', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NodeInterfaceMappings' => [ 'shape' => '__listOfNodeInterfaceMapping', 'locationName' => 'nodeInterfaceMappings', ], 'Role' => [ 'shape' => 'NodeRole', 'locationName' => 'role', ], 'State' => [ 'shape' => 'NodeState', 'locationName' => 'state', ], 'SdiSourceMappings' => [ 'shape' => 'SdiSourceMappings', 'locationName' => 'sdiSourceMappings', ], ], ], 'DeleteChannelPlacementGroupRequest' => [ 'type' => 'structure', 'members' => [ 'ChannelPlacementGroupId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'channelPlacementGroupId', ], 'ClusterId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterId', ], ], 'required' => [ 'ClusterId', 'ChannelPlacementGroupId', ], ], 'DeleteChannelPlacementGroupResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'Channels' => [ 'shape' => '__listOf__string', 'locationName' => 'channels', ], 'ClusterId' => [ 'shape' => '__string', 'locationName' => 'clusterId', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'Nodes' => [ 'shape' => '__listOf__string', 'locationName' => 'nodes', ], 'State' => [ 'shape' => 'ChannelPlacementGroupState', 'locationName' => 'state', ], ], ], 'DeleteClusterRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterId', ], ], 'required' => [ 'ClusterId', ], ], 'DeleteClusterResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'ChannelIds' => [ 'shape' => '__listOf__string', 'locationName' => 'channelIds', ], 'ClusterType' => [ 'shape' => 'ClusterType', 'locationName' => 'clusterType', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'InstanceRoleArn' => [ 'shape' => '__string', 'locationName' => 'instanceRoleArn', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NetworkSettings' => [ 'shape' => 'ClusterNetworkSettings', 'locationName' => 'networkSettings', ], 'State' => [ 'shape' => 'ClusterState', 'locationName' => 'state', ], ], ], 'DeleteNetworkRequest' => [ 'type' => 'structure', 'members' => [ 'NetworkId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'networkId', ], ], 'required' => [ 'NetworkId', ], ], 'DeleteNetworkResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'AssociatedClusterIds' => [ 'shape' => '__listOf__string', 'locationName' => 'associatedClusterIds', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'IpPools' => [ 'shape' => '__listOfIpPool', 'locationName' => 'ipPools', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'Routes' => [ 'shape' => '__listOfRoute', 'locationName' => 'routes', ], 'State' => [ 'shape' => 'NetworkState', 'locationName' => 'state', ], ], ], 'DeleteNodeRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterId', ], 'NodeId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'nodeId', ], ], 'required' => [ 'NodeId', 'ClusterId', ], ], 'DeleteNodeResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'ChannelPlacementGroups' => [ 'shape' => '__listOf__string', 'locationName' => 'channelPlacementGroups', ], 'ClusterId' => [ 'shape' => '__string', 'locationName' => 'clusterId', ], 'ConnectionState' => [ 'shape' => 'NodeConnectionState', 'locationName' => 'connectionState', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'InstanceArn' => [ 'shape' => '__string', 'locationName' => 'instanceArn', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NodeInterfaceMappings' => [ 'shape' => '__listOfNodeInterfaceMapping', 'locationName' => 'nodeInterfaceMappings', ], 'Role' => [ 'shape' => 'NodeRole', 'locationName' => 'role', ], 'State' => [ 'shape' => 'NodeState', 'locationName' => 'state', ], 'SdiSourceMappings' => [ 'shape' => 'SdiSourceMappings', 'locationName' => 'sdiSourceMappings', ], ], ], 'DescribeAnywhereSettings' => [ 'type' => 'structure', 'members' => [ 'ChannelPlacementGroupId' => [ 'shape' => '__string', 'locationName' => 'channelPlacementGroupId', ], 'ClusterId' => [ 'shape' => '__string', 'locationName' => 'clusterId', ], ], ], 'DescribeChannelPlacementGroupRequest' => [ 'type' => 'structure', 'members' => [ 'ChannelPlacementGroupId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'channelPlacementGroupId', ], 'ClusterId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterId', ], ], 'required' => [ 'ClusterId', 'ChannelPlacementGroupId', ], ], 'DescribeChannelPlacementGroupResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'Channels' => [ 'shape' => '__listOf__string', 'locationName' => 'channels', ], 'ClusterId' => [ 'shape' => '__string', 'locationName' => 'clusterId', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'Nodes' => [ 'shape' => '__listOf__string', 'locationName' => 'nodes', ], 'State' => [ 'shape' => 'ChannelPlacementGroupState', 'locationName' => 'state', ], ], ], 'DescribeChannelPlacementGroupResult' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'Channels' => [ 'shape' => '__listOf__string', 'locationName' => 'channels', ], 'ClusterId' => [ 'shape' => '__string', 'locationName' => 'clusterId', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'Nodes' => [ 'shape' => '__listOf__string', 'locationName' => 'nodes', ], 'State' => [ 'shape' => 'ChannelPlacementGroupState', 'locationName' => 'state', ], ], ], 'DescribeChannelPlacementGroupSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'Channels' => [ 'shape' => '__listOf__string', 'locationName' => 'channels', ], 'ClusterId' => [ 'shape' => '__string', 'locationName' => 'clusterId', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'Nodes' => [ 'shape' => '__listOf__string', 'locationName' => 'nodes', ], 'State' => [ 'shape' => 'ChannelPlacementGroupState', 'locationName' => 'state', ], ], ], 'DescribeClusterRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterId', ], ], 'required' => [ 'ClusterId', ], ], 'DescribeClusterResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'ChannelIds' => [ 'shape' => '__listOf__string', 'locationName' => 'channelIds', ], 'ClusterType' => [ 'shape' => 'ClusterType', 'locationName' => 'clusterType', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'InstanceRoleArn' => [ 'shape' => '__string', 'locationName' => 'instanceRoleArn', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NetworkSettings' => [ 'shape' => 'ClusterNetworkSettings', 'locationName' => 'networkSettings', ], 'State' => [ 'shape' => 'ClusterState', 'locationName' => 'state', ], ], ], 'DescribeClusterResult' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'ChannelIds' => [ 'shape' => '__listOf__string', 'locationName' => 'channelIds', ], 'ClusterType' => [ 'shape' => 'ClusterType', 'locationName' => 'clusterType', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'InstanceRoleArn' => [ 'shape' => '__string', 'locationName' => 'instanceRoleArn', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NetworkSettings' => [ 'shape' => 'ClusterNetworkSettings', 'locationName' => 'networkSettings', ], 'State' => [ 'shape' => 'ClusterState', 'locationName' => 'state', ], ], ], 'DescribeClusterSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'ChannelIds' => [ 'shape' => '__listOf__string', 'locationName' => 'channelIds', ], 'ClusterType' => [ 'shape' => 'ClusterType', 'locationName' => 'clusterType', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'InstanceRoleArn' => [ 'shape' => '__string', 'locationName' => 'instanceRoleArn', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NetworkSettings' => [ 'shape' => 'ClusterNetworkSettings', 'locationName' => 'networkSettings', ], 'State' => [ 'shape' => 'ClusterState', 'locationName' => 'state', ], ], ], 'DescribeNetworkRequest' => [ 'type' => 'structure', 'members' => [ 'NetworkId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'networkId', ], ], 'required' => [ 'NetworkId', ], ], 'DescribeNetworkResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'AssociatedClusterIds' => [ 'shape' => '__listOf__string', 'locationName' => 'associatedClusterIds', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'IpPools' => [ 'shape' => '__listOfIpPool', 'locationName' => 'ipPools', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'Routes' => [ 'shape' => '__listOfRoute', 'locationName' => 'routes', ], 'State' => [ 'shape' => 'NetworkState', 'locationName' => 'state', ], ], ], 'DescribeNetworkResult' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'AssociatedClusterIds' => [ 'shape' => '__listOf__string', 'locationName' => 'associatedClusterIds', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'IpPools' => [ 'shape' => '__listOfIpPool', 'locationName' => 'ipPools', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'Routes' => [ 'shape' => '__listOfRoute', 'locationName' => 'routes', ], 'State' => [ 'shape' => 'NetworkState', 'locationName' => 'state', ], ], ], 'DescribeNetworkSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'AssociatedClusterIds' => [ 'shape' => '__listOf__string', 'locationName' => 'associatedClusterIds', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'IpPools' => [ 'shape' => '__listOfIpPool', 'locationName' => 'ipPools', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'Routes' => [ 'shape' => '__listOfRoute', 'locationName' => 'routes', ], 'State' => [ 'shape' => 'NetworkState', 'locationName' => 'state', ], ], ], 'DescribeNodeRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterId', ], 'NodeId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'nodeId', ], ], 'required' => [ 'NodeId', 'ClusterId', ], ], 'DescribeNodeResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'ChannelPlacementGroups' => [ 'shape' => '__listOf__string', 'locationName' => 'channelPlacementGroups', ], 'ClusterId' => [ 'shape' => '__string', 'locationName' => 'clusterId', ], 'ConnectionState' => [ 'shape' => 'NodeConnectionState', 'locationName' => 'connectionState', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'InstanceArn' => [ 'shape' => '__string', 'locationName' => 'instanceArn', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NodeInterfaceMappings' => [ 'shape' => '__listOfNodeInterfaceMapping', 'locationName' => 'nodeInterfaceMappings', ], 'Role' => [ 'shape' => 'NodeRole', 'locationName' => 'role', ], 'State' => [ 'shape' => 'NodeState', 'locationName' => 'state', ], 'SdiSourceMappings' => [ 'shape' => 'SdiSourceMappings', 'locationName' => 'sdiSourceMappings', ], ], ], 'DescribeNodeResult' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'ChannelPlacementGroups' => [ 'shape' => '__listOf__string', 'locationName' => 'channelPlacementGroups', ], 'ClusterId' => [ 'shape' => '__string', 'locationName' => 'clusterId', ], 'ConnectionState' => [ 'shape' => 'NodeConnectionState', 'locationName' => 'connectionState', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'InstanceArn' => [ 'shape' => '__string', 'locationName' => 'instanceArn', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NodeInterfaceMappings' => [ 'shape' => '__listOfNodeInterfaceMapping', 'locationName' => 'nodeInterfaceMappings', ], 'Role' => [ 'shape' => 'NodeRole', 'locationName' => 'role', ], 'State' => [ 'shape' => 'NodeState', 'locationName' => 'state', ], 'SdiSourceMappings' => [ 'shape' => 'SdiSourceMappings', 'locationName' => 'sdiSourceMappings', ], ], ], 'DescribeNodeSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'ChannelPlacementGroups' => [ 'shape' => '__listOf__string', 'locationName' => 'channelPlacementGroups', ], 'ClusterId' => [ 'shape' => '__string', 'locationName' => 'clusterId', ], 'ConnectionState' => [ 'shape' => 'NodeConnectionState', 'locationName' => 'connectionState', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'InstanceArn' => [ 'shape' => '__string', 'locationName' => 'instanceArn', ], 'ManagedInstanceId' => [ 'shape' => '__string', 'locationName' => 'managedInstanceId', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NodeInterfaceMappings' => [ 'shape' => '__listOfNodeInterfaceMapping', 'locationName' => 'nodeInterfaceMappings', ], 'Role' => [ 'shape' => 'NodeRole', 'locationName' => 'role', ], 'State' => [ 'shape' => 'NodeState', 'locationName' => 'state', ], 'SdiSourceMappings' => [ 'shape' => 'SdiSourceMappings', 'locationName' => 'sdiSourceMappings', ], ], ], 'InputDestinationRoute' => [ 'type' => 'structure', 'members' => [ 'Cidr' => [ 'shape' => '__string', 'locationName' => 'cidr', ], 'Gateway' => [ 'shape' => '__string', 'locationName' => 'gateway', ], ], ], 'InputNetworkLocation' => [ 'type' => 'string', 'enum' => [ 'AWS', 'ON_PREMISES', ], ], 'InputRequestDestinationRoute' => [ 'type' => 'structure', 'members' => [ 'Cidr' => [ 'shape' => '__string', 'locationName' => 'cidr', ], 'Gateway' => [ 'shape' => '__string', 'locationName' => 'gateway', ], ], ], 'InterfaceMapping' => [ 'type' => 'structure', 'members' => [ 'LogicalInterfaceName' => [ 'shape' => '__string', 'locationName' => 'logicalInterfaceName', ], 'NetworkId' => [ 'shape' => '__string', 'locationName' => 'networkId', ], ], ], 'InterfaceMappingCreateRequest' => [ 'type' => 'structure', 'members' => [ 'LogicalInterfaceName' => [ 'shape' => '__string', 'locationName' => 'logicalInterfaceName', ], 'NetworkId' => [ 'shape' => '__string', 'locationName' => 'networkId', ], ], ], 'InterfaceMappingUpdateRequest' => [ 'type' => 'structure', 'members' => [ 'LogicalInterfaceName' => [ 'shape' => '__string', 'locationName' => 'logicalInterfaceName', ], 'NetworkId' => [ 'shape' => '__string', 'locationName' => 'networkId', ], ], ], 'IpPool' => [ 'type' => 'structure', 'members' => [ 'Cidr' => [ 'shape' => '__string', 'locationName' => 'cidr', ], ], ], 'IpPoolCreateRequest' => [ 'type' => 'structure', 'members' => [ 'Cidr' => [ 'shape' => '__string', 'locationName' => 'cidr', ], ], ], 'IpPoolUpdateRequest' => [ 'type' => 'structure', 'members' => [ 'Cidr' => [ 'shape' => '__string', 'locationName' => 'cidr', ], ], ], 'ListChannelPlacementGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], 'required' => [ 'ClusterId', ], ], 'ListChannelPlacementGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'ChannelPlacementGroups' => [ 'shape' => '__listOfDescribeChannelPlacementGroupSummary', 'locationName' => 'channelPlacementGroups', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListChannelPlacementGroupsResult' => [ 'type' => 'structure', 'members' => [ 'ChannelPlacementGroups' => [ 'shape' => '__listOfDescribeChannelPlacementGroupSummary', 'locationName' => 'channelPlacementGroups', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListClustersRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListClustersResponse' => [ 'type' => 'structure', 'members' => [ 'Clusters' => [ 'shape' => '__listOfDescribeClusterSummary', 'locationName' => 'clusters', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListClustersResult' => [ 'type' => 'structure', 'members' => [ 'Clusters' => [ 'shape' => '__listOfDescribeClusterSummary', 'locationName' => 'clusters', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListNetworksRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListNetworksResponse' => [ 'type' => 'structure', 'members' => [ 'Networks' => [ 'shape' => '__listOfDescribeNetworkSummary', 'locationName' => 'networks', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListNetworksResult' => [ 'type' => 'structure', 'members' => [ 'Networks' => [ 'shape' => '__listOfDescribeNetworkSummary', 'locationName' => 'networks', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListNodesRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], 'required' => [ 'ClusterId', ], ], 'ListNodesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'Nodes' => [ 'shape' => '__listOfDescribeNodeSummary', 'locationName' => 'nodes', ], ], ], 'ListNodesResult' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'Nodes' => [ 'shape' => '__listOfDescribeNodeSummary', 'locationName' => 'nodes', ], ], ], 'MulticastInputSettings' => [ 'type' => 'structure', 'members' => [ 'SourceIpAddress' => [ 'shape' => '__string', 'locationName' => 'sourceIpAddress', ], ], ], 'MulticastSettings' => [ 'type' => 'structure', 'members' => [ 'Sources' => [ 'shape' => '__listOfMulticastSource', 'locationName' => 'sources', ], ], ], 'MulticastSettingsCreateRequest' => [ 'type' => 'structure', 'members' => [ 'Sources' => [ 'shape' => '__listOfMulticastSourceCreateRequest', 'locationName' => 'sources', ], ], ], 'MulticastSettingsUpdateRequest' => [ 'type' => 'structure', 'members' => [ 'Sources' => [ 'shape' => '__listOfMulticastSourceUpdateRequest', 'locationName' => 'sources', ], ], ], 'MulticastSource' => [ 'type' => 'structure', 'members' => [ 'SourceIp' => [ 'shape' => '__string', 'locationName' => 'sourceIp', ], 'Url' => [ 'shape' => '__string', 'locationName' => 'url', ], ], 'required' => [ 'Url', ], ], 'MulticastSourceCreateRequest' => [ 'type' => 'structure', 'members' => [ 'SourceIp' => [ 'shape' => '__string', 'locationName' => 'sourceIp', ], 'Url' => [ 'shape' => '__string', 'locationName' => 'url', ], ], 'required' => [ 'Url', ], ], 'MulticastSourceUpdateRequest' => [ 'type' => 'structure', 'members' => [ 'SourceIp' => [ 'shape' => '__string', 'locationName' => 'sourceIp', ], 'Url' => [ 'shape' => '__string', 'locationName' => 'url', ], ], 'required' => [ 'Url', ], ], 'NetworkInterfaceMode' => [ 'type' => 'string', 'enum' => [ 'NAT', 'BRIDGE', ], ], 'NetworkState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATE_FAILED', 'ACTIVE', 'DELETING', 'IDLE', 'IN_USE', 'UPDATING', 'DELETE_FAILED', 'DELETED', ], ], 'NodeConfigurationValidationError' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], 'ValidationErrors' => [ 'shape' => '__listOfValidationError', 'locationName' => 'validationErrors', ], ], ], 'NodeConnectionState' => [ 'type' => 'string', 'enum' => [ 'CONNECTED', 'DISCONNECTED', ], ], 'NodeInterfaceMapping' => [ 'type' => 'structure', 'members' => [ 'LogicalInterfaceName' => [ 'shape' => '__string', 'locationName' => 'logicalInterfaceName', ], 'NetworkInterfaceMode' => [ 'shape' => 'NetworkInterfaceMode', 'locationName' => 'networkInterfaceMode', ], 'PhysicalInterfaceName' => [ 'shape' => '__string', 'locationName' => 'physicalInterfaceName', ], ], ], 'NodeInterfaceMappingCreateRequest' => [ 'type' => 'structure', 'members' => [ 'LogicalInterfaceName' => [ 'shape' => '__string', 'locationName' => 'logicalInterfaceName', ], 'NetworkInterfaceMode' => [ 'shape' => 'NetworkInterfaceMode', 'locationName' => 'networkInterfaceMode', ], 'PhysicalInterfaceName' => [ 'shape' => '__string', 'locationName' => 'physicalInterfaceName', ], ], ], 'NodeRole' => [ 'type' => 'string', 'enum' => [ 'BACKUP', 'ACTIVE', ], ], 'NodeState' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'REGISTERING', 'READY_TO_ACTIVATE', 'REGISTRATION_FAILED', 'ACTIVATION_FAILED', 'ACTIVE', 'READY', 'IN_USE', 'DEREGISTERING', 'DRAINING', 'DEREGISTRATION_FAILED', 'DEREGISTERED', ], ], 'Route' => [ 'type' => 'structure', 'members' => [ 'Cidr' => [ 'shape' => '__string', 'locationName' => 'cidr', ], 'Gateway' => [ 'shape' => '__string', 'locationName' => 'gateway', ], ], ], 'RouteCreateRequest' => [ 'type' => 'structure', 'members' => [ 'Cidr' => [ 'shape' => '__string', 'locationName' => 'cidr', ], 'Gateway' => [ 'shape' => '__string', 'locationName' => 'gateway', ], ], ], 'RouteUpdateRequest' => [ 'type' => 'structure', 'members' => [ 'Cidr' => [ 'shape' => '__string', 'locationName' => 'cidr', ], 'Gateway' => [ 'shape' => '__string', 'locationName' => 'gateway', ], ], ], 'SrtEncryptionType' => [ 'type' => 'string', 'enum' => [ 'AES128', 'AES192', 'AES256', ], ], 'SrtGroupSettings' => [ 'type' => 'structure', 'members' => [ 'InputLossAction' => [ 'shape' => 'InputLossActionForUdpOut', 'locationName' => 'inputLossAction', ], ], ], 'SrtOutputDestinationSettings' => [ 'type' => 'structure', 'members' => [ 'EncryptionPassphraseSecretArn' => [ 'shape' => '__string', 'locationName' => 'encryptionPassphraseSecretArn', ], 'StreamId' => [ 'shape' => '__string', 'locationName' => 'streamId', ], 'Url' => [ 'shape' => '__string', 'locationName' => 'url', ], ], ], 'SrtOutputSettings' => [ 'type' => 'structure', 'members' => [ 'BufferMsec' => [ 'shape' => '__integerMin0Max10000', 'locationName' => 'bufferMsec', ], 'ContainerSettings' => [ 'shape' => 'UdpContainerSettings', 'locationName' => 'containerSettings', ], 'Destination' => [ 'shape' => 'OutputLocationRef', 'locationName' => 'destination', ], 'EncryptionType' => [ 'shape' => 'SrtEncryptionType', 'locationName' => 'encryptionType', ], 'Latency' => [ 'shape' => '__integerMin40Max16000', 'locationName' => 'latency', ], ], 'required' => [ 'Destination', 'ContainerSettings', ], ], 'UpdateChannelPlacementGroupRequest' => [ 'type' => 'structure', 'members' => [ 'ChannelPlacementGroupId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'channelPlacementGroupId', ], 'ClusterId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterId', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'Nodes' => [ 'shape' => '__listOf__string', 'locationName' => 'nodes', ], ], 'required' => [ 'ClusterId', 'ChannelPlacementGroupId', ], ], 'UpdateChannelPlacementGroupResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'Channels' => [ 'shape' => '__listOf__string', 'locationName' => 'channels', ], 'ClusterId' => [ 'shape' => '__string', 'locationName' => 'clusterId', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'Nodes' => [ 'shape' => '__listOf__string', 'locationName' => 'nodes', ], 'State' => [ 'shape' => 'ChannelPlacementGroupState', 'locationName' => 'state', ], ], ], 'UpdateClusterRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterId', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NetworkSettings' => [ 'shape' => 'ClusterNetworkSettingsUpdateRequest', 'locationName' => 'networkSettings', ], ], 'required' => [ 'ClusterId', ], ], 'UpdateClusterResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'ChannelIds' => [ 'shape' => '__listOf__string', 'locationName' => 'channelIds', ], 'ClusterType' => [ 'shape' => 'ClusterType', 'locationName' => 'clusterType', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NetworkSettings' => [ 'shape' => 'ClusterNetworkSettings', 'locationName' => 'networkSettings', ], 'State' => [ 'shape' => 'ClusterState', 'locationName' => 'state', ], ], ], 'UpdateClusterResult' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'ChannelIds' => [ 'shape' => '__listOf__string', 'locationName' => 'channelIds', ], 'ClusterType' => [ 'shape' => 'ClusterType', 'locationName' => 'clusterType', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NetworkSettings' => [ 'shape' => 'ClusterNetworkSettings', 'locationName' => 'networkSettings', ], 'State' => [ 'shape' => 'ClusterState', 'locationName' => 'state', ], ], ], 'UpdateNetworkRequest' => [ 'type' => 'structure', 'members' => [ 'IpPools' => [ 'shape' => '__listOfIpPoolUpdateRequest', 'locationName' => 'ipPools', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NetworkId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'networkId', ], 'Routes' => [ 'shape' => '__listOfRouteUpdateRequest', 'locationName' => 'routes', ], ], 'required' => [ 'NetworkId', ], ], 'UpdateNetworkResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'AssociatedClusterIds' => [ 'shape' => '__listOf__string', 'locationName' => 'associatedClusterIds', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'IpPools' => [ 'shape' => '__listOfIpPool', 'locationName' => 'ipPools', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'Routes' => [ 'shape' => '__listOfRoute', 'locationName' => 'routes', ], 'State' => [ 'shape' => 'NetworkState', 'locationName' => 'state', ], ], ], 'UpdateNetworkResult' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'AssociatedClusterIds' => [ 'shape' => '__listOf__string', 'locationName' => 'associatedClusterIds', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'IpPools' => [ 'shape' => '__listOfIpPool', 'locationName' => 'ipPools', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'Routes' => [ 'shape' => '__listOfRoute', 'locationName' => 'routes', ], 'State' => [ 'shape' => 'NetworkState', 'locationName' => 'state', ], ], ], 'UpdateNodeRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterId', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NodeId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'nodeId', ], 'Role' => [ 'shape' => 'NodeRole', 'locationName' => 'role', ], 'SdiSourceMappings' => [ 'shape' => 'SdiSourceMappingsUpdateRequest', 'locationName' => 'sdiSourceMappings', ], ], 'required' => [ 'NodeId', 'ClusterId', ], ], 'UpdateNodeResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'ChannelPlacementGroups' => [ 'shape' => '__listOf__string', 'locationName' => 'channelPlacementGroups', ], 'ClusterId' => [ 'shape' => '__string', 'locationName' => 'clusterId', ], 'ConnectionState' => [ 'shape' => 'NodeConnectionState', 'locationName' => 'connectionState', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'InstanceArn' => [ 'shape' => '__string', 'locationName' => 'instanceArn', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NodeInterfaceMappings' => [ 'shape' => '__listOfNodeInterfaceMapping', 'locationName' => 'nodeInterfaceMappings', ], 'Role' => [ 'shape' => 'NodeRole', 'locationName' => 'role', ], 'State' => [ 'shape' => 'NodeState', 'locationName' => 'state', ], 'SdiSourceMappings' => [ 'shape' => 'SdiSourceMappings', 'locationName' => 'sdiSourceMappings', ], ], ], 'UpdateNodeState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'DRAINING', ], ], 'UpdateNodeStateRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'clusterId', ], 'NodeId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'nodeId', ], 'State' => [ 'shape' => 'UpdateNodeState', 'locationName' => 'state', ], ], 'required' => [ 'NodeId', 'ClusterId', ], ], 'UpdateNodeStateResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'ChannelPlacementGroups' => [ 'shape' => '__listOf__string', 'locationName' => 'channelPlacementGroups', ], 'ClusterId' => [ 'shape' => '__string', 'locationName' => 'clusterId', ], 'ConnectionState' => [ 'shape' => 'NodeConnectionState', 'locationName' => 'connectionState', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'InstanceArn' => [ 'shape' => '__string', 'locationName' => 'instanceArn', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'NodeInterfaceMappings' => [ 'shape' => '__listOfNodeInterfaceMapping', 'locationName' => 'nodeInterfaceMappings', ], 'Role' => [ 'shape' => 'NodeRole', 'locationName' => 'role', ], 'State' => [ 'shape' => 'NodeState', 'locationName' => 'state', ], 'SdiSourceMappings' => [ 'shape' => 'SdiSourceMappings', 'locationName' => 'sdiSourceMappings', ], ], ], '__integerMin40Max16000' => [ 'type' => 'integer', 'min' => 40, 'max' => 16000, ], '__integerMin50000Max16000000' => [ 'type' => 'integer', 'min' => 50000, 'max' => 16000000, ], '__integerMin50000Max8000000' => [ 'type' => 'integer', 'min' => 50000, 'max' => 8000000, ], '__listOfDescribeChannelPlacementGroupSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'DescribeChannelPlacementGroupSummary', ], ], '__listOfDescribeClusterSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'DescribeClusterSummary', ], ], '__listOfDescribeNetworkSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'DescribeNetworkSummary', ], ], '__listOfDescribeNodeSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'DescribeNodeSummary', ], ], '__listOfInputDestinationRoute' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputDestinationRoute', ], ], '__listOfInputRequestDestinationRoute' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputRequestDestinationRoute', ], ], '__listOfInterfaceMapping' => [ 'type' => 'list', 'member' => [ 'shape' => 'InterfaceMapping', ], ], '__listOfInterfaceMappingCreateRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'InterfaceMappingCreateRequest', ], ], '__listOfInterfaceMappingUpdateRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'InterfaceMappingUpdateRequest', ], ], '__listOfIpPool' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpPool', ], ], '__listOfIpPoolCreateRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpPoolCreateRequest', ], ], '__listOfIpPoolUpdateRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpPoolUpdateRequest', ], ], '__listOfMulticastSource' => [ 'type' => 'list', 'member' => [ 'shape' => 'MulticastSource', ], ], '__listOfMulticastSourceCreateRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'MulticastSourceCreateRequest', ], ], '__listOfMulticastSourceUpdateRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'MulticastSourceUpdateRequest', ], ], '__listOfNodeInterfaceMapping' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeInterfaceMapping', ], ], '__listOfNodeInterfaceMappingCreateRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeInterfaceMappingCreateRequest', ], ], '__listOfRoute' => [ 'type' => 'list', 'member' => [ 'shape' => 'Route', ], ], '__listOfRouteCreateRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteCreateRequest', ], ], '__listOfRouteUpdateRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteUpdateRequest', ], ], '__listOfSrtOutputDestinationSettings' => [ 'type' => 'list', 'member' => [ 'shape' => 'SrtOutputDestinationSettings', ], ], 'BandwidthReductionFilterSettings' => [ 'type' => 'structure', 'members' => [ 'PostFilterSharpening' => [ 'shape' => 'BandwidthReductionPostFilterSharpening', 'locationName' => 'postFilterSharpening', ], 'Strength' => [ 'shape' => 'BandwidthReductionFilterStrength', 'locationName' => 'strength', ], ], ], 'BandwidthReductionFilterStrength' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'STRENGTH_1', 'STRENGTH_2', 'STRENGTH_3', 'STRENGTH_4', ], ], 'BandwidthReductionPostFilterSharpening' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'SHARPENING_1', 'SHARPENING_2', 'SHARPENING_3', ], ], 'MultiplexContainerSettings' => [ 'type' => 'structure', 'members' => [ 'MultiplexM2tsSettings' => [ 'shape' => 'MultiplexM2tsSettings', 'locationName' => 'multiplexM2tsSettings', ], ], ], 'MultiplexM2tsSettings' => [ 'type' => 'structure', 'members' => [ 'AbsentInputAudioBehavior' => [ 'shape' => 'M2tsAbsentInputAudioBehavior', 'locationName' => 'absentInputAudioBehavior', ], 'Arib' => [ 'shape' => 'M2tsArib', 'locationName' => 'arib', ], 'AudioBufferModel' => [ 'shape' => 'M2tsAudioBufferModel', 'locationName' => 'audioBufferModel', ], 'AudioFramesPerPes' => [ 'shape' => '__integerMin0', 'locationName' => 'audioFramesPerPes', ], 'AudioStreamType' => [ 'shape' => 'M2tsAudioStreamType', 'locationName' => 'audioStreamType', ], 'CcDescriptor' => [ 'shape' => 'M2tsCcDescriptor', 'locationName' => 'ccDescriptor', ], 'Ebif' => [ 'shape' => 'M2tsEbifControl', 'locationName' => 'ebif', ], 'EsRateInPes' => [ 'shape' => 'M2tsEsRateInPes', 'locationName' => 'esRateInPes', ], 'Klv' => [ 'shape' => 'M2tsKlv', 'locationName' => 'klv', ], 'NielsenId3Behavior' => [ 'shape' => 'M2tsNielsenId3Behavior', 'locationName' => 'nielsenId3Behavior', ], 'PcrControl' => [ 'shape' => 'M2tsPcrControl', 'locationName' => 'pcrControl', ], 'PcrPeriod' => [ 'shape' => '__integerMin0Max500', 'locationName' => 'pcrPeriod', ], 'Scte35Control' => [ 'shape' => 'M2tsScte35Control', 'locationName' => 'scte35Control', ], 'Scte35PrerollPullupMilliseconds' => [ 'shape' => '__doubleMin0Max5000', 'locationName' => 'scte35PrerollPullupMilliseconds', ], ], ], 'H265Deblocking' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'CmafKLVBehavior' => [ 'type' => 'string', 'enum' => [ 'NO_PASSTHROUGH', 'PASSTHROUGH', ], ], '__stringMax100' => [ 'type' => 'string', 'max' => 100, ], 'ChannelEngineVersionRequest' => [ 'type' => 'structure', 'members' => [ 'Version' => [ 'shape' => '__string', 'locationName' => 'version', ], ], ], 'ChannelEngineVersionResponse' => [ 'type' => 'structure', 'members' => [ 'ExpirationDate' => [ 'shape' => '__timestampIso8601', 'locationName' => 'expirationDate', ], 'Version' => [ 'shape' => '__string', 'locationName' => 'version', ], ], ], 'ListVersionsRequest' => [ 'type' => 'structure', 'members' => [], ], 'ListVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'Versions' => [ 'shape' => '__listOfChannelEngineVersionResponse', 'locationName' => 'versions', ], ], ], '__listOfChannelEngineVersionResponse' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelEngineVersionResponse', ], ], 'CmafId3Behavior' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'Id3SegmentTaggingScheduleActionSettings' => [ 'type' => 'structure', 'members' => [ 'Id3' => [ 'shape' => '__string', 'locationName' => 'id3', ], 'Tag' => [ 'shape' => '__string', 'locationName' => 'tag', ], ], ], 'TimedMetadataScheduleActionSettings' => [ 'type' => 'structure', 'members' => [ 'Id3' => [ 'shape' => '__string', 'locationName' => 'id3', ], ], 'required' => [ 'Id3', ], ], '__stringMin1Max256PatternS' => [ 'type' => 'string', 'min' => 1, 'max' => 256, 'pattern' => '^[\\S]+$', ], '__integerMin1Max800' => [ 'type' => 'integer', 'min' => 1, 'max' => 800, ], '__integerMin80Max800' => [ 'type' => 'integer', 'min' => 80, 'max' => 800, ], 'InputSdpLocation' => [ 'type' => 'structure', 'members' => [ 'MediaIndex' => [ 'shape' => '__integer', 'locationName' => 'mediaIndex', ], 'SdpUrl' => [ 'shape' => '__string', 'locationName' => 'sdpUrl', ], ], ], 'Smpte2110ReceiverGroup' => [ 'type' => 'structure', 'members' => [ 'SdpSettings' => [ 'shape' => 'Smpte2110ReceiverGroupSdpSettings', 'locationName' => 'sdpSettings', ], ], ], 'Smpte2110ReceiverGroupSdpSettings' => [ 'type' => 'structure', 'members' => [ 'AncillarySdps' => [ 'shape' => '__listOfInputSdpLocation', 'locationName' => 'ancillarySdps', ], 'AudioSdps' => [ 'shape' => '__listOfInputSdpLocation', 'locationName' => 'audioSdps', ], 'VideoSdp' => [ 'shape' => 'InputSdpLocation', 'locationName' => 'videoSdp', ], ], ], 'Smpte2110ReceiverGroupSettings' => [ 'type' => 'structure', 'members' => [ 'Smpte2110ReceiverGroups' => [ 'shape' => '__listOfSmpte2110ReceiverGroup', 'locationName' => 'smpte2110ReceiverGroups', ], ], ], '__listOfInputSdpLocation' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputSdpLocation', ], ], '__listOfSmpte2110ReceiverGroup' => [ 'type' => 'list', 'member' => [ 'shape' => 'Smpte2110ReceiverGroup', ], ], 'CreateSdiSourceRequest' => [ 'type' => 'structure', 'members' => [ 'Mode' => [ 'shape' => 'SdiSourceMode', 'locationName' => 'mode', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'RequestId' => [ 'shape' => '__string', 'locationName' => 'requestId', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'tags', ], 'Type' => [ 'shape' => 'SdiSourceType', 'locationName' => 'type', ], ], ], 'CreateSdiSourceResponse' => [ 'type' => 'structure', 'members' => [ 'SdiSource' => [ 'shape' => 'SdiSource', 'locationName' => 'sdiSource', ], ], ], 'DeleteSdiSourceRequest' => [ 'type' => 'structure', 'members' => [ 'SdiSourceId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'sdiSourceId', ], ], 'required' => [ 'SdiSourceId', ], ], 'DeleteSdiSourceResponse' => [ 'type' => 'structure', 'members' => [ 'SdiSource' => [ 'shape' => 'SdiSource', 'locationName' => 'sdiSource', ], ], ], 'DescribeSdiSourceRequest' => [ 'type' => 'structure', 'members' => [ 'SdiSourceId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'sdiSourceId', ], ], 'required' => [ 'SdiSourceId', ], ], 'DescribeSdiSourceResponse' => [ 'type' => 'structure', 'members' => [ 'SdiSource' => [ 'shape' => 'SdiSource', 'locationName' => 'sdiSource', ], ], ], 'InputSdiSources' => [ 'type' => 'list', 'member' => [ 'shape' => '__string', ], ], 'ListSdiSourcesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListSdiSourcesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'SdiSources' => [ 'shape' => '__listOfSdiSourceSummary', 'locationName' => 'sdiSources', ], ], ], 'SdiSource' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'Inputs' => [ 'shape' => '__listOf__string', 'locationName' => 'inputs', ], 'Mode' => [ 'shape' => 'SdiSourceMode', 'locationName' => 'mode', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'State' => [ 'shape' => 'SdiSourceState', 'locationName' => 'state', ], 'Type' => [ 'shape' => 'SdiSourceType', 'locationName' => 'type', ], ], ], 'SdiSourceMapping' => [ 'type' => 'structure', 'members' => [ 'CardNumber' => [ 'shape' => '__integer', 'locationName' => 'cardNumber', ], 'ChannelNumber' => [ 'shape' => '__integer', 'locationName' => 'channelNumber', ], 'SdiSource' => [ 'shape' => '__string', 'locationName' => 'sdiSource', ], ], ], 'SdiSourceMappingUpdateRequest' => [ 'type' => 'structure', 'members' => [ 'CardNumber' => [ 'shape' => '__integer', 'locationName' => 'cardNumber', ], 'ChannelNumber' => [ 'shape' => '__integer', 'locationName' => 'channelNumber', ], 'SdiSource' => [ 'shape' => '__string', 'locationName' => 'sdiSource', ], ], ], 'SdiSourceMappings' => [ 'type' => 'list', 'member' => [ 'shape' => 'SdiSourceMapping', ], ], 'SdiSourceMappingsUpdateRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'SdiSourceMappingUpdateRequest', ], ], 'SdiSourceMode' => [ 'type' => 'string', 'enum' => [ 'QUADRANT', 'INTERLEAVE', ], ], 'SdiSourceState' => [ 'type' => 'string', 'enum' => [ 'IDLE', 'IN_USE', 'DELETED', ], ], 'SdiSourceSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'Inputs' => [ 'shape' => '__listOf__string', 'locationName' => 'inputs', ], 'Mode' => [ 'shape' => 'SdiSourceMode', 'locationName' => 'mode', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'State' => [ 'shape' => 'SdiSourceState', 'locationName' => 'state', ], 'Type' => [ 'shape' => 'SdiSourceType', 'locationName' => 'type', ], ], ], 'SdiSourceType' => [ 'type' => 'string', 'enum' => [ 'SINGLE', 'QUAD', ], ], 'UpdateSdiSourceRequest' => [ 'type' => 'structure', 'members' => [ 'Mode' => [ 'shape' => 'SdiSourceMode', 'locationName' => 'mode', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'SdiSourceId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'sdiSourceId', ], 'Type' => [ 'shape' => 'SdiSourceType', 'locationName' => 'type', ], ], 'required' => [ 'SdiSourceId', ], ], 'UpdateSdiSourceResponse' => [ 'type' => 'structure', 'members' => [ 'SdiSource' => [ 'shape' => 'SdiSource', 'locationName' => 'sdiSource', ], ], ], '__listOfSdiSourceSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'SdiSourceSummary', ], ], 'CmafIngestCaptionLanguageMapping' => [ 'type' => 'structure', 'members' => [ 'CaptionChannel' => [ 'shape' => '__integerMin1Max4', 'locationName' => 'captionChannel', ], 'LanguageCode' => [ 'shape' => '__stringMin3Max3', 'locationName' => 'languageCode', ], ], 'required' => [ 'LanguageCode', 'CaptionChannel', ], ], 'CmafTimedMetadataId3Frame' => [ 'type' => 'string', 'enum' => [ 'NONE', 'PRIV', 'TDRL', ], ], 'CmafTimedMetadataPassthrough' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], '__listOfCmafIngestCaptionLanguageMapping' => [ 'type' => 'list', 'member' => [ 'shape' => 'CmafIngestCaptionLanguageMapping', ], ], 'Av1RateControlMode' => [ 'type' => 'string', 'enum' => [ 'CBR', 'QVBR', ], ], ],];
