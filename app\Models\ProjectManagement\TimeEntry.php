<?php

namespace App\Models\ProjectManagement;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use App\Models\User;
use App\Traits\ProjectManagement\HasTimestamps;
use App\Traits\ProjectManagement\Searchable;

/**
 * نموذج سجل تتبع الوقت المتقدم
 * 
 * هذا النموذج يمثل سجلات الوقت المستغرق في المهام والمشاريع
 * ويوفر إمكانيات متقدمة لتتبع الوقت وحساب التكاليف
 * 
 * @package App\Models\ProjectManagement
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 * 
 * @property int $id معرف السجل الفريد
 * @property int $user_id معرف المستخدم
 * @property int $project_id معرف المشروع
 * @property int|null $task_id معرف المهمة
 * @property string|null $description وصف العمل المنجز
 * @property float $hours عدد الساعات
 * @property Carbon $date تاريخ العمل
 * @property Carbon|null $start_time وقت البداية
 * @property Carbon|null $end_time وقت النهاية
 * @property bool $is_billable قابل للفوترة
 * @property float|null $hourly_rate السعر بالساعة
 * @property float|null $total_cost التكلفة الإجمالية
 * @property string|null $invoice_id معرف الفاتورة
 * @property array|null $metadata بيانات إضافية
 * @property Carbon $created_at تاريخ الإنشاء
 * @property Carbon $updated_at تاريخ آخر تحديث
 * @property Carbon|null $deleted_at تاريخ الحذف الناعم
 */
class TimeEntry extends Model
{
    use HasFactory, SoftDeletes, HasTimestamps, Searchable;

    /**
     * اسم الجدول في قاعدة البيانات
     * 
     * @var string
     */
    protected $table = 'time_entries';

    /**
     * الحقول القابلة للتعبئة الجماعية
     * 
     * @var array<string>
     */
    protected $fillable = [
        'user_id',
        'project_id',
        'task_id',
        'description',
        'hours',
        'date',
        'start_time',
        'end_time',
        'is_billable',
        'hourly_rate',
        'total_cost',
        'invoice_id',
        'metadata',
    ];

    /**
     * الحقول المخفية من التسلسل
     * 
     * @var array<string>
     */
    protected $hidden = [
        'deleted_at',
    ];

    /**
     * تحويل أنواع البيانات
     * 
     * @var array<string, string>
     */
    protected $casts = [
        'hours' => 'float',
        'date' => 'date',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'is_billable' => 'boolean',
        'hourly_rate' => 'float',
        'total_cost' => 'float',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * الحقول القابلة للبحث
     * 
     * @var array<string>
     */
    protected $searchable = [
        'description',
    ];

    /**
     * القيم الافتراضية للحقول
     * 
     * @var array<string, mixed>
     */
    protected $attributes = [
        'is_billable' => true,
        'hours' => 0,
    ];

    /**
     * أحداث النموذج
     * 
     * @return void
     */
    protected static function boot(): void
    {
        parent::boot();

        // حساب عدد الساعات تلقائياً من وقت البداية والنهاية
        static::saving(function ($timeEntry) {
            if ($timeEntry->start_time && $timeEntry->end_time) {
                $hours = $timeEntry->start_time->diffInMinutes($timeEntry->end_time) / 60;
                $timeEntry->hours = round($hours, 2);
            }

            // حساب التكلفة الإجمالية
            if ($timeEntry->hours && $timeEntry->hourly_rate) {
                $timeEntry->total_cost = $timeEntry->hours * $timeEntry->hourly_rate;
            }
        });

        // تحديث الساعات الفعلية في المهمة عند الحفظ
        static::saved(function ($timeEntry) {
            if ($timeEntry->task) {
                $timeEntry->task->updateActualHours();
            }
        });

        // تحديث الساعات الفعلية في المهمة عند الحذف
        static::deleted(function ($timeEntry) {
            if ($timeEntry->task) {
                $timeEntry->task->updateActualHours();
            }
        });
    }

    /*
    |--------------------------------------------------------------------------
    | العلاقات (Relationships)
    |--------------------------------------------------------------------------
    */

    /**
     * العلاقة مع المستخدم
     * 
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع المشروع
     * 
     * @return BelongsTo
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * العلاقة مع المهمة
     * 
     * @return BelongsTo
     */
    public function task(): BelongsTo
    {
        return $this->belongsTo(Task::class);
    }

    /*
    |--------------------------------------------------------------------------
    | النطاقات (Scopes)
    |--------------------------------------------------------------------------
    */

    /**
     * نطاق السجلات القابلة للفوترة
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeBillable(Builder $query): Builder
    {
        return $query->where('is_billable', true);
    }

    /**
     * نطاق السجلات غير المفوترة
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeUnbilled(Builder $query): Builder
    {
        return $query->whereNull('invoice_id')
                    ->where('is_billable', true);
    }

    /**
     * نطاق السجلات المفوترة
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeBilled(Builder $query): Builder
    {
        return $query->whereNotNull('invoice_id');
    }

    /**
     * نطاق السجلات حسب المستخدم
     * 
     * @param Builder $query
     * @param int $userId
     * @return Builder
     */
    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * نطاق السجلات حسب المشروع
     * 
     * @param Builder $query
     * @param int $projectId
     * @return Builder
     */
    public function scopeByProject(Builder $query, int $projectId): Builder
    {
        return $query->where('project_id', $projectId);
    }

    /**
     * نطاق السجلات حسب المهمة
     * 
     * @param Builder $query
     * @param int $taskId
     * @return Builder
     */
    public function scopeByTask(Builder $query, int $taskId): Builder
    {
        return $query->where('task_id', $taskId);
    }

    /**
     * نطاق السجلات حسب فترة زمنية
     * 
     * @param Builder $query
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return Builder
     */
    public function scopeByDateRange(Builder $query, Carbon $startDate, Carbon $endDate): Builder
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * نطاق السجلات لهذا الأسبوع
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeThisWeek(Builder $query): Builder
    {
        return $query->whereBetween('date', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    /**
     * نطاق السجلات لهذا الشهر
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeThisMonth(Builder $query): Builder
    {
        return $query->whereBetween('date', [
            now()->startOfMonth(),
            now()->endOfMonth()
        ]);
    }

    /*
    |--------------------------------------------------------------------------
    | الطرق المساعدة (Helper Methods)
    |--------------------------------------------------------------------------
    */

    /**
     * التحقق من كون السجل قابل للفوترة
     * 
     * @return bool
     */
    public function isBillable(): bool
    {
        return $this->is_billable;
    }

    /**
     * التحقق من كون السجل مفوتر
     * 
     * @return bool
     */
    public function isBilled(): bool
    {
        return !is_null($this->invoice_id);
    }

    /**
     * حساب المدة بالدقائق
     * 
     * @return int|null
     */
    public function getDurationInMinutes(): ?int
    {
        if (!$this->start_time || !$this->end_time) {
            return null;
        }

        return $this->start_time->diffInMinutes($this->end_time);
    }

    /**
     * تنسيق المدة كنص
     * 
     * @return string
     */
    public function getFormattedDuration(): string
    {
        $hours = floor($this->hours);
        $minutes = ($this->hours - $hours) * 60;
        
        return sprintf('%02d:%02d', $hours, $minutes);
    }

    /**
     * حساب التكلفة بناءً على السعر المحدد
     * 
     * @param float|null $hourlyRate
     * @return float
     */
    public function calculateCost(?float $hourlyRate = null): float
    {
        $rate = $hourlyRate ?? $this->hourly_rate ?? 0;
        return $this->hours * $rate;
    }

    /**
     * تحديد السجل كمفوتر
     * 
     * @param string $invoiceId
     * @return void
     */
    public function markAsBilled(string $invoiceId): void
    {
        $this->update(['invoice_id' => $invoiceId]);
    }

    /**
     * إلغاء فوترة السجل
     * 
     * @return void
     */
    public function unmarkBilled(): void
    {
        $this->update(['invoice_id' => null]);
    }

    /**
     * الحصول على وصف مختصر للسجل
     * 
     * @return string
     */
    public function getShortDescription(): string
    {
        if ($this->description) {
            return \Str::limit($this->description, 50);
        }

        $taskName = $this->task ? $this->task->title : 'عمل عام';
        return "عمل على: {$taskName}";
    }

    /**
     * تحويل الوقت إلى تنسيق قابل للقراءة
     * 
     * @return string
     */
    public function getReadableTimeAttribute(): string
    {
        if ($this->hours < 1) {
            $minutes = round($this->hours * 60);
            return "{$minutes} دقيقة";
        }

        $hours = floor($this->hours);
        $minutes = round(($this->hours - $hours) * 60);

        if ($minutes > 0) {
            return "{$hours} ساعة و {$minutes} دقيقة";
        }

        return "{$hours} ساعة";
    }

    /**
     * الحصول على لون الحالة بناءً على نوع السجل
     * 
     * @return string
     */
    public function getStatusColorAttribute(): string
    {
        if ($this->isBilled()) {
            return '#28a745'; // أخضر للمفوتر
        }

        if ($this->isBillable()) {
            return '#ffc107'; // أصفر للقابل للفوترة
        }

        return '#6c757d'; // رمادي لغير القابل للفوترة
    }

    /**
     * الحصول على تسمية الحالة
     * 
     * @return string
     */
    public function getStatusLabelAttribute(): string
    {
        if ($this->isBilled()) {
            return 'مفوتر';
        }

        if ($this->isBillable()) {
            return 'قابل للفوترة';
        }

        return 'غير قابل للفوترة';
    }
}
