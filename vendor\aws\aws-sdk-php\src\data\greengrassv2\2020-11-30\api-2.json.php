<?php
// This file was auto-generated from sdk-root/src/data/greengrassv2/2020-11-30/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-11-30', 'endpointPrefix' => 'greengrass', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceAbbreviation' => 'AWS GreengrassV2', 'serviceFullName' => 'AWS IoT Greengrass V2', 'serviceId' => 'GreengrassV2', 'signatureVersion' => 'v4', 'uid' => 'greengrassv2-2020-11-30', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AssociateServiceRoleToAccount' => [ 'name' => 'AssociateServiceRoleToAccount', 'http' => [ 'method' => 'PUT', 'requestUri' => '/greengrass/servicerole', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateServiceRoleToAccountRequest', ], 'output' => [ 'shape' => 'AssociateServiceRoleToAccountResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchAssociateClientDeviceWithCoreDevice' => [ 'name' => 'BatchAssociateClientDeviceWithCoreDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/greengrass/v2/coreDevices/{coreDeviceThingName}/associateClientDevices', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchAssociateClientDeviceWithCoreDeviceRequest', ], 'output' => [ 'shape' => 'BatchAssociateClientDeviceWithCoreDeviceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'BatchDisassociateClientDeviceFromCoreDevice' => [ 'name' => 'BatchDisassociateClientDeviceFromCoreDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/greengrass/v2/coreDevices/{coreDeviceThingName}/disassociateClientDevices', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchDisassociateClientDeviceFromCoreDeviceRequest', ], 'output' => [ 'shape' => 'BatchDisassociateClientDeviceFromCoreDeviceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CancelDeployment' => [ 'name' => 'CancelDeployment', 'http' => [ 'method' => 'POST', 'requestUri' => '/greengrass/v2/deployments/{deploymentId}/cancel', ], 'input' => [ 'shape' => 'CancelDeploymentRequest', ], 'output' => [ 'shape' => 'CancelDeploymentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateComponentVersion' => [ 'name' => 'CreateComponentVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/greengrass/v2/createComponentVersion', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateComponentVersionRequest', ], 'output' => [ 'shape' => 'CreateComponentVersionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'RequestAlreadyInProgressException', ], ], ], 'CreateDeployment' => [ 'name' => 'CreateDeployment', 'http' => [ 'method' => 'POST', 'requestUri' => '/greengrass/v2/deployments', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateDeploymentRequest', ], 'output' => [ 'shape' => 'CreateDeploymentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'RequestAlreadyInProgressException', ], ], ], 'DeleteComponent' => [ 'name' => 'DeleteComponent', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/greengrass/v2/components/{arn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteComponentRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteCoreDevice' => [ 'name' => 'DeleteCoreDevice', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/greengrass/v2/coreDevices/{coreDeviceThingName}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteCoreDeviceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteDeployment' => [ 'name' => 'DeleteDeployment', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/greengrass/v2/deployments/{deploymentId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteDeploymentRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeComponent' => [ 'name' => 'DescribeComponent', 'http' => [ 'method' => 'GET', 'requestUri' => '/greengrass/v2/components/{arn}/metadata', ], 'input' => [ 'shape' => 'DescribeComponentRequest', ], 'output' => [ 'shape' => 'DescribeComponentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DisassociateServiceRoleFromAccount' => [ 'name' => 'DisassociateServiceRoleFromAccount', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/greengrass/servicerole', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateServiceRoleFromAccountRequest', ], 'output' => [ 'shape' => 'DisassociateServiceRoleFromAccountResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], ], ], 'GetComponent' => [ 'name' => 'GetComponent', 'http' => [ 'method' => 'GET', 'requestUri' => '/greengrass/v2/components/{arn}', ], 'input' => [ 'shape' => 'GetComponentRequest', ], 'output' => [ 'shape' => 'GetComponentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetComponentVersionArtifact' => [ 'name' => 'GetComponentVersionArtifact', 'http' => [ 'method' => 'GET', 'requestUri' => '/greengrass/v2/components/{arn}/artifacts/{artifactName+}', ], 'input' => [ 'shape' => 'GetComponentVersionArtifactRequest', ], 'output' => [ 'shape' => 'GetComponentVersionArtifactResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetConnectivityInfo' => [ 'name' => 'GetConnectivityInfo', 'http' => [ 'method' => 'GET', 'requestUri' => '/greengrass/things/{thingName}/connectivityInfo', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetConnectivityInfoRequest', ], 'output' => [ 'shape' => 'GetConnectivityInfoResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetCoreDevice' => [ 'name' => 'GetCoreDevice', 'http' => [ 'method' => 'GET', 'requestUri' => '/greengrass/v2/coreDevices/{coreDeviceThingName}', ], 'input' => [ 'shape' => 'GetCoreDeviceRequest', ], 'output' => [ 'shape' => 'GetCoreDeviceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetDeployment' => [ 'name' => 'GetDeployment', 'http' => [ 'method' => 'GET', 'requestUri' => '/greengrass/v2/deployments/{deploymentId}', ], 'input' => [ 'shape' => 'GetDeploymentRequest', ], 'output' => [ 'shape' => 'GetDeploymentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetServiceRoleForAccount' => [ 'name' => 'GetServiceRoleForAccount', 'http' => [ 'method' => 'GET', 'requestUri' => '/greengrass/servicerole', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetServiceRoleForAccountRequest', ], 'output' => [ 'shape' => 'GetServiceRoleForAccountResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], ], ], 'ListClientDevicesAssociatedWithCoreDevice' => [ 'name' => 'ListClientDevicesAssociatedWithCoreDevice', 'http' => [ 'method' => 'GET', 'requestUri' => '/greengrass/v2/coreDevices/{coreDeviceThingName}/associatedClientDevices', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListClientDevicesAssociatedWithCoreDeviceRequest', ], 'output' => [ 'shape' => 'ListClientDevicesAssociatedWithCoreDeviceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListComponentVersions' => [ 'name' => 'ListComponentVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/greengrass/v2/components/{arn}/versions', ], 'input' => [ 'shape' => 'ListComponentVersionsRequest', ], 'output' => [ 'shape' => 'ListComponentVersionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListComponents' => [ 'name' => 'ListComponents', 'http' => [ 'method' => 'GET', 'requestUri' => '/greengrass/v2/components', ], 'input' => [ 'shape' => 'ListComponentsRequest', ], 'output' => [ 'shape' => 'ListComponentsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListCoreDevices' => [ 'name' => 'ListCoreDevices', 'http' => [ 'method' => 'GET', 'requestUri' => '/greengrass/v2/coreDevices', ], 'input' => [ 'shape' => 'ListCoreDevicesRequest', ], 'output' => [ 'shape' => 'ListCoreDevicesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListDeployments' => [ 'name' => 'ListDeployments', 'http' => [ 'method' => 'GET', 'requestUri' => '/greengrass/v2/deployments', ], 'input' => [ 'shape' => 'ListDeploymentsRequest', ], 'output' => [ 'shape' => 'ListDeploymentsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListEffectiveDeployments' => [ 'name' => 'ListEffectiveDeployments', 'http' => [ 'method' => 'GET', 'requestUri' => '/greengrass/v2/coreDevices/{coreDeviceThingName}/effectiveDeployments', ], 'input' => [ 'shape' => 'ListEffectiveDeploymentsRequest', ], 'output' => [ 'shape' => 'ListEffectiveDeploymentsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListInstalledComponents' => [ 'name' => 'ListInstalledComponents', 'http' => [ 'method' => 'GET', 'requestUri' => '/greengrass/v2/coreDevices/{coreDeviceThingName}/installedComponents', ], 'input' => [ 'shape' => 'ListInstalledComponentsRequest', ], 'output' => [ 'shape' => 'ListInstalledComponentsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ResolveComponentCandidates' => [ 'name' => 'ResolveComponentCandidates', 'http' => [ 'method' => 'POST', 'requestUri' => '/greengrass/v2/resolveComponentCandidates', ], 'input' => [ 'shape' => 'ResolveComponentCandidatesRequest', ], 'output' => [ 'shape' => 'ResolveComponentCandidatesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateConnectivityInfo' => [ 'name' => 'UpdateConnectivityInfo', 'http' => [ 'method' => 'PUT', 'requestUri' => '/greengrass/things/{thingName}/connectivityInfo', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateConnectivityInfoRequest', ], 'output' => [ 'shape' => 'UpdateConnectivityInfoResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AssociateClientDeviceWithCoreDeviceEntry' => [ 'type' => 'structure', 'required' => [ 'thingName', ], 'members' => [ 'thingName' => [ 'shape' => 'IoTThingName', ], ], ], 'AssociateClientDeviceWithCoreDeviceEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociateClientDeviceWithCoreDeviceEntry', ], 'max' => 100, 'min' => 1, ], 'AssociateClientDeviceWithCoreDeviceErrorEntry' => [ 'type' => 'structure', 'members' => [ 'thingName' => [ 'shape' => 'IoTThingName', ], 'code' => [ 'shape' => 'NonEmptyString', ], 'message' => [ 'shape' => 'NonEmptyString', ], ], ], 'AssociateClientDeviceWithCoreDeviceErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociateClientDeviceWithCoreDeviceErrorEntry', ], 'max' => 100, ], 'AssociateServiceRoleToAccountRequest' => [ 'type' => 'structure', 'required' => [ 'roleArn', ], 'members' => [ 'roleArn' => [ 'shape' => 'String', 'locationName' => 'RoleArn', ], ], ], 'AssociateServiceRoleToAccountResponse' => [ 'type' => 'structure', 'members' => [ 'associatedAt' => [ 'shape' => 'String', 'locationName' => 'AssociatedAt', ], ], ], 'AssociatedClientDevice' => [ 'type' => 'structure', 'members' => [ 'thingName' => [ 'shape' => 'IoTThingName', ], 'associationTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'AssociatedClientDeviceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociatedClientDevice', ], 'max' => 100, 'min' => 1, ], 'BatchAssociateClientDeviceWithCoreDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'coreDeviceThingName', ], 'members' => [ 'entries' => [ 'shape' => 'AssociateClientDeviceWithCoreDeviceEntryList', ], 'coreDeviceThingName' => [ 'shape' => 'IoTThingName', 'location' => 'uri', 'locationName' => 'coreDeviceThingName', ], ], ], 'BatchAssociateClientDeviceWithCoreDeviceResponse' => [ 'type' => 'structure', 'members' => [ 'errorEntries' => [ 'shape' => 'AssociateClientDeviceWithCoreDeviceErrorList', ], ], ], 'BatchDisassociateClientDeviceFromCoreDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'coreDeviceThingName', ], 'members' => [ 'entries' => [ 'shape' => 'DisassociateClientDeviceFromCoreDeviceEntryList', ], 'coreDeviceThingName' => [ 'shape' => 'IoTThingName', 'location' => 'uri', 'locationName' => 'coreDeviceThingName', ], ], ], 'BatchDisassociateClientDeviceFromCoreDeviceResponse' => [ 'type' => 'structure', 'members' => [ 'errorEntries' => [ 'shape' => 'DisassociateClientDeviceFromCoreDeviceErrorList', ], ], ], 'CPU' => [ 'type' => 'double', 'min' => 0, ], 'CancelDeploymentRequest' => [ 'type' => 'structure', 'required' => [ 'deploymentId', ], 'members' => [ 'deploymentId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'deploymentId', ], ], ], 'CancelDeploymentResponse' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonEmptyString', ], ], ], 'ClientTokenString' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-]+$', ], 'CloudComponentState' => [ 'type' => 'string', 'enum' => [ 'REQUESTED', 'INITIATED', 'DEPLOYABLE', 'FAILED', 'DEPRECATED', ], ], 'CloudComponentStatus' => [ 'type' => 'structure', 'members' => [ 'componentState' => [ 'shape' => 'CloudComponentState', ], 'message' => [ 'shape' => 'NonEmptyString', ], 'errors' => [ 'shape' => 'StringMap', ], 'vendorGuidance' => [ 'shape' => 'VendorGuidance', ], 'vendorGuidanceMessage' => [ 'shape' => 'NonEmptyString', ], ], ], 'Component' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ComponentARN', ], 'componentName' => [ 'shape' => 'ComponentNameString', ], 'latestVersion' => [ 'shape' => 'ComponentLatestVersion', ], ], ], 'ComponentARN' => [ 'type' => 'string', 'pattern' => 'arn:[^:]*:greengrass:[^:]*:(aws|[0-9]+):components:[^:]+', ], 'ComponentCandidate' => [ 'type' => 'structure', 'members' => [ 'componentName' => [ 'shape' => 'ComponentNameString', ], 'componentVersion' => [ 'shape' => 'ComponentVersionString', ], 'versionRequirements' => [ 'shape' => 'ComponentVersionRequirementMap', ], ], ], 'ComponentCandidateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentCandidate', ], ], 'ComponentConfigurationPath' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'ComponentConfigurationPathList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentConfigurationPath', ], ], 'ComponentConfigurationString' => [ 'type' => 'string', 'max' => 10485760, 'min' => 1, ], 'ComponentConfigurationUpdate' => [ 'type' => 'structure', 'members' => [ 'merge' => [ 'shape' => 'ComponentConfigurationString', ], 'reset' => [ 'shape' => 'ComponentConfigurationPathList', ], ], ], 'ComponentDependencyMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'ComponentDependencyRequirement', ], ], 'ComponentDependencyRequirement' => [ 'type' => 'structure', 'members' => [ 'versionRequirement' => [ 'shape' => 'NonEmptyString', ], 'dependencyType' => [ 'shape' => 'ComponentDependencyType', ], ], ], 'ComponentDependencyType' => [ 'type' => 'string', 'enum' => [ 'HARD', 'SOFT', ], ], 'ComponentDeploymentSpecification' => [ 'type' => 'structure', 'required' => [ 'componentVersion', ], 'members' => [ 'componentVersion' => [ 'shape' => 'ComponentVersionString', ], 'configurationUpdate' => [ 'shape' => 'ComponentConfigurationUpdate', ], 'runWith' => [ 'shape' => 'ComponentRunWith', ], ], ], 'ComponentDeploymentSpecifications' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'ComponentDeploymentSpecification', ], ], 'ComponentLatestVersion' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ComponentVersionARN', ], 'componentVersion' => [ 'shape' => 'ComponentVersionString', ], 'creationTimestamp' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'publisher' => [ 'shape' => 'NonEmptyString', ], 'platforms' => [ 'shape' => 'ComponentPlatformList', ], ], ], 'ComponentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Component', ], ], 'ComponentNameString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'ComponentPlatform' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'NonEmptyString', ], 'attributes' => [ 'shape' => 'PlatformAttributesMap', ], ], ], 'ComponentPlatformList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentPlatform', ], ], 'ComponentRunWith' => [ 'type' => 'structure', 'members' => [ 'posixUser' => [ 'shape' => 'NonEmptyString', ], 'systemResourceLimits' => [ 'shape' => 'SystemResourceLimits', ], 'windowsUser' => [ 'shape' => 'NonEmptyString', ], ], ], 'ComponentVersionARN' => [ 'type' => 'string', 'pattern' => 'arn:[^:]*:greengrass:[^:]*:(aws|[0-9]+):components:[^:]+:versions:[^:]+', ], 'ComponentVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentVersionListItem', ], ], 'ComponentVersionListItem' => [ 'type' => 'structure', 'members' => [ 'componentName' => [ 'shape' => 'ComponentNameString', ], 'componentVersion' => [ 'shape' => 'ComponentVersionString', ], 'arn' => [ 'shape' => 'NonEmptyString', ], ], ], 'ComponentVersionRequirementMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'NonEmptyString', ], ], 'ComponentVersionString' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ComponentVisibilityScope' => [ 'type' => 'string', 'enum' => [ 'PRIVATE', 'PUBLIC', ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ConnectivityInfo' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', 'locationName' => 'Id', ], 'hostAddress' => [ 'shape' => 'String', 'locationName' => 'HostAddress', ], 'portNumber' => [ 'shape' => 'PortNumberInt', 'locationName' => 'PortNumber', ], 'metadata' => [ 'shape' => 'String', 'locationName' => 'Metadata', ], ], ], 'CoreDevice' => [ 'type' => 'structure', 'members' => [ 'coreDeviceThingName' => [ 'shape' => 'CoreDeviceThingName', ], 'status' => [ 'shape' => 'CoreDeviceStatus', ], 'lastStatusUpdateTimestamp' => [ 'shape' => 'Timestamp', ], 'platform' => [ 'shape' => 'CoreDevicePlatformString', ], 'architecture' => [ 'shape' => 'CoreDeviceArchitectureString', ], 'runtime' => [ 'shape' => 'CoreDeviceRuntimeString', ], ], ], 'CoreDeviceArchitectureString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'CoreDevicePlatformString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'CoreDeviceRuntimeString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'CoreDeviceStatus' => [ 'type' => 'string', 'enum' => [ 'HEALTHY', 'UNHEALTHY', ], ], 'CoreDeviceThingName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'CoreDevicesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CoreDevice', ], ], 'CreateComponentVersionRequest' => [ 'type' => 'structure', 'members' => [ 'inlineRecipe' => [ 'shape' => 'RecipeBlob', ], 'lambdaFunction' => [ 'shape' => 'LambdaFunctionRecipeSource', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientToken' => [ 'shape' => 'ClientTokenString', 'idempotencyToken' => true, ], ], ], 'CreateComponentVersionResponse' => [ 'type' => 'structure', 'required' => [ 'componentName', 'componentVersion', 'creationTimestamp', 'status', ], 'members' => [ 'arn' => [ 'shape' => 'ComponentVersionARN', ], 'componentName' => [ 'shape' => 'ComponentNameString', ], 'componentVersion' => [ 'shape' => 'ComponentVersionString', ], 'creationTimestamp' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'CloudComponentStatus', ], ], ], 'CreateDeploymentRequest' => [ 'type' => 'structure', 'required' => [ 'targetArn', ], 'members' => [ 'targetArn' => [ 'shape' => 'TargetARN', ], 'deploymentName' => [ 'shape' => 'DeploymentNameString', ], 'components' => [ 'shape' => 'ComponentDeploymentSpecifications', ], 'iotJobConfiguration' => [ 'shape' => 'DeploymentIoTJobConfiguration', ], 'deploymentPolicies' => [ 'shape' => 'DeploymentPolicies', ], 'parentTargetArn' => [ 'shape' => 'ThingGroupARN', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientToken' => [ 'shape' => 'ClientTokenString', 'idempotencyToken' => true, ], ], ], 'CreateDeploymentResponse' => [ 'type' => 'structure', 'members' => [ 'deploymentId' => [ 'shape' => 'NonEmptyString', ], 'iotJobId' => [ 'shape' => 'NonEmptyString', ], 'iotJobArn' => [ 'shape' => 'IoTJobARN', ], ], ], 'DefaultMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'DeleteComponentRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'ComponentVersionARN', 'location' => 'uri', 'locationName' => 'arn', ], ], ], 'DeleteCoreDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'coreDeviceThingName', ], 'members' => [ 'coreDeviceThingName' => [ 'shape' => 'CoreDeviceThingName', 'location' => 'uri', 'locationName' => 'coreDeviceThingName', ], ], ], 'DeleteDeploymentRequest' => [ 'type' => 'structure', 'required' => [ 'deploymentId', ], 'members' => [ 'deploymentId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'deploymentId', ], ], ], 'Deployment' => [ 'type' => 'structure', 'members' => [ 'targetArn' => [ 'shape' => 'TargetARN', ], 'revisionId' => [ 'shape' => 'NonEmptyString', ], 'deploymentId' => [ 'shape' => 'NonEmptyString', ], 'deploymentName' => [ 'shape' => 'NonEmptyString', ], 'creationTimestamp' => [ 'shape' => 'Timestamp', ], 'deploymentStatus' => [ 'shape' => 'DeploymentStatus', ], 'isLatestForTarget' => [ 'shape' => 'IsLatestForTarget', ], 'parentTargetArn' => [ 'shape' => 'ThingGroupARN', ], ], ], 'DeploymentComponentUpdatePolicy' => [ 'type' => 'structure', 'members' => [ 'timeoutInSeconds' => [ 'shape' => 'OptionalInteger', 'box' => true, ], 'action' => [ 'shape' => 'DeploymentComponentUpdatePolicyAction', ], ], ], 'DeploymentComponentUpdatePolicyAction' => [ 'type' => 'string', 'enum' => [ 'NOTIFY_COMPONENTS', 'SKIP_NOTIFY_COMPONENTS', ], ], 'DeploymentConfigurationValidationPolicy' => [ 'type' => 'structure', 'members' => [ 'timeoutInSeconds' => [ 'shape' => 'OptionalInteger', 'box' => true, ], ], ], 'DeploymentFailureHandlingPolicy' => [ 'type' => 'string', 'enum' => [ 'ROLLBACK', 'DO_NOTHING', ], ], 'DeploymentHistoryFilter' => [ 'type' => 'string', 'enum' => [ 'ALL', 'LATEST_ONLY', ], ], 'DeploymentID' => [ 'type' => 'string', ], 'DeploymentIoTJobConfiguration' => [ 'type' => 'structure', 'members' => [ 'jobExecutionsRolloutConfig' => [ 'shape' => 'IoTJobExecutionsRolloutConfig', ], 'abortConfig' => [ 'shape' => 'IoTJobAbortConfig', ], 'timeoutConfig' => [ 'shape' => 'IoTJobTimeoutConfig', ], ], ], 'DeploymentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Deployment', ], ], 'DeploymentName' => [ 'type' => 'string', ], 'DeploymentNameString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'DeploymentPolicies' => [ 'type' => 'structure', 'members' => [ 'failureHandlingPolicy' => [ 'shape' => 'DeploymentFailureHandlingPolicy', ], 'componentUpdatePolicy' => [ 'shape' => 'DeploymentComponentUpdatePolicy', ], 'configurationValidationPolicy' => [ 'shape' => 'DeploymentConfigurationValidationPolicy', ], ], ], 'DeploymentStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'COMPLETED', 'CANCELED', 'FAILED', 'INACTIVE', ], ], 'DescribeComponentRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'ComponentVersionARN', 'location' => 'uri', 'locationName' => 'arn', ], ], ], 'DescribeComponentResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ComponentVersionARN', ], 'componentName' => [ 'shape' => 'ComponentNameString', ], 'componentVersion' => [ 'shape' => 'ComponentVersionString', ], 'creationTimestamp' => [ 'shape' => 'Timestamp', ], 'publisher' => [ 'shape' => 'PublisherString', ], 'description' => [ 'shape' => 'DescriptionString', ], 'status' => [ 'shape' => 'CloudComponentStatus', ], 'platforms' => [ 'shape' => 'ComponentPlatformList', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'DescriptionString' => [ 'type' => 'string', ], 'DisassociateClientDeviceFromCoreDeviceEntry' => [ 'type' => 'structure', 'required' => [ 'thingName', ], 'members' => [ 'thingName' => [ 'shape' => 'IoTThingName', ], ], ], 'DisassociateClientDeviceFromCoreDeviceEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DisassociateClientDeviceFromCoreDeviceEntry', ], 'max' => 100, 'min' => 1, ], 'DisassociateClientDeviceFromCoreDeviceErrorEntry' => [ 'type' => 'structure', 'members' => [ 'thingName' => [ 'shape' => 'IoTThingName', ], 'code' => [ 'shape' => 'NonEmptyString', ], 'message' => [ 'shape' => 'NonEmptyString', ], ], ], 'DisassociateClientDeviceFromCoreDeviceErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DisassociateClientDeviceFromCoreDeviceErrorEntry', ], 'max' => 100, ], 'DisassociateServiceRoleFromAccountRequest' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateServiceRoleFromAccountResponse' => [ 'type' => 'structure', 'members' => [ 'disassociatedAt' => [ 'shape' => 'String', 'locationName' => 'DisassociatedAt', ], ], ], 'EffectiveDeployment' => [ 'type' => 'structure', 'required' => [ 'deploymentId', 'deploymentName', 'targetArn', 'coreDeviceExecutionStatus', 'creationTimestamp', 'modifiedTimestamp', ], 'members' => [ 'deploymentId' => [ 'shape' => 'DeploymentID', ], 'deploymentName' => [ 'shape' => 'DeploymentName', ], 'iotJobId' => [ 'shape' => 'IoTJobId', ], 'iotJobArn' => [ 'shape' => 'IoTJobARN', ], 'description' => [ 'shape' => 'Description', ], 'targetArn' => [ 'shape' => 'TargetARN', ], 'coreDeviceExecutionStatus' => [ 'shape' => 'EffectiveDeploymentExecutionStatus', ], 'reason' => [ 'shape' => 'Reason', ], 'creationTimestamp' => [ 'shape' => 'Timestamp', ], 'modifiedTimestamp' => [ 'shape' => 'Timestamp', ], 'statusDetails' => [ 'shape' => 'EffectiveDeploymentStatusDetails', ], ], ], 'EffectiveDeploymentErrorCode' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'EffectiveDeploymentErrorStack' => [ 'type' => 'list', 'member' => [ 'shape' => 'EffectiveDeploymentErrorCode', ], ], 'EffectiveDeploymentErrorType' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'EffectiveDeploymentErrorTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EffectiveDeploymentErrorType', ], ], 'EffectiveDeploymentExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'QUEUED', 'FAILED', 'COMPLETED', 'TIMED_OUT', 'CANCELED', 'REJECTED', 'SUCCEEDED', ], ], 'EffectiveDeploymentStatusDetails' => [ 'type' => 'structure', 'members' => [ 'errorStack' => [ 'shape' => 'EffectiveDeploymentErrorStack', ], 'errorTypes' => [ 'shape' => 'EffectiveDeploymentErrorTypeList', ], ], ], 'EffectiveDeploymentsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EffectiveDeployment', ], ], 'FileSystemPath' => [ 'type' => 'string', ], 'GGCVersion' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'GenericV2ARN' => [ 'type' => 'string', 'pattern' => 'arn:[^:]*:greengrass:[^:]*:(aws|[0-9]+):(components|deployments|coreDevices):.*', ], 'GetComponentRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'recipeOutputFormat' => [ 'shape' => 'RecipeOutputFormat', 'location' => 'querystring', 'locationName' => 'recipeOutputFormat', ], 'arn' => [ 'shape' => 'ComponentVersionARN', 'location' => 'uri', 'locationName' => 'arn', ], ], ], 'GetComponentResponse' => [ 'type' => 'structure', 'required' => [ 'recipeOutputFormat', 'recipe', ], 'members' => [ 'recipeOutputFormat' => [ 'shape' => 'RecipeOutputFormat', ], 'recipe' => [ 'shape' => 'RecipeBlob', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'GetComponentVersionArtifactRequest' => [ 'type' => 'structure', 'required' => [ 'arn', 'artifactName', ], 'members' => [ 'arn' => [ 'shape' => 'ComponentVersionARN', 'location' => 'uri', 'locationName' => 'arn', ], 'artifactName' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'artifactName', ], 's3EndpointType' => [ 'shape' => 'S3EndpointType', 'location' => 'querystring', 'locationName' => 's3EndpointType', ], 'iotEndpointType' => [ 'shape' => 'IotEndpointType', 'location' => 'header', 'locationName' => 'x-amz-iot-endpoint-type', ], ], ], 'GetComponentVersionArtifactResponse' => [ 'type' => 'structure', 'required' => [ 'preSignedUrl', ], 'members' => [ 'preSignedUrl' => [ 'shape' => 'NonEmptyString', ], ], ], 'GetConnectivityInfoRequest' => [ 'type' => 'structure', 'required' => [ 'thingName', ], 'members' => [ 'thingName' => [ 'shape' => 'CoreDeviceThingName', 'location' => 'uri', 'locationName' => 'thingName', ], ], ], 'GetConnectivityInfoResponse' => [ 'type' => 'structure', 'members' => [ 'connectivityInfo' => [ 'shape' => 'connectivityInfoList', 'locationName' => 'ConnectivityInfo', ], 'message' => [ 'shape' => 'String', 'locationName' => 'Message', ], ], ], 'GetCoreDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'coreDeviceThingName', ], 'members' => [ 'coreDeviceThingName' => [ 'shape' => 'CoreDeviceThingName', 'location' => 'uri', 'locationName' => 'coreDeviceThingName', ], ], ], 'GetCoreDeviceResponse' => [ 'type' => 'structure', 'members' => [ 'coreDeviceThingName' => [ 'shape' => 'CoreDeviceThingName', ], 'coreVersion' => [ 'shape' => 'GGCVersion', ], 'platform' => [ 'shape' => 'CoreDevicePlatformString', ], 'architecture' => [ 'shape' => 'CoreDeviceArchitectureString', ], 'runtime' => [ 'shape' => 'CoreDeviceRuntimeString', ], 'status' => [ 'shape' => 'CoreDeviceStatus', ], 'lastStatusUpdateTimestamp' => [ 'shape' => 'Timestamp', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'GetDeploymentRequest' => [ 'type' => 'structure', 'required' => [ 'deploymentId', ], 'members' => [ 'deploymentId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'deploymentId', ], ], ], 'GetDeploymentResponse' => [ 'type' => 'structure', 'members' => [ 'targetArn' => [ 'shape' => 'TargetARN', ], 'revisionId' => [ 'shape' => 'NonEmptyString', ], 'deploymentId' => [ 'shape' => 'NonEmptyString', ], 'deploymentName' => [ 'shape' => 'NullableString', ], 'deploymentStatus' => [ 'shape' => 'DeploymentStatus', ], 'iotJobId' => [ 'shape' => 'NullableString', ], 'iotJobArn' => [ 'shape' => 'IoTJobARN', ], 'components' => [ 'shape' => 'ComponentDeploymentSpecifications', ], 'deploymentPolicies' => [ 'shape' => 'DeploymentPolicies', ], 'iotJobConfiguration' => [ 'shape' => 'DeploymentIoTJobConfiguration', ], 'creationTimestamp' => [ 'shape' => 'Timestamp', ], 'isLatestForTarget' => [ 'shape' => 'IsLatestForTarget', ], 'parentTargetArn' => [ 'shape' => 'ThingGroupARN', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'GetServiceRoleForAccountRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetServiceRoleForAccountResponse' => [ 'type' => 'structure', 'members' => [ 'associatedAt' => [ 'shape' => 'String', 'locationName' => 'AssociatedAt', ], 'roleArn' => [ 'shape' => 'String', 'locationName' => 'RoleArn', ], ], ], 'InstalledComponent' => [ 'type' => 'structure', 'members' => [ 'componentName' => [ 'shape' => 'ComponentNameString', ], 'componentVersion' => [ 'shape' => 'ComponentVersionString', ], 'lifecycleState' => [ 'shape' => 'InstalledComponentLifecycleState', ], 'lifecycleStateDetails' => [ 'shape' => 'LifecycleStateDetails', ], 'isRoot' => [ 'shape' => 'IsRoot', ], 'lastStatusChangeTimestamp' => [ 'shape' => 'Timestamp', ], 'lastReportedTimestamp' => [ 'shape' => 'Timestamp', ], 'lastInstallationSource' => [ 'shape' => 'NonEmptyString', ], 'lifecycleStatusCodes' => [ 'shape' => 'InstalledComponentLifecycleStatusCodeList', ], ], ], 'InstalledComponentLifecycleState' => [ 'type' => 'string', 'enum' => [ 'NEW', 'INSTALLED', 'STARTING', 'RUNNING', 'STOPPING', 'ERRORED', 'BROKEN', 'FINISHED', ], ], 'InstalledComponentLifecycleStatusCode' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'InstalledComponentLifecycleStatusCodeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstalledComponentLifecycleStatusCode', ], ], 'InstalledComponentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstalledComponent', ], ], 'InstalledComponentTopologyFilter' => [ 'type' => 'string', 'enum' => [ 'ALL', 'ROOT', ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'RetryAfterSeconds', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'IoTJobARN' => [ 'type' => 'string', 'pattern' => 'arn:[^:]*:iot:[^:]+:[0-9]+:job/.+', ], 'IoTJobAbortAction' => [ 'type' => 'string', 'enum' => [ 'CANCEL', ], ], 'IoTJobAbortConfig' => [ 'type' => 'structure', 'required' => [ 'criteriaList', ], 'members' => [ 'criteriaList' => [ 'shape' => 'IoTJobAbortCriteriaList', ], ], ], 'IoTJobAbortCriteria' => [ 'type' => 'structure', 'required' => [ 'failureType', 'action', 'thresholdPercentage', 'minNumberOfExecutedThings', ], 'members' => [ 'failureType' => [ 'shape' => 'IoTJobExecutionFailureType', ], 'action' => [ 'shape' => 'IoTJobAbortAction', ], 'thresholdPercentage' => [ 'shape' => 'IoTJobAbortThresholdPercentage', ], 'minNumberOfExecutedThings' => [ 'shape' => 'IoTJobMinimumNumberOfExecutedThings', ], ], ], 'IoTJobAbortCriteriaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IoTJobAbortCriteria', ], 'min' => 1, ], 'IoTJobAbortThresholdPercentage' => [ 'type' => 'double', 'max' => 100, ], 'IoTJobExecutionFailureType' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'REJECTED', 'TIMED_OUT', 'ALL', ], ], 'IoTJobExecutionsRolloutConfig' => [ 'type' => 'structure', 'members' => [ 'exponentialRate' => [ 'shape' => 'IoTJobExponentialRolloutRate', ], 'maximumPerMinute' => [ 'shape' => 'IoTJobMaxExecutionsPerMin', 'box' => true, ], ], ], 'IoTJobExponentialRolloutRate' => [ 'type' => 'structure', 'required' => [ 'baseRatePerMinute', 'incrementFactor', 'rateIncreaseCriteria', ], 'members' => [ 'baseRatePerMinute' => [ 'shape' => 'IoTJobRolloutBaseRatePerMinute', ], 'incrementFactor' => [ 'shape' => 'IoTJobRolloutIncrementFactor', ], 'rateIncreaseCriteria' => [ 'shape' => 'IoTJobRateIncreaseCriteria', ], ], ], 'IoTJobId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'IoTJobInProgressTimeoutInMinutes' => [ 'type' => 'long', ], 'IoTJobMaxExecutionsPerMin' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'IoTJobMinimumNumberOfExecutedThings' => [ 'type' => 'integer', 'min' => 1, ], 'IoTJobNumberOfThings' => [ 'type' => 'integer', 'min' => 1, ], 'IoTJobRateIncreaseCriteria' => [ 'type' => 'structure', 'members' => [ 'numberOfNotifiedThings' => [ 'shape' => 'IoTJobNumberOfThings', 'box' => true, ], 'numberOfSucceededThings' => [ 'shape' => 'IoTJobNumberOfThings', 'box' => true, ], ], ], 'IoTJobRolloutBaseRatePerMinute' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'IoTJobRolloutIncrementFactor' => [ 'type' => 'double', 'max' => 5, 'min' => 1, ], 'IoTJobTimeoutConfig' => [ 'type' => 'structure', 'members' => [ 'inProgressTimeoutInMinutes' => [ 'shape' => 'IoTJobInProgressTimeoutInMinutes', 'box' => true, ], ], ], 'IoTThingName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'IotEndpointType' => [ 'type' => 'string', 'enum' => [ 'fips', 'standard', ], ], 'IsLatestForTarget' => [ 'type' => 'boolean', ], 'IsRoot' => [ 'type' => 'boolean', ], 'LambdaContainerParams' => [ 'type' => 'structure', 'members' => [ 'memorySizeInKB' => [ 'shape' => 'OptionalInteger', 'box' => true, ], 'mountROSysfs' => [ 'shape' => 'OptionalBoolean', 'box' => true, ], 'volumes' => [ 'shape' => 'LambdaVolumeList', ], 'devices' => [ 'shape' => 'LambdaDeviceList', ], ], ], 'LambdaDeviceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LambdaDeviceMount', ], ], 'LambdaDeviceMount' => [ 'type' => 'structure', 'required' => [ 'path', ], 'members' => [ 'path' => [ 'shape' => 'FileSystemPath', ], 'permission' => [ 'shape' => 'LambdaFilesystemPermission', ], 'addGroupOwner' => [ 'shape' => 'OptionalBoolean', 'box' => true, ], ], ], 'LambdaEnvironmentVariables' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'String', ], ], 'LambdaEventSource' => [ 'type' => 'structure', 'required' => [ 'topic', 'type', ], 'members' => [ 'topic' => [ 'shape' => 'TopicString', ], 'type' => [ 'shape' => 'LambdaEventSourceType', ], ], ], 'LambdaEventSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LambdaEventSource', ], ], 'LambdaEventSourceType' => [ 'type' => 'string', 'enum' => [ 'PUB_SUB', 'IOT_CORE', ], ], 'LambdaExecArg' => [ 'type' => 'string', ], 'LambdaExecArgsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LambdaExecArg', ], ], 'LambdaExecutionParameters' => [ 'type' => 'structure', 'members' => [ 'eventSources' => [ 'shape' => 'LambdaEventSourceList', ], 'maxQueueSize' => [ 'shape' => 'OptionalInteger', 'box' => true, ], 'maxInstancesCount' => [ 'shape' => 'OptionalInteger', 'box' => true, ], 'maxIdleTimeInSeconds' => [ 'shape' => 'OptionalInteger', 'box' => true, ], 'timeoutInSeconds' => [ 'shape' => 'OptionalInteger', 'box' => true, ], 'statusTimeoutInSeconds' => [ 'shape' => 'OptionalInteger', 'box' => true, ], 'pinned' => [ 'shape' => 'OptionalBoolean', 'box' => true, ], 'inputPayloadEncodingType' => [ 'shape' => 'LambdaInputPayloadEncodingType', ], 'execArgs' => [ 'shape' => 'LambdaExecArgsList', ], 'environmentVariables' => [ 'shape' => 'LambdaEnvironmentVariables', ], 'linuxProcessParams' => [ 'shape' => 'LambdaLinuxProcessParams', ], ], ], 'LambdaFilesystemPermission' => [ 'type' => 'string', 'enum' => [ 'ro', 'rw', ], ], 'LambdaFunctionRecipeSource' => [ 'type' => 'structure', 'required' => [ 'lambdaArn', ], 'members' => [ 'lambdaArn' => [ 'shape' => 'NonEmptyString', ], 'componentName' => [ 'shape' => 'ComponentNameString', ], 'componentVersion' => [ 'shape' => 'ComponentVersionString', ], 'componentPlatforms' => [ 'shape' => 'ComponentPlatformList', ], 'componentDependencies' => [ 'shape' => 'ComponentDependencyMap', ], 'componentLambdaParameters' => [ 'shape' => 'LambdaExecutionParameters', ], ], ], 'LambdaInputPayloadEncodingType' => [ 'type' => 'string', 'enum' => [ 'json', 'binary', ], ], 'LambdaIsolationMode' => [ 'type' => 'string', 'enum' => [ 'GreengrassContainer', 'NoContainer', ], ], 'LambdaLinuxProcessParams' => [ 'type' => 'structure', 'members' => [ 'isolationMode' => [ 'shape' => 'LambdaIsolationMode', ], 'containerParams' => [ 'shape' => 'LambdaContainerParams', ], ], ], 'LambdaVolumeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LambdaVolumeMount', ], ], 'LambdaVolumeMount' => [ 'type' => 'structure', 'required' => [ 'sourcePath', 'destinationPath', ], 'members' => [ 'sourcePath' => [ 'shape' => 'FileSystemPath', ], 'destinationPath' => [ 'shape' => 'FileSystemPath', ], 'permission' => [ 'shape' => 'LambdaFilesystemPermission', ], 'addGroupOwner' => [ 'shape' => 'OptionalBoolean', 'box' => true, ], ], ], 'LifecycleStateDetails' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'ListClientDevicesAssociatedWithCoreDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'coreDeviceThingName', ], 'members' => [ 'coreDeviceThingName' => [ 'shape' => 'IoTThingName', 'location' => 'uri', 'locationName' => 'coreDeviceThingName', ], 'maxResults' => [ 'shape' => 'DefaultMaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextTokenString', 'box' => true, 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListClientDevicesAssociatedWithCoreDeviceResponse' => [ 'type' => 'structure', 'members' => [ 'associatedClientDevices' => [ 'shape' => 'AssociatedClientDeviceList', ], 'nextToken' => [ 'shape' => 'NextTokenString', 'box' => true, ], ], ], 'ListComponentVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'ComponentARN', 'location' => 'uri', 'locationName' => 'arn', ], 'maxResults' => [ 'shape' => 'DefaultMaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextTokenString', 'box' => true, 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListComponentVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'componentVersions' => [ 'shape' => 'ComponentVersionList', ], 'nextToken' => [ 'shape' => 'NextTokenString', ], ], ], 'ListComponentsRequest' => [ 'type' => 'structure', 'members' => [ 'scope' => [ 'shape' => 'ComponentVisibilityScope', 'location' => 'querystring', 'locationName' => 'scope', ], 'maxResults' => [ 'shape' => 'DefaultMaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextTokenString', 'box' => true, 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListComponentsResponse' => [ 'type' => 'structure', 'members' => [ 'components' => [ 'shape' => 'ComponentList', ], 'nextToken' => [ 'shape' => 'NextTokenString', ], ], ], 'ListCoreDevicesRequest' => [ 'type' => 'structure', 'members' => [ 'thingGroupArn' => [ 'shape' => 'ThingGroupARN', 'location' => 'querystring', 'locationName' => 'thingGroupArn', ], 'status' => [ 'shape' => 'CoreDeviceStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'maxResults' => [ 'shape' => 'DefaultMaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextTokenString', 'box' => true, 'location' => 'querystring', 'locationName' => 'nextToken', ], 'runtime' => [ 'shape' => 'CoreDeviceRuntimeString', 'location' => 'querystring', 'locationName' => 'runtime', ], ], ], 'ListCoreDevicesResponse' => [ 'type' => 'structure', 'members' => [ 'coreDevices' => [ 'shape' => 'CoreDevicesList', ], 'nextToken' => [ 'shape' => 'NextTokenString', ], ], ], 'ListDeploymentsRequest' => [ 'type' => 'structure', 'members' => [ 'targetArn' => [ 'shape' => 'TargetARN', 'location' => 'querystring', 'locationName' => 'targetArn', ], 'historyFilter' => [ 'shape' => 'DeploymentHistoryFilter', 'location' => 'querystring', 'locationName' => 'historyFilter', ], 'parentTargetArn' => [ 'shape' => 'ThingGroupARN', 'location' => 'querystring', 'locationName' => 'parentTargetArn', ], 'maxResults' => [ 'shape' => 'DefaultMaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextTokenString', 'box' => true, 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListDeploymentsResponse' => [ 'type' => 'structure', 'members' => [ 'deployments' => [ 'shape' => 'DeploymentList', ], 'nextToken' => [ 'shape' => 'NextTokenString', ], ], ], 'ListEffectiveDeploymentsRequest' => [ 'type' => 'structure', 'required' => [ 'coreDeviceThingName', ], 'members' => [ 'coreDeviceThingName' => [ 'shape' => 'CoreDeviceThingName', 'location' => 'uri', 'locationName' => 'coreDeviceThingName', ], 'maxResults' => [ 'shape' => 'DefaultMaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextTokenString', 'box' => true, 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListEffectiveDeploymentsResponse' => [ 'type' => 'structure', 'members' => [ 'effectiveDeployments' => [ 'shape' => 'EffectiveDeploymentsList', ], 'nextToken' => [ 'shape' => 'NextTokenString', ], ], ], 'ListInstalledComponentsRequest' => [ 'type' => 'structure', 'required' => [ 'coreDeviceThingName', ], 'members' => [ 'coreDeviceThingName' => [ 'shape' => 'CoreDeviceThingName', 'location' => 'uri', 'locationName' => 'coreDeviceThingName', ], 'maxResults' => [ 'shape' => 'DefaultMaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextTokenString', 'box' => true, 'location' => 'querystring', 'locationName' => 'nextToken', ], 'topologyFilter' => [ 'shape' => 'InstalledComponentTopologyFilter', 'box' => true, 'location' => 'querystring', 'locationName' => 'topologyFilter', ], ], ], 'ListInstalledComponentsResponse' => [ 'type' => 'structure', 'members' => [ 'installedComponents' => [ 'shape' => 'InstalledComponentList', ], 'nextToken' => [ 'shape' => 'NextTokenString', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'GenericV2ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'Memory' => [ 'type' => 'long', 'max' => 9223372036854771712, 'min' => 0, ], 'NextTokenString' => [ 'type' => 'string', ], 'NonEmptyString' => [ 'type' => 'string', 'min' => 1, ], 'NullableString' => [ 'type' => 'string', ], 'OptionalBoolean' => [ 'type' => 'boolean', ], 'OptionalInteger' => [ 'type' => 'integer', ], 'PlatformAttributesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'NonEmptyString', ], ], 'PortNumberInt' => [ 'type' => 'integer', 'max' => 65535, 'min' => 0, ], 'PublisherString' => [ 'type' => 'string', ], 'Reason' => [ 'type' => 'string', ], 'RecipeBlob' => [ 'type' => 'blob', ], 'RecipeOutputFormat' => [ 'type' => 'string', 'enum' => [ 'JSON', 'YAML', ], ], 'RequestAlreadyInProgressException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ResolveComponentCandidatesRequest' => [ 'type' => 'structure', 'members' => [ 'platform' => [ 'shape' => 'ComponentPlatform', ], 'componentCandidates' => [ 'shape' => 'ComponentCandidateList', ], ], ], 'ResolveComponentCandidatesResponse' => [ 'type' => 'structure', 'members' => [ 'resolvedComponentVersions' => [ 'shape' => 'ResolvedComponentVersionsList', ], ], ], 'ResolvedComponentVersion' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ComponentVersionARN', ], 'componentName' => [ 'shape' => 'ComponentNameString', ], 'componentVersion' => [ 'shape' => 'ComponentVersionString', ], 'recipe' => [ 'shape' => 'RecipeBlob', ], 'vendorGuidance' => [ 'shape' => 'VendorGuidance', ], 'message' => [ 'shape' => 'NonEmptyString', ], ], ], 'ResolvedComponentVersionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResolvedComponentVersion', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'RetryAfterSeconds' => [ 'type' => 'integer', ], 'S3EndpointType' => [ 'type' => 'string', 'enum' => [ 'REGIONAL', 'GLOBAL', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'quotaCode', 'serviceCode', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, ], 'exception' => true, ], 'String' => [ 'type' => 'string', ], 'StringMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'NonEmptyString', ], ], 'SystemResourceLimits' => [ 'type' => 'structure', 'members' => [ 'memory' => [ 'shape' => 'Memory', ], 'cpus' => [ 'shape' => 'CPU', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'GenericV2ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, ], 'TargetARN' => [ 'type' => 'string', 'pattern' => 'arn:[^:]*:iot:[^:]*:[0-9]+:(thing|thinggroup)/.+', ], 'ThingGroupARN' => [ 'type' => 'string', 'pattern' => 'arn:[^:]*:iot:[^:]*:[0-9]+:thinggroup/.+', ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'RetryAfterSeconds', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TopicString' => [ 'type' => 'string', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'GenericV2ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateConnectivityInfoRequest' => [ 'type' => 'structure', 'required' => [ 'thingName', 'connectivityInfo', ], 'members' => [ 'thingName' => [ 'shape' => 'CoreDeviceThingName', 'location' => 'uri', 'locationName' => 'thingName', ], 'connectivityInfo' => [ 'shape' => 'connectivityInfoList', 'locationName' => 'ConnectivityInfo', ], ], ], 'UpdateConnectivityInfoResponse' => [ 'type' => 'structure', 'members' => [ 'version' => [ 'shape' => 'String', 'locationName' => 'Version', ], 'message' => [ 'shape' => 'String', 'locationName' => 'Message', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], 'fields' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'name', 'message', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'UNKNOWN_OPERATION', 'CANNOT_PARSE', 'FIELD_VALIDATION_FAILED', 'OTHER', ], ], 'VendorGuidance' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'DISCONTINUED', 'DELETED', ], ], 'connectivityInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectivityInfo', ], ], ],];
