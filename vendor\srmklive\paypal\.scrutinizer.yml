filter:
    excluded_paths: [tests/*]

checks:
    php:
        remove_extra_empty_lines: true
        remove_php_closing_tag: true
        remove_trailing_whitespace: true
        fix_use_statements:
            remove_unused: true
            preserve_multiple: false
            preserve_blanklines: true
            order_alphabetically: true
        fix_php_opening_tag: true
        fix_linefeed: true
        fix_line_ending: true
        fix_identation_4spaces: true
        fix_doc_comments: true

build:
  nodes:
    analysis:
      tests:
        override:
        - php-scrutinizer-run
  environment:
      php:
          version: 8.1.13 
  tests:
      override:
      -
          command: 'vendor/bin/phpunit --coverage-clover=build/logs/clover.xml'
          coverage:
              file: 'build/logs/clover.xml'
              format: 'clover'
