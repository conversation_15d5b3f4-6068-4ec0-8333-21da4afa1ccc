[![Latest Stable Version](https://poser.pugx.org/sebastian/environment/v)](https://packagist.org/packages/sebastian/environment)
[![CI Status](https://github.com/sebastianbergmann/environment/workflows/CI/badge.svg)](https://github.com/sebastianbergmann/environment/actions)
[![codecov](https://codecov.io/gh/sebastianbergmann/environment/branch/main/graph/badge.svg)](https://codecov.io/gh/sebastianbergmann/environment)

# sebastian/environment

This component provides functionality that helps writing PHP code that has runtime-specific (PHP / HHVM) execution paths.

## Installation

You can add this library as a local, per-project dependency to your project using [Composer](https://getcomposer.org/):

```
composer require sebastian/environment
```

If you only need this library during development, for instance to run your project's test suite, then you should add it as a development-time dependency:

```
composer require --dev sebastian/environment
```
