<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Organization Public API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\PreviewIam\Versionless\Organization;

use Twilio\Values;
abstract class RoleAssignmentModels
{
    /**
     * @property string $roleSid Twilio Role Sid representing assigned role
     * @property string $scope Twilio Sid representing scope of this assignment
     * @property string $identity Twilio <PERSON> representing identity of this assignment
    */
    public static function createPublicApiCreateRoleAssignmentRequest(array $payload = []): PublicApiCreateRoleAssignmentRequest
    {
        return new PublicApiCreateRoleAssignmentRequest($payload);
    }

}

class PublicApiCreateRoleAssignmentRequest implements \JsonSerializable
{
    /**
     * @property string $roleSid Twilio Role Sid representing assigned role
     * @property string $scope Twilio Sid representing scope of this assignment
     * @property string $identity Twilio Sid representing identity of this assignment
    */
        protected $roleSid;
        protected $scope;
        protected $identity;
    public function __construct(array $payload = []) {
        $this->roleSid = Values::array_get($payload, 'role_sid');
        $this->scope = Values::array_get($payload, 'scope');
        $this->identity = Values::array_get($payload, 'identity');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'role_sid' => $this->roleSid,
            'scope' => $this->scope,
            'identity' => $this->identity
        ];
    }
}

