<?php

namespace App\Traits\ProjectManagement;

use Illuminate\Support\Carbon;

/**
 * Trait للتعامل مع الطوابع الزمنية المتقدمة
 * 
 * يوفر طرق مساعدة للتعامل مع التواريخ والأوقات
 * في نظام إدارة المشاريع
 * 
 * @package App\Traits\ProjectManagement
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
trait HasTimestamps
{
    /**
     * الحصول على تاريخ الإنشاء منسق
     * 
     * @param string $format تنسيق التاريخ
     * @return string
     */
    public function getFormattedCreatedAt(string $format = 'Y-m-d H:i:s'): string
    {
        return $this->created_at ? $this->created_at->format($format) : '';
    }

    /**
     * الحصول على تاريخ التحديث منسق
     * 
     * @param string $format تنسيق التاريخ
     * @return string
     */
    public function getFormattedUpdatedAt(string $format = 'Y-m-d H:i:s'): string
    {
        return $this->updated_at ? $this->updated_at->format($format) : '';
    }

    /**
     * الحصول على الوقت المنقضي منذ الإنشاء
     * 
     * @return string
     */
    public function getCreatedAtDiffForHumans(): string
    {
        return $this->created_at ? $this->created_at->diffForHumans() : '';
    }

    /**
     * الحصول على الوقت المنقضي منذ التحديث
     * 
     * @return string
     */
    public function getUpdatedAtDiffForHumans(): string
    {
        return $this->updated_at ? $this->updated_at->diffForHumans() : '';
    }

    /**
     * التحقق من أن السجل تم إنشاؤه اليوم
     * 
     * @return bool
     */
    public function isCreatedToday(): bool
    {
        return $this->created_at ? $this->created_at->isToday() : false;
    }

    /**
     * التحقق من أن السجل تم تحديثه اليوم
     * 
     * @return bool
     */
    public function isUpdatedToday(): bool
    {
        return $this->updated_at ? $this->updated_at->isToday() : false;
    }

    /**
     * الحصول على عدد الأيام منذ الإنشاء
     * 
     * @return int
     */
    public function getDaysOld(): int
    {
        return $this->created_at ? $this->created_at->diffInDays(now()) : 0;
    }

    /**
     * الحصول على عدد الساعات منذ آخر تحديث
     * 
     * @return int
     */
    public function getHoursSinceUpdate(): int
    {
        return $this->updated_at ? $this->updated_at->diffInHours(now()) : 0;
    }

    /**
     * تحديد ما إذا كان السجل قديماً (أكثر من عدد معين من الأيام)
     * 
     * @param int $days عدد الأيام
     * @return bool
     */
    public function isOlderThan(int $days): bool
    {
        return $this->created_at ? $this->created_at->lt(now()->subDays($days)) : false;
    }

    /**
     * تحديد ما إذا كان السجل حديثاً (أقل من عدد معين من الأيام)
     * 
     * @param int $days عدد الأيام
     * @return bool
     */
    public function isNewerThan(int $days): bool
    {
        return $this->created_at ? $this->created_at->gt(now()->subDays($days)) : false;
    }

    /**
     * الحصول على تاريخ الإنشاء بالتوقيت المحلي
     * 
     * @param string $timezone المنطقة الزمنية
     * @return Carbon|null
     */
    public function getCreatedAtInTimezone(string $timezone = 'Asia/Riyadh'): ?Carbon
    {
        return $this->created_at ? $this->created_at->setTimezone($timezone) : null;
    }

    /**
     * الحصول على تاريخ التحديث بالتوقيت المحلي
     * 
     * @param string $timezone المنطقة الزمنية
     * @return Carbon|null
     */
    public function getUpdatedAtInTimezone(string $timezone = 'Asia/Riyadh'): ?Carbon
    {
        return $this->updated_at ? $this->updated_at->setTimezone($timezone) : null;
    }

    /**
     * scope للسجلات المنشأة في فترة معينة
     * 
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param Carbon $from
     * @param Carbon $to
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCreatedBetween($query, Carbon $from, Carbon $to)
    {
        return $query->whereBetween('created_at', [$from, $to]);
    }

    /**
     * scope للسجلات المحدثة في فترة معينة
     * 
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param Carbon $from
     * @param Carbon $to
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUpdatedBetween($query, Carbon $from, Carbon $to)
    {
        return $query->whereBetween('updated_at', [$from, $to]);
    }

    /**
     * scope للسجلات المنشأة اليوم
     * 
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCreatedToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    /**
     * scope للسجلات المحدثة اليوم
     * 
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUpdatedToday($query)
    {
        return $query->whereDate('updated_at', today());
    }

    /**
     * scope للسجلات القديمة
     * 
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $days عدد الأيام
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOlderThan($query, int $days)
    {
        return $query->where('created_at', '<', now()->subDays($days));
    }

    /**
     * scope للسجلات الحديثة
     * 
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $days عدد الأيام
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeNewerThan($query, int $days)
    {
        return $query->where('created_at', '>', now()->subDays($days));
    }
}
