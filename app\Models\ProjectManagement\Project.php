<?php

namespace App\Models\ProjectManagement;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use App\Models\User;
use App\Traits\ProjectManagement\HasTimestamps;
use App\Traits\ProjectManagement\HasStatus;
use App\Traits\ProjectManagement\HasPriority;
use App\Traits\ProjectManagement\Searchable;

/**
 * نموذج المشروع المتقدم
 *
 * هذا النموذج يمثل المشاريع في النظام ويحتوي على جميع الخصائص
 * والعلاقات والطرق المطلوبة لإدارة المشاريع بشكل احترافي
 *
 * @package App\Models\ProjectManagement
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 *
 * @property int $id معرف المشروع الفريد
 * @property string $name اسم المشروع
 * @property string $slug الرابط الودود للمشروع
 * @property string|null $description وصف المشروع
 * @property string $type نوع المشروع
 * @property string $status حالة المشروع
 * @property string $priority أولوية المشروع
 * @property decimal $budget الميزانية المخصصة
 * @property decimal $spent_budget الميزانية المستخدمة
 * @property Carbon $start_date تاريخ بداية المشروع
 * @property Carbon|null $end_date تاريخ انتهاء المشروع
 * @property Carbon|null $actual_end_date التاريخ الفعلي للانتهاء
 * @property int $progress_percentage نسبة الإنجاز
 * @property int $client_id معرف العميل
 * @property int $manager_id معرف مدير المشروع
 * @property int $created_by معرف منشئ المشروع
 * @property array|null $settings إعدادات إضافية للمشروع
 * @property Carbon $created_at تاريخ الإنشاء
 * @property Carbon $updated_at تاريخ آخر تحديث
 * @property Carbon|null $deleted_at تاريخ الحذف الناعم
 */
class Project extends Model
{
    use HasFactory, SoftDeletes, HasTimestamps, HasStatus, HasPriority, Searchable;

    /**
     * اسم الجدول في قاعدة البيانات
     *
     * @var string
     */
    protected $table = 'projects';

    /**
     * الحقول القابلة للتعبئة الجماعية
     *
     * @var array<string>
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
        'type',
        'status',
        'priority',
        'budget',
        'spent_budget',
        'start_date',
        'end_date',
        'actual_end_date',
        'progress_percentage',
        'client_id',
        'manager_id',
        'created_by',
        'settings',
    ];

    /**
     * الحقول المخفية من التسلسل
     *
     * @var array<string>
     */
    protected $hidden = [
        'deleted_at',
    ];

    /**
     * تحويل أنواع البيانات
     *
     * @var array<string, string>
     */
    protected $casts = [
        'budget' => 'decimal:2',
        'spent_budget' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
        'actual_end_date' => 'date',
        'progress_percentage' => 'integer',
        'settings' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * الحقول القابلة للبحث
     *
     * @var array<string>
     */
    protected $searchable = [
        'name',
        'description',
        'type',
        'status',
    ];

    /**
     * القيم الافتراضية للحقول
     *
     * @var array<string, mixed>
     */
    protected $attributes = [
        'status' => 'planning',
        'priority' => 'normal',
        'progress_percentage' => 0,
        'spent_budget' => 0.00,
    ];

    /**
     * أحداث النموذج
     *
     * @return void
     */
    protected static function boot(): void
    {
        parent::boot();

        // إنشاء slug تلقائياً عند الإنشاء
        static::creating(function ($project) {
            if (empty($project->slug)) {
                $project->slug = Str::slug($project->name);
            }
        });

        // تحديث slug عند تغيير الاسم
        static::updating(function ($project) {
            if ($project->isDirty('name')) {
                $project->slug = Str::slug($project->name);
            }
        });

        // تحديث نسبة الإنجاز تلقائياً عند تحديث المهام
        static::updated(function ($project) {
            $project->updateProgressPercentage();
        });
    }

    /*
    |--------------------------------------------------------------------------
    | العلاقات (Relationships)
    |--------------------------------------------------------------------------
    */

    /**
     * العلاقة مع العميل
     *
     * @return BelongsTo
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(User::class, 'client_id');
    }

    /**
     * العلاقة مع مدير المشروع
     *
     * @return BelongsTo
     */
    public function manager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    /**
     * العلاقة مع منشئ المشروع
     *
     * @return BelongsTo
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * العلاقة مع المهام
     *
     * @return HasMany
     */
    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class);
    }

    /**
     * العلاقة مع المعالم
     *
     * @return HasMany
     */
    public function milestones(): HasMany
    {
        return $this->hasMany(Milestone::class);
    }

    /**
     * العلاقة مع أعضاء الفريق
     *
     * @return BelongsToMany
     */
    public function teamMembers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'project_team_members')
                    ->withPivot(['role', 'joined_at', 'hourly_rate'])
                    ->withTimestamps();
    }

    /**
     * العلاقة مع الملفات
     *
     * @return MorphMany
     */
    public function files(): MorphMany
    {
        return $this->morphMany(ProjectFile::class, 'fileable');
    }

    /**
     * العلاقة مع التعليقات
     *
     * @return MorphMany
     */
    public function comments(): MorphMany
    {
        return $this->morphMany(Comment::class, 'commentable');
    }

    /**
     * العلاقة مع سجلات تتبع الوقت
     *
     * @return HasMany
     */
    public function timeEntries(): HasMany
    {
        return $this->hasMany(TimeEntry::class);
    }

    /*
    |--------------------------------------------------------------------------
    | النطاقات (Scopes)
    |--------------------------------------------------------------------------
    */

    /**
     * نطاق المشاريع النشطة
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->whereNotIn('status', ['completed', 'cancelled']);
    }

    /**
     * نطاق المشاريع المكتملة
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeCompleted(Builder $query): Builder
    {
        return $query->where('status', 'completed');
    }

    /**
     * نطاق المشاريع المتأخرة
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeOverdue(Builder $query): Builder
    {
        return $query->where('end_date', '<', now())
                    ->whereNotIn('status', ['completed', 'cancelled']);
    }

    /**
     * نطاق المشاريع حسب الأولوية
     *
     * @param Builder $query
     * @param string $priority
     * @return Builder
     */
    public function scopeByPriority(Builder $query, string $priority): Builder
    {
        return $query->where('priority', $priority);
    }

    /**
     * نطاق المشاريع حسب المدير
     *
     * @param Builder $query
     * @param int $managerId
     * @return Builder
     */
    public function scopeByManager(Builder $query, int $managerId): Builder
    {
        return $query->where('manager_id', $managerId);
    }

    /*
    |--------------------------------------------------------------------------
    | الطرق المساعدة (Helper Methods)
    |--------------------------------------------------------------------------
    */

    /**
     * تحديث نسبة الإنجاز بناءً على المهام المكتملة
     *
     * @return void
     */
    public function updateProgressPercentage(): void
    {
        $totalTasks = $this->tasks()->count();

        if ($totalTasks === 0) {
            $this->update(['progress_percentage' => 0]);
            return;
        }

        $completedTasks = $this->tasks()->where('status', 'completed')->count();
        $progressPercentage = round(($completedTasks / $totalTasks) * 100);

        $this->update(['progress_percentage' => $progressPercentage]);
    }

    /**
     * التحقق من تأخر المشروع
     *
     * @return bool
     */
    public function isOverdue(): bool
    {
        return $this->end_date &&
               $this->end_date->isPast() &&
               !in_array($this->status, ['completed', 'cancelled']);
    }

    /**
     * التحقق من اكتمال المشروع
     *
     * @return bool
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * حساب المدة المتبقية للمشروع
     *
     * @return int|null عدد الأيام المتبقية
     */
    public function daysRemaining(): ?int
    {
        if (!$this->end_date || $this->isCompleted()) {
            return null;
        }

        return now()->diffInDays($this->end_date, false);
    }

    /**
     * حساب إجمالي الساعات المسجلة
     *
     * @return float
     */
    public function totalHoursLogged(): float
    {
        return $this->timeEntries()->sum('hours');
    }

    /**
     * حساب التكلفة الإجمالية بناءً على الساعات المسجلة
     *
     * @return float
     */
    public function totalCost(): float
    {
        return $this->timeEntries()
                    ->join('project_team_members', function ($join) {
                        $join->on('time_entries.user_id', '=', 'project_team_members.user_id')
                             ->where('project_team_members.project_id', $this->id);
                    })
                    ->sum(\DB::raw('time_entries.hours * project_team_members.hourly_rate'));
    }

    /**
     * الحصول على لون الحالة
     *
     * @return string
     */
    public function getStatusColorAttribute(): string
    {
        $statuses = config('project_management.projects.statuses');
        return $statuses[$this->status]['color'] ?? '#6c757d';
    }

    /**
     * الحصول على أيقونة الحالة
     *
     * @return string
     */
    public function getStatusIconAttribute(): string
    {
        $statuses = config('project_management.projects.statuses');
        return $statuses[$this->status]['icon'] ?? 'fas fa-question';
    }

    /**
     * الحصول على تسمية الحالة
     *
     * @return string
     */
    public function getStatusLabelAttribute(): string
    {
        $statuses = config('project_management.projects.statuses');
        return $statuses[$this->status]['label'] ?? $this->status;
    }

    /**
     * الحصول على نسبة الميزانية المستخدمة
     *
     * @return float
     */
    public function getBudgetUsagePercentageAttribute(): float
    {
        if ($this->budget <= 0) {
            return 0;
        }

        return round(($this->spent_budget / $this->budget) * 100, 2);
    }

    /**
     * التحقق من تجاوز الميزانية
     *
     * @return bool
     */
    public function isOverBudget(): bool
    {
        return $this->spent_budget > $this->budget;
    }

    /**
     * الحصول على الميزانية المتبقية
     *
     * @return float
     */
    public function getRemainingBudgetAttribute(): float
    {
        return max(0, $this->budget - $this->spent_budget);
    }
}
