<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Studio
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Studio\V1\Flow\Engagement;

use Twilio\ListResource;
use Twilio\Version;


class EngagementContextList extends ListResource
    {
    /**
     * Construct the EngagementContextList
     *
     * @param Version $version Version that contains the resource
     * @param string $flowSid The SID of the Flow.
     * @param string $engagementSid The SID of the Engagement.
     */
    public function __construct(
        Version $version,
        string $flowSid,
        string $engagementSid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'flowSid' =>
            $flowSid,
        
        'engagementSid' =>
            $engagementSid,
        
        ];
    }

    /**
     * Constructs a EngagementContextContext
     */
    public function getContext(
        
    ): EngagementContextContext
    {
        return new EngagementContextContext(
            $this->version,
            $this->solution['flowSid'],
            $this->solution['engagementSid']
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Studio.V1.EngagementContextList]';
    }
}
