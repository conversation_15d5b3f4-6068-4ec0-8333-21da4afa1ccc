net\authorize\api\contract\v1\ContactDetailType:
    properties:
        email:
            expose: true
            access_type: public_method
            serialized_name: email
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getEmail
                setter: setEmail
            type: string
        firstName:
            expose: true
            access_type: public_method
            serialized_name: firstName
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getFirstName
                setter: setFirstName
            type: string
        lastName:
            expose: true
            access_type: public_method
            serialized_name: lastName
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getLastName
                setter: setLastName
            type: string
