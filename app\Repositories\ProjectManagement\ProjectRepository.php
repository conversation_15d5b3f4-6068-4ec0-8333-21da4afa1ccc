<?php

namespace App\Repositories\ProjectManagement;

use App\Models\ProjectManagement\Project;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;

/**
 * مستودع المشاريع المتقدم
 *
 * هذا المستودع يحتوي على جميع العمليات المتعلقة بالمشاريع
 * مع طرق متقدمة للبحث والفلترة والإحصائيات
 *
 * @package App\Repositories\ProjectManagement
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
class ProjectRepository extends BaseRepository
{
    /**
     * منشئ مستودع المشاريع
     */
    public function __construct()
    {
        parent::__construct(new Project());
    }

    /**
     * الحصول على المشاريع النشطة
     *
     * @param array $relations العلاقات المطلوب تحميلها
     * @return Collection
     */
    public function getActiveProjects(array $relations = []): Collection
    {
        $query = $this->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->whereIn('status', ['planning', 'in_progress', 'testing'])->get();
    }

    /**
     * الحصول على المشاريع المكتملة
     *
     * @param array $relations العلاقات المطلوب تحميلها
     * @return Collection
     */
    public function getCompletedProjects(array $relations = []): Collection
    {
        $query = $this->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->where('status', 'completed')->get();
    }

    /**
     * الحصول على المشاريع المتأخرة
     *
     * @param array $relations العلاقات المطلوب تحميلها
     * @return Collection
     */
    public function getOverdueProjects(array $relations = []): Collection
    {
        $query = $this->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->where('end_date', '<', Carbon::now())
                    ->whereNotIn('status', ['completed', 'cancelled'])
                    ->get();
    }

    /**
     * الحصول على مشاريع مدير معين
     *
     * @param int $managerId معرف المدير
     * @param array $relations العلاقات المطلوب تحميلها
     * @return Collection
     */
    public function getProjectsByManager(int $managerId, array $relations = []): Collection
    {
        $query = $this->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->where('manager_id', $managerId)->get();
    }

    /**
     * الحصول على مشاريع عميل معين
     *
     * @param int $clientId معرف العميل
     * @param array $relations العلاقات المطلوب تحميلها
     * @return Collection
     */
    public function getProjectsByClient(int $clientId, array $relations = []): Collection
    {
        return $this->findAllBy(['client_id' => $clientId], ['*'], $relations);
    }

    /**
     * الحصول على مشاريع بأولوية معينة
     *
     * @param string $priority الأولوية
     * @param array $relations العلاقات المطلوب تحميلها
     * @return Collection
     */
    public function getProjectsByPriorityFilter(string $priority, array $relations = []): Collection
    {
        $query = $this->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->where('priority', $priority)->get();
    }

    /**
     * البحث المتقدم في المشاريع
     *
     * @param array $filters مرشحات البحث
     * @param int $perPage عدد النتائج في الصفحة
     * @return LengthAwarePaginator
     */
    public function searchProjects(array $filters, int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->newQuery()->with(['client', 'manager', 'creator']);

        // فلترة حسب الاسم أو الوصف
        if (!empty($filters['search'])) {
            $searchTerm = $filters['search'];
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('description', 'LIKE', "%{$searchTerm}%");
            });
        }

        // فلترة حسب الحالة
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // فلترة حسب النوع
        if (!empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        // فلترة حسب الأولوية
        if (!empty($filters['priority'])) {
            $query->where('priority', $filters['priority']);
        }

        // فلترة حسب المدير
        if (!empty($filters['manager_id'])) {
            $query->where('manager_id', $filters['manager_id']);
        }

        // فلترة حسب العميل
        if (!empty($filters['client_id'])) {
            $query->where('client_id', $filters['client_id']);
        }

        // فلترة حسب تاريخ البداية
        if (!empty($filters['start_date_from'])) {
            $query->where('start_date', '>=', $filters['start_date_from']);
        }

        if (!empty($filters['start_date_to'])) {
            $query->where('start_date', '<=', $filters['start_date_to']);
        }

        // فلترة حسب تاريخ النهاية
        if (!empty($filters['end_date_from'])) {
            $query->where('end_date', '>=', $filters['end_date_from']);
        }

        if (!empty($filters['end_date_to'])) {
            $query->where('end_date', '<=', $filters['end_date_to']);
        }

        // فلترة حسب نسبة الإنجاز
        if (!empty($filters['progress_min'])) {
            $query->where('progress_percentage', '>=', $filters['progress_min']);
        }

        if (!empty($filters['progress_max'])) {
            $query->where('progress_percentage', '<=', $filters['progress_max']);
        }

        // فلترة حسب الميزانية
        if (!empty($filters['budget_min'])) {
            $query->where('budget', '>=', $filters['budget_min']);
        }

        if (!empty($filters['budget_max'])) {
            $query->where('budget', '<=', $filters['budget_max']);
        }

        // ترتيب النتائج
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortBy, $sortDirection);

        return $query->paginate($perPage);
    }

    /**
     * الحصول على إحصائيات المشاريع
     *
     * @return array
     */
    public function getProjectStatistics(): array
    {
        $total = $this->count();
        $active = $this->count(['status' => ['planning', 'in_progress', 'testing']]);
        $completed = $this->count(['status' => 'completed']);
        $overdue = $this->newQuery()
            ->where('end_date', '<', Carbon::now())
            ->whereNotIn('status', ['completed', 'cancelled'])
            ->count();
        $onHold = $this->count(['status' => 'on_hold']);
        $cancelled = $this->count(['status' => 'cancelled']);

        return [
            'total' => $total,
            'active' => $active,
            'completed' => $completed,
            'overdue' => $overdue,
            'on_hold' => $onHold,
            'cancelled' => $cancelled,
            'completion_rate' => $total > 0 ? round(($completed / $total) * 100, 2) : 0,
        ];
    }

    /**
     * الحصول على إحصائيات المشاريع حسب الحالة
     *
     * @return array
     */
    public function getProjectsByStatus(): array
    {
        return $this->newQuery()
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();
    }

    /**
     * الحصول على إحصائيات المشاريع حسب النوع
     *
     * @return array
     */
    public function getProjectsByType(): array
    {
        return $this->newQuery()
            ->selectRaw('type, COUNT(*) as count')
            ->groupBy('type')
            ->pluck('count', 'type')
            ->toArray();
    }

    /**
     * الحصول على إحصائيات المشاريع حسب الأولوية
     *
     * @return array
     */
    public function getProjectsByPriority(): array
    {
        return $this->newQuery()
            ->selectRaw('priority, COUNT(*) as count')
            ->groupBy('priority')
            ->pluck('count', 'priority')
            ->toArray();
    }

    /**
     * الحصول على المشاريع القادمة للانتهاء
     *
     * @param int $days عدد الأيام
     * @return Collection
     */
    public function getProjectsDueSoon(int $days = 7): Collection
    {
        $endDate = Carbon::now()->addDays($days);

        return $this->newQuery()
            ->where('end_date', '<=', $endDate)
            ->where('end_date', '>=', Carbon::now())
            ->whereNotIn('status', ['completed', 'cancelled'])
            ->with(['manager', 'client'])
            ->orderBy('end_date')
            ->get();
    }

    /**
     * الحصول على أكثر المديرين نشاطاً
     *
     * @param int $limit عدد النتائج
     * @return array
     */
    public function getMostActiveManagers(int $limit = 10): array
    {
        return $this->newQuery()
            ->selectRaw('manager_id, COUNT(*) as projects_count')
            ->whereNotNull('manager_id')
            ->groupBy('manager_id')
            ->orderByDesc('projects_count')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    /**
     * الحصول على إجمالي الميزانيات
     *
     * @return array
     */
    public function getBudgetStatistics(): array
    {
        $stats = $this->newQuery()
            ->selectRaw('
                SUM(budget) as total_budget,
                SUM(spent_budget) as total_spent,
                AVG(budget) as average_budget,
                AVG(spent_budget) as average_spent
            ')
            ->first();

        return [
            'total_budget' => $stats->total_budget ?? 0,
            'total_spent' => $stats->total_spent ?? 0,
            'remaining_budget' => ($stats->total_budget ?? 0) - ($stats->total_spent ?? 0),
            'average_budget' => $stats->average_budget ?? 0,
            'average_spent' => $stats->average_spent ?? 0,
            'budget_utilization' => $stats->total_budget > 0
                ? round(($stats->total_spent / $stats->total_budget) * 100, 2)
                : 0,
        ];
    }

    /**
     * تحديث نسبة الإنجاز لمشروع معين
     *
     * @param int $projectId معرف المشروع
     * @return bool
     */
    public function updateProjectProgress(int $projectId): bool
    {
        $project = $this->findOrFail($projectId);
        $project->updateProgressPercentage();
        return true;
    }

    /**
     * إضافة عضو لفريق المشروع
     *
     * @param int $projectId معرف المشروع
     * @param int $userId معرف المستخدم
     * @param string $role الدور
     * @param float|null $hourlyRate السعر بالساعة
     * @return bool
     */
    public function addTeamMember(int $projectId, int $userId, string $role, ?float $hourlyRate = null): bool
    {
        $project = $this->findOrFail($projectId);

        $project->teamMembers()->syncWithoutDetaching([
            $userId => [
                'role' => $role,
                'hourly_rate' => $hourlyRate,
                'joined_at' => now(),
            ]
        ]);

        return true;
    }

    /**
     * إزالة عضو من فريق المشروع
     *
     * @param int $projectId معرف المشروع
     * @param int $userId معرف المستخدم
     * @return bool
     */
    public function removeTeamMember(int $projectId, int $userId): bool
    {
        $project = $this->findOrFail($projectId);
        $project->teamMembers()->detach($userId);
        return true;
    }
}
