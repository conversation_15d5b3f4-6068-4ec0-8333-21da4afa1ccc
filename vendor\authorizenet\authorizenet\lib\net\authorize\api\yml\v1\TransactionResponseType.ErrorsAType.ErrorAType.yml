net\authorize\api\contract\v1\TransactionResponseType\ErrorsAType\ErrorAType:
    properties:
        errorCode:
            expose: true
            access_type: public_method
            serialized_name: errorCode
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getErrorCode
                setter: setErrorCode
            type: string
        errorText:
            expose: true
            access_type: public_method
            serialized_name: errorText
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getErrorText
                setter: setErrorText
            type: string
