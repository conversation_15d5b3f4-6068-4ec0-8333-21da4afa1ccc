<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Preview
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Preview\HostedNumbers;

use Twilio\Options;
use Twilio\Values;

abstract class AuthorizationDocumentOptions
{
    /**
     * @param string[] $ccEmails Email recipients who will be informed when an Authorization Document has been sent and signed.
     * @return CreateAuthorizationDocumentOptions Options builder
     */
    public static function create(
        
        array $ccEmails = Values::ARRAY_NONE

    ): CreateAuthorizationDocumentOptions
    {
        return new CreateAuthorizationDocumentOptions(
            $ccEmails
        );
    }


    /**
     * @param string $email Email that this AuthorizationDocument will be sent to for signing.
     * @param string $status Status of an instance resource. It can hold one of the values: 1. opened 2. signing, 3. signed LOA, 4. canceled, 5. failed. See the section entitled [Status Values](https://www.twilio.com/docs/phone-numbers/hosted-numbers/hosted-numbers-api/authorization-document-resource#status-values) for more information on each of these statuses.
     * @return ReadAuthorizationDocumentOptions Options builder
     */
    public static function read(
        
        string $email = Values::NONE,
        string $status = Values::NONE

    ): ReadAuthorizationDocumentOptions
    {
        return new ReadAuthorizationDocumentOptions(
            $email,
            $status
        );
    }

    /**
     * @param string[] $hostedNumberOrderSids A list of HostedNumberOrder sids that this AuthorizationDocument will authorize for hosting phone number capabilities on Twilio's platform.
     * @param string $addressSid A 34 character string that uniquely identifies the Address resource that is associated with this AuthorizationDocument.
     * @param string $email Email that this AuthorizationDocument will be sent to for signing.
     * @param string[] $ccEmails Email recipients who will be informed when an Authorization Document has been sent and signed
     * @param string $status
     * @param string $contactTitle The title of the person authorized to sign the Authorization Document for this phone number.
     * @param string $contactPhoneNumber The contact phone number of the person authorized to sign the Authorization Document.
     * @return UpdateAuthorizationDocumentOptions Options builder
     */
    public static function update(
        
        array $hostedNumberOrderSids = Values::ARRAY_NONE,
        string $addressSid = Values::NONE,
        string $email = Values::NONE,
        array $ccEmails = Values::ARRAY_NONE,
        string $status = Values::NONE,
        string $contactTitle = Values::NONE,
        string $contactPhoneNumber = Values::NONE

    ): UpdateAuthorizationDocumentOptions
    {
        return new UpdateAuthorizationDocumentOptions(
            $hostedNumberOrderSids,
            $addressSid,
            $email,
            $ccEmails,
            $status,
            $contactTitle,
            $contactPhoneNumber
        );
    }

}

class CreateAuthorizationDocumentOptions extends Options
    {
    /**
     * @param string[] $ccEmails Email recipients who will be informed when an Authorization Document has been sent and signed.
     */
    public function __construct(
        
        array $ccEmails = Values::ARRAY_NONE

    ) {
        $this->options['ccEmails'] = $ccEmails;
    }

    /**
     * Email recipients who will be informed when an Authorization Document has been sent and signed.
     *
     * @param string[] $ccEmails Email recipients who will be informed when an Authorization Document has been sent and signed.
     * @return $this Fluent Builder
     */
    public function setCcEmails(array $ccEmails): self
    {
        $this->options['ccEmails'] = $ccEmails;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Preview.HostedNumbers.CreateAuthorizationDocumentOptions ' . $options . ']';
    }
}


class ReadAuthorizationDocumentOptions extends Options
    {
    /**
     * @param string $email Email that this AuthorizationDocument will be sent to for signing.
     * @param string $status Status of an instance resource. It can hold one of the values: 1. opened 2. signing, 3. signed LOA, 4. canceled, 5. failed. See the section entitled [Status Values](https://www.twilio.com/docs/phone-numbers/hosted-numbers/hosted-numbers-api/authorization-document-resource#status-values) for more information on each of these statuses.
     */
    public function __construct(
        
        string $email = Values::NONE,
        string $status = Values::NONE

    ) {
        $this->options['email'] = $email;
        $this->options['status'] = $status;
    }

    /**
     * Email that this AuthorizationDocument will be sent to for signing.
     *
     * @param string $email Email that this AuthorizationDocument will be sent to for signing.
     * @return $this Fluent Builder
     */
    public function setEmail(string $email): self
    {
        $this->options['email'] = $email;
        return $this;
    }

    /**
     * Status of an instance resource. It can hold one of the values: 1. opened 2. signing, 3. signed LOA, 4. canceled, 5. failed. See the section entitled [Status Values](https://www.twilio.com/docs/phone-numbers/hosted-numbers/hosted-numbers-api/authorization-document-resource#status-values) for more information on each of these statuses.
     *
     * @param string $status Status of an instance resource. It can hold one of the values: 1. opened 2. signing, 3. signed LOA, 4. canceled, 5. failed. See the section entitled [Status Values](https://www.twilio.com/docs/phone-numbers/hosted-numbers/hosted-numbers-api/authorization-document-resource#status-values) for more information on each of these statuses.
     * @return $this Fluent Builder
     */
    public function setStatus(string $status): self
    {
        $this->options['status'] = $status;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Preview.HostedNumbers.ReadAuthorizationDocumentOptions ' . $options . ']';
    }
}

class UpdateAuthorizationDocumentOptions extends Options
    {
    /**
     * @param string[] $hostedNumberOrderSids A list of HostedNumberOrder sids that this AuthorizationDocument will authorize for hosting phone number capabilities on Twilio's platform.
     * @param string $addressSid A 34 character string that uniquely identifies the Address resource that is associated with this AuthorizationDocument.
     * @param string $email Email that this AuthorizationDocument will be sent to for signing.
     * @param string[] $ccEmails Email recipients who will be informed when an Authorization Document has been sent and signed
     * @param string $status
     * @param string $contactTitle The title of the person authorized to sign the Authorization Document for this phone number.
     * @param string $contactPhoneNumber The contact phone number of the person authorized to sign the Authorization Document.
     */
    public function __construct(
        
        array $hostedNumberOrderSids = Values::ARRAY_NONE,
        string $addressSid = Values::NONE,
        string $email = Values::NONE,
        array $ccEmails = Values::ARRAY_NONE,
        string $status = Values::NONE,
        string $contactTitle = Values::NONE,
        string $contactPhoneNumber = Values::NONE

    ) {
        $this->options['hostedNumberOrderSids'] = $hostedNumberOrderSids;
        $this->options['addressSid'] = $addressSid;
        $this->options['email'] = $email;
        $this->options['ccEmails'] = $ccEmails;
        $this->options['status'] = $status;
        $this->options['contactTitle'] = $contactTitle;
        $this->options['contactPhoneNumber'] = $contactPhoneNumber;
    }

    /**
     * A list of HostedNumberOrder sids that this AuthorizationDocument will authorize for hosting phone number capabilities on Twilio's platform.
     *
     * @param string[] $hostedNumberOrderSids A list of HostedNumberOrder sids that this AuthorizationDocument will authorize for hosting phone number capabilities on Twilio's platform.
     * @return $this Fluent Builder
     */
    public function setHostedNumberOrderSids(array $hostedNumberOrderSids): self
    {
        $this->options['hostedNumberOrderSids'] = $hostedNumberOrderSids;
        return $this;
    }

    /**
     * A 34 character string that uniquely identifies the Address resource that is associated with this AuthorizationDocument.
     *
     * @param string $addressSid A 34 character string that uniquely identifies the Address resource that is associated with this AuthorizationDocument.
     * @return $this Fluent Builder
     */
    public function setAddressSid(string $addressSid): self
    {
        $this->options['addressSid'] = $addressSid;
        return $this;
    }

    /**
     * Email that this AuthorizationDocument will be sent to for signing.
     *
     * @param string $email Email that this AuthorizationDocument will be sent to for signing.
     * @return $this Fluent Builder
     */
    public function setEmail(string $email): self
    {
        $this->options['email'] = $email;
        return $this;
    }

    /**
     * Email recipients who will be informed when an Authorization Document has been sent and signed
     *
     * @param string[] $ccEmails Email recipients who will be informed when an Authorization Document has been sent and signed
     * @return $this Fluent Builder
     */
    public function setCcEmails(array $ccEmails): self
    {
        $this->options['ccEmails'] = $ccEmails;
        return $this;
    }

    /**
     * @param string $status
     * @return $this Fluent Builder
     */
    public function setStatus(string $status): self
    {
        $this->options['status'] = $status;
        return $this;
    }

    /**
     * The title of the person authorized to sign the Authorization Document for this phone number.
     *
     * @param string $contactTitle The title of the person authorized to sign the Authorization Document for this phone number.
     * @return $this Fluent Builder
     */
    public function setContactTitle(string $contactTitle): self
    {
        $this->options['contactTitle'] = $contactTitle;
        return $this;
    }

    /**
     * The contact phone number of the person authorized to sign the Authorization Document.
     *
     * @param string $contactPhoneNumber The contact phone number of the person authorized to sign the Authorization Document.
     * @return $this Fluent Builder
     */
    public function setContactPhoneNumber(string $contactPhoneNumber): self
    {
        $this->options['contactPhoneNumber'] = $contactPhoneNumber;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Preview.HostedNumbers.UpdateAuthorizationDocumentOptions ' . $options . ']';
    }
}

