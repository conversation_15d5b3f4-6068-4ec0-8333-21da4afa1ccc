<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\FlexApi\V1\Interaction\InteractionChannel;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;


/**
 * @property string|null $sid
 * @property string|null $instanceSid
 * @property string|null $accountSid
 * @property string|null $interactionSid
 * @property string|null $channelSid
 * @property string|null $executionSid
 * @property string $type
 * @property string $status
 * @property string|null $from
 * @property string|null $to
 * @property string|null $noteSid
 * @property string|null $summarySid
 * @property \DateTime|null $dateCreated
 * @property \DateTime|null $dateUpdated
 * @property string|null $url
 */
class InteractionTransferInstance extends InstanceResource
{
    /**
     * Initialize the InteractionTransferInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $interactionSid The Interaction Sid for the Interaction
     * @param string $channelSid The Channel Sid for the Channel.
     * @param string $sid The unique string created by Twilio to identify a Transfer resource.
     */
    public function __construct(Version $version, array $payload, string $interactionSid, string $channelSid, ?string $sid = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'sid' => Values::array_get($payload, 'sid'),
            'instanceSid' => Values::array_get($payload, 'instance_sid'),
            'accountSid' => Values::array_get($payload, 'account_sid'),
            'interactionSid' => Values::array_get($payload, 'interaction_sid'),
            'channelSid' => Values::array_get($payload, 'channel_sid'),
            'executionSid' => Values::array_get($payload, 'execution_sid'),
            'type' => Values::array_get($payload, 'type'),
            'status' => Values::array_get($payload, 'status'),
            'from' => Values::array_get($payload, 'from'),
            'to' => Values::array_get($payload, 'to'),
            'noteSid' => Values::array_get($payload, 'note_sid'),
            'summarySid' => Values::array_get($payload, 'summary_sid'),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'dateUpdated' => Deserialize::dateTime(Values::array_get($payload, 'date_updated')),
            'url' => Values::array_get($payload, 'url'),
        ];

        $this->solution = ['interactionSid' => $interactionSid, 'channelSid' => $channelSid, 'sid' => $sid ?: $this->properties['sid'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return InteractionTransferContext Context for this InteractionTransferInstance
     */
    protected function proxy(): InteractionTransferContext
    {
        if (!$this->context) {
            $this->context = new InteractionTransferContext(
                $this->version,
                $this->solution['interactionSid'],
                $this->solution['channelSid'],
                $this->solution['sid']
            );
        }

        return $this->context;
    }

    /**
     * Fetch the InteractionTransferInstance
     *
     * @return InteractionTransferInstance Fetched InteractionTransferInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): InteractionTransferInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Update the InteractionTransferInstance
     *
     * @return InteractionTransferInstance Updated InteractionTransferInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(): InteractionTransferInstance
    {

        return $this->proxy()->update();
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.FlexApi.V1.InteractionTransferInstance ' . \implode(' ', $context) . ']';
    }
}

