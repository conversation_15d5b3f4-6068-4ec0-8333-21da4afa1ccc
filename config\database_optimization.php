<?php

/**
 * ملف تكوين تحسين قاعدة البيانات للتعامل مع مليون تسجيل
 * 
 * هذا الملف يحتوي على جميع الإعدادات والتكوينات المطلوبة
 * لتحسين أداء قاعدة البيانات مع البيانات الكبيرة
 * 
 * @package Config
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */

return [

    /*
    |--------------------------------------------------------------------------
    | إعدادات الأرشفة
    |--------------------------------------------------------------------------
    |
    | تحديد متى وكيف يتم أرشفة البيانات القديمة
    |
    */
    'archiving' => [
        'enabled' => env('DB_ARCHIVING_ENABLED', true),
        
        // عدد الأشهر لاعتبار البيانات قديمة
        'thresholds' => [
            'projects' => env('ARCHIVE_PROJECTS_MONTHS', 12),
            'tasks' => env('ARCHIVE_TASKS_MONTHS', 12),
            'time_entries' => env('ARCHIVE_TIME_ENTRIES_MONTHS', 24),
            'comments' => env('ARCHIVE_COMMENTS_MONTHS', 18),
            'files' => env('ARCHIVE_FILES_MONTHS', 36),
            'activity_logs' => env('ARCHIVE_ACTIVITY_LOGS_MONTHS', 6),
        ],
        
        // الحد الأقصى لعدد السجلات المؤرشفة في دفعة واحدة
        'batch_size' => env('ARCHIVE_BATCH_SIZE', 1000),
        
        // تشغيل الأرشفة تلقائياً
        'auto_archive' => env('AUTO_ARCHIVE_ENABLED', true),
        
        // الاحتفاظ بنسخة احتياطية قبل الأرشفة
        'backup_before_archive' => env('BACKUP_BEFORE_ARCHIVE', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات التخزين المؤقت
    |--------------------------------------------------------------------------
    |
    | تكوين التخزين المؤقت للاستعلامات والإحصائيات
    |
    */
    'caching' => [
        'enabled' => env('DB_CACHING_ENABLED', true),
        
        // مدة التخزين المؤقت بالدقائق
        'ttl' => [
            'statistics' => env('CACHE_STATISTICS_TTL', 30),
            'reports' => env('CACHE_REPORTS_TTL', 120),
            'search' => env('CACHE_SEARCH_TTL', 60),
            'dashboard' => env('CACHE_DASHBOARD_TTL', 15),
        ],
        
        // الحد الأقصى لحجم التخزين المؤقت (بالميجابايت)
        'max_size_mb' => env('CACHE_MAX_SIZE_MB', 500),
        
        // تنظيف التخزين المؤقت تلقائياً
        'auto_cleanup' => env('CACHE_AUTO_CLEANUP', true),
        
        // استخدام ضغط البيانات في التخزين المؤقت
        'compression' => env('CACHE_COMPRESSION', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات الفهرسة والبحث
    |--------------------------------------------------------------------------
    |
    | تكوين فهارس البحث والفهرسة التلقائية
    |
    */
    'indexing' => [
        'enabled' => env('DB_INDEXING_ENABLED', true),
        
        // تحديث الفهارس تلقائياً
        'auto_update' => env('AUTO_INDEX_UPDATE', true),
        
        // الحد الأدنى لدرجة الصلة في البحث
        'min_relevance_score' => env('MIN_RELEVANCE_SCORE', 0.5),
        
        // عدد النتائج الافتراضي في البحث
        'default_search_limit' => env('DEFAULT_SEARCH_LIMIT', 50),
        
        // تحديث الفهارس عند تغيير البيانات
        'real_time_indexing' => env('REAL_TIME_INDEXING', false),
        
        // استخدام البحث النصي الكامل
        'full_text_search' => env('FULL_TEXT_SEARCH', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات مراقبة الأداء
    |--------------------------------------------------------------------------
    |
    | تكوين مراقبة أداء قاعدة البيانات والتنبيهات
    |
    */
    'monitoring' => [
        'enabled' => env('DB_MONITORING_ENABLED', true),
        
        // حدود التنبيهات
        'thresholds' => [
            'slow_query_seconds' => env('SLOW_QUERY_THRESHOLD', 5),
            'table_size_mb' => env('TABLE_SIZE_THRESHOLD', 1000),
            'connection_usage_percent' => env('CONNECTION_THRESHOLD', 80),
            'disk_usage_percent' => env('DISK_USAGE_THRESHOLD', 80),
            'cache_hit_rate_percent' => env('CACHE_HIT_RATE_THRESHOLD', 70),
        ],
        
        // تكرار المراقبة (بالدقائق)
        'check_interval' => env('MONITORING_INTERVAL', 5),
        
        // إرسال تنبيهات
        'alerts' => [
            'enabled' => env('MONITORING_ALERTS_ENABLED', true),
            'email' => env('MONITORING_ALERT_EMAIL', '<EMAIL>'),
            'slack_webhook' => env('MONITORING_SLACK_WEBHOOK'),
            'critical_only' => env('ALERT_CRITICAL_ONLY', false),
        ],
        
        // حفظ سجل المراقبة
        'logging' => [
            'enabled' => env('MONITORING_LOGGING_ENABLED', true),
            'retention_days' => env('MONITORING_LOG_RETENTION', 30),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات تحسين MySQL
    |--------------------------------------------------------------------------
    |
    | إعدادات محددة لتحسين أداء MySQL مع البيانات الكبيرة
    |
    */
    'mysql_optimization' => [
        'enabled' => env('MYSQL_OPTIMIZATION_ENABLED', true),
        
        // إعدادات InnoDB
        'innodb' => [
            'buffer_pool_size' => env('INNODB_BUFFER_POOL_SIZE', '1G'),
            'log_file_size' => env('INNODB_LOG_FILE_SIZE', '256M'),
            'flush_log_at_trx_commit' => env('INNODB_FLUSH_LOG_AT_TRX_COMMIT', 2),
            'file_per_table' => env('INNODB_FILE_PER_TABLE', true),
            'flush_method' => env('INNODB_FLUSH_METHOD', 'O_DIRECT'),
        ],
        
        // إعدادات Query Cache
        'query_cache' => [
            'size' => env('QUERY_CACHE_SIZE', '128M'),
            'type' => env('QUERY_CACHE_TYPE', 1),
            'limit' => env('QUERY_CACHE_LIMIT', '2M'),
        ],
        
        // إعدادات الذاكرة المؤقتة
        'tmp_table' => [
            'size' => env('TMP_TABLE_SIZE', '128M'),
            'max_heap_table_size' => env('MAX_HEAP_TABLE_SIZE', '128M'),
        ],
        
        // إعدادات الاتصالات
        'connections' => [
            'max_connections' => env('MAX_CONNECTIONS', 200),
            'max_connect_errors' => env('MAX_CONNECT_ERRORS', 100000),
            'connect_timeout' => env('CONNECT_TIMEOUT', 10),
            'wait_timeout' => env('WAIT_TIMEOUT', 28800),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات التقسيم (Partitioning)
    |--------------------------------------------------------------------------
    |
    | تكوين تقسيم الجداول الكبيرة
    |
    */
    'partitioning' => [
        'enabled' => env('DB_PARTITIONING_ENABLED', true),
        
        // الجداول المقسمة
        'tables' => [
            'time_entries' => [
                'type' => 'RANGE',
                'column' => 'date',
                'interval' => 'YEAR',
            ],
            'activity_logs' => [
                'type' => 'RANGE', 
                'column' => 'created_at',
                'interval' => 'YEAR',
            ],
            'time_entries_archive' => [
                'type' => 'RANGE',
                'column' => 'date', 
                'interval' => 'YEAR',
            ],
        ],
        
        // إنشاء أقسام جديدة تلقائياً
        'auto_create_partitions' => env('AUTO_CREATE_PARTITIONS', true),
        
        // حذف الأقسام القديمة تلقائياً
        'auto_drop_old_partitions' => env('AUTO_DROP_OLD_PARTITIONS', false),
        
        // عدد السنوات للاحتفاظ بالأقسام
        'retention_years' => env('PARTITION_RETENTION_YEARS', 5),
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات النسخ الاحتياطي
    |--------------------------------------------------------------------------
    |
    | تكوين النسخ الاحتياطي التلقائي
    |
    */
    'backup' => [
        'enabled' => env('DB_BACKUP_ENABLED', true),
        
        // تكرار النسخ الاحتياطي
        'schedule' => [
            'daily' => env('DAILY_BACKUP_ENABLED', true),
            'weekly' => env('WEEKLY_BACKUP_ENABLED', true),
            'monthly' => env('MONTHLY_BACKUP_ENABLED', true),
        ],
        
        // مسار حفظ النسخ الاحتياطي
        'path' => env('BACKUP_PATH', storage_path('backups')),
        
        // ضغط النسخ الاحتياطي
        'compression' => env('BACKUP_COMPRESSION', true),
        
        // الاحتفاظ بالنسخ الاحتياطي (بالأيام)
        'retention' => [
            'daily' => env('DAILY_BACKUP_RETENTION', 7),
            'weekly' => env('WEEKLY_BACKUP_RETENTION', 30),
            'monthly' => env('MONTHLY_BACKUP_RETENTION', 365),
        ],
        
        // رفع النسخ الاحتياطي للتخزين السحابي
        'cloud_storage' => [
            'enabled' => env('BACKUP_CLOUD_ENABLED', false),
            'disk' => env('BACKUP_CLOUD_DISK', 's3'),
            'path' => env('BACKUP_CLOUD_PATH', 'database-backups'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات التقارير والإحصائيات
    |--------------------------------------------------------------------------
    |
    | تكوين إنتاج التقارير والإحصائيات
    |
    */
    'reporting' => [
        'enabled' => env('DB_REPORTING_ENABLED', true),
        
        // تحديث الإحصائيات تلقائياً
        'auto_update_statistics' => env('AUTO_UPDATE_STATISTICS', true),
        
        // تكرار تحديث الإحصائيات (بالدقائق)
        'statistics_update_interval' => env('STATISTICS_UPDATE_INTERVAL', 60),
        
        // إنتاج التقارير تلقائياً
        'auto_generate_reports' => env('AUTO_GENERATE_REPORTS', true),
        
        // أنواع التقارير المفعلة
        'enabled_reports' => [
            'performance' => env('PERFORMANCE_REPORTS_ENABLED', true),
            'usage' => env('USAGE_REPORTS_ENABLED', true),
            'growth' => env('GROWTH_REPORTS_ENABLED', true),
            'errors' => env('ERROR_REPORTS_ENABLED', true),
        ],
        
        // تصدير التقارير
        'export' => [
            'formats' => ['pdf', 'excel', 'csv'],
            'auto_email' => env('AUTO_EMAIL_REPORTS', false),
            'email_recipients' => env('REPORT_EMAIL_RECIPIENTS', '<EMAIL>'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات متقدمة
    |--------------------------------------------------------------------------
    |
    | إعدادات متقدمة لتحسين الأداء
    |
    */
    'advanced' => [
        // استخدام Read Replicas
        'read_replicas' => [
            'enabled' => env('READ_REPLICAS_ENABLED', false),
            'connections' => env('READ_REPLICA_CONNECTIONS', []),
        ],
        
        // تحسين الاستعلامات تلقائياً
        'query_optimization' => [
            'enabled' => env('QUERY_OPTIMIZATION_ENABLED', true),
            'explain_slow_queries' => env('EXPLAIN_SLOW_QUERIES', true),
            'suggest_indexes' => env('SUGGEST_INDEXES', true),
        ],
        
        // استخدام Connection Pooling
        'connection_pooling' => [
            'enabled' => env('CONNECTION_POOLING_ENABLED', false),
            'pool_size' => env('CONNECTION_POOL_SIZE', 20),
        ],
        
        // تحسين الذاكرة
        'memory_optimization' => [
            'enabled' => env('MEMORY_OPTIMIZATION_ENABLED', true),
            'lazy_loading' => env('LAZY_LOADING_ENABLED', true),
            'chunk_size' => env('CHUNK_SIZE', 1000),
        ],
    ],

];
