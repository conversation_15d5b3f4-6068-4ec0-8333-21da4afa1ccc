<?php

namespace App\Console\Commands\ProjectManagement;

use Illuminate\Console\Command;
use App\Services\ProjectManagement\DatabaseOptimizationService;
use App\Services\ProjectManagement\CacheService;
use Illuminate\Support\Facades\Log;

/**
 * أمر تحسين قاعدة البيانات
 * 
 * هذا الأمر يقوم بتحسين أداء قاعدة البيانات وإدارة البيانات الكبيرة
 * يمكن تشغيله يدوياً أو جدولته للتشغيل التلقائي
 * 
 * @package App\Console\Commands\ProjectManagement
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
class OptimizeDatabaseCommand extends Command
{
    /**
     * اسم الأمر وتوقيعه
     *
     * @var string
     */
    protected $signature = 'pm:optimize-database 
                            {--archive : أرشفة البيانات القديمة}
                            {--cache : تنظيف التخزين المؤقت}
                            {--index : تحديث فهارس البحث}
                            {--analyze : تحليل أداء قاعدة البيانات}
                            {--all : تنفيذ جميع عمليات التحسين}
                            {--months=12 : عدد الأشهر للأرشفة}
                            {--force : تنفيذ بدون تأكيد}';

    /**
     * وصف الأمر
     *
     * @var string
     */
    protected $description = 'تحسين أداء قاعدة البيانات وإدارة البيانات الكبيرة';

    /**
     * خدمة تحسين قاعدة البيانات
     *
     * @var DatabaseOptimizationService
     */
    protected DatabaseOptimizationService $optimizationService;

    /**
     * خدمة التخزين المؤقت
     *
     * @var CacheService
     */
    protected CacheService $cacheService;

    /**
     * منشئ الأمر
     *
     * @param DatabaseOptimizationService $optimizationService
     * @param CacheService $cacheService
     */
    public function __construct(
        DatabaseOptimizationService $optimizationService,
        CacheService $cacheService
    ) {
        parent::__construct();
        $this->optimizationService = $optimizationService;
        $this->cacheService = $cacheService;
    }

    /**
     * تنفيذ الأمر
     *
     * @return int
     */
    public function handle(): int
    {
        $this->info('🚀 بدء عملية تحسين قاعدة البيانات...');
        $this->newLine();

        $startTime = microtime(true);
        $operations = [];

        try {
            // تحديد العمليات المطلوبة
            if ($this->option('all')) {
                $operations = ['archive', 'cache', 'index', 'analyze'];
            } else {
                if ($this->option('archive')) $operations[] = 'archive';
                if ($this->option('cache')) $operations[] = 'cache';
                if ($this->option('index')) $operations[] = 'index';
                if ($this->option('analyze')) $operations[] = 'analyze';
            }

            if (empty($operations)) {
                $this->warn('⚠️  لم يتم تحديد أي عمليات للتنفيذ. استخدم --help لمعرفة الخيارات المتاحة.');
                return Command::FAILURE;
            }

            // تأكيد التنفيذ
            if (!$this->option('force') && !$this->confirmExecution($operations)) {
                $this->info('تم إلغاء العملية.');
                return Command::SUCCESS;
            }

            // تنفيذ العمليات
            foreach ($operations as $operation) {
                $this->executeOperation($operation);
            }

            $executionTime = round(microtime(true) - $startTime, 2);
            $this->newLine();
            $this->info("✅ تم إنجاز جميع عمليات التحسين بنجاح في {$executionTime} ثانية");

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('❌ خطأ في تنفيذ عمليات التحسين: ' . $e->getMessage());
            Log::error('خطأ في أمر تحسين قاعدة البيانات', [
                'error' => $e->getMessage(),
                'operations' => $operations
            ]);

            return Command::FAILURE;
        }
    }

    /**
     * تأكيد تنفيذ العمليات
     *
     * @param array $operations العمليات المطلوبة
     * @return bool
     */
    protected function confirmExecution(array $operations): bool
    {
        $this->warn('سيتم تنفيذ العمليات التالية:');
        
        foreach ($operations as $operation) {
            $description = $this->getOperationDescription($operation);
            $this->line("  • {$description}");
        }

        $this->newLine();
        
        return $this->confirm('هل تريد المتابعة؟', false);
    }

    /**
     * الحصول على وصف العملية
     *
     * @param string $operation العملية
     * @return string الوصف
     */
    protected function getOperationDescription(string $operation): string
    {
        $descriptions = [
            'archive' => 'أرشفة البيانات القديمة (المشاريع والمهام وسجلات الوقت)',
            'cache' => 'تنظيف التخزين المؤقت المنتهي الصلاحية',
            'index' => 'تحديث فهارس البحث والفهرسة',
            'analyze' => 'تحليل أداء قاعدة البيانات وإنتاج تقرير'
        ];

        return $descriptions[$operation] ?? $operation;
    }

    /**
     * تنفيذ عملية محددة
     *
     * @param string $operation العملية
     */
    protected function executeOperation(string $operation): void
    {
        $this->info("🔄 تنفيذ عملية: " . $this->getOperationDescription($operation));
        
        $startTime = microtime(true);
        
        switch ($operation) {
            case 'archive':
                $this->executeArchiveOperation();
                break;
                
            case 'cache':
                $this->executeCacheOperation();
                break;
                
            case 'index':
                $this->executeIndexOperation();
                break;
                
            case 'analyze':
                $this->executeAnalyzeOperation();
                break;
        }
        
        $executionTime = round(microtime(true) - $startTime, 2);
        $this->line("   ✓ مكتملة في {$executionTime} ثانية");
        $this->newLine();
    }

    /**
     * تنفيذ عملية الأرشفة
     */
    protected function executeArchiveOperation(): void
    {
        $months = (int) $this->option('months');
        
        $this->line("   📦 أرشفة المشاريع الأقدم من {$months} شهر...");
        $projectResult = $this->optimizationService->archiveOldProjects($months);
        
        if ($projectResult['success']) {
            $this->line("      ✓ تم أرشفة {$projectResult['archived_count']} مشروع");
            if (!empty($projectResult['errors'])) {
                $this->warn("      ⚠️  {$projectResult['errors']} أخطاء");
            }
        } else {
            $this->error("      ❌ فشل في أرشفة المشاريع: {$projectResult['error']}");
        }

        $this->line("   📝 أرشفة سجلات الوقت الأقدم من 24 شهر...");
        $timeResult = $this->optimizationService->archiveOldTimeEntries(24);
        
        if ($timeResult['success']) {
            $this->line("      ✓ تم أرشفة {$timeResult['archived_count']} سجل وقت");
        } else {
            $this->error("      ❌ فشل في أرشفة سجلات الوقت: {$timeResult['error']}");
        }
    }

    /**
     * تنفيذ عملية تنظيف التخزين المؤقت
     */
    protected function executeCacheOperation(): void
    {
        $this->line("   🧹 تنظيف التخزين المؤقت المنتهي الصلاحية...");
        $result = $this->optimizationService->cleanExpiredCache();
        
        if ($result['success']) {
            $this->line("      ✓ تم تنظيف {$result['total_cleaned']} عنصر");
            $this->line("        - إحصائيات: {$result['expired_stats']}");
            $this->line("        - تقارير: {$result['expired_reports']}");
            $this->line("        - فهارس بحث: {$result['old_search_index']}");
        } else {
            $this->error("      ❌ فشل في تنظيف التخزين المؤقت: {$result['error']}");
        }

        $this->line("   📊 عرض إحصائيات التخزين المؤقت...");
        $stats = $this->cacheService->getCacheStatistics();
        
        if (!empty($stats['by_category'])) {
            foreach ($stats['by_category'] as $category) {
                $this->line("      • {$category->category}: {$category->active_entries} نشط، {$category->total_hits} استخدام");
            }
        }
    }

    /**
     * تنفيذ عملية تحديث الفهارس
     */
    protected function executeIndexOperation(): void
    {
        $this->line("   🔍 تحديث فهارس البحث...");
        $result = $this->optimizationService->updateSearchIndex();
        
        if ($result['success']) {
            $this->line("      ✓ تم فهرسة {$result['indexed_count']} عنصر");
        } else {
            $this->error("      ❌ فشل في تحديث الفهارس: {$result['error']}");
        }
    }

    /**
     * تنفيذ عملية تحليل الأداء
     */
    protected function executeAnalyzeOperation(): void
    {
        $this->line("   📈 تحليل أداء قاعدة البيانات...");
        $result = $this->optimizationService->analyzeDatabasePerformance();
        
        if ($result['success']) {
            $analysis = $result['analysis'];
            
            $this->line("      📊 أحجام الجداول:");
            foreach (array_slice($analysis['table_sizes'], 0, 5) as $table) {
                $this->line("        • {$table->table_name}: {$table->size_mb} MB ({$table->table_rows} صف)");
            }
            
            if (!empty($analysis['slow_queries'])) {
                $this->line("      🐌 الاستعلامات البطيئة:");
                foreach (array_slice($analysis['slow_queries'], 0, 3) as $query) {
                    $this->line("        • وقت التنفيذ: {$query->query_time}s");
                }
            } else {
                $this->line("      ✓ لا توجد استعلامات بطيئة");
            }
            
        } else {
            $this->error("      ❌ فشل في تحليل الأداء: {$result['error']}");
        }
    }

    /**
     * عرض معلومات إضافية عن الأمر
     */
    public function getHelp(): string
    {
        return "
هذا الأمر يقوم بتحسين أداء قاعدة البيانات من خلال:

🔧 العمليات المتاحة:
  --archive    أرشفة البيانات القديمة لتقليل حجم الجداول الرئيسية
  --cache      تنظيف التخزين المؤقت المنتهي الصلاحية
  --index      تحديث فهارس البحث لتحسين سرعة البحث
  --analyze    تحليل أداء قاعدة البيانات وإنتاج تقرير مفصل
  --all        تنفيذ جميع العمليات السابقة

⚙️ خيارات إضافية:
  --months=12  عدد الأشهر لاعتبار البيانات قديمة (افتراضي: 12)
  --force      تنفيذ بدون طلب تأكيد

📝 أمثلة على الاستخدام:
  php artisan pm:optimize-database --all
  php artisan pm:optimize-database --archive --months=6
  php artisan pm:optimize-database --cache --index
  php artisan pm:optimize-database --analyze --force

⏰ يُنصح بجدولة هذا الأمر للتشغيل التلقائي:
  - يومياً: --cache
  - أسبوعياً: --index
  - شهرياً: --archive --analyze
";
    }
}
