<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Insights
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Insights\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;
use Twilio\Rest\Insights\V1\Conference\ConferenceParticipantList;


/**
 * @property string|null $conferenceSid
 * @property string|null $accountSid
 * @property string|null $friendlyName
 * @property \DateTime|null $createTime
 * @property \DateTime|null $startTime
 * @property \DateTime|null $endTime
 * @property int|null $durationSeconds
 * @property int|null $connectDurationSeconds
 * @property string $status
 * @property int|null $maxParticipants
 * @property int|null $maxConcurrentParticipants
 * @property int|null $uniqueParticipants
 * @property string $endReason
 * @property string|null $endedBy
 * @property string $mixerRegion
 * @property string $mixerRegionRequested
 * @property bool|null $recordingEnabled
 * @property array|null $detectedIssues
 * @property string[]|null $tags
 * @property array|null $tagInfo
 * @property string $processingState
 * @property string|null $url
 * @property array|null $links
 */
class ConferenceInstance extends InstanceResource
{
    protected $_conferenceParticipants;

    /**
     * Initialize the ConferenceInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $conferenceSid The unique SID identifier of the Conference.
     */
    public function __construct(Version $version, array $payload, ?string $conferenceSid = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'conferenceSid' => Values::array_get($payload, 'conference_sid'),
            'accountSid' => Values::array_get($payload, 'account_sid'),
            'friendlyName' => Values::array_get($payload, 'friendly_name'),
            'createTime' => Deserialize::dateTime(Values::array_get($payload, 'create_time')),
            'startTime' => Deserialize::dateTime(Values::array_get($payload, 'start_time')),
            'endTime' => Deserialize::dateTime(Values::array_get($payload, 'end_time')),
            'durationSeconds' => Values::array_get($payload, 'duration_seconds'),
            'connectDurationSeconds' => Values::array_get($payload, 'connect_duration_seconds'),
            'status' => Values::array_get($payload, 'status'),
            'maxParticipants' => Values::array_get($payload, 'max_participants'),
            'maxConcurrentParticipants' => Values::array_get($payload, 'max_concurrent_participants'),
            'uniqueParticipants' => Values::array_get($payload, 'unique_participants'),
            'endReason' => Values::array_get($payload, 'end_reason'),
            'endedBy' => Values::array_get($payload, 'ended_by'),
            'mixerRegion' => Values::array_get($payload, 'mixer_region'),
            'mixerRegionRequested' => Values::array_get($payload, 'mixer_region_requested'),
            'recordingEnabled' => Values::array_get($payload, 'recording_enabled'),
            'detectedIssues' => Values::array_get($payload, 'detected_issues'),
            'tags' => Values::array_get($payload, 'tags'),
            'tagInfo' => Values::array_get($payload, 'tag_info'),
            'processingState' => Values::array_get($payload, 'processing_state'),
            'url' => Values::array_get($payload, 'url'),
            'links' => Values::array_get($payload, 'links'),
        ];

        $this->solution = ['conferenceSid' => $conferenceSid ?: $this->properties['conferenceSid'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return ConferenceContext Context for this ConferenceInstance
     */
    protected function proxy(): ConferenceContext
    {
        if (!$this->context) {
            $this->context = new ConferenceContext(
                $this->version,
                $this->solution['conferenceSid']
            );
        }

        return $this->context;
    }

    /**
     * Fetch the ConferenceInstance
     *
     * @return ConferenceInstance Fetched ConferenceInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): ConferenceInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Access the conferenceParticipants
     */
    protected function getConferenceParticipants(): ConferenceParticipantList
    {
        return $this->proxy()->conferenceParticipants;
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Insights.V1.ConferenceInstance ' . \implode(' ', $context) . ']';
    }
}

