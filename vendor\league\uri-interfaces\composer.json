{"name": "league/uri-interfaces", "type": "library", "description": "Common interfaces and classes for URI representation and interaction", "keywords": ["url", "uri", "rfc3986", "rfc3987", "rfc6570", "psr-7", "parse_url", "http", "https", "ws", "ftp", "data-uri", "file-uri", "parse_str", "query-string", "querystring", "hostname"], "license": "MIT", "homepage": "https://uri.thephpleague.com", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "funding": [{"type": "github", "url": "https://github.com/sponsors/nyamsprod"}], "require": {"php": "^8.1", "ext-filter": "*", "psr/http-message": "^1.1 || ^2.0", "psr/http-factory": "^1"}, "autoload": {"psr-4": {"League\\Uri\\": ""}}, "suggest": {"ext-bcmath": "to improve IPV4 host parsing", "ext-gmp": "to improve IPV4 host parsing", "ext-intl": "to handle IDN host with the best performance", "php-64bit": "to improve IPV4 host parsing", "symfony/polyfill-intl-idn": "to handle IDN host via the Symfony polyfill if ext-intl is not present"}, "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "support": {"forum": "https://thephpleague.slack.com", "docs": "https://uri.thephpleague.com", "issues": "https://github.com/thephpleague/uri-src/issues"}, "config": {"sort-packages": true}}