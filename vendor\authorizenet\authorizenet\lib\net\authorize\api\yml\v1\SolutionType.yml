net\authorize\api\contract\v1\SolutionType:
    properties:
        id:
            expose: true
            access_type: public_method
            serialized_name: id
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getId
                setter: setId
            type: string
        name:
            expose: true
            access_type: public_method
            serialized_name: name
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getName
                setter: setName
            type: string
        vendorName:
            expose: true
            access_type: public_method
            serialized_name: vendorName
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getVendorName
                setter: setVendorName
            type: string
