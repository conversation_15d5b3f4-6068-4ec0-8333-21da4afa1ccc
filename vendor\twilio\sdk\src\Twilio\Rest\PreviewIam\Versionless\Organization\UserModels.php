<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Organization Public API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\PreviewIam\Versionless\Organization;

use Twilio\Values;
abstract class UserModels
{
    /**
     * @property string $givenName The user's first or given name
     * @property string $familyName The user's last or family name
    */
    public static function createScimName(array $payload = []): ScimName
    {
        return new ScimName($payload);
    }

    /**
     * @property bool $primary Indicates if this email address is the primary one
     * @property string $value The actual email address value
     * @property string $type The type of email address (e.g., work, home, etc.)
    */
    public static function createScimEmailAddress(array $payload = []): ScimEmailAddress
    {
        return new ScimEmailAddress($payload);
    }

    /**
     * @property string $resourceType Indicates the type of the resource
     * @property \DateTime $created The date and time when the resource was created in the system
     * @property \DateTime $lastModified The date and time when the resource was last modified
     * @property string $version A version identifier for the resource. This can be used to manage resource versioning and concurrency control.
    */
    public static function createScimMeta(array $payload = []): ScimMeta
    {
        return new ScimMeta($payload);
    }

    /**
     * @property string $id Unique Twilio user sid
     * @property string $externalId External unique resource id defined by provisioning client
     * @property string $userName Unique username, MUST be same as primary email address
     * @property string $displayName User friendly display name
     * @property string $name
     * @property string[] $emails Email address list of the user. Primary email must be defined if there are more than 1 email. Primary email must match the username.
     * @property bool $active Indicates whether the user is active
     * @property string $locale User's locale
     * @property string $timezone User's time zone
     * @property string[] $schemas An array of URIs that indicate the schemas supported for this user resource
     * @property string $meta
    */
    public static function createScimUser(array $payload = []): ScimUser
    {
        return new ScimUser($payload);
    }

}

class ScimName implements \JsonSerializable
{
    /**
     * @property string $givenName The user's first or given name
     * @property string $familyName The user's last or family name
    */
        protected $givenName;
        protected $familyName;
    public function __construct(array $payload = []) {
        $this->givenName = Values::array_get($payload, 'givenName');
        $this->familyName = Values::array_get($payload, 'familyName');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'givenName' => $this->givenName,
            'familyName' => $this->familyName
        ];
    }
}

class ScimEmailAddress implements \JsonSerializable
{
    /**
     * @property bool $primary Indicates if this email address is the primary one
     * @property string $value The actual email address value
     * @property string $type The type of email address (e.g., work, home, etc.)
    */
        protected $primary;
        protected $value;
        protected $type;
    public function __construct(array $payload = []) {
        $this->primary = Values::array_get($payload, 'primary');
        $this->value = Values::array_get($payload, 'value');
        $this->type = Values::array_get($payload, 'type');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'primary' => $this->primary,
            'value' => $this->value,
            'type' => $this->type
        ];
    }
}

class ScimMeta implements \JsonSerializable
{
    /**
     * @property string $resourceType Indicates the type of the resource
     * @property \DateTime $created The date and time when the resource was created in the system
     * @property \DateTime $lastModified The date and time when the resource was last modified
     * @property string $version A version identifier for the resource. This can be used to manage resource versioning and concurrency control.
    */
        protected $resourceType;
        protected $created;
        protected $lastModified;
        protected $version;
    public function __construct(array $payload = []) {
        $this->resourceType = Values::array_get($payload, 'resourceType');
        $this->created = Values::array_get($payload, 'created');
        $this->lastModified = Values::array_get($payload, 'lastModified');
        $this->version = Values::array_get($payload, 'version');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'resourceType' => $this->resourceType,
            'created' => $this->created,
            'lastModified' => $this->lastModified,
            'version' => $this->version
        ];
    }
}

class ScimUser implements \JsonSerializable
{
    /**
     * @property string $id Unique Twilio user sid
     * @property string $externalId External unique resource id defined by provisioning client
     * @property string $userName Unique username, MUST be same as primary email address
     * @property string $displayName User friendly display name
     * @property string $name
     * @property string[] $emails Email address list of the user. Primary email must be defined if there are more than 1 email. Primary email must match the username.
     * @property bool $active Indicates whether the user is active
     * @property string $locale User's locale
     * @property string $timezone User's time zone
     * @property string[] $schemas An array of URIs that indicate the schemas supported for this user resource
     * @property string $meta
    */
        protected $id;
        protected $externalId;
        protected $userName;
        protected $displayName;
        protected $name;
        protected $emails;
        protected $active;
        protected $locale;
        protected $timezone;
        protected $schemas;
        protected $meta;
    public function __construct(array $payload = []) {
        $this->id = Values::array_get($payload, 'id');
        $this->externalId = Values::array_get($payload, 'externalId');
        $this->userName = Values::array_get($payload, 'userName');
        $this->displayName = Values::array_get($payload, 'displayName');
        $this->name = Values::array_get($payload, 'name');
        $this->emails = Values::array_get($payload, 'emails');
        $this->active = Values::array_get($payload, 'active');
        $this->locale = Values::array_get($payload, 'locale');
        $this->timezone = Values::array_get($payload, 'timezone');
        $this->schemas = Values::array_get($payload, 'schemas');
        $this->meta = Values::array_get($payload, 'meta');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'id' => $this->id,
            'externalId' => $this->externalId,
            'userName' => $this->userName,
            'displayName' => $this->displayName,
            'name' => $this->name,
            'emails' => $this->emails,
            'active' => $this->active,
            'locale' => $this->locale,
            'timezone' => $this->timezone,
            'schemas' => $this->schemas,
            'meta' => $this->meta
        ];
    }
}

