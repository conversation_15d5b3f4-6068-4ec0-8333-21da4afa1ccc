# Changelog

All notable changes to `error-solutions` will be documented in this file.

## 1.1.2 - 2024-12-11

### What's Changed

* Documentation link should follow the latest major version by @mshukurlu in https://github.com/spatie/error-solutions/pull/17
* Replace implicitly nullable parameters for PHP 8.4 by @txdFabio in https://github.com/spatie/error-solutions/pull/22

### New Contributors

* @mshukurlu made their first contribution in https://github.com/spatie/error-solutions/pull/17
* @txdFabio made their first contribution in https://github.com/spatie/error-solutions/pull/22

**Full Changelog**: https://github.com/spatie/error-solutions/compare/1.1.1...1.1.2

## 1.1.1 - 2024-07-25

### What's Changed

* Fix OpenAI response text links by @<PERSON><PERSON><PERSON><PERSON><PERSON> in https://github.com/spatie/error-solutions/pull/9

### New Contributors

* @<PERSON><PERSON><PERSON><PERSON><PERSON> made their first contribution in https://github.com/spatie/error-solutions/pull/9

**Full Changelog**: https://github.com/spatie/error-solutions/compare/1.1.0...1.1.1

## 1.1.0 - 2024-07-22

### What's Changed

* Allow to customize OpenAI Model by @arnebr in https://github.com/spatie/error-solutions/pull/7

### New Contributors

* @arnebr made their first contribution in https://github.com/spatie/error-solutions/pull/7

**Full Changelog**: https://github.com/spatie/error-solutions/compare/1.0.5...1.1.0

## 1.0.5 - 2024-07-09

### What's Changed

* Legacy `RunnableSolution` should continue to extend legacy `Solution` by @duncanmcclean in https://github.com/spatie/error-solutions/pull/6

### New Contributors

* @duncanmcclean made their first contribution in https://github.com/spatie/error-solutions/pull/6

**Full Changelog**: https://github.com/spatie/error-solutions/compare/1.0.4...1.0.5

## 1.0.4 - 2024-06-28

**Full Changelog**: https://github.com/spatie/error-solutions/compare/1.0.3...1.0.4

## 1.0.3 - 2024-06-27

**Full Changelog**: https://github.com/spatie/error-solutions/compare/1.0.2...1.0.3

## 1.0.2 - 2024-06-26

### What's Changed

* Fix AI solutions
* Bump dependabot/fetch-metadata from 1.6.0 to 2.1.0 by @dependabot in https://github.com/spatie/error-solutions/pull/1

### New Contributors

* @dependabot made their first contribution in https://github.com/spatie/error-solutions/pull/1

**Full Changelog**: https://github.com/spatie/error-solutions/compare/0.0.1...1.0.2

## 1.0.1 - 2024-06-21

- Add the legacy string comperator

**Full Changelog**: https://github.com/spatie/error-solutions/compare/1.0.0...1.0.1

## 1.0.0 - 2024-06-12

- Initial release

**Full Changelog**: https://github.com/spatie/error-solutions/compare/0.0.1...1.0.0

## 0.0.1 - 2024-06-11

**Full Changelog**: https://github.com/spatie/error-solutions/commits/0.0.1
