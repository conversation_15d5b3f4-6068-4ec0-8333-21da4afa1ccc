<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Microvisor
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Microvisor\V1\Device;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;


/**
 * @property string|null $deviceSid
 * @property string|null $key
 * @property \DateTime|null $dateRotated
 * @property string|null $url
 */
class DeviceSecretInstance extends InstanceResource
{
    /**
     * Initialize the DeviceSecretInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $deviceSid A 34-character string that uniquely identifies the Device.
     * @param string $key The secret key; up to 100 characters.
     */
    public function __construct(Version $version, array $payload, string $deviceSid, ?string $key = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'deviceSid' => Values::array_get($payload, 'device_sid'),
            'key' => Values::array_get($payload, 'key'),
            'dateRotated' => Deserialize::dateTime(Values::array_get($payload, 'date_rotated')),
            'url' => Values::array_get($payload, 'url'),
        ];

        $this->solution = ['deviceSid' => $deviceSid, 'key' => $key ?: $this->properties['key'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return DeviceSecretContext Context for this DeviceSecretInstance
     */
    protected function proxy(): DeviceSecretContext
    {
        if (!$this->context) {
            $this->context = new DeviceSecretContext(
                $this->version,
                $this->solution['deviceSid'],
                $this->solution['key']
            );
        }

        return $this->context;
    }

    /**
     * Delete the DeviceSecretInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->proxy()->delete();
    }

    /**
     * Fetch the DeviceSecretInstance
     *
     * @return DeviceSecretInstance Fetched DeviceSecretInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): DeviceSecretInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Update the DeviceSecretInstance
     *
     * @param string $value The secret value; up to 4096 characters.
     * @return DeviceSecretInstance Updated DeviceSecretInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(string $value): DeviceSecretInstance
    {

        return $this->proxy()->update($value);
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Microvisor.V1.DeviceSecretInstance ' . \implode(' ', $context) . ']';
    }
}

