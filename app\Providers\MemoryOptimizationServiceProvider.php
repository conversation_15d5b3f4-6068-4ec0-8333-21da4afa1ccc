<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Eloquent\Model;
use App\Services\MemoryOptimizationService;
use App\Services\SmartCacheService;

class MemoryOptimizationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // تسجيل الخدمات
        $this->app->singleton(MemoryOptimizationService::class);
        $this->app->singleton(SmartCacheService::class);

        // تسجيل الـ config
        $this->mergeConfigFrom(
            __DIR__.'/../../config/memory_optimization.php', 'memory_optimization'
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // نشر ملف التكوين
        $this->publishes([
            __DIR__.'/../../config/memory_optimization.php' => config_path('memory_optimization.php'),
        ], 'memory-optimization-config');

        // تطبيق إعدادات Lazy Loading على النماذج
        $this->configureLazyLoading();

        // تسجيل أحداث النماذج لتنظيف Cache
        $this->registerModelEvents();

        // تسجيل Commands
        if ($this->app->runningInConsole()) {
            $this->commands([
                \App\Console\Commands\OptimizeMemoryUsage::class,
            ]);
        }
    }

    /**
     * تكوين Lazy Loading للنماذج
     */
    protected function configureLazyLoading(): void
    {
        $config = config('memory_optimization.lazy_loading', []);
        $enabledModels = $config['enabled_models'] ?? [];

        foreach ($enabledModels as $modelClass) {
            if (class_exists($modelClass)) {
                // تفعيل منع Lazy Loading للنماذج المحددة
                $modelClass::preventLazyLoading(
                    !app()->environment('production')
                );
            }
        }

        // تحديد حجم الصفحات الافتراضي
        // Model::setPerPage($config['default_pagination_size'] ?? 50); // تم تعطيله مؤقتاً
    }

    /**
     * تسجيل أحداث النماذج لتنظيف Cache
     */
    protected function registerModelEvents(): void
    {
        $cache = $this->app->make(SmartCacheService::class);

        // تنظيف Cache عند تحديث النماذج
        Model::saved(function ($model) use ($cache) {
            $this->clearModelCache($model, $cache);
        });

        Model::deleted(function ($model) use ($cache) {
            $this->clearModelCache($model, $cache);
        });
    }

    /**
     * تنظيف Cache للنموذج
     */
    protected function clearModelCache($model, SmartCacheService $cache): void
    {
        $modelName = class_basename($model);
        $tags = [];

        switch ($modelName) {
            case 'Invoice':
                $tags = ['invoices', 'stats', 'financial'];
                break;
            case 'Customer':
                $tags = ['customers', 'stats'];
                break;
            case 'InvoicePayment':
            case 'Payment':
                $tags = ['payments', 'stats', 'financial'];
                break;
            default:
                $tags = ['stats'];
        }

        if (!empty($tags)) {
            $cache->forgetByTags($tags);
        }
    }
}
