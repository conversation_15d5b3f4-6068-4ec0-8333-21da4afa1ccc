<?php

namespace App\Repositories;

use App\Models\Customer;
use Illuminate\Support\LazyCollection;

class CustomerRepository extends BaseRepository
{
    public function __construct(Customer $model)
    {
        parent::__construct($model);
    }

    /**
     * الحصول على العملاء مع البيانات الأساسية فقط
     */
    public function getCustomersBasic($perPage = 50)
    {
        return $this->model->select([
                'id', 'customer_id', 'name', 'email', 
                'contact', 'is_active', 'created_at'
            ])
            ->paginate($perPage);
    }

    /**
     * الحصول على تفاصيل العميل مع العلاقات المحددة
     */
    public function getCustomerDetails($id, $includeInvoices = false)
    {
        $query = $this->model->select('*');

        if ($includeInvoices) {
            $query->with([
                'invoices' => function($q) {
                    $q->select('id', 'customer_id', 'invoice_id', 'status', 'total', 'send_date')
                      ->latest()
                      ->limit(10); // آخر 10 فواتير فقط
                }
            ]);
        }

        return $query->find($id);
    }

    /**
     * البحث في العملاء مع Lazy Loading
     */
    public function searchCustomersLazy($searchTerm)
    {
        return $this->model->select([
                'id', 'customer_id', 'name', 'email', 'contact'
            ])
            ->where(function($query) use ($searchTerm) {
                $query->where('name', 'like', "%{$searchTerm}%")
                      ->orWhere('email', 'like', "%{$searchTerm}%")
                      ->orWhere('customer_id', 'like', "%{$searchTerm}%");
            })
            ->lazy();
    }

    /**
     * الحصول على العملاء النشطين فقط
     */
    public function getActiveCustomersLazy(): LazyCollection
    {
        return $this->model->select([
                'id', 'customer_id', 'name', 'email', 'contact'
            ])
            ->where('is_active', 1)
            ->lazy();
    }

    /**
     * إحصائيات العملاء
     */
    public function getCustomerStats()
    {
        return [
            'total_customers' => $this->model->count(),
            'active_customers' => $this->model->where('is_active', 1)->count(),
            'customers_with_invoices' => $this->model->whereHas('invoices')->count(),
            'new_customers_this_month' => $this->model->whereMonth('created_at', now()->month)
                                                    ->whereYear('created_at', now()->year)
                                                    ->count(),
        ];
    }

    /**
     * الحصول على أفضل العملاء حسب المبيعات
     */
    public function getTopCustomersByRevenue($limit = 10)
    {
        return $this->model->select([
                'customers.id', 'customers.name', 'customers.email'
            ])
            ->selectRaw('SUM(invoices.total) as total_revenue')
            ->selectRaw('COUNT(invoices.id) as invoice_count')
            ->leftJoin('invoices', 'customers.id', '=', 'invoices.customer_id')
            ->where('invoices.status', 'paid')
            ->groupBy('customers.id', 'customers.name', 'customers.email')
            ->orderByDesc('total_revenue')
            ->limit($limit)
            ->get();
    }

    /**
     * تصدير العملاء مع Lazy Loading
     */
    public function exportCustomersLazy($includeInactive = false)
    {
        $query = $this->model->select([
            'customer_id', 'name', 'email', 'contact',
            'billing_country', 'billing_city', 'created_at'
        ]);

        if (!$includeInactive) {
            $query->where('is_active', 1);
        }

        return $query->lazy();
    }

    /**
     * معالجة العملاء غير النشطين بـ Chunking
     */
    public function processInactiveCustomers(callable $callback, $inactiveDays = 90)
    {
        $cutoffDate = now()->subDays($inactiveDays);
        
        return $this->model->where('last_login_at', '<', $cutoffDate)
            ->orWhereNull('last_login_at')
            ->chunk(100, $callback);
    }

    /**
     * تحديث آخر تسجيل دخول للعملاء بـ Chunking
     */
    public function updateLastLoginInChunks($customerIds, $loginTime)
    {
        return $this->model->whereIn('id', $customerIds)
            ->chunk(100, function($customers) use ($loginTime) {
                foreach ($customers as $customer) {
                    $customer->update(['last_login_at' => $loginTime]);
                }
            });
    }

    /**
     * الحصول على العملاء حسب المنطقة مع Lazy Loading
     */
    public function getCustomersByRegionLazy($country = null, $state = null)
    {
        $query = $this->model->select([
            'id', 'name', 'email', 'billing_country', 
            'billing_state', 'billing_city'
        ]);

        if ($country) {
            $query->where('billing_country', $country);
        }

        if ($state) {
            $query->where('billing_state', $state);
        }

        return $query->lazy();
    }
}
