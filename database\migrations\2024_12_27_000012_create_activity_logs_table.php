<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * إنشاء جدول سجل الأنشطة
 * 
 * هذا الجدول يحتفظ بسجل مفصل لجميع الأنشطة والتغييرات
 * التي تحدث في النظام لأغراض التدقيق والمتابعة
 * 
 * @package Database\Migrations
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
return new class extends Migration
{
    /**
     * تشغيل الـ Migration
     * 
     * @return void
     */
    public function up(): void
    {
        Schema::create('activity_logs', function (Blueprint $table) {
            // المعرف الأساسي
            $table->id()->comment('معرف النشاط الفريد');
            
            // معلومات المستخدم
            $table->foreignId('user_id')
                  ->nullable()
                  ->constrained('users')
                  ->onDelete('set null')
                  ->comment('معرف المستخدم الذي قام بالنشاط');
            
            // نوع النشاط
            $table->string('action', 100)->comment('نوع النشاط المنفذ');
            $table->string('entity_type', 100)->comment('نوع الكائن المتأثر');
            $table->unsignedBigInteger('entity_id')->nullable()->comment('معرف الكائن المتأثر');
            
            // وصف النشاط
            $table->text('description')->comment('وصف النشاط المنفذ');
            $table->text('description_ar')->nullable()->comment('وصف النشاط باللغة العربية');
            
            // البيانات قبل وبعد التغيير
            $table->json('old_values')->nullable()->comment('القيم القديمة قبل التغيير');
            $table->json('new_values')->nullable()->comment('القيم الجديدة بعد التغيير');
            
            // معلومات إضافية
            $table->json('properties')->nullable()->comment('خصائص إضافية للنشاط');
            
            // معلومات الجلسة والطلب
            $table->string('session_id', 100)->nullable()->comment('معرف الجلسة');
            $table->ipAddress('ip_address')->nullable()->comment('عنوان IP');
            $table->text('user_agent')->nullable()->comment('معلومات المتصفح');
            $table->string('request_method', 10)->nullable()->comment('طريقة الطلب HTTP');
            $table->text('request_url')->nullable()->comment('رابط الطلب');
            
            // تصنيف النشاط
            $table->enum('category', [
                'project',       // أنشطة المشاريع
                'task',          // أنشطة المهام
                'user',          // أنشطة المستخدمين
                'file',          // أنشطة الملفات
                'comment',       // أنشطة التعليقات
                'time',          // أنشطة تتبع الوقت
                'milestone',     // أنشطة المعالم
                'team',          // أنشطة الفريق
                'system',        // أنشطة النظام
                'security',      // أنشطة الأمان
                'other'          // أنشطة أخرى
            ])->default('other')->comment('تصنيف النشاط');
            
            // مستوى الأهمية
            $table->enum('level', [
                'info',          // معلوماتي
                'warning',       // تحذير
                'error',         // خطأ
                'critical'       // حرج
            ])->default('info')->comment('مستوى أهمية النشاط');
            
            // معلومات المشروع (للربط السريع)
            $table->foreignId('project_id')
                  ->nullable()
                  ->constrained('projects')
                  ->onDelete('set null')
                  ->comment('معرف المشروع المرتبط (للفلترة السريعة)');
            
            // طابع زمني
            $table->timestamp('created_at')->useCurrent()->comment('وقت حدوث النشاط');
            
            // الفهارس لتحسين الأداء
            $table->index(['user_id', 'created_at'], 'idx_activity_user_date');
            $table->index(['entity_type', 'entity_id'], 'idx_activity_entity');
            $table->index(['project_id', 'created_at'], 'idx_activity_project_date');
            $table->index(['category', 'level'], 'idx_activity_category_level');
            $table->index(['action', 'entity_type'], 'idx_activity_action_entity');
            $table->index(['created_at', 'level'], 'idx_activity_date_level');
            $table->index(['session_id'], 'idx_activity_session');
            $table->index(['ip_address'], 'idx_activity_ip');
            
            // فهرس نصي للبحث
            $table->fullText(['description', 'description_ar'], 'idx_activity_description_search');
        });
        
        DB::statement("ALTER TABLE activity_logs COMMENT = 'جدول سجل الأنشطة - يحتفظ بسجل مفصل لجميع الأنشطة في النظام'");
    }

    /**
     * التراجع عن الـ Migration
     * 
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_logs');
    }
};
