<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Messaging\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;
use Twilio\Rest\Messaging\V1\Service\AlphaSenderList;
use Twilio\Rest\Messaging\V1\Service\PhoneNumberList;
use Twilio\Rest\Messaging\V1\Service\DestinationAlphaSenderList;
use Twilio\Rest\Messaging\V1\Service\UsAppToPersonUsecaseList;
use Twilio\Rest\Messaging\V1\Service\ChannelSenderList;
use Twilio\Rest\Messaging\V1\Service\ShortCodeList;
use Twilio\Rest\Messaging\V1\Service\UsAppToPersonList;


/**
 * @property string|null $sid
 * @property string|null $accountSid
 * @property string|null $friendlyName
 * @property \DateTime|null $dateCreated
 * @property \DateTime|null $dateUpdated
 * @property string|null $inboundRequestUrl
 * @property string|null $inboundMethod
 * @property string|null $fallbackUrl
 * @property string|null $fallbackMethod
 * @property string|null $statusCallback
 * @property bool|null $stickySender
 * @property bool|null $mmsConverter
 * @property bool|null $smartEncoding
 * @property string $scanMessageContent
 * @property bool|null $fallbackToLongCode
 * @property bool|null $areaCodeGeomatch
 * @property bool|null $synchronousValidation
 * @property int $validityPeriod
 * @property string|null $url
 * @property array|null $links
 * @property string|null $usecase
 * @property bool|null $usAppToPersonRegistered
 * @property bool|null $useInboundWebhookOnNumber
 */
class ServiceInstance extends InstanceResource
{
    protected $_alphaSenders;
    protected $_phoneNumbers;
    protected $_destinationAlphaSenders;
    protected $_usAppToPersonUsecases;
    protected $_channelSenders;
    protected $_shortCodes;
    protected $_usAppToPerson;

    /**
     * Initialize the ServiceInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $sid The SID of the Service resource to delete.
     */
    public function __construct(Version $version, array $payload, ?string $sid = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'sid' => Values::array_get($payload, 'sid'),
            'accountSid' => Values::array_get($payload, 'account_sid'),
            'friendlyName' => Values::array_get($payload, 'friendly_name'),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'dateUpdated' => Deserialize::dateTime(Values::array_get($payload, 'date_updated')),
            'inboundRequestUrl' => Values::array_get($payload, 'inbound_request_url'),
            'inboundMethod' => Values::array_get($payload, 'inbound_method'),
            'fallbackUrl' => Values::array_get($payload, 'fallback_url'),
            'fallbackMethod' => Values::array_get($payload, 'fallback_method'),
            'statusCallback' => Values::array_get($payload, 'status_callback'),
            'stickySender' => Values::array_get($payload, 'sticky_sender'),
            'mmsConverter' => Values::array_get($payload, 'mms_converter'),
            'smartEncoding' => Values::array_get($payload, 'smart_encoding'),
            'scanMessageContent' => Values::array_get($payload, 'scan_message_content'),
            'fallbackToLongCode' => Values::array_get($payload, 'fallback_to_long_code'),
            'areaCodeGeomatch' => Values::array_get($payload, 'area_code_geomatch'),
            'synchronousValidation' => Values::array_get($payload, 'synchronous_validation'),
            'validityPeriod' => Values::array_get($payload, 'validity_period'),
            'url' => Values::array_get($payload, 'url'),
            'links' => Values::array_get($payload, 'links'),
            'usecase' => Values::array_get($payload, 'usecase'),
            'usAppToPersonRegistered' => Values::array_get($payload, 'us_app_to_person_registered'),
            'useInboundWebhookOnNumber' => Values::array_get($payload, 'use_inbound_webhook_on_number'),
        ];

        $this->solution = ['sid' => $sid ?: $this->properties['sid'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return ServiceContext Context for this ServiceInstance
     */
    protected function proxy(): ServiceContext
    {
        if (!$this->context) {
            $this->context = new ServiceContext(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->context;
    }

    /**
     * Delete the ServiceInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->proxy()->delete();
    }

    /**
     * Fetch the ServiceInstance
     *
     * @return ServiceInstance Fetched ServiceInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): ServiceInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Update the ServiceInstance
     *
     * @param array|Options $options Optional Arguments
     * @return ServiceInstance Updated ServiceInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(array $options = []): ServiceInstance
    {

        return $this->proxy()->update($options);
    }

    /**
     * Access the alphaSenders
     */
    protected function getAlphaSenders(): AlphaSenderList
    {
        return $this->proxy()->alphaSenders;
    }

    /**
     * Access the phoneNumbers
     */
    protected function getPhoneNumbers(): PhoneNumberList
    {
        return $this->proxy()->phoneNumbers;
    }

    /**
     * Access the destinationAlphaSenders
     */
    protected function getDestinationAlphaSenders(): DestinationAlphaSenderList
    {
        return $this->proxy()->destinationAlphaSenders;
    }

    /**
     * Access the usAppToPersonUsecases
     */
    protected function getUsAppToPersonUsecases(): UsAppToPersonUsecaseList
    {
        return $this->proxy()->usAppToPersonUsecases;
    }

    /**
     * Access the channelSenders
     */
    protected function getChannelSenders(): ChannelSenderList
    {
        return $this->proxy()->channelSenders;
    }

    /**
     * Access the shortCodes
     */
    protected function getShortCodes(): ShortCodeList
    {
        return $this->proxy()->shortCodes;
    }

    /**
     * Access the usAppToPerson
     */
    protected function getUsAppToPerson(): UsAppToPersonList
    {
        return $this->proxy()->usAppToPerson;
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Messaging.V1.ServiceInstance ' . \implode(' ', $context) . ']';
    }
}

