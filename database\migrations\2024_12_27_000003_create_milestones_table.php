<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * إنشاء جدول المعالم
 * 
 * المعالم هي نقاط مهمة في دورة حياة المشروع تمثل إنجازات رئيسية
 * 
 * @package Database\Migrations
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
return new class extends Migration
{
    /**
     * تشغيل الـ Migration
     * 
     * @return void
     */
    public function up(): void
    {
        Schema::create('milestones', function (Blueprint $table) {
            // المعرف الأساسي
            $table->id()->comment('معرف المعلم الفريد');
            
            // معلومات المعلم
            $table->string('name', 255)->comment('اسم المعلم');
            $table->text('description')->nullable()->comment('وصف المعلم والمخرجات المطلوبة');
            
            // العلاقة مع المشروع
            $table->foreignId('project_id')
                  ->constrained('projects')
                  ->onDelete('cascade')
                  ->comment('معرف المشروع');
            
            // التواريخ
            $table->date('due_date')->comment('تاريخ الاستحقاق المخطط');
            $table->timestamp('completed_at')->nullable()->comment('تاريخ ووقت الإنجاز الفعلي');
            
            // حالة المعلم
            $table->enum('status', [
                'pending',
                'in_progress', 
                'completed',
                'overdue'
            ])->default('pending')->comment('حالة المعلم');
            
            // ترتيب المعالم
            $table->integer('order_index')->default(0)->comment('ترتيب المعلم ضمن المشروع');
            
            // المخرجات والمتطلبات
            $table->json('deliverables')->nullable()->comment('قائمة المخرجات والمتطلبات');
            
            // معلومات الإنشاء
            $table->foreignId('created_by')
                  ->constrained('users')
                  ->onDelete('cascade')
                  ->comment('معرف منشئ المعلم');
            
            // طوابع زمنية
            $table->timestamps();
            $table->softDeletes();
            
            // الفهارس
            $table->index(['project_id', 'status'], 'idx_milestones_project_status');
            $table->index(['due_date', 'status'], 'idx_milestones_due_status');
            $table->index(['project_id', 'order_index'], 'idx_milestones_project_order');
            $table->index(['created_by', 'created_at'], 'idx_milestones_creator_date');
            
            // فهرس نصي للبحث
            $table->fullText(['name', 'description'], 'idx_milestones_search');
            
            // قيود
            $table->check('order_index >= 0', 'chk_milestones_order_positive');
        });
        
        DB::statement("ALTER TABLE milestones COMMENT = 'جدول المعالم - نقاط مهمة في دورة حياة المشروع'");
    }

    /**
     * التراجع عن الـ Migration
     * 
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('milestones');
    }
};
