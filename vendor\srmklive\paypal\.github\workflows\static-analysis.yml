name: Static Analysis
on: [push, pull_request]
jobs:
  paypal:
    name: PHP ${{ matrix.php-versions }}
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        php-versions: ['8.0', '8.1', '8.2', '8.3']
    steps:
    - name: Checkout
      uses: actions/checkout@v2
    - name: Setup PHP with Composer and extensions
      with:
        php-version: ${{ matrix.php-versions }}
      uses: shivammathur/setup-php@v2
    - name: Get Composer cache directory
      id: composercache
      run: echo "::set-output name=dir::$(composer config cache-files-dir)"
    - name: Cache Composer dependencies
      uses: actions/cache@v2
      with:
        php-version: ${{ matrix.php-versions }}
        path: ${{ steps.composercache.outputs.dir }}
        key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
        restore-keys: ${{ runner.os }}-composer-
    - name: Install Composer dependencies
      env:
        PHP_VERSION: ${{ matrix.php-versions }}
      run: composer install --no-progress --prefer-dist --optimize-autoloader $(if [ "$PHP_VERSION" == "8.0" || "$PHP_VERSION" == "8.1" ]; then echo "--ignore-platform-reqs"; fi;)    
    - name: Run type checking analysis
      env:
        PHP_VERSION: ${{ matrix.php-versions }}
      run: vendor/bin/phpstan
