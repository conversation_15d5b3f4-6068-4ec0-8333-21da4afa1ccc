<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * إنشاء جدول تبعيات المهام
 * 
 * هذا الجدول يحدد العلاقات والتبعيات بين المهام المختلفة
 * مما يساعد في تنظيم تسلسل العمل وإدارة المشروع
 * 
 * @package Database\Migrations
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
return new class extends Migration
{
    /**
     * تشغيل الـ Migration
     * 
     * @return void
     */
    public function up(): void
    {
        Schema::create('task_dependencies', function (Blueprint $table) {
            // المعرف الأساسي
            $table->id()->comment('معرف التبعية الفريد');
            
            // المهمة التي تعتمد على مهمة أخرى
            $table->foreignId('task_id')
                  ->constrained('tasks')
                  ->onDelete('cascade')
                  ->comment('معرف المهمة التي تعتمد على مهمة أخرى');
            
            // المهمة التي يجب إنجازها أولاً
            $table->foreignId('depends_on_task_id')
                  ->constrained('tasks')
                  ->onDelete('cascade')
                  ->comment('معرف المهمة التي يجب إنجازها أولاً');
            
            // نوع التبعية
            $table->enum('dependency_type', [
                'finish_to_start',    // انتهاء إلى بداية (الأكثر شيوعاً)
                'start_to_start',     // بداية إلى بداية
                'finish_to_finish',   // انتهاء إلى انتهاء
                'start_to_finish'     // بداية إلى انتهاء
            ])->default('finish_to_start')->comment('نوع التبعية بين المهام');
            
            // تأخير اختياري (بالأيام)
            $table->integer('lag_days')->default(0)->comment('عدد أيام التأخير بين المهام');
            
            // وصف التبعية
            $table->text('description')->nullable()->comment('وصف سبب التبعية');
            
            // معلومات الإنشاء
            $table->foreignId('created_by')
                  ->constrained('users')
                  ->onDelete('cascade')
                  ->comment('معرف المستخدم الذي أنشأ التبعية');
            
            // طوابع زمنية
            $table->timestamps();
            
            // فهرس فريد لمنع التبعيات المكررة
            $table->unique(['task_id', 'depends_on_task_id'], 'unique_task_dependency');
            
            // فهارس إضافية
            $table->index(['task_id', 'dependency_type'], 'idx_dependencies_task_type');
            $table->index(['depends_on_task_id', 'dependency_type'], 'idx_dependencies_depends_type');
            $table->index(['created_by', 'created_at'], 'idx_dependencies_creator_date');
            
            // قيد لمنع المهمة من الاعتماد على نفسها
            $table->check('task_id != depends_on_task_id', 'chk_dependencies_no_self_reference');
        });
        
        DB::statement("ALTER TABLE task_dependencies COMMENT = 'جدول تبعيات المهام - يحدد العلاقات والتسلسل بين المهام'");
    }

    /**
     * التراجع عن الـ Migration
     * 
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('task_dependencies');
    }
};
