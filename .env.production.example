# ملف البيئة المحسن للإنتاج - نظام إدارة المشاريع Hesabiai
# مُحسن للتعامل مع مليون تسجيل وأكثر

APP_NAME="Hesabiai Project Management"
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=https://hesabiai.com

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# =============================================================================
# إعدادات قاعدة البيانات المحسنة
# =============================================================================

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=said_hesabiai
DB_USERNAME=said_hesabiai
DB_PASSWORD=Colorado2020@

# إعدادات اتصال قاعدة البيانات المحسنة
DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_unicode_ci
DB_STRICT=true
DB_ENGINE=InnoDB

# تجمع الاتصالات (Connection Pooling)
DB_POOL_MIN=5
DB_POOL_MAX=50
DB_POOL_ACQUIRE_TIMEOUT=60000
DB_POOL_TIMEOUT=30000

# إعدادات Read Replicas (اختياري)
DB_READ_HOST=127.0.0.1
DB_READ_USERNAME=hesabiai_read_user
DB_READ_PASSWORD=

# =============================================================================
# إعدادات تحسين قاعدة البيانات
# =============================================================================

# تفعيل تحسينات قاعدة البيانات
DB_OPTIMIZATION_ENABLED=true
MYSQL_OPTIMIZATION_ENABLED=true
DB_MONITORING_ENABLED=true
DB_CACHING_ENABLED=true
DB_INDEXING_ENABLED=true
DB_PARTITIONING_ENABLED=true

# إعدادات الأرشفة التلقائية
DB_ARCHIVING_ENABLED=true
ARCHIVE_PROJECTS_MONTHS=12
ARCHIVE_TASKS_MONTHS=12
ARCHIVE_TIME_ENTRIES_MONTHS=24
ARCHIVE_COMMENTS_MONTHS=18
ARCHIVE_FILES_MONTHS=36
ARCHIVE_ACTIVITY_LOGS_MONTHS=6
ARCHIVE_BATCH_SIZE=1000
AUTO_ARCHIVE_ENABLED=true
BACKUP_BEFORE_ARCHIVE=true

# إعدادات التخزين المؤقت
CACHE_STATISTICS_TTL=30
CACHE_REPORTS_TTL=120
CACHE_SEARCH_TTL=60
CACHE_DASHBOARD_TTL=15
CACHE_MAX_SIZE_MB=500
CACHE_AUTO_CLEANUP=true
CACHE_COMPRESSION=true

# إعدادات الفهرسة والبحث
AUTO_INDEX_UPDATE=true
MIN_RELEVANCE_SCORE=0.5
DEFAULT_SEARCH_LIMIT=50
REAL_TIME_INDEXING=false
FULL_TEXT_SEARCH=true

# =============================================================================
# إعدادات مراقبة الأداء
# =============================================================================

# تفعيل المراقبة والتنبيهات
MONITORING_INTERVAL=5
MONITORING_ALERTS_ENABLED=true
MONITORING_ALERT_EMAIL=<EMAIL>
MONITORING_SLACK_WEBHOOK=
ALERT_CRITICAL_ONLY=false
MONITORING_LOGGING_ENABLED=true
MONITORING_LOG_RETENTION=30

# حدود التنبيهات
SLOW_QUERY_THRESHOLD=5
TABLE_SIZE_THRESHOLD=1000
CONNECTION_THRESHOLD=80
DISK_USAGE_THRESHOLD=80
CACHE_HIT_RATE_THRESHOLD=70

# =============================================================================
# إعدادات MySQL المحسنة
# =============================================================================

# إعدادات InnoDB
INNODB_BUFFER_POOL_SIZE=2G
INNODB_LOG_FILE_SIZE=512M
INNODB_FLUSH_LOG_AT_TRX_COMMIT=2
INNODB_FILE_PER_TABLE=1
INNODB_FLUSH_METHOD=O_DIRECT

# إعدادات Query Cache
QUERY_CACHE_SIZE=256M
QUERY_CACHE_TYPE=1
QUERY_CACHE_LIMIT=4M

# إعدادات الذاكرة المؤقتة
TMP_TABLE_SIZE=256M
MAX_HEAP_TABLE_SIZE=256M

# إعدادات الاتصالات
MAX_CONNECTIONS=300
MAX_CONNECT_ERRORS=100000
CONNECT_TIMEOUT=10
WAIT_TIMEOUT=28800

# =============================================================================
# إعدادات التقسيم (Partitioning)
# =============================================================================

AUTO_CREATE_PARTITIONS=true
AUTO_DROP_OLD_PARTITIONS=false
PARTITION_RETENTION_YEARS=5

# =============================================================================
# إعدادات النسخ الاحتياطي
# =============================================================================

DB_BACKUP_ENABLED=true
DAILY_BACKUP_ENABLED=true
WEEKLY_BACKUP_ENABLED=true
MONTHLY_BACKUP_ENABLED=true
BACKUP_PATH=/var/backups/hesabiai
BACKUP_COMPRESSION=true

# مدة الاحتفاظ بالنسخ الاحتياطي (بالأيام)
DAILY_BACKUP_RETENTION=7
WEEKLY_BACKUP_RETENTION=30
MONTHLY_BACKUP_RETENTION=365

# التخزين السحابي للنسخ الاحتياطي
BACKUP_CLOUD_ENABLED=true
BACKUP_CLOUD_DISK=s3
BACKUP_CLOUD_PATH=database-backups

# =============================================================================
# إعدادات التقارير والإحصائيات
# =============================================================================

DB_REPORTING_ENABLED=true
AUTO_UPDATE_STATISTICS=true
STATISTICS_UPDATE_INTERVAL=60
AUTO_GENERATE_REPORTS=true

# أنواع التقارير المفعلة
PERFORMANCE_REPORTS_ENABLED=true
USAGE_REPORTS_ENABLED=true
GROWTH_REPORTS_ENABLED=true
ERROR_REPORTS_ENABLED=true

# تصدير التقارير
AUTO_EMAIL_REPORTS=false
REPORT_EMAIL_RECIPIENTS=<EMAIL>

# =============================================================================
# إعدادات متقدمة للأداء
# =============================================================================

# Read Replicas
READ_REPLICAS_ENABLED=false
READ_REPLICA_CONNECTIONS=

# تحسين الاستعلامات
QUERY_OPTIMIZATION_ENABLED=true
EXPLAIN_SLOW_QUERIES=true
SUGGEST_INDEXES=true

# Connection Pooling
CONNECTION_POOLING_ENABLED=false
CONNECTION_POOL_SIZE=20

# تحسين الذاكرة
MEMORY_OPTIMIZATION_ENABLED=true
LAZY_LOADING_ENABLED=true
CHUNK_SIZE=1000

# =============================================================================
# إعدادات Redis للتخزين المؤقت
# =============================================================================

BROADCAST_DRIVER=redis
CACHE_DRIVER=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_DB=0

# إعدادات Redis محسنة للأداء
REDIS_CACHE_DB=1
REDIS_SESSION_DB=2
REDIS_QUEUE_DB=3
REDIS_TIMEOUT=5
REDIS_READ_TIMEOUT=10
REDIS_PERSISTENT=true

# =============================================================================
# إعدادات البريد الإلكتروني
# =============================================================================

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# بريد المدير للتنبيهات
MAIL_ADMIN_EMAIL=<EMAIL>

# =============================================================================
# إعدادات AWS (للتخزين السحابي)
# =============================================================================

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=hesabiai-backups
AWS_USE_PATH_STYLE_ENDPOINT=false

# =============================================================================
# إعدادات Pusher (للإشعارات الفورية)
# =============================================================================

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# =============================================================================
# إعدادات الأمان
# =============================================================================

# تشفير قاعدة البيانات
DB_ENCRYPTION_ENABLED=false
DB_ENCRYPTION_KEY=

# حماية من هجمات DDoS
RATE_LIMITING_ENABLED=true
RATE_LIMIT_PER_MINUTE=60

# تسجيل الأنشطة الأمنية
SECURITY_LOGGING_ENABLED=true
FAILED_LOGIN_ATTEMPTS_LOG=true

# =============================================================================
# إعدادات التطوير والتصحيح (للإنتاج: false)
# =============================================================================

# تعطيل في الإنتاج
TELESCOPE_ENABLED=false
DEBUGBAR_ENABLED=false
QUERY_LOGGING_ENABLED=false

# تفعيل في الإنتاج للمراقبة
HORIZON_ENABLED=true
PULSE_ENABLED=true

# =============================================================================
# إعدادات إضافية للأداء
# =============================================================================

# ضغط الاستجابات
RESPONSE_COMPRESSION=true

# تحسين الصور
IMAGE_OPTIMIZATION=true
IMAGE_QUALITY=85

# CDN للملفات الثابتة
CDN_ENABLED=false
CDN_URL=

# تخزين الجلسات في قاعدة البيانات (للتوزيع)
SESSION_STORE_DATABASE=true

# تفعيل OPcache
OPCACHE_ENABLED=true

# =============================================================================
# متغيرات خاصة بنظام إدارة المشاريع
# =============================================================================

# الحد الأقصى لحجم الملفات المرفوعة (بالميجابايت)
MAX_FILE_SIZE=50

# الحد الأقصى لعدد المشاريع لكل مستخدم
MAX_PROJECTS_PER_USER=100

# الحد الأقصى لعدد أعضاء الفريق لكل مشروع
MAX_TEAM_MEMBERS_PER_PROJECT=50

# تفعيل الإشعارات الفورية
REAL_TIME_NOTIFICATIONS=true

# تفعيل تتبع الوقت التلقائي
AUTO_TIME_TRACKING=false

# اللغة الافتراضية
DEFAULT_LOCALE=ar
FALLBACK_LOCALE=en

# المنطقة الزمنية
APP_TIMEZONE=Asia/Riyadh
