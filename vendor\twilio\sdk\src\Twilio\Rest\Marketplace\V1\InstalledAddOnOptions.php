<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Marketplace
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Marketplace\V1;

use Twilio\Options;
use Twilio\Values;

abstract class InstalledAddOnOptions
{
    /**
     * @param array $configuration The JSON object that represents the configuration of the new Add-on being installed.
     * @param string $uniqueName An application-defined string that uniquely identifies the resource. This value must be unique within the Account.
     * @return CreateInstalledAddOnOptions Options builder
     */
    public static function create(
        
        array $configuration = Values::ARRAY_NONE,
        string $uniqueName = Values::NONE

    ): CreateInstalledAddOnOptions
    {
        return new CreateInstalledAddOnOptions(
            $configuration,
            $uniqueName
        );
    }




    /**
     * @param array $configuration Valid JSON object that conform to the configuration schema exposed by the associated AvailableAddOn resource. This is only required by Add-ons that need to be configured
     * @param string $uniqueName An application-defined string that uniquely identifies the resource. This value must be unique within the Account.
     * @return UpdateInstalledAddOnOptions Options builder
     */
    public static function update(
        
        array $configuration = Values::ARRAY_NONE,
        string $uniqueName = Values::NONE

    ): UpdateInstalledAddOnOptions
    {
        return new UpdateInstalledAddOnOptions(
            $configuration,
            $uniqueName
        );
    }

}

class CreateInstalledAddOnOptions extends Options
    {
    /**
     * @param array $configuration The JSON object that represents the configuration of the new Add-on being installed.
     * @param string $uniqueName An application-defined string that uniquely identifies the resource. This value must be unique within the Account.
     */
    public function __construct(
        
        array $configuration = Values::ARRAY_NONE,
        string $uniqueName = Values::NONE

    ) {
        $this->options['configuration'] = $configuration;
        $this->options['uniqueName'] = $uniqueName;
    }

    /**
     * The JSON object that represents the configuration of the new Add-on being installed.
     *
     * @param array $configuration The JSON object that represents the configuration of the new Add-on being installed.
     * @return $this Fluent Builder
     */
    public function setConfiguration(array $configuration): self
    {
        $this->options['configuration'] = $configuration;
        return $this;
    }

    /**
     * An application-defined string that uniquely identifies the resource. This value must be unique within the Account.
     *
     * @param string $uniqueName An application-defined string that uniquely identifies the resource. This value must be unique within the Account.
     * @return $this Fluent Builder
     */
    public function setUniqueName(string $uniqueName): self
    {
        $this->options['uniqueName'] = $uniqueName;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Marketplace.V1.CreateInstalledAddOnOptions ' . $options . ']';
    }
}




class UpdateInstalledAddOnOptions extends Options
    {
    /**
     * @param array $configuration Valid JSON object that conform to the configuration schema exposed by the associated AvailableAddOn resource. This is only required by Add-ons that need to be configured
     * @param string $uniqueName An application-defined string that uniquely identifies the resource. This value must be unique within the Account.
     */
    public function __construct(
        
        array $configuration = Values::ARRAY_NONE,
        string $uniqueName = Values::NONE

    ) {
        $this->options['configuration'] = $configuration;
        $this->options['uniqueName'] = $uniqueName;
    }

    /**
     * Valid JSON object that conform to the configuration schema exposed by the associated AvailableAddOn resource. This is only required by Add-ons that need to be configured
     *
     * @param array $configuration Valid JSON object that conform to the configuration schema exposed by the associated AvailableAddOn resource. This is only required by Add-ons that need to be configured
     * @return $this Fluent Builder
     */
    public function setConfiguration(array $configuration): self
    {
        $this->options['configuration'] = $configuration;
        return $this;
    }

    /**
     * An application-defined string that uniquely identifies the resource. This value must be unique within the Account.
     *
     * @param string $uniqueName An application-defined string that uniquely identifies the resource. This value must be unique within the Account.
     * @return $this Fluent Builder
     */
    public function setUniqueName(string $uniqueName): self
    {
        $this->options['uniqueName'] = $uniqueName;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Marketplace.V1.UpdateInstalledAddOnOptions ' . $options . ']';
    }
}

