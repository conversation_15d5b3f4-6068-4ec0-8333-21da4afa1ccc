<?php
// This file was auto-generated from sdk-root/src/data/elasticloadbalancingv2/2015-12-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2015-12-01', 'endpointPrefix' => 'elasticloadbalancing', 'protocol' => 'query', 'protocols' => [ 'query', ], 'serviceAbbreviation' => 'Elastic Load Balancing v2', 'serviceFullName' => 'Elastic Load Balancing', 'serviceId' => 'Elastic Load Balancing v2', 'signatureVersion' => 'v4', 'uid' => 'elasticloadbalancingv2-2015-12-01', 'xmlNamespace' => 'http://elasticloadbalancing.amazonaws.com/doc/2015-12-01/', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AddListenerCertificates' => [ 'name' => 'AddListenerCertificates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddListenerCertificatesInput', ], 'output' => [ 'shape' => 'AddListenerCertificatesOutput', 'resultWrapper' => 'AddListenerCertificatesResult', ], 'errors' => [ [ 'shape' => 'ListenerNotFoundException', ], [ 'shape' => 'TooManyCertificatesException', ], [ 'shape' => 'CertificateNotFoundException', ], ], ], 'AddTags' => [ 'name' => 'AddTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddTagsInput', ], 'output' => [ 'shape' => 'AddTagsOutput', 'resultWrapper' => 'AddTagsResult', ], 'errors' => [ [ 'shape' => 'DuplicateTagKeysException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'LoadBalancerNotFoundException', ], [ 'shape' => 'TargetGroupNotFoundException', ], [ 'shape' => 'ListenerNotFoundException', ], [ 'shape' => 'RuleNotFoundException', ], [ 'shape' => 'TrustStoreNotFoundException', ], ], ], 'AddTrustStoreRevocations' => [ 'name' => 'AddTrustStoreRevocations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddTrustStoreRevocationsInput', ], 'output' => [ 'shape' => 'AddTrustStoreRevocationsOutput', 'resultWrapper' => 'AddTrustStoreRevocationsResult', ], 'errors' => [ [ 'shape' => 'TrustStoreNotFoundException', ], [ 'shape' => 'InvalidRevocationContentException', ], [ 'shape' => 'TooManyTrustStoreRevocationEntriesException', ], [ 'shape' => 'RevocationContentNotFoundException', ], ], ], 'CreateListener' => [ 'name' => 'CreateListener', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateListenerInput', ], 'output' => [ 'shape' => 'CreateListenerOutput', 'resultWrapper' => 'CreateListenerResult', ], 'errors' => [ [ 'shape' => 'DuplicateListenerException', ], [ 'shape' => 'TooManyListenersException', ], [ 'shape' => 'TooManyCertificatesException', ], [ 'shape' => 'LoadBalancerNotFoundException', ], [ 'shape' => 'TargetGroupNotFoundException', ], [ 'shape' => 'TargetGroupAssociationLimitException', ], [ 'shape' => 'InvalidConfigurationRequestException', ], [ 'shape' => 'IncompatibleProtocolsException', ], [ 'shape' => 'SSLPolicyNotFoundException', ], [ 'shape' => 'CertificateNotFoundException', ], [ 'shape' => 'UnsupportedProtocolException', ], [ 'shape' => 'TooManyRegistrationsForTargetIdException', ], [ 'shape' => 'TooManyTargetsException', ], [ 'shape' => 'TooManyActionsException', ], [ 'shape' => 'InvalidLoadBalancerActionException', ], [ 'shape' => 'TooManyUniqueTargetGroupsPerLoadBalancerException', ], [ 'shape' => 'ALPNPolicyNotSupportedException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'TrustStoreNotFoundException', ], [ 'shape' => 'TrustStoreNotReadyException', ], ], ], 'CreateLoadBalancer' => [ 'name' => 'CreateLoadBalancer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLoadBalancerInput', ], 'output' => [ 'shape' => 'CreateLoadBalancerOutput', 'resultWrapper' => 'CreateLoadBalancerResult', ], 'errors' => [ [ 'shape' => 'DuplicateLoadBalancerNameException', ], [ 'shape' => 'TooManyLoadBalancersException', ], [ 'shape' => 'InvalidConfigurationRequestException', ], [ 'shape' => 'SubnetNotFoundException', ], [ 'shape' => 'InvalidSubnetException', ], [ 'shape' => 'InvalidSecurityGroupException', ], [ 'shape' => 'InvalidSchemeException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'DuplicateTagKeysException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'AllocationIdNotFoundException', ], [ 'shape' => 'AvailabilityZoneNotSupportedException', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'CreateRule' => [ 'name' => 'CreateRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRuleInput', ], 'output' => [ 'shape' => 'CreateRuleOutput', 'resultWrapper' => 'CreateRuleResult', ], 'errors' => [ [ 'shape' => 'PriorityInUseException', ], [ 'shape' => 'TooManyTargetGroupsException', ], [ 'shape' => 'TooManyRulesException', ], [ 'shape' => 'TargetGroupAssociationLimitException', ], [ 'shape' => 'IncompatibleProtocolsException', ], [ 'shape' => 'ListenerNotFoundException', ], [ 'shape' => 'TargetGroupNotFoundException', ], [ 'shape' => 'InvalidConfigurationRequestException', ], [ 'shape' => 'TooManyRegistrationsForTargetIdException', ], [ 'shape' => 'TooManyTargetsException', ], [ 'shape' => 'UnsupportedProtocolException', ], [ 'shape' => 'TooManyActionsException', ], [ 'shape' => 'InvalidLoadBalancerActionException', ], [ 'shape' => 'TooManyUniqueTargetGroupsPerLoadBalancerException', ], [ 'shape' => 'TooManyTagsException', ], ], ], 'CreateTargetGroup' => [ 'name' => 'CreateTargetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTargetGroupInput', ], 'output' => [ 'shape' => 'CreateTargetGroupOutput', 'resultWrapper' => 'CreateTargetGroupResult', ], 'errors' => [ [ 'shape' => 'DuplicateTargetGroupNameException', ], [ 'shape' => 'TooManyTargetGroupsException', ], [ 'shape' => 'InvalidConfigurationRequestException', ], [ 'shape' => 'TooManyTagsException', ], ], ], 'CreateTrustStore' => [ 'name' => 'CreateTrustStore', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTrustStoreInput', ], 'output' => [ 'shape' => 'CreateTrustStoreOutput', 'resultWrapper' => 'CreateTrustStoreResult', ], 'errors' => [ [ 'shape' => 'DuplicateTrustStoreNameException', ], [ 'shape' => 'TooManyTrustStoresException', ], [ 'shape' => 'InvalidCaCertificatesBundleException', ], [ 'shape' => 'CaCertificatesBundleNotFoundException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'DuplicateTagKeysException', ], ], ], 'DeleteListener' => [ 'name' => 'DeleteListener', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteListenerInput', ], 'output' => [ 'shape' => 'DeleteListenerOutput', 'resultWrapper' => 'DeleteListenerResult', ], 'errors' => [ [ 'shape' => 'ListenerNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'DeleteLoadBalancer' => [ 'name' => 'DeleteLoadBalancer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteLoadBalancerInput', ], 'output' => [ 'shape' => 'DeleteLoadBalancerOutput', 'resultWrapper' => 'DeleteLoadBalancerResult', ], 'errors' => [ [ 'shape' => 'LoadBalancerNotFoundException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'DeleteRule' => [ 'name' => 'DeleteRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRuleInput', ], 'output' => [ 'shape' => 'DeleteRuleOutput', 'resultWrapper' => 'DeleteRuleResult', ], 'errors' => [ [ 'shape' => 'RuleNotFoundException', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'DeleteSharedTrustStoreAssociation' => [ 'name' => 'DeleteSharedTrustStoreAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSharedTrustStoreAssociationInput', ], 'output' => [ 'shape' => 'DeleteSharedTrustStoreAssociationOutput', 'resultWrapper' => 'DeleteSharedTrustStoreAssociationResult', ], 'errors' => [ [ 'shape' => 'TrustStoreNotFoundException', ], [ 'shape' => 'DeleteAssociationSameAccountException', ], [ 'shape' => 'TrustStoreAssociationNotFoundException', ], ], ], 'DeleteTargetGroup' => [ 'name' => 'DeleteTargetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTargetGroupInput', ], 'output' => [ 'shape' => 'DeleteTargetGroupOutput', 'resultWrapper' => 'DeleteTargetGroupResult', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], ], ], 'DeleteTrustStore' => [ 'name' => 'DeleteTrustStore', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTrustStoreInput', ], 'output' => [ 'shape' => 'DeleteTrustStoreOutput', 'resultWrapper' => 'DeleteTrustStoreResult', ], 'errors' => [ [ 'shape' => 'TrustStoreNotFoundException', ], [ 'shape' => 'TrustStoreInUseException', ], ], ], 'DeregisterTargets' => [ 'name' => 'DeregisterTargets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterTargetsInput', ], 'output' => [ 'shape' => 'DeregisterTargetsOutput', 'resultWrapper' => 'DeregisterTargetsResult', ], 'errors' => [ [ 'shape' => 'TargetGroupNotFoundException', ], [ 'shape' => 'InvalidTargetException', ], ], ], 'DescribeAccountLimits' => [ 'name' => 'DescribeAccountLimits', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAccountLimitsInput', ], 'output' => [ 'shape' => 'DescribeAccountLimitsOutput', 'resultWrapper' => 'DescribeAccountLimitsResult', ], ], 'DescribeCapacityReservation' => [ 'name' => 'DescribeCapacityReservation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCapacityReservationInput', ], 'output' => [ 'shape' => 'DescribeCapacityReservationOutput', 'resultWrapper' => 'DescribeCapacityReservationResult', ], 'errors' => [ [ 'shape' => 'LoadBalancerNotFoundException', ], ], ], 'DescribeListenerAttributes' => [ 'name' => 'DescribeListenerAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeListenerAttributesInput', ], 'output' => [ 'shape' => 'DescribeListenerAttributesOutput', 'resultWrapper' => 'DescribeListenerAttributesResult', ], 'errors' => [ [ 'shape' => 'ListenerNotFoundException', ], ], ], 'DescribeListenerCertificates' => [ 'name' => 'DescribeListenerCertificates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeListenerCertificatesInput', ], 'output' => [ 'shape' => 'DescribeListenerCertificatesOutput', 'resultWrapper' => 'DescribeListenerCertificatesResult', ], 'errors' => [ [ 'shape' => 'ListenerNotFoundException', ], ], ], 'DescribeListeners' => [ 'name' => 'DescribeListeners', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeListenersInput', ], 'output' => [ 'shape' => 'DescribeListenersOutput', 'resultWrapper' => 'DescribeListenersResult', ], 'errors' => [ [ 'shape' => 'ListenerNotFoundException', ], [ 'shape' => 'LoadBalancerNotFoundException', ], [ 'shape' => 'UnsupportedProtocolException', ], ], ], 'DescribeLoadBalancerAttributes' => [ 'name' => 'DescribeLoadBalancerAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLoadBalancerAttributesInput', ], 'output' => [ 'shape' => 'DescribeLoadBalancerAttributesOutput', 'resultWrapper' => 'DescribeLoadBalancerAttributesResult', ], 'errors' => [ [ 'shape' => 'LoadBalancerNotFoundException', ], ], ], 'DescribeLoadBalancers' => [ 'name' => 'DescribeLoadBalancers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLoadBalancersInput', ], 'output' => [ 'shape' => 'DescribeLoadBalancersOutput', 'resultWrapper' => 'DescribeLoadBalancersResult', ], 'errors' => [ [ 'shape' => 'LoadBalancerNotFoundException', ], ], ], 'DescribeRules' => [ 'name' => 'DescribeRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRulesInput', ], 'output' => [ 'shape' => 'DescribeRulesOutput', 'resultWrapper' => 'DescribeRulesResult', ], 'errors' => [ [ 'shape' => 'ListenerNotFoundException', ], [ 'shape' => 'RuleNotFoundException', ], [ 'shape' => 'UnsupportedProtocolException', ], ], ], 'DescribeSSLPolicies' => [ 'name' => 'DescribeSSLPolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSSLPoliciesInput', ], 'output' => [ 'shape' => 'DescribeSSLPoliciesOutput', 'resultWrapper' => 'DescribeSSLPoliciesResult', ], 'errors' => [ [ 'shape' => 'SSLPolicyNotFoundException', ], ], ], 'DescribeTags' => [ 'name' => 'DescribeTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTagsInput', ], 'output' => [ 'shape' => 'DescribeTagsOutput', 'resultWrapper' => 'DescribeTagsResult', ], 'errors' => [ [ 'shape' => 'LoadBalancerNotFoundException', ], [ 'shape' => 'TargetGroupNotFoundException', ], [ 'shape' => 'ListenerNotFoundException', ], [ 'shape' => 'RuleNotFoundException', ], [ 'shape' => 'TrustStoreNotFoundException', ], ], ], 'DescribeTargetGroupAttributes' => [ 'name' => 'DescribeTargetGroupAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTargetGroupAttributesInput', ], 'output' => [ 'shape' => 'DescribeTargetGroupAttributesOutput', 'resultWrapper' => 'DescribeTargetGroupAttributesResult', ], 'errors' => [ [ 'shape' => 'TargetGroupNotFoundException', ], ], ], 'DescribeTargetGroups' => [ 'name' => 'DescribeTargetGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTargetGroupsInput', ], 'output' => [ 'shape' => 'DescribeTargetGroupsOutput', 'resultWrapper' => 'DescribeTargetGroupsResult', ], 'errors' => [ [ 'shape' => 'LoadBalancerNotFoundException', ], [ 'shape' => 'TargetGroupNotFoundException', ], ], ], 'DescribeTargetHealth' => [ 'name' => 'DescribeTargetHealth', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTargetHealthInput', ], 'output' => [ 'shape' => 'DescribeTargetHealthOutput', 'resultWrapper' => 'DescribeTargetHealthResult', ], 'errors' => [ [ 'shape' => 'InvalidTargetException', ], [ 'shape' => 'TargetGroupNotFoundException', ], [ 'shape' => 'HealthUnavailableException', ], ], ], 'DescribeTrustStoreAssociations' => [ 'name' => 'DescribeTrustStoreAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTrustStoreAssociationsInput', ], 'output' => [ 'shape' => 'DescribeTrustStoreAssociationsOutput', 'resultWrapper' => 'DescribeTrustStoreAssociationsResult', ], 'errors' => [ [ 'shape' => 'TrustStoreNotFoundException', ], ], ], 'DescribeTrustStoreRevocations' => [ 'name' => 'DescribeTrustStoreRevocations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTrustStoreRevocationsInput', ], 'output' => [ 'shape' => 'DescribeTrustStoreRevocationsOutput', 'resultWrapper' => 'DescribeTrustStoreRevocationsResult', ], 'errors' => [ [ 'shape' => 'TrustStoreNotFoundException', ], [ 'shape' => 'RevocationIdNotFoundException', ], ], ], 'DescribeTrustStores' => [ 'name' => 'DescribeTrustStores', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTrustStoresInput', ], 'output' => [ 'shape' => 'DescribeTrustStoresOutput', 'resultWrapper' => 'DescribeTrustStoresResult', ], 'errors' => [ [ 'shape' => 'TrustStoreNotFoundException', ], ], ], 'GetResourcePolicy' => [ 'name' => 'GetResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResourcePolicyInput', ], 'output' => [ 'shape' => 'GetResourcePolicyOutput', 'resultWrapper' => 'GetResourcePolicyResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetTrustStoreCaCertificatesBundle' => [ 'name' => 'GetTrustStoreCaCertificatesBundle', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTrustStoreCaCertificatesBundleInput', ], 'output' => [ 'shape' => 'GetTrustStoreCaCertificatesBundleOutput', 'resultWrapper' => 'GetTrustStoreCaCertificatesBundleResult', ], 'errors' => [ [ 'shape' => 'TrustStoreNotFoundException', ], ], ], 'GetTrustStoreRevocationContent' => [ 'name' => 'GetTrustStoreRevocationContent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTrustStoreRevocationContentInput', ], 'output' => [ 'shape' => 'GetTrustStoreRevocationContentOutput', 'resultWrapper' => 'GetTrustStoreRevocationContentResult', ], 'errors' => [ [ 'shape' => 'TrustStoreNotFoundException', ], [ 'shape' => 'RevocationIdNotFoundException', ], ], ], 'ModifyCapacityReservation' => [ 'name' => 'ModifyCapacityReservation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyCapacityReservationInput', ], 'output' => [ 'shape' => 'ModifyCapacityReservationOutput', 'resultWrapper' => 'ModifyCapacityReservationResult', ], 'errors' => [ [ 'shape' => 'LoadBalancerNotFoundException', ], [ 'shape' => 'InvalidConfigurationRequestException', ], [ 'shape' => 'CapacityUnitsLimitExceededException', ], [ 'shape' => 'CapacityReservationPendingException', ], [ 'shape' => 'InsufficientCapacityException', ], [ 'shape' => 'CapacityDecreaseRequestsLimitExceededException', ], [ 'shape' => 'PriorRequestNotCompleteException', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'ModifyIpPools' => [ 'name' => 'ModifyIpPools', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyIpPoolsInput', ], 'output' => [ 'shape' => 'ModifyIpPoolsOutput', 'resultWrapper' => 'ModifyIpPoolsResult', ], 'errors' => [ [ 'shape' => 'LoadBalancerNotFoundException', ], ], ], 'ModifyListener' => [ 'name' => 'ModifyListener', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyListenerInput', ], 'output' => [ 'shape' => 'ModifyListenerOutput', 'resultWrapper' => 'ModifyListenerResult', ], 'errors' => [ [ 'shape' => 'DuplicateListenerException', ], [ 'shape' => 'TooManyListenersException', ], [ 'shape' => 'TooManyCertificatesException', ], [ 'shape' => 'ListenerNotFoundException', ], [ 'shape' => 'TargetGroupNotFoundException', ], [ 'shape' => 'TargetGroupAssociationLimitException', ], [ 'shape' => 'IncompatibleProtocolsException', ], [ 'shape' => 'SSLPolicyNotFoundException', ], [ 'shape' => 'CertificateNotFoundException', ], [ 'shape' => 'InvalidConfigurationRequestException', ], [ 'shape' => 'UnsupportedProtocolException', ], [ 'shape' => 'TooManyRegistrationsForTargetIdException', ], [ 'shape' => 'TooManyTargetsException', ], [ 'shape' => 'TooManyActionsException', ], [ 'shape' => 'InvalidLoadBalancerActionException', ], [ 'shape' => 'TooManyUniqueTargetGroupsPerLoadBalancerException', ], [ 'shape' => 'ALPNPolicyNotSupportedException', ], [ 'shape' => 'TrustStoreNotFoundException', ], [ 'shape' => 'TrustStoreNotReadyException', ], ], ], 'ModifyListenerAttributes' => [ 'name' => 'ModifyListenerAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyListenerAttributesInput', ], 'output' => [ 'shape' => 'ModifyListenerAttributesOutput', 'resultWrapper' => 'ModifyListenerAttributesResult', ], 'errors' => [ [ 'shape' => 'ListenerNotFoundException', ], [ 'shape' => 'InvalidConfigurationRequestException', ], ], ], 'ModifyLoadBalancerAttributes' => [ 'name' => 'ModifyLoadBalancerAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyLoadBalancerAttributesInput', ], 'output' => [ 'shape' => 'ModifyLoadBalancerAttributesOutput', 'resultWrapper' => 'ModifyLoadBalancerAttributesResult', ], 'errors' => [ [ 'shape' => 'LoadBalancerNotFoundException', ], [ 'shape' => 'InvalidConfigurationRequestException', ], ], ], 'ModifyRule' => [ 'name' => 'ModifyRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyRuleInput', ], 'output' => [ 'shape' => 'ModifyRuleOutput', 'resultWrapper' => 'ModifyRuleResult', ], 'errors' => [ [ 'shape' => 'TargetGroupAssociationLimitException', ], [ 'shape' => 'IncompatibleProtocolsException', ], [ 'shape' => 'RuleNotFoundException', ], [ 'shape' => 'OperationNotPermittedException', ], [ 'shape' => 'TooManyRegistrationsForTargetIdException', ], [ 'shape' => 'TooManyTargetsException', ], [ 'shape' => 'TargetGroupNotFoundException', ], [ 'shape' => 'UnsupportedProtocolException', ], [ 'shape' => 'TooManyActionsException', ], [ 'shape' => 'InvalidLoadBalancerActionException', ], [ 'shape' => 'TooManyUniqueTargetGroupsPerLoadBalancerException', ], ], ], 'ModifyTargetGroup' => [ 'name' => 'ModifyTargetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyTargetGroupInput', ], 'output' => [ 'shape' => 'ModifyTargetGroupOutput', 'resultWrapper' => 'ModifyTargetGroupResult', ], 'errors' => [ [ 'shape' => 'TargetGroupNotFoundException', ], [ 'shape' => 'InvalidConfigurationRequestException', ], ], ], 'ModifyTargetGroupAttributes' => [ 'name' => 'ModifyTargetGroupAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyTargetGroupAttributesInput', ], 'output' => [ 'shape' => 'ModifyTargetGroupAttributesOutput', 'resultWrapper' => 'ModifyTargetGroupAttributesResult', ], 'errors' => [ [ 'shape' => 'TargetGroupNotFoundException', ], [ 'shape' => 'InvalidConfigurationRequestException', ], ], ], 'ModifyTrustStore' => [ 'name' => 'ModifyTrustStore', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyTrustStoreInput', ], 'output' => [ 'shape' => 'ModifyTrustStoreOutput', 'resultWrapper' => 'ModifyTrustStoreResult', ], 'errors' => [ [ 'shape' => 'TrustStoreNotFoundException', ], [ 'shape' => 'InvalidCaCertificatesBundleException', ], [ 'shape' => 'CaCertificatesBundleNotFoundException', ], ], ], 'RegisterTargets' => [ 'name' => 'RegisterTargets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterTargetsInput', ], 'output' => [ 'shape' => 'RegisterTargetsOutput', 'resultWrapper' => 'RegisterTargetsResult', ], 'errors' => [ [ 'shape' => 'TargetGroupNotFoundException', ], [ 'shape' => 'TooManyTargetsException', ], [ 'shape' => 'InvalidTargetException', ], [ 'shape' => 'TooManyRegistrationsForTargetIdException', ], ], ], 'RemoveListenerCertificates' => [ 'name' => 'RemoveListenerCertificates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveListenerCertificatesInput', ], 'output' => [ 'shape' => 'RemoveListenerCertificatesOutput', 'resultWrapper' => 'RemoveListenerCertificatesResult', ], 'errors' => [ [ 'shape' => 'ListenerNotFoundException', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'RemoveTags' => [ 'name' => 'RemoveTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveTagsInput', ], 'output' => [ 'shape' => 'RemoveTagsOutput', 'resultWrapper' => 'RemoveTagsResult', ], 'errors' => [ [ 'shape' => 'LoadBalancerNotFoundException', ], [ 'shape' => 'TargetGroupNotFoundException', ], [ 'shape' => 'ListenerNotFoundException', ], [ 'shape' => 'RuleNotFoundException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'TrustStoreNotFoundException', ], ], ], 'RemoveTrustStoreRevocations' => [ 'name' => 'RemoveTrustStoreRevocations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveTrustStoreRevocationsInput', ], 'output' => [ 'shape' => 'RemoveTrustStoreRevocationsOutput', 'resultWrapper' => 'RemoveTrustStoreRevocationsResult', ], 'errors' => [ [ 'shape' => 'TrustStoreNotFoundException', ], [ 'shape' => 'RevocationIdNotFoundException', ], ], ], 'SetIpAddressType' => [ 'name' => 'SetIpAddressType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetIpAddressTypeInput', ], 'output' => [ 'shape' => 'SetIpAddressTypeOutput', 'resultWrapper' => 'SetIpAddressTypeResult', ], 'errors' => [ [ 'shape' => 'LoadBalancerNotFoundException', ], [ 'shape' => 'InvalidConfigurationRequestException', ], [ 'shape' => 'InvalidSubnetException', ], ], ], 'SetRulePriorities' => [ 'name' => 'SetRulePriorities', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetRulePrioritiesInput', ], 'output' => [ 'shape' => 'SetRulePrioritiesOutput', 'resultWrapper' => 'SetRulePrioritiesResult', ], 'errors' => [ [ 'shape' => 'RuleNotFoundException', ], [ 'shape' => 'PriorityInUseException', ], [ 'shape' => 'OperationNotPermittedException', ], ], ], 'SetSecurityGroups' => [ 'name' => 'SetSecurityGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetSecurityGroupsInput', ], 'output' => [ 'shape' => 'SetSecurityGroupsOutput', 'resultWrapper' => 'SetSecurityGroupsResult', ], 'errors' => [ [ 'shape' => 'LoadBalancerNotFoundException', ], [ 'shape' => 'InvalidConfigurationRequestException', ], [ 'shape' => 'InvalidSecurityGroupException', ], ], ], 'SetSubnets' => [ 'name' => 'SetSubnets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetSubnetsInput', ], 'output' => [ 'shape' => 'SetSubnetsOutput', 'resultWrapper' => 'SetSubnetsResult', ], 'errors' => [ [ 'shape' => 'LoadBalancerNotFoundException', ], [ 'shape' => 'InvalidConfigurationRequestException', ], [ 'shape' => 'SubnetNotFoundException', ], [ 'shape' => 'InvalidSubnetException', ], [ 'shape' => 'AllocationIdNotFoundException', ], [ 'shape' => 'AvailabilityZoneNotSupportedException', ], [ 'shape' => 'CapacityReservationPendingException', ], ], ], ], 'shapes' => [ 'ALPNPolicyNotSupportedException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ALPNPolicyNotFound', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Action' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Type' => [ 'shape' => 'ActionTypeEnum', ], 'TargetGroupArn' => [ 'shape' => 'TargetGroupArn', ], 'AuthenticateOidcConfig' => [ 'shape' => 'AuthenticateOidcActionConfig', ], 'AuthenticateCognitoConfig' => [ 'shape' => 'AuthenticateCognitoActionConfig', ], 'Order' => [ 'shape' => 'ActionOrder', ], 'RedirectConfig' => [ 'shape' => 'RedirectActionConfig', ], 'FixedResponseConfig' => [ 'shape' => 'FixedResponseActionConfig', ], 'ForwardConfig' => [ 'shape' => 'ForwardActionConfig', ], ], ], 'ActionOrder' => [ 'type' => 'integer', 'max' => 50000, 'min' => 1, ], 'ActionTypeEnum' => [ 'type' => 'string', 'enum' => [ 'forward', 'authenticate-oidc', 'authenticate-cognito', 'redirect', 'fixed-response', ], ], 'Actions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Action', ], ], 'AddListenerCertificatesInput' => [ 'type' => 'structure', 'required' => [ 'ListenerArn', 'Certificates', ], 'members' => [ 'ListenerArn' => [ 'shape' => 'ListenerArn', ], 'Certificates' => [ 'shape' => 'CertificateList', ], ], ], 'AddListenerCertificatesOutput' => [ 'type' => 'structure', 'members' => [ 'Certificates' => [ 'shape' => 'CertificateList', ], ], ], 'AddTagsInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArns', 'Tags', ], 'members' => [ 'ResourceArns' => [ 'shape' => 'ResourceArns', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'AddTagsOutput' => [ 'type' => 'structure', 'members' => [], ], 'AddTrustStoreRevocationsInput' => [ 'type' => 'structure', 'required' => [ 'TrustStoreArn', ], 'members' => [ 'TrustStoreArn' => [ 'shape' => 'TrustStoreArn', ], 'RevocationContents' => [ 'shape' => 'RevocationContents', ], ], ], 'AddTrustStoreRevocationsOutput' => [ 'type' => 'structure', 'members' => [ 'TrustStoreRevocations' => [ 'shape' => 'TrustStoreRevocations', ], ], ], 'AdministrativeOverride' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'TargetAdministrativeOverrideStateEnum', ], 'Reason' => [ 'shape' => 'TargetAdministrativeOverrideReasonEnum', ], 'Description' => [ 'shape' => 'Description', ], ], ], 'AdvertiseTrustStoreCaNamesEnum' => [ 'type' => 'string', 'enum' => [ 'on', 'off', ], ], 'AllocationId' => [ 'type' => 'string', ], 'AllocationIdNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'AllocationIdNotFound', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'AlpnPolicyName' => [ 'type' => 'list', 'member' => [ 'shape' => 'AlpnPolicyValue', ], ], 'AlpnPolicyValue' => [ 'type' => 'string', ], 'AnomalyDetection' => [ 'type' => 'structure', 'members' => [ 'Result' => [ 'shape' => 'AnomalyResultEnum', ], 'MitigationInEffect' => [ 'shape' => 'MitigationInEffectEnum', ], ], ], 'AnomalyResultEnum' => [ 'type' => 'string', 'enum' => [ 'anomalous', 'normal', ], ], 'AuthenticateCognitoActionAuthenticationRequestExtraParams' => [ 'type' => 'map', 'key' => [ 'shape' => 'AuthenticateCognitoActionAuthenticationRequestParamName', ], 'value' => [ 'shape' => 'AuthenticateCognitoActionAuthenticationRequestParamValue', ], ], 'AuthenticateCognitoActionAuthenticationRequestParamName' => [ 'type' => 'string', ], 'AuthenticateCognitoActionAuthenticationRequestParamValue' => [ 'type' => 'string', ], 'AuthenticateCognitoActionConditionalBehaviorEnum' => [ 'type' => 'string', 'enum' => [ 'deny', 'allow', 'authenticate', ], ], 'AuthenticateCognitoActionConfig' => [ 'type' => 'structure', 'required' => [ 'UserPoolArn', 'UserPoolClientId', 'UserPoolDomain', ], 'members' => [ 'UserPoolArn' => [ 'shape' => 'AuthenticateCognitoActionUserPoolArn', ], 'UserPoolClientId' => [ 'shape' => 'AuthenticateCognitoActionUserPoolClientId', ], 'UserPoolDomain' => [ 'shape' => 'AuthenticateCognitoActionUserPoolDomain', ], 'SessionCookieName' => [ 'shape' => 'AuthenticateCognitoActionSessionCookieName', ], 'Scope' => [ 'shape' => 'AuthenticateCognitoActionScope', ], 'SessionTimeout' => [ 'shape' => 'AuthenticateCognitoActionSessionTimeout', ], 'AuthenticationRequestExtraParams' => [ 'shape' => 'AuthenticateCognitoActionAuthenticationRequestExtraParams', ], 'OnUnauthenticatedRequest' => [ 'shape' => 'AuthenticateCognitoActionConditionalBehaviorEnum', ], ], ], 'AuthenticateCognitoActionScope' => [ 'type' => 'string', ], 'AuthenticateCognitoActionSessionCookieName' => [ 'type' => 'string', ], 'AuthenticateCognitoActionSessionTimeout' => [ 'type' => 'long', ], 'AuthenticateCognitoActionUserPoolArn' => [ 'type' => 'string', ], 'AuthenticateCognitoActionUserPoolClientId' => [ 'type' => 'string', ], 'AuthenticateCognitoActionUserPoolDomain' => [ 'type' => 'string', ], 'AuthenticateOidcActionAuthenticationRequestExtraParams' => [ 'type' => 'map', 'key' => [ 'shape' => 'AuthenticateOidcActionAuthenticationRequestParamName', ], 'value' => [ 'shape' => 'AuthenticateOidcActionAuthenticationRequestParamValue', ], ], 'AuthenticateOidcActionAuthenticationRequestParamName' => [ 'type' => 'string', ], 'AuthenticateOidcActionAuthenticationRequestParamValue' => [ 'type' => 'string', ], 'AuthenticateOidcActionAuthorizationEndpoint' => [ 'type' => 'string', ], 'AuthenticateOidcActionClientId' => [ 'type' => 'string', ], 'AuthenticateOidcActionClientSecret' => [ 'type' => 'string', ], 'AuthenticateOidcActionConditionalBehaviorEnum' => [ 'type' => 'string', 'enum' => [ 'deny', 'allow', 'authenticate', ], ], 'AuthenticateOidcActionConfig' => [ 'type' => 'structure', 'required' => [ 'Issuer', 'AuthorizationEndpoint', 'TokenEndpoint', 'UserInfoEndpoint', 'ClientId', ], 'members' => [ 'Issuer' => [ 'shape' => 'AuthenticateOidcActionIssuer', ], 'AuthorizationEndpoint' => [ 'shape' => 'AuthenticateOidcActionAuthorizationEndpoint', ], 'TokenEndpoint' => [ 'shape' => 'AuthenticateOidcActionTokenEndpoint', ], 'UserInfoEndpoint' => [ 'shape' => 'AuthenticateOidcActionUserInfoEndpoint', ], 'ClientId' => [ 'shape' => 'AuthenticateOidcActionClientId', ], 'ClientSecret' => [ 'shape' => 'AuthenticateOidcActionClientSecret', ], 'SessionCookieName' => [ 'shape' => 'AuthenticateOidcActionSessionCookieName', ], 'Scope' => [ 'shape' => 'AuthenticateOidcActionScope', ], 'SessionTimeout' => [ 'shape' => 'AuthenticateOidcActionSessionTimeout', ], 'AuthenticationRequestExtraParams' => [ 'shape' => 'AuthenticateOidcActionAuthenticationRequestExtraParams', ], 'OnUnauthenticatedRequest' => [ 'shape' => 'AuthenticateOidcActionConditionalBehaviorEnum', ], 'UseExistingClientSecret' => [ 'shape' => 'AuthenticateOidcActionUseExistingClientSecret', ], ], ], 'AuthenticateOidcActionIssuer' => [ 'type' => 'string', ], 'AuthenticateOidcActionScope' => [ 'type' => 'string', ], 'AuthenticateOidcActionSessionCookieName' => [ 'type' => 'string', ], 'AuthenticateOidcActionSessionTimeout' => [ 'type' => 'long', ], 'AuthenticateOidcActionTokenEndpoint' => [ 'type' => 'string', ], 'AuthenticateOidcActionUseExistingClientSecret' => [ 'type' => 'boolean', ], 'AuthenticateOidcActionUserInfoEndpoint' => [ 'type' => 'string', ], 'AvailabilityZone' => [ 'type' => 'structure', 'members' => [ 'ZoneName' => [ 'shape' => 'ZoneName', ], 'SubnetId' => [ 'shape' => 'SubnetId', ], 'OutpostId' => [ 'shape' => 'OutpostId', ], 'LoadBalancerAddresses' => [ 'shape' => 'LoadBalancerAddresses', ], 'SourceNatIpv6Prefixes' => [ 'shape' => 'SourceNatIpv6Prefixes', ], ], ], 'AvailabilityZoneNotSupportedException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'AvailabilityZoneNotSupported', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'AvailabilityZones' => [ 'type' => 'list', 'member' => [ 'shape' => 'AvailabilityZone', ], ], 'CaCertificatesBundleNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CaCertificatesBundleNotFound', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'CanonicalHostedZoneId' => [ 'type' => 'string', ], 'CapacityDecreaseRequestsLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CapacityDecreaseRequestLimitExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'CapacityReservationPendingException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CapacityReservationPending', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'CapacityReservationStateEnum' => [ 'type' => 'string', 'enum' => [ 'provisioned', 'pending', 'rebalancing', 'failed', ], ], 'CapacityReservationStatus' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'CapacityReservationStateEnum', ], 'Reason' => [ 'shape' => 'StateReason', ], ], ], 'CapacityUnits' => [ 'type' => 'integer', ], 'CapacityUnitsDouble' => [ 'type' => 'double', ], 'CapacityUnitsLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CapacityUnitsLimitExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Certificate' => [ 'type' => 'structure', 'members' => [ 'CertificateArn' => [ 'shape' => 'CertificateArn', ], 'IsDefault' => [ 'shape' => 'Default', ], ], ], 'CertificateArn' => [ 'type' => 'string', ], 'CertificateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Certificate', ], ], 'CertificateNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CertificateNotFound', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Cipher' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'CipherName', ], 'Priority' => [ 'shape' => 'CipherPriority', ], ], ], 'CipherName' => [ 'type' => 'string', ], 'CipherPriority' => [ 'type' => 'integer', ], 'Ciphers' => [ 'type' => 'list', 'member' => [ 'shape' => 'Cipher', ], ], 'ConditionFieldName' => [ 'type' => 'string', 'max' => 64, ], 'CreateListenerInput' => [ 'type' => 'structure', 'required' => [ 'LoadBalancerArn', 'DefaultActions', ], 'members' => [ 'LoadBalancerArn' => [ 'shape' => 'LoadBalancerArn', ], 'Protocol' => [ 'shape' => 'ProtocolEnum', ], 'Port' => [ 'shape' => 'Port', ], 'SslPolicy' => [ 'shape' => 'SslPolicyName', ], 'Certificates' => [ 'shape' => 'CertificateList', ], 'DefaultActions' => [ 'shape' => 'Actions', ], 'AlpnPolicy' => [ 'shape' => 'AlpnPolicyName', ], 'Tags' => [ 'shape' => 'TagList', ], 'MutualAuthentication' => [ 'shape' => 'MutualAuthenticationAttributes', ], ], ], 'CreateListenerOutput' => [ 'type' => 'structure', 'members' => [ 'Listeners' => [ 'shape' => 'Listeners', ], ], ], 'CreateLoadBalancerInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'LoadBalancerName', ], 'Subnets' => [ 'shape' => 'Subnets', ], 'SubnetMappings' => [ 'shape' => 'SubnetMappings', ], 'SecurityGroups' => [ 'shape' => 'SecurityGroups', ], 'Scheme' => [ 'shape' => 'LoadBalancerSchemeEnum', ], 'Tags' => [ 'shape' => 'TagList', ], 'Type' => [ 'shape' => 'LoadBalancerTypeEnum', ], 'IpAddressType' => [ 'shape' => 'IpAddressType', ], 'CustomerOwnedIpv4Pool' => [ 'shape' => 'CustomerOwnedIpv4Pool', ], 'EnablePrefixForIpv6SourceNat' => [ 'shape' => 'EnablePrefixForIpv6SourceNatEnum', ], 'IpamPools' => [ 'shape' => 'IpamPools', ], ], ], 'CreateLoadBalancerOutput' => [ 'type' => 'structure', 'members' => [ 'LoadBalancers' => [ 'shape' => 'LoadBalancers', ], ], ], 'CreateRuleInput' => [ 'type' => 'structure', 'required' => [ 'ListenerArn', 'Conditions', 'Priority', 'Actions', ], 'members' => [ 'ListenerArn' => [ 'shape' => 'ListenerArn', ], 'Conditions' => [ 'shape' => 'RuleConditionList', ], 'Priority' => [ 'shape' => 'RulePriority', ], 'Actions' => [ 'shape' => 'Actions', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateRuleOutput' => [ 'type' => 'structure', 'members' => [ 'Rules' => [ 'shape' => 'Rules', ], ], ], 'CreateTargetGroupInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'TargetGroupName', ], 'Protocol' => [ 'shape' => 'ProtocolEnum', ], 'ProtocolVersion' => [ 'shape' => 'ProtocolVersion', ], 'Port' => [ 'shape' => 'Port', ], 'VpcId' => [ 'shape' => 'VpcId', ], 'HealthCheckProtocol' => [ 'shape' => 'ProtocolEnum', ], 'HealthCheckPort' => [ 'shape' => 'HealthCheckPort', ], 'HealthCheckEnabled' => [ 'shape' => 'HealthCheckEnabled', ], 'HealthCheckPath' => [ 'shape' => 'Path', ], 'HealthCheckIntervalSeconds' => [ 'shape' => 'HealthCheckIntervalSeconds', ], 'HealthCheckTimeoutSeconds' => [ 'shape' => 'HealthCheckTimeoutSeconds', ], 'HealthyThresholdCount' => [ 'shape' => 'HealthCheckThresholdCount', ], 'UnhealthyThresholdCount' => [ 'shape' => 'HealthCheckThresholdCount', ], 'Matcher' => [ 'shape' => 'Matcher', ], 'TargetType' => [ 'shape' => 'TargetTypeEnum', ], 'Tags' => [ 'shape' => 'TagList', ], 'IpAddressType' => [ 'shape' => 'TargetGroupIpAddressTypeEnum', ], ], ], 'CreateTargetGroupOutput' => [ 'type' => 'structure', 'members' => [ 'TargetGroups' => [ 'shape' => 'TargetGroups', ], ], ], 'CreateTrustStoreInput' => [ 'type' => 'structure', 'required' => [ 'Name', 'CaCertificatesBundleS3Bucket', 'CaCertificatesBundleS3Key', ], 'members' => [ 'Name' => [ 'shape' => 'TrustStoreName', ], 'CaCertificatesBundleS3Bucket' => [ 'shape' => 'S3Bucket', ], 'CaCertificatesBundleS3Key' => [ 'shape' => 'S3Key', ], 'CaCertificatesBundleS3ObjectVersion' => [ 'shape' => 'S3ObjectVersion', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateTrustStoreOutput' => [ 'type' => 'structure', 'members' => [ 'TrustStores' => [ 'shape' => 'TrustStores', ], ], ], 'CreatedTime' => [ 'type' => 'timestamp', ], 'CustomerOwnedIpv4Pool' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^(ipv4pool-coip-)[a-zA-Z0-9]+$', ], 'DNSName' => [ 'type' => 'string', ], 'DecreaseRequestsRemaining' => [ 'type' => 'integer', ], 'Default' => [ 'type' => 'boolean', ], 'DeleteAssociationSameAccountException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DeleteAssociationSameAccount', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DeleteListenerInput' => [ 'type' => 'structure', 'required' => [ 'ListenerArn', ], 'members' => [ 'ListenerArn' => [ 'shape' => 'ListenerArn', ], ], ], 'DeleteListenerOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteLoadBalancerInput' => [ 'type' => 'structure', 'required' => [ 'LoadBalancerArn', ], 'members' => [ 'LoadBalancerArn' => [ 'shape' => 'LoadBalancerArn', ], ], ], 'DeleteLoadBalancerOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRuleInput' => [ 'type' => 'structure', 'required' => [ 'RuleArn', ], 'members' => [ 'RuleArn' => [ 'shape' => 'RuleArn', ], ], ], 'DeleteRuleOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSharedTrustStoreAssociationInput' => [ 'type' => 'structure', 'required' => [ 'TrustStoreArn', 'ResourceArn', ], 'members' => [ 'TrustStoreArn' => [ 'shape' => 'TrustStoreArn', ], 'ResourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'DeleteSharedTrustStoreAssociationOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTargetGroupInput' => [ 'type' => 'structure', 'required' => [ 'TargetGroupArn', ], 'members' => [ 'TargetGroupArn' => [ 'shape' => 'TargetGroupArn', ], ], ], 'DeleteTargetGroupOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTrustStoreInput' => [ 'type' => 'structure', 'required' => [ 'TrustStoreArn', ], 'members' => [ 'TrustStoreArn' => [ 'shape' => 'TrustStoreArn', ], ], ], 'DeleteTrustStoreOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeregisterTargetsInput' => [ 'type' => 'structure', 'required' => [ 'TargetGroupArn', 'Targets', ], 'members' => [ 'TargetGroupArn' => [ 'shape' => 'TargetGroupArn', ], 'Targets' => [ 'shape' => 'TargetDescriptions', ], ], ], 'DeregisterTargetsOutput' => [ 'type' => 'structure', 'members' => [], ], 'DescribeAccountLimitsInput' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'Marker', ], 'PageSize' => [ 'shape' => 'PageSize', ], ], ], 'DescribeAccountLimitsOutput' => [ 'type' => 'structure', 'members' => [ 'Limits' => [ 'shape' => 'Limits', ], 'NextMarker' => [ 'shape' => 'Marker', ], ], ], 'DescribeCapacityReservationInput' => [ 'type' => 'structure', 'required' => [ 'LoadBalancerArn', ], 'members' => [ 'LoadBalancerArn' => [ 'shape' => 'LoadBalancerArn', ], ], ], 'DescribeCapacityReservationOutput' => [ 'type' => 'structure', 'members' => [ 'LastModifiedTime' => [ 'shape' => 'LastModifiedTime', ], 'DecreaseRequestsRemaining' => [ 'shape' => 'DecreaseRequestsRemaining', ], 'MinimumLoadBalancerCapacity' => [ 'shape' => 'MinimumLoadBalancerCapacity', ], 'CapacityReservationState' => [ 'shape' => 'ZonalCapacityReservationStates', ], ], ], 'DescribeListenerAttributesInput' => [ 'type' => 'structure', 'required' => [ 'ListenerArn', ], 'members' => [ 'ListenerArn' => [ 'shape' => 'ListenerArn', ], ], ], 'DescribeListenerAttributesOutput' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'ListenerAttributes', ], ], ], 'DescribeListenerCertificatesInput' => [ 'type' => 'structure', 'required' => [ 'ListenerArn', ], 'members' => [ 'ListenerArn' => [ 'shape' => 'ListenerArn', ], 'Marker' => [ 'shape' => 'Marker', ], 'PageSize' => [ 'shape' => 'PageSize', ], ], ], 'DescribeListenerCertificatesOutput' => [ 'type' => 'structure', 'members' => [ 'Certificates' => [ 'shape' => 'CertificateList', ], 'NextMarker' => [ 'shape' => 'Marker', ], ], ], 'DescribeListenersInput' => [ 'type' => 'structure', 'members' => [ 'LoadBalancerArn' => [ 'shape' => 'LoadBalancerArn', ], 'ListenerArns' => [ 'shape' => 'ListenerArns', ], 'Marker' => [ 'shape' => 'Marker', ], 'PageSize' => [ 'shape' => 'PageSize', ], ], ], 'DescribeListenersOutput' => [ 'type' => 'structure', 'members' => [ 'Listeners' => [ 'shape' => 'Listeners', ], 'NextMarker' => [ 'shape' => 'Marker', ], ], ], 'DescribeLoadBalancerAttributesInput' => [ 'type' => 'structure', 'required' => [ 'LoadBalancerArn', ], 'members' => [ 'LoadBalancerArn' => [ 'shape' => 'LoadBalancerArn', ], ], ], 'DescribeLoadBalancerAttributesOutput' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'LoadBalancerAttributes', ], ], ], 'DescribeLoadBalancersInput' => [ 'type' => 'structure', 'members' => [ 'LoadBalancerArns' => [ 'shape' => 'LoadBalancerArns', ], 'Names' => [ 'shape' => 'LoadBalancerNames', ], 'Marker' => [ 'shape' => 'Marker', ], 'PageSize' => [ 'shape' => 'PageSize', ], ], ], 'DescribeLoadBalancersOutput' => [ 'type' => 'structure', 'members' => [ 'LoadBalancers' => [ 'shape' => 'LoadBalancers', ], 'NextMarker' => [ 'shape' => 'Marker', ], ], ], 'DescribeRulesInput' => [ 'type' => 'structure', 'members' => [ 'ListenerArn' => [ 'shape' => 'ListenerArn', ], 'RuleArns' => [ 'shape' => 'RuleArns', ], 'Marker' => [ 'shape' => 'Marker', ], 'PageSize' => [ 'shape' => 'PageSize', ], ], ], 'DescribeRulesOutput' => [ 'type' => 'structure', 'members' => [ 'Rules' => [ 'shape' => 'Rules', ], 'NextMarker' => [ 'shape' => 'Marker', ], ], ], 'DescribeSSLPoliciesInput' => [ 'type' => 'structure', 'members' => [ 'Names' => [ 'shape' => 'SslPolicyNames', ], 'Marker' => [ 'shape' => 'Marker', ], 'PageSize' => [ 'shape' => 'PageSize', ], 'LoadBalancerType' => [ 'shape' => 'LoadBalancerTypeEnum', ], ], ], 'DescribeSSLPoliciesOutput' => [ 'type' => 'structure', 'members' => [ 'SslPolicies' => [ 'shape' => 'SslPolicies', ], 'NextMarker' => [ 'shape' => 'Marker', ], ], ], 'DescribeTagsInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArns', ], 'members' => [ 'ResourceArns' => [ 'shape' => 'ResourceArns', ], ], ], 'DescribeTagsOutput' => [ 'type' => 'structure', 'members' => [ 'TagDescriptions' => [ 'shape' => 'TagDescriptions', ], ], ], 'DescribeTargetGroupAttributesInput' => [ 'type' => 'structure', 'required' => [ 'TargetGroupArn', ], 'members' => [ 'TargetGroupArn' => [ 'shape' => 'TargetGroupArn', ], ], ], 'DescribeTargetGroupAttributesOutput' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'TargetGroupAttributes', ], ], ], 'DescribeTargetGroupsInput' => [ 'type' => 'structure', 'members' => [ 'LoadBalancerArn' => [ 'shape' => 'LoadBalancerArn', ], 'TargetGroupArns' => [ 'shape' => 'TargetGroupArns', ], 'Names' => [ 'shape' => 'TargetGroupNames', ], 'Marker' => [ 'shape' => 'Marker', ], 'PageSize' => [ 'shape' => 'PageSize', ], ], ], 'DescribeTargetGroupsOutput' => [ 'type' => 'structure', 'members' => [ 'TargetGroups' => [ 'shape' => 'TargetGroups', ], 'NextMarker' => [ 'shape' => 'Marker', ], ], ], 'DescribeTargetHealthInput' => [ 'type' => 'structure', 'required' => [ 'TargetGroupArn', ], 'members' => [ 'TargetGroupArn' => [ 'shape' => 'TargetGroupArn', ], 'Targets' => [ 'shape' => 'TargetDescriptions', ], 'Include' => [ 'shape' => 'ListOfDescribeTargetHealthIncludeOptions', ], ], ], 'DescribeTargetHealthInputIncludeEnum' => [ 'type' => 'string', 'enum' => [ 'AnomalyDetection', 'All', ], ], 'DescribeTargetHealthOutput' => [ 'type' => 'structure', 'members' => [ 'TargetHealthDescriptions' => [ 'shape' => 'TargetHealthDescriptions', ], ], ], 'DescribeTrustStoreAssociationsInput' => [ 'type' => 'structure', 'required' => [ 'TrustStoreArn', ], 'members' => [ 'TrustStoreArn' => [ 'shape' => 'TrustStoreArn', ], 'Marker' => [ 'shape' => 'Marker', ], 'PageSize' => [ 'shape' => 'PageSize', ], ], ], 'DescribeTrustStoreAssociationsOutput' => [ 'type' => 'structure', 'members' => [ 'TrustStoreAssociations' => [ 'shape' => 'TrustStoreAssociations', ], 'NextMarker' => [ 'shape' => 'Marker', ], ], ], 'DescribeTrustStoreRevocation' => [ 'type' => 'structure', 'members' => [ 'TrustStoreArn' => [ 'shape' => 'TrustStoreArn', ], 'RevocationId' => [ 'shape' => 'RevocationId', ], 'RevocationType' => [ 'shape' => 'RevocationType', ], 'NumberOfRevokedEntries' => [ 'shape' => 'NumberOfRevokedEntries', ], ], ], 'DescribeTrustStoreRevocationResponse' => [ 'type' => 'list', 'member' => [ 'shape' => 'DescribeTrustStoreRevocation', ], ], 'DescribeTrustStoreRevocationsInput' => [ 'type' => 'structure', 'required' => [ 'TrustStoreArn', ], 'members' => [ 'TrustStoreArn' => [ 'shape' => 'TrustStoreArn', ], 'RevocationIds' => [ 'shape' => 'RevocationIds', ], 'Marker' => [ 'shape' => 'Marker', ], 'PageSize' => [ 'shape' => 'PageSize', ], ], ], 'DescribeTrustStoreRevocationsOutput' => [ 'type' => 'structure', 'members' => [ 'TrustStoreRevocations' => [ 'shape' => 'DescribeTrustStoreRevocationResponse', ], 'NextMarker' => [ 'shape' => 'Marker', ], ], ], 'DescribeTrustStoresInput' => [ 'type' => 'structure', 'members' => [ 'TrustStoreArns' => [ 'shape' => 'TrustStoreArns', ], 'Names' => [ 'shape' => 'TrustStoreNames', ], 'Marker' => [ 'shape' => 'Marker', ], 'PageSize' => [ 'shape' => 'PageSize', ], ], ], 'DescribeTrustStoresOutput' => [ 'type' => 'structure', 'members' => [ 'TrustStores' => [ 'shape' => 'TrustStores', ], 'NextMarker' => [ 'shape' => 'Marker', ], ], ], 'Description' => [ 'type' => 'string', ], 'DuplicateListenerException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DuplicateListener', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DuplicateLoadBalancerNameException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DuplicateLoadBalancerName', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DuplicateTagKeysException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DuplicateTagKeys', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DuplicateTargetGroupNameException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DuplicateTargetGroupName', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DuplicateTrustStoreNameException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DuplicateTrustStoreName', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'EnablePrefixForIpv6SourceNatEnum' => [ 'type' => 'string', 'enum' => [ 'on', 'off', ], ], 'EnforceSecurityGroupInboundRulesOnPrivateLinkTraffic' => [ 'type' => 'string', ], 'EnforceSecurityGroupInboundRulesOnPrivateLinkTrafficEnum' => [ 'type' => 'string', 'enum' => [ 'on', 'off', ], ], 'FixedResponseActionConfig' => [ 'type' => 'structure', 'required' => [ 'StatusCode', ], 'members' => [ 'MessageBody' => [ 'shape' => 'FixedResponseActionMessage', ], 'StatusCode' => [ 'shape' => 'FixedResponseActionStatusCode', ], 'ContentType' => [ 'shape' => 'FixedResponseActionContentType', ], ], ], 'FixedResponseActionContentType' => [ 'type' => 'string', 'max' => 32, 'min' => 0, ], 'FixedResponseActionMessage' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'FixedResponseActionStatusCode' => [ 'type' => 'string', 'pattern' => '^(2|4|5)\\d\\d$', ], 'ForwardActionConfig' => [ 'type' => 'structure', 'members' => [ 'TargetGroups' => [ 'shape' => 'TargetGroupList', ], 'TargetGroupStickinessConfig' => [ 'shape' => 'TargetGroupStickinessConfig', ], ], ], 'GetResourcePolicyInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'GetResourcePolicyOutput' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'Policy', ], ], ], 'GetTrustStoreCaCertificatesBundleInput' => [ 'type' => 'structure', 'required' => [ 'TrustStoreArn', ], 'members' => [ 'TrustStoreArn' => [ 'shape' => 'TrustStoreArn', ], ], ], 'GetTrustStoreCaCertificatesBundleOutput' => [ 'type' => 'structure', 'members' => [ 'Location' => [ 'shape' => 'Location', ], ], ], 'GetTrustStoreRevocationContentInput' => [ 'type' => 'structure', 'required' => [ 'TrustStoreArn', 'RevocationId', ], 'members' => [ 'TrustStoreArn' => [ 'shape' => 'TrustStoreArn', ], 'RevocationId' => [ 'shape' => 'RevocationId', ], ], ], 'GetTrustStoreRevocationContentOutput' => [ 'type' => 'structure', 'members' => [ 'Location' => [ 'shape' => 'Location', ], ], ], 'GrpcCode' => [ 'type' => 'string', ], 'HealthCheckEnabled' => [ 'type' => 'boolean', ], 'HealthCheckIntervalSeconds' => [ 'type' => 'integer', 'max' => 300, 'min' => 5, ], 'HealthCheckPort' => [ 'type' => 'string', ], 'HealthCheckThresholdCount' => [ 'type' => 'integer', 'max' => 10, 'min' => 2, ], 'HealthCheckTimeoutSeconds' => [ 'type' => 'integer', 'max' => 120, 'min' => 2, ], 'HealthUnavailableException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'HealthUnavailable', 'httpStatusCode' => 500, ], 'exception' => true, ], 'HostHeaderConditionConfig' => [ 'type' => 'structure', 'members' => [ 'Values' => [ 'shape' => 'ListOfString', ], ], ], 'HttpCode' => [ 'type' => 'string', ], 'HttpHeaderConditionConfig' => [ 'type' => 'structure', 'members' => [ 'HttpHeaderName' => [ 'shape' => 'HttpHeaderConditionName', ], 'Values' => [ 'shape' => 'ListOfString', ], ], ], 'HttpHeaderConditionName' => [ 'type' => 'string', ], 'HttpRequestMethodConditionConfig' => [ 'type' => 'structure', 'members' => [ 'Values' => [ 'shape' => 'ListOfString', ], ], ], 'IPv6Address' => [ 'type' => 'string', ], 'IgnoreClientCertificateExpiry' => [ 'type' => 'boolean', ], 'IncompatibleProtocolsException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'IncompatibleProtocols', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InsufficientCapacityException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InsufficientCapacity', 'httpStatusCode' => 500, ], 'exception' => true, ], 'InvalidCaCertificatesBundleException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidCaCertificatesBundle', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidConfigurationRequestException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidConfigurationRequest', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidLoadBalancerActionException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidLoadBalancerAction', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidRevocationContentException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidRevocationContent', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidSchemeException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidScheme', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidSecurityGroupException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidSecurityGroup', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidSubnetException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidSubnet', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidTargetException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidTarget', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'IpAddress' => [ 'type' => 'string', ], 'IpAddressType' => [ 'type' => 'string', 'enum' => [ 'ipv4', 'dualstack', 'dualstack-without-public-ipv4', ], ], 'IpamPoolId' => [ 'type' => 'string', 'max' => 1000, 'pattern' => '^(ipam-pool-)[a-zA-Z0-9]+$', ], 'IpamPools' => [ 'type' => 'structure', 'members' => [ 'Ipv4IpamPoolId' => [ 'shape' => 'IpamPoolId', ], ], ], 'IsDefault' => [ 'type' => 'boolean', ], 'LastModifiedTime' => [ 'type' => 'timestamp', ], 'Limit' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'Max' => [ 'shape' => 'Max', ], ], ], 'Limits' => [ 'type' => 'list', 'member' => [ 'shape' => 'Limit', ], ], 'ListOfDescribeTargetHealthIncludeOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'DescribeTargetHealthInputIncludeEnum', ], ], 'ListOfString' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringValue', ], ], 'Listener' => [ 'type' => 'structure', 'members' => [ 'ListenerArn' => [ 'shape' => 'ListenerArn', ], 'LoadBalancerArn' => [ 'shape' => 'LoadBalancerArn', ], 'Port' => [ 'shape' => 'Port', ], 'Protocol' => [ 'shape' => 'ProtocolEnum', ], 'Certificates' => [ 'shape' => 'CertificateList', ], 'SslPolicy' => [ 'shape' => 'SslPolicyName', ], 'DefaultActions' => [ 'shape' => 'Actions', ], 'AlpnPolicy' => [ 'shape' => 'AlpnPolicyName', ], 'MutualAuthentication' => [ 'shape' => 'MutualAuthenticationAttributes', ], ], ], 'ListenerArn' => [ 'type' => 'string', ], 'ListenerArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListenerArn', ], ], 'ListenerAttribute' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'ListenerAttributeKey', ], 'Value' => [ 'shape' => 'ListenerAttributeValue', ], ], ], 'ListenerAttributeKey' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^[a-zA-Z0-9._]+$', ], 'ListenerAttributeValue' => [ 'type' => 'string', ], 'ListenerAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListenerAttribute', ], ], 'ListenerNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ListenerNotFound', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Listeners' => [ 'type' => 'list', 'member' => [ 'shape' => 'Listener', ], ], 'LoadBalancer' => [ 'type' => 'structure', 'members' => [ 'LoadBalancerArn' => [ 'shape' => 'LoadBalancerArn', ], 'DNSName' => [ 'shape' => 'DNSName', ], 'CanonicalHostedZoneId' => [ 'shape' => 'CanonicalHostedZoneId', ], 'CreatedTime' => [ 'shape' => 'CreatedTime', ], 'LoadBalancerName' => [ 'shape' => 'LoadBalancerName', ], 'Scheme' => [ 'shape' => 'LoadBalancerSchemeEnum', ], 'VpcId' => [ 'shape' => 'VpcId', ], 'State' => [ 'shape' => 'LoadBalancerState', ], 'Type' => [ 'shape' => 'LoadBalancerTypeEnum', ], 'AvailabilityZones' => [ 'shape' => 'AvailabilityZones', ], 'SecurityGroups' => [ 'shape' => 'SecurityGroups', ], 'IpAddressType' => [ 'shape' => 'IpAddressType', ], 'CustomerOwnedIpv4Pool' => [ 'shape' => 'CustomerOwnedIpv4Pool', ], 'EnforceSecurityGroupInboundRulesOnPrivateLinkTraffic' => [ 'shape' => 'EnforceSecurityGroupInboundRulesOnPrivateLinkTraffic', ], 'EnablePrefixForIpv6SourceNat' => [ 'shape' => 'EnablePrefixForIpv6SourceNatEnum', ], 'IpamPools' => [ 'shape' => 'IpamPools', ], ], ], 'LoadBalancerAddress' => [ 'type' => 'structure', 'members' => [ 'IpAddress' => [ 'shape' => 'IpAddress', ], 'AllocationId' => [ 'shape' => 'AllocationId', ], 'PrivateIPv4Address' => [ 'shape' => 'PrivateIPv4Address', ], 'IPv6Address' => [ 'shape' => 'IPv6Address', ], ], ], 'LoadBalancerAddresses' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoadBalancerAddress', ], ], 'LoadBalancerArn' => [ 'type' => 'string', ], 'LoadBalancerArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoadBalancerArn', ], ], 'LoadBalancerAttribute' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'LoadBalancerAttributeKey', ], 'Value' => [ 'shape' => 'LoadBalancerAttributeValue', ], ], ], 'LoadBalancerAttributeKey' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^[a-zA-Z0-9._]+$', ], 'LoadBalancerAttributeValue' => [ 'type' => 'string', 'max' => 1024, ], 'LoadBalancerAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoadBalancerAttribute', ], 'max' => 20, ], 'LoadBalancerName' => [ 'type' => 'string', ], 'LoadBalancerNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoadBalancerName', ], ], 'LoadBalancerNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'LoadBalancerNotFound', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'LoadBalancerSchemeEnum' => [ 'type' => 'string', 'enum' => [ 'internet-facing', 'internal', ], ], 'LoadBalancerState' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'LoadBalancerStateEnum', ], 'Reason' => [ 'shape' => 'StateReason', ], ], ], 'LoadBalancerStateEnum' => [ 'type' => 'string', 'enum' => [ 'active', 'provisioning', 'active_impaired', 'failed', ], ], 'LoadBalancerTypeEnum' => [ 'type' => 'string', 'enum' => [ 'application', 'network', 'gateway', ], ], 'LoadBalancers' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoadBalancer', ], ], 'Location' => [ 'type' => 'string', ], 'Marker' => [ 'type' => 'string', ], 'Matcher' => [ 'type' => 'structure', 'members' => [ 'HttpCode' => [ 'shape' => 'HttpCode', ], 'GrpcCode' => [ 'shape' => 'GrpcCode', ], ], ], 'Max' => [ 'type' => 'string', ], 'MinimumLoadBalancerCapacity' => [ 'type' => 'structure', 'members' => [ 'CapacityUnits' => [ 'shape' => 'CapacityUnits', ], ], ], 'MitigationInEffectEnum' => [ 'type' => 'string', 'enum' => [ 'yes', 'no', ], ], 'Mode' => [ 'type' => 'string', ], 'ModifyCapacityReservationInput' => [ 'type' => 'structure', 'required' => [ 'LoadBalancerArn', ], 'members' => [ 'LoadBalancerArn' => [ 'shape' => 'LoadBalancerArn', ], 'MinimumLoadBalancerCapacity' => [ 'shape' => 'MinimumLoadBalancerCapacity', ], 'ResetCapacityReservation' => [ 'shape' => 'ResetCapacityReservation', ], ], ], 'ModifyCapacityReservationOutput' => [ 'type' => 'structure', 'members' => [ 'LastModifiedTime' => [ 'shape' => 'LastModifiedTime', ], 'DecreaseRequestsRemaining' => [ 'shape' => 'DecreaseRequestsRemaining', ], 'MinimumLoadBalancerCapacity' => [ 'shape' => 'MinimumLoadBalancerCapacity', ], 'CapacityReservationState' => [ 'shape' => 'ZonalCapacityReservationStates', ], ], ], 'ModifyIpPoolsInput' => [ 'type' => 'structure', 'required' => [ 'LoadBalancerArn', ], 'members' => [ 'LoadBalancerArn' => [ 'shape' => 'LoadBalancerArn', ], 'IpamPools' => [ 'shape' => 'IpamPools', ], 'RemoveIpamPools' => [ 'shape' => 'RemoveIpamPools', ], ], ], 'ModifyIpPoolsOutput' => [ 'type' => 'structure', 'members' => [ 'IpamPools' => [ 'shape' => 'IpamPools', ], ], ], 'ModifyListenerAttributesInput' => [ 'type' => 'structure', 'required' => [ 'ListenerArn', 'Attributes', ], 'members' => [ 'ListenerArn' => [ 'shape' => 'ListenerArn', ], 'Attributes' => [ 'shape' => 'ListenerAttributes', ], ], ], 'ModifyListenerAttributesOutput' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'ListenerAttributes', ], ], ], 'ModifyListenerInput' => [ 'type' => 'structure', 'required' => [ 'ListenerArn', ], 'members' => [ 'ListenerArn' => [ 'shape' => 'ListenerArn', ], 'Port' => [ 'shape' => 'Port', ], 'Protocol' => [ 'shape' => 'ProtocolEnum', ], 'SslPolicy' => [ 'shape' => 'SslPolicyName', ], 'Certificates' => [ 'shape' => 'CertificateList', ], 'DefaultActions' => [ 'shape' => 'Actions', ], 'AlpnPolicy' => [ 'shape' => 'AlpnPolicyName', ], 'MutualAuthentication' => [ 'shape' => 'MutualAuthenticationAttributes', ], ], ], 'ModifyListenerOutput' => [ 'type' => 'structure', 'members' => [ 'Listeners' => [ 'shape' => 'Listeners', ], ], ], 'ModifyLoadBalancerAttributesInput' => [ 'type' => 'structure', 'required' => [ 'LoadBalancerArn', 'Attributes', ], 'members' => [ 'LoadBalancerArn' => [ 'shape' => 'LoadBalancerArn', ], 'Attributes' => [ 'shape' => 'LoadBalancerAttributes', ], ], ], 'ModifyLoadBalancerAttributesOutput' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'LoadBalancerAttributes', ], ], ], 'ModifyRuleInput' => [ 'type' => 'structure', 'required' => [ 'RuleArn', ], 'members' => [ 'RuleArn' => [ 'shape' => 'RuleArn', ], 'Conditions' => [ 'shape' => 'RuleConditionList', ], 'Actions' => [ 'shape' => 'Actions', ], ], ], 'ModifyRuleOutput' => [ 'type' => 'structure', 'members' => [ 'Rules' => [ 'shape' => 'Rules', ], ], ], 'ModifyTargetGroupAttributesInput' => [ 'type' => 'structure', 'required' => [ 'TargetGroupArn', 'Attributes', ], 'members' => [ 'TargetGroupArn' => [ 'shape' => 'TargetGroupArn', ], 'Attributes' => [ 'shape' => 'TargetGroupAttributes', ], ], ], 'ModifyTargetGroupAttributesOutput' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'TargetGroupAttributes', ], ], ], 'ModifyTargetGroupInput' => [ 'type' => 'structure', 'required' => [ 'TargetGroupArn', ], 'members' => [ 'TargetGroupArn' => [ 'shape' => 'TargetGroupArn', ], 'HealthCheckProtocol' => [ 'shape' => 'ProtocolEnum', ], 'HealthCheckPort' => [ 'shape' => 'HealthCheckPort', ], 'HealthCheckPath' => [ 'shape' => 'Path', ], 'HealthCheckEnabled' => [ 'shape' => 'HealthCheckEnabled', ], 'HealthCheckIntervalSeconds' => [ 'shape' => 'HealthCheckIntervalSeconds', ], 'HealthCheckTimeoutSeconds' => [ 'shape' => 'HealthCheckTimeoutSeconds', ], 'HealthyThresholdCount' => [ 'shape' => 'HealthCheckThresholdCount', ], 'UnhealthyThresholdCount' => [ 'shape' => 'HealthCheckThresholdCount', ], 'Matcher' => [ 'shape' => 'Matcher', ], ], ], 'ModifyTargetGroupOutput' => [ 'type' => 'structure', 'members' => [ 'TargetGroups' => [ 'shape' => 'TargetGroups', ], ], ], 'ModifyTrustStoreInput' => [ 'type' => 'structure', 'required' => [ 'TrustStoreArn', 'CaCertificatesBundleS3Bucket', 'CaCertificatesBundleS3Key', ], 'members' => [ 'TrustStoreArn' => [ 'shape' => 'TrustStoreArn', ], 'CaCertificatesBundleS3Bucket' => [ 'shape' => 'S3Bucket', ], 'CaCertificatesBundleS3Key' => [ 'shape' => 'S3Key', ], 'CaCertificatesBundleS3ObjectVersion' => [ 'shape' => 'S3ObjectVersion', ], ], ], 'ModifyTrustStoreOutput' => [ 'type' => 'structure', 'members' => [ 'TrustStores' => [ 'shape' => 'TrustStores', ], ], ], 'MutualAuthenticationAttributes' => [ 'type' => 'structure', 'members' => [ 'Mode' => [ 'shape' => 'Mode', ], 'TrustStoreArn' => [ 'shape' => 'TrustStoreArn', ], 'IgnoreClientCertificateExpiry' => [ 'shape' => 'IgnoreClientCertificateExpiry', ], 'TrustStoreAssociationStatus' => [ 'shape' => 'TrustStoreAssociationStatusEnum', ], 'AdvertiseTrustStoreCaNames' => [ 'shape' => 'AdvertiseTrustStoreCaNamesEnum', ], ], ], 'Name' => [ 'type' => 'string', ], 'NumberOfCaCertificates' => [ 'type' => 'integer', ], 'NumberOfRevokedEntries' => [ 'type' => 'long', ], 'OperationNotPermittedException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'OperationNotPermitted', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'OutpostId' => [ 'type' => 'string', ], 'PageSize' => [ 'type' => 'integer', 'max' => 400, 'min' => 1, ], 'Path' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'PathPatternConditionConfig' => [ 'type' => 'structure', 'members' => [ 'Values' => [ 'shape' => 'ListOfString', ], ], ], 'Policy' => [ 'type' => 'string', 'min' => 1, ], 'Port' => [ 'type' => 'integer', 'max' => 65535, 'min' => 1, ], 'PriorRequestNotCompleteException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'PriorRequestNotComplete', 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'PriorityInUseException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'PriorityInUse', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'PrivateIPv4Address' => [ 'type' => 'string', ], 'ProtocolEnum' => [ 'type' => 'string', 'enum' => [ 'HTTP', 'HTTPS', 'TCP', 'TLS', 'UDP', 'TCP_UDP', 'GENEVE', ], ], 'ProtocolVersion' => [ 'type' => 'string', ], 'QueryStringConditionConfig' => [ 'type' => 'structure', 'members' => [ 'Values' => [ 'shape' => 'QueryStringKeyValuePairList', ], ], ], 'QueryStringKeyValuePair' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'StringValue', ], 'Value' => [ 'shape' => 'StringValue', ], ], ], 'QueryStringKeyValuePairList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueryStringKeyValuePair', ], ], 'RedirectActionConfig' => [ 'type' => 'structure', 'required' => [ 'StatusCode', ], 'members' => [ 'Protocol' => [ 'shape' => 'RedirectActionProtocol', ], 'Port' => [ 'shape' => 'RedirectActionPort', ], 'Host' => [ 'shape' => 'RedirectActionHost', ], 'Path' => [ 'shape' => 'RedirectActionPath', ], 'Query' => [ 'shape' => 'RedirectActionQuery', ], 'StatusCode' => [ 'shape' => 'RedirectActionStatusCodeEnum', ], ], ], 'RedirectActionHost' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'RedirectActionPath' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'RedirectActionPort' => [ 'type' => 'string', ], 'RedirectActionProtocol' => [ 'type' => 'string', 'pattern' => '^(HTTPS?|#\\{protocol\\})$', ], 'RedirectActionQuery' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'RedirectActionStatusCodeEnum' => [ 'type' => 'string', 'enum' => [ 'HTTP_301', 'HTTP_302', ], ], 'RegisterTargetsInput' => [ 'type' => 'structure', 'required' => [ 'TargetGroupArn', 'Targets', ], 'members' => [ 'TargetGroupArn' => [ 'shape' => 'TargetGroupArn', ], 'Targets' => [ 'shape' => 'TargetDescriptions', ], ], ], 'RegisterTargetsOutput' => [ 'type' => 'structure', 'members' => [], ], 'RemoveIpamPoolEnum' => [ 'type' => 'string', 'enum' => [ 'ipv4', ], ], 'RemoveIpamPools' => [ 'type' => 'list', 'member' => [ 'shape' => 'RemoveIpamPoolEnum', ], ], 'RemoveListenerCertificatesInput' => [ 'type' => 'structure', 'required' => [ 'ListenerArn', 'Certificates', ], 'members' => [ 'ListenerArn' => [ 'shape' => 'ListenerArn', ], 'Certificates' => [ 'shape' => 'CertificateList', ], ], ], 'RemoveListenerCertificatesOutput' => [ 'type' => 'structure', 'members' => [], ], 'RemoveTagsInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArns', 'TagKeys', ], 'members' => [ 'ResourceArns' => [ 'shape' => 'ResourceArns', ], 'TagKeys' => [ 'shape' => 'TagKeys', ], ], ], 'RemoveTagsOutput' => [ 'type' => 'structure', 'members' => [], ], 'RemoveTrustStoreRevocationsInput' => [ 'type' => 'structure', 'required' => [ 'TrustStoreArn', 'RevocationIds', ], 'members' => [ 'TrustStoreArn' => [ 'shape' => 'TrustStoreArn', ], 'RevocationIds' => [ 'shape' => 'RevocationIds', ], ], ], 'RemoveTrustStoreRevocationsOutput' => [ 'type' => 'structure', 'members' => [], ], 'ResetCapacityReservation' => [ 'type' => 'boolean', ], 'ResourceArn' => [ 'type' => 'string', ], 'ResourceArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceArn', ], ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ResourceInUse', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ResourceNotFound', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'RevocationContent' => [ 'type' => 'structure', 'members' => [ 'S3Bucket' => [ 'shape' => 'S3Bucket', ], 'S3Key' => [ 'shape' => 'S3Key', ], 'S3ObjectVersion' => [ 'shape' => 'S3ObjectVersion', ], 'RevocationType' => [ 'shape' => 'RevocationType', ], ], ], 'RevocationContentNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'RevocationContentNotFound', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'RevocationContents' => [ 'type' => 'list', 'member' => [ 'shape' => 'RevocationContent', ], ], 'RevocationId' => [ 'type' => 'long', ], 'RevocationIdNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'RevocationIdNotFound', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'RevocationIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'RevocationId', ], ], 'RevocationType' => [ 'type' => 'string', 'enum' => [ 'CRL', ], ], 'Rule' => [ 'type' => 'structure', 'members' => [ 'RuleArn' => [ 'shape' => 'RuleArn', ], 'Priority' => [ 'shape' => 'String', ], 'Conditions' => [ 'shape' => 'RuleConditionList', ], 'Actions' => [ 'shape' => 'Actions', ], 'IsDefault' => [ 'shape' => 'IsDefault', ], ], ], 'RuleArn' => [ 'type' => 'string', ], 'RuleArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleArn', ], ], 'RuleCondition' => [ 'type' => 'structure', 'members' => [ 'Field' => [ 'shape' => 'ConditionFieldName', ], 'Values' => [ 'shape' => 'ListOfString', ], 'HostHeaderConfig' => [ 'shape' => 'HostHeaderConditionConfig', ], 'PathPatternConfig' => [ 'shape' => 'PathPatternConditionConfig', ], 'HttpHeaderConfig' => [ 'shape' => 'HttpHeaderConditionConfig', ], 'QueryStringConfig' => [ 'shape' => 'QueryStringConditionConfig', ], 'HttpRequestMethodConfig' => [ 'shape' => 'HttpRequestMethodConditionConfig', ], 'SourceIpConfig' => [ 'shape' => 'SourceIpConditionConfig', ], ], ], 'RuleConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleCondition', ], ], 'RuleNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'RuleNotFound', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'RulePriority' => [ 'type' => 'integer', 'max' => 50000, 'min' => 1, ], 'RulePriorityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RulePriorityPair', ], ], 'RulePriorityPair' => [ 'type' => 'structure', 'members' => [ 'RuleArn' => [ 'shape' => 'RuleArn', ], 'Priority' => [ 'shape' => 'RulePriority', ], ], ], 'Rules' => [ 'type' => 'list', 'member' => [ 'shape' => 'Rule', ], ], 'S3Bucket' => [ 'type' => 'string', ], 'S3Key' => [ 'type' => 'string', ], 'S3ObjectVersion' => [ 'type' => 'string', ], 'SSLPolicyNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SSLPolicyNotFound', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SecurityGroupId' => [ 'type' => 'string', ], 'SecurityGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], ], 'SetIpAddressTypeInput' => [ 'type' => 'structure', 'required' => [ 'LoadBalancerArn', 'IpAddressType', ], 'members' => [ 'LoadBalancerArn' => [ 'shape' => 'LoadBalancerArn', ], 'IpAddressType' => [ 'shape' => 'IpAddressType', ], ], ], 'SetIpAddressTypeOutput' => [ 'type' => 'structure', 'members' => [ 'IpAddressType' => [ 'shape' => 'IpAddressType', ], ], ], 'SetRulePrioritiesInput' => [ 'type' => 'structure', 'required' => [ 'RulePriorities', ], 'members' => [ 'RulePriorities' => [ 'shape' => 'RulePriorityList', ], ], ], 'SetRulePrioritiesOutput' => [ 'type' => 'structure', 'members' => [ 'Rules' => [ 'shape' => 'Rules', ], ], ], 'SetSecurityGroupsInput' => [ 'type' => 'structure', 'required' => [ 'LoadBalancerArn', 'SecurityGroups', ], 'members' => [ 'LoadBalancerArn' => [ 'shape' => 'LoadBalancerArn', ], 'SecurityGroups' => [ 'shape' => 'SecurityGroups', ], 'EnforceSecurityGroupInboundRulesOnPrivateLinkTraffic' => [ 'shape' => 'EnforceSecurityGroupInboundRulesOnPrivateLinkTrafficEnum', ], ], ], 'SetSecurityGroupsOutput' => [ 'type' => 'structure', 'members' => [ 'SecurityGroupIds' => [ 'shape' => 'SecurityGroups', ], 'EnforceSecurityGroupInboundRulesOnPrivateLinkTraffic' => [ 'shape' => 'EnforceSecurityGroupInboundRulesOnPrivateLinkTrafficEnum', ], ], ], 'SetSubnetsInput' => [ 'type' => 'structure', 'required' => [ 'LoadBalancerArn', ], 'members' => [ 'LoadBalancerArn' => [ 'shape' => 'LoadBalancerArn', ], 'Subnets' => [ 'shape' => 'Subnets', ], 'SubnetMappings' => [ 'shape' => 'SubnetMappings', ], 'IpAddressType' => [ 'shape' => 'IpAddressType', ], 'EnablePrefixForIpv6SourceNat' => [ 'shape' => 'EnablePrefixForIpv6SourceNatEnum', ], ], ], 'SetSubnetsOutput' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZones' => [ 'shape' => 'AvailabilityZones', ], 'IpAddressType' => [ 'shape' => 'IpAddressType', ], 'EnablePrefixForIpv6SourceNat' => [ 'shape' => 'EnablePrefixForIpv6SourceNatEnum', ], ], ], 'SourceIpConditionConfig' => [ 'type' => 'structure', 'members' => [ 'Values' => [ 'shape' => 'ListOfString', ], ], ], 'SourceNatIpv6Prefix' => [ 'type' => 'string', ], 'SourceNatIpv6Prefixes' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceNatIpv6Prefix', ], ], 'SslPolicies' => [ 'type' => 'list', 'member' => [ 'shape' => 'SslPolicy', ], ], 'SslPolicy' => [ 'type' => 'structure', 'members' => [ 'SslProtocols' => [ 'shape' => 'SslProtocols', ], 'Ciphers' => [ 'shape' => 'Ciphers', ], 'Name' => [ 'shape' => 'SslPolicyName', ], 'SupportedLoadBalancerTypes' => [ 'shape' => 'ListOfString', ], ], ], 'SslPolicyName' => [ 'type' => 'string', ], 'SslPolicyNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'SslPolicyName', ], ], 'SslProtocol' => [ 'type' => 'string', ], 'SslProtocols' => [ 'type' => 'list', 'member' => [ 'shape' => 'SslProtocol', ], ], 'StateReason' => [ 'type' => 'string', ], 'String' => [ 'type' => 'string', ], 'StringValue' => [ 'type' => 'string', ], 'SubnetId' => [ 'type' => 'string', ], 'SubnetMapping' => [ 'type' => 'structure', 'members' => [ 'SubnetId' => [ 'shape' => 'SubnetId', ], 'AllocationId' => [ 'shape' => 'AllocationId', ], 'PrivateIPv4Address' => [ 'shape' => 'PrivateIPv4Address', ], 'IPv6Address' => [ 'shape' => 'IPv6Address', ], 'SourceNatIpv6Prefix' => [ 'shape' => 'SourceNatIpv6Prefix', ], ], ], 'SubnetMappings' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetMapping', ], ], 'SubnetNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SubnetNotFound', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Subnets' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagDescription' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagDescriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagDescription', ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'min' => 1, ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TargetAdministrativeOverrideReasonEnum' => [ 'type' => 'string', 'enum' => [ 'AdministrativeOverride.Unknown', 'AdministrativeOverride.NoOverride', 'AdministrativeOverride.ZonalShiftActive', 'AdministrativeOverride.ZonalShiftDelegatedToDns', ], ], 'TargetAdministrativeOverrideStateEnum' => [ 'type' => 'string', 'enum' => [ 'unknown', 'no_override', 'zonal_shift_active', 'zonal_shift_delegated_to_dns', ], ], 'TargetDescription' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'TargetId', ], 'Port' => [ 'shape' => 'Port', ], 'AvailabilityZone' => [ 'shape' => 'ZoneName', ], ], ], 'TargetDescriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetDescription', ], ], 'TargetGroup' => [ 'type' => 'structure', 'members' => [ 'TargetGroupArn' => [ 'shape' => 'TargetGroupArn', ], 'TargetGroupName' => [ 'shape' => 'TargetGroupName', ], 'Protocol' => [ 'shape' => 'ProtocolEnum', ], 'Port' => [ 'shape' => 'Port', ], 'VpcId' => [ 'shape' => 'VpcId', ], 'HealthCheckProtocol' => [ 'shape' => 'ProtocolEnum', ], 'HealthCheckPort' => [ 'shape' => 'HealthCheckPort', ], 'HealthCheckEnabled' => [ 'shape' => 'HealthCheckEnabled', ], 'HealthCheckIntervalSeconds' => [ 'shape' => 'HealthCheckIntervalSeconds', ], 'HealthCheckTimeoutSeconds' => [ 'shape' => 'HealthCheckTimeoutSeconds', ], 'HealthyThresholdCount' => [ 'shape' => 'HealthCheckThresholdCount', ], 'UnhealthyThresholdCount' => [ 'shape' => 'HealthCheckThresholdCount', ], 'HealthCheckPath' => [ 'shape' => 'Path', ], 'Matcher' => [ 'shape' => 'Matcher', ], 'LoadBalancerArns' => [ 'shape' => 'LoadBalancerArns', ], 'TargetType' => [ 'shape' => 'TargetTypeEnum', ], 'ProtocolVersion' => [ 'shape' => 'ProtocolVersion', ], 'IpAddressType' => [ 'shape' => 'TargetGroupIpAddressTypeEnum', ], ], ], 'TargetGroupArn' => [ 'type' => 'string', ], 'TargetGroupArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetGroupArn', ], ], 'TargetGroupAssociationLimitException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TargetGroupAssociationLimit', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TargetGroupAttribute' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'TargetGroupAttributeKey', ], 'Value' => [ 'shape' => 'TargetGroupAttributeValue', ], ], ], 'TargetGroupAttributeKey' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^[a-zA-Z0-9._]+$', ], 'TargetGroupAttributeValue' => [ 'type' => 'string', ], 'TargetGroupAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetGroupAttribute', ], ], 'TargetGroupIpAddressTypeEnum' => [ 'type' => 'string', 'enum' => [ 'ipv4', 'ipv6', ], ], 'TargetGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetGroupTuple', ], ], 'TargetGroupName' => [ 'type' => 'string', ], 'TargetGroupNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetGroupName', ], ], 'TargetGroupNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TargetGroupNotFound', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TargetGroupStickinessConfig' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'TargetGroupStickinessEnabled', ], 'DurationSeconds' => [ 'shape' => 'TargetGroupStickinessDurationSeconds', ], ], ], 'TargetGroupStickinessDurationSeconds' => [ 'type' => 'integer', ], 'TargetGroupStickinessEnabled' => [ 'type' => 'boolean', ], 'TargetGroupTuple' => [ 'type' => 'structure', 'members' => [ 'TargetGroupArn' => [ 'shape' => 'TargetGroupArn', ], 'Weight' => [ 'shape' => 'TargetGroupWeight', ], ], ], 'TargetGroupWeight' => [ 'type' => 'integer', ], 'TargetGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetGroup', ], ], 'TargetHealth' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'TargetHealthStateEnum', ], 'Reason' => [ 'shape' => 'TargetHealthReasonEnum', ], 'Description' => [ 'shape' => 'Description', ], ], ], 'TargetHealthDescription' => [ 'type' => 'structure', 'members' => [ 'Target' => [ 'shape' => 'TargetDescription', ], 'HealthCheckPort' => [ 'shape' => 'HealthCheckPort', ], 'TargetHealth' => [ 'shape' => 'TargetHealth', ], 'AnomalyDetection' => [ 'shape' => 'AnomalyDetection', ], 'AdministrativeOverride' => [ 'shape' => 'AdministrativeOverride', ], ], ], 'TargetHealthDescriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetHealthDescription', ], ], 'TargetHealthReasonEnum' => [ 'type' => 'string', 'enum' => [ 'Elb.RegistrationInProgress', 'Elb.InitialHealthChecking', 'Target.ResponseCodeMismatch', 'Target.Timeout', 'Target.FailedHealthChecks', 'Target.NotRegistered', 'Target.NotInUse', 'Target.DeregistrationInProgress', 'Target.InvalidState', 'Target.IpUnusable', 'Target.HealthCheckDisabled', 'Elb.InternalError', ], ], 'TargetHealthStateEnum' => [ 'type' => 'string', 'enum' => [ 'initial', 'healthy', 'unhealthy', 'unhealthy.draining', 'unused', 'draining', 'unavailable', ], ], 'TargetId' => [ 'type' => 'string', ], 'TargetTypeEnum' => [ 'type' => 'string', 'enum' => [ 'instance', 'ip', 'lambda', 'alb', ], ], 'TooManyActionsException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TooManyActions', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyCertificatesException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TooManyCertificates', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyListenersException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TooManyListeners', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyLoadBalancersException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TooManyLoadBalancers', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyRegistrationsForTargetIdException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TooManyRegistrationsForTargetId', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyRulesException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TooManyRules', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TooManyTags', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyTargetGroupsException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TooManyTargetGroups', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyTargetsException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TooManyTargets', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyTrustStoreRevocationEntriesException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TooManyTrustStoreRevocationEntries', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyTrustStoresException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TooManyTrustStores', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyUniqueTargetGroupsPerLoadBalancerException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TooManyUniqueTargetGroupsPerLoadBalancer', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TotalRevokedEntries' => [ 'type' => 'long', ], 'TrustStore' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'TrustStoreName', ], 'TrustStoreArn' => [ 'shape' => 'TrustStoreArn', ], 'Status' => [ 'shape' => 'TrustStoreStatus', ], 'NumberOfCaCertificates' => [ 'shape' => 'NumberOfCaCertificates', ], 'TotalRevokedEntries' => [ 'shape' => 'TotalRevokedEntries', ], ], ], 'TrustStoreArn' => [ 'type' => 'string', ], 'TrustStoreArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrustStoreArn', ], ], 'TrustStoreAssociation' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'TrustStoreAssociationResourceArn', ], ], ], 'TrustStoreAssociationNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'AssociationNotFound', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TrustStoreAssociationResourceArn' => [ 'type' => 'string', ], 'TrustStoreAssociationStatusEnum' => [ 'type' => 'string', 'enum' => [ 'active', 'removed', ], ], 'TrustStoreAssociations' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrustStoreAssociation', ], ], 'TrustStoreInUseException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TrustStoreInUse', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TrustStoreName' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^([a-zA-Z0-9]+-)*[a-zA-Z0-9]+$', ], 'TrustStoreNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrustStoreName', ], ], 'TrustStoreNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TrustStoreNotFound', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TrustStoreNotReadyException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TrustStoreNotReady', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TrustStoreRevocation' => [ 'type' => 'structure', 'members' => [ 'TrustStoreArn' => [ 'shape' => 'TrustStoreArn', ], 'RevocationId' => [ 'shape' => 'RevocationId', ], 'RevocationType' => [ 'shape' => 'RevocationType', ], 'NumberOfRevokedEntries' => [ 'shape' => 'NumberOfRevokedEntries', ], ], ], 'TrustStoreRevocations' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrustStoreRevocation', ], ], 'TrustStoreStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'CREATING', ], ], 'TrustStores' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrustStore', ], ], 'UnsupportedProtocolException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'UnsupportedProtocol', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'VpcId' => [ 'type' => 'string', ], 'ZonalCapacityReservationState' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'CapacityReservationStatus', ], 'AvailabilityZone' => [ 'shape' => 'ZoneName', ], 'EffectiveCapacityUnits' => [ 'shape' => 'CapacityUnitsDouble', ], ], ], 'ZonalCapacityReservationStates' => [ 'type' => 'list', 'member' => [ 'shape' => 'ZonalCapacityReservationState', ], ], 'ZoneName' => [ 'type' => 'string', ], ],];
