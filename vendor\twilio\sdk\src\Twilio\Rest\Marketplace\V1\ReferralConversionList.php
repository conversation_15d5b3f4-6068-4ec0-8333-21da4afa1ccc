<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Marketplace
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Marketplace\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\ListResource;
use Twilio\Values;
use Twilio\Version;


class ReferralConversionList extends ListResource
    {
    /**
     * Construct the ReferralConversionList
     *
     * @param Version $version Version that contains the resource
     */
    public function __construct(
        Version $version
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        ];

        $this->uri = '/ReferralConversion';
    }

    /**
     * Create the ReferralConversionInstance
     *
     * @param CreateReferralConversionRequest $createReferralConversionRequest
     * @return ReferralConversionInstance Created ReferralConversionInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function create(CreateReferralConversionRequest $createReferralConversionRequest): ReferralConversionInstance
    {

        $headers = Values::of(['Content-Type' => 'application/json', 'Accept' => 'application/json' ]);
        $data = $createReferralConversionRequest->toArray();
        $payload = $this->version->create('POST', $this->uri, [], $data, $headers);

        return new ReferralConversionInstance(
            $this->version,
            $payload
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Marketplace.V1.ReferralConversionList]';
    }
}
