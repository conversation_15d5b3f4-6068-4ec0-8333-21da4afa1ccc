<?php
// This file was auto-generated from sdk-root/src/data/accessanalyzer/2019-11-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2019-11-01', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'access-analyzer', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Access Analyzer', 'serviceId' => 'AccessAnalyzer', 'signatureVersion' => 'v4', 'signingName' => 'access-analyzer', 'uid' => 'accessanalyzer-2019-11-01', ], 'operations' => [ 'ApplyArchiveRule' => [ 'name' => 'ApplyArchiveRule', 'http' => [ 'method' => 'PUT', 'requestUri' => '/archive-rule', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ApplyArchiveRuleRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CancelPolicyGeneration' => [ 'name' => 'CancelPolicyGeneration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/policy/generation/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelPolicyGenerationRequest', ], 'output' => [ 'shape' => 'CancelPolicyGenerationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CheckAccessNotGranted' => [ 'name' => 'CheckAccessNotGranted', 'http' => [ 'method' => 'POST', 'requestUri' => '/policy/check-access-not-granted', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CheckAccessNotGrantedRequest', ], 'output' => [ 'shape' => 'CheckAccessNotGrantedResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CheckNoNewAccess' => [ 'name' => 'CheckNoNewAccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/policy/check-no-new-access', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CheckNoNewAccessRequest', ], 'output' => [ 'shape' => 'CheckNoNewAccessResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CheckNoPublicAccess' => [ 'name' => 'CheckNoPublicAccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/policy/check-no-public-access', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CheckNoPublicAccessRequest', ], 'output' => [ 'shape' => 'CheckNoPublicAccessResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnprocessableEntityException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateAccessPreview' => [ 'name' => 'CreateAccessPreview', 'http' => [ 'method' => 'PUT', 'requestUri' => '/access-preview', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAccessPreviewRequest', ], 'output' => [ 'shape' => 'CreateAccessPreviewResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateAnalyzer' => [ 'name' => 'CreateAnalyzer', 'http' => [ 'method' => 'PUT', 'requestUri' => '/analyzer', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAnalyzerRequest', ], 'output' => [ 'shape' => 'CreateAnalyzerResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateArchiveRule' => [ 'name' => 'CreateArchiveRule', 'http' => [ 'method' => 'PUT', 'requestUri' => '/analyzer/{analyzerName}/archive-rule', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateArchiveRuleRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteAnalyzer' => [ 'name' => 'DeleteAnalyzer', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/analyzer/{analyzerName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAnalyzerRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteArchiveRule' => [ 'name' => 'DeleteArchiveRule', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/analyzer/{analyzerName}/archive-rule/{ruleName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteArchiveRuleRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'GenerateFindingRecommendation' => [ 'name' => 'GenerateFindingRecommendation', 'http' => [ 'method' => 'POST', 'requestUri' => '/recommendation/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GenerateFindingRecommendationRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetAccessPreview' => [ 'name' => 'GetAccessPreview', 'http' => [ 'method' => 'GET', 'requestUri' => '/access-preview/{accessPreviewId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAccessPreviewRequest', ], 'output' => [ 'shape' => 'GetAccessPreviewResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetAnalyzedResource' => [ 'name' => 'GetAnalyzedResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/analyzed-resource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAnalyzedResourceRequest', ], 'output' => [ 'shape' => 'GetAnalyzedResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetAnalyzer' => [ 'name' => 'GetAnalyzer', 'http' => [ 'method' => 'GET', 'requestUri' => '/analyzer/{analyzerName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAnalyzerRequest', ], 'output' => [ 'shape' => 'GetAnalyzerResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetArchiveRule' => [ 'name' => 'GetArchiveRule', 'http' => [ 'method' => 'GET', 'requestUri' => '/analyzer/{analyzerName}/archive-rule/{ruleName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetArchiveRuleRequest', ], 'output' => [ 'shape' => 'GetArchiveRuleResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetFinding' => [ 'name' => 'GetFinding', 'http' => [ 'method' => 'GET', 'requestUri' => '/finding/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFindingRequest', ], 'output' => [ 'shape' => 'GetFindingResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetFindingRecommendation' => [ 'name' => 'GetFindingRecommendation', 'http' => [ 'method' => 'GET', 'requestUri' => '/recommendation/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFindingRecommendationRequest', ], 'output' => [ 'shape' => 'GetFindingRecommendationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetFindingV2' => [ 'name' => 'GetFindingV2', 'http' => [ 'method' => 'GET', 'requestUri' => '/findingv2/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFindingV2Request', ], 'output' => [ 'shape' => 'GetFindingV2Response', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetFindingsStatistics' => [ 'name' => 'GetFindingsStatistics', 'http' => [ 'method' => 'POST', 'requestUri' => '/analyzer/findings/statistics', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFindingsStatisticsRequest', ], 'output' => [ 'shape' => 'GetFindingsStatisticsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetGeneratedPolicy' => [ 'name' => 'GetGeneratedPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/policy/generation/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetGeneratedPolicyRequest', ], 'output' => [ 'shape' => 'GetGeneratedPolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAccessPreviewFindings' => [ 'name' => 'ListAccessPreviewFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/access-preview/{accessPreviewId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAccessPreviewFindingsRequest', ], 'output' => [ 'shape' => 'ListAccessPreviewFindingsResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAccessPreviews' => [ 'name' => 'ListAccessPreviews', 'http' => [ 'method' => 'GET', 'requestUri' => '/access-preview', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAccessPreviewsRequest', ], 'output' => [ 'shape' => 'ListAccessPreviewsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAnalyzedResources' => [ 'name' => 'ListAnalyzedResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/analyzed-resource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAnalyzedResourcesRequest', ], 'output' => [ 'shape' => 'ListAnalyzedResourcesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAnalyzers' => [ 'name' => 'ListAnalyzers', 'http' => [ 'method' => 'GET', 'requestUri' => '/analyzer', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAnalyzersRequest', ], 'output' => [ 'shape' => 'ListAnalyzersResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListArchiveRules' => [ 'name' => 'ListArchiveRules', 'http' => [ 'method' => 'GET', 'requestUri' => '/analyzer/{analyzerName}/archive-rule', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListArchiveRulesRequest', ], 'output' => [ 'shape' => 'ListArchiveRulesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListFindings' => [ 'name' => 'ListFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/finding', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFindingsRequest', ], 'output' => [ 'shape' => 'ListFindingsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListFindingsV2' => [ 'name' => 'ListFindingsV2', 'http' => [ 'method' => 'POST', 'requestUri' => '/findingv2', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFindingsV2Request', ], 'output' => [ 'shape' => 'ListFindingsV2Response', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListPolicyGenerations' => [ 'name' => 'ListPolicyGenerations', 'http' => [ 'method' => 'GET', 'requestUri' => '/policy/generation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPolicyGenerationsRequest', ], 'output' => [ 'shape' => 'ListPolicyGenerationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StartPolicyGeneration' => [ 'name' => 'StartPolicyGeneration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/policy/generation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartPolicyGenerationRequest', ], 'output' => [ 'shape' => 'StartPolicyGenerationResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'StartResourceScan' => [ 'name' => 'StartResourceScan', 'http' => [ 'method' => 'POST', 'requestUri' => '/resource/scan', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartResourceScanRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateAnalyzer' => [ 'name' => 'UpdateAnalyzer', 'http' => [ 'method' => 'PUT', 'requestUri' => '/analyzer/{analyzerName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAnalyzerRequest', ], 'output' => [ 'shape' => 'UpdateAnalyzerResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateArchiveRule' => [ 'name' => 'UpdateArchiveRule', 'http' => [ 'method' => 'PUT', 'requestUri' => '/analyzer/{analyzerName}/archive-rule/{ruleName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateArchiveRuleRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateFindings' => [ 'name' => 'UpdateFindings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/finding', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFindingsRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'ValidatePolicy' => [ 'name' => 'ValidatePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/policy/validation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ValidatePolicyRequest', ], 'output' => [ 'shape' => 'ValidatePolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], ], 'shapes' => [ 'Access' => [ 'type' => 'structure', 'members' => [ 'actions' => [ 'shape' => 'AccessActionsList', ], 'resources' => [ 'shape' => 'AccessResourcesList', ], ], ], 'AccessActionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Action', ], 'max' => 100, 'min' => 0, ], 'AccessCheckPolicyDocument' => [ 'type' => 'string', 'sensitive' => true, ], 'AccessCheckPolicyType' => [ 'type' => 'string', 'enum' => [ 'IDENTITY_POLICY', 'RESOURCE_POLICY', ], ], 'AccessCheckResourceType' => [ 'type' => 'string', 'enum' => [ 'AWS::DynamoDB::Table', 'AWS::DynamoDB::Stream', 'AWS::EFS::FileSystem', 'AWS::OpenSearchService::Domain', 'AWS::Kinesis::Stream', 'AWS::Kinesis::StreamConsumer', 'AWS::KMS::Key', 'AWS::Lambda::Function', 'AWS::S3::Bucket', 'AWS::S3::AccessPoint', 'AWS::S3Express::DirectoryBucket', 'AWS::S3::Glacier', 'AWS::S3Outposts::Bucket', 'AWS::S3Outposts::AccessPoint', 'AWS::SecretsManager::Secret', 'AWS::SNS::Topic', 'AWS::SQS::Queue', 'AWS::IAM::AssumeRolePolicyDocument', 'AWS::S3Tables::TableBucket', 'AWS::ApiGateway::RestApi', 'AWS::CodeArtifact::Domain', 'AWS::Backup::BackupVault', 'AWS::CloudTrail::Dashboard', 'AWS::CloudTrail::EventDataStore', 'AWS::S3Tables::Table', 'AWS::S3Express::AccessPoint', ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccessPointArn' => [ 'type' => 'string', 'pattern' => 'arn:[^:]*:s3:[^:]*:[^:]*:accesspoint/.*', ], 'AccessPointPolicy' => [ 'type' => 'string', ], 'AccessPreview' => [ 'type' => 'structure', 'required' => [ 'id', 'analyzerArn', 'configurations', 'createdAt', 'status', ], 'members' => [ 'id' => [ 'shape' => 'AccessPreviewId', ], 'analyzerArn' => [ 'shape' => 'AnalyzerArn', ], 'configurations' => [ 'shape' => 'ConfigurationsMap', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'AccessPreviewStatus', ], 'statusReason' => [ 'shape' => 'AccessPreviewStatusReason', ], ], ], 'AccessPreviewFinding' => [ 'type' => 'structure', 'required' => [ 'id', 'resourceType', 'createdAt', 'changeType', 'status', 'resourceOwnerAccount', ], 'members' => [ 'id' => [ 'shape' => 'AccessPreviewFindingId', ], 'existingFindingId' => [ 'shape' => 'FindingId', ], 'existingFindingStatus' => [ 'shape' => 'FindingStatus', ], 'principal' => [ 'shape' => 'PrincipalMap', ], 'action' => [ 'shape' => 'ActionList', ], 'condition' => [ 'shape' => 'ConditionKeyMap', ], 'resource' => [ 'shape' => 'String', ], 'isPublic' => [ 'shape' => 'Boolean', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'changeType' => [ 'shape' => 'FindingChangeType', ], 'status' => [ 'shape' => 'FindingStatus', ], 'resourceOwnerAccount' => [ 'shape' => 'String', ], 'error' => [ 'shape' => 'String', ], 'sources' => [ 'shape' => 'FindingSourceList', ], 'resourceControlPolicyRestriction' => [ 'shape' => 'ResourceControlPolicyRestriction', ], ], ], 'AccessPreviewFindingId' => [ 'type' => 'string', ], 'AccessPreviewFindingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessPreviewFinding', ], ], 'AccessPreviewId' => [ 'type' => 'string', 'pattern' => '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', ], 'AccessPreviewStatus' => [ 'type' => 'string', 'enum' => [ 'COMPLETED', 'CREATING', 'FAILED', ], ], 'AccessPreviewStatusReason' => [ 'type' => 'structure', 'required' => [ 'code', ], 'members' => [ 'code' => [ 'shape' => 'AccessPreviewStatusReasonCode', ], ], ], 'AccessPreviewStatusReasonCode' => [ 'type' => 'string', 'enum' => [ 'INTERNAL_ERROR', 'INVALID_CONFIGURATION', ], ], 'AccessPreviewSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'analyzerArn', 'createdAt', 'status', ], 'members' => [ 'id' => [ 'shape' => 'AccessPreviewId', ], 'analyzerArn' => [ 'shape' => 'AnalyzerArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'AccessPreviewStatus', ], 'statusReason' => [ 'shape' => 'AccessPreviewStatusReason', ], ], ], 'AccessPreviewsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessPreviewSummary', ], ], 'AccessResourcesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Resource', ], 'max' => 100, 'min' => 0, ], 'AccountAggregations' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingAggregationAccountDetails', ], 'max' => 10, 'min' => 1, ], 'AccountIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'AclCanonicalId' => [ 'type' => 'string', ], 'AclGrantee' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'AclCanonicalId', ], 'uri' => [ 'shape' => 'AclUri', ], ], 'union' => true, ], 'AclPermission' => [ 'type' => 'string', 'enum' => [ 'READ', 'WRITE', 'READ_ACP', 'WRITE_ACP', 'FULL_CONTROL', ], ], 'AclUri' => [ 'type' => 'string', ], 'Action' => [ 'type' => 'string', ], 'ActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'AnalysisRule' => [ 'type' => 'structure', 'members' => [ 'exclusions' => [ 'shape' => 'AnalysisRuleCriteriaList', ], ], ], 'AnalysisRuleCriteria' => [ 'type' => 'structure', 'members' => [ 'accountIds' => [ 'shape' => 'AccountIdsList', ], 'resourceTags' => [ 'shape' => 'TagsList', ], ], ], 'AnalysisRuleCriteriaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalysisRuleCriteria', ], ], 'AnalyzedResource' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'resourceType', 'createdAt', 'analyzedAt', 'updatedAt', 'isPublic', 'resourceOwnerAccount', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'analyzedAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'isPublic' => [ 'shape' => 'Boolean', ], 'actions' => [ 'shape' => 'ActionList', ], 'sharedVia' => [ 'shape' => 'SharedViaList', ], 'status' => [ 'shape' => 'FindingStatus', ], 'resourceOwnerAccount' => [ 'shape' => 'String', ], 'error' => [ 'shape' => 'String', ], ], ], 'AnalyzedResourceSummary' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'resourceOwnerAccount', 'resourceType', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'resourceOwnerAccount' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], ], 'AnalyzedResourcesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyzedResourceSummary', ], ], 'AnalyzerArn' => [ 'type' => 'string', 'pattern' => '[^:]*:[^:]*:[^:]*:[^:]*:[^:]*:analyzer/.{1,255}', ], 'AnalyzerConfiguration' => [ 'type' => 'structure', 'members' => [ 'unusedAccess' => [ 'shape' => 'UnusedAccessConfiguration', ], 'internalAccess' => [ 'shape' => 'InternalAccessConfiguration', ], ], 'union' => true, ], 'AnalyzerStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'CREATING', 'DISABLED', 'FAILED', ], ], 'AnalyzerSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'name', 'type', 'createdAt', 'status', ], 'members' => [ 'arn' => [ 'shape' => 'AnalyzerArn', ], 'name' => [ 'shape' => 'Name', ], 'type' => [ 'shape' => 'Type', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'lastResourceAnalyzed' => [ 'shape' => 'String', ], 'lastResourceAnalyzedAt' => [ 'shape' => 'Timestamp', ], 'tags' => [ 'shape' => 'TagsMap', ], 'status' => [ 'shape' => 'AnalyzerStatus', ], 'statusReason' => [ 'shape' => 'StatusReason', ], 'configuration' => [ 'shape' => 'AnalyzerConfiguration', ], ], ], 'AnalyzersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyzerSummary', ], ], 'ApplyArchiveRuleRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', 'ruleName', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', ], 'ruleName' => [ 'shape' => 'Name', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], ], ], 'ArchiveRuleSummary' => [ 'type' => 'structure', 'required' => [ 'ruleName', 'filter', 'createdAt', 'updatedAt', ], 'members' => [ 'ruleName' => [ 'shape' => 'Name', ], 'filter' => [ 'shape' => 'FilterCriteriaMap', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'ArchiveRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ArchiveRuleSummary', ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'CancelPolicyGenerationRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], ], ], 'CancelPolicyGenerationResponse' => [ 'type' => 'structure', 'members' => [], ], 'CheckAccessNotGrantedRequest' => [ 'type' => 'structure', 'required' => [ 'policyDocument', 'access', 'policyType', ], 'members' => [ 'policyDocument' => [ 'shape' => 'AccessCheckPolicyDocument', ], 'access' => [ 'shape' => 'CheckAccessNotGrantedRequestAccessList', ], 'policyType' => [ 'shape' => 'AccessCheckPolicyType', ], ], ], 'CheckAccessNotGrantedRequestAccessList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Access', ], 'max' => 1, 'min' => 0, ], 'CheckAccessNotGrantedResponse' => [ 'type' => 'structure', 'members' => [ 'result' => [ 'shape' => 'CheckAccessNotGrantedResult', ], 'message' => [ 'shape' => 'String', ], 'reasons' => [ 'shape' => 'ReasonSummaryList', ], ], ], 'CheckAccessNotGrantedResult' => [ 'type' => 'string', 'enum' => [ 'PASS', 'FAIL', ], ], 'CheckNoNewAccessRequest' => [ 'type' => 'structure', 'required' => [ 'newPolicyDocument', 'existingPolicyDocument', 'policyType', ], 'members' => [ 'newPolicyDocument' => [ 'shape' => 'AccessCheckPolicyDocument', ], 'existingPolicyDocument' => [ 'shape' => 'AccessCheckPolicyDocument', ], 'policyType' => [ 'shape' => 'AccessCheckPolicyType', ], ], ], 'CheckNoNewAccessResponse' => [ 'type' => 'structure', 'members' => [ 'result' => [ 'shape' => 'CheckNoNewAccessResult', ], 'message' => [ 'shape' => 'String', ], 'reasons' => [ 'shape' => 'ReasonSummaryList', ], ], ], 'CheckNoNewAccessResult' => [ 'type' => 'string', 'enum' => [ 'PASS', 'FAIL', ], ], 'CheckNoPublicAccessRequest' => [ 'type' => 'structure', 'required' => [ 'policyDocument', 'resourceType', ], 'members' => [ 'policyDocument' => [ 'shape' => 'AccessCheckPolicyDocument', ], 'resourceType' => [ 'shape' => 'AccessCheckResourceType', ], ], ], 'CheckNoPublicAccessResponse' => [ 'type' => 'structure', 'members' => [ 'result' => [ 'shape' => 'CheckNoPublicAccessResult', ], 'message' => [ 'shape' => 'String', ], 'reasons' => [ 'shape' => 'ReasonSummaryList', ], ], ], 'CheckNoPublicAccessResult' => [ 'type' => 'string', 'enum' => [ 'PASS', 'FAIL', ], ], 'CloudTrailArn' => [ 'type' => 'string', 'pattern' => 'arn:[^:]*:cloudtrail:[^:]*:[^:]*:trail/.{1,576}', ], 'CloudTrailDetails' => [ 'type' => 'structure', 'required' => [ 'trails', 'accessRole', 'startTime', ], 'members' => [ 'trails' => [ 'shape' => 'TrailList', ], 'accessRole' => [ 'shape' => 'RoleArn', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], ], ], 'CloudTrailProperties' => [ 'type' => 'structure', 'required' => [ 'trailProperties', 'startTime', 'endTime', ], 'members' => [ 'trailProperties' => [ 'shape' => 'TrailPropertiesList', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], ], ], 'ConditionKeyMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'Configuration' => [ 'type' => 'structure', 'members' => [ 'ebsSnapshot' => [ 'shape' => 'EbsSnapshotConfiguration', ], 'ecrRepository' => [ 'shape' => 'EcrRepositoryConfiguration', ], 'iamRole' => [ 'shape' => 'IamRoleConfiguration', ], 'efsFileSystem' => [ 'shape' => 'EfsFileSystemConfiguration', ], 'kmsKey' => [ 'shape' => 'KmsKeyConfiguration', ], 'rdsDbClusterSnapshot' => [ 'shape' => 'RdsDbClusterSnapshotConfiguration', ], 'rdsDbSnapshot' => [ 'shape' => 'RdsDbSnapshotConfiguration', ], 'secretsManagerSecret' => [ 'shape' => 'SecretsManagerSecretConfiguration', ], 's3Bucket' => [ 'shape' => 'S3BucketConfiguration', ], 'snsTopic' => [ 'shape' => 'SnsTopicConfiguration', ], 'sqsQueue' => [ 'shape' => 'SqsQueueConfiguration', ], 's3ExpressDirectoryBucket' => [ 'shape' => 'S3ExpressDirectoryBucketConfiguration', ], 'dynamodbStream' => [ 'shape' => 'DynamodbStreamConfiguration', ], 'dynamodbTable' => [ 'shape' => 'DynamodbTableConfiguration', ], ], 'union' => true, ], 'ConfigurationsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ConfigurationsMapKey', ], 'value' => [ 'shape' => 'Configuration', ], ], 'ConfigurationsMapKey' => [ 'type' => 'string', ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateAccessPreviewRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', 'configurations', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', ], 'configurations' => [ 'shape' => 'ConfigurationsMap', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], ], ], 'CreateAccessPreviewResponse' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'AccessPreviewId', ], ], ], 'CreateAnalyzerRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerName', 'type', ], 'members' => [ 'analyzerName' => [ 'shape' => 'Name', ], 'type' => [ 'shape' => 'Type', ], 'archiveRules' => [ 'shape' => 'InlineArchiveRulesList', ], 'tags' => [ 'shape' => 'TagsMap', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'configuration' => [ 'shape' => 'AnalyzerConfiguration', ], ], ], 'CreateAnalyzerResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'AnalyzerArn', ], ], ], 'CreateArchiveRuleRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerName', 'ruleName', 'filter', ], 'members' => [ 'analyzerName' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'analyzerName', ], 'ruleName' => [ 'shape' => 'Name', ], 'filter' => [ 'shape' => 'FilterCriteriaMap', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], ], ], 'Criterion' => [ 'type' => 'structure', 'members' => [ 'eq' => [ 'shape' => 'ValueList', ], 'neq' => [ 'shape' => 'ValueList', ], 'contains' => [ 'shape' => 'ValueList', ], 'exists' => [ 'shape' => 'Boolean', ], ], ], 'DeleteAnalyzerRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerName', ], 'members' => [ 'analyzerName' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'analyzerName', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeleteArchiveRuleRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerName', 'ruleName', ], 'members' => [ 'analyzerName' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'analyzerName', ], 'ruleName' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'ruleName', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DynamodbStreamConfiguration' => [ 'type' => 'structure', 'members' => [ 'streamPolicy' => [ 'shape' => 'DynamodbStreamPolicy', ], ], ], 'DynamodbStreamPolicy' => [ 'type' => 'string', ], 'DynamodbTableConfiguration' => [ 'type' => 'structure', 'members' => [ 'tablePolicy' => [ 'shape' => 'DynamodbTablePolicy', ], ], ], 'DynamodbTablePolicy' => [ 'type' => 'string', ], 'EbsGroup' => [ 'type' => 'string', ], 'EbsGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EbsGroup', ], ], 'EbsSnapshotConfiguration' => [ 'type' => 'structure', 'members' => [ 'userIds' => [ 'shape' => 'EbsUserIdList', ], 'groups' => [ 'shape' => 'EbsGroupList', ], 'kmsKeyId' => [ 'shape' => 'EbsSnapshotDataEncryptionKeyId', ], ], ], 'EbsSnapshotDataEncryptionKeyId' => [ 'type' => 'string', ], 'EbsUserId' => [ 'type' => 'string', ], 'EbsUserIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EbsUserId', ], ], 'EcrRepositoryConfiguration' => [ 'type' => 'structure', 'members' => [ 'repositoryPolicy' => [ 'shape' => 'EcrRepositoryPolicy', ], ], ], 'EcrRepositoryPolicy' => [ 'type' => 'string', ], 'EfsFileSystemConfiguration' => [ 'type' => 'structure', 'members' => [ 'fileSystemPolicy' => [ 'shape' => 'EfsFileSystemPolicy', ], ], ], 'EfsFileSystemPolicy' => [ 'type' => 'string', ], 'ExternalAccessDetails' => [ 'type' => 'structure', 'required' => [ 'condition', ], 'members' => [ 'action' => [ 'shape' => 'ActionList', ], 'condition' => [ 'shape' => 'ConditionKeyMap', ], 'isPublic' => [ 'shape' => 'Boolean', ], 'principal' => [ 'shape' => 'PrincipalMap', ], 'sources' => [ 'shape' => 'FindingSourceList', ], 'resourceControlPolicyRestriction' => [ 'shape' => 'ResourceControlPolicyRestriction', ], ], ], 'ExternalAccessFindingsStatistics' => [ 'type' => 'structure', 'members' => [ 'resourceTypeStatistics' => [ 'shape' => 'ResourceTypeStatisticsMap', ], 'totalActiveFindings' => [ 'shape' => 'Integer', ], 'totalArchivedFindings' => [ 'shape' => 'Integer', ], 'totalResolvedFindings' => [ 'shape' => 'Integer', ], ], ], 'FilterCriteriaMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'Criterion', ], ], 'Finding' => [ 'type' => 'structure', 'required' => [ 'id', 'resourceType', 'condition', 'createdAt', 'analyzedAt', 'updatedAt', 'status', 'resourceOwnerAccount', ], 'members' => [ 'id' => [ 'shape' => 'FindingId', ], 'principal' => [ 'shape' => 'PrincipalMap', ], 'action' => [ 'shape' => 'ActionList', ], 'resource' => [ 'shape' => 'String', ], 'isPublic' => [ 'shape' => 'Boolean', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'condition' => [ 'shape' => 'ConditionKeyMap', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'analyzedAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'FindingStatus', ], 'resourceOwnerAccount' => [ 'shape' => 'String', ], 'error' => [ 'shape' => 'String', ], 'sources' => [ 'shape' => 'FindingSourceList', ], 'resourceControlPolicyRestriction' => [ 'shape' => 'ResourceControlPolicyRestriction', ], ], ], 'FindingAggregationAccountDetails' => [ 'type' => 'structure', 'members' => [ 'account' => [ 'shape' => 'String', ], 'numberOfActiveFindings' => [ 'shape' => 'Integer', ], 'details' => [ 'shape' => 'FindingAggregationAccountDetailsMap', ], ], ], 'FindingAggregationAccountDetailsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'Integer', ], ], 'FindingChangeType' => [ 'type' => 'string', 'enum' => [ 'CHANGED', 'NEW', 'UNCHANGED', ], ], 'FindingDetails' => [ 'type' => 'structure', 'members' => [ 'internalAccessDetails' => [ 'shape' => 'InternalAccessDetails', ], 'externalAccessDetails' => [ 'shape' => 'ExternalAccessDetails', ], 'unusedPermissionDetails' => [ 'shape' => 'UnusedPermissionDetails', ], 'unusedIamUserAccessKeyDetails' => [ 'shape' => 'UnusedIamUserAccessKeyDetails', ], 'unusedIamRoleDetails' => [ 'shape' => 'UnusedIamRoleDetails', ], 'unusedIamUserPasswordDetails' => [ 'shape' => 'UnusedIamUserPasswordDetails', ], ], 'union' => true, ], 'FindingDetailsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingDetails', ], ], 'FindingId' => [ 'type' => 'string', ], 'FindingIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingId', ], ], 'FindingSource' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'FindingSourceType', ], 'detail' => [ 'shape' => 'FindingSourceDetail', ], ], ], 'FindingSourceDetail' => [ 'type' => 'structure', 'members' => [ 'accessPointArn' => [ 'shape' => 'String', ], 'accessPointAccount' => [ 'shape' => 'String', ], ], ], 'FindingSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingSource', ], ], 'FindingSourceType' => [ 'type' => 'string', 'enum' => [ 'POLICY', 'BUCKET_ACL', 'S3_ACCESS_POINT', 'S3_ACCESS_POINT_ACCOUNT', ], ], 'FindingStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'ARCHIVED', 'RESOLVED', ], ], 'FindingStatusUpdate' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'ARCHIVED', ], ], 'FindingSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'resourceType', 'condition', 'createdAt', 'analyzedAt', 'updatedAt', 'status', 'resourceOwnerAccount', ], 'members' => [ 'id' => [ 'shape' => 'FindingId', ], 'principal' => [ 'shape' => 'PrincipalMap', ], 'action' => [ 'shape' => 'ActionList', ], 'resource' => [ 'shape' => 'String', ], 'isPublic' => [ 'shape' => 'Boolean', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'condition' => [ 'shape' => 'ConditionKeyMap', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'analyzedAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'FindingStatus', ], 'resourceOwnerAccount' => [ 'shape' => 'String', ], 'error' => [ 'shape' => 'String', ], 'sources' => [ 'shape' => 'FindingSourceList', ], 'resourceControlPolicyRestriction' => [ 'shape' => 'ResourceControlPolicyRestriction', ], ], ], 'FindingSummaryV2' => [ 'type' => 'structure', 'required' => [ 'analyzedAt', 'createdAt', 'id', 'resourceType', 'resourceOwnerAccount', 'status', 'updatedAt', ], 'members' => [ 'analyzedAt' => [ 'shape' => 'Timestamp', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'error' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'FindingId', ], 'resource' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'resourceOwnerAccount' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'FindingStatus', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'findingType' => [ 'shape' => 'FindingType', ], ], ], 'FindingType' => [ 'type' => 'string', 'enum' => [ 'ExternalAccess', 'UnusedIAMRole', 'UnusedIAMUserAccessKey', 'UnusedIAMUserPassword', 'UnusedPermission', 'InternalAccess', ], ], 'FindingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingSummary', ], ], 'FindingsListV2' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingSummaryV2', ], ], 'FindingsStatistics' => [ 'type' => 'structure', 'members' => [ 'externalAccessFindingsStatistics' => [ 'shape' => 'ExternalAccessFindingsStatistics', ], 'internalAccessFindingsStatistics' => [ 'shape' => 'InternalAccessFindingsStatistics', ], 'unusedAccessFindingsStatistics' => [ 'shape' => 'UnusedAccessFindingsStatistics', ], ], 'union' => true, ], 'FindingsStatisticsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingsStatistics', ], ], 'GenerateFindingRecommendationRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', 'id', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', 'location' => 'querystring', 'locationName' => 'analyzerArn', ], 'id' => [ 'shape' => 'GenerateFindingRecommendationRequestIdString', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GenerateFindingRecommendationRequestIdString' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'GeneratedPolicy' => [ 'type' => 'structure', 'required' => [ 'policy', ], 'members' => [ 'policy' => [ 'shape' => 'String', ], ], ], 'GeneratedPolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GeneratedPolicy', ], ], 'GeneratedPolicyProperties' => [ 'type' => 'structure', 'required' => [ 'principalArn', ], 'members' => [ 'isComplete' => [ 'shape' => 'Boolean', ], 'principalArn' => [ 'shape' => 'PrincipalArn', ], 'cloudTrailProperties' => [ 'shape' => 'CloudTrailProperties', ], ], ], 'GeneratedPolicyResult' => [ 'type' => 'structure', 'required' => [ 'properties', ], 'members' => [ 'properties' => [ 'shape' => 'GeneratedPolicyProperties', ], 'generatedPolicies' => [ 'shape' => 'GeneratedPolicyList', ], ], ], 'GetAccessPreviewRequest' => [ 'type' => 'structure', 'required' => [ 'accessPreviewId', 'analyzerArn', ], 'members' => [ 'accessPreviewId' => [ 'shape' => 'AccessPreviewId', 'location' => 'uri', 'locationName' => 'accessPreviewId', ], 'analyzerArn' => [ 'shape' => 'AnalyzerArn', 'location' => 'querystring', 'locationName' => 'analyzerArn', ], ], ], 'GetAccessPreviewResponse' => [ 'type' => 'structure', 'required' => [ 'accessPreview', ], 'members' => [ 'accessPreview' => [ 'shape' => 'AccessPreview', ], ], ], 'GetAnalyzedResourceRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', 'resourceArn', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', 'location' => 'querystring', 'locationName' => 'analyzerArn', ], 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'querystring', 'locationName' => 'resourceArn', ], ], ], 'GetAnalyzedResourceResponse' => [ 'type' => 'structure', 'members' => [ 'resource' => [ 'shape' => 'AnalyzedResource', ], ], ], 'GetAnalyzerRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerName', ], 'members' => [ 'analyzerName' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'analyzerName', ], ], ], 'GetAnalyzerResponse' => [ 'type' => 'structure', 'required' => [ 'analyzer', ], 'members' => [ 'analyzer' => [ 'shape' => 'AnalyzerSummary', ], ], ], 'GetArchiveRuleRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerName', 'ruleName', ], 'members' => [ 'analyzerName' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'analyzerName', ], 'ruleName' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'ruleName', ], ], ], 'GetArchiveRuleResponse' => [ 'type' => 'structure', 'required' => [ 'archiveRule', ], 'members' => [ 'archiveRule' => [ 'shape' => 'ArchiveRuleSummary', ], ], ], 'GetFindingRecommendationRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', 'id', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', 'location' => 'querystring', 'locationName' => 'analyzerArn', ], 'id' => [ 'shape' => 'GetFindingRecommendationRequestIdString', 'location' => 'uri', 'locationName' => 'id', ], 'maxResults' => [ 'shape' => 'GetFindingRecommendationRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetFindingRecommendationRequestIdString' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'GetFindingRecommendationRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'GetFindingRecommendationResponse' => [ 'type' => 'structure', 'required' => [ 'startedAt', 'resourceArn', 'recommendationType', 'status', ], 'members' => [ 'startedAt' => [ 'shape' => 'Timestamp', ], 'completedAt' => [ 'shape' => 'Timestamp', ], 'nextToken' => [ 'shape' => 'Token', ], 'error' => [ 'shape' => 'RecommendationError', ], 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'recommendedSteps' => [ 'shape' => 'RecommendedStepList', ], 'recommendationType' => [ 'shape' => 'RecommendationType', ], 'status' => [ 'shape' => 'Status', ], ], ], 'GetFindingRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', 'id', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', 'location' => 'querystring', 'locationName' => 'analyzerArn', ], 'id' => [ 'shape' => 'FindingId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetFindingResponse' => [ 'type' => 'structure', 'members' => [ 'finding' => [ 'shape' => 'Finding', ], ], ], 'GetFindingV2Request' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', 'id', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', 'location' => 'querystring', 'locationName' => 'analyzerArn', ], 'id' => [ 'shape' => 'FindingId', 'location' => 'uri', 'locationName' => 'id', ], 'maxResults' => [ 'shape' => 'Integer', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetFindingV2Response' => [ 'type' => 'structure', 'required' => [ 'analyzedAt', 'createdAt', 'id', 'resourceType', 'resourceOwnerAccount', 'status', 'updatedAt', 'findingDetails', ], 'members' => [ 'analyzedAt' => [ 'shape' => 'Timestamp', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'error' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'FindingId', ], 'nextToken' => [ 'shape' => 'Token', ], 'resource' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'resourceOwnerAccount' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'FindingStatus', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'findingDetails' => [ 'shape' => 'FindingDetailsList', ], 'findingType' => [ 'shape' => 'FindingType', ], ], ], 'GetFindingsStatisticsRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', ], ], ], 'GetFindingsStatisticsResponse' => [ 'type' => 'structure', 'members' => [ 'findingsStatistics' => [ 'shape' => 'FindingsStatisticsList', ], 'lastUpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'GetGeneratedPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'includeResourcePlaceholders' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'includeResourcePlaceholders', ], 'includeServiceLevelTemplate' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'includeServiceLevelTemplate', ], ], ], 'GetGeneratedPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'jobDetails', 'generatedPolicyResult', ], 'members' => [ 'jobDetails' => [ 'shape' => 'JobDetails', ], 'generatedPolicyResult' => [ 'shape' => 'GeneratedPolicyResult', ], ], ], 'GranteePrincipal' => [ 'type' => 'string', ], 'IamRoleConfiguration' => [ 'type' => 'structure', 'members' => [ 'trustPolicy' => [ 'shape' => 'IamTrustPolicy', ], ], ], 'IamTrustPolicy' => [ 'type' => 'string', ], 'InlineArchiveRule' => [ 'type' => 'structure', 'required' => [ 'ruleName', 'filter', ], 'members' => [ 'ruleName' => [ 'shape' => 'Name', ], 'filter' => [ 'shape' => 'FilterCriteriaMap', ], ], ], 'InlineArchiveRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InlineArchiveRule', ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalAccessAnalysisRule' => [ 'type' => 'structure', 'members' => [ 'inclusions' => [ 'shape' => 'InternalAccessAnalysisRuleCriteriaList', ], ], ], 'InternalAccessAnalysisRuleCriteria' => [ 'type' => 'structure', 'members' => [ 'accountIds' => [ 'shape' => 'AccountIdsList', ], 'resourceTypes' => [ 'shape' => 'ResourceTypeList', ], 'resourceArns' => [ 'shape' => 'ResourceArnsList', ], ], ], 'InternalAccessAnalysisRuleCriteriaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InternalAccessAnalysisRuleCriteria', ], ], 'InternalAccessConfiguration' => [ 'type' => 'structure', 'members' => [ 'analysisRule' => [ 'shape' => 'InternalAccessAnalysisRule', ], ], ], 'InternalAccessDetails' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'ActionList', ], 'condition' => [ 'shape' => 'ConditionKeyMap', ], 'principal' => [ 'shape' => 'PrincipalMap', ], 'principalOwnerAccount' => [ 'shape' => 'String', ], 'accessType' => [ 'shape' => 'InternalAccessType', ], 'principalType' => [ 'shape' => 'PrincipalType', ], 'sources' => [ 'shape' => 'FindingSourceList', ], 'resourceControlPolicyRestriction' => [ 'shape' => 'ResourceControlPolicyRestriction', ], 'serviceControlPolicyRestriction' => [ 'shape' => 'ServiceControlPolicyRestriction', ], ], ], 'InternalAccessFindingsStatistics' => [ 'type' => 'structure', 'members' => [ 'resourceTypeStatistics' => [ 'shape' => 'InternalAccessResourceTypeStatisticsMap', ], 'totalActiveFindings' => [ 'shape' => 'Integer', ], 'totalArchivedFindings' => [ 'shape' => 'Integer', ], 'totalResolvedFindings' => [ 'shape' => 'Integer', ], ], ], 'InternalAccessResourceTypeDetails' => [ 'type' => 'structure', 'members' => [ 'totalActiveFindings' => [ 'shape' => 'Integer', ], 'totalResolvedFindings' => [ 'shape' => 'Integer', ], 'totalArchivedFindings' => [ 'shape' => 'Integer', ], ], ], 'InternalAccessResourceTypeStatisticsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ResourceType', ], 'value' => [ 'shape' => 'InternalAccessResourceTypeDetails', ], ], 'InternalAccessType' => [ 'type' => 'string', 'enum' => [ 'INTRA_ACCOUNT', 'INTRA_ORG', ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'InternetConfiguration' => [ 'type' => 'structure', 'members' => [], ], 'InvalidParameterException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'IssueCode' => [ 'type' => 'string', ], 'IssuingAccount' => [ 'type' => 'string', ], 'JobDetails' => [ 'type' => 'structure', 'required' => [ 'jobId', 'status', 'startedOn', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'status' => [ 'shape' => 'JobStatus', ], 'startedOn' => [ 'shape' => 'Timestamp', ], 'completedOn' => [ 'shape' => 'Timestamp', ], 'jobError' => [ 'shape' => 'JobError', ], ], ], 'JobError' => [ 'type' => 'structure', 'required' => [ 'code', 'message', ], 'members' => [ 'code' => [ 'shape' => 'JobErrorCode', ], 'message' => [ 'shape' => 'String', ], ], ], 'JobErrorCode' => [ 'type' => 'string', 'enum' => [ 'AUTHORIZATION_ERROR', 'RESOURCE_NOT_FOUND_ERROR', 'SERVICE_QUOTA_EXCEEDED_ERROR', 'SERVICE_ERROR', ], ], 'JobId' => [ 'type' => 'string', ], 'JobStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'SUCCEEDED', 'FAILED', 'CANCELED', ], ], 'KmsConstraintsKey' => [ 'type' => 'string', ], 'KmsConstraintsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'KmsConstraintsKey', ], 'value' => [ 'shape' => 'KmsConstraintsValue', ], ], 'KmsConstraintsValue' => [ 'type' => 'string', ], 'KmsGrantConfiguration' => [ 'type' => 'structure', 'required' => [ 'operations', 'granteePrincipal', 'issuingAccount', ], 'members' => [ 'operations' => [ 'shape' => 'KmsGrantOperationsList', ], 'granteePrincipal' => [ 'shape' => 'GranteePrincipal', ], 'retiringPrincipal' => [ 'shape' => 'RetiringPrincipal', ], 'constraints' => [ 'shape' => 'KmsGrantConstraints', ], 'issuingAccount' => [ 'shape' => 'IssuingAccount', ], ], ], 'KmsGrantConfigurationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KmsGrantConfiguration', ], ], 'KmsGrantConstraints' => [ 'type' => 'structure', 'members' => [ 'encryptionContextEquals' => [ 'shape' => 'KmsConstraintsMap', ], 'encryptionContextSubset' => [ 'shape' => 'KmsConstraintsMap', ], ], ], 'KmsGrantOperation' => [ 'type' => 'string', 'enum' => [ 'CreateGrant', 'Decrypt', 'DescribeKey', 'Encrypt', 'GenerateDataKey', 'GenerateDataKeyPair', 'GenerateDataKeyPairWithoutPlaintext', 'GenerateDataKeyWithoutPlaintext', 'GetPublicKey', 'ReEncryptFrom', 'ReEncryptTo', 'RetireGrant', 'Sign', 'Verify', ], ], 'KmsGrantOperationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KmsGrantOperation', ], ], 'KmsKeyConfiguration' => [ 'type' => 'structure', 'members' => [ 'keyPolicies' => [ 'shape' => 'KmsKeyPoliciesMap', ], 'grants' => [ 'shape' => 'KmsGrantConfigurationsList', ], ], ], 'KmsKeyPoliciesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'PolicyName', ], 'value' => [ 'shape' => 'KmsKeyPolicy', ], ], 'KmsKeyPolicy' => [ 'type' => 'string', ], 'LearnMoreLink' => [ 'type' => 'string', ], 'ListAccessPreviewFindingsRequest' => [ 'type' => 'structure', 'required' => [ 'accessPreviewId', 'analyzerArn', ], 'members' => [ 'accessPreviewId' => [ 'shape' => 'AccessPreviewId', 'location' => 'uri', 'locationName' => 'accessPreviewId', ], 'analyzerArn' => [ 'shape' => 'AnalyzerArn', ], 'filter' => [ 'shape' => 'FilterCriteriaMap', ], 'nextToken' => [ 'shape' => 'Token', ], 'maxResults' => [ 'shape' => 'Integer', ], ], ], 'ListAccessPreviewFindingsResponse' => [ 'type' => 'structure', 'required' => [ 'findings', ], 'members' => [ 'findings' => [ 'shape' => 'AccessPreviewFindingsList', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListAccessPreviewsRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', 'location' => 'querystring', 'locationName' => 'analyzerArn', ], 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'Integer', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAccessPreviewsResponse' => [ 'type' => 'structure', 'required' => [ 'accessPreviews', ], 'members' => [ 'accessPreviews' => [ 'shape' => 'AccessPreviewsList', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListAnalyzedResourcesRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'nextToken' => [ 'shape' => 'Token', ], 'maxResults' => [ 'shape' => 'Integer', ], ], ], 'ListAnalyzedResourcesResponse' => [ 'type' => 'structure', 'required' => [ 'analyzedResources', ], 'members' => [ 'analyzedResources' => [ 'shape' => 'AnalyzedResourcesList', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListAnalyzersRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'Integer', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'type' => [ 'shape' => 'Type', 'location' => 'querystring', 'locationName' => 'type', ], ], ], 'ListAnalyzersResponse' => [ 'type' => 'structure', 'required' => [ 'analyzers', ], 'members' => [ 'analyzers' => [ 'shape' => 'AnalyzersList', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListArchiveRulesRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerName', ], 'members' => [ 'analyzerName' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'analyzerName', ], 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'Integer', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListArchiveRulesResponse' => [ 'type' => 'structure', 'required' => [ 'archiveRules', ], 'members' => [ 'archiveRules' => [ 'shape' => 'ArchiveRulesList', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListFindingsRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', ], 'filter' => [ 'shape' => 'FilterCriteriaMap', ], 'sort' => [ 'shape' => 'SortCriteria', ], 'nextToken' => [ 'shape' => 'Token', ], 'maxResults' => [ 'shape' => 'Integer', ], ], ], 'ListFindingsResponse' => [ 'type' => 'structure', 'required' => [ 'findings', ], 'members' => [ 'findings' => [ 'shape' => 'FindingsList', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListFindingsV2Request' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', ], 'filter' => [ 'shape' => 'FilterCriteriaMap', ], 'maxResults' => [ 'shape' => 'Integer', ], 'nextToken' => [ 'shape' => 'Token', ], 'sort' => [ 'shape' => 'SortCriteria', ], ], ], 'ListFindingsV2Response' => [ 'type' => 'structure', 'required' => [ 'findings', ], 'members' => [ 'findings' => [ 'shape' => 'FindingsListV2', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListPolicyGenerationsRequest' => [ 'type' => 'structure', 'members' => [ 'principalArn' => [ 'shape' => 'PrincipalArn', 'location' => 'querystring', 'locationName' => 'principalArn', ], 'maxResults' => [ 'shape' => 'ListPolicyGenerationsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListPolicyGenerationsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'ListPolicyGenerationsResponse' => [ 'type' => 'structure', 'required' => [ 'policyGenerations', ], 'members' => [ 'policyGenerations' => [ 'shape' => 'PolicyGenerationList', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'Locale' => [ 'type' => 'string', 'enum' => [ 'DE', 'EN', 'ES', 'FR', 'IT', 'JA', 'KO', 'PT_BR', 'ZH_CN', 'ZH_TW', ], ], 'Location' => [ 'type' => 'structure', 'required' => [ 'path', 'span', ], 'members' => [ 'path' => [ 'shape' => 'PathElementList', ], 'span' => [ 'shape' => 'Span', ], ], ], 'LocationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Location', ], ], 'Name' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[A-Za-z][A-Za-z0-9_.-]*', ], 'NetworkOriginConfiguration' => [ 'type' => 'structure', 'members' => [ 'vpcConfiguration' => [ 'shape' => 'VpcConfiguration', ], 'internetConfiguration' => [ 'shape' => 'InternetConfiguration', ], ], 'union' => true, ], 'OrderBy' => [ 'type' => 'string', 'enum' => [ 'ASC', 'DESC', ], ], 'PathElement' => [ 'type' => 'structure', 'members' => [ 'index' => [ 'shape' => 'Integer', ], 'key' => [ 'shape' => 'String', ], 'substring' => [ 'shape' => 'Substring', ], 'value' => [ 'shape' => 'String', ], ], 'union' => true, ], 'PathElementList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PathElement', ], ], 'PolicyDocument' => [ 'type' => 'string', ], 'PolicyGeneration' => [ 'type' => 'structure', 'required' => [ 'jobId', 'principalArn', 'status', 'startedOn', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'principalArn' => [ 'shape' => 'PrincipalArn', ], 'status' => [ 'shape' => 'JobStatus', ], 'startedOn' => [ 'shape' => 'Timestamp', ], 'completedOn' => [ 'shape' => 'Timestamp', ], ], ], 'PolicyGenerationDetails' => [ 'type' => 'structure', 'required' => [ 'principalArn', ], 'members' => [ 'principalArn' => [ 'shape' => 'PrincipalArn', ], ], ], 'PolicyGenerationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolicyGeneration', ], ], 'PolicyName' => [ 'type' => 'string', ], 'PolicyType' => [ 'type' => 'string', 'enum' => [ 'IDENTITY_POLICY', 'RESOURCE_POLICY', 'SERVICE_CONTROL_POLICY', 'RESOURCE_CONTROL_POLICY', ], ], 'Position' => [ 'type' => 'structure', 'required' => [ 'line', 'column', 'offset', ], 'members' => [ 'line' => [ 'shape' => 'Integer', ], 'column' => [ 'shape' => 'Integer', ], 'offset' => [ 'shape' => 'Integer', ], ], ], 'PrincipalArn' => [ 'type' => 'string', 'pattern' => 'arn:[^:]*:iam::[^:]*:(role|user)/.{1,576}', ], 'PrincipalMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'PrincipalType' => [ 'type' => 'string', 'enum' => [ 'IAM_ROLE', 'IAM_USER', ], ], 'RdsDbClusterSnapshotAccountId' => [ 'type' => 'string', ], 'RdsDbClusterSnapshotAccountIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RdsDbClusterSnapshotAccountId', ], ], 'RdsDbClusterSnapshotAttributeName' => [ 'type' => 'string', ], 'RdsDbClusterSnapshotAttributeValue' => [ 'type' => 'structure', 'members' => [ 'accountIds' => [ 'shape' => 'RdsDbClusterSnapshotAccountIdsList', ], ], 'union' => true, ], 'RdsDbClusterSnapshotAttributesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'RdsDbClusterSnapshotAttributeName', ], 'value' => [ 'shape' => 'RdsDbClusterSnapshotAttributeValue', ], ], 'RdsDbClusterSnapshotConfiguration' => [ 'type' => 'structure', 'members' => [ 'attributes' => [ 'shape' => 'RdsDbClusterSnapshotAttributesMap', ], 'kmsKeyId' => [ 'shape' => 'RdsDbClusterSnapshotKmsKeyId', ], ], ], 'RdsDbClusterSnapshotKmsKeyId' => [ 'type' => 'string', ], 'RdsDbSnapshotAccountId' => [ 'type' => 'string', ], 'RdsDbSnapshotAccountIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RdsDbSnapshotAccountId', ], ], 'RdsDbSnapshotAttributeName' => [ 'type' => 'string', ], 'RdsDbSnapshotAttributeValue' => [ 'type' => 'structure', 'members' => [ 'accountIds' => [ 'shape' => 'RdsDbSnapshotAccountIdsList', ], ], 'union' => true, ], 'RdsDbSnapshotAttributesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'RdsDbSnapshotAttributeName', ], 'value' => [ 'shape' => 'RdsDbSnapshotAttributeValue', ], ], 'RdsDbSnapshotConfiguration' => [ 'type' => 'structure', 'members' => [ 'attributes' => [ 'shape' => 'RdsDbSnapshotAttributesMap', ], 'kmsKeyId' => [ 'shape' => 'RdsDbSnapshotKmsKeyId', ], ], ], 'RdsDbSnapshotKmsKeyId' => [ 'type' => 'string', ], 'ReasonCode' => [ 'type' => 'string', 'enum' => [ 'AWS_SERVICE_ACCESS_DISABLED', 'DELEGATED_ADMINISTRATOR_DEREGISTERED', 'ORGANIZATION_DELETED', 'SERVICE_LINKED_ROLE_CREATION_FAILED', ], ], 'ReasonSummary' => [ 'type' => 'structure', 'members' => [ 'description' => [ 'shape' => 'String', ], 'statementIndex' => [ 'shape' => 'Integer', ], 'statementId' => [ 'shape' => 'String', ], ], ], 'ReasonSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReasonSummary', ], ], 'RecommendationError' => [ 'type' => 'structure', 'required' => [ 'code', 'message', ], 'members' => [ 'code' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'RecommendationType' => [ 'type' => 'string', 'enum' => [ 'UnusedPermissionRecommendation', ], ], 'RecommendedRemediationAction' => [ 'type' => 'string', 'enum' => [ 'CREATE_POLICY', 'DETACH_POLICY', ], ], 'RecommendedStep' => [ 'type' => 'structure', 'members' => [ 'unusedPermissionsRecommendedStep' => [ 'shape' => 'UnusedPermissionsRecommendedStep', ], ], 'union' => true, ], 'RecommendedStepList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendedStep', ], ], 'RegionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Resource' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'ResourceArn' => [ 'type' => 'string', 'pattern' => 'arn:[^:]*:[^:]*:[^:]*:[^:]*:.*', ], 'ResourceArnsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ResourceControlPolicyRestriction' => [ 'type' => 'string', 'enum' => [ 'APPLICABLE', 'FAILED_TO_EVALUATE_RCP', 'NOT_APPLICABLE', 'APPLIED', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'AWS::S3::Bucket', 'AWS::IAM::Role', 'AWS::SQS::Queue', 'AWS::Lambda::Function', 'AWS::Lambda::LayerVersion', 'AWS::KMS::Key', 'AWS::SecretsManager::Secret', 'AWS::EFS::FileSystem', 'AWS::EC2::Snapshot', 'AWS::ECR::Repository', 'AWS::RDS::DBSnapshot', 'AWS::RDS::DBClusterSnapshot', 'AWS::SNS::Topic', 'AWS::S3Express::DirectoryBucket', 'AWS::DynamoDB::Table', 'AWS::DynamoDB::Stream', 'AWS::IAM::User', ], ], 'ResourceTypeDetails' => [ 'type' => 'structure', 'members' => [ 'totalActivePublic' => [ 'shape' => 'Integer', ], 'totalActiveCrossAccount' => [ 'shape' => 'Integer', ], ], ], 'ResourceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceType', ], ], 'ResourceTypeStatisticsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ResourceType', ], 'value' => [ 'shape' => 'ResourceTypeDetails', ], ], 'RetiringPrincipal' => [ 'type' => 'string', ], 'RoleArn' => [ 'type' => 'string', 'pattern' => 'arn:[^:]*:iam::[^:]*:role/.{1,576}', ], 'S3AccessPointConfiguration' => [ 'type' => 'structure', 'members' => [ 'accessPointPolicy' => [ 'shape' => 'AccessPointPolicy', ], 'publicAccessBlock' => [ 'shape' => 'S3PublicAccessBlockConfiguration', ], 'networkOrigin' => [ 'shape' => 'NetworkOriginConfiguration', ], ], ], 'S3AccessPointConfigurationsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'AccessPointArn', ], 'value' => [ 'shape' => 'S3AccessPointConfiguration', ], ], 'S3BucketAclGrantConfiguration' => [ 'type' => 'structure', 'required' => [ 'permission', 'grantee', ], 'members' => [ 'permission' => [ 'shape' => 'AclPermission', ], 'grantee' => [ 'shape' => 'AclGrantee', ], ], ], 'S3BucketAclGrantConfigurationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3BucketAclGrantConfiguration', ], ], 'S3BucketConfiguration' => [ 'type' => 'structure', 'members' => [ 'bucketPolicy' => [ 'shape' => 'S3BucketPolicy', ], 'bucketAclGrants' => [ 'shape' => 'S3BucketAclGrantConfigurationsList', ], 'bucketPublicAccessBlock' => [ 'shape' => 'S3PublicAccessBlockConfiguration', ], 'accessPoints' => [ 'shape' => 'S3AccessPointConfigurationsMap', ], ], ], 'S3BucketPolicy' => [ 'type' => 'string', ], 'S3ExpressDirectoryAccessPointArn' => [ 'type' => 'string', 'pattern' => 'arn:[^:]*:s3express:[^:]*:[^:]*:accesspoint/.*', ], 'S3ExpressDirectoryAccessPointConfiguration' => [ 'type' => 'structure', 'members' => [ 'accessPointPolicy' => [ 'shape' => 'AccessPointPolicy', ], 'networkOrigin' => [ 'shape' => 'NetworkOriginConfiguration', ], ], ], 'S3ExpressDirectoryAccessPointConfigurationsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'S3ExpressDirectoryAccessPointArn', ], 'value' => [ 'shape' => 'S3ExpressDirectoryAccessPointConfiguration', ], ], 'S3ExpressDirectoryBucketConfiguration' => [ 'type' => 'structure', 'members' => [ 'bucketPolicy' => [ 'shape' => 'S3ExpressDirectoryBucketPolicy', ], 'accessPoints' => [ 'shape' => 'S3ExpressDirectoryAccessPointConfigurationsMap', ], ], ], 'S3ExpressDirectoryBucketPolicy' => [ 'type' => 'string', ], 'S3PublicAccessBlockConfiguration' => [ 'type' => 'structure', 'required' => [ 'ignorePublicAcls', 'restrictPublicBuckets', ], 'members' => [ 'ignorePublicAcls' => [ 'shape' => 'Boolean', ], 'restrictPublicBuckets' => [ 'shape' => 'Boolean', ], ], ], 'SecretsManagerSecretConfiguration' => [ 'type' => 'structure', 'members' => [ 'kmsKeyId' => [ 'shape' => 'SecretsManagerSecretKmsId', ], 'secretPolicy' => [ 'shape' => 'SecretsManagerSecretPolicy', ], ], ], 'SecretsManagerSecretKmsId' => [ 'type' => 'string', ], 'SecretsManagerSecretPolicy' => [ 'type' => 'string', ], 'ServiceControlPolicyRestriction' => [ 'type' => 'string', 'enum' => [ 'APPLICABLE', 'FAILED_TO_EVALUATE_SCP', 'NOT_APPLICABLE', 'APPLIED', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SharedViaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SnsTopicConfiguration' => [ 'type' => 'structure', 'members' => [ 'topicPolicy' => [ 'shape' => 'SnsTopicPolicy', ], ], ], 'SnsTopicPolicy' => [ 'type' => 'string', 'max' => 30720, 'min' => 0, ], 'SortCriteria' => [ 'type' => 'structure', 'members' => [ 'attributeName' => [ 'shape' => 'String', ], 'orderBy' => [ 'shape' => 'OrderBy', ], ], ], 'Span' => [ 'type' => 'structure', 'required' => [ 'start', 'end', ], 'members' => [ 'start' => [ 'shape' => 'Position', ], 'end' => [ 'shape' => 'Position', ], ], ], 'SqsQueueConfiguration' => [ 'type' => 'structure', 'members' => [ 'queuePolicy' => [ 'shape' => 'SqsQueuePolicy', ], ], ], 'SqsQueuePolicy' => [ 'type' => 'string', ], 'StartPolicyGenerationRequest' => [ 'type' => 'structure', 'required' => [ 'policyGenerationDetails', ], 'members' => [ 'policyGenerationDetails' => [ 'shape' => 'PolicyGenerationDetails', ], 'cloudTrailDetails' => [ 'shape' => 'CloudTrailDetails', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], ], ], 'StartPolicyGenerationResponse' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], ], ], 'StartResourceScanRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', 'resourceArn', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', ], 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'resourceOwnerAccount' => [ 'shape' => 'String', ], ], ], 'Status' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'FAILED', 'IN_PROGRESS', ], ], 'StatusReason' => [ 'type' => 'structure', 'required' => [ 'code', ], 'members' => [ 'code' => [ 'shape' => 'ReasonCode', ], ], ], 'String' => [ 'type' => 'string', ], 'Substring' => [ 'type' => 'structure', 'required' => [ 'start', 'length', ], 'members' => [ 'start' => [ 'shape' => 'Integer', ], 'length' => [ 'shape' => 'Integer', ], ], ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagsMap', ], ], 'TagsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'Timestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'Token' => [ 'type' => 'string', ], 'Trail' => [ 'type' => 'structure', 'required' => [ 'cloudTrailArn', ], 'members' => [ 'cloudTrailArn' => [ 'shape' => 'CloudTrailArn', ], 'regions' => [ 'shape' => 'RegionList', ], 'allRegions' => [ 'shape' => 'Boolean', ], ], ], 'TrailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Trail', ], ], 'TrailProperties' => [ 'type' => 'structure', 'required' => [ 'cloudTrailArn', ], 'members' => [ 'cloudTrailArn' => [ 'shape' => 'CloudTrailArn', ], 'regions' => [ 'shape' => 'RegionList', ], 'allRegions' => [ 'shape' => 'Boolean', ], ], ], 'TrailPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrailProperties', ], ], 'Type' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT', 'ORGANIZATION', 'ACCOUNT_UNUSED_ACCESS', 'ORGANIZATION_UNUSED_ACCESS', 'ACCOUNT_INTERNAL_ACCESS', 'ORGANIZATION_INTERNAL_ACCESS', ], ], 'UnprocessableEntityException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 422, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UnusedAccessConfiguration' => [ 'type' => 'structure', 'members' => [ 'unusedAccessAge' => [ 'shape' => 'Integer', ], 'analysisRule' => [ 'shape' => 'AnalysisRule', ], ], ], 'UnusedAccessFindingsStatistics' => [ 'type' => 'structure', 'members' => [ 'unusedAccessTypeStatistics' => [ 'shape' => 'UnusedAccessTypeStatisticsList', ], 'topAccounts' => [ 'shape' => 'AccountAggregations', ], 'totalActiveFindings' => [ 'shape' => 'Integer', ], 'totalArchivedFindings' => [ 'shape' => 'Integer', ], 'totalResolvedFindings' => [ 'shape' => 'Integer', ], ], ], 'UnusedAccessTypeStatistics' => [ 'type' => 'structure', 'members' => [ 'unusedAccessType' => [ 'shape' => 'String', ], 'total' => [ 'shape' => 'Integer', ], ], ], 'UnusedAccessTypeStatisticsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UnusedAccessTypeStatistics', ], ], 'UnusedAction' => [ 'type' => 'structure', 'required' => [ 'action', ], 'members' => [ 'action' => [ 'shape' => 'String', ], 'lastAccessed' => [ 'shape' => 'Timestamp', ], ], ], 'UnusedActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UnusedAction', ], ], 'UnusedIamRoleDetails' => [ 'type' => 'structure', 'members' => [ 'lastAccessed' => [ 'shape' => 'Timestamp', ], ], ], 'UnusedIamUserAccessKeyDetails' => [ 'type' => 'structure', 'required' => [ 'accessKeyId', ], 'members' => [ 'accessKeyId' => [ 'shape' => 'String', ], 'lastAccessed' => [ 'shape' => 'Timestamp', ], ], ], 'UnusedIamUserPasswordDetails' => [ 'type' => 'structure', 'members' => [ 'lastAccessed' => [ 'shape' => 'Timestamp', ], ], ], 'UnusedPermissionDetails' => [ 'type' => 'structure', 'required' => [ 'serviceNamespace', ], 'members' => [ 'actions' => [ 'shape' => 'UnusedActionList', ], 'serviceNamespace' => [ 'shape' => 'String', ], 'lastAccessed' => [ 'shape' => 'Timestamp', ], ], ], 'UnusedPermissionsRecommendedStep' => [ 'type' => 'structure', 'required' => [ 'recommendedAction', ], 'members' => [ 'policyUpdatedAt' => [ 'shape' => 'Timestamp', ], 'recommendedAction' => [ 'shape' => 'RecommendedRemediationAction', ], 'recommendedPolicy' => [ 'shape' => 'String', ], 'existingPolicyId' => [ 'shape' => 'String', ], ], ], 'UpdateAnalyzerRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerName', ], 'members' => [ 'analyzerName' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'analyzerName', ], 'configuration' => [ 'shape' => 'AnalyzerConfiguration', ], ], ], 'UpdateAnalyzerResponse' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'AnalyzerConfiguration', ], ], ], 'UpdateArchiveRuleRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerName', 'ruleName', 'filter', ], 'members' => [ 'analyzerName' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'analyzerName', ], 'ruleName' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'ruleName', ], 'filter' => [ 'shape' => 'FilterCriteriaMap', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], ], ], 'UpdateFindingsRequest' => [ 'type' => 'structure', 'required' => [ 'analyzerArn', 'status', ], 'members' => [ 'analyzerArn' => [ 'shape' => 'AnalyzerArn', ], 'status' => [ 'shape' => 'FindingStatusUpdate', ], 'ids' => [ 'shape' => 'FindingIdList', ], 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], ], ], 'ValidatePolicyFinding' => [ 'type' => 'structure', 'required' => [ 'findingDetails', 'findingType', 'issueCode', 'learnMoreLink', 'locations', ], 'members' => [ 'findingDetails' => [ 'shape' => 'String', ], 'findingType' => [ 'shape' => 'ValidatePolicyFindingType', ], 'issueCode' => [ 'shape' => 'IssueCode', ], 'learnMoreLink' => [ 'shape' => 'LearnMoreLink', ], 'locations' => [ 'shape' => 'LocationList', ], ], ], 'ValidatePolicyFindingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidatePolicyFinding', ], ], 'ValidatePolicyFindingType' => [ 'type' => 'string', 'enum' => [ 'ERROR', 'SECURITY_WARNING', 'SUGGESTION', 'WARNING', ], ], 'ValidatePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyDocument', 'policyType', ], 'members' => [ 'locale' => [ 'shape' => 'Locale', ], 'maxResults' => [ 'shape' => 'Integer', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'policyDocument' => [ 'shape' => 'PolicyDocument', ], 'policyType' => [ 'shape' => 'PolicyType', ], 'validatePolicyResourceType' => [ 'shape' => 'ValidatePolicyResourceType', ], ], ], 'ValidatePolicyResourceType' => [ 'type' => 'string', 'enum' => [ 'AWS::S3::Bucket', 'AWS::S3::AccessPoint', 'AWS::S3::MultiRegionAccessPoint', 'AWS::S3ObjectLambda::AccessPoint', 'AWS::IAM::AssumeRolePolicyDocument', 'AWS::DynamoDB::Table', ], ], 'ValidatePolicyResponse' => [ 'type' => 'structure', 'required' => [ 'findings', ], 'members' => [ 'findings' => [ 'shape' => 'ValidatePolicyFindingList', ], 'nextToken' => [ 'shape' => 'Token', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'name', 'message', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'unknownOperation', 'cannotParse', 'fieldValidationFailed', 'other', 'notSupported', ], ], 'ValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 20, 'min' => 1, ], 'VpcConfiguration' => [ 'type' => 'structure', 'required' => [ 'vpcId', ], 'members' => [ 'vpcId' => [ 'shape' => 'VpcId', ], ], ], 'VpcId' => [ 'type' => 'string', 'pattern' => 'vpc-([0-9a-f]){8}(([0-9a-f]){9})?', ], ],];
