<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

/**
 * إنشاء جداول الأرشفة والتخزين المؤقت
 * 
 * هذا الـ Migration ينشئ جداول متخصصة للأرشفة والتخزين المؤقت
 * لتحسين الأداء مع البيانات الكبيرة
 * 
 * @package Database\Migrations
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
return new class extends Migration
{
    /**
     * تشغيل الـ Migration
     * 
     * @return void
     */
    public function up(): void
    {
        // إنشاء جدول أرشيف المشاريع
        $this->createProjectsArchiveTable();
        
        // إنشاء جدول أرشيف المهام
        $this->createTasksArchiveTable();
        
        // إنشاء جدول أرشيف سجلات الوقت
        $this->createTimeEntriesArchiveTable();
        
        // إنشاء جدول التخزين المؤقت للإحصائيات
        $this->createStatisticsCacheTable();
        
        // إنشاء جدول التخزين المؤقت للتقارير
        $this->createReportsCacheTable();
        
        // إنشاء جدول فهرسة البحث
        $this->createSearchIndexTable();
        
        // إنشاء جدول تجميع البيانات
        $this->createDataAggregationTables();
    }

    /**
     * إنشاء جدول أرشيف المشاريع
     * 
     * @return void
     */
    protected function createProjectsArchiveTable(): void
    {
        Schema::create('projects_archive', function (Blueprint $table) {
            $table->id()->comment('معرف السجل المؤرشف');
            $table->unsignedBigInteger('original_id')->comment('المعرف الأصلي للمشروع');
            
            // نسخ جميع حقول المشاريع
            $table->string('name', 255)->comment('اسم المشروع');
            $table->string('slug', 255)->comment('الرابط الودود');
            $table->text('description')->nullable()->comment('وصف المشروع');
            $table->string('type', 50)->comment('نوع المشروع');
            $table->string('status', 50)->comment('حالة المشروع');
            $table->string('priority', 50)->comment('أولوية المشروع');
            $table->decimal('budget', 15, 2)->comment('الميزانية');
            $table->decimal('spent_budget', 15, 2)->comment('الميزانية المستخدمة');
            $table->date('start_date')->comment('تاريخ البداية');
            $table->date('end_date')->nullable()->comment('تاريخ النهاية');
            $table->timestamp('actual_end_date')->nullable()->comment('التاريخ الفعلي للانتهاء');
            $table->tinyInteger('progress_percentage')->comment('نسبة الإنجاز');
            $table->unsignedBigInteger('client_id')->nullable()->comment('معرف العميل');
            $table->unsignedBigInteger('manager_id')->nullable()->comment('معرف المدير');
            $table->unsignedBigInteger('created_by')->comment('معرف المنشئ');
            $table->json('settings')->nullable()->comment('الإعدادات');
            
            // معلومات الأرشفة
            $table->timestamp('archived_at')->useCurrent()->comment('تاريخ الأرشفة');
            $table->unsignedBigInteger('archived_by')->comment('معرف من قام بالأرشفة');
            $table->text('archive_reason')->nullable()->comment('سبب الأرشفة');
            $table->json('archive_metadata')->nullable()->comment('بيانات إضافية للأرشفة');
            
            // الطوابع الزمنية الأصلية
            $table->timestamp('original_created_at')->comment('تاريخ الإنشاء الأصلي');
            $table->timestamp('original_updated_at')->comment('تاريخ آخر تحديث أصلي');
            $table->timestamp('original_deleted_at')->nullable()->comment('تاريخ الحذف الأصلي');
            
            // فهارس محسنة
            $table->index(['original_id'], 'idx_archive_projects_original');
            $table->index(['archived_at', 'status'], 'idx_archive_projects_date_status');
            $table->index(['client_id', 'archived_at'], 'idx_archive_projects_client_date');
            $table->index(['manager_id', 'archived_at'], 'idx_archive_projects_manager_date');
            $table->index(['type', 'status'], 'idx_archive_projects_type_status');
        });
        
        DB::statement("ALTER TABLE projects_archive COMMENT = 'جدول أرشيف المشاريع - للمشاريع المكتملة أو الملغية'");
    }

    /**
     * إنشاء جدول أرشيف المهام
     * 
     * @return void
     */
    protected function createTasksArchiveTable(): void
    {
        Schema::create('tasks_archive', function (Blueprint $table) {
            $table->id()->comment('معرف السجل المؤرشف');
            $table->unsignedBigInteger('original_id')->comment('المعرف الأصلي للمهمة');
            $table->unsignedBigInteger('original_project_id')->comment('معرف المشروع الأصلي');
            
            // نسخ حقول المهام
            $table->string('title', 255)->comment('عنوان المهمة');
            $table->text('description')->nullable()->comment('وصف المهمة');
            $table->string('type', 50)->comment('نوع المهمة');
            $table->string('status', 50)->comment('حالة المهمة');
            $table->string('priority', 50)->comment('أولوية المهمة');
            $table->unsignedBigInteger('parent_task_id')->nullable()->comment('معرف المهمة الأب');
            $table->unsignedBigInteger('assigned_to')->nullable()->comment('معرف المكلف');
            $table->decimal('estimated_hours', 8, 2)->comment('الساعات المقدرة');
            $table->decimal('actual_hours', 8, 2)->comment('الساعات الفعلية');
            $table->date('start_date')->nullable()->comment('تاريخ البداية');
            $table->date('due_date')->nullable()->comment('تاريخ الاستحقاق');
            $table->timestamp('completed_at')->nullable()->comment('تاريخ الإنجاز');
            $table->integer('order_index')->comment('ترتيب المهمة');
            $table->json('metadata')->nullable()->comment('بيانات إضافية');
            $table->unsignedBigInteger('created_by')->comment('معرف المنشئ');
            
            // معلومات الأرشفة
            $table->timestamp('archived_at')->useCurrent()->comment('تاريخ الأرشفة');
            $table->unsignedBigInteger('archived_by')->comment('معرف من قام بالأرشفة');
            $table->text('archive_reason')->nullable()->comment('سبب الأرشفة');
            
            // الطوابع الزمنية الأصلية
            $table->timestamp('original_created_at')->comment('تاريخ الإنشاء الأصلي');
            $table->timestamp('original_updated_at')->comment('تاريخ آخر تحديث أصلي');
            
            // فهارس محسنة
            $table->index(['original_id'], 'idx_archive_tasks_original');
            $table->index(['original_project_id', 'archived_at'], 'idx_archive_tasks_project_date');
            $table->index(['assigned_to', 'archived_at'], 'idx_archive_tasks_assigned_date');
            $table->index(['status', 'archived_at'], 'idx_archive_tasks_status_date');
        });
        
        DB::statement("ALTER TABLE tasks_archive COMMENT = 'جدول أرشيف المهام - للمهام المكتملة أو الملغية'");
    }

    /**
     * إنشاء جدول أرشيف سجلات الوقت
     * 
     * @return void
     */
    protected function createTimeEntriesArchiveTable(): void
    {
        Schema::create('time_entries_archive', function (Blueprint $table) {
            $table->id()->comment('معرف السجل المؤرشف');
            $table->unsignedBigInteger('original_id')->comment('المعرف الأصلي');
            $table->unsignedBigInteger('user_id')->comment('معرف المستخدم');
            $table->unsignedBigInteger('project_id')->comment('معرف المشروع');
            $table->unsignedBigInteger('task_id')->nullable()->comment('معرف المهمة');
            $table->text('description')->nullable()->comment('وصف العمل');
            $table->decimal('hours', 8, 2)->comment('عدد الساعات');
            $table->date('date')->comment('تاريخ العمل');
            $table->timestamp('start_time')->nullable()->comment('وقت البداية');
            $table->timestamp('end_time')->nullable()->comment('وقت النهاية');
            $table->boolean('is_billable')->comment('قابل للفوترة');
            $table->decimal('hourly_rate', 8, 2)->nullable()->comment('السعر بالساعة');
            $table->decimal('total_cost', 10, 2)->nullable()->comment('التكلفة الإجمالية');
            $table->string('invoice_id', 100)->nullable()->comment('معرف الفاتورة');
            
            // معلومات الأرشفة
            $table->timestamp('archived_at')->useCurrent()->comment('تاريخ الأرشفة');
            $table->text('archive_reason')->nullable()->comment('سبب الأرشفة');
            
            // الطوابع الزمنية الأصلية
            $table->timestamp('original_created_at')->comment('تاريخ الإنشاء الأصلي');
            
            // فهارس محسنة مع التقسيم
            $table->index(['user_id', 'date'], 'idx_archive_time_user_date');
            $table->index(['project_id', 'date'], 'idx_archive_time_project_date');
            $table->index(['archived_at'], 'idx_archive_time_archived_date');
        });
        
        // تقسيم الجدول حسب السنة
        DB::statement("
            ALTER TABLE time_entries_archive 
            PARTITION BY RANGE (YEAR(date)) (
                PARTITION p2020 VALUES LESS THAN (2021),
                PARTITION p2021 VALUES LESS THAN (2022),
                PARTITION p2022 VALUES LESS THAN (2023),
                PARTITION p2023 VALUES LESS THAN (2024),
                PARTITION p2024 VALUES LESS THAN (2025),
                PARTITION p_old VALUES LESS THAN MAXVALUE
            )
        ");
        
        DB::statement("ALTER TABLE time_entries_archive COMMENT = 'جدول أرشيف سجلات الوقت - للسجلات القديمة'");
    }

    /**
     * إنشاء جدول التخزين المؤقت للإحصائيات
     * 
     * @return void
     */
    protected function createStatisticsCacheTable(): void
    {
        Schema::create('statistics_cache', function (Blueprint $table) {
            $table->id()->comment('معرف الإحصائية');
            $table->string('cache_key', 255)->unique()->comment('مفتاح التخزين المؤقت');
            $table->string('category', 100)->comment('فئة الإحصائية');
            $table->json('data')->comment('بيانات الإحصائية');
            $table->json('filters')->nullable()->comment('المرشحات المطبقة');
            $table->timestamp('calculated_at')->useCurrent()->comment('وقت الحساب');
            $table->timestamp('expires_at')->comment('وقت انتهاء الصلاحية');
            $table->unsignedInteger('hit_count')->default(0)->comment('عدد مرات الاستخدام');
            $table->timestamp('last_accessed_at')->nullable()->comment('آخر وقت وصول');
            
            // فهارس محسنة
            $table->index(['category', 'expires_at'], 'idx_stats_cache_category_expires');
            $table->index(['expires_at'], 'idx_stats_cache_expires');
            $table->index(['calculated_at'], 'idx_stats_cache_calculated');
            $table->index(['hit_count', 'last_accessed_at'], 'idx_stats_cache_usage');
        });
        
        DB::statement("ALTER TABLE statistics_cache COMMENT = 'جدول التخزين المؤقت للإحصائيات - لتسريع الاستعلامات المعقدة'");
    }

    /**
     * إنشاء جدول التخزين المؤقت للتقارير
     * 
     * @return void
     */
    protected function createReportsCacheTable(): void
    {
        Schema::create('reports_cache', function (Blueprint $table) {
            $table->id()->comment('معرف التقرير');
            $table->string('report_type', 100)->comment('نوع التقرير');
            $table->string('cache_key', 255)->unique()->comment('مفتاح التخزين المؤقت');
            $table->json('parameters')->comment('معاملات التقرير');
            $table->longText('report_data')->comment('بيانات التقرير');
            $table->string('format', 20)->default('json')->comment('تنسيق البيانات');
            $table->unsignedBigInteger('file_size')->nullable()->comment('حجم الملف بالبايت');
            $table->timestamp('generated_at')->useCurrent()->comment('وقت الإنتاج');
            $table->timestamp('expires_at')->comment('وقت انتهاء الصلاحية');
            $table->unsignedBigInteger('generated_by')->comment('معرف من أنتج التقرير');
            $table->unsignedInteger('download_count')->default(0)->comment('عدد مرات التحميل');
            
            // فهارس محسنة
            $table->index(['report_type', 'expires_at'], 'idx_reports_cache_type_expires');
            $table->index(['generated_by', 'generated_at'], 'idx_reports_cache_user_date');
            $table->index(['expires_at'], 'idx_reports_cache_expires');
        });
        
        DB::statement("ALTER TABLE reports_cache COMMENT = 'جدول التخزين المؤقت للتقارير - للتقارير المعقدة والكبيرة'");
    }

    /**
     * إنشاء جدول فهرسة البحث
     * 
     * @return void
     */
    protected function createSearchIndexTable(): void
    {
        Schema::create('search_index', function (Blueprint $table) {
            $table->id()->comment('معرف الفهرس');
            $table->string('entity_type', 100)->comment('نوع الكائن');
            $table->unsignedBigInteger('entity_id')->comment('معرف الكائن');
            $table->text('searchable_content')->comment('المحتوى القابل للبحث');
            $table->json('keywords')->comment('الكلمات المفتاحية');
            $table->decimal('relevance_score', 5, 2)->default(1.00)->comment('درجة الصلة');
            $table->timestamp('indexed_at')->useCurrent()->comment('وقت الفهرسة');
            $table->timestamp('updated_at')->useCurrent()->onUpdate('CURRENT_TIMESTAMP')->comment('آخر تحديث');
            
            // فهارس محسنة للبحث السريع
            $table->index(['entity_type', 'entity_id'], 'idx_search_entity');
            $table->index(['relevance_score'], 'idx_search_relevance');
            $table->fullText(['searchable_content'], 'idx_search_content');
            
            // فهرس مركب للبحث المتقدم
            $table->index(['entity_type', 'relevance_score', 'indexed_at'], 'idx_search_advanced');
        });
        
        DB::statement("ALTER TABLE search_index COMMENT = 'جدول فهرسة البحث - لتسريع عمليات البحث النصي'");
    }

    /**
     * إنشاء جداول تجميع البيانات
     * 
     * @return void
     */
    protected function createDataAggregationTables(): void
    {
        // جدول الإحصائيات اليومية
        Schema::create('daily_statistics', function (Blueprint $table) {
            $table->id()->comment('معرف الإحصائية');
            $table->date('date')->comment('التاريخ');
            $table->string('metric_type', 100)->comment('نوع المقياس');
            $table->unsignedBigInteger('entity_id')->nullable()->comment('معرف الكائن');
            $table->string('entity_type', 100)->nullable()->comment('نوع الكائن');
            $table->decimal('value', 15, 2)->comment('القيمة');
            $table->json('metadata')->nullable()->comment('بيانات إضافية');
            $table->timestamp('calculated_at')->useCurrent()->comment('وقت الحساب');
            
            // فهارس محسنة
            $table->unique(['date', 'metric_type', 'entity_id', 'entity_type'], 'unique_daily_metric');
            $table->index(['date', 'metric_type'], 'idx_daily_stats_date_type');
            $table->index(['entity_type', 'entity_id', 'date'], 'idx_daily_stats_entity_date');
        });

        // جدول الإحصائيات الشهرية
        Schema::create('monthly_statistics', function (Blueprint $table) {
            $table->id()->comment('معرف الإحصائية');
            $table->year('year')->comment('السنة');
            $table->tinyInteger('month')->comment('الشهر');
            $table->string('metric_type', 100)->comment('نوع المقياس');
            $table->unsignedBigInteger('entity_id')->nullable()->comment('معرف الكائن');
            $table->string('entity_type', 100)->nullable()->comment('نوع الكائن');
            $table->decimal('value', 15, 2)->comment('القيمة');
            $table->decimal('average_value', 15, 2)->nullable()->comment('المتوسط');
            $table->decimal('min_value', 15, 2)->nullable()->comment('أقل قيمة');
            $table->decimal('max_value', 15, 2)->nullable()->comment('أعلى قيمة');
            $table->json('metadata')->nullable()->comment('بيانات إضافية');
            $table->timestamp('calculated_at')->useCurrent()->comment('وقت الحساب');
            
            // فهارس محسنة
            $table->unique(['year', 'month', 'metric_type', 'entity_id', 'entity_type'], 'unique_monthly_metric');
            $table->index(['year', 'month', 'metric_type'], 'idx_monthly_stats_date_type');
            $table->index(['entity_type', 'entity_id'], 'idx_monthly_stats_entity');
        });
        
        DB::statement("ALTER TABLE daily_statistics COMMENT = 'جدول الإحصائيات اليومية - لتجميع البيانات اليومية'");
        DB::statement("ALTER TABLE monthly_statistics COMMENT = 'جدول الإحصائيات الشهرية - لتجميع البيانات الشهرية'");
    }

    /**
     * التراجع عن الـ Migration
     * 
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('monthly_statistics');
        Schema::dropIfExists('daily_statistics');
        Schema::dropIfExists('search_index');
        Schema::dropIfExists('reports_cache');
        Schema::dropIfExists('statistics_cache');
        Schema::dropIfExists('time_entries_archive');
        Schema::dropIfExists('tasks_archive');
        Schema::dropIfExists('projects_archive');
    }
};
