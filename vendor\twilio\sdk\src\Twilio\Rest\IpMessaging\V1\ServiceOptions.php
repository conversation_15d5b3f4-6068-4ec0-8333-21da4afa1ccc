<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Ip_messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\IpMessaging\V1;

use Twilio\Options;
use Twilio\Values;

abstract class ServiceOptions
{




    /**
     * @param string $friendlyName 
     * @param string $defaultServiceRoleSid 
     * @param string $defaultChannelRoleSid 
     * @param string $defaultChannelCreatorRoleSid 
     * @param bool $readStatusEnabled 
     * @param bool $reachabilityEnabled 
     * @param int $typingIndicatorTimeout 
     * @param int $consumptionReportInterval 
     * @param bool $notificationsNewMessageEnabled 
     * @param string $notificationsNewMessageTemplate 
     * @param bool $notificationsAddedToChannelEnabled 
     * @param string $notificationsAddedToChannelTemplate 
     * @param bool $notificationsRemovedFromChannelEnabled 
     * @param string $notificationsRemovedFromChannelTemplate 
     * @param bool $notificationsInvitedToChannelEnabled 
     * @param string $notificationsInvitedToChannelTemplate 
     * @param string $preWebhookUrl 
     * @param string $postWebhookUrl 
     * @param string $webhookMethod 
     * @param string[] $webhookFilters 
     * @param string $webhooksOnMessageSendUrl 
     * @param string $webhooksOnMessageSendMethod 
     * @param string $webhooksOnMessageUpdateUrl 
     * @param string $webhooksOnMessageUpdateMethod 
     * @param string $webhooksOnMessageRemoveUrl 
     * @param string $webhooksOnMessageRemoveMethod 
     * @param string $webhooksOnChannelAddUrl 
     * @param string $webhooksOnChannelAddMethod 
     * @param string $webhooksOnChannelDestroyUrl 
     * @param string $webhooksOnChannelDestroyMethod 
     * @param string $webhooksOnChannelUpdateUrl 
     * @param string $webhooksOnChannelUpdateMethod 
     * @param string $webhooksOnMemberAddUrl 
     * @param string $webhooksOnMemberAddMethod 
     * @param string $webhooksOnMemberRemoveUrl 
     * @param string $webhooksOnMemberRemoveMethod 
     * @param string $webhooksOnMessageSentUrl 
     * @param string $webhooksOnMessageSentMethod 
     * @param string $webhooksOnMessageUpdatedUrl 
     * @param string $webhooksOnMessageUpdatedMethod 
     * @param string $webhooksOnMessageRemovedUrl 
     * @param string $webhooksOnMessageRemovedMethod 
     * @param string $webhooksOnChannelAddedUrl 
     * @param string $webhooksOnChannelAddedMethod 
     * @param string $webhooksOnChannelDestroyedUrl 
     * @param string $webhooksOnChannelDestroyedMethod 
     * @param string $webhooksOnChannelUpdatedUrl 
     * @param string $webhooksOnChannelUpdatedMethod 
     * @param string $webhooksOnMemberAddedUrl 
     * @param string $webhooksOnMemberAddedMethod 
     * @param string $webhooksOnMemberRemovedUrl 
     * @param string $webhooksOnMemberRemovedMethod 
     * @param int $limitsChannelMembers 
     * @param int $limitsUserChannels 
     * @return UpdateServiceOptions Options builder
     */
    public static function update(
        
        string $friendlyName = Values::NONE,
        string $defaultServiceRoleSid = Values::NONE,
        string $defaultChannelRoleSid = Values::NONE,
        string $defaultChannelCreatorRoleSid = Values::NONE,
        bool $readStatusEnabled = Values::BOOL_NONE,
        bool $reachabilityEnabled = Values::BOOL_NONE,
        int $typingIndicatorTimeout = Values::INT_NONE,
        int $consumptionReportInterval = Values::INT_NONE,
        bool $notificationsNewMessageEnabled = Values::BOOL_NONE,
        string $notificationsNewMessageTemplate = Values::NONE,
        bool $notificationsAddedToChannelEnabled = Values::BOOL_NONE,
        string $notificationsAddedToChannelTemplate = Values::NONE,
        bool $notificationsRemovedFromChannelEnabled = Values::BOOL_NONE,
        string $notificationsRemovedFromChannelTemplate = Values::NONE,
        bool $notificationsInvitedToChannelEnabled = Values::BOOL_NONE,
        string $notificationsInvitedToChannelTemplate = Values::NONE,
        string $preWebhookUrl = Values::NONE,
        string $postWebhookUrl = Values::NONE,
        string $webhookMethod = Values::NONE,
        array $webhookFilters = Values::ARRAY_NONE,
        string $webhooksOnMessageSendUrl = Values::NONE,
        string $webhooksOnMessageSendMethod = Values::NONE,
        string $webhooksOnMessageUpdateUrl = Values::NONE,
        string $webhooksOnMessageUpdateMethod = Values::NONE,
        string $webhooksOnMessageRemoveUrl = Values::NONE,
        string $webhooksOnMessageRemoveMethod = Values::NONE,
        string $webhooksOnChannelAddUrl = Values::NONE,
        string $webhooksOnChannelAddMethod = Values::NONE,
        string $webhooksOnChannelDestroyUrl = Values::NONE,
        string $webhooksOnChannelDestroyMethod = Values::NONE,
        string $webhooksOnChannelUpdateUrl = Values::NONE,
        string $webhooksOnChannelUpdateMethod = Values::NONE,
        string $webhooksOnMemberAddUrl = Values::NONE,
        string $webhooksOnMemberAddMethod = Values::NONE,
        string $webhooksOnMemberRemoveUrl = Values::NONE,
        string $webhooksOnMemberRemoveMethod = Values::NONE,
        string $webhooksOnMessageSentUrl = Values::NONE,
        string $webhooksOnMessageSentMethod = Values::NONE,
        string $webhooksOnMessageUpdatedUrl = Values::NONE,
        string $webhooksOnMessageUpdatedMethod = Values::NONE,
        string $webhooksOnMessageRemovedUrl = Values::NONE,
        string $webhooksOnMessageRemovedMethod = Values::NONE,
        string $webhooksOnChannelAddedUrl = Values::NONE,
        string $webhooksOnChannelAddedMethod = Values::NONE,
        string $webhooksOnChannelDestroyedUrl = Values::NONE,
        string $webhooksOnChannelDestroyedMethod = Values::NONE,
        string $webhooksOnChannelUpdatedUrl = Values::NONE,
        string $webhooksOnChannelUpdatedMethod = Values::NONE,
        string $webhooksOnMemberAddedUrl = Values::NONE,
        string $webhooksOnMemberAddedMethod = Values::NONE,
        string $webhooksOnMemberRemovedUrl = Values::NONE,
        string $webhooksOnMemberRemovedMethod = Values::NONE,
        int $limitsChannelMembers = Values::INT_NONE,
        int $limitsUserChannels = Values::INT_NONE

    ): UpdateServiceOptions
    {
        return new UpdateServiceOptions(
            $friendlyName,
            $defaultServiceRoleSid,
            $defaultChannelRoleSid,
            $defaultChannelCreatorRoleSid,
            $readStatusEnabled,
            $reachabilityEnabled,
            $typingIndicatorTimeout,
            $consumptionReportInterval,
            $notificationsNewMessageEnabled,
            $notificationsNewMessageTemplate,
            $notificationsAddedToChannelEnabled,
            $notificationsAddedToChannelTemplate,
            $notificationsRemovedFromChannelEnabled,
            $notificationsRemovedFromChannelTemplate,
            $notificationsInvitedToChannelEnabled,
            $notificationsInvitedToChannelTemplate,
            $preWebhookUrl,
            $postWebhookUrl,
            $webhookMethod,
            $webhookFilters,
            $webhooksOnMessageSendUrl,
            $webhooksOnMessageSendMethod,
            $webhooksOnMessageUpdateUrl,
            $webhooksOnMessageUpdateMethod,
            $webhooksOnMessageRemoveUrl,
            $webhooksOnMessageRemoveMethod,
            $webhooksOnChannelAddUrl,
            $webhooksOnChannelAddMethod,
            $webhooksOnChannelDestroyUrl,
            $webhooksOnChannelDestroyMethod,
            $webhooksOnChannelUpdateUrl,
            $webhooksOnChannelUpdateMethod,
            $webhooksOnMemberAddUrl,
            $webhooksOnMemberAddMethod,
            $webhooksOnMemberRemoveUrl,
            $webhooksOnMemberRemoveMethod,
            $webhooksOnMessageSentUrl,
            $webhooksOnMessageSentMethod,
            $webhooksOnMessageUpdatedUrl,
            $webhooksOnMessageUpdatedMethod,
            $webhooksOnMessageRemovedUrl,
            $webhooksOnMessageRemovedMethod,
            $webhooksOnChannelAddedUrl,
            $webhooksOnChannelAddedMethod,
            $webhooksOnChannelDestroyedUrl,
            $webhooksOnChannelDestroyedMethod,
            $webhooksOnChannelUpdatedUrl,
            $webhooksOnChannelUpdatedMethod,
            $webhooksOnMemberAddedUrl,
            $webhooksOnMemberAddedMethod,
            $webhooksOnMemberRemovedUrl,
            $webhooksOnMemberRemovedMethod,
            $limitsChannelMembers,
            $limitsUserChannels
        );
    }

}





class UpdateServiceOptions extends Options
    {
    /**
     * @param string $friendlyName 
     * @param string $defaultServiceRoleSid 
     * @param string $defaultChannelRoleSid 
     * @param string $defaultChannelCreatorRoleSid 
     * @param bool $readStatusEnabled 
     * @param bool $reachabilityEnabled 
     * @param int $typingIndicatorTimeout 
     * @param int $consumptionReportInterval 
     * @param bool $notificationsNewMessageEnabled 
     * @param string $notificationsNewMessageTemplate 
     * @param bool $notificationsAddedToChannelEnabled 
     * @param string $notificationsAddedToChannelTemplate 
     * @param bool $notificationsRemovedFromChannelEnabled 
     * @param string $notificationsRemovedFromChannelTemplate 
     * @param bool $notificationsInvitedToChannelEnabled 
     * @param string $notificationsInvitedToChannelTemplate 
     * @param string $preWebhookUrl 
     * @param string $postWebhookUrl 
     * @param string $webhookMethod 
     * @param string[] $webhookFilters 
     * @param string $webhooksOnMessageSendUrl 
     * @param string $webhooksOnMessageSendMethod 
     * @param string $webhooksOnMessageUpdateUrl 
     * @param string $webhooksOnMessageUpdateMethod 
     * @param string $webhooksOnMessageRemoveUrl 
     * @param string $webhooksOnMessageRemoveMethod 
     * @param string $webhooksOnChannelAddUrl 
     * @param string $webhooksOnChannelAddMethod 
     * @param string $webhooksOnChannelDestroyUrl 
     * @param string $webhooksOnChannelDestroyMethod 
     * @param string $webhooksOnChannelUpdateUrl 
     * @param string $webhooksOnChannelUpdateMethod 
     * @param string $webhooksOnMemberAddUrl 
     * @param string $webhooksOnMemberAddMethod 
     * @param string $webhooksOnMemberRemoveUrl 
     * @param string $webhooksOnMemberRemoveMethod 
     * @param string $webhooksOnMessageSentUrl 
     * @param string $webhooksOnMessageSentMethod 
     * @param string $webhooksOnMessageUpdatedUrl 
     * @param string $webhooksOnMessageUpdatedMethod 
     * @param string $webhooksOnMessageRemovedUrl 
     * @param string $webhooksOnMessageRemovedMethod 
     * @param string $webhooksOnChannelAddedUrl 
     * @param string $webhooksOnChannelAddedMethod 
     * @param string $webhooksOnChannelDestroyedUrl 
     * @param string $webhooksOnChannelDestroyedMethod 
     * @param string $webhooksOnChannelUpdatedUrl 
     * @param string $webhooksOnChannelUpdatedMethod 
     * @param string $webhooksOnMemberAddedUrl 
     * @param string $webhooksOnMemberAddedMethod 
     * @param string $webhooksOnMemberRemovedUrl 
     * @param string $webhooksOnMemberRemovedMethod 
     * @param int $limitsChannelMembers 
     * @param int $limitsUserChannels 
     */
    public function __construct(
        
        string $friendlyName = Values::NONE,
        string $defaultServiceRoleSid = Values::NONE,
        string $defaultChannelRoleSid = Values::NONE,
        string $defaultChannelCreatorRoleSid = Values::NONE,
        bool $readStatusEnabled = Values::BOOL_NONE,
        bool $reachabilityEnabled = Values::BOOL_NONE,
        int $typingIndicatorTimeout = Values::INT_NONE,
        int $consumptionReportInterval = Values::INT_NONE,
        bool $notificationsNewMessageEnabled = Values::BOOL_NONE,
        string $notificationsNewMessageTemplate = Values::NONE,
        bool $notificationsAddedToChannelEnabled = Values::BOOL_NONE,
        string $notificationsAddedToChannelTemplate = Values::NONE,
        bool $notificationsRemovedFromChannelEnabled = Values::BOOL_NONE,
        string $notificationsRemovedFromChannelTemplate = Values::NONE,
        bool $notificationsInvitedToChannelEnabled = Values::BOOL_NONE,
        string $notificationsInvitedToChannelTemplate = Values::NONE,
        string $preWebhookUrl = Values::NONE,
        string $postWebhookUrl = Values::NONE,
        string $webhookMethod = Values::NONE,
        array $webhookFilters = Values::ARRAY_NONE,
        string $webhooksOnMessageSendUrl = Values::NONE,
        string $webhooksOnMessageSendMethod = Values::NONE,
        string $webhooksOnMessageUpdateUrl = Values::NONE,
        string $webhooksOnMessageUpdateMethod = Values::NONE,
        string $webhooksOnMessageRemoveUrl = Values::NONE,
        string $webhooksOnMessageRemoveMethod = Values::NONE,
        string $webhooksOnChannelAddUrl = Values::NONE,
        string $webhooksOnChannelAddMethod = Values::NONE,
        string $webhooksOnChannelDestroyUrl = Values::NONE,
        string $webhooksOnChannelDestroyMethod = Values::NONE,
        string $webhooksOnChannelUpdateUrl = Values::NONE,
        string $webhooksOnChannelUpdateMethod = Values::NONE,
        string $webhooksOnMemberAddUrl = Values::NONE,
        string $webhooksOnMemberAddMethod = Values::NONE,
        string $webhooksOnMemberRemoveUrl = Values::NONE,
        string $webhooksOnMemberRemoveMethod = Values::NONE,
        string $webhooksOnMessageSentUrl = Values::NONE,
        string $webhooksOnMessageSentMethod = Values::NONE,
        string $webhooksOnMessageUpdatedUrl = Values::NONE,
        string $webhooksOnMessageUpdatedMethod = Values::NONE,
        string $webhooksOnMessageRemovedUrl = Values::NONE,
        string $webhooksOnMessageRemovedMethod = Values::NONE,
        string $webhooksOnChannelAddedUrl = Values::NONE,
        string $webhooksOnChannelAddedMethod = Values::NONE,
        string $webhooksOnChannelDestroyedUrl = Values::NONE,
        string $webhooksOnChannelDestroyedMethod = Values::NONE,
        string $webhooksOnChannelUpdatedUrl = Values::NONE,
        string $webhooksOnChannelUpdatedMethod = Values::NONE,
        string $webhooksOnMemberAddedUrl = Values::NONE,
        string $webhooksOnMemberAddedMethod = Values::NONE,
        string $webhooksOnMemberRemovedUrl = Values::NONE,
        string $webhooksOnMemberRemovedMethod = Values::NONE,
        int $limitsChannelMembers = Values::INT_NONE,
        int $limitsUserChannels = Values::INT_NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
        $this->options['defaultServiceRoleSid'] = $defaultServiceRoleSid;
        $this->options['defaultChannelRoleSid'] = $defaultChannelRoleSid;
        $this->options['defaultChannelCreatorRoleSid'] = $defaultChannelCreatorRoleSid;
        $this->options['readStatusEnabled'] = $readStatusEnabled;
        $this->options['reachabilityEnabled'] = $reachabilityEnabled;
        $this->options['typingIndicatorTimeout'] = $typingIndicatorTimeout;
        $this->options['consumptionReportInterval'] = $consumptionReportInterval;
        $this->options['notificationsNewMessageEnabled'] = $notificationsNewMessageEnabled;
        $this->options['notificationsNewMessageTemplate'] = $notificationsNewMessageTemplate;
        $this->options['notificationsAddedToChannelEnabled'] = $notificationsAddedToChannelEnabled;
        $this->options['notificationsAddedToChannelTemplate'] = $notificationsAddedToChannelTemplate;
        $this->options['notificationsRemovedFromChannelEnabled'] = $notificationsRemovedFromChannelEnabled;
        $this->options['notificationsRemovedFromChannelTemplate'] = $notificationsRemovedFromChannelTemplate;
        $this->options['notificationsInvitedToChannelEnabled'] = $notificationsInvitedToChannelEnabled;
        $this->options['notificationsInvitedToChannelTemplate'] = $notificationsInvitedToChannelTemplate;
        $this->options['preWebhookUrl'] = $preWebhookUrl;
        $this->options['postWebhookUrl'] = $postWebhookUrl;
        $this->options['webhookMethod'] = $webhookMethod;
        $this->options['webhookFilters'] = $webhookFilters;
        $this->options['webhooksOnMessageSendUrl'] = $webhooksOnMessageSendUrl;
        $this->options['webhooksOnMessageSendMethod'] = $webhooksOnMessageSendMethod;
        $this->options['webhooksOnMessageUpdateUrl'] = $webhooksOnMessageUpdateUrl;
        $this->options['webhooksOnMessageUpdateMethod'] = $webhooksOnMessageUpdateMethod;
        $this->options['webhooksOnMessageRemoveUrl'] = $webhooksOnMessageRemoveUrl;
        $this->options['webhooksOnMessageRemoveMethod'] = $webhooksOnMessageRemoveMethod;
        $this->options['webhooksOnChannelAddUrl'] = $webhooksOnChannelAddUrl;
        $this->options['webhooksOnChannelAddMethod'] = $webhooksOnChannelAddMethod;
        $this->options['webhooksOnChannelDestroyUrl'] = $webhooksOnChannelDestroyUrl;
        $this->options['webhooksOnChannelDestroyMethod'] = $webhooksOnChannelDestroyMethod;
        $this->options['webhooksOnChannelUpdateUrl'] = $webhooksOnChannelUpdateUrl;
        $this->options['webhooksOnChannelUpdateMethod'] = $webhooksOnChannelUpdateMethod;
        $this->options['webhooksOnMemberAddUrl'] = $webhooksOnMemberAddUrl;
        $this->options['webhooksOnMemberAddMethod'] = $webhooksOnMemberAddMethod;
        $this->options['webhooksOnMemberRemoveUrl'] = $webhooksOnMemberRemoveUrl;
        $this->options['webhooksOnMemberRemoveMethod'] = $webhooksOnMemberRemoveMethod;
        $this->options['webhooksOnMessageSentUrl'] = $webhooksOnMessageSentUrl;
        $this->options['webhooksOnMessageSentMethod'] = $webhooksOnMessageSentMethod;
        $this->options['webhooksOnMessageUpdatedUrl'] = $webhooksOnMessageUpdatedUrl;
        $this->options['webhooksOnMessageUpdatedMethod'] = $webhooksOnMessageUpdatedMethod;
        $this->options['webhooksOnMessageRemovedUrl'] = $webhooksOnMessageRemovedUrl;
        $this->options['webhooksOnMessageRemovedMethod'] = $webhooksOnMessageRemovedMethod;
        $this->options['webhooksOnChannelAddedUrl'] = $webhooksOnChannelAddedUrl;
        $this->options['webhooksOnChannelAddedMethod'] = $webhooksOnChannelAddedMethod;
        $this->options['webhooksOnChannelDestroyedUrl'] = $webhooksOnChannelDestroyedUrl;
        $this->options['webhooksOnChannelDestroyedMethod'] = $webhooksOnChannelDestroyedMethod;
        $this->options['webhooksOnChannelUpdatedUrl'] = $webhooksOnChannelUpdatedUrl;
        $this->options['webhooksOnChannelUpdatedMethod'] = $webhooksOnChannelUpdatedMethod;
        $this->options['webhooksOnMemberAddedUrl'] = $webhooksOnMemberAddedUrl;
        $this->options['webhooksOnMemberAddedMethod'] = $webhooksOnMemberAddedMethod;
        $this->options['webhooksOnMemberRemovedUrl'] = $webhooksOnMemberRemovedUrl;
        $this->options['webhooksOnMemberRemovedMethod'] = $webhooksOnMemberRemovedMethod;
        $this->options['limitsChannelMembers'] = $limitsChannelMembers;
        $this->options['limitsUserChannels'] = $limitsUserChannels;
    }

    /**
     * 
     *
     * @param string $friendlyName 
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * 
     *
     * @param string $defaultServiceRoleSid 
     * @return $this Fluent Builder
     */
    public function setDefaultServiceRoleSid(string $defaultServiceRoleSid): self
    {
        $this->options['defaultServiceRoleSid'] = $defaultServiceRoleSid;
        return $this;
    }

    /**
     * 
     *
     * @param string $defaultChannelRoleSid 
     * @return $this Fluent Builder
     */
    public function setDefaultChannelRoleSid(string $defaultChannelRoleSid): self
    {
        $this->options['defaultChannelRoleSid'] = $defaultChannelRoleSid;
        return $this;
    }

    /**
     * 
     *
     * @param string $defaultChannelCreatorRoleSid 
     * @return $this Fluent Builder
     */
    public function setDefaultChannelCreatorRoleSid(string $defaultChannelCreatorRoleSid): self
    {
        $this->options['defaultChannelCreatorRoleSid'] = $defaultChannelCreatorRoleSid;
        return $this;
    }

    /**
     * 
     *
     * @param bool $readStatusEnabled 
     * @return $this Fluent Builder
     */
    public function setReadStatusEnabled(bool $readStatusEnabled): self
    {
        $this->options['readStatusEnabled'] = $readStatusEnabled;
        return $this;
    }

    /**
     * 
     *
     * @param bool $reachabilityEnabled 
     * @return $this Fluent Builder
     */
    public function setReachabilityEnabled(bool $reachabilityEnabled): self
    {
        $this->options['reachabilityEnabled'] = $reachabilityEnabled;
        return $this;
    }

    /**
     * 
     *
     * @param int $typingIndicatorTimeout 
     * @return $this Fluent Builder
     */
    public function setTypingIndicatorTimeout(int $typingIndicatorTimeout): self
    {
        $this->options['typingIndicatorTimeout'] = $typingIndicatorTimeout;
        return $this;
    }

    /**
     * 
     *
     * @param int $consumptionReportInterval 
     * @return $this Fluent Builder
     */
    public function setConsumptionReportInterval(int $consumptionReportInterval): self
    {
        $this->options['consumptionReportInterval'] = $consumptionReportInterval;
        return $this;
    }

    /**
     * 
     *
     * @param bool $notificationsNewMessageEnabled 
     * @return $this Fluent Builder
     */
    public function setNotificationsNewMessageEnabled(bool $notificationsNewMessageEnabled): self
    {
        $this->options['notificationsNewMessageEnabled'] = $notificationsNewMessageEnabled;
        return $this;
    }

    /**
     * 
     *
     * @param string $notificationsNewMessageTemplate 
     * @return $this Fluent Builder
     */
    public function setNotificationsNewMessageTemplate(string $notificationsNewMessageTemplate): self
    {
        $this->options['notificationsNewMessageTemplate'] = $notificationsNewMessageTemplate;
        return $this;
    }

    /**
     * 
     *
     * @param bool $notificationsAddedToChannelEnabled 
     * @return $this Fluent Builder
     */
    public function setNotificationsAddedToChannelEnabled(bool $notificationsAddedToChannelEnabled): self
    {
        $this->options['notificationsAddedToChannelEnabled'] = $notificationsAddedToChannelEnabled;
        return $this;
    }

    /**
     * 
     *
     * @param string $notificationsAddedToChannelTemplate 
     * @return $this Fluent Builder
     */
    public function setNotificationsAddedToChannelTemplate(string $notificationsAddedToChannelTemplate): self
    {
        $this->options['notificationsAddedToChannelTemplate'] = $notificationsAddedToChannelTemplate;
        return $this;
    }

    /**
     * 
     *
     * @param bool $notificationsRemovedFromChannelEnabled 
     * @return $this Fluent Builder
     */
    public function setNotificationsRemovedFromChannelEnabled(bool $notificationsRemovedFromChannelEnabled): self
    {
        $this->options['notificationsRemovedFromChannelEnabled'] = $notificationsRemovedFromChannelEnabled;
        return $this;
    }

    /**
     * 
     *
     * @param string $notificationsRemovedFromChannelTemplate 
     * @return $this Fluent Builder
     */
    public function setNotificationsRemovedFromChannelTemplate(string $notificationsRemovedFromChannelTemplate): self
    {
        $this->options['notificationsRemovedFromChannelTemplate'] = $notificationsRemovedFromChannelTemplate;
        return $this;
    }

    /**
     * 
     *
     * @param bool $notificationsInvitedToChannelEnabled 
     * @return $this Fluent Builder
     */
    public function setNotificationsInvitedToChannelEnabled(bool $notificationsInvitedToChannelEnabled): self
    {
        $this->options['notificationsInvitedToChannelEnabled'] = $notificationsInvitedToChannelEnabled;
        return $this;
    }

    /**
     * 
     *
     * @param string $notificationsInvitedToChannelTemplate 
     * @return $this Fluent Builder
     */
    public function setNotificationsInvitedToChannelTemplate(string $notificationsInvitedToChannelTemplate): self
    {
        $this->options['notificationsInvitedToChannelTemplate'] = $notificationsInvitedToChannelTemplate;
        return $this;
    }

    /**
     * 
     *
     * @param string $preWebhookUrl 
     * @return $this Fluent Builder
     */
    public function setPreWebhookUrl(string $preWebhookUrl): self
    {
        $this->options['preWebhookUrl'] = $preWebhookUrl;
        return $this;
    }

    /**
     * 
     *
     * @param string $postWebhookUrl 
     * @return $this Fluent Builder
     */
    public function setPostWebhookUrl(string $postWebhookUrl): self
    {
        $this->options['postWebhookUrl'] = $postWebhookUrl;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhookMethod 
     * @return $this Fluent Builder
     */
    public function setWebhookMethod(string $webhookMethod): self
    {
        $this->options['webhookMethod'] = $webhookMethod;
        return $this;
    }

    /**
     * 
     *
     * @param string[] $webhookFilters 
     * @return $this Fluent Builder
     */
    public function setWebhookFilters(array $webhookFilters): self
    {
        $this->options['webhookFilters'] = $webhookFilters;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnMessageSendUrl 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageSendUrl(string $webhooksOnMessageSendUrl): self
    {
        $this->options['webhooksOnMessageSendUrl'] = $webhooksOnMessageSendUrl;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnMessageSendMethod 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageSendMethod(string $webhooksOnMessageSendMethod): self
    {
        $this->options['webhooksOnMessageSendMethod'] = $webhooksOnMessageSendMethod;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnMessageUpdateUrl 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageUpdateUrl(string $webhooksOnMessageUpdateUrl): self
    {
        $this->options['webhooksOnMessageUpdateUrl'] = $webhooksOnMessageUpdateUrl;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnMessageUpdateMethod 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageUpdateMethod(string $webhooksOnMessageUpdateMethod): self
    {
        $this->options['webhooksOnMessageUpdateMethod'] = $webhooksOnMessageUpdateMethod;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnMessageRemoveUrl 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageRemoveUrl(string $webhooksOnMessageRemoveUrl): self
    {
        $this->options['webhooksOnMessageRemoveUrl'] = $webhooksOnMessageRemoveUrl;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnMessageRemoveMethod 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageRemoveMethod(string $webhooksOnMessageRemoveMethod): self
    {
        $this->options['webhooksOnMessageRemoveMethod'] = $webhooksOnMessageRemoveMethod;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnChannelAddUrl 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelAddUrl(string $webhooksOnChannelAddUrl): self
    {
        $this->options['webhooksOnChannelAddUrl'] = $webhooksOnChannelAddUrl;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnChannelAddMethod 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelAddMethod(string $webhooksOnChannelAddMethod): self
    {
        $this->options['webhooksOnChannelAddMethod'] = $webhooksOnChannelAddMethod;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnChannelDestroyUrl 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelDestroyUrl(string $webhooksOnChannelDestroyUrl): self
    {
        $this->options['webhooksOnChannelDestroyUrl'] = $webhooksOnChannelDestroyUrl;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnChannelDestroyMethod 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelDestroyMethod(string $webhooksOnChannelDestroyMethod): self
    {
        $this->options['webhooksOnChannelDestroyMethod'] = $webhooksOnChannelDestroyMethod;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnChannelUpdateUrl 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelUpdateUrl(string $webhooksOnChannelUpdateUrl): self
    {
        $this->options['webhooksOnChannelUpdateUrl'] = $webhooksOnChannelUpdateUrl;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnChannelUpdateMethod 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelUpdateMethod(string $webhooksOnChannelUpdateMethod): self
    {
        $this->options['webhooksOnChannelUpdateMethod'] = $webhooksOnChannelUpdateMethod;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnMemberAddUrl 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMemberAddUrl(string $webhooksOnMemberAddUrl): self
    {
        $this->options['webhooksOnMemberAddUrl'] = $webhooksOnMemberAddUrl;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnMemberAddMethod 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMemberAddMethod(string $webhooksOnMemberAddMethod): self
    {
        $this->options['webhooksOnMemberAddMethod'] = $webhooksOnMemberAddMethod;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnMemberRemoveUrl 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMemberRemoveUrl(string $webhooksOnMemberRemoveUrl): self
    {
        $this->options['webhooksOnMemberRemoveUrl'] = $webhooksOnMemberRemoveUrl;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnMemberRemoveMethod 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMemberRemoveMethod(string $webhooksOnMemberRemoveMethod): self
    {
        $this->options['webhooksOnMemberRemoveMethod'] = $webhooksOnMemberRemoveMethod;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnMessageSentUrl 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageSentUrl(string $webhooksOnMessageSentUrl): self
    {
        $this->options['webhooksOnMessageSentUrl'] = $webhooksOnMessageSentUrl;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnMessageSentMethod 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageSentMethod(string $webhooksOnMessageSentMethod): self
    {
        $this->options['webhooksOnMessageSentMethod'] = $webhooksOnMessageSentMethod;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnMessageUpdatedUrl 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageUpdatedUrl(string $webhooksOnMessageUpdatedUrl): self
    {
        $this->options['webhooksOnMessageUpdatedUrl'] = $webhooksOnMessageUpdatedUrl;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnMessageUpdatedMethod 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageUpdatedMethod(string $webhooksOnMessageUpdatedMethod): self
    {
        $this->options['webhooksOnMessageUpdatedMethod'] = $webhooksOnMessageUpdatedMethod;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnMessageRemovedUrl 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageRemovedUrl(string $webhooksOnMessageRemovedUrl): self
    {
        $this->options['webhooksOnMessageRemovedUrl'] = $webhooksOnMessageRemovedUrl;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnMessageRemovedMethod 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMessageRemovedMethod(string $webhooksOnMessageRemovedMethod): self
    {
        $this->options['webhooksOnMessageRemovedMethod'] = $webhooksOnMessageRemovedMethod;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnChannelAddedUrl 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelAddedUrl(string $webhooksOnChannelAddedUrl): self
    {
        $this->options['webhooksOnChannelAddedUrl'] = $webhooksOnChannelAddedUrl;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnChannelAddedMethod 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelAddedMethod(string $webhooksOnChannelAddedMethod): self
    {
        $this->options['webhooksOnChannelAddedMethod'] = $webhooksOnChannelAddedMethod;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnChannelDestroyedUrl 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelDestroyedUrl(string $webhooksOnChannelDestroyedUrl): self
    {
        $this->options['webhooksOnChannelDestroyedUrl'] = $webhooksOnChannelDestroyedUrl;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnChannelDestroyedMethod 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelDestroyedMethod(string $webhooksOnChannelDestroyedMethod): self
    {
        $this->options['webhooksOnChannelDestroyedMethod'] = $webhooksOnChannelDestroyedMethod;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnChannelUpdatedUrl 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelUpdatedUrl(string $webhooksOnChannelUpdatedUrl): self
    {
        $this->options['webhooksOnChannelUpdatedUrl'] = $webhooksOnChannelUpdatedUrl;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnChannelUpdatedMethod 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnChannelUpdatedMethod(string $webhooksOnChannelUpdatedMethod): self
    {
        $this->options['webhooksOnChannelUpdatedMethod'] = $webhooksOnChannelUpdatedMethod;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnMemberAddedUrl 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMemberAddedUrl(string $webhooksOnMemberAddedUrl): self
    {
        $this->options['webhooksOnMemberAddedUrl'] = $webhooksOnMemberAddedUrl;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnMemberAddedMethod 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMemberAddedMethod(string $webhooksOnMemberAddedMethod): self
    {
        $this->options['webhooksOnMemberAddedMethod'] = $webhooksOnMemberAddedMethod;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnMemberRemovedUrl 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMemberRemovedUrl(string $webhooksOnMemberRemovedUrl): self
    {
        $this->options['webhooksOnMemberRemovedUrl'] = $webhooksOnMemberRemovedUrl;
        return $this;
    }

    /**
     * 
     *
     * @param string $webhooksOnMemberRemovedMethod 
     * @return $this Fluent Builder
     */
    public function setWebhooksOnMemberRemovedMethod(string $webhooksOnMemberRemovedMethod): self
    {
        $this->options['webhooksOnMemberRemovedMethod'] = $webhooksOnMemberRemovedMethod;
        return $this;
    }

    /**
     * 
     *
     * @param int $limitsChannelMembers 
     * @return $this Fluent Builder
     */
    public function setLimitsChannelMembers(int $limitsChannelMembers): self
    {
        $this->options['limitsChannelMembers'] = $limitsChannelMembers;
        return $this;
    }

    /**
     * 
     *
     * @param int $limitsUserChannels 
     * @return $this Fluent Builder
     */
    public function setLimitsUserChannels(int $limitsUserChannels): self
    {
        $this->options['limitsUserChannels'] = $limitsUserChannels;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.IpMessaging.V1.UpdateServiceOptions ' . $options . ']';
    }
}

