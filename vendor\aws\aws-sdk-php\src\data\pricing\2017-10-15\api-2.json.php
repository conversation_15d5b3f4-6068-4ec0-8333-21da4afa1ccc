<?php
// This file was auto-generated from sdk-root/src/data/pricing/2017-10-15/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-10-15', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'api.pricing', 'jsonVersion' => '1.1', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceAbbreviation' => 'AWS Pricing', 'serviceFullName' => 'AWS Price List Service', 'serviceId' => 'Pricing', 'signatureVersion' => 'v4', 'signingName' => 'pricing', 'targetPrefix' => 'AWSPriceListService', 'uid' => 'pricing-2017-10-15', ], 'operations' => [ 'DescribeServices' => [ 'name' => 'DescribeServices', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeServicesRequest', ], 'output' => [ 'shape' => 'DescribeServicesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ExpiredNextTokenException', ], ], ], 'GetAttributeValues' => [ 'name' => 'GetAttributeValues', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAttributeValuesRequest', ], 'output' => [ 'shape' => 'GetAttributeValuesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ExpiredNextTokenException', ], ], ], 'GetPriceListFileUrl' => [ 'name' => 'GetPriceListFileUrl', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPriceListFileUrlRequest', ], 'output' => [ 'shape' => 'GetPriceListFileUrlResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetProducts' => [ 'name' => 'GetProducts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetProductsRequest', ], 'output' => [ 'shape' => 'GetProductsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ExpiredNextTokenException', ], ], ], 'ListPriceLists' => [ 'name' => 'ListPriceLists', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPriceListsRequest', ], 'output' => [ 'shape' => 'ListPriceListsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ExpiredNextTokenException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'AttributeNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'AttributeValue' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'String', ], ], ], 'AttributeValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeValue', ], ], 'BoxedInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'CurrencyCode' => [ 'type' => 'string', 'pattern' => '[A-Z]{3}', ], 'DescribeServicesRequest' => [ 'type' => 'structure', 'members' => [ 'ServiceCode' => [ 'shape' => 'String', ], 'FormatVersion' => [ 'shape' => 'FormatVersion', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'BoxedInteger', ], ], ], 'DescribeServicesResponse' => [ 'type' => 'structure', 'members' => [ 'Services' => [ 'shape' => 'ServiceList', ], 'FormatVersion' => [ 'shape' => 'FormatVersion', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'EffectiveDate' => [ 'type' => 'timestamp', ], 'ExpiredNextTokenException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'Field' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'FileFormat' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'FileFormats' => [ 'type' => 'list', 'member' => [ 'shape' => 'FileFormat', ], ], 'Filter' => [ 'type' => 'structure', 'required' => [ 'Type', 'Field', 'Value', ], 'members' => [ 'Type' => [ 'shape' => 'FilterType', ], 'Field' => [ 'shape' => 'Field', ], 'Value' => [ 'shape' => 'Value', ], ], ], 'FilterType' => [ 'type' => 'string', 'enum' => [ 'TERM_MATCH', ], ], 'Filters' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], 'max' => 50, 'min' => 0, ], 'FormatVersion' => [ 'type' => 'string', 'max' => 32, 'min' => 1, ], 'GetAttributeValuesRequest' => [ 'type' => 'structure', 'required' => [ 'ServiceCode', 'AttributeName', ], 'members' => [ 'ServiceCode' => [ 'shape' => 'String', ], 'AttributeName' => [ 'shape' => 'String', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'BoxedInteger', ], ], ], 'GetAttributeValuesResponse' => [ 'type' => 'structure', 'members' => [ 'AttributeValues' => [ 'shape' => 'AttributeValueList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetPriceListFileUrlRequest' => [ 'type' => 'structure', 'required' => [ 'PriceListArn', 'FileFormat', ], 'members' => [ 'PriceListArn' => [ 'shape' => 'PriceListArn', ], 'FileFormat' => [ 'shape' => 'FileFormat', ], ], ], 'GetPriceListFileUrlResponse' => [ 'type' => 'structure', 'members' => [ 'Url' => [ 'shape' => 'String', ], ], ], 'GetProductsRequest' => [ 'type' => 'structure', 'required' => [ 'ServiceCode', ], 'members' => [ 'ServiceCode' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'Filters', ], 'FormatVersion' => [ 'shape' => 'FormatVersion', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'BoxedInteger', ], ], ], 'GetProductsResponse' => [ 'type' => 'structure', 'members' => [ 'FormatVersion' => [ 'shape' => 'FormatVersion', ], 'PriceList' => [ 'shape' => 'PriceListJsonItems', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'InternalErrorException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'InvalidNextTokenException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'InvalidParameterException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'ListPriceListsRequest' => [ 'type' => 'structure', 'required' => [ 'ServiceCode', 'EffectiveDate', 'CurrencyCode', ], 'members' => [ 'ServiceCode' => [ 'shape' => 'ServiceCode', ], 'EffectiveDate' => [ 'shape' => 'EffectiveDate', ], 'RegionCode' => [ 'shape' => 'RegionCode', ], 'CurrencyCode' => [ 'shape' => 'CurrencyCode', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListPriceListsResponse' => [ 'type' => 'structure', 'members' => [ 'PriceLists' => [ 'shape' => 'PriceLists', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'PriceList' => [ 'type' => 'structure', 'members' => [ 'PriceListArn' => [ 'shape' => 'PriceListArn', ], 'RegionCode' => [ 'shape' => 'RegionCode', ], 'CurrencyCode' => [ 'shape' => 'CurrencyCode', ], 'FileFormats' => [ 'shape' => 'FileFormats', ], ], ], 'PriceListArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 18, 'pattern' => 'arn:[A-Za-z0-9][-.A-Za-z0-9]{0,62}:pricing:::price-list/[A-Za-z0-9+_/.-]{1,1023}', ], 'PriceListJsonItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'SynthesizedJsonPriceListJsonItem', 'jsonvalue' => true, ], ], 'PriceLists' => [ 'type' => 'list', 'member' => [ 'shape' => 'PriceList', ], ], 'RegionCode' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'Service' => [ 'type' => 'structure', 'required' => [ 'ServiceCode', ], 'members' => [ 'ServiceCode' => [ 'shape' => 'String', ], 'AttributeNames' => [ 'shape' => 'AttributeNameList', ], ], ], 'ServiceCode' => [ 'type' => 'string', 'max' => 32, 'min' => 1, ], 'ServiceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Service', ], ], 'String' => [ 'type' => 'string', ], 'SynthesizedJsonPriceListJsonItem' => [ 'type' => 'string', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'Value' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'errorMessage' => [ 'type' => 'string', ], ],];
