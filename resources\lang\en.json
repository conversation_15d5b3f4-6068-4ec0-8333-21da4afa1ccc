{"\tPaid Amount": "<PERSON><PERSON>", " --- Select Account ---": "--- Select Account ---", " Generate With AI": " Generate With AI", " If you have any questions whatsoever, please reply and I’d be happy to clarify them.": "If you have any questions whatsoever, please reply and I’d be happy to clarify them.", " Manually Payment": "Manually Payment", " Pay Now": "Pay Now", " Payment successfully added.": "Payment successfully added.", " Payment successfully added....": " Payment successfully added....", " quantity purchase in bill": "quantity purchase in bill", " quantity sold in": "quantity sold in", " quantity sold in invoice": "quantity sold in invoice", " Remove": "Remove", " Revenue": "Revenue", " simply click on the button below ": "simply click on the button below", " Strictly Cookie Title": "Strictly Cookie Title", " Thank you for joining to ": "Thank you for joining to", " These details will be used to collect invoice payments. Each invoice will have a payment button based on the below configuration.": "These details will be used to collect invoice payments. Each invoice will have a payment button based on the below configuration.", " Transaction fail.": "Transaction fail.", "1,000+ customers": "1,000+ customers", "70% Special Offer": "70% Special Offer", "A new verification link has been sent to the email address you provided during registration.": "A new verification link has been sent to the email address you provided during registration.", "Aamarpay": "<PERSON><PERSON><PERSON><PERSON>", "Accept": "Accept", "Access Token": "Access Token", "Account": "Account", "Account Amount": "Account Amount", "Account Balance": "Account <PERSON><PERSON>", "Account Code": "Account Code", "Account Name": "Account Name", "Account Number": "Account Number", "Account Statement": "Account Statement", "Account Statement Summary": "Account Statement Summary", "Account successfully created.": "Account successfully created.", "Account successfully deleted.": "Account successfully deleted.", "Account successfully updated.": "Account successfully updated.", "Account Type": "Account Type", "Action": "Action", "Add": "Add", "Add Account": "Add Account", "Add Accounts": "Add Accounts", "Add additional security to your account using two factor authentication.": "Add additional security to your account using two factor authentication.", "Add Credit Note": "Add Credit Note", "Add Debit Note": "Add Debit Note", "Add Item": "Add Item", "Add item": "Add item", "Add Payment": "Add Payment", "Add Quantity": "Add Quantity", "Add Receipt": "Add Receipt", "Address": "Address", "after tax & discount": "after tax & discount", "AI Creativity": "AI Creativity", "All": "All", "All Categories": "All Categories", "All Employee": "All Employee", "All items here cannot be deleted.": "All items here cannot be deleted.", "Already convert to Invoice": "Already convert to Invoice", "Already convert to Retainer": "Already convert to Retainer", "Amount": "Amount", "Amount Due": "Amount Due", "Amount successfully transfer updated.": "Amount successfully transfer updated.", "Amount successfully transfer.": "Amount successfully transfer.", "Amount transfer successfully deleted.": "Amount transfer successfully deleted.", "Answer": "Answer", "API Key": "API Key", "API Token": "API Token", "API Token Permissions": "API Token Permissions", "API Tokens": "API Tokens", "API tokens allow third-party services to authenticate with our application on your behalf.": "API tokens allow third-party services to authenticate with our application on your behalf.", "App ID": "App ID", "App Name": "App Name", "App Secret": "App Secret", "App Url": "App Url", "apply": "apply", "Apply": "Apply", "Approval": "Approval", "Apr-Jun": "Apr-Jun", "April": "April", "Are you sure you want to delete your account? Once your account is deleted, all of its resources and data will be permanently deleted. Please enter your password to confirm you would like to permanently delete your account.": "Are you sure you want to delete your account? Once your account is deleted, all of its resources and data will be permanently deleted. Please enter your password to confirm you would like to permanently delete your account.", "Are you sure you would like to delete this API token?": "Are you sure you would like to delete this API token?", "Are You Sure?": "Are You Sure?", "As": "As", "Asset": "<PERSON><PERSON>", "Assets": "Assets", "Assets successfully created.": "Assets successfully created.", "Assets successfully deleted.": "Assets successfully deleted.", "Assets successfully updated.": "Assets successfully updated.", "Assign Permission to Roles": "Assign Permission to Roles", "Attachment": "Attachment", "Attachment successfully deleted!": "Attachment successfully deleted!", "Attachments": "Attachments", "August": "August", "auth.password": "auth.password", "Authorizenet": "Authorizenet", "AuthorizeNet": "AuthorizeNet", "Authorizenet Invoice Payment": "Authorizenet Invoice Payment", "AuthorizeNet Mode": "AuthorizeNet Mode", "Authorizenet Plan Payment": "Authorizenet Plan Payment", "Availabe currencies: XOF, CDF, USD, KMF, GNF": "Availabe currencies: XOF, CDF, USD, KMF, GNF", "Available currencies: XOF, CDF, USD, KMF, GNF": "Available currencies: XOF, CDF, USD, KMF, GNF", "Average Sales": "Average Sales", "Avtar": "Avtar", "Awaiting payment": "Awaiting payment", "AWS S3": "AWS S3", "Back": "Back", "Back to": "Back to", "Balance": "Balance", "Balance Sheet": "Balance Sheet", "Bank": "Bank", "Bank Account": "Bank Account", "Bank Address": "Bank Address", "Bank Balance Transfer": "Bank Balance Transfer", "Bank Branch": "Bank Branch", "Bank Details": "Bank Details", "Bank Details :": "Bank Details :", "Bank Holder Name": "Bank Holder Name", "Bank Name": "Bank Name", "Bank Transfer": "Bank Transfer", "Banking": "Banking", "Banner": "Banner", "Basic Info": "Basic Info", "before tax & discount": "before tax & discount", "Benefit": "Benefit", "Benefit Key": "Benefit Key", "Benefit Secret Key": "Benefit Secret Key", "Best Regards,": "Best Regards,", "Bill": "Bill", "BILL": "BILL", "Bill :": "Bill :", "Bill : ": "Bill :", "Bill Create": "<PERSON>", "Bill Date": "<PERSON>", "Bill Detail": "<PERSON>", "Bill duplicate successfully.": "Bill duplicate successfully.", "Bill Edit": "<PERSON>", "Bill Generated": "<PERSON>", "Bill Logo": "<PERSON>", "Bill Name": "<PERSON>", "Bill Not Found.": "<PERSON> Not Found.", "Bill Number": "<PERSON>", "Bill Payment Create": "Bill Payment Create", "Bill Prefix": "<PERSON>", "Bill Print Settings": "Bill Print Settings", "Bill product not found.": "Bill product not found.", "Bill product successfully deleted.": "Bill product successfully deleted.", "Bill Send": "<PERSON>", "Bill Setting updated successfully": "<PERSON> updated successfully", "Bill Starting Number": "<PERSON> Starting Number", "Bill successfully created.": "<PERSON> successfully created.", "Bill successfully deleted.": "<PERSON> successfully deleted.", "Bill successfully sent.": "<PERSON> successfully sent.", "Bill successfully updated.": "Bill successfully updated.", "Bill Summary": "<PERSON>", "Bill Template": "<PERSON>", "Bill to": "Bill to", "Bill To": "Bill <PERSON>", "Bill To:": "Bill To:", "Bill Url": "<PERSON>", "Billed To": "Billed To", "Billing Address": "Billing Address", "BIlling Address": "BIlling Address", "Billing City": "Billing City", "Billing Country": "Billing Country", "Billing Info": "Billing Info", "Billing Name": "Billing Name", "Billing Phone": "Billing Phone", "Billing State": "Billing State", "Billing Zip": "Billing Zip", "Bills": "Bills", "Bills Monthly Statistics": "Bills Monthly Statistics", "Bills Weekly Statistics": "Bills Weekly Statistics", "Brand Settings": "Brand Settings", "Browser Language": "Browser Language", "Browser Name": "Browser Name", "Browser Sessions": "Browser Sessions", "Budget Not Found.": "Budget Not Found.", "Budget Period": "Budget Period", "Budget Planner": "Budget Planner", "Budget planner successfully created.": "Budget planner successfully created.", "Budget Planner successfully deleted.": "Budget Planner successfully deleted.", "Budget planner successfully updated.": "Budget planner successfully updated.", "Budget Vs Actual: ": "Budget Vs Actual:", "button": "button", "Buy Now Link": "Buy Now Link", "Cache Settings": "<PERSON><PERSON>", "Cancel": "Cancel", "Card Number": "Card Number", "Cash Flow": "Cash Flow", "Cashflow": "Cashflow", "Cashfree": "Cashfree", "Cashfree Key": "Cashfree Key", "Cashfree Secret Key": "Cashfree Secret Key", "Category": "Category", "Category Code": "Category Code", "Category code": "Category code", "Category Color": "Category Color", "Category Name": "Category Name", "Category successfully created.": "Category successfully created.", "Category successfully deleted.": "Category successfully deleted.", "Category successfully updated.": "Category successfully updated.", "Category Type": "Category Type", "Change Password": "Change Password", "Channel": "Channel", "Chart Of Account": "Chart Of Account", "Chart of Account": "Chart of Account", "Chart of account type successfully created.": "Chart of account type successfully created.", "Chart of account type successfully deleted.": "Chart of account type successfully deleted.", "Chart of account type successfully updated.": "Chart of account type successfully updated.", "Chart of Accounts": "Chart of Accounts", "Chat GPT Key": "Chat GPT Key", "Chat GPT Key Settings": "Chat GPT Key Settings", "Chat GPT Model name": "Chat GPT Model name", "Chatgpykey successfully saved.": "<PERSON><PERSON><PERSON><PERSON><PERSON> successfully saved.", "Choose file here": "Choose file here", "CinetPay": "CinetPay", "Cinetpay": "Cinetpay", "Cinetpay Api Key": "Cinetpay Api Key", "Cinetpay Secret Key": "Cinetpay Secret Key", "Cinetpay Site Id": "Cinetpay Site Id", "City": "City", "Clear": "Clear", "Clear Cache": "<PERSON>ache", "Click here to add New User": "Click here to add New User", "Click to change status": "Click to change status", "Click to copy invoice link": "Click to copy invoice link", "Click to copy Retainer link": "Click to copy Retainer link", "Click to Upgrade Plan": "Click to Upgrade Plan", "Client": "Client", "Client ID": "Client ID", "Close": "Close", "Code": "Code", "Coingate": "Coingate", "CoinGate": "CoinGate", "CoinGate Auth Token": "CoinGate Auth Token", "CoinGate Mode": "CoinGate Mode", "Collapse": "Collapse", "Color Input": "Color Input", "Comment": "Comment", "Comment Added Successfully!": "Comment Added Successfully!", "Comment successfully deleted!": "Comment successfully deleted!", "Comments": "Comments", "Company Info": "Company Info", "Company Name": "Company Name", "Company Name *": "Company Name *", "Company Registration Number *": "Company Registration Number *", "Company Settings": "Company Settings", "Company Signature": "Company Signature", "Confirm New Password": "Confirm New Password", "Confirm Password": "Confirm Password", "Constant": "Constant", "Contact": "Contact", "Contact Number": "Contact Number", "Contact Us Description": "Contact Us Description", "Contact Us URL": "Contact Us URL", "Contract": "Contract", "Contract Customer": "Contract Customer", "Contract Description successfully saved.": "Contract Description successfully saved.", "Contract Detail": "Contract Detail", "Contract End_Date": "Contract End_Date", "Contract Number   :": "Contract Number   :", "Contract Signed successfully": "Contract Signed successfully", "Contract Start_Date": "Contract Start_Date", "Contract Subject": "Contract Subject", "Contract successfully created.": "Contract successfully created.", "Contract successfully deleted!": "Contract successfully deleted!", "Contract successfully deleted.": "Contract successfully deleted.", "Contract successfully updated.": "Contract successfully updated.", "Contract Type": "Contract Type", "Contract Type   :": "Contract Type   :", "Contract Type successfully created.": "Contract Type successfully created.", "Contract Type successfully deleted.": "Contract Type successfully deleted.", "Contract Type successfully updated.": "Contract Type successfully updated.", "Contract Value": "Contract Value", "Convert into  Invoice": "Convert into  Invoice", "Convert into Invoice": "Convert into Invoice", "Convert into Retainer": "Convert into Retainer", "Convert to Invoice": "Convert to Invoice", "Convert to Retainer": "Convert to Retainer", "Cookie Description": "<PERSON><PERSON>", "Cookie Settings": "<PERSON><PERSON>", "Cookie Title": "<PERSON>ie Title", "Copy": "Copy", "copy bill": "copy bill", "Copy proposal": "Copy proposal", "Copy Retainer": "Copy Retainer", "Copy Selected Text": "Copy Selected Text", "Copy Text": "Copy Text", "Country": "Country", "Country Code": "Country Code", "Coupon": "Coupon", "Coupon code required.": "Coupon code required.", "Coupon Discount": "Coupon Discount", "Create": "Create", "Create account": "Create account", "Create account here.": "Create account here.", "Create API Token": "Create API Token", "Create bill": "Create bill", "Create Bill": "Create Bill", "Create bill here.": "Create bill here.", "Create Budget Planner": "Create Budget Planner", "Create category": "Create category", "Create category here.": "Create category here.", "Create contract type": "Create contract type", "Create contract type here.": "Create contract type here.", "Create customer": "Create customer", "Create Customer": "Create Customer", "Create customer here.": "Create customer here.", "Create Discover": "Create Discover", "Create expense account": "Create expense account", "Create expense account here.": "Create expense account here.", "Create FAQ": "Create FAQ", "Create Features": "Create Features", "Create Features Block": "Create Features Block", "Create from account": "Create from account", "Create from account here.": "Create from account here.", "Create income account": "Create income account", "Create income account here.": "Create income account here.", "Create invoice": "Create invoice", "Create Invoice": "Create Invoice", "Create invoice here.": "Create invoice here.", "Create Language": "Create Language", "Create New Account": "Create New Account", "Create New Assets": "Create New Assets", "Create New Bank Account": "Create New Bank Account", "Create New Bill": "Create New Bill", "Create New Category": "Create New Category", "Create New Contract": "Create New Contract", "Create New contracts": "Create New contracts", "Create New Credit Note": "Create New Credit Note", "Create New Custom Field": "Create New Custom Field", "Create New Customer": "Create New Customer", "Create New Debit Note": "Create New Debit Note", "Create New Expense": "Create New Expense", "Create New Goal": "Create New Goal", "Create New Invoice": "Create New Invoice", "Create New Journal": "Create New Journal", "Create New Language": "Create New Language", "Create New Payment": "Create New Payment", "Create New Product": "Create New Product", "Create New Proposal": "Create New Proposal", "Create New Revenue": "Create New Revenue", "Create New Signature": "Create New Signature", "Create New Team": "Create New Team", "Create New Type": "Create New Type", "Create New Unit": "Create New Unit", "Create New User": "Create New User", "Create New Vendor": "Create <PERSON>or", "Create Page": "Create Page", "Create Product & Service": "Create Product & Service", "Create Proposal": "Create Proposal", "Create Retainer": "Create Retainer", "Create Role": "Create Role", "Create Screenshot": "Create Screenshot", "Create Signature": "Create Signature", "Create tax": "Create tax", "Create tax here.": "Create tax here.", "Create Tax Rate": "Create Tax Rate", "Create Testimonial": "Create Testimonial", "Create to account": "Create to account", "Create to account here.": "Create to account here.", "Create Transfer": "Create Transfer", "Create unit": "Create unit", "Create unit here.": "Create unit here.", "Create User": "Create User", "Create user role": "Create user role", "Create user role here.": "Create user role here.", "Create vendor": "Create vendor", "Create vendor here.": "Create vendor here.", "created by": "created by", "Created on ": "Created on", "Created.": "Created.", "Credit": "Credit", "Credit / Debit Card": "Credit / Debit Card", "Credit Note": "Credit Note", "Credit Note successfully created.": "Credit Note successfully created.", "Credit Note successfully deleted.": "Credit Note successfully deleted.", "Credit Note successfully updated.": "Credit Note successfully updated.", "Credit Note Summary": "Credit Note Summary", "Currency *": "Currency *", "Currency Symbol *": "Currency Symbol *", "Currency Symbol Position": "Currency Symbol Position", "Current Balance": "Current Balance", "Current cache size": "Current cache size", "Current Password": "Current Password", "Current Quantity": "Current Quantity", "Current year": "Current year", "Current Year": "Current Year", "Custom Field": "Custom Field", "Custom Field Name": "Custom Field Name", "Custom Field successfully created!": "Custom Field successfully created!", "Custom Field successfully deleted!": "Custom Field successfully deleted!", "Custom Field successfully updated!": "Custom Field successfully updated!", "Custom Page": "Custom Page", "Customer": "Customer", "Customer Id": "Customer Id", "Customer Info": "Customer Info", "Customer Invoice Send": "Customer Invoice Send", "Customer Login": "Customer <PERSON><PERSON>", "Customer Name": "Customer Name", "Customer Prefix": "Customer Prefix", "Customer Signature": "Customer Signature", "Customer Statement": "Customer Statement", "Customer Statement for Aaron Clark": "Customer Statement for <PERSON>", "Customer successfully created.": "Customer successfully created.", "Customer successfully deleted.": "Customer successfully deleted.", "Customer successfully updated.": "Customer successfully updated.", "Customers": "Customers", "CVV": "CVV", "Dark Layout": "Dark Layout", "Dashboard": "Dashboard", "Date": "Date", "Date Format": "Date Format", "Date of Creation": "Date of Creation", "Days": "Days", "Dear ": "Dear", "Debit": "Debit", "Debit and Credit must be Equal.": "Debit and Credit must be Equal.", "Debit Note": "Debit Note", "Debit Note successfully created.": "Debit Note successfully created.", "Debit Note successfully deleted.": "Debit Note successfully deleted.", "Debit Note successfully updated.": "Debit Note successfully updated.", "Debit Note Summary": "Debit Note Summary", "December": "December", "Decimal Number Format": "Decimal Number Format", "Decline": "Decline", "Default Language": "Default Language", "Delete": "Delete", "Delete Account": "Delete Account", "Delete API Token": "Delete API Token", "Delete Customer": "Delete Customer", "Delete Notes": "Delete Notes", "Description": "Description", "Descriptions": "Descriptions", "Designation": "Designation", "Detail": "Detail", "Device Type": "Device Type", "Disable": "Disable", "Disabled": "Disabled", "Discount": "Discount", "Discover": "Discover", "Discover List": "Discover List", "Display On Dashboard": "Display On Dashboard", "Display Shipping in Proposal / Invoice / Bill": "Display Shipping in Proposal / Invoice / Bill", "div": "div", "Done.": "Done.", "Double Entry": "Double Entry", "Download": "Download", "Download cookie accepted data": "Download cookie accepted data", "Download sample customer CSV file": "Download sample customer CSV file", "Download sample product CSV file": "Download sample product CSV file", "Download sample vendor CSV file": "Download sample vendor CSV file", "Due": "Due", "Due Amount": "Due Amount", "Due Date": "Due Date", "Due Date:": "Due Date:", "Duplicate": "Duplicate", "Duplicate Bill": "Duplicate Bill", "Duplicate Contract": "Duplicate Contract", "Duplicate Contract successfully created.": "Duplicate Contract successfully created.", "Duplicate Invoice": "Duplicate Invoice", "Duplicate Proposal": "Duplicate Proposal", "Duration": "Duration", "E-Mail": "E-Mail", "E-Mail Address": "E-Mail Address", "E-Mail has been not sent due to SMTP configuration": "E-Mail has been not sent due to SMTP configuration", "Edit": "Edit", "Edit Account": "Edit Account", "Edit Assets": "Edit Assets", "Edit Bank Account": "Edit Bank Account", "Edit Budget Planner": "Edit Budget Planner", "Edit Contract": "Edit Contract", "Edit Contract Type": "Edit Contract Type", "Edit Credit Note": "Edit Credit Note", "Edit Custom Field": "Edit Custom Field", "Edit Customer": "Edit Customer", "Edit Debit Note": "Edit Debit Note", "Edit Expense": "Edit Expense", "Edit Goal": "Edit Goal", "Edit Journal": "Edit Journal", "Edit Page": "Edit Page", "Edit Payment": "Edit Payment", "Edit Product & Service": "Edit Product & Service", "Edit Product Category": "Edit Product Category", "Edit Revenue": "Edit Revenue", "Edit Role": "Edit Role", "Edit Tax Rate": "Edit Tax Rate", "Edit Transfer": "Edit Transfer", "Edit Unit": "Edit Unit", "Edit User": "Edit User", "Edit Vendor": "<PERSON>", "Edit Webhook": "Edit Webhook", "Edit your brand details": "Edit your brand details", "Edit your company bill details": "Edit your company bill details", "Edit your company details": "Edit your company details", "Edit your company invoice details": "Edit your company invoice details", "Edit your company proposal details": "Edit your company proposal details", "Edit your company retainer details": "Edit your company retainer details", "Edit your company twilio setting details": "Edit your company twilio setting details", "Edit your key details": "Edit your key details", "Edit your system details": "Edit your system details", "Email": "Email", "Email Message": "Email Message", "Email Notification Settings": "Email Notification Settings", "Email send Successfully": "Email send Successfully", "Email Send successfully!": "Email Send successfully!", "Email setting successfully updated.": "Email setting successfully updated.", "Email Settings": "<PERSON><PERSON>s", "Email Template": "<PERSON>ail Te<PERSON>late", "Email Template Detail successfully updated.": "Email Template Detail successfully updated.", "Email Template successfully created.": "Email Template successfully created.", "Email Template successfully updated.": "Email Template successfully updated.", "Email Templates": "Email Templates", "Enable": "Enable", "Enable cookie": "Enable cookie", "Enable Landing Page": "Enable Landing Page", "Enable logging": "Enable logging", "Enable RTL": "Enable RTL", "Enable/Disable": "Enable/Disable", "Enable:": "Enable:", "Enabled": "Enabled", "End Date": "End Date", "End Date   :": "End Date   :", "End Date   : ": "End Date   :", "End Month": "End Month", "Ensure your account is using a long, random password to stay secure.": "Ensure your account is using a long, random password to stay secure.", "Enter Account Number": "Enter Account Number", "Enter Address": "Enter Address", "Enter Amount": "Enter Amount", "Enter Answer": "Enter Answer", "Enter Bank Address": "Enter Bank Address", "Enter Bank Holder Name": "Enter Bank Holder Name", "Enter Bank Name": "Enter Bank Name", "Enter Benefit Key": "Enter Benefit Key", "Enter Benefit Secret key": "Enter Benefit Secret key", "Enter Bill Prefix": "Enter <PERSON>", "Enter Bill Starting Number": "Enter Bill Starting Number", "Enter Billing Address": "Enter Billing Address", "Enter Billing City": "Enter Billing City", "Enter Billing Country": "Enter Billing Country", "Enter Billing Name": "Enter Billing Name", "Enter Billing Phone": "Enter Billing Phone", "Enter Billing State": "Enter Billing State", "Enter Billing Zip": "Enter Billing Zip", "Enter Cashfree Key": "Enter Cashfree Key", "Enter Cashfree Secret key": "Enter Cashfree Secret key", "Enter Category Name": "Enter Category Name", "Enter Chatgpt Key Here": "Enter Chatgpt Key Here", "Enter Chatgpt Model Name": "Enter Chatgpt Model Name", "Enter City": "Enter City", "Enter Code": "Enter Code", "Enter Company Email": "Enter Company Email", "Enter Company Name": "Enter Company Name", "Enter Company Password": "Enter Company Password", "Enter Company Registration Number": "Enter Company Registration Number", "Enter Confirm Password": "Enter Confirm Password", "Enter Contact": "Enter Contact", "Enter Contact Number": "Enter Contact Number", "Enter Contact Us Description": "Enter Contact Us Description", "Enter Contact Us URL": "Enter Contact Us URL", "Enter Contract Value": "Enter Contract Value", "Enter Cookie Description": "Enter <PERSON>ie <PERSON>", "Enter Cookie Title": "Enter <PERSON><PERSON> Title", "Enter Country": "Enter Country", "Enter Coupon Code": "Enter Coupon Code", "Enter Currency": "<PERSON><PERSON>", "Enter Currency Symbol": "Enter Currency Symbol", "Enter Custom Field Name": "Enter Custom Field Name", "Enter Customer Prefix": "Enter Customer Prefix", "Enter Decimal Number Format": "Enter Decimal Number Format", "Enter Description": "Enter Description", "Enter Designation": "Enter Designation", "Enter Email": "<PERSON><PERSON>", "Enter email": "Enter email", "Enter Email address": "Enter Email address", "Enter Footer Text": "Enter Footer Text", "Enter Full Name": "Enter Full Name", "Enter Google Recaptcha Key": "Enter Google Recaptcha Key", "Enter Google Recaptcha Secret Key": "Enter Google Recaptcha Secret Key", "Enter Heading": "Enter Heading", "Enter Invoice Prefix": "Enter Invoice Prefix", "Enter Invoice Starting Number": "Enter Invoice Starting Number", "Enter Invoice/Bill Footer Notes": "Enter Invoice/Bill <PERSON> Notes", "Enter Invoice/Bill Footer Title": "Enter Invoice/<PERSON> Title", "Enter Journal Prefix": "Enter Journal Prefix", "Enter Language Code": "Enter Language Code", "Enter Link": "Enter Link", "Enter Long Description": "Enter Long Description", "Enter Mail Driver": "Enter Mail Driver", "Enter Mail Encryption": "Enter Mail Encryption", "Enter Mail From Address": "Enter Mail From Address", "Enter Mail From Name": "Enter Mail From Name", "Enter Mail Host": "Enter Mail Host", "Enter Mail Password": "Enter Mail Password", "Enter Mail Port": "Enter Mail Port", "Enter Mail Username": "Enter Mail Username", "Enter Mobile Number": "Enter Mobile Number", "Enter Name": "Enter Name", "Enter New Password": "Enter New Password", "Enter Old Password": "Enter Old Password", "Enter Opening Balance": "Enter Opening Balance", "Enter Order Number": "Enter Order Number", "Enter Page Name": "Enter Page Name", "Enter Page URL": "Enter Page URL", "Enter Password": "Enter Password", "Enter Permission Name": "Enter Permission Name", "Enter Phone": "Enter Phone", "Enter Proposal Prefix": "Enter Proposal Prefix", "Enter Proposal Starting Number": "Enter Proposal Starting Number", "Enter Purchase Price": "Enter Purchase Price", "Enter Quantity": "Enter Quantity", "Enter Question": "Enter Question", "Enter Questions": "Enter Questions", "Enter Ref Number": "Enter Ref Number", "Enter Reference": "Enter Reference", "Enter Retainer Prefix": "Enter Retainer Prefix", "Enter Retainer Starting Number": "Enter Retainer Starting Number", "Enter Role Name": "Enter Role Name", "Enter Sale Price": "Enter Sale Price", "Enter Shipping Address": "Enter Shipping Address", "Enter Shipping City": "Enter Shipping City", "Enter Shipping Country": "Enter Shipping Country", "Enter Shipping Name": "Enter Shipping Name", "Enter Shipping Phone": "Enter Shipping Phone", "Enter Shipping State": "Enter Shipping State", "Enter Shipping Zip": "Enter Shipping Zip", "Enter SKU": "Enter SKU", "Enter Star": "Enter Star", "Enter State": "Enter State", "Enter Strictly Cookie Description": "Enter Strictly Cookie <PERSON>", "Enter Strictly Cookie Title": "Enter Strictly Cookie Title", "Enter Stripe Key": "Enter Stripe Key", "Enter Subject": "Enter Subject", "Enter Tax Number": "Enter Tax Number", "Enter Tax Rate %": "Enter Tax Rate %", "Enter Tax Rate Name": "Enter Tax Rate Name", "Enter Telephone": "Enter Telephone", "Enter Title": "Enter Title", "Enter Title Text": "Enter Title Text", "Enter Twilio From": "<PERSON><PERSON>", "Enter Twilio SID": "<PERSON><PERSON>", "Enter Twilio Token": "<PERSON><PERSON> <PERSON>", "Enter Unit Name": "Enter Unit Name", "Enter Url": "Enter Url", "Enter User Email": "Enter User Email", "Enter User Name": "Enter User Name", "Enter valid amount.": "Enter valid amount.", "Enter VAT / GST Number": "Enter VAT / GST Number", "Enter Vender Prefix": "Enter Vender Prefix", "Enter Your Confirm Password": "Enter Your Confirm Password", "Enter Your Email   ": "Enter Your Email", "Enter Your Email Address": "Enter Your Email Address", "Enter Your Name": "Enter Your Name", "Enter Your Password": "Enter Your Password", "Enter Zip Code": "Enter Zip Code", "Enter Zip/Post Code": "Enter Zip/Post Code", "entries": "entries", "error": "error", "Error": "Error", "Example : Bank : Bank name </br> Account Number : 0000 0000 </br>": "Example : Bank : Bank name </br> Account Number : 0000 0000 </br>", "Expand": "Expand", "Expense": "Expense", "Expense :": "Expense :", "Expense : ": "Expense :", "Expense = Payment + Bill :": "Expense = Payment + Bill :", "Expense Account": "Expense Account", "Expense By Category": "Expense By Category", "Expense successfully created.": "Expense successfully created.", "Expense successfully deleted.": "Expense successfully deleted.", "Expense successfully updated.": "Expense successfully updated.", "Expense Summary": "Expense Summary", "Expense tax not found": "Expense tax not found", "Expense This Month": "Expense This Month", "Expense Today": "Expense Today", "Expenses": "Expenses", "Expiration": "Expiration", "Export": "Export", "FAQ": "FAQ", "FAQ List": "FAQ List", "Favicon": "Favicon", "Feature": "Feature", "Feature Section Head": "Feature Section Head", "Features": "Features", "Features Block List": "Features Block List", "Features List": "Features List", "February": "February", "Fedapay": "Fedapay", "Fedapay Mode": "Fedapay Mode", "Feel free to reach out if you have any questions.": "Feel free to reach out if you have any questions.", "File is not exist.": "File is not exist.", "File size exceeds the maximum limit of ": "File size exceeds the maximum limit of", "Filter": "Filter", "Flutterware": "Flutterware", "Flutterwave": "Flutterwave", "Footer": "Footer", "Footer Text": "Footer Text", "For chart representation": "For chart representation", "For What": "For What", "Forgot Password": "Forgot Password", "Forgot Your Password?": "Forgot Your Password?", "From": "From", "From Account": "From Account", "From Date": "From Date", "FROM:": "FROM:", "From:": "From:", "Full Name": "Full Name", "General": "General", "Generate": "Generate", "Generate content with AI": "Generate content with AI", "Generate Content With AI": "Generate Content With AI", "Generate Grammar": "Generate Grammar", "Generate with AI": "Generate with AI", "Get Paid": "Get Paid", "Goal": "Goal", "Goal successfully created.": "Goal successfully created.", "Goal successfully deleted.": "Goal successfully deleted.", "Goal successfully updated.": "Goal successfully updated.", "Google Recaptcha Key": "Google Recaptcha Key", "Google Recaptcha Secret Key": "Google Recaptcha Secret Key", "Google Recaptcha Version": "Google Recaptcha Version", "Grammar check with AI": "Grammar check with AI", "Gross Profit": "Gross Profit", "GST Number": "GST Number", "Header": "Header", "Heading": "Heading", "Hi ": "Hi", "Hi Dear,": "Hi <PERSON>,", "Hi, ": "Hi,", "High": "High", "Holder Name": "Holder Name", "Home": "Home", "Home Section": "Home Section", "Horizontal View": "Horizontal View", "If necessary, you may log out of all of your other browser sessions across all of your devices. Some of your recent sessions are listed below; however, this list may not be exhaustive. If you feel your account has been compromised, you should also update your password.": "If necessary, you may log out of all of your other browser sessions across all of your devices. Some of your recent sessions are listed below; however, this list may not be exhaustive. If you feel your account has been compromised, you should also update your password.", "If the link doesn’t work, copy this URL into your browser": "If the link doesn’t work, copy this URL into your browser", "If you did not request a password reset, no further action is required..": "If you did not request a password reset, no further action is required..", "If you’re having trouble clicking the \"Reset Password\" button, copy and paste the URL below into your web browser:": "If you’re having trouble clicking the \"Reset Password\" button, copy and paste the URL below into your web browser:", "Image": "Image", "Import": "Import", "Import customer CSV file": "Import customer CSV file", "Import product CSV file": "Import product CSV file", "Income": "Income", "Income & Expense": "Income & Expense", "Income :": "Income :", "Income : ": "Income :", "Income = Revenue + Invoice :": "Income = Revenue + Invoice :", "Income Account": "Income Account", "Income By Category": "Income By Category", "Income Period": "Income Period", "Income Summary": "Income Summary", "Income tax not found": "Income tax not found", "Income This Month": "Income This Month", "Income Today": "Income Today", "Income VS Expense": "Income VS Expense", "Income Vs Expense": "Income Vs Expense", "Income Vs Expense Summary": "Income Vs Expense Summary", "Income vs Expense Summary": "Income vs Expense Summary", "Industry Type": "Industry Type", "Invalid amount.": "Invalid amount.", "Invalid file type. Please select a valid file ": "Invalid file type. Please select a valid file", "Invoice": "Invoice", "invoice": "invoice", "INVOICE": "INVOICE", "Invoice ": "Invoice", "Invoice :": "Invoice :", "Invoice : ": "Invoice :", "Invoice Create": "Invoice Create", "Invoice Detail": "Invoice Detail", "Invoice duplicate successfully.": "Invoice duplicate successfully.", "Invoice Edit": "Invoice Edit", "Invoice Generated": "Invoice Generated", "Invoice is deleted.": "Invoice is deleted.", "Invoice is not found": "Invoice is not found", "Invoice Logo": "Invoice Logo", "Invoice Name": "Invoice Name", "Invoice not found": "Invoice not found", "Invoice not found!": "Invoice not found!", "Invoice not found.": "Invoice not found.", "Invoice Number": "Invoice Number", "Invoice paid Successfully!": "Invoice paid Successfully!", "Invoice payment Create": "Invoice payment Create", "Invoice payment failed.": "Invoice payment failed.", "Invoice payment request send successfully.": "Invoice payment request send successfully.", "Invoice Prefix": "Invoice Prefix", "Invoice Print Settings": "Invoice Print Settings", "Invoice product not found!": "Invoice product not found!", "Invoice product successfully deleted.": "Invoice product successfully deleted.", "Invoice Reminder": "Invoice Reminder", "Invoice Send": "Invoice Send", "Invoice Setting updated successfully": "Invoice Setting updated successfully", "Invoice Starting Number": "Invoice Starting Number", "Invoice successfully .": "Invoice successfully .", "Invoice successfully created.": "Invoice successfully created.", "Invoice successfully deleted.": "Invoice successfully deleted.", "Invoice successfully sent.": "Invoice successfully sent.", "Invoice successfully updated.": "Invoice successfully updated.", "Invoice Summary": "Invoice Summary", "Invoice Template": "Invoice Template", "Invoice Total": "Invoice Total", "Invoice URL": "Invoice URL", "Invoice Url": "Invoice Url", "Invoice/Bill Footer Notes": "Invoice/<PERSON> Notes", "Invoice/Bill Footer Title": "Invoice/<PERSON> Title", "Invoices": "Invoices", "Invoices Monthly Statistics": "Invoices Monthly Statistics", "Invoices Weekly Statistics": "Invoices Weekly Statistics", "Ip": "Ip", "Is Dashboard Display": "Is Dashboard Display", "Is Enabled": "Is Enabled", "Isp": "Isp", "Issue Date": "Issue Date", "Issue Date:": "Issue Date:", "Item": "<PERSON><PERSON>", "Items": "Items", "Iyzipay": "Iyzipay", "IyziPay": "IyziPay", "IyziPay Mode": "IyziPay Mode", "I’m sure you’re busy, but I’d appreciate if you could take a moment and look over the invoice when you get a chance.": "I’m sure you’re busy, but I’d appreciate if you could take a moment and look over the invoice when you get a chance.", "Jan-Mar": "Jan-<PERSON>", "January": "January", "Join Us": "Join Us", "Join Us User": "Join <PERSON>r", "Join User": "Join User", "Journal": "Journal", "Journal Account": "Journal Account", "Journal Account Summary": "Journal Account Summary", "Journal Date": "Journal Date", "Journal Detail": "Journal Detail", "Journal Entry": "Journal Entry", "Journal entry account successfully deleted.": "Journal entry account successfully deleted.", "Journal Entry Create": "Journal Entry Create", "Journal Entry Edit": "Journal Entry Edit", "Journal entry successfully created.": "Journal entry successfully created.", "Journal entry successfully deleted.": "Journal entry successfully deleted.", "Journal entry successfully updated.": "Journal entry successfully updated.", "Journal ID": "Journal ID", "Journal No": "Journal No", "Journal Number": "Journal Number", "Journal Prefix": "Journal Prefix", "Journal Ref": "Journal Ref", "Journal successfully deleted.": "Journal successfully deleted.", "Jul-Sep": "Jul-Sep", "July": "July", "June": "June", "Khalti": "Khalti", "Khalti Mode": "Khalti Mode", "label": "label", "Labels": "Labels", "Landing Page": "<PERSON>", "Language": "Language", "Language change successfully.": "Language change successfully.", "Language Code": "Language Code", "Language Deleted Successfully.": "Language Deleted Successfully.", "Language Disabled Successfully": "Language Disabled Successfully", "Language Enabled Successfully": "Language Enabled Successfully", "Language save successfully.": "Language save successfully.", "Language successfully created.": "Language successfully created.", "Last 30 Days Total Contracts": "Last 30 Days Total Contracts", "Last active": "Last active", "Last Login": "Last Login", "Last Login At": "Last Login At", "Last used": "Last used", "Latest Expense": "Latest Expense", "Latest Income": "Latest Income", "Latitude": "Latitude", "Layout settings": "Layout settings", "Ledger Summary": "Ledger Summary", "Less Quantity": "Less Quantity", "li": "li", "Liabilities & Equity": "Liabilities & Equity", "Lifetime": "Lifetime", "Link Copy on Clipboard": "<PERSON>py on Clipboard", "List View": "List View", "Live": "Live", "Live Demo Link": "Live Demo Link", "Local": "Local", "Local Settings": "Local Settings", "Log in": "Log in", "Log Out": "Log Out", "Log Out Other Browser Sessions": "Log Out Other Browser Sessions", "Login": "<PERSON><PERSON>", "login": "login", "Login Disable": "Login Disable", "Login Enable": "Login Enable", "Login is enable": "Login is enable", "Logo": "Logo", "Logo dark": "Logo dark", "Logo Light": "Logo Light", "Logout": "Logout", "Long Description": "Long Description", "Longitude": "Longitude", "Low": "Low", "Mail Driver": "Mail Driver", "Mail Encryption": "Mail Encryption", "Mail From Address": "Mail From Address", "Mail From Name": "Mail From Name", "Mail Host": "Mail Host", "Mail not send, email is empty": "Mail not send, email is empty", "Mail not send, email not found": "Mail not send, email not found", "Mail Password": "Mail Password", "Mail Port": "Mail Port", "Mail Username": "Mail Username", "Make Payment": "Make Payment", "Make this a sub-account": "Make this a sub-account", "Manage Account": "Manage Account", "Manage and log out your active sessions on other browsers and devices.": "Manage and log out your active sessions on other browsers and devices.", "Manage API Tokens": "Manage API Tokens", "Manage Assets": "Manage Assets", "Manage Bank Account": "Manage Bank Account", "Manage Bills": "Manage Bills", "Manage Budget Planner": "Manage Budget Planner", "Manage Chart of Account Type": "Manage Chart of Account Type", "Manage Chart of Accounts": "Manage Chart of Accounts", "Manage Contract": "Manage Contract", "Manage Contract Type": "Manage Contract Type", "Manage Credit Notes": "Manage Credit Notes", "Manage Custom Field": "Manage Custom Field", "Manage Customer-Detail": "Manage Customer-Detail", "Manage Customers": "Manage Customers", "Manage Debit Notes": "Manage Debit Notes", "Manage Email Templates": "Manage Email Templates", "Manage Expense": "Manage Expense", "Manage Goals": "Manage Goals", "Manage Invoices": "Manage Invoices", "Manage Journal Entry": "Manage Journal Entry", "Manage Language": "Manage Language", "Manage Notification Templates": "Manage Notification Templates", "Manage Order Summary": "Manage Order Summary", "Manage Payments": "Manage Payments", "Manage Product & Service Unit": "Manage Product & Service Unit", "Manage Product & Services": "Manage Product & Services", "Manage Product Stock": "Manage Product Stock", "Manage Product-Service & Income-Expense Category": "Manage Product-Service & Income-Expense Category", "Manage Proposals": "Manage Proposals", "Manage Retainers": "Manage Retainers", "Manage Revenues": "Manage Revenues", "Manage Roles": "Manage Roles", "Manage Tax Rate": "Manage Tax Rate", "Manage Team": "Manage Team", "Manage User": "Manage User", "Manage Users logs": "Manage Users logs", "Manage Vendor-Detail": "Manage Vendor-Detail", "Manage Vendors": "Manage Vendors", "March": "March", "Mark Sent": "<PERSON>", "Max upload size": "Max upload size", "Max upload size ( In KB)": "Max upload size ( In KB)", "Maximum Result Length": "Maximum Result Length", "May": "May", "MB": "MB", "Meduium": "Meduium", "Menu Bar": "Menu Bar", "Mercado": "<PERSON><PERSON><PERSON>", "Mercado Mode": "Mercado Mode", "Mercado Pago": "Mercado <PERSON>", "Merchant ID": "Merchant ID", "Merchant Id": "Merchant Id", "Merchant Key": "Merchant Key", "Merchant Login ID": "Merchant Login ID", "Merchant Salt": "Merchant Salt", "Merchant Secret": "Merchant Secret", "Merchant Transaction Key": "Merchant Transaction Key", "Message": "Message", "Messages": "Messages", "Meta Description": "Meta Description", "Meta Image": "Meta Image", "Meta Keywords": "Meta Keywords", "Method": "Method", "Midtrans": "Midtrans", "Midtrans Mode": "Midtrans Mode", "Mobile No": "Mobile No", "Mobile Number": "Mobile Number", "Module": "<PERSON><PERSON><PERSON>", "Module Settings": "<PERSON><PERSON><PERSON>", "Modules": "<PERSON><PERSON><PERSON>", "Mollie": "<PERSON><PERSON>", "Mollie Api Key": "<PERSON><PERSON>", "Mollie Partner ID": "Mollie Partner ID", "Mollie Profile ID": "Mollie Profile ID", "Month": "Month", "Monthly": "Monthly", "Monthly Cashflow": "Monthly Cashflow", "Months": "Months", "More Information": "More Information", "My Company": "My Company", "My Profile": "My Profile", "N/A": "N/A", "Name": "Name", "Name on card": "Name on card", "Nepalste": "Nepalste", "Nepalste Mode": "Nepalste Mode", "Nepalste Public Key": "Nepalste Public Key", "Nepalste Secret Key": "Nepalste Secret Key", "Net Profit": "Net Profit", "NET PROFIT :": "NET PROFIT :", "Net Profit = Total Income - Total Expense": "Net Profit = Total Income - Total Expense", "Net Profit = Total Income - Total Expense ": "Net Profit = Total Income - Total Expense", "Net Profit/Loss": "Net Profit/Loss", "New Bill": "New Bill", "New Customer": "New Customer", "New Invoice": "New Invoice", "New Password": "New Password", "New Payment": "New Payment", "New Payment Reminder of ": "New Payment Reminder of", "New Proposal": "New Proposal", "New Revenue": "New Revenue", "New User": "New User", "New Vendor": "New Vendor", "Next": "Next", "No": "No", "No Contract Found..": "No Contract Found..", "No data available in table": "No data available in table", "No Data Found": "No Data Found", "No reponse returned!": "No reponse returned!", "Not exists in notification template.": "Not exists in notification template.", "Not found.": "Not found.", "Not Sent": "Not Sent", "Note successfully saved.": "Note successfully saved.", "Note: Add currency code as per three-letter ISO code.": "Note: Add currency code as per three-letter ISO code.", "Notes": "Notes", "Notes successfully deleted!": "Notes successfully deleted!", "Notification Message": "Notification Message", "Notification Template": "Notification Template", "Notification Template successfully updated.": "Notification Template successfully updated.", "Notification Templates": "Notification Templates", "November": "November", "Number": "Number", "Number of Result": "Number of Result", "Number:": "Number:", "Oct-Dec": "Oct-Dec", "October": "October", "of": "of", "Offer Text": "Offer Text", "Old Password": "Old Password", "On/Off": "On/Off", "On/Off:": "On/Off:", "On/Off: ": "On/Off:", "Once your account is deleted, all of its resources and data will be permanently deleted. Before deleting your account, please download any data or information that you wish to retain.": "Once your account is deleted, all of its resources and data will be permanently deleted. Before deleting your account, please download any data or information that you wish to retain.", "Only Upload Files": "Only Upload Files", "Oops something went wrong.": "Oops something went wrong.", "Open media in new tab": "Open media in new tab", "Opening Balance": "Opening Balance", "opps something went wrong.": "opps something went wrong.", "opps something when wrong.": "opps something when wrong.", "OR": "OR", "Order Id": "Order Id", "Order Number": "Order Number", "OrderId": "OrderId", "Org": "Org", "Os": "<PERSON><PERSON>", "Os Name": "Os Name", "Overdue": "Overdue", "Owner Name": "Owner Name", "Ozow": "Ozow", "Ozow Api Key": "Ozow Api Key", "Ozow Mode": "Ozow Mode", "Ozow Private Key": "Ozow Private Key", "Ozow Site Key": "Ozow Site Key", "Page Content": "Page Content", "Page Name": "Page Name", "Page URL": "Page URL", "Paid": "Paid", "Paid Amount": "<PERSON><PERSON>", "Paiementpro": "Paiementpro", "Parent Account": "Parent Account", "Parent Account Name": "Parent Account Name", "Partial Paid": "Partial Paid", "Password": "Password", "Password Confirmation": "Password Confirmation", "Password successfully updated.": "Password successfully updated.", "Password updated successfully.": "Password updated successfully.", "pay": "pay", "Pay Now": "Pay Now", "PayFast": "PayFast", "Payfast": "Payfast", "Payfast Mode": "Payfast Mode", "PayHere": "PayHere", "Payhere": "Payhere", "PayHere Mode": "PayHere Mode", "Payment": "Payment", "Payment :": "Payment :", "Payment : ": "Payment :", "Payment Amount": "Payment Amount", "Payment Bill": "Payment Bill", "Payment Date": "Payment Date", "Payment Due Amount": "Payment Due Amount", "Payment DueAmount": "Payment DueAmount", "Payment failed": "Payment failed", "Payment Method": "Payment Method", "Payment Name": "Payment Name", "Payment Receipt": "Payment Receipt", "Payment Reminder": "Payment Reminder", "Payment reminder successfully send.": "Payment reminder successfully send.", "Payment setting successfully updated.": "Payment setting successfully updated.", "Payment Settings": "Payment Settings", "Payment Status": "Payment Status", "Payment status successfully updated.": "Payment status successfully updated.", "Payment successful.": "Payment successful.", "Payment successfully added": "Payment successfully added", "Payment successfully added.": "Payment successfully added.", "Payment successfully added. ": "Payment successfully added. ", "Payment successfully created.": "Payment successfully created.", "Payment successfully deleted.": "Payment successfully deleted.", "Payment successfully updated.": "Payment successfully updated.", "Payment Summary": "Payment Summary", "Payment Type": "Payment Type", "PaymentWall": "PaymentWall", "Paymentwall": "Paymentwall", "PAYPAL": "PAYPAL", "Paypal": "<PERSON><PERSON>", "Paypal Mode": "Paypal Mode", "Paystack": "Paystack", "paytab": "paytab", "Paytab": "Paytab", "paytab Secret": "paytab Secret", "Paytm": "Paytm", "Paytm Environment": "Paytm Environment", "PayTR": "PayTR", "Pending": "Pending", "Period": "Period", "Permanently delete your account.": "Permanently delete your account.", "Permission denied": "Permission denied", "Permission denied.": "Permission denied.", "Permission Denied.": "Permission Denied.", "Permissions": "Permissions", "Personal Info": "Personal Info", "Phone": "Phone", "Photo": "Photo", "Plan": "Plan", "Plan activated Successfully.": "Plan activated Successfully.", "Plan is deleted.": "Plan is deleted.", "Plan not found!": "Plan not found!", "Plan storage limit is over so please upgrade the plan.": "Plan storage limit is over so please upgrade the plan.", "Plan Successfully Activated": "Plan Successfully Activated", "Plan successfully activated.": "Plan successfully activated.", "Please confirm access to your account by entering one of your emergency recovery codes.": "Please confirm access to your account by entering one of your emergency recovery codes.", "Please confirm access to your account by entering the authentication code provided by your authenticator application.": "Please confirm access to your account by entering the authentication code provided by your authenticator application.", "Please confirm your password before continuing.": "Please confirm your password before continuing.", "Please copy your new API token. For your security, it won't be shown again.": "Please copy your new API token. For your security, it won't be shown again.", "Please correct the errors and try again.": "Please correct the errors and try again.", "Please delete related record of this account.": "Please delete related record of this account.", "Please enter a 16-digit card number.": "Please enter a 16-digit card number.", "Please enter correct current password.": "Please enter correct current password.", "Please enter Email Address": "Please enter Email Address", "Please enter your password to confirm you would like to log out of your other browser sessions across all of your devices.": "Please enter your password to confirm you would like to log out of your other browser sessions across all of your devices.", "Please set proper configuration for Api Key": "Please set proper configuration for Api Key", "Please set proper configuration for storage.": "Please set proper configuration for storage.", "Please upload a valid image file. Size of image should not be more than 2MB.": "Please upload a valid image file. Size of image should not be more than 2MB.", "Please use with country code. (ex. +91)": "Please use with country code. (ex. +91)", "Post": "Post", "pply": "pply", "Pre": "Pre", "Presale": "Presale", "Preview": "Preview", "Previous": "Previous", "Price": "Price", "Primary color settings": "Primary color settings", "Print": "Print", "Private Key": "Private Key", "Private key": "Private key", "Product": "Product", "Product & Services": "Product & Services", "Product Name": "Product Name", "Product quantity updated manually.": "Product quantity updated manually.", "Product Stock": "Product Stock", "Product Stock Report": "Product Stock Report", "Product successfully created.": "Product successfully created.", "Product successfully deleted.": "Product successfully deleted.", "Product successfully updated.": "Product successfully updated.", "Product Summary": "Product Summary", "Production": "Production", "Profile": "Profile", "Profile Account": "Profile Account", "Profile Id": "Profile Id", "Profile Information": "Profile Information", "Profit": "Profit", "Profit & Loss": "Profit & Loss", "Profit & Loss Summary": "Profit & Loss Summary", "Profit = Income - Expense ": "Profit = Income - Expense ", "Progress": "Progress", "Project": "Project", "Proposal": "Proposal", "PROPOSAL": "PROPOSAL", "Proposal convert to invoice": "Proposal convert to invoice", "Proposal Create": "Proposal Create", "Proposal Detail": "Proposal Detail", "Proposal duplicate successfully.": "Proposal duplicate successfully.", "Proposal Edit": "Proposal Edit", "Proposal is not found": "Proposal is not found", "Proposal Logo": "Proposal Logo", "Proposal Name": "Proposal Name", "Proposal Number": "Proposal Number", "Proposal Prefix": "Proposal Prefix", "Proposal Print Settings": "Proposal Print Settings", "Proposal Print Template": "Proposal Print Template", "Proposal product successfully deleted.": "Proposal product successfully deleted.", "Proposal Send": "Proposal Send", "Proposal Setting updated successfully": "Proposal Setting updated successfully", "Proposal Starting Number": "Proposal Starting Number", "Proposal Status": "Proposal Status", "Proposal status changed successfully.": "Proposal status changed successfully.", "Proposal successfully created.": "Proposal successfully created.", "Proposal successfully deleted.": "Proposal successfully deleted.", "Proposal successfully sent.": "Proposal successfully sent.", "Proposal successfully updated.": "Proposal successfully updated.", "Proposal to invoice convert successfully.": "Proposal to invoice convert successfully.", "Proposal to Retainer convert successfully.": "Proposal to Retainer convert successfully.", "Proposal Url": "Proposal Url", "Public Key": "Public Key", "Purchase Date": "Purchase Date", "Purchase Price": "Purchase Price", "QR Display?": "QR Display?", "Qty": "Qty", "Quantity": "Quantity", "quantity added by manually": "quantity added by manually", "Quantity of Bills": "Quantity of Bills", "Quantity of Invoice": "Quantity of Invoice", "quantity purchase in bill": "quantity purchase in bill", "Quarterly": "Quarterly", "Quarterly Cashflow": "Quarterly Cashflow", "Query": "Query", "Question": "Question", "Questions": "Questions", "Rate": "Rate", "Rate %": "Rate %", "Razorpay": "Razorpay", "Re Generate": "Re Generate", "ReCaptcha Settings": "ReCaptcha Settings", "Recaptcha Settings updated successfully": "Recaptcha Settings updated successfully", "Receipt": "Receipt", "Receipt Reminder": "Receipt Reminder", "Receipt Summary": "Receipt Summary", "Recent Bills": "Recent Bills", "Recent Invoices": "Recent Invoices", "Record successfully imported": "Record successfully imported", "Recovery Code": "Recovery Code", "Ref Number": "Ref Number", "Reference": "Reference", "Referrer Host": "Referrer Host", "Referrer Path": "Referrer Path", "Regards,": "<PERSON><PERSON>,", "Regenerate Recovery Codes": "Regenerate Recovery Codes", "Region": "Region", "Region Name": "Region Name", "Registration Number": "Registration Number", "Reject": "Reject", "Remove Photo": "Remove Photo", "Report": "Report", "Resend Bill": "Resend Bill", "Resend Invoice": "Resend Invoice", "Resend Proposal": "Resend Proposal", "Resend Retainer": "<PERSON><PERSON><PERSON> Retainer", "Resend Verification Email": "Resend Verification Email", "Reset": "Reset", "Reset Password": "Reset Password", "Restore": "Rest<PERSON>", "Retainer": "Retainer", "retainer": "retainer", "RETAINER": "RETAINER", "Retainer ": "Retainer ", "Retainer Create": "Retainer Create", "Retainer Detail": "Retainer Detail", "Retainer duplicate successfully.": "Retainer duplicate successfully.", "Retainer Edit": "Retainer Edit", "Retainer is deleted.": "Retainer is deleted.", "Retainer Logo": "Retainer Logo", "Retainer Name": "Retainer Name", "Retainer not found!": "Retainer not found!", "Retainer not found.": "Retainer not found.", "Retainer Number": "Retainer Number", "Retainer paid Successfully!": "Retainer paid Successfully!", "Retainer payment failed.": "Retainer payment failed.", "Retainer payment has been received successfully.": "Retainer payment has been received successfully.", "Retainer Prefix": "Retainer Prefix", "Retainer Print Settings": "Retainer Print Settings", "Retainer Print Template": "Retainer Print Template", "Retainer product not found!": "Retainer product not found!", "Retainer product successfully deleted.": "Retainer product successfully deleted.", "Retainer Setting updated successfully": "Retainer Setting updated successfully", "Retainer Starting Number": "Retainer Starting Number", "Retainer status changed successfully.": "Retainer status changed successfully.", "Retainer successfully created.": "Retainer successfully created.", "Retainer successfully deleted.": "Retainer successfully deleted.", "Retainer successfully sent.": "Retainer successfully sent.", "Retainer successfully updated.": "Retainer successfully updated.", "Retainer to invoice convert successfully.": "Retainer to invoice convert successfully.", "Retainer Url": "Retainer <PERSON>rl", "Retainers": "Retainers", "Revenue": "Revenue", "Revenue :": "Revenue :", "Revenue : ": "Revenue : ", "Revenue name": "Revenue name", "Revenue successfully created.": "Revenue successfully created.", "Revenue successfully deleted.": "Revenue successfully deleted.", "Revenue successfully updated.": "Revenue successfully updated.", "Role": "Role", "Role successfully created.": "Role successfully created.", "Role successfully updated.": "Role successfully updated.", "S3 Bucket": "S3 Bucket", "S3 Endpoint": "S3 Endpoint", "S3 Key": "S3 Key", "S3 Region": "S3 Region", "S3 Secret": "S3 Secret", "S3 URL": "S3 URL", "Safe money transfer using your bank account. We support Mastercard, Visa, Discover and American express.": "Safe money transfer using your bank account. We support Mastercard, Visa, Discover and American express.", "Sale Price": "Sale Price", "Salt Passphrase": "Salt Passphrase", "Sandbox": "Sandbox", "Save": "Save", "Save Changes": "Save Changes", "Saved.": "Saved.", "Screenshot": "Screenshot", "Screenshot List": "Screenshot List", "Screenshots": "Screenshots", "Search": "Search", "Search:": "Search:", "Secret Key": "Secret Key", "Select A New Photo": "Select A New Photo", "Select Bill": "Select Bill", "Select Category": "Select Category", "Select CSV File": "Select CSV File", "Select Employee": "Select Employee", "Select Invoice": "Select Invoice", "Select Your Own Brand Color": "Select Your Own Brand Color", "Send": "Send", "Send Bill": "Send Bill", "Send Email": "Send Email", "Send Invoice": "Send Invoice", "Send Mail": "Send Mail", "Send Password Reset Link": "Send Password Reset Link", "Send Proposal": "Send Proposal", "Send Retainer": "Send Retainer", "Send Test Mail": "Send Test Mail", "Sending ...": "Sending ...", "Sent on": "<PERSON>t on", "SEO Settings": "SEO Settings", "September": "September", "Server Key": "Server Key", "Service": "Service", "Setting successfully updated.": "Setting successfully updated.", "Settings": "Settings", "Ship to": "Ship to", "Ship To": "Ship To", "Ship To to": "Ship To to", "Ship To:": "Ship To:", "Shipped To": "Shipped To", "Shipping Address": "Shipping Address", "Shipping address status successfully changed.": "Shipping address status successfully changed.", "Shipping City": "Shipping City", "Shipping Country": "Shipping Country", "Shipping Info": "Shipping Info", "Shipping Name": "Shipping Name", "Shipping Phone": "Shipping Phone", "Shipping Same As Billing": "Shipping Same As Billing", "Shipping State": "Shipping State", "Shipping Zip": "Shipping Zip", "Shop id": "Shop id", "Show": "Show", "Show Recovery Codes": "Show Recovery Codes", "Showing": "Showing", "Sidebar settings": "Sidebar settings", "Sign": "Sign", "Sign in": "Sign in", "Sign In": "Sign In", "Signature": "Signature", "Signature Key": "Signature Key", "Site Description": "Site Description", "Site Logo": "Site Logo", "Skrill": "Skrill", "Skrill Email": "Skrill Email", "SKU": "SKU", "Sku": "S<PERSON>", "Slider": "Slide<PERSON>", "Something into warning.": "Something into warning.", "Something is wrong.": "Something is wrong.", "Something went wrong": "Something went wrong", "Something went wrong!": "Something went wrong!", "Something went wrong.": "Something went wrong.", "span": "span", "Sspay": "Sspay", "Staff": "Staff", "Star": "Star", "Start Date": "Start Date", "Start Date   :": "Start Date   :", "Start Date   : ": "Start Date   : ", "Start Month": "Start Month", "State": "State", "Statement": "Statement", "Statement of Account": "Statement of Account", "Statement of Accounts": "Statement of Accounts", "Status": "Status", "Status successfully updated!": "Status successfully updated!", "Storage Settings": "Storage Settings", "Store Id": "Store Id", "Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost.": "Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost.", "Strictly Cookie Description": "Strictly Cookie Description", "Strictly necessary cookies": "Strictly necessary cookies", "STRIPE": "STRIPE", "Stripe": "Stripe", "Stripe / Paypal": "Stripe / Paypal", "Stripe Key": "Stripe Key", "Stripe Secret": "Stripe Secret", "Sub Total": "Sub Total", "Subject": "Subject", "Subject : ": "Subject : ", "Subtotal": "Subtotal", "success": "success", "Success": "Success", "successfull": "successfull", "Summary": "Summary", "Supported Date": "Supported Date", "Switch Teams": "Switch Teams", "System Settings": "System Settings", "Tap": "Tap", "Target": "Target", "Tax": "Tax", "Tax Name": "Tax Name", "Tax Number": "Tax Number", "Tax Number ": "Tax Number ", "Tax Rate %": "Tax Rate %", "Tax Rate Name": "Tax Rate Name", "Tax rate successfully created.": "Tax rate successfully created.", "Tax rate successfully deleted.": "Tax rate successfully deleted.", "Tax rate successfully updated.": "Tax rate successfully updated.", "Tax Summary": "Tax Summary", "Taxes": "Taxes", "Team Settings": "Team Settings", "Telephone": "Telephone", "Templates": "Templates", "Testimonials": "Testimonials", "Testimonials List": "Testimonials List", "Text was not generated, due to invalid API key": "Text was not generated, due to invalid API key", "Text was not generated, please try again": "Text was not generated, please try again", "Thank you for your business!": "Thank you for your business!", "Thank you very much and have a good day.": "Thank you very much and have a good day.", "Thanks for signing up! Before getting started, could you verify your email address by clicking on the link we just emailed to you? If you didn't receive the email, we will gladly send you another.": "Thanks for signing up! Before getting started, could you verify your email address by clicking on the link we just emailed to you? If you didn't receive the email, we will gladly send you another.", "Thanks,": "Thanks,", "The email template details are updated successfully": "The email template details are updated successfully", "Theme Customizer": "Theme Customizer", "there is no account balance": "there is no account balance", "There is no goal.": "There is no goal.", "there is no latest expense": "there is no latest expense", "there is no latest income": "there is no latest income", "there is no recent bill": "there is no recent bill", "there is no recent invoice": "there is no recent invoice", "This action can not be undone. Do you want to continue?": "This action can not be undone. Do you want to continue?", "this category is already assign so please move or remove this category related data.": "this category is already assign so please move or remove this category related data.", "This coupon code has expired.": "This coupon code has expired.", "This coupon code is invalid or has expired.": "This coupon code is invalid or has expired.", "This device": "This device", "This is a page meant for more advanced users, simply ignore it if you don`t understand what cache is.": "This is a page meant for more advanced users, simply ignore it if you don`t understand what cache is.", "This mail send only for testing purpose.": "This mail send only for testing purpose.", "This Month Total Contracts": "This Month Total Contracts", "this tax is already assign to proposal or bill or invoice so please move or remove this tax related data.": "this tax is already assign to proposal or bill or invoice so please move or remove this tax related data.", "this type is already use so please transfer or delete this type related data.": "this type is already use so please transfer or delete this type related data.", "this unit is already assign so please move or remove this unit related data.": "this unit is already assign so please move or remove this unit related data.", "This Week Total Contracts": "This Week Total Contracts", "Time Format": "Time Format", "Timezone": "Timezone", "Title": "Title", "Title Text": "Title Text", "To": "To", "to": "to", "To Account": "To Account", "To login your account detail, simply click on the button below:": "To login your account detail, simply click on the button below:", "Token": "Token", "Token Name": "Token Name", "Tone": "<PERSON><PERSON>", "Top Bar": "Top Bar", "Total": "Total", "Total :": "Total :", "TOTAL :": "TOTAL :", "Total Amount": "Total Amount", "Total Bill": "Total Bill", "Total Contracts": "Total Contracts", "Total Credit": "Total Credit", "Total Debit": "Total Debit", "Total Due": "Total Due", "Total Expense =  Payment + Bill ": "Total Expense =  Payment + Bill ", "Total Expenses": "Total Expenses", "Total Income": "Total Income", "Total Income =  Revenue + Invoice ": "Total Income =  Revenue + Invoice ", "Total Invoice": "Total Invoice", "Total Paid": "Total Paid", "Total Sum of Bills": "Total Sum of Bills", "Total Sum of Invoices": "Total Sum of Invoices", "Toyyibpay": "Toyyibpay", "Transaction": "Transaction", "Transaction currency must be ZAR.": "Transaction currency must be ZAR.", "Transaction Date": "Transaction Date", "Transaction fail": "Transaction fail", "Transaction fail!": "Transaction fail!", "Transaction Failed!": "Transaction Failed!", "Transaction has been completed.": "Transaction has been completed.", "Transaction has been complted.": "Transaction has been complted.", "Transaction has been failed!": "Transaction has been failed!", "Transaction has been failed! ": "Transaction has been failed! ", "Transaction has been failed.": "Transaction has been failed.", "Transaction has been success": "Transaction has been success", "Transaction has been successful": "Transaction has been successful", "Transaction has failed": "Transaction has failed", "Transaction succesfull": "Transaction succesfull", "Transaction Summary": "Transaction Summary", "Transaction Type": "Transaction Type", "Transaction Unsuccesfull": "Transaction Unsuccesfull", "Transfer": "Transfer", "Transparent layout": "Transparent layout", "Trial Balance": "Trial Balance", "Trusted by": "Trusted by", "Twilio From": "<PERSON><PERSON><PERSON>", "Twilio Settings": "<PERSON><PERSON><PERSON>", "Twilio SID ": "<PERSON><PERSON><PERSON> ", "Twilio Token": "<PERSON><PERSON><PERSON>", "Twilio updated successfully.": "<PERSON><PERSON><PERSON> updated successfully.", "Two Factor Authentication": "Two Factor Authentication", "Two factor authentication is now enabled. Scan the following QR code using your phone's authenticator application.": "Two factor authentication is now enabled. Scan the following QR code using your phone's authenticator application.", "Type": "Type", "Type   :": "Type   :", "Type   : ": "Type   : ", "Unit": "Unit", "Unit Name": "Unit Name", "Unit successfully created.": "Unit successfully created.", "Unit successfully deleted.": "Unit successfully deleted.", "Unit successfully updated.": "Unit successfully updated.", "Unknown error occurred": "Unknown error occurred", "Unpaid": "Unpaid", "Until Date": "Until Date", "Update": "Update", "Update Password": "Update Password", "Update Quantity": "Update Quantity", "Update Role": "Update Role", "Update your account's profile information and email address.": "Update your account's profile information and email address.", "Upload": "Upload", "Url": "Url", "URL": "URL", "url": "url", "Use a recovery code": "Use a recovery code", "Use an authentication code": "Use an authentication code", "User": "User", "User Avtar": "User Avtar", "User Log": "User Log", "User Login": "User Login", "User login disable successfully.": "User login disable successfully.", "User login enable successfully.": "User login enable successfully.", "User Role": "User Role", "User successfully added.": "User successfully added.", "User successfully deleted .": "User successfully deleted .", "Username": "Username", "Users": "Users", "Users logs": "Users logs", "v2": "v2", "v3": "v3", "Value": "Value", "Value  :": "Value  :", "Value  : ": "Value  : ", "Variables": "Variables", "VAT Number": "VAT Number", "Vender": "Vender", "Vender Prefix": "Vender Prefix", "Vendor": "<PERSON><PERSON><PERSON>", "Vendor Bill Send": "V<PERSON><PERSON>", "Vendor Id": "Vendor Id", "Vendor Info": "Vendor Info", "Vendor Login": "<PERSON><PERSON><PERSON>", "Vendor Name": "Vendor Name", "Vendor Statement": "Vendor Statement", "Vendor successfully created.": "<PERSON><PERSON><PERSON> successfully created.", "Vendor successfully deleted.": "<PERSON><PERSON><PERSON> successfully deleted.", "Vendor successfully updated.": "<PERSON><PERSON><PERSON> successfully updated.", "Vendors": "Vend<PERSON>", "Verical View": "Verical View", "Verify Email": "<PERSON><PERSON><PERSON>", "Verify Your Email Address": "Verify Your Email Address", "Vertical View": "Vertical View", "View": "View", "View User Logs": "View User Logs", "Wasabi": "<PERSON><PERSON>", "Wasabi Bucket": "<PERSON><PERSON>", "Wasabi Key": "<PERSON><PERSON>", "Wasabi Region": "Wasabi Region", "Wasabi Root": "<PERSON><PERSON>", "Wasabi Secret": "<PERSON>abi <PERSON>", "Wasabi URL": "Wasabi URL", "We appreciate your prompt payment and look forward to continued business with you in the future.": "We appreciate your prompt payment and look forward to continued business with you in the future.", "We will send a link to reset your password": "We will send a link to reset your password", "Webhook call failed.": "Webhook call failed.", "Webhook Settings": "Webhook Settings", "Webhook Successfully created.": "Webhook Successfully created.", "Webhook Successfully updated.": "Webhook Successfully updated.", "Welcome, to ": "Welcome, to ", "When two factor authentication is enabled, you will be prompted for a secure, random token during authentication. You may retrieve this token from your phone's Google Authenticator application.": "When two factor authentication is enabled, you will be prompted for a secure, random token during authentication. You may retrieve this token from your phone's Google Authenticator application.", "Whoops! Something went wrong.": "Whoops! Something went wrong.", "Xendit": "Xendit", "Year": "Year", "Year :": "Year :", "Yes": "Yes", "Yookassa": "<PERSON><PERSON><PERSON>", "YooKassa": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "You are receiving this email because we received a password reset request for your account": "You are receiving this email because we received a password reset request for your account", "you can find out here..": "you can find out here..", "You have enabled two factor authentication.": "You have enabled two factor authentication.", "You have not enabled two factor authentication.": "You have not enabled two factor authentication.", "You may delete any of your existing tokens if they are no longer needed.": "You may delete any of your existing tokens if they are no longer needed.", "You want to confirm convert to invoice. Press Yes to continue or Cancel to go back": "You want to confirm convert to invoice. Press Yes to continue or Can<PERSON> to go back", "You want to confirm duplicate this invoice. Press Yes to continue or Cancel to go back": "You want to confirm duplicate this invoice. Press Yes to continue or <PERSON><PERSON> to go back", "You want to confirm this action. Press Yes to continue or Cancel to go back": "You want to confirm this action. Press Yes to continue or Can<PERSON> to go back", "Your Account is disable from company.": "Your Account is disable from company.", "Your Account is disable from customer.": "Your Account is disable from customer.", "Your Account is disable from vendor.": "Your Account is disable from vendor.", "Your Payment has failed!": "Your Payment has failed!", "Your payment has failed.": "Your payment has failed.", "Your payment is cancel": "Your payment is cancel", "Your Transaction is fail please try again": "Your Transaction is fail please try again", "Your Transaction on pending": "Your Transaction on pending", "YYYY-MM-DD": "YYYY-MM-DD", "Zip": "Zip", "Zip Code": "Zip Code", "Zip/Post Code": "Zip/Post Code"}