<?php
namespace Aws\PcaConnectorAd;

use Aws\AwsClient;

/**
 * This client is used to interact with the **PcaConnectorAd** service.
 * @method \Aws\Result createConnector(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createConnectorAsync(array $args = [])
 * @method \Aws\Result createDirectoryRegistration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDirectoryRegistrationAsync(array $args = [])
 * @method \Aws\Result createServicePrincipalName(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createServicePrincipalNameAsync(array $args = [])
 * @method \Aws\Result createTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createTemplateAsync(array $args = [])
 * @method \Aws\Result createTemplateGroupAccessControlEntry(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createTemplateGroupAccessControlEntryAsync(array $args = [])
 * @method \Aws\Result deleteConnector(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteConnectorAsync(array $args = [])
 * @method \Aws\Result deleteDirectoryRegistration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDirectoryRegistrationAsync(array $args = [])
 * @method \Aws\Result deleteServicePrincipalName(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteServicePrincipalNameAsync(array $args = [])
 * @method \Aws\Result deleteTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteTemplateAsync(array $args = [])
 * @method \Aws\Result deleteTemplateGroupAccessControlEntry(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteTemplateGroupAccessControlEntryAsync(array $args = [])
 * @method \Aws\Result getConnector(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getConnectorAsync(array $args = [])
 * @method \Aws\Result getDirectoryRegistration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDirectoryRegistrationAsync(array $args = [])
 * @method \Aws\Result getServicePrincipalName(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getServicePrincipalNameAsync(array $args = [])
 * @method \Aws\Result getTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTemplateAsync(array $args = [])
 * @method \Aws\Result getTemplateGroupAccessControlEntry(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTemplateGroupAccessControlEntryAsync(array $args = [])
 * @method \Aws\Result listConnectors(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listConnectorsAsync(array $args = [])
 * @method \Aws\Result listDirectoryRegistrations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDirectoryRegistrationsAsync(array $args = [])
 * @method \Aws\Result listServicePrincipalNames(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listServicePrincipalNamesAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result listTemplateGroupAccessControlEntries(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTemplateGroupAccessControlEntriesAsync(array $args = [])
 * @method \Aws\Result listTemplates(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTemplatesAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateTemplateAsync(array $args = [])
 * @method \Aws\Result updateTemplateGroupAccessControlEntry(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateTemplateGroupAccessControlEntryAsync(array $args = [])
 */
class PcaConnectorAdClient extends AwsClient {}
