<?php

namespace Spatie\LaravelIgnition\Exceptions;

use Spa<PERSON>\ErrorSolutions\Contracts\BaseSolution;
use Spa<PERSON>\ErrorSolutions\Contracts\ProvidesSolution;
use <PERSON><PERSON>\ErrorSolutions\Contracts\Solution;
use Symfony\Component\HttpKernel\Exception\HttpException;

class CannotExecuteSolutionForNonLocalIp extends HttpException implements ProvidesSolution
{
    public static function make(): self
    {
        return new self(403, 'Solutions cannot be run from your current IP address.');
    }

    public function getSolution(): Solution
    {
        return BaseSolution::create()
            ->setSolutionTitle('Checking your environment settings')
            ->setSolutionDescription("Solutions can only be executed by requests from a local IP address. Keep in mind that `APP_DEBUG` should set to false on any production environment.");
    }
}
