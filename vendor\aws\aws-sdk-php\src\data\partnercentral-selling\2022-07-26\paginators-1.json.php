<?php
// This file was auto-generated from sdk-root/src/data/partnercentral-selling/2022-07-26/paginators-1.json
return [ 'pagination' => [ 'ListEngagementByAcceptingInvitationTasks' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'TaskSummaries', ], 'ListEngagementFromOpportunityTasks' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'TaskSummaries', ], 'ListEngagementInvitations' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'EngagementInvitationSummaries', ], 'ListEngagementMembers' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'EngagementMemberList', ], 'ListEngagementResourceAssociations' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'EngagementResourceAssociationSummaries', ], 'ListEngagements' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'EngagementSummaryList', ], 'ListOpportunities' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'OpportunitySummaries', ], 'ListResourceSnapshotJobs' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'ResourceSnapshotJobSummaries', ], 'ListResourceSnapshots' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'ResourceSnapshotSummaries', ], 'ListSolutions' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'SolutionSummaries', ], ],];
