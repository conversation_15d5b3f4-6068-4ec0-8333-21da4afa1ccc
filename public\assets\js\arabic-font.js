/**
 * Arabic Font Handler for Hesa<PERSON>i
 * Automatically applies Cairo font when Arabic language is detected
 */

(function() {
    'use strict';

    // Function to apply Arabic font and RTL layout
    function applyArabicFont() {
        const html = document.documentElement;
        const body = document.body;
        
        // Check if current language is Arabic
        const currentLang = html.getAttribute('lang') || 'en';
        const isArabic = currentLang === 'ar' || currentLang.startsWith('ar-');
        
        if (isArabic) {
            // Apply Arabic font class
            html.classList.add('lang-ar');
            body.classList.add('lang-ar');
            
            // Set RTL direction
            html.setAttribute('dir', 'rtl');
            
            // Apply Cairo font to all text elements
            const textElements = document.querySelectorAll('body, p, span, div, h1, h2, h3, h4, h5, h6, label, input, textarea, select, button, .form-control, .btn, .nav-link, .dropdown-item, .card-title, .modal-title');
            
            textElements.forEach(element => {
                element.style.fontFamily = "'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
            });
            
            console.log('Cairo font applied for Arabic language');
        } else {
            // Remove Arabic classes if not Arabic
            html.classList.remove('lang-ar');
            body.classList.remove('lang-ar');
            
            // Reset direction for non-Arabic languages
            if (currentLang !== 'he') { // Keep RTL for Hebrew
                html.setAttribute('dir', 'ltr');
            }
        }
    }

    // Function to handle language change
    function handleLanguageChange() {
        // Listen for language dropdown changes
        const languageLinks = document.querySelectorAll('a[href*="/change-language/"], a[href*="/lang/"]');
        
        languageLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                const href = this.getAttribute('href');
                if (href.includes('/ar') || href.includes('ar/')) {
                    // Arabic language selected
                    setTimeout(applyArabicFont, 100);
                }
            });
        });
    }

    // Function to observe DOM changes for dynamic content
    function observeChanges() {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Check if new content was added
                    const hasTextNodes = Array.from(mutation.addedNodes).some(node => 
                        node.nodeType === Node.ELEMENT_NODE && node.textContent.trim().length > 0
                    );
                    
                    if (hasTextNodes) {
                        applyArabicFont();
                    }
                }
                
                // Check for attribute changes on html element
                if (mutation.type === 'attributes' && mutation.target === document.documentElement) {
                    if (mutation.attributeName === 'lang' || mutation.attributeName === 'dir') {
                        applyArabicFont();
                    }
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['lang', 'dir']
        });

        // Also observe the html element for lang changes
        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['lang', 'dir']
        });
    }

    // Initialize when DOM is ready
    function init() {
        applyArabicFont();
        handleLanguageChange();
        observeChanges();
        
        // Re-apply font after page transitions (for SPA-like behavior)
        window.addEventListener('popstate', applyArabicFont);
        
        // Re-apply font when page becomes visible (tab switching)
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                setTimeout(applyArabicFont, 100);
            }
        });
    }

    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // Expose function globally for manual triggering
    window.applyArabicFont = applyArabicFont;

})();

// Additional CSS injection for better Arabic support
(function() {
    const style = document.createElement('style');
    style.textContent = `
        /* Enhanced Arabic text rendering */
        .lang-ar * {
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        /* Ensure proper Arabic number display */
        .lang-ar .arabic-numbers {
            font-feature-settings: "lnum" 1;
        }
        
        /* Better line height for Arabic text */
        .lang-ar p, .lang-ar span, .lang-ar div {
            line-height: 1.6;
        }
        
        /* Improved Arabic headings */
        .lang-ar h1, .lang-ar h2, .lang-ar h3, .lang-ar h4, .lang-ar h5, .lang-ar h6 {
            font-weight: 600;
            line-height: 1.4;
        }
    `;
    document.head.appendChild(style);
})();
