<?php
// This file was auto-generated from sdk-root/src/data/ssm-sap/2018-05-10/paginators-1.json
return [ 'pagination' => [ 'ListApplications' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'Applications', ], 'ListComponents' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'Components', ], 'ListDatabases' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'Databases', ], 'ListOperationEvents' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'OperationEvents', ], 'ListOperations' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'Operations', ], ],];
