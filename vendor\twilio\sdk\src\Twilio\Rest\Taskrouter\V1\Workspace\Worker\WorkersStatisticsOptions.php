<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Taskrouter
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Taskrouter\V1\Workspace\Worker;

use Twilio\Options;
use Twilio\Values;

abstract class WorkersStatisticsOptions
{
    /**
     * @param int $minutes Only calculate statistics since this many minutes in the past. The default 15 minutes. This is helpful for displaying statistics for the last 15 minutes, 240 minutes (4 hours), and 480 minutes (8 hours) to see trends.
     * @param \DateTime $startDate Only calculate statistics from this date and time and later, specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
     * @param \DateTime $endDate Only calculate statistics from this date and time and earlier, specified in GMT as an [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) date-time.
     * @param string $taskQueueSid The SID of the TaskQueue for which to fetch Worker statistics.
     * @param string $taskQueueName The `friendly_name` of the TaskQueue for which to fetch Worker statistics.
     * @param string $friendlyName Only include Workers with `friendly_name` values that match this parameter.
     * @param string $taskChannel Only calculate statistics on this TaskChannel. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.
     * @return FetchWorkersStatisticsOptions Options builder
     */
    public static function fetch(
        
        int $minutes = Values::INT_NONE,
        ?\DateTime $startDate = null,
        ?\DateTime $endDate = null,
        string $taskQueueSid = Values::NONE,
        string $taskQueueName = Values::NONE,
        string $friendlyName = Values::NONE,
        string $taskChannel = Values::NONE

    ): FetchWorkersStatisticsOptions
    {
        return new FetchWorkersStatisticsOptions(
            $minutes,
            $startDate,
            $endDate,
            $taskQueueSid,
            $taskQueueName,
            $friendlyName,
            $taskChannel
        );
    }

}

class FetchWorkersStatisticsOptions extends Options
    {
    /**
     * @param int $minutes Only calculate statistics since this many minutes in the past. The default 15 minutes. This is helpful for displaying statistics for the last 15 minutes, 240 minutes (4 hours), and 480 minutes (8 hours) to see trends.
     * @param \DateTime $startDate Only calculate statistics from this date and time and later, specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
     * @param \DateTime $endDate Only calculate statistics from this date and time and earlier, specified in GMT as an [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) date-time.
     * @param string $taskQueueSid The SID of the TaskQueue for which to fetch Worker statistics.
     * @param string $taskQueueName The `friendly_name` of the TaskQueue for which to fetch Worker statistics.
     * @param string $friendlyName Only include Workers with `friendly_name` values that match this parameter.
     * @param string $taskChannel Only calculate statistics on this TaskChannel. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.
     */
    public function __construct(
        
        int $minutes = Values::INT_NONE,
        ?\DateTime $startDate = null,
        ?\DateTime $endDate = null,
        string $taskQueueSid = Values::NONE,
        string $taskQueueName = Values::NONE,
        string $friendlyName = Values::NONE,
        string $taskChannel = Values::NONE

    ) {
        $this->options['minutes'] = $minutes;
        $this->options['startDate'] = $startDate;
        $this->options['endDate'] = $endDate;
        $this->options['taskQueueSid'] = $taskQueueSid;
        $this->options['taskQueueName'] = $taskQueueName;
        $this->options['friendlyName'] = $friendlyName;
        $this->options['taskChannel'] = $taskChannel;
    }

    /**
     * Only calculate statistics since this many minutes in the past. The default 15 minutes. This is helpful for displaying statistics for the last 15 minutes, 240 minutes (4 hours), and 480 minutes (8 hours) to see trends.
     *
     * @param int $minutes Only calculate statistics since this many minutes in the past. The default 15 minutes. This is helpful for displaying statistics for the last 15 minutes, 240 minutes (4 hours), and 480 minutes (8 hours) to see trends.
     * @return $this Fluent Builder
     */
    public function setMinutes(int $minutes): self
    {
        $this->options['minutes'] = $minutes;
        return $this;
    }

    /**
     * Only calculate statistics from this date and time and later, specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
     *
     * @param \DateTime $startDate Only calculate statistics from this date and time and later, specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
     * @return $this Fluent Builder
     */
    public function setStartDate(\DateTime $startDate): self
    {
        $this->options['startDate'] = $startDate;
        return $this;
    }

    /**
     * Only calculate statistics from this date and time and earlier, specified in GMT as an [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) date-time.
     *
     * @param \DateTime $endDate Only calculate statistics from this date and time and earlier, specified in GMT as an [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) date-time.
     * @return $this Fluent Builder
     */
    public function setEndDate(\DateTime $endDate): self
    {
        $this->options['endDate'] = $endDate;
        return $this;
    }

    /**
     * The SID of the TaskQueue for which to fetch Worker statistics.
     *
     * @param string $taskQueueSid The SID of the TaskQueue for which to fetch Worker statistics.
     * @return $this Fluent Builder
     */
    public function setTaskQueueSid(string $taskQueueSid): self
    {
        $this->options['taskQueueSid'] = $taskQueueSid;
        return $this;
    }

    /**
     * The `friendly_name` of the TaskQueue for which to fetch Worker statistics.
     *
     * @param string $taskQueueName The `friendly_name` of the TaskQueue for which to fetch Worker statistics.
     * @return $this Fluent Builder
     */
    public function setTaskQueueName(string $taskQueueName): self
    {
        $this->options['taskQueueName'] = $taskQueueName;
        return $this;
    }

    /**
     * Only include Workers with `friendly_name` values that match this parameter.
     *
     * @param string $friendlyName Only include Workers with `friendly_name` values that match this parameter.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * Only calculate statistics on this TaskChannel. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.
     *
     * @param string $taskChannel Only calculate statistics on this TaskChannel. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.
     * @return $this Fluent Builder
     */
    public function setTaskChannel(string $taskChannel): self
    {
        $this->options['taskChannel'] = $taskChannel;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Taskrouter.V1.FetchWorkersStatisticsOptions ' . $options . ']';
    }
}

