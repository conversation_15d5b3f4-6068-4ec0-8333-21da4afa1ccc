<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Bulkexports
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Bulkexports\V1;

use Twilio\ListResource;
use Twilio\Version;


class ExportConfigurationList extends ListResource
    {
    /**
     * Construct the ExportConfigurationList
     *
     * @param Version $version Version that contains the resource
     */
    public function __construct(
        Version $version
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        ];
    }

    /**
     * Constructs a ExportConfigurationContext
     *
     * @param string $resourceType The type of communication – Messages, Calls, Conferences, and Participants
     */
    public function getContext(
        string $resourceType
        
    ): ExportConfigurationContext
    {
        return new ExportConfigurationContext(
            $this->version,
            $resourceType
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Bulkexports.V1.ExportConfigurationList]';
    }
}
