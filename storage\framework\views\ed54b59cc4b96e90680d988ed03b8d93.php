<!DOCTYPE html>
<html>
<head>
    <title>Test Form</title>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
</head>
<body>
    <h1>Test Form Communication</h1>
    
    <?php if(session('success')): ?>
        <div style="color: green;"><?php echo e(session('success')); ?></div>
    <?php endif; ?>
    
    <?php if($errors->any()): ?>
        <div style="color: red;">
            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <p><?php echo e($error); ?></p>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    <?php endif; ?>
    
    <form action="<?php echo e(route('test.form')); ?>" method="POST">
        <?php echo csrf_field(); ?>
        <div>
            <label>Email:</label>
            <input type="email" name="email" value="<?php echo e(old('email')); ?>" required>
        </div>
        <div>
            <label>Password:</label>
            <input type="password" name="password" required>
        </div>
        <div>
            <button type="submit">Test Submit</button>
        </div>
    </form>
    
    <script>
        console.log('CSRF Token:', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
    </script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\accounting\resources\views/test-form.blade.php ENDPATH**/ ?>