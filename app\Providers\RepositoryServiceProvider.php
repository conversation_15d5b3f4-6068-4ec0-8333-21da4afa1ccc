<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Repositories\InvoiceRepository;
use App\Repositories\CustomerRepository;
use App\Models\Invoice;
use App\Models\Customer;

class RepositoryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->bind(InvoiceRepository::class, function ($app) {
            return new InvoiceRepository(new Invoice());
        });

        $this->app->bind(CustomerRepository::class, function ($app) {
            return new CustomerRepository(new Customer());
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
