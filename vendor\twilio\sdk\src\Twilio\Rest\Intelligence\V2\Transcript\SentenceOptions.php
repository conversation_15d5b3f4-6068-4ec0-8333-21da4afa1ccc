<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Intelligence
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Intelligence\V2\Transcript;

use Twilio\Options;
use Twilio\Values;

abstract class SentenceOptions
{
    /**
     * @param bool $redacted Grant access to PII Redacted/Unredacted Sentences. If redaction is enabled, the default is `true` to access redacted sentences.
     * @param bool $wordTimestamps Returns word level timestamps information, if word_timestamps is enabled. The default is `false`.
     * @return ReadSentenceOptions Options builder
     */
    public static function read(
        
        bool $redacted = Values::BOOL_NONE,
        bool $wordTimestamps = Values::BOOL_NONE

    ): ReadSentenceOptions
    {
        return new ReadSentenceOptions(
            $redacted,
            $wordTimestamps
        );
    }

}

class ReadSentenceOptions extends Options
    {
    /**
     * @param bool $redacted Grant access to PII Redacted/Unredacted Sentences. If redaction is enabled, the default is `true` to access redacted sentences.
     * @param bool $wordTimestamps Returns word level timestamps information, if word_timestamps is enabled. The default is `false`.
     */
    public function __construct(
        
        bool $redacted = Values::BOOL_NONE,
        bool $wordTimestamps = Values::BOOL_NONE

    ) {
        $this->options['redacted'] = $redacted;
        $this->options['wordTimestamps'] = $wordTimestamps;
    }

    /**
     * Grant access to PII Redacted/Unredacted Sentences. If redaction is enabled, the default is `true` to access redacted sentences.
     *
     * @param bool $redacted Grant access to PII Redacted/Unredacted Sentences. If redaction is enabled, the default is `true` to access redacted sentences.
     * @return $this Fluent Builder
     */
    public function setRedacted(bool $redacted): self
    {
        $this->options['redacted'] = $redacted;
        return $this;
    }

    /**
     * Returns word level timestamps information, if word_timestamps is enabled. The default is `false`.
     *
     * @param bool $wordTimestamps Returns word level timestamps information, if word_timestamps is enabled. The default is `false`.
     * @return $this Fluent Builder
     */
    public function setWordTimestamps(bool $wordTimestamps): self
    {
        $this->options['wordTimestamps'] = $wordTimestamps;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Intelligence.V2.ReadSentenceOptions ' . $options . ']';
    }
}

