sudo: false

language: php

php:
  - '5.4'
  - '5.5'
  - '5.6'
  - '7.0'
  - hhvm

before_script:
  - composer selfupdate
  - composer install

after_script:
  - vendor/bin/test-reporter --stdout > codeclimate.json
  - "curl -X POST -d @codeclimate.json -H 'Content-Type: application/json' -H 'User-Agent: Code Climate (PHP Test Reporter v0.1.1)' https://codeclimate.com/test_reports"

notifications:
  webhooks:
    urls:
      - https://webhooks.gitter.im/e/802f417bb6e3e1e8b20d
    on_success: always
    on_failure: always
    on_start: false

matrix:
  fast_finish: true
  allow_failures:
    - php: hhvm

notifications:
  webhooks:
    urls:
      - https://webhooks.gitter.im/e/d4319553d0aecfd5b9ac
    on_success: always
    on_failure: always
    on_start: false
