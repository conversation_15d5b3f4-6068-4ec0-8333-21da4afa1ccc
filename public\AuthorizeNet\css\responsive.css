@media screen and (max-width:1300px) {
    /*---vehicle booking page css start---*/ 
    .form-control, .custom-select, .dataTable-selector, .dataTable-input{
        padding: 10px;
    }
    .booking-form .form-group .btn{
        padding: 10px;
    }
    .vehicle-booking-sec .vehicle-booking-inner{
        padding: 20px 15px;
    }
    /*---vehicle booking page css end---*/ 
    /*--- booking form page css start---*/
    .booking-form-banner{
        background-position: center;
    } 
    /*--- booking form page css end---*/ 
}
    
@media screen and (max-width:1199px) {
     /*---vehicle booking page css start---*/ 
    .vehicle-booking-sec .booking-form,.modify-search .booking-form{
      justify-content: center;
      gap: 15px;
    }
    /*---vehicle booking page css end---*/ 
    /*--- booking form page css start---*/
    .booking-form-banner{
        padding: 60px 0;
    }
    .booking-form-banner h2 {
        font-size: 40px;
    }
    .seating-arrangement{
        max-width: 100%;
    }
    .seating-arrangement .bus{
        gap: 15px;
    }
  
    /*--- booking form page css end---*/ 
}
@media screen and (max-width:991px) {
    .pt{
        padding-top: 40px;
    }
    .pb{
        padding-bottom: 40px;
    }
    /*--- booking form page css start---*/
    .booking-form-banner::after{
       content: '';
       position: absolute;
       top: 0;
       left: 0;
       right: 0;
       width: 100%;
       height: 100%;
       background-color: #00000052;
       z-index: 0;
    }
    .booking-form-banner .section-title{
        position: relative;
        z-index: 1;
    }
    .seating-arrangement{
        max-width: 70%;
    }
  
     /*--- booking form page css end---*/ 
 
}
@media screen and (max-width:767px) {
    .pt{
        padding-top: 30px;
    }
    .pb{
        padding-bottom: 30px;
    }
     /*---vehicle booking page css start---*/ 
    .vehicle-booking-sec .booking-form,.modify-search .booking-form{
        flex-direction: column;
        align-items: flex-start;
    }
    .vehicle-booking-sec .booking-form .form-group,  .vehicle-booking-inner .booking-form .date-pick input,.booking-form .form-group .btn,
    .modify-search .booking-form .form-group,.modify-search .booking-form .date-pick input,.modify-search .booking-form .form-group .btn{
        width: 100%;
    }
    .header-style-one .main-navigationbar .logo-col {
        max-width: 80px;
    }
    
     /*---vehicle booking page css end---*/ 
     /*--- booking form page css start---*/ 
     .booking-form-banner h2 {
        font-size: 26px;
    }
    .seating-arrangement{
        max-width: 100%;
    }
    /*--- booking form page css end---*/ 

}
@media screen and (max-width:575px) {
    /*--- booking form page css start---*/ 
    .booking-form-banner {
        padding: 40px 0;
    }
    .modify-search.set .acnav-label {
        font-size: 14px;
    }
    .search-results-sec .search-result-bar {
        flex-direction: column;
        align-items: flex-start !important;
    }
    .info-col-one, .info-col-four, .info-col-three,.info-col{
        flex: 0 0 100%;
    }
    .seating-arrangement .bus{
        overflow-x: scroll;
    }
    /*--- booking form page css end---*/ 
    /*****  ppayment form css  start  */
    .payment-sec{
        height: 100%;
    }
     /*****  ppayment form css  end */
}

