<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Numbers\V1;

use Twilio\Options;
use Twilio\Values;

abstract class SigningRequestConfigurationOptions
{

    /**
     * @param string $country The country ISO code to apply this configuration, this is an optional field, Example: US, MX
     * @param string $product The product or service for which is requesting the signature, this is an optional field, Example: Porting, Hosting
     * @return ReadSigningRequestConfigurationOptions Options builder
     */
    public static function read(
        
        string $country = Values::NONE,
        string $product = Values::NONE

    ): ReadSigningRequestConfigurationOptions
    {
        return new ReadSigningRequestConfigurationOptions(
            $country,
            $product
        );
    }

}


class ReadSigningRequestConfigurationOptions extends Options
    {
    /**
     * @param string $country The country ISO code to apply this configuration, this is an optional field, Example: US, MX
     * @param string $product The product or service for which is requesting the signature, this is an optional field, Example: Porting, Hosting
     */
    public function __construct(
        
        string $country = Values::NONE,
        string $product = Values::NONE

    ) {
        $this->options['country'] = $country;
        $this->options['product'] = $product;
    }

    /**
     * The country ISO code to apply this configuration, this is an optional field, Example: US, MX
     *
     * @param string $country The country ISO code to apply this configuration, this is an optional field, Example: US, MX
     * @return $this Fluent Builder
     */
    public function setCountry(string $country): self
    {
        $this->options['country'] = $country;
        return $this;
    }

    /**
     * The product or service for which is requesting the signature, this is an optional field, Example: Porting, Hosting
     *
     * @param string $product The product or service for which is requesting the signature, this is an optional field, Example: Porting, Hosting
     * @return $this Fluent Builder
     */
    public function setProduct(string $product): self
    {
        $this->options['product'] = $product;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Numbers.V1.ReadSigningRequestConfigurationOptions ' . $options . ']';
    }
}

