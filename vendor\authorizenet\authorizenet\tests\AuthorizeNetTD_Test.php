<?php

class AuthorizeNetTD_Test extends PHPUnit_Framework_TestCase
{


    public function testGetSettledBatchList()
    {
        $this->markTestSkipped('Ignoring for <PERSON>. Will fix after release.'); //TODO
        $request = new AuthorizeNetTD;
        $response = $request->getSettledBatchList();
        $this->assertTrue($response->isOk());
        $this->assertEquals("I00001",(string)array_pop($response->xpath("messages/message/code")));
    }

    public function testGetSettledBatchListIncludeStatistics()
    {
        $this->markTestSkipped('Ignoring for Travis. Will fix after release.'); //TODO
        $request = new AuthorizeNetTD;
        $response = $request->getSettledBatchList(true);
        $this->assertTrue($response->isOk());
    }

    public function testGetSettledBatchListForMonth()
    {
        $this->markTestSkipped('Ignoring for Travis. Will fix after release.'); //TODO
        $request = new AuthorizeNetTD;
        $response = $request->getSettledBatchListForMonth();
        $this->assertTrue($response->isOk());
    }

    public function testGetTransactionsForDay()
    {
        $request = new AuthorizeNetTD;
        $transactions = $request->getTransactionsForDay(12, 8, 2010);
        $this->assertTrue(is_array($transactions));
    }

    public function testGetTransactionList()
    {
        $this->markTestSkipped('Ignoring for Travis. Will fix after release.'); //TODO
        $request = new AuthorizeNetTD;
        $response = $request->getSettledBatchList();
        $this->assertTrue($response->isOk());
        $batches = $response->xpath("batchList/batch");
        $batch_id = (string)$batches[0]->batchId;
        $response = $request->getTransactionList($batch_id);
        $this->assertTrue($response->isOk());
    }

    public function testGetTransactionListReturnedItems()
    {
        $this->markTestSkipped('Ignoring for Travis. Will fix after release.'); //TODO
        $request = new AuthorizeNetTD;
        $batchId = 0; // Set your $batchId here
        $response = $request->getTransactionList($batchId);
        $this->assertTrue($response->isOk());
        $transactions = $response->xpath("transactions/transaction");
        $transId = $transactions[0]->transId;

        $details = new AuthorizeNetTD;
        $response = $details->getTransactionDetails($transId);
        $this->assertTrue($response->isOk());
        $transaction = $response->xml->transaction[0];
        $this->assertFalse(empty($transaction->returnedItems));

    }


    public function testGetTransactionListSubscription()
    {
        $this->markTestSkipped('Ignoring for Travis. Will fix after release.'); //TODO
        $transId = 0; // Set your $transId here

        $details = new AuthorizeNetTD;
        $response = $details->getTransactionDetails($transId);
        $this->assertTrue($response->isOk());
        $transaction = $response->xml->transaction[0];

        $this->assertFalse(empty($transaction->subscription));
    }

    public function testGetTransactionDetails()
    {
        $this->markTestSkipped('Ignoring for Travis. Will fix after release.'); //TODO
        $sale = new AuthorizeNetAIM;
        $amount = rand(1, 100);
        $response = $sale->authorizeAndCapture($amount, '*************', '04/17');
        $this->assertTrue($response->approved);

        $transId = $response->transaction_id;

        $request = new AuthorizeNetTD;
        $response = $request->getTransactionDetails($transId);
        $this->assertTrue($response->isOk());

        $this->assertEquals($transId, (string)$response->xml->transaction->transId);
        $this->assertEquals($amount, (string)$response->xml->transaction->authAmount);
        $this->assertEquals("Visa", (string)$response->xml->transaction->payment->creditCard->cardType);

    }


    public function testGetTransactionDetailsWithSolutionId()
    {
        $this->markTestSkipped('Ignoring for Travis. Will fix after release.'); //TODO
        $sale = new AuthorizeNetAIM;
        $amount = rand(1, 100);
        $sale->setCustomField('x_solution_id', 'A1000002');
        $response = $sale->authorizeAndCapture($amount, '*************', '04/17');
        $this->assertTrue($response->approved);

        $transId = $response->transaction_id;

        $request = new AuthorizeNetTD;
        $response = $request->getTransactionDetails($transId);
        $this->assertTrue($response->isOk());

        $this->assertEquals($transId, (string)$response->xml->transaction->transId);
        $this->assertEquals($amount, (string)$response->xml->transaction->authAmount);
        $this->assertEquals("Visa", (string)$response->xml->transaction->payment->creditCard->cardType);
        $this->assertEquals("A1000002", (string)$response->xml->transaction->solution->id);
    }

    public function testGetUnsettledTransactionList()
    {
        $this->markTestSkipped('Ignoring for Travis. Will fix after release.'); //TODO
        $sale = new AuthorizeNetAIM;
        $amount = rand(1, 100);
        $response = $sale->authorizeAndCapture($amount, '*************', '04/17');
        $this->assertTrue($response->approved);

        $request = new AuthorizeNetTD;
        $response = $request->getUnsettledTransactionList();
        $this->assertTrue($response->isOk());
        $this->assertTrue($response->xml->transactions->count() >= 1);
    }

    public function testGetUnsettledTransactionListHasNoReturnedItems()
    {
        $this->markTestSkipped('Ignoring for Travis. Will fix after release.'); //TODO
        $request = new AuthorizeNetTD;
        $response = $request->getUnsettledTransactionList();
        $this->assertTrue($response->isOk());
        $this->assertTrue($response->xml->transactions->count() >= 1);

        foreach($response->xml->transactions->transaction as $transaction)
        {
            if($transaction->hasReturnedItems)
            {
                $this->assertEquals("false", $transaction->hasReturnedItems);
            }
        }
    }

    
    public function testGetBatchStatistics()
    {
        $this->markTestSkipped('Ignoring for Travis. Will fix after release.'); //TODO
        $request = new AuthorizeNetTD;
        $response = $request->getSettledBatchList();
        $this->assertTrue($response->isOk());
        $this->assertTrue($response->xml->batchList->count() >= 1);
        $batchId = $response->xml->batchList->batch[0]->batchId;

        $request = new AuthorizeNetTD;
        $response = $request->getBatchStatistics($batchId);
        $this->assertTrue($response->isOk());
    }


}