<?php
namespace Aws\Route53RecoveryReadiness;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS Route53 Recovery Readiness** service.
 * @method \Aws\Result createCell(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createCellAsync(array $args = [])
 * @method \Aws\Result createCrossAccountAuthorization(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createCrossAccountAuthorizationAsync(array $args = [])
 * @method \Aws\Result createReadinessCheck(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createReadinessCheckAsync(array $args = [])
 * @method \Aws\Result createRecoveryGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createRecoveryGroupAsync(array $args = [])
 * @method \Aws\Result createResourceSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createResourceSetAsync(array $args = [])
 * @method \Aws\Result deleteCell(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteCellAsync(array $args = [])
 * @method \Aws\Result deleteCrossAccountAuthorization(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteCrossAccountAuthorizationAsync(array $args = [])
 * @method \Aws\Result deleteReadinessCheck(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteReadinessCheckAsync(array $args = [])
 * @method \Aws\Result deleteRecoveryGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteRecoveryGroupAsync(array $args = [])
 * @method \Aws\Result deleteResourceSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteResourceSetAsync(array $args = [])
 * @method \Aws\Result getArchitectureRecommendations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getArchitectureRecommendationsAsync(array $args = [])
 * @method \Aws\Result getCell(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCellAsync(array $args = [])
 * @method \Aws\Result getCellReadinessSummary(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCellReadinessSummaryAsync(array $args = [])
 * @method \Aws\Result getReadinessCheck(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getReadinessCheckAsync(array $args = [])
 * @method \Aws\Result getReadinessCheckResourceStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getReadinessCheckResourceStatusAsync(array $args = [])
 * @method \Aws\Result getReadinessCheckStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getReadinessCheckStatusAsync(array $args = [])
 * @method \Aws\Result getRecoveryGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getRecoveryGroupAsync(array $args = [])
 * @method \Aws\Result getRecoveryGroupReadinessSummary(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getRecoveryGroupReadinessSummaryAsync(array $args = [])
 * @method \Aws\Result getResourceSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getResourceSetAsync(array $args = [])
 * @method \Aws\Result listCells(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCellsAsync(array $args = [])
 * @method \Aws\Result listCrossAccountAuthorizations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCrossAccountAuthorizationsAsync(array $args = [])
 * @method \Aws\Result listReadinessChecks(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listReadinessChecksAsync(array $args = [])
 * @method \Aws\Result listRecoveryGroups(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listRecoveryGroupsAsync(array $args = [])
 * @method \Aws\Result listResourceSets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listResourceSetsAsync(array $args = [])
 * @method \Aws\Result listRules(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listRulesAsync(array $args = [])
 * @method \Aws\Result listTagsForResources(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourcesAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateCell(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateCellAsync(array $args = [])
 * @method \Aws\Result updateReadinessCheck(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateReadinessCheckAsync(array $args = [])
 * @method \Aws\Result updateRecoveryGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateRecoveryGroupAsync(array $args = [])
 * @method \Aws\Result updateResourceSet(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateResourceSetAsync(array $args = [])
 */
class Route53RecoveryReadinessClient extends AwsClient {}
