net\authorize\api\contract\v1\MessagesType:
    properties:
        resultCode:
            expose: true
            access_type: public_method
            serialized_name: resultCode
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getResultCode
                setter: setResultCode
            type: string
        message:
            expose: true
            access_type: public_method
            serialized_name: message
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getMessage
                setter: setMessage
            xml_list:
                inline: true
                entry_name: message
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            type: array<net\authorize\api\contract\v1\MessagesType\MessageAType>
