<?php
// This file was auto-generated from sdk-root/src/data/gameliftstreams/2018-05-10/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-10', 'endpointPrefix' => 'gameliftstreams', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Amazon GameLift Streams', 'serviceId' => 'GameLiftStreams', 'signatureVersion' => 'v4', 'signingName' => 'gameliftstreams', 'uid' => 'gameliftstreams-2018-05-10', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AddStreamGroupLocations' => [ 'name' => 'AddStreamGroupLocations', 'http' => [ 'method' => 'POST', 'requestUri' => '/streamgroups/{Identifier}/locations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AddStreamGroupLocationsInput', ], 'output' => [ 'shape' => 'AddStreamGroupLocationsOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'AssociateApplications' => [ 'name' => 'AssociateApplications', 'http' => [ 'method' => 'POST', 'requestUri' => '/streamgroups/{Identifier}/associations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateApplicationsInput', ], 'output' => [ 'shape' => 'AssociateApplicationsOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateApplication' => [ 'name' => 'CreateApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateApplicationInput', ], 'output' => [ 'shape' => 'CreateApplicationOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateStreamGroup' => [ 'name' => 'CreateStreamGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/streamgroups', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateStreamGroupInput', ], 'output' => [ 'shape' => 'CreateStreamGroupOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateStreamSessionConnection' => [ 'name' => 'CreateStreamSessionConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/streamgroups/{Identifier}/streamsessions/{StreamSessionIdentifier}/connections', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateStreamSessionConnectionInput', ], 'output' => [ 'shape' => 'CreateStreamSessionConnectionOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteApplication' => [ 'name' => 'DeleteApplication', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{Identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteApplicationInput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeleteStreamGroup' => [ 'name' => 'DeleteStreamGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/streamgroups/{Identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteStreamGroupInput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DisassociateApplications' => [ 'name' => 'DisassociateApplications', 'http' => [ 'method' => 'POST', 'requestUri' => '/streamgroups/{Identifier}/disassociations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateApplicationsInput', ], 'output' => [ 'shape' => 'DisassociateApplicationsOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'ExportStreamSessionFiles' => [ 'name' => 'ExportStreamSessionFiles', 'http' => [ 'method' => 'PUT', 'requestUri' => '/streamgroups/{Identifier}/streamsessions/{StreamSessionIdentifier}/exportfiles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ExportStreamSessionFilesInput', ], 'output' => [ 'shape' => 'ExportStreamSessionFilesOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'GetApplication' => [ 'name' => 'GetApplication', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetApplicationInput', ], 'output' => [ 'shape' => 'GetApplicationOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetStreamGroup' => [ 'name' => 'GetStreamGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/streamgroups/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStreamGroupInput', ], 'output' => [ 'shape' => 'GetStreamGroupOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetStreamSession' => [ 'name' => 'GetStreamSession', 'http' => [ 'method' => 'GET', 'requestUri' => '/streamgroups/{Identifier}/streamsessions/{StreamSessionIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStreamSessionInput', ], 'output' => [ 'shape' => 'GetStreamSessionOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListApplications' => [ 'name' => 'ListApplications', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListApplicationsInput', ], 'output' => [ 'shape' => 'ListApplicationsOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListStreamGroups' => [ 'name' => 'ListStreamGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/streamgroups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStreamGroupsInput', ], 'output' => [ 'shape' => 'ListStreamGroupsOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListStreamSessions' => [ 'name' => 'ListStreamSessions', 'http' => [ 'method' => 'GET', 'requestUri' => '/streamgroups/{Identifier}/streamsessions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStreamSessionsInput', ], 'output' => [ 'shape' => 'ListStreamSessionsOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListStreamSessionsByAccount' => [ 'name' => 'ListStreamSessionsByAccount', 'http' => [ 'method' => 'GET', 'requestUri' => '/streamsessions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStreamSessionsByAccountInput', ], 'output' => [ 'shape' => 'ListStreamSessionsByAccountOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'RemoveStreamGroupLocations' => [ 'name' => 'RemoveStreamGroupLocations', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/streamgroups/{Identifier}/locations', 'responseCode' => 204, ], 'input' => [ 'shape' => 'RemoveStreamGroupLocationsInput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'StartStreamSession' => [ 'name' => 'StartStreamSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/streamgroups/{Identifier}/streamsessions', 'responseCode' => 201, ], 'input' => [ 'shape' => 'StartStreamSessionInput', ], 'output' => [ 'shape' => 'StartStreamSessionOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'TerminateStreamSession' => [ 'name' => 'TerminateStreamSession', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/streamgroups/{Identifier}/streamsessions/{StreamSessionIdentifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TerminateStreamSessionInput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'UpdateApplication' => [ 'name' => 'UpdateApplication', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/applications/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateApplicationInput', ], 'output' => [ 'shape' => 'UpdateApplicationOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateStreamGroup' => [ 'name' => 'UpdateStreamGroup', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/streamgroups/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateStreamGroupInput', ], 'output' => [ 'shape' => 'UpdateStreamGroupOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AddStreamGroupLocationsInput' => [ 'type' => 'structure', 'required' => [ 'Identifier', 'LocationConfigurations', ], 'members' => [ 'Identifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], 'LocationConfigurations' => [ 'shape' => 'LocationConfigurations', ], ], ], 'AddStreamGroupLocationsOutput' => [ 'type' => 'structure', 'required' => [ 'Identifier', 'Locations', ], 'members' => [ 'Identifier' => [ 'shape' => 'Identifier', ], 'Locations' => [ 'shape' => 'LocationStates', ], ], ], 'AlwaysOnCapacity' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'ApplicationLogOutputUri' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '^$|^s3://([a-zA-Z0-9][a-zA-Z0-9._-]{1,61}[a-zA-Z0-9])(/[a-zA-Z0-9._-]+)*/?$', ], 'ApplicationSourceUri' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ApplicationStatus' => [ 'type' => 'string', 'enum' => [ 'INITIALIZED', 'PROCESSING', 'READY', 'DELETING', 'ERROR', ], ], 'ApplicationStatusReason' => [ 'type' => 'string', 'enum' => [ 'internalError', 'accessDenied', ], ], 'ApplicationSummary' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Identifier', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'Description', ], 'Id' => [ 'shape' => 'Id', ], 'LastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'RuntimeEnvironment' => [ 'shape' => 'RuntimeEnvironment', ], 'Status' => [ 'shape' => 'ApplicationStatus', ], ], ], 'ApplicationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationSummary', ], ], 'Arn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^arn:aws:gameliftstreams:([^: ]*):([0-9]{12}):([^: ]*)$', ], 'ArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], ], 'AssociateApplicationsInput' => [ 'type' => 'structure', 'required' => [ 'ApplicationIdentifiers', 'Identifier', ], 'members' => [ 'ApplicationIdentifiers' => [ 'shape' => 'Identifiers', ], 'Identifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'AssociateApplicationsOutput' => [ 'type' => 'structure', 'members' => [ 'ApplicationArns' => [ 'shape' => 'ArnList', ], 'Arn' => [ 'shape' => 'Arn', ], ], ], 'CapacityValue' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'ClientToken' => [ 'type' => 'string', 'max' => 128, 'min' => 32, 'pattern' => '^[\\x21-\\x7E]+$', ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ConnectionTimeoutSeconds' => [ 'type' => 'integer', 'box' => true, 'max' => 3600, 'min' => 1, ], 'CreateApplicationInput' => [ 'type' => 'structure', 'required' => [ 'ApplicationSourceUri', 'Description', 'ExecutablePath', 'RuntimeEnvironment', ], 'members' => [ 'ApplicationLogOutputUri' => [ 'shape' => 'ApplicationLogOutputUri', ], 'ApplicationLogPaths' => [ 'shape' => 'FilePaths', ], 'ApplicationSourceUri' => [ 'shape' => 'ApplicationSourceUri', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'Description' => [ 'shape' => 'Description', ], 'ExecutablePath' => [ 'shape' => 'ExecutablePath', ], 'RuntimeEnvironment' => [ 'shape' => 'RuntimeEnvironment', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateApplicationOutput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'ApplicationLogOutputUri' => [ 'shape' => 'ApplicationLogOutputUri', ], 'ApplicationLogPaths' => [ 'shape' => 'FilePaths', ], 'ApplicationSourceUri' => [ 'shape' => 'ApplicationSourceUri', ], 'Arn' => [ 'shape' => 'Identifier', ], 'AssociatedStreamGroups' => [ 'shape' => 'ArnList', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'Description', ], 'ExecutablePath' => [ 'shape' => 'ExecutablePath', ], 'Id' => [ 'shape' => 'Id', ], 'LastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'ReplicationStatuses' => [ 'shape' => 'ReplicationStatuses', ], 'RuntimeEnvironment' => [ 'shape' => 'RuntimeEnvironment', ], 'Status' => [ 'shape' => 'ApplicationStatus', ], 'StatusReason' => [ 'shape' => 'ApplicationStatusReason', ], ], ], 'CreateStreamGroupInput' => [ 'type' => 'structure', 'required' => [ 'Description', 'StreamClass', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'DefaultApplicationIdentifier' => [ 'shape' => 'Identifier', ], 'Description' => [ 'shape' => 'Description', ], 'LocationConfigurations' => [ 'shape' => 'LocationConfigurations', ], 'StreamClass' => [ 'shape' => 'StreamClass', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateStreamGroupOutput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Identifier', ], 'AssociatedApplications' => [ 'shape' => 'ArnList', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'DefaultApplication' => [ 'shape' => 'DefaultApplication', ], 'Description' => [ 'shape' => 'Description', ], 'Id' => [ 'shape' => 'Id', ], 'LastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'LocationStates' => [ 'shape' => 'LocationStates', ], 'Status' => [ 'shape' => 'StreamGroupStatus', ], 'StatusReason' => [ 'shape' => 'StreamGroupStatusReason', ], 'StreamClass' => [ 'shape' => 'StreamClass', ], ], ], 'CreateStreamSessionConnectionInput' => [ 'type' => 'structure', 'required' => [ 'Identifier', 'SignalRequest', 'StreamSessionIdentifier', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'Identifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], 'SignalRequest' => [ 'shape' => 'SignalRequest', ], 'StreamSessionIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'StreamSessionIdentifier', ], ], ], 'CreateStreamSessionConnectionOutput' => [ 'type' => 'structure', 'members' => [ 'SignalResponse' => [ 'shape' => 'SignalResponse', ], ], ], 'DefaultApplication' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Id' => [ 'shape' => 'Id', ], ], ], 'DeleteApplicationInput' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'DeleteStreamGroupInput' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 80, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-_.!+@/][a-zA-Z0-9-_.!+@/ ]*$', ], 'DisassociateApplicationsInput' => [ 'type' => 'structure', 'required' => [ 'ApplicationIdentifiers', 'Identifier', ], 'members' => [ 'ApplicationIdentifiers' => [ 'shape' => 'Identifiers', ], 'Identifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'DisassociateApplicationsOutput' => [ 'type' => 'structure', 'members' => [ 'ApplicationArns' => [ 'shape' => 'ArnList', ], 'Arn' => [ 'shape' => 'Arn', ], ], ], 'EnvironmentVariables' => [ 'type' => 'map', 'key' => [ 'shape' => 'EnvironmentVariablesKeyString', ], 'value' => [ 'shape' => 'EnvironmentVariablesValueString', ], 'max' => 50, 'min' => 0, ], 'EnvironmentVariablesKeyString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[_a-zA-Z][_a-zA-Z0-9]*$', ], 'EnvironmentVariablesValueString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'ExecutablePath' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ExportFilesMetadata' => [ 'type' => 'structure', 'members' => [ 'OutputUri' => [ 'shape' => 'OutputUri', ], 'Status' => [ 'shape' => 'ExportFilesStatus', ], 'StatusReason' => [ 'shape' => 'ExportFilesReason', ], ], ], 'ExportFilesReason' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'ExportFilesStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'FAILED', 'PENDING', ], ], 'ExportStreamSessionFilesInput' => [ 'type' => 'structure', 'required' => [ 'Identifier', 'OutputUri', 'StreamSessionIdentifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], 'OutputUri' => [ 'shape' => 'OutputUri', ], 'StreamSessionIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'StreamSessionIdentifier', ], ], ], 'ExportStreamSessionFilesOutput' => [ 'type' => 'structure', 'members' => [], ], 'FileLocationUri' => [ 'type' => 'string', ], 'FilePath' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'FilePaths' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilePath', ], 'max' => 10, 'min' => 0, ], 'GameLaunchArgList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 100, 'min' => 0, ], 'GetApplicationInput' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'GetApplicationOutput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'ApplicationLogOutputUri' => [ 'shape' => 'ApplicationLogOutputUri', ], 'ApplicationLogPaths' => [ 'shape' => 'FilePaths', ], 'ApplicationSourceUri' => [ 'shape' => 'ApplicationSourceUri', ], 'Arn' => [ 'shape' => 'Identifier', ], 'AssociatedStreamGroups' => [ 'shape' => 'ArnList', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'Description', ], 'ExecutablePath' => [ 'shape' => 'ExecutablePath', ], 'Id' => [ 'shape' => 'Id', ], 'LastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'ReplicationStatuses' => [ 'shape' => 'ReplicationStatuses', ], 'RuntimeEnvironment' => [ 'shape' => 'RuntimeEnvironment', ], 'Status' => [ 'shape' => 'ApplicationStatus', ], 'StatusReason' => [ 'shape' => 'ApplicationStatusReason', ], ], ], 'GetStreamGroupInput' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'GetStreamGroupOutput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Identifier', ], 'AssociatedApplications' => [ 'shape' => 'ArnList', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'DefaultApplication' => [ 'shape' => 'DefaultApplication', ], 'Description' => [ 'shape' => 'Description', ], 'Id' => [ 'shape' => 'Id', ], 'LastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'LocationStates' => [ 'shape' => 'LocationStates', ], 'Status' => [ 'shape' => 'StreamGroupStatus', ], 'StatusReason' => [ 'shape' => 'StreamGroupStatusReason', ], 'StreamClass' => [ 'shape' => 'StreamClass', ], ], ], 'GetStreamSessionInput' => [ 'type' => 'structure', 'required' => [ 'Identifier', 'StreamSessionIdentifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], 'StreamSessionIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'StreamSessionIdentifier', ], ], ], 'GetStreamSessionOutput' => [ 'type' => 'structure', 'members' => [ 'AdditionalEnvironmentVariables' => [ 'shape' => 'EnvironmentVariables', ], 'AdditionalLaunchArgs' => [ 'shape' => 'GameLaunchArgList', ], 'ApplicationArn' => [ 'shape' => 'Arn', ], 'Arn' => [ 'shape' => 'Arn', ], 'ConnectionTimeoutSeconds' => [ 'shape' => 'ConnectionTimeoutSeconds', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'Description', ], 'ExportFilesMetadata' => [ 'shape' => 'ExportFilesMetadata', ], 'LastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'Location' => [ 'shape' => 'LocationName', ], 'LogFileLocationUri' => [ 'shape' => 'FileLocationUri', ], 'Protocol' => [ 'shape' => 'Protocol', ], 'SessionLengthSeconds' => [ 'shape' => 'SessionLengthSeconds', ], 'SignalRequest' => [ 'shape' => 'SignalRequest', ], 'SignalResponse' => [ 'shape' => 'SignalResponse', ], 'Status' => [ 'shape' => 'StreamSessionStatus', ], 'StatusReason' => [ 'shape' => 'StreamSessionStatusReason', ], 'StreamGroupId' => [ 'shape' => 'Id', ], 'UserId' => [ 'shape' => 'UserId', ], 'WebSdkProtocolUrl' => [ 'shape' => 'WebSdkProtocolUrl', ], ], ], 'Id' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-]+$', ], 'Identifier' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(^[a-zA-Z0-9-]+$)|(^arn:aws:gameliftstreams:([^: ]*):([0-9]{12}):([^: ]*)$)$', ], 'Identifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'Identifier', ], 'max' => 50, 'min' => 1, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'ListApplicationsInput' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListApplicationsOutput' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'ApplicationSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStreamGroupsInput' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListStreamGroupsOutput' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'StreamGroupSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStreamSessionsByAccountInput' => [ 'type' => 'structure', 'members' => [ 'ExportFilesStatus' => [ 'shape' => 'ExportFilesStatus', 'location' => 'querystring', 'locationName' => 'ExportFilesStatus', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'Status' => [ 'shape' => 'StreamSessionStatus', 'location' => 'querystring', 'locationName' => 'Status', ], ], ], 'ListStreamSessionsByAccountOutput' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'StreamSessionSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStreamSessionsInput' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'ExportFilesStatus' => [ 'shape' => 'ExportFilesStatus', 'location' => 'querystring', 'locationName' => 'ExportFilesStatus', ], 'Identifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'Status' => [ 'shape' => 'StreamSessionStatus', 'location' => 'querystring', 'locationName' => 'Status', ], ], ], 'ListStreamSessionsOutput' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'StreamSessionSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'Tags', ], ], ], 'LocationConfiguration' => [ 'type' => 'structure', 'required' => [ 'LocationName', ], 'members' => [ 'AlwaysOnCapacity' => [ 'shape' => 'AlwaysOnCapacity', ], 'LocationName' => [ 'shape' => 'LocationName', ], 'OnDemandCapacity' => [ 'shape' => 'OnDemandCapacity', ], ], ], 'LocationConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'LocationConfiguration', ], 'max' => 100, 'min' => 1, ], 'LocationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LocationName', ], 'min' => 1, ], 'LocationName' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-]+$', ], 'LocationState' => [ 'type' => 'structure', 'members' => [ 'AllocatedCapacity' => [ 'shape' => 'CapacityValue', ], 'AlwaysOnCapacity' => [ 'shape' => 'AlwaysOnCapacity', ], 'IdleCapacity' => [ 'shape' => 'CapacityValue', ], 'LocationName' => [ 'shape' => 'LocationName', ], 'OnDemandCapacity' => [ 'shape' => 'OnDemandCapacity', ], 'RequestedCapacity' => [ 'shape' => 'CapacityValue', ], 'Status' => [ 'shape' => 'StreamGroupLocationStatus', ], ], ], 'LocationStates' => [ 'type' => 'list', 'member' => [ 'shape' => 'LocationState', ], ], 'LocationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 100, 'min' => 1, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'NextToken' => [ 'type' => 'string', ], 'OnDemandCapacity' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'OutputUri' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '^s3://.*(/|\\.zip|\\.ZIP)$', ], 'Protocol' => [ 'type' => 'string', 'enum' => [ 'WebRTC', ], ], 'RemoveStreamGroupLocationsInput' => [ 'type' => 'structure', 'required' => [ 'Identifier', 'Locations', ], 'members' => [ 'Identifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], 'Locations' => [ 'shape' => 'LocationsList', 'location' => 'querystring', 'locationName' => 'locations', ], ], ], 'ReplicationStatus' => [ 'type' => 'structure', 'members' => [ 'Location' => [ 'shape' => 'LocationName', ], 'Status' => [ 'shape' => 'ReplicationStatusType', ], ], ], 'ReplicationStatusType' => [ 'type' => 'string', 'enum' => [ 'REPLICATING', 'COMPLETED', ], ], 'ReplicationStatuses' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationStatus', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RuntimeEnvironment' => [ 'type' => 'structure', 'required' => [ 'Type', 'Version', ], 'members' => [ 'Type' => [ 'shape' => 'RuntimeEnvironmentType', ], 'Version' => [ 'shape' => 'RuntimeEnvironmentVersion', ], ], ], 'RuntimeEnvironmentType' => [ 'type' => 'string', 'enum' => [ 'PROTON', 'WINDOWS', 'UBUNTU', ], ], 'RuntimeEnvironmentVersion' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SessionLengthSeconds' => [ 'type' => 'integer', 'box' => true, 'max' => 86400, 'min' => 1, ], 'SignalRequest' => [ 'type' => 'string', 'min' => 1, 'sensitive' => true, ], 'SignalResponse' => [ 'type' => 'string', 'sensitive' => true, ], 'StartStreamSessionInput' => [ 'type' => 'structure', 'required' => [ 'ApplicationIdentifier', 'Identifier', 'Protocol', 'SignalRequest', ], 'members' => [ 'AdditionalEnvironmentVariables' => [ 'shape' => 'EnvironmentVariables', ], 'AdditionalLaunchArgs' => [ 'shape' => 'GameLaunchArgList', ], 'ApplicationIdentifier' => [ 'shape' => 'Identifier', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'ConnectionTimeoutSeconds' => [ 'shape' => 'ConnectionTimeoutSeconds', ], 'Description' => [ 'shape' => 'Description', ], 'Identifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], 'Locations' => [ 'shape' => 'LocationList', ], 'Protocol' => [ 'shape' => 'Protocol', ], 'SessionLengthSeconds' => [ 'shape' => 'SessionLengthSeconds', ], 'SignalRequest' => [ 'shape' => 'SignalRequest', ], 'UserId' => [ 'shape' => 'UserId', ], ], ], 'StartStreamSessionOutput' => [ 'type' => 'structure', 'members' => [ 'AdditionalEnvironmentVariables' => [ 'shape' => 'EnvironmentVariables', ], 'AdditionalLaunchArgs' => [ 'shape' => 'GameLaunchArgList', ], 'ApplicationArn' => [ 'shape' => 'Arn', ], 'Arn' => [ 'shape' => 'Arn', ], 'ConnectionTimeoutSeconds' => [ 'shape' => 'ConnectionTimeoutSeconds', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'Description', ], 'ExportFilesMetadata' => [ 'shape' => 'ExportFilesMetadata', ], 'LastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'Location' => [ 'shape' => 'LocationName', ], 'LogFileLocationUri' => [ 'shape' => 'FileLocationUri', ], 'Protocol' => [ 'shape' => 'Protocol', ], 'SessionLengthSeconds' => [ 'shape' => 'SessionLengthSeconds', ], 'SignalRequest' => [ 'shape' => 'SignalRequest', ], 'SignalResponse' => [ 'shape' => 'SignalResponse', ], 'Status' => [ 'shape' => 'StreamSessionStatus', ], 'StatusReason' => [ 'shape' => 'StreamSessionStatusReason', ], 'StreamGroupId' => [ 'shape' => 'Id', ], 'UserId' => [ 'shape' => 'UserId', ], 'WebSdkProtocolUrl' => [ 'shape' => 'WebSdkProtocolUrl', ], ], ], 'StreamClass' => [ 'type' => 'string', 'enum' => [ 'gen4n_high', 'gen4n_ultra', 'gen4n_win2022', 'gen5n_high', 'gen5n_ultra', 'gen5n_win2022', ], ], 'StreamGroupLocationStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVATING', 'ACTIVE', 'ERROR', 'REMOVING', ], ], 'StreamGroupStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVATING', 'UPDATING_LOCATIONS', 'ACTIVE', 'ACTIVE_WITH_ERRORS', 'ERROR', 'DELETING', ], ], 'StreamGroupStatusReason' => [ 'type' => 'string', 'enum' => [ 'internalError', 'noAvailableInstances', ], ], 'StreamGroupSummary' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Identifier', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'DefaultApplication' => [ 'shape' => 'DefaultApplication', ], 'Description' => [ 'shape' => 'Description', ], 'Id' => [ 'shape' => 'Id', ], 'LastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'Status' => [ 'shape' => 'StreamGroupStatus', ], 'StreamClass' => [ 'shape' => 'StreamClass', ], ], ], 'StreamGroupSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StreamGroupSummary', ], ], 'StreamSessionStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVATING', 'ACTIVE', 'CONNECTED', 'PENDING_CLIENT_RECONNECTION', 'RECONNECTING', 'TERMINATING', 'TERMINATED', 'ERROR', ], ], 'StreamSessionStatusReason' => [ 'type' => 'string', 'enum' => [ 'internalError', 'invalidSignalRequest', 'placementTimeout', 'applicationLogS3DestinationError', ], ], 'StreamSessionSummary' => [ 'type' => 'structure', 'members' => [ 'ApplicationArn' => [ 'shape' => 'Arn', ], 'Arn' => [ 'shape' => 'Arn', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'ExportFilesMetadata' => [ 'shape' => 'ExportFilesMetadata', ], 'LastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'Location' => [ 'shape' => 'LocationName', ], 'Protocol' => [ 'shape' => 'Protocol', ], 'Status' => [ 'shape' => 'StreamSessionStatus', ], 'UserId' => [ 'shape' => 'UserId', ], ], ], 'StreamSessionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StreamSessionSummary', ], ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'TerminateStreamSessionInput' => [ 'type' => 'structure', 'required' => [ 'Identifier', 'StreamSessionIdentifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], 'StreamSessionIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'StreamSessionIdentifier', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateApplicationInput' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'ApplicationLogOutputUri' => [ 'shape' => 'ApplicationLogOutputUri', ], 'ApplicationLogPaths' => [ 'shape' => 'FilePaths', ], 'Description' => [ 'shape' => 'Description', ], 'Identifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'UpdateApplicationOutput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'ApplicationLogOutputUri' => [ 'shape' => 'ApplicationLogOutputUri', ], 'ApplicationLogPaths' => [ 'shape' => 'FilePaths', ], 'ApplicationSourceUri' => [ 'shape' => 'ApplicationSourceUri', ], 'Arn' => [ 'shape' => 'Identifier', ], 'AssociatedStreamGroups' => [ 'shape' => 'ArnList', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'Description', ], 'ExecutablePath' => [ 'shape' => 'ExecutablePath', ], 'Id' => [ 'shape' => 'Id', ], 'LastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'ReplicationStatuses' => [ 'shape' => 'ReplicationStatuses', ], 'RuntimeEnvironment' => [ 'shape' => 'RuntimeEnvironment', ], 'Status' => [ 'shape' => 'ApplicationStatus', ], 'StatusReason' => [ 'shape' => 'ApplicationStatusReason', ], ], ], 'UpdateStreamGroupInput' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Description' => [ 'shape' => 'Description', ], 'Identifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], 'LocationConfigurations' => [ 'shape' => 'LocationConfigurations', ], ], ], 'UpdateStreamGroupOutput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Identifier', ], 'AssociatedApplications' => [ 'shape' => 'ArnList', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'DefaultApplication' => [ 'shape' => 'DefaultApplication', ], 'Description' => [ 'shape' => 'Description', ], 'Id' => [ 'shape' => 'Id', ], 'LastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'LocationStates' => [ 'shape' => 'LocationStates', ], 'Status' => [ 'shape' => 'StreamGroupStatus', ], 'StatusReason' => [ 'shape' => 'StreamGroupStatusReason', ], 'StreamClass' => [ 'shape' => 'StreamClass', ], ], ], 'UserId' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '^[-_a-zA-Z0-9/=+]*$', ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'WebSdkProtocolUrl' => [ 'type' => 'string', ], ],];
