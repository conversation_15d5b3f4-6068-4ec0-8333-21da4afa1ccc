net\authorize\api\contract\v1\SubsequentAuthInformationType:
    properties:
        originalNetworkTransId:
            expose: true
            access_type: public_method
            serialized_name: originalNetworkTransId
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getOriginalNetworkTransId
                setter: setOriginalNetworkTransId
            type: string
        originalAuthAmount:
            expose: true
            access_type: public_method
            serialized_name: originalAuthAmount
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getOriginalAuthAmount
                setter: setOriginalAuthAmount
            type: float
        reason:
            expose: true
            access_type: public_method
            serialized_name: reason
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getReason
                setter: setReason
            type: string
