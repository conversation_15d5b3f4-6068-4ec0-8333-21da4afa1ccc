name: tests

on: [push]

jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    strategy:
      matrix:
        php: ['8.1', '8.2', '8.3', '8.4']
        laravel: ['10.0', '10.48', '11.0', '11.25']
        exclude:
          - php: '8.1'
            laravel: '11.0'
          - php: '8.1'
            laravel: '11.25'
    name: PHP ${{ matrix.php }} Laravel ${{ matrix.laravel }}
    steps:
      - name: Checkout
        uses: actions/checkout@master
      - name: Installing PHP
        uses: shivammathur/setup-php@master
        with:
          php-version: ${{ matrix.php }}
          extensions: mbstring, json
          tools: composer:v2
      - name: Lock Package Versions
        run: |
          composer require "illuminate/http:${{ matrix.laravel }}.*" --no-update -v
          composer require "illuminate/routing:${{ matrix.laravel }}.*" --no-update -v
          composer require "illuminate/session:${{ matrix.laravel }}.*" --no-update -v
          composer require "illuminate/support:${{ matrix.laravel }}.*" --no-update -v
          composer require "illuminate/view:${{ matrix.laravel }}.*" --no-update -v
          composer require "illuminate/database:${{ matrix.laravel }}.*" --no-update -v
      - name: Composer Install
        run: composer install --prefer-dist --no-progress --no-interaction
      - name: Run Tests
        run: php vendor/bin/phpunit --testdox
