{"name": "kkomelin/laravel-translatable-string-exporter", "description": "Translatable String Exporter for Laravel", "keywords": ["laravel", "translations", "translation", "export", "exporter", "json", "localization"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.0", "ext-json": "*", "illuminate/support": "^8|^9|^10.0|^11.0|^12.0", "illuminate/translation": "^8|^9|^10.0|^11.0|^12.0", "symfony/finder": "^5|^6|^7.0"}, "require-dev": {"nunomaduro/larastan": "^1.0|^2.0|^3.0", "phpunit/phpunit": "^9.0|^10.5|^11.5|^12.0", "orchestra/testbench": "^6.0|^7.0|^8.0|^9.0|^10.0"}, "autoload": {"psr-4": {"KKomelin\\TranslatableStringExporter\\": "src/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "extra": {"laravel": {"providers": ["KKomelin\\TranslatableStringExporter\\Providers\\ExporterServiceProvider"]}}, "config": {"sort-packages": true}, "minimum-stability": "stable"}