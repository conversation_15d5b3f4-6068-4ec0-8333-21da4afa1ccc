<?php
// This file was auto-generated from sdk-root/src/data/finspace/2021-03-12/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2021-03-12', 'endpointPrefix' => 'finspace', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceAbbreviation' => 'finspace', 'serviceFullName' => 'FinSpace User Environment Management service', 'serviceId' => 'finspace', 'signatureVersion' => 'v4', 'signingName' => 'finspace', 'uid' => 'finspace-2021-03-12', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'CreateEnvironment' => [ 'name' => 'CreateEnvironment', 'http' => [ 'method' => 'POST', 'requestUri' => '/environment', ], 'input' => [ 'shape' => 'CreateEnvironmentRequest', ], 'output' => [ 'shape' => 'CreateEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'LimitExceededException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'CreateKxChangeset' => [ 'name' => 'CreateKxChangeset', 'http' => [ 'method' => 'POST', 'requestUri' => '/kx/environments/{environmentId}/databases/{databaseName}/changesets', ], 'input' => [ 'shape' => 'CreateKxChangesetRequest', ], 'output' => [ 'shape' => 'CreateKxChangesetResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateKxCluster' => [ 'name' => 'CreateKxCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/kx/environments/{environmentId}/clusters', ], 'input' => [ 'shape' => 'CreateKxClusterRequest', ], 'output' => [ 'shape' => 'CreateKxClusterResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateKxDatabase' => [ 'name' => 'CreateKxDatabase', 'http' => [ 'method' => 'POST', 'requestUri' => '/kx/environments/{environmentId}/databases', ], 'input' => [ 'shape' => 'CreateKxDatabaseRequest', ], 'output' => [ 'shape' => 'CreateKxDatabaseResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateKxDataview' => [ 'name' => 'CreateKxDataview', 'http' => [ 'method' => 'POST', 'requestUri' => '/kx/environments/{environmentId}/databases/{databaseName}/dataviews', ], 'input' => [ 'shape' => 'CreateKxDataviewRequest', ], 'output' => [ 'shape' => 'CreateKxDataviewResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], ], ], 'CreateKxEnvironment' => [ 'name' => 'CreateKxEnvironment', 'http' => [ 'method' => 'POST', 'requestUri' => '/kx/environments', ], 'input' => [ 'shape' => 'CreateKxEnvironmentRequest', ], 'output' => [ 'shape' => 'CreateKxEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateKxScalingGroup' => [ 'name' => 'CreateKxScalingGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/kx/environments/{environmentId}/scalingGroups', ], 'input' => [ 'shape' => 'CreateKxScalingGroupRequest', ], 'output' => [ 'shape' => 'CreateKxScalingGroupResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateKxUser' => [ 'name' => 'CreateKxUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/kx/environments/{environmentId}/users', ], 'input' => [ 'shape' => 'CreateKxUserRequest', ], 'output' => [ 'shape' => 'CreateKxUserResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateKxVolume' => [ 'name' => 'CreateKxVolume', 'http' => [ 'method' => 'POST', 'requestUri' => '/kx/environments/{environmentId}/kxvolumes', ], 'input' => [ 'shape' => 'CreateKxVolumeRequest', ], 'output' => [ 'shape' => 'CreateKxVolumeResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteEnvironment' => [ 'name' => 'DeleteEnvironment', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/environment/{environmentId}', ], 'input' => [ 'shape' => 'DeleteEnvironmentRequest', ], 'output' => [ 'shape' => 'DeleteEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'DeleteKxCluster' => [ 'name' => 'DeleteKxCluster', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/kx/environments/{environmentId}/clusters/{clusterName}', ], 'input' => [ 'shape' => 'DeleteKxClusterRequest', ], 'output' => [ 'shape' => 'DeleteKxClusterResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteKxClusterNode' => [ 'name' => 'DeleteKxClusterNode', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/kx/environments/{environmentId}/clusters/{clusterName}/nodes/{nodeId}', ], 'input' => [ 'shape' => 'DeleteKxClusterNodeRequest', ], 'output' => [ 'shape' => 'DeleteKxClusterNodeResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteKxDatabase' => [ 'name' => 'DeleteKxDatabase', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/kx/environments/{environmentId}/databases/{databaseName}', ], 'input' => [ 'shape' => 'DeleteKxDatabaseRequest', ], 'output' => [ 'shape' => 'DeleteKxDatabaseResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteKxDataview' => [ 'name' => 'DeleteKxDataview', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/kx/environments/{environmentId}/databases/{databaseName}/dataviews/{dataviewName}', ], 'input' => [ 'shape' => 'DeleteKxDataviewRequest', ], 'output' => [ 'shape' => 'DeleteKxDataviewResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteKxEnvironment' => [ 'name' => 'DeleteKxEnvironment', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/kx/environments/{environmentId}', ], 'input' => [ 'shape' => 'DeleteKxEnvironmentRequest', ], 'output' => [ 'shape' => 'DeleteKxEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteKxScalingGroup' => [ 'name' => 'DeleteKxScalingGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/kx/environments/{environmentId}/scalingGroups/{scalingGroupName}', ], 'input' => [ 'shape' => 'DeleteKxScalingGroupRequest', ], 'output' => [ 'shape' => 'DeleteKxScalingGroupResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteKxUser' => [ 'name' => 'DeleteKxUser', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/kx/environments/{environmentId}/users/{userName}', ], 'input' => [ 'shape' => 'DeleteKxUserRequest', ], 'output' => [ 'shape' => 'DeleteKxUserResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteKxVolume' => [ 'name' => 'DeleteKxVolume', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/kx/environments/{environmentId}/kxvolumes/{volumeName}', ], 'input' => [ 'shape' => 'DeleteKxVolumeRequest', ], 'output' => [ 'shape' => 'DeleteKxVolumeResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetEnvironment' => [ 'name' => 'GetEnvironment', 'http' => [ 'method' => 'GET', 'requestUri' => '/environment/{environmentId}', ], 'input' => [ 'shape' => 'GetEnvironmentRequest', ], 'output' => [ 'shape' => 'GetEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'GetKxChangeset' => [ 'name' => 'GetKxChangeset', 'http' => [ 'method' => 'GET', 'requestUri' => '/kx/environments/{environmentId}/databases/{databaseName}/changesets/{changesetId}', ], 'input' => [ 'shape' => 'GetKxChangesetRequest', ], 'output' => [ 'shape' => 'GetKxChangesetResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetKxCluster' => [ 'name' => 'GetKxCluster', 'http' => [ 'method' => 'GET', 'requestUri' => '/kx/environments/{environmentId}/clusters/{clusterName}', ], 'input' => [ 'shape' => 'GetKxClusterRequest', ], 'output' => [ 'shape' => 'GetKxClusterResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetKxConnectionString' => [ 'name' => 'GetKxConnectionString', 'http' => [ 'method' => 'GET', 'requestUri' => '/kx/environments/{environmentId}/connectionString', ], 'input' => [ 'shape' => 'GetKxConnectionStringRequest', ], 'output' => [ 'shape' => 'GetKxConnectionStringResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetKxDatabase' => [ 'name' => 'GetKxDatabase', 'http' => [ 'method' => 'GET', 'requestUri' => '/kx/environments/{environmentId}/databases/{databaseName}', ], 'input' => [ 'shape' => 'GetKxDatabaseRequest', ], 'output' => [ 'shape' => 'GetKxDatabaseResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetKxDataview' => [ 'name' => 'GetKxDataview', 'http' => [ 'method' => 'GET', 'requestUri' => '/kx/environments/{environmentId}/databases/{databaseName}/dataviews/{dataviewName}', ], 'input' => [ 'shape' => 'GetKxDataviewRequest', ], 'output' => [ 'shape' => 'GetKxDataviewResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetKxEnvironment' => [ 'name' => 'GetKxEnvironment', 'http' => [ 'method' => 'GET', 'requestUri' => '/kx/environments/{environmentId}', ], 'input' => [ 'shape' => 'GetKxEnvironmentRequest', ], 'output' => [ 'shape' => 'GetKxEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetKxScalingGroup' => [ 'name' => 'GetKxScalingGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/kx/environments/{environmentId}/scalingGroups/{scalingGroupName}', ], 'input' => [ 'shape' => 'GetKxScalingGroupRequest', ], 'output' => [ 'shape' => 'GetKxScalingGroupResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetKxUser' => [ 'name' => 'GetKxUser', 'http' => [ 'method' => 'GET', 'requestUri' => '/kx/environments/{environmentId}/users/{userName}', ], 'input' => [ 'shape' => 'GetKxUserRequest', ], 'output' => [ 'shape' => 'GetKxUserResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetKxVolume' => [ 'name' => 'GetKxVolume', 'http' => [ 'method' => 'GET', 'requestUri' => '/kx/environments/{environmentId}/kxvolumes/{volumeName}', ], 'input' => [ 'shape' => 'GetKxVolumeRequest', ], 'output' => [ 'shape' => 'GetKxVolumeResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListEnvironments' => [ 'name' => 'ListEnvironments', 'http' => [ 'method' => 'GET', 'requestUri' => '/environment', ], 'input' => [ 'shape' => 'ListEnvironmentsRequest', ], 'output' => [ 'shape' => 'ListEnvironmentsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'ListKxChangesets' => [ 'name' => 'ListKxChangesets', 'http' => [ 'method' => 'GET', 'requestUri' => '/kx/environments/{environmentId}/databases/{databaseName}/changesets', ], 'input' => [ 'shape' => 'ListKxChangesetsRequest', ], 'output' => [ 'shape' => 'ListKxChangesetsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListKxClusterNodes' => [ 'name' => 'ListKxClusterNodes', 'http' => [ 'method' => 'GET', 'requestUri' => '/kx/environments/{environmentId}/clusters/{clusterName}/nodes', ], 'input' => [ 'shape' => 'ListKxClusterNodesRequest', ], 'output' => [ 'shape' => 'ListKxClusterNodesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListKxClusters' => [ 'name' => 'ListKxClusters', 'http' => [ 'method' => 'GET', 'requestUri' => '/kx/environments/{environmentId}/clusters', ], 'input' => [ 'shape' => 'ListKxClustersRequest', ], 'output' => [ 'shape' => 'ListKxClustersResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListKxDatabases' => [ 'name' => 'ListKxDatabases', 'http' => [ 'method' => 'GET', 'requestUri' => '/kx/environments/{environmentId}/databases', ], 'input' => [ 'shape' => 'ListKxDatabasesRequest', ], 'output' => [ 'shape' => 'ListKxDatabasesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListKxDataviews' => [ 'name' => 'ListKxDataviews', 'http' => [ 'method' => 'GET', 'requestUri' => '/kx/environments/{environmentId}/databases/{databaseName}/dataviews', ], 'input' => [ 'shape' => 'ListKxDataviewsRequest', ], 'output' => [ 'shape' => 'ListKxDataviewsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListKxEnvironments' => [ 'name' => 'ListKxEnvironments', 'http' => [ 'method' => 'GET', 'requestUri' => '/kx/environments', ], 'input' => [ 'shape' => 'ListKxEnvironmentsRequest', ], 'output' => [ 'shape' => 'ListKxEnvironmentsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListKxScalingGroups' => [ 'name' => 'ListKxScalingGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/kx/environments/{environmentId}/scalingGroups', ], 'input' => [ 'shape' => 'ListKxScalingGroupsRequest', ], 'output' => [ 'shape' => 'ListKxScalingGroupsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListKxUsers' => [ 'name' => 'ListKxUsers', 'http' => [ 'method' => 'GET', 'requestUri' => '/kx/environments/{environmentId}/users', ], 'input' => [ 'shape' => 'ListKxUsersRequest', ], 'output' => [ 'shape' => 'ListKxUsersResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListKxVolumes' => [ 'name' => 'ListKxVolumes', 'http' => [ 'method' => 'GET', 'requestUri' => '/kx/environments/{environmentId}/kxvolumes', ], 'input' => [ 'shape' => 'ListKxVolumesRequest', ], 'output' => [ 'shape' => 'ListKxVolumesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateEnvironment' => [ 'name' => 'UpdateEnvironment', 'http' => [ 'method' => 'PUT', 'requestUri' => '/environment/{environmentId}', ], 'input' => [ 'shape' => 'UpdateEnvironmentRequest', ], 'output' => [ 'shape' => 'UpdateEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This method will be discontinued.', ], 'UpdateKxClusterCodeConfiguration' => [ 'name' => 'UpdateKxClusterCodeConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/kx/environments/{environmentId}/clusters/{clusterName}/configuration/code', ], 'input' => [ 'shape' => 'UpdateKxClusterCodeConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateKxClusterCodeConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateKxClusterDatabases' => [ 'name' => 'UpdateKxClusterDatabases', 'http' => [ 'method' => 'PUT', 'requestUri' => '/kx/environments/{environmentId}/clusters/{clusterName}/configuration/databases', ], 'input' => [ 'shape' => 'UpdateKxClusterDatabasesRequest', ], 'output' => [ 'shape' => 'UpdateKxClusterDatabasesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateKxDatabase' => [ 'name' => 'UpdateKxDatabase', 'http' => [ 'method' => 'PUT', 'requestUri' => '/kx/environments/{environmentId}/databases/{databaseName}', ], 'input' => [ 'shape' => 'UpdateKxDatabaseRequest', ], 'output' => [ 'shape' => 'UpdateKxDatabaseResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateKxDataview' => [ 'name' => 'UpdateKxDataview', 'http' => [ 'method' => 'PUT', 'requestUri' => '/kx/environments/{environmentId}/databases/{databaseName}/dataviews/{dataviewName}', ], 'input' => [ 'shape' => 'UpdateKxDataviewRequest', ], 'output' => [ 'shape' => 'UpdateKxDataviewResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], ], ], 'UpdateKxEnvironment' => [ 'name' => 'UpdateKxEnvironment', 'http' => [ 'method' => 'PUT', 'requestUri' => '/kx/environments/{environmentId}', ], 'input' => [ 'shape' => 'UpdateKxEnvironmentRequest', ], 'output' => [ 'shape' => 'UpdateKxEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateKxEnvironmentNetwork' => [ 'name' => 'UpdateKxEnvironmentNetwork', 'http' => [ 'method' => 'PUT', 'requestUri' => '/kx/environments/{environmentId}/network', ], 'input' => [ 'shape' => 'UpdateKxEnvironmentNetworkRequest', ], 'output' => [ 'shape' => 'UpdateKxEnvironmentNetworkResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateKxUser' => [ 'name' => 'UpdateKxUser', 'http' => [ 'method' => 'PUT', 'requestUri' => '/kx/environments/{environmentId}/users/{userName}', ], 'input' => [ 'shape' => 'UpdateKxUserRequest', ], 'output' => [ 'shape' => 'UpdateKxUserResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateKxVolume' => [ 'name' => 'UpdateKxVolume', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/kx/environments/{environmentId}/kxvolumes/{volumeName}', ], 'input' => [ 'shape' => 'UpdateKxVolumeRequest', ], 'output' => [ 'shape' => 'UpdateKxVolumeResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AttachedClusterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KxClusterName', ], ], 'AttributeMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'FederationAttributeKey', ], 'value' => [ 'shape' => 'FederationAttributeValue', ], ], 'AutoScalingConfiguration' => [ 'type' => 'structure', 'members' => [ 'minNodeCount' => [ 'shape' => 'NodeCount', ], 'maxNodeCount' => [ 'shape' => 'NodeCount', ], 'autoScalingMetric' => [ 'shape' => 'AutoScalingMetric', ], 'metricTarget' => [ 'shape' => 'AutoScalingMetricTarget', ], 'scaleInCooldownSeconds' => [ 'shape' => 'CooldownTime', ], 'scaleOutCooldownSeconds' => [ 'shape' => 'CooldownTime', ], ], ], 'AutoScalingMetric' => [ 'type' => 'string', 'enum' => [ 'CPU_UTILIZATION_PERCENTAGE', ], ], 'AutoScalingMetricTarget' => [ 'type' => 'double', 'max' => 100, 'min' => 1, ], 'AvailabilityZoneId' => [ 'type' => 'string', 'max' => 12, 'min' => 8, 'pattern' => '^[a-zA-Z0-9-]+$', ], 'AvailabilityZoneIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'AvailabilityZoneId', ], ], 'BoxedInteger' => [ 'type' => 'integer', 'box' => true, ], 'CapacityConfiguration' => [ 'type' => 'structure', 'members' => [ 'nodeType' => [ 'shape' => 'NodeType', ], 'nodeCount' => [ 'shape' => 'NodeCount', ], ], ], 'ChangeRequest' => [ 'type' => 'structure', 'required' => [ 'changeType', 'dbPath', ], 'members' => [ 'changeType' => [ 'shape' => 'ChangeType', ], 's3Path' => [ 'shape' => 'S3Path', ], 'dbPath' => [ 'shape' => 'DbPath', ], ], ], 'ChangeRequests' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChangeRequest', ], 'max' => 32, 'min' => 1, ], 'ChangeType' => [ 'type' => 'string', 'enum' => [ 'PUT', 'DELETE', ], ], 'ChangesetId' => [ 'type' => 'string', 'max' => 26, 'min' => 1, 'pattern' => '^[a-zA-Z0-9]+$', ], 'ChangesetStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'PROCESSING', 'FAILED', 'COMPLETED', ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 36, 'min' => 1, 'pattern' => '.*\\S.*', ], 'ClientTokenString' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-]+$', ], 'ClusterNodeCount' => [ 'type' => 'integer', 'min' => 1, ], 'CodeConfiguration' => [ 'type' => 'structure', 'members' => [ 's3Bucket' => [ 'shape' => 'S3Bucket', ], 's3Key' => [ 'shape' => 'S3Key', ], 's3ObjectVersion' => [ 'shape' => 'S3ObjectVersion', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], 'reason' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'CooldownTime' => [ 'type' => 'double', 'max' => 100000, 'min' => 0, ], 'CpuCount' => [ 'type' => 'double', 'min' => 0.1, ], 'CreateEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'EnvironmentName', ], 'description' => [ 'shape' => 'Description', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'tags' => [ 'shape' => 'TagMap', ], 'federationMode' => [ 'shape' => 'FederationMode', ], 'federationParameters' => [ 'shape' => 'FederationParameters', ], 'superuserParameters' => [ 'shape' => 'SuperuserParameters', ], 'dataBundles' => [ 'shape' => 'DataBundleArns', ], ], ], 'CreateEnvironmentResponse' => [ 'type' => 'structure', 'members' => [ 'environmentId' => [ 'shape' => 'IdType', ], 'environmentArn' => [ 'shape' => 'EnvironmentArn', ], 'environmentUrl' => [ 'shape' => 'url', ], ], ], 'CreateKxChangesetRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'databaseName', 'changeRequests', 'clientToken', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'databaseName' => [ 'shape' => 'DatabaseName', 'location' => 'uri', 'locationName' => 'databaseName', ], 'changeRequests' => [ 'shape' => 'ChangeRequests', ], 'clientToken' => [ 'shape' => 'ClientTokenString', 'idempotencyToken' => true, ], ], ], 'CreateKxChangesetResponse' => [ 'type' => 'structure', 'members' => [ 'changesetId' => [ 'shape' => 'ChangesetId', ], 'databaseName' => [ 'shape' => 'DatabaseName', ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'changeRequests' => [ 'shape' => 'ChangeRequests', ], 'createdTimestamp' => [ 'shape' => 'Timestamp', ], 'lastModifiedTimestamp' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'ChangesetStatus', ], 'errorInfo' => [ 'shape' => 'ErrorInfo', ], ], ], 'CreateKxClusterRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'clusterName', 'clusterType', 'releaseLabel', 'vpcConfiguration', 'azMode', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'environmentId' => [ 'shape' => 'KxEnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'clusterName' => [ 'shape' => 'KxClusterName', ], 'clusterType' => [ 'shape' => 'KxClusterType', ], 'tickerplantLogConfiguration' => [ 'shape' => 'TickerplantLogConfiguration', ], 'databases' => [ 'shape' => 'KxDatabaseConfigurations', ], 'cacheStorageConfigurations' => [ 'shape' => 'KxCacheStorageConfigurations', ], 'autoScalingConfiguration' => [ 'shape' => 'AutoScalingConfiguration', ], 'clusterDescription' => [ 'shape' => 'KxClusterDescription', ], 'capacityConfiguration' => [ 'shape' => 'CapacityConfiguration', ], 'releaseLabel' => [ 'shape' => 'ReleaseLabel', ], 'vpcConfiguration' => [ 'shape' => 'VpcConfiguration', ], 'initializationScript' => [ 'shape' => 'InitializationScriptFilePath', ], 'commandLineArguments' => [ 'shape' => 'KxCommandLineArguments', ], 'code' => [ 'shape' => 'CodeConfiguration', ], 'executionRole' => [ 'shape' => 'ExecutionRoleArn', ], 'savedownStorageConfiguration' => [ 'shape' => 'KxSavedownStorageConfiguration', ], 'azMode' => [ 'shape' => 'KxAzMode', ], 'availabilityZoneId' => [ 'shape' => 'AvailabilityZoneId', ], 'tags' => [ 'shape' => 'TagMap', ], 'scalingGroupConfiguration' => [ 'shape' => 'KxScalingGroupConfiguration', ], ], ], 'CreateKxClusterResponse' => [ 'type' => 'structure', 'members' => [ 'environmentId' => [ 'shape' => 'KxEnvironmentId', ], 'status' => [ 'shape' => 'KxClusterStatus', ], 'statusReason' => [ 'shape' => 'KxClusterStatusReason', ], 'clusterName' => [ 'shape' => 'KxClusterName', ], 'clusterType' => [ 'shape' => 'KxClusterType', ], 'tickerplantLogConfiguration' => [ 'shape' => 'TickerplantLogConfiguration', ], 'volumes' => [ 'shape' => 'Volumes', ], 'databases' => [ 'shape' => 'KxDatabaseConfigurations', ], 'cacheStorageConfigurations' => [ 'shape' => 'KxCacheStorageConfigurations', ], 'autoScalingConfiguration' => [ 'shape' => 'AutoScalingConfiguration', ], 'clusterDescription' => [ 'shape' => 'KxClusterDescription', ], 'capacityConfiguration' => [ 'shape' => 'CapacityConfiguration', ], 'releaseLabel' => [ 'shape' => 'ReleaseLabel', ], 'vpcConfiguration' => [ 'shape' => 'VpcConfiguration', ], 'initializationScript' => [ 'shape' => 'InitializationScriptFilePath', ], 'commandLineArguments' => [ 'shape' => 'KxCommandLineArguments', ], 'code' => [ 'shape' => 'CodeConfiguration', ], 'executionRole' => [ 'shape' => 'ExecutionRoleArn', ], 'lastModifiedTimestamp' => [ 'shape' => 'Timestamp', ], 'savedownStorageConfiguration' => [ 'shape' => 'KxSavedownStorageConfiguration', ], 'azMode' => [ 'shape' => 'KxAzMode', ], 'availabilityZoneId' => [ 'shape' => 'AvailabilityZoneId', ], 'createdTimestamp' => [ 'shape' => 'Timestamp', ], 'scalingGroupConfiguration' => [ 'shape' => 'KxScalingGroupConfiguration', ], ], ], 'CreateKxDatabaseRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'databaseName', 'clientToken', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'databaseName' => [ 'shape' => 'DatabaseName', ], 'description' => [ 'shape' => 'Description', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientToken' => [ 'shape' => 'ClientTokenString', 'idempotencyToken' => true, ], ], ], 'CreateKxDatabaseResponse' => [ 'type' => 'structure', 'members' => [ 'databaseName' => [ 'shape' => 'DatabaseName', ], 'databaseArn' => [ 'shape' => 'DatabaseArn', ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'description' => [ 'shape' => 'Description', ], 'createdTimestamp' => [ 'shape' => 'Timestamp', ], 'lastModifiedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'CreateKxDataviewRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'databaseName', 'dataviewName', 'azMode', 'clientToken', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'databaseName' => [ 'shape' => 'DatabaseName', 'location' => 'uri', 'locationName' => 'databaseName', ], 'dataviewName' => [ 'shape' => 'KxDataviewName', ], 'azMode' => [ 'shape' => 'KxAzMode', ], 'availabilityZoneId' => [ 'shape' => 'AvailabilityZoneId', ], 'changesetId' => [ 'shape' => 'ChangesetId', ], 'segmentConfigurations' => [ 'shape' => 'KxDataviewSegmentConfigurationList', ], 'autoUpdate' => [ 'shape' => 'booleanValue', ], 'readWrite' => [ 'shape' => 'booleanValue', ], 'description' => [ 'shape' => 'Description', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientToken' => [ 'shape' => 'ClientTokenString', 'idempotencyToken' => true, ], ], ], 'CreateKxDataviewResponse' => [ 'type' => 'structure', 'members' => [ 'dataviewName' => [ 'shape' => 'KxDataviewName', ], 'databaseName' => [ 'shape' => 'DatabaseName', ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'azMode' => [ 'shape' => 'KxAzMode', ], 'availabilityZoneId' => [ 'shape' => 'AvailabilityZoneId', ], 'changesetId' => [ 'shape' => 'ChangesetId', ], 'segmentConfigurations' => [ 'shape' => 'KxDataviewSegmentConfigurationList', ], 'description' => [ 'shape' => 'Description', ], 'autoUpdate' => [ 'shape' => 'booleanValue', ], 'readWrite' => [ 'shape' => 'booleanValue', ], 'createdTimestamp' => [ 'shape' => 'Timestamp', ], 'lastModifiedTimestamp' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'KxDataviewStatus', ], ], ], 'CreateKxEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'kmsKeyId', ], 'members' => [ 'name' => [ 'shape' => 'KxEnvironmentName', ], 'description' => [ 'shape' => 'Description', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyARN', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateKxEnvironmentResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'KxEnvironmentName', ], 'status' => [ 'shape' => 'EnvironmentStatus', ], 'environmentId' => [ 'shape' => 'IdType', ], 'description' => [ 'shape' => 'Description', ], 'environmentArn' => [ 'shape' => 'EnvironmentArn', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'creationTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'CreateKxScalingGroupRequest' => [ 'type' => 'structure', 'required' => [ 'clientToken', 'environmentId', 'scalingGroupName', 'hostType', 'availabilityZoneId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'environmentId' => [ 'shape' => 'KxEnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'scalingGroupName' => [ 'shape' => 'KxScalingGroupName', ], 'hostType' => [ 'shape' => 'KxHostType', ], 'availabilityZoneId' => [ 'shape' => 'AvailabilityZoneId', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateKxScalingGroupResponse' => [ 'type' => 'structure', 'members' => [ 'environmentId' => [ 'shape' => 'KxEnvironmentId', ], 'scalingGroupName' => [ 'shape' => 'KxScalingGroupName', ], 'hostType' => [ 'shape' => 'KxHostType', ], 'availabilityZoneId' => [ 'shape' => 'AvailabilityZoneId', ], 'status' => [ 'shape' => 'KxScalingGroupStatus', ], 'lastModifiedTimestamp' => [ 'shape' => 'Timestamp', ], 'createdTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'CreateKxUserRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'userName', 'iamRole', ], 'members' => [ 'environmentId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'environmentId', ], 'userName' => [ 'shape' => 'KxUserNameString', ], 'iamRole' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateKxUserResponse' => [ 'type' => 'structure', 'members' => [ 'userName' => [ 'shape' => 'KxUserNameString', ], 'userArn' => [ 'shape' => 'KxUserArn', ], 'environmentId' => [ 'shape' => 'IdType', ], 'iamRole' => [ 'shape' => 'RoleArn', ], ], ], 'CreateKxVolumeRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'volumeType', 'volumeName', 'azMode', 'availabilityZoneIds', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'environmentId' => [ 'shape' => 'KxEnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'volumeType' => [ 'shape' => 'KxVolumeType', ], 'volumeName' => [ 'shape' => 'KxVolumeName', ], 'description' => [ 'shape' => 'Description', ], 'nas1Configuration' => [ 'shape' => 'KxNAS1Configuration', ], 'azMode' => [ 'shape' => 'KxAzMode', ], 'availabilityZoneIds' => [ 'shape' => 'AvailabilityZoneIds', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateKxVolumeResponse' => [ 'type' => 'structure', 'members' => [ 'environmentId' => [ 'shape' => 'KxEnvironmentId', ], 'volumeName' => [ 'shape' => 'KxVolumeName', ], 'volumeType' => [ 'shape' => 'KxVolumeType', ], 'volumeArn' => [ 'shape' => 'KxVolumeArn', ], 'nas1Configuration' => [ 'shape' => 'KxNAS1Configuration', ], 'status' => [ 'shape' => 'KxVolumeStatus', ], 'statusReason' => [ 'shape' => 'KxVolumeStatusReason', ], 'azMode' => [ 'shape' => 'KxAzMode', ], 'description' => [ 'shape' => 'Description', ], 'availabilityZoneIds' => [ 'shape' => 'AvailabilityZoneIds', ], 'createdTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'CustomDNSConfiguration' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomDNSServer', ], ], 'CustomDNSServer' => [ 'type' => 'structure', 'required' => [ 'customDNSServerName', 'customDNSServerIP', ], 'members' => [ 'customDNSServerName' => [ 'shape' => 'ValidHostname', ], 'customDNSServerIP' => [ 'shape' => 'ValidIPAddress', ], ], ], 'DataBundleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws:finspace:[A-Za-z0-9_/.-]{0,63}:\\d*:data-bundle/[0-9A-Za-z_-]{1,128}$', ], 'DataBundleArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataBundleArn', ], ], 'DatabaseArn' => [ 'type' => 'string', ], 'DatabaseName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9-_]*[a-zA-Z0-9]$', ], 'DbPath' => [ 'type' => 'string', 'max' => 1025, 'min' => 1, 'pattern' => '^(\\*)*[\\/\\?\\*]([^\\/]+\\/){0,2}[^\\/]*$', ], 'DbPaths' => [ 'type' => 'list', 'member' => [ 'shape' => 'DbPath', ], ], 'DeleteEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'environmentId', ], ], ], 'DeleteEnvironmentResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteKxClusterNodeRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'nodeId', 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'KxEnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'clusterName' => [ 'shape' => 'KxClusterName', 'location' => 'uri', 'locationName' => 'clusterName', ], 'nodeId' => [ 'shape' => 'KxClusterNodeIdString', 'location' => 'uri', 'locationName' => 'nodeId', ], ], ], 'DeleteKxClusterNodeResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteKxClusterRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'clusterName', ], 'members' => [ 'environmentId' => [ 'shape' => 'KxEnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'clusterName' => [ 'shape' => 'KxClusterName', 'location' => 'uri', 'locationName' => 'clusterName', ], 'clientToken' => [ 'shape' => 'ClientTokenString', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeleteKxClusterResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteKxDatabaseRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'databaseName', 'clientToken', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'databaseName' => [ 'shape' => 'DatabaseName', 'location' => 'uri', 'locationName' => 'databaseName', ], 'clientToken' => [ 'shape' => 'ClientTokenString', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeleteKxDatabaseResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteKxDataviewRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'databaseName', 'dataviewName', 'clientToken', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'databaseName' => [ 'shape' => 'DatabaseName', 'location' => 'uri', 'locationName' => 'databaseName', ], 'dataviewName' => [ 'shape' => 'KxDataviewName', 'location' => 'uri', 'locationName' => 'dataviewName', ], 'clientToken' => [ 'shape' => 'ClientTokenString', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeleteKxDataviewResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteKxEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'environmentId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeleteKxEnvironmentResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteKxScalingGroupRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'scalingGroupName', ], 'members' => [ 'environmentId' => [ 'shape' => 'KxEnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'scalingGroupName' => [ 'shape' => 'KxScalingGroupName', 'location' => 'uri', 'locationName' => 'scalingGroupName', ], 'clientToken' => [ 'shape' => 'ClientTokenString', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeleteKxScalingGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteKxUserRequest' => [ 'type' => 'structure', 'required' => [ 'userName', 'environmentId', ], 'members' => [ 'userName' => [ 'shape' => 'KxUserNameString', 'location' => 'uri', 'locationName' => 'userName', ], 'environmentId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'environmentId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeleteKxUserResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteKxVolumeRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'volumeName', ], 'members' => [ 'environmentId' => [ 'shape' => 'KxEnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'volumeName' => [ 'shape' => 'KxVolumeName', 'location' => 'uri', 'locationName' => 'volumeName', ], 'clientToken' => [ 'shape' => 'ClientTokenString', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeleteKxVolumeResponse' => [ 'type' => 'structure', 'members' => [], ], 'Description' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^[a-zA-Z0-9. ]{1,1000}$', ], 'EmailId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+[.]+[A-Za-z]+', 'sensitive' => true, ], 'Environment' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'EnvironmentName', ], 'environmentId' => [ 'shape' => 'IdType', ], 'awsAccountId' => [ 'shape' => 'IdType', ], 'status' => [ 'shape' => 'EnvironmentStatus', ], 'environmentUrl' => [ 'shape' => 'url', ], 'description' => [ 'shape' => 'Description', ], 'environmentArn' => [ 'shape' => 'EnvironmentArn', ], 'sageMakerStudioDomainUrl' => [ 'shape' => 'SmsDomainUrl', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'dedicatedServiceAccountId' => [ 'shape' => 'IdType', ], 'federationMode' => [ 'shape' => 'FederationMode', ], 'federationParameters' => [ 'shape' => 'FederationParameters', ], ], ], 'EnvironmentArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws:finspace:[A-Za-z0-9_/.-]{0,63}:\\d+:environment/[0-9A-Za-z_-]{1,128}$', ], 'EnvironmentErrorMessage' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'pattern' => '^[a-zA-Z0-9. ]{1,1000}$', ], 'EnvironmentId' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '.*\\S.*', ], 'EnvironmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Environment', ], ], 'EnvironmentName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]$', ], 'EnvironmentStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_REQUESTED', 'CREATING', 'CREATED', 'DELETE_REQUESTED', 'DELETING', 'DELETED', 'FAILED_CREATION', 'RETRY_DELETION', 'FAILED_DELETION', 'UPDATE_NETWORK_REQUESTED', 'UPDATING_NETWORK', 'FAILED_UPDATING_NETWORK', 'SUSPENDED', ], ], 'ErrorDetails' => [ 'type' => 'string', 'enum' => [ 'The inputs to this request are invalid.', 'Service limits have been exceeded.', 'Missing required permission to perform this request.', 'One or more inputs to this request were not found.', 'The system temporarily lacks sufficient resources to process the request.', 'An internal error has occurred.', 'Cancelled', 'A user recoverable error has occurred', ], ], 'ErrorInfo' => [ 'type' => 'structure', 'members' => [ 'errorMessage' => [ 'shape' => 'ErrorMessage', ], 'errorType' => [ 'shape' => 'ErrorDetails', ], ], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 1000, ], 'ExecutionRoleArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^arn:aws[a-z0-9-]*:iam::\\d{12}:role\\/[\\w-\\/.@+=,]{1,1017}$', ], 'FederationAttributeKey' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '.*', ], 'FederationAttributeValue' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '.*', ], 'FederationMode' => [ 'type' => 'string', 'enum' => [ 'FEDERATED', 'LOCAL', ], ], 'FederationParameters' => [ 'type' => 'structure', 'members' => [ 'samlMetadataDocument' => [ 'shape' => 'SamlMetadataDocument', ], 'samlMetadataURL' => [ 'shape' => 'url', ], 'applicationCallBackURL' => [ 'shape' => 'url', ], 'federationURN' => [ 'shape' => 'urn', ], 'federationProviderName' => [ 'shape' => 'FederationProviderName', ], 'attributeMap' => [ 'shape' => 'AttributeMap', ], ], ], 'FederationProviderName' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '[^_\\p{Z}][\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}][^_\\p{Z}]+', ], 'FinSpaceTaggableArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws:finspace:[A-Za-z0-9_/.-]{0,63}:\\d+:(environment|kxEnvironment)/[0-9A-Za-z_-]{1,128}(/(kxCluster|kxUser|kxVolume|kxScalingGroup)/[a-zA-Z0-9_-]{1,255}|/(kxDatabase/[a-zA-Z0-9_-]{1,255}(/kxDataview/[a-zA-Z0-9_-]{1,255})?))?$', ], 'GetEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'environmentId', ], ], ], 'GetEnvironmentResponse' => [ 'type' => 'structure', 'members' => [ 'environment' => [ 'shape' => 'Environment', ], ], ], 'GetKxChangesetRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'databaseName', 'changesetId', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'databaseName' => [ 'shape' => 'DatabaseName', 'location' => 'uri', 'locationName' => 'databaseName', ], 'changesetId' => [ 'shape' => 'ChangesetId', 'location' => 'uri', 'locationName' => 'changesetId', ], ], ], 'GetKxChangesetResponse' => [ 'type' => 'structure', 'members' => [ 'changesetId' => [ 'shape' => 'ChangesetId', ], 'databaseName' => [ 'shape' => 'DatabaseName', ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'changeRequests' => [ 'shape' => 'ChangeRequests', ], 'createdTimestamp' => [ 'shape' => 'Timestamp', ], 'activeFromTimestamp' => [ 'shape' => 'Timestamp', ], 'lastModifiedTimestamp' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'ChangesetStatus', ], 'errorInfo' => [ 'shape' => 'ErrorInfo', ], ], ], 'GetKxClusterRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'clusterName', ], 'members' => [ 'environmentId' => [ 'shape' => 'KxEnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'clusterName' => [ 'shape' => 'KxClusterName', 'location' => 'uri', 'locationName' => 'clusterName', ], ], ], 'GetKxClusterResponse' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'KxClusterStatus', ], 'statusReason' => [ 'shape' => 'KxClusterStatusReason', ], 'clusterName' => [ 'shape' => 'KxClusterName', ], 'clusterType' => [ 'shape' => 'KxClusterType', ], 'tickerplantLogConfiguration' => [ 'shape' => 'TickerplantLogConfiguration', ], 'volumes' => [ 'shape' => 'Volumes', ], 'databases' => [ 'shape' => 'KxDatabaseConfigurations', ], 'cacheStorageConfigurations' => [ 'shape' => 'KxCacheStorageConfigurations', ], 'autoScalingConfiguration' => [ 'shape' => 'AutoScalingConfiguration', ], 'clusterDescription' => [ 'shape' => 'KxClusterDescription', ], 'capacityConfiguration' => [ 'shape' => 'CapacityConfiguration', ], 'releaseLabel' => [ 'shape' => 'ReleaseLabel', ], 'vpcConfiguration' => [ 'shape' => 'VpcConfiguration', ], 'initializationScript' => [ 'shape' => 'InitializationScriptFilePath', ], 'commandLineArguments' => [ 'shape' => 'KxCommandLineArguments', ], 'code' => [ 'shape' => 'CodeConfiguration', ], 'executionRole' => [ 'shape' => 'ExecutionRoleArn', ], 'lastModifiedTimestamp' => [ 'shape' => 'Timestamp', ], 'savedownStorageConfiguration' => [ 'shape' => 'KxSavedownStorageConfiguration', ], 'azMode' => [ 'shape' => 'KxAzMode', ], 'availabilityZoneId' => [ 'shape' => 'AvailabilityZoneId', ], 'createdTimestamp' => [ 'shape' => 'Timestamp', ], 'scalingGroupConfiguration' => [ 'shape' => 'KxScalingGroupConfiguration', ], ], ], 'GetKxConnectionStringRequest' => [ 'type' => 'structure', 'required' => [ 'userArn', 'environmentId', 'clusterName', ], 'members' => [ 'userArn' => [ 'shape' => 'KxUserArn', 'location' => 'querystring', 'locationName' => 'userArn', ], 'environmentId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'environmentId', ], 'clusterName' => [ 'shape' => 'KxClusterName', 'location' => 'querystring', 'locationName' => 'clusterName', ], ], ], 'GetKxConnectionStringResponse' => [ 'type' => 'structure', 'members' => [ 'signedConnectionString' => [ 'shape' => 'SignedKxConnectionString', ], ], ], 'GetKxDatabaseRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'databaseName', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'databaseName' => [ 'shape' => 'DatabaseName', 'location' => 'uri', 'locationName' => 'databaseName', ], ], ], 'GetKxDatabaseResponse' => [ 'type' => 'structure', 'members' => [ 'databaseName' => [ 'shape' => 'DatabaseName', ], 'databaseArn' => [ 'shape' => 'DatabaseArn', ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'description' => [ 'shape' => 'Description', ], 'createdTimestamp' => [ 'shape' => 'Timestamp', ], 'lastModifiedTimestamp' => [ 'shape' => 'Timestamp', ], 'lastCompletedChangesetId' => [ 'shape' => 'ChangesetId', ], 'numBytes' => [ 'shape' => 'numBytes', ], 'numChangesets' => [ 'shape' => 'numChangesets', ], 'numFiles' => [ 'shape' => 'numFiles', ], ], ], 'GetKxDataviewRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'databaseName', 'dataviewName', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'databaseName' => [ 'shape' => 'DatabaseName', 'location' => 'uri', 'locationName' => 'databaseName', ], 'dataviewName' => [ 'shape' => 'KxDataviewName', 'location' => 'uri', 'locationName' => 'dataviewName', ], ], ], 'GetKxDataviewResponse' => [ 'type' => 'structure', 'members' => [ 'databaseName' => [ 'shape' => 'DatabaseName', ], 'dataviewName' => [ 'shape' => 'KxDataviewName', ], 'azMode' => [ 'shape' => 'KxAzMode', ], 'availabilityZoneId' => [ 'shape' => 'AvailabilityZoneId', ], 'changesetId' => [ 'shape' => 'ChangesetId', ], 'segmentConfigurations' => [ 'shape' => 'KxDataviewSegmentConfigurationList', ], 'activeVersions' => [ 'shape' => 'KxDataviewActiveVersionList', ], 'description' => [ 'shape' => 'Description', ], 'autoUpdate' => [ 'shape' => 'booleanValue', ], 'readWrite' => [ 'shape' => 'booleanValue', ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'createdTimestamp' => [ 'shape' => 'Timestamp', ], 'lastModifiedTimestamp' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'KxDataviewStatus', ], 'statusReason' => [ 'shape' => 'KxDataviewStatusReason', ], ], ], 'GetKxEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'environmentId', ], ], ], 'GetKxEnvironmentResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'KxEnvironmentName', ], 'environmentId' => [ 'shape' => 'IdType', ], 'awsAccountId' => [ 'shape' => 'IdType', ], 'status' => [ 'shape' => 'EnvironmentStatus', ], 'tgwStatus' => [ 'shape' => 'tgwStatus', ], 'dnsStatus' => [ 'shape' => 'dnsStatus', ], 'errorMessage' => [ 'shape' => 'EnvironmentErrorMessage', ], 'description' => [ 'shape' => 'Description', ], 'environmentArn' => [ 'shape' => 'EnvironmentArn', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'dedicatedServiceAccountId' => [ 'shape' => 'IdType', ], 'transitGatewayConfiguration' => [ 'shape' => 'TransitGatewayConfiguration', ], 'customDNSConfiguration' => [ 'shape' => 'CustomDNSConfiguration', ], 'creationTimestamp' => [ 'shape' => 'Timestamp', ], 'updateTimestamp' => [ 'shape' => 'Timestamp', ], 'availabilityZoneIds' => [ 'shape' => 'AvailabilityZoneIds', ], 'certificateAuthorityArn' => [ 'shape' => 'stringValueLength1to255', ], ], ], 'GetKxScalingGroupRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'scalingGroupName', ], 'members' => [ 'environmentId' => [ 'shape' => 'KxEnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'scalingGroupName' => [ 'shape' => 'KxScalingGroupName', 'location' => 'uri', 'locationName' => 'scalingGroupName', ], ], ], 'GetKxScalingGroupResponse' => [ 'type' => 'structure', 'members' => [ 'scalingGroupName' => [ 'shape' => 'KxScalingGroupName', ], 'scalingGroupArn' => [ 'shape' => 'arn', ], 'hostType' => [ 'shape' => 'KxHostType', ], 'clusters' => [ 'shape' => 'KxClusterNameList', ], 'availabilityZoneId' => [ 'shape' => 'AvailabilityZoneId', ], 'status' => [ 'shape' => 'KxScalingGroupStatus', ], 'statusReason' => [ 'shape' => 'KxClusterStatusReason', ], 'lastModifiedTimestamp' => [ 'shape' => 'Timestamp', ], 'createdTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'GetKxUserRequest' => [ 'type' => 'structure', 'required' => [ 'userName', 'environmentId', ], 'members' => [ 'userName' => [ 'shape' => 'KxUserNameString', 'location' => 'uri', 'locationName' => 'userName', ], 'environmentId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'environmentId', ], ], ], 'GetKxUserResponse' => [ 'type' => 'structure', 'members' => [ 'userName' => [ 'shape' => 'IdType', ], 'userArn' => [ 'shape' => 'KxUserArn', ], 'environmentId' => [ 'shape' => 'IdType', ], 'iamRole' => [ 'shape' => 'RoleArn', ], ], ], 'GetKxVolumeRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'volumeName', ], 'members' => [ 'environmentId' => [ 'shape' => 'KxEnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'volumeName' => [ 'shape' => 'KxVolumeName', 'location' => 'uri', 'locationName' => 'volumeName', ], ], ], 'GetKxVolumeResponse' => [ 'type' => 'structure', 'members' => [ 'environmentId' => [ 'shape' => 'KxEnvironmentId', ], 'volumeName' => [ 'shape' => 'KxVolumeName', ], 'volumeType' => [ 'shape' => 'KxVolumeType', ], 'volumeArn' => [ 'shape' => 'KxVolumeArn', ], 'nas1Configuration' => [ 'shape' => 'KxNAS1Configuration', ], 'status' => [ 'shape' => 'KxVolumeStatus', ], 'statusReason' => [ 'shape' => 'KxVolumeStatusReason', ], 'createdTimestamp' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'azMode' => [ 'shape' => 'KxAzMode', ], 'availabilityZoneIds' => [ 'shape' => 'AvailabilityZoneIds', ], 'lastModifiedTimestamp' => [ 'shape' => 'Timestamp', ], 'attachedClusters' => [ 'shape' => 'KxAttachedClusters', ], ], ], 'IPAddressType' => [ 'type' => 'string', 'enum' => [ 'IP_V4', ], ], 'IcmpTypeCode' => [ 'type' => 'structure', 'required' => [ 'type', 'code', ], 'members' => [ 'type' => [ 'shape' => 'IcmpTypeOrCode', ], 'code' => [ 'shape' => 'IcmpTypeOrCode', ], ], ], 'IcmpTypeOrCode' => [ 'type' => 'integer', ], 'IdType' => [ 'type' => 'string', 'max' => 26, 'min' => 1, 'pattern' => '^[a-zA-Z0-9]{1,26}$', ], 'InitializationScriptFilePath' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\_\\-\\.\\/\\\\]+$', ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'KmsKeyARN' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^arn:aws:kms:.*:\\d+:.*$', ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^[a-zA-Z-0-9-:\\/]*$', ], 'KxAttachedCluster' => [ 'type' => 'structure', 'members' => [ 'clusterName' => [ 'shape' => 'KxClusterName', ], 'clusterType' => [ 'shape' => 'KxClusterType', ], 'clusterStatus' => [ 'shape' => 'KxClusterStatus', ], ], ], 'KxAttachedClusters' => [ 'type' => 'list', 'member' => [ 'shape' => 'KxAttachedCluster', ], ], 'KxAzMode' => [ 'type' => 'string', 'enum' => [ 'SINGLE', 'MULTI', ], ], 'KxCacheStorageConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', 'size', ], 'members' => [ 'type' => [ 'shape' => 'KxCacheStorageType', ], 'size' => [ 'shape' => 'KxCacheStorageSize', ], ], ], 'KxCacheStorageConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'KxCacheStorageConfiguration', ], ], 'KxCacheStorageSize' => [ 'type' => 'integer', ], 'KxCacheStorageType' => [ 'type' => 'string', 'max' => 10, 'min' => 8, ], 'KxChangesetListEntry' => [ 'type' => 'structure', 'members' => [ 'changesetId' => [ 'shape' => 'ChangesetId', ], 'createdTimestamp' => [ 'shape' => 'Timestamp', ], 'activeFromTimestamp' => [ 'shape' => 'Timestamp', ], 'lastModifiedTimestamp' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'ChangesetStatus', ], ], ], 'KxChangesets' => [ 'type' => 'list', 'member' => [ 'shape' => 'KxChangesetListEntry', ], ], 'KxCluster' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'KxClusterStatus', ], 'statusReason' => [ 'shape' => 'KxClusterStatusReason', ], 'clusterName' => [ 'shape' => 'KxClusterName', ], 'clusterType' => [ 'shape' => 'KxClusterType', ], 'clusterDescription' => [ 'shape' => 'KxClusterDescription', ], 'releaseLabel' => [ 'shape' => 'ReleaseLabel', ], 'volumes' => [ 'shape' => 'Volumes', ], 'initializationScript' => [ 'shape' => 'InitializationScriptFilePath', ], 'executionRole' => [ 'shape' => 'ExecutionRoleArn', ], 'azMode' => [ 'shape' => 'KxAzMode', ], 'availabilityZoneId' => [ 'shape' => 'AvailabilityZoneId', ], 'lastModifiedTimestamp' => [ 'shape' => 'Timestamp', ], 'createdTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'KxClusterCodeDeploymentConfiguration' => [ 'type' => 'structure', 'required' => [ 'deploymentStrategy', ], 'members' => [ 'deploymentStrategy' => [ 'shape' => 'KxClusterCodeDeploymentStrategy', ], ], ], 'KxClusterCodeDeploymentStrategy' => [ 'type' => 'string', 'enum' => [ 'NO_RESTART', 'ROLLING', 'FORCE', ], ], 'KxClusterDescription' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\_\\-\\.\\s]+$', ], 'KxClusterName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9-_]*[a-zA-Z0-9]$', ], 'KxClusterNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KxClusterName', ], ], 'KxClusterNodeIdString' => [ 'type' => 'string', 'max' => 40, 'min' => 1, ], 'KxClusterStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'CREATING', 'CREATE_FAILED', 'RUNNING', 'UPDATING', 'DELETING', 'DELETED', 'DELETE_FAILED', ], ], 'KxClusterStatusReason' => [ 'type' => 'string', 'max' => 250, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\_\\-\\.\\s]+$', ], 'KxClusterType' => [ 'type' => 'string', 'enum' => [ 'HDB', 'RDB', 'GATEWAY', 'GP', 'TICKERPLANT', ], ], 'KxClusters' => [ 'type' => 'list', 'member' => [ 'shape' => 'KxCluster', ], ], 'KxCommandLineArgument' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'KxCommandLineArgumentKey', ], 'value' => [ 'shape' => 'KxCommandLineArgumentValue', ], ], ], 'KxCommandLineArgumentKey' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^(?![Aa][Ww][Ss])(s|([a-zA-Z][a-zA-Z0-9_]+))|(AWS_ZIP_DEFAULT)', ], 'KxCommandLineArgumentValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_:./,; ]+$', ], 'KxCommandLineArguments' => [ 'type' => 'list', 'member' => [ 'shape' => 'KxCommandLineArgument', ], ], 'KxDatabaseCacheConfiguration' => [ 'type' => 'structure', 'required' => [ 'cacheType', 'dbPaths', ], 'members' => [ 'cacheType' => [ 'shape' => 'KxCacheStorageType', ], 'dbPaths' => [ 'shape' => 'DbPaths', ], 'dataviewName' => [ 'shape' => 'KxDataviewName', ], ], ], 'KxDatabaseCacheConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'KxDatabaseCacheConfiguration', ], ], 'KxDatabaseConfiguration' => [ 'type' => 'structure', 'required' => [ 'databaseName', ], 'members' => [ 'databaseName' => [ 'shape' => 'DatabaseName', ], 'cacheConfigurations' => [ 'shape' => 'KxDatabaseCacheConfigurations', ], 'changesetId' => [ 'shape' => 'ChangesetId', ], 'dataviewName' => [ 'shape' => 'KxDataviewName', ], 'dataviewConfiguration' => [ 'shape' => 'KxDataviewConfiguration', ], ], ], 'KxDatabaseConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'KxDatabaseConfiguration', ], ], 'KxDatabaseListEntry' => [ 'type' => 'structure', 'members' => [ 'databaseName' => [ 'shape' => 'DatabaseName', ], 'createdTimestamp' => [ 'shape' => 'Timestamp', ], 'lastModifiedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'KxDatabases' => [ 'type' => 'list', 'member' => [ 'shape' => 'KxDatabaseListEntry', ], ], 'KxDataviewActiveVersion' => [ 'type' => 'structure', 'members' => [ 'changesetId' => [ 'shape' => 'ChangesetId', ], 'segmentConfigurations' => [ 'shape' => 'KxDataviewSegmentConfigurationList', ], 'attachedClusters' => [ 'shape' => 'AttachedClusterList', ], 'createdTimestamp' => [ 'shape' => 'Timestamp', ], 'versionId' => [ 'shape' => 'VersionId', ], ], ], 'KxDataviewActiveVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KxDataviewActiveVersion', ], ], 'KxDataviewConfiguration' => [ 'type' => 'structure', 'members' => [ 'dataviewName' => [ 'shape' => 'KxDataviewName', ], 'dataviewVersionId' => [ 'shape' => 'VersionId', ], 'changesetId' => [ 'shape' => 'ChangesetId', ], 'segmentConfigurations' => [ 'shape' => 'KxDataviewSegmentConfigurationList', ], ], ], 'KxDataviewListEntry' => [ 'type' => 'structure', 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'databaseName' => [ 'shape' => 'DatabaseName', ], 'dataviewName' => [ 'shape' => 'KxDataviewName', ], 'azMode' => [ 'shape' => 'KxAzMode', ], 'availabilityZoneId' => [ 'shape' => 'AvailabilityZoneId', ], 'changesetId' => [ 'shape' => 'ChangesetId', ], 'segmentConfigurations' => [ 'shape' => 'KxDataviewSegmentConfigurationList', ], 'activeVersions' => [ 'shape' => 'KxDataviewActiveVersionList', ], 'status' => [ 'shape' => 'KxDataviewStatus', ], 'description' => [ 'shape' => 'Description', ], 'autoUpdate' => [ 'shape' => 'booleanValue', ], 'readWrite' => [ 'shape' => 'booleanValue', ], 'createdTimestamp' => [ 'shape' => 'Timestamp', ], 'lastModifiedTimestamp' => [ 'shape' => 'Timestamp', ], 'statusReason' => [ 'shape' => 'KxDataviewStatusReason', ], ], ], 'KxDataviewName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9-_]*[a-zA-Z0-9]$', ], 'KxDataviewSegmentConfiguration' => [ 'type' => 'structure', 'required' => [ 'dbPaths', 'volumeName', ], 'members' => [ 'dbPaths' => [ 'shape' => 'SegmentConfigurationDbPathList', ], 'volumeName' => [ 'shape' => 'KxVolumeName', ], 'onDemand' => [ 'shape' => 'booleanValue', ], ], ], 'KxDataviewSegmentConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KxDataviewSegmentConfiguration', ], 'max' => 50, 'min' => 0, ], 'KxDataviewStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'UPDATING', 'FAILED', 'DELETING', ], ], 'KxDataviewStatusReason' => [ 'type' => 'string', 'max' => 250, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\_\\-\\.\\s]+$', ], 'KxDataviews' => [ 'type' => 'list', 'member' => [ 'shape' => 'KxDataviewListEntry', ], ], 'KxDeploymentConfiguration' => [ 'type' => 'structure', 'required' => [ 'deploymentStrategy', ], 'members' => [ 'deploymentStrategy' => [ 'shape' => 'KxDeploymentStrategy', ], ], ], 'KxDeploymentStrategy' => [ 'type' => 'string', 'enum' => [ 'NO_RESTART', 'ROLLING', ], ], 'KxEnvironment' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'KxEnvironmentName', ], 'environmentId' => [ 'shape' => 'IdType', ], 'awsAccountId' => [ 'shape' => 'IdType', ], 'status' => [ 'shape' => 'EnvironmentStatus', ], 'tgwStatus' => [ 'shape' => 'tgwStatus', ], 'dnsStatus' => [ 'shape' => 'dnsStatus', ], 'errorMessage' => [ 'shape' => 'EnvironmentErrorMessage', ], 'description' => [ 'shape' => 'Description', ], 'environmentArn' => [ 'shape' => 'EnvironmentArn', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'dedicatedServiceAccountId' => [ 'shape' => 'IdType', ], 'transitGatewayConfiguration' => [ 'shape' => 'TransitGatewayConfiguration', ], 'customDNSConfiguration' => [ 'shape' => 'CustomDNSConfiguration', ], 'creationTimestamp' => [ 'shape' => 'Timestamp', ], 'updateTimestamp' => [ 'shape' => 'Timestamp', ], 'availabilityZoneIds' => [ 'shape' => 'AvailabilityZoneIds', ], 'certificateAuthorityArn' => [ 'shape' => 'stringValueLength1to255', ], ], ], 'KxEnvironmentId' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^[a-z0-9]+$', ], 'KxEnvironmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KxEnvironment', ], ], 'KxEnvironmentName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9-_]*[a-zA-Z0-9]$', ], 'KxHostType' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^[a-zA-Z0-9._]+', ], 'KxNAS1Configuration' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'KxNAS1Type', ], 'size' => [ 'shape' => 'KxNAS1Size', ], ], ], 'KxNAS1Size' => [ 'type' => 'integer', 'min' => 1200, ], 'KxNAS1Type' => [ 'type' => 'string', 'enum' => [ 'SSD_1000', 'SSD_250', 'HDD_12', ], ], 'KxNode' => [ 'type' => 'structure', 'members' => [ 'nodeId' => [ 'shape' => 'KxClusterNodeIdString', ], 'availabilityZoneId' => [ 'shape' => 'AvailabilityZoneId', ], 'launchTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'KxNodeStatus', ], ], ], 'KxNodeStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'PROVISIONING', ], ], 'KxNodeSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'KxNode', ], ], 'KxSavedownStorageConfiguration' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'KxSavedownStorageType', ], 'size' => [ 'shape' => 'KxSavedownStorageSize', ], 'volumeName' => [ 'shape' => 'KxVolumeName', ], ], ], 'KxSavedownStorageSize' => [ 'type' => 'integer', 'max' => 16000, 'min' => 10, ], 'KxSavedownStorageType' => [ 'type' => 'string', 'enum' => [ 'SDS01', ], ], 'KxScalingGroup' => [ 'type' => 'structure', 'members' => [ 'scalingGroupName' => [ 'shape' => 'KxScalingGroupName', ], 'hostType' => [ 'shape' => 'KxHostType', ], 'clusters' => [ 'shape' => 'KxClusterNameList', ], 'availabilityZoneId' => [ 'shape' => 'AvailabilityZoneId', ], 'status' => [ 'shape' => 'KxScalingGroupStatus', ], 'statusReason' => [ 'shape' => 'KxClusterStatusReason', ], 'lastModifiedTimestamp' => [ 'shape' => 'Timestamp', ], 'createdTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'KxScalingGroupConfiguration' => [ 'type' => 'structure', 'required' => [ 'scalingGroupName', 'memoryReservation', 'nodeCount', ], 'members' => [ 'scalingGroupName' => [ 'shape' => 'KxScalingGroupName', ], 'memoryLimit' => [ 'shape' => 'MemoryMib', ], 'memoryReservation' => [ 'shape' => 'MemoryMib', ], 'nodeCount' => [ 'shape' => 'ClusterNodeCount', ], 'cpu' => [ 'shape' => 'CpuCount', ], ], ], 'KxScalingGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KxScalingGroup', ], ], 'KxScalingGroupName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9-_]*[a-zA-Z0-9]$', ], 'KxScalingGroupStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATE_FAILED', 'ACTIVE', 'DELETING', 'DELETED', 'DELETE_FAILED', ], ], 'KxUser' => [ 'type' => 'structure', 'members' => [ 'userArn' => [ 'shape' => 'KxUserArn', ], 'userName' => [ 'shape' => 'KxUserNameString', ], 'iamRole' => [ 'shape' => 'RoleArn', ], 'createTimestamp' => [ 'shape' => 'Timestamp', ], 'updateTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'KxUserArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws:finspace:[A-Za-z0-9_/.-]{0,63}:\\d+:kxEnvironment/[0-9A-Za-z_-]{1,128}/kxUser/[0-9A-Za-z_-]{1,128}$', ], 'KxUserList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KxUser', ], ], 'KxUserNameString' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '^[0-9A-Za-z_-]{1,50}$', ], 'KxVolume' => [ 'type' => 'structure', 'members' => [ 'volumeName' => [ 'shape' => 'KxVolumeName', ], 'volumeType' => [ 'shape' => 'KxVolumeType', ], 'status' => [ 'shape' => 'KxVolumeStatus', ], 'description' => [ 'shape' => 'Description', ], 'statusReason' => [ 'shape' => 'KxVolumeStatusReason', ], 'azMode' => [ 'shape' => 'KxAzMode', ], 'availabilityZoneIds' => [ 'shape' => 'AvailabilityZoneIds', ], 'createdTimestamp' => [ 'shape' => 'Timestamp', ], 'lastModifiedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'KxVolumeArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws:finspace:[A-Za-z0-9_/.-]{0,63}:\\d+:kxEnvironment/[0-9A-Za-z_-]{1,128}(/kxSharedVolume/[a-zA-Z0-9_-]{1,255})?$', ], 'KxVolumeName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9-_]*[a-zA-Z0-9]$', ], 'KxVolumeStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATE_FAILED', 'ACTIVE', 'UPDATING', 'UPDATED', 'UPDATE_FAILED', 'DELETING', 'DELETED', 'DELETE_FAILED', ], ], 'KxVolumeStatusReason' => [ 'type' => 'string', 'max' => 250, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\_\\-\\.\\s]+$', ], 'KxVolumeType' => [ 'type' => 'string', 'enum' => [ 'NAS_1', ], ], 'KxVolumes' => [ 'type' => 'list', 'member' => [ 'shape' => 'KxVolume', ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ListEnvironmentsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ResultLimit', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListEnvironmentsResponse' => [ 'type' => 'structure', 'members' => [ 'environments' => [ 'shape' => 'EnvironmentList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListKxChangesetsRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'databaseName', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'databaseName' => [ 'shape' => 'DatabaseName', 'location' => 'uri', 'locationName' => 'databaseName', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListKxChangesetsResponse' => [ 'type' => 'structure', 'members' => [ 'kxChangesets' => [ 'shape' => 'KxChangesets', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListKxClusterNodesRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'KxEnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'clusterName' => [ 'shape' => 'KxClusterName', 'location' => 'uri', 'locationName' => 'clusterName', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ResultLimit', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListKxClusterNodesResponse' => [ 'type' => 'structure', 'members' => [ 'nodes' => [ 'shape' => 'KxNodeSummaries', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListKxClustersRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'KxEnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'clusterType' => [ 'shape' => 'KxClusterType', 'location' => 'querystring', 'locationName' => 'clusterType', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListKxClustersResponse' => [ 'type' => 'structure', 'members' => [ 'kxClusterSummaries' => [ 'shape' => 'KxClusters', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListKxDatabasesRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListKxDatabasesResponse' => [ 'type' => 'structure', 'members' => [ 'kxDatabases' => [ 'shape' => 'KxDatabases', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListKxDataviewsRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'databaseName', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'databaseName' => [ 'shape' => 'DatabaseName', 'location' => 'uri', 'locationName' => 'databaseName', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListKxDataviewsResponse' => [ 'type' => 'structure', 'members' => [ 'kxDataviews' => [ 'shape' => 'KxDataviews', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListKxEnvironmentsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'BoxedInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListKxEnvironmentsResponse' => [ 'type' => 'structure', 'members' => [ 'environments' => [ 'shape' => 'KxEnvironmentList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListKxScalingGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'KxEnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListKxScalingGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'scalingGroups' => [ 'shape' => 'KxScalingGroupList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListKxUsersRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'environmentId', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ResultLimit', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListKxUsersResponse' => [ 'type' => 'structure', 'members' => [ 'users' => [ 'shape' => 'KxUserList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListKxVolumesRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'KxEnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'volumeType' => [ 'shape' => 'KxVolumeType', 'location' => 'querystring', 'locationName' => 'volumeType', ], ], ], 'ListKxVolumesResponse' => [ 'type' => 'structure', 'members' => [ 'kxVolumeSummaries' => [ 'shape' => 'KxVolumes', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'FinSpaceTaggableArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'MemoryMib' => [ 'type' => 'integer', 'min' => 6, ], 'NameString' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '^[a-zA-Z0-9]{1,50}$', ], 'NetworkACLConfiguration' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkACLEntry', ], 'max' => 100, 'min' => 1, ], 'NetworkACLEntry' => [ 'type' => 'structure', 'required' => [ 'ruleNumber', 'protocol', 'ruleAction', 'cidrBlock', ], 'members' => [ 'ruleNumber' => [ 'shape' => 'RuleNumber', ], 'protocol' => [ 'shape' => 'Protocol', ], 'ruleAction' => [ 'shape' => 'RuleAction', ], 'portRange' => [ 'shape' => 'PortRange', ], 'icmpTypeCode' => [ 'shape' => 'IcmpTypeCode', ], 'cidrBlock' => [ 'shape' => 'ValidCIDRBlock', ], ], ], 'NodeCount' => [ 'type' => 'integer', 'min' => 1, ], 'NodeType' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^[a-zA-Z0-9._]+$', ], 'PaginationToken' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '.*', ], 'Port' => [ 'type' => 'integer', 'max' => 65535, 'min' => 0, ], 'PortRange' => [ 'type' => 'structure', 'required' => [ 'from', 'to', ], 'members' => [ 'from' => [ 'shape' => 'Port', ], 'to' => [ 'shape' => 'Port', ], ], ], 'Protocol' => [ 'type' => 'string', 'max' => 5, 'min' => 1, 'pattern' => '^-1|[0-9]+$', ], 'ReleaseLabel' => [ 'type' => 'string', 'max' => 16, 'min' => 1, 'pattern' => '^[a-zA-Z0-9._-]+$', ], 'ResourceAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ResultLimit' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'RoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws[a-z\\-]*:iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+$', ], 'RuleAction' => [ 'type' => 'string', 'enum' => [ 'allow', 'deny', ], ], 'RuleNumber' => [ 'type' => 'integer', 'max' => 32766, 'min' => 1, ], 'S3Bucket' => [ 'type' => 'string', 'max' => 255, 'min' => 3, 'pattern' => '^[a-z0-9][a-z0-9\\.\\-]*[a-z0-9]$', ], 'S3Key' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\/\\!\\-_\\.\\*\'\\(\\)]+$', ], 'S3ObjectVersion' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'S3Path' => [ 'type' => 'string', 'max' => 1093, 'min' => 9, 'pattern' => '^s3:\\/\\/[a-z0-9][a-z0-9-.]{1,61}[a-z0-9]\\/([^\\/]+\\/)*[^\\/]*$', ], 'SamlMetadataDocument' => [ 'type' => 'string', 'max' => 10000000, 'min' => 1000, 'pattern' => '.*', ], 'SecurityGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupIdString', ], ], 'SecurityGroupIdString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^sg-([a-z0-9]{8}$|[a-z0-9]{17}$)', ], 'SegmentConfigurationDbPathList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DbPath', ], 'max' => 30, 'min' => 1, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 402, ], 'exception' => true, ], 'SignedKxConnectionString' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(:|:tcps:\\/\\/)[a-zA-Z0-9-\\.\\_]+:\\d+:[a-zA-Z0-9-\\.\\_]+:\\S+$', 'sensitive' => true, ], 'SmsDomainUrl' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^[a-zA-Z-0-9-:\\/.]*$', ], 'SubnetIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetIdString', ], ], 'SubnetIdString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^subnet-([a-z0-9]{8}$|[a-z0-9]{17}$)', ], 'SuperuserParameters' => [ 'type' => 'structure', 'required' => [ 'emailAddress', 'firstName', 'lastName', ], 'members' => [ 'emailAddress' => [ 'shape' => 'EmailId', ], 'firstName' => [ 'shape' => 'NameString', ], 'lastName' => [ 'shape' => 'NameString', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[a-zA-Z+-=._:/]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'FinSpaceTaggableArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[a-zA-Z0-9+-=._:@ ]+$', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'TickerplantLogConfiguration' => [ 'type' => 'structure', 'members' => [ 'tickerplantLogVolumes' => [ 'shape' => 'TickerplantLogVolumes', ], ], ], 'TickerplantLogVolumes' => [ 'type' => 'list', 'member' => [ 'shape' => 'VolumeName', ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TransitGatewayConfiguration' => [ 'type' => 'structure', 'required' => [ 'transitGatewayID', 'routableCIDRSpace', ], 'members' => [ 'transitGatewayID' => [ 'shape' => 'TransitGatewayID', ], 'routableCIDRSpace' => [ 'shape' => 'ValidCIDRSpace', ], 'attachmentNetworkAclConfiguration' => [ 'shape' => 'NetworkACLConfiguration', ], ], ], 'TransitGatewayID' => [ 'type' => 'string', 'max' => 32, 'min' => 1, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'FinSpaceTaggableArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'environmentId', ], 'name' => [ 'shape' => 'EnvironmentName', ], 'description' => [ 'shape' => 'Description', ], 'federationMode' => [ 'shape' => 'FederationMode', ], 'federationParameters' => [ 'shape' => 'FederationParameters', ], ], ], 'UpdateEnvironmentResponse' => [ 'type' => 'structure', 'members' => [ 'environment' => [ 'shape' => 'Environment', ], ], ], 'UpdateKxClusterCodeConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'clusterName', 'code', ], 'members' => [ 'environmentId' => [ 'shape' => 'KxEnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'clusterName' => [ 'shape' => 'KxClusterName', 'location' => 'uri', 'locationName' => 'clusterName', ], 'clientToken' => [ 'shape' => 'ClientTokenString', 'idempotencyToken' => true, ], 'code' => [ 'shape' => 'CodeConfiguration', ], 'initializationScript' => [ 'shape' => 'InitializationScriptFilePath', ], 'commandLineArguments' => [ 'shape' => 'KxCommandLineArguments', ], 'deploymentConfiguration' => [ 'shape' => 'KxClusterCodeDeploymentConfiguration', ], ], ], 'UpdateKxClusterCodeConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateKxClusterDatabasesRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'clusterName', 'databases', ], 'members' => [ 'environmentId' => [ 'shape' => 'KxEnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'clusterName' => [ 'shape' => 'KxClusterName', 'location' => 'uri', 'locationName' => 'clusterName', ], 'clientToken' => [ 'shape' => 'ClientTokenString', 'idempotencyToken' => true, ], 'databases' => [ 'shape' => 'KxDatabaseConfigurations', ], 'deploymentConfiguration' => [ 'shape' => 'KxDeploymentConfiguration', ], ], ], 'UpdateKxClusterDatabasesResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateKxDatabaseRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'databaseName', 'clientToken', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'databaseName' => [ 'shape' => 'DatabaseName', 'location' => 'uri', 'locationName' => 'databaseName', ], 'description' => [ 'shape' => 'Description', ], 'clientToken' => [ 'shape' => 'ClientTokenString', 'idempotencyToken' => true, ], ], ], 'UpdateKxDatabaseResponse' => [ 'type' => 'structure', 'members' => [ 'databaseName' => [ 'shape' => 'DatabaseName', ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'description' => [ 'shape' => 'Description', ], 'lastModifiedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateKxDataviewRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'databaseName', 'dataviewName', 'clientToken', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'databaseName' => [ 'shape' => 'DatabaseName', 'location' => 'uri', 'locationName' => 'databaseName', ], 'dataviewName' => [ 'shape' => 'KxDataviewName', 'location' => 'uri', 'locationName' => 'dataviewName', ], 'description' => [ 'shape' => 'Description', ], 'changesetId' => [ 'shape' => 'ChangesetId', ], 'segmentConfigurations' => [ 'shape' => 'KxDataviewSegmentConfigurationList', ], 'clientToken' => [ 'shape' => 'ClientTokenString', 'idempotencyToken' => true, ], ], ], 'UpdateKxDataviewResponse' => [ 'type' => 'structure', 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'databaseName' => [ 'shape' => 'DatabaseName', ], 'dataviewName' => [ 'shape' => 'KxDataviewName', ], 'azMode' => [ 'shape' => 'KxAzMode', ], 'availabilityZoneId' => [ 'shape' => 'AvailabilityZoneId', ], 'changesetId' => [ 'shape' => 'ChangesetId', ], 'segmentConfigurations' => [ 'shape' => 'KxDataviewSegmentConfigurationList', ], 'activeVersions' => [ 'shape' => 'KxDataviewActiveVersionList', ], 'status' => [ 'shape' => 'KxDataviewStatus', ], 'autoUpdate' => [ 'shape' => 'booleanValue', ], 'readWrite' => [ 'shape' => 'booleanValue', ], 'description' => [ 'shape' => 'Description', ], 'createdTimestamp' => [ 'shape' => 'Timestamp', ], 'lastModifiedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateKxEnvironmentNetworkRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'environmentId', ], 'transitGatewayConfiguration' => [ 'shape' => 'TransitGatewayConfiguration', ], 'customDNSConfiguration' => [ 'shape' => 'CustomDNSConfiguration', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateKxEnvironmentNetworkResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'KxEnvironmentName', ], 'environmentId' => [ 'shape' => 'IdType', ], 'awsAccountId' => [ 'shape' => 'IdType', ], 'status' => [ 'shape' => 'EnvironmentStatus', ], 'tgwStatus' => [ 'shape' => 'tgwStatus', ], 'dnsStatus' => [ 'shape' => 'dnsStatus', ], 'errorMessage' => [ 'shape' => 'EnvironmentErrorMessage', ], 'description' => [ 'shape' => 'Description', ], 'environmentArn' => [ 'shape' => 'EnvironmentArn', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'dedicatedServiceAccountId' => [ 'shape' => 'IdType', ], 'transitGatewayConfiguration' => [ 'shape' => 'TransitGatewayConfiguration', ], 'customDNSConfiguration' => [ 'shape' => 'CustomDNSConfiguration', ], 'creationTimestamp' => [ 'shape' => 'Timestamp', ], 'updateTimestamp' => [ 'shape' => 'Timestamp', ], 'availabilityZoneIds' => [ 'shape' => 'AvailabilityZoneIds', ], ], ], 'UpdateKxEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'environmentId', ], 'name' => [ 'shape' => 'KxEnvironmentName', ], 'description' => [ 'shape' => 'Description', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateKxEnvironmentResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'KxEnvironmentName', ], 'environmentId' => [ 'shape' => 'IdType', ], 'awsAccountId' => [ 'shape' => 'IdType', ], 'status' => [ 'shape' => 'EnvironmentStatus', ], 'tgwStatus' => [ 'shape' => 'tgwStatus', ], 'dnsStatus' => [ 'shape' => 'dnsStatus', ], 'errorMessage' => [ 'shape' => 'EnvironmentErrorMessage', ], 'description' => [ 'shape' => 'Description', ], 'environmentArn' => [ 'shape' => 'EnvironmentArn', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'dedicatedServiceAccountId' => [ 'shape' => 'IdType', ], 'transitGatewayConfiguration' => [ 'shape' => 'TransitGatewayConfiguration', ], 'customDNSConfiguration' => [ 'shape' => 'CustomDNSConfiguration', ], 'creationTimestamp' => [ 'shape' => 'Timestamp', ], 'updateTimestamp' => [ 'shape' => 'Timestamp', ], 'availabilityZoneIds' => [ 'shape' => 'AvailabilityZoneIds', ], ], ], 'UpdateKxUserRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'userName', 'iamRole', ], 'members' => [ 'environmentId' => [ 'shape' => 'IdType', 'location' => 'uri', 'locationName' => 'environmentId', ], 'userName' => [ 'shape' => 'KxUserNameString', 'location' => 'uri', 'locationName' => 'userName', ], 'iamRole' => [ 'shape' => 'RoleArn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateKxUserResponse' => [ 'type' => 'structure', 'members' => [ 'userName' => [ 'shape' => 'KxUserNameString', ], 'userArn' => [ 'shape' => 'KxUserArn', ], 'environmentId' => [ 'shape' => 'IdType', ], 'iamRole' => [ 'shape' => 'RoleArn', ], ], ], 'UpdateKxVolumeRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'volumeName', ], 'members' => [ 'environmentId' => [ 'shape' => 'KxEnvironmentId', 'location' => 'uri', 'locationName' => 'environmentId', ], 'volumeName' => [ 'shape' => 'KxVolumeName', 'location' => 'uri', 'locationName' => 'volumeName', ], 'description' => [ 'shape' => 'Description', ], 'clientToken' => [ 'shape' => 'ClientTokenString', 'idempotencyToken' => true, ], 'nas1Configuration' => [ 'shape' => 'KxNAS1Configuration', ], ], ], 'UpdateKxVolumeResponse' => [ 'type' => 'structure', 'members' => [ 'environmentId' => [ 'shape' => 'KxEnvironmentId', ], 'volumeName' => [ 'shape' => 'KxVolumeName', ], 'volumeType' => [ 'shape' => 'KxVolumeType', ], 'volumeArn' => [ 'shape' => 'KxVolumeArn', ], 'nas1Configuration' => [ 'shape' => 'KxNAS1Configuration', ], 'status' => [ 'shape' => 'KxVolumeStatus', ], 'description' => [ 'shape' => 'Description', ], 'statusReason' => [ 'shape' => 'KxVolumeStatusReason', ], 'createdTimestamp' => [ 'shape' => 'Timestamp', ], 'azMode' => [ 'shape' => 'KxAzMode', ], 'availabilityZoneIds' => [ 'shape' => 'AvailabilityZoneIds', ], 'lastModifiedTimestamp' => [ 'shape' => 'Timestamp', ], 'attachedClusters' => [ 'shape' => 'KxAttachedClusters', ], ], ], 'ValidCIDRBlock' => [ 'type' => 'string', 'max' => 18, 'min' => 1, 'pattern' => '^(?:\\d{1,3}\\.){3}\\d{1,3}(?:\\/(?:3[0-2]|[12]\\d|\\d))$', ], 'ValidCIDRSpace' => [ 'type' => 'string', ], 'ValidHostname' => [ 'type' => 'string', 'max' => 255, 'min' => 3, 'pattern' => '^([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])(\\.([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9]))*$', ], 'ValidIPAddress' => [ 'type' => 'string', 'pattern' => '^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$', ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'VersionId' => [ 'type' => 'string', 'max' => 26, 'min' => 1, ], 'Volume' => [ 'type' => 'structure', 'members' => [ 'volumeName' => [ 'shape' => 'VolumeName', ], 'volumeType' => [ 'shape' => 'VolumeType', ], ], ], 'VolumeName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[a-zA-Z0-9][a-zA-Z0-9-_]*[a-zA-Z0-9]$', ], 'VolumeType' => [ 'type' => 'string', 'enum' => [ 'NAS_1', ], ], 'Volumes' => [ 'type' => 'list', 'member' => [ 'shape' => 'Volume', ], 'max' => 5, 'min' => 0, ], 'VpcConfiguration' => [ 'type' => 'structure', 'members' => [ 'vpcId' => [ 'shape' => 'VpcIdString', ], 'securityGroupIds' => [ 'shape' => 'SecurityGroupIdList', ], 'subnetIds' => [ 'shape' => 'SubnetIdList', ], 'ipAddressType' => [ 'shape' => 'IPAddressType', ], ], ], 'VpcIdString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^vpc-([a-z0-9]{8}$|[a-z0-9]{17}$)', ], 'arn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:*:*:*:*:*', ], 'booleanValue' => [ 'type' => 'boolean', ], 'dnsStatus' => [ 'type' => 'string', 'enum' => [ 'NONE', 'UPDATE_REQUESTED', 'UPDATING', 'FAILED_UPDATE', 'SUCCESSFULLY_UPDATED', ], ], 'errorMessage' => [ 'type' => 'string', ], 'numBytes' => [ 'type' => 'long', ], 'numChangesets' => [ 'type' => 'integer', ], 'numFiles' => [ 'type' => 'integer', ], 'stringValueLength1to255' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'tgwStatus' => [ 'type' => 'string', 'enum' => [ 'NONE', 'UPDATE_REQUESTED', 'UPDATING', 'FAILED_UPDATE', 'SUCCESSFULLY_UPDATED', ], ], 'url' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^https?://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]', ], 'urn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[A-Za-z0-9._\\-:\\/#\\+]+$', ], ],];
