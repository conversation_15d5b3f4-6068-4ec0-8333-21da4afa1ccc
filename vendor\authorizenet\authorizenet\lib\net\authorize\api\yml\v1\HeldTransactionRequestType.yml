net\authorize\api\contract\v1\HeldTransactionRequestType:
    properties:
        action:
            expose: true
            access_type: public_method
            serialized_name: action
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getAction
                setter: setAction
            type: string
        refTransId:
            expose: true
            access_type: public_method
            serialized_name: refTransId
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getRefTransId
                setter: setRefTransId
            type: string
