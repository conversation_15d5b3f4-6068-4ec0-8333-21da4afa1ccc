{"name": "coingate/coingate-php", "description": "CoinGate library for PHP", "keywords": ["coingate", "bitcoin", "litecoin", "altcoin", "merchant", "gateway", "payment"], "homepage": "https://coingate.com", "license": "MIT", "authors": [{"name": "CoinGate and contributors", "homepage": "https://github.com/coingate/coingate-php/graphs/contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.3.0", "ext-curl": "*", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "^5.7 || ^9.0", "squizlabs/php_codesniffer": "~3.6.2", "phpstan/phpstan": "~1.4.10"}, "autoload": {"psr-4": {"CoinGate\\": "lib/"}}, "autoload-dev": {"psr-4": {"CoinGate\\": ["tests/", "tests/CoinGate/"]}}}