<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Api\V2010\Account;

use Twilio\Exceptions\TwilioException;
use Twilio\ListResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\InstanceContext;
use Twilio\Serialize;
use Twilio\Rest\Api\V2010\Account\IncomingPhoneNumber\AssignedAddOnList;


/**
 * @property AssignedAddOnList $assignedAddOns
 * @method \Twilio\Rest\Api\V2010\Account\IncomingPhoneNumber\AssignedAddOnContext assignedAddOns(string $sid)
 */
class IncomingPhoneNumberContext extends InstanceContext
    {
    protected $_assignedAddOns;

    /**
     * Initialize the IncomingPhoneNumberContext
     *
     * @param Version $version Version that contains the resource
     * @param string $accountSid The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that will create the resource.
     * @param string $sid The Twilio-provided string that uniquely identifies the IncomingPhoneNumber resource to delete.
     */
    public function __construct(
        Version $version,
        $accountSid,
        $sid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'accountSid' =>
            $accountSid,
        'sid' =>
            $sid,
        ];

        $this->uri = '/Accounts/' . \rawurlencode($accountSid)
        .'/IncomingPhoneNumbers/' . \rawurlencode($sid)
        .'.json';
    }

    /**
     * Delete the IncomingPhoneNumberInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded' ]);
        return $this->version->delete('DELETE', $this->uri, [], [], $headers);
    }


    /**
     * Fetch the IncomingPhoneNumberInstance
     *
     * @return IncomingPhoneNumberInstance Fetched IncomingPhoneNumberInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): IncomingPhoneNumberInstance
    {

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json' ]);
        $payload = $this->version->fetch('GET', $this->uri, [], [], $headers);

        return new IncomingPhoneNumberInstance(
            $this->version,
            $payload,
            $this->solution['accountSid'],
            $this->solution['sid']
        );
    }


    /**
     * Update the IncomingPhoneNumberInstance
     *
     * @param array|Options $options Optional Arguments
     * @return IncomingPhoneNumberInstance Updated IncomingPhoneNumberInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(array $options = []): IncomingPhoneNumberInstance
    {

        $options = new Values($options);

        $data = Values::of([
            'AccountSid' =>
                $options['accountSid'],
            'ApiVersion' =>
                $options['apiVersion'],
            'FriendlyName' =>
                $options['friendlyName'],
            'SmsApplicationSid' =>
                $options['smsApplicationSid'],
            'SmsFallbackMethod' =>
                $options['smsFallbackMethod'],
            'SmsFallbackUrl' =>
                $options['smsFallbackUrl'],
            'SmsMethod' =>
                $options['smsMethod'],
            'SmsUrl' =>
                $options['smsUrl'],
            'StatusCallback' =>
                $options['statusCallback'],
            'StatusCallbackMethod' =>
                $options['statusCallbackMethod'],
            'VoiceApplicationSid' =>
                $options['voiceApplicationSid'],
            'VoiceCallerIdLookup' =>
                Serialize::booleanToString($options['voiceCallerIdLookup']),
            'VoiceFallbackMethod' =>
                $options['voiceFallbackMethod'],
            'VoiceFallbackUrl' =>
                $options['voiceFallbackUrl'],
            'VoiceMethod' =>
                $options['voiceMethod'],
            'VoiceUrl' =>
                $options['voiceUrl'],
            'EmergencyStatus' =>
                $options['emergencyStatus'],
            'EmergencyAddressSid' =>
                $options['emergencyAddressSid'],
            'TrunkSid' =>
                $options['trunkSid'],
            'VoiceReceiveMode' =>
                $options['voiceReceiveMode'],
            'IdentitySid' =>
                $options['identitySid'],
            'AddressSid' =>
                $options['addressSid'],
            'BundleSid' =>
                $options['bundleSid'],
        ]);

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json' ]);
        $payload = $this->version->update('POST', $this->uri, [], $data, $headers);

        return new IncomingPhoneNumberInstance(
            $this->version,
            $payload,
            $this->solution['accountSid'],
            $this->solution['sid']
        );
    }


    /**
     * Access the assignedAddOns
     */
    protected function getAssignedAddOns(): AssignedAddOnList
    {
        if (!$this->_assignedAddOns) {
            $this->_assignedAddOns = new AssignedAddOnList(
                $this->version,
                $this->solution['accountSid'],
                $this->solution['sid']
            );
        }

        return $this->_assignedAddOns;
    }

    /**
     * Magic getter to lazy load subresources
     *
     * @param string $name Subresource to return
     * @return ListResource The requested subresource
     * @throws TwilioException For unknown subresources
     */
    public function __get(string $name): ListResource
    {
        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown subresource ' . $name);
    }

    /**
     * Magic caller to get resource contexts
     *
     * @param string $name Resource to return
     * @param array $arguments Context parameters
     * @return InstanceContext The requested resource context
     * @throws TwilioException For unknown resource
     */
    public function __call(string $name, array $arguments): InstanceContext
    {
        $property = $this->$name;
        if (\method_exists($property, 'getContext')) {
            return \call_user_func_array(array($property, 'getContext'), $arguments);
        }

        throw new TwilioException('Resource does not have a context');
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Api.V2010.IncomingPhoneNumberContext ' . \implode(' ', $context) . ']';
    }
}
