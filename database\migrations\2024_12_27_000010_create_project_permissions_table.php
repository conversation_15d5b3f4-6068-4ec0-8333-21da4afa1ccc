<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * إنشاء جدول أذونات المشاريع
 * 
 * هذا الجدول يحدد الأذونات المخصصة للمستخدمين في مشاريع معينة
 * بما يتجاوز الأدوار العامة في النظام
 * 
 * @package Database\Migrations
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
return new class extends Migration
{
    /**
     * تشغيل الـ Migration
     * 
     * @return void
     */
    public function up(): void
    {
        Schema::create('project_permissions', function (Blueprint $table) {
            // المعرف الأساسي
            $table->id()->comment('معرف الإذن الفريد');
            
            // العلاقات الأساسية
            $table->foreignId('project_id')
                  ->constrained('projects')
                  ->onDelete('cascade')
                  ->comment('معرف المشروع');
                  
            $table->foreignId('user_id')
                  ->constrained('users')
                  ->onDelete('cascade')
                  ->comment('معرف المستخدم');
            
            // أذونات المشروع
            $table->boolean('can_view_project')->default(true)->comment('يمكن عرض المشروع');
            $table->boolean('can_edit_project')->default(false)->comment('يمكن تعديل المشروع');
            $table->boolean('can_delete_project')->default(false)->comment('يمكن حذف المشروع');
            
            // أذونات المهام
            $table->boolean('can_view_tasks')->default(true)->comment('يمكن عرض المهام');
            $table->boolean('can_create_tasks')->default(false)->comment('يمكن إنشاء المهام');
            $table->boolean('can_edit_tasks')->default(false)->comment('يمكن تعديل المهام');
            $table->boolean('can_delete_tasks')->default(false)->comment('يمكن حذف المهام');
            $table->boolean('can_assign_tasks')->default(false)->comment('يمكن تكليف المهام');
            
            // أذونات الوقت
            $table->boolean('can_log_time')->default(true)->comment('يمكن تسجيل الوقت');
            $table->boolean('can_view_time_entries')->default(false)->comment('يمكن عرض سجلات الوقت');
            $table->boolean('can_edit_time_entries')->default(false)->comment('يمكن تعديل سجلات الوقت');
            
            // أذونات الملفات
            $table->boolean('can_upload_files')->default(true)->comment('يمكن رفع الملفات');
            $table->boolean('can_download_files')->default(true)->comment('يمكن تحميل الملفات');
            $table->boolean('can_delete_files')->default(false)->comment('يمكن حذف الملفات');
            
            // أذونات التعليقات
            $table->boolean('can_add_comments')->default(true)->comment('يمكن إضافة التعليقات');
            $table->boolean('can_edit_comments')->default(false)->comment('يمكن تعديل التعليقات');
            $table->boolean('can_delete_comments')->default(false)->comment('يمكن حذف التعليقات');
            
            // أذونات الفريق
            $table->boolean('can_manage_team')->default(false)->comment('يمكن إدارة الفريق');
            $table->boolean('can_view_team')->default(true)->comment('يمكن عرض الفريق');
            
            // أذونات التقارير
            $table->boolean('can_view_reports')->default(false)->comment('يمكن عرض التقارير');
            $table->boolean('can_export_data')->default(false)->comment('يمكن تصدير البيانات');
            
            // أذونات المعالم
            $table->boolean('can_manage_milestones')->default(false)->comment('يمكن إدارة المعالم');
            $table->boolean('can_view_milestones')->default(true)->comment('يمكن عرض المعالم');
            
            // أذونات الميزانية
            $table->boolean('can_view_budget')->default(false)->comment('يمكن عرض الميزانية');
            $table->boolean('can_manage_budget')->default(false)->comment('يمكن إدارة الميزانية');
            
            // معلومات الإنشاء والتعديل
            $table->foreignId('granted_by')
                  ->constrained('users')
                  ->onDelete('cascade')
                  ->comment('معرف المستخدم الذي منح الأذونات');
            
            $table->text('notes')->nullable()->comment('ملاحظات حول الأذونات الممنوحة');
            
            // تاريخ انتهاء الأذونات (اختياري)
            $table->timestamp('expires_at')->nullable()->comment('تاريخ انتهاء الأذونات');
            
            // طوابع زمنية
            $table->timestamps();
            
            // فهرس فريد لمنع التكرار
            $table->unique(['project_id', 'user_id'], 'unique_project_user_permission');
            
            // فهارس إضافية
            $table->index(['user_id', 'project_id'], 'idx_permissions_user_project');
            $table->index(['granted_by', 'created_at'], 'idx_permissions_granter_date');
            $table->index(['expires_at'], 'idx_permissions_expiry');
        });
        
        DB::statement("ALTER TABLE project_permissions COMMENT = 'جدول أذونات المشاريع - أذونات مخصصة للمستخدمين في مشاريع معينة'");
    }

    /**
     * التراجع عن الـ Migration
     * 
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('project_permissions');
    }
};
