<?php
namespace Aws\SupplyChain;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS Supply Chain** service.
 * @method \Aws\Result createBillOfMaterialsImportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createBillOfMaterialsImportJobAsync(array $args = [])
 * @method \Aws\Result createDataIntegrationFlow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDataIntegrationFlowAsync(array $args = [])
 * @method \Aws\Result createDataLakeDataset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDataLakeDatasetAsync(array $args = [])
 * @method \Aws\Result createDataLakeNamespace(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDataLakeNamespaceAsync(array $args = [])
 * @method \Aws\Result createInstance(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createInstanceAsync(array $args = [])
 * @method \Aws\Result deleteDataIntegrationFlow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDataIntegrationFlowAsync(array $args = [])
 * @method \Aws\Result deleteDataLakeDataset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDataLakeDatasetAsync(array $args = [])
 * @method \Aws\Result deleteDataLakeNamespace(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDataLakeNamespaceAsync(array $args = [])
 * @method \Aws\Result deleteInstance(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteInstanceAsync(array $args = [])
 * @method \Aws\Result getBillOfMaterialsImportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getBillOfMaterialsImportJobAsync(array $args = [])
 * @method \Aws\Result getDataIntegrationEvent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDataIntegrationEventAsync(array $args = [])
 * @method \Aws\Result getDataIntegrationFlow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDataIntegrationFlowAsync(array $args = [])
 * @method \Aws\Result getDataIntegrationFlowExecution(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDataIntegrationFlowExecutionAsync(array $args = [])
 * @method \Aws\Result getDataLakeDataset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDataLakeDatasetAsync(array $args = [])
 * @method \Aws\Result getDataLakeNamespace(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDataLakeNamespaceAsync(array $args = [])
 * @method \Aws\Result getInstance(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getInstanceAsync(array $args = [])
 * @method \Aws\Result listDataIntegrationEvents(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDataIntegrationEventsAsync(array $args = [])
 * @method \Aws\Result listDataIntegrationFlowExecutions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDataIntegrationFlowExecutionsAsync(array $args = [])
 * @method \Aws\Result listDataIntegrationFlows(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDataIntegrationFlowsAsync(array $args = [])
 * @method \Aws\Result listDataLakeDatasets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDataLakeDatasetsAsync(array $args = [])
 * @method \Aws\Result listDataLakeNamespaces(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDataLakeNamespacesAsync(array $args = [])
 * @method \Aws\Result listInstances(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listInstancesAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result sendDataIntegrationEvent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise sendDataIntegrationEventAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateDataIntegrationFlow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateDataIntegrationFlowAsync(array $args = [])
 * @method \Aws\Result updateDataLakeDataset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateDataLakeDatasetAsync(array $args = [])
 * @method \Aws\Result updateDataLakeNamespace(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateDataLakeNamespaceAsync(array $args = [])
 * @method \Aws\Result updateInstance(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateInstanceAsync(array $args = [])
 */
class SupplyChainClient extends AwsClient {}
