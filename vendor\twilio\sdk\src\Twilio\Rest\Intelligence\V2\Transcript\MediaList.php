<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Intelligence
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Intelligence\V2\Transcript;

use Twilio\ListResource;
use Twilio\Version;


class MediaList extends ListResource
    {
    /**
     * Construct the MediaList
     *
     * @param Version $version Version that contains the resource
     * @param string $sid The unique SID identifier of the Transcript.
     */
    public function __construct(
        Version $version,
        string $sid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'sid' =>
            $sid,
        
        ];
    }

    /**
     * Constructs a MediaContext
     */
    public function getContext(
        
    ): MediaContext
    {
        return new MediaContext(
            $this->version,
            $this->solution['sid']
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Intelligence.V2.MediaList]';
    }
}
