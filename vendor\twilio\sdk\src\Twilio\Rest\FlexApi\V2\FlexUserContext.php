<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\FlexApi\V2;

use Twilio\Exceptions\TwilioException;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\InstanceContext;


class FlexUserContext extends InstanceContext
    {
    /**
     * Initialize the FlexUserContext
     *
     * @param Version $version Version that contains the resource
     * @param string $instanceSid The unique ID created by <PERSON><PERSON><PERSON> to identify a Flex instance.
     * @param string $flexUserSid The unique id for the flex user to be retrieved.
     */
    public function __construct(
        Version $version,
        $instanceSid,
        $flexUserSid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'instanceSid' =>
            $instanceSid,
        'flexUserSid' =>
            $flexUserSid,
        ];

        $this->uri = '/Instances/' . \rawurlencode($instanceSid)
        .'/Users/' . \rawurlencode($flexUserSid)
        .'';
    }

    /**
     * Fetch the FlexUserInstance
     *
     * @return FlexUserInstance Fetched FlexUserInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): FlexUserInstance
    {

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json' ]);
        $payload = $this->version->fetch('GET', $this->uri, [], [], $headers);

        return new FlexUserInstance(
            $this->version,
            $payload,
            $this->solution['instanceSid'],
            $this->solution['flexUserSid']
        );
    }


    /**
     * Update the FlexUserInstance
     *
     * @param array|Options $options Optional Arguments
     * @return FlexUserInstance Updated FlexUserInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(array $options = []): FlexUserInstance
    {

        $options = new Values($options);

        $data = Values::of([
            'Email' =>
                $options['email'],
            'UserSid' =>
                $options['userSid'],
            'Locale' =>
                $options['locale'],
        ]);

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json' ]);
        $payload = $this->version->update('POST', $this->uri, [], $data, $headers);

        return new FlexUserInstance(
            $this->version,
            $payload,
            $this->solution['instanceSid'],
            $this->solution['flexUserSid']
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.FlexApi.V2.FlexUserContext ' . \implode(' ', $context) . ']';
    }
}
