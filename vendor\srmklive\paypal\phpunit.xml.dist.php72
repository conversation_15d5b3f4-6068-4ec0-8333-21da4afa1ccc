<?xml version="1.0" encoding="UTF-8"?>
<phpunit bootstrap="vendor/autoload.php"
         backupGlobals="false"
         backupStaticAttributes="false"
         colors="true"
         verbose="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         processIsolation="false"
         stopOnFailure="false">
  <testsuites>
    <testsuite name="PayPal Test Suite">
      <directory>tests</directory>
    </testsuite>
  </testsuites>
  <filter>
    <whitelist>
      <directory suffix=".php">src/</directory>
      <exclude>
        <file>src/PayPalFacadeAccessor.php</file>
        <file>src/Traits/PayPalVerifyIPN.php</file>
        <file>src/Services/Str.php</file>
        <directory suffix=".php">src/Facades/</directory>
        <directory suffix=".php">src/Providers/</directory>
      </exclude>
    </whitelist>
  </filter>
  <logging>
    <log type="junit" target="build/report.junit.xml"/>
    <log type="coverage-html" target="build/coverage"/>
    <log type="coverage-text" target="build/coverage.txt"/>
    <log type="coverage-clover" target="build/logs/clover.xml"/>
  </logging>
</phpunit>
