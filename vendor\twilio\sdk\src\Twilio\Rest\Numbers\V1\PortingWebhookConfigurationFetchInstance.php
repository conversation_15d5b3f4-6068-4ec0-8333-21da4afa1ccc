<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Numbers\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;


/**
 * @property string|null $url
 * @property string|null $portInTargetUrl
 * @property string|null $portOutTargetUrl
 * @property string[]|null $notificationsOf
 * @property \DateTime|null $portInTargetDateCreated
 * @property \DateTime|null $portOutTargetDateCreated
 */
class PortingWebhookConfigurationFetchInstance extends InstanceResource
{
    /**
     * Initialize the PortingWebhookConfigurationFetchInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     */
    public function __construct(Version $version, array $payload)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'url' => Values::array_get($payload, 'url'),
            'portInTargetUrl' => Values::array_get($payload, 'port_in_target_url'),
            'portOutTargetUrl' => Values::array_get($payload, 'port_out_target_url'),
            'notificationsOf' => Values::array_get($payload, 'notifications_of'),
            'portInTargetDateCreated' => Deserialize::dateTime(Values::array_get($payload, 'port_in_target_date_created')),
            'portOutTargetDateCreated' => Deserialize::dateTime(Values::array_get($payload, 'port_out_target_date_created')),
        ];

        $this->solution = [];
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Numbers.V1.PortingWebhookConfigurationFetchInstance]';
    }
}

