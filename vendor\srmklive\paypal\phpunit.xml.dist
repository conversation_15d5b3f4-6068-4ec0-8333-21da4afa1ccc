<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" bootstrap="vendor/autoload.php" backupGlobals="false" colors="true" processIsolation="false" stopOnFailure="false" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.1/phpunit.xsd" cacheDirectory=".phpunit.cache" backupStaticProperties="false">
  <coverage>
    <report>
      <clover outputFile="build/logs/clover.xml"/>
      <html outputDirectory="build/coverage"/>
      <text outputFile="build/coverage.txt"/>
    </report>
  </coverage>
  <testsuites>
    <testsuite name="PayPal Test Suite">
      <directory>tests</directory>
    </testsuite>
  </testsuites>
  <logging>
    <junit outputFile="build/report.junit.xml"/>
  </logging>
  <source>
    <include>
      <directory suffix=".php">src/</directory>
    </include>
    <exclude>
      <file>src/PayPalFacadeAccessor.php</file>
      <file>src/Traits/PayPalVerifyIPN.php</file>
      <file>src/Services/Str.php</file>
      <directory suffix=".php">src/Facades/</directory>
      <directory suffix=".php">src/Providers/</directory>
    </exclude>
  </source>
</phpunit>
