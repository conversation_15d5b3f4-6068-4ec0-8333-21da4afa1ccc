<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\FlexApi\V1;

use Twilio\ListResource;
use Twilio\Version;


class PluginVersionArchiveList extends ListResource
    {
    /**
     * Construct the PluginVersionArchiveList
     *
     * @param Version $version Version that contains the resource
     */
    public function __construct(
        Version $version
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        ];
    }

    /**
     * Constructs a PluginVersionArchiveContext
     *
     * @param string $pluginSid The SID of the Flex Plugin the resource to belongs to.
     *
     * @param string $sid The SID of the Flex Plugin Version resource to archive.
     */
    public function getContext(
        string $pluginSid
        , string $sid
        
    ): PluginVersionArchiveContext
    {
        return new PluginVersionArchiveContext(
            $this->version,
            $pluginSid,
            $sid
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.FlexApi.V1.PluginVersionArchiveList]';
    }
}
