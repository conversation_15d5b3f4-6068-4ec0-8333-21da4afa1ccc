net\authorize\api\contract\v1\CreateCustomerPaymentProfileResponse:
    xml_root_name: createCustomerPaymentProfileResponse
    xml_root_namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
    properties:
        customerProfileId:
            expose: true
            access_type: public_method
            serialized_name: customerProfileId
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getCustomerProfileId
                setter: setCustomerProfileId
            type: string
        customerPaymentProfileId:
            expose: true
            access_type: public_method
            serialized_name: customerPaymentProfileId
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getCustomerPaymentProfileId
                setter: setCustomerPaymentProfileId
            type: string
        validationDirectResponse:
            expose: true
            access_type: public_method
            serialized_name: validationDirectResponse
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getValidationDirectResponse
                setter: setValidationDirectResponse
            type: string
