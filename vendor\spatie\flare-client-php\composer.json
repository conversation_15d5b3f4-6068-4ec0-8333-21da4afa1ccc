{"name": "spatie/flare-client-php", "description": "Send PHP errors to <PERSON><PERSON><PERSON>", "keywords": ["spatie", "flare", "exception", "reporting"], "homepage": "https://github.com/spatie/flare-client-php", "license": "MIT", "require": {"php": "^8.0", "illuminate/pipeline": "^8.0|^9.0|^10.0|^11.0|^12.0", "spatie/backtrace": "^1.6.1", "symfony/http-foundation": "^5.2|^6.0|^7.0", "symfony/mime": "^5.2|^6.0|^7.0", "symfony/process": "^5.2|^6.0|^7.0", "symfony/var-dumper": "^5.2|^6.0|^7.0"}, "require-dev": {"dms/phpunit-arraysubset-asserts": "^0.5.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "spatie/pest-plugin-snapshots": "^1.0|^2.0", "pestphp/pest": "^1.20|^2.0"}, "autoload": {"psr-4": {"Spatie\\FlareClient\\": "src"}, "files": ["src/helpers.php"]}, "autoload-dev": {"psr-4": {"Spatie\\FlareClient\\Tests\\": "tests"}}, "scripts": {"analyse": "vendor/bin/phpstan analyse", "baseline": "vendor/bin/phpstan analyse --generate-baseline", "format": "vendor/bin/php-cs-fixer fix --allow-risky=yes", "test": "vendor/bin/pest", "test-coverage": "vendor/bin/phpunit --coverage-html coverage"}, "config": {"sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "phpstan/extension-installer": true}}, "prefer-stable": true, "minimum-stability": "dev", "extra": {"branch-alias": {"dev-main": "1.3.x-dev"}}}