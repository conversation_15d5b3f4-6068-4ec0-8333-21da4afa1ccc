<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Messaging\V1\Service;

use Twilio\Options;
use Twilio\Values;

abstract class DestinationAlphaSenderOptions
{
    /**
     * @param string $isoCountryCode The Optional Two Character ISO Country Code the Alphanumeric Sender ID will be used for. If the IsoCountryCode is not provided, a default Alpha Sender will be created that can be used across all countries.
     * @return CreateDestinationAlphaSenderOptions Options builder
     */
    public static function create(
        
        string $isoCountryCode = Values::NONE

    ): CreateDestinationAlphaSenderOptions
    {
        return new CreateDestinationAlphaSenderOptions(
            $isoCountryCode
        );
    }



    /**
     * @param string $isoCountryCode Optional filter to return only alphanumeric sender IDs associated with the specified two-character ISO country code.
     * @return ReadDestinationAlphaSenderOptions Options builder
     */
    public static function read(
        
        string $isoCountryCode = Values::NONE

    ): ReadDestinationAlphaSenderOptions
    {
        return new ReadDestinationAlphaSenderOptions(
            $isoCountryCode
        );
    }

}

class CreateDestinationAlphaSenderOptions extends Options
    {
    /**
     * @param string $isoCountryCode The Optional Two Character ISO Country Code the Alphanumeric Sender ID will be used for. If the IsoCountryCode is not provided, a default Alpha Sender will be created that can be used across all countries.
     */
    public function __construct(
        
        string $isoCountryCode = Values::NONE

    ) {
        $this->options['isoCountryCode'] = $isoCountryCode;
    }

    /**
     * The Optional Two Character ISO Country Code the Alphanumeric Sender ID will be used for. If the IsoCountryCode is not provided, a default Alpha Sender will be created that can be used across all countries.
     *
     * @param string $isoCountryCode The Optional Two Character ISO Country Code the Alphanumeric Sender ID will be used for. If the IsoCountryCode is not provided, a default Alpha Sender will be created that can be used across all countries.
     * @return $this Fluent Builder
     */
    public function setIsoCountryCode(string $isoCountryCode): self
    {
        $this->options['isoCountryCode'] = $isoCountryCode;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Messaging.V1.CreateDestinationAlphaSenderOptions ' . $options . ']';
    }
}



class ReadDestinationAlphaSenderOptions extends Options
    {
    /**
     * @param string $isoCountryCode Optional filter to return only alphanumeric sender IDs associated with the specified two-character ISO country code.
     */
    public function __construct(
        
        string $isoCountryCode = Values::NONE

    ) {
        $this->options['isoCountryCode'] = $isoCountryCode;
    }

    /**
     * Optional filter to return only alphanumeric sender IDs associated with the specified two-character ISO country code.
     *
     * @param string $isoCountryCode Optional filter to return only alphanumeric sender IDs associated with the specified two-character ISO country code.
     * @return $this Fluent Builder
     */
    public function setIsoCountryCode(string $isoCountryCode): self
    {
        $this->options['isoCountryCode'] = $isoCountryCode;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Messaging.V1.ReadDestinationAlphaSenderOptions ' . $options . ']';
    }
}

