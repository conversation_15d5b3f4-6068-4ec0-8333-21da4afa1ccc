<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Insights
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Insights\V1;

use Twilio\Options;
use Twilio\Values;

abstract class SettingOptions
{
    /**
     * @param string $subaccountSid The unique SID identifier of the Subaccount.
     * @return FetchSettingOptions Options builder
     */
    public static function fetch(
        
        string $subaccountSid = Values::NONE

    ): FetchSettingOptions
    {
        return new FetchSettingOptions(
            $subaccountSid
        );
    }

    /**
     * @param bool $advancedFeatures A boolean flag to enable Advanced Features for Voice Insights.
     * @param bool $voiceTrace A boolean flag to enable Voice Trace.
     * @param string $subaccountSid The unique SID identifier of the Subaccount.
     * @return UpdateSettingOptions Options builder
     */
    public static function update(
        
        bool $advancedFeatures = Values::BOOL_NONE,
        bool $voiceTrace = Values::BOOL_NONE,
        string $subaccountSid = Values::NONE

    ): UpdateSettingOptions
    {
        return new UpdateSettingOptions(
            $advancedFeatures,
            $voiceTrace,
            $subaccountSid
        );
    }

}

class FetchSettingOptions extends Options
    {
    /**
     * @param string $subaccountSid The unique SID identifier of the Subaccount.
     */
    public function __construct(
        
        string $subaccountSid = Values::NONE

    ) {
        $this->options['subaccountSid'] = $subaccountSid;
    }

    /**
     * The unique SID identifier of the Subaccount.
     *
     * @param string $subaccountSid The unique SID identifier of the Subaccount.
     * @return $this Fluent Builder
     */
    public function setSubaccountSid(string $subaccountSid): self
    {
        $this->options['subaccountSid'] = $subaccountSid;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Insights.V1.FetchSettingOptions ' . $options . ']';
    }
}

class UpdateSettingOptions extends Options
    {
    /**
     * @param bool $advancedFeatures A boolean flag to enable Advanced Features for Voice Insights.
     * @param bool $voiceTrace A boolean flag to enable Voice Trace.
     * @param string $subaccountSid The unique SID identifier of the Subaccount.
     */
    public function __construct(
        
        bool $advancedFeatures = Values::BOOL_NONE,
        bool $voiceTrace = Values::BOOL_NONE,
        string $subaccountSid = Values::NONE

    ) {
        $this->options['advancedFeatures'] = $advancedFeatures;
        $this->options['voiceTrace'] = $voiceTrace;
        $this->options['subaccountSid'] = $subaccountSid;
    }

    /**
     * A boolean flag to enable Advanced Features for Voice Insights.
     *
     * @param bool $advancedFeatures A boolean flag to enable Advanced Features for Voice Insights.
     * @return $this Fluent Builder
     */
    public function setAdvancedFeatures(bool $advancedFeatures): self
    {
        $this->options['advancedFeatures'] = $advancedFeatures;
        return $this;
    }

    /**
     * A boolean flag to enable Voice Trace.
     *
     * @param bool $voiceTrace A boolean flag to enable Voice Trace.
     * @return $this Fluent Builder
     */
    public function setVoiceTrace(bool $voiceTrace): self
    {
        $this->options['voiceTrace'] = $voiceTrace;
        return $this;
    }

    /**
     * The unique SID identifier of the Subaccount.
     *
     * @param string $subaccountSid The unique SID identifier of the Subaccount.
     * @return $this Fluent Builder
     */
    public function setSubaccountSid(string $subaccountSid): self
    {
        $this->options['subaccountSid'] = $subaccountSid;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Insights.V1.UpdateSettingOptions ' . $options . ']';
    }
}

