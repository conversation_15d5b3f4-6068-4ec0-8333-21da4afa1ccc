<?php
// This file was auto-generated from sdk-root/src/data/migrationhubstrategy/2020-02-19/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-02-19', 'endpointPrefix' => 'migrationhub-strategy', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Migration Hub Strategy Recommendations', 'serviceId' => 'MigrationHubStrategy', 'signatureVersion' => 'v4', 'signingName' => 'migrationhub-strategy', 'uid' => 'migrationhubstrategy-2020-02-19', ], 'operations' => [ 'GetApplicationComponentDetails' => [ 'name' => 'GetApplicationComponentDetails', 'http' => [ 'method' => 'GET', 'requestUri' => '/get-applicationcomponent-details/{applicationComponentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetApplicationComponentDetailsRequest', ], 'output' => [ 'shape' => 'GetApplicationComponentDetailsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetApplicationComponentStrategies' => [ 'name' => 'GetApplicationComponentStrategies', 'http' => [ 'method' => 'GET', 'requestUri' => '/get-applicationcomponent-strategies/{applicationComponentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetApplicationComponentStrategiesRequest', ], 'output' => [ 'shape' => 'GetApplicationComponentStrategiesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetAssessment' => [ 'name' => 'GetAssessment', 'http' => [ 'method' => 'GET', 'requestUri' => '/get-assessment/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAssessmentRequest', ], 'output' => [ 'shape' => 'GetAssessmentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetImportFileTask' => [ 'name' => 'GetImportFileTask', 'http' => [ 'method' => 'GET', 'requestUri' => '/get-import-file-task/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetImportFileTaskRequest', ], 'output' => [ 'shape' => 'GetImportFileTaskResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetLatestAssessmentId' => [ 'name' => 'GetLatestAssessmentId', 'http' => [ 'method' => 'GET', 'requestUri' => '/get-latest-assessment-id', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetLatestAssessmentIdRequest', ], 'output' => [ 'shape' => 'GetLatestAssessmentIdResponse', ], 'errors' => [ [ 'shape' => 'DependencyException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetPortfolioPreferences' => [ 'name' => 'GetPortfolioPreferences', 'http' => [ 'method' => 'GET', 'requestUri' => '/get-portfolio-preferences', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPortfolioPreferencesRequest', ], 'output' => [ 'shape' => 'GetPortfolioPreferencesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetPortfolioSummary' => [ 'name' => 'GetPortfolioSummary', 'http' => [ 'method' => 'GET', 'requestUri' => '/get-portfolio-summary', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPortfolioSummaryRequest', ], 'output' => [ 'shape' => 'GetPortfolioSummaryResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetRecommendationReportDetails' => [ 'name' => 'GetRecommendationReportDetails', 'http' => [ 'method' => 'GET', 'requestUri' => '/get-recommendation-report-details/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRecommendationReportDetailsRequest', ], 'output' => [ 'shape' => 'GetRecommendationReportDetailsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetServerDetails' => [ 'name' => 'GetServerDetails', 'http' => [ 'method' => 'GET', 'requestUri' => '/get-server-details/{serverId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetServerDetailsRequest', ], 'output' => [ 'shape' => 'GetServerDetailsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetServerStrategies' => [ 'name' => 'GetServerStrategies', 'http' => [ 'method' => 'GET', 'requestUri' => '/get-server-strategies/{serverId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetServerStrategiesRequest', ], 'output' => [ 'shape' => 'GetServerStrategiesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListAnalyzableServers' => [ 'name' => 'ListAnalyzableServers', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-analyzable-servers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAnalyzableServersRequest', ], 'output' => [ 'shape' => 'ListAnalyzableServersResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListApplicationComponents' => [ 'name' => 'ListApplicationComponents', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-applicationcomponents', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListApplicationComponentsRequest', ], 'output' => [ 'shape' => 'ListApplicationComponentsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceLinkedRoleLockClientException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListCollectors' => [ 'name' => 'ListCollectors', 'http' => [ 'method' => 'GET', 'requestUri' => '/list-collectors', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCollectorsRequest', ], 'output' => [ 'shape' => 'ListCollectorsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListImportFileTask' => [ 'name' => 'ListImportFileTask', 'http' => [ 'method' => 'GET', 'requestUri' => '/list-import-file-task', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListImportFileTaskRequest', ], 'output' => [ 'shape' => 'ListImportFileTaskResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListServers' => [ 'name' => 'ListServers', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-servers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListServersRequest', ], 'output' => [ 'shape' => 'ListServersResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'PutPortfolioPreferences' => [ 'name' => 'PutPortfolioPreferences', 'http' => [ 'method' => 'POST', 'requestUri' => '/put-portfolio-preferences', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutPortfolioPreferencesRequest', ], 'output' => [ 'shape' => 'PutPortfolioPreferencesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartAssessment' => [ 'name' => 'StartAssessment', 'http' => [ 'method' => 'POST', 'requestUri' => '/start-assessment', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartAssessmentRequest', ], 'output' => [ 'shape' => 'StartAssessmentResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartImportFileTask' => [ 'name' => 'StartImportFileTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/start-import-file-task', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartImportFileTaskRequest', ], 'output' => [ 'shape' => 'StartImportFileTaskResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartRecommendationReportGeneration' => [ 'name' => 'StartRecommendationReportGeneration', 'http' => [ 'method' => 'POST', 'requestUri' => '/start-recommendation-report-generation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartRecommendationReportGenerationRequest', ], 'output' => [ 'shape' => 'StartRecommendationReportGenerationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StopAssessment' => [ 'name' => 'StopAssessment', 'http' => [ 'method' => 'POST', 'requestUri' => '/stop-assessment', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopAssessmentRequest', ], 'output' => [ 'shape' => 'StopAssessmentResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateApplicationComponentConfig' => [ 'name' => 'UpdateApplicationComponentConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-applicationcomponent-config/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateApplicationComponentConfigRequest', ], 'output' => [ 'shape' => 'UpdateApplicationComponentConfigResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateServerConfig' => [ 'name' => 'UpdateServerConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-server-config/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateServerConfigRequest', ], 'output' => [ 'shape' => 'UpdateServerConfigResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AnalysisStatusUnion' => [ 'type' => 'structure', 'members' => [ 'runtimeAnalysisStatus' => [ 'shape' => 'RuntimeAnalysisStatus', ], 'srcCodeOrDbAnalysisStatus' => [ 'shape' => 'SrcCodeOrDbAnalysisStatus', ], ], 'union' => true, ], 'AnalysisType' => [ 'type' => 'string', 'enum' => [ 'SOURCE_CODE_ANALYSIS', 'DATABASE_ANALYSIS', 'RUNTIME_ANALYSIS', 'BINARY_ANALYSIS', ], ], 'AnalyzableServerSummary' => [ 'type' => 'structure', 'members' => [ 'hostname' => [ 'shape' => 'String', ], 'ipAddress' => [ 'shape' => 'String', ], 'source' => [ 'shape' => 'String', ], 'vmId' => [ 'shape' => 'String', ], ], ], 'AnalyzableServerSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyzableServerSummary', ], ], 'AnalyzerNameUnion' => [ 'type' => 'structure', 'members' => [ 'binaryAnalyzerName' => [ 'shape' => 'BinaryAnalyzerName', ], 'runTimeAnalyzerName' => [ 'shape' => 'RunTimeAnalyzerName', ], 'sourceCodeAnalyzerName' => [ 'shape' => 'SourceCodeAnalyzerName', ], ], 'union' => true, ], 'AntipatternReportResult' => [ 'type' => 'structure', 'members' => [ 'analyzerName' => [ 'shape' => 'AnalyzerNameUnion', ], 'antiPatternReportS3Object' => [ 'shape' => 'S3Object', ], 'antipatternReportStatus' => [ 'shape' => 'AntipatternReportStatus', ], 'antipatternReportStatusMessage' => [ 'shape' => 'StatusMessage', ], ], ], 'AntipatternReportResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AntipatternReportResult', ], ], 'AntipatternReportStatus' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'IN_PROGRESS', 'SUCCESS', ], ], 'AntipatternSeveritySummary' => [ 'type' => 'structure', 'members' => [ 'count' => [ 'shape' => 'Integer', ], 'severity' => [ 'shape' => 'Severity', ], ], ], 'AppType' => [ 'type' => 'string', 'enum' => [ 'DotNetFramework', 'Java', 'SQLServer', 'IIS', 'Oracle', 'Other', 'Tomcat', 'JBoss', 'Spring', 'Mongo DB', 'DB2', 'Maria DB', 'MySQL', 'Sybase', 'PostgreSQLServer', 'Cassandra', 'IBM WebSphere', 'Oracle WebLogic', 'Visual Basic', 'Unknown', 'DotnetCore', 'Dotnet', ], ], 'AppUnitError' => [ 'type' => 'structure', 'members' => [ 'appUnitErrorCategory' => [ 'shape' => 'AppUnitErrorCategory', ], ], ], 'AppUnitErrorCategory' => [ 'type' => 'string', 'enum' => [ 'CREDENTIAL_ERROR', 'CONNECTIVITY_ERROR', 'PERMISSION_ERROR', 'UNSUPPORTED_ERROR', 'OTHER_ERROR', ], ], 'ApplicationComponentCriteria' => [ 'type' => 'string', 'enum' => [ 'NOT_DEFINED', 'APP_NAME', 'SERVER_ID', 'APP_TYPE', 'STRATEGY', 'DESTINATION', 'ANALYSIS_STATUS', 'ERROR_CATEGORY', ], ], 'ApplicationComponentDetail' => [ 'type' => 'structure', 'members' => [ 'analysisStatus' => [ 'shape' => 'SrcCodeOrDbAnalysisStatus', ], 'antipatternReportS3Object' => [ 'shape' => 'S3Object', ], 'antipatternReportStatus' => [ 'shape' => 'AntipatternReportStatus', ], 'antipatternReportStatusMessage' => [ 'shape' => 'StatusMessage', ], 'appType' => [ 'shape' => 'AppType', ], 'appUnitError' => [ 'shape' => 'AppUnitError', ], 'associatedServerId' => [ 'shape' => 'ServerId', ], 'databaseConfigDetail' => [ 'shape' => 'DatabaseConfigDetail', ], 'id' => [ 'shape' => 'ResourceId', ], 'inclusionStatus' => [ 'shape' => 'InclusionStatus', ], 'lastAnalyzedTimestamp' => [ 'shape' => 'TimeStamp', ], 'listAntipatternSeveritySummary' => [ 'shape' => 'ListAntipatternSeveritySummary', ], 'moreServerAssociationExists' => [ 'shape' => 'Boolean', ], 'name' => [ 'shape' => 'ResourceName', ], 'osDriver' => [ 'shape' => 'String', ], 'osVersion' => [ 'shape' => 'String', ], 'recommendationSet' => [ 'shape' => 'RecommendationSet', ], 'resourceSubType' => [ 'shape' => 'ResourceSubType', ], 'resultList' => [ 'shape' => 'ResultList', ], 'runtimeStatus' => [ 'shape' => 'RuntimeAnalysisStatus', ], 'runtimeStatusMessage' => [ 'shape' => 'StatusMessage', ], 'sourceCodeRepositories' => [ 'shape' => 'SourceCodeRepositories', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], ], ], 'ApplicationComponentDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationComponentDetail', ], ], 'ApplicationComponentId' => [ 'type' => 'string', 'max' => 44, 'min' => 0, 'pattern' => '[0-9a-zA-Z-]+', ], 'ApplicationComponentStatusSummary' => [ 'type' => 'structure', 'members' => [ 'count' => [ 'shape' => 'Integer', ], 'srcCodeOrDbAnalysisStatus' => [ 'shape' => 'SrcCodeOrDbAnalysisStatus', ], ], ], 'ApplicationComponentStrategies' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationComponentStrategy', ], ], 'ApplicationComponentStrategy' => [ 'type' => 'structure', 'members' => [ 'isPreferred' => [ 'shape' => 'Boolean', ], 'recommendation' => [ 'shape' => 'RecommendationSet', ], 'status' => [ 'shape' => 'StrategyRecommendation', ], ], ], 'ApplicationComponentSummary' => [ 'type' => 'structure', 'members' => [ 'appType' => [ 'shape' => 'AppType', ], 'count' => [ 'shape' => 'Integer', ], ], ], 'ApplicationMode' => [ 'type' => 'string', 'enum' => [ 'ALL', 'KNOWN', 'UNKNOWN', ], ], 'ApplicationPreferences' => [ 'type' => 'structure', 'members' => [ 'managementPreference' => [ 'shape' => 'ManagementPreference', ], ], ], 'AssessmentDataSourceType' => [ 'type' => 'string', 'enum' => [ 'StrategyRecommendationsApplicationDataCollector', 'ManualImport', 'ApplicationDiscoveryService', ], ], 'AssessmentStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'COMPLETE', 'FAILED', 'STOPPED', ], ], 'AssessmentStatusMessage' => [ 'type' => 'string', 'max' => 512, 'min' => 0, 'pattern' => '.*\\S.*', ], 'AssessmentSummary' => [ 'type' => 'structure', 'members' => [ 'antipatternReportS3Object' => [ 'shape' => 'S3Object', ], 'antipatternReportStatus' => [ 'shape' => 'AntipatternReportStatus', ], 'antipatternReportStatusMessage' => [ 'shape' => 'StatusMessage', ], 'lastAnalyzedTimestamp' => [ 'shape' => 'TimeStamp', ], 'listAntipatternSeveritySummary' => [ 'shape' => 'ListAntipatternSeveritySummary', ], 'listApplicationComponentStatusSummary' => [ 'shape' => 'ListApplicationComponentStatusSummary', ], 'listApplicationComponentStrategySummary' => [ 'shape' => 'ListStrategySummary', ], 'listApplicationComponentSummary' => [ 'shape' => 'ListApplicationComponentSummary', ], 'listServerStatusSummary' => [ 'shape' => 'ListServerStatusSummary', ], 'listServerStrategySummary' => [ 'shape' => 'ListStrategySummary', ], 'listServerSummary' => [ 'shape' => 'ListServerSummary', ], ], ], 'AssessmentTarget' => [ 'type' => 'structure', 'required' => [ 'condition', 'name', 'values', ], 'members' => [ 'condition' => [ 'shape' => 'Condition', ], 'name' => [ 'shape' => 'String', ], 'values' => [ 'shape' => 'AssessmentTargetValues', ], ], ], 'AssessmentTargetValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'AssessmentTargets' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssessmentTarget', ], 'max' => 10, 'min' => 0, ], 'AssociatedApplication' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], ], ], 'AssociatedApplications' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociatedApplication', ], ], 'AssociatedServerIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'AsyncTaskId' => [ 'type' => 'string', 'max' => 52, 'min' => 0, 'pattern' => '[0-9a-z-:]+', ], 'AuthType' => [ 'type' => 'string', 'enum' => [ 'NTLM', 'SSH', 'CERT', ], ], 'AwsManagedResources' => [ 'type' => 'structure', 'required' => [ 'targetDestination', ], 'members' => [ 'targetDestination' => [ 'shape' => 'AwsManagedTargetDestinations', ], ], ], 'AwsManagedTargetDestination' => [ 'type' => 'string', 'enum' => [ 'None specified', 'AWS Elastic BeanStalk', 'AWS Fargate', ], ], 'AwsManagedTargetDestinations' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsManagedTargetDestination', ], 'max' => 1, 'min' => 1, ], 'BinaryAnalyzerName' => [ 'type' => 'string', 'enum' => [ 'DLL_ANALYZER', 'BYTECODE_ANALYZER', ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'BusinessGoals' => [ 'type' => 'structure', 'members' => [ 'licenseCostReduction' => [ 'shape' => 'BusinessGoalsInteger', ], 'modernizeInfrastructureWithCloudNativeTechnologies' => [ 'shape' => 'BusinessGoalsInteger', ], 'reduceOperationalOverheadWithManagedServices' => [ 'shape' => 'BusinessGoalsInteger', ], 'speedOfMigration' => [ 'shape' => 'BusinessGoalsInteger', ], ], ], 'BusinessGoalsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 5, 'min' => 1, ], 'Collector' => [ 'type' => 'structure', 'members' => [ 'collectorHealth' => [ 'shape' => 'CollectorHealth', ], 'collectorId' => [ 'shape' => 'String', ], 'collectorVersion' => [ 'shape' => 'String', ], 'configurationSummary' => [ 'shape' => 'ConfigurationSummary', ], 'hostName' => [ 'shape' => 'String', ], 'ipAddress' => [ 'shape' => 'String', ], 'lastActivityTimeStamp' => [ 'shape' => 'String', ], 'registeredTimeStamp' => [ 'shape' => 'String', ], ], ], 'CollectorHealth' => [ 'type' => 'string', 'enum' => [ 'COLLECTOR_HEALTHY', 'COLLECTOR_UNHEALTHY', ], ], 'Collectors' => [ 'type' => 'list', 'member' => [ 'shape' => 'Collector', ], ], 'Condition' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'NOT_EQUALS', 'CONTAINS', 'NOT_CONTAINS', ], ], 'ConfigurationSummary' => [ 'type' => 'structure', 'members' => [ 'ipAddressBasedRemoteInfoList' => [ 'shape' => 'IPAddressBasedRemoteInfoList', ], 'pipelineInfoList' => [ 'shape' => 'PipelineInfoList', ], 'remoteSourceCodeAnalysisServerInfo' => [ 'shape' => 'RemoteSourceCodeAnalysisServerInfo', ], 'vcenterBasedRemoteInfoList' => [ 'shape' => 'VcenterBasedRemoteInfoList', ], 'versionControlInfoList' => [ 'shape' => 'VersionControlInfoList', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'DataCollectionDetails' => [ 'type' => 'structure', 'members' => [ 'completionTime' => [ 'shape' => 'TimeStamp', ], 'failed' => [ 'shape' => 'Integer', ], 'inProgress' => [ 'shape' => 'Integer', ], 'servers' => [ 'shape' => 'Integer', ], 'startTime' => [ 'shape' => 'TimeStamp', ], 'status' => [ 'shape' => 'AssessmentStatus', ], 'statusMessage' => [ 'shape' => 'AssessmentStatusMessage', ], 'success' => [ 'shape' => 'Integer', ], ], ], 'DataSourceType' => [ 'type' => 'string', 'enum' => [ 'ApplicationDiscoveryService', 'MPA', 'Import', 'StrategyRecommendationsApplicationDataCollector', ], ], 'DatabaseConfigDetail' => [ 'type' => 'structure', 'members' => [ 'secretName' => [ 'shape' => 'String', ], ], ], 'DatabaseManagementPreference' => [ 'type' => 'string', 'enum' => [ 'AWS-managed', 'Self-manage', 'No preference', ], ], 'DatabaseMigrationPreference' => [ 'type' => 'structure', 'members' => [ 'heterogeneous' => [ 'shape' => 'Heterogeneous', ], 'homogeneous' => [ 'shape' => 'Homogeneous', ], 'noPreference' => [ 'shape' => 'NoDatabaseMigrationPreference', ], ], 'union' => true, ], 'DatabasePreferences' => [ 'type' => 'structure', 'members' => [ 'databaseManagementPreference' => [ 'shape' => 'DatabaseManagementPreference', ], 'databaseMigrationPreference' => [ 'shape' => 'DatabaseMigrationPreference', ], ], ], 'DependencyException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'GetApplicationComponentDetailsRequest' => [ 'type' => 'structure', 'required' => [ 'applicationComponentId', ], 'members' => [ 'applicationComponentId' => [ 'shape' => 'ApplicationComponentId', 'location' => 'uri', 'locationName' => 'applicationComponentId', ], ], ], 'GetApplicationComponentDetailsResponse' => [ 'type' => 'structure', 'members' => [ 'applicationComponentDetail' => [ 'shape' => 'ApplicationComponentDetail', ], 'associatedApplications' => [ 'shape' => 'AssociatedApplications', ], 'associatedServerIds' => [ 'shape' => 'AssociatedServerIDs', ], 'moreApplicationResource' => [ 'shape' => 'Boolean', ], ], ], 'GetApplicationComponentStrategiesRequest' => [ 'type' => 'structure', 'required' => [ 'applicationComponentId', ], 'members' => [ 'applicationComponentId' => [ 'shape' => 'ApplicationComponentId', 'location' => 'uri', 'locationName' => 'applicationComponentId', ], ], ], 'GetApplicationComponentStrategiesResponse' => [ 'type' => 'structure', 'members' => [ 'applicationComponentStrategies' => [ 'shape' => 'ApplicationComponentStrategies', ], ], ], 'GetAssessmentRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'AsyncTaskId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetAssessmentResponse' => [ 'type' => 'structure', 'members' => [ 'assessmentTargets' => [ 'shape' => 'AssessmentTargets', ], 'dataCollectionDetails' => [ 'shape' => 'DataCollectionDetails', ], 'id' => [ 'shape' => 'AsyncTaskId', ], ], ], 'GetImportFileTaskRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetImportFileTaskResponse' => [ 'type' => 'structure', 'members' => [ 'completionTime' => [ 'shape' => 'TimeStamp', ], 'id' => [ 'shape' => 'String', ], 'importName' => [ 'shape' => 'String', ], 'inputS3Bucket' => [ 'shape' => 'importS3Bucket', ], 'inputS3Key' => [ 'shape' => 'importS3Key', ], 'numberOfRecordsFailed' => [ 'shape' => 'Integer', ], 'numberOfRecordsSuccess' => [ 'shape' => 'Integer', ], 'startTime' => [ 'shape' => 'TimeStamp', ], 'status' => [ 'shape' => 'ImportFileTaskStatus', ], 'statusReportS3Bucket' => [ 'shape' => 'importS3Bucket', ], 'statusReportS3Key' => [ 'shape' => 'importS3Key', ], ], ], 'GetLatestAssessmentIdRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetLatestAssessmentIdResponse' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'AsyncTaskId', ], ], ], 'GetPortfolioPreferencesRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetPortfolioPreferencesResponse' => [ 'type' => 'structure', 'members' => [ 'applicationMode' => [ 'shape' => 'ApplicationMode', ], 'applicationPreferences' => [ 'shape' => 'ApplicationPreferences', ], 'databasePreferences' => [ 'shape' => 'DatabasePreferences', ], 'prioritizeBusinessGoals' => [ 'shape' => 'PrioritizeBusinessGoals', ], ], ], 'GetPortfolioSummaryRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetPortfolioSummaryResponse' => [ 'type' => 'structure', 'members' => [ 'assessmentSummary' => [ 'shape' => 'AssessmentSummary', ], ], ], 'GetRecommendationReportDetailsRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'RecommendationTaskId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetRecommendationReportDetailsResponse' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'RecommendationTaskId', ], 'recommendationReportDetails' => [ 'shape' => 'RecommendationReportDetails', ], ], ], 'GetServerDetailsRequest' => [ 'type' => 'structure', 'required' => [ 'serverId', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxResult', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'serverId' => [ 'shape' => 'ServerId', 'location' => 'uri', 'locationName' => 'serverId', ], ], ], 'GetServerDetailsResponse' => [ 'type' => 'structure', 'members' => [ 'associatedApplications' => [ 'shape' => 'AssociatedApplications', ], 'nextToken' => [ 'shape' => 'String', ], 'serverDetail' => [ 'shape' => 'ServerDetail', ], ], ], 'GetServerStrategiesRequest' => [ 'type' => 'structure', 'required' => [ 'serverId', ], 'members' => [ 'serverId' => [ 'shape' => 'ServerId', 'location' => 'uri', 'locationName' => 'serverId', ], ], ], 'GetServerStrategiesResponse' => [ 'type' => 'structure', 'members' => [ 'serverStrategies' => [ 'shape' => 'ServerStrategies', ], ], ], 'Group' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'GroupName', ], 'value' => [ 'shape' => 'String', ], ], ], 'GroupIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'Group', ], ], 'GroupName' => [ 'type' => 'string', 'enum' => [ 'ExternalId', 'ExternalSourceType', ], ], 'Heterogeneous' => [ 'type' => 'structure', 'required' => [ 'targetDatabaseEngine', ], 'members' => [ 'targetDatabaseEngine' => [ 'shape' => 'HeterogeneousTargetDatabaseEngines', ], ], ], 'HeterogeneousTargetDatabaseEngine' => [ 'type' => 'string', 'enum' => [ 'None specified', 'Amazon Aurora', 'AWS PostgreSQL', 'MySQL', 'Microsoft SQL Server', 'Oracle Database', 'MariaDB', 'SAP', 'Db2 LUW', 'MongoDB', ], ], 'HeterogeneousTargetDatabaseEngines' => [ 'type' => 'list', 'member' => [ 'shape' => 'HeterogeneousTargetDatabaseEngine', ], 'max' => 1, 'min' => 1, ], 'Homogeneous' => [ 'type' => 'structure', 'members' => [ 'targetDatabaseEngine' => [ 'shape' => 'HomogeneousTargetDatabaseEngines', ], ], ], 'HomogeneousTargetDatabaseEngine' => [ 'type' => 'string', 'enum' => [ 'None specified', ], ], 'HomogeneousTargetDatabaseEngines' => [ 'type' => 'list', 'member' => [ 'shape' => 'HomogeneousTargetDatabaseEngine', ], 'max' => 1, 'min' => 0, ], 'IPAddress' => [ 'type' => 'string', 'max' => 15, 'min' => 0, 'pattern' => '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$', ], 'IPAddressBasedRemoteInfo' => [ 'type' => 'structure', 'members' => [ 'authType' => [ 'shape' => 'AuthType', ], 'ipAddressConfigurationTimeStamp' => [ 'shape' => 'String', ], 'osType' => [ 'shape' => 'OSType', ], ], ], 'IPAddressBasedRemoteInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IPAddressBasedRemoteInfo', ], ], 'ImportFileTaskInformation' => [ 'type' => 'structure', 'members' => [ 'completionTime' => [ 'shape' => 'TimeStamp', ], 'id' => [ 'shape' => 'String', ], 'importName' => [ 'shape' => 'String', ], 'inputS3Bucket' => [ 'shape' => 'importS3Bucket', ], 'inputS3Key' => [ 'shape' => 'importS3Key', ], 'numberOfRecordsFailed' => [ 'shape' => 'Integer', ], 'numberOfRecordsSuccess' => [ 'shape' => 'Integer', ], 'startTime' => [ 'shape' => 'TimeStamp', ], 'status' => [ 'shape' => 'ImportFileTaskStatus', ], 'statusReportS3Bucket' => [ 'shape' => 'importS3Bucket', ], 'statusReportS3Key' => [ 'shape' => 'importS3Key', ], ], ], 'ImportFileTaskStatus' => [ 'type' => 'string', 'enum' => [ 'ImportInProgress', 'ImportFailed', 'ImportPartialSuccess', 'ImportSuccess', 'DeleteInProgress', 'DeleteFailed', 'DeletePartialSuccess', 'DeleteSuccess', ], ], 'InclusionStatus' => [ 'type' => 'string', 'enum' => [ 'excludeFromAssessment', 'includeInAssessment', ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InterfaceName' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '.*', ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ListAnalyzableServersRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResult', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'sort' => [ 'shape' => 'SortOrder', ], ], ], 'ListAnalyzableServersResponse' => [ 'type' => 'structure', 'members' => [ 'analyzableServers' => [ 'shape' => 'AnalyzableServerSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAntipatternSeveritySummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'AntipatternSeveritySummary', ], ], 'ListApplicationComponentStatusSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationComponentStatusSummary', ], ], 'ListApplicationComponentSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationComponentSummary', ], ], 'ListApplicationComponentsRequest' => [ 'type' => 'structure', 'members' => [ 'applicationComponentCriteria' => [ 'shape' => 'ApplicationComponentCriteria', ], 'filterValue' => [ 'shape' => 'ListApplicationComponentsRequestFilterValueString', ], 'groupIdFilter' => [ 'shape' => 'GroupIds', ], 'maxResults' => [ 'shape' => 'MaxResult', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'sort' => [ 'shape' => 'SortOrder', ], ], ], 'ListApplicationComponentsRequestFilterValueString' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '.*\\S.*', ], 'ListApplicationComponentsResponse' => [ 'type' => 'structure', 'members' => [ 'applicationComponentInfos' => [ 'shape' => 'ApplicationComponentDetails', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCollectorsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResult', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListCollectorsResponse' => [ 'type' => 'structure', 'members' => [ 'Collectors' => [ 'shape' => 'Collectors', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListImportFileTaskInformation' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImportFileTaskInformation', ], ], 'ListImportFileTaskRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'Integer', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListImportFileTaskResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'taskInfos' => [ 'shape' => 'ListImportFileTaskInformation', ], ], ], 'ListServerStatusSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServerStatusSummary', ], ], 'ListServerSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServerSummary', ], ], 'ListServersRequest' => [ 'type' => 'structure', 'members' => [ 'filterValue' => [ 'shape' => 'String', ], 'groupIdFilter' => [ 'shape' => 'GroupIds', ], 'maxResults' => [ 'shape' => 'MaxResult', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'serverCriteria' => [ 'shape' => 'ServerCriteria', ], 'sort' => [ 'shape' => 'SortOrder', ], ], ], 'ListServersResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'serverInfos' => [ 'shape' => 'ServerDetails', ], ], ], 'ListStrategySummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'StrategySummary', ], ], 'Location' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '.*\\S.*', ], 'MacAddress' => [ 'type' => 'string', 'max' => 17, 'min' => 0, 'pattern' => '^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})|([0-9a-fA-F]{4}\\\\.[0-9a-fA-F]{4}\\\\.[0-9a-fA-F]{4})$”$', ], 'ManagementPreference' => [ 'type' => 'structure', 'members' => [ 'awsManagedResources' => [ 'shape' => 'AwsManagedResources', ], 'noPreference' => [ 'shape' => 'NoManagementPreference', ], 'selfManageResources' => [ 'shape' => 'SelfManageResources', ], ], 'union' => true, ], 'MaxResult' => [ 'type' => 'integer', 'box' => true, ], 'NetMask' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '.*', ], 'NetworkInfo' => [ 'type' => 'structure', 'required' => [ 'interfaceName', 'ipAddress', 'macAddress', 'netMask', ], 'members' => [ 'interfaceName' => [ 'shape' => 'InterfaceName', ], 'ipAddress' => [ 'shape' => 'IPAddress', ], 'macAddress' => [ 'shape' => 'MacAddress', ], 'netMask' => [ 'shape' => 'NetMask', ], ], ], 'NetworkInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkInfo', ], ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '.*\\S.*', ], 'NoDatabaseMigrationPreference' => [ 'type' => 'structure', 'required' => [ 'targetDatabaseEngine', ], 'members' => [ 'targetDatabaseEngine' => [ 'shape' => 'TargetDatabaseEngines', ], ], ], 'NoManagementPreference' => [ 'type' => 'structure', 'required' => [ 'targetDestination', ], 'members' => [ 'targetDestination' => [ 'shape' => 'NoPreferenceTargetDestinations', ], ], ], 'NoPreferenceTargetDestination' => [ 'type' => 'string', 'enum' => [ 'None specified', 'AWS Elastic BeanStalk', 'AWS Fargate', 'Amazon Elastic Cloud Compute (EC2)', 'Amazon Elastic Container Service (ECS)', 'Amazon Elastic Kubernetes Service (EKS)', ], ], 'NoPreferenceTargetDestinations' => [ 'type' => 'list', 'member' => [ 'shape' => 'NoPreferenceTargetDestination', ], 'max' => 1, 'min' => 1, ], 'OSInfo' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'OSType', ], 'version' => [ 'shape' => 'OSVersion', ], ], ], 'OSType' => [ 'type' => 'string', 'enum' => [ 'LINUX', 'WINDOWS', ], ], 'OSVersion' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '.*\\S.*', ], 'OutputFormat' => [ 'type' => 'string', 'enum' => [ 'Excel', 'Json', ], ], 'PipelineInfo' => [ 'type' => 'structure', 'members' => [ 'pipelineConfigurationTimeStamp' => [ 'shape' => 'String', ], 'pipelineType' => [ 'shape' => 'PipelineType', ], ], ], 'PipelineInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PipelineInfo', ], ], 'PipelineType' => [ 'type' => 'string', 'enum' => [ 'AZURE_DEVOPS', ], ], 'PrioritizeBusinessGoals' => [ 'type' => 'structure', 'members' => [ 'businessGoals' => [ 'shape' => 'BusinessGoals', ], ], ], 'ProjectName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '.*\\S.*', ], 'PutPortfolioPreferencesRequest' => [ 'type' => 'structure', 'members' => [ 'applicationMode' => [ 'shape' => 'ApplicationMode', ], 'applicationPreferences' => [ 'shape' => 'ApplicationPreferences', ], 'databasePreferences' => [ 'shape' => 'DatabasePreferences', ], 'prioritizeBusinessGoals' => [ 'shape' => 'PrioritizeBusinessGoals', ], ], ], 'PutPortfolioPreferencesResponse' => [ 'type' => 'structure', 'members' => [], ], 'RecommendationReportDetails' => [ 'type' => 'structure', 'members' => [ 'completionTime' => [ 'shape' => 'RecommendationReportTimeStamp', ], 's3Bucket' => [ 'shape' => 'String', ], 's3Keys' => [ 'shape' => 'S3Keys', ], 'startTime' => [ 'shape' => 'RecommendationReportTimeStamp', ], 'status' => [ 'shape' => 'RecommendationReportStatus', ], 'statusMessage' => [ 'shape' => 'RecommendationReportStatusMessage', ], ], ], 'RecommendationReportStatus' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'IN_PROGRESS', 'SUCCESS', ], ], 'RecommendationReportStatusMessage' => [ 'type' => 'string', 'max' => 512, 'min' => 0, 'pattern' => '.*\\S.*', ], 'RecommendationReportTimeStamp' => [ 'type' => 'timestamp', ], 'RecommendationSet' => [ 'type' => 'structure', 'members' => [ 'strategy' => [ 'shape' => 'Strategy', ], 'targetDestination' => [ 'shape' => 'TargetDestination', ], 'transformationTool' => [ 'shape' => 'TransformationTool', ], ], ], 'RecommendationTaskId' => [ 'type' => 'string', 'max' => 52, 'min' => 0, 'pattern' => '[0-9a-z-:]+', ], 'RemoteSourceCodeAnalysisServerInfo' => [ 'type' => 'structure', 'members' => [ 'remoteSourceCodeAnalysisServerConfigurationTimestamp' => [ 'shape' => 'String', ], ], ], 'ResourceId' => [ 'type' => 'string', 'max' => 44, 'min' => 0, 'pattern' => '^[0-9a-b]+', ], 'ResourceName' => [ 'type' => 'string', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourceSubType' => [ 'type' => 'string', 'enum' => [ 'Database', 'Process', 'DatabaseProcess', ], ], 'Result' => [ 'type' => 'structure', 'members' => [ 'analysisStatus' => [ 'shape' => 'AnalysisStatusUnion', ], 'analysisType' => [ 'shape' => 'AnalysisType', ], 'antipatternReportResultList' => [ 'shape' => 'AntipatternReportResultList', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], ], ], 'ResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Result', ], ], 'RunTimeAnalyzerName' => [ 'type' => 'string', 'enum' => [ 'A2C_ANALYZER', 'REHOST_ANALYZER', 'EMP_PA_ANALYZER', 'DATABASE_ANALYZER', 'SCT_ANALYZER', ], ], 'RunTimeAssessmentStatus' => [ 'type' => 'string', 'enum' => [ 'dataCollectionTaskToBeScheduled', 'dataCollectionTaskScheduled', 'dataCollectionTaskStarted', 'dataCollectionTaskStopped', 'dataCollectionTaskSuccess', 'dataCollectionTaskFailed', 'dataCollectionTaskPartialSuccess', ], ], 'RuntimeAnalysisStatus' => [ 'type' => 'string', 'enum' => [ 'ANALYSIS_TO_BE_SCHEDULED', 'ANALYSIS_STARTED', 'ANALYSIS_SUCCESS', 'ANALYSIS_FAILED', ], ], 'S3Bucket' => [ 'type' => 'string', 'max' => 63, 'min' => 0, 'pattern' => '[0-9a-z]+[0-9a-z\\.\\-]*[0-9a-z]+', ], 'S3Key' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '.*\\S.*', ], 'S3Keys' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'S3Object' => [ 'type' => 'structure', 'members' => [ 's3Bucket' => [ 'shape' => 'S3Bucket', ], 's3key' => [ 'shape' => 'S3Key', ], ], ], 'SecretsManagerKey' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '.*', 'sensitive' => true, ], 'SelfManageResources' => [ 'type' => 'structure', 'required' => [ 'targetDestination', ], 'members' => [ 'targetDestination' => [ 'shape' => 'SelfManageTargetDestinations', ], ], ], 'SelfManageTargetDestination' => [ 'type' => 'string', 'enum' => [ 'None specified', 'Amazon Elastic Cloud Compute (EC2)', 'Amazon Elastic Container Service (ECS)', 'Amazon Elastic Kubernetes Service (EKS)', ], ], 'SelfManageTargetDestinations' => [ 'type' => 'list', 'member' => [ 'shape' => 'SelfManageTargetDestination', ], 'max' => 1, 'min' => 1, ], 'ServerCriteria' => [ 'type' => 'string', 'enum' => [ 'NOT_DEFINED', 'OS_NAME', 'STRATEGY', 'DESTINATION', 'SERVER_ID', 'ANALYSIS_STATUS', 'ERROR_CATEGORY', ], ], 'ServerDetail' => [ 'type' => 'structure', 'members' => [ 'antipatternReportS3Object' => [ 'shape' => 'S3Object', ], 'antipatternReportStatus' => [ 'shape' => 'AntipatternReportStatus', ], 'antipatternReportStatusMessage' => [ 'shape' => 'StatusMessage', ], 'applicationComponentStrategySummary' => [ 'shape' => 'ListStrategySummary', ], 'dataCollectionStatus' => [ 'shape' => 'RunTimeAssessmentStatus', ], 'id' => [ 'shape' => 'ResourceId', ], 'lastAnalyzedTimestamp' => [ 'shape' => 'TimeStamp', ], 'listAntipatternSeveritySummary' => [ 'shape' => 'ListAntipatternSeveritySummary', ], 'name' => [ 'shape' => 'ResourceName', ], 'recommendationSet' => [ 'shape' => 'RecommendationSet', ], 'serverError' => [ 'shape' => 'ServerError', ], 'serverType' => [ 'shape' => 'String', ], 'statusMessage' => [ 'shape' => 'StatusMessage', ], 'systemInfo' => [ 'shape' => 'SystemInfo', ], ], ], 'ServerDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServerDetail', ], ], 'ServerError' => [ 'type' => 'structure', 'members' => [ 'serverErrorCategory' => [ 'shape' => 'ServerErrorCategory', ], ], ], 'ServerErrorCategory' => [ 'type' => 'string', 'enum' => [ 'CONNECTIVITY_ERROR', 'CREDENTIAL_ERROR', 'PERMISSION_ERROR', 'ARCHITECTURE_ERROR', 'OTHER_ERROR', ], ], 'ServerId' => [ 'type' => 'string', 'max' => 27, 'min' => 1, 'pattern' => '.*\\S.*', ], 'ServerOsType' => [ 'type' => 'string', 'enum' => [ 'WindowsServer', 'AmazonLinux', 'EndOfSupportWindowsServer', 'Redhat', 'Other', ], ], 'ServerStatusSummary' => [ 'type' => 'structure', 'members' => [ 'count' => [ 'shape' => 'Integer', ], 'runTimeAssessmentStatus' => [ 'shape' => 'RunTimeAssessmentStatus', ], ], ], 'ServerStrategies' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServerStrategy', ], ], 'ServerStrategy' => [ 'type' => 'structure', 'members' => [ 'isPreferred' => [ 'shape' => 'Boolean', ], 'numberOfApplicationComponents' => [ 'shape' => 'Integer', ], 'recommendation' => [ 'shape' => 'RecommendationSet', ], 'status' => [ 'shape' => 'StrategyRecommendation', ], ], ], 'ServerSummary' => [ 'type' => 'structure', 'members' => [ 'ServerOsType' => [ 'shape' => 'ServerOsType', ], 'count' => [ 'shape' => 'Integer', ], ], ], 'ServiceLinkedRoleLockClientException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'Severity' => [ 'type' => 'string', 'enum' => [ 'HIGH', 'MEDIUM', 'LOW', ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASC', 'DESC', ], ], 'SourceCode' => [ 'type' => 'structure', 'members' => [ 'location' => [ 'shape' => 'Location', ], 'projectName' => [ 'shape' => 'ProjectName', ], 'sourceVersion' => [ 'shape' => 'SourceVersion', ], 'versionControl' => [ 'shape' => 'VersionControl', ], ], ], 'SourceCodeAnalyzerName' => [ 'type' => 'string', 'enum' => [ 'CSHARP_ANALYZER', 'JAVA_ANALYZER', 'BYTECODE_ANALYZER', 'PORTING_ASSISTANT', ], ], 'SourceCodeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceCode', ], ], 'SourceCodeRepositories' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceCodeRepository', ], ], 'SourceCodeRepository' => [ 'type' => 'structure', 'members' => [ 'branch' => [ 'shape' => 'String', ], 'projectName' => [ 'shape' => 'String', ], 'repository' => [ 'shape' => 'String', ], 'versionControlType' => [ 'shape' => 'String', ], ], ], 'SourceVersion' => [ 'type' => 'string', 'max' => 40, 'min' => 1, 'pattern' => '.*\\S.*', ], 'SrcCodeOrDbAnalysisStatus' => [ 'type' => 'string', 'enum' => [ 'ANALYSIS_TO_BE_SCHEDULED', 'ANALYSIS_STARTED', 'ANALYSIS_SUCCESS', 'ANALYSIS_FAILED', 'ANALYSIS_PARTIAL_SUCCESS', 'UNCONFIGURED', 'CONFIGURED', ], ], 'StartAssessmentRequest' => [ 'type' => 'structure', 'members' => [ 'assessmentDataSourceType' => [ 'shape' => 'AssessmentDataSourceType', ], 'assessmentTargets' => [ 'shape' => 'AssessmentTargets', ], 's3bucketForAnalysisData' => [ 'shape' => 'StartAssessmentRequestS3bucketForAnalysisDataString', ], 's3bucketForReportData' => [ 'shape' => 'StartAssessmentRequestS3bucketForReportDataString', ], ], ], 'StartAssessmentRequestS3bucketForAnalysisDataString' => [ 'type' => 'string', 'max' => 63, 'min' => 0, 'pattern' => '[0-9a-z]+[0-9a-z\\.\\-]*[0-9a-z]+', ], 'StartAssessmentRequestS3bucketForReportDataString' => [ 'type' => 'string', 'max' => 63, 'min' => 0, 'pattern' => '[0-9a-z]+[0-9a-z\\.\\-]*[0-9a-z]+', ], 'StartAssessmentResponse' => [ 'type' => 'structure', 'members' => [ 'assessmentId' => [ 'shape' => 'AsyncTaskId', ], ], ], 'StartImportFileTaskRequest' => [ 'type' => 'structure', 'required' => [ 'S3Bucket', 'name', 's3key', ], 'members' => [ 'S3Bucket' => [ 'shape' => 'importS3Bucket', ], 'dataSourceType' => [ 'shape' => 'DataSourceType', ], 'groupId' => [ 'shape' => 'GroupIds', ], 'name' => [ 'shape' => 'StartImportFileTaskRequestNameString', ], 's3bucketForReportData' => [ 'shape' => 'StartImportFileTaskRequestS3bucketForReportDataString', ], 's3key' => [ 'shape' => 'String', ], ], ], 'StartImportFileTaskRequestNameString' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'StartImportFileTaskRequestS3bucketForReportDataString' => [ 'type' => 'string', 'max' => 63, 'min' => 0, 'pattern' => '[0-9a-z]+[0-9a-z\\.\\-]*[0-9a-z]+', ], 'StartImportFileTaskResponse' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', ], ], ], 'StartRecommendationReportGenerationRequest' => [ 'type' => 'structure', 'members' => [ 'groupIdFilter' => [ 'shape' => 'GroupIds', ], 'outputFormat' => [ 'shape' => 'OutputFormat', ], ], ], 'StartRecommendationReportGenerationResponse' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'RecommendationTaskId', ], ], ], 'StatusMessage' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '.*\\S.*', ], 'StopAssessmentRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentId', ], 'members' => [ 'assessmentId' => [ 'shape' => 'AsyncTaskId', ], ], ], 'StopAssessmentResponse' => [ 'type' => 'structure', 'members' => [], ], 'Strategy' => [ 'type' => 'string', 'enum' => [ 'Rehost', 'Retirement', 'Refactor', 'Replatform', 'Retain', 'Relocate', 'Repurchase', ], ], 'StrategyOption' => [ 'type' => 'structure', 'members' => [ 'isPreferred' => [ 'shape' => 'Boolean', ], 'strategy' => [ 'shape' => 'Strategy', ], 'targetDestination' => [ 'shape' => 'TargetDestination', ], 'toolName' => [ 'shape' => 'TransformationToolName', ], ], ], 'StrategyRecommendation' => [ 'type' => 'string', 'enum' => [ 'recommended', 'viableOption', 'notRecommended', 'potential', ], ], 'StrategySummary' => [ 'type' => 'structure', 'members' => [ 'count' => [ 'shape' => 'Integer', ], 'strategy' => [ 'shape' => 'Strategy', ], ], ], 'String' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '.*\\S.*', ], 'SystemInfo' => [ 'type' => 'structure', 'members' => [ 'cpuArchitecture' => [ 'shape' => 'String', ], 'fileSystemType' => [ 'shape' => 'String', ], 'networkInfoList' => [ 'shape' => 'NetworkInfoList', ], 'osInfo' => [ 'shape' => 'OSInfo', ], ], ], 'TargetDatabaseEngine' => [ 'type' => 'string', 'enum' => [ 'None specified', 'Amazon Aurora', 'AWS PostgreSQL', 'MySQL', 'Microsoft SQL Server', 'Oracle Database', 'MariaDB', 'SAP', 'Db2 LUW', 'MongoDB', ], ], 'TargetDatabaseEngines' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetDatabaseEngine', ], 'max' => 1, 'min' => 1, ], 'TargetDestination' => [ 'type' => 'string', 'enum' => [ 'None specified', 'AWS Elastic BeanStalk', 'AWS Fargate', 'Amazon Elastic Cloud Compute (EC2)', 'Amazon Elastic Container Service (ECS)', 'Amazon Elastic Kubernetes Service (EKS)', 'Aurora MySQL', 'Aurora PostgreSQL', 'Amazon Relational Database Service on MySQL', 'Amazon Relational Database Service on PostgreSQL', 'Amazon DocumentDB', 'Amazon DynamoDB', 'Amazon Relational Database Service', 'Babelfish for Aurora PostgreSQL', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'TimeStamp' => [ 'type' => 'timestamp', ], 'TranformationToolDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '.*\\S.*', ], 'TranformationToolInstallationLink' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '.*\\S.*', ], 'TransformationTool' => [ 'type' => 'structure', 'members' => [ 'description' => [ 'shape' => 'TranformationToolDescription', ], 'name' => [ 'shape' => 'TransformationToolName', ], 'tranformationToolInstallationLink' => [ 'shape' => 'TranformationToolInstallationLink', ], ], ], 'TransformationToolName' => [ 'type' => 'string', 'enum' => [ 'App2Container', 'Porting Assistant For .NET', 'End of Support Migration', 'Windows Web Application Migration Assistant', 'Application Migration Service', 'Strategy Recommendation Support', 'In Place Operating System Upgrade', 'Schema Conversion Tool', 'Database Migration Service', 'Native SQL Server Backup/Restore', ], ], 'UpdateApplicationComponentConfigRequest' => [ 'type' => 'structure', 'required' => [ 'applicationComponentId', ], 'members' => [ 'appType' => [ 'shape' => 'AppType', ], 'applicationComponentId' => [ 'shape' => 'ApplicationComponentId', ], 'configureOnly' => [ 'shape' => 'Boolean', ], 'inclusionStatus' => [ 'shape' => 'InclusionStatus', ], 'secretsManagerKey' => [ 'shape' => 'SecretsManagerKey', ], 'sourceCodeList' => [ 'shape' => 'SourceCodeList', ], 'strategyOption' => [ 'shape' => 'StrategyOption', ], ], ], 'UpdateApplicationComponentConfigResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateServerConfigRequest' => [ 'type' => 'structure', 'required' => [ 'serverId', ], 'members' => [ 'serverId' => [ 'shape' => 'ServerId', ], 'strategyOption' => [ 'shape' => 'StrategyOption', ], ], ], 'UpdateServerConfigResponse' => [ 'type' => 'structure', 'members' => [], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'VcenterBasedRemoteInfo' => [ 'type' => 'structure', 'members' => [ 'osType' => [ 'shape' => 'OSType', ], 'vcenterConfigurationTimeStamp' => [ 'shape' => 'String', ], ], ], 'VcenterBasedRemoteInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VcenterBasedRemoteInfo', ], ], 'VersionControl' => [ 'type' => 'string', 'enum' => [ 'GITHUB', 'GITHUB_ENTERPRISE', 'AZURE_DEVOPS_GIT', ], ], 'VersionControlInfo' => [ 'type' => 'structure', 'members' => [ 'versionControlConfigurationTimeStamp' => [ 'shape' => 'String', ], 'versionControlType' => [ 'shape' => 'VersionControlType', ], ], ], 'VersionControlInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VersionControlInfo', ], ], 'VersionControlType' => [ 'type' => 'string', 'enum' => [ 'GITHUB', 'GITHUB_ENTERPRISE', 'AZURE_DEVOPS_GIT', ], ], 'errorMessage' => [ 'type' => 'string', ], 'importS3Bucket' => [ 'type' => 'string', 'max' => 63, 'min' => 0, 'pattern' => '[0-9a-z]+[0-9a-z\\.\\-]*[0-9a-z]+', ], 'importS3Key' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '.*\\S.*', ], ],];
