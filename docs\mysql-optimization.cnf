# ملف تكوين MySQL محسن لنظام إدارة المشاريع Hesabiai
# مُحسن للتعامل مع مليون تسجيل وأكثر
# 
# استخدام: انسخ هذه الإعدادات إلى ملف my.cnf أو my.ini
# مسار الملف عادة: /etc/mysql/my.cnf أو /etc/my.cnf
#
# ملاحظة: اضبط القيم حسب موارد الخادم المتاحة

[mysqld]

# =============================================================================
# إعدادات عامة
# =============================================================================

# منفذ MySQL الافتراضي
port = 3306

# مجلد البيانات
datadir = /var/lib/mysql

# ملف Socket
socket = /var/lib/mysql/mysql.sock

# معرف الخادم (مهم للنسخ المتماثل)
server-id = 1

# =============================================================================
# إعدادات الأمان والشبكة
# =============================================================================

# ربط بجميع عناوين IP (احذر في الإنتاج)
bind-address = 0.0.0.0

# الحد الأقصى للاتصالات المتزامنة
max_connections = 300

# الحد الأقصى لأخطاء الاتصال قبل الحظر
max_connect_errors = 100000

# مهلة الاتصال (ثانية)
connect_timeout = 10

# مهلة الانتظار للاتصالات غير النشطة (ثانية)
wait_timeout = 28800

# مهلة الاتصالات التفاعلية (ثانية)
interactive_timeout = 28800

# تعطيل DNS reverse lookup لتسريع الاتصالات
skip-name-resolve

# =============================================================================
# إعدادات InnoDB المحسنة (محرك قاعدة البيانات الرئيسي)
# =============================================================================

# محرك التخزين الافتراضي
default-storage-engine = InnoDB

# حجم buffer pool (70-80% من ذاكرة الخادم المتاحة)
# للخادم بذاكرة 4GB: استخدم 2-3GB
# للخادم بذاكرة 8GB: استخدم 5-6GB
# للخادم بذاكرة 16GB: استخدم 12-13GB
innodb_buffer_pool_size = 2G

# عدد instances لـ buffer pool (يُنصح بـ 1 لكل GB)
innodb_buffer_pool_instances = 2

# حجم ملف السجل (25% من buffer pool size)
innodb_log_file_size = 512M

# عدد ملفات السجل
innodb_log_files_in_group = 2

# حجم buffer للسجل
innodb_log_buffer_size = 64M

# طريقة flush للبيانات والسجلات
# 0 = أسرع لكن أقل أماناً
# 1 = أبطأ لكن أكثر أماناً (افتراضي)
# 2 = متوازن (مُنصح به للأداء)
innodb_flush_log_at_trx_commit = 2

# طريقة flush للملفات
# O_DIRECT = تجنب double buffering (مُنصح به)
innodb_flush_method = O_DIRECT

# ملف منفصل لكل جدول
innodb_file_per_table = 1

# حجم صفحة البيانات
innodb_page_size = 16K

# عدد threads للقراءة
innodb_read_io_threads = 8

# عدد threads للكتابة
innodb_write_io_threads = 8

# تفعيل ضغط الجداول
innodb_file_format = Barracuda

# مستوى ضغط البيانات
innodb_compression_level = 6

# =============================================================================
# إعدادات Query Cache (تخزين مؤقت للاستعلامات)
# =============================================================================

# تفعيل query cache
query_cache_type = 1

# حجم query cache
query_cache_size = 256M

# الحد الأقصى لحجم الاستعلام المخزن مؤقتاً
query_cache_limit = 4M

# الحد الأدنى لحجم النتيجة للتخزين المؤقت
query_cache_min_res_unit = 2K

# =============================================================================
# إعدادات الذاكرة والجداول المؤقتة
# =============================================================================

# حجم الجداول المؤقتة في الذاكرة
tmp_table_size = 256M
max_heap_table_size = 256M

# حجم buffer للفرز
sort_buffer_size = 2M

# حجم buffer للقراءة
read_buffer_size = 1M

# حجم buffer للقراءة العشوائية
read_rnd_buffer_size = 2M

# حجم buffer للانضمام (JOIN)
join_buffer_size = 2M

# =============================================================================
# إعدادات MyISAM (للجداول النظام)
# =============================================================================

# حجم key buffer لفهارس MyISAM
key_buffer_size = 128M

# حجم buffer للإصلاح
myisam_sort_buffer_size = 64M

# =============================================================================
# إعدادات الشبكة والحزم
# =============================================================================

# الحد الأقصى لحجم الحزمة
max_allowed_packet = 64M

# حجم buffer للشبكة
net_buffer_length = 32K

# =============================================================================
# إعدادات السجلات والمراقبة
# =============================================================================

# تفعيل slow query log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow-query.log

# حد الاستعلامات البطيئة (ثانية)
long_query_time = 2

# تسجيل الاستعلامات التي لا تستخدم فهارس
log_queries_not_using_indexes = 1

# الحد الأقصى لتسجيل الاستعلامات غير المفهرسة في الدقيقة
log_throttle_queries_not_using_indexes = 60

# تسجيل الاستعلامات الإدارية البطيئة
log_slow_admin_statements = 1

# تفعيل general log (فقط للتطوير - عطله في الإنتاج)
# general_log = 0
# general_log_file = /var/log/mysql/general.log

# تفعيل binary log للنسخ المتماثل
log_bin = /var/log/mysql/mysql-bin.log
binlog_format = ROW
expire_logs_days = 7
max_binlog_size = 100M

# =============================================================================
# إعدادات الأداء المتقدمة
# =============================================================================

# تفعيل performance schema
performance_schema = ON

# حجم جدول performance schema
performance_schema_max_table_instances = 12500
performance_schema_max_table_handles = 4000

# تحسين الفهارس
ft_min_word_len = 3
ft_max_word_len = 84

# إعدادات thread cache
thread_cache_size = 50

# حد فتح الجداول
table_open_cache = 4000

# حد فتح الملفات
open_files_limit = 65535

# =============================================================================
# إعدادات خاصة بنظام التشغيل
# =============================================================================

# تحسين للنظم متعددة المعالجات
innodb_thread_concurrency = 0

# استخدام native AIO في Linux
innodb_use_native_aio = 1

# تحسين الذاكرة الافتراضية
innodb_adaptive_hash_index = 1

# =============================================================================
# إعدادات النسخ المتماثل (Replication)
# =============================================================================

# تفعيل إذا كنت تستخدم master-slave setup
# read_only = 0  # للـ master
# read_only = 1  # للـ slave

# تحسين النسخ المتماثل
sync_binlog = 1
binlog_cache_size = 1M

# =============================================================================
# إعدادات الأمان
# =============================================================================

# منع تحميل الملفات المحلية
local_infile = 0

# تعطيل symbolic links
symbolic_links = 0

# حماية من SQL injection
sql_mode = STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION

# =============================================================================
# إعدادات العميل
# =============================================================================

[mysql]
default-character-set = utf8mb4

[mysqldump]
quick
quote-names
max_allowed_packet = 64M

[client]
default-character-set = utf8mb4
port = 3306
socket = /var/lib/mysql/mysql.sock

# =============================================================================
# ملاحظات مهمة للتحسين
# =============================================================================

# 1. راقب استخدام الذاكرة بعد تطبيق هذه الإعدادات:
#    SHOW GLOBAL STATUS LIKE 'Innodb_buffer_pool_pages_%';
#
# 2. راقب الاستعلامات البطيئة:
#    mysqldumpslow /var/log/mysql/slow-query.log
#
# 3. راقب استخدام Query Cache:
#    SHOW GLOBAL STATUS LIKE 'Qcache%';
#
# 4. راقب استخدام الاتصالات:
#    SHOW GLOBAL STATUS LIKE 'Connections';
#    SHOW GLOBAL STATUS LIKE 'Threads_connected';
#
# 5. راقب استخدام الجداول المؤقتة:
#    SHOW GLOBAL STATUS LIKE 'Created_tmp%';
#
# 6. بعد إعادة تشغيل MySQL، تحقق من السجلات:
#    tail -f /var/log/mysql/error.log
#
# 7. لتطبيق الإعدادات:
#    sudo systemctl restart mysql
#    أو
#    sudo service mysql restart

# =============================================================================
# إعدادات إضافية للخوادم عالية الأداء
# =============================================================================

# للخوادم بذاكرة أكبر من 16GB
# innodb_buffer_pool_size = 12G
# innodb_buffer_pool_instances = 12
# innodb_log_file_size = 3G
# query_cache_size = 512M
# tmp_table_size = 512M
# max_heap_table_size = 512M

# للخوادم بمعالجات متعددة الأنوية
# innodb_read_io_threads = 16
# innodb_write_io_threads = 16
# thread_cache_size = 100
