<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Studio
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Studio\V2\Flow;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;
use Twilio\Rest\Studio\V2\Flow\Execution\ExecutionStepList;
use Twilio\Rest\Studio\V2\Flow\Execution\ExecutionContextList;


/**
 * @property string|null $sid
 * @property string|null $accountSid
 * @property string|null $flowSid
 * @property string|null $contactChannelAddress
 * @property array|null $context
 * @property string $status
 * @property \DateTime|null $dateCreated
 * @property \DateTime|null $dateUpdated
 * @property string|null $url
 * @property array|null $links
 */
class ExecutionInstance extends InstanceResource
{
    protected $_steps;
    protected $_executionContext;

    /**
     * Initialize the ExecutionInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $flowSid The SID of the Excecution's Flow.
     * @param string $sid The SID of the Execution resource to delete.
     */
    public function __construct(Version $version, array $payload, string $flowSid, ?string $sid = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'sid' => Values::array_get($payload, 'sid'),
            'accountSid' => Values::array_get($payload, 'account_sid'),
            'flowSid' => Values::array_get($payload, 'flow_sid'),
            'contactChannelAddress' => Values::array_get($payload, 'contact_channel_address'),
            'context' => Values::array_get($payload, 'context'),
            'status' => Values::array_get($payload, 'status'),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'dateUpdated' => Deserialize::dateTime(Values::array_get($payload, 'date_updated')),
            'url' => Values::array_get($payload, 'url'),
            'links' => Values::array_get($payload, 'links'),
        ];

        $this->solution = ['flowSid' => $flowSid, 'sid' => $sid ?: $this->properties['sid'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return ExecutionContext Context for this ExecutionInstance
     */
    protected function proxy(): ExecutionContext
    {
        if (!$this->context) {
            $this->context = new ExecutionContext(
                $this->version,
                $this->solution['flowSid'],
                $this->solution['sid']
            );
        }

        return $this->context;
    }

    /**
     * Delete the ExecutionInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->proxy()->delete();
    }

    /**
     * Fetch the ExecutionInstance
     *
     * @return ExecutionInstance Fetched ExecutionInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): ExecutionInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Update the ExecutionInstance
     *
     * @param string $status
     * @return ExecutionInstance Updated ExecutionInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(string $status): ExecutionInstance
    {

        return $this->proxy()->update($status);
    }

    /**
     * Access the steps
     */
    protected function getSteps(): ExecutionStepList
    {
        return $this->proxy()->steps;
    }

    /**
     * Access the executionContext
     */
    protected function getExecutionContext(): ExecutionContextList
    {
        return $this->proxy()->executionContext;
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Studio.V2.ExecutionInstance ' . \implode(' ', $context) . ']';
    }
}

