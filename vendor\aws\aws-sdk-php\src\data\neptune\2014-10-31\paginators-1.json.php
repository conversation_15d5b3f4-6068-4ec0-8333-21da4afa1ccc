<?php
// This file was auto-generated from sdk-root/src/data/neptune/2014-10-31/paginators-1.json
return [ 'pagination' => [ 'DescribeDBClusterEndpoints' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'DBClusterEndpoints', ], 'DescribeDBClusterParameterGroups' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'DBClusterParameterGroups', ], 'DescribeDBClusterParameters' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'Parameters', ], 'DescribeDBClusterSnapshots' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'DBClusterSnapshots', ], 'DescribeDBClusters' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'DBClusters', ], 'DescribeDBEngineVersions' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'DBEngineVersions', ], 'DescribeDBInstances' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'DBInstances', ], 'DescribeDBParameterGroups' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'DBParameterGroups', ], 'DescribeDBParameters' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'Parameters', ], 'DescribeDBSubnetGroups' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'DBSubnetGroups', ], 'DescribeEngineDefaultParameters' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'EngineDefaults.Marker', 'result_key' => 'EngineDefaults.Parameters', ], 'DescribeEventSubscriptions' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'EventSubscriptionsList', ], 'DescribeEvents' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'Events', ], 'DescribeGlobalClusters' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'GlobalClusters', ], 'DescribeOrderableDBInstanceOptions' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'OrderableDBInstanceOptions', ], 'DescribePendingMaintenanceActions' => [ 'input_token' => 'Marker', 'limit_key' => 'MaxRecords', 'output_token' => 'Marker', 'result_key' => 'PendingMaintenanceActions', ], 'ListTagsForResource' => [ 'result_key' => 'TagList', ], ],];
