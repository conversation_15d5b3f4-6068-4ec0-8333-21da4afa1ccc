<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Assistants
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Assistants\V1;

use Twilio\Options;
use Twilio\Values;

abstract class KnowledgeOptions
{



    /**
     * @param string $assistantId
     * @return ReadKnowledgeOptions Options builder
     */
    public static function read(
        
        string $assistantId = Values::NONE

    ): ReadKnowledgeOptions
    {
        return new ReadKnowledgeOptions(
            $assistantId
        );
    }


}




class ReadKnowledgeOptions extends Options
    {
    /**
     * @param string $assistantId
     */
    public function __construct(
        
        string $assistantId = Values::NONE

    ) {
        $this->options['assistantId'] = $assistantId;
    }

    /**
     * @param string $assistantId
     * @return $this Fluent Builder
     */
    public function setAssistantId(string $assistantId): self
    {
        $this->options['assistantId'] = $assistantId;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Assistants.V1.ReadKnowledgeOptions ' . $options . ']';
    }
}


