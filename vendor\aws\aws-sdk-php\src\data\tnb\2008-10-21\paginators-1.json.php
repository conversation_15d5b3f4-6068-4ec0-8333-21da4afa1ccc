<?php
// This file was auto-generated from sdk-root/src/data/tnb/2008-10-21/paginators-1.json
return [ 'pagination' => [ 'ListSolFunctionInstances' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'functionInstances', ], 'ListSolFunctionPackages' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'functionPackages', ], 'ListSolNetworkInstances' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'networkInstances', ], 'ListSolNetworkOperations' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'networkOperations', ], 'ListSolNetworkPackages' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'networkPackages', ], ],];
