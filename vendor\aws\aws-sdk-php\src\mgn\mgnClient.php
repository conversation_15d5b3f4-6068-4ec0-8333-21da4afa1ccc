<?php
namespace Aws\mgn;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Application Migration Service** service.
 * @method \Aws\Result archiveApplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise archiveApplicationAsync(array $args = [])
 * @method \Aws\Result archiveWave(array $args = [])
 * @method \GuzzleHttp\Promise\Promise archiveWaveAsync(array $args = [])
 * @method \Aws\Result associateApplications(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateApplicationsAsync(array $args = [])
 * @method \Aws\Result associateSourceServers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateSourceServersAsync(array $args = [])
 * @method \Aws\Result changeServerLifeCycleState(array $args = [])
 * @method \GuzzleHttp\Promise\Promise changeServerLifeCycleStateAsync(array $args = [])
 * @method \Aws\Result createApplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createApplicationAsync(array $args = [])
 * @method \Aws\Result createConnector(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createConnectorAsync(array $args = [])
 * @method \Aws\Result createLaunchConfigurationTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createLaunchConfigurationTemplateAsync(array $args = [])
 * @method \Aws\Result createReplicationConfigurationTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createReplicationConfigurationTemplateAsync(array $args = [])
 * @method \Aws\Result createWave(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createWaveAsync(array $args = [])
 * @method \Aws\Result deleteApplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteApplicationAsync(array $args = [])
 * @method \Aws\Result deleteConnector(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteConnectorAsync(array $args = [])
 * @method \Aws\Result deleteJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteJobAsync(array $args = [])
 * @method \Aws\Result deleteLaunchConfigurationTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteLaunchConfigurationTemplateAsync(array $args = [])
 * @method \Aws\Result deleteReplicationConfigurationTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteReplicationConfigurationTemplateAsync(array $args = [])
 * @method \Aws\Result deleteSourceServer(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteSourceServerAsync(array $args = [])
 * @method \Aws\Result deleteVcenterClient(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteVcenterClientAsync(array $args = [])
 * @method \Aws\Result deleteWave(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteWaveAsync(array $args = [])
 * @method \Aws\Result describeJobLogItems(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeJobLogItemsAsync(array $args = [])
 * @method \Aws\Result describeJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeJobsAsync(array $args = [])
 * @method \Aws\Result describeLaunchConfigurationTemplates(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeLaunchConfigurationTemplatesAsync(array $args = [])
 * @method \Aws\Result describeReplicationConfigurationTemplates(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeReplicationConfigurationTemplatesAsync(array $args = [])
 * @method \Aws\Result describeSourceServers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeSourceServersAsync(array $args = [])
 * @method \Aws\Result describeVcenterClients(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeVcenterClientsAsync(array $args = [])
 * @method \Aws\Result disassociateApplications(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateApplicationsAsync(array $args = [])
 * @method \Aws\Result disassociateSourceServers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateSourceServersAsync(array $args = [])
 * @method \Aws\Result disconnectFromService(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disconnectFromServiceAsync(array $args = [])
 * @method \Aws\Result finalizeCutover(array $args = [])
 * @method \GuzzleHttp\Promise\Promise finalizeCutoverAsync(array $args = [])
 * @method \Aws\Result getLaunchConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getLaunchConfigurationAsync(array $args = [])
 * @method \Aws\Result getReplicationConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getReplicationConfigurationAsync(array $args = [])
 * @method \Aws\Result initializeService(array $args = [])
 * @method \GuzzleHttp\Promise\Promise initializeServiceAsync(array $args = [])
 * @method \Aws\Result listApplications(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listApplicationsAsync(array $args = [])
 * @method \Aws\Result listConnectors(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listConnectorsAsync(array $args = [])
 * @method \Aws\Result listExportErrors(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listExportErrorsAsync(array $args = [])
 * @method \Aws\Result listExports(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listExportsAsync(array $args = [])
 * @method \Aws\Result listImportErrors(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listImportErrorsAsync(array $args = [])
 * @method \Aws\Result listImports(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listImportsAsync(array $args = [])
 * @method \Aws\Result listManagedAccounts(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listManagedAccountsAsync(array $args = [])
 * @method \Aws\Result listSourceServerActions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSourceServerActionsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result listTemplateActions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTemplateActionsAsync(array $args = [])
 * @method \Aws\Result listWaves(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listWavesAsync(array $args = [])
 * @method \Aws\Result markAsArchived(array $args = [])
 * @method \GuzzleHttp\Promise\Promise markAsArchivedAsync(array $args = [])
 * @method \Aws\Result pauseReplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise pauseReplicationAsync(array $args = [])
 * @method \Aws\Result putSourceServerAction(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putSourceServerActionAsync(array $args = [])
 * @method \Aws\Result putTemplateAction(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putTemplateActionAsync(array $args = [])
 * @method \Aws\Result removeSourceServerAction(array $args = [])
 * @method \GuzzleHttp\Promise\Promise removeSourceServerActionAsync(array $args = [])
 * @method \Aws\Result removeTemplateAction(array $args = [])
 * @method \GuzzleHttp\Promise\Promise removeTemplateActionAsync(array $args = [])
 * @method \Aws\Result resumeReplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise resumeReplicationAsync(array $args = [])
 * @method \Aws\Result retryDataReplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise retryDataReplicationAsync(array $args = [])
 * @method \Aws\Result startCutover(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startCutoverAsync(array $args = [])
 * @method \Aws\Result startExport(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startExportAsync(array $args = [])
 * @method \Aws\Result startImport(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startImportAsync(array $args = [])
 * @method \Aws\Result startReplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startReplicationAsync(array $args = [])
 * @method \Aws\Result startTest(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startTestAsync(array $args = [])
 * @method \Aws\Result stopReplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopReplicationAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result terminateTargetInstances(array $args = [])
 * @method \GuzzleHttp\Promise\Promise terminateTargetInstancesAsync(array $args = [])
 * @method \Aws\Result unarchiveApplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise unarchiveApplicationAsync(array $args = [])
 * @method \Aws\Result unarchiveWave(array $args = [])
 * @method \GuzzleHttp\Promise\Promise unarchiveWaveAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateApplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateApplicationAsync(array $args = [])
 * @method \Aws\Result updateConnector(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateConnectorAsync(array $args = [])
 * @method \Aws\Result updateLaunchConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateLaunchConfigurationAsync(array $args = [])
 * @method \Aws\Result updateLaunchConfigurationTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateLaunchConfigurationTemplateAsync(array $args = [])
 * @method \Aws\Result updateReplicationConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateReplicationConfigurationAsync(array $args = [])
 * @method \Aws\Result updateReplicationConfigurationTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateReplicationConfigurationTemplateAsync(array $args = [])
 * @method \Aws\Result updateSourceServer(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateSourceServerAsync(array $args = [])
 * @method \Aws\Result updateSourceServerReplicationType(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateSourceServerReplicationTypeAsync(array $args = [])
 * @method \Aws\Result updateWave(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateWaveAsync(array $args = [])
 */
class mgnClient extends AwsClient {}
