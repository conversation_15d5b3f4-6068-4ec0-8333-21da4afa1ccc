<?php
// This file was auto-generated from sdk-root/src/data/ssm-guiconnect/2021-05-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2021-05-01', 'endpointPrefix' => 'ssm-guiconnect', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS SSM-GUIConnect', 'serviceId' => 'SSM GuiConnect', 'signatureVersion' => 'v4', 'signingName' => 'ssm-guiconnect', 'uid' => 'ssm-guiconnect-2021-05-01', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'DeleteConnectionRecordingPreferences' => [ 'name' => 'DeleteConnectionRecordingPreferences', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteConnectionRecordingPreferences', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteConnectionRecordingPreferencesRequest', ], 'output' => [ 'shape' => 'DeleteConnectionRecordingPreferencesResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'GetConnectionRecordingPreferences' => [ 'name' => 'GetConnectionRecordingPreferences', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetConnectionRecordingPreferences', 'responseCode' => 200, ], 'output' => [ 'shape' => 'GetConnectionRecordingPreferencesResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateConnectionRecordingPreferences' => [ 'name' => 'UpdateConnectionRecordingPreferences', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateConnectionRecordingPreferences', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateConnectionRecordingPreferencesRequest', ], 'output' => [ 'shape' => 'UpdateConnectionRecordingPreferencesResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccountId' => [ 'type' => 'string', 'pattern' => '^[0-9]{12}$', ], 'BucketName' => [ 'type' => 'string', 'pattern' => '(?=^.{3,63}$)(?!^(\\d+\\.)+\\d+$)(^(([a-z0-9]|[a-z0-9][a-z0-9\\-]*[a-z0-9])\\.)*([a-z0-9]|[a-z0-9][a-z0-9\\-]*[a-z0-9])$)', ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ConnectionRecordingPreferences' => [ 'type' => 'structure', 'required' => [ 'KMSKeyArn', 'RecordingDestinations', ], 'members' => [ 'KMSKeyArn' => [ 'shape' => 'ConnectionRecordingPreferencesKMSKeyArnString', ], 'RecordingDestinations' => [ 'shape' => 'RecordingDestinations', ], ], ], 'ConnectionRecordingPreferencesKMSKeyArnString' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'DeleteConnectionRecordingPreferencesRequest' => [ 'type' => 'structure', 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'DeleteConnectionRecordingPreferencesResponse' => [ 'type' => 'structure', 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', ], ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'GetConnectionRecordingPreferencesResponse' => [ 'type' => 'structure', 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', ], 'ConnectionRecordingPreferences' => [ 'shape' => 'ConnectionRecordingPreferences', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'RecordingDestinations' => [ 'type' => 'structure', 'required' => [ 'S3Buckets', ], 'members' => [ 'S3Buckets' => [ 'shape' => 'S3Buckets', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'S3Bucket' => [ 'type' => 'structure', 'required' => [ 'BucketName', 'BucketOwner', ], 'members' => [ 'BucketName' => [ 'shape' => 'BucketName', ], 'BucketOwner' => [ 'shape' => 'AccountId', ], ], ], 'S3Buckets' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3Bucket', ], 'max' => 1, 'min' => 1, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'UpdateConnectionRecordingPreferencesRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectionRecordingPreferences', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'ConnectionRecordingPreferences' => [ 'shape' => 'ConnectionRecordingPreferences', ], ], ], 'UpdateConnectionRecordingPreferencesResponse' => [ 'type' => 'structure', 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', ], 'ConnectionRecordingPreferences' => [ 'shape' => 'ConnectionRecordingPreferences', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], ],];
