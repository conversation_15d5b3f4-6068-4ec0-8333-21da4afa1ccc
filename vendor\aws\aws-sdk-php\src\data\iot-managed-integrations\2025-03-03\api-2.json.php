<?php
// This file was auto-generated from sdk-root/src/data/iot-managed-integrations/2025-03-03/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2025-03-03', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'api.iotmanagedintegrations', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Managed integrations for AWS IoT Device Management', 'serviceId' => 'IoT Managed Integrations', 'signatureVersion' => 'v4', 'signingName' => 'iotmanagedintegrations', 'uid' => 'iot-managed-integrations-2025-03-03', ], 'operations' => [ 'CreateAccountAssociation' => [ 'name' => 'CreateAccountAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/account-associations', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateAccountAssociationRequest', ], 'output' => [ 'shape' => 'CreateAccountAssociationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateCloudConnector' => [ 'name' => 'CreateCloudConnector', 'http' => [ 'method' => 'POST', 'requestUri' => '/cloud-connectors', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateCloudConnectorRequest', ], 'output' => [ 'shape' => 'CreateCloudConnectorResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateConnectorDestination' => [ 'name' => 'CreateConnectorDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/connector-destinations', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateConnectorDestinationRequest', ], 'output' => [ 'shape' => 'CreateConnectorDestinationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateCredentialLocker' => [ 'name' => 'CreateCredentialLocker', 'http' => [ 'method' => 'POST', 'requestUri' => '/credential-lockers', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateCredentialLockerRequest', ], 'output' => [ 'shape' => 'CreateCredentialLockerResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateDestination' => [ 'name' => 'CreateDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/destinations', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateDestinationRequest', ], 'output' => [ 'shape' => 'CreateDestinationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateEventLogConfiguration' => [ 'name' => 'CreateEventLogConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/event-log-configurations', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateEventLogConfigurationRequest', ], 'output' => [ 'shape' => 'CreateEventLogConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateManagedThing' => [ 'name' => 'CreateManagedThing', 'http' => [ 'method' => 'POST', 'requestUri' => '/managed-things', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateManagedThingRequest', ], 'output' => [ 'shape' => 'CreateManagedThingResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateNotificationConfiguration' => [ 'name' => 'CreateNotificationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/notification-configurations', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateNotificationConfigurationRequest', ], 'output' => [ 'shape' => 'CreateNotificationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateOtaTask' => [ 'name' => 'CreateOtaTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/ota-tasks', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateOtaTaskRequest', ], 'output' => [ 'shape' => 'CreateOtaTaskResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateOtaTaskConfiguration' => [ 'name' => 'CreateOtaTaskConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/ota-task-configurations', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateOtaTaskConfigurationRequest', ], 'output' => [ 'shape' => 'CreateOtaTaskConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateProvisioningProfile' => [ 'name' => 'CreateProvisioningProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/provisioning-profiles', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateProvisioningProfileRequest', ], 'output' => [ 'shape' => 'CreateProvisioningProfileResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteAccountAssociation' => [ 'name' => 'DeleteAccountAssociation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/account-associations/{AccountAssociationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAccountAssociationRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteCloudConnector' => [ 'name' => 'DeleteCloudConnector', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/cloud-connectors/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteCloudConnectorRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteConnectorDestination' => [ 'name' => 'DeleteConnectorDestination', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/connector-destinations/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteConnectorDestinationRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteCredentialLocker' => [ 'name' => 'DeleteCredentialLocker', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/credential-lockers/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteCredentialLockerRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteDestination' => [ 'name' => 'DeleteDestination', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/destinations/{Name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteDestinationRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteEventLogConfiguration' => [ 'name' => 'DeleteEventLogConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/event-log-configurations/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteEventLogConfigurationRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteManagedThing' => [ 'name' => 'DeleteManagedThing', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/managed-things/{Identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteManagedThingRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteNotificationConfiguration' => [ 'name' => 'DeleteNotificationConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/notification-configurations/{EventType}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteNotificationConfigurationRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteOtaTask' => [ 'name' => 'DeleteOtaTask', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/ota-tasks/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteOtaTaskRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], 'idempotent' => true, ], 'DeleteOtaTaskConfiguration' => [ 'name' => 'DeleteOtaTaskConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/ota-task-configurations/{Identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteOtaTaskConfigurationRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteProvisioningProfile' => [ 'name' => 'DeleteProvisioningProfile', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/provisioning-profiles/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteProvisioningProfileRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeregisterAccountAssociation' => [ 'name' => 'DeregisterAccountAssociation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/managed-thing-associations/deregister', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeregisterAccountAssociationRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'GetAccountAssociation' => [ 'name' => 'GetAccountAssociation', 'http' => [ 'method' => 'GET', 'requestUri' => '/account-associations/{AccountAssociationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAccountAssociationRequest', ], 'output' => [ 'shape' => 'GetAccountAssociationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetCloudConnector' => [ 'name' => 'GetCloudConnector', 'http' => [ 'method' => 'GET', 'requestUri' => '/cloud-connectors/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCloudConnectorRequest', ], 'output' => [ 'shape' => 'GetCloudConnectorResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetConnectorDestination' => [ 'name' => 'GetConnectorDestination', 'http' => [ 'method' => 'GET', 'requestUri' => '/connector-destinations/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetConnectorDestinationRequest', ], 'output' => [ 'shape' => 'GetConnectorDestinationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetCredentialLocker' => [ 'name' => 'GetCredentialLocker', 'http' => [ 'method' => 'GET', 'requestUri' => '/credential-lockers/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCredentialLockerRequest', ], 'output' => [ 'shape' => 'GetCredentialLockerResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetCustomEndpoint' => [ 'name' => 'GetCustomEndpoint', 'http' => [ 'method' => 'GET', 'requestUri' => '/custom-endpoint', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCustomEndpointRequest', ], 'output' => [ 'shape' => 'GetCustomEndpointResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetDefaultEncryptionConfiguration' => [ 'name' => 'GetDefaultEncryptionConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/configuration/account/encryption', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDefaultEncryptionConfigurationRequest', ], 'output' => [ 'shape' => 'GetDefaultEncryptionConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetDestination' => [ 'name' => 'GetDestination', 'http' => [ 'method' => 'GET', 'requestUri' => '/destinations/{Name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDestinationRequest', ], 'output' => [ 'shape' => 'GetDestinationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetDeviceDiscovery' => [ 'name' => 'GetDeviceDiscovery', 'http' => [ 'method' => 'GET', 'requestUri' => '/device-discoveries/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDeviceDiscoveryRequest', ], 'output' => [ 'shape' => 'GetDeviceDiscoveryResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetEventLogConfiguration' => [ 'name' => 'GetEventLogConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/event-log-configurations/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEventLogConfigurationRequest', ], 'output' => [ 'shape' => 'GetEventLogConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetHubConfiguration' => [ 'name' => 'GetHubConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/hub-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetHubConfigurationRequest', ], 'output' => [ 'shape' => 'GetHubConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetManagedThing' => [ 'name' => 'GetManagedThing', 'http' => [ 'method' => 'GET', 'requestUri' => '/managed-things/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetManagedThingRequest', ], 'output' => [ 'shape' => 'GetManagedThingResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetManagedThingCapabilities' => [ 'name' => 'GetManagedThingCapabilities', 'http' => [ 'method' => 'GET', 'requestUri' => '/managed-things-capabilities/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetManagedThingCapabilitiesRequest', ], 'output' => [ 'shape' => 'GetManagedThingCapabilitiesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetManagedThingConnectivityData' => [ 'name' => 'GetManagedThingConnectivityData', 'http' => [ 'method' => 'POST', 'requestUri' => '/managed-things-connectivity-data/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetManagedThingConnectivityDataRequest', ], 'output' => [ 'shape' => 'GetManagedThingConnectivityDataResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetManagedThingMetaData' => [ 'name' => 'GetManagedThingMetaData', 'http' => [ 'method' => 'GET', 'requestUri' => '/managed-things-metadata/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetManagedThingMetaDataRequest', ], 'output' => [ 'shape' => 'GetManagedThingMetaDataResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetManagedThingState' => [ 'name' => 'GetManagedThingState', 'http' => [ 'method' => 'GET', 'requestUri' => '/managed-thing-states/{ManagedThingId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetManagedThingStateRequest', ], 'output' => [ 'shape' => 'GetManagedThingStateResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetNotificationConfiguration' => [ 'name' => 'GetNotificationConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/notification-configurations/{EventType}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetNotificationConfigurationRequest', ], 'output' => [ 'shape' => 'GetNotificationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetOtaTask' => [ 'name' => 'GetOtaTask', 'http' => [ 'method' => 'GET', 'requestUri' => '/ota-tasks/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetOtaTaskRequest', ], 'output' => [ 'shape' => 'GetOtaTaskResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetOtaTaskConfiguration' => [ 'name' => 'GetOtaTaskConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/ota-task-configurations/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetOtaTaskConfigurationRequest', ], 'output' => [ 'shape' => 'GetOtaTaskConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetProvisioningProfile' => [ 'name' => 'GetProvisioningProfile', 'http' => [ 'method' => 'GET', 'requestUri' => '/provisioning-profiles/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetProvisioningProfileRequest', ], 'output' => [ 'shape' => 'GetProvisioningProfileResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetRuntimeLogConfiguration' => [ 'name' => 'GetRuntimeLogConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/runtime-log-configurations/{ManagedThingId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRuntimeLogConfigurationRequest', ], 'output' => [ 'shape' => 'GetRuntimeLogConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetSchemaVersion' => [ 'name' => 'GetSchemaVersion', 'http' => [ 'method' => 'GET', 'requestUri' => '/schema-versions/{Type}/{SchemaVersionedId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSchemaVersionRequest', ], 'output' => [ 'shape' => 'GetSchemaVersionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListAccountAssociations' => [ 'name' => 'ListAccountAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/account-associations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAccountAssociationsRequest', ], 'output' => [ 'shape' => 'ListAccountAssociationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListCloudConnectors' => [ 'name' => 'ListCloudConnectors', 'http' => [ 'method' => 'GET', 'requestUri' => '/cloud-connectors', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCloudConnectorsRequest', ], 'output' => [ 'shape' => 'ListCloudConnectorsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListConnectorDestinations' => [ 'name' => 'ListConnectorDestinations', 'http' => [ 'method' => 'GET', 'requestUri' => '/connector-destinations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListConnectorDestinationsRequest', ], 'output' => [ 'shape' => 'ListConnectorDestinationsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListCredentialLockers' => [ 'name' => 'ListCredentialLockers', 'http' => [ 'method' => 'GET', 'requestUri' => '/credential-lockers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCredentialLockersRequest', ], 'output' => [ 'shape' => 'ListCredentialLockersResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListDestinations' => [ 'name' => 'ListDestinations', 'http' => [ 'method' => 'GET', 'requestUri' => '/destinations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDestinationsRequest', ], 'output' => [ 'shape' => 'ListDestinationsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListDeviceDiscoveries' => [ 'name' => 'ListDeviceDiscoveries', 'http' => [ 'method' => 'GET', 'requestUri' => '/device-discoveries', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDeviceDiscoveriesRequest', ], 'output' => [ 'shape' => 'ListDeviceDiscoveriesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListDiscoveredDevices' => [ 'name' => 'ListDiscoveredDevices', 'http' => [ 'method' => 'GET', 'requestUri' => '/device-discoveries/{Identifier}/devices', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDiscoveredDevicesRequest', ], 'output' => [ 'shape' => 'ListDiscoveredDevicesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListEventLogConfigurations' => [ 'name' => 'ListEventLogConfigurations', 'http' => [ 'method' => 'GET', 'requestUri' => '/event-log-configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEventLogConfigurationsRequest', ], 'output' => [ 'shape' => 'ListEventLogConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListManagedThingAccountAssociations' => [ 'name' => 'ListManagedThingAccountAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/managed-thing-associations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListManagedThingAccountAssociationsRequest', ], 'output' => [ 'shape' => 'ListManagedThingAccountAssociationsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListManagedThingSchemas' => [ 'name' => 'ListManagedThingSchemas', 'http' => [ 'method' => 'GET', 'requestUri' => '/managed-thing-schemas/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListManagedThingSchemasRequest', ], 'output' => [ 'shape' => 'ListManagedThingSchemasResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListManagedThings' => [ 'name' => 'ListManagedThings', 'http' => [ 'method' => 'GET', 'requestUri' => '/managed-things', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListManagedThingsRequest', ], 'output' => [ 'shape' => 'ListManagedThingsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListNotificationConfigurations' => [ 'name' => 'ListNotificationConfigurations', 'http' => [ 'method' => 'GET', 'requestUri' => '/notification-configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListNotificationConfigurationsRequest', ], 'output' => [ 'shape' => 'ListNotificationConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListOtaTaskConfigurations' => [ 'name' => 'ListOtaTaskConfigurations', 'http' => [ 'method' => 'GET', 'requestUri' => '/ota-task-configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListOtaTaskConfigurationsRequest', ], 'output' => [ 'shape' => 'ListOtaTaskConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListOtaTaskExecutions' => [ 'name' => 'ListOtaTaskExecutions', 'http' => [ 'method' => 'GET', 'requestUri' => '/ota-tasks/{Identifier}/devices', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListOtaTaskExecutionsRequest', ], 'output' => [ 'shape' => 'ListOtaTaskExecutionsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListOtaTasks' => [ 'name' => 'ListOtaTasks', 'http' => [ 'method' => 'GET', 'requestUri' => '/ota-tasks', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListOtaTasksRequest', ], 'output' => [ 'shape' => 'ListOtaTasksResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListProvisioningProfiles' => [ 'name' => 'ListProvisioningProfiles', 'http' => [ 'method' => 'GET', 'requestUri' => '/provisioning-profiles', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListProvisioningProfilesRequest', ], 'output' => [ 'shape' => 'ListProvisioningProfilesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListSchemaVersions' => [ 'name' => 'ListSchemaVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/schema-versions/{Type}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSchemaVersionsRequest', ], 'output' => [ 'shape' => 'ListSchemaVersionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'PutDefaultEncryptionConfiguration' => [ 'name' => 'PutDefaultEncryptionConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/configuration/account/encryption', 'responseCode' => 201, ], 'input' => [ 'shape' => 'PutDefaultEncryptionConfigurationRequest', ], 'output' => [ 'shape' => 'PutDefaultEncryptionConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'PutHubConfiguration' => [ 'name' => 'PutHubConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/hub-configuration', 'responseCode' => 201, ], 'input' => [ 'shape' => 'PutHubConfigurationRequest', ], 'output' => [ 'shape' => 'PutHubConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'PutRuntimeLogConfiguration' => [ 'name' => 'PutRuntimeLogConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/runtime-log-configurations/{ManagedThingId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutRuntimeLogConfigurationRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'RegisterAccountAssociation' => [ 'name' => 'RegisterAccountAssociation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/managed-thing-associations/register', 'responseCode' => 201, ], 'input' => [ 'shape' => 'RegisterAccountAssociationRequest', ], 'output' => [ 'shape' => 'RegisterAccountAssociationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'RegisterCustomEndpoint' => [ 'name' => 'RegisterCustomEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/custom-endpoint', 'responseCode' => 201, ], 'input' => [ 'shape' => 'RegisterCustomEndpointRequest', ], 'output' => [ 'shape' => 'RegisterCustomEndpointResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ResetRuntimeLogConfiguration' => [ 'name' => 'ResetRuntimeLogConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/runtime-log-configurations/{ManagedThingId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ResetRuntimeLogConfigurationRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'SendConnectorEvent' => [ 'name' => 'SendConnectorEvent', 'http' => [ 'method' => 'POST', 'requestUri' => '/connector-event/{ConnectorId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'SendConnectorEventRequest', ], 'output' => [ 'shape' => 'SendConnectorEventResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'SendManagedThingCommand' => [ 'name' => 'SendManagedThingCommand', 'http' => [ 'method' => 'POST', 'requestUri' => '/managed-things-command/{ManagedThingId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'SendManagedThingCommandRequest', ], 'output' => [ 'shape' => 'SendManagedThingCommandResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'StartAccountAssociationRefresh' => [ 'name' => 'StartAccountAssociationRefresh', 'http' => [ 'method' => 'POST', 'requestUri' => '/account-associations/{AccountAssociationId}/refresh', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartAccountAssociationRefreshRequest', ], 'output' => [ 'shape' => 'StartAccountAssociationRefreshResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'StartDeviceDiscovery' => [ 'name' => 'StartDeviceDiscovery', 'http' => [ 'method' => 'POST', 'requestUri' => '/device-discoveries', 'responseCode' => 201, ], 'input' => [ 'shape' => 'StartDeviceDiscoveryRequest', ], 'output' => [ 'shape' => 'StartDeviceDiscoveryResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdateAccountAssociation' => [ 'name' => 'UpdateAccountAssociation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/account-associations/{AccountAssociationId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateAccountAssociationRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdateCloudConnector' => [ 'name' => 'UpdateCloudConnector', 'http' => [ 'method' => 'PUT', 'requestUri' => '/cloud-connectors/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateCloudConnectorRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdateConnectorDestination' => [ 'name' => 'UpdateConnectorDestination', 'http' => [ 'method' => 'PUT', 'requestUri' => '/connector-destinations/{Identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateConnectorDestinationRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdateDestination' => [ 'name' => 'UpdateDestination', 'http' => [ 'method' => 'PUT', 'requestUri' => '/destinations/{Name}', 'responseCode' => 201, ], 'input' => [ 'shape' => 'UpdateDestinationRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdateEventLogConfiguration' => [ 'name' => 'UpdateEventLogConfiguration', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/event-log-configurations/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateEventLogConfigurationRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdateManagedThing' => [ 'name' => 'UpdateManagedThing', 'http' => [ 'method' => 'PUT', 'requestUri' => '/managed-things/{Identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateManagedThingRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdateNotificationConfiguration' => [ 'name' => 'UpdateNotificationConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/notification-configurations/{EventType}', 'responseCode' => 201, ], 'input' => [ 'shape' => 'UpdateNotificationConfigurationRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdateOtaTask' => [ 'name' => 'UpdateOtaTask', 'http' => [ 'method' => 'PUT', 'requestUri' => '/ota-tasks/{Identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateOtaTaskRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AbortConfigCriteria' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'AbortCriteriaAction', ], 'FailureType' => [ 'shape' => 'AbortCriteriaFailureType', ], 'MinNumberOfExecutedThings' => [ 'shape' => 'MinNumberOfExecutedThings', ], 'ThresholdPercentage' => [ 'shape' => 'ThresholdPercentage', ], ], ], 'AbortConfigCriteriaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AbortConfigCriteria', ], ], 'AbortCriteriaAction' => [ 'type' => 'string', 'enum' => [ 'CANCEL', ], ], 'AbortCriteriaFailureType' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'REJECTED', 'TIMED_OUT', 'ALL', ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccountAssociationArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 67, 'pattern' => 'arn:aws:iotmanagedintegrations:[0-9a-zA-Z-]+:[0-9]+:account-association/[0-9a-zA-Z]+', ], 'AccountAssociationDescription' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '[A-Za-z0-9-_ ]+', ], 'AccountAssociationErrorMessage' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '[A-Za-z0-9-_ ]+', ], 'AccountAssociationId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[0-9a-zA-Z]+', ], 'AccountAssociationItem' => [ 'type' => 'structure', 'required' => [ 'AccountAssociationId', 'AssociationState', ], 'members' => [ 'AccountAssociationId' => [ 'shape' => 'AccountAssociationId', ], 'AssociationState' => [ 'shape' => 'AssociationState', ], 'ErrorMessage' => [ 'shape' => 'AccountAssociationErrorMessage', ], 'ConnectorDestinationId' => [ 'shape' => 'ConnectorDestinationId', ], 'Name' => [ 'shape' => 'AccountAssociationName', ], 'Description' => [ 'shape' => 'AccountAssociationDescription', ], 'Arn' => [ 'shape' => 'AccountAssociationArn', ], ], ], 'AccountAssociationListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountAssociationItem', ], ], 'AccountAssociationName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[A-Za-z0-9-_ ]+', ], 'ActionName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[/a-zA-Z0-9\\._ ]+', ], 'ActionReference' => [ 'type' => 'string', 'pattern' => '[a-zA-Z.]+', ], 'ActionTraceId' => [ 'type' => 'string', 'max' => 20, 'min' => 16, 'pattern' => '[a-zA-Z0-9]+=(?:_[0-9]+)?', ], 'AdvertisedProductId' => [ 'type' => 'string', 'max' => 5, 'min' => 5, 'pattern' => '([A-Za-z0-9!#$%&()*\\+\\-;<=>?@^_`{|}~])+', ], 'AssociationState' => [ 'type' => 'string', 'enum' => [ 'ASSOCIATION_IN_PROGRESS', 'ASSOCIATION_FAILED', 'ASSOCIATION_SUCCEEDED', 'ASSOCIATION_DELETING', 'REFRESH_TOKEN_EXPIRED', ], ], 'AttributeName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '.*[a-zA-Z0-9_.,@/:#-]+.*', ], 'AttributeValue' => [ 'type' => 'string', 'max' => 800, 'min' => 0, 'pattern' => '.*[a-zA-Z0-9_.,@/:#-]*.*', ], 'AuthConfig' => [ 'type' => 'structure', 'members' => [ 'oAuth' => [ 'shape' => 'OAuthConfig', ], ], ], 'AuthConfigUpdate' => [ 'type' => 'structure', 'members' => [ 'oAuthUpdate' => [ 'shape' => 'OAuthUpdate', ], ], ], 'AuthMaterialString' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[0-9A-Za-z!#$%&()*\\+\\-;<=>?@^_`{|}~\\/: {},\\\\"]+', 'sensitive' => true, ], 'AuthMaterialType' => [ 'type' => 'string', 'enum' => [ 'CUSTOM_PROTOCOL_QR_BAR_CODE', 'WIFI_SETUP_QR_BAR_CODE', 'ZWAVE_QR_BAR_CODE', 'ZIGBEE_QR_BAR_CODE', 'DISCOVERED_DEVICE', ], ], 'AuthType' => [ 'type' => 'string', 'enum' => [ 'OAUTH', ], ], 'AuthUrl' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => 'https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}([-a-zA-Z0-9()@:%_\\+.~#?&\\/=]*)', ], 'BaseRatePerMinute' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'Brand' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[A-Za-z0-9-_ ]+', 'sensitive' => true, ], 'CaCertificate' => [ 'type' => 'string', 'pattern' => '-----BEGIN CERTIFICATE-----.*(.|\\n)*-----END CERTIFICATE-----\\n?', 'sensitive' => true, ], 'Capabilities' => [ 'type' => 'string', 'max' => 65535, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\s\'\\x{0022},.:\\\\\\/{$}\\[\\]=_\\-\\+]+', ], 'CapabilityAction' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'CapabilityActionName', ], 'ref' => [ 'shape' => 'ActionReference', ], 'actionTraceId' => [ 'shape' => 'ActionTraceId', ], 'parameters' => [ 'shape' => 'CapabilityProperties', ], ], ], 'CapabilityActionName' => [ 'type' => 'string', 'pattern' => '[/a-zA-Z]+', ], 'CapabilityActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'CapabilityAction', ], 'max' => 5, 'min' => 1, ], 'CapabilityId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9./]+(@\\d+\\.\\d+)?', ], 'CapabilityName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[/a-zA-Z0-9\\._ ]+', ], 'CapabilityProperties' => [ 'type' => 'structure', 'members' => [], 'document' => true, 'sensitive' => true, ], 'CapabilityReport' => [ 'type' => 'structure', 'required' => [ 'version', 'endpoints', ], 'members' => [ 'version' => [ 'shape' => 'CapabilityReportVersion', ], 'nodeId' => [ 'shape' => 'NodeId', ], 'endpoints' => [ 'shape' => 'CapabilityReportEndpoints', ], ], ], 'CapabilityReportActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActionName', ], 'max' => 100, 'min' => 0, ], 'CapabilityReportCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'CapabilityReportCapability', ], 'max' => 40, 'min' => 0, ], 'CapabilityReportCapability' => [ 'type' => 'structure', 'required' => [ 'id', 'name', 'version', 'properties', 'actions', 'events', ], 'members' => [ 'id' => [ 'shape' => 'SchemaVersionedId', ], 'name' => [ 'shape' => 'CapabilityName', ], 'version' => [ 'shape' => 'CapabilityVersion', ], 'properties' => [ 'shape' => 'CapabilityReportProperties', ], 'actions' => [ 'shape' => 'CapabilityReportActions', ], 'events' => [ 'shape' => 'CapabilityReportEvents', ], ], ], 'CapabilityReportEndpoint' => [ 'type' => 'structure', 'required' => [ 'id', 'deviceTypes', 'capabilities', ], 'members' => [ 'id' => [ 'shape' => 'EndpointId', ], 'deviceTypes' => [ 'shape' => 'DeviceTypes', ], 'capabilities' => [ 'shape' => 'CapabilityReportCapabilities', ], ], ], 'CapabilityReportEndpoints' => [ 'type' => 'list', 'member' => [ 'shape' => 'CapabilityReportEndpoint', ], 'max' => 40, 'min' => 0, ], 'CapabilityReportEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventName', ], 'max' => 100, 'min' => 0, ], 'CapabilityReportProperties' => [ 'type' => 'list', 'member' => [ 'shape' => 'PropertyName', ], 'max' => 100, 'min' => 0, ], 'CapabilityReportVersion' => [ 'type' => 'string', 'max' => 10, 'min' => 1, 'pattern' => '1\\.0\\.0', ], 'CapabilitySchemaItem' => [ 'type' => 'structure', 'required' => [ 'Format', 'CapabilityId', 'ExtrinsicId', 'ExtrinsicVersion', 'Schema', ], 'members' => [ 'Format' => [ 'shape' => 'SchemaVersionFormat', ], 'CapabilityId' => [ 'shape' => 'SchemaVersionedId', ], 'ExtrinsicId' => [ 'shape' => 'ExtrinsicSchemaId', ], 'ExtrinsicVersion' => [ 'shape' => 'MatterCapabilityReportClusterRevisionId', ], 'Schema' => [ 'shape' => 'ValidationSchema', ], ], ], 'CapabilitySchemas' => [ 'type' => 'list', 'member' => [ 'shape' => 'CapabilitySchemaItem', ], 'max' => 40, 'min' => 0, ], 'CapabilityVersion' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '(0|[1-9][0-9]*)', ], 'ClaimCertificate' => [ 'type' => 'string', 'sensitive' => true, ], 'ClaimCertificatePrivateKey' => [ 'type' => 'string', 'sensitive' => true, ], 'Classification' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'sensitive' => true, ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9=_-]+', ], 'CloudConnectorDescription' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[0-9A-Za-z_\\- ]+', ], 'CloudConnectorId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[A-Za-z0-9-_]+', ], 'CloudConnectorType' => [ 'type' => 'string', 'enum' => [ 'LISTED', 'UNLISTED', ], ], 'ClusterId' => [ 'type' => 'string', 'max' => 24, 'min' => 1, 'pattern' => '0[xX][0-9a-fA-F]+$|^[0-9]+', ], 'CommandCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'CommandCapability', ], 'max' => 5, 'min' => 1, ], 'CommandCapability' => [ 'type' => 'structure', 'required' => [ 'id', 'name', 'version', 'actions', ], 'members' => [ 'id' => [ 'shape' => 'SchemaVersionedId', ], 'name' => [ 'shape' => 'CapabilityName', ], 'version' => [ 'shape' => 'CapabilityVersion', ], 'actions' => [ 'shape' => 'CapabilityActions', ], ], ], 'CommandEndpoint' => [ 'type' => 'structure', 'required' => [ 'endpointId', 'capabilities', ], 'members' => [ 'endpointId' => [ 'shape' => 'EndpointId', ], 'capabilities' => [ 'shape' => 'CommandCapabilities', ], ], ], 'CommandEndpoints' => [ 'type' => 'list', 'member' => [ 'shape' => 'CommandEndpoint', ], 'max' => 5, 'min' => 1, ], 'ConfigurationError' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'ConfigurationErrorCode', ], 'message' => [ 'shape' => 'ConfigurationErrorMessage', ], ], ], 'ConfigurationErrorCode' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'ConfigurationErrorMessage' => [ 'type' => 'string', 'max' => 65535, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\-._,]+', ], 'ConfigurationState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'UPDATE_IN_PROGRESS', 'UPDATE_FAILED', ], ], 'ConfigurationStatus' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'error' => [ 'shape' => 'ConfigurationError', ], 'state' => [ 'shape' => 'ConfigurationState', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ConnectivityStatus' => [ 'type' => 'boolean', 'box' => true, ], 'ConnectivityTimestamp' => [ 'type' => 'timestamp', ], 'ConnectorAssociationId' => [ 'type' => 'string', 'deprecated' => true, 'deprecatedMessage' => 'ConnectorAssociationId is deprecated', 'deprecatedSince' => '2025-06-25', 'max' => 64, 'min' => 1, 'pattern' => '[0-9a-zA-Z]+', ], 'ConnectorDestinationDescription' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[0-9A-Za-z_\\- ]+', ], 'ConnectorDestinationId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[A-Za-z0-9-_]+', ], 'ConnectorDestinationListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectorDestinationSummary', ], ], 'ConnectorDestinationName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[A-Za-z0-9-_ ]+', ], 'ConnectorDestinationSummary' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ConnectorDestinationName', ], 'Description' => [ 'shape' => 'ConnectorDestinationDescription', ], 'CloudConnectorId' => [ 'shape' => 'CloudConnectorId', ], 'Id' => [ 'shape' => 'ConnectorDestinationId', ], ], ], 'ConnectorDeviceId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9_.,@-]+', 'sensitive' => true, ], 'ConnectorDeviceName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\p{L}\\p{N} ._-]+', 'sensitive' => true, ], 'ConnectorEventMessage' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\sa-zA-Z0-9_.,@-]+', 'sensitive' => true, ], 'ConnectorEventOperation' => [ 'type' => 'string', 'enum' => [ 'DEVICE_COMMAND_RESPONSE', 'DEVICE_DISCOVERY', 'DEVICE_EVENT', 'DEVICE_COMMAND_REQUEST', ], ], 'ConnectorEventOperationVersion' => [ 'type' => 'string', 'max' => 6, 'min' => 1, 'pattern' => '[0-9.]+', 'sensitive' => true, ], 'ConnectorEventStatusCode' => [ 'type' => 'integer', 'box' => true, 'max' => 550, 'min' => 100, 'sensitive' => true, ], 'ConnectorId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[A-Za-z0-9-_]+', ], 'ConnectorItem' => [ 'type' => 'structure', 'required' => [ 'Name', 'EndpointConfig', ], 'members' => [ 'Name' => [ 'shape' => 'DisplayName', ], 'EndpointConfig' => [ 'shape' => 'EndpointConfig', ], 'Description' => [ 'shape' => 'CloudConnectorDescription', ], 'EndpointType' => [ 'shape' => 'EndpointType', ], 'Id' => [ 'shape' => 'CloudConnectorId', ], 'Type' => [ 'shape' => 'CloudConnectorType', ], ], ], 'ConnectorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectorItem', ], ], 'ConnectorPolicyId' => [ 'type' => 'string', 'deprecated' => true, 'deprecatedMessage' => 'ConnectorPolicyId is deprecated', 'deprecatedSince' => '2025-06-25', 'max' => 64, 'min' => 1, 'pattern' => '[A-Za-z0-9-_]+', ], 'CreateAccountAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectorDestinationId', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'ConnectorDestinationId' => [ 'shape' => 'ConnectorDestinationId', ], 'Name' => [ 'shape' => 'AccountAssociationName', ], 'Description' => [ 'shape' => 'AccountAssociationDescription', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateAccountAssociationResponse' => [ 'type' => 'structure', 'required' => [ 'OAuthAuthorizationUrl', 'AccountAssociationId', 'AssociationState', ], 'members' => [ 'OAuthAuthorizationUrl' => [ 'shape' => 'OAuthAuthorizationUrl', ], 'AccountAssociationId' => [ 'shape' => 'AccountAssociationId', ], 'AssociationState' => [ 'shape' => 'AssociationState', ], 'Arn' => [ 'shape' => 'AccountAssociationArn', ], ], ], 'CreateCloudConnectorRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'EndpointConfig', ], 'members' => [ 'Name' => [ 'shape' => 'DisplayName', ], 'EndpointConfig' => [ 'shape' => 'EndpointConfig', ], 'Description' => [ 'shape' => 'CloudConnectorDescription', ], 'EndpointType' => [ 'shape' => 'EndpointType', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateCloudConnectorResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'CloudConnectorId', ], ], ], 'CreateConnectorDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'CloudConnectorId', 'AuthType', 'AuthConfig', 'SecretsManager', ], 'members' => [ 'Name' => [ 'shape' => 'ConnectorDestinationName', ], 'Description' => [ 'shape' => 'ConnectorDestinationDescription', ], 'CloudConnectorId' => [ 'shape' => 'CloudConnectorId', ], 'AuthType' => [ 'shape' => 'AuthType', ], 'AuthConfig' => [ 'shape' => 'AuthConfig', ], 'SecretsManager' => [ 'shape' => 'SecretsManager', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateConnectorDestinationResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ConnectorDestinationId', ], ], ], 'CreateCredentialLockerRequest' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'CredentialLockerName', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateCredentialLockerResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'CredentialLockerId', ], 'Arn' => [ 'shape' => 'CredentialLockerArn', ], 'CreatedAt' => [ 'shape' => 'CredentialLockerCreatedAt', ], ], ], 'CreateDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'DeliveryDestinationArn', 'DeliveryDestinationType', 'Name', 'RoleArn', ], 'members' => [ 'DeliveryDestinationArn' => [ 'shape' => 'DeliveryDestinationArn', ], 'DeliveryDestinationType' => [ 'shape' => 'DeliveryDestinationType', ], 'Name' => [ 'shape' => 'DestinationName', ], 'RoleArn' => [ 'shape' => 'DeliveryDestinationRoleArn', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'Description' => [ 'shape' => 'DestinationDescription', ], 'Tags' => [ 'shape' => 'TagsMap', 'deprecated' => true, 'deprecatedMessage' => 'Tags have been deprecated from this api', 'deprecatedSince' => '06-25-2025', ], ], ], 'CreateDestinationResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'DestinationName', ], ], ], 'CreateEventLogConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceType', 'EventLogLevel', ], 'members' => [ 'ResourceType' => [ 'shape' => 'SmartHomeResourceType', ], 'ResourceId' => [ 'shape' => 'SmartHomeResourceId', ], 'EventLogLevel' => [ 'shape' => 'LogLevel', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateEventLogConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'LogConfigurationId', ], ], ], 'CreateManagedThingRequest' => [ 'type' => 'structure', 'required' => [ 'Role', 'AuthenticationMaterial', 'AuthenticationMaterialType', ], 'members' => [ 'Role' => [ 'shape' => 'Role', ], 'Owner' => [ 'shape' => 'Owner', ], 'CredentialLockerId' => [ 'shape' => 'CredentialLockerId', ], 'AuthenticationMaterial' => [ 'shape' => 'AuthMaterialString', ], 'AuthenticationMaterialType' => [ 'shape' => 'AuthMaterialType', ], 'SerialNumber' => [ 'shape' => 'SerialNumber', ], 'Brand' => [ 'shape' => 'Brand', ], 'Model' => [ 'shape' => 'Model', ], 'Name' => [ 'shape' => 'Name', ], 'CapabilityReport' => [ 'shape' => 'CapabilityReport', ], 'CapabilitySchemas' => [ 'shape' => 'CapabilitySchemas', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'Classification' => [ 'shape' => 'Classification', ], 'Tags' => [ 'shape' => 'TagsMap', ], 'MetaData' => [ 'shape' => 'MetaData', ], ], ], 'CreateManagedThingResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ManagedThingId', ], 'Arn' => [ 'shape' => 'ManagedThingArn', ], 'CreatedAt' => [ 'shape' => 'CreatedAt', ], ], ], 'CreateNotificationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'EventType', 'DestinationName', ], 'members' => [ 'EventType' => [ 'shape' => 'EventType', ], 'DestinationName' => [ 'shape' => 'DestinationName', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagsMap', 'deprecated' => true, 'deprecatedMessage' => 'Tags has been deprecated from this api', 'deprecatedSince' => '06-25-2025', ], ], ], 'CreateNotificationConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'EventType' => [ 'shape' => 'EventType', ], ], ], 'CreateOtaTaskConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'OtaDescription', ], 'Name' => [ 'shape' => 'OtaTaskConfigurationName', ], 'PushConfig' => [ 'shape' => 'PushConfig', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateOtaTaskConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'TaskConfigurationId' => [ 'shape' => 'OtaTaskConfigurationId', ], ], ], 'CreateOtaTaskRequest' => [ 'type' => 'structure', 'required' => [ 'S3Url', 'OtaType', ], 'members' => [ 'Description' => [ 'shape' => 'OtaDescription', ], 'S3Url' => [ 'shape' => 'S3Url', ], 'Protocol' => [ 'shape' => 'OtaProtocol', ], 'Target' => [ 'shape' => 'Target', ], 'TaskConfigurationId' => [ 'shape' => 'OtaTaskConfigurationId', ], 'OtaMechanism' => [ 'shape' => 'OtaMechanism', ], 'OtaType' => [ 'shape' => 'OtaType', ], 'OtaTargetQueryString' => [ 'shape' => 'OtaTargetQueryString', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'OtaSchedulingConfig' => [ 'shape' => 'OtaTaskSchedulingConfig', ], 'OtaTaskExecutionRetryConfig' => [ 'shape' => 'OtaTaskExecutionRetryConfig', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateOtaTaskResponse' => [ 'type' => 'structure', 'members' => [ 'TaskId' => [ 'shape' => 'OtaTaskId', ], 'TaskArn' => [ 'shape' => 'OtaTaskArn', ], 'Description' => [ 'shape' => 'OtaDescription', ], ], ], 'CreateProvisioningProfileRequest' => [ 'type' => 'structure', 'required' => [ 'ProvisioningType', ], 'members' => [ 'ProvisioningType' => [ 'shape' => 'ProvisioningType', ], 'CaCertificate' => [ 'shape' => 'CaCertificate', ], 'Name' => [ 'shape' => 'ProvisioningProfileName', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateProvisioningProfileResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ProvisioningProfileArn', ], 'Name' => [ 'shape' => 'ProvisioningProfileName', ], 'ProvisioningType' => [ 'shape' => 'ProvisioningType', ], 'Id' => [ 'shape' => 'ProvisioningProfileId', ], 'ClaimCertificate' => [ 'shape' => 'ClaimCertificate', ], 'ClaimCertificatePrivateKey' => [ 'shape' => 'ClaimCertificatePrivateKey', ], ], ], 'CreatedAt' => [ 'type' => 'timestamp', ], 'CredentialLockerArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 32, 'pattern' => 'arn:aws:iotmanagedintegrations:[0-9a-zA-Z-]+:[0-9]+:credential-locker/[0-9a-zA-Z]+', ], 'CredentialLockerCreatedAt' => [ 'type' => 'timestamp', ], 'CredentialLockerId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9]*', ], 'CredentialLockerListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'CredentialLockerSummary', ], ], 'CredentialLockerName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[A-Za-z0-9-_ ]+', 'sensitive' => true, ], 'CredentialLockerSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'CredentialLockerId', ], 'Arn' => [ 'shape' => 'CredentialLockerArn', ], 'Name' => [ 'shape' => 'CredentialLockerName', ], 'CreatedAt' => [ 'shape' => 'CredentialLockerCreatedAt', ], ], ], 'CustomProtocolDetail' => [ 'type' => 'map', 'key' => [ 'shape' => 'CustomProtocolDetailKey', ], 'value' => [ 'shape' => 'CustomProtocolDetailValue', ], 'max' => 50, 'min' => 0, ], 'CustomProtocolDetailKey' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9 _.-]+', ], 'CustomProtocolDetailValue' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[a-zA-Z0-9 _.{}:"-]+', ], 'DeleteAccountAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'AccountAssociationId', ], 'members' => [ 'AccountAssociationId' => [ 'shape' => 'AccountAssociationId', 'location' => 'uri', 'locationName' => 'AccountAssociationId', ], ], ], 'DeleteCloudConnectorRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'CloudConnectorId', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'DeleteConnectorDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'ConnectorDestinationId', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'DeleteCredentialLockerRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'CredentialLockerId', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'DeleteDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'DestinationName', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'DeleteEventLogConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'LogConfigurationId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'DeleteLocalStoreAfterUpload' => [ 'type' => 'boolean', 'box' => true, ], 'DeleteManagedThingRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'ManagedThingId', 'location' => 'uri', 'locationName' => 'Identifier', ], 'Force' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'Force', ], ], ], 'DeleteNotificationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'EventType', ], 'members' => [ 'EventType' => [ 'shape' => 'EventType', 'location' => 'uri', 'locationName' => 'EventType', ], ], ], 'DeleteOtaTaskConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'OtaTaskConfigurationId', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'DeleteOtaTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'OtaTaskId', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'DeleteProvisioningProfileRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'ProvisioningProfileId', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'DeliveryDestinationArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws:[0-9a-zA-Z]+:[0-9a-zA-Z-]+:[0-9]+:[0-9a-zA-Z]+/[0-9a-zA-Z._-]+', ], 'DeliveryDestinationRoleArn' => [ 'type' => 'string', ], 'DeliveryDestinationType' => [ 'type' => 'string', 'enum' => [ 'KINESIS', ], ], 'DeregisterAccountAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'ManagedThingId', 'AccountAssociationId', ], 'members' => [ 'ManagedThingId' => [ 'shape' => 'ManagedThingId', ], 'AccountAssociationId' => [ 'shape' => 'AccountAssociationId', ], ], ], 'DestinationCreatedAt' => [ 'type' => 'timestamp', ], 'DestinationDescription' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[0-9A-Za-z_\\- ]+', ], 'DestinationListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'DestinationSummary', ], ], 'DestinationName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\p{L}\\p{N} ._-]+', ], 'DestinationSummary' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'DestinationDescription', ], 'DeliveryDestinationArn' => [ 'shape' => 'DeliveryDestinationArn', ], 'DeliveryDestinationType' => [ 'shape' => 'DeliveryDestinationType', ], 'Name' => [ 'shape' => 'DestinationName', ], 'RoleArn' => [ 'shape' => 'DeliveryDestinationRoleArn', ], ], ], 'DestinationUpdatedAt' => [ 'type' => 'timestamp', ], 'Device' => [ 'type' => 'structure', 'required' => [ 'ConnectorDeviceId', 'CapabilityReport', ], 'members' => [ 'ConnectorDeviceId' => [ 'shape' => 'ConnectorDeviceId', ], 'ConnectorDeviceName' => [ 'shape' => 'ConnectorDeviceName', ], 'CapabilityReport' => [ 'shape' => 'MatterCapabilityReport', ], 'CapabilitySchemas' => [ 'shape' => 'CapabilitySchemas', ], 'DeviceMetadata' => [ 'shape' => 'DeviceMetadata', ], ], ], 'DeviceDiscoveryArn' => [ 'type' => 'string', 'pattern' => 'arn:aws:iotmanagedintegrations:[0-9a-zA-Z-]+:[0-9]+:device-discovery/[0-9a-zA-Z]+', ], 'DeviceDiscoveryId' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[A-Za-z0-9]+', ], 'DeviceDiscoveryListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceDiscoverySummary', ], ], 'DeviceDiscoveryStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'SUCCEEDED', 'FAILED', 'TIMED_OUT', ], ], 'DeviceDiscoverySummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'DeviceDiscoveryId', ], 'DiscoveryType' => [ 'shape' => 'DiscoveryType', ], 'Status' => [ 'shape' => 'DeviceDiscoveryStatus', ], ], ], 'DeviceMetadata' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'DeviceSpecificKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9=_.,@\\+\\-]+', 'sensitive' => true, ], 'DeviceType' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[a-zA-Z0-9=_. ,@\\+\\-/]+', ], 'DeviceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceType', ], ], 'DeviceTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceType', ], 'max' => 50, 'min' => 0, ], 'Devices' => [ 'type' => 'list', 'member' => [ 'shape' => 'Device', ], ], 'DisconnectReasonValue' => [ 'type' => 'string', 'enum' => [ 'AUTH_ERROR', 'CLIENT_INITIATED_DISCONNECT', 'CLIENT_ERROR', 'CONNECTION_LOST', 'DUPLICATE_CLIENTID', 'FORBIDDEN_ACCESS', 'MQTT_KEEP_ALIVE_TIMEOUT', 'SERVER_ERROR', 'SERVER_INITIATED_DISCONNECT', 'THROTTLED', 'WEBSOCKET_TTL_EXPIRATION', 'CUSTOMAUTH_TTL_EXPIRATION', 'UNKNOWN', 'NONE', ], ], 'DiscoveredAt' => [ 'type' => 'timestamp', ], 'DiscoveredDeviceListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'DiscoveredDeviceSummary', ], ], 'DiscoveredDeviceSummary' => [ 'type' => 'structure', 'members' => [ 'ConnectorDeviceId' => [ 'shape' => 'ConnectorDeviceId', ], 'ConnectorDeviceName' => [ 'shape' => 'ConnectorDeviceName', ], 'DeviceTypes' => [ 'shape' => 'DeviceTypeList', ], 'ManagedThingId' => [ 'shape' => 'ManagedThingId', ], 'Modification' => [ 'shape' => 'DiscoveryModification', ], 'DiscoveredAt' => [ 'shape' => 'DiscoveredAt', ], 'Brand' => [ 'shape' => 'Brand', ], 'Model' => [ 'shape' => 'Model', ], 'AuthenticationMaterial' => [ 'shape' => 'AuthMaterialString', ], ], ], 'DiscoveryAuthMaterialString' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[0-9A-Za-z_\\-\\+=\\/:; ]+', 'sensitive' => true, ], 'DiscoveryAuthMaterialType' => [ 'type' => 'string', 'enum' => [ 'ZWAVE_INSTALL_CODE', ], ], 'DiscoveryFinishedAt' => [ 'type' => 'timestamp', ], 'DiscoveryModification' => [ 'type' => 'string', 'enum' => [ 'DISCOVERED', 'UPDATED', 'NO_CHANGE', ], ], 'DiscoveryStartedAt' => [ 'type' => 'timestamp', ], 'DiscoveryType' => [ 'type' => 'string', 'enum' => [ 'ZWAVE', 'ZIGBEE', 'CLOUD', 'CUSTOM', ], ], 'DisplayName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[A-Za-z0-9-_ ]+', ], 'DurationInMinutes' => [ 'type' => 'integer', 'box' => true, 'max' => 1430, 'min' => 1, ], 'EncryptionType' => [ 'type' => 'string', 'enum' => [ 'MANAGED_INTEGRATIONS_DEFAULT_ENCRYPTION', 'CUSTOMER_KEY_ENCRYPTION', ], ], 'EndTime' => [ 'type' => 'string', ], 'EndpointAddress' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[A-Za-z0-9._@-]+', ], 'EndpointConfig' => [ 'type' => 'structure', 'members' => [ 'lambda' => [ 'shape' => 'LambdaConfig', ], ], ], 'EndpointId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[0-9a-zA-Z]+', ], 'EndpointSemanticTag' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[0-9a-zA-Z._-]+', ], 'EndpointType' => [ 'type' => 'string', 'enum' => [ 'LAMBDA', ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'ErrorResourceId' => [ 'type' => 'string', ], 'ErrorResourceType' => [ 'type' => 'string', ], 'EventLogConfigurationListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventLogConfigurationSummary', ], ], 'EventLogConfigurationSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'LogConfigurationId', ], 'ResourceType' => [ 'shape' => 'SmartHomeResourceType', ], 'ResourceId' => [ 'shape' => 'SmartHomeResourceId', ], 'EventLogLevel' => [ 'shape' => 'LogLevel', ], ], ], 'EventName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[/a-zA-Z0-9\\._ ]+', ], 'EventType' => [ 'type' => 'string', 'enum' => [ 'DEVICE_COMMAND', 'DEVICE_COMMAND_REQUEST', 'DEVICE_DISCOVERY_STATUS', 'DEVICE_EVENT', 'DEVICE_LIFE_CYCLE', 'DEVICE_STATE', 'DEVICE_OTA', 'CONNECTOR_ASSOCIATION', 'ACCOUNT_ASSOCIATION', 'CONNECTOR_ERROR_REPORT', ], ], 'ExecutionNumber' => [ 'type' => 'long', 'box' => true, ], 'ExponentialRolloutRate' => [ 'type' => 'structure', 'members' => [ 'BaseRatePerMinute' => [ 'shape' => 'BaseRatePerMinute', ], 'IncrementFactor' => [ 'shape' => 'IncrementFactor', ], 'RateIncreaseCriteria' => [ 'shape' => 'RolloutRateIncreaseCriteria', ], ], ], 'ExtrinsicSchemaId' => [ 'type' => 'string', 'max' => 10, 'min' => 1, 'pattern' => '0[xX][0-9a-fA-F]+$|^[0-9]+', ], 'GetAccountAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'AccountAssociationId', ], 'members' => [ 'AccountAssociationId' => [ 'shape' => 'AccountAssociationId', 'location' => 'uri', 'locationName' => 'AccountAssociationId', ], ], ], 'GetAccountAssociationResponse' => [ 'type' => 'structure', 'required' => [ 'AccountAssociationId', 'AssociationState', 'OAuthAuthorizationUrl', ], 'members' => [ 'AccountAssociationId' => [ 'shape' => 'AccountAssociationId', ], 'AssociationState' => [ 'shape' => 'AssociationState', ], 'ErrorMessage' => [ 'shape' => 'AccountAssociationErrorMessage', ], 'ConnectorDestinationId' => [ 'shape' => 'ConnectorDestinationId', ], 'Name' => [ 'shape' => 'AccountAssociationName', ], 'Description' => [ 'shape' => 'AccountAssociationDescription', ], 'Arn' => [ 'shape' => 'AccountAssociationArn', ], 'OAuthAuthorizationUrl' => [ 'shape' => 'OAuthAuthorizationUrl', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'GetCloudConnectorRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'CloudConnectorId', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'GetCloudConnectorResponse' => [ 'type' => 'structure', 'required' => [ 'Name', 'EndpointConfig', ], 'members' => [ 'Name' => [ 'shape' => 'DisplayName', ], 'EndpointConfig' => [ 'shape' => 'EndpointConfig', ], 'Description' => [ 'shape' => 'CloudConnectorDescription', ], 'EndpointType' => [ 'shape' => 'EndpointType', ], 'Id' => [ 'shape' => 'CloudConnectorId', ], 'Type' => [ 'shape' => 'CloudConnectorType', ], ], ], 'GetConnectorDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'ConnectorDestinationId', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'GetConnectorDestinationResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ConnectorDestinationName', ], 'Description' => [ 'shape' => 'ConnectorDestinationDescription', ], 'CloudConnectorId' => [ 'shape' => 'CloudConnectorId', ], 'Id' => [ 'shape' => 'ConnectorDestinationId', ], 'AuthType' => [ 'shape' => 'AuthType', ], 'AuthConfig' => [ 'shape' => 'AuthConfig', ], 'SecretsManager' => [ 'shape' => 'SecretsManager', ], 'OAuthCompleteRedirectUrl' => [ 'shape' => 'OAuthCompleteRedirectUrl', ], ], ], 'GetCredentialLockerRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'CredentialLockerId', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'GetCredentialLockerResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'CredentialLockerId', ], 'Arn' => [ 'shape' => 'CredentialLockerArn', ], 'Name' => [ 'shape' => 'CredentialLockerName', ], 'CreatedAt' => [ 'shape' => 'CredentialLockerCreatedAt', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'GetCustomEndpointRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetCustomEndpointResponse' => [ 'type' => 'structure', 'required' => [ 'EndpointAddress', ], 'members' => [ 'EndpointAddress' => [ 'shape' => 'EndpointAddress', ], ], ], 'GetDefaultEncryptionConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetDefaultEncryptionConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'configurationStatus', 'encryptionType', ], 'members' => [ 'configurationStatus' => [ 'shape' => 'ConfigurationStatus', ], 'encryptionType' => [ 'shape' => 'EncryptionType', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'GetDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'DestinationName', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'GetDestinationResponse' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'DestinationDescription', ], 'DeliveryDestinationArn' => [ 'shape' => 'DeliveryDestinationArn', ], 'DeliveryDestinationType' => [ 'shape' => 'DeliveryDestinationType', ], 'Name' => [ 'shape' => 'DestinationName', ], 'RoleArn' => [ 'shape' => 'DeliveryDestinationRoleArn', ], 'CreatedAt' => [ 'shape' => 'DestinationCreatedAt', ], 'UpdatedAt' => [ 'shape' => 'DestinationUpdatedAt', ], 'Tags' => [ 'shape' => 'TagsMap', 'deprecated' => true, 'deprecatedMessage' => 'Tags has been deprecated from this api', 'deprecatedSince' => '06-25-2025', ], ], ], 'GetDeviceDiscoveryRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'DeviceDiscoveryId', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'GetDeviceDiscoveryResponse' => [ 'type' => 'structure', 'required' => [ 'Id', 'Arn', 'DiscoveryType', 'Status', 'StartedAt', ], 'members' => [ 'Id' => [ 'shape' => 'DeviceDiscoveryId', ], 'Arn' => [ 'shape' => 'DeviceDiscoveryArn', ], 'DiscoveryType' => [ 'shape' => 'DiscoveryType', ], 'Status' => [ 'shape' => 'DeviceDiscoveryStatus', ], 'StartedAt' => [ 'shape' => 'DiscoveryStartedAt', ], 'ControllerId' => [ 'shape' => 'ManagedThingId', ], 'ConnectorAssociationId' => [ 'shape' => 'ConnectorAssociationId', 'deprecated' => true, 'deprecatedMessage' => 'ConnectorAssociationId has been deprecated', 'deprecatedSince' => '2025-06-25', ], 'AccountAssociationId' => [ 'shape' => 'AccountAssociationId', ], 'FinishedAt' => [ 'shape' => 'DiscoveryFinishedAt', ], 'Tags' => [ 'shape' => 'TagsMap', 'deprecated' => true, 'deprecatedMessage' => 'Tags have been deprecated from this api', 'deprecatedSince' => '06-25-2025', ], ], ], 'GetEventLogConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'LogConfigurationId', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetEventLogConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'LogConfigurationId', ], 'ResourceType' => [ 'shape' => 'SmartHomeResourceType', ], 'ResourceId' => [ 'shape' => 'SmartHomeResourceId', ], 'EventLogLevel' => [ 'shape' => 'LogLevel', ], ], ], 'GetHubConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetHubConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'HubTokenTimerExpirySettingInSeconds' => [ 'shape' => 'HubTokenTimerExpirySettingInSeconds', ], 'UpdatedAt' => [ 'shape' => 'HubConfigurationUpdatedAt', ], ], ], 'GetManagedThingCapabilitiesRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'ManagedThingId', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'GetManagedThingCapabilitiesResponse' => [ 'type' => 'structure', 'members' => [ 'ManagedThingId' => [ 'shape' => 'ManagedThingId', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'CapabilityReport' => [ 'shape' => 'CapabilityReport', ], ], ], 'GetManagedThingConnectivityDataRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'ManagedThingId', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'GetManagedThingConnectivityDataResponse' => [ 'type' => 'structure', 'members' => [ 'ManagedThingId' => [ 'shape' => 'ManagedThingId', ], 'Connected' => [ 'shape' => 'ConnectivityStatus', ], 'Timestamp' => [ 'shape' => 'ConnectivityTimestamp', ], 'DisconnectReason' => [ 'shape' => 'DisconnectReasonValue', ], ], ], 'GetManagedThingMetaDataRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'ManagedThingId', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'GetManagedThingMetaDataResponse' => [ 'type' => 'structure', 'members' => [ 'ManagedThingId' => [ 'shape' => 'ManagedThingId', ], 'MetaData' => [ 'shape' => 'MetaData', ], ], ], 'GetManagedThingRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'ManagedThingId', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'GetManagedThingResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ManagedThingId', ], 'Arn' => [ 'shape' => 'ManagedThingArn', ], 'Owner' => [ 'shape' => 'Owner', ], 'CredentialLockerId' => [ 'shape' => 'CredentialLockerId', ], 'AdvertisedProductId' => [ 'shape' => 'AdvertisedProductId', ], 'Role' => [ 'shape' => 'Role', ], 'ProvisioningStatus' => [ 'shape' => 'ProvisioningStatus', ], 'Name' => [ 'shape' => 'Name', ], 'Model' => [ 'shape' => 'Model', ], 'Brand' => [ 'shape' => 'Brand', ], 'SerialNumber' => [ 'shape' => 'SerialNumber', ], 'UniversalProductCode' => [ 'shape' => 'UniversalProductCode', ], 'InternationalArticleNumber' => [ 'shape' => 'InternationalArticleNumber', ], 'ConnectorPolicyId' => [ 'shape' => 'ConnectorPolicyId', 'deprecated' => true, 'deprecatedMessage' => 'ConnectorPolicyId is deprecated', 'deprecatedSince' => '2025-06-25', ], 'ConnectorDestinationId' => [ 'shape' => 'ConnectorDestinationId', ], 'ConnectorDeviceId' => [ 'shape' => 'ConnectorDeviceId', ], 'DeviceSpecificKey' => [ 'shape' => 'DeviceSpecificKey', ], 'MacAddress' => [ 'shape' => 'MacAddress', ], 'ParentControllerId' => [ 'shape' => 'ParentControllerId', ], 'Classification' => [ 'shape' => 'Classification', ], 'CreatedAt' => [ 'shape' => 'CreatedAt', ], 'UpdatedAt' => [ 'shape' => 'UpdatedAt', ], 'ActivatedAt' => [ 'shape' => 'SetupAt', ], 'HubNetworkMode' => [ 'shape' => 'HubNetworkMode', ], 'MetaData' => [ 'shape' => 'MetaData', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'GetManagedThingStateRequest' => [ 'type' => 'structure', 'required' => [ 'ManagedThingId', ], 'members' => [ 'ManagedThingId' => [ 'shape' => 'ManagedThingId', 'location' => 'uri', 'locationName' => 'ManagedThingId', ], ], ], 'GetManagedThingStateResponse' => [ 'type' => 'structure', 'required' => [ 'Endpoints', ], 'members' => [ 'Endpoints' => [ 'shape' => 'StateEndpoints', ], ], ], 'GetNotificationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'EventType', ], 'members' => [ 'EventType' => [ 'shape' => 'EventType', 'location' => 'uri', 'locationName' => 'EventType', ], ], ], 'GetNotificationConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'EventType' => [ 'shape' => 'EventType', ], 'DestinationName' => [ 'shape' => 'DestinationName', ], 'CreatedAt' => [ 'shape' => 'NotificationConfigurationCreatedAt', ], 'UpdatedAt' => [ 'shape' => 'NotificationConfigurationUpdatedAt', ], 'Tags' => [ 'shape' => 'TagsMap', 'deprecated' => true, 'deprecatedMessage' => 'Tags has been deprecated for this api', 'deprecatedSince' => '06-25-2025', ], ], ], 'GetOtaTaskConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'OtaTaskConfigurationId', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'GetOtaTaskConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'TaskConfigurationId' => [ 'shape' => 'OtaTaskConfigurationId', ], 'Name' => [ 'shape' => 'OtaTaskConfigurationName', ], 'PushConfig' => [ 'shape' => 'PushConfig', ], 'Description' => [ 'shape' => 'OtaDescription', ], 'CreatedAt' => [ 'shape' => 'CreatedAt', ], ], ], 'GetOtaTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'OtaTaskId', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'GetOtaTaskResponse' => [ 'type' => 'structure', 'members' => [ 'TaskId' => [ 'shape' => 'OtaTaskId', ], 'TaskArn' => [ 'shape' => 'OtaTaskArn', ], 'Description' => [ 'shape' => 'OtaDescription', ], 'S3Url' => [ 'shape' => 'S3Url', ], 'Protocol' => [ 'shape' => 'OtaProtocol', ], 'OtaType' => [ 'shape' => 'OtaType', ], 'OtaTargetQueryString' => [ 'shape' => 'OtaTargetQueryString', ], 'OtaMechanism' => [ 'shape' => 'OtaMechanism', ], 'Target' => [ 'shape' => 'Target', ], 'CreatedAt' => [ 'shape' => 'CreatedAt', ], 'LastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], 'TaskConfigurationId' => [ 'shape' => 'OtaTaskConfigurationId', ], 'TaskProcessingDetails' => [ 'shape' => 'TaskProcessingDetails', ], 'OtaSchedulingConfig' => [ 'shape' => 'OtaTaskSchedulingConfig', ], 'OtaTaskExecutionRetryConfig' => [ 'shape' => 'OtaTaskExecutionRetryConfig', ], 'Status' => [ 'shape' => 'OtaStatus', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'GetProvisioningProfileRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'ProvisioningProfileId', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'GetProvisioningProfileResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ProvisioningProfileArn', ], 'Name' => [ 'shape' => 'ProvisioningProfileName', ], 'ProvisioningType' => [ 'shape' => 'ProvisioningType', ], 'Id' => [ 'shape' => 'ProvisioningProfileId', ], 'ClaimCertificate' => [ 'shape' => 'ClaimCertificate', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'GetRuntimeLogConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ManagedThingId', ], 'members' => [ 'ManagedThingId' => [ 'shape' => 'ManagedThingId', 'location' => 'uri', 'locationName' => 'ManagedThingId', ], ], ], 'GetRuntimeLogConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'ManagedThingId' => [ 'shape' => 'ManagedThingId', ], 'RuntimeLogConfigurations' => [ 'shape' => 'RuntimeLogConfigurations', ], ], ], 'GetSchemaVersionRequest' => [ 'type' => 'structure', 'required' => [ 'Type', 'SchemaVersionedId', ], 'members' => [ 'Type' => [ 'shape' => 'SchemaVersionType', 'location' => 'uri', 'locationName' => 'Type', ], 'SchemaVersionedId' => [ 'shape' => 'SchemaVersionedId', 'location' => 'uri', 'locationName' => 'SchemaVersionedId', ], 'Format' => [ 'shape' => 'SchemaVersionFormat', 'location' => 'querystring', 'locationName' => 'Format', ], ], ], 'GetSchemaVersionResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'Type' => [ 'shape' => 'SchemaVersionType', ], 'Description' => [ 'shape' => 'SchemaVersionDescription', ], 'Namespace' => [ 'shape' => 'SchemaVersionNamespaceName', ], 'SemanticVersion' => [ 'shape' => 'SchemaVersionVersion', ], 'Visibility' => [ 'shape' => 'SchemaVersionVisibility', ], 'Schema' => [ 'shape' => 'SchemaVersionSchema', ], ], ], 'HubConfigurationUpdatedAt' => [ 'type' => 'timestamp', ], 'HubNetworkMode' => [ 'type' => 'string', 'enum' => [ 'STANDARD', 'NETWORK_WIDE_EXCLUSION', ], ], 'HubTokenTimerExpirySettingInSeconds' => [ 'type' => 'long', 'box' => true, 'min' => 1, ], 'InProgressTimeoutInMinutes' => [ 'type' => 'long', 'box' => true, 'max' => 10080, 'min' => 1, ], 'IncrementFactor' => [ 'type' => 'double', 'box' => true, 'max' => 5, 'min' => 1.1, ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalFailureException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InternationalArticleNumber' => [ 'type' => 'string', 'max' => 13, 'min' => 8, 'pattern' => '[0-9]+', 'sensitive' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'IoTManagedIntegrationsResourceARN' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => 'arn:aws:iotmanagedintegrations:[0-9a-zA-Z-]+:[0-9]+:(managed-thing|provisioning-profile|ota-task|credential-locker|account-association)/[0-9a-zA-Z]+', ], 'KmsKeyArn' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => 'arn:aws:kms:[0-9a-zA-Z-]+:[0-9]+:key/[0-9a-zA-Z-]+', ], 'LambdaArn' => [ 'type' => 'string', 'pattern' => '(arn:aws:lambda:[0-9a-zA-Z-]+:[0-9]+:function:)?([a-zA-Z0-9-_]+(:(\\$LATEST|[a-zA-Z0-9-_]+))?)', ], 'LambdaConfig' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'LambdaArn', ], ], ], 'LastUpdatedAt' => [ 'type' => 'timestamp', ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 410, 'senderFault' => true, ], 'exception' => true, ], 'ListAccountAssociationsRequest' => [ 'type' => 'structure', 'members' => [ 'ConnectorDestinationId' => [ 'shape' => 'ConnectorDestinationId', 'location' => 'querystring', 'locationName' => 'ConnectorDestinationId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListAccountAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'AccountAssociationListDefinition', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCloudConnectorsRequest' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'CloudConnectorType', 'location' => 'querystring', 'locationName' => 'Type', ], 'LambdaArn' => [ 'shape' => 'LambdaArn', 'location' => 'querystring', 'locationName' => 'LambdaArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListCloudConnectorsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'ConnectorList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListConnectorDestinationsRequest' => [ 'type' => 'structure', 'members' => [ 'CloudConnectorId' => [ 'shape' => 'CloudConnectorId', 'location' => 'querystring', 'locationName' => 'CloudConnectorId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListConnectorDestinationsResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectorDestinationList' => [ 'shape' => 'ConnectorDestinationListDefinition', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCredentialLockersRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListCredentialLockersResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'CredentialLockerListDefinition', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDestinationsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListDestinationsResponse' => [ 'type' => 'structure', 'members' => [ 'DestinationList' => [ 'shape' => 'DestinationListDefinition', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDeviceDiscoveriesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'TypeFilter' => [ 'shape' => 'DiscoveryType', 'location' => 'querystring', 'locationName' => 'TypeFilter', ], 'StatusFilter' => [ 'shape' => 'DeviceDiscoveryStatus', 'location' => 'querystring', 'locationName' => 'StatusFilter', ], ], ], 'ListDeviceDiscoveriesResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'DeviceDiscoveryListDefinition', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDiscoveredDevicesRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'DeviceDiscoveryId', 'location' => 'uri', 'locationName' => 'Identifier', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListDiscoveredDevicesResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'DiscoveredDeviceListDefinition', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEventLogConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListEventLogConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'EventLogConfigurationList' => [ 'shape' => 'EventLogConfigurationListDefinition', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListManagedThingAccountAssociationsRequest' => [ 'type' => 'structure', 'members' => [ 'ManagedThingId' => [ 'shape' => 'ManagedThingId', 'location' => 'querystring', 'locationName' => 'ManagedThingId', ], 'AccountAssociationId' => [ 'shape' => 'AccountAssociationId', 'location' => 'querystring', 'locationName' => 'AccountAssociationId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListManagedThingAccountAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'ManagedThingAssociationList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListManagedThingSchemasRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'ManagedThingId', 'location' => 'uri', 'locationName' => 'Identifier', ], 'EndpointIdFilter' => [ 'shape' => 'EndpointId', 'location' => 'querystring', 'locationName' => 'EndpointIdFilter', ], 'CapabilityIdFilter' => [ 'shape' => 'CapabilityId', 'location' => 'querystring', 'locationName' => 'CapabilityIdFilter', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListManagedThingSchemasResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'ManagedThingSchemaListDefinition', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListManagedThingsRequest' => [ 'type' => 'structure', 'members' => [ 'OwnerFilter' => [ 'shape' => 'Owner', 'location' => 'querystring', 'locationName' => 'OwnerFilter', ], 'CredentialLockerFilter' => [ 'shape' => 'CredentialLockerId', 'location' => 'querystring', 'locationName' => 'CredentialLockerFilter', ], 'RoleFilter' => [ 'shape' => 'Role', 'location' => 'querystring', 'locationName' => 'RoleFilter', ], 'ParentControllerIdentifierFilter' => [ 'shape' => 'ParentControllerId', 'location' => 'querystring', 'locationName' => 'ParentControllerIdentifierFilter', ], 'ConnectorPolicyIdFilter' => [ 'shape' => 'ConnectorPolicyId', 'deprecated' => true, 'deprecatedMessage' => 'ConnectorPolicyIdFilter is deprecated', 'deprecatedSince' => '06-25-2025', 'location' => 'querystring', 'locationName' => 'ConnectorPolicyIdFilter', ], 'ConnectorDestinationIdFilter' => [ 'shape' => 'ConnectorDestinationId', 'location' => 'querystring', 'locationName' => 'ConnectorDestinationIdFilter', ], 'ConnectorDeviceIdFilter' => [ 'shape' => 'ConnectorDeviceId', 'location' => 'querystring', 'locationName' => 'ConnectorDeviceIdFilter', ], 'SerialNumberFilter' => [ 'shape' => 'SerialNumber', 'location' => 'querystring', 'locationName' => 'SerialNumberFilter', ], 'ProvisioningStatusFilter' => [ 'shape' => 'ProvisioningStatus', 'location' => 'querystring', 'locationName' => 'ProvisioningStatusFilter', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListManagedThingsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'ManagedThingListDefinition', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListNotificationConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListNotificationConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'NotificationConfigurationList' => [ 'shape' => 'NotificationConfigurationListDefinition', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListOtaTaskConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListOtaTaskConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'OtaTaskConfigurationListDefinition', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListOtaTaskExecutionsRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'OtaTaskId', 'location' => 'uri', 'locationName' => 'Identifier', ], 'NextToken' => [ 'shape' => 'OtaNextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListOtaTaskExecutionsResponse' => [ 'type' => 'structure', 'members' => [ 'ExecutionSummaries' => [ 'shape' => 'OtaTaskExecutionSummariesListDefinition', ], 'NextToken' => [ 'shape' => 'OtaNextToken', ], ], ], 'ListOtaTasksRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'OtaNextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListOtaTasksResponse' => [ 'type' => 'structure', 'members' => [ 'Tasks' => [ 'shape' => 'OtaTaskListDefinition', ], 'NextToken' => [ 'shape' => 'OtaNextToken', ], ], ], 'ListProvisioningProfilesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListProvisioningProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'ProvisioningProfileListDefinition', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSchemaVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Type' => [ 'shape' => 'SchemaVersionType', 'location' => 'uri', 'locationName' => 'Type', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'SchemaId' => [ 'shape' => 'SchemaId', 'location' => 'querystring', 'locationName' => 'SchemaIdFilter', ], 'Namespace' => [ 'shape' => 'SchemaVersionNamespaceName', 'location' => 'querystring', 'locationName' => 'NamespaceFilter', ], 'Visibility' => [ 'shape' => 'SchemaVersionVisibility', 'location' => 'querystring', 'locationName' => 'VisibilityFilter', ], 'SemanticVersion' => [ 'shape' => 'SchemaVersionVersion', 'location' => 'querystring', 'locationName' => 'SemanticVersionFilter', ], ], ], 'ListSchemaVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'SchemaVersionList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'IoTManagedIntegrationsResourceARN', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'LocalStoreFileRotationMaxBytes' => [ 'type' => 'integer', 'box' => true, ], 'LocalStoreFileRotationMaxFiles' => [ 'type' => 'integer', 'box' => true, ], 'LocalStoreLocation' => [ 'type' => 'string', ], 'LogConfigurationId' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[A-Za-z0-9]+', ], 'LogLevel' => [ 'type' => 'string', 'enum' => [ 'DEBUG', 'ERROR', 'INFO', 'WARN', ], ], 'MacAddress' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'sensitive' => true, ], 'ManagedThingArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 32, 'pattern' => 'arn:aws:iotmanagedintegrations:[0-9a-zA-Z-]+:[0-9]+:managed-thing/([0-9a-zA-Z:_-])+', ], 'ManagedThingAssociation' => [ 'type' => 'structure', 'members' => [ 'ManagedThingId' => [ 'shape' => 'ManagedThingId', ], 'AccountAssociationId' => [ 'shape' => 'AccountAssociationId', ], ], ], 'ManagedThingAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManagedThingAssociation', ], ], 'ManagedThingId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9:_-]*', ], 'ManagedThingListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManagedThingSummary', ], ], 'ManagedThingSchemaListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManagedThingSchemaListItem', ], ], 'ManagedThingSchemaListItem' => [ 'type' => 'structure', 'members' => [ 'EndpointId' => [ 'shape' => 'EndpointId', ], 'CapabilityId' => [ 'shape' => 'CapabilityId', ], 'Schema' => [ 'shape' => 'ValidationSchema', ], ], ], 'ManagedThingSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ManagedThingId', ], 'Arn' => [ 'shape' => 'ManagedThingArn', ], 'AdvertisedProductId' => [ 'shape' => 'AdvertisedProductId', ], 'Brand' => [ 'shape' => 'Brand', ], 'Classification' => [ 'shape' => 'Classification', ], 'ConnectorDeviceId' => [ 'shape' => 'ConnectorDeviceId', ], 'ConnectorPolicyId' => [ 'shape' => 'ConnectorPolicyId', 'deprecated' => true, 'deprecatedMessage' => 'ConnectorPolicyId has been deprecated', 'deprecatedSince' => '06-25-2025', ], 'ConnectorDestinationId' => [ 'shape' => 'ConnectorDestinationId', ], 'Model' => [ 'shape' => 'Model', ], 'Name' => [ 'shape' => 'Name', ], 'Owner' => [ 'shape' => 'Owner', ], 'CredentialLockerId' => [ 'shape' => 'CredentialLockerId', ], 'ParentControllerId' => [ 'shape' => 'ParentControllerId', ], 'ProvisioningStatus' => [ 'shape' => 'ProvisioningStatus', ], 'Role' => [ 'shape' => 'Role', ], 'SerialNumber' => [ 'shape' => 'SerialNumber', ], 'CreatedAt' => [ 'shape' => 'CreatedAt', ], 'UpdatedAt' => [ 'shape' => 'CreatedAt', ], 'ActivatedAt' => [ 'shape' => 'SetupAt', ], ], ], 'MatterAttributeId' => [ 'type' => 'string', 'max' => 24, 'min' => 1, 'pattern' => '0[xX][0-9a-fA-F]+$|^[0-9]+', ], 'MatterAttributes' => [ 'type' => 'structure', 'members' => [], 'document' => true, 'sensitive' => true, ], 'MatterCapabilityReport' => [ 'type' => 'structure', 'required' => [ 'version', 'endpoints', ], 'members' => [ 'version' => [ 'shape' => 'CapabilityReportVersion', ], 'nodeId' => [ 'shape' => 'NodeId', ], 'endpoints' => [ 'shape' => 'MatterCapabilityReportEndpoints', ], ], ], 'MatterCapabilityReportAttribute' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'MatterAttributeId', ], 'name' => [ 'shape' => 'ActionName', ], 'value' => [ 'shape' => 'MatterCapabilityReportAttributeValue', ], ], ], 'MatterCapabilityReportAttributeValue' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'MatterCapabilityReportAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatterCapabilityReportAttribute', ], 'max' => 100, 'min' => 0, ], 'MatterCapabilityReportCluster' => [ 'type' => 'structure', 'required' => [ 'id', 'revision', ], 'members' => [ 'id' => [ 'shape' => 'ClusterId', ], 'revision' => [ 'shape' => 'MatterCapabilityReportClusterRevisionId', ], 'publicId' => [ 'shape' => 'SchemaVersionedId', ], 'name' => [ 'shape' => 'CapabilityName', ], 'specVersion' => [ 'shape' => 'SpecVersion', ], 'attributes' => [ 'shape' => 'MatterCapabilityReportAttributes', ], 'commands' => [ 'shape' => 'MatterCapabilityReportCommands', ], 'events' => [ 'shape' => 'MatterCapabilityReportEvents', ], 'featureMap' => [ 'shape' => 'MatterCapabilityReportFeatureMap', ], 'generatedCommands' => [ 'shape' => 'MatterCapabilityReportGeneratedCommands', ], 'fabricIndex' => [ 'shape' => 'MatterCapabilityReportFabricIndex', ], ], ], 'MatterCapabilityReportClusterRevisionId' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 1, ], 'MatterCapabilityReportClusters' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatterCapabilityReportCluster', ], 'max' => 50, 'min' => 0, ], 'MatterCapabilityReportCommands' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatterCommandId', ], 'max' => 100, 'min' => 0, ], 'MatterCapabilityReportEndpoint' => [ 'type' => 'structure', 'required' => [ 'id', 'deviceTypes', 'clusters', ], 'members' => [ 'id' => [ 'shape' => 'EndpointId', ], 'deviceTypes' => [ 'shape' => 'DeviceTypes', ], 'clusters' => [ 'shape' => 'MatterCapabilityReportClusters', ], 'parts' => [ 'shape' => 'MatterCapabilityReportEndpointParts', ], 'semanticTags' => [ 'shape' => 'MatterCapabilityReportEndpointSemanticTags', ], 'clientClusters' => [ 'shape' => 'MatterCapabilityReportEndpointClientClusters', ], ], ], 'MatterCapabilityReportEndpointClientClusters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ClusterId', ], 'max' => 32, 'min' => 0, ], 'MatterCapabilityReportEndpointParts' => [ 'type' => 'list', 'member' => [ 'shape' => 'EndpointId', ], 'max' => 32, 'min' => 0, ], 'MatterCapabilityReportEndpointSemanticTags' => [ 'type' => 'list', 'member' => [ 'shape' => 'EndpointSemanticTag', ], 'max' => 32, 'min' => 0, ], 'MatterCapabilityReportEndpoints' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatterCapabilityReportEndpoint', ], 'max' => 50, 'min' => 0, ], 'MatterCapabilityReportEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatterEventId', ], 'max' => 100, 'min' => 0, ], 'MatterCapabilityReportFabricIndex' => [ 'type' => 'integer', 'box' => true, 'max' => 4096, 'min' => 0, ], 'MatterCapabilityReportFeatureMap' => [ 'type' => 'long', 'box' => true, 'max' => 4294967295, 'min' => 0, ], 'MatterCapabilityReportGeneratedCommands' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatterCommandId', ], 'max' => 50, 'min' => 0, ], 'MatterCluster' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ClusterId', ], 'attributes' => [ 'shape' => 'MatterAttributes', ], 'commands' => [ 'shape' => 'MatterCommands', ], 'events' => [ 'shape' => 'MatterEvents', ], ], ], 'MatterClusters' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatterCluster', ], 'max' => 5, 'min' => 1, ], 'MatterCommandId' => [ 'type' => 'string', 'max' => 24, 'min' => 1, 'pattern' => '0[xX][0-9a-fA-F]+$|^[0-9]+', ], 'MatterCommands' => [ 'type' => 'map', 'key' => [ 'shape' => 'MatterCommandId', ], 'value' => [ 'shape' => 'MatterFields', ], 'max' => 5, 'min' => 1, ], 'MatterEndpoint' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'EndpointId', ], 'clusters' => [ 'shape' => 'MatterClusters', ], ], ], 'MatterEventId' => [ 'type' => 'string', 'max' => 24, 'min' => 1, 'pattern' => '0[xX][0-9a-fA-F]+$|^[0-9]+', ], 'MatterEvents' => [ 'type' => 'map', 'key' => [ 'shape' => 'MatterEventId', ], 'value' => [ 'shape' => 'MatterFields', ], 'max' => 5, 'min' => 1, ], 'MatterFields' => [ 'type' => 'structure', 'members' => [], 'document' => true, 'sensitive' => true, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'MaximumPerMinute' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'MetaData' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeName', ], 'value' => [ 'shape' => 'AttributeValue', ], 'max' => 50, 'min' => 1, ], 'MinNumberOfExecutedThings' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MinNumberOfRetries' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 0, ], 'Model' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[A-Za-z0-9-_ ]+', 'sensitive' => true, ], 'Name' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\p{L}\\p{N} ._-]+', ], 'NextToken' => [ 'type' => 'string', 'max' => 65535, 'min' => 1, 'pattern' => '[a-zA-Z0-9=_-]+', ], 'NodeId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9=_.,@\\+\\-/]+', ], 'NotificationConfigurationCreatedAt' => [ 'type' => 'timestamp', ], 'NotificationConfigurationListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'NotificationConfigurationSummary', ], ], 'NotificationConfigurationSummary' => [ 'type' => 'structure', 'members' => [ 'EventType' => [ 'shape' => 'EventType', ], 'DestinationName' => [ 'shape' => 'DestinationName', ], ], ], 'NotificationConfigurationUpdatedAt' => [ 'type' => 'timestamp', ], 'NumberOfNotifiedThings' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'NumberOfSucceededThings' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'OAuthAuthorizationUrl' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '(https)://.*', 'sensitive' => true, ], 'OAuthCompleteRedirectUrl' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '(http|https)://.*', ], 'OAuthConfig' => [ 'type' => 'structure', 'required' => [ 'authUrl', 'tokenUrl', 'tokenEndpointAuthenticationScheme', ], 'members' => [ 'authUrl' => [ 'shape' => 'AuthUrl', ], 'tokenUrl' => [ 'shape' => 'TokenUrl', ], 'scope' => [ 'shape' => 'String', ], 'tokenEndpointAuthenticationScheme' => [ 'shape' => 'TokenEndpointAuthenticationScheme', ], 'oAuthCompleteRedirectUrl' => [ 'shape' => 'String', ], 'proactiveRefreshTokenRenewal' => [ 'shape' => 'ProactiveRefreshTokenRenewal', ], ], ], 'OAuthUpdate' => [ 'type' => 'structure', 'members' => [ 'oAuthCompleteRedirectUrl' => [ 'shape' => 'String', ], 'proactiveRefreshTokenRenewal' => [ 'shape' => 'ProactiveRefreshTokenRenewal', ], ], ], 'OtaDescription' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[0-9A-Za-z_\\- ]+', ], 'OtaMechanism' => [ 'type' => 'string', 'enum' => [ 'PUSH', ], ], 'OtaNextToken' => [ 'type' => 'string', 'max' => 65535, 'min' => 1, 'pattern' => '[a-zA-Z0-9=_+/-]+', ], 'OtaProtocol' => [ 'type' => 'string', 'enum' => [ 'HTTP', ], ], 'OtaStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'CANCELED', 'COMPLETED', 'DELETION_IN_PROGRESS', 'SCHEDULED', ], ], 'OtaTargetQueryString' => [ 'type' => 'string', ], 'OtaTaskAbortConfig' => [ 'type' => 'structure', 'members' => [ 'AbortConfigCriteriaList' => [ 'shape' => 'AbortConfigCriteriaList', ], ], ], 'OtaTaskArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 32, 'pattern' => 'arn:aws:iotmanagedintegrations:[0-9a-zA-Z-]+:[0-9]+:ota-task/[0-9a-zA-Z]+', ], 'OtaTaskConfigurationId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9]*', ], 'OtaTaskConfigurationListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'OtaTaskConfigurationSummary', ], ], 'OtaTaskConfigurationName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[A-Za-z0-9-_ ]+', 'sensitive' => true, ], 'OtaTaskConfigurationSummary' => [ 'type' => 'structure', 'members' => [ 'TaskConfigurationId' => [ 'shape' => 'OtaTaskConfigurationId', ], 'Name' => [ 'shape' => 'OtaTaskConfigurationName', ], 'CreatedAt' => [ 'shape' => 'CreatedAt', ], ], ], 'OtaTaskExecutionRetryConfig' => [ 'type' => 'structure', 'members' => [ 'RetryConfigCriteria' => [ 'shape' => 'RetryConfigCriteriaList', ], ], ], 'OtaTaskExecutionRolloutConfig' => [ 'type' => 'structure', 'members' => [ 'ExponentialRolloutRate' => [ 'shape' => 'ExponentialRolloutRate', ], 'MaximumPerMinute' => [ 'shape' => 'MaximumPerMinute', ], ], ], 'OtaTaskExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'QUEUED', 'IN_PROGRESS', 'SUCCEEDED', 'FAILED', 'TIMED_OUT', 'REJECTED', 'REMOVED', 'CANCELED', ], ], 'OtaTaskExecutionSummaries' => [ 'type' => 'structure', 'members' => [ 'TaskExecutionSummary' => [ 'shape' => 'OtaTaskExecutionSummary', ], 'ManagedThingId' => [ 'shape' => 'ManagedThingId', ], ], ], 'OtaTaskExecutionSummariesListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'OtaTaskExecutionSummaries', ], ], 'OtaTaskExecutionSummary' => [ 'type' => 'structure', 'members' => [ 'ExecutionNumber' => [ 'shape' => 'ExecutionNumber', ], 'LastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], 'QueuedAt' => [ 'shape' => 'QueuedAt', ], 'RetryAttempt' => [ 'shape' => 'RetryAttempt', ], 'StartedAt' => [ 'shape' => 'StartedAt', ], 'Status' => [ 'shape' => 'OtaTaskExecutionStatus', ], ], ], 'OtaTaskId' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[A-Za-z0-9]+', ], 'OtaTaskListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'OtaTaskSummary', ], ], 'OtaTaskSchedulingConfig' => [ 'type' => 'structure', 'members' => [ 'EndBehavior' => [ 'shape' => 'SchedulingConfigEndBehavior', ], 'EndTime' => [ 'shape' => 'EndTime', ], 'MaintenanceWindows' => [ 'shape' => 'ScheduleMaintenanceWindowList', ], 'StartTime' => [ 'shape' => 'ScheduleStartTime', ], ], ], 'OtaTaskSummary' => [ 'type' => 'structure', 'members' => [ 'TaskId' => [ 'shape' => 'OtaTaskId', ], 'TaskArn' => [ 'shape' => 'OtaTaskArn', ], 'CreatedAt' => [ 'shape' => 'CreatedAt', ], 'LastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], 'TaskConfigurationId' => [ 'shape' => 'OtaTaskConfigurationId', ], 'Status' => [ 'shape' => 'OtaStatus', ], ], ], 'OtaTaskTimeoutConfig' => [ 'type' => 'structure', 'members' => [ 'InProgressTimeoutInMinutes' => [ 'shape' => 'InProgressTimeoutInMinutes', ], ], ], 'OtaType' => [ 'type' => 'string', 'enum' => [ 'ONE_TIME', 'CONTINUOUS', ], ], 'Owner' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_.,@-]+', 'sensitive' => true, ], 'ParentControllerId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[A-Za-z0-9]+', ], 'ProactiveRefreshTokenRenewal' => [ 'type' => 'structure', 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], 'DaysBeforeRenewal' => [ 'shape' => 'ProactiveRefreshTokenRenewalDaysBeforeRenewalInteger', ], ], ], 'ProactiveRefreshTokenRenewalDaysBeforeRenewalInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 30, ], 'PropertyName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[/a-zA-Z0-9\\._ ]+', ], 'ProvisioningProfileArn' => [ 'type' => 'string', 'max' => 64, 'min' => 32, 'pattern' => 'arn:aws:iotmanagedintegrations:[0-9a-zA-Z-]+:[0-9]+:provisioning-profile/[0-9a-zA-Z]+', ], 'ProvisioningProfileId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[A-Za-z0-9-_]+', ], 'ProvisioningProfileListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProvisioningProfileSummary', ], ], 'ProvisioningProfileName' => [ 'type' => 'string', 'max' => 36, 'min' => 1, 'pattern' => '[0-9A-Za-z_-]+', ], 'ProvisioningProfileSummary' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ProvisioningProfileName', ], 'Id' => [ 'shape' => 'ProvisioningProfileId', ], 'Arn' => [ 'shape' => 'ProvisioningProfileArn', ], 'ProvisioningType' => [ 'shape' => 'ProvisioningType', ], ], ], 'ProvisioningStatus' => [ 'type' => 'string', 'enum' => [ 'UNASSOCIATED', 'PRE_ASSOCIATED', 'DISCOVERED', 'ACTIVATED', 'DELETION_FAILED', 'DELETE_IN_PROGRESS', 'ISOLATED', 'DELETED', ], ], 'ProvisioningType' => [ 'type' => 'string', 'enum' => [ 'FLEET_PROVISIONING', 'JITR', ], ], 'PushConfig' => [ 'type' => 'structure', 'members' => [ 'AbortConfig' => [ 'shape' => 'OtaTaskAbortConfig', ], 'RolloutConfig' => [ 'shape' => 'OtaTaskExecutionRolloutConfig', ], 'TimeoutConfig' => [ 'shape' => 'OtaTaskTimeoutConfig', ], ], ], 'PutDefaultEncryptionConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'encryptionType', ], 'members' => [ 'encryptionType' => [ 'shape' => 'EncryptionType', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'PutDefaultEncryptionConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'configurationStatus', 'encryptionType', ], 'members' => [ 'configurationStatus' => [ 'shape' => 'ConfigurationStatus', ], 'encryptionType' => [ 'shape' => 'EncryptionType', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'PutHubConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'HubTokenTimerExpirySettingInSeconds', ], 'members' => [ 'HubTokenTimerExpirySettingInSeconds' => [ 'shape' => 'HubTokenTimerExpirySettingInSeconds', ], ], ], 'PutHubConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'HubTokenTimerExpirySettingInSeconds' => [ 'shape' => 'HubTokenTimerExpirySettingInSeconds', ], ], ], 'PutRuntimeLogConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ManagedThingId', 'RuntimeLogConfigurations', ], 'members' => [ 'ManagedThingId' => [ 'shape' => 'ManagedThingId', 'location' => 'uri', 'locationName' => 'ManagedThingId', ], 'RuntimeLogConfigurations' => [ 'shape' => 'RuntimeLogConfigurations', ], ], ], 'QueuedAt' => [ 'type' => 'timestamp', ], 'RegisterAccountAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'ManagedThingId', 'AccountAssociationId', 'DeviceDiscoveryId', ], 'members' => [ 'ManagedThingId' => [ 'shape' => 'ManagedThingId', ], 'AccountAssociationId' => [ 'shape' => 'AccountAssociationId', ], 'DeviceDiscoveryId' => [ 'shape' => 'DeviceDiscoveryId', ], ], ], 'RegisterAccountAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'AccountAssociationId' => [ 'shape' => 'AccountAssociationId', ], 'DeviceDiscoveryId' => [ 'shape' => 'DeviceDiscoveryId', ], 'ManagedThingId' => [ 'shape' => 'ManagedThingId', ], ], ], 'RegisterCustomEndpointRequest' => [ 'type' => 'structure', 'members' => [], ], 'RegisterCustomEndpointResponse' => [ 'type' => 'structure', 'required' => [ 'EndpointAddress', ], 'members' => [ 'EndpointAddress' => [ 'shape' => 'EndpointAddress', ], ], ], 'ResetRuntimeLogConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ManagedThingId', ], 'members' => [ 'ManagedThingId' => [ 'shape' => 'ManagedThingId', 'location' => 'uri', 'locationName' => 'ManagedThingId', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], 'ResourceId' => [ 'shape' => 'ErrorResourceId', ], 'ResourceType' => [ 'shape' => 'ErrorResourceType', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RetryAttempt' => [ 'type' => 'integer', 'box' => true, ], 'RetryConfigCriteria' => [ 'type' => 'structure', 'members' => [ 'FailureType' => [ 'shape' => 'RetryCriteriaFailureType', ], 'MinNumberOfRetries' => [ 'shape' => 'MinNumberOfRetries', ], ], ], 'RetryConfigCriteriaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RetryConfigCriteria', ], ], 'RetryCriteriaFailureType' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'TIMED_OUT', 'ALL', ], ], 'Role' => [ 'type' => 'string', 'enum' => [ 'CONTROLLER', 'DEVICE', ], ], 'RolloutRateIncreaseCriteria' => [ 'type' => 'structure', 'members' => [ 'numberOfNotifiedThings' => [ 'shape' => 'NumberOfNotifiedThings', ], 'numberOfSucceededThings' => [ 'shape' => 'NumberOfSucceededThings', ], ], ], 'RuntimeLogConfigurations' => [ 'type' => 'structure', 'members' => [ 'LogLevel' => [ 'shape' => 'LogLevel', ], 'LogFlushLevel' => [ 'shape' => 'LogLevel', ], 'LocalStoreLocation' => [ 'shape' => 'LocalStoreLocation', ], 'LocalStoreFileRotationMaxFiles' => [ 'shape' => 'LocalStoreFileRotationMaxFiles', ], 'LocalStoreFileRotationMaxBytes' => [ 'shape' => 'LocalStoreFileRotationMaxBytes', ], 'UploadLog' => [ 'shape' => 'UploadLog', ], 'UploadPeriodMinutes' => [ 'shape' => 'UploadPeriodMinutes', ], 'DeleteLocalStoreAfterUpload' => [ 'shape' => 'DeleteLocalStoreAfterUpload', ], ], ], 'S3Url' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'ScheduleMaintenanceWindow' => [ 'type' => 'structure', 'members' => [ 'DurationInMinutes' => [ 'shape' => 'DurationInMinutes', ], 'StartTime' => [ 'shape' => 'StartTime', ], ], ], 'ScheduleMaintenanceWindowList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScheduleMaintenanceWindow', ], ], 'ScheduleStartTime' => [ 'type' => 'string', ], 'SchedulingConfigEndBehavior' => [ 'type' => 'string', 'enum' => [ 'STOP_ROLLOUT', 'CANCEL', 'FORCE_CANCEL', ], ], 'SchemaId' => [ 'type' => 'string', 'max' => 128, 'min' => 3, 'pattern' => '[a-zA-Z0-9.]+', ], 'SchemaVersionDescription' => [ 'type' => 'string', 'max' => 2048, 'min' => 10, 'pattern' => '[a-zA-Z0-9.,/ -]+', ], 'SchemaVersionFormat' => [ 'type' => 'string', 'enum' => [ 'AWS', 'ZCL', 'CONNECTOR', ], ], 'SchemaVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaVersionListItem', ], ], 'SchemaVersionListItem' => [ 'type' => 'structure', 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'Type' => [ 'shape' => 'SchemaVersionType', ], 'Description' => [ 'shape' => 'SchemaVersionDescription', ], 'Namespace' => [ 'shape' => 'SchemaVersionNamespaceName', ], 'SemanticVersion' => [ 'shape' => 'SchemaVersionVersion', ], 'Visibility' => [ 'shape' => 'SchemaVersionVisibility', ], ], ], 'SchemaVersionNamespaceName' => [ 'type' => 'string', 'max' => 12, 'min' => 3, 'pattern' => '[a-z0-9]+', ], 'SchemaVersionSchema' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'SchemaVersionType' => [ 'type' => 'string', 'enum' => [ 'capability', 'definition', ], ], 'SchemaVersionVersion' => [ 'type' => 'string', 'max' => 12, 'min' => 3, 'pattern' => '(\\d+\\.\\d+(\\.\\d+)?|\\$latest)', ], 'SchemaVersionVisibility' => [ 'type' => 'string', 'enum' => [ 'PUBLIC', 'PRIVATE', ], ], 'SchemaVersionedId' => [ 'type' => 'string', 'max' => 128, 'min' => 7, 'pattern' => '[a-zA-Z0-9.]+@(\\d+\\.\\d+(\\.\\d+)?|\\$latest)', ], 'SecretsManager' => [ 'type' => 'structure', 'required' => [ 'arn', 'versionId', ], 'members' => [ 'arn' => [ 'shape' => 'SecretsManagerArn', ], 'versionId' => [ 'shape' => 'SecretsManagerVersionId', ], ], ], 'SecretsManagerArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws:secretsmanager:[0-9a-zA-Z-]{1,32}:\\d{12}:secret:[A-Za-z0-9/_+=.@-]{8,520}', ], 'SecretsManagerVersionId' => [ 'type' => 'string', 'max' => 64, 'min' => 32, 'pattern' => '[a-zA-Z0-9-_]+', ], 'SendConnectorEventRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectorId', 'Operation', ], 'members' => [ 'ConnectorId' => [ 'shape' => 'ConnectorId', 'location' => 'uri', 'locationName' => 'ConnectorId', ], 'UserId' => [ 'shape' => 'ThirdPartyUserId', ], 'Operation' => [ 'shape' => 'ConnectorEventOperation', ], 'OperationVersion' => [ 'shape' => 'ConnectorEventOperationVersion', ], 'StatusCode' => [ 'shape' => 'ConnectorEventStatusCode', ], 'Message' => [ 'shape' => 'ConnectorEventMessage', ], 'DeviceDiscoveryId' => [ 'shape' => 'DeviceDiscoveryId', ], 'ConnectorDeviceId' => [ 'shape' => 'ConnectorDeviceId', ], 'TraceId' => [ 'shape' => 'TraceId', ], 'Devices' => [ 'shape' => 'Devices', ], 'MatterEndpoint' => [ 'shape' => 'MatterEndpoint', ], ], ], 'SendConnectorEventResponse' => [ 'type' => 'structure', 'required' => [ 'ConnectorId', ], 'members' => [ 'ConnectorId' => [ 'shape' => 'ConnectorId', ], ], ], 'SendManagedThingCommandRequest' => [ 'type' => 'structure', 'required' => [ 'ManagedThingId', 'Endpoints', ], 'members' => [ 'ManagedThingId' => [ 'shape' => 'ManagedThingId', 'location' => 'uri', 'locationName' => 'ManagedThingId', ], 'Endpoints' => [ 'shape' => 'CommandEndpoints', ], 'ConnectorAssociationId' => [ 'shape' => 'ConnectorAssociationId', 'deprecated' => true, 'deprecatedMessage' => 'ConnectorAssociationId has been deprecated', 'deprecatedSince' => '06-25-2025', ], 'AccountAssociationId' => [ 'shape' => 'AccountAssociationId', ], ], ], 'SendManagedThingCommandResponse' => [ 'type' => 'structure', 'members' => [ 'TraceId' => [ 'shape' => 'TraceId', ], ], ], 'SerialNumber' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[A-Za-z0-9-_ ]+', 'sensitive' => true, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], 'SetupAt' => [ 'type' => 'timestamp', ], 'SmartHomeResourceId' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[a-zA-Z0-9+*]*', ], 'SmartHomeResourceType' => [ 'type' => 'string', 'pattern' => '[*]$|^(managed-thing|credential-locker|provisioning-profile|ota-task|account-association)', ], 'SpecVersion' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '\\d+\\.\\d+', ], 'StartAccountAssociationRefreshRequest' => [ 'type' => 'structure', 'required' => [ 'AccountAssociationId', ], 'members' => [ 'AccountAssociationId' => [ 'shape' => 'AccountAssociationId', 'location' => 'uri', 'locationName' => 'AccountAssociationId', ], ], ], 'StartAccountAssociationRefreshResponse' => [ 'type' => 'structure', 'required' => [ 'OAuthAuthorizationUrl', ], 'members' => [ 'OAuthAuthorizationUrl' => [ 'shape' => 'OAuthAuthorizationUrl', ], ], ], 'StartDeviceDiscoveryRequest' => [ 'type' => 'structure', 'required' => [ 'DiscoveryType', ], 'members' => [ 'DiscoveryType' => [ 'shape' => 'DiscoveryType', ], 'CustomProtocolDetail' => [ 'shape' => 'CustomProtocolDetail', ], 'ControllerIdentifier' => [ 'shape' => 'ManagedThingId', ], 'ConnectorAssociationIdentifier' => [ 'shape' => 'ConnectorAssociationId', 'deprecated' => true, 'deprecatedMessage' => 'ConnectorAssociationIdentifier is deprecated', 'deprecatedSince' => '06-25-2025', ], 'AccountAssociationId' => [ 'shape' => 'AccountAssociationId', ], 'AuthenticationMaterial' => [ 'shape' => 'DiscoveryAuthMaterialString', ], 'AuthenticationMaterialType' => [ 'shape' => 'DiscoveryAuthMaterialType', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], 'Tags' => [ 'shape' => 'TagsMap', 'deprecated' => true, 'deprecatedMessage' => 'Tags have been deprecated from this api', 'deprecatedSince' => '06-25-2025', ], ], ], 'StartDeviceDiscoveryResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'DeviceDiscoveryId', ], 'StartedAt' => [ 'shape' => 'DiscoveryStartedAt', ], ], ], 'StartTime' => [ 'type' => 'string', ], 'StartedAt' => [ 'type' => 'timestamp', ], 'StateCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'StateCapability', ], 'max' => 5, 'min' => 1, ], 'StateCapability' => [ 'type' => 'structure', 'required' => [ 'id', 'name', 'version', ], 'members' => [ 'id' => [ 'shape' => 'SchemaVersionedId', ], 'name' => [ 'shape' => 'CapabilityName', ], 'version' => [ 'shape' => 'CapabilityVersion', ], 'properties' => [ 'shape' => 'CapabilityProperties', ], ], ], 'StateEndpoint' => [ 'type' => 'structure', 'required' => [ 'endpointId', 'capabilities', ], 'members' => [ 'endpointId' => [ 'shape' => 'EndpointId', ], 'capabilities' => [ 'shape' => 'StateCapabilities', ], ], ], 'StateEndpoints' => [ 'type' => 'list', 'member' => [ 'shape' => 'StateEndpoint', ], 'max' => 5, 'min' => 0, ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'IoTManagedIntegrationsResourceARN', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TagsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, 'sensitive' => true, ], 'Target' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'TaskProcessingDetails' => [ 'type' => 'structure', 'members' => [ 'NumberOfCanceledThings' => [ 'shape' => 'Integer', ], 'NumberOfFailedThings' => [ 'shape' => 'Integer', ], 'NumberOfInProgressThings' => [ 'shape' => 'Integer', ], 'numberOfQueuedThings' => [ 'shape' => 'Integer', ], 'numberOfRejectedThings' => [ 'shape' => 'Integer', ], 'numberOfRemovedThings' => [ 'shape' => 'Integer', ], 'numberOfSucceededThings' => [ 'shape' => 'Integer', ], 'numberOfTimedOutThings' => [ 'shape' => 'Integer', ], 'processingTargets' => [ 'shape' => 'Target', ], ], ], 'ThirdPartyUserId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_.,@-]+', 'sensitive' => true, ], 'ThresholdPercentage' => [ 'type' => 'double', 'box' => true, 'max' => 100, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'TokenEndpointAuthenticationScheme' => [ 'type' => 'string', 'enum' => [ 'HTTP_BASIC', 'REQUEST_BODY_CREDENTIALS', ], ], 'TokenUrl' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => 'https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}([-a-zA-Z0-9()@:%_\\+.~#?&\\/=]*)', ], 'TraceId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9:=_-]+', ], 'UnauthorizedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 401, 'senderFault' => true, ], 'exception' => true, ], 'UniversalProductCode' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '[0-9]+', 'sensitive' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'IoTManagedIntegrationsResourceARN', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAccountAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'AccountAssociationId', ], 'members' => [ 'AccountAssociationId' => [ 'shape' => 'AccountAssociationId', 'location' => 'uri', 'locationName' => 'AccountAssociationId', ], 'Name' => [ 'shape' => 'AccountAssociationName', ], 'Description' => [ 'shape' => 'AccountAssociationDescription', ], ], ], 'UpdateCloudConnectorRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'CloudConnectorId', 'location' => 'uri', 'locationName' => 'Identifier', ], 'Name' => [ 'shape' => 'DisplayName', ], 'Description' => [ 'shape' => 'CloudConnectorDescription', ], ], ], 'UpdateConnectorDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'ConnectorDestinationId', 'location' => 'uri', 'locationName' => 'Identifier', ], 'Description' => [ 'shape' => 'ConnectorDestinationDescription', ], 'Name' => [ 'shape' => 'ConnectorDestinationName', ], 'AuthType' => [ 'shape' => 'AuthType', ], 'AuthConfig' => [ 'shape' => 'AuthConfigUpdate', ], 'SecretsManager' => [ 'shape' => 'SecretsManager', ], ], ], 'UpdateDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'DestinationName', 'location' => 'uri', 'locationName' => 'Name', ], 'DeliveryDestinationArn' => [ 'shape' => 'DeliveryDestinationArn', ], 'DeliveryDestinationType' => [ 'shape' => 'DeliveryDestinationType', ], 'RoleArn' => [ 'shape' => 'DeliveryDestinationRoleArn', ], 'Description' => [ 'shape' => 'DestinationDescription', ], ], ], 'UpdateEventLogConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'EventLogLevel', ], 'members' => [ 'Id' => [ 'shape' => 'LogConfigurationId', 'location' => 'uri', 'locationName' => 'Id', ], 'EventLogLevel' => [ 'shape' => 'LogLevel', ], ], ], 'UpdateManagedThingRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'ManagedThingId', 'location' => 'uri', 'locationName' => 'Identifier', ], 'Owner' => [ 'shape' => 'Owner', ], 'CredentialLockerId' => [ 'shape' => 'CredentialLockerId', ], 'SerialNumber' => [ 'shape' => 'SerialNumber', ], 'Brand' => [ 'shape' => 'Brand', ], 'Model' => [ 'shape' => 'Model', ], 'Name' => [ 'shape' => 'Name', ], 'CapabilityReport' => [ 'shape' => 'CapabilityReport', ], 'CapabilitySchemas' => [ 'shape' => 'CapabilitySchemas', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'Classification' => [ 'shape' => 'Classification', ], 'HubNetworkMode' => [ 'shape' => 'HubNetworkMode', ], 'MetaData' => [ 'shape' => 'MetaData', ], ], ], 'UpdateNotificationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'EventType', 'DestinationName', ], 'members' => [ 'EventType' => [ 'shape' => 'EventType', 'location' => 'uri', 'locationName' => 'EventType', ], 'DestinationName' => [ 'shape' => 'DestinationName', ], ], ], 'UpdateOtaTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'OtaTaskId', 'location' => 'uri', 'locationName' => 'Identifier', ], 'Description' => [ 'shape' => 'OtaDescription', ], 'TaskConfigurationId' => [ 'shape' => 'OtaTaskConfigurationId', ], ], ], 'UpdatedAt' => [ 'type' => 'timestamp', ], 'UploadLog' => [ 'type' => 'boolean', 'box' => true, ], 'UploadPeriodMinutes' => [ 'type' => 'integer', 'box' => true, ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationSchema' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], ],];
