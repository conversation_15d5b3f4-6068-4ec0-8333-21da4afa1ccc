<?php

/**
 * Create.php
 * php version 7.2.0
 *
 * @category Trait
 * @package  Xendit
 * <AUTHOR> <<EMAIL>>
 * @license  https://opensource.org/licenses/MIT MIT License
 * @link     https://api.xendit.co
 */

namespace App\Xendit\ApiOperations;

/**
 * Trait Create
 *
 * @category Trait
 * @package  Xendit\ApiOperations
 * <AUTHOR> <<EMAIL>>
 * @license  https://opensource.org/licenses/MIT MIT License
 * @link     https://api.xendit.co
 */
trait Create
{
    /**
     * Send a create request
     *
     * @param array $params user's params
     *
     * @return array
     */
    public static function create($params = [])
    {
        self::validateParams($params, static::createReqParams());

        $url = static::classUrl();

        return static::_request('POST', $url, $params);
    }
}
