net\authorize\api\contract\v1\ANetApiRequestType:
    properties:
        merchantAuthentication:
            expose: true
            access_type: public_method
            serialized_name: merchantAuthentication
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getMerchantAuthentication
                setter: setMerchantAuthentication
            type: net\authorize\api\contract\v1\MerchantAuthenticationType
        clientId:
            expose: true
            access_type: public_method
            serialized_name: clientId
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getClientId
                setter: setClientId
            type: string
        refId:
            expose: true
            access_type: public_method
            serialized_name: refId
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getRefId
                setter: setRefId
            type: string
