<?php

namespace App\Models\ProjectManagement;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use App\Models\User;
use App\Traits\ProjectManagement\HasTimestamps;
use App\Traits\ProjectManagement\Searchable;

/**
 * نموذج التعليقات المتقدم
 * 
 * هذا النموذج يمثل التعليقات التي يمكن إضافتها على المشاريع والمهام
 * ويدعم التعليقات المتداخلة والإشارات والتفاعلات
 * 
 * @package App\Models\ProjectManagement
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 * 
 * @property int $id معرف التعليق الفريد
 * @property string $content محتوى التعليق
 * @property int $user_id معرف المستخدم
 * @property string $commentable_type نوع الكائن المعلق عليه
 * @property int $commentable_id معرف الكائن المعلق عليه
 * @property int|null $parent_id معرف التعليق الأب
 * @property bool $is_private تعليق خاص
 * @property bool $is_pinned تعليق مثبت
 * @property array|null $mentions الإشارات في التعليق
 * @property array|null $metadata بيانات إضافية
 * @property Carbon $created_at تاريخ الإنشاء
 * @property Carbon $updated_at تاريخ آخر تحديث
 * @property Carbon|null $deleted_at تاريخ الحذف الناعم
 */
class Comment extends Model
{
    use HasFactory, SoftDeletes, HasTimestamps, Searchable;

    /**
     * اسم الجدول في قاعدة البيانات
     * 
     * @var string
     */
    protected $table = 'comments';

    /**
     * الحقول القابلة للتعبئة الجماعية
     * 
     * @var array<string>
     */
    protected $fillable = [
        'content',
        'user_id',
        'commentable_type',
        'commentable_id',
        'parent_id',
        'is_private',
        'is_pinned',
        'mentions',
        'metadata',
    ];

    /**
     * الحقول المخفية من التسلسل
     * 
     * @var array<string>
     */
    protected $hidden = [
        'deleted_at',
    ];

    /**
     * تحويل أنواع البيانات
     * 
     * @var array<string, string>
     */
    protected $casts = [
        'is_private' => 'boolean',
        'is_pinned' => 'boolean',
        'mentions' => 'array',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * الحقول القابلة للبحث
     * 
     * @var array<string>
     */
    protected $searchable = [
        'content',
    ];

    /**
     * القيم الافتراضية للحقول
     * 
     * @var array<string, mixed>
     */
    protected $attributes = [
        'is_private' => false,
        'is_pinned' => false,
    ];

    /**
     * أحداث النموذج
     * 
     * @return void
     */
    protected static function boot(): void
    {
        parent::boot();

        // استخراج الإشارات من محتوى التعليق
        static::saving(function ($comment) {
            $comment->extractMentions();
        });

        // إرسال إشعارات للمستخدمين المذكورين
        static::created(function ($comment) {
            $comment->notifyMentionedUsers();
        });
    }

    /*
    |--------------------------------------------------------------------------
    | العلاقات (Relationships)
    |--------------------------------------------------------------------------
    */

    /**
     * العلاقة مع المستخدم
     * 
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع الكائن المعلق عليه (Polymorphic)
     * 
     * @return MorphTo
     */
    public function commentable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * العلاقة مع التعليق الأب
     * 
     * @return BelongsTo
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Comment::class, 'parent_id');
    }

    /**
     * العلاقة مع التعليقات الفرعية
     * 
     * @return HasMany
     */
    public function replies(): HasMany
    {
        return $this->hasMany(Comment::class, 'parent_id');
    }

    /*
    |--------------------------------------------------------------------------
    | النطاقات (Scopes)
    |--------------------------------------------------------------------------
    */

    /**
     * نطاق التعليقات العامة
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopePublic(Builder $query): Builder
    {
        return $query->where('is_private', false);
    }

    /**
     * نطاق التعليقات الخاصة
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopePrivate(Builder $query): Builder
    {
        return $query->where('is_private', true);
    }

    /**
     * نطاق التعليقات المثبتة
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopePinned(Builder $query): Builder
    {
        return $query->where('is_pinned', true);
    }

    /**
     * نطاق التعليقات الرئيسية (بدون تعليق أب)
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeMainComments(Builder $query): Builder
    {
        return $query->whereNull('parent_id');
    }

    /**
     * نطاق الردود (التعليقات الفرعية)
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeReplies(Builder $query): Builder
    {
        return $query->whereNotNull('parent_id');
    }

    /**
     * نطاق التعليقات حسب المستخدم
     * 
     * @param Builder $query
     * @param int $userId
     * @return Builder
     */
    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * نطاق التعليقات التي تحتوي على إشارة لمستخدم معين
     * 
     * @param Builder $query
     * @param int $userId
     * @return Builder
     */
    public function scopeMentioning(Builder $query, int $userId): Builder
    {
        return $query->whereJsonContains('mentions', $userId);
    }

    /*
    |--------------------------------------------------------------------------
    | الطرق المساعدة (Helper Methods)
    |--------------------------------------------------------------------------
    */

    /**
     * التحقق من كون التعليق خاص
     * 
     * @return bool
     */
    public function isPrivate(): bool
    {
        return $this->is_private;
    }

    /**
     * التحقق من كون التعليق مثبت
     * 
     * @return bool
     */
    public function isPinned(): bool
    {
        return $this->is_pinned;
    }

    /**
     * التحقق من كون التعليق رد على تعليق آخر
     * 
     * @return bool
     */
    public function isReply(): bool
    {
        return !is_null($this->parent_id);
    }

    /**
     * الحصول على عدد الردود
     * 
     * @return int
     */
    public function getRepliesCount(): int
    {
        return $this->replies()->count();
    }

    /**
     * استخراج الإشارات من محتوى التعليق
     * 
     * @return void
     */
    protected function extractMentions(): void
    {
        // البحث عن الإشارات في النص باستخدام نمط @username
        preg_match_all('/@(\w+)/', $this->content, $matches);
        
        if (!empty($matches[1])) {
            $usernames = $matches[1];
            $userIds = User::whereIn('name', $usernames)->pluck('id')->toArray();
            $this->mentions = $userIds;
        } else {
            $this->mentions = [];
        }
    }

    /**
     * إرسال إشعارات للمستخدمين المذكورين
     * 
     * @return void
     */
    protected function notifyMentionedUsers(): void
    {
        if (empty($this->mentions)) {
            return;
        }

        $mentionedUsers = User::whereIn('id', $this->mentions)->get();
        
        foreach ($mentionedUsers as $user) {
            // إرسال إشعار للمستخدم المذكور
            $user->notify(new \App\Notifications\CommentMention($this));
        }
    }

    /**
     * تحويل محتوى التعليق لعرض الإشارات كروابط
     * 
     * @return string
     */
    public function getFormattedContentAttribute(): string
    {
        $content = $this->content;
        
        // تحويل الإشارات إلى روابط
        $content = preg_replace_callback('/@(\w+)/', function ($matches) {
            $username = $matches[1];
            $user = User::where('name', $username)->first();
            
            if ($user) {
                return '<a href="#" class="mention" data-user-id="' . $user->id . '">@' . $username . '</a>';
            }
            
            return $matches[0];
        }, $content);

        return $content;
    }

    /**
     * الحصول على محتوى مختصر للتعليق
     * 
     * @param int $length
     * @return string
     */
    public function getExcerpt(int $length = 100): string
    {
        return \Str::limit(strip_tags($this->content), $length);
    }

    /**
     * تثبيت التعليق
     * 
     * @return void
     */
    public function pin(): void
    {
        $this->update(['is_pinned' => true]);
    }

    /**
     * إلغاء تثبيت التعليق
     * 
     * @return void
     */
    public function unpin(): void
    {
        $this->update(['is_pinned' => false]);
    }

    /**
     * تحويل التعليق إلى خاص
     * 
     * @return void
     */
    public function makePrivate(): void
    {
        $this->update(['is_private' => true]);
    }

    /**
     * تحويل التعليق إلى عام
     * 
     * @return void
     */
    public function makePublic(): void
    {
        $this->update(['is_private' => false]);
    }

    /**
     * التحقق من إمكانية تعديل التعليق من قبل المستخدم
     * 
     * @param User $user
     * @return bool
     */
    public function canBeEditedBy(User $user): bool
    {
        // المؤلف يمكنه التعديل خلال 15 دقيقة من الإنشاء
        if ($this->user_id === $user->id) {
            return $this->created_at->diffInMinutes(now()) <= 15;
        }

        // المديرون يمكنهم التعديل دائماً
        return $user->hasRole(['admin', 'project_manager']);
    }

    /**
     * التحقق من إمكانية حذف التعليق من قبل المستخدم
     * 
     * @param User $user
     * @return bool
     */
    public function canBeDeletedBy(User $user): bool
    {
        // المؤلف يمكنه الحذف
        if ($this->user_id === $user->id) {
            return true;
        }

        // المديرون يمكنهم الحذف
        return $user->hasRole(['admin', 'project_manager']);
    }

    /**
     * الحصول على الوقت النسبي للتعليق
     * 
     * @return string
     */
    public function getTimeAgoAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }
}
