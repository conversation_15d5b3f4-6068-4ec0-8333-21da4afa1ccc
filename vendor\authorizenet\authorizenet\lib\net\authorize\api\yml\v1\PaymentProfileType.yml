net\authorize\api\contract\v1\PaymentProfileType:
    properties:
        paymentProfileId:
            expose: true
            access_type: public_method
            serialized_name: paymentProfileId
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getPaymentProfileId
                setter: setPaymentProfileId
            type: string
        cardCode:
            expose: true
            access_type: public_method
            serialized_name: cardCode
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getCardCode
                setter: setCardCode
            type: string
