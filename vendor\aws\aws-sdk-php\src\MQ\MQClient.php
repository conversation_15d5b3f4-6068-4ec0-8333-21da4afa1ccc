<?php
namespace Aws\MQ;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AmazonMQ** service.
 * @method \Aws\Result createBroker(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createBrokerAsync(array $args = [])
 * @method \Aws\Result createConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createConfigurationAsync(array $args = [])
 * @method \Aws\Result createTags(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createTagsAsync(array $args = [])
 * @method \Aws\Result createUser(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createUserAsync(array $args = [])
 * @method \Aws\Result deleteBroker(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteBrokerAsync(array $args = [])
 * @method \Aws\Result deleteConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteConfigurationAsync(array $args = [])
 * @method \Aws\Result deleteTags(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteTagsAsync(array $args = [])
 * @method \Aws\Result deleteUser(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteUserAsync(array $args = [])
 * @method \Aws\Result describeBroker(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeBrokerAsync(array $args = [])
 * @method \Aws\Result describeBrokerEngineTypes(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeBrokerEngineTypesAsync(array $args = [])
 * @method \Aws\Result describeBrokerInstanceOptions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeBrokerInstanceOptionsAsync(array $args = [])
 * @method \Aws\Result describeConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeConfigurationAsync(array $args = [])
 * @method \Aws\Result describeConfigurationRevision(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeConfigurationRevisionAsync(array $args = [])
 * @method \Aws\Result describeUser(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeUserAsync(array $args = [])
 * @method \Aws\Result listBrokers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listBrokersAsync(array $args = [])
 * @method \Aws\Result listConfigurationRevisions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listConfigurationRevisionsAsync(array $args = [])
 * @method \Aws\Result listConfigurations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listConfigurationsAsync(array $args = [])
 * @method \Aws\Result listTags(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsAsync(array $args = [])
 * @method \Aws\Result listUsers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listUsersAsync(array $args = [])
 * @method \Aws\Result promote(array $args = [])
 * @method \GuzzleHttp\Promise\Promise promoteAsync(array $args = [])
 * @method \Aws\Result rebootBroker(array $args = [])
 * @method \GuzzleHttp\Promise\Promise rebootBrokerAsync(array $args = [])
 * @method \Aws\Result updateBroker(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateBrokerAsync(array $args = [])
 * @method \Aws\Result updateConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateConfigurationAsync(array $args = [])
 * @method \Aws\Result updateUser(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateUserAsync(array $args = [])
 */
class MQClient extends AwsClient {}
