<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Serverless
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Serverless\V1\Service\Environment;

use Twilio\Options;
use Twilio\Values;

abstract class LogOptions
{

    /**
     * @param string $functionSid The SID of the function whose invocation produced the Log resources to read.
     * @param \DateTime $startDate The date/time (in GMT, ISO 8601) after which the Log resources must have been created. Defaults to 1 day prior to current date/time.
     * @param \DateTime $endDate The date/time (in GMT, ISO 8601) before which the Log resources must have been created. Defaults to current date/time.
     * @return ReadLogOptions Options builder
     */
    public static function read(
        
        string $functionSid = Values::NONE,
        ?\DateTime $startDate = null,
        ?\DateTime $endDate = null

    ): ReadLogOptions
    {
        return new ReadLogOptions(
            $functionSid,
            $startDate,
            $endDate
        );
    }

}


class ReadLogOptions extends Options
    {
    /**
     * @param string $functionSid The SID of the function whose invocation produced the Log resources to read.
     * @param \DateTime $startDate The date/time (in GMT, ISO 8601) after which the Log resources must have been created. Defaults to 1 day prior to current date/time.
     * @param \DateTime $endDate The date/time (in GMT, ISO 8601) before which the Log resources must have been created. Defaults to current date/time.
     */
    public function __construct(
        
        string $functionSid = Values::NONE,
        ?\DateTime $startDate = null,
        ?\DateTime $endDate = null

    ) {
        $this->options['functionSid'] = $functionSid;
        $this->options['startDate'] = $startDate;
        $this->options['endDate'] = $endDate;
    }

    /**
     * The SID of the function whose invocation produced the Log resources to read.
     *
     * @param string $functionSid The SID of the function whose invocation produced the Log resources to read.
     * @return $this Fluent Builder
     */
    public function setFunctionSid(string $functionSid): self
    {
        $this->options['functionSid'] = $functionSid;
        return $this;
    }

    /**
     * The date/time (in GMT, ISO 8601) after which the Log resources must have been created. Defaults to 1 day prior to current date/time.
     *
     * @param \DateTime $startDate The date/time (in GMT, ISO 8601) after which the Log resources must have been created. Defaults to 1 day prior to current date/time.
     * @return $this Fluent Builder
     */
    public function setStartDate(\DateTime $startDate): self
    {
        $this->options['startDate'] = $startDate;
        return $this;
    }

    /**
     * The date/time (in GMT, ISO 8601) before which the Log resources must have been created. Defaults to current date/time.
     *
     * @param \DateTime $endDate The date/time (in GMT, ISO 8601) before which the Log resources must have been created. Defaults to current date/time.
     * @return $this Fluent Builder
     */
    public function setEndDate(\DateTime $endDate): self
    {
        $this->options['endDate'] = $endDate;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Serverless.V1.ReadLogOptions ' . $options . ']';
    }
}

