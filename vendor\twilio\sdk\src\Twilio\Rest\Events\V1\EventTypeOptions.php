<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Events
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Events\V1;

use Twilio\Options;
use Twilio\Values;

abstract class EventTypeOptions
{

    /**
     * @param string $schemaId A string parameter filtering the results to return only the Event Types using a given schema.
     * @return ReadEventTypeOptions Options builder
     */
    public static function read(
        
        string $schemaId = Values::NONE

    ): ReadEventTypeOptions
    {
        return new ReadEventTypeOptions(
            $schemaId
        );
    }

}


class ReadEventTypeOptions extends Options
    {
    /**
     * @param string $schemaId A string parameter filtering the results to return only the Event Types using a given schema.
     */
    public function __construct(
        
        string $schemaId = Values::NONE

    ) {
        $this->options['schemaId'] = $schemaId;
    }

    /**
     * A string parameter filtering the results to return only the Event Types using a given schema.
     *
     * @param string $schemaId A string parameter filtering the results to return only the Event Types using a given schema.
     * @return $this Fluent Builder
     */
    public function setSchemaId(string $schemaId): self
    {
        $this->options['schemaId'] = $schemaId;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Events.V1.ReadEventTypeOptions ' . $options . ']';
    }
}

