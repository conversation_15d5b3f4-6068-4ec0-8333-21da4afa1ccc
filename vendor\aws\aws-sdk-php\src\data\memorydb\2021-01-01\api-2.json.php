<?php
// This file was auto-generated from sdk-root/src/data/memorydb/2021-01-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2021-01-01', 'endpointPrefix' => 'memory-db', 'jsonVersion' => '1.1', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceAbbreviation' => 'Amazon MemoryDB', 'serviceFullName' => 'Amazon MemoryDB', 'serviceId' => 'MemoryDB', 'signatureVersion' => 'v4', 'signingName' => 'memorydb', 'targetPrefix' => 'AmazonMemoryDB', 'uid' => 'memorydb-2021-01-01', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'BatchUpdateCluster' => [ 'name' => 'BatchUpdateCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchUpdateClusterRequest', ], 'output' => [ 'shape' => 'BatchUpdateClusterResponse', ], 'errors' => [ [ 'shape' => 'ServiceUpdateNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'CopySnapshot' => [ 'name' => 'CopySnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CopySnapshotRequest', ], 'output' => [ 'shape' => 'CopySnapshotResponse', ], 'errors' => [ [ 'shape' => 'SnapshotAlreadyExistsFault', ], [ 'shape' => 'SnapshotNotFoundFault', ], [ 'shape' => 'SnapshotQuotaExceededFault', ], [ 'shape' => 'InvalidSnapshotStateFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'TagQuotaPerResourceExceeded', ], ], ], 'CreateACL' => [ 'name' => 'CreateACL', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateACLRequest', ], 'output' => [ 'shape' => 'CreateACLResponse', ], 'errors' => [ [ 'shape' => 'UserNotFoundFault', ], [ 'shape' => 'DuplicateUserNameFault', ], [ 'shape' => 'ACLAlreadyExistsFault', ], [ 'shape' => 'DefaultUserRequired', ], [ 'shape' => 'ACLQuotaExceededFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TagQuotaPerResourceExceeded', ], ], ], 'CreateCluster' => [ 'name' => 'CreateCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateClusterRequest', ], 'output' => [ 'shape' => 'CreateClusterResponse', ], 'errors' => [ [ 'shape' => 'ClusterAlreadyExistsFault', ], [ 'shape' => 'SubnetGroupNotFoundFault', ], [ 'shape' => 'ClusterQuotaForCustomerExceededFault', ], [ 'shape' => 'NodeQuotaForClusterExceededFault', ], [ 'shape' => 'NodeQuotaForCustomerExceededFault', ], [ 'shape' => 'ParameterGroupNotFoundFault', ], [ 'shape' => 'InsufficientClusterCapacityFault', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'ShardsPerClusterQuotaExceededFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'InvalidCredentialsException', ], [ 'shape' => 'TagQuotaPerResourceExceeded', ], [ 'shape' => 'ACLNotFoundFault', ], [ 'shape' => 'InvalidACLStateFault', ], [ 'shape' => 'MultiRegionClusterNotFoundFault', ], [ 'shape' => 'InvalidMultiRegionClusterStateFault', ], ], ], 'CreateMultiRegionCluster' => [ 'name' => 'CreateMultiRegionCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateMultiRegionClusterRequest', ], 'output' => [ 'shape' => 'CreateMultiRegionClusterResponse', ], 'errors' => [ [ 'shape' => 'MultiRegionClusterAlreadyExistsFault', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MultiRegionParameterGroupNotFoundFault', ], [ 'shape' => 'ClusterQuotaForCustomerExceededFault', ], [ 'shape' => 'TagQuotaPerResourceExceeded', ], ], ], 'CreateParameterGroup' => [ 'name' => 'CreateParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateParameterGroupRequest', ], 'output' => [ 'shape' => 'CreateParameterGroupResponse', ], 'errors' => [ [ 'shape' => 'ParameterGroupQuotaExceededFault', ], [ 'shape' => 'ParameterGroupAlreadyExistsFault', ], [ 'shape' => 'InvalidParameterGroupStateFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'TagQuotaPerResourceExceeded', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'CreateSnapshot' => [ 'name' => 'CreateSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSnapshotRequest', ], 'output' => [ 'shape' => 'CreateSnapshotResponse', ], 'errors' => [ [ 'shape' => 'SnapshotAlreadyExistsFault', ], [ 'shape' => 'ClusterNotFoundFault', ], [ 'shape' => 'InvalidClusterStateFault', ], [ 'shape' => 'SnapshotQuotaExceededFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TagQuotaPerResourceExceeded', ], ], ], 'CreateSubnetGroup' => [ 'name' => 'CreateSubnetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSubnetGroupRequest', ], 'output' => [ 'shape' => 'CreateSubnetGroupResponse', ], 'errors' => [ [ 'shape' => 'SubnetGroupAlreadyExistsFault', ], [ 'shape' => 'SubnetGroupQuotaExceededFault', ], [ 'shape' => 'SubnetQuotaExceededFault', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'SubnetNotAllowedFault', ], [ 'shape' => 'TagQuotaPerResourceExceeded', ], ], ], 'CreateUser' => [ 'name' => 'CreateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUserRequest', ], 'output' => [ 'shape' => 'CreateUserResponse', ], 'errors' => [ [ 'shape' => 'UserAlreadyExistsFault', ], [ 'shape' => 'UserQuotaExceededFault', ], [ 'shape' => 'DuplicateUserNameFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'TagQuotaPerResourceExceeded', ], ], ], 'DeleteACL' => [ 'name' => 'DeleteACL', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteACLRequest', ], 'output' => [ 'shape' => 'DeleteACLResponse', ], 'errors' => [ [ 'shape' => 'ACLNotFoundFault', ], [ 'shape' => 'InvalidACLStateFault', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'DeleteCluster' => [ 'name' => 'DeleteCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteClusterRequest', ], 'output' => [ 'shape' => 'DeleteClusterResponse', ], 'errors' => [ [ 'shape' => 'ClusterNotFoundFault', ], [ 'shape' => 'InvalidClusterStateFault', ], [ 'shape' => 'SnapshotAlreadyExistsFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DeleteMultiRegionCluster' => [ 'name' => 'DeleteMultiRegionCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteMultiRegionClusterRequest', ], 'output' => [ 'shape' => 'DeleteMultiRegionClusterResponse', ], 'errors' => [ [ 'shape' => 'MultiRegionClusterNotFoundFault', ], [ 'shape' => 'InvalidMultiRegionClusterStateFault', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'DeleteParameterGroup' => [ 'name' => 'DeleteParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteParameterGroupRequest', ], 'output' => [ 'shape' => 'DeleteParameterGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterGroupStateFault', ], [ 'shape' => 'ParameterGroupNotFoundFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DeleteSnapshot' => [ 'name' => 'DeleteSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSnapshotRequest', ], 'output' => [ 'shape' => 'DeleteSnapshotResponse', ], 'errors' => [ [ 'shape' => 'SnapshotNotFoundFault', ], [ 'shape' => 'InvalidSnapshotStateFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DeleteSubnetGroup' => [ 'name' => 'DeleteSubnetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSubnetGroupRequest', ], 'output' => [ 'shape' => 'DeleteSubnetGroupResponse', ], 'errors' => [ [ 'shape' => 'SubnetGroupInUseFault', ], [ 'shape' => 'SubnetGroupNotFoundFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], ], ], 'DeleteUser' => [ 'name' => 'DeleteUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteUserRequest', ], 'output' => [ 'shape' => 'DeleteUserResponse', ], 'errors' => [ [ 'shape' => 'InvalidUserStateFault', ], [ 'shape' => 'UserNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'DescribeACLs' => [ 'name' => 'DescribeACLs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeACLsRequest', ], 'output' => [ 'shape' => 'DescribeACLsResponse', ], 'errors' => [ [ 'shape' => 'ACLNotFoundFault', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeClusters' => [ 'name' => 'DescribeClusters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeClustersRequest', ], 'output' => [ 'shape' => 'DescribeClustersResponse', ], 'errors' => [ [ 'shape' => 'ClusterNotFoundFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeEngineVersions' => [ 'name' => 'DescribeEngineVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEngineVersionsRequest', ], 'output' => [ 'shape' => 'DescribeEngineVersionsResponse', ], 'errors' => [ [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeEvents' => [ 'name' => 'DescribeEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEventsRequest', ], 'output' => [ 'shape' => 'DescribeEventsResponse', ], 'errors' => [ [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeMultiRegionClusters' => [ 'name' => 'DescribeMultiRegionClusters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMultiRegionClustersRequest', ], 'output' => [ 'shape' => 'DescribeMultiRegionClustersResponse', ], 'errors' => [ [ 'shape' => 'ClusterNotFoundFault', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MultiRegionClusterNotFoundFault', ], ], ], 'DescribeParameterGroups' => [ 'name' => 'DescribeParameterGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeParameterGroupsRequest', ], 'output' => [ 'shape' => 'DescribeParameterGroupsResponse', ], 'errors' => [ [ 'shape' => 'ParameterGroupNotFoundFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeParameters' => [ 'name' => 'DescribeParameters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeParametersRequest', ], 'output' => [ 'shape' => 'DescribeParametersResponse', ], 'errors' => [ [ 'shape' => 'ParameterGroupNotFoundFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeReservedNodes' => [ 'name' => 'DescribeReservedNodes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReservedNodesRequest', ], 'output' => [ 'shape' => 'DescribeReservedNodesResponse', ], 'errors' => [ [ 'shape' => 'ReservedNodeNotFoundFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeReservedNodesOfferings' => [ 'name' => 'DescribeReservedNodesOfferings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReservedNodesOfferingsRequest', ], 'output' => [ 'shape' => 'DescribeReservedNodesOfferingsResponse', ], 'errors' => [ [ 'shape' => 'ReservedNodesOfferingNotFoundFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeServiceUpdates' => [ 'name' => 'DescribeServiceUpdates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeServiceUpdatesRequest', ], 'output' => [ 'shape' => 'DescribeServiceUpdatesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeSnapshots' => [ 'name' => 'DescribeSnapshots', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSnapshotsRequest', ], 'output' => [ 'shape' => 'DescribeSnapshotsResponse', ], 'errors' => [ [ 'shape' => 'SnapshotNotFoundFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeSubnetGroups' => [ 'name' => 'DescribeSubnetGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSubnetGroupsRequest', ], 'output' => [ 'shape' => 'DescribeSubnetGroupsResponse', ], 'errors' => [ [ 'shape' => 'SubnetGroupNotFoundFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], ], ], 'DescribeUsers' => [ 'name' => 'DescribeUsers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeUsersRequest', ], 'output' => [ 'shape' => 'DescribeUsersResponse', ], 'errors' => [ [ 'shape' => 'UserNotFoundFault', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'FailoverShard' => [ 'name' => 'FailoverShard', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'FailoverShardRequest', ], 'output' => [ 'shape' => 'FailoverShardResponse', ], 'errors' => [ [ 'shape' => 'APICallRateForCustomerExceededFault', ], [ 'shape' => 'InvalidClusterStateFault', ], [ 'shape' => 'ShardNotFoundFault', ], [ 'shape' => 'ClusterNotFoundFault', ], [ 'shape' => 'TestFailoverNotAvailableFault', ], [ 'shape' => 'InvalidKMSKeyFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'ListAllowedMultiRegionClusterUpdates' => [ 'name' => 'ListAllowedMultiRegionClusterUpdates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAllowedMultiRegionClusterUpdatesRequest', ], 'output' => [ 'shape' => 'ListAllowedMultiRegionClusterUpdatesResponse', ], 'errors' => [ [ 'shape' => 'MultiRegionClusterNotFoundFault', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'ListAllowedNodeTypeUpdates' => [ 'name' => 'ListAllowedNodeTypeUpdates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAllowedNodeTypeUpdatesRequest', ], 'output' => [ 'shape' => 'ListAllowedNodeTypeUpdatesResponse', ], 'errors' => [ [ 'shape' => 'ClusterNotFoundFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'ListTags' => [ 'name' => 'ListTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsRequest', ], 'output' => [ 'shape' => 'ListTagsResponse', ], 'errors' => [ [ 'shape' => 'ClusterNotFoundFault', ], [ 'shape' => 'InvalidClusterStateFault', ], [ 'shape' => 'ParameterGroupNotFoundFault', ], [ 'shape' => 'SubnetGroupNotFoundFault', ], [ 'shape' => 'SnapshotNotFoundFault', ], [ 'shape' => 'InvalidARNFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'UserNotFoundFault', ], [ 'shape' => 'ACLNotFoundFault', ], [ 'shape' => 'MultiRegionClusterNotFoundFault', ], [ 'shape' => 'MultiRegionParameterGroupNotFoundFault', ], ], ], 'PurchaseReservedNodesOffering' => [ 'name' => 'PurchaseReservedNodesOffering', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PurchaseReservedNodesOfferingRequest', ], 'output' => [ 'shape' => 'PurchaseReservedNodesOfferingResponse', ], 'errors' => [ [ 'shape' => 'ReservedNodesOfferingNotFoundFault', ], [ 'shape' => 'ReservedNodeAlreadyExistsFault', ], [ 'shape' => 'ReservedNodeQuotaExceededFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'TagQuotaPerResourceExceeded', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'ResetParameterGroup' => [ 'name' => 'ResetParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ResetParameterGroupRequest', ], 'output' => [ 'shape' => 'ResetParameterGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterGroupStateFault', ], [ 'shape' => 'ParameterGroupNotFoundFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ClusterNotFoundFault', ], [ 'shape' => 'ParameterGroupNotFoundFault', ], [ 'shape' => 'SubnetGroupNotFoundFault', ], [ 'shape' => 'InvalidClusterStateFault', ], [ 'shape' => 'SnapshotNotFoundFault', ], [ 'shape' => 'UserNotFoundFault', ], [ 'shape' => 'ACLNotFoundFault', ], [ 'shape' => 'MultiRegionClusterNotFoundFault', ], [ 'shape' => 'MultiRegionParameterGroupNotFoundFault', ], [ 'shape' => 'TagQuotaPerResourceExceeded', ], [ 'shape' => 'InvalidARNFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ClusterNotFoundFault', ], [ 'shape' => 'InvalidClusterStateFault', ], [ 'shape' => 'ParameterGroupNotFoundFault', ], [ 'shape' => 'SubnetGroupNotFoundFault', ], [ 'shape' => 'SnapshotNotFoundFault', ], [ 'shape' => 'InvalidARNFault', ], [ 'shape' => 'TagNotFoundFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'UserNotFoundFault', ], [ 'shape' => 'ACLNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MultiRegionClusterNotFoundFault', ], [ 'shape' => 'MultiRegionParameterGroupNotFoundFault', ], ], ], 'UpdateACL' => [ 'name' => 'UpdateACL', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateACLRequest', ], 'output' => [ 'shape' => 'UpdateACLResponse', ], 'errors' => [ [ 'shape' => 'ACLNotFoundFault', ], [ 'shape' => 'UserNotFoundFault', ], [ 'shape' => 'DuplicateUserNameFault', ], [ 'shape' => 'DefaultUserRequired', ], [ 'shape' => 'InvalidACLStateFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'UpdateCluster' => [ 'name' => 'UpdateCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateClusterRequest', ], 'output' => [ 'shape' => 'UpdateClusterResponse', ], 'errors' => [ [ 'shape' => 'ClusterNotFoundFault', ], [ 'shape' => 'InvalidClusterStateFault', ], [ 'shape' => 'InvalidNodeStateFault', ], [ 'shape' => 'ParameterGroupNotFoundFault', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidKMSKeyFault', ], [ 'shape' => 'NodeQuotaForClusterExceededFault', ], [ 'shape' => 'ClusterQuotaForCustomerExceededFault', ], [ 'shape' => 'ShardsPerClusterQuotaExceededFault', ], [ 'shape' => 'NodeQuotaForCustomerExceededFault', ], [ 'shape' => 'NoOperationFault', ], [ 'shape' => 'InvalidACLStateFault', ], [ 'shape' => 'ACLNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'UpdateMultiRegionCluster' => [ 'name' => 'UpdateMultiRegionCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateMultiRegionClusterRequest', ], 'output' => [ 'shape' => 'UpdateMultiRegionClusterResponse', ], 'errors' => [ [ 'shape' => 'MultiRegionClusterNotFoundFault', ], [ 'shape' => 'MultiRegionParameterGroupNotFoundFault', ], [ 'shape' => 'InvalidMultiRegionClusterStateFault', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'UpdateParameterGroup' => [ 'name' => 'UpdateParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateParameterGroupRequest', ], 'output' => [ 'shape' => 'UpdateParameterGroupResponse', ], 'errors' => [ [ 'shape' => 'ParameterGroupNotFoundFault', ], [ 'shape' => 'InvalidParameterGroupStateFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'UpdateSubnetGroup' => [ 'name' => 'UpdateSubnetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateSubnetGroupRequest', ], 'output' => [ 'shape' => 'UpdateSubnetGroupResponse', ], 'errors' => [ [ 'shape' => 'SubnetGroupNotFoundFault', ], [ 'shape' => 'SubnetQuotaExceededFault', ], [ 'shape' => 'SubnetInUse', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'SubnetNotAllowedFault', ], ], ], 'UpdateUser' => [ 'name' => 'UpdateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateUserRequest', ], 'output' => [ 'shape' => 'UpdateUserResponse', ], 'errors' => [ [ 'shape' => 'UserNotFoundFault', ], [ 'shape' => 'InvalidUserStateFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], ], 'shapes' => [ 'ACL' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'UserNames' => [ 'shape' => 'UserNameList', ], 'MinimumEngineVersion' => [ 'shape' => 'String', ], 'PendingChanges' => [ 'shape' => 'ACLPendingChanges', ], 'Clusters' => [ 'shape' => 'ACLClusterNameList', ], 'ARN' => [ 'shape' => 'String', ], ], ], 'ACLAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ACLClusterNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ACLList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ACL', ], ], 'ACLName' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[a-zA-Z][a-zA-Z0-9\\-]*', ], 'ACLNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ACLName', ], ], 'ACLNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ACLPendingChanges' => [ 'type' => 'structure', 'members' => [ 'UserNamesToRemove' => [ 'shape' => 'UserNameList', ], 'UserNamesToAdd' => [ 'shape' => 'UserNameList', ], ], ], 'ACLQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ACLsUpdateStatus' => [ 'type' => 'structure', 'members' => [ 'ACLToApply' => [ 'shape' => 'ACLName', ], ], ], 'APICallRateForCustomerExceededFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'AZStatus' => [ 'type' => 'string', 'enum' => [ 'singleaz', 'multiaz', ], ], 'AccessString' => [ 'type' => 'string', 'pattern' => '.*\\S.*', ], 'Authentication' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'AuthenticationType', ], 'PasswordCount' => [ 'shape' => 'IntegerOptional', ], ], ], 'AuthenticationMode' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'InputAuthenticationType', ], 'Passwords' => [ 'shape' => 'PasswordListInput', ], ], ], 'AuthenticationType' => [ 'type' => 'string', 'enum' => [ 'password', 'no-password', 'iam', ], ], 'AvailabilityZone' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], ], ], 'AwsQueryErrorMessage' => [ 'type' => 'string', ], 'BatchUpdateClusterRequest' => [ 'type' => 'structure', 'required' => [ 'ClusterNames', ], 'members' => [ 'ClusterNames' => [ 'shape' => 'ClusterNameList', ], 'ServiceUpdate' => [ 'shape' => 'ServiceUpdateRequest', ], ], ], 'BatchUpdateClusterResponse' => [ 'type' => 'structure', 'members' => [ 'ProcessedClusters' => [ 'shape' => 'ClusterList', ], 'UnprocessedClusters' => [ 'shape' => 'UnprocessedClusterList', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BooleanOptional' => [ 'type' => 'boolean', ], 'Cluster' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'PendingUpdates' => [ 'shape' => 'ClusterPendingUpdates', ], 'MultiRegionClusterName' => [ 'shape' => 'String', ], 'NumberOfShards' => [ 'shape' => 'IntegerOptional', ], 'Shards' => [ 'shape' => 'ShardList', ], 'AvailabilityMode' => [ 'shape' => 'AZStatus', ], 'ClusterEndpoint' => [ 'shape' => 'Endpoint', ], 'NodeType' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'EnginePatchVersion' => [ 'shape' => 'String', ], 'ParameterGroupName' => [ 'shape' => 'String', ], 'ParameterGroupStatus' => [ 'shape' => 'String', ], 'SecurityGroups' => [ 'shape' => 'SecurityGroupMembershipList', ], 'SubnetGroupName' => [ 'shape' => 'String', ], 'TLSEnabled' => [ 'shape' => 'BooleanOptional', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'ARN' => [ 'shape' => 'String', ], 'SnsTopicArn' => [ 'shape' => 'String', ], 'SnsTopicStatus' => [ 'shape' => 'String', ], 'SnapshotRetentionLimit' => [ 'shape' => 'IntegerOptional', ], 'MaintenanceWindow' => [ 'shape' => 'String', ], 'SnapshotWindow' => [ 'shape' => 'String', ], 'ACLName' => [ 'shape' => 'ACLName', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'BooleanOptional', ], 'DataTiering' => [ 'shape' => 'DataTieringStatus', ], 'NetworkType' => [ 'shape' => 'NetworkType', ], 'IpDiscovery' => [ 'shape' => 'IpDiscovery', ], ], ], 'ClusterAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ClusterConfiguration' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'NodeType' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'MaintenanceWindow' => [ 'shape' => 'String', ], 'TopicArn' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'ParameterGroupName' => [ 'shape' => 'String', ], 'SubnetGroupName' => [ 'shape' => 'String', ], 'VpcId' => [ 'shape' => 'String', ], 'SnapshotRetentionLimit' => [ 'shape' => 'IntegerOptional', ], 'SnapshotWindow' => [ 'shape' => 'String', ], 'NumShards' => [ 'shape' => 'IntegerOptional', ], 'Shards' => [ 'shape' => 'ShardDetails', ], 'MultiRegionParameterGroupName' => [ 'shape' => 'String', ], 'MultiRegionClusterName' => [ 'shape' => 'String', ], ], ], 'ClusterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Cluster', ], ], 'ClusterNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 20, ], 'ClusterNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ClusterPendingUpdates' => [ 'type' => 'structure', 'members' => [ 'Resharding' => [ 'shape' => 'ReshardingStatus', ], 'ACLs' => [ 'shape' => 'ACLsUpdateStatus', ], 'ServiceUpdates' => [ 'shape' => 'PendingModifiedServiceUpdateList', ], ], ], 'ClusterQuotaForCustomerExceededFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CopySnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'SourceSnapshotName', 'TargetSnapshotName', ], 'members' => [ 'SourceSnapshotName' => [ 'shape' => 'String', ], 'TargetSnapshotName' => [ 'shape' => 'String', ], 'TargetBucket' => [ 'shape' => 'TargetBucket', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CopySnapshotResponse' => [ 'type' => 'structure', 'members' => [ 'Snapshot' => [ 'shape' => 'Snapshot', ], ], ], 'CreateACLRequest' => [ 'type' => 'structure', 'required' => [ 'ACLName', ], 'members' => [ 'ACLName' => [ 'shape' => 'String', ], 'UserNames' => [ 'shape' => 'UserNameListInput', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateACLResponse' => [ 'type' => 'structure', 'members' => [ 'ACL' => [ 'shape' => 'ACL', ], ], ], 'CreateClusterRequest' => [ 'type' => 'structure', 'required' => [ 'ClusterName', 'NodeType', 'ACLName', ], 'members' => [ 'ClusterName' => [ 'shape' => 'String', ], 'NodeType' => [ 'shape' => 'String', ], 'MultiRegionClusterName' => [ 'shape' => 'String', ], 'ParameterGroupName' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'NumShards' => [ 'shape' => 'IntegerOptional', ], 'NumReplicasPerShard' => [ 'shape' => 'IntegerOptional', ], 'SubnetGroupName' => [ 'shape' => 'String', ], 'SecurityGroupIds' => [ 'shape' => 'SecurityGroupIdsList', ], 'MaintenanceWindow' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'SnsTopicArn' => [ 'shape' => 'String', ], 'TLSEnabled' => [ 'shape' => 'BooleanOptional', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'SnapshotArns' => [ 'shape' => 'SnapshotArnsList', ], 'SnapshotName' => [ 'shape' => 'String', ], 'SnapshotRetentionLimit' => [ 'shape' => 'IntegerOptional', ], 'Tags' => [ 'shape' => 'TagList', ], 'SnapshotWindow' => [ 'shape' => 'String', ], 'ACLName' => [ 'shape' => 'ACLName', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'BooleanOptional', ], 'DataTiering' => [ 'shape' => 'BooleanOptional', ], 'NetworkType' => [ 'shape' => 'NetworkType', ], 'IpDiscovery' => [ 'shape' => 'IpDiscovery', ], ], ], 'CreateClusterResponse' => [ 'type' => 'structure', 'members' => [ 'Cluster' => [ 'shape' => 'Cluster', ], ], ], 'CreateMultiRegionClusterRequest' => [ 'type' => 'structure', 'required' => [ 'MultiRegionClusterNameSuffix', 'NodeType', ], 'members' => [ 'MultiRegionClusterNameSuffix' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'NodeType' => [ 'shape' => 'String', ], 'MultiRegionParameterGroupName' => [ 'shape' => 'String', ], 'NumShards' => [ 'shape' => 'IntegerOptional', ], 'TLSEnabled' => [ 'shape' => 'BooleanOptional', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateMultiRegionClusterResponse' => [ 'type' => 'structure', 'members' => [ 'MultiRegionCluster' => [ 'shape' => 'MultiRegionCluster', ], ], ], 'CreateParameterGroupRequest' => [ 'type' => 'structure', 'required' => [ 'ParameterGroupName', 'Family', ], 'members' => [ 'ParameterGroupName' => [ 'shape' => 'String', ], 'Family' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateParameterGroupResponse' => [ 'type' => 'structure', 'members' => [ 'ParameterGroup' => [ 'shape' => 'ParameterGroup', ], ], ], 'CreateSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'ClusterName', 'SnapshotName', ], 'members' => [ 'ClusterName' => [ 'shape' => 'String', ], 'SnapshotName' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateSnapshotResponse' => [ 'type' => 'structure', 'members' => [ 'Snapshot' => [ 'shape' => 'Snapshot', ], ], ], 'CreateSubnetGroupRequest' => [ 'type' => 'structure', 'required' => [ 'SubnetGroupName', 'SubnetIds', ], 'members' => [ 'SubnetGroupName' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'SubnetIds' => [ 'shape' => 'SubnetIdentifierList', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateSubnetGroupResponse' => [ 'type' => 'structure', 'members' => [ 'SubnetGroup' => [ 'shape' => 'SubnetGroup', ], ], ], 'CreateUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserName', 'AuthenticationMode', 'AccessString', ], 'members' => [ 'UserName' => [ 'shape' => 'UserName', ], 'AuthenticationMode' => [ 'shape' => 'AuthenticationMode', ], 'AccessString' => [ 'shape' => 'AccessString', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateUserResponse' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'User', ], ], ], 'DataTieringStatus' => [ 'type' => 'string', 'enum' => [ 'true', 'false', ], ], 'DefaultUserRequired' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DeleteACLRequest' => [ 'type' => 'structure', 'required' => [ 'ACLName', ], 'members' => [ 'ACLName' => [ 'shape' => 'String', ], ], ], 'DeleteACLResponse' => [ 'type' => 'structure', 'members' => [ 'ACL' => [ 'shape' => 'ACL', ], ], ], 'DeleteClusterRequest' => [ 'type' => 'structure', 'required' => [ 'ClusterName', ], 'members' => [ 'ClusterName' => [ 'shape' => 'String', ], 'MultiRegionClusterName' => [ 'shape' => 'String', ], 'FinalSnapshotName' => [ 'shape' => 'String', ], ], ], 'DeleteClusterResponse' => [ 'type' => 'structure', 'members' => [ 'Cluster' => [ 'shape' => 'Cluster', ], ], ], 'DeleteMultiRegionClusterRequest' => [ 'type' => 'structure', 'required' => [ 'MultiRegionClusterName', ], 'members' => [ 'MultiRegionClusterName' => [ 'shape' => 'String', ], ], ], 'DeleteMultiRegionClusterResponse' => [ 'type' => 'structure', 'members' => [ 'MultiRegionCluster' => [ 'shape' => 'MultiRegionCluster', ], ], ], 'DeleteParameterGroupRequest' => [ 'type' => 'structure', 'required' => [ 'ParameterGroupName', ], 'members' => [ 'ParameterGroupName' => [ 'shape' => 'String', ], ], ], 'DeleteParameterGroupResponse' => [ 'type' => 'structure', 'members' => [ 'ParameterGroup' => [ 'shape' => 'ParameterGroup', ], ], ], 'DeleteSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'SnapshotName', ], 'members' => [ 'SnapshotName' => [ 'shape' => 'String', ], ], ], 'DeleteSnapshotResponse' => [ 'type' => 'structure', 'members' => [ 'Snapshot' => [ 'shape' => 'Snapshot', ], ], ], 'DeleteSubnetGroupRequest' => [ 'type' => 'structure', 'required' => [ 'SubnetGroupName', ], 'members' => [ 'SubnetGroupName' => [ 'shape' => 'String', ], ], ], 'DeleteSubnetGroupResponse' => [ 'type' => 'structure', 'members' => [ 'SubnetGroup' => [ 'shape' => 'SubnetGroup', ], ], ], 'DeleteUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserName', ], 'members' => [ 'UserName' => [ 'shape' => 'UserName', ], ], ], 'DeleteUserResponse' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'User', ], ], ], 'DescribeACLsRequest' => [ 'type' => 'structure', 'members' => [ 'ACLName' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'IntegerOptional', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeACLsResponse' => [ 'type' => 'structure', 'members' => [ 'ACLs' => [ 'shape' => 'ACLList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeClustersRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterName' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'IntegerOptional', ], 'NextToken' => [ 'shape' => 'String', ], 'ShowShardDetails' => [ 'shape' => 'BooleanOptional', ], ], ], 'DescribeClustersResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'Clusters' => [ 'shape' => 'ClusterList', ], ], ], 'DescribeEngineVersionsRequest' => [ 'type' => 'structure', 'members' => [ 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'ParameterGroupFamily' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'IntegerOptional', ], 'NextToken' => [ 'shape' => 'String', ], 'DefaultOnly' => [ 'shape' => 'Boolean', ], ], ], 'DescribeEngineVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'EngineVersions' => [ 'shape' => 'EngineVersionInfoList', ], ], ], 'DescribeEventsRequest' => [ 'type' => 'structure', 'members' => [ 'SourceName' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'SourceType', ], 'StartTime' => [ 'shape' => 'TStamp', ], 'EndTime' => [ 'shape' => 'TStamp', ], 'Duration' => [ 'shape' => 'IntegerOptional', ], 'MaxResults' => [ 'shape' => 'IntegerOptional', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeEventsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'Events' => [ 'shape' => 'EventList', ], ], ], 'DescribeMultiRegionClustersRequest' => [ 'type' => 'structure', 'members' => [ 'MultiRegionClusterName' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'IntegerOptional', ], 'NextToken' => [ 'shape' => 'String', ], 'ShowClusterDetails' => [ 'shape' => 'BooleanOptional', ], ], ], 'DescribeMultiRegionClustersResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'MultiRegionClusters' => [ 'shape' => 'MultiRegionClusterList', ], ], ], 'DescribeParameterGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'ParameterGroupName' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'IntegerOptional', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeParameterGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'ParameterGroups' => [ 'shape' => 'ParameterGroupList', ], ], ], 'DescribeParametersRequest' => [ 'type' => 'structure', 'required' => [ 'ParameterGroupName', ], 'members' => [ 'ParameterGroupName' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'IntegerOptional', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeParametersResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'Parameters' => [ 'shape' => 'ParametersList', ], ], ], 'DescribeReservedNodesOfferingsRequest' => [ 'type' => 'structure', 'members' => [ 'ReservedNodesOfferingId' => [ 'shape' => 'String', ], 'NodeType' => [ 'shape' => 'String', ], 'Duration' => [ 'shape' => 'String', ], 'OfferingType' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'IntegerOptional', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeReservedNodesOfferingsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'ReservedNodesOfferings' => [ 'shape' => 'ReservedNodesOfferingList', ], ], ], 'DescribeReservedNodesRequest' => [ 'type' => 'structure', 'members' => [ 'ReservationId' => [ 'shape' => 'String', ], 'ReservedNodesOfferingId' => [ 'shape' => 'String', ], 'NodeType' => [ 'shape' => 'String', ], 'Duration' => [ 'shape' => 'String', ], 'OfferingType' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'IntegerOptional', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeReservedNodesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'ReservedNodes' => [ 'shape' => 'ReservedNodeList', ], ], ], 'DescribeServiceUpdatesRequest' => [ 'type' => 'structure', 'members' => [ 'ServiceUpdateName' => [ 'shape' => 'String', ], 'ClusterNames' => [ 'shape' => 'ClusterNameList', ], 'Status' => [ 'shape' => 'ServiceUpdateStatusList', ], 'MaxResults' => [ 'shape' => 'IntegerOptional', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeServiceUpdatesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'ServiceUpdates' => [ 'shape' => 'ServiceUpdateList', ], ], ], 'DescribeSnapshotsRequest' => [ 'type' => 'structure', 'members' => [ 'ClusterName' => [ 'shape' => 'String', ], 'SnapshotName' => [ 'shape' => 'String', ], 'Source' => [ 'shape' => 'String', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'IntegerOptional', ], 'ShowDetail' => [ 'shape' => 'BooleanOptional', ], ], ], 'DescribeSnapshotsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'Snapshots' => [ 'shape' => 'SnapshotList', ], ], ], 'DescribeSubnetGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'SubnetGroupName' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'IntegerOptional', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeSubnetGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'SubnetGroups' => [ 'shape' => 'SubnetGroupList', ], ], ], 'DescribeUsersRequest' => [ 'type' => 'structure', 'members' => [ 'UserName' => [ 'shape' => 'UserName', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxResults' => [ 'shape' => 'IntegerOptional', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeUsersResponse' => [ 'type' => 'structure', 'members' => [ 'Users' => [ 'shape' => 'UserList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'Double' => [ 'type' => 'double', ], 'DuplicateUserNameFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Endpoint' => [ 'type' => 'structure', 'members' => [ 'Address' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'Integer', ], ], ], 'EngineVersionInfo' => [ 'type' => 'structure', 'members' => [ 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'EnginePatchVersion' => [ 'shape' => 'String', ], 'ParameterGroupFamily' => [ 'shape' => 'String', ], ], ], 'EngineVersionInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EngineVersionInfo', ], ], 'Event' => [ 'type' => 'structure', 'members' => [ 'SourceName' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'SourceType', ], 'Message' => [ 'shape' => 'String', ], 'Date' => [ 'shape' => 'TStamp', ], ], ], 'EventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Event', ], ], 'FailoverShardRequest' => [ 'type' => 'structure', 'required' => [ 'ClusterName', 'ShardName', ], 'members' => [ 'ClusterName' => [ 'shape' => 'String', ], 'ShardName' => [ 'shape' => 'String', ], ], ], 'FailoverShardResponse' => [ 'type' => 'structure', 'members' => [ 'Cluster' => [ 'shape' => 'Cluster', ], ], ], 'Filter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'FilterName', ], 'Values' => [ 'shape' => 'FilterValueList', ], ], ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], ], 'FilterName' => [ 'type' => 'string', 'pattern' => '.*\\S.*', ], 'FilterValue' => [ 'type' => 'string', 'pattern' => '.*\\S.*', ], 'FilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterValue', ], 'min' => 1, ], 'InputAuthenticationType' => [ 'type' => 'string', 'enum' => [ 'password', 'iam', ], ], 'InsufficientClusterCapacityFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Integer' => [ 'type' => 'integer', ], 'IntegerOptional' => [ 'type' => 'integer', ], 'InvalidACLStateFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidARNFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidClusterStateFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidCredentialsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidKMSKeyFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidMultiRegionClusterStateFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidNodeStateFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidParameterCombinationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'AwsQueryErrorMessage', ], ], 'exception' => true, 'synthetic' => true, ], 'InvalidParameterGroupStateFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidParameterValueException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'AwsQueryErrorMessage', ], ], 'exception' => true, 'synthetic' => true, ], 'InvalidSnapshotStateFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidSubnet' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidUserStateFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidVPCNetworkStateFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'IpDiscovery' => [ 'type' => 'string', 'enum' => [ 'ipv4', 'ipv6', ], ], 'KeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 2048, ], 'ListAllowedMultiRegionClusterUpdatesRequest' => [ 'type' => 'structure', 'required' => [ 'MultiRegionClusterName', ], 'members' => [ 'MultiRegionClusterName' => [ 'shape' => 'String', ], ], ], 'ListAllowedMultiRegionClusterUpdatesResponse' => [ 'type' => 'structure', 'members' => [ 'ScaleUpNodeTypes' => [ 'shape' => 'NodeTypeList', ], 'ScaleDownNodeTypes' => [ 'shape' => 'NodeTypeList', ], ], ], 'ListAllowedNodeTypeUpdatesRequest' => [ 'type' => 'structure', 'required' => [ 'ClusterName', ], 'members' => [ 'ClusterName' => [ 'shape' => 'String', ], ], ], 'ListAllowedNodeTypeUpdatesResponse' => [ 'type' => 'structure', 'members' => [ 'ScaleUpNodeTypes' => [ 'shape' => 'NodeTypeList', ], 'ScaleDownNodeTypes' => [ 'shape' => 'NodeTypeList', ], ], ], 'ListTagsRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], ], ], 'ListTagsResponse' => [ 'type' => 'structure', 'members' => [ 'TagList' => [ 'shape' => 'TagList', ], ], ], 'MultiRegionCluster' => [ 'type' => 'structure', 'members' => [ 'MultiRegionClusterName' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'NodeType' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'NumberOfShards' => [ 'shape' => 'IntegerOptional', ], 'Clusters' => [ 'shape' => 'RegionalClusterList', ], 'MultiRegionParameterGroupName' => [ 'shape' => 'String', ], 'TLSEnabled' => [ 'shape' => 'BooleanOptional', ], 'ARN' => [ 'shape' => 'String', ], ], ], 'MultiRegionClusterAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MultiRegionClusterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MultiRegionCluster', ], ], 'MultiRegionClusterNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MultiRegionParameterGroupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NetworkType' => [ 'type' => 'string', 'enum' => [ 'ipv4', 'ipv6', 'dual_stack', ], ], 'NetworkTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkType', ], ], 'NoOperationFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Node' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'AvailabilityZone' => [ 'shape' => 'String', ], 'CreateTime' => [ 'shape' => 'TStamp', ], 'Endpoint' => [ 'shape' => 'Endpoint', ], ], ], 'NodeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Node', ], ], 'NodeQuotaForClusterExceededFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NodeQuotaForCustomerExceededFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NodeTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Parameter' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'DataType' => [ 'shape' => 'String', ], 'AllowedValues' => [ 'shape' => 'String', ], 'MinimumEngineVersion' => [ 'shape' => 'String', ], ], ], 'ParameterGroup' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Family' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'ARN' => [ 'shape' => 'String', ], ], ], 'ParameterGroupAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ParameterGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParameterGroup', ], ], 'ParameterGroupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ParameterGroupQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ParameterNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ParameterNameValue' => [ 'type' => 'structure', 'members' => [ 'ParameterName' => [ 'shape' => 'String', ], 'ParameterValue' => [ 'shape' => 'String', ], ], ], 'ParameterNameValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParameterNameValue', ], ], 'ParametersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Parameter', ], ], 'PasswordListInput' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'min' => 1, ], 'PendingModifiedServiceUpdate' => [ 'type' => 'structure', 'members' => [ 'ServiceUpdateName' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'ServiceUpdateStatus', ], ], ], 'PendingModifiedServiceUpdateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PendingModifiedServiceUpdate', ], ], 'PurchaseReservedNodesOfferingRequest' => [ 'type' => 'structure', 'required' => [ 'ReservedNodesOfferingId', ], 'members' => [ 'ReservedNodesOfferingId' => [ 'shape' => 'String', ], 'ReservationId' => [ 'shape' => 'String', ], 'NodeCount' => [ 'shape' => 'IntegerOptional', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'PurchaseReservedNodesOfferingResponse' => [ 'type' => 'structure', 'members' => [ 'ReservedNode' => [ 'shape' => 'ReservedNode', ], ], ], 'RecurringCharge' => [ 'type' => 'structure', 'members' => [ 'RecurringChargeAmount' => [ 'shape' => 'Double', ], 'RecurringChargeFrequency' => [ 'shape' => 'String', ], ], ], 'RecurringChargeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecurringCharge', ], ], 'RegionalCluster' => [ 'type' => 'structure', 'members' => [ 'ClusterName' => [ 'shape' => 'String', ], 'Region' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'ARN' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'RegionalClusterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegionalCluster', ], ], 'ReplicaConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'ReplicaCount' => [ 'shape' => 'Integer', ], ], ], 'ReservedNode' => [ 'type' => 'structure', 'members' => [ 'ReservationId' => [ 'shape' => 'String', ], 'ReservedNodesOfferingId' => [ 'shape' => 'String', ], 'NodeType' => [ 'shape' => 'String', ], 'StartTime' => [ 'shape' => 'TStamp', ], 'Duration' => [ 'shape' => 'Integer', ], 'FixedPrice' => [ 'shape' => 'Double', ], 'NodeCount' => [ 'shape' => 'Integer', ], 'OfferingType' => [ 'shape' => 'String', ], 'State' => [ 'shape' => 'String', ], 'RecurringCharges' => [ 'shape' => 'RecurringChargeList', ], 'ARN' => [ 'shape' => 'String', ], ], ], 'ReservedNodeAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ReservedNodeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReservedNode', ], ], 'ReservedNodeNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ReservedNodeQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ReservedNodesOffering' => [ 'type' => 'structure', 'members' => [ 'ReservedNodesOfferingId' => [ 'shape' => 'String', ], 'NodeType' => [ 'shape' => 'String', ], 'Duration' => [ 'shape' => 'Integer', ], 'FixedPrice' => [ 'shape' => 'Double', ], 'OfferingType' => [ 'shape' => 'String', ], 'RecurringCharges' => [ 'shape' => 'RecurringChargeList', ], ], ], 'ReservedNodesOfferingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReservedNodesOffering', ], ], 'ReservedNodesOfferingNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ResetParameterGroupRequest' => [ 'type' => 'structure', 'required' => [ 'ParameterGroupName', ], 'members' => [ 'ParameterGroupName' => [ 'shape' => 'String', ], 'AllParameters' => [ 'shape' => 'Boolean', ], 'ParameterNames' => [ 'shape' => 'ParameterNameList', ], ], ], 'ResetParameterGroupResponse' => [ 'type' => 'structure', 'members' => [ 'ParameterGroup' => [ 'shape' => 'ParameterGroup', ], ], ], 'ReshardingStatus' => [ 'type' => 'structure', 'members' => [ 'SlotMigration' => [ 'shape' => 'SlotMigration', ], ], ], 'SecurityGroupIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SecurityGroupMembership' => [ 'type' => 'structure', 'members' => [ 'SecurityGroupId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], ], ], 'SecurityGroupMembershipList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupMembership', ], ], 'ServiceLinkedRoleNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ServiceUpdate' => [ 'type' => 'structure', 'members' => [ 'ClusterName' => [ 'shape' => 'String', ], 'ServiceUpdateName' => [ 'shape' => 'String', ], 'ReleaseDate' => [ 'shape' => 'TStamp', ], 'Description' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'ServiceUpdateStatus', ], 'Type' => [ 'shape' => 'ServiceUpdateType', ], 'Engine' => [ 'shape' => 'String', ], 'NodesUpdated' => [ 'shape' => 'String', ], 'AutoUpdateStartDate' => [ 'shape' => 'TStamp', ], ], ], 'ServiceUpdateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceUpdate', ], ], 'ServiceUpdateNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ServiceUpdateRequest' => [ 'type' => 'structure', 'members' => [ 'ServiceUpdateNameToApply' => [ 'shape' => 'String', ], ], ], 'ServiceUpdateStatus' => [ 'type' => 'string', 'enum' => [ 'available', 'in-progress', 'complete', 'scheduled', ], ], 'ServiceUpdateStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceUpdateStatus', ], 'max' => 4, ], 'ServiceUpdateType' => [ 'type' => 'string', 'enum' => [ 'security-update', ], ], 'Shard' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'Slots' => [ 'shape' => 'String', ], 'Nodes' => [ 'shape' => 'NodeList', ], 'NumberOfNodes' => [ 'shape' => 'IntegerOptional', ], ], ], 'ShardConfiguration' => [ 'type' => 'structure', 'members' => [ 'Slots' => [ 'shape' => 'String', ], 'ReplicaCount' => [ 'shape' => 'IntegerOptional', ], ], ], 'ShardConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'ShardCount' => [ 'shape' => 'Integer', ], ], ], 'ShardDetail' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Configuration' => [ 'shape' => 'ShardConfiguration', ], 'Size' => [ 'shape' => 'String', ], 'SnapshotCreationTime' => [ 'shape' => 'TStamp', ], ], ], 'ShardDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'ShardDetail', ], ], 'ShardList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Shard', ], ], 'ShardNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ShardsPerClusterQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'SlotMigration' => [ 'type' => 'structure', 'members' => [ 'ProgressPercentage' => [ 'shape' => 'Double', ], ], ], 'Snapshot' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'Source' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'ARN' => [ 'shape' => 'String', ], 'ClusterConfiguration' => [ 'shape' => 'ClusterConfiguration', ], 'DataTiering' => [ 'shape' => 'DataTieringStatus', ], ], ], 'SnapshotAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'SnapshotArnsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SnapshotList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Snapshot', ], ], 'SnapshotNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'SnapshotQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'SourceType' => [ 'type' => 'string', 'enum' => [ 'node', 'parameter-group', 'subnet-group', 'cluster', 'user', 'acl', ], ], 'String' => [ 'type' => 'string', ], 'Subnet' => [ 'type' => 'structure', 'members' => [ 'Identifier' => [ 'shape' => 'String', ], 'AvailabilityZone' => [ 'shape' => 'AvailabilityZone', ], 'SupportedNetworkTypes' => [ 'shape' => 'NetworkTypeList', ], ], ], 'SubnetGroup' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'VpcId' => [ 'shape' => 'String', ], 'Subnets' => [ 'shape' => 'SubnetList', ], 'ARN' => [ 'shape' => 'String', ], 'SupportedNetworkTypes' => [ 'shape' => 'NetworkTypeList', ], ], ], 'SubnetGroupAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'SubnetGroupInUseFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'SubnetGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetGroup', ], ], 'SubnetGroupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'SubnetGroupQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'SubnetIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SubnetInUse' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'SubnetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Subnet', ], ], 'SubnetNotAllowedFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'SubnetQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'TStamp' => [ 'type' => 'timestamp', ], 'Tag' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, ], 'TagNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'TagQuotaPerResourceExceeded' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [ 'TagList' => [ 'shape' => 'TagList', ], ], ], 'TargetBucket' => [ 'type' => 'string', 'max' => 255, 'pattern' => '^[A-Za-z0-9._-]+$', ], 'TestFailoverNotAvailableFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'UnprocessedCluster' => [ 'type' => 'structure', 'members' => [ 'ClusterName' => [ 'shape' => 'String', ], 'ErrorType' => [ 'shape' => 'String', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'UnprocessedClusterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UnprocessedCluster', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], 'TagKeys' => [ 'shape' => 'KeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [ 'TagList' => [ 'shape' => 'TagList', ], ], ], 'UpdateACLRequest' => [ 'type' => 'structure', 'required' => [ 'ACLName', ], 'members' => [ 'ACLName' => [ 'shape' => 'String', ], 'UserNamesToAdd' => [ 'shape' => 'UserNameListInput', ], 'UserNamesToRemove' => [ 'shape' => 'UserNameListInput', ], ], ], 'UpdateACLResponse' => [ 'type' => 'structure', 'members' => [ 'ACL' => [ 'shape' => 'ACL', ], ], ], 'UpdateClusterRequest' => [ 'type' => 'structure', 'required' => [ 'ClusterName', ], 'members' => [ 'ClusterName' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'SecurityGroupIds' => [ 'shape' => 'SecurityGroupIdsList', ], 'MaintenanceWindow' => [ 'shape' => 'String', ], 'SnsTopicArn' => [ 'shape' => 'String', ], 'SnsTopicStatus' => [ 'shape' => 'String', ], 'ParameterGroupName' => [ 'shape' => 'String', ], 'SnapshotWindow' => [ 'shape' => 'String', ], 'SnapshotRetentionLimit' => [ 'shape' => 'IntegerOptional', ], 'NodeType' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'ReplicaConfiguration' => [ 'shape' => 'ReplicaConfigurationRequest', ], 'ShardConfiguration' => [ 'shape' => 'ShardConfigurationRequest', ], 'ACLName' => [ 'shape' => 'ACLName', ], 'IpDiscovery' => [ 'shape' => 'IpDiscovery', ], ], ], 'UpdateClusterResponse' => [ 'type' => 'structure', 'members' => [ 'Cluster' => [ 'shape' => 'Cluster', ], ], ], 'UpdateMultiRegionClusterRequest' => [ 'type' => 'structure', 'required' => [ 'MultiRegionClusterName', ], 'members' => [ 'MultiRegionClusterName' => [ 'shape' => 'String', ], 'NodeType' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'ShardConfiguration' => [ 'shape' => 'ShardConfigurationRequest', ], 'MultiRegionParameterGroupName' => [ 'shape' => 'String', ], 'UpdateStrategy' => [ 'shape' => 'UpdateStrategy', ], ], ], 'UpdateMultiRegionClusterResponse' => [ 'type' => 'structure', 'members' => [ 'MultiRegionCluster' => [ 'shape' => 'MultiRegionCluster', ], ], ], 'UpdateParameterGroupRequest' => [ 'type' => 'structure', 'required' => [ 'ParameterGroupName', 'ParameterNameValues', ], 'members' => [ 'ParameterGroupName' => [ 'shape' => 'String', ], 'ParameterNameValues' => [ 'shape' => 'ParameterNameValueList', ], ], ], 'UpdateParameterGroupResponse' => [ 'type' => 'structure', 'members' => [ 'ParameterGroup' => [ 'shape' => 'ParameterGroup', ], ], ], 'UpdateStrategy' => [ 'type' => 'string', 'enum' => [ 'coordinated', 'uncoordinated', ], ], 'UpdateSubnetGroupRequest' => [ 'type' => 'structure', 'required' => [ 'SubnetGroupName', ], 'members' => [ 'SubnetGroupName' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'SubnetIds' => [ 'shape' => 'SubnetIdentifierList', ], ], ], 'UpdateSubnetGroupResponse' => [ 'type' => 'structure', 'members' => [ 'SubnetGroup' => [ 'shape' => 'SubnetGroup', ], ], ], 'UpdateUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserName', ], 'members' => [ 'UserName' => [ 'shape' => 'UserName', ], 'AuthenticationMode' => [ 'shape' => 'AuthenticationMode', ], 'AccessString' => [ 'shape' => 'AccessString', ], ], ], 'UpdateUserResponse' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'User', ], ], ], 'User' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'AccessString' => [ 'shape' => 'String', ], 'ACLNames' => [ 'shape' => 'ACLNameList', ], 'MinimumEngineVersion' => [ 'shape' => 'String', ], 'Authentication' => [ 'shape' => 'Authentication', ], 'ARN' => [ 'shape' => 'String', ], ], ], 'UserAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'UserList' => [ 'type' => 'list', 'member' => [ 'shape' => 'User', ], ], 'UserName' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[a-zA-Z][a-zA-Z0-9\\-]*', ], 'UserNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserName', ], ], 'UserNameListInput' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserName', ], 'min' => 1, ], 'UserNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'UserQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], ],];
