<?php
// This file was auto-generated from sdk-root/src/data/notificationscontacts/2018-05-10/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-10', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'notifications-contacts', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS User Notifications Contacts', 'serviceId' => 'NotificationsContacts', 'signatureVersion' => 'v4', 'signingName' => 'notifications-contacts', 'uid' => 'notificationscontacts-2018-05-10', ], 'operations' => [ 'ActivateEmailContact' => [ 'name' => 'ActivateEmailContact', 'http' => [ 'method' => 'PUT', 'requestUri' => '/emailcontacts/{arn}/activate/{code}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ActivateEmailContactRequest', ], 'output' => [ 'shape' => 'ActivateEmailContactResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'CreateEmailContact' => [ 'name' => 'CreateEmailContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/2022-09-19/emailcontacts', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateEmailContactRequest', ], 'output' => [ 'shape' => 'CreateEmailContactResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteEmailContact' => [ 'name' => 'DeleteEmailContact', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/emailcontacts/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteEmailContactRequest', ], 'output' => [ 'shape' => 'DeleteEmailContactResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'GetEmailContact' => [ 'name' => 'GetEmailContact', 'http' => [ 'method' => 'GET', 'requestUri' => '/emailcontacts/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEmailContactRequest', ], 'output' => [ 'shape' => 'GetEmailContactResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListEmailContacts' => [ 'name' => 'ListEmailContacts', 'http' => [ 'method' => 'GET', 'requestUri' => '/emailcontacts', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEmailContactsRequest', ], 'output' => [ 'shape' => 'ListEmailContactsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'SendActivationCode' => [ 'name' => 'SendActivationCode', 'http' => [ 'method' => 'POST', 'requestUri' => '/2022-10-31/emailcontacts/{arn}/activate/send', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SendActivationCodeRequest', ], 'output' => [ 'shape' => 'SendActivationCodeResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'ActivateEmailContactRequest' => [ 'type' => 'structure', 'required' => [ 'arn', 'code', ], 'members' => [ 'arn' => [ 'shape' => 'EmailContactArn', 'location' => 'uri', 'locationName' => 'arn', ], 'code' => [ 'shape' => 'Token', 'location' => 'uri', 'locationName' => 'code', ], ], ], 'ActivateEmailContactResponse' => [ 'type' => 'structure', 'members' => [], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateEmailContactRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'emailAddress', ], 'members' => [ 'name' => [ 'shape' => 'EmailContactName', ], 'emailAddress' => [ 'shape' => 'EmailContactAddress', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateEmailContactResponse' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'EmailContactArn', ], ], ], 'CreationTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'DeleteEmailContactRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'EmailContactArn', 'location' => 'uri', 'locationName' => 'arn', ], ], ], 'DeleteEmailContactResponse' => [ 'type' => 'structure', 'members' => [], ], 'EmailContact' => [ 'type' => 'structure', 'required' => [ 'arn', 'name', 'address', 'status', 'creationTime', 'updateTime', ], 'members' => [ 'arn' => [ 'shape' => 'EmailContactArn', ], 'name' => [ 'shape' => 'EmailContactName', ], 'address' => [ 'shape' => 'SensitiveEmailContactAddress', ], 'status' => [ 'shape' => 'EmailContactStatus', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'updateTime' => [ 'shape' => 'UpdateTime', ], ], ], 'EmailContactAddress' => [ 'type' => 'string', 'max' => 254, 'min' => 6, 'pattern' => '(.+)@(.+)', ], 'EmailContactArn' => [ 'type' => 'string', 'pattern' => 'arn:aws:notifications-contacts::[0-9]{12}:emailcontact/[a-z0-9]{27}', ], 'EmailContactName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '.*[\\w-.~]+.*', 'sensitive' => true, ], 'EmailContactStatus' => [ 'type' => 'string', 'enum' => [ 'inactive', 'active', ], ], 'EmailContacts' => [ 'type' => 'list', 'member' => [ 'shape' => 'EmailContact', ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'GetEmailContactRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'EmailContactArn', 'location' => 'uri', 'locationName' => 'arn', ], ], ], 'GetEmailContactResponse' => [ 'type' => 'structure', 'required' => [ 'emailContact', ], 'members' => [ 'emailContact' => [ 'shape' => 'EmailContact', ], ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'ListEmailContactsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListEmailContactsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListEmailContactsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListEmailContactsResponse' => [ 'type' => 'structure', 'required' => [ 'emailContacts', ], 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'emailContacts' => [ 'shape' => 'EmailContacts', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'EmailContactArn', 'location' => 'uri', 'locationName' => 'arn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'QuotaCode' => [ 'type' => 'string', ], 'ResourceId' => [ 'type' => 'string', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', ], 'SendActivationCodeRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'EmailContactArn', 'location' => 'uri', 'locationName' => 'arn', ], ], ], 'SendActivationCodeResponse' => [ 'type' => 'structure', 'members' => [], ], 'SensitiveEmailContactAddress' => [ 'type' => 'string', 'max' => 254, 'min' => 6, 'pattern' => '(.+)@(.+)', 'sensitive' => true, ], 'ServiceCode' => [ 'type' => 'string', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', 'serviceCode', 'quotaCode', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'serviceCode' => [ 'shape' => 'ServiceCode', ], 'quotaCode' => [ 'shape' => 'QuotaCode', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'pattern' => '(?!aws:).{1,128}', ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'arn', 'tags', ], 'members' => [ 'arn' => [ 'shape' => 'EmailContactArn', 'location' => 'uri', 'locationName' => 'arn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'serviceCode' => [ 'shape' => 'ServiceCode', ], 'quotaCode' => [ 'shape' => 'QuotaCode', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'Token' => [ 'type' => 'string', 'max' => 7, 'min' => 7, 'pattern' => '[a-z0-9]{7}', 'sensitive' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'arn', 'tagKeys', ], 'members' => [ 'arn' => [ 'shape' => 'EmailContactArn', 'location' => 'uri', 'locationName' => 'arn', ], 'tagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'name', 'message', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'fieldValidationFailed', 'other', ], ], ],];
