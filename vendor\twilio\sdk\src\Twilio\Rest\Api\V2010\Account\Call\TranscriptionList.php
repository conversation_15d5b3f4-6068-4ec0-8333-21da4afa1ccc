<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Api\V2010\Account\Call;

use Twilio\Exceptions\TwilioException;
use Twilio\ListResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\Serialize;


class TranscriptionList extends ListResource
    {
    /**
     * Construct the TranscriptionList
     *
     * @param Version $version Version that contains the resource
     * @param string $accountSid The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created this Transcription resource.
     * @param string $callSid The SID of the [Call](https://www.twilio.com/docs/voice/api/call-resource) the Transcription resource is associated with.
     */
    public function __construct(
        Version $version,
        string $accountSid,
        string $callSid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'accountSid' =>
            $accountSid,
        
        'callSid' =>
            $callSid,
        
        ];

        $this->uri = '/Accounts/' . \rawurlencode($accountSid)
        .'/Calls/' . \rawurlencode($callSid)
        .'/Transcriptions.json';
    }

    /**
     * Create the TranscriptionInstance
     *
     * @param array|Options $options Optional Arguments
     * @return TranscriptionInstance Created TranscriptionInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function create(array $options = []): TranscriptionInstance
    {

        $options = new Values($options);

        $data = Values::of([
            'Name' =>
                $options['name'],
            'Track' =>
                $options['track'],
            'StatusCallbackUrl' =>
                $options['statusCallbackUrl'],
            'StatusCallbackMethod' =>
                $options['statusCallbackMethod'],
            'InboundTrackLabel' =>
                $options['inboundTrackLabel'],
            'OutboundTrackLabel' =>
                $options['outboundTrackLabel'],
            'PartialResults' =>
                Serialize::booleanToString($options['partialResults']),
            'LanguageCode' =>
                $options['languageCode'],
            'TranscriptionEngine' =>
                $options['transcriptionEngine'],
            'ProfanityFilter' =>
                Serialize::booleanToString($options['profanityFilter']),
            'SpeechModel' =>
                $options['speechModel'],
            'Hints' =>
                $options['hints'],
            'EnableAutomaticPunctuation' =>
                Serialize::booleanToString($options['enableAutomaticPunctuation']),
            'IntelligenceService' =>
                $options['intelligenceService'],
        ]);

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json' ]);
        $payload = $this->version->create('POST', $this->uri, [], $data, $headers);

        return new TranscriptionInstance(
            $this->version,
            $payload,
            $this->solution['accountSid'],
            $this->solution['callSid']
        );
    }


    /**
     * Constructs a TranscriptionContext
     *
     * @param string $sid The SID of the Transcription resource, or the `name` used when creating the resource
     */
    public function getContext(
        string $sid
        
    ): TranscriptionContext
    {
        return new TranscriptionContext(
            $this->version,
            $this->solution['accountSid'],
            $this->solution['callSid'],
            $sid
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Api.V2010.TranscriptionList]';
    }
}
