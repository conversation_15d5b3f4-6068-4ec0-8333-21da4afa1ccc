<?php
namespace Aws\SSMGuiConnect;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS SSM-GUIConnect** service.
 * @method \Aws\Result deleteConnectionRecordingPreferences(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteConnectionRecordingPreferencesAsync(array $args = [])
 * @method \Aws\Result getConnectionRecordingPreferences(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getConnectionRecordingPreferencesAsync(array $args = [])
 * @method \Aws\Result updateConnectionRecordingPreferences(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateConnectionRecordingPreferencesAsync(array $args = [])
 */
class SSMGuiConnectClient extends AwsClient {}
