<?php
// This file was auto-generated from sdk-root/src/data/iotsitewise/2019-12-02/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2019-12-02', 'endpointPrefix' => 'iotsitewise', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS IoT SiteWise', 'serviceId' => 'IoTSiteWise', 'signatureVersion' => 'v4', 'signingName' => 'iotsitewise', 'uid' => 'iotsitewise-2019-12-02', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AssociateAssets' => [ 'name' => 'AssociateAssets', 'http' => [ 'method' => 'POST', 'requestUri' => '/assets/{assetId}/associate', ], 'input' => [ 'shape' => 'AssociateAssetsRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'AssociateTimeSeriesToAssetProperty' => [ 'name' => 'AssociateTimeSeriesToAssetProperty', 'http' => [ 'method' => 'POST', 'requestUri' => '/timeseries/associate/', ], 'input' => [ 'shape' => 'AssociateTimeSeriesToAssetPropertyRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'BatchAssociateProjectAssets' => [ 'name' => 'BatchAssociateProjectAssets', 'http' => [ 'method' => 'POST', 'requestUri' => '/projects/{projectId}/assets/associate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchAssociateProjectAssetsRequest', ], 'output' => [ 'shape' => 'BatchAssociateProjectAssetsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'BatchDisassociateProjectAssets' => [ 'name' => 'BatchDisassociateProjectAssets', 'http' => [ 'method' => 'POST', 'requestUri' => '/projects/{projectId}/assets/disassociate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchDisassociateProjectAssetsRequest', ], 'output' => [ 'shape' => 'BatchDisassociateProjectAssetsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'BatchGetAssetPropertyAggregates' => [ 'name' => 'BatchGetAssetPropertyAggregates', 'http' => [ 'method' => 'POST', 'requestUri' => '/properties/batch/aggregates', ], 'input' => [ 'shape' => 'BatchGetAssetPropertyAggregatesRequest', ], 'output' => [ 'shape' => 'BatchGetAssetPropertyAggregatesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'BatchGetAssetPropertyValue' => [ 'name' => 'BatchGetAssetPropertyValue', 'http' => [ 'method' => 'POST', 'requestUri' => '/properties/batch/latest', ], 'input' => [ 'shape' => 'BatchGetAssetPropertyValueRequest', ], 'output' => [ 'shape' => 'BatchGetAssetPropertyValueResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'BatchGetAssetPropertyValueHistory' => [ 'name' => 'BatchGetAssetPropertyValueHistory', 'http' => [ 'method' => 'POST', 'requestUri' => '/properties/batch/history', ], 'input' => [ 'shape' => 'BatchGetAssetPropertyValueHistoryRequest', ], 'output' => [ 'shape' => 'BatchGetAssetPropertyValueHistoryResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'BatchPutAssetPropertyValue' => [ 'name' => 'BatchPutAssetPropertyValue', 'http' => [ 'method' => 'POST', 'requestUri' => '/properties', ], 'input' => [ 'shape' => 'BatchPutAssetPropertyValueRequest', ], 'output' => [ 'shape' => 'BatchPutAssetPropertyValueResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'CreateAccessPolicy' => [ 'name' => 'CreateAccessPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/access-policies', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateAccessPolicyRequest', ], 'output' => [ 'shape' => 'CreateAccessPolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'CreateAsset' => [ 'name' => 'CreateAsset', 'http' => [ 'method' => 'POST', 'requestUri' => '/assets', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateAssetRequest', ], 'output' => [ 'shape' => 'CreateAssetResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'CreateAssetModel' => [ 'name' => 'CreateAssetModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/asset-models', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateAssetModelRequest', ], 'output' => [ 'shape' => 'CreateAssetModelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'CreateAssetModelCompositeModel' => [ 'name' => 'CreateAssetModelCompositeModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/asset-models/{assetModelId}/composite-models', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateAssetModelCompositeModelRequest', ], 'output' => [ 'shape' => 'CreateAssetModelCompositeModelResponse', ], 'errors' => [ [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'CreateBulkImportJob' => [ 'name' => 'CreateBulkImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/jobs', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateBulkImportJobRequest', ], 'output' => [ 'shape' => 'CreateBulkImportJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'CreateDashboard' => [ 'name' => 'CreateDashboard', 'http' => [ 'method' => 'POST', 'requestUri' => '/dashboards', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateDashboardRequest', ], 'output' => [ 'shape' => 'CreateDashboardResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'CreateDataset' => [ 'name' => 'CreateDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/datasets', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateDatasetRequest', ], 'output' => [ 'shape' => 'CreateDatasetResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'CreateGateway' => [ 'name' => 'CreateGateway', 'http' => [ 'method' => 'POST', 'requestUri' => '/********/gateways', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateGatewayRequest', ], 'output' => [ 'shape' => 'CreateGatewayResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'CreatePortal' => [ 'name' => 'CreatePortal', 'http' => [ 'method' => 'POST', 'requestUri' => '/portals', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreatePortalRequest', ], 'output' => [ 'shape' => 'CreatePortalResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'CreateProject' => [ 'name' => 'CreateProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/projects', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateProjectRequest', ], 'output' => [ 'shape' => 'CreateProjectResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'DeleteAccessPolicy' => [ 'name' => 'DeleteAccessPolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/access-policies/{accessPolicyId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAccessPolicyRequest', ], 'output' => [ 'shape' => 'DeleteAccessPolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'DeleteAsset' => [ 'name' => 'DeleteAsset', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/assets/{assetId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteAssetRequest', ], 'output' => [ 'shape' => 'DeleteAssetResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DeleteAssetModel' => [ 'name' => 'DeleteAssetModel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/asset-models/{assetModelId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteAssetModelRequest', ], 'output' => [ 'shape' => 'DeleteAssetModelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'PreconditionFailedException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DeleteAssetModelCompositeModel' => [ 'name' => 'DeleteAssetModelCompositeModel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/asset-models/{assetModelId}/composite-models/{assetModelCompositeModelId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteAssetModelCompositeModelRequest', ], 'output' => [ 'shape' => 'DeleteAssetModelCompositeModelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'PreconditionFailedException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DeleteDashboard' => [ 'name' => 'DeleteDashboard', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/dashboards/{dashboardId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteDashboardRequest', ], 'output' => [ 'shape' => 'DeleteDashboardResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'DeleteDataset' => [ 'name' => 'DeleteDataset', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/datasets/{datasetId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteDatasetRequest', ], 'output' => [ 'shape' => 'DeleteDatasetResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DeleteGateway' => [ 'name' => 'DeleteGateway', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/********/gateways/{gatewayId}', ], 'input' => [ 'shape' => 'DeleteGatewayRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DeletePortal' => [ 'name' => 'DeletePortal', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/portals/{portalId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeletePortalRequest', ], 'output' => [ 'shape' => 'DeletePortalResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'DeleteProject' => [ 'name' => 'DeleteProject', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/projects/{projectId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteProjectRequest', ], 'output' => [ 'shape' => 'DeleteProjectResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'DeleteTimeSeries' => [ 'name' => 'DeleteTimeSeries', 'http' => [ 'method' => 'POST', 'requestUri' => '/timeseries/delete/', ], 'input' => [ 'shape' => 'DeleteTimeSeriesRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DescribeAccessPolicy' => [ 'name' => 'DescribeAccessPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/access-policies/{accessPolicyId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeAccessPolicyRequest', ], 'output' => [ 'shape' => 'DescribeAccessPolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'DescribeAction' => [ 'name' => 'DescribeAction', 'http' => [ 'method' => 'GET', 'requestUri' => '/actions/{actionId}', ], 'input' => [ 'shape' => 'DescribeActionRequest', ], 'output' => [ 'shape' => 'DescribeActionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DescribeAsset' => [ 'name' => 'DescribeAsset', 'http' => [ 'method' => 'GET', 'requestUri' => '/assets/{assetId}', ], 'input' => [ 'shape' => 'DescribeAssetRequest', ], 'output' => [ 'shape' => 'DescribeAssetResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DescribeAssetCompositeModel' => [ 'name' => 'DescribeAssetCompositeModel', 'http' => [ 'method' => 'GET', 'requestUri' => '/assets/{assetId}/composite-models/{assetCompositeModelId}', ], 'input' => [ 'shape' => 'DescribeAssetCompositeModelRequest', ], 'output' => [ 'shape' => 'DescribeAssetCompositeModelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DescribeAssetModel' => [ 'name' => 'DescribeAssetModel', 'http' => [ 'method' => 'GET', 'requestUri' => '/asset-models/{assetModelId}', ], 'input' => [ 'shape' => 'DescribeAssetModelRequest', ], 'output' => [ 'shape' => 'DescribeAssetModelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DescribeAssetModelCompositeModel' => [ 'name' => 'DescribeAssetModelCompositeModel', 'http' => [ 'method' => 'GET', 'requestUri' => '/asset-models/{assetModelId}/composite-models/{assetModelCompositeModelId}', ], 'input' => [ 'shape' => 'DescribeAssetModelCompositeModelRequest', ], 'output' => [ 'shape' => 'DescribeAssetModelCompositeModelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DescribeAssetProperty' => [ 'name' => 'DescribeAssetProperty', 'http' => [ 'method' => 'GET', 'requestUri' => '/assets/{assetId}/properties/{propertyId}', ], 'input' => [ 'shape' => 'DescribeAssetPropertyRequest', ], 'output' => [ 'shape' => 'DescribeAssetPropertyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DescribeBulkImportJob' => [ 'name' => 'DescribeBulkImportJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/jobs/{jobId}', ], 'input' => [ 'shape' => 'DescribeBulkImportJobRequest', ], 'output' => [ 'shape' => 'DescribeBulkImportJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'DescribeDashboard' => [ 'name' => 'DescribeDashboard', 'http' => [ 'method' => 'GET', 'requestUri' => '/dashboards/{dashboardId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeDashboardRequest', ], 'output' => [ 'shape' => 'DescribeDashboardResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'DescribeDataset' => [ 'name' => 'DescribeDataset', 'http' => [ 'method' => 'GET', 'requestUri' => '/datasets/{datasetId}', ], 'input' => [ 'shape' => 'DescribeDatasetRequest', ], 'output' => [ 'shape' => 'DescribeDatasetResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DescribeDefaultEncryptionConfiguration' => [ 'name' => 'DescribeDefaultEncryptionConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/configuration/account/encryption', ], 'input' => [ 'shape' => 'DescribeDefaultEncryptionConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeDefaultEncryptionConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DescribeGateway' => [ 'name' => 'DescribeGateway', 'http' => [ 'method' => 'GET', 'requestUri' => '/********/gateways/{gatewayId}', ], 'input' => [ 'shape' => 'DescribeGatewayRequest', ], 'output' => [ 'shape' => 'DescribeGatewayResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DescribeGatewayCapabilityConfiguration' => [ 'name' => 'DescribeGatewayCapabilityConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/********/gateways/{gatewayId}/capability/{capabilityNamespace}', ], 'input' => [ 'shape' => 'DescribeGatewayCapabilityConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeGatewayCapabilityConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DescribeLoggingOptions' => [ 'name' => 'DescribeLoggingOptions', 'http' => [ 'method' => 'GET', 'requestUri' => '/logging', ], 'input' => [ 'shape' => 'DescribeLoggingOptionsRequest', ], 'output' => [ 'shape' => 'DescribeLoggingOptionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DescribePortal' => [ 'name' => 'DescribePortal', 'http' => [ 'method' => 'GET', 'requestUri' => '/portals/{portalId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribePortalRequest', ], 'output' => [ 'shape' => 'DescribePortalResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'DescribeProject' => [ 'name' => 'DescribeProject', 'http' => [ 'method' => 'GET', 'requestUri' => '/projects/{projectId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeProjectRequest', ], 'output' => [ 'shape' => 'DescribeProjectResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'DescribeStorageConfiguration' => [ 'name' => 'DescribeStorageConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/configuration/account/storage', ], 'input' => [ 'shape' => 'DescribeStorageConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeStorageConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DescribeTimeSeries' => [ 'name' => 'DescribeTimeSeries', 'http' => [ 'method' => 'GET', 'requestUri' => '/timeseries/describe/', ], 'input' => [ 'shape' => 'DescribeTimeSeriesRequest', ], 'output' => [ 'shape' => 'DescribeTimeSeriesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DisassociateAssets' => [ 'name' => 'DisassociateAssets', 'http' => [ 'method' => 'POST', 'requestUri' => '/assets/{assetId}/disassociate', ], 'input' => [ 'shape' => 'DisassociateAssetsRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DisassociateTimeSeriesFromAssetProperty' => [ 'name' => 'DisassociateTimeSeriesFromAssetProperty', 'http' => [ 'method' => 'POST', 'requestUri' => '/timeseries/disassociate/', ], 'input' => [ 'shape' => 'DisassociateTimeSeriesFromAssetPropertyRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ExecuteAction' => [ 'name' => 'ExecuteAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/actions', 'responseCode' => 202, ], 'input' => [ 'shape' => 'ExecuteActionRequest', ], 'output' => [ 'shape' => 'ExecuteActionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ExecuteQuery' => [ 'name' => 'ExecuteQuery', 'http' => [ 'method' => 'POST', 'requestUri' => '/queries/execution', ], 'input' => [ 'shape' => 'ExecuteQueryRequest', ], 'output' => [ 'shape' => 'ExecuteQueryResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'QueryTimeoutException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'GetAssetPropertyAggregates' => [ 'name' => 'GetAssetPropertyAggregates', 'http' => [ 'method' => 'GET', 'requestUri' => '/properties/aggregates', ], 'input' => [ 'shape' => 'GetAssetPropertyAggregatesRequest', ], 'output' => [ 'shape' => 'GetAssetPropertyAggregatesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'GetAssetPropertyValue' => [ 'name' => 'GetAssetPropertyValue', 'http' => [ 'method' => 'GET', 'requestUri' => '/properties/latest', ], 'input' => [ 'shape' => 'GetAssetPropertyValueRequest', ], 'output' => [ 'shape' => 'GetAssetPropertyValueResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'GetAssetPropertyValueHistory' => [ 'name' => 'GetAssetPropertyValueHistory', 'http' => [ 'method' => 'GET', 'requestUri' => '/properties/history', ], 'input' => [ 'shape' => 'GetAssetPropertyValueHistoryRequest', ], 'output' => [ 'shape' => 'GetAssetPropertyValueHistoryResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'GetInterpolatedAssetPropertyValues' => [ 'name' => 'GetInterpolatedAssetPropertyValues', 'http' => [ 'method' => 'GET', 'requestUri' => '/properties/interpolated', ], 'input' => [ 'shape' => 'GetInterpolatedAssetPropertyValuesRequest', ], 'output' => [ 'shape' => 'GetInterpolatedAssetPropertyValuesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'InvokeAssistant' => [ 'name' => 'InvokeAssistant', 'http' => [ 'method' => 'POST', 'requestUri' => '/assistant/invocation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'InvokeAssistantRequest', ], 'output' => [ 'shape' => 'InvokeAssistantResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'ListAccessPolicies' => [ 'name' => 'ListAccessPolicies', 'http' => [ 'method' => 'GET', 'requestUri' => '/access-policies', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAccessPoliciesRequest', ], 'output' => [ 'shape' => 'ListAccessPoliciesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'ListActions' => [ 'name' => 'ListActions', 'http' => [ 'method' => 'GET', 'requestUri' => '/actions', ], 'input' => [ 'shape' => 'ListActionsRequest', ], 'output' => [ 'shape' => 'ListActionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListAssetModelCompositeModels' => [ 'name' => 'ListAssetModelCompositeModels', 'http' => [ 'method' => 'GET', 'requestUri' => '/asset-models/{assetModelId}/composite-models', ], 'input' => [ 'shape' => 'ListAssetModelCompositeModelsRequest', ], 'output' => [ 'shape' => 'ListAssetModelCompositeModelsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListAssetModelProperties' => [ 'name' => 'ListAssetModelProperties', 'http' => [ 'method' => 'GET', 'requestUri' => '/asset-models/{assetModelId}/properties', ], 'input' => [ 'shape' => 'ListAssetModelPropertiesRequest', ], 'output' => [ 'shape' => 'ListAssetModelPropertiesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListAssetModels' => [ 'name' => 'ListAssetModels', 'http' => [ 'method' => 'GET', 'requestUri' => '/asset-models', ], 'input' => [ 'shape' => 'ListAssetModelsRequest', ], 'output' => [ 'shape' => 'ListAssetModelsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListAssetProperties' => [ 'name' => 'ListAssetProperties', 'http' => [ 'method' => 'GET', 'requestUri' => '/assets/{assetId}/properties', ], 'input' => [ 'shape' => 'ListAssetPropertiesRequest', ], 'output' => [ 'shape' => 'ListAssetPropertiesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListAssetRelationships' => [ 'name' => 'ListAssetRelationships', 'http' => [ 'method' => 'GET', 'requestUri' => '/assets/{assetId}/assetRelationships', ], 'input' => [ 'shape' => 'ListAssetRelationshipsRequest', ], 'output' => [ 'shape' => 'ListAssetRelationshipsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListAssets' => [ 'name' => 'ListAssets', 'http' => [ 'method' => 'GET', 'requestUri' => '/assets', ], 'input' => [ 'shape' => 'ListAssetsRequest', ], 'output' => [ 'shape' => 'ListAssetsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListAssociatedAssets' => [ 'name' => 'ListAssociatedAssets', 'http' => [ 'method' => 'GET', 'requestUri' => '/assets/{assetId}/hierarchies', ], 'input' => [ 'shape' => 'ListAssociatedAssetsRequest', ], 'output' => [ 'shape' => 'ListAssociatedAssetsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListBulkImportJobs' => [ 'name' => 'ListBulkImportJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/jobs', ], 'input' => [ 'shape' => 'ListBulkImportJobsRequest', ], 'output' => [ 'shape' => 'ListBulkImportJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'ListCompositionRelationships' => [ 'name' => 'ListCompositionRelationships', 'http' => [ 'method' => 'GET', 'requestUri' => '/asset-models/{assetModelId}/composition-relationships', ], 'input' => [ 'shape' => 'ListCompositionRelationshipsRequest', ], 'output' => [ 'shape' => 'ListCompositionRelationshipsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListDashboards' => [ 'name' => 'ListDashboards', 'http' => [ 'method' => 'GET', 'requestUri' => '/dashboards', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDashboardsRequest', ], 'output' => [ 'shape' => 'ListDashboardsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'ListDatasets' => [ 'name' => 'ListDatasets', 'http' => [ 'method' => 'GET', 'requestUri' => '/datasets', ], 'input' => [ 'shape' => 'ListDatasetsRequest', ], 'output' => [ 'shape' => 'ListDatasetsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListGateways' => [ 'name' => 'ListGateways', 'http' => [ 'method' => 'GET', 'requestUri' => '/********/gateways', ], 'input' => [ 'shape' => 'ListGatewaysRequest', ], 'output' => [ 'shape' => 'ListGatewaysResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListPortals' => [ 'name' => 'ListPortals', 'http' => [ 'method' => 'GET', 'requestUri' => '/portals', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPortalsRequest', ], 'output' => [ 'shape' => 'ListPortalsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'ListProjectAssets' => [ 'name' => 'ListProjectAssets', 'http' => [ 'method' => 'GET', 'requestUri' => '/projects/{projectId}/assets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListProjectAssetsRequest', ], 'output' => [ 'shape' => 'ListProjectAssetsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'ListProjects' => [ 'name' => 'ListProjects', 'http' => [ 'method' => 'GET', 'requestUri' => '/projects', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListProjectsRequest', ], 'output' => [ 'shape' => 'ListProjectsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnauthorizedException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListTimeSeries' => [ 'name' => 'ListTimeSeries', 'http' => [ 'method' => 'GET', 'requestUri' => '/timeseries/', ], 'input' => [ 'shape' => 'ListTimeSeriesRequest', ], 'output' => [ 'shape' => 'ListTimeSeriesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'PutDefaultEncryptionConfiguration' => [ 'name' => 'PutDefaultEncryptionConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/configuration/account/encryption', ], 'input' => [ 'shape' => 'PutDefaultEncryptionConfigurationRequest', ], 'output' => [ 'shape' => 'PutDefaultEncryptionConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'PutLoggingOptions' => [ 'name' => 'PutLoggingOptions', 'http' => [ 'method' => 'PUT', 'requestUri' => '/logging', ], 'input' => [ 'shape' => 'PutLoggingOptionsRequest', ], 'output' => [ 'shape' => 'PutLoggingOptionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'PutStorageConfiguration' => [ 'name' => 'PutStorageConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/configuration/account/storage', ], 'input' => [ 'shape' => 'PutStorageConfigurationRequest', ], 'output' => [ 'shape' => 'PutStorageConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'TooManyTagsException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnauthorizedException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UpdateAccessPolicy' => [ 'name' => 'UpdateAccessPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/access-policies/{accessPolicyId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAccessPolicyRequest', ], 'output' => [ 'shape' => 'UpdateAccessPolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'UpdateAsset' => [ 'name' => 'UpdateAsset', 'http' => [ 'method' => 'PUT', 'requestUri' => '/assets/{assetId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateAssetRequest', ], 'output' => [ 'shape' => 'UpdateAssetResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UpdateAssetModel' => [ 'name' => 'UpdateAssetModel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/asset-models/{assetModelId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateAssetModelRequest', ], 'output' => [ 'shape' => 'UpdateAssetModelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'PreconditionFailedException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UpdateAssetModelCompositeModel' => [ 'name' => 'UpdateAssetModelCompositeModel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/asset-models/{assetModelId}/composite-models/{assetModelCompositeModelId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateAssetModelCompositeModelRequest', ], 'output' => [ 'shape' => 'UpdateAssetModelCompositeModelResponse', ], 'errors' => [ [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UpdateAssetProperty' => [ 'name' => 'UpdateAssetProperty', 'http' => [ 'method' => 'PUT', 'requestUri' => '/assets/{assetId}/properties/{propertyId}', ], 'input' => [ 'shape' => 'UpdateAssetPropertyRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UpdateDashboard' => [ 'name' => 'UpdateDashboard', 'http' => [ 'method' => 'PUT', 'requestUri' => '/dashboards/{dashboardId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDashboardRequest', ], 'output' => [ 'shape' => 'UpdateDashboardResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'UpdateDataset' => [ 'name' => 'UpdateDataset', 'http' => [ 'method' => 'PUT', 'requestUri' => '/datasets/{datasetId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateDatasetRequest', ], 'output' => [ 'shape' => 'UpdateDatasetResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UpdateGateway' => [ 'name' => 'UpdateGateway', 'http' => [ 'method' => 'PUT', 'requestUri' => '/********/gateways/{gatewayId}', ], 'input' => [ 'shape' => 'UpdateGatewayRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UpdateGatewayCapabilityConfiguration' => [ 'name' => 'UpdateGatewayCapabilityConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/********/gateways/{gatewayId}/capability', 'responseCode' => 201, ], 'input' => [ 'shape' => 'UpdateGatewayCapabilityConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateGatewayCapabilityConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UpdatePortal' => [ 'name' => 'UpdatePortal', 'http' => [ 'method' => 'PUT', 'requestUri' => '/portals/{portalId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdatePortalRequest', ], 'output' => [ 'shape' => 'UpdatePortalResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'UpdateProject' => [ 'name' => 'UpdateProject', 'http' => [ 'method' => 'PUT', 'requestUri' => '/projects/{projectId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateProjectRequest', ], 'output' => [ 'shape' => 'UpdateProjectResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], ], 'shapes' => [ 'ARN' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '^arn:aws(-cn|-us-gov)?:[a-zA-Z0-9-:\\/_\\.]+$', ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AccessPolicySummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessPolicySummary', ], ], 'AccessPolicySummary' => [ 'type' => 'structure', 'required' => [ 'id', 'identity', 'resource', 'permission', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'identity' => [ 'shape' => 'Identity', ], 'resource' => [ 'shape' => 'Resource', ], 'permission' => [ 'shape' => 'Permission', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastUpdateDate' => [ 'shape' => 'Timestamp', ], ], ], 'ActionDefinition' => [ 'type' => 'structure', 'required' => [ 'actionDefinitionId', 'actionName', 'actionType', ], 'members' => [ 'actionDefinitionId' => [ 'shape' => 'ID', ], 'actionName' => [ 'shape' => 'Name', ], 'actionType' => [ 'shape' => 'Name', ], ], ], 'ActionDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActionDefinition', ], ], 'ActionPayload' => [ 'type' => 'structure', 'required' => [ 'stringValue', ], 'members' => [ 'stringValue' => [ 'shape' => 'ActionPayloadString', ], ], ], 'ActionPayloadString' => [ 'type' => 'string', 'pattern' => '[^\\u0000-\\u001F\\u007F]+', ], 'ActionSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActionSummary', ], ], 'ActionSummary' => [ 'type' => 'structure', 'members' => [ 'actionId' => [ 'shape' => 'ID', ], 'actionDefinitionId' => [ 'shape' => 'ID', ], 'targetResource' => [ 'shape' => 'TargetResource', ], ], ], 'AdaptiveIngestion' => [ 'type' => 'boolean', ], 'AggregateType' => [ 'type' => 'string', 'enum' => [ 'AVERAGE', 'COUNT', 'MAXIMUM', 'MINIMUM', 'SUM', 'STANDARD_DEVIATION', ], ], 'AggregateTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregateType', ], 'min' => 1, ], 'AggregatedDoubleValue' => [ 'type' => 'double', ], 'AggregatedValue' => [ 'type' => 'structure', 'required' => [ 'timestamp', 'value', ], 'members' => [ 'timestamp' => [ 'shape' => 'Timestamp', ], 'quality' => [ 'shape' => 'Quality', ], 'value' => [ 'shape' => 'Aggregates', ], ], ], 'AggregatedValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregatedValue', ], ], 'Aggregates' => [ 'type' => 'structure', 'members' => [ 'average' => [ 'shape' => 'AggregatedDoubleValue', ], 'count' => [ 'shape' => 'AggregatedDoubleValue', ], 'maximum' => [ 'shape' => 'AggregatedDoubleValue', ], 'minimum' => [ 'shape' => 'AggregatedDoubleValue', ], 'sum' => [ 'shape' => 'AggregatedDoubleValue', ], 'standardDeviation' => [ 'shape' => 'AggregatedDoubleValue', ], ], ], 'Alarms' => [ 'type' => 'structure', 'required' => [ 'alarmRoleArn', ], 'members' => [ 'alarmRoleArn' => [ 'shape' => 'IamArn', ], 'notificationLambdaArn' => [ 'shape' => 'ARN', ], ], ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, ], 'AssetCompositeModel' => [ 'type' => 'structure', 'required' => [ 'name', 'type', 'properties', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'type' => [ 'shape' => 'Name', ], 'properties' => [ 'shape' => 'AssetProperties', ], 'id' => [ 'shape' => 'ID', ], 'externalId' => [ 'shape' => 'ExternalId', ], ], ], 'AssetCompositeModelPath' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetCompositeModelPathSegment', ], ], 'AssetCompositeModelPathSegment' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ID', ], 'name' => [ 'shape' => 'Name', ], ], ], 'AssetCompositeModelSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetCompositeModelSummary', ], ], 'AssetCompositeModelSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'name', 'type', 'description', 'path', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'externalId' => [ 'shape' => 'ExternalId', ], 'name' => [ 'shape' => 'Name', ], 'type' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'path' => [ 'shape' => 'AssetCompositeModelPath', ], ], ], 'AssetCompositeModels' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetCompositeModel', ], ], 'AssetErrorCode' => [ 'type' => 'string', 'enum' => [ 'INTERNAL_FAILURE', ], ], 'AssetErrorDetails' => [ 'type' => 'structure', 'required' => [ 'assetId', 'code', 'message', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', ], 'code' => [ 'shape' => 'AssetErrorCode', ], 'message' => [ 'shape' => 'AssetErrorMessage', ], ], ], 'AssetErrorMessage' => [ 'type' => 'string', ], 'AssetHierarchies' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetHierarchy', ], ], 'AssetHierarchy' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'name' => [ 'shape' => 'Name', ], 'externalId' => [ 'shape' => 'ExternalId', ], ], ], 'AssetHierarchyInfo' => [ 'type' => 'structure', 'members' => [ 'parentAssetId' => [ 'shape' => 'ID', ], 'childAssetId' => [ 'shape' => 'ID', ], ], ], 'AssetIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'ID', ], ], 'AssetModelCompositeModel' => [ 'type' => 'structure', 'required' => [ 'name', 'type', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'type' => [ 'shape' => 'Name', ], 'properties' => [ 'shape' => 'AssetModelProperties', ], 'id' => [ 'shape' => 'CustomID', ], 'externalId' => [ 'shape' => 'ExternalId', ], ], ], 'AssetModelCompositeModelDefinition' => [ 'type' => 'structure', 'required' => [ 'name', 'type', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'externalId' => [ 'shape' => 'ExternalId', ], 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'type' => [ 'shape' => 'Name', ], 'properties' => [ 'shape' => 'AssetModelPropertyDefinitions', ], ], ], 'AssetModelCompositeModelDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetModelCompositeModelDefinition', ], ], 'AssetModelCompositeModelPath' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetModelCompositeModelPathSegment', ], ], 'AssetModelCompositeModelPathSegment' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ID', ], 'name' => [ 'shape' => 'Name', ], ], ], 'AssetModelCompositeModelSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetModelCompositeModelSummary', ], ], 'AssetModelCompositeModelSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'name', 'type', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'externalId' => [ 'shape' => 'ExternalId', ], 'name' => [ 'shape' => 'Name', ], 'type' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'path' => [ 'shape' => 'AssetModelCompositeModelPath', ], ], ], 'AssetModelCompositeModels' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetModelCompositeModel', ], ], 'AssetModelHierarchies' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetModelHierarchy', ], ], 'AssetModelHierarchy' => [ 'type' => 'structure', 'required' => [ 'name', 'childAssetModelId', ], 'members' => [ 'id' => [ 'shape' => 'CustomID', ], 'externalId' => [ 'shape' => 'ExternalId', ], 'name' => [ 'shape' => 'Name', ], 'childAssetModelId' => [ 'shape' => 'CustomID', ], ], ], 'AssetModelHierarchyDefinition' => [ 'type' => 'structure', 'required' => [ 'name', 'childAssetModelId', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'externalId' => [ 'shape' => 'ExternalId', ], 'name' => [ 'shape' => 'Name', ], 'childAssetModelId' => [ 'shape' => 'CustomID', ], ], ], 'AssetModelHierarchyDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetModelHierarchyDefinition', ], ], 'AssetModelProperties' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetModelProperty', ], ], 'AssetModelProperty' => [ 'type' => 'structure', 'required' => [ 'name', 'dataType', 'type', ], 'members' => [ 'id' => [ 'shape' => 'CustomID', ], 'externalId' => [ 'shape' => 'ExternalId', ], 'name' => [ 'shape' => 'Name', ], 'dataType' => [ 'shape' => 'PropertyDataType', ], 'dataTypeSpec' => [ 'shape' => 'Name', ], 'unit' => [ 'shape' => 'PropertyUnit', ], 'type' => [ 'shape' => 'PropertyType', ], 'path' => [ 'shape' => 'AssetModelPropertyPath', ], ], ], 'AssetModelPropertyDefinition' => [ 'type' => 'structure', 'required' => [ 'name', 'dataType', 'type', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'externalId' => [ 'shape' => 'ExternalId', ], 'name' => [ 'shape' => 'Name', ], 'dataType' => [ 'shape' => 'PropertyDataType', ], 'dataTypeSpec' => [ 'shape' => 'Name', ], 'unit' => [ 'shape' => 'PropertyUnit', ], 'type' => [ 'shape' => 'PropertyType', ], ], ], 'AssetModelPropertyDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetModelPropertyDefinition', ], ], 'AssetModelPropertyPath' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetModelPropertyPathSegment', ], ], 'AssetModelPropertyPathSegment' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ID', ], 'name' => [ 'shape' => 'Name', ], ], ], 'AssetModelPropertySummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetModelPropertySummary', ], ], 'AssetModelPropertySummary' => [ 'type' => 'structure', 'required' => [ 'name', 'dataType', 'type', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'externalId' => [ 'shape' => 'ExternalId', ], 'name' => [ 'shape' => 'Name', ], 'dataType' => [ 'shape' => 'PropertyDataType', ], 'dataTypeSpec' => [ 'shape' => 'Name', ], 'unit' => [ 'shape' => 'PropertyUnit', ], 'type' => [ 'shape' => 'PropertyType', ], 'assetModelCompositeModelId' => [ 'shape' => 'ID', ], 'path' => [ 'shape' => 'AssetModelPropertyPath', ], ], ], 'AssetModelState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'UPDATING', 'PROPAGATING', 'DELETING', 'FAILED', ], ], 'AssetModelStatus' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'AssetModelState', ], 'error' => [ 'shape' => 'ErrorDetails', ], ], ], 'AssetModelSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetModelSummary', ], ], 'AssetModelSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'name', 'description', 'creationDate', 'lastUpdateDate', 'status', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'externalId' => [ 'shape' => 'ExternalId', ], 'arn' => [ 'shape' => 'ARN', ], 'name' => [ 'shape' => 'Name', ], 'assetModelType' => [ 'shape' => 'AssetModelType', ], 'description' => [ 'shape' => 'Description', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastUpdateDate' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'AssetModelStatus', ], 'version' => [ 'shape' => 'Version', ], ], ], 'AssetModelType' => [ 'type' => 'string', 'enum' => [ 'ASSET_MODEL', 'COMPONENT_MODEL', ], ], 'AssetModelVersionFilter' => [ 'type' => 'string', 'pattern' => '^(LATEST|ACTIVE)$', ], 'AssetModelVersionType' => [ 'type' => 'string', 'enum' => [ 'LATEST', 'ACTIVE', ], ], 'AssetProperties' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetProperty', ], ], 'AssetProperty' => [ 'type' => 'structure', 'required' => [ 'id', 'name', 'dataType', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'name' => [ 'shape' => 'Name', ], 'alias' => [ 'shape' => 'PropertyAlias', ], 'notification' => [ 'shape' => 'PropertyNotification', ], 'dataType' => [ 'shape' => 'PropertyDataType', ], 'dataTypeSpec' => [ 'shape' => 'Name', ], 'unit' => [ 'shape' => 'PropertyUnit', ], 'path' => [ 'shape' => 'AssetPropertyPath', ], 'externalId' => [ 'shape' => 'ExternalId', ], ], ], 'AssetPropertyAlias' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[^\\u0000-\\u001F\\u007F]+', ], 'AssetPropertyPath' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetPropertyPathSegment', ], ], 'AssetPropertyPathSegment' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ID', ], 'name' => [ 'shape' => 'Name', ], ], ], 'AssetPropertySummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetPropertySummary', ], ], 'AssetPropertySummary' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'alias' => [ 'shape' => 'PropertyAlias', ], 'unit' => [ 'shape' => 'PropertyUnit', ], 'notification' => [ 'shape' => 'PropertyNotification', ], 'assetCompositeModelId' => [ 'shape' => 'ID', ], 'path' => [ 'shape' => 'AssetPropertyPath', ], 'externalId' => [ 'shape' => 'ExternalId', ], ], ], 'AssetPropertyValue' => [ 'type' => 'structure', 'required' => [ 'value', 'timestamp', ], 'members' => [ 'value' => [ 'shape' => 'Variant', ], 'timestamp' => [ 'shape' => 'TimeInNanos', ], 'quality' => [ 'shape' => 'Quality', ], ], ], 'AssetPropertyValueHistory' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetPropertyValue', ], ], 'AssetPropertyValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetPropertyValue', ], ], 'AssetRelationshipSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetRelationshipSummary', ], ], 'AssetRelationshipSummary' => [ 'type' => 'structure', 'required' => [ 'relationshipType', ], 'members' => [ 'hierarchyInfo' => [ 'shape' => 'AssetHierarchyInfo', ], 'relationshipType' => [ 'shape' => 'AssetRelationshipType', ], ], ], 'AssetRelationshipType' => [ 'type' => 'string', 'enum' => [ 'HIERARCHY', ], ], 'AssetState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'UPDATING', 'DELETING', 'FAILED', ], ], 'AssetStatus' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'AssetState', ], 'error' => [ 'shape' => 'ErrorDetails', ], ], ], 'AssetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetSummary', ], ], 'AssetSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'name', 'assetModelId', 'creationDate', 'lastUpdateDate', 'status', 'hierarchies', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'arn' => [ 'shape' => 'ARN', ], 'name' => [ 'shape' => 'Name', ], 'assetModelId' => [ 'shape' => 'ID', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastUpdateDate' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'AssetStatus', ], 'hierarchies' => [ 'shape' => 'AssetHierarchies', ], 'description' => [ 'shape' => 'Description', ], 'externalId' => [ 'shape' => 'ExternalId', ], ], ], 'AssociateAssetsRequest' => [ 'type' => 'structure', 'required' => [ 'assetId', 'hierarchyId', 'childAssetId', ], 'members' => [ 'assetId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetId', ], 'hierarchyId' => [ 'shape' => 'CustomID', ], 'childAssetId' => [ 'shape' => 'CustomID', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'AssociateTimeSeriesToAssetPropertyRequest' => [ 'type' => 'structure', 'required' => [ 'alias', 'assetId', 'propertyId', ], 'members' => [ 'alias' => [ 'shape' => 'PropertyAlias', 'location' => 'querystring', 'locationName' => 'alias', ], 'assetId' => [ 'shape' => 'CustomID', 'location' => 'querystring', 'locationName' => 'assetId', ], 'propertyId' => [ 'shape' => 'CustomID', 'location' => 'querystring', 'locationName' => 'propertyId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'AssociatedAssetsSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociatedAssetsSummary', ], ], 'AssociatedAssetsSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'name', 'assetModelId', 'creationDate', 'lastUpdateDate', 'status', 'hierarchies', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'arn' => [ 'shape' => 'ARN', ], 'name' => [ 'shape' => 'Name', ], 'assetModelId' => [ 'shape' => 'ID', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastUpdateDate' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'AssetStatus', ], 'hierarchies' => [ 'shape' => 'AssetHierarchies', ], 'description' => [ 'shape' => 'Description', ], 'externalId' => [ 'shape' => 'ExternalId', ], ], ], 'Attribute' => [ 'type' => 'structure', 'members' => [ 'defaultValue' => [ 'shape' => 'DefaultValue', ], ], ], 'AuthMode' => [ 'type' => 'string', 'enum' => [ 'IAM', 'SSO', ], ], 'BatchAssociateProjectAssetsErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetErrorDetails', ], ], 'BatchAssociateProjectAssetsRequest' => [ 'type' => 'structure', 'required' => [ 'projectId', 'assetIds', ], 'members' => [ 'projectId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'projectId', ], 'assetIds' => [ 'shape' => 'IDs', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'BatchAssociateProjectAssetsResponse' => [ 'type' => 'structure', 'members' => [ 'errors' => [ 'shape' => 'BatchAssociateProjectAssetsErrors', ], ], ], 'BatchDisassociateProjectAssetsErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetErrorDetails', ], ], 'BatchDisassociateProjectAssetsRequest' => [ 'type' => 'structure', 'required' => [ 'projectId', 'assetIds', ], 'members' => [ 'projectId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'projectId', ], 'assetIds' => [ 'shape' => 'IDs', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'BatchDisassociateProjectAssetsResponse' => [ 'type' => 'structure', 'members' => [ 'errors' => [ 'shape' => 'BatchDisassociateProjectAssetsErrors', ], ], ], 'BatchEntryCompletionStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCESS', 'ERROR', ], ], 'BatchGetAssetPropertyAggregatesEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetAssetPropertyAggregatesEntry', ], ], 'BatchGetAssetPropertyAggregatesEntry' => [ 'type' => 'structure', 'required' => [ 'entryId', 'aggregateTypes', 'resolution', 'startDate', 'endDate', ], 'members' => [ 'entryId' => [ 'shape' => 'EntryId', ], 'assetId' => [ 'shape' => 'ID', ], 'propertyId' => [ 'shape' => 'ID', ], 'propertyAlias' => [ 'shape' => 'AssetPropertyAlias', ], 'aggregateTypes' => [ 'shape' => 'AggregateTypes', ], 'resolution' => [ 'shape' => 'Resolution', ], 'startDate' => [ 'shape' => 'Timestamp', ], 'endDate' => [ 'shape' => 'Timestamp', ], 'qualities' => [ 'shape' => 'Qualities', ], 'timeOrdering' => [ 'shape' => 'TimeOrdering', ], ], ], 'BatchGetAssetPropertyAggregatesErrorCode' => [ 'type' => 'string', 'enum' => [ 'ResourceNotFoundException', 'InvalidRequestException', 'AccessDeniedException', ], ], 'BatchGetAssetPropertyAggregatesErrorEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetAssetPropertyAggregatesErrorEntry', ], ], 'BatchGetAssetPropertyAggregatesErrorEntry' => [ 'type' => 'structure', 'required' => [ 'errorCode', 'errorMessage', 'entryId', ], 'members' => [ 'errorCode' => [ 'shape' => 'BatchGetAssetPropertyAggregatesErrorCode', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], 'entryId' => [ 'shape' => 'EntryId', ], ], ], 'BatchGetAssetPropertyAggregatesErrorInfo' => [ 'type' => 'structure', 'required' => [ 'errorCode', 'errorTimestamp', ], 'members' => [ 'errorCode' => [ 'shape' => 'BatchGetAssetPropertyAggregatesErrorCode', ], 'errorTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'BatchGetAssetPropertyAggregatesMaxResults' => [ 'type' => 'integer', 'min' => 1, ], 'BatchGetAssetPropertyAggregatesRequest' => [ 'type' => 'structure', 'required' => [ 'entries', ], 'members' => [ 'entries' => [ 'shape' => 'BatchGetAssetPropertyAggregatesEntries', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'BatchGetAssetPropertyAggregatesMaxResults', ], ], ], 'BatchGetAssetPropertyAggregatesResponse' => [ 'type' => 'structure', 'required' => [ 'errorEntries', 'successEntries', 'skippedEntries', ], 'members' => [ 'errorEntries' => [ 'shape' => 'BatchGetAssetPropertyAggregatesErrorEntries', ], 'successEntries' => [ 'shape' => 'BatchGetAssetPropertyAggregatesSuccessEntries', ], 'skippedEntries' => [ 'shape' => 'BatchGetAssetPropertyAggregatesSkippedEntries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'BatchGetAssetPropertyAggregatesSkippedEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetAssetPropertyAggregatesSkippedEntry', ], ], 'BatchGetAssetPropertyAggregatesSkippedEntry' => [ 'type' => 'structure', 'required' => [ 'entryId', 'completionStatus', ], 'members' => [ 'entryId' => [ 'shape' => 'EntryId', ], 'completionStatus' => [ 'shape' => 'BatchEntryCompletionStatus', ], 'errorInfo' => [ 'shape' => 'BatchGetAssetPropertyAggregatesErrorInfo', ], ], ], 'BatchGetAssetPropertyAggregatesSuccessEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetAssetPropertyAggregatesSuccessEntry', ], ], 'BatchGetAssetPropertyAggregatesSuccessEntry' => [ 'type' => 'structure', 'required' => [ 'entryId', 'aggregatedValues', ], 'members' => [ 'entryId' => [ 'shape' => 'EntryId', ], 'aggregatedValues' => [ 'shape' => 'AggregatedValues', ], ], ], 'BatchGetAssetPropertyValueEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetAssetPropertyValueEntry', ], ], 'BatchGetAssetPropertyValueEntry' => [ 'type' => 'structure', 'required' => [ 'entryId', ], 'members' => [ 'entryId' => [ 'shape' => 'EntryId', ], 'assetId' => [ 'shape' => 'ID', ], 'propertyId' => [ 'shape' => 'ID', ], 'propertyAlias' => [ 'shape' => 'AssetPropertyAlias', ], ], ], 'BatchGetAssetPropertyValueErrorCode' => [ 'type' => 'string', 'enum' => [ 'ResourceNotFoundException', 'InvalidRequestException', 'AccessDeniedException', ], ], 'BatchGetAssetPropertyValueErrorEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetAssetPropertyValueErrorEntry', ], ], 'BatchGetAssetPropertyValueErrorEntry' => [ 'type' => 'structure', 'required' => [ 'errorCode', 'errorMessage', 'entryId', ], 'members' => [ 'errorCode' => [ 'shape' => 'BatchGetAssetPropertyValueErrorCode', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], 'entryId' => [ 'shape' => 'EntryId', ], ], ], 'BatchGetAssetPropertyValueErrorInfo' => [ 'type' => 'structure', 'required' => [ 'errorCode', 'errorTimestamp', ], 'members' => [ 'errorCode' => [ 'shape' => 'BatchGetAssetPropertyValueErrorCode', ], 'errorTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'BatchGetAssetPropertyValueHistoryEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetAssetPropertyValueHistoryEntry', ], ], 'BatchGetAssetPropertyValueHistoryEntry' => [ 'type' => 'structure', 'required' => [ 'entryId', ], 'members' => [ 'entryId' => [ 'shape' => 'EntryId', ], 'assetId' => [ 'shape' => 'ID', ], 'propertyId' => [ 'shape' => 'ID', ], 'propertyAlias' => [ 'shape' => 'AssetPropertyAlias', ], 'startDate' => [ 'shape' => 'Timestamp', ], 'endDate' => [ 'shape' => 'Timestamp', ], 'qualities' => [ 'shape' => 'Qualities', ], 'timeOrdering' => [ 'shape' => 'TimeOrdering', ], ], ], 'BatchGetAssetPropertyValueHistoryErrorCode' => [ 'type' => 'string', 'enum' => [ 'ResourceNotFoundException', 'InvalidRequestException', 'AccessDeniedException', ], ], 'BatchGetAssetPropertyValueHistoryErrorEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetAssetPropertyValueHistoryErrorEntry', ], ], 'BatchGetAssetPropertyValueHistoryErrorEntry' => [ 'type' => 'structure', 'required' => [ 'errorCode', 'errorMessage', 'entryId', ], 'members' => [ 'errorCode' => [ 'shape' => 'BatchGetAssetPropertyValueHistoryErrorCode', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], 'entryId' => [ 'shape' => 'EntryId', ], ], ], 'BatchGetAssetPropertyValueHistoryErrorInfo' => [ 'type' => 'structure', 'required' => [ 'errorCode', 'errorTimestamp', ], 'members' => [ 'errorCode' => [ 'shape' => 'BatchGetAssetPropertyValueHistoryErrorCode', ], 'errorTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'BatchGetAssetPropertyValueHistoryMaxResults' => [ 'type' => 'integer', 'min' => 1, ], 'BatchGetAssetPropertyValueHistoryRequest' => [ 'type' => 'structure', 'required' => [ 'entries', ], 'members' => [ 'entries' => [ 'shape' => 'BatchGetAssetPropertyValueHistoryEntries', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'BatchGetAssetPropertyValueHistoryMaxResults', ], ], ], 'BatchGetAssetPropertyValueHistoryResponse' => [ 'type' => 'structure', 'required' => [ 'errorEntries', 'successEntries', 'skippedEntries', ], 'members' => [ 'errorEntries' => [ 'shape' => 'BatchGetAssetPropertyValueHistoryErrorEntries', ], 'successEntries' => [ 'shape' => 'BatchGetAssetPropertyValueHistorySuccessEntries', ], 'skippedEntries' => [ 'shape' => 'BatchGetAssetPropertyValueHistorySkippedEntries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'BatchGetAssetPropertyValueHistorySkippedEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetAssetPropertyValueHistorySkippedEntry', ], ], 'BatchGetAssetPropertyValueHistorySkippedEntry' => [ 'type' => 'structure', 'required' => [ 'entryId', 'completionStatus', ], 'members' => [ 'entryId' => [ 'shape' => 'EntryId', ], 'completionStatus' => [ 'shape' => 'BatchEntryCompletionStatus', ], 'errorInfo' => [ 'shape' => 'BatchGetAssetPropertyValueHistoryErrorInfo', ], ], ], 'BatchGetAssetPropertyValueHistorySuccessEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetAssetPropertyValueHistorySuccessEntry', ], ], 'BatchGetAssetPropertyValueHistorySuccessEntry' => [ 'type' => 'structure', 'required' => [ 'entryId', 'assetPropertyValueHistory', ], 'members' => [ 'entryId' => [ 'shape' => 'EntryId', ], 'assetPropertyValueHistory' => [ 'shape' => 'AssetPropertyValueHistory', ], ], ], 'BatchGetAssetPropertyValueRequest' => [ 'type' => 'structure', 'required' => [ 'entries', ], 'members' => [ 'entries' => [ 'shape' => 'BatchGetAssetPropertyValueEntries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'BatchGetAssetPropertyValueResponse' => [ 'type' => 'structure', 'required' => [ 'errorEntries', 'successEntries', 'skippedEntries', ], 'members' => [ 'errorEntries' => [ 'shape' => 'BatchGetAssetPropertyValueErrorEntries', ], 'successEntries' => [ 'shape' => 'BatchGetAssetPropertyValueSuccessEntries', ], 'skippedEntries' => [ 'shape' => 'BatchGetAssetPropertyValueSkippedEntries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'BatchGetAssetPropertyValueSkippedEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetAssetPropertyValueSkippedEntry', ], ], 'BatchGetAssetPropertyValueSkippedEntry' => [ 'type' => 'structure', 'required' => [ 'entryId', 'completionStatus', ], 'members' => [ 'entryId' => [ 'shape' => 'EntryId', ], 'completionStatus' => [ 'shape' => 'BatchEntryCompletionStatus', ], 'errorInfo' => [ 'shape' => 'BatchGetAssetPropertyValueErrorInfo', ], ], ], 'BatchGetAssetPropertyValueSuccessEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetAssetPropertyValueSuccessEntry', ], ], 'BatchGetAssetPropertyValueSuccessEntry' => [ 'type' => 'structure', 'required' => [ 'entryId', ], 'members' => [ 'entryId' => [ 'shape' => 'EntryId', ], 'assetPropertyValue' => [ 'shape' => 'AssetPropertyValue', ], ], ], 'BatchPutAssetPropertyError' => [ 'type' => 'structure', 'required' => [ 'errorCode', 'errorMessage', 'timestamps', ], 'members' => [ 'errorCode' => [ 'shape' => 'BatchPutAssetPropertyValueErrorCode', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], 'timestamps' => [ 'shape' => 'Timestamps', ], ], ], 'BatchPutAssetPropertyErrorEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPutAssetPropertyErrorEntry', ], ], 'BatchPutAssetPropertyErrorEntry' => [ 'type' => 'structure', 'required' => [ 'entryId', 'errors', ], 'members' => [ 'entryId' => [ 'shape' => 'EntryId', ], 'errors' => [ 'shape' => 'BatchPutAssetPropertyErrors', ], ], ], 'BatchPutAssetPropertyErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPutAssetPropertyError', ], ], 'BatchPutAssetPropertyValueErrorCode' => [ 'type' => 'string', 'enum' => [ 'ResourceNotFoundException', 'InvalidRequestException', 'InternalFailureException', 'ServiceUnavailableException', 'ThrottlingException', 'LimitExceededException', 'ConflictingOperationException', 'TimestampOutOfRangeException', 'AccessDeniedException', ], ], 'BatchPutAssetPropertyValueRequest' => [ 'type' => 'structure', 'required' => [ 'entries', ], 'members' => [ 'enablePartialEntryProcessing' => [ 'shape' => 'BooleanValue', ], 'entries' => [ 'shape' => 'PutAssetPropertyValueEntries', ], ], ], 'BatchPutAssetPropertyValueResponse' => [ 'type' => 'structure', 'required' => [ 'errorEntries', ], 'members' => [ 'errorEntries' => [ 'shape' => 'BatchPutAssetPropertyErrorEntries', ], ], ], 'BooleanValue' => [ 'type' => 'boolean', ], 'Bucket' => [ 'type' => 'string', 'max' => 63, 'min' => 3, ], 'CapabilityConfiguration' => [ 'type' => 'string', 'max' => 10000000, 'min' => 1, ], 'CapabilityNamespace' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '^[a-zA-Z]+:[a-zA-Z]+:[0-9]+$', ], 'CapabilitySyncStatus' => [ 'type' => 'string', 'enum' => [ 'IN_SYNC', 'OUT_OF_SYNC', 'SYNC_FAILED', 'UNKNOWN', 'NOT_APPLICABLE', ], ], 'Citation' => [ 'type' => 'structure', 'members' => [ 'reference' => [ 'shape' => 'Reference', ], 'content' => [ 'shape' => 'Content', ], ], ], 'Citations' => [ 'type' => 'list', 'member' => [ 'shape' => 'Citation', ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 36, 'pattern' => '\\S{36,64}', ], 'ColumnInfo' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'ColumnType', ], ], ], 'ColumnName' => [ 'type' => 'string', 'enum' => [ 'ALIAS', 'ASSET_ID', 'PROPERTY_ID', 'DATA_TYPE', 'TIMESTAMP_SECONDS', 'TIMESTAMP_NANO_OFFSET', 'QUALITY', 'VALUE', ], ], 'ColumnNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnName', ], ], 'ColumnType' => [ 'type' => 'structure', 'members' => [ 'scalarType' => [ 'shape' => 'ScalarType', ], ], ], 'ColumnsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnInfo', ], ], 'CompositeModelProperty' => [ 'type' => 'structure', 'required' => [ 'name', 'type', 'assetProperty', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'type' => [ 'shape' => 'Name', ], 'assetProperty' => [ 'shape' => 'Property', ], 'id' => [ 'shape' => 'ID', ], 'externalId' => [ 'shape' => 'ExternalId', ], ], ], 'CompositionDetails' => [ 'type' => 'structure', 'members' => [ 'compositionRelationship' => [ 'shape' => 'CompositionRelationship', ], ], ], 'CompositionRelationship' => [ 'type' => 'list', 'member' => [ 'shape' => 'CompositionRelationshipItem', ], ], 'CompositionRelationshipItem' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ID', ], ], ], 'CompositionRelationshipSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'CompositionRelationshipSummary', ], ], 'CompositionRelationshipSummary' => [ 'type' => 'structure', 'required' => [ 'assetModelId', 'assetModelCompositeModelId', 'assetModelCompositeModelType', ], 'members' => [ 'assetModelId' => [ 'shape' => 'ID', ], 'assetModelCompositeModelId' => [ 'shape' => 'ID', ], 'assetModelCompositeModelType' => [ 'shape' => 'Name', ], ], ], 'ComputeLocation' => [ 'type' => 'string', 'enum' => [ 'EDGE', 'CLOUD', ], ], 'ConfigurationErrorDetails' => [ 'type' => 'structure', 'required' => [ 'code', 'message', ], 'members' => [ 'code' => [ 'shape' => 'ErrorCode', ], 'message' => [ 'shape' => 'ErrorMessage', ], ], ], 'ConfigurationState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'UPDATE_IN_PROGRESS', 'UPDATE_FAILED', ], ], 'ConfigurationStatus' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'ConfigurationState', ], 'error' => [ 'shape' => 'ConfigurationErrorDetails', ], ], ], 'ConflictingOperationException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceArn', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceArn' => [ 'shape' => 'ResourceArn', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'Content' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'String', ], ], ], 'ConversationId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'CoreDeviceOperatingSystem' => [ 'type' => 'string', 'enum' => [ 'LINUX_AARCH64', 'LINUX_AMD64', 'WINDOWS_AMD64', ], ], 'CoreDeviceThingName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9:_-]+$', ], 'CreateAccessPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'accessPolicyIdentity', 'accessPolicyResource', 'accessPolicyPermission', ], 'members' => [ 'accessPolicyIdentity' => [ 'shape' => 'Identity', ], 'accessPolicyResource' => [ 'shape' => 'Resource', ], 'accessPolicyPermission' => [ 'shape' => 'Permission', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateAccessPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'accessPolicyId', 'accessPolicyArn', ], 'members' => [ 'accessPolicyId' => [ 'shape' => 'ID', ], 'accessPolicyArn' => [ 'shape' => 'ARN', ], ], ], 'CreateAssetModelCompositeModelRequest' => [ 'type' => 'structure', 'required' => [ 'assetModelId', 'assetModelCompositeModelName', 'assetModelCompositeModelType', ], 'members' => [ 'assetModelId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetModelId', ], 'assetModelCompositeModelExternalId' => [ 'shape' => 'ExternalId', ], 'parentAssetModelCompositeModelId' => [ 'shape' => 'CustomID', ], 'assetModelCompositeModelId' => [ 'shape' => 'ID', ], 'assetModelCompositeModelDescription' => [ 'shape' => 'Description', ], 'assetModelCompositeModelName' => [ 'shape' => 'Name', ], 'assetModelCompositeModelType' => [ 'shape' => 'Name', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'composedAssetModelId' => [ 'shape' => 'CustomID', ], 'assetModelCompositeModelProperties' => [ 'shape' => 'AssetModelPropertyDefinitions', ], 'ifMatch' => [ 'shape' => 'ETag', 'location' => 'header', 'locationName' => 'If-Match', ], 'ifNoneMatch' => [ 'shape' => 'SelectAll', 'location' => 'header', 'locationName' => 'If-None-Match', ], 'matchForVersionType' => [ 'shape' => 'AssetModelVersionType', 'location' => 'header', 'locationName' => 'Match-For-Version-Type', ], ], ], 'CreateAssetModelCompositeModelResponse' => [ 'type' => 'structure', 'required' => [ 'assetModelCompositeModelId', 'assetModelCompositeModelPath', 'assetModelStatus', ], 'members' => [ 'assetModelCompositeModelId' => [ 'shape' => 'ID', ], 'assetModelCompositeModelPath' => [ 'shape' => 'AssetModelCompositeModelPath', ], 'assetModelStatus' => [ 'shape' => 'AssetModelStatus', ], ], ], 'CreateAssetModelRequest' => [ 'type' => 'structure', 'required' => [ 'assetModelName', ], 'members' => [ 'assetModelName' => [ 'shape' => 'Name', ], 'assetModelType' => [ 'shape' => 'AssetModelType', ], 'assetModelId' => [ 'shape' => 'ID', ], 'assetModelExternalId' => [ 'shape' => 'ExternalId', ], 'assetModelDescription' => [ 'shape' => 'Description', ], 'assetModelProperties' => [ 'shape' => 'AssetModelPropertyDefinitions', ], 'assetModelHierarchies' => [ 'shape' => 'AssetModelHierarchyDefinitions', ], 'assetModelCompositeModels' => [ 'shape' => 'AssetModelCompositeModelDefinitions', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateAssetModelResponse' => [ 'type' => 'structure', 'required' => [ 'assetModelId', 'assetModelArn', 'assetModelStatus', ], 'members' => [ 'assetModelId' => [ 'shape' => 'ID', ], 'assetModelArn' => [ 'shape' => 'ARN', ], 'assetModelStatus' => [ 'shape' => 'AssetModelStatus', ], ], ], 'CreateAssetRequest' => [ 'type' => 'structure', 'required' => [ 'assetName', 'assetModelId', ], 'members' => [ 'assetName' => [ 'shape' => 'Name', ], 'assetModelId' => [ 'shape' => 'CustomID', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], 'assetDescription' => [ 'shape' => 'Description', ], 'assetId' => [ 'shape' => 'ID', ], 'assetExternalId' => [ 'shape' => 'ExternalId', ], ], ], 'CreateAssetResponse' => [ 'type' => 'structure', 'required' => [ 'assetId', 'assetArn', 'assetStatus', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', ], 'assetArn' => [ 'shape' => 'ARN', ], 'assetStatus' => [ 'shape' => 'AssetStatus', ], ], ], 'CreateBulkImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobName', 'jobRoleArn', 'files', 'errorReportLocation', 'jobConfiguration', ], 'members' => [ 'jobName' => [ 'shape' => 'Name', ], 'jobRoleArn' => [ 'shape' => 'ARN', ], 'files' => [ 'shape' => 'Files', ], 'errorReportLocation' => [ 'shape' => 'ErrorReportLocation', ], 'jobConfiguration' => [ 'shape' => 'JobConfiguration', ], 'adaptiveIngestion' => [ 'shape' => 'AdaptiveIngestion', ], 'deleteFilesAfterImport' => [ 'shape' => 'DeleteFilesAfterImport', ], ], ], 'CreateBulkImportJobResponse' => [ 'type' => 'structure', 'required' => [ 'jobId', 'jobName', 'jobStatus', ], 'members' => [ 'jobId' => [ 'shape' => 'ID', ], 'jobName' => [ 'shape' => 'Name', ], 'jobStatus' => [ 'shape' => 'JobStatus', ], ], ], 'CreateDashboardRequest' => [ 'type' => 'structure', 'required' => [ 'projectId', 'dashboardName', 'dashboardDefinition', ], 'members' => [ 'projectId' => [ 'shape' => 'ID', ], 'dashboardName' => [ 'shape' => 'Name', ], 'dashboardDescription' => [ 'shape' => 'Description', ], 'dashboardDefinition' => [ 'shape' => 'DashboardDefinition', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateDashboardResponse' => [ 'type' => 'structure', 'required' => [ 'dashboardId', 'dashboardArn', ], 'members' => [ 'dashboardId' => [ 'shape' => 'ID', ], 'dashboardArn' => [ 'shape' => 'ARN', ], ], ], 'CreateDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'datasetName', 'datasetSource', ], 'members' => [ 'datasetId' => [ 'shape' => 'ID', ], 'datasetName' => [ 'shape' => 'RestrictedName', ], 'datasetDescription' => [ 'shape' => 'RestrictedDescription', ], 'datasetSource' => [ 'shape' => 'DatasetSource', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateDatasetResponse' => [ 'type' => 'structure', 'required' => [ 'datasetId', 'datasetArn', 'datasetStatus', ], 'members' => [ 'datasetId' => [ 'shape' => 'ID', ], 'datasetArn' => [ 'shape' => 'ARN', ], 'datasetStatus' => [ 'shape' => 'DatasetStatus', ], ], ], 'CreateGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'gatewayName', 'gatewayPlatform', ], 'members' => [ 'gatewayName' => [ 'shape' => 'GatewayName', ], 'gatewayPlatform' => [ 'shape' => 'GatewayPlatform', ], 'gatewayVersion' => [ 'shape' => 'GatewayVersion', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateGatewayResponse' => [ 'type' => 'structure', 'required' => [ 'gatewayId', 'gatewayArn', ], 'members' => [ 'gatewayId' => [ 'shape' => 'ID', ], 'gatewayArn' => [ 'shape' => 'ARN', ], ], ], 'CreatePortalRequest' => [ 'type' => 'structure', 'required' => [ 'portalName', 'portalContactEmail', 'roleArn', ], 'members' => [ 'portalName' => [ 'shape' => 'Name', ], 'portalDescription' => [ 'shape' => 'Description', ], 'portalContactEmail' => [ 'shape' => 'Email', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'portalLogoImageFile' => [ 'shape' => 'ImageFile', ], 'roleArn' => [ 'shape' => 'IamArn', ], 'tags' => [ 'shape' => 'TagMap', ], 'portalAuthMode' => [ 'shape' => 'AuthMode', ], 'notificationSenderEmail' => [ 'shape' => 'Email', ], 'alarms' => [ 'shape' => 'Alarms', ], 'portalType' => [ 'shape' => 'PortalType', ], 'portalTypeConfiguration' => [ 'shape' => 'PortalTypeConfiguration', ], ], ], 'CreatePortalResponse' => [ 'type' => 'structure', 'required' => [ 'portalId', 'portalArn', 'portalStartUrl', 'portalStatus', 'ssoApplicationId', ], 'members' => [ 'portalId' => [ 'shape' => 'ID', ], 'portalArn' => [ 'shape' => 'ARN', ], 'portalStartUrl' => [ 'shape' => 'Url', ], 'portalStatus' => [ 'shape' => 'PortalStatus', ], 'ssoApplicationId' => [ 'shape' => 'SSOApplicationId', ], ], ], 'CreateProjectRequest' => [ 'type' => 'structure', 'required' => [ 'portalId', 'projectName', ], 'members' => [ 'portalId' => [ 'shape' => 'ID', ], 'projectName' => [ 'shape' => 'Name', ], 'projectDescription' => [ 'shape' => 'Description', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateProjectResponse' => [ 'type' => 'structure', 'required' => [ 'projectId', 'projectArn', ], 'members' => [ 'projectId' => [ 'shape' => 'ID', ], 'projectArn' => [ 'shape' => 'ARN', ], ], ], 'Csv' => [ 'type' => 'structure', 'required' => [ 'columnNames', ], 'members' => [ 'columnNames' => [ 'shape' => 'ColumnNames', ], ], ], 'CustomID' => [ 'type' => 'string', 'max' => 139, 'min' => 13, 'pattern' => '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$|^externalId:[a-zA-Z0-9][a-zA-Z_\\-0-9.:]*[a-zA-Z0-9]+', ], 'CustomerManagedS3Storage' => [ 'type' => 'structure', 'required' => [ 's3ResourceArn', 'roleArn', ], 'members' => [ 's3ResourceArn' => [ 'shape' => 'ARN', ], 'roleArn' => [ 'shape' => 'ARN', ], ], ], 'DashboardDefinition' => [ 'type' => 'string', 'max' => 204800, 'min' => 0, 'pattern' => '.+', ], 'DashboardSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'DashboardSummary', ], ], 'DashboardSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'name', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastUpdateDate' => [ 'shape' => 'Timestamp', ], ], ], 'DataSetReference' => [ 'type' => 'structure', 'members' => [ 'datasetArn' => [ 'shape' => 'String', ], 'source' => [ 'shape' => 'Source', ], ], ], 'DatasetSource' => [ 'type' => 'structure', 'required' => [ 'sourceType', 'sourceFormat', ], 'members' => [ 'sourceType' => [ 'shape' => 'DatasetSourceType', ], 'sourceFormat' => [ 'shape' => 'DatasetSourceFormat', ], 'sourceDetail' => [ 'shape' => 'SourceDetail', ], ], ], 'DatasetSourceFormat' => [ 'type' => 'string', 'enum' => [ 'KNOWLEDGE_BASE', ], ], 'DatasetSourceType' => [ 'type' => 'string', 'enum' => [ 'KENDRA', ], ], 'DatasetState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'UPDATING', 'DELETING', 'FAILED', ], ], 'DatasetStatus' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'DatasetState', ], 'error' => [ 'shape' => 'ErrorDetails', ], ], ], 'DatasetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatasetSummary', ], ], 'DatasetSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'name', 'description', 'creationDate', 'lastUpdateDate', 'status', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'arn' => [ 'shape' => 'ARN', ], 'name' => [ 'shape' => 'RestrictedName', ], 'description' => [ 'shape' => 'RestrictedDescription', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastUpdateDate' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'DatasetStatus', ], ], ], 'Datum' => [ 'type' => 'structure', 'members' => [ 'scalarValue' => [ 'shape' => 'ScalarValue', ], 'arrayValue' => [ 'shape' => 'DatumList', ], 'rowValue' => [ 'shape' => 'Row', ], 'nullValue' => [ 'shape' => 'NullableBoolean', ], ], ], 'DatumList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Datum', ], ], 'DefaultValue' => [ 'type' => 'string', 'pattern' => '[^\\u0000-\\u001F\\u007F]+', ], 'DeleteAccessPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'accessPolicyId', ], 'members' => [ 'accessPolicyId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'accessPolicyId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeleteAccessPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAssetModelCompositeModelRequest' => [ 'type' => 'structure', 'required' => [ 'assetModelId', 'assetModelCompositeModelId', ], 'members' => [ 'assetModelId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetModelId', ], 'assetModelCompositeModelId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetModelCompositeModelId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], 'ifMatch' => [ 'shape' => 'ETag', 'location' => 'header', 'locationName' => 'If-Match', ], 'ifNoneMatch' => [ 'shape' => 'SelectAll', 'location' => 'header', 'locationName' => 'If-None-Match', ], 'matchForVersionType' => [ 'shape' => 'AssetModelVersionType', 'location' => 'header', 'locationName' => 'Match-For-Version-Type', ], ], ], 'DeleteAssetModelCompositeModelResponse' => [ 'type' => 'structure', 'required' => [ 'assetModelStatus', ], 'members' => [ 'assetModelStatus' => [ 'shape' => 'AssetModelStatus', ], ], ], 'DeleteAssetModelRequest' => [ 'type' => 'structure', 'required' => [ 'assetModelId', ], 'members' => [ 'assetModelId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetModelId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], 'ifMatch' => [ 'shape' => 'ETag', 'location' => 'header', 'locationName' => 'If-Match', ], 'ifNoneMatch' => [ 'shape' => 'SelectAll', 'location' => 'header', 'locationName' => 'If-None-Match', ], 'matchForVersionType' => [ 'shape' => 'AssetModelVersionType', 'location' => 'header', 'locationName' => 'Match-For-Version-Type', ], ], ], 'DeleteAssetModelResponse' => [ 'type' => 'structure', 'required' => [ 'assetModelStatus', ], 'members' => [ 'assetModelStatus' => [ 'shape' => 'AssetModelStatus', ], ], ], 'DeleteAssetRequest' => [ 'type' => 'structure', 'required' => [ 'assetId', ], 'members' => [ 'assetId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeleteAssetResponse' => [ 'type' => 'structure', 'required' => [ 'assetStatus', ], 'members' => [ 'assetStatus' => [ 'shape' => 'AssetStatus', ], ], ], 'DeleteDashboardRequest' => [ 'type' => 'structure', 'required' => [ 'dashboardId', ], 'members' => [ 'dashboardId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'dashboardId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeleteDashboardResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'datasetId', ], 'members' => [ 'datasetId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'datasetId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeleteDatasetResponse' => [ 'type' => 'structure', 'required' => [ 'datasetStatus', ], 'members' => [ 'datasetStatus' => [ 'shape' => 'DatasetStatus', ], ], ], 'DeleteFilesAfterImport' => [ 'type' => 'boolean', ], 'DeleteGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'gatewayId', ], 'members' => [ 'gatewayId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'gatewayId', ], ], ], 'DeletePortalRequest' => [ 'type' => 'structure', 'required' => [ 'portalId', ], 'members' => [ 'portalId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'portalId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeletePortalResponse' => [ 'type' => 'structure', 'required' => [ 'portalStatus', ], 'members' => [ 'portalStatus' => [ 'shape' => 'PortalStatus', ], ], ], 'DeleteProjectRequest' => [ 'type' => 'structure', 'required' => [ 'projectId', ], 'members' => [ 'projectId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'projectId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeleteProjectResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTimeSeriesRequest' => [ 'type' => 'structure', 'members' => [ 'alias' => [ 'shape' => 'PropertyAlias', 'location' => 'querystring', 'locationName' => 'alias', ], 'assetId' => [ 'shape' => 'CustomID', 'location' => 'querystring', 'locationName' => 'assetId', ], 'propertyId' => [ 'shape' => 'CustomID', 'location' => 'querystring', 'locationName' => 'propertyId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'DescribeAccessPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'accessPolicyId', ], 'members' => [ 'accessPolicyId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'accessPolicyId', ], ], ], 'DescribeAccessPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'accessPolicyId', 'accessPolicyArn', 'accessPolicyIdentity', 'accessPolicyResource', 'accessPolicyPermission', 'accessPolicyCreationDate', 'accessPolicyLastUpdateDate', ], 'members' => [ 'accessPolicyId' => [ 'shape' => 'ID', ], 'accessPolicyArn' => [ 'shape' => 'ARN', ], 'accessPolicyIdentity' => [ 'shape' => 'Identity', ], 'accessPolicyResource' => [ 'shape' => 'Resource', ], 'accessPolicyPermission' => [ 'shape' => 'Permission', ], 'accessPolicyCreationDate' => [ 'shape' => 'Timestamp', ], 'accessPolicyLastUpdateDate' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeActionRequest' => [ 'type' => 'structure', 'required' => [ 'actionId', ], 'members' => [ 'actionId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'actionId', ], ], ], 'DescribeActionResponse' => [ 'type' => 'structure', 'required' => [ 'actionId', 'targetResource', 'actionDefinitionId', 'actionPayload', 'executionTime', ], 'members' => [ 'actionId' => [ 'shape' => 'ID', ], 'targetResource' => [ 'shape' => 'TargetResource', ], 'actionDefinitionId' => [ 'shape' => 'ID', ], 'actionPayload' => [ 'shape' => 'ActionPayload', ], 'executionTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeAssetCompositeModelRequest' => [ 'type' => 'structure', 'required' => [ 'assetId', 'assetCompositeModelId', ], 'members' => [ 'assetId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetId', ], 'assetCompositeModelId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetCompositeModelId', ], ], ], 'DescribeAssetCompositeModelResponse' => [ 'type' => 'structure', 'required' => [ 'assetId', 'assetCompositeModelId', 'assetCompositeModelPath', 'assetCompositeModelName', 'assetCompositeModelDescription', 'assetCompositeModelType', 'assetCompositeModelProperties', 'assetCompositeModelSummaries', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', ], 'assetCompositeModelId' => [ 'shape' => 'ID', ], 'assetCompositeModelExternalId' => [ 'shape' => 'ExternalId', ], 'assetCompositeModelPath' => [ 'shape' => 'AssetCompositeModelPath', ], 'assetCompositeModelName' => [ 'shape' => 'Name', ], 'assetCompositeModelDescription' => [ 'shape' => 'Description', ], 'assetCompositeModelType' => [ 'shape' => 'Name', ], 'assetCompositeModelProperties' => [ 'shape' => 'AssetProperties', ], 'assetCompositeModelSummaries' => [ 'shape' => 'AssetCompositeModelSummaries', ], 'actionDefinitions' => [ 'shape' => 'ActionDefinitions', ], ], ], 'DescribeAssetModelCompositeModelRequest' => [ 'type' => 'structure', 'required' => [ 'assetModelId', 'assetModelCompositeModelId', ], 'members' => [ 'assetModelId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetModelId', ], 'assetModelCompositeModelId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetModelCompositeModelId', ], 'assetModelVersion' => [ 'shape' => 'AssetModelVersionFilter', 'location' => 'querystring', 'locationName' => 'assetModelVersion', ], ], ], 'DescribeAssetModelCompositeModelResponse' => [ 'type' => 'structure', 'required' => [ 'assetModelId', 'assetModelCompositeModelId', 'assetModelCompositeModelPath', 'assetModelCompositeModelName', 'assetModelCompositeModelDescription', 'assetModelCompositeModelType', 'assetModelCompositeModelProperties', 'assetModelCompositeModelSummaries', ], 'members' => [ 'assetModelId' => [ 'shape' => 'ID', ], 'assetModelCompositeModelId' => [ 'shape' => 'ID', ], 'assetModelCompositeModelExternalId' => [ 'shape' => 'ExternalId', ], 'assetModelCompositeModelPath' => [ 'shape' => 'AssetModelCompositeModelPath', ], 'assetModelCompositeModelName' => [ 'shape' => 'Name', ], 'assetModelCompositeModelDescription' => [ 'shape' => 'Description', ], 'assetModelCompositeModelType' => [ 'shape' => 'Name', ], 'assetModelCompositeModelProperties' => [ 'shape' => 'AssetModelProperties', ], 'compositionDetails' => [ 'shape' => 'CompositionDetails', ], 'assetModelCompositeModelSummaries' => [ 'shape' => 'AssetModelCompositeModelSummaries', ], 'actionDefinitions' => [ 'shape' => 'ActionDefinitions', ], ], ], 'DescribeAssetModelRequest' => [ 'type' => 'structure', 'required' => [ 'assetModelId', ], 'members' => [ 'assetModelId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetModelId', ], 'excludeProperties' => [ 'shape' => 'ExcludeProperties', 'location' => 'querystring', 'locationName' => 'excludeProperties', ], 'assetModelVersion' => [ 'shape' => 'AssetModelVersionFilter', 'location' => 'querystring', 'locationName' => 'assetModelVersion', ], ], ], 'DescribeAssetModelResponse' => [ 'type' => 'structure', 'required' => [ 'assetModelId', 'assetModelArn', 'assetModelName', 'assetModelDescription', 'assetModelProperties', 'assetModelHierarchies', 'assetModelCreationDate', 'assetModelLastUpdateDate', 'assetModelStatus', ], 'members' => [ 'assetModelId' => [ 'shape' => 'ID', ], 'assetModelExternalId' => [ 'shape' => 'ExternalId', ], 'assetModelArn' => [ 'shape' => 'ARN', ], 'assetModelName' => [ 'shape' => 'Name', ], 'assetModelType' => [ 'shape' => 'AssetModelType', ], 'assetModelDescription' => [ 'shape' => 'Description', ], 'assetModelProperties' => [ 'shape' => 'AssetModelProperties', ], 'assetModelHierarchies' => [ 'shape' => 'AssetModelHierarchies', ], 'assetModelCompositeModels' => [ 'shape' => 'AssetModelCompositeModels', ], 'assetModelCompositeModelSummaries' => [ 'shape' => 'AssetModelCompositeModelSummaries', ], 'assetModelCreationDate' => [ 'shape' => 'Timestamp', ], 'assetModelLastUpdateDate' => [ 'shape' => 'Timestamp', ], 'assetModelStatus' => [ 'shape' => 'AssetModelStatus', ], 'assetModelVersion' => [ 'shape' => 'Version', ], 'eTag' => [ 'shape' => 'ETag', 'location' => 'header', 'locationName' => 'ETag', ], ], ], 'DescribeAssetPropertyRequest' => [ 'type' => 'structure', 'required' => [ 'assetId', 'propertyId', ], 'members' => [ 'assetId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetId', ], 'propertyId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'propertyId', ], ], ], 'DescribeAssetPropertyResponse' => [ 'type' => 'structure', 'required' => [ 'assetId', 'assetName', 'assetModelId', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', ], 'assetName' => [ 'shape' => 'Name', ], 'assetModelId' => [ 'shape' => 'ID', ], 'assetProperty' => [ 'shape' => 'Property', ], 'compositeModel' => [ 'shape' => 'CompositeModelProperty', ], 'assetExternalId' => [ 'shape' => 'ExternalId', ], ], ], 'DescribeAssetRequest' => [ 'type' => 'structure', 'required' => [ 'assetId', ], 'members' => [ 'assetId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetId', ], 'excludeProperties' => [ 'shape' => 'ExcludeProperties', 'location' => 'querystring', 'locationName' => 'excludeProperties', ], ], ], 'DescribeAssetResponse' => [ 'type' => 'structure', 'required' => [ 'assetId', 'assetArn', 'assetName', 'assetModelId', 'assetProperties', 'assetHierarchies', 'assetCreationDate', 'assetLastUpdateDate', 'assetStatus', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', ], 'assetArn' => [ 'shape' => 'ARN', ], 'assetName' => [ 'shape' => 'Name', ], 'assetModelId' => [ 'shape' => 'ID', ], 'assetProperties' => [ 'shape' => 'AssetProperties', ], 'assetHierarchies' => [ 'shape' => 'AssetHierarchies', ], 'assetCompositeModels' => [ 'shape' => 'AssetCompositeModels', ], 'assetCreationDate' => [ 'shape' => 'Timestamp', ], 'assetLastUpdateDate' => [ 'shape' => 'Timestamp', ], 'assetStatus' => [ 'shape' => 'AssetStatus', ], 'assetDescription' => [ 'shape' => 'Description', ], 'assetCompositeModelSummaries' => [ 'shape' => 'AssetCompositeModelSummaries', ], 'assetExternalId' => [ 'shape' => 'ExternalId', ], ], ], 'DescribeBulkImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'jobId', ], ], ], 'DescribeBulkImportJobResponse' => [ 'type' => 'structure', 'required' => [ 'jobId', 'jobName', 'jobStatus', 'jobRoleArn', 'files', 'errorReportLocation', 'jobConfiguration', 'jobCreationDate', 'jobLastUpdateDate', ], 'members' => [ 'jobId' => [ 'shape' => 'ID', ], 'jobName' => [ 'shape' => 'Name', ], 'jobStatus' => [ 'shape' => 'JobStatus', ], 'jobRoleArn' => [ 'shape' => 'ARN', ], 'files' => [ 'shape' => 'Files', ], 'errorReportLocation' => [ 'shape' => 'ErrorReportLocation', ], 'jobConfiguration' => [ 'shape' => 'JobConfiguration', ], 'jobCreationDate' => [ 'shape' => 'Timestamp', ], 'jobLastUpdateDate' => [ 'shape' => 'Timestamp', ], 'adaptiveIngestion' => [ 'shape' => 'AdaptiveIngestion', ], 'deleteFilesAfterImport' => [ 'shape' => 'DeleteFilesAfterImport', ], ], ], 'DescribeDashboardRequest' => [ 'type' => 'structure', 'required' => [ 'dashboardId', ], 'members' => [ 'dashboardId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'dashboardId', ], ], ], 'DescribeDashboardResponse' => [ 'type' => 'structure', 'required' => [ 'dashboardId', 'dashboardArn', 'dashboardName', 'projectId', 'dashboardDefinition', 'dashboardCreationDate', 'dashboardLastUpdateDate', ], 'members' => [ 'dashboardId' => [ 'shape' => 'ID', ], 'dashboardArn' => [ 'shape' => 'ARN', ], 'dashboardName' => [ 'shape' => 'Name', ], 'projectId' => [ 'shape' => 'ID', ], 'dashboardDescription' => [ 'shape' => 'Description', ], 'dashboardDefinition' => [ 'shape' => 'DashboardDefinition', ], 'dashboardCreationDate' => [ 'shape' => 'Timestamp', ], 'dashboardLastUpdateDate' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'datasetId', ], 'members' => [ 'datasetId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'datasetId', ], ], ], 'DescribeDatasetResponse' => [ 'type' => 'structure', 'required' => [ 'datasetId', 'datasetArn', 'datasetName', 'datasetDescription', 'datasetSource', 'datasetStatus', 'datasetCreationDate', 'datasetLastUpdateDate', ], 'members' => [ 'datasetId' => [ 'shape' => 'ID', ], 'datasetArn' => [ 'shape' => 'ARN', ], 'datasetName' => [ 'shape' => 'RestrictedName', ], 'datasetDescription' => [ 'shape' => 'RestrictedDescription', ], 'datasetSource' => [ 'shape' => 'DatasetSource', ], 'datasetStatus' => [ 'shape' => 'DatasetStatus', ], 'datasetCreationDate' => [ 'shape' => 'Timestamp', ], 'datasetLastUpdateDate' => [ 'shape' => 'Timestamp', ], 'datasetVersion' => [ 'shape' => 'Version', ], ], ], 'DescribeDefaultEncryptionConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeDefaultEncryptionConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'encryptionType', 'configurationStatus', ], 'members' => [ 'encryptionType' => [ 'shape' => 'EncryptionType', ], 'kmsKeyArn' => [ 'shape' => 'ARN', ], 'configurationStatus' => [ 'shape' => 'ConfigurationStatus', ], ], ], 'DescribeGatewayCapabilityConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'gatewayId', 'capabilityNamespace', ], 'members' => [ 'gatewayId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'gatewayId', ], 'capabilityNamespace' => [ 'shape' => 'CapabilityNamespace', 'location' => 'uri', 'locationName' => 'capabilityNamespace', ], ], ], 'DescribeGatewayCapabilityConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'gatewayId', 'capabilityNamespace', 'capabilityConfiguration', 'capabilitySyncStatus', ], 'members' => [ 'gatewayId' => [ 'shape' => 'ID', ], 'capabilityNamespace' => [ 'shape' => 'CapabilityNamespace', ], 'capabilityConfiguration' => [ 'shape' => 'CapabilityConfiguration', ], 'capabilitySyncStatus' => [ 'shape' => 'CapabilitySyncStatus', ], ], ], 'DescribeGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'gatewayId', ], 'members' => [ 'gatewayId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'gatewayId', ], ], ], 'DescribeGatewayResponse' => [ 'type' => 'structure', 'required' => [ 'gatewayId', 'gatewayName', 'gatewayArn', 'gatewayCapabilitySummaries', 'creationDate', 'lastUpdateDate', ], 'members' => [ 'gatewayId' => [ 'shape' => 'ID', ], 'gatewayName' => [ 'shape' => 'GatewayName', ], 'gatewayArn' => [ 'shape' => 'ARN', ], 'gatewayPlatform' => [ 'shape' => 'GatewayPlatform', ], 'gatewayVersion' => [ 'shape' => 'GatewayVersion', ], 'gatewayCapabilitySummaries' => [ 'shape' => 'GatewayCapabilitySummaries', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastUpdateDate' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeLoggingOptionsRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeLoggingOptionsResponse' => [ 'type' => 'structure', 'required' => [ 'loggingOptions', ], 'members' => [ 'loggingOptions' => [ 'shape' => 'LoggingOptions', ], ], ], 'DescribePortalRequest' => [ 'type' => 'structure', 'required' => [ 'portalId', ], 'members' => [ 'portalId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'portalId', ], ], ], 'DescribePortalResponse' => [ 'type' => 'structure', 'required' => [ 'portalId', 'portalArn', 'portalName', 'portalClientId', 'portalStartUrl', 'portalContactEmail', 'portalStatus', 'portalCreationDate', 'portalLastUpdateDate', ], 'members' => [ 'portalId' => [ 'shape' => 'ID', ], 'portalArn' => [ 'shape' => 'ARN', ], 'portalName' => [ 'shape' => 'Name', ], 'portalDescription' => [ 'shape' => 'Description', ], 'portalClientId' => [ 'shape' => 'PortalClientId', ], 'portalStartUrl' => [ 'shape' => 'Url', ], 'portalContactEmail' => [ 'shape' => 'Email', ], 'portalStatus' => [ 'shape' => 'PortalStatus', ], 'portalCreationDate' => [ 'shape' => 'Timestamp', ], 'portalLastUpdateDate' => [ 'shape' => 'Timestamp', ], 'portalLogoImageLocation' => [ 'shape' => 'ImageLocation', ], 'roleArn' => [ 'shape' => 'IamArn', ], 'portalAuthMode' => [ 'shape' => 'AuthMode', ], 'notificationSenderEmail' => [ 'shape' => 'Email', ], 'alarms' => [ 'shape' => 'Alarms', ], 'portalType' => [ 'shape' => 'PortalType', ], 'portalTypeConfiguration' => [ 'shape' => 'PortalTypeConfiguration', ], ], ], 'DescribeProjectRequest' => [ 'type' => 'structure', 'required' => [ 'projectId', ], 'members' => [ 'projectId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'projectId', ], ], ], 'DescribeProjectResponse' => [ 'type' => 'structure', 'required' => [ 'projectId', 'projectArn', 'projectName', 'portalId', 'projectCreationDate', 'projectLastUpdateDate', ], 'members' => [ 'projectId' => [ 'shape' => 'ID', ], 'projectArn' => [ 'shape' => 'ARN', ], 'projectName' => [ 'shape' => 'Name', ], 'portalId' => [ 'shape' => 'ID', ], 'projectDescription' => [ 'shape' => 'Description', ], 'projectCreationDate' => [ 'shape' => 'Timestamp', ], 'projectLastUpdateDate' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeStorageConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeStorageConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'storageType', 'configurationStatus', ], 'members' => [ 'storageType' => [ 'shape' => 'StorageType', ], 'multiLayerStorage' => [ 'shape' => 'MultiLayerStorage', ], 'disassociatedDataStorage' => [ 'shape' => 'DisassociatedDataStorageState', ], 'retentionPeriod' => [ 'shape' => 'RetentionPeriod', ], 'configurationStatus' => [ 'shape' => 'ConfigurationStatus', ], 'lastUpdateDate' => [ 'shape' => 'Timestamp', ], 'warmTier' => [ 'shape' => 'WarmTierState', ], 'warmTierRetentionPeriod' => [ 'shape' => 'WarmTierRetentionPeriod', ], 'disallowIngestNullNaN' => [ 'shape' => 'DisallowIngestNullNaN', ], ], ], 'DescribeTimeSeriesRequest' => [ 'type' => 'structure', 'members' => [ 'alias' => [ 'shape' => 'PropertyAlias', 'location' => 'querystring', 'locationName' => 'alias', ], 'assetId' => [ 'shape' => 'CustomID', 'location' => 'querystring', 'locationName' => 'assetId', ], 'propertyId' => [ 'shape' => 'CustomID', 'location' => 'querystring', 'locationName' => 'propertyId', ], ], ], 'DescribeTimeSeriesResponse' => [ 'type' => 'structure', 'required' => [ 'timeSeriesId', 'dataType', 'timeSeriesCreationDate', 'timeSeriesLastUpdateDate', 'timeSeriesArn', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', ], 'propertyId' => [ 'shape' => 'ID', ], 'alias' => [ 'shape' => 'PropertyAlias', ], 'timeSeriesId' => [ 'shape' => 'TimeSeriesId', ], 'dataType' => [ 'shape' => 'PropertyDataType', ], 'dataTypeSpec' => [ 'shape' => 'Name', ], 'timeSeriesCreationDate' => [ 'shape' => 'Timestamp', ], 'timeSeriesLastUpdateDate' => [ 'shape' => 'Timestamp', ], 'timeSeriesArn' => [ 'shape' => 'ARN', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[^\\u0000-\\u001F\\u007F]+', ], 'DetailedError' => [ 'type' => 'structure', 'required' => [ 'code', 'message', ], 'members' => [ 'code' => [ 'shape' => 'DetailedErrorCode', ], 'message' => [ 'shape' => 'DetailedErrorMessage', ], ], ], 'DetailedErrorCode' => [ 'type' => 'string', 'enum' => [ 'INCOMPATIBLE_COMPUTE_LOCATION', 'INCOMPATIBLE_FORWARDING_CONFIGURATION', ], ], 'DetailedErrorMessage' => [ 'type' => 'string', ], 'DetailedErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'DetailedError', ], ], 'DisallowIngestNullNaN' => [ 'type' => 'boolean', ], 'DisassociateAssetsRequest' => [ 'type' => 'structure', 'required' => [ 'assetId', 'hierarchyId', 'childAssetId', ], 'members' => [ 'assetId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetId', ], 'hierarchyId' => [ 'shape' => 'CustomID', ], 'childAssetId' => [ 'shape' => 'CustomID', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'DisassociateTimeSeriesFromAssetPropertyRequest' => [ 'type' => 'structure', 'required' => [ 'alias', 'assetId', 'propertyId', ], 'members' => [ 'alias' => [ 'shape' => 'PropertyAlias', 'location' => 'querystring', 'locationName' => 'alias', ], 'assetId' => [ 'shape' => 'CustomID', 'location' => 'querystring', 'locationName' => 'assetId', ], 'propertyId' => [ 'shape' => 'CustomID', 'location' => 'querystring', 'locationName' => 'propertyId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'DisassociatedDataStorageState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'ETag' => [ 'type' => 'string', 'pattern' => '^[\\w-]{43}$', ], 'Email' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_\\-\\.\\+]+@[a-zA-Z0-9_\\-\\.\\+]+\\.[a-zA-Z]{2,}$', 'sensitive' => true, ], 'EncryptionType' => [ 'type' => 'string', 'enum' => [ 'SITEWISE_DEFAULT_ENCRYPTION', 'KMS_BASED_ENCRYPTION', ], ], 'EntryId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_-]+$', ], 'ErrorCode' => [ 'type' => 'string', 'enum' => [ 'VALIDATION_ERROR', 'INTERNAL_FAILURE', ], ], 'ErrorDetails' => [ 'type' => 'structure', 'required' => [ 'code', 'message', ], 'members' => [ 'code' => [ 'shape' => 'ErrorCode', ], 'message' => [ 'shape' => 'ErrorMessage', ], 'details' => [ 'shape' => 'DetailedErrors', ], ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'ErrorReportLocation' => [ 'type' => 'structure', 'required' => [ 'bucket', 'prefix', ], 'members' => [ 'bucket' => [ 'shape' => 'Bucket', ], 'prefix' => [ 'shape' => 'String', ], ], ], 'ExceptionMessage' => [ 'type' => 'string', ], 'ExcludeProperties' => [ 'type' => 'boolean', ], 'ExecuteActionRequest' => [ 'type' => 'structure', 'required' => [ 'targetResource', 'actionDefinitionId', 'actionPayload', ], 'members' => [ 'targetResource' => [ 'shape' => 'TargetResource', ], 'actionDefinitionId' => [ 'shape' => 'ID', ], 'actionPayload' => [ 'shape' => 'ActionPayload', ], 'clientToken' => [ 'shape' => 'ClientToken', ], ], ], 'ExecuteActionResponse' => [ 'type' => 'structure', 'required' => [ 'actionId', ], 'members' => [ 'actionId' => [ 'shape' => 'ID', ], ], ], 'ExecuteQueryMaxResults' => [ 'type' => 'integer', 'min' => 1, ], 'ExecuteQueryNextToken' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'ExecuteQueryRequest' => [ 'type' => 'structure', 'required' => [ 'queryStatement', ], 'members' => [ 'queryStatement' => [ 'shape' => 'QueryStatement', ], 'nextToken' => [ 'shape' => 'ExecuteQueryNextToken', ], 'maxResults' => [ 'shape' => 'ExecuteQueryMaxResults', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'ExecuteQueryResponse' => [ 'type' => 'structure', 'members' => [ 'columns' => [ 'shape' => 'ColumnsList', ], 'rows' => [ 'shape' => 'Rows', ], 'nextToken' => [ 'shape' => 'ExecuteQueryNextToken', ], ], ], 'Expression' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ExpressionVariable' => [ 'type' => 'structure', 'required' => [ 'name', 'value', ], 'members' => [ 'name' => [ 'shape' => 'VariableName', ], 'value' => [ 'shape' => 'VariableValue', ], ], ], 'ExpressionVariables' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExpressionVariable', ], ], 'ExternalId' => [ 'type' => 'string', 'max' => 128, 'min' => 2, 'pattern' => '[a-zA-Z0-9_][a-zA-Z_\\-0-9.:]*[a-zA-Z0-9_]+', ], 'File' => [ 'type' => 'structure', 'required' => [ 'bucket', 'key', ], 'members' => [ 'bucket' => [ 'shape' => 'Bucket', ], 'key' => [ 'shape' => 'String', ], 'versionId' => [ 'shape' => 'String', ], ], ], 'FileFormat' => [ 'type' => 'structure', 'members' => [ 'csv' => [ 'shape' => 'Csv', ], 'parquet' => [ 'shape' => 'Parquet', ], ], ], 'Files' => [ 'type' => 'list', 'member' => [ 'shape' => 'File', ], ], 'ForwardingConfig' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'ForwardingConfigState', ], ], ], 'ForwardingConfigState' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'GatewayCapabilitySummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'GatewayCapabilitySummary', ], ], 'GatewayCapabilitySummary' => [ 'type' => 'structure', 'required' => [ 'capabilityNamespace', 'capabilitySyncStatus', ], 'members' => [ 'capabilityNamespace' => [ 'shape' => 'CapabilityNamespace', ], 'capabilitySyncStatus' => [ 'shape' => 'CapabilitySyncStatus', ], ], ], 'GatewayName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[^\\u0000-\\u001F\\u007F]+', ], 'GatewayPlatform' => [ 'type' => 'structure', 'members' => [ 'greengrass' => [ 'shape' => 'Greengrass', ], 'greengrassV2' => [ 'shape' => 'GreengrassV2', ], 'siemensIE' => [ 'shape' => 'SiemensIE', ], ], ], 'GatewaySummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'GatewaySummary', ], ], 'GatewaySummary' => [ 'type' => 'structure', 'required' => [ 'gatewayId', 'gatewayName', 'creationDate', 'lastUpdateDate', ], 'members' => [ 'gatewayId' => [ 'shape' => 'ID', ], 'gatewayName' => [ 'shape' => 'GatewayName', ], 'gatewayPlatform' => [ 'shape' => 'GatewayPlatform', ], 'gatewayVersion' => [ 'shape' => 'GatewayVersion', ], 'gatewayCapabilitySummaries' => [ 'shape' => 'GatewayCapabilitySummaries', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastUpdateDate' => [ 'shape' => 'Timestamp', ], ], ], 'GatewayVersion' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^[0-9]+$', ], 'GetAssetPropertyAggregatesRequest' => [ 'type' => 'structure', 'required' => [ 'aggregateTypes', 'resolution', 'startDate', 'endDate', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'assetId', ], 'propertyId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'propertyId', ], 'propertyAlias' => [ 'shape' => 'AssetPropertyAlias', 'location' => 'querystring', 'locationName' => 'propertyAlias', ], 'aggregateTypes' => [ 'shape' => 'AggregateTypes', 'location' => 'querystring', 'locationName' => 'aggregateTypes', ], 'resolution' => [ 'shape' => 'Resolution', 'location' => 'querystring', 'locationName' => 'resolution', ], 'qualities' => [ 'shape' => 'Qualities', 'location' => 'querystring', 'locationName' => 'qualities', ], 'startDate' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'startDate', ], 'endDate' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'endDate', ], 'timeOrdering' => [ 'shape' => 'TimeOrdering', 'location' => 'querystring', 'locationName' => 'timeOrdering', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'GetAssetPropertyValueAggregatesMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'GetAssetPropertyAggregatesResponse' => [ 'type' => 'structure', 'required' => [ 'aggregatedValues', ], 'members' => [ 'aggregatedValues' => [ 'shape' => 'AggregatedValues', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetAssetPropertyValueAggregatesMaxResults' => [ 'type' => 'integer', 'min' => 1, ], 'GetAssetPropertyValueHistoryMaxResults' => [ 'type' => 'integer', 'min' => 1, ], 'GetAssetPropertyValueHistoryRequest' => [ 'type' => 'structure', 'members' => [ 'assetId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'assetId', ], 'propertyId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'propertyId', ], 'propertyAlias' => [ 'shape' => 'AssetPropertyAlias', 'location' => 'querystring', 'locationName' => 'propertyAlias', ], 'startDate' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'startDate', ], 'endDate' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'endDate', ], 'qualities' => [ 'shape' => 'Qualities', 'location' => 'querystring', 'locationName' => 'qualities', ], 'timeOrdering' => [ 'shape' => 'TimeOrdering', 'location' => 'querystring', 'locationName' => 'timeOrdering', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'GetAssetPropertyValueHistoryMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'GetAssetPropertyValueHistoryResponse' => [ 'type' => 'structure', 'required' => [ 'assetPropertyValueHistory', ], 'members' => [ 'assetPropertyValueHistory' => [ 'shape' => 'AssetPropertyValueHistory', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetAssetPropertyValueRequest' => [ 'type' => 'structure', 'members' => [ 'assetId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'assetId', ], 'propertyId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'propertyId', ], 'propertyAlias' => [ 'shape' => 'AssetPropertyAlias', 'location' => 'querystring', 'locationName' => 'propertyAlias', ], ], ], 'GetAssetPropertyValueResponse' => [ 'type' => 'structure', 'members' => [ 'propertyValue' => [ 'shape' => 'AssetPropertyValue', ], ], ], 'GetInterpolatedAssetPropertyValuesRequest' => [ 'type' => 'structure', 'required' => [ 'startTimeInSeconds', 'endTimeInSeconds', 'quality', 'intervalInSeconds', 'type', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'assetId', ], 'propertyId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'propertyId', ], 'propertyAlias' => [ 'shape' => 'AssetPropertyAlias', 'location' => 'querystring', 'locationName' => 'propertyAlias', ], 'startTimeInSeconds' => [ 'shape' => 'TimeInSeconds', 'location' => 'querystring', 'locationName' => 'startTimeInSeconds', ], 'startTimeOffsetInNanos' => [ 'shape' => 'OffsetInNanos', 'location' => 'querystring', 'locationName' => 'startTimeOffsetInNanos', ], 'endTimeInSeconds' => [ 'shape' => 'TimeInSeconds', 'location' => 'querystring', 'locationName' => 'endTimeInSeconds', ], 'endTimeOffsetInNanos' => [ 'shape' => 'OffsetInNanos', 'location' => 'querystring', 'locationName' => 'endTimeOffsetInNanos', ], 'quality' => [ 'shape' => 'Quality', 'location' => 'querystring', 'locationName' => 'quality', ], 'intervalInSeconds' => [ 'shape' => 'IntervalInSeconds', 'location' => 'querystring', 'locationName' => 'intervalInSeconds', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxInterpolatedResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'type' => [ 'shape' => 'InterpolationType', 'location' => 'querystring', 'locationName' => 'type', ], 'intervalWindowInSeconds' => [ 'shape' => 'IntervalWindowInSeconds', 'location' => 'querystring', 'locationName' => 'intervalWindowInSeconds', ], ], ], 'GetInterpolatedAssetPropertyValuesResponse' => [ 'type' => 'structure', 'required' => [ 'interpolatedAssetPropertyValues', ], 'members' => [ 'interpolatedAssetPropertyValues' => [ 'shape' => 'InterpolatedAssetPropertyValues', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'Greengrass' => [ 'type' => 'structure', 'required' => [ 'groupArn', ], 'members' => [ 'groupArn' => [ 'shape' => 'ARN', ], ], ], 'GreengrassV2' => [ 'type' => 'structure', 'required' => [ 'coreDeviceThingName', ], 'members' => [ 'coreDeviceThingName' => [ 'shape' => 'CoreDeviceThingName', ], 'coreDeviceOperatingSystem' => [ 'shape' => 'CoreDeviceOperatingSystem', ], ], ], 'GroupIdentity' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'IdentityId', ], ], ], 'IAMRoleIdentity' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'IamArn', ], ], ], 'IAMUserIdentity' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'IamArn', ], ], ], 'ID' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', ], 'IDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'ID', ], 'max' => 100, 'min' => 1, ], 'IamArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '^arn:aws(-cn|-us-gov)?:[a-zA-Z0-9-:\\/_\\.\\+=,@]+$', ], 'Identity' => [ 'type' => 'structure', 'members' => [ 'user' => [ 'shape' => 'UserIdentity', ], 'group' => [ 'shape' => 'GroupIdentity', ], 'iamUser' => [ 'shape' => 'IAMUserIdentity', ], 'iamRole' => [ 'shape' => 'IAMRoleIdentity', ], ], ], 'IdentityId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '\\S+', ], 'IdentityType' => [ 'type' => 'string', 'enum' => [ 'USER', 'GROUP', 'IAM', ], ], 'Image' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ID', ], 'file' => [ 'shape' => 'ImageFile', ], ], ], 'ImageFile' => [ 'type' => 'structure', 'required' => [ 'data', 'type', ], 'members' => [ 'data' => [ 'shape' => 'ImageFileData', ], 'type' => [ 'shape' => 'ImageFileType', ], ], ], 'ImageFileData' => [ 'type' => 'blob', 'max' => 1500000, 'min' => 1, ], 'ImageFileType' => [ 'type' => 'string', 'enum' => [ 'PNG', ], ], 'ImageLocation' => [ 'type' => 'structure', 'required' => [ 'id', 'url', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'url' => [ 'shape' => 'Url', ], ], ], 'InternalFailureException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InterpolatedAssetPropertyValue' => [ 'type' => 'structure', 'required' => [ 'timestamp', 'value', ], 'members' => [ 'timestamp' => [ 'shape' => 'TimeInNanos', ], 'value' => [ 'shape' => 'Variant', ], ], ], 'InterpolatedAssetPropertyValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'InterpolatedAssetPropertyValue', ], ], 'InterpolationType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'Interval' => [ 'type' => 'string', 'max' => 23, 'min' => 2, ], 'IntervalInSeconds' => [ 'type' => 'long', 'max' => 320000000, 'min' => 1, ], 'IntervalWindowInSeconds' => [ 'type' => 'long', 'max' => 320000000, 'min' => 1, ], 'InvalidRequestException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvocationOutput' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'citations' => [ 'shape' => 'Citations', ], ], 'event' => true, ], 'InvokeAssistantRequest' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'conversationId' => [ 'shape' => 'ConversationId', ], 'message' => [ 'shape' => 'MessageInput', ], 'enableTrace' => [ 'shape' => 'PrimitiveBoolean', ], ], ], 'InvokeAssistantResponse' => [ 'type' => 'structure', 'required' => [ 'body', 'conversationId', ], 'members' => [ 'body' => [ 'shape' => 'ResponseStream', ], 'conversationId' => [ 'shape' => 'ConversationId', 'location' => 'header', 'locationName' => 'x-amz-iotsitewise-assistant-conversation-id', ], ], 'payload' => 'body', ], 'IotCoreThingName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9:_-]+$', ], 'JobConfiguration' => [ 'type' => 'structure', 'required' => [ 'fileFormat', ], 'members' => [ 'fileFormat' => [ 'shape' => 'FileFormat', ], ], ], 'JobStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'CANCELLED', 'RUNNING', 'COMPLETED', 'FAILED', 'COMPLETED_WITH_FAILURES', ], ], 'JobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobSummary', ], ], 'JobSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'name', 'status', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'name' => [ 'shape' => 'Name', ], 'status' => [ 'shape' => 'JobStatus', ], ], ], 'KendraSourceDetail' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseArn', 'roleArn', ], 'members' => [ 'knowledgeBaseArn' => [ 'shape' => 'ARN', ], 'roleArn' => [ 'shape' => 'ARN', ], ], ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'LimitExceededException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 410, ], 'exception' => true, ], 'ListAccessPoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'identityType' => [ 'shape' => 'IdentityType', 'location' => 'querystring', 'locationName' => 'identityType', ], 'identityId' => [ 'shape' => 'IdentityId', 'location' => 'querystring', 'locationName' => 'identityId', ], 'resourceType' => [ 'shape' => 'ResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'resourceId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'resourceId', ], 'iamArn' => [ 'shape' => 'IamArn', 'location' => 'querystring', 'locationName' => 'iamArn', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAccessPoliciesResponse' => [ 'type' => 'structure', 'required' => [ 'accessPolicySummaries', ], 'members' => [ 'accessPolicySummaries' => [ 'shape' => 'AccessPolicySummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListActionsRequest' => [ 'type' => 'structure', 'required' => [ 'targetResourceType', 'targetResourceId', ], 'members' => [ 'targetResourceType' => [ 'shape' => 'TargetResourceType', 'location' => 'querystring', 'locationName' => 'targetResourceType', ], 'targetResourceId' => [ 'shape' => 'CustomID', 'location' => 'querystring', 'locationName' => 'targetResourceId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListActionsResponse' => [ 'type' => 'structure', 'required' => [ 'actionSummaries', 'nextToken', ], 'members' => [ 'actionSummaries' => [ 'shape' => 'ActionSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAssetModelCompositeModelsRequest' => [ 'type' => 'structure', 'required' => [ 'assetModelId', ], 'members' => [ 'assetModelId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetModelId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'assetModelVersion' => [ 'shape' => 'AssetModelVersionFilter', 'location' => 'querystring', 'locationName' => 'assetModelVersion', ], ], ], 'ListAssetModelCompositeModelsResponse' => [ 'type' => 'structure', 'required' => [ 'assetModelCompositeModelSummaries', ], 'members' => [ 'assetModelCompositeModelSummaries' => [ 'shape' => 'AssetModelCompositeModelSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAssetModelPropertiesFilter' => [ 'type' => 'string', 'enum' => [ 'ALL', 'BASE', ], ], 'ListAssetModelPropertiesRequest' => [ 'type' => 'structure', 'required' => [ 'assetModelId', ], 'members' => [ 'assetModelId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetModelId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'filter' => [ 'shape' => 'ListAssetModelPropertiesFilter', 'location' => 'querystring', 'locationName' => 'filter', ], 'assetModelVersion' => [ 'shape' => 'AssetModelVersionFilter', 'location' => 'querystring', 'locationName' => 'assetModelVersion', ], ], ], 'ListAssetModelPropertiesResponse' => [ 'type' => 'structure', 'required' => [ 'assetModelPropertySummaries', ], 'members' => [ 'assetModelPropertySummaries' => [ 'shape' => 'AssetModelPropertySummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAssetModelsRequest' => [ 'type' => 'structure', 'members' => [ 'assetModelTypes' => [ 'shape' => 'ListAssetModelsTypeFilter', 'location' => 'querystring', 'locationName' => 'assetModelTypes', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'assetModelVersion' => [ 'shape' => 'AssetModelVersionFilter', 'location' => 'querystring', 'locationName' => 'assetModelVersion', ], ], ], 'ListAssetModelsResponse' => [ 'type' => 'structure', 'required' => [ 'assetModelSummaries', ], 'members' => [ 'assetModelSummaries' => [ 'shape' => 'AssetModelSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAssetModelsTypeFilter' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetModelType', ], ], 'ListAssetPropertiesFilter' => [ 'type' => 'string', 'enum' => [ 'ALL', 'BASE', ], ], 'ListAssetPropertiesRequest' => [ 'type' => 'structure', 'required' => [ 'assetId', ], 'members' => [ 'assetId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'filter' => [ 'shape' => 'ListAssetPropertiesFilter', 'location' => 'querystring', 'locationName' => 'filter', ], ], ], 'ListAssetPropertiesResponse' => [ 'type' => 'structure', 'required' => [ 'assetPropertySummaries', ], 'members' => [ 'assetPropertySummaries' => [ 'shape' => 'AssetPropertySummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAssetRelationshipsRequest' => [ 'type' => 'structure', 'required' => [ 'assetId', 'traversalType', ], 'members' => [ 'assetId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetId', ], 'traversalType' => [ 'shape' => 'TraversalType', 'location' => 'querystring', 'locationName' => 'traversalType', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAssetRelationshipsResponse' => [ 'type' => 'structure', 'required' => [ 'assetRelationshipSummaries', ], 'members' => [ 'assetRelationshipSummaries' => [ 'shape' => 'AssetRelationshipSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAssetsFilter' => [ 'type' => 'string', 'enum' => [ 'ALL', 'TOP_LEVEL', ], ], 'ListAssetsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'assetModelId' => [ 'shape' => 'CustomID', 'location' => 'querystring', 'locationName' => 'assetModelId', ], 'filter' => [ 'shape' => 'ListAssetsFilter', 'location' => 'querystring', 'locationName' => 'filter', ], ], ], 'ListAssetsResponse' => [ 'type' => 'structure', 'required' => [ 'assetSummaries', ], 'members' => [ 'assetSummaries' => [ 'shape' => 'AssetSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAssociatedAssetsRequest' => [ 'type' => 'structure', 'required' => [ 'assetId', ], 'members' => [ 'assetId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetId', ], 'hierarchyId' => [ 'shape' => 'CustomID', 'location' => 'querystring', 'locationName' => 'hierarchyId', ], 'traversalDirection' => [ 'shape' => 'TraversalDirection', 'location' => 'querystring', 'locationName' => 'traversalDirection', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAssociatedAssetsResponse' => [ 'type' => 'structure', 'required' => [ 'assetSummaries', ], 'members' => [ 'assetSummaries' => [ 'shape' => 'AssociatedAssetsSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBulkImportJobsFilter' => [ 'type' => 'string', 'enum' => [ 'ALL', 'PENDING', 'RUNNING', 'CANCELLED', 'FAILED', 'COMPLETED_WITH_FAILURES', 'COMPLETED', ], ], 'ListBulkImportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'filter' => [ 'shape' => 'ListBulkImportJobsFilter', 'location' => 'querystring', 'locationName' => 'filter', ], ], ], 'ListBulkImportJobsResponse' => [ 'type' => 'structure', 'required' => [ 'jobSummaries', ], 'members' => [ 'jobSummaries' => [ 'shape' => 'JobSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCompositionRelationshipsRequest' => [ 'type' => 'structure', 'required' => [ 'assetModelId', ], 'members' => [ 'assetModelId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'assetModelId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListCompositionRelationshipsResponse' => [ 'type' => 'structure', 'required' => [ 'compositionRelationshipSummaries', ], 'members' => [ 'compositionRelationshipSummaries' => [ 'shape' => 'CompositionRelationshipSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDashboardsRequest' => [ 'type' => 'structure', 'required' => [ 'projectId', ], 'members' => [ 'projectId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'projectId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListDashboardsResponse' => [ 'type' => 'structure', 'required' => [ 'dashboardSummaries', ], 'members' => [ 'dashboardSummaries' => [ 'shape' => 'DashboardSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDatasetsRequest' => [ 'type' => 'structure', 'required' => [ 'sourceType', ], 'members' => [ 'sourceType' => [ 'shape' => 'DatasetSourceType', 'location' => 'querystring', 'locationName' => 'sourceType', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListDatasetsResponse' => [ 'type' => 'structure', 'required' => [ 'datasetSummaries', ], 'members' => [ 'datasetSummaries' => [ 'shape' => 'DatasetSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListGatewaysRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListGatewaysResponse' => [ 'type' => 'structure', 'required' => [ 'gatewaySummaries', ], 'members' => [ 'gatewaySummaries' => [ 'shape' => 'GatewaySummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPortalsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListPortalsResponse' => [ 'type' => 'structure', 'members' => [ 'portalSummaries' => [ 'shape' => 'PortalSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListProjectAssetsRequest' => [ 'type' => 'structure', 'required' => [ 'projectId', ], 'members' => [ 'projectId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'projectId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListProjectAssetsResponse' => [ 'type' => 'structure', 'required' => [ 'assetIds', ], 'members' => [ 'assetIds' => [ 'shape' => 'AssetIDs', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListProjectsRequest' => [ 'type' => 'structure', 'required' => [ 'portalId', ], 'members' => [ 'portalId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'portalId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListProjectsResponse' => [ 'type' => 'structure', 'required' => [ 'projectSummaries', ], 'members' => [ 'projectSummaries' => [ 'shape' => 'ProjectSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'querystring', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListTimeSeriesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'assetId' => [ 'shape' => 'CustomID', 'location' => 'querystring', 'locationName' => 'assetId', ], 'aliasPrefix' => [ 'shape' => 'PropertyAlias', 'location' => 'querystring', 'locationName' => 'aliasPrefix', ], 'timeSeriesType' => [ 'shape' => 'ListTimeSeriesType', 'location' => 'querystring', 'locationName' => 'timeSeriesType', ], ], ], 'ListTimeSeriesResponse' => [ 'type' => 'structure', 'required' => [ 'TimeSeriesSummaries', ], 'members' => [ 'TimeSeriesSummaries' => [ 'shape' => 'TimeSeriesSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTimeSeriesType' => [ 'type' => 'string', 'enum' => [ 'ASSOCIATED', 'DISASSOCIATED', ], ], 'Location' => [ 'type' => 'structure', 'members' => [ 'uri' => [ 'shape' => 'String', ], ], ], 'LoggingLevel' => [ 'type' => 'string', 'enum' => [ 'ERROR', 'INFO', 'OFF', ], ], 'LoggingOptions' => [ 'type' => 'structure', 'required' => [ 'level', ], 'members' => [ 'level' => [ 'shape' => 'LoggingLevel', ], ], ], 'Macro' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[^\\u0000-\\u001F\\u007F]+', ], 'MaxInterpolatedResults' => [ 'type' => 'integer', 'min' => 1, ], 'MaxResults' => [ 'type' => 'integer', 'max' => 250, 'min' => 1, ], 'Measurement' => [ 'type' => 'structure', 'members' => [ 'processingConfig' => [ 'shape' => 'MeasurementProcessingConfig', ], ], ], 'MeasurementProcessingConfig' => [ 'type' => 'structure', 'required' => [ 'forwardingConfig', ], 'members' => [ 'forwardingConfig' => [ 'shape' => 'ForwardingConfig', ], ], ], 'MessageInput' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, 'sensitive' => true, ], 'Metric' => [ 'type' => 'structure', 'required' => [ 'expression', 'variables', 'window', ], 'members' => [ 'expression' => [ 'shape' => 'Expression', ], 'variables' => [ 'shape' => 'ExpressionVariables', ], 'window' => [ 'shape' => 'MetricWindow', ], 'processingConfig' => [ 'shape' => 'MetricProcessingConfig', ], ], ], 'MetricProcessingConfig' => [ 'type' => 'structure', 'required' => [ 'computeLocation', ], 'members' => [ 'computeLocation' => [ 'shape' => 'ComputeLocation', ], ], ], 'MetricWindow' => [ 'type' => 'structure', 'members' => [ 'tumbling' => [ 'shape' => 'TumblingWindow', ], ], ], 'MonitorErrorCode' => [ 'type' => 'string', 'enum' => [ 'INTERNAL_FAILURE', 'VALIDATION_ERROR', 'LIMIT_EXCEEDED', ], ], 'MonitorErrorDetails' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'MonitorErrorCode', ], 'message' => [ 'shape' => 'MonitorErrorMessage', ], ], ], 'MonitorErrorMessage' => [ 'type' => 'string', ], 'MultiLayerStorage' => [ 'type' => 'structure', 'required' => [ 'customerManagedS3Storage', ], 'members' => [ 'customerManagedS3Storage' => [ 'shape' => 'CustomerManagedS3Storage', ], ], ], 'Name' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[^\\u0000-\\u001F\\u007F]+', ], 'NextToken' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '[A-Za-z0-9+/=]+', ], 'NullableBoolean' => [ 'type' => 'boolean', 'box' => true, ], 'NumberOfDays' => [ 'type' => 'integer', ], 'Offset' => [ 'type' => 'string', 'max' => 25, 'min' => 2, ], 'OffsetInNanos' => [ 'type' => 'integer', 'max' => 999999999, 'min' => 0, ], 'Parquet' => [ 'type' => 'structure', 'members' => [], ], 'Permission' => [ 'type' => 'string', 'enum' => [ 'ADMINISTRATOR', 'VIEWER', ], ], 'PortalClientId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[!-~]*', ], 'PortalResource' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], ], ], 'PortalState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'PENDING', 'UPDATING', 'DELETING', 'ACTIVE', 'FAILED', ], ], 'PortalStatus' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'PortalState', ], 'error' => [ 'shape' => 'MonitorErrorDetails', ], ], ], 'PortalSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'PortalSummary', ], ], 'PortalSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'name', 'startUrl', 'status', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'startUrl' => [ 'shape' => 'Url', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastUpdateDate' => [ 'shape' => 'Timestamp', ], 'roleArn' => [ 'shape' => 'IamArn', ], 'status' => [ 'shape' => 'PortalStatus', ], 'portalType' => [ 'shape' => 'PortalType', ], ], ], 'PortalTools' => [ 'type' => 'list', 'member' => [ 'shape' => 'Name', ], ], 'PortalType' => [ 'type' => 'string', 'enum' => [ 'SITEWISE_PORTAL_V1', 'SITEWISE_PORTAL_V2', ], ], 'PortalTypeConfiguration' => [ 'type' => 'map', 'key' => [ 'shape' => 'PortalTypeKey', ], 'value' => [ 'shape' => 'PortalTypeEntry', ], ], 'PortalTypeEntry' => [ 'type' => 'structure', 'members' => [ 'portalTools' => [ 'shape' => 'PortalTools', ], ], ], 'PortalTypeKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'PreconditionFailedException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceArn', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceArn' => [ 'shape' => 'ResourceArn', ], ], 'error' => [ 'httpStatusCode' => 412, ], 'exception' => true, ], 'PrimitiveBoolean' => [ 'type' => 'boolean', ], 'ProjectResource' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], ], ], 'ProjectSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProjectSummary', ], ], 'ProjectSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'name', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastUpdateDate' => [ 'shape' => 'Timestamp', ], ], ], 'Property' => [ 'type' => 'structure', 'required' => [ 'id', 'name', 'dataType', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'name' => [ 'shape' => 'Name', ], 'alias' => [ 'shape' => 'PropertyAlias', ], 'notification' => [ 'shape' => 'PropertyNotification', ], 'dataType' => [ 'shape' => 'PropertyDataType', ], 'unit' => [ 'shape' => 'PropertyUnit', ], 'type' => [ 'shape' => 'PropertyType', ], 'path' => [ 'shape' => 'AssetPropertyPath', ], 'externalId' => [ 'shape' => 'ExternalId', ], ], ], 'PropertyAlias' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[^\\u0000-\\u001F\\u007F]+', ], 'PropertyDataType' => [ 'type' => 'string', 'enum' => [ 'STRING', 'INTEGER', 'DOUBLE', 'BOOLEAN', 'STRUCT', ], ], 'PropertyNotification' => [ 'type' => 'structure', 'required' => [ 'topic', 'state', ], 'members' => [ 'topic' => [ 'shape' => 'PropertyNotificationTopic', ], 'state' => [ 'shape' => 'PropertyNotificationState', ], ], ], 'PropertyNotificationState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'PropertyNotificationTopic' => [ 'type' => 'string', ], 'PropertyType' => [ 'type' => 'structure', 'members' => [ 'attribute' => [ 'shape' => 'Attribute', ], 'measurement' => [ 'shape' => 'Measurement', ], 'transform' => [ 'shape' => 'Transform', ], 'metric' => [ 'shape' => 'Metric', ], ], ], 'PropertyUnit' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[^\\u0000-\\u001F\\u007F]+', ], 'PropertyValueBooleanValue' => [ 'type' => 'boolean', ], 'PropertyValueDoubleValue' => [ 'type' => 'double', ], 'PropertyValueIntegerValue' => [ 'type' => 'integer', ], 'PropertyValueNullValue' => [ 'type' => 'structure', 'required' => [ 'valueType', ], 'members' => [ 'valueType' => [ 'shape' => 'RawValueType', ], ], ], 'PropertyValueStringValue' => [ 'type' => 'string', ], 'PutAssetPropertyValueEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'PutAssetPropertyValueEntry', ], ], 'PutAssetPropertyValueEntry' => [ 'type' => 'structure', 'required' => [ 'entryId', 'propertyValues', ], 'members' => [ 'entryId' => [ 'shape' => 'EntryId', ], 'assetId' => [ 'shape' => 'ID', ], 'propertyId' => [ 'shape' => 'ID', ], 'propertyAlias' => [ 'shape' => 'AssetPropertyAlias', ], 'propertyValues' => [ 'shape' => 'AssetPropertyValues', ], ], ], 'PutDefaultEncryptionConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'encryptionType', ], 'members' => [ 'encryptionType' => [ 'shape' => 'EncryptionType', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'PutDefaultEncryptionConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'encryptionType', 'configurationStatus', ], 'members' => [ 'encryptionType' => [ 'shape' => 'EncryptionType', ], 'kmsKeyArn' => [ 'shape' => 'ARN', ], 'configurationStatus' => [ 'shape' => 'ConfigurationStatus', ], ], ], 'PutLoggingOptionsRequest' => [ 'type' => 'structure', 'required' => [ 'loggingOptions', ], 'members' => [ 'loggingOptions' => [ 'shape' => 'LoggingOptions', ], ], ], 'PutLoggingOptionsResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutStorageConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'storageType', ], 'members' => [ 'storageType' => [ 'shape' => 'StorageType', ], 'multiLayerStorage' => [ 'shape' => 'MultiLayerStorage', ], 'disassociatedDataStorage' => [ 'shape' => 'DisassociatedDataStorageState', ], 'retentionPeriod' => [ 'shape' => 'RetentionPeriod', ], 'warmTier' => [ 'shape' => 'WarmTierState', ], 'warmTierRetentionPeriod' => [ 'shape' => 'WarmTierRetentionPeriod', ], 'disallowIngestNullNaN' => [ 'shape' => 'DisallowIngestNullNaN', ], ], ], 'PutStorageConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'storageType', 'configurationStatus', ], 'members' => [ 'storageType' => [ 'shape' => 'StorageType', ], 'multiLayerStorage' => [ 'shape' => 'MultiLayerStorage', ], 'disassociatedDataStorage' => [ 'shape' => 'DisassociatedDataStorageState', ], 'retentionPeriod' => [ 'shape' => 'RetentionPeriod', ], 'configurationStatus' => [ 'shape' => 'ConfigurationStatus', ], 'warmTier' => [ 'shape' => 'WarmTierState', ], 'warmTierRetentionPeriod' => [ 'shape' => 'WarmTierRetentionPeriod', ], 'disallowIngestNullNaN' => [ 'shape' => 'DisallowIngestNullNaN', ], ], ], 'Qualities' => [ 'type' => 'list', 'member' => [ 'shape' => 'Quality', ], 'max' => 1, 'min' => 1, ], 'Quality' => [ 'type' => 'string', 'enum' => [ 'GOOD', 'BAD', 'UNCERTAIN', ], ], 'QueryStatement' => [ 'type' => 'string', 'min' => 1, 'pattern' => '^[\\s\\S]+$', ], 'QueryTimeoutException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'RawValueType' => [ 'type' => 'string', 'enum' => [ 'D', 'B', 'S', 'I', 'U', ], ], 'Reference' => [ 'type' => 'structure', 'members' => [ 'dataset' => [ 'shape' => 'DataSetReference', ], ], ], 'Resolution' => [ 'type' => 'string', 'max' => 3, 'min' => 2, 'pattern' => '1m|15m|1h|1d', ], 'Resource' => [ 'type' => 'structure', 'members' => [ 'portal' => [ 'shape' => 'PortalResource', ], 'project' => [ 'shape' => 'ProjectResource', ], ], ], 'ResourceAlreadyExistsException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceArn', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceArn' => [ 'shape' => 'ResourceArn', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResourceArn' => [ 'type' => 'string', ], 'ResourceId' => [ 'type' => 'string', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'PORTAL', 'PROJECT', ], ], 'ResponseStream' => [ 'type' => 'structure', 'members' => [ 'trace' => [ 'shape' => 'Trace', ], 'output' => [ 'shape' => 'InvocationOutput', ], 'accessDeniedException' => [ 'shape' => 'AccessDeniedException', ], 'conflictingOperationException' => [ 'shape' => 'ConflictingOperationException', ], 'internalFailureException' => [ 'shape' => 'InternalFailureException', ], 'invalidRequestException' => [ 'shape' => 'InvalidRequestException', ], 'limitExceededException' => [ 'shape' => 'LimitExceededException', ], 'resourceNotFoundException' => [ 'shape' => 'ResourceNotFoundException', ], 'throttlingException' => [ 'shape' => 'ThrottlingException', ], ], 'eventstream' => true, ], 'RestrictedDescription' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^[a-zA-Z0-9 _\\-#$*!@]+$', ], 'RestrictedName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[a-zA-Z0-9 _\\-#$*!@]+$', ], 'RetentionPeriod' => [ 'type' => 'structure', 'members' => [ 'numberOfDays' => [ 'shape' => 'NumberOfDays', ], 'unlimited' => [ 'shape' => 'Unlimited', ], ], ], 'Row' => [ 'type' => 'structure', 'required' => [ 'data', ], 'members' => [ 'data' => [ 'shape' => 'DatumList', ], ], ], 'Rows' => [ 'type' => 'list', 'member' => [ 'shape' => 'Row', ], ], 'SSOApplicationId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[!-~]*', ], 'ScalarType' => [ 'type' => 'string', 'enum' => [ 'BOOLEAN', 'INT', 'DOUBLE', 'TIMESTAMP', 'STRING', ], ], 'ScalarValue' => [ 'type' => 'string', ], 'SelectAll' => [ 'type' => 'string', 'pattern' => '\\*', ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], 'SiemensIE' => [ 'type' => 'structure', 'required' => [ 'iotCoreThingName', ], 'members' => [ 'iotCoreThingName' => [ 'shape' => 'IotCoreThingName', ], ], ], 'Source' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'String', ], 'location' => [ 'shape' => 'Location', ], ], ], 'SourceDetail' => [ 'type' => 'structure', 'members' => [ 'kendra' => [ 'shape' => 'KendraSourceDetail', ], ], ], 'StorageType' => [ 'type' => 'string', 'enum' => [ 'SITEWISE_DEFAULT_STORAGE', 'MULTI_LAYER_STORAGE', ], ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'querystring', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TargetResource' => [ 'type' => 'structure', 'required' => [ 'assetId', ], 'members' => [ 'assetId' => [ 'shape' => 'CustomID', ], ], ], 'TargetResourceType' => [ 'type' => 'string', 'enum' => [ 'ASSET', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'TimeInNanos' => [ 'type' => 'structure', 'required' => [ 'timeInSeconds', ], 'members' => [ 'timeInSeconds' => [ 'shape' => 'TimeInSeconds', ], 'offsetInNanos' => [ 'shape' => 'OffsetInNanos', ], ], ], 'TimeInSeconds' => [ 'type' => 'long', 'max' => 9223372036854774, 'min' => 1, ], 'TimeOrdering' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'TimeSeriesId' => [ 'type' => 'string', 'max' => 73, 'min' => 36, ], 'TimeSeriesSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'TimeSeriesSummary', ], ], 'TimeSeriesSummary' => [ 'type' => 'structure', 'required' => [ 'timeSeriesId', 'dataType', 'timeSeriesCreationDate', 'timeSeriesLastUpdateDate', 'timeSeriesArn', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', ], 'propertyId' => [ 'shape' => 'ID', ], 'alias' => [ 'shape' => 'PropertyAlias', ], 'timeSeriesId' => [ 'shape' => 'TimeSeriesId', ], 'dataType' => [ 'shape' => 'PropertyDataType', ], 'dataTypeSpec' => [ 'shape' => 'Name', ], 'timeSeriesCreationDate' => [ 'shape' => 'Timestamp', ], 'timeSeriesLastUpdateDate' => [ 'shape' => 'Timestamp', ], 'timeSeriesArn' => [ 'shape' => 'ARN', ], ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'Timestamps' => [ 'type' => 'list', 'member' => [ 'shape' => 'TimeInNanos', ], ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'resourceName' => [ 'shape' => 'AmazonResourceName', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Trace' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'String', ], ], 'event' => true, ], 'Transform' => [ 'type' => 'structure', 'required' => [ 'expression', 'variables', ], 'members' => [ 'expression' => [ 'shape' => 'Expression', ], 'variables' => [ 'shape' => 'ExpressionVariables', ], 'processingConfig' => [ 'shape' => 'TransformProcessingConfig', ], ], ], 'TransformProcessingConfig' => [ 'type' => 'structure', 'required' => [ 'computeLocation', ], 'members' => [ 'computeLocation' => [ 'shape' => 'ComputeLocation', ], 'forwardingConfig' => [ 'shape' => 'ForwardingConfig', ], ], ], 'TraversalDirection' => [ 'type' => 'string', 'enum' => [ 'PARENT', 'CHILD', ], ], 'TraversalType' => [ 'type' => 'string', 'enum' => [ 'PATH_TO_ROOT', ], ], 'TumblingWindow' => [ 'type' => 'structure', 'required' => [ 'interval', ], 'members' => [ 'interval' => [ 'shape' => 'Interval', ], 'offset' => [ 'shape' => 'Offset', ], ], ], 'UnauthorizedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], 'Unlimited' => [ 'type' => 'boolean', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'querystring', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAccessPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'accessPolicyId', 'accessPolicyIdentity', 'accessPolicyResource', 'accessPolicyPermission', ], 'members' => [ 'accessPolicyId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'accessPolicyId', ], 'accessPolicyIdentity' => [ 'shape' => 'Identity', ], 'accessPolicyResource' => [ 'shape' => 'Resource', ], 'accessPolicyPermission' => [ 'shape' => 'Permission', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateAccessPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAssetModelCompositeModelRequest' => [ 'type' => 'structure', 'required' => [ 'assetModelId', 'assetModelCompositeModelId', 'assetModelCompositeModelName', ], 'members' => [ 'assetModelId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetModelId', ], 'assetModelCompositeModelId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetModelCompositeModelId', ], 'assetModelCompositeModelExternalId' => [ 'shape' => 'ExternalId', ], 'assetModelCompositeModelDescription' => [ 'shape' => 'Description', ], 'assetModelCompositeModelName' => [ 'shape' => 'Name', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'assetModelCompositeModelProperties' => [ 'shape' => 'AssetModelProperties', ], 'ifMatch' => [ 'shape' => 'ETag', 'location' => 'header', 'locationName' => 'If-Match', ], 'ifNoneMatch' => [ 'shape' => 'SelectAll', 'location' => 'header', 'locationName' => 'If-None-Match', ], 'matchForVersionType' => [ 'shape' => 'AssetModelVersionType', 'location' => 'header', 'locationName' => 'Match-For-Version-Type', ], ], ], 'UpdateAssetModelCompositeModelResponse' => [ 'type' => 'structure', 'required' => [ 'assetModelCompositeModelPath', 'assetModelStatus', ], 'members' => [ 'assetModelCompositeModelPath' => [ 'shape' => 'AssetModelCompositeModelPath', ], 'assetModelStatus' => [ 'shape' => 'AssetModelStatus', ], ], ], 'UpdateAssetModelRequest' => [ 'type' => 'structure', 'required' => [ 'assetModelId', 'assetModelName', ], 'members' => [ 'assetModelId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetModelId', ], 'assetModelExternalId' => [ 'shape' => 'ExternalId', ], 'assetModelName' => [ 'shape' => 'Name', ], 'assetModelDescription' => [ 'shape' => 'Description', ], 'assetModelProperties' => [ 'shape' => 'AssetModelProperties', ], 'assetModelHierarchies' => [ 'shape' => 'AssetModelHierarchies', ], 'assetModelCompositeModels' => [ 'shape' => 'AssetModelCompositeModels', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'ifMatch' => [ 'shape' => 'ETag', 'location' => 'header', 'locationName' => 'If-Match', ], 'ifNoneMatch' => [ 'shape' => 'SelectAll', 'location' => 'header', 'locationName' => 'If-None-Match', ], 'matchForVersionType' => [ 'shape' => 'AssetModelVersionType', 'location' => 'header', 'locationName' => 'Match-For-Version-Type', ], ], ], 'UpdateAssetModelResponse' => [ 'type' => 'structure', 'required' => [ 'assetModelStatus', ], 'members' => [ 'assetModelStatus' => [ 'shape' => 'AssetModelStatus', ], ], ], 'UpdateAssetPropertyRequest' => [ 'type' => 'structure', 'required' => [ 'assetId', 'propertyId', ], 'members' => [ 'assetId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetId', ], 'propertyId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'propertyId', ], 'propertyAlias' => [ 'shape' => 'PropertyAlias', ], 'propertyNotificationState' => [ 'shape' => 'PropertyNotificationState', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'propertyUnit' => [ 'shape' => 'PropertyUnit', ], ], ], 'UpdateAssetRequest' => [ 'type' => 'structure', 'required' => [ 'assetId', 'assetName', ], 'members' => [ 'assetId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'assetId', ], 'assetName' => [ 'shape' => 'Name', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'assetDescription' => [ 'shape' => 'Description', ], 'assetExternalId' => [ 'shape' => 'ExternalId', ], ], ], 'UpdateAssetResponse' => [ 'type' => 'structure', 'required' => [ 'assetStatus', ], 'members' => [ 'assetStatus' => [ 'shape' => 'AssetStatus', ], ], ], 'UpdateDashboardRequest' => [ 'type' => 'structure', 'required' => [ 'dashboardId', 'dashboardName', 'dashboardDefinition', ], 'members' => [ 'dashboardId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'dashboardId', ], 'dashboardName' => [ 'shape' => 'Name', ], 'dashboardDescription' => [ 'shape' => 'Description', ], 'dashboardDefinition' => [ 'shape' => 'DashboardDefinition', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateDashboardResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'datasetId', 'datasetName', 'datasetSource', ], 'members' => [ 'datasetId' => [ 'shape' => 'CustomID', 'location' => 'uri', 'locationName' => 'datasetId', ], 'datasetName' => [ 'shape' => 'RestrictedName', ], 'datasetDescription' => [ 'shape' => 'RestrictedDescription', ], 'datasetSource' => [ 'shape' => 'DatasetSource', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateDatasetResponse' => [ 'type' => 'structure', 'members' => [ 'datasetId' => [ 'shape' => 'CustomID', ], 'datasetArn' => [ 'shape' => 'ARN', ], 'datasetStatus' => [ 'shape' => 'DatasetStatus', ], ], ], 'UpdateGatewayCapabilityConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'gatewayId', 'capabilityNamespace', 'capabilityConfiguration', ], 'members' => [ 'gatewayId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'gatewayId', ], 'capabilityNamespace' => [ 'shape' => 'CapabilityNamespace', ], 'capabilityConfiguration' => [ 'shape' => 'CapabilityConfiguration', ], ], ], 'UpdateGatewayCapabilityConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'capabilityNamespace', 'capabilitySyncStatus', ], 'members' => [ 'capabilityNamespace' => [ 'shape' => 'CapabilityNamespace', ], 'capabilitySyncStatus' => [ 'shape' => 'CapabilitySyncStatus', ], ], ], 'UpdateGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'gatewayId', 'gatewayName', ], 'members' => [ 'gatewayId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'gatewayId', ], 'gatewayName' => [ 'shape' => 'GatewayName', ], ], ], 'UpdatePortalRequest' => [ 'type' => 'structure', 'required' => [ 'portalId', 'portalName', 'portalContactEmail', 'roleArn', ], 'members' => [ 'portalId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'portalId', ], 'portalName' => [ 'shape' => 'Name', ], 'portalDescription' => [ 'shape' => 'Description', ], 'portalContactEmail' => [ 'shape' => 'Email', ], 'portalLogoImage' => [ 'shape' => 'Image', ], 'roleArn' => [ 'shape' => 'IamArn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'notificationSenderEmail' => [ 'shape' => 'Email', ], 'alarms' => [ 'shape' => 'Alarms', ], 'portalType' => [ 'shape' => 'PortalType', ], 'portalTypeConfiguration' => [ 'shape' => 'PortalTypeConfiguration', ], ], ], 'UpdatePortalResponse' => [ 'type' => 'structure', 'required' => [ 'portalStatus', ], 'members' => [ 'portalStatus' => [ 'shape' => 'PortalStatus', ], ], ], 'UpdateProjectRequest' => [ 'type' => 'structure', 'required' => [ 'projectId', 'projectName', ], 'members' => [ 'projectId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'projectId', ], 'projectName' => [ 'shape' => 'Name', ], 'projectDescription' => [ 'shape' => 'Description', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateProjectResponse' => [ 'type' => 'structure', 'members' => [], ], 'Url' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^(http|https)\\://\\S+', ], 'UserIdentity' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'IdentityId', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'VariableName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-z][a-z0-9_]*$', ], 'VariableValue' => [ 'type' => 'structure', 'members' => [ 'propertyId' => [ 'shape' => 'Macro', ], 'hierarchyId' => [ 'shape' => 'Macro', ], 'propertyPath' => [ 'shape' => 'AssetModelPropertyPath', ], ], ], 'Variant' => [ 'type' => 'structure', 'members' => [ 'stringValue' => [ 'shape' => 'PropertyValueStringValue', ], 'integerValue' => [ 'shape' => 'PropertyValueIntegerValue', ], 'doubleValue' => [ 'shape' => 'PropertyValueDoubleValue', ], 'booleanValue' => [ 'shape' => 'PropertyValueBooleanValue', ], 'nullValue' => [ 'shape' => 'PropertyValueNullValue', ], ], ], 'Version' => [ 'type' => 'string', 'max' => 10, 'min' => 1, 'pattern' => '^(0|([1-9]{1}\\d*))$', ], 'WarmTierRetentionPeriod' => [ 'type' => 'structure', 'members' => [ 'numberOfDays' => [ 'shape' => 'NumberOfDays', ], 'unlimited' => [ 'shape' => 'Unlimited', ], ], ], 'WarmTierState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], ],];
