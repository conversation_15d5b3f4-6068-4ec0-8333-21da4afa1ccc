/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.10.3 (2022-02-09)
 */
!function(){"use strict";var y=function(){return(y=Object.assign||function(n){for(var e,t=1,o=arguments.length;t<o;t++)for(var r in e=arguments[t])Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n}).apply(this,arguments)};function c(n,e){var t={};for(r in n)Object.prototype.hasOwnProperty.call(n,r)&&e.indexOf(r)<0&&(t[r]=n[r]);if(null!=n&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(n);o<r.length;o++)e.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(n,r[o])&&(t[r[o]]=n[r[o]]);return t}function u(n,e,t){if(t||2===arguments.length)for(var o,r=0,i=e.length;r<i;r++)!o&&r in e||((o=o||Array.prototype.slice.call(e,0,r))[r]=e[r]);return n.concat(o||Array.prototype.slice.call(e))}function n(o){return function(n){return t=typeof(e=n),(null===e?"null":"object"==t&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"==t&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":t)===o;var e,t}}function e(e){return function(n){return typeof n===e}}function t(e){return function(n){return e===n}}function l(n){return!(null==n)}function Y(){}function i(t,o){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t(o.apply(null,n))}}function w(n){return function(){return n}}function K(n){return n}function o(n,e){return n===e}var r=n("string"),a=n("object"),D=n("array"),s=t(null),d=e("boolean"),f=t(void 0),m=e("function"),g=e("number");function J(o){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var t=r.concat(n);return o.apply(null,t)}}function Q(e){return function(n){return!e(n)}}function h(n){return function(){throw new Error(n)}}function p(n){return n()}function v(){return b}var S=w(!1),x=w(!0),b={fold:function(n,e){return n()},isSome:S,isNone:x,getOr:K,getOrThunk:O,getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:w(null),getOrUndefined:w(void 0),or:K,orThunk:O,map:v,each:Y,bind:v,exists:S,forall:x,filter:function(){return b},toArray:function(){return[]},toString:w("none()")};function O(n){return n()}function k(t){var o,r=!1;return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return r||(r=!0,o=t.apply(null,n)),o}}function T(n,e){return-1<pn.call(n,e)}function C(n,e){for(var t=0,o=n.length;t<o;t++)if(e(n[t],t))return 1}function Z(n,e){for(var t=n.length,o=new Array(t),r=0;r<t;r++){var i=n[r];o[r]=e(i,r)}return o}function M(n,e){for(var t=0,o=n.length;t<o;t++)e(n[t],t)}function E(n,e){for(var t=[],o=0,r=n.length;o<r;o++){var i=n[o];e(i,o)&&t.push(i)}return t}function _(n,o,r){return function(n){for(var e,t=n.length-1;0<=t;t--)e=n[t],r=o(r,e,t)}(n),r}function R(n,t,o){return M(n,function(n,e){o=t(o,n,e)}),o}function F(n,e){return function(n,e,t){for(var o=0,r=n.length;o<r;o++){var i=n[o];if(e(i,o))return mn.some(i);if(t(i,o))break}return mn.none()}(n,e,S)}function I(n,e){for(var t=0,o=n.length;t<o;t++)if(e(n[t],t))return mn.some(t);return mn.none()}function V(n){for(var e=[],t=0,o=n.length;t<o;++t){if(!D(n[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+n);hn.apply(e,n[t])}return e}function B(n,e){return V(Z(n,e))}function A(n,e){for(var t=0,o=n.length;t<o;++t)if(!0!==e(n[t],t))return!1;return!0}function N(n){var e=gn.call(n,0);return e.reverse(),e}function H(n,e){return E(n,function(n){return!T(e,n)})}function P(n){return[n]}function z(n,e){for(var t=0;t<n.length;t++){var o=e(n[t],t);if(o.isSome())return o}return mn.none()}function j(n,e){var t=String(e).toLowerCase();return F(n,function(n){return n.search(t)})}function L(n,e){return-1!==n.indexOf(e)}function $(e){return function(n){return L(n,e)}}function G(n){return window.matchMedia(n).matches}function U(n){return w("alloy."+n)}function W(n,e){se(n,n.element,e,{})}function X(n,e,t){se(n,n.element,e,t)}function q(n){W(n,Zn())}function nn(n,e,t){se(n,e,t,{})}function en(n,e){var t=n.dom;if(1!==t.nodeType)return!1;var o=t;if(void 0!==o.matches)return o.matches(e);if(void 0!==o.msMatchesSelector)return o.msMatchesSelector(e);if(void 0!==o.webkitMatchesSelector)return o.webkitMatchesSelector(e);if(void 0!==o.mozMatchesSelector)return o.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")}function tn(n){return 1!==n.nodeType&&9!==n.nodeType&&11!==n.nodeType||0===n.childElementCount}function on(n,e){var t=void 0===e?document:e.dom;return tn(t)?[]:Z(t.querySelectorAll(n),fe.fromDom)}function rn(n,e){var t=void 0===e?document:e.dom;return tn(t)?mn.none():mn.from(t.querySelector(n)).map(fe.fromDom)}function un(n,e){return n.dom===e.dom}function cn(){return vn(0,0)}function an(n){function e(n){return function(){return t===n}}var t=n.current,o=n.version;return{current:t,version:o,isEdge:e("Edge"),isChrome:e("Chrome"),isIE:e("IE"),isOpera:e("Opera"),isFirefox:e(Sn),isSafari:e("Safari")}}function sn(n){function e(n){return function(){return t===n}}var t=n.current,o=n.version;return{current:t,version:o,isWindows:e(Tn),isiOS:e("iOS"),isAndroid:e(Cn),isOSX:e("OSX"),isLinux:e("Linux"),isSolaris:e(En),isFreeBSD:e(Dn),isChromeOS:e(Mn)}}function fn(n){if(null==n)throw new Error("Node cannot be null or undefined");return{dom:n}}var ln,dn=function(t){function n(){return r}function e(n){return n(t)}var o=w(t),r={fold:function(n,e){return e(t)},isSome:x,isNone:S,getOr:o,getOrThunk:o,getOrDie:o,getOrNull:o,getOrUndefined:o,or:n,orThunk:n,map:function(n){return dn(n(t))},each:function(n){n(t)},bind:e,exists:e,forall:e,filter:function(n){return n(t)?r:b},toArray:function(){return[t]},toString:function(){return"some("+t+")"}};return r},mn={some:dn,none:v,from:function(n){return null==n?b:dn(n)}},gn=Array.prototype.slice,pn=Array.prototype.indexOf,hn=Array.prototype.push,vn=function(n,e){return{major:n,minor:e}},bn={nu:vn,detect:function(n,e){var t,o,r=String(e).toLowerCase();return 0===n.length?cn():(o=function(n,e){for(var t=0;t<n.length;t++){var o=n[t];if(o.test(e))return o}}(n,t=r))?vn(i(1),i(2)):{major:0,minor:0};function i(n){return Number(t.replace(o,"$"+n))}},unknown:cn},yn=(ln=/^\s+|\s+$/g,function(n){return n.replace(ln,"")}),xn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,wn={browsers:w([{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return L(n,"edge/")&&L(n,"chrome")&&L(n,"safari")&&L(n,"applewebkit")}},{name:"Chrome",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,xn],search:function(n){return L(n,"chrome")&&!L(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return L(n,"msie")||L(n,"trident")}},{name:"Opera",versionRegexes:[xn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:$("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:$("firefox")},{name:"Safari",versionRegexes:[xn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(L(n,"safari")||L(n,"mobile/"))&&L(n,"applewebkit")}}]),oses:w([{name:"Windows",search:$("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return L(n,"iphone")||L(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:$("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:$("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:$("linux"),versionRegexes:[]},{name:"Solaris",search:$("sunos"),versionRegexes:[]},{name:"FreeBSD",search:$("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:$("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}])},Sn="Firefox",On=function(){return an({current:void 0,version:bn.unknown()})},kn=an,Tn=(w("Edge"),w("Chrome"),w("IE"),w("Opera"),w(Sn),w("Safari"),"Windows"),Cn="Android",En="Solaris",Dn="FreeBSD",Mn="ChromeOS",_n=function(){return sn({current:void 0,version:bn.unknown()})},Rn=sn,Fn=(w(Tn),w("iOS"),w(Cn),w("Linux"),w("OSX"),w(En),w(Dn),w(Mn),k(function(){return n=navigator.userAgent,e=mn.from(navigator.userAgentData),t=G,p=wn.browsers(),h=wn.oses(),v=e.bind(function(n){return o=p,z(n.brands,function(e){var t=e.brand.toLowerCase();return F(o,function(n){var e;return t===(null===(e=n.brand)||void 0===e?void 0:e.toLowerCase())}).map(function(n){return{current:n.name,version:bn.nu(parseInt(e.version,10),0)}})});var o}).orThunk(function(){return j(p,t=n).map(function(n){var e=bn.detect(n.versionRegexes,t);return{current:n.name,version:e}});var t}).fold(On,kn),b=j(h,o=n).map(function(n){var e=bn.detect(n.versionRegexes,o);return{current:n.name,version:e}}).fold(_n,Rn),{browser:v,os:b,deviceType:(i=v,u=n,c=t,a=(r=b).isiOS()&&!0===/ipad/i.test(u),s=r.isiOS()&&!a,l=(f=r.isiOS()||r.isAndroid())||c("(pointer:coarse)"),d=a||!s&&f&&c("(min-device-width:768px)"),m=s||f&&!d,g=i.isSafari()&&r.isiOS()&&!1===/safari/i.test(u),{isiPad:w(a),isiPhone:w(s),isTablet:w(d),isPhone:w(m),isTouch:w(l),isAndroid:r.isAndroid,isiOS:r.isiOS,isWebView:w(g),isDesktop:w(!m&&!d&&!g)})};var n,e,t,o,r,i,u,c,a,s,f,l,d,m,g,p,h,v,b})),In=w,Vn=In("touchstart"),Bn=In("touchmove"),An=In("touchend"),Nn=In("mousedown"),Hn=In("mousemove"),Pn=In("mouseup"),zn=In("mouseover"),jn=In("keydown"),Ln=In("keyup"),$n=In("input"),Gn=In("change"),Un=In("click"),Wn=In("transitionend"),Xn=In("selectstart"),qn={tap:U("tap")},Yn=U("focus"),Kn=U("blur.post"),Jn=U("paste.post"),Qn=U("receive"),Zn=U("execute"),ne=U("focus.item"),ee=qn.tap,te=U("longpress"),oe=U("system.init"),re=U("system.attached"),ie=U("system.detached"),ue=U("focusmanager.shifted"),ce=U("highlight"),ae=U("dehighlight"),se=function(n,e,t,o){var r=y({target:e},o);n.getSystem().triggerEvent(t,e,r)},fe={fromHtml:function(n,e){var t=(e||document).createElement("div");if(t.innerHTML=n,!t.hasChildNodes()||1<t.childNodes.length)throw console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return fn(t.childNodes[0])},fromTag:function(n,e){var t=(e||document).createElement(n);return fn(t)},fromText:function(n,e){var t=(e||document).createTextNode(n);return fn(t)},fromDom:fn,fromPoint:function(n,e,t){return mn.from(n.dom.elementFromPoint(e,t)).map(fn)}};function le(n){return n.dom.nodeName.toLowerCase()}function de(e){return function(n){return n.dom.nodeType===e}}function me(n){return fe.fromDom(n.dom.ownerDocument)}function ge(n){return Ye(n)?n:me(n)}function pe(n){return mn.from(n.dom.parentNode).map(fe.fromDom)}function he(n,e){var t=n.dom.childNodes;return mn.from(t[e]).map(fe.fromDom)}function ve(e,t){pe(e).each(function(n){n.dom.insertBefore(t.dom,e.dom)})}function be(n,e){mn.from(n.dom.nextSibling).map(fe.fromDom).fold(function(){pe(n).each(function(n){Qe(n,e)})},function(n){ve(n,e)})}function ye(e,t){he(e,0).fold(function(){Qe(e,t)},function(n){e.dom.insertBefore(t.dom,n.dom)})}function xe(e,n){M(n,function(n){Qe(e,n)})}function we(n){n.dom.textContent="",M(Je(n),function(n){Ze(n)})}function Se(n){return Ke(n)&&l(n.dom.host)}function Oe(n){return fe.fromDom(n.dom.host)}function ke(){return it(fe.fromDom(document))}function Te(n,e){Qe(n.element,e.element)}function Ce(e){var n,t=pe(e.element).bind(function(n){return e.getSystem().getByDom(n).toOptional()});ut(n=e),Ze(n.element),n.getSystem().removeFromWorld(n),t.each(function(n){n.syncComponents()})}function Ee(n,e){for(var t=st(n),o=0,r=t.length;o<r;o++){var i=t[o];e(n[i],i)}}function De(n,t){return lt(n,function(n,e){return{k:e,v:t(n,e)}})}function Me(n,t){var o=[];return Ee(n,function(n,e){o.push(t(n,e))}),o}function _e(n,e){return dt(n,e)?mn.from(n[e]):mn.none()}function Re(n,e){return dt(n,e)&&void 0!==n[e]&&null!==n[e]}function Fe(n,e,t){if(!(r(t)||d(t)||g(t)))throw console.error("Invalid call to Attribute.set. Key ",e,":: Value ",t,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(e,t+"")}function Ie(n,e,t){Fe(n.dom,e,t)}function Ve(n,e){var t=n.dom;Ee(e,function(n,e){Fe(t,e,n)})}function Be(n,e){var t=n.dom.getAttribute(e);return null===t?void 0:t}function Ae(n,e){return mn.from(Be(n,e))}function Ne(n,e){var t=n.dom;return!(!t||!t.hasAttribute)&&t.hasAttribute(e)}function He(n,e){n.dom.removeAttribute(e)}function Pe(n,e){var t=Be(n,e);return void 0===t||""===t?[]:t.split(" ")}function ze(n){return void 0!==n.dom.classList}function je(n,e){var t,o,r;ze(n)?n.dom.classList.add(e):(o=e,r=Pe(t=n,"class").concat([o]),Ie(t,"class",r.join(" ")))}function Le(n,e){var t,o,r,i;ze(n)?n.dom.classList.remove(e):(r=e,0<(i=E(Pe(o=n,"class"),function(n){return n!==r})).length?Ie(o,"class",i.join(" ")):He(o,"class")),0===(ze(t=n)?t.dom.classList:Pe(t,"class")).length&&He(t,"class")}function $e(n,e){return ze(n)&&n.dom.classList.contains(e)}function Ge(n,e,t){Le(n,t),je(n,e)}"undefined"!=typeof window||Function("return this;")();var Ue,We,Xe=de(1),qe=de(3),Ye=de(9),Ke=de(11),Je=function(n){return Z(n.dom.childNodes,fe.fromDom)},Qe=function(n,e){n.dom.appendChild(e.dom)},Ze=function(n){var e=n.dom;null!==e.parentNode&&e.parentNode.removeChild(e)},nt=m(Element.prototype.attachShadow)&&m(Node.prototype.getRootNode),et=w(nt),tt=nt?function(n){return fe.fromDom(n.dom.getRootNode())}:ge,ot=function(n){return l(n.dom.shadowRoot)},rt=function(n){var e=qe(n)?n.dom.parentNode:n.dom;if(null==e||null===e.ownerDocument)return!1;var t,o,r=e.ownerDocument,i=fe.fromDom(e),u=tt(i);return(Se(u)?mn.some(u):mn.none()).fold(function(){return r.body.contains(e)},(t=rt,o=Oe,function(n){return t(o(n))}))},it=function(n){var e=n.dom.body;if(null==e)throw new Error("Body is not available yet");return fe.fromDom(e)},ut=function(n){W(n,ie()),M(n.components(),ut)},ct=function(n){M(n.components(),ct),W(n,re())},at=function(n,e,t){n.getSystem().addToWorld(e),t(n.element,e.element),rt(n.element)&&ct(e),n.syncComponents()},st=Object.keys,ft=Object.hasOwnProperty,lt=function(n,o){var r={};return Ee(n,function(n,e){var t=o(n,e);r[t.k]=t.v}),r},dt=function(n,e){return ft.call(n,e)},mt=Object.freeze({__proto__:null,toAlpha:function(n,e,t){Ge(n.element,e.alpha,e.omega)},toOmega:function(n,e,t){Ge(n.element,e.omega,e.alpha)},isAlpha:function(n,e,t){return $e(n.element,e.alpha)},isOmega:function(n,e,t){return $e(n.element,e.omega)},clear:function(n,e,t){Le(n.element,e.alpha),Le(n.element,e.omega)}}),gt=function(t){return{isValue:x,isError:S,getOr:w(t),getOrThunk:w(t),getOrDie:w(t),or:function(n){return gt(t)},orThunk:function(n){return gt(t)},fold:function(n,e){return e(t)},map:function(n){return gt(n(t))},mapError:function(n){return gt(t)},each:function(n){n(t)},bind:function(n){return n(t)},exists:function(n){return n(t)},forall:function(n){return n(t)},toOptional:function(){return mn.some(t)}}},pt=function(t){return{isValue:S,isError:x,getOr:K,getOrThunk:function(n){return n()},getOrDie:function(){return h(String(t))()},or:K,orThunk:function(n){return n()},fold:function(n,e){return n(t)},map:function(n){return pt(t)},mapError:function(n){return pt(n(t))},each:Y,bind:function(n){return pt(t)},exists:S,forall:x,toOptional:mn.none}},ht={value:gt,error:pt,fromOption:function(n,e){return n.fold(function(){return pt(e)},gt)}};function vt(n,e,t){return n.stype===Ue.Error?e(n.serror):t(n.svalue)}function bt(n){return{stype:Ue.Value,svalue:n}}function yt(n){return{stype:Ue.Error,serror:n}}function xt(n,e,t,o){return{tag:"field",key:n,newKey:e,presence:t,prop:o}}function wt(n,e,t){switch(n.tag){case"field":return e(n.key,n.newKey,n.presence,n.prop);case"custom":return t(n.newKey,n.instantiator)}}function St(u){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];if(0===n.length)throw new Error("Can't merge zero objects");for(var t={},o=0;o<n.length;o++){var r,i=n[o];for(r in i)dt(i,r)&&(t[r]=u(t[r],i[r]))}return t}}function Ot(){return{tag:"required",process:{}}}function kt(n){return{tag:"defaultedThunk",process:n}}function Tt(n){return kt(w(n))}function Ct(){return{tag:"option",process:{}}}function Et(n){return{tag:"mergeWithThunk",process:n}}function Dt(n){return a(n)&&100<st(n).length?" removed due to size":JSON.stringify(n,null,2)}function Mt(n,e){return eo([{path:n,getErrorInfo:e}])}function _t(t){return{extract:function(e,n){return oo(t(n),function(n){return Mt(e,w(n))})},toString:w("val")}}function Rt(n,e,t,o){return o(_e(n,e).getOrThunk(function(){return t(n)}))}function Ft(e,t,o,r,i){function u(n){return i.extract(t.concat([r]),n)}function n(n){return n.fold(function(){return no(mn.none())},function(n){var e=i.extract(t.concat([r]),n);return ro(e,mn.some)})}var c,a,s,f,l,d;switch(e.tag){case"required":return s=t,d=u,_e(f=o,l=r).fold(function(){return n=l,e=f,Mt(s,function(){return'Could not find valid *required* value for "'+n+'" in '+Dt(e)});var n,e},d);case"defaultedThunk":return Rt(o,r,e.process,u);case"option":return n(_e(o,r));case"defaultedOptionThunk":return a=e.process,n(_e(c=o,r).map(function(n){return!0===n?a(c):n}));case"mergeWithThunk":return Rt(o,r,w({}),function(n){return u(co(e.process(o),n))})}}function It(n){var s=lo(n),f=_(n,function(t,n){return wt(n,function(n){var e;return co(t,((e={})[n]=!0,e))},w(t))},{});return{extract:function(n,e){var t,o,r,i,u,c,a=E(d(e)?[]:st((r=l,i=o={},u=function(n,e){i[e]=n},c=Y,Ee(e,function(n,e){(r(n,e)?u:c)(n,e)}),o)),function(n){return!Re(f,n)});return 0===a.length?s.extract(n,e):(t=a,Mt(n,function(){return"There are unsupported fields: ["+t.join(", ")+"] specified"}))},toString:s.toString}}function Vt(c,a){return{extract:function(t,o){var r,n,i,e=st(o),u=(i=_t(c),r=t,n=Z(e,function(n,e){return i.extract(r.concat(["["+e+"]"]),n)}),so(n));return to(u,function(n){var e=Z(n,function(n){return xt(n,n,Ot(),a)});return lo(e).extract(t,o)})},toString:function(){return"setOf("+a.toString()+")"}}}function Bt(e,c){return{extract:function(i,u){return _e(u,e).fold(function(){return n=e,Mt(i,function(){return'Choice schema did not contain choice key: "'+n+'"'});var n},function(n){return t=i,e=u,_e(o=c,r=n).fold(function(){return n=o,e=r,Mt(t,function(){return'The chosen schema: "'+e+'" did not exist in branches: '+Dt(n)});var n,e},function(n){return n.extract(t.concat(["branch: "+r]),e)});var t,e,o,r})},toString:function(){return"chooseOn("+e+"). Possible values: "+st(c)}}}function At(e){return _t(function(n){return e(n).fold(eo,no)})}function Nt(e,n){return Vt(function(n){return e(n).fold(yt,bt)},n)}function Ht(n,e,t){return Zt((r=e.extract([n],o=t),io(r,function(n){return{input:o,errors:n}})));var o,r}function Pt(n){return n.fold(function(n){throw new Error(po(n))},K)}function zt(n,e,t){return Pt(Ht(n,e,t))}function jt(n,e){return Bt(n,De(e,lo))}function Lt(n){return ho(n,n,Ot(),mo())}function $t(n,e){return ho(n,n,Ot(),e)}function Gt(n,e){return ho(n,n,Ot(),lo(e))}function Ut(n){return ho(n,n,Ct(),mo())}function Wt(n,e){return ho(n,n,Ct(),e)}function Xt(n,e){return Wt(n,lo(e))}function qt(n,e){return Wt(n,It(e))}function Yt(n,e){return ho(n,n,Tt(e),mo())}function Kt(n,e,t){return ho(n,n,Tt(e),t)}(We=Ue={})[We.Error=0]="Error",We[We.Value=1]="Value";function Jt(u){if(!D(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var c=[],t={};return M(u,function(n,o){var e=st(n);if(1!==e.length)throw new Error("one and only one name per case");var r=e[0],i=n[r];if(void 0!==t[r])throw new Error("duplicate key detected:"+r);if("cata"===r)throw new Error("cannot have a case named cata (sorry)");if(!D(i))throw new Error("case arguments must be an array");c.push(r),t[r]=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var e=t.length;if(e!==i.length)throw new Error("Wrong number of arguments to case "+r+". Expected "+i.length+" ("+i+"), got "+e);return{fold:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];if(n.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+n.length);return n[o].apply(null,t)},match:function(n){var e=st(n);if(c.length!==e.length)throw new Error("Wrong number of arguments to match. Expected: "+c.join(",")+"\nActual: "+e.join(","));if(!A(c,function(n){return T(e,n)}))throw new Error("Not all branches were specified when using match. Specified: "+e.join(", ")+"\nRequired: "+c.join(", "));return n[r].apply(null,t)},log:function(n){console.log(n,{constructors:c,constructor:r,params:t})}}}}),t}var Qt,Zt=function(n){return vt(n,ht.error,ht.value)},no=bt,eo=yt,to=function(n,e){return n.stype===Ue.Value?e(n.svalue):n},oo=function(n,e){return n.stype===Ue.Error?e(n.serror):n},ro=function(n,e){return n.stype===Ue.Value?{stype:Ue.Value,svalue:e(n.svalue)}:n},io=function(n,e){return n.stype===Ue.Error?{stype:Ue.Error,serror:e(n.serror)}:n},uo=vt,co=St(function(n,e){return a(n)&&a(e)?co(n,e):e}),ao=St(function(n,e){return e}),so=function(n){var e,t,o=(e=[],t=[],M(n,function(n){vt(n,function(n){return t.push(n)},function(n){return e.push(n)})}),{values:e,errors:t});return 0<o.errors.length?i(eo,V)(o.errors):no(o.values)},fo=_t(no),lo=function(t){return{extract:function(i,u){for(var c={},a=[],n=0,e=t;n<e.length;n++)wt(e[n],function(n,e,t,o){var r=Ft(t,i,u,n,o);uo(r,function(n){a.push.apply(a,n)},function(n){c[e]=n})},function(n,e){c[n]=e(u)});return 0<a.length?eo(a):no(c)},toString:function(){return"obj{\n"+Z(t,function(n){return wt(n,function(n,e,t,o){return n+" -> "+o.toString()},function(n,e){return"state("+n+")"})}).join("\n")+"}"}}},mo=w(fo),go=(Qt=m,_t(function(n){var e=typeof n;return Qt(n)?no(n):eo("Expected type: function but got: "+e)})),po=function(n){return"Errors: \n"+Z(10<(e=n.errors).length?e.slice(0,10).concat([{path:[],getErrorInfo:w("... (only showing first ten failures)")}]):e,function(n){return"Failed path: ("+n.path.join(" > ")+")\n"+n.getErrorInfo()}).join("\n")+"\n\nInput object: "+Dt(n.input);var e},ho=xt,vo=function(n,e){return{tag:"custom",newKey:n,instantiator:e}},bo=[Lt("alpha"),Lt("omega")];function yo(n,e){return(t={})[n]=e,t;var t}function xo(n){return e={},M(n,function(n){e[n.key]=n.value}),e;var e}function wo(n,e,t){return void 0===t&&(t=o),n.exists(function(n){return t(n,e)})}function So(n){for(var e=[],t=function(n){e.push(n)},o=0;o<n.length;o++)n[o].each(t);return e}function Oo(n){return m(n)?n:S}function ko(n,e){return un(n.element,e.event.target)}function To(n){if(!Re(n,"can")&&!Re(n,"abort")&&!Re(n,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(n,null,2)+" does not have can, abort, or run!");return y(y({},Xo),n)}function Co(n,e){return{key:n,value:To({abort:e})}}function Eo(n,e){return{key:n,value:To({run:e})}}function Do(n,t,o){return{key:n,value:To({run:function(n,e){t.apply(void 0,[n,e].concat(o))}})}}function Mo(n){return function(t){return{key:n,value:To({run:function(n,e){ko(n,e)&&t(n,e)}})}}}function _o(n,e,t){var i,o,r=e.partUids[t];return o=r,Eo(i=n,function(n,r){n.getSystem().getByUid(o).each(function(n){var e=n.element,t=i,o=r;n.getSystem().triggerEvent(t,e,o.event)})})}function Ro(n){return Eo(n,function(n,e){e.cut()})}function Fo(n,e){var t=n.toString(),o=t.indexOf(")")+1,r=t.indexOf("("),i=t.substring(r+1,o-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:e,parameters:Zo(i)}},n}function Io(n){return{classes:f(n.classes)?[]:n.classes,attributes:f(n.attributes)?{}:n.attributes,styles:f(n.styles)?{}:n.styles}}function Vo(t,o,r){return Jo(function(n,e){r(n,t,o)})}function Bo(n){return{key:n,value:void 0}}function Ao(n){var e=zt("Creating behaviour: "+n.name,rr,n),t=e.fields,o=e.name,r=e.active,i=e.apis,u=e.extra,c=e.state,a=It(t),s=Xt(o,[qt("config",t)]);return nr(a,s,o,r,i,u,c)}function No(n){var e=n;return{get:function(){return e},set:function(n){e=n}}}function Ho(n){return n.dom.focus()}function Po(n){return n.dom.blur()}function zo(n){return void 0===n&&(n=fe.fromDom(document)),mn.from(n.dom.activeElement).map(fe.fromDom)}function jo(e){return zo(tt(e)).filter(function(n){return e.dom.contains(n.dom)})}function Lo(n){return n.dom.innerHTML}function $o(n){if(Se(n))return"#shadow-root";var e=fe.fromDom(n.dom.cloneNode(!1)),t=fe.fromTag("div"),o=fe.fromDom(e.dom.cloneNode(!0));return Qe(t,o),Lo(t)}Jt([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);var Go,Uo,Wo,Xo={can:x,abort:S,run:Y},qo=xo,Yo=Mo(re()),Ko=Mo(ie()),Jo=Mo(oe()),Qo=(Go=Zn(),function(n){return Eo(Go,n)}),Zo=function(n){return Z(n,function(n){return 0,e=n.length-"/*".length,n.length>="/*".length&&"/*"===n.substr(e,e+"/*".length)?n.substring(0,n.length-"/*".length):n;var e})},nr=function(t,n,l,i,e,o,r){function u(n){return Re(n,l)?n[l]():mn.none()}var c=De(e,function(n,e){return r=l,t=function(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];var o=[t].concat(n);return t.config({name:w(r)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+r+". Using API: "+u)},function(n){var e=Array.prototype.slice.call(o,1);return i.apply(void 0,[t,n.config,n.state].concat(e))})},o=u=e,c=(i=n).toString(),a=c.indexOf(")")+1,s=c.indexOf("("),f=c.substring(s+1,a-1).split(/,\s*/),t.toFunctionAnnotation=function(){return{name:o,parameters:Zo(f.slice(0,1).concat(f.slice(3)))}},t;var r,i,u,t,o,c,a,s,f}),a=De(o,Fo),s=y(y(y({},a),c),{revoke:J(Bo,l),config:function(n){var e=zt(l+"-config",t,n);return{key:l,value:{config:e,me:s,configAsRaw:k(function(){return zt(l+"-config",t,n)}),initialConfig:n,state:r}}},schema:w(n),exhibit:function(n,t){return e=u(n),o=_e(i,"exhibit"),r=function(n,e){return e(t,n.config,n.state)},(e.isSome()&&o.isSome()?mn.some(r(e.getOrDie(),o.getOrDie())):mn.none()).getOrThunk(function(){return Io({})});var e,o,r},name:w(l),handlers:function(n){return u(n).map(function(n){return _e(i,"events").getOr(function(){return{}})(n.config,n.state)}).getOr({})}});return s},er={init:function(){return tr({readState:w("No State required")})}},tr=function(n){return n},or=xo,rr=It([Lt("fields"),Lt("name"),Yt("active",{}),Yt("apis",{}),Yt("state",er),Yt("extra",{})]),ir=It([Lt("branchKey"),Lt("branches"),Lt("name"),Yt("active",{}),Yt("apis",{}),Yt("state",er),Yt("extra",{})]),ur=w(void 0),cr=Ao({fields:bo,name:"swapping",apis:mt}),ar=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),sr=tinymce.util.Tools.resolve("tinymce.ThemeManager"),fr=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",icon:"bold",format:"bold"},{title:"Italic",icon:"italic",format:"italic"},{title:"Underline",icon:"underline",format:"underline"},{title:"Strikethrough",icon:"strikethrough",format:"strikethrough"},{title:"Superscript",icon:"superscript",format:"superscript"},{title:"Subscript",icon:"subscript",format:"subscript"},{title:"Code",icon:"code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Alignment",items:[{title:"Left",icon:"alignleft",format:"alignleft"},{title:"Center",icon:"aligncenter",format:"aligncenter"},{title:"Right",icon:"alignright",format:"alignright"},{title:"Justify",icon:"alignjustify",format:"alignjustify"}]}],lr=["undo","bold","italic","link","image","bullist","styleselect"],dr="formatChanged",mr="orientationChanged",gr="dropupDismissed",pr=Object.freeze({__proto__:null,events:function(o){return qo([Eo(Qn(),function(r,n){var e,i=o.channels,t=st(i),u=n;M((e=u).universal?t:E(t,function(n){return T(e.channels,n)}),function(n){var e=i[n],t=e.schema,o=zt("channel["+n+"] data\nReceiver: "+$o(r.element),t,u.data);e.onReceive(r,o)})})])}}),hr="unknown";function vr(e,n,t){var o,r,i,u;switch(_e(br.get(),e).orThunk(function(){return z(st(br.get()),function(n){return-1<e.indexOf(n)?mn.some(br.get()[n]):mn.none()})}).getOr(Uo.NORMAL)){case Uo.NORMAL:return t(xr());case Uo.LOGGING:var c=(o=e,r=n,i=[],u=(new Date).getTime(),{logEventCut:function(n,e,t){i.push({outcome:"cut",target:e,purpose:t})},logEventStopped:function(n,e,t){i.push({outcome:"stopped",target:e,purpose:t})},logNoParent:function(n,e,t){i.push({outcome:"no-parent",target:e,purpose:t})},logEventNoHandlers:function(n,e){i.push({outcome:"no-handlers-left",target:e})},logEventResponse:function(n,e,t){i.push({outcome:"response",purpose:t,target:e})},write:function(){var n=(new Date).getTime();T(["mousemove","mouseover","mouseout",oe()],o)||console.log(o,{event:o,time:n-u,target:r.dom,sequence:Z(i,function(n){return T(["cut","stopped","response"],n.outcome)?"{"+n.purpose+"} "+n.outcome+" at ("+$o(n.target)+")":n.outcome})})}}),a=t(c);return c.write(),a;case Uo.STOP:return!0}}(Wo=Uo={})[Wo.STOP=0]="STOP",Wo[Wo.NORMAL=1]="NORMAL",Wo[Wo.LOGGING=2]="LOGGING";var br=No({}),yr=["alloy/data/Fields","alloy/debugging/Debugging"],xr=w({logEventCut:Y,logEventStopped:Y,logNoParent:Y,logEventNoHandlers:Y,logEventResponse:Y,write:Y}),wr=w([Lt("menu"),Lt("selectedMenu")]),Sr=w([Lt("item"),Lt("selectedItem")]);function Or(n,e,t){return void 0!==(o=new Error).stack&&F(o.stack.split("\n"),function(e){return 0<e.indexOf("alloy")&&!C(yr,function(n){return-1<e.indexOf(n)})}).getOr(hr),ho(e,e,t,At(function(t){return ht.value(function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t.apply(void 0,n)})}));var o}function kr(n){return Or(0,n,Tt(Y))}function Tr(n){return Or(0,n,Tt(mn.none))}function Cr(n){return Or(0,n,Ot())}function Er(n){return Or(0,n,Ot())}function Dr(n,e){return vo(n,w(e))}function Mr(n){return vo(n,K)}function _r(n,e,t){var o=e.aria;o.update(n,o,t.get())}function Rr(e,n,t){n.toggleClass.each(function(n){(t.get()?je:Le)(e.element,n)})}function Fr(n,e,t){Qr(n,e,t,!t.get())}function Ir(n,e,t){t.set(!0),Rr(n,e,t),_r(n,e,t)}function Vr(n,e,t){t.set(!1),Rr(n,e,t),_r(n,e,t)}function Br(n,e,t){Qr(n,e,t,e.selected)}function Ar(t,o){return ti.config({channels:yo(dr,{onReceive:function(n,e){e.command===t&&o(n,e.state)}})})}function Nr(n){return ti.config({channels:yo(mr,{onReceive:n})})}function Hr(n,e){return{key:n,value:{onReceive:e}}}function Pr(){function n(n,e){e.stop(),q(n)}return[Eo(Un(),n),Eo(ee(),n),Ro(Vn()),Ro(Nn())]}function zr(n,e){e.ignore||(Ho(n.element),e.onFocus(n))}function jr(n){return void 0!==n.style&&m(n.style.getPropertyValue)}function Lr(n,e,t){if(!r(t))throw console.error("Invalid call to CSS.set. Property ",e,":: Value ",t,":: Element ",n),new Error("CSS value must be a string: "+t);jr(n)&&n.style.setProperty(e,t)}function $r(n,e,t){Lr(n.dom,e,t)}function Gr(n,e){var t=n.dom;Ee(e,function(n,e){Lr(t,e,n)})}function Ur(n,e){var t=n.dom,o=window.getComputedStyle(t).getPropertyValue(e);return""!==o||rt(n)?o:li(t,e)}function Wr(n,e){var t=n.dom,o=li(t,e);return mn.from(o).filter(function(n){return 0<n.length})}function Xr(n,e){var t,o=e;jr(t=n.dom)&&t.style.removeProperty(o),wo(Ae(n,"style").map(yn),"")&&He(n,"style")}function qr(n){return n.dom.offsetWidth}function Yr(o,r){function n(n){var e=r(n);if(e<=0||null===e){var t=Ur(n,o);return parseFloat(t)||0}return e}function i(r,n){return R(n,function(n,e){var t=Ur(r,e),o=void 0===t?0:parseInt(t,10);return isNaN(o)?n:n+o},0)}return{set:function(n,e){if(!g(e)&&!e.match(/^[0-9]+$/))throw new Error(o+".set accepts only positive integer values. Value was "+e);var t=n.dom;jr(t)&&(t.style[o]=e+"px")},get:n,getOuter:n,aggregate:i,max:function(n,e,t){var o=i(n,t);return o<e?e-o:0}}}function Kr(n){return di.get(n)}function Jr(n,e){return on(e,n)}w(lo(Sr().concat(wr())));function Qr(n,e,t,o){(o?Ir:Vr)(n,e,t)}function Zr(n,e,t){Ie(n.element,"aria-expanded",t)}var ni=w(lo(Sr())),ei=w(Gt("initSize",[Lt("numColumns"),Lt("numRows")])),ti=Ao({fields:[$t("channels",Nt(ht.value,It([Cr("onReceive"),Yt("schema",mo())])))],name:"receiving",active:pr}),oi=Object.freeze({__proto__:null,onLoad:Br,toggle:Fr,isOn:function(n,e,t){return t.get()},on:Ir,off:Vr,set:Qr}),ri=Object.freeze({__proto__:null,exhibit:function(){return Io({})},events:function(n,e){var t,o,r,i=(t=n,o=e,r=Fr,Qo(function(n){r(n,t,o)})),u=Vo(n,e,Br);return qo(V([n.toggleOnExecute?[i]:[],[u]]))}}),ii=Ao({fields:[Yt("selected",!1),Ut("toggleClass"),Yt("toggleOnExecute",!0),Kt("aria",{mode:"none"},jt("mode",{pressed:[Yt("syncWithExpanded",!1),Dr("update",function(n,e,t){Ie(n.element,"aria-pressed",t),e.syncWithExpanded&&Zr(n,0,t)})],checked:[Dr("update",function(n,e,t){Ie(n.element,"aria-checked",t)})],expanded:[Dr("update",Zr)],selected:[Dr("update",function(n,e,t){Ie(n.element,"aria-selected",t)})],none:[Dr("update",Y)]}))],name:"toggling",active:ri,apis:oi,state:{init:function(){var e=No(!1);return{get:function(){return e.get()},set:function(n){return e.set(n)},clear:function(){return e.set(!1)},readState:function(){return e.get()}}}}}),ui="tinymce-mobile",ci=function(n){return ui+"-"+n},ai=Object.freeze({__proto__:null,focus:zr,blur:function(n,e){e.ignore||Po(n.element)},isFocused:function(n){return e=n.element,t=tt(e).dom,e.dom===t.activeElement;var e,t}}),si=Object.freeze({__proto__:null,exhibit:function(n,e){return Io(e.ignore?{}:{attributes:{tabindex:"-1"}})},events:function(t){return qo([Eo(Yn(),function(n,e){zr(n,t),e.stop()})].concat(t.stopMousedown?[Eo(Nn(),function(n,e){e.event.prevent()})]:[]))}}),fi=Ao({fields:[kr("onFocus"),Yt("stopMousedown",!1),Yt("ignore",!1)],name:"focusing",active:si,apis:ai}),li=function(n,e){return jr(n)?n.style.getPropertyValue(e):""},di=Yr("height",function(n){var e=n.dom;return rt(n)?e.getBoundingClientRect().height:e.offsetHeight});function mi(n,e,t,o,r){return n(t,o)?mn.some(t):m(r)&&r(t)?mn.none():e(t,o,r)}function gi(n,e,t){for(var o=n.dom,r=m(t)?t:S;o.parentNode;){var o=o.parentNode,i=fe.fromDom(o);if(e(i))return mn.some(i);if(r(i))break}return mn.none()}function pi(n,e,t){return gi(n,function(n){return en(n,e)},t)}function hi(n,e){return rn(e,n)}function vi(n,e,t){return mi(en,pi,n,e,t)}function bi(n,e,t){var o=N(n.slice(0,e)),r=N(n.slice(e+1));return F(o.concat(r),t)}function yi(n,e,t){return F(N(n.slice(0,e)),t)}function xi(n,e,t){var o=n.slice(0,e);return F(n.slice(e+1).concat(o),t)}function wi(n,e,t){return F(n.slice(e+1),t)}function Si(t){return function(n){var e=n.raw;return T(t,e.which)}}function Oi(n){return function(e){return A(n,function(n){return n(e)})}}function ki(n){return!0===n.raw.shiftKey}function Ti(n){return!0===n.raw.ctrlKey}function Ci(n,e){return{matches:n,classification:e}}function Ei(n,e,t,o){var r=n+e;return o<r?t:r<t?o:r}function Di(n,e,t){return Math.min(Math.max(n,e),t)}function Mi(t,o,n,r){M(Jr(t.element,"."+o.highlightClass),function(e){C(r,function(n){return n.element===e})||(Le(e,o.highlightClass),t.getSystem().getByDom(e).each(function(n){o.onDehighlight(t,n),W(n,ae())}))})}function _i(n,e,t,o){Mi(n,e,0,[o]),Bi(0,e,0,o)||(je(o.element,e.highlightClass),e.onHighlight(n,o),W(o,ce()))}function Ri(t,e,n,o){var r=Jr(t.element,"."+e.itemClass);return I(r,function(n){return $e(n,e.highlightClass)}).bind(function(n){var e=Ei(n,o,0,r.length-1);return t.getSystem().getByDom(r[e]).toOptional()})}function Fi(n,e,t){e.exists(function(e){return t.exists(function(n){return un(n,e)})})||X(n,ue(),{prevFocus:e,newFocus:t})}function Ii(){function o(n){return jo(n.element)}return{get:o,set:function(n,e){var t=o(n);n.getSystem().triggerFocus(e,n.element),Fi(n,t,o(n))}}}function Vi(){function r(n){return Qi.getHighlighted(n).map(function(n){return n.element})}return{get:r,set:function(e,n){var t=r(e);e.getSystem().getByDom(n).fold(Y,function(n){Qi.highlight(e,n)});var o=r(e);Fi(e,t,o)}}}function Bi(n,e,t,o){return $e(o.element,e.highlightClass)}function Ai(e,n,t){return hi(e.element,"."+n.itemClass).bind(function(n){return e.getSystem().getByDom(n).toOptional()})}function Ni(e,n,t){var o=Jr(e.element,"."+n.itemClass);return(0<o.length?mn.some(o[o.length-1]):mn.none()).bind(function(n){return e.getSystem().getByDom(n).toOptional()})}function Hi(e,n,t){return So(Z(Jr(e.element,"."+n.itemClass),function(n){return e.getSystem().getByDom(n).toOptional()}))}var Pi,zi,ji=[8],Li=[9],$i=[13],Gi=[27],Ui=[32],Wi=[37],Xi=[38],qi=[39],Yi=[40],Ki=Q(ki),Ji=Object.freeze({__proto__:null,dehighlightAll:function(n,e,t){return Mi(n,e,0,[])},dehighlight:function(n,e,t,o){Bi(0,e,0,o)&&(Le(o.element,e.highlightClass),e.onDehighlight(n,o),W(o,ae()))},highlight:_i,highlightFirst:function(e,t,n){Ai(e,t).each(function(n){_i(e,t,0,n)})},highlightLast:function(e,t,n){Ni(e,t).each(function(n){_i(e,t,0,n)})},highlightAt:function(e,t,n,o){var r,i,u;i=o,u=Jr((r=e).element,"."+t.itemClass),mn.from(u[i]).fold(function(){return ht.error(new Error("No element found with index "+i))},r.getSystem().getByDom).fold(function(n){throw n},function(n){_i(e,t,0,n)})},highlightBy:function(e,t,n,o){F(Hi(e,t),o).each(function(n){_i(e,t,0,n)})},isHighlighted:Bi,getHighlighted:function(e,n,t){return hi(e.element,"."+n.highlightClass).bind(function(n){return e.getSystem().getByDom(n).toOptional()})},getFirst:Ai,getLast:Ni,getPrevious:function(n,e,t){return Ri(n,e,0,-1)},getNext:function(n,e,t){return Ri(n,e,0,1)},getCandidates:Hi}),Qi=Ao({fields:[Lt("highlightClass"),Lt("itemClass"),kr("onHighlight"),kr("onDehighlight")],name:"highlighting",apis:Ji});function Zi(n,e,t,o,c){function a(e,t,n,o,r){var i=n(e,t,o,r),u=t.event;return F(i,function(n){return n.matches(u)}).map(function(n){return n.classification}).bind(function(n){return n(e,t,o,r)})}var r={schema:function(){return n.concat([Yt("focusManager",Ii()),Kt("focusInside","onFocus",At(function(n){return T(["onFocus","onEnterOrSpace","onApi"],n)?ht.value(n):ht.error("Invalid value for focusInside")})),Dr("handler",r),Dr("state",e),Dr("sendFocusIn",c)])},processKey:a,toEvents:function(i,u){var n=i.focusInside!==Pi.OnFocusMode?mn.none():c(i).map(function(t){return Eo(Yn(),function(n,e){t(n,i,u),e.stop()})}),e=[Eo(jn(),function(o,r){a(o,r,t,i,u).fold(function(){var e=o,t=r,n=Si(Ui.concat($i))(t.event);i.focusInside===Pi.OnEnterOrSpaceMode&&n&&ko(e,t)&&c(i).each(function(n){n(e,i,u),t.stop()})},function(n){r.stop()})}),Eo(Ln(),function(n,e){a(n,e,o,i,u).each(function(n){e.stop()})})];return qo(n.toArray().concat(e))}};return r}function nu(n){function c(n,e){return 0<Kr(n.visibilitySelector.bind(function(n){return vi(e,n)}).getOr(e))}function e(e,t,n){var o=t,r=E(Jr(e.element,o.selector),function(n){return c(o,n)});mn.from(r[o.firstTabstop]).each(function(n){t.focusManager.set(e,n)})}function o(t,n,r,i){var e,u=Jr(t.element,r.selector);return(e=r).focusManager.get(t).bind(function(n){return vi(n,e.selector)}).bind(function(n){return I(u,J(un,n)).bind(function(n){return e=t,o=r,i(u,n,function(n){return c(e=o,t=n)&&e.useTabstopAt(t);var e,t}).fold(function(){return o.cyclic?mn.some(!0):mn.none()},function(n){return o.focusManager.set(e,n),mn.some(!0)});var e,o})})}var t=[Ut("onEscape"),Ut("onEnter"),Yt("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),Yt("firstTabstop",0),Yt("useTabstopAt",x),Ut("visibilitySelector")].concat([n]),r=w([Ci(Oi([ki,Si(Li)]),function(n,e,t){return o(n,0,t,t.cyclic?bi:yi)}),Ci(Si(Li),function(n,e,t){return o(n,0,t,t.cyclic?xi:wi)}),Ci(Si(Gi),function(e,t,n){return n.onEscape.bind(function(n){return n(e,t)})}),Ci(Oi([Ki,Si($i)]),function(e,t,n){return n.onEnter.bind(function(n){return n(e,t)})})]),i=w([]);return Zi(t,er.init,r,i,function(){return mn.some(e)})}function eu(n){return"input"===le(n)&&"radio"!==Be(n,"type")||"textarea"===le(n)}function tu(n,e,t){return eu(t)&&Si(Ui)(e.event)?mn.none():(nn(n,t,Zn()),mn.some(!0))}function ou(n,e){return mn.some(!0)}function ru(n,e,t){return t.execute(n,e,n.element)}function iu(n){function e(){return t.get().each(n)}var t=No(mn.none());return{clear:function(){e(),t.set(mn.none())},isSet:function(){return t.get().isSome()},get:function(){return t.get()},set:function(n){e(),t.set(mn.some(n))}}}function uu(){var e=iu(function(n){return n.destroy()});return y(y({},e),{run:function(n){return e.get().each(n)}})}function cu(){var e=iu(Y);return y(y({},e),{on:function(n){return e.get().each(n)}})}function au(){var t=cu();return tr({readState:function(){return t.get().map(function(n){return{numRows:String(n.numRows),numColumns:String(n.numColumns)}}).getOr({numRows:"?",numColumns:"?"})},setGridSize:function(n,e){t.set({numRows:n,numColumns:e})},getNumRows:function(){return t.get().map(function(n){return n.numRows})},getNumColumns:function(){return t.get().map(function(n){return n.numColumns})}})}function su(e,t){return function(n){return"rtl"===cc(n)?t:e}}function fu(i){return function(n,e,t,o){var r=i(n.element);return ac(r,n,e,t,o)}}function lu(n,e){return fu(su(n,e))}function du(n,e){return fu(su(e,n))}function mu(r){return function(n,e,t,o){return ac(r,n,e,t,o)}}function gu(n){return!((e=n.dom).offsetWidth<=0&&e.offsetHeight<=0);var e}function pu(n,e,t){var o,r=E(Jr(n,t),gu);return I(o=r,function(n){return un(n,e)}).map(function(n){return{index:n,candidates:o}})}function hu(n,e){return I(n,function(n){return un(e,n)})}function vu(t,n,o,e){return e(Math.floor(n/o),n%o).bind(function(n){var e=n.row*o+n.column;return 0<=e&&e<t.length?mn.some(t[e]):mn.none()})}function bu(r,n,i,u,c){return vu(r,n,u,function(n,e){var t=n===i-1?r.length-n*u:u,o=Ei(e,c,0,t-1);return mn.some({row:n,column:o})})}function yu(r,n,i,u,c){return vu(r,n,u,function(n,e){var t=Ei(n,c,0,i-1),o=Di(e,0,(t===i-1?r.length-t*u:u)-1);return mn.some({row:t,column:o})})}function xu(e,t,n){hi(e.element,t.selector).each(function(n){t.focusManager.set(e,n)})}function wu(r){return function(n,e,t,o){return pu(n,e,t.selector).bind(function(n){return r(n.candidates,n.index,o.getNumRows().getOr(t.initSize.numRows),o.getNumColumns().getOr(t.initSize.numColumns))})}}function Su(n,e,t){return t.captureTab?mn.some(!0):mn.none()}function Ou(n,e,t,i){var u=function(n,e,t){var o,r=Ei(e,i,0,t.length-1);return r===n?mn.none():"button"===le(o=t[r])&&"disabled"===Be(o,"disabled")?u(n,r,t):mn.from(t[r])};return pu(n,t,e).bind(function(n){var e=n.index,t=n.candidates;return u(e,e,t)})}function ku(e,t,o){return(r=o).focusManager.get(e).bind(function(n){return vi(n,r.selector)}).bind(function(n){return o.execute(e,t,n)});var r}function Tu(e,t,n){t.getInitial(e).orThunk(function(){return hi(e.element,t.selector)}).each(function(n){t.focusManager.set(e,n)})}function Cu(n,e,t){return Ou(n,t.selector,e,-1)}function Eu(n,e,t){return Ou(n,t.selector,e,1)}function Du(r){return function(n,e,t,o){return r(n,e,t,o).bind(function(){return t.executeOnMove?ku(n,e,t):mn.some(!0)})}}function Mu(n,e,t){return t.onEscape(n,e)}function _u(n,e,t){return mn.from(n[e]).bind(function(n){return mn.from(n[t]).map(function(n){return{rowIndex:e,columnIndex:t,cell:n}})})}function Ru(n,e,t,o){return _u(n,e,Ei(t,o,0,n[e].length-1))}function Fu(n,e,t,o){var r=Ei(t,o,0,n.length-1);return _u(n,r,Di(e,0,n[r].length-1))}function Iu(n,e,t,o){return _u(n,e,Di(t+o,0,n[e].length-1))}function Vu(n,e,t,o){var r=Di(t+o,0,n.length-1);return _u(n,r,Di(e,0,n[r].length-1))}function Bu(e,t,n){t.previousSelector(e).orThunk(function(){var n=t.selectors;return hi(e.element,n.cell)}).each(function(n){t.focusManager.set(e,n)})}function Au(n,o){return function(e,t,i){var u=i.cycles?n:o;return vi(t,i.selectors.row).bind(function(n){return hu(Jr(n,i.selectors.cell),t).bind(function(o){var r=Jr(e,i.selectors.row);return hu(r,n).bind(function(n){var e,t=(e=i,Z(r,function(n){return Jr(n,e.selectors.cell)}));return u(t,n,o).map(function(n){return n.cell})})})})}}function Nu(e,t,o){return o.focusManager.get(e).bind(function(n){return o.execute(e,t,n)})}function Hu(e,t,n){hi(e.element,t.selector).each(function(n){t.focusManager.set(e,n)})}function Pu(n,e,t){return Ou(n,t.selector,e,-1)}function zu(n,e,t){return Ou(n,t.selector,e,1)}function ju(o,n){return e=o,t={},r=Z(n,function(n){return e=n.name(),t="Cannot configure "+n.name()+" for "+o,ho(e,e,Ct(),_t(function(n){return eo("The field: "+e+" is forbidden. "+t)}));var e,t}).concat([vo("dump",K)]),Kt(e,t,lo(r));var e,t,r}function Lu(n,e){return y(y({},or(e)),n.dump)}function $u(n){return dt(n,"uiType")}function Gu(n){var e=(new Date).getTime();return n+"_"+Math.floor(1e9*Math.random())+ ++Kc+String(e)}function Uu(n){function e(n){return n.name}return n.fold(e,e,e,e)}function Wu(t,o){return function(n){var e=zt("Converting part type",o,n);return t(e)}}(zi=Pi=Pi||{}).OnFocusMode="onFocus",zi.OnEnterOrSpaceMode="onEnterOrSpace",zi.OnApiMode="onApi";var Xu,qu,Yu,Ku,Ju,Qu,Zu,nc,ec,tc,oc=nu(vo("cyclic",S)),rc=nu(vo("cyclic",x)),ic=Zi([Yt("execute",tu),Yt("useSpace",!1),Yt("useEnter",!0),Yt("useControlEnter",!1),Yt("useDown",!1)],er.init,function(n,e,t,o){var r=t.useSpace&&!eu(n.element)?Ui:[],i=t.useEnter?$i:[],u=t.useDown?Yi:[];return[Ci(Si(r.concat(i).concat(u)),ru)].concat(t.useControlEnter?[Ci(Oi([Ti,Si($i)]),ru)]:[])},function(n,e,t,o){return t.useSpace&&!eu(n.element)?[Ci(Si(Ui),ou)]:[]},function(){return mn.none()}),uc=Object.freeze({__proto__:null,flatgrid:au,init:function(n){return n.state(n)}}),cc=function(n){return"rtl"===Ur(n,"direction")?"rtl":"ltr"},ac=function(e,t,n,o,r){return o.focusManager.get(t).bind(function(n){return e(t.element,n,o,r)}).map(function(n){return o.focusManager.set(t,n),!0})},sc=mu,fc=mu,lc=mu,dc=wu(function(n,e,t,o){return bu(n,e,t,o,-1)}),mc=wu(function(n,e,t,o){return bu(n,e,t,o,1)}),gc=wu(function(n,e,t,o){return yu(n,e,t,o,-1)}),pc=wu(function(n,e,t,o){return yu(n,e,t,o,1)}),hc=Zi([Lt("selector"),Yt("execute",tu),Tr("onEscape"),Yt("captureTab",!1),ei()],au,w([Ci(Si(Wi),lu(dc,mc)),Ci(Si(qi),du(dc,mc)),Ci(Si(Xi),sc(gc)),Ci(Si(Yi),fc(pc)),Ci(Oi([ki,Si(Li)]),Su),Ci(Oi([Ki,Si(Li)]),Su),Ci(Si(Gi),function(n,e,t){return t.onEscape(n,e)}),Ci(Si(Ui.concat($i)),function(e,t,o,n){return(r=o).focusManager.get(e).bind(function(n){return vi(n,r.selector)}).bind(function(n){return o.execute(e,t,n)});var r})]),w([Ci(Si(Ui),ou)]),function(){return mn.some(xu)}),vc=[Lt("selector"),Yt("getInitial",mn.none),Yt("execute",tu),Tr("onEscape"),Yt("executeOnMove",!1),Yt("allowVertical",!0)],bc=w([Ci(Si(Ui),ou)]),yc=Zi(vc,er.init,function(n,e,t,o){var r=Wi.concat(t.allowVertical?Xi:[]),i=qi.concat(t.allowVertical?Yi:[]);return[Ci(Si(r),Du(lu(Cu,Eu))),Ci(Si(i),Du(du(Cu,Eu))),Ci(Si($i),ku),Ci(Si(Ui),ku),Ci(Si(Gi),Mu)]},bc,function(){return mn.some(Tu)}),xc=[Gt("selectors",[Lt("row"),Lt("cell")]),Yt("cycles",!0),Yt("previousSelector",mn.none),Yt("execute",tu)],wc=Au(function(n,e,t){return Ru(n,e,t,-1)},function(n,e,t){return Iu(n,e,t,-1)}),Sc=Au(function(n,e,t){return Ru(n,e,t,1)},function(n,e,t){return Iu(n,e,t,1)}),Oc=Au(function(n,e,t){return Fu(n,t,e,-1)},function(n,e,t){return Vu(n,t,e,-1)}),kc=Au(function(n,e,t){return Fu(n,t,e,1)},function(n,e,t){return Vu(n,t,e,1)}),Tc=w([Ci(Si(Wi),lu(wc,Sc)),Ci(Si(qi),du(wc,Sc)),Ci(Si(Xi),sc(Oc)),Ci(Si(Yi),fc(kc)),Ci(Si(Ui.concat($i)),function(e,t,o){return jo(e.element).bind(function(n){return o.execute(e,t,n)})})]),Cc=w([Ci(Si(Ui),ou)]),Ec=Zi(xc,er.init,Tc,Cc,function(){return mn.some(Bu)}),Dc=[Lt("selector"),Yt("execute",tu),Yt("moveOnTab",!1)],Mc=w([Ci(Si(Xi),lc(Pu)),Ci(Si(Yi),lc(zu)),Ci(Oi([ki,Si(Li)]),function(n,e,t,o){return t.moveOnTab?lc(Pu)(n,e,t,o):mn.none()}),Ci(Oi([Ki,Si(Li)]),function(n,e,t,o){return t.moveOnTab?lc(zu)(n,e,t,o):mn.none()}),Ci(Si($i),Nu),Ci(Si(Ui),Nu)]),_c=w([Ci(Si(Ui),ou)]),Rc=Zi(Dc,er.init,Mc,_c,function(){return mn.some(Hu)}),Fc=Zi([Tr("onSpace"),Tr("onEnter"),Tr("onShiftEnter"),Tr("onLeft"),Tr("onRight"),Tr("onTab"),Tr("onShiftTab"),Tr("onUp"),Tr("onDown"),Tr("onEscape"),Yt("stopSpaceKeyup",!1),Ut("focusIn")],er.init,function(n,e,t){return[Ci(Si(Ui),t.onSpace),Ci(Oi([Ki,Si($i)]),t.onEnter),Ci(Oi([ki,Si($i)]),t.onShiftEnter),Ci(Oi([ki,Si(Li)]),t.onShiftTab),Ci(Oi([Ki,Si(Li)]),t.onTab),Ci(Si(Xi),t.onUp),Ci(Si(Yi),t.onDown),Ci(Si(Wi),t.onLeft),Ci(Si(qi),t.onRight),Ci(Si(Ui),t.onSpace),Ci(Si(Gi),t.onEscape)]},function(n,e,t){return t.stopSpaceKeyup?[Ci(Si(Ui),ou)]:[]},function(n){return n.focusIn}),Ic=oc.schema(),Vc=rc.schema(),Bc=yc.schema(),Ac=hc.schema(),Nc=Ec.schema(),Hc=ic.schema(),Pc=Rc.schema(),zc=Fc.schema(),jc=(qu=jt((tc=zt("Creating behaviour: "+(Xu={branchKey:"mode",branches:Object.freeze({__proto__:null,acyclic:Ic,cyclic:Vc,flow:Bc,flatgrid:Ac,matrix:Nc,execution:Hc,menu:Pc,special:zc}),name:"keying",active:{events:function(n,e){return n.handler.toEvents(n,e)}},apis:{focusIn:function(e,t,o){t.sendFocusIn(t).fold(function(){e.getSystem().triggerFocus(e.element,e.element)},function(n){n(e,t,o)})},setGridSize:function(n,e,t,o,r){Re(t,"setGridSize")?t.setGridSize(o,r):console.error("Layout does not support setGridSize")}},state:uc}).name,ir,Xu)).branchKey,tc.branches),Yu=tc.name,Ku=tc.active,Ju=tc.apis,Qu=tc.extra,Zu=tc.state,ec=Xt(Yu,[Wt("config",nc=qu)]),nr(nc,ec,Yu,Ku,Ju,Qu,Zu)),Lc=ju,$c=Lu,Gc="placeholder",Uc=Jt([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),Wc=function(r,i,u,c){return n=r,t=c,($u(e=u)&&e.uiType===Gc?(a=e,s=t,(o=n).exists(function(n){return n!==a.owner})?Uc.single(!0,w(a)):_e(s,a.name).fold(function(){throw new Error("Unknown placeholder component: "+a.name+"\nKnown: ["+st(s)+"]\nNamespace: "+o.getOr("none")+"\nSpec: "+JSON.stringify(a,null,2))},function(n){return n.replace()})):Uc.single(!1,w(e))).fold(function(n,e){var t=$u(u)?e(i,u.config,u.validated):e(i),o=B(_e(t,"components").getOr([]),function(n){return Wc(r,i,n,c)});return[y(y({},t),{components:o})]},function(n,e){if($u(u)){var t=e(i,u.config,u.validated);return u.validated.preprocess.getOr(K)(t)}return e(i)});var n,e,t,o,a,s},Xc=Uc.single,qc=Uc.multiple,Yc=w(Gc),Kc=0,Jc=Jt([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),Qc=Yt("factory",{sketch:K}),Zc=Yt("schema",[]),na=Lt("name"),ea=ho("pname","pname",kt(function(n){return"<alloy."+Gu(n.name)+">"}),mo()),ta=vo("schema",function(){return[Ut("preprocess")]}),oa=Yt("defaults",w({})),ra=Yt("overrides",w({})),ia=lo([Qc,Zc,na,ea,oa,ra]),ua=lo([Qc,Zc,na,oa,ra]),ca=lo([Qc,Zc,na,ea,oa,ra]),aa=lo([Qc,ta,na,Lt("unit"),ea,oa,ra]),sa=Wu(Jc.required,ia);function fa(n,e,t,o){return co(e.defaults(n,t,o),t,{uid:n.partUids[e.name]},e.overrides(n,t,o))}function la(n,e,t){return o=e,r={},i={},M(t,function(n){n.fold(function(o){r[o.pname]=Xc(!0,function(n,e,t){return o.factory.sketch(fa(n,o,e,t))})},function(n){var e=o.parts[n.name];i[n.name]=w(n.factory.sketch(fa(o,n,e[zs()]),e))},function(o){r[o.pname]=Xc(!1,function(n,e,t){return o.factory.sketch(fa(n,o,e,t))})},function(o){r[o.pname]=qc(!0,function(e,n,t){return Z(e[o.name],function(n){return o.factory.sketch(co(o.defaults(e,n,t),n,o.overrides(e,n)))})})})}),{internals:w(r),externals:w(i)};var o,r,i}function da(n,e,t){return o=mn.some(n),i=(r=e).components,s=De(t,function(n,e){return o=n,r=!1,{name:w(t=e),required:function(){return o.fold(function(n,e){return n},function(n,e){return n})},used:function(){return r},replace:function(){if(r)throw new Error("Trying to use the same placeholder more than once: "+t);return r=!0,o}};var t,o,r}),u=o,c=r,a=s,f=B(i,function(n){return Wc(u,c,n,a)}),Ee(s,function(n){if(!1===n.used()&&n.required())throw new Error("Placeholder: "+n.name()+" was not found in components list\nNamespace: "+o.getOr("none")+"\nComponents: "+JSON.stringify(r.components,null,2))}),f;var o,r,i,u,c,a,s,f}function ma(n,e,t){var o=e.partUids[t];return n.getSystem().getByUid(o).toOptional()}function ga(n,e,t){return ma(n,e,t).getOrDie("Could not find part: "+t)}function pa(o){return ho("partUids","partUids",Et(function(n){return e=n.uid,t=Z(o,Uu),xo(Z(t,function(n){return{key:n,value:e+"-"+n}}));var e,t}),mo())}function ha(n){return yo(Ls,n)}function va(o){return n=function(n){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];return o.apply(void 0,u([n.getApis(),n],e,!1))},t=(e=o.toString()).indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,t-1).split(/,\s*/),n.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:Zo(i.slice(1))}},n;var n,e,t,r,i}function ba(n){var e=Xe(n)?n.dom[Ws]:null;return mn.from(e)}function ya(n,e,t,o,r){var i;return zt(n+" [SpecSchema]",It((i=r,(0<o.length?[Gt("parts",o)]:[]).concat([Lt("uid"),Yt("dom",{}),Yt("components",[]),Mr("originalSpec"),Yt("debug.sketcher",{})]).concat(i)).concat(e)),t)}function xa(n,e,t,o,r){var i=Ys(r),u=ya(n,e,i,B(t,function(n){return n.fold(mn.none,mn.some,mn.none,mn.none).map(function(n){return Gt(n.name,n.schema.concat([Mr(zs())]))}).toArray()}),[pa(t)]),c=la(0,u,t);return o(u,da(n,u,c.internals()),i,c.externals())}function wa(n){var r=zt("Sketcher for "+n.name,Ks,n),e=De(r.apis,va),t=De(r.extraApis,Fo);return y(y({name:r.name,configFields:r.configFields,sketch:function(n){return e=r.name,t=r.configFields,(0,r.factory)(ya(e,t,o=Ys(n),[],[]),o);var e,t,o}},e),t)}function Sa(n){var r,e,i,t=zt("Sketcher for "+n.name,Js,n),o=(r=t.name,e=t.partFields,i={},M(e,function(n){n.fold(mn.some,mn.none,mn.some,mn.some).each(function(t){var o=js(r,t.pname);i[t.name]=function(n){var e=zt("Part: "+t.name+" in "+r,lo(t.schema),n);return y(y({},o),{config:n,validated:e})}})}),i),u=De(t.apis,va),c=De(t.extraApis,Fo);return y(y({name:t.name,partFields:t.partFields,configFields:t.configFields,sketch:function(n){return xa(t.name,t.configFields,t.partFields,t.factory,n)},parts:o},u),c)}function Oa(n){var r,e,t,o,i,u,c=(r={prefix:ui},n.replace(/\$\{([^{}]*)\}/g,function(n,e){var t,o=r[e];return"string"==(t=typeof o)||"number"==t?o.toString():n}));return e=fe.fromHtml(c),t=Je(e),o=R(void 0!==e.dom.attributes?e.dom.attributes:[],function(n,e){var t;return"class"===e.name?n:y(y({},n),((t={})[e.name]=e.value,t))},{}),i=Array.prototype.slice.call(e.dom.classList,0),u=0===t.length?{}:{innerHtml:Lo(e)},y({tag:le(e),classes:i,attributes:o},u)}function ka(n){return{dom:Oa(n)}}function Ta(n){return or([ii.config({toggleClass:ci("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),Ar(n,function(n,e){(e?ii.on:ii.off)(n)})])}function Ca(n,e,t,o){var r=Ta(t);return nf(e,o,r,n)}function Ea(n,e){var t=e.ui.registry.getAll().icons;return mn.from(t[n]).fold(function(){return Oa('<span class="${prefix}-toolbar-button ${prefix}-toolbar-group-item ${prefix}-icon-'+n+' ${prefix}-icon"></span>')},function(n){return Oa('<span class="${prefix}-toolbar-button ${prefix}-toolbar-group-item">'+n+"</span>")})}function Da(e){return Hs({name:e+"-edge",overrides:function(n){return n.model.manager.edgeActions[e].fold(function(){return{}},function(o){return{events:qo([Do(Vn(),function(n,e,t){return o(n,t)},[n]),Do(Nn(),function(n,e,t){return o(n,t)},[n]),Do(Hn(),function(n,e,t){t.mouseIsDown.get()&&o(n,t)},[n])])}})}})}function Ma(n,e,t){e.store.manager.onLoad(n,e,t)}function _a(n,e,t){e.store.manager.onUnload(n,e,t)}function Ra(){var n=No(null);return tr({set:n.set,get:n.get,isNotSet:function(){return null===n.get()},clear:function(){n.set(null)},readState:function(){return{mode:"memory",value:n.get()}}})}function Fa(){var i=No({}),u=No({});return tr({readState:function(){return{mode:"dataset",dataByValue:i.get(),dataByText:u.get()}},lookup:function(n){return _e(i.get(),n).orThunk(function(){return _e(u.get(),n)})},update:function(n){var e=i.get(),t=u.get(),o={},r={};M(n,function(e){_e(o[e.value]=e,"meta").each(function(n){_e(n,"text").each(function(n){r[n]=e})})}),i.set(y(y({},e),o)),u.set(y(y({},t),r))},clear:function(){i.set({}),u.set({})}})}function Ia(n,e,t,o){var r=e.store;t.update([o]),r.setValue(n,o),e.onSetValue(n,o)}function Va(n,e){return vf.set(n,e)}function Ba(n){return vf.get(n)}function Aa(n){var e=n.event.raw;return-1===e.type.indexOf("touch")?void 0!==e.clientX?mn.some(e).map(function(n){return yf(n.clientX,n.clientY)}):mn.none():void 0!==e.touches&&1===e.touches.length?mn.some(e.touches[0]).map(function(n){return yf(n.clientX,n.clientY)}):mn.none()}function Na(n){return n.model.minX}function Ha(n){return n.model.minY}function Pa(n){return n.model.minX-1}function za(n){return n.model.minY-1}function ja(n){return n.model.maxX}function La(n){return n.model.maxY}function $a(n){return n.model.maxX+1}function Ga(n){return n.model.maxY+1}function Ua(n,e,t){return e(n)-t(n)}function Wa(n){return Ua(n,ja,Na)}function Xa(n){return Ua(n,La,Ha)}function qa(n){return Wa(n)/2}function Ya(n){return Xa(n)/2}function Ka(n){return n.stepSize}function Ja(n){return n.snapToGrid}function Qa(n){return n.snapStart}function Za(n){return n.rounded}function ns(n,e){return void 0!==n[e+"-edge"]}function es(n){return ns(n,"left")}function ts(n){return ns(n,"right")}function os(n){return ns(n,"top")}function rs(n){return ns(n,"bottom")}function is(n){return n.model.value.get()}function us(n,e){return{x:n,y:e}}function cs(n,e){X(n,xf(),{value:e})}function as(n,e,t,o){return n<e?n:t<n?t:n===e?e-1:Math.max(e,n-o)}function ss(n,e,t,o){return t<n?n:n<e?e:n===t?t+1:Math.min(t,n+o)}function fs(n,e,t){return Math.max(e,Math.min(t,n))}function ls(n){var e=n.min,t=n.max,o=n.range,r=n.value,i=n.step,u=n.snap,c=n.snapStart,a=n.rounded,s=n.hasMinEdge,f=n.hasMaxEdge,l=n.minBound,d=n.maxBound,m=n.screenRange,g=s?e-1:e,p=f?t+1:t;if(r<l)return g;if(d<r)return p;var h,v,b,y,x,w=fs((x=l,Math.min(d,Math.max(r,x))-x)/m*o+e,g,p);return u&&e<=w&&w<=t?(h=w,v=e,b=t,y=i,c.fold(function(){var n=Math.round((h-v)/y)*y;return fs(v+n,v-1,b+1)},function(n){var e=Math.round((h-n)%y/y),t=Math.floor((h-n)/y),o=Math.floor((b-n)/y),r=Math.min(o,t+e);return Math.max(n,n+r*y)})):a?Math.round(w):w}function ds(n){var e=n.min,t=n.max,o=n.range,r=n.value,i=n.hasMinEdge,u=n.hasMaxEdge,c=n.maxBound,a=n.maxOffset,s=n.centerMinEdge,f=n.centerMaxEdge;return r<e?i?0:s:t<r?u?c:f:(r-e)/o*a}function ms(n){return n.element.dom.getBoundingClientRect()}function gs(n){return ms(n)[wf]}function ps(n){return ms(n).right}function hs(n){return ms(n).top}function vs(n){return ms(n).bottom}function bs(n){return ms(n).width}function ys(n){return ms(n).height}function xs(n,e){var t=ms(n),o=ms(e);return(t[wf]+t.right)/2-o[wf]}function ws(n,e){var t=ms(n),o=ms(e);return(t.top+t.bottom)/2-o.top}function Ss(n,e){X(n,xf(),{value:e})}function Os(n,e,t){return ls({min:Na(e),max:ja(e),range:Wa(e),value:t,step:Ka(e),snap:Ja(e),snapStart:Qa(e),rounded:Za(e),hasMinEdge:es(e),hasMaxEdge:ts(e),minBound:gs(n),maxBound:ps(n),screenRange:bs(n)})}function ks(r){return function(n,e){return Ss(n,{x:o=(0<r?ss:as)(is(t=e).x,Na(t),ja(t),Ka(t))}),mn.some(o).map(x);var t,o}}function Ts(n,e,t,o,r,i){var u,c,a,s,f,l,d,m,g=(c=i,a=t,s=o,f=r,l=bs(u=e),d=s.bind(function(n){return mn.some(xs(n,u))}).getOr(0),m=f.bind(function(n){return mn.some(xs(n,u))}).getOr(l),ds({min:Na(c),max:ja(c),range:Wa(c),value:a,hasMinEdge:es(c),hasMaxEdge:ts(c),minBound:gs(u),minOffset:0,maxBound:ps(u),maxOffset:l,centerMinEdge:d,centerMaxEdge:m}));return gs(e)-gs(n)+g}function Cs(n,e){X(n,xf(),{value:e})}function Es(n,e,t){return ls({min:Ha(e),max:La(e),range:Xa(e),value:t,step:Ka(e),snap:Ja(e),snapStart:Qa(e),rounded:Za(e),hasMinEdge:os(e),hasMaxEdge:rs(e),minBound:hs(n),maxBound:vs(n),screenRange:ys(n)})}function Ds(r){return function(n,e){return Cs(n,{y:o=(0<r?ss:as)(is(t=e).y,Ha(t),La(t),Ka(t))}),mn.some(o).map(x);var t,o}}function Ms(n,e,t,o,r,i){var u,c,a,s,f,l,d,m,g=(c=i,a=t,s=o,f=r,l=ys(u=e),d=s.bind(function(n){return mn.some(ws(n,u))}).getOr(0),m=f.bind(function(n){return mn.some(ws(n,u))}).getOr(l),ds({min:Ha(c),max:La(c),range:Xa(c),value:a,hasMinEdge:os(c),hasMaxEdge:rs(c),minBound:hs(u),minOffset:0,maxBound:vs(u),maxOffset:l,centerMinEdge:d,centerMaxEdge:m}));return hs(e)-hs(n)+g}function _s(n,e){X(n,xf(),{value:e})}function Rs(n,e){return{x:n,y:e}}function Fs(u,c){return function(n,e){return o=e,r=0<u?ss:as,_s(n,Rs(i=(t=c)?is(o).x:r(is(o).x,Na(o),ja(o),Ka(o)),t?r(is(o).y,Ha(o),La(o),Ka(o)):is(o).y)),mn.some(i).map(x);var t,o,r,i}}function Is(e,t,o,n){return nf(t,function(){var n=o();e.setContextToolbar([{label:t+" group",items:n}])},{},n)}function Vs(n,o){var e={onChange:function(n,e,t){o.undoManager.transact(function(){o.formatter.apply("forecolor",{value:t}),o.nodeChanged()})},getInitialValue:w(-1)};return Is(n,"color-levels",function(){return[(r=e,$f.sketch({dom:Oa('<div class="${prefix}-slider ${prefix}-hue-slider-container"></div>'),components:[$f.parts["left-edge"](ka('<div class="${prefix}-hue-slider-black"></div>')),$f.parts.spectrum({dom:Oa('<div class="${prefix}-slider-gradient-container"></div>'),components:[ka('<div class="${prefix}-slider-gradient"></div>')],behaviours:or([ii.config({toggleClass:ci("thumb-active")})])}),$f.parts["right-edge"](ka('<div class="${prefix}-hue-slider-white"></div>')),$f.parts.thumb({dom:Oa('<div class="${prefix}-slider-thumb"></div>'),behaviours:or([ii.config({toggleClass:ci("thumb-active")})])})],onChange:function(n,e,t){var o=i(t.x());$r(e.element,"background-color",o),r.onChange(n,e,o)},onDragStart:function(n,e){ii.on(e)},onDragEnd:function(n,e){ii.off(e)},onInit:function(n,e,t,o){var r=i(o.x());$r(e.element,"background-color",r)},stepSize:10,model:{mode:"x",minX:0,maxX:360,getInitialValue:function(){return{x:r.getInitialValue()}}},sliderBehaviours:or([Nr($f.refresh)])}))];function i(n){return n<0?"black":360<n?"white":"hsl("+n+", 100%, 50%)"}var r},o)}function Bs(n){var e,t,o=n.selection.getStart(),r=fe.fromDom(o),i=fe.fromDom(n.getBody()),u=(e=function(n){return un(i,n)},(Xe(t=r)?mn.some(t):pe(t).filter(Xe)).map(function(n){return mi(function(n,e){return e(n)},gi,n,function(n){return Wr(n,"font-size").isSome()},e).bind(function(n){return Wr(n,"font-size")}).getOrThunk(function(){return Ur(n,"font-size")})}).getOr(""));return F(Gf,function(n){return u===n}).getOr("medium")}function As(n){return[ka('<span class="${prefix}-toolbar-button ${prefix}-icon-small-font ${prefix}-icon"></span>'),(e={onChange:n.onChange,sizes:Xf,category:"font",getInitialValue:n.getInitialValue},r=zt("SizeSlider",Wf,e),$f.sketch({dom:{tag:"div",classes:[ci("slider-"+r.category+"-size-container"),ci("slider"),ci("slider-size-container")]},onChange:function(n,e,t){var o=t.x();0<=o&&o<r.sizes.length&&r.onChange(o)},onDragStart:function(n,e){ii.on(e)},onDragEnd:function(n,e){ii.off(e)},model:{mode:"x",minX:0,maxX:r.sizes.length-1,getInitialValue:function(){return{x:r.getInitialValue()}}},stepSize:1,snapToGrid:!0,sliderBehaviours:or([Nr($f.refresh)]),components:[$f.parts.spectrum({dom:Oa('<div class="${prefix}-slider-size-container"></div>'),components:[ka('<div class="${prefix}-slider-size-line"></div>')]}),$f.parts.thumb({dom:Oa('<div class="${prefix}-slider-thumb"></div>'),behaviours:or([ii.config({toggleClass:ci("thumb-active")})])})]})),ka('<span class="${prefix}-toolbar-button ${prefix}-icon-large-font ${prefix}-icon"></span>')];var e,r}function Ns(n){var e=void 0!==n.uid&&Re(n,"uid")?n.uid:qs("memento");return{get:function(n){return n.getSystem().getByUid(e).getOrDie()},getOpt:function(n){return n.getSystem().getByUid(e).toOptional()},asSpec:function(){return y(y({},n),{uid:e})}}}Wu(Jc.external,ua);var Hs=Wu(Jc.optional,ca),Ps=Wu(Jc.group,aa),zs=w("entirety"),js=function(n,e){return{uiType:Yc(),owner:n,name:e}},Ls=Gu("alloy-premade"),$s=w("alloy-id-"),Gs=w("data-alloy-id"),Us=$s(),Ws=Gs(),Xs=function(n,e){Object.defineProperty(n.dom,Ws,{value:e,writable:!0})},qs=Gu,Ys=function(n){return dt(n,"uid")?n:y(y({},n),{uid:qs("uid")})},Ks=It([Lt("name"),Lt("factory"),Lt("configFields"),Yt("apis",{}),Yt("extraApis",{})]),Js=It([Lt("name"),Lt("factory"),Lt("configFields"),Lt("partFields"),Yt("apis",{}),Yt("extraApis",{})]),Qs=wa({name:"Button",factory:function(n){function t(e){return _e(n.dom,"attributes").bind(function(n){return _e(n,e)})}var e,o=(e=n.action,qo(V([e.map(function(t){return Qo(function(n,e){t(n),e.stop()})}).toArray(),Pr()]))),r=n.dom.tag;return{uid:n.uid,dom:n.dom,components:n.components,events:o,behaviours:$c(n.buttonBehaviours,[fi.config({}),jc.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:function(){if("button"!==r)return{role:t("role").getOr("button")};var n=t("type").getOr("button"),e=t("role").map(function(n){return{role:n}}).getOr({});return y({type:n},e)}()},eventOrder:n.eventOrder}},configFields:[Yt("uid",void 0),Lt("dom"),Yt("components",[]),Lc("buttonBehaviours",[fi,jc]),Ut("action"),Ut("role"),Yt("eventOrder",{})]}),Zs=Ao({fields:[],name:"unselecting",active:Object.freeze({__proto__:null,events:function(){return qo([Co(Xn(),x)])},exhibit:function(){return Io({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}})}),nf=function(n,e,t,o){return Qs.sketch({dom:Ea(n,o),action:e,buttonBehaviours:co(or([Zs.config({})]),t)})},ef=Hs({schema:[Lt("dom")],name:"label"}),tf=Da("top-left"),of=Da("top"),rf=Da("top-right"),uf=Da("right"),cf=Da("bottom-right"),af=Da("bottom"),sf=Da("bottom-left"),ff=[ef,Da("left"),uf,of,af,tf,rf,sf,cf,sa({name:"thumb",defaults:w({dom:{styles:{position:"absolute"}}}),overrides:function(n){return{events:qo([_o(Vn(),n,"spectrum"),_o(Bn(),n,"spectrum"),_o(An(),n,"spectrum"),_o(Nn(),n,"spectrum"),_o(Hn(),n,"spectrum"),_o(Pn(),n,"spectrum")])}}}),sa({schema:[vo("mouseIsDown",function(){return No(!1)})],name:"spectrum",overrides:function(t){function o(e,n){return r.getValueFromEvent(n).map(function(n){return r.setValueFrom(e,t,n)})}var r=t.model.manager;return{behaviours:or([jc.config({mode:"special",onLeft:function(n){return r.onLeft(n,t)},onRight:function(n){return r.onRight(n,t)},onUp:function(n){return r.onUp(n,t)},onDown:function(n){return r.onDown(n,t)}}),fi.config({})]),events:qo([Eo(Vn(),o),Eo(Bn(),o),Eo(Nn(),o),Eo(Hn(),function(n,e){t.mouseIsDown.get()&&o(n,e)})])}}})],lf=Object.freeze({__proto__:null,onLoad:Ma,onUnload:_a,setValue:function(n,e,t,o){e.store.manager.setValue(n,e,t,o)},getValue:function(n,e,t){return e.store.manager.getValue(n,e,t)},getState:function(n,e,t){return t}}),df=Object.freeze({__proto__:null,events:function(t,o){var n=t.resetOnDom?[Yo(function(n,e){Ma(n,t,o)}),Ko(function(n,e){_a(n,t,o)})]:[Vo(t,o,Ma)];return qo(n)}}),mf=Object.freeze({__proto__:null,memory:Ra,dataset:Fa,manual:function(){return tr({readState:Y})},init:function(n){return n.store.manager.state(n)}}),gf=[Ut("initialValue"),Lt("getFallbackEntry"),Lt("getDataKey"),Lt("setValue"),Dr("manager",{setValue:Ia,getValue:function(n,e,t){var o=e.store,r=o.getDataKey(n);return t.lookup(r).getOrThunk(function(){return o.getFallbackEntry(r)})},onLoad:function(e,t,o){t.store.initialValue.each(function(n){Ia(e,t,o,n)})},onUnload:function(n,e,t){t.clear()},state:Fa})],pf=[Lt("getValue"),Yt("setValue",Y),Ut("initialValue"),Dr("manager",{setValue:function(n,e,t,o){e.store.setValue(n,o),e.onSetValue(n,o)},getValue:function(n,e,t){return e.store.getValue(n)},onLoad:function(e,t,n){t.store.initialValue.each(function(n){t.store.setValue(e,n)})},onUnload:Y,state:er.init})],hf=Ao({fields:[Kt("store",{mode:"memory"},jt("mode",{memory:[Ut("initialValue"),Dr("manager",{setValue:function(n,e,t,o){t.set(o),e.onSetValue(n,o)},getValue:function(n,e,t){return t.get()},onLoad:function(n,e,t){e.store.initialValue.each(function(n){t.isNotSet()&&t.set(n)})},onUnload:function(n,e,t){t.clear()},state:Ra})],manual:pf,dataset:gf})),kr("onSetValue"),Yt("resetOnDom",!1)],name:"representing",active:df,apis:lf,extra:{setValueFrom:function(n,e){var t=hf.getValue(e);hf.setValue(n,t)}},state:mf}),vf=Yr("width",function(n){return n.dom.offsetWidth}),bf=function(t,o){return{left:t,top:o,translate:function(n,e){return bf(t+n,o+e)}}},yf=bf,xf=w("slider.change.value"),wf="left",Sf=ks(-1),Of=ks(1),kf=mn.none,Tf=mn.none,Cf={"top-left":mn.none(),top:mn.none(),"top-right":mn.none(),right:mn.some(function(n,e){cs(n,{x:$a(e)})}),"bottom-right":mn.none(),bottom:mn.none(),"bottom-left":mn.none(),left:mn.some(function(n,e){cs(n,{x:Pa(e)})})},Ef=Object.freeze({__proto__:null,setValueFrom:function(n,e,t){var o=Os(n,e,t);return Ss(n,{x:o}),o},setToMin:function(n,e){Ss(n,{x:Na(e)})},setToMax:function(n,e){Ss(n,{x:ja(e)})},findValueOfOffset:Os,getValueFromEvent:function(n){return Aa(n).map(function(n){return n.left})},findPositionOfValue:Ts,setPositionFromValue:function(n,e,t,o){var r=is(t),i=Ts(n,o.getSpectrum(n),r.x,o.getLeftEdge(n),o.getRightEdge(n),t),u=Ba(e.element)/2;$r(e.element,"left",i-u+"px")},onLeft:Sf,onRight:Of,onUp:kf,onDown:Tf,edgeActions:Cf}),Df=mn.none,Mf=mn.none,_f=Ds(-1),Rf=Ds(1),Ff={"top-left":mn.none(),top:mn.some(function(n,e){cs(n,{y:za(e)})}),"top-right":mn.none(),right:mn.none(),"bottom-right":mn.none(),bottom:mn.some(function(n,e){cs(n,{y:Ga(e)})}),"bottom-left":mn.none(),left:mn.none()},If=Object.freeze({__proto__:null,setValueFrom:function(n,e,t){var o=Es(n,e,t);return Cs(n,{y:o}),o},setToMin:function(n,e){Cs(n,{y:Ha(e)})},setToMax:function(n,e){Cs(n,{y:La(e)})},findValueOfOffset:Es,getValueFromEvent:function(n){return Aa(n).map(function(n){return n.top})},findPositionOfValue:Ms,setPositionFromValue:function(n,e,t,o){var r=is(t),i=Ms(n,o.getSpectrum(n),r.y,o.getTopEdge(n),o.getBottomEdge(n),t),u=Kr(e.element)/2;$r(e.element,"top",i-u+"px")},onLeft:Df,onRight:Mf,onUp:_f,onDown:Rf,edgeActions:Ff}),Vf=Aa,Bf=Fs(-1,!1),Af=Fs(1,!1),Nf=Fs(-1,!0),Hf=Fs(1,!0),Pf={"top-left":mn.some(function(n,e){cs(n,us(Pa(e),za(e)))}),top:mn.some(function(n,e){cs(n,us(qa(e),za(e)))}),"top-right":mn.some(function(n,e){cs(n,us($a(e),za(e)))}),right:mn.some(function(n,e){cs(n,us($a(e),Ya(e)))}),"bottom-right":mn.some(function(n,e){cs(n,us($a(e),Ga(e)))}),bottom:mn.some(function(n,e){cs(n,us(qa(e),Ga(e)))}),"bottom-left":mn.some(function(n,e){cs(n,us(Pa(e),Ga(e)))}),left:mn.some(function(n,e){cs(n,us(Pa(e),Ya(e)))})},zf=Object.freeze({__proto__:null,setValueFrom:function(n,e,t){var o=Rs(Os(n,e,t.left),Es(n,e,t.top));return _s(n,o),o},setToMin:function(n,e){_s(n,Rs(Na(e),Ha(e)))},setToMax:function(n,e){_s(n,Rs(ja(e),La(e)))},getValueFromEvent:Vf,setPositionFromValue:function(n,e,t,o){var r=is(t),i=Ts(n,o.getSpectrum(n),r.x,o.getLeftEdge(n),o.getRightEdge(n),t),u=Ms(n,o.getSpectrum(n),r.y,o.getTopEdge(n),o.getBottomEdge(n),t),c=Ba(e.element)/2,a=Kr(e.element)/2;$r(e.element,"left",i-c+"px"),$r(e.element,"top",u-a+"px")},onLeft:Bf,onRight:Af,onUp:Nf,onDown:Hf,edgeActions:Pf}),jf=[Yt("stepSize",1),Yt("onChange",Y),Yt("onChoose",Y),Yt("onInit",Y),Yt("onDragStart",Y),Yt("onDragEnd",Y),Yt("snapToGrid",!1),Yt("rounded",!0),Ut("snapStart"),$t("model",jt("mode",{x:[Yt("minX",0),Yt("maxX",100),vo("value",function(n){return No(n.mode.minX)}),Lt("getInitialValue"),Dr("manager",Ef)],y:[Yt("minY",0),Yt("maxY",100),vo("value",function(n){return No(n.mode.minY)}),Lt("getInitialValue"),Dr("manager",If)],xy:[Yt("minX",0),Yt("maxX",100),Yt("minY",0),Yt("maxY",100),vo("value",function(n){return No({x:n.mode.minX,y:n.mode.minY})}),Lt("getInitialValue"),Dr("manager",zf)]})),ju("sliderBehaviours",[jc,hf]),vo("mouseIsDown",function(){return No(!1)})],Lf=w("mouse.released"),$f=Sa({name:"Slider",configFields:jf,partFields:ff,factory:function(i,n,e,t){function u(n){return ga(n,i,"thumb")}function c(n){return ga(n,i,"spectrum")}function o(n){return ma(n,i,"left-edge")}function r(n){return ma(n,i,"right-edge")}function a(n){return ma(n,i,"top-edge")}function s(n){return ma(n,i,"bottom-edge")}function f(n,e){v.setPositionFromValue(n,e,i,{getLeftEdge:o,getRightEdge:r,getTopEdge:a,getBottomEdge:s,getSpectrum:c})}function l(n,e){h.value.set(e),f(n,u(n))}function d(t){var n=i.mouseIsDown.get();i.mouseIsDown.set(!1),n&&ma(t,i,"thumb").each(function(n){var e=h.value.get();i.onChoose(t,n,e)})}function m(n,e){e.stop(),i.mouseIsDown.set(!0),i.onDragStart(n,u(n))}function g(n,e){e.stop(),i.onDragEnd(n,u(n)),d(n)}var p,h=i.model,v=h.manager;return{uid:i.uid,dom:i.dom,components:n,behaviours:Lu(i.sliderBehaviours,[jc.config({mode:"special",focusIn:function(n){return ma(n,i,"spectrum").map(jc.focusIn).map(x)}}),hf.config({store:{mode:"manual",getValue:function(n){return h.value.get()}}}),ti.config({channels:((p={})[Lf()]={onReceive:d},p)})]),events:qo([Eo(xf(),function(n,e){!function(n,e){l(n,e);var t=u(n);i.onChange(n,t,e),mn.some(!0)}(n,e.event.value)}),Yo(function(n,e){var t=h.getInitialValue();h.value.set(t);var o=u(n);f(n,o);var r=c(n);i.onInit(n,o,r,h.value.get())}),Eo(Vn(),m),Eo(An(),g),Eo(Nn(),m),Eo(Pn(),g)]),apis:{resetToMin:function(n){v.setToMin(n,i)},resetToMax:function(n){v.setToMax(n,i)},setValue:l,refresh:f},domModification:{styles:{position:"relative"}}}},apis:{setValue:function(n,e,t){n.setValue(e,t)},resetToMin:function(n,e){n.resetToMin(e)},resetToMax:function(n,e){n.resetToMax(e)},refresh:function(n,e){n.refresh(e)}}}),Gf=["9px","10px","11px","12px","14px","16px","18px","20px","24px","32px","36px"],Uf=w(Gf),Wf=It([Lt("getInitialValue"),Lt("onChange"),Lt("category"),Lt("sizes")]),Xf=Uf(),qf={},Yf={exports:qf};function Kf(u){var e=Ns({dom:{tag:"input",attributes:{accept:"image/*",type:"file",title:""},styles:{visibility:"hidden",position:"absolute"}},events:qo([Ro(Un()),Eo(Gn(),function(n,e){var t=e.event.raw,o=t.target.files||t.dataTransfer.files;mn.from(o[0]).each(function(n){var r,t,i=u;r=n,t=r,new Jl(function(n){var e=new FileReader;e.onloadend=function(){n(e.result)},e.readAsDataURL(t)}).then(function(n){return n.split(",")[1]}).then(function(o){i.undoManager.transact(function(){var n=i.editorUpload.blobCache,e=n.create(Gu("mceu"),r,o);n.add(e);var t=i.dom.createHTML("img",{src:e.blobUri()});i.insertContent(t)})})})})])});return Qs.sketch({dom:Ea("image",u),components:[e.asSpec()],action:function(n){e.get(n).element.dom.click()}})}function Jf(n){return n.dom.textContent}function Qf(n){return 0<n.length}function Zf(n){return null==n?"":n}function nl(t,u){u.url.toOptional().filter(Qf).fold(function(){var e=t;u.link.bind(K).each(function(n){e.execCommand("unlink")})},function(r){var n,e,i=(n=u,(e={}).href=r,n.title.toOptional().filter(Qf).each(function(n){e.title=n}),n.target.toOptional().filter(Qf).each(function(n){e.target=n}),e);u.link.bind(K).fold(function(){var n=u.text.toOptional().filter(Qf).getOr(r);t.insertContent(t.dom.createHTML("a",i,t.dom.encode(n)))},function(e){var t,o,n=(t=e,o=r,u.text.toOptional().filter(Qf).fold(function(){return Be(n=t,"href")===Jf(n)?mn.some(o):mn.none();var n},mn.some));Ve(e,i),n.each(function(n){e.dom.textContent=n})})})}function el(n,e){var t,o;return{key:n,value:{config:{},me:(t=n,o=qo(e),Ao({fields:[Lt("enabled")],name:t,active:{events:w(o)}})),configAsRaw:w({}),initialConfig:{},state:er}}}function tl(n){return n.dom.value}function ol(n,e){if(void 0===e)throw new Error("Value.set was undefined");n.dom.value=e}function rl(n,e){var t=Ns(rd.sketch({inputAttributes:{placeholder:cd.translate(e)},onSetValue:function(n,e){W(n,$n())},inputBehaviours:or([ed.config({find:mn.some}),ud.config({}),jc.config({mode:"execution"})]),selectOnFocus:!1})),o=Ns(Qs.sketch({dom:Oa('<button class="${prefix}-input-container-x ${prefix}-icon-cancel-circle ${prefix}-icon"></button>'),action:function(n){var e=t.get(n);hf.setValue(e,"")}}));return{name:n,spec:td.sketch({dom:Oa('<div class="${prefix}-input-container"></div>'),components:[t.asSpec(),o.asSpec()],containerBehaviours:or([ii.config({toggleClass:ci("input-container-empty")}),ed.config({find:function(n){return mn.some(t.get(n))}}),el("input-clearing",[Eo($n(),function(n){var e=t.get(n);(0<hf.getValue(e).length?ii.off:ii.on)(n)})])])})}}function il(n,e,t){(e.disabled()?sd:fd)(n,e)}function ul(n,e){return!0===e.useNative&&T(ad,le(n.element))}function cl(n,e){return ul(n,e)?Ne(n.element,"disabled"):"true"===Be(n.element,"aria-disabled")}function al(n){return"<alloy.field."+n+">"}function sl(o,r){return Ca(r,"link","link",function(){var n,e,t=hd(o,r);o.setContextToolbar(t),n=r,e=function(){o.focusToolbar()},(Zl.os.isAndroid()?function(n,e){var t=e.selection.getRng();n(),e.selection.setRng(t)}:p)(e,n),Ql(r).each(function(n){r.selection.select(n.dom)})})}function fl(e){function t(n){return"The component must be in a context to execute: "+n+(e?"\n"+$o(e().element)+" is not in context.":"")}function n(n){return function(){throw new Error(t(n))}}function o(n){return function(){console.warn(t(n))}}return{debugInfo:w("fake"),triggerEvent:o("triggerEvent"),triggerFocus:o("triggerFocus"),triggerEscape:o("triggerEscape"),broadcast:o("broadcast"),broadcastOn:o("broadcastOn"),broadcastEvent:o("broadcastEvent"),build:n("build"),addToWorld:n("addToWorld"),removeFromWorld:n("removeFromWorld"),addToGui:n("addToGui"),removeFromGui:n("removeFromGui"),getByUid:n("getByUid"),getByDom:n("getByDom"),isConnected:S}}function ll(n,r){var i={};return Ee(n,function(n,o){Ee(n,function(n,e){var t=_e(i,e).getOr([]);i[e]=t.concat([r(o,n)])})}),i}function dl(n){return n.cHandler}function ml(n,e){return{name:n,handler:e}}function gl(n,e,t){var o=e[t];return o?function(u,c,n,a){try{var e=(t=function(n,e){var t=n[c],o=e[c],r=a.indexOf(t),i=a.indexOf(o);if(-1===r)throw new Error("The ordering for "+u+" does not have an entry for "+t+".\nOrder specified: "+JSON.stringify(a,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+o+".\nOrder specified: "+JSON.stringify(a,null,2));return r<i?-1:i<r?1:0},(o=gn.call(n,0)).sort(t),o);return ht.value(e)}catch(n){return ht.error([n])}var t,o}("Event: "+t,"name",n,o).map(function(n){var t,e,o,r,i=Z(n,function(n){return n.handler});return{can:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return R(e,function(n,e){return n&&e.can.apply(void 0,t)},!0)},abort:(o=e=t=i,r=function(n){return n.abort},function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return R(o,function(n,e){return n||r(e).apply(void 0,t)},!1)}),run:function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];M(t,function(n){n.run.apply(void 0,e)})}}}):ht.error(["The event ("+t+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(Z(n,function(n){return n.name}),null,2)])}function pl(e,n){M(n,function(n){je(e,n)})}function hl(e,n){M(n,function(n){Le(e,n)})}function vl(n){var t,e,o,r,i=(t=_e(n,"behaviours").getOr({}),B(st(t),function(n){var e=t[n];return l(e)?[e.me]:[]}));return e=n,r=Z(o=i,function(n){return Xt(n.name(),[Lt("config"),Yt("state",er)])}),{list:o,data:De(Ht("component.behaviours",lo(r),e.behaviours).fold(function(n){throw new Error(po(n)+"\nComplete spec:\n"+JSON.stringify(e,null,2))},K),function(n){return w(n.map(function(n){return{config:n.config,state:n.state.init(n.config)}}))})}}function bl(n,e,t){var o,r,i=y(y({},(o=n).dom),{uid:o.uid,domChildren:Z(o.components,function(n){return n.element})}),u=n.domModification.fold(function(){return Io({})},Io),c=0<e.length?function(e,n,t,o){var r=y({},n);function i(n){return _(n,function(n,e){return y(y({},e.modification),n)},{})}M(t,function(n){r[n.name()]=n.exhibit(e,o)});var u=ll(r,function(n,e){return{name:n,modification:e}});return Io({classes:_(u.classes,function(n,e){return e.modification.concat(n)},[]),attributes:i(u.attributes),styles:i(u.styles)})}(t,{"alloy.base.modification":u},e,i):u;return y(y({},r=i),{attributes:y(y({},r.attributes),c.attributes),styles:y(y({},r.styles),c.styles),classes:r.classes.concat(c.classes)})}function yl(n,e,t){var o,r,i,u,c,a={"alloy.base.behaviour":n.events},s=n.eventOrder;return r=t,i=e,o=ll(y(y({},a),(u=r,c={},M(i,function(n){c[n.name()]=n.handlers(u)}),c)),ml),wd(o,s).getOrDie()}function xl(t){function n(){return f}var e=No(xd),o=Pt(Ht("custom.definition",Od,t)),r=vl(t),i=r.list,u=r.data,c=function(n){var c=fe.fromTag(n.tag);Ve(c,n.attributes),pl(c,n.classes),Gr(c,n.styles),n.innerHtml.each(function(n){return t=n,i=me(e=c).dom,xe(u=fe.fromDom(i.createDocumentFragment()),(o=t,(r=(i||document).createElement("div")).innerHTML=o,Je(fe.fromDom(r)))),we(e),void Qe(e,u);var e,t,o,r,i,u});var e=n.domChildren;return xe(c,e),n.value.each(function(n){ol(c,n)}),n.uid,Xs(c,n.uid),c}(bl(o,i,u)),a=yl(o,i,u),s=No(o.components),f={uid:t.uid,getSystem:e.get,config:function(n){var e=u;return(m(e[n.name()])?e[n.name()]:function(){throw new Error("Could not find "+n.name()+" in "+JSON.stringify(t,null,2))})()},hasConfigured:function(n){return m(u[n.name()])},spec:t,readState:function(n){return u[n]().map(function(n){return n.state.readState()}).getOr("not enabled")},getApis:function(){return o.apis},connect:function(n){e.set(n)},disconnect:function(){e.set(fl(n))},element:c,syncComponents:function(){var n=B(Je(c),function(n){return e.get().getByDom(n).fold(function(){return[]},P)});s.set(n)},components:s.get,events:a};return f}function wl(n){return function(n){var e=zt("external.component",It([Lt("element"),Ut("uid")]),n),t=No(fl()),o=e.uid.getOrThunk(function(){return qs("external")});Xs(e.element,o);var r={uid:o,getSystem:t.get,config:mn.none,hasConfigured:S,connect:function(n){t.set(n)},disconnect:function(){t.set(fl(function(){return r}))},getApis:function(){return{}},element:e.element,spec:n,readState:w("No state"),syncComponents:Y,components:w([]),events:{}};return ha(r)}({element:fe.fromText(n)})}function Sl(n){(jo(n.element).isNone()||fi.isFocused(n))&&(fi.isFocused(n)||fi.focus(n),X(n,Ed,{item:n}))}function Ol(n){X(n,Dd,{item:n})}function kl(n,e,t,o){var r=n.getSystem().build(o);at(n,r,t)}function Tl(n,e,t,o){F(zd(n),function(n){return un(o.element,n.element)}).each(Ce)}function Cl(e,n,t,r,o){var i=zd(e);return mn.from(i[r]).map(function(n){return Tl(e,0,0,n),o.each(function(n){kl(e,0,function(n,e){var t,o=e;he(t=n,r).fold(function(){Qe(t,o)},function(n){ve(n,o)})},n)}),n})}function El(n,e,t,o){return _e(e.routes,o.start).bind(function(n){return _e(n,o.destination)})}function Dl(t,o,n){var e,r,i=n;qd(e=t,r=o).bind(function(n){return Xd(e,r,i,n)}).each(function(n){var e=n.transition;Le(t.element,e.transitionClass),He(t.element,o.destinationAttr)})}function Ml(n,e,t,o){Dl(n,e,t),Ne(n.element,e.stateAttr)&&Be(n.element,e.stateAttr)!==o&&e.onFinish(n,o),Ie(n.element,e.stateAttr,o)}function _l(n){je(n,Qd)}function Rl(n){Le(n,Qd)}function Fl(n){return _e(n,"format").getOr(n.title)}function Il(n){return Re(n,"items")?(r=co((e=n,t=["items"],o={},Ee(e,function(n,e){T(t,e)||(o[e]=n)}),o),{menu:!0}),i=tm(e.items),{item:r,menus:co(i.menus,yo(e.title,i.items)),expansions:co(i.expansions,yo(e.title,e.title))}):{item:n,menus:{},expansions:{}};var t,o,e,r,i}function Vl(t,n,o){var e,r,i,u,c,a,s,f,l,d,m,g=(e=t,i=(r=function(n){return B(n,function(n){return void 0===n.items?!Re(n,"format")||e.formatter.canApply(n.format)?[n]:[]:0<r(n.items).length?[n]:[]})})(n),tm(i));return c=(u={formats:g,handle:function(n,e){t.undoManager.transact(function(){ii.isOn(n)?t.formatter.remove(e):t.formatter.apply(e)}),o()}}).formats,a=function(){return m},s=em("Styles",[].concat(Z(c.items,function(n){return nm(Fl(n),n.title,n.isSelected(),n.getPreview(),Re(c.expansions,Fl(n)))})),a,!1),f=De(c.menus,function(n,e){var t=Z(n,function(n){return nm(Fl(n),n.title,void 0!==n.isSelected&&n.isSelected(),void 0!==n.getPreview?n.getPreview():"",Re(c.expansions,Fl(n)))});return em(e,t,a,!0)}),l=co(f,yo("styles",s)),d={tmenu:Wd.tieredData("styles",l,c.expansions)},(m=Ns(Wd.sketch({dom:{tag:"div",classes:[ci("styles-menu")]},components:[],fakeFocus:!0,stayInDom:!0,onExecute:function(n,e){var t=hf.getValue(e);return u.handle(e,t.value),mn.none()},onEscape:function(){return mn.none()},onOpenMenu:function(n,e){var t=Ba(n.element);Va(e.element,t),Jd.jumpTo(e,"current")},onOpenSubmenu:function(n,e,t){var o=Ba(n.element),r=pi(e.element,'[role="menu"]').getOrDie("hacky"),i=n.getSystem().getByDom(r).getOrDie();Va(t.element,o),Jd.progressTo(i,"before"),Jd.jumpTo(t,"after"),Jd.progressTo(t,"current")},onCollapseMenu:function(n,e,t){var o=pi(e.element,'[role="menu"]').getOrDie("hacky"),r=n.getSystem().getByDom(o).getOrDie();Jd.progressTo(r,"after"),Jd.progressTo(t,"current")},navigateOnHover:!1,highlightImmediately:!0,data:d.tmenu,markers:{backgroundMenu:ci("styles-background-menu"),menu:ci("styles-menu"),selectedMenu:ci("styles-selected-menu"),item:ci("styles-item"),selectedItem:ci("styles-selected-item")}}))).asSpec()}function Bl(n){var e=n.replace(/\|/g," ").trim();return 0<e.length?e.split(/\s+/):[]}function Al(e,r){function n(t){return function(){return nf(e=t,function(){n.execCommand(e)},{},n=r);var n,e}}function t(o){return function(){return n=r,t=Ta(e=o),nf(e,function(){n.execCommand(e)},t,n);var n,e,t}}function o(n,e,t){return function(){return Ca(r,n,e,t)}}function i(){return Vl(r,b,function(){r.fire("scrollIntoView")})}function u(n,e){return{isSupported:function(){var e=r.ui.registry.getAll().buttons;return n.forall(function(n){return Re(e,n)})},sketch:e}}var c,a,s=n("undo"),f=n("redo"),l=t("bold"),d=t("italic"),m=t("underline"),g=n("removeformat"),p=o("unlink","link",function(){r.execCommand("unlink",null,!1)}),h=o("unordered-list","ul",function(){r.execCommand("InsertUnorderedList",null,!1)}),v=o("ordered-list","ol",function(){r.execCommand("InsertOrderedList",null,!1)}),b=(a=function(n){return Z(n,function(n){if(Re(n,"items")){var e=a(n.items);return co(co(n,{isSelected:S,getPreview:w("")}),{items:e})}return Re(n,"format")?co(n,{isSelected:y(n.format),getPreview:x(n.format)}):(o=Gu((t=n).title),r=co(t,{format:o,isSelected:y(o),getPreview:x(o)}),c.formatter.register(o,r),r);var t,o,r})})((c=r).getParam("style_formats",fr,"array"));function y(n){return function(){return c.formatter.match(n)}}function x(n){return function(){return c.formatter.getCssText(n)}}return{undo:u(mn.none(),s),redo:u(mn.none(),f),bold:u(mn.none(),l),italic:u(mn.none(),d),underline:u(mn.none(),m),removeformat:u(mn.none(),g),link:u(mn.none(),function(){return sl(e,r)}),unlink:u(mn.none(),p),image:u(mn.none(),function(){return Kf(r)}),bullist:u(mn.some("bullist"),h),numlist:u(mn.some("numlist"),v),fontsizeselect:u(mn.none(),function(){return n={onChange:function(n){var o;o=t,mn.from(Gf[n]).each(function(n){var e,t=n;Bs(e=o)!==t&&e.execCommand("fontSize",!1,t)})},getInitialValue:function(){return e=Bs(t),I(Gf,function(n){return n===e}).getOr(2);var e}},Is(e,"font-size",function(){return As(n)},t=r);var t,n}),forecolor:u(mn.none(),function(){return Vs(e,r)}),styleselect:u(mn.none(),function(){return nf("style-formats",function(n){r.fire("toReading"),e.dropup.appear(i,ii.on,n)},or([ii.config({toggleClass:ci("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),ti.config({channels:xo([Hr(mr,ii.off),Hr(gr,ii.off)])})]),r)})}}function Nl(n){function e(){return n.stopPropagation()}function t(){return n.preventDefault()}var o=i(t,e);return{target:fe.fromDom(function(n){if(et()&&l(n.target)){var e=fe.fromDom(n.target);if(Xe(e)&&ot(e)&&n.composed&&n.composedPath){var t=n.composedPath();if(t)return 0<(o=t).length?mn.some(o[0]):mn.none()}}var o;return mn.from(n.target)}(n).getOr(n.target)),x:n.clientX,y:n.clientY,stop:e,prevent:t,kill:o,raw:n}}function Hl(n,e,t,o,r){var i,u,c=(i=t,u=o,function(n){i(n)&&u(Nl(n))});return n.dom.addEventListener(e,c,r),{unbind:J(rm,n,e,c,r)}}function Pl(n,e,t){return Hl(n,e,im,t,!1)}function zl(n,e,t){return Hl(n,e,im,t,!0)}function jl(n){return{isPortrait:w(n.matchMedia("(orientation: portrait)").matches)}}function Ll(o,e){var n=fe.fromDom(o),r=null,t=Pl(n,"orientationchange",function(){um.clearInterval(r);var n=jl(o);e.onChange(n),i(function(){e.onReady(n)})}),i=function(n){um.clearInterval(r);var e=o.innerHeight,t=0;r=um.setInterval(function(){e!==o.innerHeight?(um.clearInterval(r),n(mn.some(o.innerHeight))):20<t&&(um.clearInterval(r),n(mn.none())),t++},50)};return{onAdjustment:i,destroy:function(){t.unbind()}}}function $l(n,e,t){var o,r=n.document.createRange(),i=r;return e.fold(function(n){i.setStartBefore(n.dom)},function(n,e){i.setStart(n.dom,e)},function(n){i.setStartAfter(n.dom)}),o=r,t.fold(function(n){o.setEndBefore(n.dom)},function(n,e){o.setEnd(n.dom,e)},function(n){o.setEndAfter(n.dom)}),r}function Gl(n,e,t,o,r){var i=n.document.createRange();return i.setStart(e.dom,t),i.setEnd(o.dom,r),i}function Ul(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom,width:n.width,height:n.height}}function Wl(n,e,t){return e(fe.fromDom(t.startContainer),t.startOffset,fe.fromDom(t.endContainer),t.endOffset)}function Xl(n,e){var r,t,o,i=(r=n,e.match({domRange:function(n){return{ltr:w(n),rtl:mn.none}},relative:function(n,e){return{ltr:k(function(){return $l(r,n,e)}),rtl:k(function(){return mn.some($l(r,e,n))})}},exact:function(n,e,t,o){return{ltr:k(function(){return Gl(r,n,e,t,o)}),rtl:k(function(){return mn.some(Gl(r,t,o,n,e))})}}}));return(o=(t=i).ltr()).collapsed?t.rtl().filter(function(n){return!1===n.collapsed}).map(function(n){return cm.rtl(fe.fromDom(n.endContainer),n.endOffset,fe.fromDom(n.startContainer),n.startOffset)}).getOrThunk(function(){return Wl(0,cm.ltr,o)}):Wl(0,cm.ltr,o)}!function(){var n=this,e=function(){var n,e,t,o={exports:{}};function r(){}function i(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],l(n,this)}function u(t,o){for(;3===t._state;)t=t._value;0!==t._state?(t._handled=!0,i._immediateFn(function(){var n,e=1===t._state?o.onFulfilled:o.onRejected;if(null!==e){try{n=e(t._value)}catch(n){return void a(o.promise,n)}c(o.promise,n)}else(1===t._state?c:a)(o.promise,t._value)})):t._deferreds.push(o)}function c(e,n){try{if(n===e)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if(n instanceof i)return e._state=3,e._value=n,void s(e);if("function"==typeof t)return void l((o=t,r=n,function(){o.apply(r,arguments)}),e)}e._state=1,e._value=n,s(e)}catch(n){a(e,n)}var o,r}function a(n,e){n._state=2,n._value=e,s(n)}function s(n){2===n._state&&0===n._deferreds.length&&i._immediateFn(function(){n._handled||i._unhandledRejectionFn(n._value)});for(var e=0,t=n._deferreds.length;e<t;e++)u(n,n._deferreds[e]);n._deferreds=null}function f(n,e,t){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof e?e:null,this.promise=t}function l(n,e){var t=!1;try{n(function(n){t||(t=!0,c(e,n))},function(n){t||(t=!0,a(e,n))})}catch(n){if(t)return;t=!0,a(e,n)}}n=o,e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=setTimeout,i.prototype.catch=function(n){return this.then(null,n)},i.prototype.then=function(n,e){var t=new this.constructor(r);return u(this,new f(n,e,t)),t},i.all=function(n){var c=Array.prototype.slice.call(n);return new i(function(r,i){if(0===c.length)return r([]);for(var u=c.length,n=0;n<c.length;n++)!function e(t,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var o=n.then;if("function"==typeof o)return o.call(n,function(n){e(t,n)},i),0}c[t]=n,0==--u&&r(c)}catch(n){i(n)}}(n,c[n])})},i.resolve=function(e){return e&&"object"==typeof e&&e.constructor===i?e:new i(function(n){n(e)})},i.reject=function(t){return new i(function(n,e){e(t)})},i.race=function(r){return new i(function(n,e){for(var t=0,o=r.length;t<o;t++)r[t].then(n,e)})},i._immediateFn="function"==typeof setImmediate?function(n){setImmediate(n)}:function(n){t(n,0)},i._unhandledRejectionFn=function(n){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",n)},i._setImmediateFn=function(n){i._immediateFn=n},i._setUnhandledRejectionFn=function(n){i._unhandledRejectionFn=n},n.exports?n.exports=i:e.Promise||(e.Promise=i);var d=o.exports;return{boltExport:("undefined"!=typeof window?window:Function("return this;")()).Promise||d}};"object"==typeof qf&&void 0!==Yf?Yf.exports=e():(n="undefined"!=typeof globalThis?globalThis:n||self).EphoxContactWrapper=e()}();function ql(r,n){return{uid:r.uid,dom:r.dom,components:n,behaviours:Lu(r.formBehaviours,[hf.config({store:{mode:"manual",getValue:function(n){var e,t,o=(e=r,t=n.getSystem(),De(e.partUids,function(n,e){return w(t.getByUid(n))}));return De(o,function(n,o){return n().bind(function(n){var e=ed.getCurrent(n),t=new Error("Cannot find a current component to extract the value from for form part '"+o+"': "+$o(n.element));return e.fold(function(){return ht.error(t)},ht.value)}).map(hf.getValue)})},setValue:function(t,n){Ee(n,function(e,n){ma(t,r,n).each(function(n){ed.getCurrent(n).each(function(n){hf.setValue(n,e)})})})}}})]),apis:{getField:function(n,e){return ma(n,r,e).bind(ed.getCurrent)}}}}function Yl(n){return"prepared"===n.type?mn.some(n.menu):mn.none()}var Kl,Jl=Yf.exports.boltExport,Ql=function(n){return vi(fe.fromDom(n.selection.getStart()),"a")},Zl=Fn(),nd=Object.freeze({__proto__:null,getCurrent:function(n,e,t){return e.find(n)}}),ed=Ao({fields:[Lt("find")],name:"composing",apis:nd}),td=wa({name:"Container",factory:function(n){var e=n.dom,t=e.attributes,o=c(e,["attributes"]);return{uid:n.uid,dom:y({tag:"div",attributes:y({role:"presentation"},t)},o),components:n.components,behaviours:n.containerBehaviours.dump,events:n.events,domModification:n.domModification,eventOrder:n.eventOrder}},configFields:[Yt("components",[]),ju("containerBehaviours",[]),Yt("events",{}),Yt("domModification",{}),Yt("eventOrder",{})]}),od=wa({name:"DataField",factory:function(t){return{uid:t.uid,dom:t.dom,behaviours:$c(t.dataBehaviours,[hf.config({store:{mode:"memory",initialValue:t.getInitialValue()}}),ed.config({find:mn.some})]),events:qo([Yo(function(n,e){hf.setValue(n,t.getInitialValue())})])}},configFields:[Lt("uid"),Lt("dom"),Lt("getInitialValue"),Lc("dataBehaviours",[hf,ed])]}),rd=wa({name:"Input",configFields:w([Ut("data"),Yt("inputAttributes",{}),Yt("inputStyles",{}),Yt("tag","input"),Yt("inputClasses",[]),kr("onSetValue"),Yt("styles",{}),Yt("eventOrder",{}),ju("inputBehaviours",[hf,fi]),Yt("selectOnFocus",!0)])(),factory:function(n,e){return{uid:n.uid,dom:{tag:(o=n).tag,attributes:y({type:"text"},o.inputAttributes),styles:o.inputStyles,classes:o.inputClasses},components:[],behaviours:(t=n,y(y({},or([fi.config({onFocus:t.selectOnFocus?function(n){var e=n.element,t=tl(e);e.dom.setSelectionRange(0,t.length)}:Y})])),Lu(t.inputBehaviours,[hf.config({store:y(y({mode:"manual"},t.data.map(function(n){return{initialValue:n}}).getOr({})),{getValue:function(n){return tl(n.element)},setValue:function(n,e){tl(n.element)!==e&&ol(n.element,e)}}),onSetValue:t.onSetValue})]))),eventOrder:n.eventOrder};var t,o}}),id=Object.freeze({__proto__:null,exhibit:function(n,e){return Io({attributes:xo([{key:e.tabAttr,value:"true"}])})}}),ud=Ao({fields:[Yt("tabAttr","data-alloy-tabstop")],name:"tabstopping",active:id}),cd=tinymce.util.Tools.resolve("tinymce.util.I18n"),ad=["input","button","textarea","select"],sd=function(e,n,t){n.disableClass.each(function(n){je(e.element,n)}),(ul(e,n)?function(n){Ie(n.element,"disabled","disabled")}:function(n){Ie(n.element,"aria-disabled","true")})(e),n.onDisabled(e)},fd=function(e,n,t){n.disableClass.each(function(n){Le(e.element,n)}),(ul(e,n)?function(n){He(n.element,"disabled")}:function(n){Ie(n.element,"aria-disabled","false")})(e),n.onEnabled(e)},ld=Object.freeze({__proto__:null,enable:fd,disable:sd,isDisabled:cl,onLoad:il,set:function(n,e,t,o){(o?sd:fd)(n,e)}}),dd=Object.freeze({__proto__:null,exhibit:function(n,e){return Io({classes:e.disabled()?e.disableClass.toArray():[]})},events:function(t,n){return qo([Co(Zn(),function(n,e){return cl(n,t)}),Vo(t,n,il)])}}),md=Ao({fields:[Kt("disabled",S,go),Yt("useNative",!0),Ut("disableClass"),kr("onDisabled"),kr("onEnabled")],name:"disabling",active:dd,apis:ld}),gd=[ju("formBehaviours",[hf])],pd=(va(function(n,e,t){return n.getField(e,t)}),function(n){var r,e={field:function(n,e){return r.push(n),t=al(n),o=e,{uiType:Yc(),owner:"form",name:t,config:o,validated:{}};var t,o},record:w(r=[])},t=n(e),o=Z(e.record(),function(n){return sa({name:n,pname:al(n)})});return xa("form",gd,o,ql,t)}),hd=k(function(o,r){return[{label:"the link group",items:[(n={fields:[rl("url","Type or paste URL"),rl("text","Link text"),rl("title","Link title"),rl("target","Link target"),{name:"link",spec:od.sketch({dom:{tag:"span",styles:{display:"none"}},getInitialValue:function(){return mn.none()}})}],maxFieldIndex:["url","text","title","target"].length-1,getInitialValue:function(){return mn.some(Ql(n=r).fold(function(){return{url:"",text:n.selection.getContent({format:"text"}),title:"",target:"",link:mn.none()}},function(n){return t=Jf(e=n),o=Be(e,"href"),r=Be(e,"title"),i=Be(e,"target"),{url:Zf(o),text:t!==o?Zf(t):"",title:Zf(r),target:Zf(i),link:mn.some(e)};var e,t,o,r,i}));var n},onExecute:function(n,e){var t=hf.getValue(n);nl(r,t),o.restoreToolbar(),r.focus()}},a="navigateEvent",s=zt("SerialisedDialog",lo([Lt("fields"),Yt("maxFieldIndex",n.fields.length-1),Lt("onExecute"),Lt("getInitialValue"),vo("state",function(){return{dialogSwipeState:cu(),currentScreen:No(0)}})]),n),f=Ns(pd(function(t){return{dom:Oa('<div class="${prefix}-serialised-dialog"></div>'),components:[td.sketch({dom:Oa('<div class="${prefix}-serialised-dialog-chain" style="left: 0px; position: absolute;"></div>'),components:Z(s.fields,function(n,e){return e<=s.maxFieldIndex?td.sketch({dom:Oa('<div class="${prefix}-serialised-dialog-screen"></div>'),components:[i(-1,"previous",0<e),t.field(n.name,n.spec),i(1,"next",e<s.maxFieldIndex)]}):t.field(n.name,n.spec)})})],formBehaviours:or([Nr(function(n,e){var t=e;hi(n.element,"."+ci("serialised-dialog-chain")).each(function(n){$r(n,"left",-s.state.currentScreen.get()*t.width+"px")})}),jc.config({mode:"special",focusIn:function(n,e){c(n)},onTab:function(n,e){return u(n,1),mn.some(!0)},onShiftTab:function(n,e){return u(n,-1),mn.some(!0)}}),el("form-events",[Yo(function(e,n){s.state.currentScreen.set(0),s.state.dialogSwipeState.clear();var t=l.get(e);Qi.highlightFirst(t),s.getInitialValue(e).each(function(n){hf.setValue(e,n)})}),Qo(s.onExecute),Eo(Wn(),function(n,e){"left"===e.event.raw.propertyName&&c(n)}),Eo(a,function(n,e){u(n,e.event.direction)})])])}})),l=Ns({dom:Oa('<div class="${prefix}-dot-container"></div>'),behaviours:or([Qi.config({highlightClass:ci("dot-active"),itemClass:ci("dot-item")})]),components:B(s.fields,function(n,e){return e<=s.maxFieldIndex?[ka('<div class="${prefix}-dot-item ${prefix}-icon-full-dot ${prefix}-icon"></div>')]:[]})}),{dom:Oa('<div class="${prefix}-serializer-wrapper"></div>'),components:[f.asSpec(),l.asSpec()],behaviours:or([jc.config({mode:"special",focusIn:function(n){var e=f.get(n);jc.focusIn(e)}}),el("serializer-wrapper-events",[Eo(Vn(),function(n,e){var t=e.event;s.state.dialogSwipeState.set({xValue:t.raw.touches[0].clientX,points:[]})}),Eo(Bn(),function(n,e){var t=e.event;s.state.dialogSwipeState.on(function(n){e.event.prevent(),s.state.dialogSwipeState.set(function(n,e){if(e===n.xValue)return n;var t=0<e-n.xValue?1:-1,o={direction:t,xValue:e};return{xValue:e,points:(0===n.points.length?[]:n.points[n.points.length-1].direction===t?n.points.slice(0,n.points.length-1):n.points).concat([o])}}(n,t.raw.touches[0].clientX))})}),Eo(An(),function(n,e){s.state.dialogSwipeState.on(function(t){u(f.get(n),-1*function(){if(0===t.points.length)return 0;var n=t.points[0].direction,e=t.points[t.points.length-1].direction;return-1===n&&-1===e?-1:1===n&&1===e?1:0}())})})])])})]}];function i(e,n,t){return Qs.sketch({dom:Oa('<span class="${prefix}-icon-'+n+' ${prefix}-icon"></span>'),action:function(n){X(n,a,{direction:e})},buttonBehaviours:or([md.config({disableClass:ci("toolbar-navigation-disabled"),disabled:function(){return!t}})])})}function u(n,r){var i=Jr(n.element,"."+ci("serialised-dialog-screen"));hi(n.element,"."+ci("serialised-dialog-chain")).each(function(o){0<=s.state.currentScreen.get()+r&&s.state.currentScreen.get()+r<i.length&&(Wr(o,"left").each(function(n){var e=parseInt(n,10),t=Ba(i[0]);$r(o,"left",e-r*t+"px")}),s.state.currentScreen.set(s.state.currentScreen.get()+r))})}function c(o){var n=Jr(o.element,"input");mn.from(n[s.state.currentScreen.get()]).each(function(n){o.getSystem().getByDom(n).each(function(n){var e=o,t=n.element;e.getSystem().triggerFocus(t,e.element)})});var e=l.get(o);Qi.highlightAt(e,s.state.currentScreen.get())}var n,a,s,f,l}),vd=qo([{key:Yn(),value:To({can:function(n,e){var t,o=e.event,r=o.originator,i=o.target;return!(un(t=r,n.element)&&!un(t,i)&&(console.warn(Yn()+" did not get interpreted by the desired target. \nOriginator: "+$o(r)+"\nTarget: "+$o(i)+"\nCheck the "+Yn()+" event handlers"),1))}})}]),bd=Object.freeze({__proto__:null,events:vd}),yd=K,xd=fl(),wd=function(n,c){var e,t,o,r,i,u,a=Me(n,function(r,u){return(1===r.length?ht.value(r[0].handler):gl(r,c,u)).map(function(n){var e,i,t=(i=m(e=n)?{can:x,abort:S,run:e}:e,function(n,e){for(var t=[],o=2;o<arguments.length;o++)t[o-2]=arguments[o];var r=[n,e].concat(t);i.abort.apply(void 0,r)?e.stop():i.can.apply(void 0,r)&&i.run.apply(void 0,r)}),o=1<r.length?E(c[u],function(e){return C(r,function(n){return n.name===e})}).join(" > "):r[0].name;return yo(u,{handler:t,purpose:o})})});return e={},t=[],o=[],M(a,function(n){n.fold(function(n){t.push(n)},function(n){o.push(n)})}),0<(u={errors:t,values:o}).errors.length?ht.error(V(u.errors)):(i=e,0===(r=u.values).length?ht.value(i):ht.value(co(i,ao.apply(void 0,r))))},Sd="alloy.base.behaviour",Od=lo([ho("dom","dom",Ot(),lo([Lt("tag"),Yt("styles",{}),Yt("classes",[]),Yt("attributes",{}),Ut("value"),Ut("innerHtml")])),Lt("components"),Lt("uid"),Yt("events",{}),Yt("apis",{}),ho("eventOrder","eventOrder",((Kl={})[Zn()]=["disabling",Sd,"toggling","typeaheadevents"],Kl[Yn()]=[Sd,"focusing","keying"],Kl[oe()]=[Sd,"disabling","toggling","representing"],Kl[$n()]=[Sd,"representing","streaming","invalidating"],Kl[ie()]=[Sd,"representing","item-events","tooltipping"],Kl[Nn()]=["focusing",Sd,"item-type-events"],Kl[Vn()]=["focusing",Sd,"item-type-events"],Kl[zn()]=["item-type-events","tooltipping"],Kl[Qn()]=["receiving","reflecting","tooltipping"],Et(w(Kl))),mo()),Ut("domModification")]),kd=qs,Td=function(u){return _e(u,Ls).getOrThunk(function(){var n,e,t,o,r,i=dt(u,"uid")?u:y({uid:kd("")},u);return n=yd(i),e=n.events,t=c(n,["events"]),o=Z(_e(t,"components").getOr([]),Td),r=y(y({},t),{events:y(y({},bd),e),components:o}),ht.value(xl(r)).getOrDie()})},Cd=ha,Ed="alloy.item-hover",Dd="alloy.item-focus",Md=w(Ed),_d=w(Dd),Rd=[Lt("data"),Lt("components"),Lt("dom"),Yt("hasSubmenu",!1),Ut("toggling"),Lc("itemBehaviours",[ii,fi,jc,hf]),Yt("ignoreFocus",!1),Yt("domModification",{}),Dr("builder",function(n){return{dom:n.dom,domModification:y(y({},n.domModification),{attributes:y(y(y({role:n.toggling.isSome()?"menuitemcheckbox":"menuitem"},n.domModification.attributes),{"aria-haspopup":n.hasSubmenu}),n.hasSubmenu?{"aria-expanded":!1}:{})}),behaviours:$c(n.itemBehaviours,[n.toggling.fold(ii.revoke,function(n){return ii.config(y({aria:{mode:"checked"}},n))}),fi.config({ignore:n.ignoreFocus,stopMousedown:n.ignoreFocus,onFocus:function(n){Ol(n)}}),jc.config({mode:"execution"}),hf.config({store:{mode:"memory",initialValue:n.data}}),el("item-type-events",u(u([],Pr(),!0),[Eo(zn(),Sl),Eo(ne(),fi.focus)],!1))]),components:n.components,eventOrder:n.eventOrder}}),Yt("eventOrder",{})],Fd=[Lt("dom"),Lt("components"),Dr("builder",function(n){return{dom:n.dom,components:n.components,events:qo([Eo(ne(),function(n,e){e.stop()})])}})],Id=w("item-widget"),Vd=w([sa({name:"widget",overrides:function(e){return{behaviours:or([hf.config({store:{mode:"manual",getValue:function(n){return e.data},setValue:Y}})])}}})]),Bd=jt("type",{widget:[Lt("uid"),Lt("data"),Lt("components"),Lt("dom"),Yt("autofocus",!1),Yt("ignoreFocus",!1),Lc("widgetBehaviours",[hf,fi,jc]),Yt("domModification",{}),pa(Vd()),Dr("builder",function(t){function o(n){return ma(n,t,"widget").map(function(n){return jc.focusIn(n),n})}function n(n,e){return eu(e.event.target)||t.autofocus&&e.setSource(n.element),mn.none()}var e=la(Id(),t,Vd()),r=da(Id(),t,e.internals());return{dom:t.dom,components:r,domModification:t.domModification,events:qo([Qo(function(n,e){o(n).each(function(n){e.stop()})}),Eo(zn(),Sl),Eo(ne(),function(n,e){t.autofocus?o(n):fi.focus(n)})]),behaviours:$c(t.widgetBehaviours,[hf.config({store:{mode:"memory",initialValue:t.data}}),fi.config({ignore:t.ignoreFocus,onFocus:function(n){Ol(n)}}),jc.config({mode:"special",focusIn:t.autofocus?function(n){o(n)}:ur(),onLeft:n,onRight:n,onEscape:function(n,e){return fi.isFocused(n)||t.autofocus?(t.autofocus&&e.setSource(n.element),mn.none()):(fi.focus(n),mn.some(!0))}})])}})],item:Rd,separator:Fd}),Ad=w([Ps({factory:{sketch:function(n){var e=zt("menu.spec item",Bd,n);return e.builder(e)}},name:"items",unit:"item",defaults:function(n,e){return dt(e,"uid")?e:y(y({},e),{uid:qs("item")})},overrides:function(n,e){return{type:e.type,ignoreFocus:n.fakeFocus,domModification:{classes:[n.markers.item]}}}})]),Nd=w([Lt("value"),Lt("items"),Lt("dom"),Lt("components"),Yt("eventOrder",{}),ju("menuBehaviours",[Qi,hf,ed,jc]),Kt("movement",{mode:"menu",moveOnTab:!0},jt("mode",{grid:[ei(),Dr("config",function(n,e){return{mode:"flatgrid",selector:"."+n.markers.item,initSize:{numColumns:e.initSize.numColumns,numRows:e.initSize.numRows},focusManager:n.focusManager}})],matrix:[Dr("config",function(n,e){return{mode:"matrix",selectors:{row:e.rowSelector,cell:"."+n.markers.item},focusManager:n.focusManager}}),Lt("rowSelector")],menu:[Yt("moveOnTab",!0),Dr("config",function(n,e){return{mode:"menu",selector:"."+n.markers.item,moveOnTab:e.moveOnTab,focusManager:n.focusManager}})]})),$t("markers",ni()),Yt("fakeFocus",!1),Yt("focusManager",Ii()),kr("onHighlight")]),Hd=w("alloy.menu-focus"),Pd=Sa({name:"Menu",configFields:Nd(),partFields:Ad(),factory:function(n,e,t,o){return{uid:n.uid,dom:n.dom,markers:n.markers,behaviours:Lu(n.menuBehaviours,[Qi.config({highlightClass:n.markers.selectedItem,itemClass:n.markers.item,onHighlight:n.onHighlight}),hf.config({store:{mode:"memory",initialValue:n.value}}),ed.config({find:mn.some}),jc.config(n.movement.config(n,n.movement))]),events:qo([Eo(_d(),function(e,t){var n=t.event;e.getSystem().getByDom(n.target).each(function(n){Qi.highlight(e,n),t.stop(),X(e,Hd(),{menu:e,item:n})})}),Eo(Md(),function(n,e){var t=e.event.item;Qi.highlight(n,t)})]),components:e,eventOrder:n.eventOrder,domModification:{attributes:{role:"menu"}}}}}),zd=function(n,e){return n.components()},jd=Ao({fields:[],name:"replacing",apis:Object.freeze({__proto__:null,append:function(n,e,t,o){kl(n,0,Qe,o)},prepend:function(n,e,t,o){kl(n,0,ye,o)},remove:Tl,replaceAt:Cl,replaceBy:function(e,n,t,o,r){return I(zd(e),o).bind(function(n){return Cl(e,0,0,n,r)})},set:function(i,n,e,u){var t,o,r,c;t=function(){var e,n,t,o,r=Z(u,i.getSystem().build);n=r,o=(e=i).components(),M((t=e).components(),function(n){return Ze(n.element)}),we(t.element),t.syncComponents(),M(H(o,n),function(n){ut(n),e.getSystem().removeFromWorld(n)}),M(n,function(n){n.getSystem().isConnected()?Te(e,n):(e.getSystem().addToWorld(n),Te(e,n),rt(e.element)&&ct(n)),e.syncComponents()})},o=i.element,r=tt(o),c=zo(r).bind(function(e){function n(n){return un(e,n)}var r,i;return n(o)?mn.some(o):(r=n,(i=function(n){for(var e=0;e<n.childNodes.length;e++){var t=fe.fromDom(n.childNodes[e]);if(r(t))return mn.some(t);var o=i(n.childNodes[e]);if(o.isSome())return o}return mn.none()})(o.dom))}),t(o),c.each(function(e){zo(r).filter(function(n){return un(n,e)}).fold(function(){Ho(e)},Y)})},contents:zd})}),Ld=function(t,o,r,n){return _e(r,n).bind(function(n){return _e(t,n).bind(function(n){var e=Ld(t,o,r,n);return mn.some([n].concat(e))})}).getOr([])},$d=function(){function c(u){return function(n){for(var e=st(n),t=0,o=e.length;t<o;t++){var r=e[t],i=n[r];if(i===u)return mn.some(i)}return mn.none()}(i.get())}function a(n){return e(n).bind(Yl)}function t(n){return _e(i.get(),n)}var i=No({}),u=No({}),s=No({}),f=cu(),l=No({}),e=function(n){return _e(u.get(),n)};return{setMenuBuilt:function(n,e){var t;u.set(y(y({},u.get()),((t={})[n]={type:"prepared",menu:e},t)))},setContents:function(n,e,t,o){f.set(n),i.set(t),u.set(e),l.set(o);var r=function(n,e){var t={};Ee(n,function(n,e){M(n,function(n){t[n]=e})});var o=e,r=lt(e,function(n,e){return{k:n,v:e}}),i=De(r,function(n,e){return[e].concat(Ld(t,o,r,e))});return De(t,function(n){return _e(i,n).getOr([n])})}(o,t);s.set(r)},expand:function(t){return _e(i.get(),t).map(function(n){var e=_e(s.get(),t).getOr([]);return[n].concat(e)})},refresh:function(n){return _e(s.get(),n)},collapse:function(n){return _e(s.get(),n).bind(function(n){return 1<n.length?mn.some(n.slice(1)):mn.none()})},lookupMenu:e,lookupItem:t,otherMenus:function(n){var e=l.get();return H(st(e),n)},getPrimary:function(){return f.get().bind(a)},getMenus:function(){return u.get()},clear:function(){i.set({}),u.set({}),s.set({}),f.clear()},isClear:function(){return f.get().isNone()},getTriggeringPath:function(n,u){var e=E(t(n).toArray(),function(n){return a(n).isSome()});return _e(s.get(),n).bind(function(n){var i=N(e.concat(n));return function(n){for(var e=[],t=0;t<n.length;t++){var o=n[t];if(!o.isSome())return mn.none();e.push(o.getOrDie())}return mn.some(e)}(B(i,function(n,e){return t=n,o=u,r=i.slice(0,e+1),a(t).bind(function(e){return c(t).bind(function(n){return o(n).map(function(n){return{triggeredMenu:e,triggeringItem:n,triggeringPath:r}})})}).fold(function(){return wo(f.get(),n)?[]:[mn.none()]},function(n){return[mn.some(n)]});var t,o,r}))})}}},Gd=Yl,Ud=w("collapse-item"),Wd=wa({name:"TieredMenu",configFields:[Er("onExecute"),Er("onEscape"),Cr("onOpenMenu"),Cr("onOpenSubmenu"),kr("onRepositionMenu"),kr("onCollapseMenu"),Yt("highlightImmediately",!0),Gt("data",[Lt("primary"),Lt("menus"),Lt("expansions")]),Yt("fakeFocus",!1),kr("onHighlight"),kr("onHover"),Gt("markers",[Lt("backgroundMenu")].concat(wr()).concat(Sr())),Lt("dom"),Yt("navigateOnHover",!0),Yt("stayInDom",!1),ju("tmenuBehaviours",[jc,Qi,ed,jd]),Yt("eventOrder",{})],apis:{collapseMenu:function(n,e){n.collapseMenu(e)},highlightPrimary:function(n,e){n.highlightPrimary(e)},repositionMenus:function(n,e){n.repositionMenus(e)}},factory:function(c,n){function t(n){var o,r,e=(o=n,r=c.data.primary,De(c.data.menus,function(n,e){function t(){return Pd.sketch(y(y({},n),{value:e,markers:c.markers,fakeFocus:c.fakeFocus,onHighlight:c.onHighlight,focusManager:(c.fakeFocus?Vi:Ii)()}))}return e===r?{type:"prepared",menu:o.getSystem().build(t())}:{type:"notbuilt",nbMenu:t}})),t=De(c.data.menus,function(n,e){return B(n.items,function(n){return"separator"===n.type?[]:[n.data.value]})});return m.setContents(c.data.primary,e,c.data.expansions,t),m.getPrimary()}function a(n){return hf.getValue(n).value}function i(e,n){Qi.highlight(e,n),Qi.getHighlighted(n).orThunk(function(){return Qi.getFirst(n)}).each(function(n){nn(e,n.element,ne())})}function u(e,n){return So(Z(n,function(n){return e.lookupMenu(n).bind(function(n){return"prepared"===n.type?mn.some(n.menu):mn.none()})}))}function s(e,n,t){M(u(n,n.otherMenus(t)),function(n){hl(n.element,[c.markers.backgroundMenu]),c.stayInDom||jd.remove(e,n)})}function f(n,o){var e;Ee((e=n,r.get().getOrThunk(function(){var t={};return M(E(Jr(e.element,"."+c.markers.item),function(n){return"true"===Be(n,"aria-haspopup")}),function(n){e.getSystem().getByDom(n).each(function(n){var e=a(n);t[e]=n})}),r.set(t),t})),function(n,e){var t=T(o,e);Ie(n.element,"aria-expanded",t)})}function l(t,o,r){return mn.from(r[0]).bind(function(n){return o.lookupMenu(n).bind(function(n){if("notbuilt"===n.type)return mn.none();var e=n.menu;return M(u(o,r.slice(1)),function(n){je(n.element,c.markers.backgroundMenu)}),rt(e.element)||jd.append(t,Cd(e)),hl(e.element,[c.markers.backgroundMenu]),i(t,e),s(t,o,r),mn.some(e)})})}var d,e,r=cu(),m=$d();function g(r,i,u){if(void 0===u&&(u=d.HighlightSubmenu),i.hasConfigured(md)&&md.isDisabled(i))return mn.some(i);var n=a(i);return m.expand(n).bind(function(o){return f(r,o),mn.from(o[0]).bind(function(t){return m.lookupMenu(t).bind(function(n){var e=function(n,e,t){if("notbuilt"!==t.type)return t.menu;var o=n.getSystem().build(t.nbMenu());return m.setMenuBuilt(e,o),o}(r,t,n);return rt(e.element)||jd.append(r,Cd(e)),c.onOpenSubmenu(r,i,e,N(o)),u===d.HighlightSubmenu?(Qi.highlightFirst(e),l(r,m,o)):(Qi.dehighlightAll(e),mn.some(i))})})})}function o(e,t){var n=a(t);return m.collapse(n).bind(function(n){return f(e,n),l(e,m,n).map(function(n){return c.onCollapseMenu(e,t,n),n})})}function p(t){return function(e,n){return vi(n.getSource(),"."+c.markers.item).bind(function(n){return e.getSystem().getByDom(n).toOptional().bind(function(n){return t(e,n).map(x)})})}}function h(n){return Qi.getHighlighted(n).bind(Qi.getHighlighted)}(e=d={})[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent";var v=qo([Eo(Hd(),function(t,o){var n=o.event.item;m.lookupItem(a(n)).each(function(){var n=o.event.menu;Qi.highlight(t,n);var e=a(o.event.item);m.refresh(e).each(function(n){return s(t,m,n)})})}),Qo(function(e,n){var t=n.event.target;e.getSystem().getByDom(t).each(function(n){0===a(n).indexOf("collapse-item")&&o(e,n),g(e,n,d.HighlightSubmenu).fold(function(){c.onExecute(e,n)},Y)})}),Yo(function(e,n){t(e).each(function(n){jd.append(e,Cd(n)),c.onOpenMenu(e,n),c.highlightImmediately&&i(e,n)})})].concat(c.navigateOnHover?[Eo(Md(),function(n,e){var t=e.event.item,o=n,r=a(t);m.refresh(r).bind(function(n){return f(o,n),l(o,m,n)}),g(n,t,d.HighlightParent),c.onHover(n,t)})]:[])),b={collapseMenu:function(e){h(e).each(function(n){o(e,n)})},highlightPrimary:function(e){m.getPrimary().each(function(n){i(e,n)})},repositionMenus:function(o){m.getPrimary().bind(function(e){return h(o).bind(function(n){var e=a(n),t=So(Z(Me(m.getMenus(),K),Gd));return m.getTriggeringPath(e,function(n){return e=n,z(t,function(n){return n.getSystem().isConnected()?F(Qi.getCandidates(n),function(n){return a(n)===e}):mn.none()});var e})}).map(function(n){return{primary:e,triggeringPath:n}})}).fold(function(){mn.from(o.components()[0]).filter(function(n){return"menu"===Be(n.element,"role")}).each(function(n){c.onRepositionMenu(o,n,[])})},function(n){var e=n.primary,t=n.triggeringPath;c.onRepositionMenu(o,e,t)})}};return{uid:c.uid,dom:c.dom,markers:c.markers,behaviours:Lu(c.tmenuBehaviours,[jc.config({mode:"special",onRight:p(function(n,e){return eu(e.element)?mn.none():g(n,e,d.HighlightSubmenu)}),onLeft:p(function(n,e){return eu(e.element)?mn.none():o(n,e)}),onEscape:p(function(n,e){return o(n,e).orThunk(function(){return c.onEscape(n,e).map(function(){return n})})}),focusIn:function(e,n){m.getPrimary().each(function(n){nn(e,n.element,ne())})}}),Qi.config({highlightClass:c.markers.selectedMenu,itemClass:c.markers.menu}),ed.config({find:function(n){return Qi.getHighlighted(n)}}),jd.config({})]),eventOrder:c.eventOrder,apis:b,events:v}},extraApis:{tieredData:function(n,e,t){return{primary:n,menus:e,expansions:t}},singleData:function(n,e){return{primary:n,menus:yo(n,e),expansions:{}}},collapseItem:function(n){return{value:Gu(Ud()),meta:{text:n}}}}}),Xd=function(n,e,t,o){return El(0,e,0,o).bind(function(e){return e.transition.map(function(n){return{transition:n,route:e}})})},qd=function(e,t,n){return Ae(e.element,t.destinationAttr).map(function(n){return{start:Be(e.element,t.stateAttr),destination:n}})},Yd=Object.freeze({__proto__:null,findRoute:El,disableTransition:Dl,getCurrentRoute:qd,jumpTo:Ml,progressTo:function(t,o,r,i){var e,u=o;Ne((e=t).element,u.destinationAttr)&&(Ae(e.element,u.destinationAttr).each(function(n){Ie(e.element,u.stateAttr,n)}),He(e.element,u.destinationAttr));var n,c=(n=i,{start:Be(t.element,o.stateAttr),destination:n});Xd(t,o,r,c).fold(function(){Ml(t,o,r,i)},function(n){Dl(t,o,r);var e=n.transition;je(t.element,e.transitionClass),Ie(t.element,o.destinationAttr,i)})},getState:function(n,e,t){return Ae(n.element,e.stateAttr)}}),Kd=Object.freeze({__proto__:null,events:function(r,i){return qo([Eo(Wn(),function(t,n){var o=n.event.raw;qd(t,r).each(function(e){El(0,r,0,e).each(function(n){n.transition.each(function(n){o.propertyName===n.property&&(Ml(t,r,i,e.destination),r.onTransition(t,e))})})})}),Yo(function(n,e){Ml(n,r,i,r.initialState)})])}}),Jd=Ao({fields:[Yt("destinationAttr","data-transitioning-destination"),Yt("stateAttr","data-transitioning-state"),Lt("initialState"),kr("onTransition"),kr("onFinish"),$t("routes",Nt(ht.value,Nt(ht.value,It([qt("transition",[Lt("property"),Lt("transitionClass")])]))))],name:"transitioning",active:Kd,apis:Yd,extra:{createRoutes:function(n){var o={};return Ee(n,function(n,e){var t=e.split("<->");o[t[0]]=yo(t[1],n),o[t[1]]=yo(t[0],n)}),o},createBistate:function(n,e,t){return xo([{key:n,value:yo(e,t)},{key:e,value:yo(n,t)}])},createTristate:function(n,e,t,o){return xo([{key:n,value:xo([{key:e,value:o},{key:t,value:o}])},{key:e,value:xo([{key:n,value:o},{key:t,value:o}])},{key:t,value:xo([{key:n,value:o},{key:e,value:o}])}])}}}),Qd=ci("scrollable"),Zd=Qd,nm=function(n,e,t,o,r){return{data:{value:n,text:e},type:"item",dom:{tag:"div",classes:r?[ci("styles-item-is-menu")]:[]},toggling:{toggleOnExecute:!1,toggleClass:ci("format-matches"),selected:t},itemBehaviours:or(r?[]:[Ar(n,function(n,e){(e?ii.on:ii.off)(n)})]),components:[{dom:{tag:"div",attributes:{style:o},innerHtml:e}}]}},em=function(n,e,t,o){return{value:n,dom:{tag:"div"},components:[Qs.sketch({dom:{tag:"div",classes:[ci("styles-collapser")]},components:o?[{dom:{tag:"span",classes:[ci("styles-collapse-icon")]}},wl(n)]:[wl(n)],action:function(n){var e;o&&(e=t().get(n),Wd.collapseMenu(e))}}),{dom:{tag:"div",classes:[ci("styles-menu-items-container")]},components:[Pd.parts.items({})],behaviours:or([el("adhoc-scrollable-menu",[Yo(function(n,e){$r(n.element,"overflow-y","auto"),$r(n.element,"-webkit-overflow-scrolling","touch"),_l(n.element)}),Ko(function(n){Xr(n.element,"overflow-y"),Xr(n.element,"-webkit-overflow-scrolling"),Rl(n.element)})])])}],items:e,menuBehaviours:or([Jd.config({initialState:"after",routes:Jd.createTristate("before","current","after",{transition:{property:"transform",transitionClass:"transitioning"}})})])}},tm=function(n){return _(n,function(n,e){var t=Il(e);return{menus:co(n.menus,t.menus),items:[t.item].concat(n.items),expansions:co(n.expansions,t.expansions)}},{menus:{},expansions:{},items:[]})},om=function(n){return B(n,function(n){return(D(n)?om:Bl)(n)})},rm=function(n,e,t,o){n.dom.removeEventListener(e,t,o)},im=x,um=tinymce.util.Tools.resolve("tinymce.util.Delay"),cm=Jt([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]);function am(n,e){var t=le(n);return"input"===t?mg.after(n):T(["br","img"],t)?0===e?mg.before(n):mg.after(n):mg.on(n,e)}function sm(n){return mn.from(n.getSelection())}function fm(n,e,t,o,r){var i=Gl(n,e,t,o,r);sm(n).each(function(n){n.removeAllRanges(),n.addRange(i)})}function lm(n,e,t,o,r){var i,u,c,a,s,f=(i=o,u=r,c=am(e,t),a=am(i,u),pg.relative(c,a));Xl(s=n,f).match({ltr:function(n,e,t,o){fm(s,n,e,t,o)},rtl:function(r,i,u,c){sm(s).each(function(n){if(n.setBaseAndExtent)n.setBaseAndExtent(r.dom,i,u.dom,c);else if(n.extend)try{t=u,o=c,(e=n).collapse(r.dom,i),e.extend(t.dom,o)}catch(n){fm(s,u,c,r,i)}else fm(s,u,c,r,i);var e,t,o})}})}function dm(n){if(0<n.rangeCount){var e=n.getRangeAt(0),t=n.getRangeAt(n.rangeCount-1);return mn.some(fg(fe.fromDom(e.startContainer),e.startOffset,fe.fromDom(t.endContainer),t.endOffset))}return mn.none()}function mm(n){if(null===n.anchorNode||null===n.focusNode)return dm(n);var e,t,o,r,i,u,c,a,s,f,l,d=fe.fromDom(n.anchorNode),m=fe.fromDom(n.focusNode);return e=d,t=n.anchorOffset,o=m,r=n.focusOffset,u=t,c=o,a=r,(s=me(i=e).dom.createRange()).setStart(i.dom,u),s.setEnd(c.dom,a),f=s,l=un(e,o)&&t===r,f.collapsed&&!l?mn.some(fg(d,n.anchorOffset,m,n.focusOffset)):dm(n)}function gm(n){return sm(n).filter(function(n){return 0<n.rangeCount}).bind(mm)}function pm(n,e){var i,t,o,r=(t=Xl(i=n,e).match({ltr:function(n,e,t,o){var r=i.document.createRange();return r.setStart(n.dom,e),r.setEnd(t.dom,o),r},rtl:function(n,e,t,o){var r=i.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,e),r}})).getClientRects();return 0<(o=0<r.length?r[0]:t.getBoundingClientRect()).width||0<o.height?mn.some(o).map(Ul):mn.none()}function hm(n){return mn.from(n.dom.contentWindow)}function vm(n){return hm(n).bind(gm)}function bm(n){return n.getFrame()}function ym(n,e,t,o){return n[t].getOrThunk(function(){return function(n){return Pl(e,o,n)}})}function xm(c){var a=bm(c);return mn.some(fe.fromDom(a.dom.contentWindow.document.body)).bind(function(u){return mn.some(fe.fromDom(a.dom.contentWindow.document)).bind(function(i){return hm(a).map(function(r){var n=fe.fromDom(i.dom.documentElement),e=c.getCursorBox.getOrThunk(function(){return function(){return gm(r).map(function(n){return pg.exact(n.start,n.soffset,n.finish,n.foffset)}).bind(function(n){return pm(r,n).orThunk(function(){return gm(r).filter(function(n){return un(n.start,n.finish)&&n.soffset===n.foffset}).bind(function(n){var e=n.start.dom.getBoundingClientRect();return 0<e.width||0<e.height?mn.some(e):mn.none()})})})}}),t=c.setSelection.getOrThunk(function(){return function(n,e,t,o){lm(r,n,e,t,o)}}),o=c.clearSelection.getOrThunk(function(){return function(){sm(r).each(function(n){return n.removeAllRanges()})}});return{body:u,doc:i,win:r,html:n,getSelection:J(vm,a),setSelection:t,clearSelection:o,frame:a,onKeyup:ym(c,i,"onKeyup","keyup"),onNodeChanged:ym(c,i,"onNodeChanged","SelectionChange"),onDomChanged:c.onDomChanged,onScrollToCursor:c.onScrollToCursor,onScrollToElement:c.onScrollToElement,onToReading:c.onToReading,onToEditing:c.onToEditing,onToolbarScrollStart:c.onToolbarScrollStart,onTouchContent:c.onTouchContent,onTapContent:c.onTapContent,onTouchToolstrip:c.onTouchToolstrip,getCursorBox:e}})})})}function wm(){var e=rn("head").getOrDie(),n=rn('meta[name="viewport"]').getOrThunk(function(){var n=fe.fromTag("meta");return Ie(n,"name","viewport"),Qe(e,n),n}),t=Be(n,"content");return{maximize:function(){Ie(n,"content","width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0")},restore:function(){null!=t&&0<t.length?Ie(n,"content",t):Ie(n,"content","user-scalable=yes")}}}function Sm(n,e){function t(o){return function(n){var e=Be(n,"style"),t=void 0===e?"no-styles":e.trim();t===o||(Ie(n,vg,t),Ie(n,"style",o))}}var o,r,i=(r=function(n){return en(n,"*")},E(function(n,e){for(var t=m(e)?e:S,o=n.dom,r=[];null!==o.parentNode&&void 0!==o.parentNode;){var i=o.parentNode,u=fe.fromDom(i);if(r.push(u),!0===t(u))break;o=i}return r}(n,void 0),r)),u=B(i,function(n){return e=function(n){return en(n,"*")},E(pe(t=n).map(Je).map(function(n){return E(n,function(n){return!un(t,n)})}).getOr([]),e);var e,t}),c=void 0!==(o=Ur(e,"background-color"))&&""!==o?"background-color:"+o+"!important":"background-color:rgb(255,255,255)!important;";M(u,t("display:none!important;")),M(i,t(bg+yg+c)),t((!0===xg?"":bg)+yg+c)(n)}function Om(){M(on("["+vg+"]"),function(n){var e=Be(n,vg);"no-styles"!==e?Ie(n,"style",e):He(n,"style"),He(n,vg)})}function km(n){var e=n.raw;return void 0===e.touches||1!==e.touches.length?mn.none():mn.some(e.touches[0])}function Tm(t){var o,r,u=cu(),i=No(!1),c=(o=function(n){t.triggerEvent(te(),n),i.set(!0)},r=null,{cancel:function(){null!==r&&(clearTimeout(r),r=null)},schedule:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];r=setTimeout(function(){o.apply(null,n),r=null},400)}}),a=xo([{key:Vn(),value:function(t){return km(t).each(function(n){c.cancel();var e={x:n.clientX,y:n.clientY,target:t.target};c.schedule(t),i.set(!1),u.set(e)}),mn.none()}},{key:Bn(),value:function(n){return c.cancel(),km(n).each(function(i){u.on(function(n){var e=i,t=n,o=Math.abs(e.clientX-t.x),r=Math.abs(e.clientY-t.y);(5<o||5<r)&&u.clear()})}),mn.none()}},{key:An(),value:function(e){return c.cancel(),u.get().filter(function(n){return un(n.target,e.target)}).map(function(n){return i.get()?(e.prevent(),!1):t.triggerEvent(ee(),e)})}}]);return{fireIfReady:function(e,n){return _e(a,n).bind(function(n){return n(e)})}}}function Cm(t){var e=Tm({triggerEvent:function(n,e){t.onTapContent(e)}});return{fireTouchstart:function(n){e.fireIfReady(n,"touchstart")},onTouchend:function(){return Pl(t.body,"touchend",function(n){e.fireIfReady(n,"touchend")})},onTouchmove:function(){return Pl(t.body,"touchmove",function(n){e.fireIfReady(n,"touchmove")})}}}function Em(n,e){var t=parseInt(Be(n,e),10);return isNaN(t)?0:t}function Dm(n){return y(y({},n),{width:2})}function Mm(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom,width:n.width,height:n.height}}function _m(n){var e=n.getSelection();return void 0!==e&&0<e.rangeCount?function(o){if(o.collapsed){var r=fe.fromDom(o.startContainer);return pe(r).bind(function(n){var e,t=pg.exact(r,o.startOffset,n,"img"===le(e=n)?1:lg.getOption(e).fold(function(){return Je(e).length},function(n){return n.length}));return pm(o.startContainer.ownerDocument.defaultView,t).map(Dm).map(P)}).getOr([])}return Z(o.getClientRects(),Mm)}(e.getRangeAt(0)):[]}function Rm(n,e){Ie(n,Sg,e)}function Fm(n){return{top:n.top,bottom:n.top+n.height}}function Im(o,r){var i=fe.fromDom(r.document.body),n=Pl(fe.fromDom(o),"resize",function(){var n,e,t;n=o,e=Em(i,Sg),((t=n.innerHeight)<e?mn.some(e-t):mn.none()).each(function(t){var n;(0<(n=_m(r)).length?mn.some(n[0]).map(Fm):mn.none()).each(function(n){var e=n.top>r.innerHeight||n.bottom>r.innerHeight?Math.min(t,n.bottom-r.innerHeight+50):0;0!==e&&r.scrollTo(r.pageXOffset,r.pageYOffset+e)})}),Rm(i,o.innerHeight)});return Rm(i,o.innerHeight),{toEditing:function(){!function(n){n.focus();var e=fe.fromDom(n.document.body);(zo().exists(function(n){return T(["input","textarea"],le(n))})?function(n){um.setTimeout(function(){n()},0)}:p)(function(){zo().each(Po),Ho(e)})}(r)},destroy:function(){n.unbind()}}}function Vm(t,o){function r(){s(i)||(clearTimeout(i),i=null)}var i=null;return{cancel:r,throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];r(),i=setTimeout(function(){i=null,t.apply(null,n)},o)}}}function Bm(n){var t,o,e=Ns(td.sketch({dom:Oa('<div aria-hidden="true" class="${prefix}-mask-tap-icon"></div>'),containerBehaviours:or([ii.config({toggleClass:ci("mask-tap-icon-selected"),toggleOnExecute:!1})])})),r=(t=n,o=null,{cancel:function(){s(o)||(clearTimeout(o),o=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];s(o)&&(o=setTimeout(function(){o=null,t.apply(null,n)},200))}});return td.sketch({dom:Oa('<div class="${prefix}-disabled-mask"></div>'),components:[td.sketch({dom:Oa('<div class="${prefix}-content-container"></div>'),components:[Qs.sketch({dom:Oa('<div class="${prefix}-content-tap-section"></div>'),components:[e.asSpec()],action:function(n){r.throttle()},buttonBehaviours:or([ii.config({toggleClass:ci("mask-tap-icon-selected")})])})]})]})}function Am(n){var e=zt("Getting AndroidWebapp schema",kg,n);$r(e.toolstrip,"width","100%");var t=Td(Bm(function(){e.setReadOnly(e.readOnlyOnInit()),c.enter()},e.translate));e.alloy.add(t);var o={show:function(){e.alloy.add(t)},hide:function(){e.alloy.remove(t)}};Qe(e.container,t.element);var s,r,i,u,f,c=(s=e,r=o,i=wm(),u=uu(),f=uu(),{enter:function(){r.hide(),je(s.container,ci("fullscreen-maximized")),je(s.container,ci("android-maximized")),i.maximize(),je(s.body,ci("android-scroll-reload")),u.set(Im(s.win,hg(s.editor).getOrDie("no"))),xm(s.editor).each(function(n){function e(n){return!un(n.start,n.finish)||n.soffset!==n.foffset}function t(){var n=o.doc.dom.hasFocus()&&o.getSelection().exists(e);i.getByDom(r).each(!0===(n||zo(c).filter(function(n){return"input"===le(n)}).exists(function(n){return n.dom.selectionStart!==n.dom.selectionEnd}))?ii.on:ii.off)}var o,r,i,u,c,a;Sm(s.container,n.body),f.set((o=n,r=s.toolstrip,i=s.alloy,u=Cm(o),c=me(r),a=[Pl(o.body,"touchstart",function(n){o.onTouchContent(),u.fireTouchstart(n)}),u.onTouchmove(),u.onTouchend(),Pl(r,"touchstart",function(n){o.onTouchToolstrip()}),o.onToReading(function(){Po(o.body)}),o.onToEditing(Y),o.onScrollToCursor(function(n){n.preventDefault(),o.getCursorBox().each(function(n){var e=o.win,t=n.top>e.innerHeight||n.bottom>e.innerHeight?n.bottom-e.innerHeight+50:0;0!=t&&e.scrollTo(e.pageXOffset,e.pageYOffset+t)})})].concat(1==wg?[]:[Pl(fe.fromDom(o.win),"blur",function(){i.getByDom(r).each(ii.off)}),Pl(c,"select",t),Pl(o.doc,"selectionchange",t)]),{destroy:function(){M(a,function(n){n.unbind()})}}))})},exit:function(){i.restore(),r.show(),Le(s.container,ci("fullscreen-maximized")),Le(s.container,ci("android-maximized")),Om(),Le(s.body,ci("android-scroll-reload")),f.clear(),u.clear()}});return{setReadOnly:e.setReadOnly,refreshStructure:Y,enter:c.enter,exit:c.exit,destroy:Y}}function Nm(n){return"true"===Be(n,Rg)?0<n.dom.scrollLeft||function(n){n.dom.scrollLeft=1;var e=0!==n.dom.scrollLeft;return n.dom.scrollLeft=0,e}(n):0<n.dom.scrollTop||function(n){n.dom.scrollTop=1;var e=0!==n.dom.scrollTop;return n.dom.scrollTop=0,e}(n)}function Hm(){function e(n){var e=!0===n.scrollable?"${prefix}-toolbar-scrollable-group":"";return{dom:Oa('<div aria-label="'+n.label+'" class="${prefix}-toolbar-group '+e+'"></div>'),tgroupBehaviours:or([el("adhoc-scrollable-toolbar",!0===n.scrollable?[Jo(function(n,e){$r(n.element,"overflow-x","auto"),Ie(n.element,Rg,"true"),_l(n.element)})]:[])]),components:[td.sketch({components:[_g.parts.items({})]})],markers:{itemSelector:"."+ci("toolbar-group-item")},items:n.items}}function t(){Eg.setGroups(o,r.get()),ii.off(o)}var o=Td(Eg.sketch({dom:Oa('<div class="${prefix}-toolbar"></div>'),components:[Eg.parts.groups({})],toolbarBehaviours:or([ii.config({toggleClass:ci("context-toolbar"),toggleOnExecute:!1,aria:{mode:"none"}}),jc.config({mode:"cyclic"})]),shell:!0})),n=Td(td.sketch({dom:{classes:[ci("toolstrip")]},components:[Cd(o)],containerBehaviours:or([ii.config({toggleClass:ci("android-selection-context-toolbar"),toggleOnExecute:!1})])})),r=No([]);return{wrapper:n,toolbar:o,createGroups:function(n){return Z(n,i(_g.sketch,e))},setGroups:function(n){r.set(n),t()},setContextToolbar:function(n){ii.on(o),Eg.setGroups(o,n)},restoreToolbar:function(){ii.isOn(o)&&t()},refresh:function(){},focus:function(){jc.focusIn(o)}}}function Pm(n){return Td(Qs.sketch({dom:Oa('<div class="${prefix}-mask-edit-icon ${prefix}-icon"></div>'),action:function(){n.run(function(n){n.setReadOnly(!1)})}}))}function zm(){return Td(td.sketch({dom:Oa('<div class="${prefix}-editor-socket"></div>'),components:[],containerBehaviours:or([jd.config({})])}))}function jm(n,e,t,o){(!0===t?cr.toAlpha:cr.toOmega)(o),(t?function(n,e){jd.append(n,Cd(e))}:function(n,e){jd.remove(n,e)})(n,e)}function Lm(e,n){return n.getAnimationRoot.fold(function(){return e.element},function(n){return n(e)})}function $m(n){return n.dimension.property}function Gm(n,e){return n.dimension.getDimension(e)}function Um(n,e){hl(Lm(n,e),[e.shrinkingClass,e.growingClass])}function Wm(n,e){Le(n.element,e.openClass),je(n.element,e.closedClass),$r(n.element,$m(e),"0px"),qr(n.element)}function Xm(n,e){Le(n.element,e.closedClass),je(n.element,e.openClass),Xr(n.element,$m(e))}function qm(n,e,t,o){t.setCollapsed(),$r(n.element,$m(e),Gm(e,n.element)),qr(n.element),Um(n,e),Wm(n,e),e.onStartShrink(n),e.onShrunk(n)}function Ym(n,e,t){var o=Gm(e,n.element);("0px"===o?qm:function(n,e,t,o){var r=o.getOrThunk(function(){return Gm(e,n.element)});t.setCollapsed(),$r(n.element,$m(e),r),qr(n.element);var i=Lm(n,e);Le(i,e.growingClass),je(i,e.shrinkingClass),Wm(n,e),e.onStartShrink(n)})(n,e,t,mn.some(o))}function Km(n,e,t){var o=Lm(n,e),r=$e(o,e.shrinkingClass),i=Gm(e,n.element);Xm(n,e);var u=Gm(e,n.element);(r?function(){$r(n.element,$m(e),i),qr(n.element)}:function(){Wm(n,e)})(),Le(o,e.shrinkingClass),je(o,e.growingClass),Xm(n,e),$r(n.element,$m(e),u),t.setExpanded(),e.onStartGrow(n)}function Jm(n,e,t){return!0===$e(Lm(n,e),e.growingClass)}function Qm(n,e,t){return!0===$e(Lm(n,e),e.shrinkingClass)}function Zm(e,t){var o=Td(td.sketch({dom:{tag:"div",classes:[ci("dropup")]},components:[],containerBehaviours:or([jd.config({}),Vg.config({closedClass:ci("dropup-closed"),openClass:ci("dropup-open"),shrinkingClass:ci("dropup-shrinking"),growingClass:ci("dropup-growing"),dimension:{property:"height"},onShrunk:function(n){e(),t(),jd.set(n,[])},onGrown:function(n){e(),t()}}),Nr(function(n,e){r(Y)})])})),r=function(n){window.requestAnimationFrame(function(){n(),Vg.shrink(o)})};return{appear:function(n,e,t){!0===Vg.hasShrunk(o)&&!1===Vg.isTransitioning(o)&&window.requestAnimationFrame(function(){e(t),jd.set(o,[n()]),Vg.grow(o)})},disappear:r,component:o,element:o.element}}function ng(){return Fn().browser.isFirefox()}function eg(e,n){var t,o,r,i,u=y({stopBackspace:!0},n),c=Tm(u),a=Z(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","transitioncancel","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),function(n){return Pl(e,n,function(e){c.fireIfReady(e,n).each(function(n){n&&e.kill()}),u.triggerEvent(n,e)&&e.kill()})}),s=cu(),f=Pl(e,"paste",function(e){c.fireIfReady(e,"paste").each(function(n){n&&e.kill()}),u.triggerEvent("paste",e)&&e.kill(),s.set(setTimeout(function(){u.triggerEvent(Jn(),e)},0))}),l=Pl(e,"keydown",function(n){var e;u.triggerEvent("keydown",n)?n.kill():!u.stopBackspace||((e=n).raw.which!==ji[0]||T(["input","textarea"],le(e.target))||vi(e.target,'[contenteditable="true"]',void 0).isSome())||n.prevent()}),d=(t=e,o=function(n){u.triggerEvent("focusin",n)&&n.kill()},ng()?zl(t,"focus",o):Pl(t,"focusin",o)),m=cu(),g=(r=e,i=function(n){u.triggerEvent("focusout",n)&&n.kill(),m.set(setTimeout(function(){u.triggerEvent(Kn(),n)},0))},ng()?zl(r,"blur",i):Pl(r,"focusout",i));return{unbind:function(){M(a,function(n){n.unbind()}),l.unbind(),d.unbind(),g.unbind(),f.unbind(),s.on(clearTimeout),m.on(clearTimeout)}}}function tg(n,e){return No(_e(n,"target").getOr(e))}function og(n,o,e,t,r,i){var u,c,a=n(o,t),s=(u=No(!1),c=No(!1),{stop:function(){u.set(!0)},cut:function(){c.set(!0)},isStopped:u.get,isCut:c.get,event:e,setSource:r.set,getSource:r.get});return a.fold(function(){return i.logEventNoHandlers(o,t),Bg.complete()},function(e){var t=e.descHandler;return dl(t)(s),s.isStopped()?(i.logEventStopped(o,e.element,t.purpose),Bg.stopped()):s.isCut()?(i.logEventCut(o,e.element,t.purpose),Bg.complete()):pe(e.element).fold(function(){return i.logNoParent(o,e.element,t.purpose),Bg.complete()},function(n){return i.logEventResponse(o,e.element,t.purpose),Bg.resume(n)})})}function rg(){var u={};return{registerId:function(r,i,n){Ee(n,function(n,e){var t,o=void 0!==u[e]?u[e]:{};o[i]={cHandler:J.apply(void 0,[(t=n).handler].concat(r)),purpose:t.purpose},u[e]=o})},unregisterId:function(t){Ee(u,function(n,e){dt(n,t)&&delete n[t]})},filterByType:function(n){return _e(u,n).map(function(n){return Me(n,function(n,e){return{id:e,descHandler:n}})}).getOr([])},find:function(r,n,c){return _e(u,n).bind(function(o){return e=r,t=(n=function(n){return e=o,ba(t=n).bind(function(n){return _e(e,n)}).map(function(n){return{element:t,descHandler:n}});var e,t})(i=c),u=Oo(e),t.orThunk(function(){return u(i)?mn.none():function(n){for(var e=i.dom,t=Oo(u);e.parentNode;){var e=e.parentNode,o=fe.fromDom(e),r=n(o);if(r.isSome())return r;if(t(o))break}return mn.none()}(n)});var i,n,e,t,u})}}}function ig(){function r(n){ba(n.element).each(function(n){delete u[n],i.unregisterId(n)})}var i=rg(),u={};return{find:function(n,e,t){return i.find(n,e,t)},filter:function(n){return i.filterByType(n)},register:function(n){var t,o=ba((t=n).element).getOrThunk(function(){return n=t.element,e=Gu(Us+"uid-"),Xs(n,e),e;var n,e});Re(u,o)&&function(n){var e=u[o];if(e!==n)throw new Error('The tagId "'+o+'" is already used by: '+$o(e.element)+"\nCannot use it for: "+$o(n.element)+"\nThe conflicting element is"+(rt(e.element)?" ":" not ")+"already in the DOM");r(n)}(n),i.registerId([n],o,n.events),u[o]=n},unregister:r,getById:function(n){return _e(u,n)}}}cm.ltr,cm.rtl;function ug(n,e,t,o,r){var i=tg(t,o);return Ag(n,e,t,o,i,r)}var cg,ag,sg,fg=function(n,e,t,o){return{start:n,soffset:e,finish:t,foffset:o}},lg=(cg=qe,{get:function(n){if(!cg(n))throw new Error("Can only get text value of a text node");return ag(n).getOr("")},getOption:ag=function(n){return cg(n)?mn.from(n.dom.nodeValue):mn.none()},set:function(n,e){if(!cg(n))throw new Error("Can only set raw text value of a text node");n.dom.nodeValue=e}}),dg=Jt([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),mg={before:dg.before,on:dg.on,after:dg.after,cata:function(n,e,t,o){return n.fold(e,t,o)},getStart:function(n){return n.fold(K,K,K)}},gg=Jt([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),pg={domRange:gg.domRange,relative:gg.relative,exact:gg.exact,exactFromRange:function(n){return gg.exact(n.start,n.soffset,n.finish,n.foffset)},getWin:function(n){var e=n.match({domRange:function(n){return fe.fromDom(n.startContainer)},relative:function(n,e){return mg.getStart(n)},exact:function(n,e,t,o){return n}});return fe.fromDom(ge(e).dom.defaultView)},range:fg},hg=(sg=hm,function(e){return e.getWin.getOrThunk(function(){var n=bm(e);return function(){return sg(n)}})()}),vg="data-ephox-mobile-fullscreen-style",bg="position:absolute!important;",yg="top:0!important;left:0!important;margin:0!important;padding:0!important;width:100%!important;height:100%!important;overflow:visible!important;",xg=Fn().os.isAndroid(),wg=6<=Fn().os.version.major,Sg="data-"+ci("last-outer-height"),Og=w({unbind:Y}),kg=lo([Gt("editor",[Lt("getFrame"),Ut("getBody"),Ut("getDoc"),Ut("getWin"),Ut("getSelection"),Ut("setSelection"),Ut("clearSelection"),Ut("cursorSaver"),Ut("onKeyup"),Ut("onNodeChanged"),Ut("getCursorBox"),Lt("onDomChanged"),Yt("onTouchContent",Y),Yt("onTapContent",Y),Yt("onTouchToolstrip",Y),Yt("onScrollToCursor",Og),Yt("onScrollToElement",Og),Yt("onToEditing",Og),Yt("onToReading",Og),Yt("onToolbarScrollStart",K)]),Lt("socket"),Lt("toolstrip"),Lt("dropup"),Lt("toolbar"),Lt("container"),Lt("alloy"),vo("win",function(n){return me(n.socket).dom.defaultView}),vo("body",function(n){return fe.fromDom(n.socket.dom.ownerDocument.body)}),Yt("translate",K),Yt("setReadOnly",Y),Yt("readOnlyOnInit",x)]),Tg=w([Lt("dom"),Yt("shell",!0),ju("toolbarBehaviours",[jd])]),Cg=w([Hs({name:"groups",overrides:function(){return{behaviours:or([jd.config({})])}}})]),Eg=Sa({name:"Toolbar",configFields:Tg(),partFields:Cg(),factory:function(o,n,e,t){var r=o.shell?{behaviours:[jd.config({})],components:[]}:{behaviours:[],components:n};return{uid:o.uid,dom:o.dom,components:r.components,behaviours:Lu(o.toolbarBehaviours,r.behaviours),apis:{setGroups:function(n,e){var t;t=n,(o.shell?mn.some(t):ma(t,o,"groups")).fold(function(){throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(n){jd.set(n,e)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(n,e,t){n.setGroups(e,t)}}}),Dg=w([Lt("items"),Gt("markers",Z(["itemSelector"],Lt)),ju("tgroupBehaviours",[jc])]),Mg=w([Ps({name:"items",unit:"item"})]),_g=Sa({name:"ToolbarGroup",configFields:Dg(),partFields:Mg(),factory:function(n,e,t,o){return{uid:n.uid,dom:n.dom,components:e,behaviours:Lu(n.tgroupBehaviours,[jc.config({mode:"flow",selector:n.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}}}}),Rg="data-"+ci("horizontal-scroll"),Fg=Object.freeze({__proto__:null,refresh:function(n,e,t){var o;t.isExpanded()&&(Xr(n.element,$m(e)),o=Gm(e,n.element),$r(n.element,$m(e),o))},grow:function(n,e,t){t.isExpanded()||Km(n,e,t)},shrink:function(n,e,t){t.isExpanded()&&Ym(n,e,t)},immediateShrink:function(n,e,t){t.isExpanded()&&qm(n,e,t)},hasGrown:function(n,e,t){return t.isExpanded()},hasShrunk:function(n,e,t){return t.isCollapsed()},isGrowing:Jm,isShrinking:Qm,isTransitioning:function(n,e,t){return Jm(n,e)||Qm(n,e)},toggleGrow:function(n,e,t){(t.isExpanded()?Ym:Km)(n,e,t)},disableTransitions:Um}),Ig=Object.freeze({__proto__:null,exhibit:function(n,e,t){return Io(e.expanded?{classes:[e.openClass],styles:{}}:{classes:[e.closedClass],styles:yo(e.dimension.property,"0px")})},events:function(t,o){return qo([Mo(Wn())(function(n,e){e.event.raw.propertyName===t.dimension.property&&(Um(n,t),o.isExpanded()&&Xr(n.element,t.dimension.property),(o.isExpanded()?t.onGrown:t.onShrunk)(n))})])}}),Vg=Ao({fields:[Lt("closedClass"),Lt("openClass"),Lt("shrinkingClass"),Lt("growingClass"),Ut("getAnimationRoot"),kr("onShrunk"),kr("onStartShrink"),kr("onGrown"),kr("onStartGrow"),Yt("expanded",!1),$t("dimension",jt("property",{width:[Dr("property","width"),Dr("getDimension",function(n){return Ba(n)+"px"})],height:[Dr("property","height"),Dr("getDimension",function(n){return Kr(n)+"px"})]}))],name:"sliding",active:Ig,apis:Fg,state:Object.freeze({__proto__:null,init:function(n){var e=No(n.expanded);return tr({isExpanded:function(){return!0===e.get()},isCollapsed:function(){return!1===e.get()},setCollapsed:J(e.set,!1),setExpanded:J(e.set,!0),readState:function(){return"expanded: "+e.get()}})}})}),Bg=Jt([{stopped:[]},{resume:["element"]},{complete:[]}]),Ag=function(e,t,o,n,r,i){return og(e,t,o,n,r,i).fold(x,function(n){return Ag(e,t,o,n,r,i)},S)},Ng=function(t){function o(e){return pe(t.element).fold(x,function(n){return un(e,n)})}function s(n,e){return i.find(o,n,e)}function r(e){M(i.filter(Qn()),function(n){dl(n.descHandler)(e)})}var i=ig(),n=eg(t.element,{triggerEvent:function(e,t){return vr(e,t.target,function(n){return ug(s,e,t,t.target,n)})}}),u={debugInfo:w("real"),triggerEvent:function(e,t,o){vr(e,t,function(n){return ug(s,e,o,t,n)})},triggerFocus:function(c,a){ba(c).fold(function(){Ho(c)},function(n){vr(Yn(),c,function(n){var e,t,o=s,r=Yn(),i=n,u=tg(e={originator:a,kill:Y,prevent:Y,target:c},t=c);return og(o,r,e,t,u,i),!1})})},triggerEscape:function(n,e){u.triggerEvent("keydown",n.element,e.event)},getByUid:function(n){return g(n)},getByDom:function(n){return p(n)},build:Td,addToGui:function(n){a(n)},removeFromGui:function(n){f(n)},addToWorld:function(n){e(n)},removeFromWorld:function(n){c(n)},broadcast:function(n){l(n)},broadcastOn:function(n,e){d(n,e)},broadcastEvent:function(n,e){m(n,e)},isConnected:x},e=function(n){n.connect(u),qe(n.element)||(i.register(n),M(n.components(),e),u.triggerEvent(oe(),n.element,{target:n.element}))},c=function(n){qe(n.element)||(M(n.components(),c),i.unregister(n)),n.disconnect()},a=function(n){at(t,n,Qe)},f=function(n){Ce(n)},l=function(n){r({universal:!0,data:n})},d=function(n,e){r({universal:!1,channels:n,data:e})},m=function(n,e){var t,o,r=i.filter(n);return o={stop:function(){t.set(!0)},cut:Y,isStopped:(t=No(!1)).get,isCut:S,event:e,setSource:h("Cannot set source of a broadcasted event"),getSource:h("Cannot get source of a broadcasted event")},M(r,function(n){dl(n.descHandler)(o)}),o.isStopped()},g=function(n){return i.getById(n).fold(function(){return ht.error(new Error('Could not find component with uid: "'+n+'" in system.'))},ht.value)},p=function(n){var e=ba(n).getOr("not found");return g(e)};return e(t),{root:t,element:t.element,destroy:function(){n.unbind(),Ze(t.element)},add:a,remove:f,getByUid:g,getByDom:p,addToWorld:e,removeFromWorld:c,broadcast:l,broadcastOn:d,broadcastEvent:m}},Hg=ci("readonly-mode"),Pg=ci("edit-mode");function zg(n){var e=Td(td.sketch({dom:{classes:[ci("outer-container")].concat(n.classes)},containerBehaviours:or([cr.config({alpha:Hg,omega:Pg})])}));return Ng(e)}function jg(n,e){var t=fe.fromTag("input");Gr(t,{opacity:"0",position:"absolute",top:"-1000px",left:"-1000px"}),Qe(n,t),Ho(t),e(t),Ze(t)}function Lg(n){var e,t,o=n.getSelection();0<o.rangeCount&&(e=o.getRangeAt(0),(t=n.document.createRange()).setStart(e.startContainer,e.startOffset),t.setEnd(e.endContainer,e.endOffset),o.removeAllRanges(),o.addRange(t))}function $g(n,e){zo().each(function(n){un(n,e)||Po(n)}),n.focus(),Ho(fe.fromDom(n.document.body)),Lg(n)}function Gg(n,e,t,o){function r(){$g(e,o)}var i=Pl(t,"keydown",function(n){T(["input","textarea"],le(n.target))||r()});return{toReading:function(){jg(n,Po)},toEditing:r,onToolbarTouch:Y,destroy:function(){i.unbind()}}}function Ug(n){setTimeout(function(){throw n},0)}function Wg(n){var o,r,e=jl(n).isPortrait(),t=(o=n.screen.width,r=n.screen.height,z([{width:320,height:480,keyboard:{portrait:300,landscape:240}},{width:320,height:568,keyboard:{portrait:300,landscape:240}},{width:375,height:667,keyboard:{portrait:305,landscape:240}},{width:414,height:736,keyboard:{portrait:320,landscape:240}},{width:768,height:1024,keyboard:{portrait:320,landscape:400}},{width:1024,height:1366,keyboard:{portrait:380,landscape:460}}],function(n){return e=o<=n.width&&r<=n.height,t=n.keyboard,e?mn.some(t):mn.none();var e,t}).getOr({portrait:r/5,landscape:o/4})),i=e?t.portrait:t.landscape;return(e?n.screen.height:n.screen.width)-n.innerHeight>i?0:i}function Xg(n,e){var t=me(n).dom.defaultView;return Kr(n)+Kr(e)-Wg(t)}function qg(n,e,t){var o=Xg(e,t);$r(n,"padding-bottom",Kr(e)+Kr(t)-o+"px")}function Yg(n){return Em(n,lp)}function Kg(n,e){var t=Be(n,dp);return fp.fixed(n,t,e)}function Jg(n,e){return fp.scroller(n,e)}function Qg(n){var e=Yg(n);return("true"===Be(n,mp)?Jg:Kg)(n,e)}function Zg(n,e,t){var o=me(n).dom.defaultView.innerHeight;return Ie(n,gp,o+"px"),o-e-t}function np(n){var e=Wr(n,"top").getOr("0");return parseInt(e,10)}function ep(n){return parseInt(n.dom.scrollTop,10)}function tp(r,i){return ap(function(n){var e=J(ep,r);Ie(r,hp,e());var t=Math.abs(i-e()),o=Math.ceil(t/10);pp.animate(e,i,o,function(n,e){Em(r,hp)!==r.dom.scrollTop?e(r.dom.scrollTop):(r.dom.scrollTop=n,Ie(r,hp,n))},function(){r.dom.scrollTop=i,Ie(r,hp,i),n(i)},10)})}function op(n,e){$r(n,"top",e+Yg(n)+"px")}var rp,ip=function(n){function o(n){r()?i(n):e.push(n)}var t=mn.none(),e=[],r=function(){return t.isSome()},i=function(e){t.each(function(n){setTimeout(function(){e(n)},0)})};return n(function(n){r()||(t=mn.some(n),M(e,i),e=[])}),{get:o,map:function(t){return ip(function(e){o(function(n){e(t(n))})})},isReady:r}},up={nu:ip,pure:function(e){return ip(function(n){n(e)})}},cp=function(t){function n(n){t().then(n,Ug)}return{map:function(n){return cp(function(){return t().then(n)})},bind:function(e){return cp(function(){return t().then(function(n){return e(n).toPromise()})})},anonBind:function(n){return cp(function(){return t().then(function(){return n.toPromise()})})},toLazy:function(){return up.nu(n)},toCached:function(){var n=null;return cp(function(){return n=null===n?t():n})},toPromise:t,get:n}},ap=function(n){return cp(function(){return new Jl(n)})},sp=function(n){return cp(function(){return Jl.resolve(n)})},fp=Jt([{fixed:["element","property","offsetY"]},{scroller:["element","offsetY"]}]),lp="data-"+ci("position-y-fixed"),dp="data-"+ci("y-property"),mp="data-"+ci("scrolling"),gp="data-"+ci("last-window-height"),pp=(rp=null,{animate:function(r,i,u,c,e,n){function a(n){s=!0,e(n)}var s=!1;function f(n){um.clearInterval(rp),a(n)}um.clearInterval(rp),rp=um.setInterval(function(){var t=r(),n=t,e=i,o=u;(Math.abs(n-e)<=o?mn.none():n<e?mn.some(n+o):mn.some(n-o)).fold(function(){um.clearInterval(rp),a(i)},function(n){var e;c(n,f),s||((e=r())!==n||Math.abs(e-i)>Math.abs(t-i))&&(um.clearInterval(rp),a(i))})},n)}}),hp="data-"+ci("last-scroll-top");function vp(n,l){return n.fold(function(n,e,t){return $r(n,e,l+(o=t)+"px"),sp(o);var o},function(n,e){return r=l+(o=e),i=Wr(t=n,"top").getOr(o),u=r-parseInt(i,10),c=t.dom.scrollTop+u,a=t,s=c,f=r,ap(function(n){var e=J(ep,a);pp.animate(e,s,15,function(n){a.dom.scrollTop=n,$r(a,"top",np(a)+15+"px")},function(){a.dom.scrollTop=s,$r(a,"top",f+"px"),n(s)},10)});var t,o,r,i,u,c,a,s,f})}function bp(n,e){var u,t=Z(Jr(n,"["+lp+"]"),Qg),o=Z(t,function(n){return vp(n,e)});return u=o,ap(function(o){var r=[],i=0;0===u.length?o([]):M(u,function(n,e){var t;n.get((t=e,function(n){r[t]=n,++i>=u.length&&o(r)}))})})}function yp(n){var o,r,i,u,e,t,c,a,s,f,l,d,m,g,p,h,v,b,y,x=n.cWin,w=n.ceBody,S=n.socket,O=n.toolstrip,k=n.contentElement,T=n.keyboardType,C=n.outerWindow,E=n.dropup,D=n.outerBody,M=(r=w,i=O,u=E,m=me(o=S).dom.defaultView,d=Be(l=i,"style"),Gr(l,{position:"absolute",top:"0px"}),Ie(l,lp,"0px"),Ie(l,dp,"top"),g={restore:function(){Ie(l,"style",d||""),He(l,lp),He(l,dp)}},p=Kr(i),h=Kr(u),a=Zg(o,c=p,h),f=Be(s=o,"style"),_l(s),Gr(s,{position:"absolute",height:a+"px",width:"100%",top:c+"px"}),Ie(s,lp,c+"px"),Ie(s,mp,"true"),Ie(s,dp,"top"),v={restore:function(){Rl(s),Ie(s,"style",f||""),He(s,lp),He(s,mp),He(s,dp)}},t=Be(e=u,"style"),Gr(e,{position:"absolute",bottom:"0px"}),Ie(e,lp,"0px"),Ie(e,dp,"bottom"),b={restore:function(){Ie(e,"style",t||""),He(e,lp),He(e,dp)}},y=!0,qg(r,o,u),{setViewportOffset:function(n){Ie(o,lp,n+"px"),I()},isExpanding:F,isShrinking:Q(F),refresh:I,restore:function(){y=!1,g.restore(),v.restore(),b.restore()}}),_=T(D,x,ke(),k),R=Ll(C,{onChange:Y,onReady:M.refresh});function F(){var n=m.innerHeight;return Em(o,gp)<n}function I(){var n,e,t;y&&(n=Kr(i),e=Kr(u),t=Zg(o,n,e),Ie(o,lp,n+"px"),$r(o,"height",t+"px"),qg(r,o,u))}R.onAdjustment(function(){M.refresh()});function V(){q.clear()}var B,A,N,H,P,z,j,L,$,G,U,W=Pl(fe.fromDom(C),"resize",function(){M.isExpanding()&&M.refresh()}),X=(B=O,A=S,N=D,H=C,P=M,z=x,j=function(n){return t=A,o=n,r=me(e=B).dom.defaultView,ap(function(n){op(e,o),op(t,o),r.scrollTo(0,o),n(o)});var e,t,o,r},L=No(up.pure({})),$={start:function(e){var n=up.nu(function(n){return j(e).get(n)});L.set(n)},idle:function(n){L.get().get(function(){n()})}},G=Vm(function(){$.idle(function(){bp(N,H.pageYOffset).get(function(){var n=_m(z);mn.from(n[0]).bind(function(n){var e=n.top-A.dom.scrollTop;return e>H.innerHeight+5||e<-5?mn.some({top:e,bottom:e+n.height}):mn.none()}).each(function(n){A.dom.scrollTop=A.dom.scrollTop+n.top}),$.start(0),P.refresh()})})},1e3),U=Pl(fe.fromDom(H),"scroll",function(){H.pageYOffset<0||G.throttle()}),bp(N,H.pageYOffset).get(K),{unbind:U.unbind}),q=function(e,t){var n=e.document,o=fe.fromTag("div");function r(n){var e=fe.fromTag("span");return pl(e,[ci("layer-editor"),ci("unfocused-selection")]),Gr(e,{left:n.left+"px",top:n.top+"px",width:n.width+"px",height:n.height+"px"}),e}je(o,ci("unfocused-selections")),Qe(fe.fromDom(n.documentElement),o);var i=Pl(o,"touchstart",function(n){n.prevent(),$g(e,t),u()}),u=function(){we(o)};return{update:function(){u();var n=Z(_m(e),r);xe(o,n)},isActive:function(){return 0<Je(o).length},destroy:function(){i.unbind(),Ze(o)},clear:u}}(x,k);return{toEditing:function(){_.toEditing(),V()},toReading:function(){_.toReading()},onToolbarTouch:function(n){_.onToolbarTouch()},refreshSelection:function(){q.isActive()&&q.update()},clearSelection:V,highlightSelection:function(){q.update()},scrollIntoView:function(n,e){var t,o=x,r=n,i=e,u=Xg(t=S,E),c=J(Lg,o);u<r||u<i?tp(t,t.dom.scrollTop-u+i).get(c):r<0&&tp(t,t.dom.scrollTop+r).get(c)},updateToolbarPadding:Y,setViewportOffset:function(n){var i,u;M.setViewportOffset(n),i=S,u=n,ap(function(n){function e(n){$r(i,"top",n+"px")}var t=J(np,i),o=Math.abs(u-t()),r=Math.ceil(o/10);pp.animate(t,u,r,e,function(){e(u),n(u)},10)}).get(K)},syncHeight:function(){$r(k,"height",k.dom.contentWindow.document.body.scrollHeight+"px")},refreshStructure:M.refresh,destroy:function(){M.restore(),R.destroy(),X.unbind(),W.unbind(),_.destroy(),q.destroy(),jg(ke(),Po)}}}function xp(g,n){var p=wm(),h=cu(),v=cu(),b=uu(),y=uu();return{enter:function(){n.hide();var m=fe.fromDom(document);xm(g.editor).each(function(n){var e,t,o,r,i,u,c,a;function s(){o.run(function(n){n.refreshSelection()})}function f(n,e){var t=n-i.dom.scrollTop;o.run(function(n){n.scrollIntoView(t,t+e)})}function l(){o.run(function(n){n.clearSelection()})}function d(){t.getCursorBox().each(function(n){f(n.top,n.height)}),o.run(function(n){n.syncHeight()})}h.set({socketHeight:Wr(g.socket,"height"),iframeHeight:Wr(n.frame,"height"),outerScroll:document.body.scrollTop}),v.set({exclusives:(e="."+Zd,Pl(m,"touchmove",function(n){vi(n.target,e).filter(Nm).fold(function(){n.prevent()},Y)}))}),je(g.container,ci("fullscreen-maximized")),Sm(g.container,n.body),p.maximize(),$r(g.socket,"overflow","scroll"),$r(g.socket,"-webkit-overflow-scrolling","touch"),Ho(n.body),b.set(yp({cWin:n.win,ceBody:n.body,socket:g.socket,toolstrip:g.toolstrip,dropup:g.dropup.element,contentElement:n.frame,outerBody:g.body,outerWindow:g.win,keyboardType:Gg})),b.run(function(n){n.syncHeight()}),y.set((t=n,o=b,r=g.toolstrip,i=g.socket,g.dropup,u=Cm(t),c=Vm(d,300),a=[t.onKeyup(function(){l(),c.throttle()}),t.onNodeChanged(s),t.onDomChanged(c.throttle),t.onDomChanged(s),t.onScrollToCursor(function(n){n.preventDefault(),c.throttle()}),t.onScrollToElement(function(n){n.element,f(o,i)}),t.onToEditing(function(){o.run(function(n){n.toEditing()})}),t.onToReading(function(){o.run(function(n){n.toReading()})}),Pl(t.doc,"touchend",function(n){un(t.html,n.target)||un(t.body,n.target)}),Pl(r,"transitionend",function(n){var e;"height"===n.raw.propertyName&&(e=Kr(r),o.run(function(n){n.setViewportOffset(e)}),s(),d())}),zl(r,"touchstart",function(n){var e;o.run(function(n){n.highlightSelection()}),e=n,o.run(function(n){n.onToolbarTouch(e)}),t.onTouchToolstrip()}),Pl(t.body,"touchstart",function(n){l(),t.onTouchContent(),u.fireTouchstart(n)}),u.onTouchmove(),u.onTouchend(),Pl(t.body,"click",function(n){n.kill()}),Pl(r,"touchmove",function(){t.onToolbarScrollStart()})],{destroy:function(){M(a,function(n){n.unbind()})}}))})},refreshStructure:function(){b.run(function(n){n.refreshStructure()})},exit:function(){p.restore(),y.clear(),b.clear(),n.show(),h.on(function(n){n.socketHeight.each(function(n){$r(g.socket,"height",n)}),n.iframeHeight.each(function(n){$r(g.editor.getFrame(),"height",n)}),document.body.scrollTop=n.scrollTop}),h.clear(),v.on(function(n){n.exclusives.unbind()}),v.clear(),Le(g.container,ci("fullscreen-maximized")),Om(),Rl(g.toolbar),Xr(g.socket,"overflow"),Xr(g.socket,"-webkit-overflow-scrolling"),Po(g.editor.getFrame()),xm(g.editor).each(function(n){n.clearSelection()})}}}function wp(n,e,t){n.system.broadcastOn([dr],{command:e,state:t})}function Sp(e){return function(){function n(){e._skinLoaded=!0,e.fire("SkinLoaded")}e.initialized?n():e.on("init",n)}}var Op=tinymce.util.Tools.resolve("tinymce.EditorManager"),kp="toReading",Tp="toEditing";sr.add("mobile",function(E){return{getNotificationManagerImpl:function(){return{open:w({progressBar:{value:Y},close:Y,text:Y,getEl:w(null),moveTo:Y,moveRel:Y,settings:{}}),close:Y,reposition:Y,getArgs:w({})}},renderUI:function(){var n,e,t=E.getElement(),o={content:(n=mn.from(E.getParam("skin_url")).getOrThunk(function(){return Op.baseURL+"/skins/ui/oxide"}))+"/content.mobile.min.css",ui:n+"/skin.mobile.min.css"};function r(){E.fire("ScrollIntoView")}!1===E.getParam("skin")==0?(e=ar.DOM.styleSheetLoader,E.contentCSS.push(o.content),e.load(o.ui,Sp(E)),E.on("remove",function(){return e.unload(o.ui)})):Sp(E)();var i,u,c,a,s,f,l,d,m,g,p,h,v,b,y,x,w=Fn().os.isAndroid()?(i=r,u=zg({classes:[ci("android-container")]}),c=Hm(),a=uu(),s=Pm(a),f=zm(),l=Zm(Y,i),u.add(c.wrapper),u.add(f),u.add(l.component),{system:u,element:u.element,init:function(n){a.set(Am(n))},exit:function(){a.run(function(n){n.exit(),jd.remove(f,s)})},setToolbarGroups:function(n){var e=c.createGroups(n);c.setGroups(e)},setContextToolbar:function(n){var e=c.createGroups(n);c.setContextToolbar(e)},focusToolbar:function(){c.focus()},restoreToolbar:function(){c.restoreToolbar()},updateMode:function(n){jm(f,s,n,u.root)},socket:f,dropup:l}):(m=r,g=zg({classes:[ci("ios-container")]}),p=Hm(),h=uu(),v=Pm(h),b=zm(),y=Zm(function(){h.run(function(n){n.refreshStructure()})},m),g.add(p.wrapper),g.add(b),g.add(y.component),{system:g,element:g.element,init:function(r){h.set(function(){var n=zt("Getting IosWebapp schema",kg,r);$r(n.toolstrip,"width","100%"),$r(n.container,"position","relative");var e=Td(Bm(function(){n.setReadOnly(n.readOnlyOnInit()),o.enter()},n.translate));n.alloy.add(e);var t={show:function(){n.alloy.add(e)},hide:function(){n.alloy.remove(e)}},o=xp(n,t);return{setReadOnly:n.setReadOnly,refreshStructure:o.refreshStructure,enter:o.enter,exit:o.exit,destroy:Y}}())},exit:function(){h.run(function(n){jd.remove(b,v),n.exit()})},setToolbarGroups:function(n){var e=p.createGroups(n);p.setGroups(e)},setContextToolbar:function(n){var e=p.createGroups(n);p.setContextToolbar(e)},focusToolbar:function(){p.focus()},restoreToolbar:function(){p.restoreToolbar()},updateMode:function(n){jm(b,v,n,g.root)},socket:b,dropup:y}),S=fe.fromDom(t);function O(n,e,t,o){!1===o&&E.selection.collapse();var r,i,u,c=(r=e,i=t,{readOnly:(u=n.get()).backToMask.concat(r.get()),main:u.backToMask.concat(i.get())});w.setToolbarGroups(!0===o?c.readOnly:c.main),E.setMode(!0===o?"readonly":"design"),E.fire(!0===o?kp:Tp),w.updateMode(o)}function k(n,e){return E.on(n,e),{unbind:function(){E.off(n)}}}d=w.system,be(S,(x=d).element),M(Je(x.element),function(n){x.getByDom(n).each(ct)});var T=t.ownerDocument.defaultView,C=Ll(T,{onChange:function(){var n,e,t;w.system.broadcastOn([mr],{width:(n=T,e=Fn().os.isiOS(),t=jl(n).isPortrait(),e&&!t?n.screen.height:n.screen.width)})},onReady:Y});return E.on("init",function(){w.init({editor:{getFrame:function(){return fe.fromDom(E.contentAreaContainer.querySelector("iframe"))},onDomChanged:function(){return{unbind:Y}},onToReading:function(n){return k(kp,n)},onToEditing:function(n){return k(Tp,n)},onScrollToCursor:function(e){return E.on("ScrollIntoView",function(n){e(n)}),{unbind:function(){E.off("ScrollIntoView"),C.destroy()}}},onTouchToolstrip:function(){i()},onTouchContent:function(){jo(fe.fromDom(E.editorContainer.querySelector("."+ci("toolbar")))).bind(function(n){return w.system.getByDom(n).toOptional()}).each(q),w.restoreToolbar(),i()},onTapContent:function(n){var e=n.target;"img"===le(e)?(E.selection.select(e.dom),n.kill()):"a"===le(e)&&w.system.getByDom(fe.fromDom(E.editorContainer)).each(function(n){cr.isAlpha(n)&&function(n){var e=document.createElement("a");e.target="_blank",e.href=n.href,e.rel="noreferrer noopener";var t=document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),document.body.appendChild(e),e.dispatchEvent(t),document.body.removeChild(e)}(e.dom)})}},container:fe.fromDom(E.editorContainer),socket:fe.fromDom(E.contentAreaContainer),toolstrip:fe.fromDom(E.editorContainer.querySelector("."+ci("toolstrip"))),toolbar:fe.fromDom(E.editorContainer.querySelector("."+ci("toolbar"))),dropup:w.dropup,alloy:w.system,translate:Y,setReadOnly:function(n){O(d,l,f,n)},readOnlyOnInit:function(){return!1}});var t,n,o,r,e,i=function(){w.dropup.disappear(function(){w.system.broadcastOn([gr],{})})},u={label:"The first group",scrollable:!1,items:[nf("back",function(){E.selection.collapse(),w.exit()},{},E)]},c={label:"Back to read only",scrollable:!1,items:[nf("readonly-back",function(){O(d,l,f,!0)},{},E)]},a=Al(w,E),s={label:"The extra group",scrollable:!1,items:[]},f=No([{label:"the action group",scrollable:!0,items:(t=a,e=E.getParam("toolbar",lr,"array"),n=(D(e)?om:Bl)(e),o={},B(n,function(n){var e=!Re(o,n)&&Re(t,n)&&t[n].isSupported()?[t[n].sketch()]:[];return o[n]=!0,e}))},s]),l=No([{label:"The read only mode group",scrollable:!0,items:[]},s]),d=No({backToMask:[u],backToReadOnly:[c]}),m=w;M(st((r=E).formatter.get()),function(e){r.formatter.formatChanged(e,function(n){wp(m,e,n)})}),M(["ul","ol"],function(t){r.selection.selectorChanged(t,function(n,e){wp(m,t,n)})})}),E.on("remove",function(){w.exit()}),E.on("detach",function(){var e=w.system;M(Je(e.element),function(n){e.getByDom(n).each(ut)}),Ze(e.element),w.system.destroy()}),{iframeContainer:w.socket.element.dom,editorContainer:w.element.dom}}}})}();