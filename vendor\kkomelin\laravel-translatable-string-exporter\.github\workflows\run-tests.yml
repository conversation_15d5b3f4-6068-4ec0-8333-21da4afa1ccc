name: Tests

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      fail-fast: true
      matrix:
        php: [8.2, 8.4]
        laravel: ['11.*', '12.*']
        include:
          - laravel: 11.*
            testbench: ^9.0
            larastan: ^2.0
          - laravel: 12.*
            testbench: ^10.0
            larastan: ^3.0

    name: P${{ matrix.php }} - L${{ matrix.laravel }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, iconv, imagick, fileinfo
          coverage: none

      - name: Install dependencies
        run: |
          composer require "laravel/framework:${{ matrix.laravel }}" "orchestra/testbench:${{ matrix.testbench }}" "nunomaduro/larastan:${{ matrix.larastan }}" --no-interaction --no-update
          composer update --prefer-dist --no-interaction

      - name: Execute tests
        run: vendor/bin/phpunit
