<?php
namespace Aws\RolesAnywhere;

use Aws\AwsClient;

/**
 * This client is used to interact with the **IAM Roles Anywhere** service.
 * @method \Aws\Result createProfile(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createProfileAsync(array $args = [])
 * @method \Aws\Result createTrustAnchor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createTrustAnchorAsync(array $args = [])
 * @method \Aws\Result deleteAttributeMapping(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteAttributeMappingAsync(array $args = [])
 * @method \Aws\Result deleteCrl(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteCrlAsync(array $args = [])
 * @method \Aws\Result deleteProfile(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteProfileAsync(array $args = [])
 * @method \Aws\Result deleteTrustAnchor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteTrustAnchorAsync(array $args = [])
 * @method \Aws\Result disableCrl(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disableCrlAsync(array $args = [])
 * @method \Aws\Result disableProfile(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disableProfileAsync(array $args = [])
 * @method \Aws\Result disableTrustAnchor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disableTrustAnchorAsync(array $args = [])
 * @method \Aws\Result enableCrl(array $args = [])
 * @method \GuzzleHttp\Promise\Promise enableCrlAsync(array $args = [])
 * @method \Aws\Result enableProfile(array $args = [])
 * @method \GuzzleHttp\Promise\Promise enableProfileAsync(array $args = [])
 * @method \Aws\Result enableTrustAnchor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise enableTrustAnchorAsync(array $args = [])
 * @method \Aws\Result getCrl(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCrlAsync(array $args = [])
 * @method \Aws\Result getProfile(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getProfileAsync(array $args = [])
 * @method \Aws\Result getSubject(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSubjectAsync(array $args = [])
 * @method \Aws\Result getTrustAnchor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTrustAnchorAsync(array $args = [])
 * @method \Aws\Result importCrl(array $args = [])
 * @method \GuzzleHttp\Promise\Promise importCrlAsync(array $args = [])
 * @method \Aws\Result listCrls(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCrlsAsync(array $args = [])
 * @method \Aws\Result listProfiles(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listProfilesAsync(array $args = [])
 * @method \Aws\Result listSubjects(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSubjectsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result listTrustAnchors(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTrustAnchorsAsync(array $args = [])
 * @method \Aws\Result putAttributeMapping(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putAttributeMappingAsync(array $args = [])
 * @method \Aws\Result putNotificationSettings(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putNotificationSettingsAsync(array $args = [])
 * @method \Aws\Result resetNotificationSettings(array $args = [])
 * @method \GuzzleHttp\Promise\Promise resetNotificationSettingsAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateCrl(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateCrlAsync(array $args = [])
 * @method \Aws\Result updateProfile(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateProfileAsync(array $args = [])
 * @method \Aws\Result updateTrustAnchor(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateTrustAnchorAsync(array $args = [])
 */
class RolesAnywhereClient extends AwsClient {}
