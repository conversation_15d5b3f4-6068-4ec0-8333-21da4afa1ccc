<?php
// This file was auto-generated from sdk-root/src/data/resource-groups/2017-11-27/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-11-27', 'endpointPrefix' => 'resource-groups', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceAbbreviation' => 'Resource Groups', 'serviceFullName' => 'AWS Resource Groups', 'serviceId' => 'Resource Groups', 'signatureVersion' => 'v4', 'signingName' => 'resource-groups', 'uid' => 'resource-groups-2017-11-27', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'CancelTagSyncTask' => [ 'name' => 'CancelTagSyncTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/cancel-tag-sync-task', ], 'input' => [ 'shape' => 'CancelTagSyncTaskInput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'CreateGroup' => [ 'name' => 'CreateGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/groups', ], 'input' => [ 'shape' => 'CreateGroupInput', ], 'output' => [ 'shape' => 'CreateGroupOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'DeleteGroup' => [ 'name' => 'DeleteGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-group', ], 'input' => [ 'shape' => 'DeleteGroupInput', ], 'output' => [ 'shape' => 'DeleteGroupOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'GetAccountSettings' => [ 'name' => 'GetAccountSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/get-account-settings', ], 'output' => [ 'shape' => 'GetAccountSettingsOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'GetGroup' => [ 'name' => 'GetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/get-group', ], 'input' => [ 'shape' => 'GetGroupInput', ], 'output' => [ 'shape' => 'GetGroupOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'GetGroupConfiguration' => [ 'name' => 'GetGroupConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/get-group-configuration', ], 'input' => [ 'shape' => 'GetGroupConfigurationInput', ], 'output' => [ 'shape' => 'GetGroupConfigurationOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'GetGroupQuery' => [ 'name' => 'GetGroupQuery', 'http' => [ 'method' => 'POST', 'requestUri' => '/get-group-query', ], 'input' => [ 'shape' => 'GetGroupQueryInput', ], 'output' => [ 'shape' => 'GetGroupQueryOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'GetTagSyncTask' => [ 'name' => 'GetTagSyncTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/get-tag-sync-task', ], 'input' => [ 'shape' => 'GetTagSyncTaskInput', ], 'output' => [ 'shape' => 'GetTagSyncTaskOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'GetTags' => [ 'name' => 'GetTags', 'http' => [ 'method' => 'GET', 'requestUri' => '/resources/{Arn}/tags', ], 'input' => [ 'shape' => 'GetTagsInput', ], 'output' => [ 'shape' => 'GetTagsOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'GroupResources' => [ 'name' => 'GroupResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/group-resources', ], 'input' => [ 'shape' => 'GroupResourcesInput', ], 'output' => [ 'shape' => 'GroupResourcesOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListGroupResources' => [ 'name' => 'ListGroupResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-group-resources', ], 'input' => [ 'shape' => 'ListGroupResourcesInput', ], 'output' => [ 'shape' => 'ListGroupResourcesOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListGroupingStatuses' => [ 'name' => 'ListGroupingStatuses', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-grouping-statuses', ], 'input' => [ 'shape' => 'ListGroupingStatusesInput', ], 'output' => [ 'shape' => 'ListGroupingStatusesOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListGroups' => [ 'name' => 'ListGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/groups-list', ], 'input' => [ 'shape' => 'ListGroupsInput', ], 'output' => [ 'shape' => 'ListGroupsOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'ListTagSyncTasks' => [ 'name' => 'ListTagSyncTasks', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-tag-sync-tasks', ], 'input' => [ 'shape' => 'ListTagSyncTasksInput', ], 'output' => [ 'shape' => 'ListTagSyncTasksOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'PutGroupConfiguration' => [ 'name' => 'PutGroupConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/put-group-configuration', 'responseCode' => 202, ], 'input' => [ 'shape' => 'PutGroupConfigurationInput', ], 'output' => [ 'shape' => 'PutGroupConfigurationOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'SearchResources' => [ 'name' => 'SearchResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/resources/search', ], 'input' => [ 'shape' => 'SearchResourcesInput', ], 'output' => [ 'shape' => 'SearchResourcesOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'StartTagSyncTask' => [ 'name' => 'StartTagSyncTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/start-tag-sync-task', ], 'input' => [ 'shape' => 'StartTagSyncTaskInput', ], 'output' => [ 'shape' => 'StartTagSyncTaskOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'Tag' => [ 'name' => 'Tag', 'http' => [ 'method' => 'PUT', 'requestUri' => '/resources/{Arn}/tags', ], 'input' => [ 'shape' => 'TagInput', ], 'output' => [ 'shape' => 'TagOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'UngroupResources' => [ 'name' => 'UngroupResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/ungroup-resources', ], 'input' => [ 'shape' => 'UngroupResourcesInput', ], 'output' => [ 'shape' => 'UngroupResourcesOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'Untag' => [ 'name' => 'Untag', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/resources/{Arn}/tags', ], 'input' => [ 'shape' => 'UntagInput', ], 'output' => [ 'shape' => 'UntagOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'UpdateAccountSettings' => [ 'name' => 'UpdateAccountSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-account-settings', ], 'input' => [ 'shape' => 'UpdateAccountSettingsInput', ], 'output' => [ 'shape' => 'UpdateAccountSettingsOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'UpdateGroup' => [ 'name' => 'UpdateGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-group', ], 'input' => [ 'shape' => 'UpdateGroupInput', ], 'output' => [ 'shape' => 'UpdateGroupOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], 'UpdateGroupQuery' => [ 'name' => 'UpdateGroupQuery', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-group-query', ], 'input' => [ 'shape' => 'UpdateGroupQueryInput', ], 'output' => [ 'shape' => 'UpdateGroupQueryOutput', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'MethodNotAllowedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], ], ], ], 'shapes' => [ 'AccountSettings' => [ 'type' => 'structure', 'members' => [ 'GroupLifecycleEventsDesiredStatus' => [ 'shape' => 'GroupLifecycleEventsDesiredStatus', ], 'GroupLifecycleEventsStatus' => [ 'shape' => 'GroupLifecycleEventsStatus', ], 'GroupLifecycleEventsStatusMessage' => [ 'shape' => 'GroupLifecycleEventsStatusMessage', ], ], ], 'ApplicationArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 12, 'pattern' => 'arn:aws(-[a-z]+)*:resource-groups:[a-z]{2}(-[a-z]+)+-\\d{1}:[0-9]{12}:group/[a-zA-Z0-9_\\.-]{1,150}/[a-zA-Z0-9]{22,26}', ], 'ApplicationTag' => [ 'type' => 'map', 'key' => [ 'shape' => 'ApplicationTagKey', ], 'value' => [ 'shape' => 'ApplicationArn', ], ], 'ApplicationTagKey' => [ 'type' => 'string', 'pattern' => 'awsApplication', ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'CancelTagSyncTaskInput' => [ 'type' => 'structure', 'required' => [ 'TaskArn', ], 'members' => [ 'TaskArn' => [ 'shape' => 'TagSyncTaskArn', ], ], ], 'CreateGroupInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'CreateGroupName', ], 'Description' => [ 'shape' => 'Description', ], 'ResourceQuery' => [ 'shape' => 'ResourceQuery', ], 'Tags' => [ 'shape' => 'Tags', ], 'Configuration' => [ 'shape' => 'GroupConfigurationList', ], 'Criticality' => [ 'shape' => 'Criticality', ], 'Owner' => [ 'shape' => 'Owner', ], 'DisplayName' => [ 'shape' => 'DisplayName', ], ], ], 'CreateGroupName' => [ 'type' => 'string', 'max' => 300, 'min' => 1, 'pattern' => '[a-zA-Z0-9_\\.-]+', ], 'CreateGroupOutput' => [ 'type' => 'structure', 'members' => [ 'Group' => [ 'shape' => 'Group', ], 'ResourceQuery' => [ 'shape' => 'ResourceQuery', ], 'Tags' => [ 'shape' => 'Tags', ], 'GroupConfiguration' => [ 'shape' => 'GroupConfiguration', ], ], ], 'Criticality' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'DeleteGroupInput' => [ 'type' => 'structure', 'members' => [ 'GroupName' => [ 'shape' => 'GroupName', 'deprecated' => true, 'deprecatedMessage' => 'This field is deprecated, use Group instead.', ], 'Group' => [ 'shape' => 'GroupStringV2', ], ], ], 'DeleteGroupOutput' => [ 'type' => 'structure', 'members' => [ 'Group' => [ 'shape' => 'Group', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '[\\sa-zA-Z0-9_\\.-]*', ], 'DisplayName' => [ 'type' => 'string', 'max' => 300, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'ErrorCode' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'FailedResource' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], 'ErrorCode' => [ 'shape' => 'ErrorCode', ], ], ], 'FailedResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedResource', ], ], 'ForbiddenException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'GetAccountSettingsOutput' => [ 'type' => 'structure', 'members' => [ 'AccountSettings' => [ 'shape' => 'AccountSettings', ], ], ], 'GetGroupConfigurationInput' => [ 'type' => 'structure', 'members' => [ 'Group' => [ 'shape' => 'GroupString', ], ], ], 'GetGroupConfigurationOutput' => [ 'type' => 'structure', 'members' => [ 'GroupConfiguration' => [ 'shape' => 'GroupConfiguration', ], ], ], 'GetGroupInput' => [ 'type' => 'structure', 'members' => [ 'GroupName' => [ 'shape' => 'GroupName', 'deprecated' => true, 'deprecatedMessage' => 'This field is deprecated, use Group instead.', ], 'Group' => [ 'shape' => 'GroupStringV2', ], ], ], 'GetGroupOutput' => [ 'type' => 'structure', 'members' => [ 'Group' => [ 'shape' => 'Group', ], ], ], 'GetGroupQueryInput' => [ 'type' => 'structure', 'members' => [ 'GroupName' => [ 'shape' => 'GroupName', 'deprecated' => true, 'deprecatedMessage' => 'This field is deprecated, use Group instead.', ], 'Group' => [ 'shape' => 'GroupString', ], ], ], 'GetGroupQueryOutput' => [ 'type' => 'structure', 'members' => [ 'GroupQuery' => [ 'shape' => 'GroupQuery', ], ], ], 'GetTagSyncTaskInput' => [ 'type' => 'structure', 'required' => [ 'TaskArn', ], 'members' => [ 'TaskArn' => [ 'shape' => 'TagSyncTaskArn', ], ], ], 'GetTagSyncTaskOutput' => [ 'type' => 'structure', 'members' => [ 'GroupArn' => [ 'shape' => 'GroupArnV2', ], 'GroupName' => [ 'shape' => 'GroupName', ], 'TaskArn' => [ 'shape' => 'TagSyncTaskArn', ], 'TagKey' => [ 'shape' => 'TagKey', ], 'TagValue' => [ 'shape' => 'TagValue', ], 'ResourceQuery' => [ 'shape' => 'ResourceQuery', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Status' => [ 'shape' => 'TagSyncTaskStatus', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], 'CreatedAt' => [ 'shape' => 'timestamp', ], ], ], 'GetTagsInput' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'GroupArnV2', 'location' => 'uri', 'locationName' => 'Arn', ], ], ], 'GetTagsOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'GroupArnV2', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'Group' => [ 'type' => 'structure', 'required' => [ 'GroupArn', 'Name', ], 'members' => [ 'GroupArn' => [ 'shape' => 'GroupArnV2', ], 'Name' => [ 'shape' => 'GroupName', ], 'Description' => [ 'shape' => 'Description', ], 'Criticality' => [ 'shape' => 'Criticality', ], 'Owner' => [ 'shape' => 'Owner', ], 'DisplayName' => [ 'shape' => 'DisplayName', ], 'ApplicationTag' => [ 'shape' => 'ApplicationTag', ], ], ], 'GroupArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 12, 'pattern' => 'arn:aws(-[a-z]+)*:resource-groups:[a-z]{2}(-[a-z]+)+-\\d{1}:[0-9]{12}:group/([a-zA-Z0-9_\\.-]{1,300}|[a-zA-Z0-9_\\.-]{1,150}/[a-z0-9]{26})', ], 'GroupArnV2' => [ 'type' => 'string', 'max' => 1600, 'min' => 12, 'pattern' => 'arn:aws(-[a-z]+)*:resource-groups:[a-z]{2}(-[a-z]+)+-\\d{1}:[0-9]{12}:group/([a-zA-Z0-9_\\.-]{1,300}|[a-zA-Z0-9_\\.-]{1,150}/[a-z0-9]{26})', ], 'GroupConfiguration' => [ 'type' => 'structure', 'members' => [ 'Configuration' => [ 'shape' => 'GroupConfigurationList', ], 'ProposedConfiguration' => [ 'shape' => 'GroupConfigurationList', ], 'Status' => [ 'shape' => 'GroupConfigurationStatus', ], 'FailureReason' => [ 'shape' => 'GroupConfigurationFailureReason', ], ], ], 'GroupConfigurationFailureReason' => [ 'type' => 'string', ], 'GroupConfigurationItem' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Type' => [ 'shape' => 'GroupConfigurationType', ], 'Parameters' => [ 'shape' => 'GroupParameterList', ], ], ], 'GroupConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupConfigurationItem', ], 'max' => 2, ], 'GroupConfigurationParameter' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'GroupConfigurationParameterName', ], 'Values' => [ 'shape' => 'GroupConfigurationParameterValueList', ], ], ], 'GroupConfigurationParameterName' => [ 'type' => 'string', 'max' => 80, 'min' => 1, 'pattern' => '[a-z-]+', ], 'GroupConfigurationParameterValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9:\\/\\._-]+', ], 'GroupConfigurationParameterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupConfigurationParameterValue', ], ], 'GroupConfigurationStatus' => [ 'type' => 'string', 'enum' => [ 'UPDATING', 'UPDATE_COMPLETE', 'UPDATE_FAILED', ], ], 'GroupConfigurationType' => [ 'type' => 'string', 'max' => 40, 'pattern' => 'AWS::[a-zA-Z0-9]+::[a-zA-Z0-9]+', ], 'GroupFilter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'GroupFilterName', ], 'Values' => [ 'shape' => 'GroupFilterValues', ], ], ], 'GroupFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupFilter', ], ], 'GroupFilterName' => [ 'type' => 'string', 'enum' => [ 'resource-type', 'configuration-type', 'owner', 'display-name', 'criticality', ], ], 'GroupFilterValue' => [ 'type' => 'string', 'max' => 300, 'min' => 1, 'pattern' => 'AWS::(AllSupported|[a-zA-Z0-9]+::[a-zA-Z0-9]+)|[\\s\\p{L}0-9_\\.-]*', ], 'GroupFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupFilterValue', ], 'max' => 5, 'min' => 1, ], 'GroupIdentifier' => [ 'type' => 'structure', 'members' => [ 'GroupName' => [ 'shape' => 'GroupName', ], 'GroupArn' => [ 'shape' => 'GroupArn', ], 'Description' => [ 'shape' => 'Description', ], 'Criticality' => [ 'shape' => 'Criticality', ], 'Owner' => [ 'shape' => 'Owner', ], 'DisplayName' => [ 'shape' => 'DisplayName', ], ], ], 'GroupIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupIdentifier', ], ], 'GroupLifecycleEventsDesiredStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'GroupLifecycleEventsStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', 'IN_PROGRESS', 'ERROR', ], ], 'GroupLifecycleEventsStatusMessage' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'GroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Group', ], ], 'GroupName' => [ 'type' => 'string', 'max' => 300, 'min' => 1, 'pattern' => '[a-zA-Z0-9_\\.-]{1,300}|[a-zA-Z0-9_\\.-]{1,150}/[a-z0-9]{26}', ], 'GroupParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupConfigurationParameter', ], ], 'GroupQuery' => [ 'type' => 'structure', 'required' => [ 'GroupName', 'ResourceQuery', ], 'members' => [ 'GroupName' => [ 'shape' => 'GroupName', ], 'ResourceQuery' => [ 'shape' => 'ResourceQuery', ], ], ], 'GroupResourcesInput' => [ 'type' => 'structure', 'required' => [ 'Group', 'ResourceArns', ], 'members' => [ 'Group' => [ 'shape' => 'GroupStringV2', ], 'ResourceArns' => [ 'shape' => 'ResourceArnList', ], ], ], 'GroupResourcesOutput' => [ 'type' => 'structure', 'members' => [ 'Succeeded' => [ 'shape' => 'ResourceArnList', ], 'Failed' => [ 'shape' => 'FailedResourceList', ], 'Pending' => [ 'shape' => 'PendingResourceList', ], ], ], 'GroupString' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '[a-zA-Z0-9_\\.-]{1,300}|[a-zA-Z0-9_\\.-]{1,150}/[a-z0-9]{26}|arn:aws(-[a-z]+)*:resource-groups:[a-z]{2}(-[a-z]+)+-\\d{1}:[0-9]{12}:group/([a-zA-Z0-9_\\.-]{1,300}|[a-zA-Z0-9_\\.-]{1,150}/[a-z0-9]{26})', ], 'GroupStringV2' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '[a-zA-Z0-9_\\.-]{1,300}|[a-zA-Z0-9_\\.-]{1,150}/[a-z0-9]{26}|arn:aws(-[a-z]+)*:resource-groups:[a-z]{2}(-[a-z]+)+-\\d{1}:[0-9]{12}:group/([a-zA-Z0-9_\\.-]{1,300}|[a-zA-Z0-9_\\.-]{1,150}/[a-z0-9]{26})', ], 'GroupingStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCESS', 'FAILED', 'IN_PROGRESS', 'SKIPPED', ], ], 'GroupingStatusesItem' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'Action' => [ 'shape' => 'GroupingType', ], 'Status' => [ 'shape' => 'GroupingStatus', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'UpdatedAt' => [ 'shape' => 'timestamp', ], ], ], 'GroupingStatusesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupingStatusesItem', ], ], 'GroupingType' => [ 'type' => 'string', 'enum' => [ 'GROUP', 'UNGROUP', ], ], 'InternalServerErrorException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'ListGroupResourcesInput' => [ 'type' => 'structure', 'members' => [ 'GroupName' => [ 'shape' => 'GroupName', 'deprecated' => true, 'deprecatedMessage' => 'This field is deprecated, use Group instead.', ], 'Group' => [ 'shape' => 'GroupStringV2', ], 'Filters' => [ 'shape' => 'ResourceFilterList', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListGroupResourcesItem' => [ 'type' => 'structure', 'members' => [ 'Identifier' => [ 'shape' => 'ResourceIdentifier', ], 'Status' => [ 'shape' => 'ResourceStatus', ], ], ], 'ListGroupResourcesItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListGroupResourcesItem', ], ], 'ListGroupResourcesOutput' => [ 'type' => 'structure', 'members' => [ 'Resources' => [ 'shape' => 'ListGroupResourcesItemList', ], 'ResourceIdentifiers' => [ 'shape' => 'ResourceIdentifierList', 'deprecated' => true, 'deprecatedMessage' => 'This field is deprecated, use Resources instead.', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'QueryErrors' => [ 'shape' => 'QueryErrorList', ], ], ], 'ListGroupingStatusesFilter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'ListGroupingStatusesFilterName', ], 'Values' => [ 'shape' => 'ListGroupingStatusesFilterValues', ], ], ], 'ListGroupingStatusesFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListGroupingStatusesFilter', ], ], 'ListGroupingStatusesFilterName' => [ 'type' => 'string', 'enum' => [ 'status', 'resource-arn', ], ], 'ListGroupingStatusesFilterValue' => [ 'type' => 'string', 'pattern' => 'SUCCESS|FAILED|IN_PROGRESS|SKIPPED|arn:aws(-[a-z]+)*:[a-z0-9\\-]*:([a-z]{2}(-[a-z]+)+-\\d{1})?:([0-9]{12})?:.+', ], 'ListGroupingStatusesFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListGroupingStatusesFilterValue', ], 'max' => 10, 'min' => 1, ], 'ListGroupingStatusesInput' => [ 'type' => 'structure', 'required' => [ 'Group', ], 'members' => [ 'Group' => [ 'shape' => 'GroupStringV2', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'ListGroupingStatusesFilterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListGroupingStatusesOutput' => [ 'type' => 'structure', 'members' => [ 'Group' => [ 'shape' => 'GroupStringV2', ], 'GroupingStatuses' => [ 'shape' => 'GroupingStatusesList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListGroupsInput' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'GroupFilterList', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListGroupsOutput' => [ 'type' => 'structure', 'members' => [ 'GroupIdentifiers' => [ 'shape' => 'GroupIdentifierList', ], 'Groups' => [ 'shape' => 'GroupList', 'deprecated' => true, 'deprecatedMessage' => 'This field is deprecated, use GroupIdentifiers instead.', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagSyncTasksFilter' => [ 'type' => 'structure', 'members' => [ 'GroupArn' => [ 'shape' => 'GroupArnV2', ], 'GroupName' => [ 'shape' => 'GroupName', ], ], ], 'ListTagSyncTasksFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListTagSyncTasksFilter', ], ], 'ListTagSyncTasksInput' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'ListTagSyncTasksFilterList', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagSyncTasksOutput' => [ 'type' => 'structure', 'members' => [ 'TagSyncTasks' => [ 'shape' => 'TagSyncTaskList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'MethodNotAllowedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 405, ], 'exception' => true, ], 'NextToken' => [ 'type' => 'string', 'max' => 8192, 'min' => 0, 'pattern' => '^[a-zA-Z0-9+/]*={0,2}$', ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'Owner' => [ 'type' => 'string', 'max' => 300, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'PendingResource' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'PendingResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PendingResource', ], ], 'PutGroupConfigurationInput' => [ 'type' => 'structure', 'members' => [ 'Group' => [ 'shape' => 'GroupString', ], 'Configuration' => [ 'shape' => 'GroupConfigurationList', ], ], ], 'PutGroupConfigurationOutput' => [ 'type' => 'structure', 'members' => [], ], 'Query' => [ 'type' => 'string', 'max' => 4096, 'pattern' => '[\\s\\S]*', ], 'QueryError' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'QueryErrorCode', ], 'Message' => [ 'shape' => 'QueryErrorMessage', ], ], ], 'QueryErrorCode' => [ 'type' => 'string', 'enum' => [ 'CLOUDFORMATION_STACK_INACTIVE', 'CLOUDFORMATION_STACK_NOT_EXISTING', 'CLOUDFORMATION_STACK_UNASSUMABLE_ROLE', 'RESOURCE_TYPE_NOT_SUPPORTED', ], ], 'QueryErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueryError', ], ], 'QueryErrorMessage' => [ 'type' => 'string', ], 'QueryType' => [ 'type' => 'string', 'enum' => [ 'TAG_FILTERS_1_0', 'CLOUDFORMATION_STACK_1_0', ], 'max' => 128, 'min' => 1, 'pattern' => '^\\w+$', ], 'ResourceArn' => [ 'type' => 'string', 'pattern' => 'arn:aws(-[a-z]+)*:[a-z0-9\\-]*:([a-z]{2}(-[a-z]+)+-\\d{1})?:([0-9]{12})?:.+', ], 'ResourceArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceArn', ], 'max' => 10, 'min' => 1, ], 'ResourceFilter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'ResourceFilterName', ], 'Values' => [ 'shape' => 'ResourceFilterValues', ], ], ], 'ResourceFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceFilter', ], ], 'ResourceFilterName' => [ 'type' => 'string', 'enum' => [ 'resource-type', ], ], 'ResourceFilterValue' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => 'AWS::[a-zA-Z0-9]+::[a-zA-Z0-9]+', ], 'ResourceFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceFilterValue', ], 'max' => 5, 'min' => 1, ], 'ResourceIdentifier' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], ], ], 'ResourceIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceIdentifier', ], ], 'ResourceQuery' => [ 'type' => 'structure', 'required' => [ 'Type', 'Query', ], 'members' => [ 'Type' => [ 'shape' => 'QueryType', ], 'Query' => [ 'shape' => 'Query', ], ], ], 'ResourceStatus' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ResourceStatusValue', ], ], ], 'ResourceStatusValue' => [ 'type' => 'string', 'enum' => [ 'PENDING', ], ], 'ResourceType' => [ 'type' => 'string', 'pattern' => 'AWS::[a-zA-Z0-9]+::\\w+', ], 'RoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:(aws[a-zA-Z-]*)?:iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+', ], 'SearchResourcesInput' => [ 'type' => 'structure', 'required' => [ 'ResourceQuery', ], 'members' => [ 'ResourceQuery' => [ 'shape' => 'ResourceQuery', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'SearchResourcesOutput' => [ 'type' => 'structure', 'members' => [ 'ResourceIdentifiers' => [ 'shape' => 'ResourceIdentifierList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'QueryErrors' => [ 'shape' => 'QueryErrorList', ], ], ], 'StartTagSyncTaskInput' => [ 'type' => 'structure', 'required' => [ 'Group', 'RoleArn', ], 'members' => [ 'Group' => [ 'shape' => 'GroupStringV2', ], 'TagKey' => [ 'shape' => 'TagKey', ], 'TagValue' => [ 'shape' => 'TagValue', ], 'ResourceQuery' => [ 'shape' => 'ResourceQuery', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'StartTagSyncTaskOutput' => [ 'type' => 'structure', 'members' => [ 'GroupArn' => [ 'shape' => 'GroupArnV2', ], 'GroupName' => [ 'shape' => 'GroupName', ], 'TaskArn' => [ 'shape' => 'TagSyncTaskArn', ], 'TagKey' => [ 'shape' => 'TagKey', ], 'TagValue' => [ 'shape' => 'TagValue', ], 'ResourceQuery' => [ 'shape' => 'ResourceQuery', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'TagInput' => [ 'type' => 'structure', 'required' => [ 'Arn', 'Tags', ], 'members' => [ 'Arn' => [ 'shape' => 'GroupArnV2', 'location' => 'uri', 'locationName' => 'Arn', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'GroupArnV2', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'TagSyncTaskArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 12, 'pattern' => 'arn:aws(-[a-z]+)*:resource-groups:[a-z]{2}(-[a-z]+)+-\\d{1}:[0-9]{12}:group/[a-zA-Z0-9_\\.-]{1,150}/[a-z0-9]{26}/tag-sync-task/[a-z0-9]{26}', ], 'TagSyncTaskItem' => [ 'type' => 'structure', 'members' => [ 'GroupArn' => [ 'shape' => 'GroupArnV2', ], 'GroupName' => [ 'shape' => 'GroupName', ], 'TaskArn' => [ 'shape' => 'TagSyncTaskArn', ], 'TagKey' => [ 'shape' => 'TagKey', ], 'TagValue' => [ 'shape' => 'TagValue', ], 'ResourceQuery' => [ 'shape' => 'ResourceQuery', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Status' => [ 'shape' => 'TagSyncTaskStatus', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], 'CreatedAt' => [ 'shape' => 'timestamp', ], ], ], 'TagSyncTaskList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagSyncTaskItem', ], ], 'TagSyncTaskStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'ERROR', ], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], 'TooManyRequestsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'UnauthorizedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], 'UngroupResourcesInput' => [ 'type' => 'structure', 'required' => [ 'Group', 'ResourceArns', ], 'members' => [ 'Group' => [ 'shape' => 'GroupStringV2', ], 'ResourceArns' => [ 'shape' => 'ResourceArnList', ], ], ], 'UngroupResourcesOutput' => [ 'type' => 'structure', 'members' => [ 'Succeeded' => [ 'shape' => 'ResourceArnList', ], 'Failed' => [ 'shape' => 'FailedResourceList', ], 'Pending' => [ 'shape' => 'PendingResourceList', ], ], ], 'UntagInput' => [ 'type' => 'structure', 'required' => [ 'Arn', 'Keys', ], 'members' => [ 'Arn' => [ 'shape' => 'GroupArnV2', 'location' => 'uri', 'locationName' => 'Arn', ], 'Keys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'GroupArnV2', ], 'Keys' => [ 'shape' => 'TagKeyList', ], ], ], 'UpdateAccountSettingsInput' => [ 'type' => 'structure', 'members' => [ 'GroupLifecycleEventsDesiredStatus' => [ 'shape' => 'GroupLifecycleEventsDesiredStatus', ], ], ], 'UpdateAccountSettingsOutput' => [ 'type' => 'structure', 'members' => [ 'AccountSettings' => [ 'shape' => 'AccountSettings', ], ], ], 'UpdateGroupInput' => [ 'type' => 'structure', 'members' => [ 'GroupName' => [ 'shape' => 'GroupName', 'deprecated' => true, 'deprecatedMessage' => 'This field is deprecated, use Group instead.', ], 'Group' => [ 'shape' => 'GroupStringV2', ], 'Description' => [ 'shape' => 'Description', ], 'Criticality' => [ 'shape' => 'Criticality', ], 'Owner' => [ 'shape' => 'Owner', ], 'DisplayName' => [ 'shape' => 'DisplayName', ], ], ], 'UpdateGroupOutput' => [ 'type' => 'structure', 'members' => [ 'Group' => [ 'shape' => 'Group', ], ], ], 'UpdateGroupQueryInput' => [ 'type' => 'structure', 'required' => [ 'ResourceQuery', ], 'members' => [ 'GroupName' => [ 'shape' => 'GroupName', 'deprecated' => true, 'deprecatedMessage' => 'This field is deprecated, use Group instead.', ], 'Group' => [ 'shape' => 'GroupString', ], 'ResourceQuery' => [ 'shape' => 'ResourceQuery', ], ], ], 'UpdateGroupQueryOutput' => [ 'type' => 'structure', 'members' => [ 'GroupQuery' => [ 'shape' => 'GroupQuery', ], ], ], 'timestamp' => [ 'type' => 'timestamp', ], ],];
