<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\FlexApi\V1;

use Twilio\Options;
use Twilio\Values;

abstract class ConfigurationOptions
{
    /**
     * @param string $uiVersion The Pinned UI version of the Configuration resource to fetch.
     * @return FetchConfigurationOptions Options builder
     */
    public static function fetch(
        
        string $uiVersion = Values::NONE

    ): FetchConfigurationOptions
    {
        return new FetchConfigurationOptions(
            $uiVersion
        );
    }


}

class FetchConfigurationOptions extends Options
    {
    /**
     * @param string $uiVersion The Pinned UI version of the Configuration resource to fetch.
     */
    public function __construct(
        
        string $uiVersion = Values::NONE

    ) {
        $this->options['uiVersion'] = $uiVersion;
    }

    /**
     * The Pinned UI version of the Configuration resource to fetch.
     *
     * @param string $uiVersion The Pinned UI version of the Configuration resource to fetch.
     * @return $this Fluent Builder
     */
    public function setUiVersion(string $uiVersion): self
    {
        $this->options['uiVersion'] = $uiVersion;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.FlexApi.V1.FetchConfigurationOptions ' . $options . ']';
    }
}


