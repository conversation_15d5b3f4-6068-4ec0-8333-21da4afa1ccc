<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\FlexApi\V1\Plugin;

use Twilio\Options;
use Twilio\Values;
use Twilio\Version;

abstract class PluginVersionsOptions
{
    /**
     * @param string $changelog The changelog of the Flex Plugin Version.
     * @param bool $private Whether this Flex Plugin Version requires authorization.
     * @param string $cliVersion The version of Flex Plugins CLI used to create this plugin
     * @param string $validateStatus The validation status of the plugin, indicating whether it has been validated
     * @param string $flexMetadata The Flex-Metadata HTTP request header
     * @return CreatePluginVersionsOptions Options builder
     */
    public static function create(
        
        string $changelog = Values::NONE,
        bool $private = Values::BOOL_NONE,
        string $cliVersion = Values::NONE,
        string $validateStatus = Values::NONE,
        string $flexMetadata = Values::NONE

    ): CreatePluginVersionsOptions
    {
        return new CreatePluginVersionsOptions(
            $changelog,
            $private,
            $cliVersion,
            $validateStatus,
            $flexMetadata
        );
    }

    /**
     * @param string $flexMetadata The Flex-Metadata HTTP request header
     * @return FetchPluginVersionsOptions Options builder
     */
    public static function fetch(
        
        string $flexMetadata = Values::NONE

    ): FetchPluginVersionsOptions
    {
        return new FetchPluginVersionsOptions(
            $flexMetadata
        );
    }

    /**
     * @param string $flexMetadata The Flex-Metadata HTTP request header
     * @return ReadPluginVersionsOptions Options builder
     */
    public static function read(
        
        string $flexMetadata = Values::NONE

    ): ReadPluginVersionsOptions
    {
        return new ReadPluginVersionsOptions(
            $flexMetadata
        );
    }

}

class CreatePluginVersionsOptions extends Options
    {
    /**
     * @param string $changelog The changelog of the Flex Plugin Version.
     * @param bool $private Whether this Flex Plugin Version requires authorization.
     * @param string $cliVersion The version of Flex Plugins CLI used to create this plugin
     * @param string $validateStatus The validation status of the plugin, indicating whether it has been validated
     * @param string $flexMetadata The Flex-Metadata HTTP request header
     */
    public function __construct(
        
        string $changelog = Values::NONE,
        bool $private = Values::BOOL_NONE,
        string $cliVersion = Values::NONE,
        string $validateStatus = Values::NONE,
        string $flexMetadata = Values::NONE

    ) {
        $this->options['changelog'] = $changelog;
        $this->options['private'] = $private;
        $this->options['cliVersion'] = $cliVersion;
        $this->options['validateStatus'] = $validateStatus;
        $this->options['flexMetadata'] = $flexMetadata;
    }

    /**
     * The changelog of the Flex Plugin Version.
     *
     * @param string $changelog The changelog of the Flex Plugin Version.
     * @return $this Fluent Builder
     */
    public function setChangelog(string $changelog): self
    {
        $this->options['changelog'] = $changelog;
        return $this;
    }

    /**
     * Whether this Flex Plugin Version requires authorization.
     *
     * @param bool $private Whether this Flex Plugin Version requires authorization.
     * @return $this Fluent Builder
     */
    public function setPrivate(bool $private): self
    {
        $this->options['private'] = $private;
        return $this;
    }

    /**
     * The version of Flex Plugins CLI used to create this plugin
     *
     * @param string $cliVersion The version of Flex Plugins CLI used to create this plugin
     * @return $this Fluent Builder
     */
    public function setCliVersion(string $cliVersion): self
    {
        $this->options['cliVersion'] = $cliVersion;
        return $this;
    }

    /**
     * The validation status of the plugin, indicating whether it has been validated
     *
     * @param string $validateStatus The validation status of the plugin, indicating whether it has been validated
     * @return $this Fluent Builder
     */
    public function setValidateStatus(string $validateStatus): self
    {
        $this->options['validateStatus'] = $validateStatus;
        return $this;
    }

    /**
     * The Flex-Metadata HTTP request header
     *
     * @param string $flexMetadata The Flex-Metadata HTTP request header
     * @return $this Fluent Builder
     */
    public function setFlexMetadata(string $flexMetadata): self
    {
        $this->options['flexMetadata'] = $flexMetadata;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.FlexApi.V1.CreatePluginVersionsOptions ' . $options . ']';
    }
}

class FetchPluginVersionsOptions extends Options
    {
    /**
     * @param string $flexMetadata The Flex-Metadata HTTP request header
     */
    public function __construct(
        
        string $flexMetadata = Values::NONE

    ) {
        $this->options['flexMetadata'] = $flexMetadata;
    }

    /**
     * The Flex-Metadata HTTP request header
     *
     * @param string $flexMetadata The Flex-Metadata HTTP request header
     * @return $this Fluent Builder
     */
    public function setFlexMetadata(string $flexMetadata): self
    {
        $this->options['flexMetadata'] = $flexMetadata;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.FlexApi.V1.FetchPluginVersionsOptions ' . $options . ']';
    }
}

class ReadPluginVersionsOptions extends Options
    {
    /**
     * @param string $flexMetadata The Flex-Metadata HTTP request header
     */
    public function __construct(
        
        string $flexMetadata = Values::NONE

    ) {
        $this->options['flexMetadata'] = $flexMetadata;
    }

    /**
     * The Flex-Metadata HTTP request header
     *
     * @param string $flexMetadata The Flex-Metadata HTTP request header
     * @return $this Fluent Builder
     */
    public function setFlexMetadata(string $flexMetadata): self
    {
        $this->options['flexMetadata'] = $flexMetadata;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.FlexApi.V1.ReadPluginVersionsOptions ' . $options . ']';
    }
}

