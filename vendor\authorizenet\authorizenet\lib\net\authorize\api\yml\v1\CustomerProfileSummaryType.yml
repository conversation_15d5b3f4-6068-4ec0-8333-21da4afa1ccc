net\authorize\api\contract\v1\CustomerProfileSummaryType:
    properties:
        customerProfileId:
            expose: true
            access_type: public_method
            serialized_name: customerProfileId
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getCustomerProfileId
                setter: setCustomerProfileId
            type: string
        description:
            expose: true
            access_type: public_method
            serialized_name: description
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getDescription
                setter: setDescription
            type: string
        merchantCustomerId:
            expose: true
            access_type: public_method
            serialized_name: merchantCustomerId
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getMerchantCustomerId
                setter: setMerchantCustomerId
            type: string
        email:
            expose: true
            access_type: public_method
            serialized_name: email
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getEmail
                setter: setEmail
            type: string
        createdDate:
            expose: true
            access_type: public_method
            serialized_name: createdDate
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getCreatedDate
                setter: setCreatedDate
            type: GoetasWebservices\Xsd\XsdToPhp\XMLSchema\DateTime
