<?php
// This file was auto-generated from sdk-root/src/data/m2/2021-04-28/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2021-04-28', 'endpointPrefix' => 'm2', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWSMainframeModernization', 'serviceId' => 'm2', 'signatureVersion' => 'v4', 'signingName' => 'm2', 'uid' => 'm2-2021-04-28', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'CancelBatchJobExecution' => [ 'name' => 'CancelBatchJobExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/batch-job-executions/{executionId}/cancel', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelBatchJobExecutionRequest', ], 'output' => [ 'shape' => 'CancelBatchJobExecutionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateApplication' => [ 'name' => 'CreateApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateApplicationRequest', ], 'output' => [ 'shape' => 'CreateApplicationResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateDataSetExportTask' => [ 'name' => 'CreateDataSetExportTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/dataset-export-task', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateDataSetExportTaskRequest', ], 'output' => [ 'shape' => 'CreateDataSetExportTaskResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateDataSetImportTask' => [ 'name' => 'CreateDataSetImportTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/dataset-import-task', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateDataSetImportTaskRequest', ], 'output' => [ 'shape' => 'CreateDataSetImportTaskResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateDeployment' => [ 'name' => 'CreateDeployment', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/deployments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateDeploymentRequest', ], 'output' => [ 'shape' => 'CreateDeploymentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateEnvironment' => [ 'name' => 'CreateEnvironment', 'http' => [ 'method' => 'POST', 'requestUri' => '/environments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateEnvironmentRequest', ], 'output' => [ 'shape' => 'CreateEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteApplication' => [ 'name' => 'DeleteApplication', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{applicationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteApplicationRequest', ], 'output' => [ 'shape' => 'DeleteApplicationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteApplicationFromEnvironment' => [ 'name' => 'DeleteApplicationFromEnvironment', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{applicationId}/environment/{environmentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteApplicationFromEnvironmentRequest', ], 'output' => [ 'shape' => 'DeleteApplicationFromEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteEnvironment' => [ 'name' => 'DeleteEnvironment', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/environments/{environmentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteEnvironmentRequest', ], 'output' => [ 'shape' => 'DeleteEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'GetApplication' => [ 'name' => 'GetApplication', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetApplicationRequest', ], 'output' => [ 'shape' => 'GetApplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetApplicationVersion' => [ 'name' => 'GetApplicationVersion', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/versions/{applicationVersion}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetApplicationVersionRequest', ], 'output' => [ 'shape' => 'GetApplicationVersionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetBatchJobExecution' => [ 'name' => 'GetBatchJobExecution', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/batch-job-executions/{executionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBatchJobExecutionRequest', ], 'output' => [ 'shape' => 'GetBatchJobExecutionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetDataSetDetails' => [ 'name' => 'GetDataSetDetails', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/datasets/{dataSetName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDataSetDetailsRequest', ], 'output' => [ 'shape' => 'GetDataSetDetailsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ExecutionTimeoutException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetDataSetExportTask' => [ 'name' => 'GetDataSetExportTask', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/dataset-export-tasks/{taskId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDataSetExportTaskRequest', ], 'output' => [ 'shape' => 'GetDataSetExportTaskResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetDataSetImportTask' => [ 'name' => 'GetDataSetImportTask', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/dataset-import-tasks/{taskId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDataSetImportTaskRequest', ], 'output' => [ 'shape' => 'GetDataSetImportTaskResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetDeployment' => [ 'name' => 'GetDeployment', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/deployments/{deploymentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDeploymentRequest', ], 'output' => [ 'shape' => 'GetDeploymentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetEnvironment' => [ 'name' => 'GetEnvironment', 'http' => [ 'method' => 'GET', 'requestUri' => '/environments/{environmentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEnvironmentRequest', ], 'output' => [ 'shape' => 'GetEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetSignedBluinsightsUrl' => [ 'name' => 'GetSignedBluinsightsUrl', 'http' => [ 'method' => 'GET', 'requestUri' => '/signed-bi-url', 'responseCode' => 200, ], 'output' => [ 'shape' => 'GetSignedBluinsightsUrlResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListApplicationVersions' => [ 'name' => 'ListApplicationVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListApplicationVersionsRequest', ], 'output' => [ 'shape' => 'ListApplicationVersionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListApplications' => [ 'name' => 'ListApplications', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListApplicationsRequest', ], 'output' => [ 'shape' => 'ListApplicationsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListBatchJobDefinitions' => [ 'name' => 'ListBatchJobDefinitions', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/batch-job-definitions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBatchJobDefinitionsRequest', ], 'output' => [ 'shape' => 'ListBatchJobDefinitionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListBatchJobExecutions' => [ 'name' => 'ListBatchJobExecutions', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/batch-job-executions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBatchJobExecutionsRequest', ], 'output' => [ 'shape' => 'ListBatchJobExecutionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListBatchJobRestartPoints' => [ 'name' => 'ListBatchJobRestartPoints', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/batch-job-executions/{executionId}/steps', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBatchJobRestartPointsRequest', ], 'output' => [ 'shape' => 'ListBatchJobRestartPointsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDataSetExportHistory' => [ 'name' => 'ListDataSetExportHistory', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/dataset-export-tasks', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDataSetExportHistoryRequest', ], 'output' => [ 'shape' => 'ListDataSetExportHistoryResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDataSetImportHistory' => [ 'name' => 'ListDataSetImportHistory', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/dataset-import-tasks', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDataSetImportHistoryRequest', ], 'output' => [ 'shape' => 'ListDataSetImportHistoryResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDataSets' => [ 'name' => 'ListDataSets', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/datasets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDataSetsRequest', ], 'output' => [ 'shape' => 'ListDataSetsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ExecutionTimeoutException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDeployments' => [ 'name' => 'ListDeployments', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{applicationId}/deployments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDeploymentsRequest', ], 'output' => [ 'shape' => 'ListDeploymentsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListEngineVersions' => [ 'name' => 'ListEngineVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/engine-versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEngineVersionsRequest', ], 'output' => [ 'shape' => 'ListEngineVersionsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListEnvironments' => [ 'name' => 'ListEnvironments', 'http' => [ 'method' => 'GET', 'requestUri' => '/environments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEnvironmentsRequest', ], 'output' => [ 'shape' => 'ListEnvironmentsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartApplication' => [ 'name' => 'StartApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/start', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartApplicationRequest', ], 'output' => [ 'shape' => 'StartApplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartBatchJob' => [ 'name' => 'StartBatchJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/batch-job', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartBatchJobRequest', ], 'output' => [ 'shape' => 'StartBatchJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StopApplication' => [ 'name' => 'StopApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications/{applicationId}/stop', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopApplicationRequest', ], 'output' => [ 'shape' => 'StopApplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateApplication' => [ 'name' => 'UpdateApplication', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/applications/{applicationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateApplicationRequest', ], 'output' => [ 'shape' => 'UpdateApplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateEnvironment' => [ 'name' => 'UpdateEnvironment', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/environments/{environmentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateEnvironmentRequest', ], 'output' => [ 'shape' => 'UpdateEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AlternateKey' => [ 'type' => 'structure', 'required' => [ 'length', 'offset', ], 'members' => [ 'allowDuplicates' => [ 'shape' => 'Boolean', ], 'length' => [ 'shape' => 'Integer', ], 'name' => [ 'shape' => 'String', ], 'offset' => [ 'shape' => 'Integer', ], ], ], 'AlternateKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AlternateKey', ], ], 'ApplicationDeploymentLifecycle' => [ 'type' => 'string', 'enum' => [ 'Deploying', 'Deployed', ], ], 'ApplicationLifecycle' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Created', 'Available', 'Ready', 'Starting', 'Running', 'Stopping', 'Stopped', 'Failed', 'Deleting', 'Deleting From Environment', ], ], 'ApplicationSummary' => [ 'type' => 'structure', 'required' => [ 'applicationArn', 'applicationId', 'applicationVersion', 'creationTime', 'engineType', 'name', 'status', ], 'members' => [ 'applicationArn' => [ 'shape' => 'Arn', ], 'applicationId' => [ 'shape' => 'Identifier', ], 'applicationVersion' => [ 'shape' => 'Version', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'deploymentStatus' => [ 'shape' => 'ApplicationDeploymentLifecycle', ], 'description' => [ 'shape' => 'EntityDescription', ], 'engineType' => [ 'shape' => 'EngineType', ], 'environmentId' => [ 'shape' => 'Identifier', ], 'lastStartTime' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'EntityName', ], 'roleArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'ApplicationLifecycle', ], 'versionStatus' => [ 'shape' => 'ApplicationVersionLifecycle', ], ], ], 'ApplicationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationSummary', ], ], 'ApplicationVersionLifecycle' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Available', 'Failed', ], ], 'ApplicationVersionSummary' => [ 'type' => 'structure', 'required' => [ 'applicationVersion', 'creationTime', 'status', ], 'members' => [ 'applicationVersion' => [ 'shape' => 'Version', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'ApplicationVersionLifecycle', ], 'statusReason' => [ 'shape' => 'String', ], ], ], 'ApplicationVersionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationVersionSummary', ], ], 'Arn' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-cn|aws-iso|aws-iso-[a-z]{1}|aws-us-gov):[A-Za-z0-9][A-Za-z0-9_/.-]{0,62}:([a-z]{2}-((iso[a-z]{0,1}-)|(gov-)){0,1}[a-z]+-[0-9]|):[0-9]{12}:[A-Za-z0-9/][A-Za-z0-9:_/+=,@.-]{0,1023}$', ], 'ArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], 'min' => 1, ], 'AuthSecretsManagerArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, ], 'BatchJobDefinition' => [ 'type' => 'structure', 'members' => [ 'fileBatchJobDefinition' => [ 'shape' => 'FileBatchJobDefinition', ], 'scriptBatchJobDefinition' => [ 'shape' => 'ScriptBatchJobDefinition', ], ], 'union' => true, ], 'BatchJobDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchJobDefinition', ], ], 'BatchJobExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'Submitting', 'Holding', 'Dispatching', 'Running', 'Cancelling', 'Cancelled', 'Succeeded', 'Failed', 'Purged', 'Succeeded With Warning', ], ], 'BatchJobExecutionSummary' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'executionId', 'startTime', 'status', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', ], 'batchJobIdentifier' => [ 'shape' => 'BatchJobIdentifier', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'executionId' => [ 'shape' => 'Identifier', ], 'jobId' => [ 'shape' => 'String100', ], 'jobName' => [ 'shape' => 'String100', ], 'jobType' => [ 'shape' => 'BatchJobType', ], 'returnCode' => [ 'shape' => 'String', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'BatchJobExecutionStatus', ], ], ], 'BatchJobExecutionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchJobExecutionSummary', ], ], 'BatchJobIdentifier' => [ 'type' => 'structure', 'members' => [ 'fileBatchJobIdentifier' => [ 'shape' => 'FileBatchJobIdentifier', ], 'restartBatchJobIdentifier' => [ 'shape' => 'RestartBatchJobIdentifier', ], 's3BatchJobIdentifier' => [ 'shape' => 'S3BatchJobIdentifier', ], 'scriptBatchJobIdentifier' => [ 'shape' => 'ScriptBatchJobIdentifier', ], ], 'union' => true, ], 'BatchJobParametersMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'BatchParamKey', ], 'value' => [ 'shape' => 'BatchParamValue', ], 'max' => 500, 'min' => 0, ], 'BatchJobStepList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobStep', ], ], 'BatchJobType' => [ 'type' => 'string', 'enum' => [ 'VSE', 'JES2', 'JES3', ], ], 'BatchParamKey' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^[A-Za-z][A-Za-z0-9]{1,31}$', ], 'BatchParamValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'Boolean' => [ 'type' => 'boolean', ], 'CancelBatchJobExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'executionId', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], 'authSecretsManagerArn' => [ 'shape' => 'AuthSecretsManagerArn', ], 'executionId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'executionId', ], ], ], 'CancelBatchJobExecutionResponse' => [ 'type' => 'structure', 'members' => [], ], 'CapacityValue' => [ 'type' => 'integer', 'box' => true, ], 'ClientToken' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^[!-~]+$', ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'definition', 'engineType', 'name', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'definition' => [ 'shape' => 'Definition', ], 'description' => [ 'shape' => 'EntityDescription', ], 'engineType' => [ 'shape' => 'EngineType', ], 'kmsKeyId' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'EntityName', ], 'roleArn' => [ 'shape' => 'Arn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateApplicationResponse' => [ 'type' => 'structure', 'required' => [ 'applicationArn', 'applicationId', 'applicationVersion', ], 'members' => [ 'applicationArn' => [ 'shape' => 'Arn', ], 'applicationId' => [ 'shape' => 'Identifier', ], 'applicationVersion' => [ 'shape' => 'Version', ], ], ], 'CreateDataSetExportTaskRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'exportConfig', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'exportConfig' => [ 'shape' => 'DataSetExportConfig', ], 'kmsKeyId' => [ 'shape' => 'KMSKeyId', ], ], ], 'CreateDataSetExportTaskResponse' => [ 'type' => 'structure', 'required' => [ 'taskId', ], 'members' => [ 'taskId' => [ 'shape' => 'Identifier', ], ], ], 'CreateDataSetImportTaskRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'importConfig', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'importConfig' => [ 'shape' => 'DataSetImportConfig', ], ], ], 'CreateDataSetImportTaskResponse' => [ 'type' => 'structure', 'required' => [ 'taskId', ], 'members' => [ 'taskId' => [ 'shape' => 'Identifier', ], ], ], 'CreateDeploymentRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'applicationVersion', 'environmentId', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], 'applicationVersion' => [ 'shape' => 'Version', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'environmentId' => [ 'shape' => 'Identifier', ], ], ], 'CreateDeploymentResponse' => [ 'type' => 'structure', 'required' => [ 'deploymentId', ], 'members' => [ 'deploymentId' => [ 'shape' => 'Identifier', ], ], ], 'CreateEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'engineType', 'instanceType', 'name', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'EntityDescription', ], 'engineType' => [ 'shape' => 'EngineType', ], 'engineVersion' => [ 'shape' => 'EngineVersion', ], 'highAvailabilityConfig' => [ 'shape' => 'HighAvailabilityConfig', ], 'instanceType' => [ 'shape' => 'String20', ], 'kmsKeyId' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'EntityName', ], 'networkType' => [ 'shape' => 'NetworkType', ], 'preferredMaintenanceWindow' => [ 'shape' => 'String50', ], 'publiclyAccessible' => [ 'shape' => 'Boolean', ], 'securityGroupIds' => [ 'shape' => 'String50List', ], 'storageConfigurations' => [ 'shape' => 'StorageConfigurationList', ], 'subnetIds' => [ 'shape' => 'String50List', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateEnvironmentResponse' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'Identifier', ], ], ], 'DataSet' => [ 'type' => 'structure', 'required' => [ 'datasetName', 'datasetOrg', 'recordLength', ], 'members' => [ 'datasetName' => [ 'shape' => 'String', ], 'datasetOrg' => [ 'shape' => 'DatasetOrgAttributes', ], 'recordLength' => [ 'shape' => 'RecordLength', ], 'relativePath' => [ 'shape' => 'String', ], 'storageType' => [ 'shape' => 'String', ], ], ], 'DataSetExportConfig' => [ 'type' => 'structure', 'members' => [ 'dataSets' => [ 'shape' => 'DataSetExportList', ], 's3Location' => [ 'shape' => 'String', ], ], 'union' => true, ], 'DataSetExportItem' => [ 'type' => 'structure', 'required' => [ 'datasetName', 'externalLocation', ], 'members' => [ 'datasetName' => [ 'shape' => 'String200', ], 'externalLocation' => [ 'shape' => 'ExternalLocation', ], ], ], 'DataSetExportList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSetExportItem', ], 'max' => 1024, 'min' => 1, ], 'DataSetExportSummary' => [ 'type' => 'structure', 'required' => [ 'failed', 'inProgress', 'pending', 'succeeded', 'total', ], 'members' => [ 'failed' => [ 'shape' => 'Integer', ], 'inProgress' => [ 'shape' => 'Integer', ], 'pending' => [ 'shape' => 'Integer', ], 'succeeded' => [ 'shape' => 'Integer', ], 'total' => [ 'shape' => 'Integer', ], ], ], 'DataSetExportTask' => [ 'type' => 'structure', 'required' => [ 'status', 'summary', 'taskId', ], 'members' => [ 'status' => [ 'shape' => 'DataSetTaskLifecycle', ], 'statusReason' => [ 'shape' => 'String', ], 'summary' => [ 'shape' => 'DataSetExportSummary', ], 'taskId' => [ 'shape' => 'Identifier', ], ], ], 'DataSetExportTaskList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSetExportTask', ], ], 'DataSetImportConfig' => [ 'type' => 'structure', 'members' => [ 'dataSets' => [ 'shape' => 'DataSetImportList', ], 's3Location' => [ 'shape' => 'String2000', ], ], 'union' => true, ], 'DataSetImportItem' => [ 'type' => 'structure', 'required' => [ 'dataSet', 'externalLocation', ], 'members' => [ 'dataSet' => [ 'shape' => 'DataSet', ], 'externalLocation' => [ 'shape' => 'ExternalLocation', ], ], ], 'DataSetImportList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSetImportItem', ], 'max' => 100, 'min' => 1, ], 'DataSetImportSummary' => [ 'type' => 'structure', 'required' => [ 'failed', 'inProgress', 'pending', 'succeeded', 'total', ], 'members' => [ 'failed' => [ 'shape' => 'Integer', ], 'inProgress' => [ 'shape' => 'Integer', ], 'pending' => [ 'shape' => 'Integer', ], 'succeeded' => [ 'shape' => 'Integer', ], 'total' => [ 'shape' => 'Integer', ], ], ], 'DataSetImportTask' => [ 'type' => 'structure', 'required' => [ 'status', 'summary', 'taskId', ], 'members' => [ 'status' => [ 'shape' => 'DataSetTaskLifecycle', ], 'statusReason' => [ 'shape' => 'String', ], 'summary' => [ 'shape' => 'DataSetImportSummary', ], 'taskId' => [ 'shape' => 'Identifier', ], ], ], 'DataSetImportTaskList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSetImportTask', ], ], 'DataSetSummary' => [ 'type' => 'structure', 'required' => [ 'dataSetName', ], 'members' => [ 'creationTime' => [ 'shape' => 'Timestamp', ], 'dataSetName' => [ 'shape' => 'String200', ], 'dataSetOrg' => [ 'shape' => 'String20', ], 'format' => [ 'shape' => 'String20', ], 'lastReferencedTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedTime' => [ 'shape' => 'Timestamp', ], ], ], 'DataSetTaskLifecycle' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Running', 'Completed', 'Failed', ], ], 'DataSetsSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSetSummary', ], ], 'DatasetDetailOrgAttributes' => [ 'type' => 'structure', 'members' => [ 'gdg' => [ 'shape' => 'GdgDetailAttributes', ], 'po' => [ 'shape' => 'PoDetailAttributes', ], 'ps' => [ 'shape' => 'PsDetailAttributes', ], 'vsam' => [ 'shape' => 'VsamDetailAttributes', ], ], 'union' => true, ], 'DatasetOrgAttributes' => [ 'type' => 'structure', 'members' => [ 'gdg' => [ 'shape' => 'GdgAttributes', ], 'po' => [ 'shape' => 'PoAttributes', ], 'ps' => [ 'shape' => 'PsAttributes', ], 'vsam' => [ 'shape' => 'VsamAttributes', ], ], 'union' => true, ], 'Definition' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'StringFree65000', ], 's3Location' => [ 'shape' => 'String2000', ], ], 'union' => true, ], 'DeleteApplicationFromEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'environmentId', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], 'environmentId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'environmentId', ], ], ], 'DeleteApplicationFromEnvironmentResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], ], ], 'DeleteApplicationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'environmentId', ], ], ], 'DeleteEnvironmentResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeployedVersionSummary' => [ 'type' => 'structure', 'required' => [ 'applicationVersion', 'status', ], 'members' => [ 'applicationVersion' => [ 'shape' => 'Version', ], 'status' => [ 'shape' => 'DeploymentLifecycle', ], 'statusReason' => [ 'shape' => 'String', ], ], ], 'DeploymentLifecycle' => [ 'type' => 'string', 'enum' => [ 'Deploying', 'Succeeded', 'Failed', 'Updating Deployment', ], ], 'DeploymentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeploymentSummary', ], ], 'DeploymentSummary' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'applicationVersion', 'creationTime', 'deploymentId', 'environmentId', 'status', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', ], 'applicationVersion' => [ 'shape' => 'Version', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'deploymentId' => [ 'shape' => 'Identifier', ], 'environmentId' => [ 'shape' => 'Identifier', ], 'status' => [ 'shape' => 'DeploymentLifecycle', ], 'statusReason' => [ 'shape' => 'String', ], ], ], 'EfsStorageConfiguration' => [ 'type' => 'structure', 'required' => [ 'fileSystemId', 'mountPoint', ], 'members' => [ 'fileSystemId' => [ 'shape' => 'String200', 'locationName' => 'file-system-id', ], 'mountPoint' => [ 'shape' => 'String200', 'locationName' => 'mount-point', ], ], ], 'EngineType' => [ 'type' => 'string', 'enum' => [ 'microfocus', 'bluage', ], ], 'EngineVersion' => [ 'type' => 'string', 'pattern' => '^\\S{1,10}$', ], 'EngineVersionsSummary' => [ 'type' => 'structure', 'required' => [ 'engineType', 'engineVersion', ], 'members' => [ 'engineType' => [ 'shape' => 'String', ], 'engineVersion' => [ 'shape' => 'String', ], ], ], 'EngineVersionsSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EngineVersionsSummary', ], ], 'EntityDescription' => [ 'type' => 'string', 'max' => 500, 'min' => 0, ], 'EntityName' => [ 'type' => 'string', 'pattern' => '^[A-Za-z0-9][A-Za-z0-9_\\-]{1,59}$', ], 'EntityNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntityName', ], 'max' => 10, 'min' => 1, ], 'EnvironmentLifecycle' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Available', 'Updating', 'Deleting', 'Failed', 'UnHealthy', ], ], 'EnvironmentSummary' => [ 'type' => 'structure', 'required' => [ 'creationTime', 'engineType', 'engineVersion', 'environmentArn', 'environmentId', 'instanceType', 'name', 'status', ], 'members' => [ 'creationTime' => [ 'shape' => 'Timestamp', ], 'engineType' => [ 'shape' => 'EngineType', ], 'engineVersion' => [ 'shape' => 'EngineVersion', ], 'environmentArn' => [ 'shape' => 'Arn', ], 'environmentId' => [ 'shape' => 'Identifier', ], 'instanceType' => [ 'shape' => 'String20', ], 'name' => [ 'shape' => 'EntityName', ], 'networkType' => [ 'shape' => 'NetworkType', ], 'status' => [ 'shape' => 'EnvironmentLifecycle', ], ], ], 'EnvironmentSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnvironmentSummary', ], ], 'ExecutionTimeoutException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 504, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'ExternalLocation' => [ 'type' => 'structure', 'members' => [ 's3Location' => [ 'shape' => 'String2000', ], ], 'union' => true, ], 'FileBatchJobDefinition' => [ 'type' => 'structure', 'required' => [ 'fileName', ], 'members' => [ 'fileName' => [ 'shape' => 'String', ], 'folderPath' => [ 'shape' => 'String', ], ], ], 'FileBatchJobIdentifier' => [ 'type' => 'structure', 'required' => [ 'fileName', ], 'members' => [ 'fileName' => [ 'shape' => 'String', ], 'folderPath' => [ 'shape' => 'String', ], ], ], 'FsxStorageConfiguration' => [ 'type' => 'structure', 'required' => [ 'fileSystemId', 'mountPoint', ], 'members' => [ 'fileSystemId' => [ 'shape' => 'String200', 'locationName' => 'file-system-id', ], 'mountPoint' => [ 'shape' => 'String200', 'locationName' => 'mount-point', ], ], ], 'GdgAttributes' => [ 'type' => 'structure', 'members' => [ 'limit' => [ 'shape' => 'Integer', ], 'rollDisposition' => [ 'shape' => 'String', ], ], ], 'GdgDetailAttributes' => [ 'type' => 'structure', 'members' => [ 'limit' => [ 'shape' => 'Integer', ], 'rollDisposition' => [ 'shape' => 'String50', ], ], ], 'GetApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], ], ], 'GetApplicationResponse' => [ 'type' => 'structure', 'required' => [ 'applicationArn', 'applicationId', 'creationTime', 'engineType', 'latestVersion', 'name', 'status', ], 'members' => [ 'applicationArn' => [ 'shape' => 'Arn', ], 'applicationId' => [ 'shape' => 'Identifier', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'deployedVersion' => [ 'shape' => 'DeployedVersionSummary', ], 'description' => [ 'shape' => 'EntityDescription', ], 'engineType' => [ 'shape' => 'EngineType', ], 'environmentId' => [ 'shape' => 'Identifier', ], 'kmsKeyId' => [ 'shape' => 'String', ], 'lastStartTime' => [ 'shape' => 'Timestamp', ], 'latestVersion' => [ 'shape' => 'ApplicationVersionSummary', ], 'listenerArns' => [ 'shape' => 'ArnList', ], 'listenerPorts' => [ 'shape' => 'PortList', ], 'loadBalancerDnsName' => [ 'shape' => 'String100', ], 'logGroups' => [ 'shape' => 'LogGroupSummaries', ], 'name' => [ 'shape' => 'EntityName', ], 'roleArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'ApplicationLifecycle', ], 'statusReason' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'TagMap', ], 'targetGroupArns' => [ 'shape' => 'ArnList', ], ], ], 'GetApplicationVersionRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'applicationVersion', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], 'applicationVersion' => [ 'shape' => 'Version', 'location' => 'uri', 'locationName' => 'applicationVersion', ], ], ], 'GetApplicationVersionResponse' => [ 'type' => 'structure', 'required' => [ 'applicationVersion', 'creationTime', 'definitionContent', 'name', 'status', ], 'members' => [ 'applicationVersion' => [ 'shape' => 'Version', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'definitionContent' => [ 'shape' => 'StringFree65000', ], 'description' => [ 'shape' => 'EntityDescription', ], 'name' => [ 'shape' => 'EntityName', ], 'status' => [ 'shape' => 'ApplicationVersionLifecycle', ], 'statusReason' => [ 'shape' => 'String', ], ], ], 'GetBatchJobExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'executionId', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], 'executionId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'executionId', ], ], ], 'GetBatchJobExecutionResponse' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'executionId', 'startTime', 'status', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', ], 'batchJobIdentifier' => [ 'shape' => 'BatchJobIdentifier', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'executionId' => [ 'shape' => 'Identifier', ], 'jobId' => [ 'shape' => 'String100', ], 'jobName' => [ 'shape' => 'String100', ], 'jobStepRestartMarker' => [ 'shape' => 'JobStepRestartMarker', ], 'jobType' => [ 'shape' => 'BatchJobType', ], 'jobUser' => [ 'shape' => 'String100', ], 'returnCode' => [ 'shape' => 'String', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'BatchJobExecutionStatus', ], 'statusReason' => [ 'shape' => 'String', ], ], ], 'GetDataSetDetailsRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'dataSetName', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], 'dataSetName' => [ 'shape' => 'String200', 'location' => 'uri', 'locationName' => 'dataSetName', ], ], ], 'GetDataSetDetailsResponse' => [ 'type' => 'structure', 'required' => [ 'dataSetName', ], 'members' => [ 'blocksize' => [ 'shape' => 'Integer', 'box' => true, ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'dataSetName' => [ 'shape' => 'String200', ], 'dataSetOrg' => [ 'shape' => 'DatasetDetailOrgAttributes', ], 'fileSize' => [ 'shape' => 'Long', ], 'lastReferencedTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'location' => [ 'shape' => 'String2000', ], 'recordLength' => [ 'shape' => 'Integer', 'box' => true, ], ], ], 'GetDataSetExportTaskRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'taskId', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], 'taskId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'taskId', ], ], ], 'GetDataSetExportTaskResponse' => [ 'type' => 'structure', 'required' => [ 'status', 'taskId', ], 'members' => [ 'kmsKeyArn' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'DataSetTaskLifecycle', ], 'statusReason' => [ 'shape' => 'String', ], 'summary' => [ 'shape' => 'DataSetExportSummary', ], 'taskId' => [ 'shape' => 'Identifier', ], ], ], 'GetDataSetImportTaskRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'taskId', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], 'taskId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'taskId', ], ], ], 'GetDataSetImportTaskResponse' => [ 'type' => 'structure', 'required' => [ 'status', 'taskId', ], 'members' => [ 'status' => [ 'shape' => 'DataSetTaskLifecycle', ], 'summary' => [ 'shape' => 'DataSetImportSummary', ], 'taskId' => [ 'shape' => 'Identifier', ], ], ], 'GetDeploymentRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'deploymentId', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], 'deploymentId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'deploymentId', ], ], ], 'GetDeploymentResponse' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'applicationVersion', 'creationTime', 'deploymentId', 'environmentId', 'status', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', ], 'applicationVersion' => [ 'shape' => 'Version', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'deploymentId' => [ 'shape' => 'Identifier', ], 'environmentId' => [ 'shape' => 'Identifier', ], 'status' => [ 'shape' => 'DeploymentLifecycle', ], 'statusReason' => [ 'shape' => 'String', ], ], ], 'GetEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'environmentId', ], ], ], 'GetEnvironmentResponse' => [ 'type' => 'structure', 'required' => [ 'creationTime', 'engineType', 'engineVersion', 'environmentArn', 'environmentId', 'instanceType', 'name', 'securityGroupIds', 'status', 'subnetIds', 'vpcId', ], 'members' => [ 'actualCapacity' => [ 'shape' => 'CapacityValue', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'EntityDescription', ], 'engineType' => [ 'shape' => 'EngineType', ], 'engineVersion' => [ 'shape' => 'EngineVersion', ], 'environmentArn' => [ 'shape' => 'Arn', ], 'environmentId' => [ 'shape' => 'Identifier', ], 'highAvailabilityConfig' => [ 'shape' => 'HighAvailabilityConfig', ], 'instanceType' => [ 'shape' => 'String20', ], 'kmsKeyId' => [ 'shape' => 'String', ], 'loadBalancerArn' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'EntityName', ], 'networkType' => [ 'shape' => 'NetworkType', ], 'pendingMaintenance' => [ 'shape' => 'PendingMaintenance', ], 'preferredMaintenanceWindow' => [ 'shape' => 'String50', ], 'publiclyAccessible' => [ 'shape' => 'Boolean', ], 'securityGroupIds' => [ 'shape' => 'String50List', ], 'status' => [ 'shape' => 'EnvironmentLifecycle', ], 'statusReason' => [ 'shape' => 'String', ], 'storageConfigurations' => [ 'shape' => 'StorageConfigurationList', ], 'subnetIds' => [ 'shape' => 'String50List', ], 'tags' => [ 'shape' => 'TagMap', ], 'vpcId' => [ 'shape' => 'String50', ], ], ], 'GetSignedBluinsightsUrlResponse' => [ 'type' => 'structure', 'required' => [ 'signedBiUrl', ], 'members' => [ 'signedBiUrl' => [ 'shape' => 'String', ], ], ], 'HighAvailabilityConfig' => [ 'type' => 'structure', 'required' => [ 'desiredCapacity', ], 'members' => [ 'desiredCapacity' => [ 'shape' => 'CapacityValue', ], ], ], 'Identifier' => [ 'type' => 'string', 'pattern' => '^\\S{1,80}$', ], 'IdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Identifier', ], 'max' => 10, 'min' => 1, ], 'Integer' => [ 'type' => 'integer', ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'JobIdentifier' => [ 'type' => 'structure', 'members' => [ 'fileName' => [ 'shape' => 'String', ], 'scriptName' => [ 'shape' => 'String', ], ], 'union' => true, ], 'JobStep' => [ 'type' => 'structure', 'members' => [ 'procStepName' => [ 'shape' => 'String', ], 'procStepNumber' => [ 'shape' => 'Integer', ], 'stepCheckpoint' => [ 'shape' => 'Integer', 'box' => true, ], 'stepCheckpointStatus' => [ 'shape' => 'String', ], 'stepCheckpointTime' => [ 'shape' => 'Timestamp', ], 'stepCondCode' => [ 'shape' => 'String', ], 'stepName' => [ 'shape' => 'String', ], 'stepNumber' => [ 'shape' => 'Integer', ], 'stepRestartable' => [ 'shape' => 'Boolean', ], ], ], 'JobStepRestartMarker' => [ 'type' => 'structure', 'required' => [ 'fromStep', ], 'members' => [ 'fromProcStep' => [ 'shape' => 'String', ], 'fromStep' => [ 'shape' => 'String', ], 'skip' => [ 'shape' => 'Boolean', 'box' => true, ], 'stepCheckpoint' => [ 'shape' => 'Integer', 'box' => true, ], 'toProcStep' => [ 'shape' => 'String', ], 'toStep' => [ 'shape' => 'String', ], ], ], 'KMSKeyId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[a-zA-Z0-9:/_-]+$', ], 'ListApplicationVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListApplicationVersionsResponse' => [ 'type' => 'structure', 'required' => [ 'applicationVersions', ], 'members' => [ 'applicationVersions' => [ 'shape' => 'ApplicationVersionSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListApplicationsRequest' => [ 'type' => 'structure', 'members' => [ 'environmentId' => [ 'shape' => 'Identifier', 'location' => 'querystring', 'locationName' => 'environmentId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'names' => [ 'shape' => 'EntityNameList', 'location' => 'querystring', 'locationName' => 'names', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListApplicationsResponse' => [ 'type' => 'structure', 'required' => [ 'applications', ], 'members' => [ 'applications' => [ 'shape' => 'ApplicationSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBatchJobDefinitionsRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'prefix' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'prefix', ], ], ], 'ListBatchJobDefinitionsResponse' => [ 'type' => 'structure', 'required' => [ 'batchJobDefinitions', ], 'members' => [ 'batchJobDefinitions' => [ 'shape' => 'BatchJobDefinitions', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBatchJobExecutionsRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], 'executionIds' => [ 'shape' => 'IdentifierList', 'location' => 'querystring', 'locationName' => 'executionIds', ], 'jobName' => [ 'shape' => 'String100', 'location' => 'querystring', 'locationName' => 'jobName', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'startedAfter' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'startedAfter', ], 'startedBefore' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'startedBefore', ], 'status' => [ 'shape' => 'BatchJobExecutionStatus', 'location' => 'querystring', 'locationName' => 'status', ], ], ], 'ListBatchJobExecutionsResponse' => [ 'type' => 'structure', 'required' => [ 'batchJobExecutions', ], 'members' => [ 'batchJobExecutions' => [ 'shape' => 'BatchJobExecutionSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBatchJobRestartPointsRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'executionId', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], 'authSecretsManagerArn' => [ 'shape' => 'AuthSecretsManagerArn', 'location' => 'querystring', 'locationName' => 'authSecretsManagerArn', ], 'executionId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'executionId', ], ], ], 'ListBatchJobRestartPointsResponse' => [ 'type' => 'structure', 'members' => [ 'batchJobSteps' => [ 'shape' => 'BatchJobStepList', ], ], ], 'ListDataSetExportHistoryRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListDataSetExportHistoryResponse' => [ 'type' => 'structure', 'required' => [ 'dataSetExportTasks', ], 'members' => [ 'dataSetExportTasks' => [ 'shape' => 'DataSetExportTaskList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDataSetImportHistoryRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListDataSetImportHistoryResponse' => [ 'type' => 'structure', 'required' => [ 'dataSetImportTasks', ], 'members' => [ 'dataSetImportTasks' => [ 'shape' => 'DataSetImportTaskList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDataSetsRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nameFilter' => [ 'shape' => 'String200', 'location' => 'querystring', 'locationName' => 'nameFilter', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'prefix' => [ 'shape' => 'String200', 'location' => 'querystring', 'locationName' => 'prefix', ], ], ], 'ListDataSetsResponse' => [ 'type' => 'structure', 'required' => [ 'dataSets', ], 'members' => [ 'dataSets' => [ 'shape' => 'DataSetsSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDeploymentsRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListDeploymentsResponse' => [ 'type' => 'structure', 'required' => [ 'deployments', ], 'members' => [ 'deployments' => [ 'shape' => 'DeploymentList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEngineVersionsRequest' => [ 'type' => 'structure', 'members' => [ 'engineType' => [ 'shape' => 'EngineType', 'location' => 'querystring', 'locationName' => 'engineType', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListEngineVersionsResponse' => [ 'type' => 'structure', 'required' => [ 'engineVersions', ], 'members' => [ 'engineVersions' => [ 'shape' => 'EngineVersionsSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEnvironmentsRequest' => [ 'type' => 'structure', 'members' => [ 'engineType' => [ 'shape' => 'EngineType', 'location' => 'querystring', 'locationName' => 'engineType', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'names' => [ 'shape' => 'EntityNameList', 'location' => 'querystring', 'locationName' => 'names', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListEnvironmentsResponse' => [ 'type' => 'structure', 'required' => [ 'environments', ], 'members' => [ 'environments' => [ 'shape' => 'EnvironmentSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'required' => [ 'tags', ], 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'LogGroupIdentifier' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'LogGroupSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogGroupSummary', ], ], 'LogGroupSummary' => [ 'type' => 'structure', 'required' => [ 'logGroupName', 'logType', ], 'members' => [ 'logGroupName' => [ 'shape' => 'LogGroupIdentifier', ], 'logType' => [ 'shape' => 'String20', ], ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'MaintenanceSchedule' => [ 'type' => 'structure', 'members' => [ 'endTime' => [ 'shape' => 'Timestamp', ], 'startTime' => [ 'shape' => 'Timestamp', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 2000, 'min' => 1, ], 'NetworkType' => [ 'type' => 'string', 'enum' => [ 'ipv4', 'dual', ], ], 'NextToken' => [ 'type' => 'string', 'pattern' => '^\\S{1,2000}$', ], 'PendingMaintenance' => [ 'type' => 'structure', 'members' => [ 'engineVersion' => [ 'shape' => 'String', ], 'schedule' => [ 'shape' => 'MaintenanceSchedule', ], ], ], 'PoAttributes' => [ 'type' => 'structure', 'required' => [ 'format', 'memberFileExtensions', ], 'members' => [ 'encoding' => [ 'shape' => 'String', ], 'format' => [ 'shape' => 'String', ], 'memberFileExtensions' => [ 'shape' => 'String20List', ], ], ], 'PoDetailAttributes' => [ 'type' => 'structure', 'required' => [ 'encoding', 'format', ], 'members' => [ 'encoding' => [ 'shape' => 'String', ], 'format' => [ 'shape' => 'String', ], ], ], 'PortList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Integer', ], 'min' => 1, ], 'PrimaryKey' => [ 'type' => 'structure', 'required' => [ 'length', 'offset', ], 'members' => [ 'length' => [ 'shape' => 'Integer', ], 'name' => [ 'shape' => 'String', ], 'offset' => [ 'shape' => 'Integer', ], ], ], 'PsAttributes' => [ 'type' => 'structure', 'required' => [ 'format', ], 'members' => [ 'encoding' => [ 'shape' => 'String', ], 'format' => [ 'shape' => 'String', ], ], ], 'PsDetailAttributes' => [ 'type' => 'structure', 'required' => [ 'encoding', 'format', ], 'members' => [ 'encoding' => [ 'shape' => 'String', ], 'format' => [ 'shape' => 'String', ], ], ], 'RecordLength' => [ 'type' => 'structure', 'required' => [ 'max', 'min', ], 'members' => [ 'max' => [ 'shape' => 'Integer', ], 'min' => [ 'shape' => 'Integer', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RestartBatchJobIdentifier' => [ 'type' => 'structure', 'required' => [ 'executionId', 'jobStepRestartMarker', ], 'members' => [ 'executionId' => [ 'shape' => 'Identifier', ], 'jobStepRestartMarker' => [ 'shape' => 'JobStepRestartMarker', ], ], ], 'S3BatchJobIdentifier' => [ 'type' => 'structure', 'required' => [ 'bucket', 'identifier', ], 'members' => [ 'bucket' => [ 'shape' => 'String', ], 'identifier' => [ 'shape' => 'JobIdentifier', ], 'keyPrefix' => [ 'shape' => 'String', ], ], ], 'ScriptBatchJobDefinition' => [ 'type' => 'structure', 'required' => [ 'scriptName', ], 'members' => [ 'scriptName' => [ 'shape' => 'String', ], ], ], 'ScriptBatchJobIdentifier' => [ 'type' => 'structure', 'required' => [ 'scriptName', ], 'members' => [ 'scriptName' => [ 'shape' => 'String', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'StartApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], ], ], 'StartApplicationResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartBatchJobRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'batchJobIdentifier', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], 'authSecretsManagerArn' => [ 'shape' => 'AuthSecretsManagerArn', ], 'batchJobIdentifier' => [ 'shape' => 'BatchJobIdentifier', ], 'jobParams' => [ 'shape' => 'BatchJobParametersMap', ], ], ], 'StartBatchJobResponse' => [ 'type' => 'structure', 'required' => [ 'executionId', ], 'members' => [ 'executionId' => [ 'shape' => 'Identifier', ], ], ], 'StopApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], 'forceStop' => [ 'shape' => 'Boolean', ], ], ], 'StopApplicationResponse' => [ 'type' => 'structure', 'members' => [], ], 'StorageConfiguration' => [ 'type' => 'structure', 'members' => [ 'efs' => [ 'shape' => 'EfsStorageConfiguration', ], 'fsx' => [ 'shape' => 'FsxStorageConfiguration', ], ], 'union' => true, ], 'StorageConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorageConfiguration', ], ], 'String' => [ 'type' => 'string', ], 'String100' => [ 'type' => 'string', 'pattern' => '^\\S{1,100}$', ], 'String20' => [ 'type' => 'string', 'pattern' => '^\\S{1,20}$', ], 'String200' => [ 'type' => 'string', 'pattern' => '^\\S{1,200}$', ], 'String2000' => [ 'type' => 'string', 'pattern' => '^\\S{1,2000}$', ], 'String20List' => [ 'type' => 'list', 'member' => [ 'shape' => 'String20', ], 'max' => 10, 'min' => 1, ], 'String50' => [ 'type' => 'string', 'pattern' => '^\\S{1,50}$', ], 'String50List' => [ 'type' => 'list', 'member' => [ 'shape' => 'String50', ], ], 'StringFree65000' => [ 'type' => 'string', 'max' => 65000, 'min' => 1, ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:).+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], 'serviceCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'currentApplicationVersion', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'applicationId', ], 'currentApplicationVersion' => [ 'shape' => 'Version', ], 'definition' => [ 'shape' => 'Definition', ], 'description' => [ 'shape' => 'EntityDescription', ], ], ], 'UpdateApplicationResponse' => [ 'type' => 'structure', 'required' => [ 'applicationVersion', ], 'members' => [ 'applicationVersion' => [ 'shape' => 'Version', ], ], ], 'UpdateEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'applyDuringMaintenanceWindow' => [ 'shape' => 'Boolean', ], 'desiredCapacity' => [ 'shape' => 'CapacityValue', ], 'engineVersion' => [ 'shape' => 'EngineVersion', ], 'environmentId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'environmentId', ], 'forceUpdate' => [ 'shape' => 'Boolean', ], 'instanceType' => [ 'shape' => 'String20', ], 'preferredMaintenanceWindow' => [ 'shape' => 'String', ], ], ], 'UpdateEnvironmentResponse' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'Identifier', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'message', 'name', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'unknownOperation', 'cannotParse', 'featureNotAvailable', 'unsupportedEngineVersion', 'fieldValidationFailed', 'other', ], ], 'Version' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'VsamAttributes' => [ 'type' => 'structure', 'required' => [ 'format', ], 'members' => [ 'alternateKeys' => [ 'shape' => 'AlternateKeyList', ], 'compressed' => [ 'shape' => 'Boolean', ], 'encoding' => [ 'shape' => 'String', ], 'format' => [ 'shape' => 'String', ], 'primaryKey' => [ 'shape' => 'PrimaryKey', ], ], ], 'VsamDetailAttributes' => [ 'type' => 'structure', 'members' => [ 'alternateKeys' => [ 'shape' => 'AlternateKeyList', ], 'cacheAtStartup' => [ 'shape' => 'Boolean', 'box' => true, ], 'compressed' => [ 'shape' => 'Boolean', 'box' => true, ], 'encoding' => [ 'shape' => 'String20', ], 'primaryKey' => [ 'shape' => 'PrimaryKey', ], 'recordFormat' => [ 'shape' => 'String20', ], ], ], ],];
