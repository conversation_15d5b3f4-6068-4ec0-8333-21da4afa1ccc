<?php
// This file was auto-generated from sdk-root/src/data/networkflowmonitor/2023-04-19/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-04-19', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'networkflowmonitor', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Network Flow Monitor', 'serviceId' => 'NetworkFlowMonitor', 'signatureVersion' => 'v4', 'signingName' => 'networkflowmonitor', 'uid' => 'networkflowmonitor-2023-04-19', ], 'operations' => [ 'CreateMonitor' => [ 'name' => 'CreateMonitor', 'http' => [ 'method' => 'POST', 'requestUri' => '/monitors', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMonitorInput', ], 'output' => [ 'shape' => 'CreateMonitorOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateScope' => [ 'name' => 'CreateScope', 'http' => [ 'method' => 'POST', 'requestUri' => '/scopes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateScopeInput', ], 'output' => [ 'shape' => 'CreateScopeOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteMonitor' => [ 'name' => 'DeleteMonitor', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/monitors/{monitorName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteMonitorInput', ], 'output' => [ 'shape' => 'DeleteMonitorOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteScope' => [ 'name' => 'DeleteScope', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/scopes/{scopeId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteScopeInput', ], 'output' => [ 'shape' => 'DeleteScopeOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'GetMonitor' => [ 'name' => 'GetMonitor', 'http' => [ 'method' => 'GET', 'requestUri' => '/monitors/{monitorName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMonitorInput', ], 'output' => [ 'shape' => 'GetMonitorOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetQueryResultsMonitorTopContributors' => [ 'name' => 'GetQueryResultsMonitorTopContributors', 'http' => [ 'method' => 'GET', 'requestUri' => '/monitors/{monitorName}/topContributorsQueries/{queryId}/results', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetQueryResultsMonitorTopContributorsInput', ], 'output' => [ 'shape' => 'GetQueryResultsMonitorTopContributorsOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetQueryResultsWorkloadInsightsTopContributors' => [ 'name' => 'GetQueryResultsWorkloadInsightsTopContributors', 'http' => [ 'method' => 'GET', 'requestUri' => '/workloadInsights/{scopeId}/topContributorsQueries/{queryId}/results', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetQueryResultsWorkloadInsightsTopContributorsInput', ], 'output' => [ 'shape' => 'GetQueryResultsWorkloadInsightsTopContributorsOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetQueryResultsWorkloadInsightsTopContributorsData' => [ 'name' => 'GetQueryResultsWorkloadInsightsTopContributorsData', 'http' => [ 'method' => 'GET', 'requestUri' => '/workloadInsights/{scopeId}/topContributorsDataQueries/{queryId}/results', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetQueryResultsWorkloadInsightsTopContributorsDataInput', ], 'output' => [ 'shape' => 'GetQueryResultsWorkloadInsightsTopContributorsDataOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetQueryStatusMonitorTopContributors' => [ 'name' => 'GetQueryStatusMonitorTopContributors', 'http' => [ 'method' => 'GET', 'requestUri' => '/monitors/{monitorName}/topContributorsQueries/{queryId}/status', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetQueryStatusMonitorTopContributorsInput', ], 'output' => [ 'shape' => 'GetQueryStatusMonitorTopContributorsOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetQueryStatusWorkloadInsightsTopContributors' => [ 'name' => 'GetQueryStatusWorkloadInsightsTopContributors', 'http' => [ 'method' => 'GET', 'requestUri' => '/workloadInsights/{scopeId}/topContributorsQueries/{queryId}/status', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetQueryStatusWorkloadInsightsTopContributorsInput', ], 'output' => [ 'shape' => 'GetQueryStatusWorkloadInsightsTopContributorsOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetQueryStatusWorkloadInsightsTopContributorsData' => [ 'name' => 'GetQueryStatusWorkloadInsightsTopContributorsData', 'http' => [ 'method' => 'GET', 'requestUri' => '/workloadInsights/{scopeId}/topContributorsDataQueries/{queryId}/status', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetQueryStatusWorkloadInsightsTopContributorsDataInput', ], 'output' => [ 'shape' => 'GetQueryStatusWorkloadInsightsTopContributorsDataOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetScope' => [ 'name' => 'GetScope', 'http' => [ 'method' => 'GET', 'requestUri' => '/scopes/{scopeId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetScopeInput', ], 'output' => [ 'shape' => 'GetScopeOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListMonitors' => [ 'name' => 'ListMonitors', 'http' => [ 'method' => 'GET', 'requestUri' => '/monitors', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMonitorsInput', ], 'output' => [ 'shape' => 'ListMonitorsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListScopes' => [ 'name' => 'ListScopes', 'http' => [ 'method' => 'GET', 'requestUri' => '/scopes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListScopesInput', ], 'output' => [ 'shape' => 'ListScopesOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceInput', ], 'output' => [ 'shape' => 'ListTagsForResourceOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartQueryMonitorTopContributors' => [ 'name' => 'StartQueryMonitorTopContributors', 'http' => [ 'method' => 'POST', 'requestUri' => '/monitors/{monitorName}/topContributorsQueries', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartQueryMonitorTopContributorsInput', ], 'output' => [ 'shape' => 'StartQueryMonitorTopContributorsOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartQueryWorkloadInsightsTopContributors' => [ 'name' => 'StartQueryWorkloadInsightsTopContributors', 'http' => [ 'method' => 'POST', 'requestUri' => '/workloadInsights/{scopeId}/topContributorsQueries', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartQueryWorkloadInsightsTopContributorsInput', ], 'output' => [ 'shape' => 'StartQueryWorkloadInsightsTopContributorsOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartQueryWorkloadInsightsTopContributorsData' => [ 'name' => 'StartQueryWorkloadInsightsTopContributorsData', 'http' => [ 'method' => 'POST', 'requestUri' => '/workloadInsights/{scopeId}/topContributorsDataQueries', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartQueryWorkloadInsightsTopContributorsDataInput', ], 'output' => [ 'shape' => 'StartQueryWorkloadInsightsTopContributorsDataOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StopQueryMonitorTopContributors' => [ 'name' => 'StopQueryMonitorTopContributors', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/monitors/{monitorName}/topContributorsQueries/{queryId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopQueryMonitorTopContributorsInput', ], 'output' => [ 'shape' => 'StopQueryMonitorTopContributorsOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'StopQueryWorkloadInsightsTopContributors' => [ 'name' => 'StopQueryWorkloadInsightsTopContributors', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/workloadInsights/{scopeId}/topContributorsQueries/{queryId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopQueryWorkloadInsightsTopContributorsInput', ], 'output' => [ 'shape' => 'StopQueryWorkloadInsightsTopContributorsOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StopQueryWorkloadInsightsTopContributorsData' => [ 'name' => 'StopQueryWorkloadInsightsTopContributorsData', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/workloadInsights/{scopeId}/topContributorsDataQueries/{queryId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopQueryWorkloadInsightsTopContributorsDataInput', ], 'output' => [ 'shape' => 'StopQueryWorkloadInsightsTopContributorsDataOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceInput', ], 'output' => [ 'shape' => 'TagResourceOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceInput', ], 'output' => [ 'shape' => 'UntagResourceOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateMonitor' => [ 'name' => 'UpdateMonitor', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/monitors/{monitorName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMonitorInput', ], 'output' => [ 'shape' => 'UpdateMonitorOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateScope' => [ 'name' => 'UpdateScope', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/scopes/{scopeId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateScopeInput', ], 'output' => [ 'shape' => 'UpdateScopeOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 1, 'pattern' => '[0-9]{12}', ], 'Arn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:.*', ], 'AvailabilityZone' => [ 'type' => 'string', ], 'AwsRegion' => [ 'type' => 'string', ], 'Component' => [ 'type' => 'string', ], 'ComponentType' => [ 'type' => 'string', ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateMonitorInput' => [ 'type' => 'structure', 'required' => [ 'monitorName', 'localResources', 'scopeArn', ], 'members' => [ 'monitorName' => [ 'shape' => 'ResourceName', ], 'localResources' => [ 'shape' => 'CreateMonitorInputLocalResourcesList', ], 'remoteResources' => [ 'shape' => 'MonitorRemoteResources', ], 'scopeArn' => [ 'shape' => 'Arn', ], 'clientToken' => [ 'shape' => 'UuidString', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateMonitorInputLocalResourcesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MonitorLocalResource', ], 'min' => 1, ], 'CreateMonitorOutput' => [ 'type' => 'structure', 'required' => [ 'monitorArn', 'monitorName', 'monitorStatus', 'localResources', 'remoteResources', 'createdAt', 'modifiedAt', ], 'members' => [ 'monitorArn' => [ 'shape' => 'MonitorArn', ], 'monitorName' => [ 'shape' => 'ResourceName', ], 'monitorStatus' => [ 'shape' => 'MonitorStatus', ], 'localResources' => [ 'shape' => 'MonitorLocalResources', ], 'remoteResources' => [ 'shape' => 'MonitorRemoteResources', ], 'createdAt' => [ 'shape' => 'Iso8601Timestamp', ], 'modifiedAt' => [ 'shape' => 'Iso8601Timestamp', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateScopeInput' => [ 'type' => 'structure', 'required' => [ 'targets', ], 'members' => [ 'targets' => [ 'shape' => 'CreateScopeInputTargetsList', ], 'clientToken' => [ 'shape' => 'UuidString', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateScopeInputTargetsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetResource', ], 'max' => 100, 'min' => 1, ], 'CreateScopeOutput' => [ 'type' => 'structure', 'required' => [ 'scopeId', 'status', 'scopeArn', ], 'members' => [ 'scopeId' => [ 'shape' => 'ScopeId', ], 'status' => [ 'shape' => 'ScopeStatus', ], 'scopeArn' => [ 'shape' => 'Arn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'DeleteMonitorInput' => [ 'type' => 'structure', 'required' => [ 'monitorName', ], 'members' => [ 'monitorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'monitorName', ], ], ], 'DeleteMonitorOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteScopeInput' => [ 'type' => 'structure', 'required' => [ 'scopeId', ], 'members' => [ 'scopeId' => [ 'shape' => 'ScopeId', 'location' => 'uri', 'locationName' => 'scopeId', ], ], ], 'DeleteScopeOutput' => [ 'type' => 'structure', 'members' => [], ], 'DestinationCategory' => [ 'type' => 'string', 'enum' => [ 'INTRA_AZ', 'INTER_AZ', 'INTER_VPC', 'UNCLASSIFIED', 'AMAZON_S3', 'AMAZON_DYNAMODB', ], ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'GetMonitorInput' => [ 'type' => 'structure', 'required' => [ 'monitorName', ], 'members' => [ 'monitorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'monitorName', ], ], ], 'GetMonitorOutput' => [ 'type' => 'structure', 'required' => [ 'monitorArn', 'monitorName', 'monitorStatus', 'localResources', 'remoteResources', 'createdAt', 'modifiedAt', ], 'members' => [ 'monitorArn' => [ 'shape' => 'MonitorArn', ], 'monitorName' => [ 'shape' => 'ResourceName', ], 'monitorStatus' => [ 'shape' => 'MonitorStatus', ], 'localResources' => [ 'shape' => 'MonitorLocalResources', ], 'remoteResources' => [ 'shape' => 'MonitorRemoteResources', ], 'createdAt' => [ 'shape' => 'Iso8601Timestamp', ], 'modifiedAt' => [ 'shape' => 'Iso8601Timestamp', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'GetQueryResultsMonitorTopContributorsInput' => [ 'type' => 'structure', 'required' => [ 'monitorName', 'queryId', ], 'members' => [ 'monitorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'monitorName', ], 'queryId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'queryId', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'Integer', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'GetQueryResultsMonitorTopContributorsOutput' => [ 'type' => 'structure', 'members' => [ 'unit' => [ 'shape' => 'MetricUnit', ], 'topContributors' => [ 'shape' => 'MonitorTopContributorsRowList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'GetQueryResultsWorkloadInsightsTopContributorsDataInput' => [ 'type' => 'structure', 'required' => [ 'scopeId', 'queryId', ], 'members' => [ 'scopeId' => [ 'shape' => 'ScopeId', 'location' => 'uri', 'locationName' => 'scopeId', ], 'queryId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'queryId', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'Integer', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'GetQueryResultsWorkloadInsightsTopContributorsDataOutput' => [ 'type' => 'structure', 'required' => [ 'unit', 'datapoints', ], 'members' => [ 'unit' => [ 'shape' => 'MetricUnit', ], 'datapoints' => [ 'shape' => 'WorkloadInsightsTopContributorsDataPoints', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'GetQueryResultsWorkloadInsightsTopContributorsInput' => [ 'type' => 'structure', 'required' => [ 'scopeId', 'queryId', ], 'members' => [ 'scopeId' => [ 'shape' => 'ScopeId', 'location' => 'uri', 'locationName' => 'scopeId', ], 'queryId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'queryId', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'Integer', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'GetQueryResultsWorkloadInsightsTopContributorsOutput' => [ 'type' => 'structure', 'members' => [ 'topContributors' => [ 'shape' => 'WorkloadInsightsTopContributorsRowList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'GetQueryStatusMonitorTopContributorsInput' => [ 'type' => 'structure', 'required' => [ 'monitorName', 'queryId', ], 'members' => [ 'monitorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'monitorName', ], 'queryId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'queryId', ], ], ], 'GetQueryStatusMonitorTopContributorsOutput' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'QueryStatus', ], ], ], 'GetQueryStatusWorkloadInsightsTopContributorsDataInput' => [ 'type' => 'structure', 'required' => [ 'scopeId', 'queryId', ], 'members' => [ 'scopeId' => [ 'shape' => 'ScopeId', 'location' => 'uri', 'locationName' => 'scopeId', ], 'queryId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'queryId', ], ], ], 'GetQueryStatusWorkloadInsightsTopContributorsDataOutput' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'QueryStatus', ], ], ], 'GetQueryStatusWorkloadInsightsTopContributorsInput' => [ 'type' => 'structure', 'required' => [ 'scopeId', 'queryId', ], 'members' => [ 'scopeId' => [ 'shape' => 'ScopeId', 'location' => 'uri', 'locationName' => 'scopeId', ], 'queryId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'queryId', ], ], ], 'GetQueryStatusWorkloadInsightsTopContributorsOutput' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'QueryStatus', ], ], ], 'GetScopeInput' => [ 'type' => 'structure', 'required' => [ 'scopeId', ], 'members' => [ 'scopeId' => [ 'shape' => 'GetScopeInputScopeIdString', 'location' => 'uri', 'locationName' => 'scopeId', ], ], ], 'GetScopeInputScopeIdString' => [ 'type' => 'string', 'min' => 1, ], 'GetScopeOutput' => [ 'type' => 'structure', 'required' => [ 'scopeId', 'status', 'scopeArn', 'targets', ], 'members' => [ 'scopeId' => [ 'shape' => 'ScopeId', ], 'status' => [ 'shape' => 'ScopeStatus', ], 'scopeArn' => [ 'shape' => 'Arn', ], 'targets' => [ 'shape' => 'TargetResourceList', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'InstanceArn' => [ 'type' => 'string', ], 'InstanceId' => [ 'type' => 'string', 'pattern' => 'i-[a-zA-Z0-9]{8,32}', ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'Iso8601Timestamp' => [ 'type' => 'timestamp', ], 'KubernetesMetadata' => [ 'type' => 'structure', 'members' => [ 'localServiceName' => [ 'shape' => 'String', ], 'localPodName' => [ 'shape' => 'String', ], 'localPodNamespace' => [ 'shape' => 'String', ], 'remoteServiceName' => [ 'shape' => 'String', ], 'remotePodName' => [ 'shape' => 'String', ], 'remotePodNamespace' => [ 'shape' => 'String', ], ], ], 'Limit' => [ 'type' => 'integer', 'box' => true, 'max' => 500, 'min' => 1, ], 'ListMonitorsInput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'monitorStatus' => [ 'shape' => 'MonitorStatus', 'location' => 'querystring', 'locationName' => 'monitorStatus', ], ], ], 'ListMonitorsOutput' => [ 'type' => 'structure', 'required' => [ 'monitors', ], 'members' => [ 'monitors' => [ 'shape' => 'MonitorList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListScopesInput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListScopesOutput' => [ 'type' => 'structure', 'required' => [ 'scopes', ], 'members' => [ 'scopes' => [ 'shape' => 'ScopeSummaryList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListTagsForResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceOutput' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 1, ], 'MetricUnit' => [ 'type' => 'string', 'enum' => [ 'Seconds', 'Microseconds', 'Milliseconds', 'Bytes', 'Kilobytes', 'Megabytes', 'Gigabytes', 'Terabytes', 'Bits', 'Kilobits', 'Megabits', 'Gigabits', 'Terabits', 'Percent', 'Count', 'Bytes/Second', 'Kilobytes/Second', 'Megabytes/Second', 'Gigabytes/Second', 'Terabytes/Second', 'Bits/Second', 'Kilobits/Second', 'Megabits/Second', 'Gigabits/Second', 'Terabits/Second', 'Count/Second', 'None', ], ], 'MonitorArn' => [ 'type' => 'string', 'max' => 512, 'min' => 20, 'pattern' => 'arn:.*', ], 'MonitorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MonitorSummary', ], ], 'MonitorLocalResource' => [ 'type' => 'structure', 'required' => [ 'type', 'identifier', ], 'members' => [ 'type' => [ 'shape' => 'MonitorLocalResourceType', ], 'identifier' => [ 'shape' => 'String', ], ], ], 'MonitorLocalResourceType' => [ 'type' => 'string', 'enum' => [ 'AWS::EC2::VPC', 'AWS::AvailabilityZone', 'AWS::EC2::Subnet', ], ], 'MonitorLocalResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'MonitorLocalResource', ], ], 'MonitorMetric' => [ 'type' => 'string', 'enum' => [ 'ROUND_TRIP_TIME', 'TIMEOUTS', 'RETRANSMISSIONS', 'DATA_TRANSFERRED', ], ], 'MonitorRemoteResource' => [ 'type' => 'structure', 'required' => [ 'type', 'identifier', ], 'members' => [ 'type' => [ 'shape' => 'MonitorRemoteResourceType', ], 'identifier' => [ 'shape' => 'String', ], ], ], 'MonitorRemoteResourceType' => [ 'type' => 'string', 'enum' => [ 'AWS::EC2::VPC', 'AWS::AvailabilityZone', 'AWS::EC2::Subnet', 'AWS::AWSService', ], ], 'MonitorRemoteResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'MonitorRemoteResource', ], ], 'MonitorStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'ACTIVE', 'INACTIVE', 'ERROR', 'DELETING', ], ], 'MonitorSummary' => [ 'type' => 'structure', 'required' => [ 'monitorArn', 'monitorName', 'monitorStatus', ], 'members' => [ 'monitorArn' => [ 'shape' => 'MonitorArn', ], 'monitorName' => [ 'shape' => 'ResourceName', ], 'monitorStatus' => [ 'shape' => 'MonitorStatus', ], ], ], 'MonitorTopContributorsRow' => [ 'type' => 'structure', 'members' => [ 'localIp' => [ 'shape' => 'String', ], 'snatIp' => [ 'shape' => 'String', ], 'localInstanceId' => [ 'shape' => 'InstanceId', ], 'localVpcId' => [ 'shape' => 'VpcId', ], 'localRegion' => [ 'shape' => 'AwsRegion', ], 'localAz' => [ 'shape' => 'AvailabilityZone', ], 'localSubnetId' => [ 'shape' => 'SubnetId', ], 'targetPort' => [ 'shape' => 'Integer', ], 'destinationCategory' => [ 'shape' => 'DestinationCategory', ], 'remoteVpcId' => [ 'shape' => 'VpcId', ], 'remoteRegion' => [ 'shape' => 'AwsRegion', ], 'remoteAz' => [ 'shape' => 'AvailabilityZone', ], 'remoteSubnetId' => [ 'shape' => 'SubnetId', ], 'remoteInstanceId' => [ 'shape' => 'InstanceId', ], 'remoteIp' => [ 'shape' => 'String', ], 'dnatIp' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'Long', ], 'traversedConstructs' => [ 'shape' => 'TraversedConstructsList', ], 'kubernetesMetadata' => [ 'shape' => 'KubernetesMetadata', ], 'localInstanceArn' => [ 'shape' => 'InstanceArn', ], 'localSubnetArn' => [ 'shape' => 'SubnetArn', ], 'localVpcArn' => [ 'shape' => 'VpcArn', ], 'remoteInstanceArn' => [ 'shape' => 'InstanceArn', ], 'remoteSubnetArn' => [ 'shape' => 'SubnetArn', ], 'remoteVpcArn' => [ 'shape' => 'VpcArn', ], ], ], 'MonitorTopContributorsRowList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MonitorTopContributorsRow', ], ], 'QueryStatus' => [ 'type' => 'string', 'enum' => [ 'QUEUED', 'RUNNING', 'SUCCEEDED', 'FAILED', 'CANCELED', ], ], 'ResourceName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[a-zA-Z0-9_.-]+', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ScopeId' => [ 'type' => 'string', ], 'ScopeStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'IN_PROGRESS', 'FAILED', ], ], 'ScopeSummary' => [ 'type' => 'structure', 'required' => [ 'scopeId', 'status', 'scopeArn', ], 'members' => [ 'scopeId' => [ 'shape' => 'ScopeId', ], 'status' => [ 'shape' => 'ScopeStatus', ], 'scopeArn' => [ 'shape' => 'Arn', ], ], ], 'ScopeSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScopeSummary', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'StartQueryMonitorTopContributorsInput' => [ 'type' => 'structure', 'required' => [ 'monitorName', 'startTime', 'endTime', 'metricName', 'destinationCategory', ], 'members' => [ 'monitorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'monitorName', ], 'startTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'endTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'metricName' => [ 'shape' => 'MonitorMetric', ], 'destinationCategory' => [ 'shape' => 'DestinationCategory', ], 'limit' => [ 'shape' => 'Limit', ], ], ], 'StartQueryMonitorTopContributorsOutput' => [ 'type' => 'structure', 'required' => [ 'queryId', ], 'members' => [ 'queryId' => [ 'shape' => 'String', ], ], ], 'StartQueryWorkloadInsightsTopContributorsDataInput' => [ 'type' => 'structure', 'required' => [ 'scopeId', 'startTime', 'endTime', 'metricName', 'destinationCategory', ], 'members' => [ 'scopeId' => [ 'shape' => 'ScopeId', 'location' => 'uri', 'locationName' => 'scopeId', ], 'startTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'endTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'metricName' => [ 'shape' => 'WorkloadInsightsMetric', ], 'destinationCategory' => [ 'shape' => 'DestinationCategory', ], ], ], 'StartQueryWorkloadInsightsTopContributorsDataOutput' => [ 'type' => 'structure', 'required' => [ 'queryId', ], 'members' => [ 'queryId' => [ 'shape' => 'String', ], ], ], 'StartQueryWorkloadInsightsTopContributorsInput' => [ 'type' => 'structure', 'required' => [ 'scopeId', 'startTime', 'endTime', 'metricName', 'destinationCategory', ], 'members' => [ 'scopeId' => [ 'shape' => 'ScopeId', 'location' => 'uri', 'locationName' => 'scopeId', ], 'startTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'endTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'metricName' => [ 'shape' => 'WorkloadInsightsMetric', ], 'destinationCategory' => [ 'shape' => 'DestinationCategory', ], 'limit' => [ 'shape' => 'Limit', ], ], ], 'StartQueryWorkloadInsightsTopContributorsOutput' => [ 'type' => 'structure', 'required' => [ 'queryId', ], 'members' => [ 'queryId' => [ 'shape' => 'String', ], ], ], 'StopQueryMonitorTopContributorsInput' => [ 'type' => 'structure', 'required' => [ 'monitorName', 'queryId', ], 'members' => [ 'monitorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'monitorName', ], 'queryId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'queryId', ], ], ], 'StopQueryMonitorTopContributorsOutput' => [ 'type' => 'structure', 'members' => [], ], 'StopQueryWorkloadInsightsTopContributorsDataInput' => [ 'type' => 'structure', 'required' => [ 'scopeId', 'queryId', ], 'members' => [ 'scopeId' => [ 'shape' => 'ScopeId', 'location' => 'uri', 'locationName' => 'scopeId', ], 'queryId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'queryId', ], ], ], 'StopQueryWorkloadInsightsTopContributorsDataOutput' => [ 'type' => 'structure', 'members' => [], ], 'StopQueryWorkloadInsightsTopContributorsInput' => [ 'type' => 'structure', 'required' => [ 'scopeId', 'queryId', ], 'members' => [ 'scopeId' => [ 'shape' => 'ScopeId', 'location' => 'uri', 'locationName' => 'scopeId', ], 'queryId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'queryId', ], ], ], 'StopQueryWorkloadInsightsTopContributorsOutput' => [ 'type' => 'structure', 'members' => [], ], 'String' => [ 'type' => 'string', ], 'SubnetArn' => [ 'type' => 'string', ], 'SubnetId' => [ 'type' => 'string', 'pattern' => 'subnet-[a-zA-Z0-9]{8,32}', ], 'SyntheticTimestamp_date_time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 0, ], 'TagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TargetId' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], ], 'union' => true, ], 'TargetIdentifier' => [ 'type' => 'structure', 'required' => [ 'targetId', 'targetType', ], 'members' => [ 'targetId' => [ 'shape' => 'TargetId', ], 'targetType' => [ 'shape' => 'TargetType', ], ], ], 'TargetResource' => [ 'type' => 'structure', 'required' => [ 'targetIdentifier', 'region', ], 'members' => [ 'targetIdentifier' => [ 'shape' => 'TargetIdentifier', ], 'region' => [ 'shape' => 'AwsRegion', ], ], ], 'TargetResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetResource', ], ], 'TargetType' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'TraversedComponent' => [ 'type' => 'structure', 'members' => [ 'componentId' => [ 'shape' => 'Component', ], 'componentType' => [ 'shape' => 'ComponentType', ], 'componentArn' => [ 'shape' => 'Arn', ], 'serviceName' => [ 'shape' => 'String', ], ], ], 'TraversedConstructsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TraversedComponent', ], ], 'UntagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'UpdateMonitorInput' => [ 'type' => 'structure', 'required' => [ 'monitorName', ], 'members' => [ 'monitorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'monitorName', ], 'localResourcesToAdd' => [ 'shape' => 'MonitorLocalResources', ], 'localResourcesToRemove' => [ 'shape' => 'MonitorLocalResources', ], 'remoteResourcesToAdd' => [ 'shape' => 'MonitorRemoteResources', ], 'remoteResourcesToRemove' => [ 'shape' => 'MonitorRemoteResources', ], 'clientToken' => [ 'shape' => 'UuidString', 'idempotencyToken' => true, ], ], ], 'UpdateMonitorOutput' => [ 'type' => 'structure', 'required' => [ 'monitorArn', 'monitorName', 'monitorStatus', 'localResources', 'remoteResources', 'createdAt', 'modifiedAt', ], 'members' => [ 'monitorArn' => [ 'shape' => 'MonitorArn', ], 'monitorName' => [ 'shape' => 'ResourceName', ], 'monitorStatus' => [ 'shape' => 'MonitorStatus', ], 'localResources' => [ 'shape' => 'MonitorLocalResources', ], 'remoteResources' => [ 'shape' => 'MonitorRemoteResources', ], 'createdAt' => [ 'shape' => 'Iso8601Timestamp', ], 'modifiedAt' => [ 'shape' => 'Iso8601Timestamp', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'UpdateScopeInput' => [ 'type' => 'structure', 'required' => [ 'scopeId', ], 'members' => [ 'scopeId' => [ 'shape' => 'ScopeId', 'location' => 'uri', 'locationName' => 'scopeId', ], 'resourcesToAdd' => [ 'shape' => 'UpdateScopeInputResourcesToAddList', ], 'resourcesToDelete' => [ 'shape' => 'UpdateScopeInputResourcesToDeleteList', ], ], ], 'UpdateScopeInputResourcesToAddList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetResource', ], 'max' => 99, 'min' => 1, ], 'UpdateScopeInputResourcesToDeleteList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetResource', ], 'max' => 99, 'min' => 1, ], 'UpdateScopeOutput' => [ 'type' => 'structure', 'required' => [ 'scopeId', 'status', 'scopeArn', ], 'members' => [ 'scopeId' => [ 'shape' => 'ScopeId', ], 'status' => [ 'shape' => 'ScopeStatus', ], 'scopeArn' => [ 'shape' => 'Arn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'UuidString' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'VpcArn' => [ 'type' => 'string', ], 'VpcId' => [ 'type' => 'string', 'pattern' => 'vpc-[a-zA-Z0-9]{8,32}', ], 'WorkloadInsightsMetric' => [ 'type' => 'string', 'enum' => [ 'TIMEOUTS', 'RETRANSMISSIONS', 'DATA_TRANSFERRED', ], ], 'WorkloadInsightsTopContributorsDataPoint' => [ 'type' => 'structure', 'required' => [ 'timestamps', 'values', 'label', ], 'members' => [ 'timestamps' => [ 'shape' => 'WorkloadInsightsTopContributorsTimestampsList', ], 'values' => [ 'shape' => 'WorkloadInsightsTopContributorsValuesList', ], 'label' => [ 'shape' => 'String', ], ], ], 'WorkloadInsightsTopContributorsDataPoints' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkloadInsightsTopContributorsDataPoint', ], ], 'WorkloadInsightsTopContributorsRow' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'localSubnetId' => [ 'shape' => 'SubnetId', ], 'localAz' => [ 'shape' => 'AvailabilityZone', ], 'localVpcId' => [ 'shape' => 'VpcId', ], 'localRegion' => [ 'shape' => 'AwsRegion', ], 'remoteIdentifier' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'Long', ], 'localSubnetArn' => [ 'shape' => 'SubnetArn', ], 'localVpcArn' => [ 'shape' => 'VpcArn', ], ], ], 'WorkloadInsightsTopContributorsRowList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkloadInsightsTopContributorsRow', ], ], 'WorkloadInsightsTopContributorsTimestampsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], 'WorkloadInsightsTopContributorsValuesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Double', ], ], ],];
