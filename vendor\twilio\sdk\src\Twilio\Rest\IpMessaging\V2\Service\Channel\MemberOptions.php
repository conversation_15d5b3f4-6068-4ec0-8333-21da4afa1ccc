<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Ip_messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\IpMessaging\V2\Service\Channel;

use Twilio\Options;
use Twilio\Values;

abstract class MemberOptions
{
    /**
     * @param string $roleSid 
     * @param int $lastConsumedMessageIndex 
     * @param \DateTime $lastConsumptionTimestamp 
     * @param \DateTime $dateCreated 
     * @param \DateTime $dateUpdated 
     * @param string $attributes 
     * @param string $xTwilioWebhookEnabled The X-Twilio-Webhook-Enabled HTTP request header
     * @return CreateMemberOptions Options builder
     */
    public static function create(
        
        string $roleSid = Values::NONE,
        int $lastConsumedMessageIndex = Values::INT_NONE,
        ?\DateTime $lastConsumptionTimestamp = null,
        ?\DateTime $dateCreated = null,
        ?\DateTime $dateUpdated = null,
        string $attributes = Values::NONE,
        string $xTwilioWebhookEnabled = Values::NONE

    ): CreateMemberOptions
    {
        return new CreateMemberOptions(
            $roleSid,
            $lastConsumedMessageIndex,
            $lastConsumptionTimestamp,
            $dateCreated,
            $dateUpdated,
            $attributes,
            $xTwilioWebhookEnabled
        );
    }

    /**
     * @param string $xTwilioWebhookEnabled The X-Twilio-Webhook-Enabled HTTP request header
     * @return DeleteMemberOptions Options builder
     */
    public static function delete(
        
        string $xTwilioWebhookEnabled = Values::NONE

    ): DeleteMemberOptions
    {
        return new DeleteMemberOptions(
            $xTwilioWebhookEnabled
        );
    }


    /**
     * @param string[] $identity 
     * @return ReadMemberOptions Options builder
     */
    public static function read(
        
        array $identity = Values::ARRAY_NONE

    ): ReadMemberOptions
    {
        return new ReadMemberOptions(
            $identity
        );
    }

    /**
     * @param string $roleSid 
     * @param int $lastConsumedMessageIndex 
     * @param \DateTime $lastConsumptionTimestamp 
     * @param \DateTime $dateCreated 
     * @param \DateTime $dateUpdated 
     * @param string $attributes 
     * @param string $xTwilioWebhookEnabled The X-Twilio-Webhook-Enabled HTTP request header
     * @return UpdateMemberOptions Options builder
     */
    public static function update(
        
        string $roleSid = Values::NONE,
        int $lastConsumedMessageIndex = Values::INT_NONE,
        ?\DateTime $lastConsumptionTimestamp = null,
        ?\DateTime $dateCreated = null,
        ?\DateTime $dateUpdated = null,
        string $attributes = Values::NONE,
        string $xTwilioWebhookEnabled = Values::NONE

    ): UpdateMemberOptions
    {
        return new UpdateMemberOptions(
            $roleSid,
            $lastConsumedMessageIndex,
            $lastConsumptionTimestamp,
            $dateCreated,
            $dateUpdated,
            $attributes,
            $xTwilioWebhookEnabled
        );
    }

}

class CreateMemberOptions extends Options
    {
    /**
     * @param string $roleSid 
     * @param int $lastConsumedMessageIndex 
     * @param \DateTime $lastConsumptionTimestamp 
     * @param \DateTime $dateCreated 
     * @param \DateTime $dateUpdated 
     * @param string $attributes 
     * @param string $xTwilioWebhookEnabled The X-Twilio-Webhook-Enabled HTTP request header
     */
    public function __construct(
        
        string $roleSid = Values::NONE,
        int $lastConsumedMessageIndex = Values::INT_NONE,
        ?\DateTime $lastConsumptionTimestamp = null,
        ?\DateTime $dateCreated = null,
        ?\DateTime $dateUpdated = null,
        string $attributes = Values::NONE,
        string $xTwilioWebhookEnabled = Values::NONE

    ) {
        $this->options['roleSid'] = $roleSid;
        $this->options['lastConsumedMessageIndex'] = $lastConsumedMessageIndex;
        $this->options['lastConsumptionTimestamp'] = $lastConsumptionTimestamp;
        $this->options['dateCreated'] = $dateCreated;
        $this->options['dateUpdated'] = $dateUpdated;
        $this->options['attributes'] = $attributes;
        $this->options['xTwilioWebhookEnabled'] = $xTwilioWebhookEnabled;
    }

    /**
     * 
     *
     * @param string $roleSid 
     * @return $this Fluent Builder
     */
    public function setRoleSid(string $roleSid): self
    {
        $this->options['roleSid'] = $roleSid;
        return $this;
    }

    /**
     * 
     *
     * @param int $lastConsumedMessageIndex 
     * @return $this Fluent Builder
     */
    public function setLastConsumedMessageIndex(int $lastConsumedMessageIndex): self
    {
        $this->options['lastConsumedMessageIndex'] = $lastConsumedMessageIndex;
        return $this;
    }

    /**
     * 
     *
     * @param \DateTime $lastConsumptionTimestamp 
     * @return $this Fluent Builder
     */
    public function setLastConsumptionTimestamp(\DateTime $lastConsumptionTimestamp): self
    {
        $this->options['lastConsumptionTimestamp'] = $lastConsumptionTimestamp;
        return $this;
    }

    /**
     * 
     *
     * @param \DateTime $dateCreated 
     * @return $this Fluent Builder
     */
    public function setDateCreated(\DateTime $dateCreated): self
    {
        $this->options['dateCreated'] = $dateCreated;
        return $this;
    }

    /**
     * 
     *
     * @param \DateTime $dateUpdated 
     * @return $this Fluent Builder
     */
    public function setDateUpdated(\DateTime $dateUpdated): self
    {
        $this->options['dateUpdated'] = $dateUpdated;
        return $this;
    }

    /**
     * 
     *
     * @param string $attributes 
     * @return $this Fluent Builder
     */
    public function setAttributes(string $attributes): self
    {
        $this->options['attributes'] = $attributes;
        return $this;
    }

    /**
     * The X-Twilio-Webhook-Enabled HTTP request header
     *
     * @param string $xTwilioWebhookEnabled The X-Twilio-Webhook-Enabled HTTP request header
     * @return $this Fluent Builder
     */
    public function setXTwilioWebhookEnabled(string $xTwilioWebhookEnabled): self
    {
        $this->options['xTwilioWebhookEnabled'] = $xTwilioWebhookEnabled;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.IpMessaging.V2.CreateMemberOptions ' . $options . ']';
    }
}

class DeleteMemberOptions extends Options
    {
    /**
     * @param string $xTwilioWebhookEnabled The X-Twilio-Webhook-Enabled HTTP request header
     */
    public function __construct(
        
        string $xTwilioWebhookEnabled = Values::NONE

    ) {
        $this->options['xTwilioWebhookEnabled'] = $xTwilioWebhookEnabled;
    }

    /**
     * The X-Twilio-Webhook-Enabled HTTP request header
     *
     * @param string $xTwilioWebhookEnabled The X-Twilio-Webhook-Enabled HTTP request header
     * @return $this Fluent Builder
     */
    public function setXTwilioWebhookEnabled(string $xTwilioWebhookEnabled): self
    {
        $this->options['xTwilioWebhookEnabled'] = $xTwilioWebhookEnabled;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.IpMessaging.V2.DeleteMemberOptions ' . $options . ']';
    }
}


class ReadMemberOptions extends Options
    {
    /**
     * @param string[] $identity 
     */
    public function __construct(
        
        array $identity = Values::ARRAY_NONE

    ) {
        $this->options['identity'] = $identity;
    }

    /**
     * 
     *
     * @param string[] $identity 
     * @return $this Fluent Builder
     */
    public function setIdentity(array $identity): self
    {
        $this->options['identity'] = $identity;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.IpMessaging.V2.ReadMemberOptions ' . $options . ']';
    }
}

class UpdateMemberOptions extends Options
    {
    /**
     * @param string $roleSid 
     * @param int $lastConsumedMessageIndex 
     * @param \DateTime $lastConsumptionTimestamp 
     * @param \DateTime $dateCreated 
     * @param \DateTime $dateUpdated 
     * @param string $attributes 
     * @param string $xTwilioWebhookEnabled The X-Twilio-Webhook-Enabled HTTP request header
     */
    public function __construct(
        
        string $roleSid = Values::NONE,
        int $lastConsumedMessageIndex = Values::INT_NONE,
        ?\DateTime $lastConsumptionTimestamp = null,
        ?\DateTime $dateCreated = null,
        ?\DateTime $dateUpdated = null,
        string $attributes = Values::NONE,
        string $xTwilioWebhookEnabled = Values::NONE

    ) {
        $this->options['roleSid'] = $roleSid;
        $this->options['lastConsumedMessageIndex'] = $lastConsumedMessageIndex;
        $this->options['lastConsumptionTimestamp'] = $lastConsumptionTimestamp;
        $this->options['dateCreated'] = $dateCreated;
        $this->options['dateUpdated'] = $dateUpdated;
        $this->options['attributes'] = $attributes;
        $this->options['xTwilioWebhookEnabled'] = $xTwilioWebhookEnabled;
    }

    /**
     * 
     *
     * @param string $roleSid 
     * @return $this Fluent Builder
     */
    public function setRoleSid(string $roleSid): self
    {
        $this->options['roleSid'] = $roleSid;
        return $this;
    }

    /**
     * 
     *
     * @param int $lastConsumedMessageIndex 
     * @return $this Fluent Builder
     */
    public function setLastConsumedMessageIndex(int $lastConsumedMessageIndex): self
    {
        $this->options['lastConsumedMessageIndex'] = $lastConsumedMessageIndex;
        return $this;
    }

    /**
     * 
     *
     * @param \DateTime $lastConsumptionTimestamp 
     * @return $this Fluent Builder
     */
    public function setLastConsumptionTimestamp(\DateTime $lastConsumptionTimestamp): self
    {
        $this->options['lastConsumptionTimestamp'] = $lastConsumptionTimestamp;
        return $this;
    }

    /**
     * 
     *
     * @param \DateTime $dateCreated 
     * @return $this Fluent Builder
     */
    public function setDateCreated(\DateTime $dateCreated): self
    {
        $this->options['dateCreated'] = $dateCreated;
        return $this;
    }

    /**
     * 
     *
     * @param \DateTime $dateUpdated 
     * @return $this Fluent Builder
     */
    public function setDateUpdated(\DateTime $dateUpdated): self
    {
        $this->options['dateUpdated'] = $dateUpdated;
        return $this;
    }

    /**
     * 
     *
     * @param string $attributes 
     * @return $this Fluent Builder
     */
    public function setAttributes(string $attributes): self
    {
        $this->options['attributes'] = $attributes;
        return $this;
    }

    /**
     * The X-Twilio-Webhook-Enabled HTTP request header
     *
     * @param string $xTwilioWebhookEnabled The X-Twilio-Webhook-Enabled HTTP request header
     * @return $this Fluent Builder
     */
    public function setXTwilioWebhookEnabled(string $xTwilioWebhookEnabled): self
    {
        $this->options['xTwilioWebhookEnabled'] = $xTwilioWebhookEnabled;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.IpMessaging.V2.UpdateMemberOptions ' . $options . ']';
    }
}

