<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Conversations
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Conversations\V1;

use Twilio\Options;
use Twilio\Values;

abstract class ConversationWithParticipantsOptions
{
    /**
     * @param string $friendlyName The human-readable name of this conversation, limited to 256 characters. Optional.
     * @param string $uniqueName An application-defined string that uniquely identifies the resource. It can be used to address the resource in place of the resource's `sid` in the URL.
     * @param \DateTime $dateCreated The date that this resource was created.
     * @param \DateTime $dateUpdated The date that this resource was last updated.
     * @param string $messagingServiceSid The unique ID of the [Messaging Service](https://www.twilio.com/docs/messaging/api/service-resource) this conversation belongs to.
     * @param string $attributes An optional string metadata field you can use to store any data you wish. The string value must contain structurally valid JSON if specified.  **Note** that if the attributes are not set \\\"{}\\\" will be returned.
     * @param string $state
     * @param string $timersInactive ISO8601 duration when conversation will be switched to `inactive` state. Minimum value for this timer is 1 minute.
     * @param string $timersClosed ISO8601 duration when conversation will be switched to `closed` state. Minimum value for this timer is 10 minutes.
     * @param string $bindingsEmailAddress The default email address that will be used when sending outbound emails in this conversation.
     * @param string $bindingsEmailName The default name that will be used when sending outbound emails in this conversation.
     * @param string[] $participant The participant to be added to the conversation in JSON format. The JSON object attributes are as parameters in [Participant Resource](https://www.twilio.com/docs/conversations/api/conversation-participant-resource). The maximum number of participants that can be added in a single request is 10.
     * @param string $xTwilioWebhookEnabled The X-Twilio-Webhook-Enabled HTTP request header
     * @return CreateConversationWithParticipantsOptions Options builder
     */
    public static function create(
        
        string $friendlyName = Values::NONE,
        string $uniqueName = Values::NONE,
        ?\DateTime $dateCreated = null,
        ?\DateTime $dateUpdated = null,
        string $messagingServiceSid = Values::NONE,
        string $attributes = Values::NONE,
        string $state = Values::NONE,
        string $timersInactive = Values::NONE,
        string $timersClosed = Values::NONE,
        string $bindingsEmailAddress = Values::NONE,
        string $bindingsEmailName = Values::NONE,
        array $participant = Values::ARRAY_NONE,
        string $xTwilioWebhookEnabled = Values::NONE

    ): CreateConversationWithParticipantsOptions
    {
        return new CreateConversationWithParticipantsOptions(
            $friendlyName,
            $uniqueName,
            $dateCreated,
            $dateUpdated,
            $messagingServiceSid,
            $attributes,
            $state,
            $timersInactive,
            $timersClosed,
            $bindingsEmailAddress,
            $bindingsEmailName,
            $participant,
            $xTwilioWebhookEnabled
        );
    }

}

class CreateConversationWithParticipantsOptions extends Options
    {
    /**
     * @param string $friendlyName The human-readable name of this conversation, limited to 256 characters. Optional.
     * @param string $uniqueName An application-defined string that uniquely identifies the resource. It can be used to address the resource in place of the resource's `sid` in the URL.
     * @param \DateTime $dateCreated The date that this resource was created.
     * @param \DateTime $dateUpdated The date that this resource was last updated.
     * @param string $messagingServiceSid The unique ID of the [Messaging Service](https://www.twilio.com/docs/messaging/api/service-resource) this conversation belongs to.
     * @param string $attributes An optional string metadata field you can use to store any data you wish. The string value must contain structurally valid JSON if specified.  **Note** that if the attributes are not set \\\"{}\\\" will be returned.
     * @param string $state
     * @param string $timersInactive ISO8601 duration when conversation will be switched to `inactive` state. Minimum value for this timer is 1 minute.
     * @param string $timersClosed ISO8601 duration when conversation will be switched to `closed` state. Minimum value for this timer is 10 minutes.
     * @param string $bindingsEmailAddress The default email address that will be used when sending outbound emails in this conversation.
     * @param string $bindingsEmailName The default name that will be used when sending outbound emails in this conversation.
     * @param string[] $participant The participant to be added to the conversation in JSON format. The JSON object attributes are as parameters in [Participant Resource](https://www.twilio.com/docs/conversations/api/conversation-participant-resource). The maximum number of participants that can be added in a single request is 10.
     * @param string $xTwilioWebhookEnabled The X-Twilio-Webhook-Enabled HTTP request header
     */
    public function __construct(
        
        string $friendlyName = Values::NONE,
        string $uniqueName = Values::NONE,
        ?\DateTime $dateCreated = null,
        ?\DateTime $dateUpdated = null,
        string $messagingServiceSid = Values::NONE,
        string $attributes = Values::NONE,
        string $state = Values::NONE,
        string $timersInactive = Values::NONE,
        string $timersClosed = Values::NONE,
        string $bindingsEmailAddress = Values::NONE,
        string $bindingsEmailName = Values::NONE,
        array $participant = Values::ARRAY_NONE,
        string $xTwilioWebhookEnabled = Values::NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
        $this->options['uniqueName'] = $uniqueName;
        $this->options['dateCreated'] = $dateCreated;
        $this->options['dateUpdated'] = $dateUpdated;
        $this->options['messagingServiceSid'] = $messagingServiceSid;
        $this->options['attributes'] = $attributes;
        $this->options['state'] = $state;
        $this->options['timersInactive'] = $timersInactive;
        $this->options['timersClosed'] = $timersClosed;
        $this->options['bindingsEmailAddress'] = $bindingsEmailAddress;
        $this->options['bindingsEmailName'] = $bindingsEmailName;
        $this->options['participant'] = $participant;
        $this->options['xTwilioWebhookEnabled'] = $xTwilioWebhookEnabled;
    }

    /**
     * The human-readable name of this conversation, limited to 256 characters. Optional.
     *
     * @param string $friendlyName The human-readable name of this conversation, limited to 256 characters. Optional.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * An application-defined string that uniquely identifies the resource. It can be used to address the resource in place of the resource's `sid` in the URL.
     *
     * @param string $uniqueName An application-defined string that uniquely identifies the resource. It can be used to address the resource in place of the resource's `sid` in the URL.
     * @return $this Fluent Builder
     */
    public function setUniqueName(string $uniqueName): self
    {
        $this->options['uniqueName'] = $uniqueName;
        return $this;
    }

    /**
     * The date that this resource was created.
     *
     * @param \DateTime $dateCreated The date that this resource was created.
     * @return $this Fluent Builder
     */
    public function setDateCreated(\DateTime $dateCreated): self
    {
        $this->options['dateCreated'] = $dateCreated;
        return $this;
    }

    /**
     * The date that this resource was last updated.
     *
     * @param \DateTime $dateUpdated The date that this resource was last updated.
     * @return $this Fluent Builder
     */
    public function setDateUpdated(\DateTime $dateUpdated): self
    {
        $this->options['dateUpdated'] = $dateUpdated;
        return $this;
    }

    /**
     * The unique ID of the [Messaging Service](https://www.twilio.com/docs/messaging/api/service-resource) this conversation belongs to.
     *
     * @param string $messagingServiceSid The unique ID of the [Messaging Service](https://www.twilio.com/docs/messaging/api/service-resource) this conversation belongs to.
     * @return $this Fluent Builder
     */
    public function setMessagingServiceSid(string $messagingServiceSid): self
    {
        $this->options['messagingServiceSid'] = $messagingServiceSid;
        return $this;
    }

    /**
     * An optional string metadata field you can use to store any data you wish. The string value must contain structurally valid JSON if specified.  **Note** that if the attributes are not set \\\"{}\\\" will be returned.
     *
     * @param string $attributes An optional string metadata field you can use to store any data you wish. The string value must contain structurally valid JSON if specified.  **Note** that if the attributes are not set \\\"{}\\\" will be returned.
     * @return $this Fluent Builder
     */
    public function setAttributes(string $attributes): self
    {
        $this->options['attributes'] = $attributes;
        return $this;
    }

    /**
     * @param string $state
     * @return $this Fluent Builder
     */
    public function setState(string $state): self
    {
        $this->options['state'] = $state;
        return $this;
    }

    /**
     * ISO8601 duration when conversation will be switched to `inactive` state. Minimum value for this timer is 1 minute.
     *
     * @param string $timersInactive ISO8601 duration when conversation will be switched to `inactive` state. Minimum value for this timer is 1 minute.
     * @return $this Fluent Builder
     */
    public function setTimersInactive(string $timersInactive): self
    {
        $this->options['timersInactive'] = $timersInactive;
        return $this;
    }

    /**
     * ISO8601 duration when conversation will be switched to `closed` state. Minimum value for this timer is 10 minutes.
     *
     * @param string $timersClosed ISO8601 duration when conversation will be switched to `closed` state. Minimum value for this timer is 10 minutes.
     * @return $this Fluent Builder
     */
    public function setTimersClosed(string $timersClosed): self
    {
        $this->options['timersClosed'] = $timersClosed;
        return $this;
    }

    /**
     * The default email address that will be used when sending outbound emails in this conversation.
     *
     * @param string $bindingsEmailAddress The default email address that will be used when sending outbound emails in this conversation.
     * @return $this Fluent Builder
     */
    public function setBindingsEmailAddress(string $bindingsEmailAddress): self
    {
        $this->options['bindingsEmailAddress'] = $bindingsEmailAddress;
        return $this;
    }

    /**
     * The default name that will be used when sending outbound emails in this conversation.
     *
     * @param string $bindingsEmailName The default name that will be used when sending outbound emails in this conversation.
     * @return $this Fluent Builder
     */
    public function setBindingsEmailName(string $bindingsEmailName): self
    {
        $this->options['bindingsEmailName'] = $bindingsEmailName;
        return $this;
    }

    /**
     * The participant to be added to the conversation in JSON format. The JSON object attributes are as parameters in [Participant Resource](https://www.twilio.com/docs/conversations/api/conversation-participant-resource). The maximum number of participants that can be added in a single request is 10.
     *
     * @param string[] $participant The participant to be added to the conversation in JSON format. The JSON object attributes are as parameters in [Participant Resource](https://www.twilio.com/docs/conversations/api/conversation-participant-resource). The maximum number of participants that can be added in a single request is 10.
     * @return $this Fluent Builder
     */
    public function setParticipant(array $participant): self
    {
        $this->options['participant'] = $participant;
        return $this;
    }

    /**
     * The X-Twilio-Webhook-Enabled HTTP request header
     *
     * @param string $xTwilioWebhookEnabled The X-Twilio-Webhook-Enabled HTTP request header
     * @return $this Fluent Builder
     */
    public function setXTwilioWebhookEnabled(string $xTwilioWebhookEnabled): self
    {
        $this->options['xTwilioWebhookEnabled'] = $xTwilioWebhookEnabled;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Conversations.V1.CreateConversationWithParticipantsOptions ' . $options . ']';
    }
}

