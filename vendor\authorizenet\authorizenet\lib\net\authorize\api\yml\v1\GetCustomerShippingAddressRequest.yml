net\authorize\api\contract\v1\GetCustomerShippingAddressRequest:
    xml_root_name: getCustomerShippingAddressRequest
    xml_root_namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
    properties:
        customerProfileId:
            expose: true
            access_type: public_method
            serialized_name: customerProfileId
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getCustomerProfileId
                setter: setCustomerProfileId
            type: string
        customerAddressId:
            expose: true
            access_type: public_method
            serialized_name: customerAddressId
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getCustomerAddressId
                setter: setCustomerAddressId
            type: string
