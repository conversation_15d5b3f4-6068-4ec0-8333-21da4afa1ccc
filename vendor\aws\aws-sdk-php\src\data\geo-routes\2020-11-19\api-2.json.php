<?php
// This file was auto-generated from sdk-root/src/data/geo-routes/2020-11-19/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-11-19', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'geo-routes', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Amazon Location Service Routes V2', 'serviceId' => 'Geo Routes', 'signatureVersion' => 'v4', 'signingName' => 'geo-routes', 'uid' => 'geo-routes-2020-11-19', ], 'operations' => [ 'CalculateIsolines' => [ 'name' => 'CalculateIsolines', 'http' => [ 'method' => 'POST', 'requestUri' => '/isolines', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CalculateIsolinesRequest', ], 'output' => [ 'shape' => 'CalculateIsolinesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CalculateRouteMatrix' => [ 'name' => 'CalculateRouteMatrix', 'http' => [ 'method' => 'POST', 'requestUri' => '/route-matrix', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CalculateRouteMatrixRequest', ], 'output' => [ 'shape' => 'CalculateRouteMatrixResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CalculateRoutes' => [ 'name' => 'CalculateRoutes', 'http' => [ 'method' => 'POST', 'requestUri' => '/routes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CalculateRoutesRequest', ], 'output' => [ 'shape' => 'CalculateRoutesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'OptimizeWaypoints' => [ 'name' => 'OptimizeWaypoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/optimize-waypoints', 'responseCode' => 200, ], 'input' => [ 'shape' => 'OptimizeWaypointsRequest', ], 'output' => [ 'shape' => 'OptimizeWaypointsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'SnapToRoads' => [ 'name' => 'SnapToRoads', 'http' => [ 'method' => 'POST', 'requestUri' => '/snap-to-roads', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SnapToRoadsRequest', ], 'output' => [ 'shape' => 'SnapToRoadsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'ApiKey' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'sensitive' => true, ], 'BeforeWaypointsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WaypointIndex', ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'BoundingBox' => [ 'type' => 'list', 'member' => [ 'shape' => 'Double', ], 'max' => 4, 'min' => 4, 'sensitive' => true, ], 'CalculateIsolinesRequest' => [ 'type' => 'structure', 'required' => [ 'Thresholds', ], 'members' => [ 'Allow' => [ 'shape' => 'IsolineAllowOptions', ], 'ArrivalTime' => [ 'shape' => 'TimestampWithTimezoneOffset', ], 'Avoid' => [ 'shape' => 'IsolineAvoidanceOptions', ], 'DepartNow' => [ 'shape' => 'Boolean', ], 'DepartureTime' => [ 'shape' => 'TimestampWithTimezoneOffset', ], 'Destination' => [ 'shape' => 'Position', ], 'DestinationOptions' => [ 'shape' => 'IsolineDestinationOptions', ], 'IsolineGeometryFormat' => [ 'shape' => 'GeometryFormat', ], 'IsolineGranularity' => [ 'shape' => 'IsolineGranularityOptions', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], 'OptimizeIsolineFor' => [ 'shape' => 'IsolineOptimizationObjective', ], 'OptimizeRoutingFor' => [ 'shape' => 'RoutingObjective', ], 'Origin' => [ 'shape' => 'Position', ], 'OriginOptions' => [ 'shape' => 'IsolineOriginOptions', ], 'Thresholds' => [ 'shape' => 'IsolineThresholds', ], 'Traffic' => [ 'shape' => 'IsolineTrafficOptions', ], 'TravelMode' => [ 'shape' => 'IsolineTravelMode', ], 'TravelModeOptions' => [ 'shape' => 'IsolineTravelModeOptions', ], ], ], 'CalculateIsolinesResponse' => [ 'type' => 'structure', 'required' => [ 'IsolineGeometryFormat', 'Isolines', 'PricingBucket', ], 'members' => [ 'ArrivalTime' => [ 'shape' => 'TimestampWithTimezoneOffset', ], 'DepartureTime' => [ 'shape' => 'TimestampWithTimezoneOffset', ], 'IsolineGeometryFormat' => [ 'shape' => 'GeometryFormat', ], 'Isolines' => [ 'shape' => 'IsolineList', ], 'PricingBucket' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amz-geo-pricing-bucket', ], 'SnappedDestination' => [ 'shape' => 'Position', ], 'SnappedOrigin' => [ 'shape' => 'Position', ], ], ], 'CalculateRouteMatrixRequest' => [ 'type' => 'structure', 'required' => [ 'Destinations', 'Origins', 'RoutingBoundary', ], 'members' => [ 'Allow' => [ 'shape' => 'RouteMatrixAllowOptions', ], 'Avoid' => [ 'shape' => 'RouteMatrixAvoidanceOptions', ], 'DepartNow' => [ 'shape' => 'Boolean', ], 'DepartureTime' => [ 'shape' => 'TimestampWithTimezoneOffset', ], 'Destinations' => [ 'shape' => 'CalculateRouteMatrixRequestDestinationsList', ], 'Exclude' => [ 'shape' => 'RouteMatrixExclusionOptions', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], 'OptimizeRoutingFor' => [ 'shape' => 'RoutingObjective', ], 'Origins' => [ 'shape' => 'CalculateRouteMatrixRequestOriginsList', ], 'RoutingBoundary' => [ 'shape' => 'RouteMatrixBoundary', ], 'Traffic' => [ 'shape' => 'RouteMatrixTrafficOptions', ], 'TravelMode' => [ 'shape' => 'RouteMatrixTravelMode', ], 'TravelModeOptions' => [ 'shape' => 'RouteMatrixTravelModeOptions', ], ], ], 'CalculateRouteMatrixRequestDestinationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteMatrixDestination', ], 'min' => 1, ], 'CalculateRouteMatrixRequestOriginsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteMatrixOrigin', ], 'min' => 1, ], 'CalculateRouteMatrixResponse' => [ 'type' => 'structure', 'required' => [ 'ErrorCount', 'PricingBucket', 'RouteMatrix', 'RoutingBoundary', ], 'members' => [ 'ErrorCount' => [ 'shape' => 'CalculateRouteMatrixResponseErrorCountInteger', ], 'PricingBucket' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amz-geo-pricing-bucket', ], 'RouteMatrix' => [ 'shape' => 'RouteMatrix', ], 'RoutingBoundary' => [ 'shape' => 'RouteMatrixBoundary', ], ], ], 'CalculateRouteMatrixResponseErrorCountInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'CalculateRoutesRequest' => [ 'type' => 'structure', 'required' => [ 'Destination', 'Origin', ], 'members' => [ 'Allow' => [ 'shape' => 'RouteAllowOptions', ], 'ArrivalTime' => [ 'shape' => 'TimestampWithTimezoneOffset', ], 'Avoid' => [ 'shape' => 'RouteAvoidanceOptions', ], 'DepartNow' => [ 'shape' => 'Boolean', ], 'DepartureTime' => [ 'shape' => 'TimestampWithTimezoneOffset', ], 'Destination' => [ 'shape' => 'Position', ], 'DestinationOptions' => [ 'shape' => 'RouteDestinationOptions', ], 'Driver' => [ 'shape' => 'RouteDriverOptions', ], 'Exclude' => [ 'shape' => 'RouteExclusionOptions', ], 'InstructionsMeasurementSystem' => [ 'shape' => 'MeasurementSystem', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], 'Languages' => [ 'shape' => 'CalculateRoutesRequestLanguagesList', ], 'LegAdditionalFeatures' => [ 'shape' => 'RouteLegAdditionalFeatureList', ], 'LegGeometryFormat' => [ 'shape' => 'GeometryFormat', ], 'MaxAlternatives' => [ 'shape' => 'CalculateRoutesRequestMaxAlternativesInteger', ], 'OptimizeRoutingFor' => [ 'shape' => 'RoutingObjective', ], 'Origin' => [ 'shape' => 'Position', ], 'OriginOptions' => [ 'shape' => 'RouteOriginOptions', ], 'SpanAdditionalFeatures' => [ 'shape' => 'RouteSpanAdditionalFeatureList', ], 'Tolls' => [ 'shape' => 'RouteTollOptions', ], 'Traffic' => [ 'shape' => 'RouteTrafficOptions', ], 'TravelMode' => [ 'shape' => 'RouteTravelMode', ], 'TravelModeOptions' => [ 'shape' => 'RouteTravelModeOptions', ], 'TravelStepType' => [ 'shape' => 'RouteTravelStepType', ], 'Waypoints' => [ 'shape' => 'RouteWaypointList', ], ], ], 'CalculateRoutesRequestLanguagesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LanguageTag', ], 'max' => 10, 'min' => 0, ], 'CalculateRoutesRequestMaxAlternativesInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 5, 'min' => 0, ], 'CalculateRoutesResponse' => [ 'type' => 'structure', 'required' => [ 'LegGeometryFormat', 'Notices', 'PricingBucket', 'Routes', ], 'members' => [ 'LegGeometryFormat' => [ 'shape' => 'GeometryFormat', ], 'Notices' => [ 'shape' => 'RouteResponseNoticeList', ], 'PricingBucket' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amz-geo-pricing-bucket', ], 'Routes' => [ 'shape' => 'RouteList', ], ], ], 'Circle' => [ 'type' => 'structure', 'required' => [ 'Center', 'Radius', ], 'members' => [ 'Center' => [ 'shape' => 'Position', ], 'Radius' => [ 'shape' => 'Double', ], ], 'sensitive' => true, ], 'ClusterIndex' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'Corridor' => [ 'type' => 'structure', 'required' => [ 'LineString', 'Radius', ], 'members' => [ 'LineString' => [ 'shape' => 'LineString', ], 'Radius' => [ 'shape' => 'Integer', ], ], 'sensitive' => true, ], 'CountryCode' => [ 'type' => 'string', 'max' => 3, 'min' => 2, 'pattern' => '([A-Z]{2}|[A-Z]{3})', ], 'CountryCode3' => [ 'type' => 'string', 'max' => 3, 'min' => 3, 'pattern' => '[A-Z]{3}', ], 'CountryCodeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CountryCode', ], 'max' => 100, 'min' => 1, ], 'CurrencyCode' => [ 'type' => 'string', 'max' => 3, 'min' => 3, 'pattern' => '[A-Z]{3}', ], 'DayOfWeek' => [ 'type' => 'string', 'enum' => [ 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday', ], ], 'DimensionCentimeters' => [ 'type' => 'long', 'max' => 4294967295, 'min' => 0, ], 'DistanceMeters' => [ 'type' => 'long', 'max' => 4294967295, 'min' => 0, ], 'DistanceThresholdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DistanceThresholdListMemberLong', ], 'max' => 5, 'min' => 1, ], 'DistanceThresholdListMemberLong' => [ 'type' => 'long', 'max' => 300000, 'min' => 0, ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'DurationSeconds' => [ 'type' => 'long', 'max' => 4294967295, 'min' => 0, ], 'GeometryFormat' => [ 'type' => 'string', 'enum' => [ 'FlexiblePolyline', 'Simple', ], ], 'Heading' => [ 'type' => 'double', 'max' => 360.0, 'min' => 0.0, ], 'IndexList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Integer', ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'Isoline' => [ 'type' => 'structure', 'required' => [ 'Connections', 'Geometries', ], 'members' => [ 'Connections' => [ 'shape' => 'IsolineConnectionList', ], 'DistanceThreshold' => [ 'shape' => 'DistanceMeters', ], 'Geometries' => [ 'shape' => 'IsolineShapeGeometryList', ], 'TimeThreshold' => [ 'shape' => 'DurationSeconds', ], ], ], 'IsolineAllowOptions' => [ 'type' => 'structure', 'members' => [ 'Hot' => [ 'shape' => 'Boolean', ], 'Hov' => [ 'shape' => 'Boolean', ], ], ], 'IsolineAvoidanceArea' => [ 'type' => 'structure', 'required' => [ 'Geometry', ], 'members' => [ 'Except' => [ 'shape' => '********************************', ], 'Geometry' => [ 'shape' => 'IsolineAvoidanceAreaGeometry', ], ], ], 'IsolineAvoidanceAreaGeometry' => [ 'type' => 'structure', 'members' => [ 'BoundingBox' => [ 'shape' => 'BoundingBox', ], 'Corridor' => [ 'shape' => 'Corridor', ], 'Polygon' => [ 'shape' => 'IsolineAvoidanceAreaGeometryPolygonList', ], 'PolylineCorridor' => [ 'shape' => 'PolylineCorridor', ], 'PolylinePolygon' => [ 'shape' => 'IsolineAvoidanceAreaGeometryPolylinePolygonList', ], ], ], '********************************' => [ 'type' => 'list', 'member' => [ 'shape' => 'IsolineAvoidanceAreaGeometry', ], ], 'IsolineAvoidanceAreaGeometryPolygonList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LinearRing', ], 'max' => 1, 'min' => 1, ], 'IsolineAvoidanceAreaGeometryPolylinePolygonList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolylineRing', ], 'max' => 1, 'min' => 1, ], 'IsolineAvoidanceAreaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IsolineAvoidanceArea', ], ], 'IsolineAvoidanceOptions' => [ 'type' => 'structure', 'members' => [ 'Areas' => [ 'shape' => 'IsolineAvoidanceAreaList', ], 'CarShuttleTrains' => [ 'shape' => 'Boolean', ], 'ControlledAccessHighways' => [ 'shape' => 'Boolean', ], 'DirtRoads' => [ 'shape' => 'Boolean', ], 'Ferries' => [ 'shape' => 'Boolean', ], 'SeasonalClosure' => [ 'shape' => 'Boolean', ], 'TollRoads' => [ 'shape' => 'Boolean', ], 'TollTransponders' => [ 'shape' => 'Boolean', ], 'TruckRoadTypes' => [ 'shape' => 'TruckRoadTypeList', ], 'Tunnels' => [ 'shape' => 'Boolean', ], 'UTurns' => [ 'shape' => 'Boolean', ], 'ZoneCategories' => [ 'shape' => 'IsolineAvoidanceZoneCategoryList', ], ], ], 'IsolineAvoidanceZoneCategory' => [ 'type' => 'structure', 'members' => [ 'Category' => [ 'shape' => 'IsolineZoneCategory', ], ], ], 'IsolineAvoidanceZoneCategoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IsolineAvoidanceZoneCategory', ], 'max' => 3, 'min' => 0, ], 'IsolineCarOptions' => [ 'type' => 'structure', 'members' => [ 'EngineType' => [ 'shape' => 'IsolineEngineType', ], 'LicensePlate' => [ 'shape' => 'IsolineVehicleLicensePlate', ], 'MaxSpeed' => [ 'shape' => 'IsolineCarOptionsMaxSpeedDouble', 'box' => true, ], 'Occupancy' => [ 'shape' => 'IsolineCarOptionsOccupancyInteger', ], ], ], 'IsolineCarOptionsMaxSpeedDouble' => [ 'type' => 'double', 'max' => 252.0, 'min' => 3.6, ], 'IsolineCarOptionsOccupancyInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'IsolineConnection' => [ 'type' => 'structure', 'required' => [ 'FromPolygonIndex', 'Geometry', 'ToPolygonIndex', ], 'members' => [ 'FromPolygonIndex' => [ 'shape' => 'IsolineConnectionFromPolygonIndexInteger', ], 'Geometry' => [ 'shape' => 'IsolineConnectionGeometry', ], 'ToPolygonIndex' => [ 'shape' => 'IsolineConnectionToPolygonIndexInteger', ], ], ], 'IsolineConnectionFromPolygonIndexInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'IsolineConnectionGeometry' => [ 'type' => 'structure', 'members' => [ 'LineString' => [ 'shape' => 'LineString', ], 'Polyline' => [ 'shape' => 'Polyline', ], ], ], 'IsolineConnectionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IsolineConnection', ], ], 'IsolineConnectionToPolygonIndexInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'IsolineDestinationOptions' => [ 'type' => 'structure', 'members' => [ 'AvoidActionsForDistance' => [ 'shape' => 'DistanceMeters', ], 'Heading' => [ 'shape' => 'Heading', ], 'Matching' => [ 'shape' => 'IsolineMatchingOptions', ], 'SideOfStreet' => [ 'shape' => 'IsolineSideOfStreetOptions', ], ], ], 'IsolineEngineType' => [ 'type' => 'string', 'enum' => [ 'Electric', 'InternalCombustion', 'PluginHybrid', ], ], 'IsolineGranularityOptions' => [ 'type' => 'structure', 'members' => [ 'MaxPoints' => [ 'shape' => 'IsolineGranularityOptionsMaxPointsInteger', ], 'MaxResolution' => [ 'shape' => 'DistanceMeters', ], ], ], 'IsolineGranularityOptionsMaxPointsInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 31, ], 'IsolineHazardousCargoType' => [ 'type' => 'string', 'enum' => [ 'Combustible', 'Corrosive', 'Explosive', 'Flammable', 'Gas', 'HarmfulToWater', 'Organic', 'Other', 'Poison', 'PoisonousInhalation', 'Radioactive', ], ], 'IsolineHazardousCargoTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IsolineHazardousCargoType', ], 'max' => 11, 'min' => 0, ], 'IsolineList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Isoline', ], 'max' => 5, 'min' => 1, ], 'IsolineMatchingOptions' => [ 'type' => 'structure', 'members' => [ 'NameHint' => [ 'shape' => 'SensitiveString', ], 'OnRoadThreshold' => [ 'shape' => 'DistanceMeters', ], 'Radius' => [ 'shape' => 'DistanceMeters', ], 'Strategy' => [ 'shape' => 'MatchingStrategy', ], ], ], 'IsolineOptimizationObjective' => [ 'type' => 'string', 'enum' => [ 'AccurateCalculation', 'BalancedCalculation', 'FastCalculation', ], ], 'IsolineOriginOptions' => [ 'type' => 'structure', 'members' => [ 'AvoidActionsForDistance' => [ 'shape' => 'DistanceMeters', ], 'Heading' => [ 'shape' => 'Heading', ], 'Matching' => [ 'shape' => 'IsolineMatchingOptions', ], 'SideOfStreet' => [ 'shape' => 'IsolineSideOfStreetOptions', ], ], ], 'IsolineScooterOptions' => [ 'type' => 'structure', 'members' => [ 'EngineType' => [ 'shape' => 'IsolineEngineType', ], 'LicensePlate' => [ 'shape' => 'IsolineVehicleLicensePlate', ], 'MaxSpeed' => [ 'shape' => 'IsolineScooterOptionsMaxSpeedDouble', 'box' => true, ], 'Occupancy' => [ 'shape' => 'IsolineScooterOptionsOccupancyInteger', ], ], ], 'IsolineScooterOptionsMaxSpeedDouble' => [ 'type' => 'double', 'max' => 252.0, 'min' => 3.6, ], 'IsolineScooterOptionsOccupancyInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'IsolineShapeGeometry' => [ 'type' => 'structure', 'members' => [ 'Polygon' => [ 'shape' => 'LinearRings', ], 'PolylinePolygon' => [ 'shape' => 'PolylineRingList', ], ], ], 'IsolineShapeGeometryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IsolineShapeGeometry', ], ], 'IsolineSideOfStreetOptions' => [ 'type' => 'structure', 'required' => [ 'Position', ], 'members' => [ 'Position' => [ 'shape' => 'Position', ], 'UseWith' => [ 'shape' => 'SideOfStreetMatchingStrategy', ], ], ], 'IsolineThresholds' => [ 'type' => 'structure', 'members' => [ 'Distance' => [ 'shape' => 'DistanceThresholdList', ], 'Time' => [ 'shape' => 'TimeThresholdList', ], ], ], 'IsolineTrafficOptions' => [ 'type' => 'structure', 'members' => [ 'FlowEventThresholdOverride' => [ 'shape' => 'DurationSeconds', ], 'Usage' => [ 'shape' => 'TrafficUsage', ], ], ], 'IsolineTrailerOptions' => [ 'type' => 'structure', 'members' => [ 'AxleCount' => [ 'shape' => 'IsolineTrailerOptionsAxleCountInteger', ], 'TrailerCount' => [ 'shape' => 'IsolineTrailerOptionsTrailerCountInteger', ], ], ], 'IsolineTrailerOptionsAxleCountInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'IsolineTrailerOptionsTrailerCountInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 255, 'min' => 1, ], 'IsolineTravelMode' => [ 'type' => 'string', 'enum' => [ 'Car', 'Pedestrian', 'Scooter', 'Truck', ], ], 'IsolineTravelModeOptions' => [ 'type' => 'structure', 'members' => [ 'Car' => [ 'shape' => 'IsolineCarOptions', ], 'Scooter' => [ 'shape' => 'IsolineScooterOptions', ], 'Truck' => [ 'shape' => 'IsolineTruckOptions', ], ], ], 'IsolineTruckOptions' => [ 'type' => 'structure', 'members' => [ 'AxleCount' => [ 'shape' => 'IsolineTruckOptionsAxleCountInteger', ], 'EngineType' => [ 'shape' => 'IsolineEngineType', ], 'GrossWeight' => [ 'shape' => 'WeightKilograms', ], 'HazardousCargos' => [ 'shape' => 'IsolineHazardousCargoTypeList', ], 'Height' => [ 'shape' => 'IsolineTruckOptionsHeightLong', ], 'HeightAboveFirstAxle' => [ 'shape' => 'IsolineTruckOptionsHeightAboveFirstAxleLong', ], 'KpraLength' => [ 'shape' => 'DimensionCentimeters', ], 'Length' => [ 'shape' => 'IsolineTruckOptionsLengthLong', ], 'LicensePlate' => [ 'shape' => 'IsolineVehicleLicensePlate', ], 'MaxSpeed' => [ 'shape' => 'IsolineTruckOptionsMaxSpeedDouble', 'box' => true, ], 'Occupancy' => [ 'shape' => 'IsolineTruckOptionsOccupancyInteger', ], 'PayloadCapacity' => [ 'shape' => 'WeightKilograms', ], 'TireCount' => [ 'shape' => 'IsolineTruckOptionsTireCountInteger', ], 'Trailer' => [ 'shape' => 'IsolineTrailerOptions', ], 'TruckType' => [ 'shape' => 'IsolineTruckType', ], 'TunnelRestrictionCode' => [ 'shape' => 'TunnelRestrictionCode', ], 'WeightPerAxle' => [ 'shape' => 'WeightKilograms', ], 'WeightPerAxleGroup' => [ 'shape' => 'WeightPerAxleGroup', ], 'Width' => [ 'shape' => 'IsolineTruckOptionsWidthLong', ], ], ], 'IsolineTruckOptionsAxleCountInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 255, 'min' => 2, ], 'IsolineTruckOptionsHeightAboveFirstAxleLong' => [ 'type' => 'long', 'max' => 5000, 'min' => 0, ], 'IsolineTruckOptionsHeightLong' => [ 'type' => 'long', 'max' => 5000, 'min' => 0, ], 'IsolineTruckOptionsLengthLong' => [ 'type' => 'long', 'max' => 30000, 'min' => 0, ], 'IsolineTruckOptionsMaxSpeedDouble' => [ 'type' => 'double', 'max' => 252.0, 'min' => 3.6, ], 'IsolineTruckOptionsOccupancyInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'IsolineTruckOptionsTireCountInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 255, 'min' => 1, ], 'IsolineTruckOptionsWidthLong' => [ 'type' => 'long', 'max' => 5000, 'min' => 0, ], 'IsolineTruckType' => [ 'type' => 'string', 'enum' => [ 'LightTruck', 'StraightTruck', 'Tractor', ], ], 'IsolineVehicleLicensePlate' => [ 'type' => 'structure', 'members' => [ 'LastCharacter' => [ 'shape' => 'IsolineVehicleLicensePlateLastCharacterString', ], ], ], 'IsolineVehicleLicensePlateLastCharacterString' => [ 'type' => 'string', 'max' => 1, 'min' => 1, ], 'IsolineZoneCategory' => [ 'type' => 'string', 'enum' => [ 'CongestionPricing', 'Environmental', 'Vignette', ], ], 'LanguageTag' => [ 'type' => 'string', 'max' => 35, 'min' => 2, ], 'LineString' => [ 'type' => 'list', 'member' => [ 'shape' => 'Position', ], 'min' => 2, ], 'LinearRing' => [ 'type' => 'list', 'member' => [ 'shape' => 'Position', ], 'min' => 4, ], 'LinearRings' => [ 'type' => 'list', 'member' => [ 'shape' => 'LinearRing', ], 'min' => 1, ], 'LocalizedString' => [ 'type' => 'structure', 'required' => [ 'Value', ], 'members' => [ 'Language' => [ 'shape' => 'LanguageTag', ], 'Value' => [ 'shape' => 'String', ], ], ], 'LocalizedStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LocalizedString', ], ], 'MatchingStrategy' => [ 'type' => 'string', 'enum' => [ 'MatchAny', 'MatchMostSignificantRoad', ], ], 'MeasurementSystem' => [ 'type' => 'string', 'enum' => [ 'Metric', 'Imperial', ], ], 'OptimizeWaypointsRequest' => [ 'type' => 'structure', 'required' => [ 'Origin', ], 'members' => [ 'Avoid' => [ 'shape' => 'WaypointOptimizationAvoidanceOptions', ], 'Clustering' => [ 'shape' => 'WaypointOptimizationClusteringOptions', ], 'DepartureTime' => [ 'shape' => 'TimestampWithTimezoneOffset', ], 'Destination' => [ 'shape' => 'Position', ], 'DestinationOptions' => [ 'shape' => 'WaypointOptimizationDestinationOptions', ], 'Driver' => [ 'shape' => 'WaypointOptimizationDriverOptions', ], 'Exclude' => [ 'shape' => 'WaypointOptimizationExclusionOptions', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], 'OptimizeSequencingFor' => [ 'shape' => 'WaypointOptimizationSequencingObjective', ], 'Origin' => [ 'shape' => 'Position', ], 'OriginOptions' => [ 'shape' => 'WaypointOptimizationOriginOptions', ], 'Traffic' => [ 'shape' => 'WaypointOptimizationTrafficOptions', ], 'TravelMode' => [ 'shape' => 'WaypointOptimizationTravelMode', ], 'TravelModeOptions' => [ 'shape' => 'WaypointOptimizationTravelModeOptions', ], 'Waypoints' => [ 'shape' => 'WaypointOptimizationWaypointList', ], ], ], 'OptimizeWaypointsResponse' => [ 'type' => 'structure', 'required' => [ 'Connections', 'Distance', 'Duration', 'ImpedingWaypoints', 'OptimizedWaypoints', 'PricingBucket', 'TimeBreakdown', ], 'members' => [ 'Connections' => [ 'shape' => 'WaypointOptimizationConnectionList', ], 'Distance' => [ 'shape' => 'DistanceMeters', ], 'Duration' => [ 'shape' => 'DurationSeconds', ], 'ImpedingWaypoints' => [ 'shape' => 'WaypointOptimizationImpedingWaypointList', ], 'OptimizedWaypoints' => [ 'shape' => 'WaypointOptimizationOptimizedWaypointList', ], 'PricingBucket' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amz-geo-pricing-bucket', ], 'TimeBreakdown' => [ 'shape' => 'WaypointOptimizationTimeBreakdown', ], ], ], 'Polyline' => [ 'type' => 'string', 'min' => 1, 'sensitive' => true, ], 'PolylineCorridor' => [ 'type' => 'structure', 'required' => [ 'Polyline', 'Radius', ], 'members' => [ 'Polyline' => [ 'shape' => 'Polyline', ], 'Radius' => [ 'shape' => 'Integer', ], ], 'sensitive' => true, ], 'PolylineRing' => [ 'type' => 'string', 'min' => 1, 'sensitive' => true, ], 'PolylineRingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolylineRing', ], 'min' => 1, ], 'Position' => [ 'type' => 'list', 'member' => [ 'shape' => 'Double', ], 'max' => 2, 'min' => 2, 'sensitive' => true, ], 'Position23' => [ 'type' => 'list', 'member' => [ 'shape' => 'Double', ], 'max' => 3, 'min' => 2, 'sensitive' => true, ], 'RoadSnapHazardousCargoType' => [ 'type' => 'string', 'enum' => [ 'Combustible', 'Corrosive', 'Explosive', 'Flammable', 'Gas', 'HarmfulToWater', 'Organic', 'Other', 'Poison', 'PoisonousInhalation', 'Radioactive', ], ], 'RoadSnapHazardousCargoTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoadSnapHazardousCargoType', ], 'max' => 11, 'min' => 0, ], 'RoadSnapNotice' => [ 'type' => 'structure', 'required' => [ 'Code', 'Title', 'TracePointIndexes', ], 'members' => [ 'Code' => [ 'shape' => 'RoadSnapNoticeCode', ], 'Title' => [ 'shape' => 'String', ], 'TracePointIndexes' => [ 'shape' => 'RoadSnapTracePointIndexList', ], ], ], 'RoadSnapNoticeCode' => [ 'type' => 'string', 'enum' => [ 'TracePointsHeadingIgnored', 'TracePointsIgnored', 'TracePointsMovedByLargeDistance', 'TracePointsNotMatched', 'TracePointsOutOfSequence', 'TracePointsSpeedEstimated', 'TracePointsSpeedIgnored', ], ], 'RoadSnapNoticeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoadSnapNotice', ], ], 'RoadSnapSnappedGeometry' => [ 'type' => 'structure', 'members' => [ 'LineString' => [ 'shape' => 'LineString', ], 'Polyline' => [ 'shape' => 'Polyline', ], ], ], 'RoadSnapSnappedTracePoint' => [ 'type' => 'structure', 'required' => [ 'Confidence', 'OriginalPosition', 'SnappedPosition', ], 'members' => [ 'Confidence' => [ 'shape' => 'RoadSnapSnappedTracePointConfidenceDouble', ], 'OriginalPosition' => [ 'shape' => 'Position', ], 'SnappedPosition' => [ 'shape' => 'Position', ], ], ], 'RoadSnapSnappedTracePointConfidenceDouble' => [ 'type' => 'double', 'box' => true, 'max' => 1, 'min' => 0, ], 'RoadSnapSnappedTracePointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoadSnapSnappedTracePoint', ], ], 'RoadSnapTracePoint' => [ 'type' => 'structure', 'required' => [ 'Position', ], 'members' => [ 'Heading' => [ 'shape' => 'Heading', ], 'Position' => [ 'shape' => 'Position', ], 'Speed' => [ 'shape' => 'SpeedKilometersPerHour', ], 'Timestamp' => [ 'shape' => 'TimestampWithTimezoneOffset', ], ], ], 'RoadSnapTracePointIndexList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Integer', ], 'max' => 1000, 'min' => 1, ], 'RoadSnapTrailerOptions' => [ 'type' => 'structure', 'members' => [ 'TrailerCount' => [ 'shape' => 'RoadSnapTrailerOptionsTrailerCountInteger', ], ], ], 'RoadSnapTrailerOptionsTrailerCountInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 255, 'min' => 0, ], 'RoadSnapTravelMode' => [ 'type' => 'string', 'enum' => [ 'Car', 'Pedestrian', 'Scooter', 'Truck', ], ], 'RoadSnapTravelModeOptions' => [ 'type' => 'structure', 'members' => [ 'Truck' => [ 'shape' => 'RoadSnapTruckOptions', ], ], ], 'RoadSnapTruckOptions' => [ 'type' => 'structure', 'members' => [ 'GrossWeight' => [ 'shape' => 'WeightKilograms', ], 'HazardousCargos' => [ 'shape' => 'RoadSnapHazardousCargoTypeList', ], 'Height' => [ 'shape' => 'RoadSnapTruckOptionsHeightLong', ], 'Length' => [ 'shape' => 'RoadSnapTruckOptionsLengthLong', ], 'Trailer' => [ 'shape' => 'RoadSnapTrailerOptions', ], 'TunnelRestrictionCode' => [ 'shape' => 'TunnelRestrictionCode', ], 'Width' => [ 'shape' => 'RoadSnapTruckOptionsWidthLong', ], ], ], 'RoadSnapTruckOptionsHeightLong' => [ 'type' => 'long', 'max' => 5000, 'min' => 0, ], 'RoadSnapTruckOptionsLengthLong' => [ 'type' => 'long', 'max' => 30000, 'min' => 0, ], 'RoadSnapTruckOptionsWidthLong' => [ 'type' => 'long', 'max' => 5000, 'min' => 0, ], 'RoundaboutAngle' => [ 'type' => 'double', 'max' => 360, 'min' => -360, ], 'Route' => [ 'type' => 'structure', 'required' => [ 'Legs', 'MajorRoadLabels', ], 'members' => [ 'Legs' => [ 'shape' => 'RouteLegList', ], 'MajorRoadLabels' => [ 'shape' => 'RouteMajorRoadLabelsList', ], 'Summary' => [ 'shape' => 'RouteSummary', ], ], ], 'RouteAllowOptions' => [ 'type' => 'structure', 'members' => [ 'Hot' => [ 'shape' => 'Boolean', ], 'Hov' => [ 'shape' => 'Boolean', ], ], ], 'RouteAvoidanceArea' => [ 'type' => 'structure', 'required' => [ 'Geometry', ], 'members' => [ 'Except' => [ 'shape' => 'RouteAvoidanceAreaGeometryList', ], 'Geometry' => [ 'shape' => 'RouteAvoidanceAreaGeometry', ], ], ], 'RouteAvoidanceAreaGeometry' => [ 'type' => 'structure', 'members' => [ 'Corridor' => [ 'shape' => 'Corridor', ], 'BoundingBox' => [ 'shape' => 'BoundingBox', ], 'Polygon' => [ 'shape' => 'RouteAvoidanceAreaGeometryPolygonList', ], 'PolylineCorridor' => [ 'shape' => 'PolylineCorridor', ], 'PolylinePolygon' => [ 'shape' => 'RouteAvoidanceAreaGeometryPolylinePolygonList', ], ], ], 'RouteAvoidanceAreaGeometryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteAvoidanceAreaGeometry', ], ], 'RouteAvoidanceAreaGeometryPolygonList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LinearRing', ], 'max' => 1, 'min' => 1, ], 'RouteAvoidanceAreaGeometryPolylinePolygonList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolylineRing', ], 'max' => 1, 'min' => 1, ], 'RouteAvoidanceAreaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteAvoidanceArea', ], ], 'RouteAvoidanceOptions' => [ 'type' => 'structure', 'members' => [ 'Areas' => [ 'shape' => 'RouteAvoidanceAreaList', ], 'CarShuttleTrains' => [ 'shape' => 'Boolean', ], 'ControlledAccessHighways' => [ 'shape' => 'Boolean', ], 'DirtRoads' => [ 'shape' => 'Boolean', ], 'Ferries' => [ 'shape' => 'Boolean', ], 'SeasonalClosure' => [ 'shape' => 'Boolean', ], 'TollRoads' => [ 'shape' => 'Boolean', ], 'TollTransponders' => [ 'shape' => 'Boolean', ], 'TruckRoadTypes' => [ 'shape' => 'TruckRoadTypeList', ], 'Tunnels' => [ 'shape' => 'Boolean', ], 'UTurns' => [ 'shape' => 'Boolean', ], 'ZoneCategories' => [ 'shape' => 'RouteAvoidanceZoneCategoryList', ], ], ], 'RouteAvoidanceZoneCategory' => [ 'type' => 'structure', 'required' => [ 'Category', ], 'members' => [ 'Category' => [ 'shape' => 'RouteZoneCategory', ], ], ], 'RouteAvoidanceZoneCategoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteAvoidanceZoneCategory', ], 'max' => 3, 'min' => 0, ], 'RouteCarOptions' => [ 'type' => 'structure', 'members' => [ 'EngineType' => [ 'shape' => 'RouteEngineType', ], 'LicensePlate' => [ 'shape' => 'RouteVehicleLicensePlate', ], 'MaxSpeed' => [ 'shape' => 'RouteCarOptionsMaxSpeedDouble', 'box' => true, ], 'Occupancy' => [ 'shape' => 'RouteCarOptionsOccupancyInteger', ], ], ], 'RouteCarOptionsMaxSpeedDouble' => [ 'type' => 'double', 'max' => 252.0, 'min' => 3.6, ], 'RouteCarOptionsOccupancyInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'RouteContinueHighwayStepDetails' => [ 'type' => 'structure', 'required' => [ 'Intersection', ], 'members' => [ 'Intersection' => [ 'shape' => 'LocalizedStringList', ], 'SteeringDirection' => [ 'shape' => 'RouteSteeringDirection', ], 'TurnAngle' => [ 'shape' => 'TurnAngle', ], 'TurnIntensity' => [ 'shape' => 'RouteTurnIntensity', ], ], ], 'RouteContinueStepDetails' => [ 'type' => 'structure', 'required' => [ 'Intersection', ], 'members' => [ 'Intersection' => [ 'shape' => 'LocalizedStringList', ], ], ], 'RouteDestinationOptions' => [ 'type' => 'structure', 'members' => [ 'AvoidActionsForDistance' => [ 'shape' => 'RouteDestinationOptionsAvoidActionsForDistanceLong', ], 'AvoidUTurns' => [ 'shape' => 'Boolean', ], 'Heading' => [ 'shape' => 'Heading', ], 'Matching' => [ 'shape' => 'RouteMatchingOptions', ], 'SideOfStreet' => [ 'shape' => 'RouteSideOfStreetOptions', ], 'StopDuration' => [ 'shape' => 'DurationSeconds', ], ], ], 'RouteDestinationOptionsAvoidActionsForDistanceLong' => [ 'type' => 'long', 'max' => 2000, ], 'RouteDirection' => [ 'type' => 'string', 'enum' => [ 'East', 'North', 'South', 'West', ], ], 'RouteDriverOptions' => [ 'type' => 'structure', 'members' => [ 'Schedule' => [ 'shape' => 'RouteDriverScheduleIntervalList', ], ], ], 'RouteDriverScheduleInterval' => [ 'type' => 'structure', 'required' => [ 'DriveDuration', 'RestDuration', ], 'members' => [ 'DriveDuration' => [ 'shape' => 'DurationSeconds', ], 'RestDuration' => [ 'shape' => 'DurationSeconds', ], ], ], 'RouteDriverScheduleIntervalList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteDriverScheduleInterval', ], ], 'RouteEmissionType' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Co2EmissionClass' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'String', ], ], ], 'RouteEngineType' => [ 'type' => 'string', 'enum' => [ 'Electric', 'InternalCombustion', 'PluginHybrid', ], ], 'RouteEnterHighwayStepDetails' => [ 'type' => 'structure', 'required' => [ 'Intersection', ], 'members' => [ 'Intersection' => [ 'shape' => 'LocalizedStringList', ], 'SteeringDirection' => [ 'shape' => 'RouteSteeringDirection', ], 'TurnAngle' => [ 'shape' => 'TurnAngle', ], 'TurnIntensity' => [ 'shape' => 'RouteTurnIntensity', ], ], ], 'RouteExclusionOptions' => [ 'type' => 'structure', 'required' => [ 'Countries', ], 'members' => [ 'Countries' => [ 'shape' => 'CountryCodeList', ], ], ], 'RouteExitStepDetails' => [ 'type' => 'structure', 'required' => [ 'Intersection', ], 'members' => [ 'Intersection' => [ 'shape' => 'LocalizedStringList', ], 'RelativeExit' => [ 'shape' => 'RouteExitStepDetailsRelativeExitInteger', ], 'SteeringDirection' => [ 'shape' => 'RouteSteeringDirection', ], 'TurnAngle' => [ 'shape' => 'TurnAngle', ], 'TurnIntensity' => [ 'shape' => 'RouteTurnIntensity', ], ], ], 'RouteExitStepDetailsRelativeExitInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 12, 'min' => 1, ], 'RouteFerryAfterTravelStep' => [ 'type' => 'structure', 'required' => [ 'Duration', 'Type', ], 'members' => [ 'Duration' => [ 'shape' => 'DurationSeconds', ], 'Instruction' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'RouteFerryAfterTravelStepType', ], ], ], 'RouteFerryAfterTravelStepList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteFerryAfterTravelStep', ], ], 'RouteFerryAfterTravelStepType' => [ 'type' => 'string', 'enum' => [ 'Deboard', ], ], 'RouteFerryArrival' => [ 'type' => 'structure', 'required' => [ 'Place', ], 'members' => [ 'Place' => [ 'shape' => 'RouteFerryPlace', ], 'Time' => [ 'shape' => 'TimestampWithTimezoneOffset', ], ], ], 'RouteFerryBeforeTravelStep' => [ 'type' => 'structure', 'required' => [ 'Duration', 'Type', ], 'members' => [ 'Duration' => [ 'shape' => 'DurationSeconds', ], 'Instruction' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'RouteFerryBeforeTravelStepType', ], ], ], 'RouteFerryBeforeTravelStepList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteFerryBeforeTravelStep', ], ], 'RouteFerryBeforeTravelStepType' => [ 'type' => 'string', 'enum' => [ 'Board', ], ], 'RouteFerryDeparture' => [ 'type' => 'structure', 'required' => [ 'Place', ], 'members' => [ 'Place' => [ 'shape' => 'RouteFerryPlace', ], 'Time' => [ 'shape' => 'TimestampWithTimezoneOffset', ], ], ], 'RouteFerryLegDetails' => [ 'type' => 'structure', 'required' => [ 'AfterTravelSteps', 'Arrival', 'BeforeTravelSteps', 'Departure', 'Notices', 'PassThroughWaypoints', 'Spans', 'TravelSteps', ], 'members' => [ 'AfterTravelSteps' => [ 'shape' => 'RouteFerryAfterTravelStepList', ], 'Arrival' => [ 'shape' => 'RouteFerryArrival', ], 'BeforeTravelSteps' => [ 'shape' => 'RouteFerryBeforeTravelStepList', ], 'Departure' => [ 'shape' => 'RouteFerryDeparture', ], 'Notices' => [ 'shape' => 'RouteFerryNoticeList', ], 'PassThroughWaypoints' => [ 'shape' => 'RoutePassThroughWaypointList', ], 'RouteName' => [ 'shape' => 'String', ], 'Spans' => [ 'shape' => 'RouteFerrySpanList', ], 'Summary' => [ 'shape' => 'RouteFerrySummary', ], 'TravelSteps' => [ 'shape' => 'RouteFerryTravelStepList', ], ], ], 'RouteFerryNotice' => [ 'type' => 'structure', 'required' => [ 'Code', ], 'members' => [ 'Code' => [ 'shape' => 'RouteFerryNoticeCode', ], 'Impact' => [ 'shape' => 'RouteNoticeImpact', ], ], ], 'RouteFerryNoticeCode' => [ 'type' => 'string', 'enum' => [ 'AccuratePolylineUnavailable', 'NoSchedule', 'Other', 'ViolatedAvoidFerry', 'ViolatedAvoidRailFerry', 'SeasonalClosure', ], ], 'RouteFerryNoticeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteFerryNotice', ], ], 'RouteFerryOverviewSummary' => [ 'type' => 'structure', 'required' => [ 'Distance', 'Duration', ], 'members' => [ 'Distance' => [ 'shape' => 'DistanceMeters', ], 'Duration' => [ 'shape' => 'DurationSeconds', ], ], ], 'RouteFerryPlace' => [ 'type' => 'structure', 'required' => [ 'Position', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'OriginalPosition' => [ 'shape' => 'Position23', ], 'Position' => [ 'shape' => 'Position23', ], 'WaypointIndex' => [ 'shape' => 'RouteFerryPlaceWaypointIndexInteger', ], ], ], 'RouteFerryPlaceWaypointIndexInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'RouteFerrySpan' => [ 'type' => 'structure', 'members' => [ 'Country' => [ 'shape' => 'CountryCode3', ], 'Distance' => [ 'shape' => 'DistanceMeters', ], 'Duration' => [ 'shape' => 'DurationSeconds', ], 'GeometryOffset' => [ 'shape' => 'RouteFerrySpanGeometryOffsetInteger', ], 'Names' => [ 'shape' => 'LocalizedStringList', ], 'Region' => [ 'shape' => 'RouteFerrySpanRegionString', ], ], ], 'RouteFerrySpanGeometryOffsetInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'RouteFerrySpanList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteFerrySpan', ], ], 'RouteFerrySpanRegionString' => [ 'type' => 'string', 'max' => 3, 'min' => 0, ], 'RouteFerrySummary' => [ 'type' => 'structure', 'members' => [ 'Overview' => [ 'shape' => 'RouteFerryOverviewSummary', ], 'TravelOnly' => [ 'shape' => 'RouteFerryTravelOnlySummary', ], ], ], 'RouteFerryTravelOnlySummary' => [ 'type' => 'structure', 'required' => [ 'Duration', ], 'members' => [ 'Duration' => [ 'shape' => 'DurationSeconds', ], ], ], 'RouteFerryTravelStep' => [ 'type' => 'structure', 'required' => [ 'Duration', 'Type', ], 'members' => [ 'Distance' => [ 'shape' => 'DistanceMeters', ], 'Duration' => [ 'shape' => 'DurationSeconds', ], 'GeometryOffset' => [ 'shape' => 'RouteFerryTravelStepGeometryOffsetInteger', ], 'Instruction' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'RouteFerryTravelStepType', ], ], ], 'RouteFerryTravelStepGeometryOffsetInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'RouteFerryTravelStepList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteFerryTravelStep', ], ], 'RouteFerryTravelStepType' => [ 'type' => 'string', 'enum' => [ 'Depart', 'Continue', 'Arrive', ], ], 'RouteHazardousCargoType' => [ 'type' => 'string', 'enum' => [ 'Combustible', 'Corrosive', 'Explosive', 'Flammable', 'Gas', 'HarmfulToWater', 'Organic', 'Other', 'Poison', 'PoisonousInhalation', 'Radioactive', ], ], 'RouteHazardousCargoTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteHazardousCargoType', ], 'max' => 11, 'min' => 0, ], 'RouteKeepStepDetails' => [ 'type' => 'structure', 'required' => [ 'Intersection', ], 'members' => [ 'Intersection' => [ 'shape' => 'LocalizedStringList', ], 'SteeringDirection' => [ 'shape' => 'RouteSteeringDirection', ], 'TurnAngle' => [ 'shape' => 'TurnAngle', ], 'TurnIntensity' => [ 'shape' => 'RouteTurnIntensity', ], ], ], 'RouteLeg' => [ 'type' => 'structure', 'required' => [ 'Geometry', 'TravelMode', 'Type', ], 'members' => [ 'FerryLegDetails' => [ 'shape' => 'RouteFerryLegDetails', ], 'Geometry' => [ 'shape' => 'RouteLegGeometry', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'PedestrianLegDetails' => [ 'shape' => 'RoutePedestrianLegDetails', ], 'TravelMode' => [ 'shape' => 'RouteLegTravelMode', ], 'Type' => [ 'shape' => 'RouteLegType', ], 'VehicleLegDetails' => [ 'shape' => 'RouteVehicleLegDetails', ], ], ], 'RouteLegAdditionalFeature' => [ 'type' => 'string', 'enum' => [ 'Elevation', 'Incidents', 'PassThroughWaypoints', 'Summary', 'Tolls', 'TravelStepInstructions', 'TruckRoadTypes', 'TypicalDuration', 'Zones', ], ], 'RouteLegAdditionalFeatureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteLegAdditionalFeature', ], 'max' => 9, 'min' => 0, ], 'RouteLegGeometry' => [ 'type' => 'structure', 'members' => [ 'LineString' => [ 'shape' => 'LineString', ], 'Polyline' => [ 'shape' => 'Polyline', ], ], ], 'RouteLegList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteLeg', ], ], 'RouteLegTravelMode' => [ 'type' => 'string', 'enum' => [ 'Car', 'Ferry', 'Pedestrian', 'Scooter', 'Truck', 'CarShuttleTrain', ], ], 'RouteLegType' => [ 'type' => 'string', 'enum' => [ 'Ferry', 'Pedestrian', 'Vehicle', ], ], 'RouteList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Route', ], ], 'RouteMajorRoadLabel' => [ 'type' => 'structure', 'members' => [ 'RoadName' => [ 'shape' => 'LocalizedString', ], 'RouteNumber' => [ 'shape' => 'RouteNumber', ], ], ], 'RouteMajorRoadLabelsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteMajorRoadLabel', ], 'max' => 2, 'min' => 0, ], 'RouteMatchingOptions' => [ 'type' => 'structure', 'members' => [ 'NameHint' => [ 'shape' => 'RouteMatchingOptionsNameHintString', ], 'OnRoadThreshold' => [ 'shape' => 'DistanceMeters', ], 'Radius' => [ 'shape' => 'DistanceMeters', ], 'Strategy' => [ 'shape' => 'MatchingStrategy', ], ], ], 'RouteMatchingOptionsNameHintString' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'sensitive' => true, ], 'RouteMatrix' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteMatrixRow', ], ], 'RouteMatrixAllowOptions' => [ 'type' => 'structure', 'members' => [ 'Hot' => [ 'shape' => 'Boolean', ], 'Hov' => [ 'shape' => 'Boolean', ], ], ], 'RouteMatrixAutoCircle' => [ 'type' => 'structure', 'members' => [ 'Margin' => [ 'shape' => 'RouteMatrixAutoCircleMarginLong', ], 'MaxRadius' => [ 'shape' => 'RouteMatrixAutoCircleMaxRadiusLong', ], ], ], 'RouteMatrixAutoCircleMarginLong' => [ 'type' => 'long', 'max' => 200000, 'min' => 0, ], 'RouteMatrixAutoCircleMaxRadiusLong' => [ 'type' => 'long', 'max' => 200000, 'min' => 0, ], 'RouteMatrixAvoidanceArea' => [ 'type' => 'structure', 'required' => [ 'Geometry', ], 'members' => [ 'Geometry' => [ 'shape' => 'RouteMatrixAvoidanceAreaGeometry', ], ], ], 'RouteMatrixAvoidanceAreaGeometry' => [ 'type' => 'structure', 'members' => [ 'BoundingBox' => [ 'shape' => 'BoundingBox', ], 'Polygon' => [ 'shape' => 'RouteMatrixAvoidanceAreaGeometryPolygonList', ], 'PolylinePolygon' => [ 'shape' => 'RouteMatrixAvoidanceAreaGeometryPolylinePolygonList', ], ], ], 'RouteMatrixAvoidanceAreaGeometryPolygonList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LinearRing', ], 'max' => 1, 'min' => 1, ], 'RouteMatrixAvoidanceAreaGeometryPolylinePolygonList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolylineRing', ], 'max' => 1, 'min' => 1, ], 'RouteMatrixAvoidanceOptions' => [ 'type' => 'structure', 'members' => [ 'Areas' => [ 'shape' => 'RouteMatrixAvoidanceOptionsAreasList', ], 'CarShuttleTrains' => [ 'shape' => 'Boolean', ], 'ControlledAccessHighways' => [ 'shape' => 'Boolean', ], 'DirtRoads' => [ 'shape' => 'Boolean', ], 'Ferries' => [ 'shape' => 'Boolean', ], 'TollRoads' => [ 'shape' => 'Boolean', ], 'TollTransponders' => [ 'shape' => 'Boolean', ], 'TruckRoadTypes' => [ 'shape' => 'TruckRoadTypeList', ], 'Tunnels' => [ 'shape' => 'Boolean', ], 'UTurns' => [ 'shape' => 'Boolean', ], 'ZoneCategories' => [ 'shape' => 'RouteMatrixAvoidanceZoneCategoryList', ], ], ], 'RouteMatrixAvoidanceOptionsAreasList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteMatrixAvoidanceArea', ], 'max' => 250, 'min' => 0, ], 'RouteMatrixAvoidanceZoneCategory' => [ 'type' => 'structure', 'members' => [ 'Category' => [ 'shape' => 'RouteMatrixZoneCategory', ], ], ], 'RouteMatrixAvoidanceZoneCategoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteMatrixAvoidanceZoneCategory', ], 'max' => 3, 'min' => 0, ], 'RouteMatrixBoundary' => [ 'type' => 'structure', 'members' => [ 'Geometry' => [ 'shape' => 'RouteMatrixBoundaryGeometry', ], 'Unbounded' => [ 'shape' => 'Boolean', ], ], ], 'RouteMatrixBoundaryGeometry' => [ 'type' => 'structure', 'members' => [ 'AutoCircle' => [ 'shape' => 'RouteMatrixAutoCircle', ], 'Circle' => [ 'shape' => 'Circle', ], 'BoundingBox' => [ 'shape' => 'BoundingBox', ], 'Polygon' => [ 'shape' => 'RouteMatrixBoundaryGeometryPolygonList', ], ], ], 'RouteMatrixBoundaryGeometryPolygonList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LinearRing', ], 'max' => 1, 'min' => 1, ], 'RouteMatrixCarOptions' => [ 'type' => 'structure', 'members' => [ 'LicensePlate' => [ 'shape' => 'RouteMatrixVehicleLicensePlate', ], 'MaxSpeed' => [ 'shape' => 'RouteMatrixCarOptionsMaxSpeedDouble', 'box' => true, ], 'Occupancy' => [ 'shape' => 'RouteMatrixCarOptionsOccupancyInteger', ], ], ], 'RouteMatrixCarOptionsMaxSpeedDouble' => [ 'type' => 'double', 'max' => 252.0, 'min' => 3.6, ], 'RouteMatrixCarOptionsOccupancyInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'RouteMatrixDestination' => [ 'type' => 'structure', 'required' => [ 'Position', ], 'members' => [ 'Options' => [ 'shape' => 'RouteMatrixDestinationOptions', ], 'Position' => [ 'shape' => 'Position', ], ], ], 'RouteMatrixDestinationOptions' => [ 'type' => 'structure', 'members' => [ 'AvoidActionsForDistance' => [ 'shape' => 'RouteMatrixDestinationOptionsAvoidActionsForDistanceLong', ], 'Heading' => [ 'shape' => 'Heading', ], 'Matching' => [ 'shape' => 'RouteMatrixMatchingOptions', ], 'SideOfStreet' => [ 'shape' => 'RouteMatrixSideOfStreetOptions', ], ], ], 'RouteMatrixDestinationOptionsAvoidActionsForDistanceLong' => [ 'type' => 'long', 'min' => 0, ], 'RouteMatrixEntry' => [ 'type' => 'structure', 'required' => [ 'Distance', 'Duration', ], 'members' => [ 'Distance' => [ 'shape' => 'DistanceMeters', ], 'Duration' => [ 'shape' => 'DurationSeconds', ], 'Error' => [ 'shape' => 'RouteMatrixErrorCode', ], ], ], 'RouteMatrixErrorCode' => [ 'type' => 'string', 'enum' => [ 'NoMatch', 'NoMatchDestination', 'NoMatchOrigin', 'NoRoute', 'OutOfBounds', 'OutOfBoundsDestination', 'OutOfBoundsOrigin', 'Other', 'Violation', ], ], 'RouteMatrixExclusionOptions' => [ 'type' => 'structure', 'required' => [ 'Countries', ], 'members' => [ 'Countries' => [ 'shape' => 'CountryCodeList', ], ], ], 'RouteMatrixHazardousCargoType' => [ 'type' => 'string', 'enum' => [ 'Combustible', 'Corrosive', 'Explosive', 'Flammable', 'Gas', 'HarmfulToWater', 'Organic', 'Other', 'Poison', 'PoisonousInhalation', 'Radioactive', ], ], 'RouteMatrixHazardousCargoTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteMatrixHazardousCargoType', ], 'max' => 11, 'min' => 0, ], 'RouteMatrixMatchingOptions' => [ 'type' => 'structure', 'members' => [ 'NameHint' => [ 'shape' => 'SensitiveString', ], 'OnRoadThreshold' => [ 'shape' => 'RouteMatrixMatchingOptionsOnRoadThresholdLong', ], 'Radius' => [ 'shape' => 'DistanceMeters', ], 'Strategy' => [ 'shape' => 'MatchingStrategy', ], ], ], 'RouteMatrixMatchingOptionsOnRoadThresholdLong' => [ 'type' => 'long', 'min' => 0, ], 'RouteMatrixOrigin' => [ 'type' => 'structure', 'required' => [ 'Position', ], 'members' => [ 'Options' => [ 'shape' => 'RouteMatrixOriginOptions', ], 'Position' => [ 'shape' => 'Position', ], ], ], 'RouteMatrixOriginOptions' => [ 'type' => 'structure', 'members' => [ 'AvoidActionsForDistance' => [ 'shape' => 'RouteMatrixOriginOptionsAvoidActionsForDistanceLong', ], 'Heading' => [ 'shape' => 'Heading', ], 'Matching' => [ 'shape' => 'RouteMatrixMatchingOptions', ], 'SideOfStreet' => [ 'shape' => 'RouteMatrixSideOfStreetOptions', ], ], ], 'RouteMatrixOriginOptionsAvoidActionsForDistanceLong' => [ 'type' => 'long', 'min' => 0, ], 'RouteMatrixRow' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteMatrixEntry', ], ], 'RouteMatrixScooterOptions' => [ 'type' => 'structure', 'members' => [ 'LicensePlate' => [ 'shape' => 'RouteMatrixVehicleLicensePlate', ], 'MaxSpeed' => [ 'shape' => 'RouteMatrixScooterOptionsMaxSpeedDouble', 'box' => true, ], 'Occupancy' => [ 'shape' => 'RouteMatrixScooterOptionsOccupancyInteger', ], ], ], 'RouteMatrixScooterOptionsMaxSpeedDouble' => [ 'type' => 'double', 'max' => 252.0, 'min' => 3.6, ], 'RouteMatrixScooterOptionsOccupancyInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'RouteMatrixSideOfStreetOptions' => [ 'type' => 'structure', 'required' => [ 'Position', ], 'members' => [ 'Position' => [ 'shape' => 'Position', ], 'UseWith' => [ 'shape' => 'SideOfStreetMatchingStrategy', ], ], ], 'RouteMatrixTrafficOptions' => [ 'type' => 'structure', 'members' => [ 'FlowEventThresholdOverride' => [ 'shape' => 'DurationSeconds', ], 'Usage' => [ 'shape' => 'TrafficUsage', ], ], ], 'RouteMatrixTrailerOptions' => [ 'type' => 'structure', 'members' => [ 'TrailerCount' => [ 'shape' => 'RouteMatrixTrailerOptionsTrailerCountInteger', ], ], ], 'RouteMatrixTrailerOptionsTrailerCountInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 255, 'min' => 0, ], 'RouteMatrixTravelMode' => [ 'type' => 'string', 'enum' => [ 'Car', 'Pedestrian', 'Scooter', 'Truck', ], ], 'RouteMatrixTravelModeOptions' => [ 'type' => 'structure', 'members' => [ 'Car' => [ 'shape' => 'RouteMatrixCarOptions', ], 'Scooter' => [ 'shape' => 'RouteMatrixScooterOptions', ], 'Truck' => [ 'shape' => 'RouteMatrixTruckOptions', ], ], ], 'RouteMatrixTruckOptions' => [ 'type' => 'structure', 'members' => [ 'AxleCount' => [ 'shape' => 'RouteMatrixTruckOptionsAxleCountInteger', ], 'GrossWeight' => [ 'shape' => 'WeightKilograms', ], 'HazardousCargos' => [ 'shape' => 'RouteMatrixHazardousCargoTypeList', ], 'Height' => [ 'shape' => 'RouteMatrixTruckOptionsHeightLong', ], 'KpraLength' => [ 'shape' => 'DimensionCentimeters', ], 'Length' => [ 'shape' => 'RouteMatrixTruckOptionsLengthLong', ], 'LicensePlate' => [ 'shape' => 'RouteMatrixVehicleLicensePlate', ], 'MaxSpeed' => [ 'shape' => 'RouteMatrixTruckOptionsMaxSpeedDouble', 'box' => true, ], 'Occupancy' => [ 'shape' => 'RouteMatrixTruckOptionsOccupancyInteger', ], 'PayloadCapacity' => [ 'shape' => 'WeightKilograms', ], 'Trailer' => [ 'shape' => 'RouteMatrixTrailerOptions', ], 'TruckType' => [ 'shape' => 'RouteMatrixTruckType', ], 'TunnelRestrictionCode' => [ 'shape' => 'TunnelRestrictionCode', ], 'WeightPerAxle' => [ 'shape' => 'WeightKilograms', ], 'WeightPerAxleGroup' => [ 'shape' => 'WeightPerAxleGroup', ], 'Width' => [ 'shape' => 'RouteMatrixTruckOptionsWidthLong', ], ], ], 'RouteMatrixTruckOptionsAxleCountInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 255, 'min' => 2, ], 'RouteMatrixTruckOptionsHeightLong' => [ 'type' => 'long', 'max' => 5000, 'min' => 0, ], 'RouteMatrixTruckOptionsLengthLong' => [ 'type' => 'long', 'max' => 30000, 'min' => 0, ], 'RouteMatrixTruckOptionsMaxSpeedDouble' => [ 'type' => 'double', 'max' => 252.0, 'min' => 3.6, ], 'RouteMatrixTruckOptionsOccupancyInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'RouteMatrixTruckOptionsWidthLong' => [ 'type' => 'long', 'max' => 5000, 'min' => 0, ], 'RouteMatrixTruckType' => [ 'type' => 'string', 'enum' => [ 'LightTruck', 'StraightTruck', 'Tractor', ], ], 'RouteMatrixVehicleLicensePlate' => [ 'type' => 'structure', 'members' => [ 'LastCharacter' => [ 'shape' => 'RouteMatrixVehicleLicensePlateLastCharacterString', ], ], ], 'RouteMatrixVehicleLicensePlateLastCharacterString' => [ 'type' => 'string', 'max' => 1, 'min' => 1, ], 'RouteMatrixZoneCategory' => [ 'type' => 'string', 'enum' => [ 'CongestionPricing', 'Environmental', 'Vignette', ], ], 'RouteNoticeDetailRange' => [ 'type' => 'structure', 'members' => [ 'Min' => [ 'shape' => 'RouteNoticeDetailRangeMinInteger', ], 'Max' => [ 'shape' => 'RouteNoticeDetailRangeMaxInteger', ], ], ], 'RouteNoticeDetailRangeMaxInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'RouteNoticeDetailRangeMinInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'RouteNoticeImpact' => [ 'type' => 'string', 'enum' => [ 'High', 'Low', ], ], 'RouteNumber' => [ 'type' => 'structure', 'required' => [ 'Value', ], 'members' => [ 'Direction' => [ 'shape' => 'RouteDirection', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'Value' => [ 'shape' => 'String', ], ], ], 'RouteNumberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteNumber', ], ], 'RouteOriginOptions' => [ 'type' => 'structure', 'members' => [ 'AvoidActionsForDistance' => [ 'shape' => 'RouteOriginOptionsAvoidActionsForDistanceLong', ], 'AvoidUTurns' => [ 'shape' => 'Boolean', ], 'Heading' => [ 'shape' => 'Heading', ], 'Matching' => [ 'shape' => 'RouteMatchingOptions', ], 'SideOfStreet' => [ 'shape' => 'RouteSideOfStreetOptions', ], ], ], 'RouteOriginOptionsAvoidActionsForDistanceLong' => [ 'type' => 'long', 'max' => 2000, ], 'RoutePassThroughPlace' => [ 'type' => 'structure', 'required' => [ 'Position', ], 'members' => [ 'OriginalPosition' => [ 'shape' => 'Position23', ], 'Position' => [ 'shape' => 'Position23', ], 'WaypointIndex' => [ 'shape' => 'RoutePassThroughPlaceWaypointIndexInteger', ], ], ], 'RoutePassThroughPlaceWaypointIndexInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'RoutePassThroughWaypoint' => [ 'type' => 'structure', 'required' => [ 'Place', ], 'members' => [ 'GeometryOffset' => [ 'shape' => 'RoutePassThroughWaypointGeometryOffsetInteger', ], 'Place' => [ 'shape' => 'RoutePassThroughPlace', ], ], ], 'RoutePassThroughWaypointGeometryOffsetInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'RoutePassThroughWaypointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoutePassThroughWaypoint', ], ], 'RoutePedestrianArrival' => [ 'type' => 'structure', 'required' => [ 'Place', ], 'members' => [ 'Place' => [ 'shape' => 'RoutePedestrianPlace', ], 'Time' => [ 'shape' => 'TimestampWithTimezoneOffset', ], ], ], 'RoutePedestrianDeparture' => [ 'type' => 'structure', 'required' => [ 'Place', ], 'members' => [ 'Place' => [ 'shape' => 'RoutePedestrianPlace', ], 'Time' => [ 'shape' => 'TimestampWithTimezoneOffset', ], ], ], 'RoutePedestrianLegDetails' => [ 'type' => 'structure', 'required' => [ 'Arrival', 'Departure', 'Notices', 'PassThroughWaypoints', 'Spans', 'TravelSteps', ], 'members' => [ 'Arrival' => [ 'shape' => 'RoutePedestrianArrival', ], 'Departure' => [ 'shape' => 'RoutePedestrianDeparture', ], 'Notices' => [ 'shape' => 'RoutePedestrianNoticeList', ], 'PassThroughWaypoints' => [ 'shape' => 'RoutePassThroughWaypointList', ], 'Spans' => [ 'shape' => 'RoutePedestrianSpanList', ], 'Summary' => [ 'shape' => 'RoutePedestrianSummary', ], 'TravelSteps' => [ 'shape' => 'RoutePedestrianTravelStepList', ], ], ], 'RoutePedestrianNotice' => [ 'type' => 'structure', 'required' => [ 'Code', ], 'members' => [ 'Code' => [ 'shape' => 'RoutePedestrianNoticeCode', ], 'Impact' => [ 'shape' => 'RouteNoticeImpact', ], ], ], 'RoutePedestrianNoticeCode' => [ 'type' => 'string', 'enum' => [ 'AccuratePolylineUnavailable', 'Other', 'ViolatedAvoidDirtRoad', 'ViolatedAvoidTunnel', 'ViolatedPedestrianOption', ], ], 'RoutePedestrianNoticeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoutePedestrianNotice', ], ], 'RoutePedestrianOptions' => [ 'type' => 'structure', 'members' => [ 'Speed' => [ 'shape' => 'RoutePedestrianOptionsSpeedDouble', 'box' => true, ], ], ], 'RoutePedestrianOptionsSpeedDouble' => [ 'type' => 'double', 'max' => 7.2, 'min' => 1.8, ], 'RoutePedestrianOverviewSummary' => [ 'type' => 'structure', 'required' => [ 'Distance', 'Duration', ], 'members' => [ 'Distance' => [ 'shape' => 'DistanceMeters', ], 'Duration' => [ 'shape' => 'DurationSeconds', ], ], ], 'RoutePedestrianPlace' => [ 'type' => 'structure', 'required' => [ 'Position', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'OriginalPosition' => [ 'shape' => 'Position23', ], 'Position' => [ 'shape' => 'Position23', ], 'SideOfStreet' => [ 'shape' => 'RouteSideOfStreet', ], 'WaypointIndex' => [ 'shape' => 'RoutePedestrianPlaceWaypointIndexInteger', ], ], ], 'RoutePedestrianPlaceWaypointIndexInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'RoutePedestrianSpan' => [ 'type' => 'structure', 'members' => [ 'BestCaseDuration' => [ 'shape' => 'DurationSeconds', ], 'Country' => [ 'shape' => 'CountryCode3', ], 'Distance' => [ 'shape' => 'DistanceMeters', ], 'Duration' => [ 'shape' => 'DurationSeconds', ], 'DynamicSpeed' => [ 'shape' => 'RouteSpanDynamicSpeedDetails', ], 'FunctionalClassification' => [ 'shape' => 'RoutePedestrianSpanFunctionalClassificationInteger', ], 'GeometryOffset' => [ 'shape' => 'RoutePedestrianSpanGeometryOffsetInteger', ], 'Incidents' => [ 'shape' => 'IndexList', ], 'Names' => [ 'shape' => 'LocalizedStringList', ], 'PedestrianAccess' => [ 'shape' => 'RouteSpanPedestrianAccessAttributeList', ], 'Region' => [ 'shape' => 'RoutePedestrianSpanRegionString', ], 'RoadAttributes' => [ 'shape' => 'RouteSpanRoadAttributeList', ], 'RouteNumbers' => [ 'shape' => 'RouteNumberList', ], 'SpeedLimit' => [ 'shape' => 'RouteSpanSpeedLimitDetails', ], 'TypicalDuration' => [ 'shape' => 'DurationSeconds', ], ], ], 'RoutePedestrianSpanFunctionalClassificationInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 5, 'min' => 1, ], 'RoutePedestrianSpanGeometryOffsetInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'RoutePedestrianSpanList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoutePedestrianSpan', ], ], 'RoutePedestrianSpanRegionString' => [ 'type' => 'string', 'max' => 3, 'min' => 0, ], 'RoutePedestrianSummary' => [ 'type' => 'structure', 'members' => [ 'Overview' => [ 'shape' => 'RoutePedestrianOverviewSummary', ], 'TravelOnly' => [ 'shape' => 'RoutePedestrianTravelOnlySummary', ], ], ], 'RoutePedestrianTravelOnlySummary' => [ 'type' => 'structure', 'required' => [ 'Duration', ], 'members' => [ 'Duration' => [ 'shape' => 'DurationSeconds', ], ], ], 'RoutePedestrianTravelStep' => [ 'type' => 'structure', 'required' => [ 'Duration', 'Type', ], 'members' => [ 'ContinueStepDetails' => [ 'shape' => 'RouteContinueStepDetails', ], 'CurrentRoad' => [ 'shape' => 'RouteRoad', ], 'Distance' => [ 'shape' => 'DistanceMeters', ], 'Duration' => [ 'shape' => 'DurationSeconds', ], 'ExitNumber' => [ 'shape' => 'LocalizedStringList', ], 'GeometryOffset' => [ 'shape' => 'RoutePedestrianTravelStepGeometryOffsetInteger', ], 'Instruction' => [ 'shape' => 'String', ], 'KeepStepDetails' => [ 'shape' => 'RouteKeepStepDetails', ], 'NextRoad' => [ 'shape' => 'RouteRoad', ], 'RoundaboutEnterStepDetails' => [ 'shape' => 'RouteRoundaboutEnterStepDetails', ], 'RoundaboutExitStepDetails' => [ 'shape' => 'RouteRoundaboutExitStepDetails', ], 'RoundaboutPassStepDetails' => [ 'shape' => 'RouteRoundaboutPassStepDetails', ], 'Signpost' => [ 'shape' => 'RouteSignpost', ], 'TurnStepDetails' => [ 'shape' => 'RouteTurnStepDetails', ], 'Type' => [ 'shape' => 'RoutePedestrianTravelStepType', ], ], ], 'RoutePedestrianTravelStepGeometryOffsetInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'RoutePedestrianTravelStepList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoutePedestrianTravelStep', ], ], 'RoutePedestrianTravelStepType' => [ 'type' => 'string', 'enum' => [ 'Arrive', 'Continue', 'Depart', 'Keep', 'RoundaboutEnter', 'RoundaboutExit', 'RoundaboutPass', 'Turn', 'Exit', 'Ramp', 'UTurn', ], ], 'RouteRampStepDetails' => [ 'type' => 'structure', 'required' => [ 'Intersection', ], 'members' => [ 'Intersection' => [ 'shape' => 'LocalizedStringList', ], 'SteeringDirection' => [ 'shape' => 'RouteSteeringDirection', ], 'TurnAngle' => [ 'shape' => 'TurnAngle', ], 'TurnIntensity' => [ 'shape' => 'RouteTurnIntensity', ], ], ], 'RouteResponseNotice' => [ 'type' => 'structure', 'required' => [ 'Code', ], 'members' => [ 'Code' => [ 'shape' => 'RouteResponseNoticeCode', ], 'Impact' => [ 'shape' => 'RouteNoticeImpact', ], ], ], 'RouteResponseNoticeCode' => [ 'type' => 'string', 'enum' => [ 'MainLanguageNotFound', 'Other', 'TravelTimeExceedsDriverWorkHours', ], ], 'RouteResponseNoticeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteResponseNotice', ], ], 'RouteRoad' => [ 'type' => 'structure', 'required' => [ 'RoadName', 'RouteNumber', 'Towards', ], 'members' => [ 'RoadName' => [ 'shape' => 'LocalizedStringList', ], 'RouteNumber' => [ 'shape' => 'RouteNumberList', ], 'Towards' => [ 'shape' => 'LocalizedStringList', ], 'Type' => [ 'shape' => 'RouteRoadType', ], ], ], 'RouteRoadType' => [ 'type' => 'string', 'enum' => [ 'Highway', 'Rural', 'Urban', ], ], 'RouteRoundaboutEnterStepDetails' => [ 'type' => 'structure', 'required' => [ 'Intersection', ], 'members' => [ 'Intersection' => [ 'shape' => 'LocalizedStringList', ], 'SteeringDirection' => [ 'shape' => 'RouteSteeringDirection', ], 'TurnAngle' => [ 'shape' => 'TurnAngle', ], 'TurnIntensity' => [ 'shape' => 'RouteTurnIntensity', ], ], ], 'RouteRoundaboutExitStepDetails' => [ 'type' => 'structure', 'required' => [ 'Intersection', ], 'members' => [ 'Intersection' => [ 'shape' => 'LocalizedStringList', ], 'RelativeExit' => [ 'shape' => 'RouteRoundaboutExitStepDetailsRelativeExitInteger', ], 'RoundaboutAngle' => [ 'shape' => 'RoundaboutAngle', ], 'SteeringDirection' => [ 'shape' => 'RouteSteeringDirection', ], ], ], 'RouteRoundaboutExitStepDetailsRelativeExitInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 12, 'min' => 1, ], 'RouteRoundaboutPassStepDetails' => [ 'type' => 'structure', 'required' => [ 'Intersection', ], 'members' => [ 'Intersection' => [ 'shape' => 'LocalizedStringList', ], 'SteeringDirection' => [ 'shape' => 'RouteSteeringDirection', ], 'TurnAngle' => [ 'shape' => 'TurnAngle', ], 'TurnIntensity' => [ 'shape' => 'RouteTurnIntensity', ], ], ], 'RouteScooterOptions' => [ 'type' => 'structure', 'members' => [ 'EngineType' => [ 'shape' => 'RouteEngineType', ], 'LicensePlate' => [ 'shape' => 'RouteVehicleLicensePlate', ], 'MaxSpeed' => [ 'shape' => 'RouteScooterOptionsMaxSpeedDouble', 'box' => true, ], 'Occupancy' => [ 'shape' => 'RouteScooterOptionsOccupancyInteger', ], ], ], 'RouteScooterOptionsMaxSpeedDouble' => [ 'type' => 'double', 'max' => 252.0, 'min' => 3.6, ], 'RouteScooterOptionsOccupancyInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'RouteSideOfStreet' => [ 'type' => 'string', 'enum' => [ 'Left', 'Right', ], ], 'RouteSideOfStreetOptions' => [ 'type' => 'structure', 'required' => [ 'Position', ], 'members' => [ 'Position' => [ 'shape' => 'Position', ], 'UseWith' => [ 'shape' => 'SideOfStreetMatchingStrategy', ], ], ], 'RouteSignpost' => [ 'type' => 'structure', 'required' => [ 'Labels', ], 'members' => [ 'Labels' => [ 'shape' => 'RouteSignpostLabelList', ], ], ], 'RouteSignpostLabel' => [ 'type' => 'structure', 'members' => [ 'RouteNumber' => [ 'shape' => 'RouteNumber', ], 'Text' => [ 'shape' => 'LocalizedString', ], ], ], 'RouteSignpostLabelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteSignpostLabel', ], ], 'RouteSpanAdditionalFeature' => [ 'type' => 'string', 'enum' => [ 'BestCaseDuration', 'CarAccess', 'Country', 'Distance', 'Duration', 'DynamicSpeed', 'FunctionalClassification', 'Gates', 'Incidents', 'Names', 'Notices', 'PedestrianAccess', 'RailwayCrossings', 'Region', 'RoadAttributes', 'RouteNumbers', 'ScooterAccess', 'SpeedLimit', 'TollSystems', 'TruckAccess', 'TruckRoadTypes', 'TypicalDuration', 'Zones', 'Consumption', ], ], 'RouteSpanAdditionalFeatureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteSpanAdditionalFeature', ], 'max' => 24, 'min' => 0, ], 'RouteSpanCarAccessAttribute' => [ 'type' => 'string', 'enum' => [ 'Allowed', 'NoThroughTraffic', 'TollRoad', ], ], 'RouteSpanCarAccessAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteSpanCarAccessAttribute', ], 'max' => 3, 'min' => 0, ], 'RouteSpanDynamicSpeedDetails' => [ 'type' => 'structure', 'members' => [ 'BestCaseSpeed' => [ 'shape' => 'SpeedKilometersPerHour', ], 'TurnDuration' => [ 'shape' => 'DurationSeconds', ], 'TypicalSpeed' => [ 'shape' => 'SpeedKilometersPerHour', ], ], ], 'RouteSpanGateAttribute' => [ 'type' => 'string', 'enum' => [ 'Emergency', 'KeyAccess', 'PermissionRequired', ], ], 'RouteSpanPedestrianAccessAttribute' => [ 'type' => 'string', 'enum' => [ 'Allowed', 'Indoors', 'NoThroughTraffic', 'Park', 'Stairs', 'TollRoad', ], ], 'RouteSpanPedestrianAccessAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteSpanPedestrianAccessAttribute', ], 'max' => 6, 'min' => 0, ], 'RouteSpanRailwayCrossingAttribute' => [ 'type' => 'string', 'enum' => [ 'Protected', 'Unprotected', ], ], 'RouteSpanRoadAttribute' => [ 'type' => 'string', 'enum' => [ 'Bridge', 'BuiltUpArea', 'ControlledAccessHighway', 'DirtRoad', 'DividedRoad', 'Motorway', 'PrivateRoad', 'Ramp', 'RightHandTraffic', 'Roundabout', 'Tunnel', 'UnderConstruction', ], ], 'RouteSpanRoadAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteSpanRoadAttribute', ], 'max' => 12, 'min' => 0, ], 'RouteSpanScooterAccessAttribute' => [ 'type' => 'string', 'enum' => [ 'Allowed', 'NoThroughTraffic', 'TollRoad', ], ], 'RouteSpanScooterAccessAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteSpanScooterAccessAttribute', ], 'max' => 3, 'min' => 0, ], 'RouteSpanSpeedLimitDetails' => [ 'type' => 'structure', 'members' => [ 'MaxSpeed' => [ 'shape' => 'SpeedKilometersPerHour', ], 'Unlimited' => [ 'shape' => 'Boolean', ], ], ], 'RouteSpanTruckAccessAttribute' => [ 'type' => 'string', 'enum' => [ 'Allowed', 'NoThroughTraffic', 'TollRoad', ], ], 'RouteSpanTruckAccessAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteSpanTruckAccessAttribute', ], 'max' => 3, 'min' => 0, ], 'RouteSteeringDirection' => [ 'type' => 'string', 'enum' => [ 'Left', 'Right', 'Straight', ], ], 'RouteSummary' => [ 'type' => 'structure', 'members' => [ 'Distance' => [ 'shape' => 'DistanceMeters', ], 'Duration' => [ 'shape' => 'DurationSeconds', ], 'Tolls' => [ 'shape' => 'RouteTollSummary', ], ], ], 'RouteToll' => [ 'type' => 'structure', 'required' => [ 'PaymentSites', 'Rates', 'Systems', ], 'members' => [ 'Country' => [ 'shape' => 'CountryCode3', ], 'PaymentSites' => [ 'shape' => 'RouteTollPaymentSiteList', ], 'Rates' => [ 'shape' => 'RouteTollRateList', ], 'Systems' => [ 'shape' => 'IndexList', ], ], ], 'RouteTollList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteToll', ], ], 'RouteTollOptions' => [ 'type' => 'structure', 'members' => [ 'AllTransponders' => [ 'shape' => 'Boolean', ], 'AllVignettes' => [ 'shape' => 'Boolean', ], 'Currency' => [ 'shape' => 'CurrencyCode', ], 'EmissionType' => [ 'shape' => 'RouteEmissionType', ], 'VehicleCategory' => [ 'shape' => 'RouteTollVehicleCategory', ], ], ], 'RouteTollPass' => [ 'type' => 'structure', 'members' => [ 'IncludesReturnTrip' => [ 'shape' => 'Boolean', ], 'SeniorPass' => [ 'shape' => 'Boolean', ], 'TransferCount' => [ 'shape' => 'RouteTollPassTransferCountInteger', ], 'TripCount' => [ 'shape' => 'RouteTollPassTripCountInteger', ], 'ValidityPeriod' => [ 'shape' => 'RouteTollPassValidityPeriod', ], ], ], 'RouteTollPassTransferCountInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'RouteTollPassTripCountInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'RouteTollPassValidityPeriod' => [ 'type' => 'structure', 'required' => [ 'Period', ], 'members' => [ 'Period' => [ 'shape' => 'RouteTollPassValidityPeriodType', ], 'PeriodCount' => [ 'shape' => 'RouteTollPassValidityPeriodPeriodCountInteger', ], ], ], 'RouteTollPassValidityPeriodPeriodCountInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'RouteTollPassValidityPeriodType' => [ 'type' => 'string', 'enum' => [ 'Annual', 'Days', 'ExtendedAnnual', 'Minutes', 'Months', ], ], 'RouteTollPaymentMethod' => [ 'type' => 'string', 'enum' => [ 'BankCard', 'Cash', 'CashExact', 'CreditCard', 'PassSubscription', 'TravelCard', 'Transponder', 'VideoToll', ], ], 'RouteTollPaymentMethodList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteTollPaymentMethod', ], 'max' => 8, 'min' => 0, ], 'RouteTollPaymentSite' => [ 'type' => 'structure', 'required' => [ 'Position', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Position' => [ 'shape' => 'Position23', ], ], ], 'RouteTollPaymentSiteList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteTollPaymentSite', ], ], 'RouteTollPrice' => [ 'type' => 'structure', 'required' => [ 'Currency', 'Estimate', 'Range', 'Value', ], 'members' => [ 'Currency' => [ 'shape' => 'CurrencyCode', ], 'Estimate' => [ 'shape' => 'Boolean', ], 'PerDuration' => [ 'shape' => 'DurationSeconds', ], 'Range' => [ 'shape' => 'Boolean', ], 'RangeValue' => [ 'shape' => 'RouteTollPriceValueRange', ], 'Value' => [ 'shape' => 'RouteTollPriceValueDouble', ], ], ], 'RouteTollPriceSummary' => [ 'type' => 'structure', 'required' => [ 'Currency', 'Estimate', 'Range', 'Value', ], 'members' => [ 'Currency' => [ 'shape' => 'CurrencyCode', ], 'Estimate' => [ 'shape' => 'Boolean', ], 'Range' => [ 'shape' => 'Boolean', ], 'RangeValue' => [ 'shape' => 'RouteTollPriceValueRange', ], 'Value' => [ 'shape' => 'RouteTollPriceSummaryValueDouble', ], ], ], 'RouteTollPriceSummaryValueDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0.0, ], 'RouteTollPriceValueDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0.0, ], 'RouteTollPriceValueRange' => [ 'type' => 'structure', 'required' => [ 'Min', 'Max', ], 'members' => [ 'Min' => [ 'shape' => 'RouteTollPriceValueRangeMinDouble', ], 'Max' => [ 'shape' => 'RouteTollPriceValueRangeMaxDouble', ], ], ], 'RouteTollPriceValueRangeMaxDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0.0, ], 'RouteTollPriceValueRangeMinDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0.0, ], 'RouteTollRate' => [ 'type' => 'structure', 'required' => [ 'Id', 'LocalPrice', 'Name', 'PaymentMethods', 'Transponders', ], 'members' => [ 'ApplicableTimes' => [ 'shape' => 'String', ], 'ConvertedPrice' => [ 'shape' => 'RouteTollPrice', ], 'Id' => [ 'shape' => 'String', ], 'LocalPrice' => [ 'shape' => 'RouteTollPrice', ], 'Name' => [ 'shape' => 'String', ], 'Pass' => [ 'shape' => 'RouteTollPass', ], 'PaymentMethods' => [ 'shape' => 'RouteTollPaymentMethodList', ], 'Transponders' => [ 'shape' => 'RouteTransponderList', ], ], ], 'RouteTollRateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteTollRate', ], ], 'RouteTollSummary' => [ 'type' => 'structure', 'members' => [ 'Total' => [ 'shape' => 'RouteTollPriceSummary', ], ], ], 'RouteTollSystem' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], ], ], 'RouteTollSystemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteTollSystem', ], ], 'RouteTollVehicleCategory' => [ 'type' => 'string', 'enum' => [ 'Minibus', ], ], 'RouteTrafficOptions' => [ 'type' => 'structure', 'members' => [ 'FlowEventThresholdOverride' => [ 'shape' => 'DurationSeconds', ], 'Usage' => [ 'shape' => 'TrafficUsage', ], ], ], 'RouteTrailerOptions' => [ 'type' => 'structure', 'members' => [ 'AxleCount' => [ 'shape' => 'RouteTrailerOptionsAxleCountInteger', ], 'TrailerCount' => [ 'shape' => 'RouteTrailerOptionsTrailerCountInteger', ], ], ], 'RouteTrailerOptionsAxleCountInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'RouteTrailerOptionsTrailerCountInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 255, 'min' => 1, ], 'RouteTransponder' => [ 'type' => 'structure', 'members' => [ 'SystemName' => [ 'shape' => 'String', ], ], ], 'RouteTransponderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteTransponder', ], ], 'RouteTravelMode' => [ 'type' => 'string', 'enum' => [ 'Car', 'Pedestrian', 'Scooter', 'Truck', ], ], 'RouteTravelModeOptions' => [ 'type' => 'structure', 'members' => [ 'Car' => [ 'shape' => 'RouteCarOptions', ], 'Pedestrian' => [ 'shape' => 'RoutePedestrianOptions', ], 'Scooter' => [ 'shape' => 'RouteScooterOptions', ], 'Truck' => [ 'shape' => 'RouteTruckOptions', ], ], ], 'RouteTravelStepType' => [ 'type' => 'string', 'enum' => [ 'Default', 'TurnByTurn', ], ], 'RouteTruckOptions' => [ 'type' => 'structure', 'members' => [ 'AxleCount' => [ 'shape' => 'RouteTruckOptionsAxleCountInteger', ], 'EngineType' => [ 'shape' => 'RouteEngineType', ], 'GrossWeight' => [ 'shape' => 'WeightKilograms', ], 'HazardousCargos' => [ 'shape' => 'RouteHazardousCargoTypeList', ], 'Height' => [ 'shape' => 'RouteTruckOptionsHeightLong', ], 'HeightAboveFirstAxle' => [ 'shape' => 'RouteTruckOptionsHeightAboveFirstAxleLong', ], 'KpraLength' => [ 'shape' => 'DimensionCentimeters', ], 'Length' => [ 'shape' => 'RouteTruckOptionsLengthLong', ], 'LicensePlate' => [ 'shape' => 'RouteVehicleLicensePlate', ], 'MaxSpeed' => [ 'shape' => 'RouteTruckOptionsMaxSpeedDouble', 'box' => true, ], 'Occupancy' => [ 'shape' => 'RouteTruckOptionsOccupancyInteger', ], 'PayloadCapacity' => [ 'shape' => 'WeightKilograms', ], 'TireCount' => [ 'shape' => 'RouteTruckOptionsTireCountInteger', ], 'Trailer' => [ 'shape' => 'RouteTrailerOptions', ], 'TruckType' => [ 'shape' => 'RouteTruckType', ], 'TunnelRestrictionCode' => [ 'shape' => 'RouteTruckOptionsTunnelRestrictionCodeString', ], 'WeightPerAxle' => [ 'shape' => 'WeightKilograms', ], 'WeightPerAxleGroup' => [ 'shape' => 'WeightPerAxleGroup', ], 'Width' => [ 'shape' => 'RouteTruckOptionsWidthLong', ], ], ], 'RouteTruckOptionsAxleCountInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 255, 'min' => 2, ], 'RouteTruckOptionsHeightAboveFirstAxleLong' => [ 'type' => 'long', 'max' => 5000, 'min' => 0, ], 'RouteTruckOptionsHeightLong' => [ 'type' => 'long', 'max' => 5000, 'min' => 0, ], 'RouteTruckOptionsLengthLong' => [ 'type' => 'long', 'max' => 30000, 'min' => 0, ], 'RouteTruckOptionsMaxSpeedDouble' => [ 'type' => 'double', 'max' => 252.0, 'min' => 3.6, ], 'RouteTruckOptionsOccupancyInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'RouteTruckOptionsTireCountInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 255, 'min' => 1, ], 'RouteTruckOptionsTunnelRestrictionCodeString' => [ 'type' => 'string', 'max' => 20, 'min' => 0, ], 'RouteTruckOptionsWidthLong' => [ 'type' => 'long', 'max' => 5000, 'min' => 0, ], 'RouteTruckType' => [ 'type' => 'string', 'enum' => [ 'LightTruck', 'StraightTruck', 'Tractor', ], ], 'RouteTurnIntensity' => [ 'type' => 'string', 'enum' => [ 'Sharp', 'Slight', 'Typical', ], ], 'RouteTurnStepDetails' => [ 'type' => 'structure', 'required' => [ 'Intersection', ], 'members' => [ 'Intersection' => [ 'shape' => 'LocalizedStringList', ], 'SteeringDirection' => [ 'shape' => 'RouteSteeringDirection', ], 'TurnAngle' => [ 'shape' => 'TurnAngle', ], 'TurnIntensity' => [ 'shape' => 'RouteTurnIntensity', ], ], ], 'RouteUTurnStepDetails' => [ 'type' => 'structure', 'required' => [ 'Intersection', ], 'members' => [ 'Intersection' => [ 'shape' => 'LocalizedStringList', ], 'SteeringDirection' => [ 'shape' => 'RouteSteeringDirection', ], 'TurnAngle' => [ 'shape' => 'TurnAngle', ], 'TurnIntensity' => [ 'shape' => 'RouteTurnIntensity', ], ], ], 'RouteVehicleArrival' => [ 'type' => 'structure', 'required' => [ 'Place', ], 'members' => [ 'Place' => [ 'shape' => 'RouteVehiclePlace', ], 'Time' => [ 'shape' => 'TimestampWithTimezoneOffset', ], ], ], 'RouteVehicleDeparture' => [ 'type' => 'structure', 'required' => [ 'Place', ], 'members' => [ 'Place' => [ 'shape' => 'RouteVehiclePlace', ], 'Time' => [ 'shape' => 'TimestampWithTimezoneOffset', ], ], ], 'RouteVehicleIncident' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'String', ], 'EndTime' => [ 'shape' => 'TimestampWithTimezoneOffset', ], 'Severity' => [ 'shape' => 'RouteVehicleIncidentSeverity', ], 'StartTime' => [ 'shape' => 'TimestampWithTimezoneOffset', ], 'Type' => [ 'shape' => 'RouteVehicleIncidentType', ], ], ], 'RouteVehicleIncidentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteVehicleIncident', ], ], 'RouteVehicleIncidentSeverity' => [ 'type' => 'string', 'enum' => [ 'Critical', 'High', 'Medium', 'Low', ], ], 'RouteVehicleIncidentType' => [ 'type' => 'string', 'enum' => [ 'Accident', 'Congestion', 'Construction', 'DisabledVehicle', 'LaneRestriction', 'MassTransit', 'Other', 'PlannedEvent', 'RoadClosure', 'RoadHazard', 'Weather', ], ], 'RouteVehicleLegDetails' => [ 'type' => 'structure', 'required' => [ 'Arrival', 'Departure', 'Incidents', 'Notices', 'PassThroughWaypoints', 'Spans', 'Tolls', 'TollSystems', 'TravelSteps', 'TruckRoadTypes', 'Zones', ], 'members' => [ 'Arrival' => [ 'shape' => 'RouteVehicleArrival', ], 'Departure' => [ 'shape' => 'RouteVehicleDeparture', ], 'Incidents' => [ 'shape' => 'RouteVehicleIncidentList', ], 'Notices' => [ 'shape' => 'RouteVehicleNoticeList', ], 'PassThroughWaypoints' => [ 'shape' => 'RoutePassThroughWaypointList', ], 'Spans' => [ 'shape' => 'RouteVehicleSpanList', ], 'Summary' => [ 'shape' => 'RouteVehicleSummary', ], 'Tolls' => [ 'shape' => 'RouteTollList', ], 'TollSystems' => [ 'shape' => 'RouteTollSystemList', ], 'TravelSteps' => [ 'shape' => 'RouteVehicleTravelStepList', ], 'TruckRoadTypes' => [ 'shape' => 'TruckRoadTypeList', ], 'Zones' => [ 'shape' => 'RouteZoneList', ], ], ], 'RouteVehicleLicensePlate' => [ 'type' => 'structure', 'members' => [ 'LastCharacter' => [ 'shape' => 'RouteVehicleLicensePlateLastCharacterString', ], ], ], 'RouteVehicleLicensePlateLastCharacterString' => [ 'type' => 'string', 'max' => 1, 'min' => 1, ], 'RouteVehicleNotice' => [ 'type' => 'structure', 'required' => [ 'Code', 'Details', ], 'members' => [ 'Code' => [ 'shape' => 'RouteVehicleNoticeCode', ], 'Details' => [ 'shape' => 'RouteVehicleNoticeDetailList', ], 'Impact' => [ 'shape' => 'RouteNoticeImpact', ], ], ], 'RouteVehicleNoticeCode' => [ 'type' => 'string', 'enum' => [ 'AccuratePolylineUnavailable', 'Other', 'PotentialViolatedAvoidTollRoadUsage', 'PotentialViolatedCarpoolUsage', 'PotentialViolatedTurnRestrictionUsage', 'PotentialViolatedVehicleRestrictionUsage', 'PotentialViolatedZoneRestrictionUsage', 'SeasonalClosure', 'TollsDataTemporarilyUnavailable', 'TollsDataUnavailable', 'TollTransponder', 'ViolatedAvoidControlledAccessHighway', 'ViolatedAvoidDifficultTurns', 'ViolatedAvoidDirtRoad', 'ViolatedAvoidSeasonalClosure', 'ViolatedAvoidTollRoad', 'ViolatedAvoidTollTransponder', 'ViolatedAvoidTruckRoadType', 'ViolatedAvoidTunnel', 'ViolatedAvoidUTurns', 'ViolatedBlockedRoad', 'ViolatedCarpool', 'ViolatedEmergencyGate', 'ViolatedStartDirection', 'ViolatedTurnRestriction', 'ViolatedVehicleRestriction', 'ViolatedZoneRestriction', ], ], 'RouteVehicleNoticeDetail' => [ 'type' => 'structure', 'members' => [ 'Title' => [ 'shape' => 'String', ], 'ViolatedConstraints' => [ 'shape' => 'RouteViolatedConstraints', ], ], ], 'RouteVehicleNoticeDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteVehicleNoticeDetail', ], ], 'RouteVehicleNoticeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteVehicleNotice', ], ], 'RouteVehicleOverviewSummary' => [ 'type' => 'structure', 'required' => [ 'Distance', 'Duration', ], 'members' => [ 'BestCaseDuration' => [ 'shape' => 'DurationSeconds', ], 'Distance' => [ 'shape' => 'DistanceMeters', ], 'Duration' => [ 'shape' => 'DurationSeconds', ], 'TypicalDuration' => [ 'shape' => 'DurationSeconds', ], ], ], 'RouteVehiclePlace' => [ 'type' => 'structure', 'required' => [ 'Position', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'OriginalPosition' => [ 'shape' => 'Position23', ], 'Position' => [ 'shape' => 'Position23', ], 'SideOfStreet' => [ 'shape' => 'RouteSideOfStreet', ], 'WaypointIndex' => [ 'shape' => 'RouteVehiclePlaceWaypointIndexInteger', ], ], ], 'RouteVehiclePlaceWaypointIndexInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'RouteVehicleSpan' => [ 'type' => 'structure', 'members' => [ 'BestCaseDuration' => [ 'shape' => 'DurationSeconds', ], 'CarAccess' => [ 'shape' => 'RouteSpanCarAccessAttributeList', ], 'Country' => [ 'shape' => 'CountryCode3', ], 'Distance' => [ 'shape' => 'DistanceMeters', ], 'Duration' => [ 'shape' => 'DurationSeconds', ], 'DynamicSpeed' => [ 'shape' => 'RouteSpanDynamicSpeedDetails', ], 'FunctionalClassification' => [ 'shape' => 'RouteVehicleSpanFunctionalClassificationInteger', ], 'Gate' => [ 'shape' => 'RouteSpanGateAttribute', ], 'GeometryOffset' => [ 'shape' => 'RouteVehicleSpanGeometryOffsetInteger', ], 'Incidents' => [ 'shape' => 'IndexList', ], 'Names' => [ 'shape' => 'LocalizedStringList', ], 'Notices' => [ 'shape' => 'IndexList', ], 'RailwayCrossing' => [ 'shape' => 'RouteSpanRailwayCrossingAttribute', ], 'Region' => [ 'shape' => 'RouteVehicleSpanRegionString', ], 'RoadAttributes' => [ 'shape' => 'RouteSpanRoadAttributeList', ], 'RouteNumbers' => [ 'shape' => 'RouteNumberList', ], 'ScooterAccess' => [ 'shape' => 'RouteSpanScooterAccessAttributeList', ], 'SpeedLimit' => [ 'shape' => 'RouteSpanSpeedLimitDetails', ], 'TollSystems' => [ 'shape' => 'IndexList', ], 'TruckAccess' => [ 'shape' => 'RouteSpanTruckAccessAttributeList', ], 'TruckRoadTypes' => [ 'shape' => 'IndexList', ], 'TypicalDuration' => [ 'shape' => 'DurationSeconds', ], 'Zones' => [ 'shape' => 'IndexList', ], ], ], 'RouteVehicleSpanFunctionalClassificationInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 5, 'min' => 1, ], 'RouteVehicleSpanGeometryOffsetInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'RouteVehicleSpanList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteVehicleSpan', ], ], 'RouteVehicleSpanRegionString' => [ 'type' => 'string', 'max' => 3, 'min' => 0, ], 'RouteVehicleSummary' => [ 'type' => 'structure', 'members' => [ 'Overview' => [ 'shape' => 'RouteVehicleOverviewSummary', ], 'TravelOnly' => [ 'shape' => 'RouteVehicleTravelOnlySummary', ], ], ], 'RouteVehicleTravelOnlySummary' => [ 'type' => 'structure', 'required' => [ 'Duration', ], 'members' => [ 'BestCaseDuration' => [ 'shape' => 'DurationSeconds', ], 'Duration' => [ 'shape' => 'DurationSeconds', ], 'TypicalDuration' => [ 'shape' => 'DurationSeconds', ], ], ], 'RouteVehicleTravelStep' => [ 'type' => 'structure', 'required' => [ 'Duration', 'Type', ], 'members' => [ 'ContinueHighwayStepDetails' => [ 'shape' => 'RouteContinueHighwayStepDetails', ], 'ContinueStepDetails' => [ 'shape' => 'RouteContinueStepDetails', ], 'CurrentRoad' => [ 'shape' => 'RouteRoad', ], 'Distance' => [ 'shape' => 'DistanceMeters', ], 'Duration' => [ 'shape' => 'DurationSeconds', ], 'EnterHighwayStepDetails' => [ 'shape' => 'RouteEnterHighwayStepDetails', ], 'ExitNumber' => [ 'shape' => 'LocalizedStringList', ], 'ExitStepDetails' => [ 'shape' => 'RouteExitStepDetails', ], 'GeometryOffset' => [ 'shape' => 'RouteVehicleTravelStepGeometryOffsetInteger', ], 'Instruction' => [ 'shape' => 'String', ], 'KeepStepDetails' => [ 'shape' => 'RouteKeepStepDetails', ], 'NextRoad' => [ 'shape' => 'RouteRoad', ], 'RampStepDetails' => [ 'shape' => 'RouteRampStepDetails', ], 'RoundaboutEnterStepDetails' => [ 'shape' => 'RouteRoundaboutEnterStepDetails', ], 'RoundaboutExitStepDetails' => [ 'shape' => 'RouteRoundaboutExitStepDetails', ], 'RoundaboutPassStepDetails' => [ 'shape' => 'RouteRoundaboutPassStepDetails', ], 'Signpost' => [ 'shape' => 'RouteSignpost', ], 'TurnStepDetails' => [ 'shape' => 'RouteTurnStepDetails', ], 'Type' => [ 'shape' => 'RouteVehicleTravelStepType', ], 'UTurnStepDetails' => [ 'shape' => 'RouteUTurnStepDetails', ], ], ], 'RouteVehicleTravelStepGeometryOffsetInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'RouteVehicleTravelStepList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteVehicleTravelStep', ], ], 'RouteVehicleTravelStepType' => [ 'type' => 'string', 'enum' => [ 'Arrive', 'Continue', 'ContinueHighway', 'Depart', 'EnterHighway', 'Exit', 'Keep', 'Ramp', 'RoundaboutEnter', 'RoundaboutExit', 'RoundaboutPass', 'Turn', 'UTurn', ], ], 'RouteViolatedConstraints' => [ 'type' => 'structure', 'required' => [ 'HazardousCargos', ], 'members' => [ 'AllHazardsRestricted' => [ 'shape' => 'Boolean', ], 'AxleCount' => [ 'shape' => 'RouteNoticeDetailRange', ], 'HazardousCargos' => [ 'shape' => 'RouteHazardousCargoTypeList', ], 'MaxHeight' => [ 'shape' => 'DimensionCentimeters', ], 'MaxKpraLength' => [ 'shape' => 'DimensionCentimeters', ], 'MaxLength' => [ 'shape' => 'DimensionCentimeters', ], 'MaxPayloadCapacity' => [ 'shape' => 'WeightKilograms', ], 'MaxWeight' => [ 'shape' => 'RouteWeightConstraint', ], 'MaxWeightPerAxle' => [ 'shape' => 'WeightKilograms', ], 'MaxWeightPerAxleGroup' => [ 'shape' => 'WeightPerAxleGroup', ], 'MaxWidth' => [ 'shape' => 'DimensionCentimeters', ], 'Occupancy' => [ 'shape' => 'RouteNoticeDetailRange', ], 'RestrictedTimes' => [ 'shape' => 'String', ], 'TimeDependent' => [ 'shape' => 'Boolean', ], 'TrailerCount' => [ 'shape' => 'RouteNoticeDetailRange', ], 'TravelMode' => [ 'shape' => 'Boolean', ], 'TruckRoadType' => [ 'shape' => 'String', ], 'TruckType' => [ 'shape' => 'RouteTruckType', ], 'TunnelRestrictionCode' => [ 'shape' => 'TunnelRestrictionCode', ], ], ], 'RouteWaypoint' => [ 'type' => 'structure', 'required' => [ 'Position', ], 'members' => [ 'AvoidActionsForDistance' => [ 'shape' => 'RouteWaypointAvoidActionsForDistanceLong', ], 'AvoidUTurns' => [ 'shape' => 'Boolean', ], 'Heading' => [ 'shape' => 'Heading', ], 'Matching' => [ 'shape' => 'RouteMatchingOptions', ], 'PassThrough' => [ 'shape' => 'Boolean', ], 'Position' => [ 'shape' => 'Position', ], 'SideOfStreet' => [ 'shape' => 'RouteSideOfStreetOptions', ], 'StopDuration' => [ 'shape' => 'DurationSeconds', ], ], ], 'RouteWaypointAvoidActionsForDistanceLong' => [ 'type' => 'long', 'max' => 2000, ], 'RouteWaypointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteWaypoint', ], ], 'RouteWeightConstraint' => [ 'type' => 'structure', 'required' => [ 'Type', 'Value', ], 'members' => [ 'Type' => [ 'shape' => 'RouteWeightConstraintType', ], 'Value' => [ 'shape' => 'WeightKilograms', ], ], ], 'RouteWeightConstraintType' => [ 'type' => 'string', 'enum' => [ 'Current', 'Gross', 'Unknown', ], ], 'RouteZone' => [ 'type' => 'structure', 'members' => [ 'Category' => [ 'shape' => 'RouteZoneCategory', ], 'Name' => [ 'shape' => 'String', ], ], ], 'RouteZoneCategory' => [ 'type' => 'string', 'enum' => [ 'CongestionPricing', 'Environmental', 'Vignette', ], ], 'RouteZoneList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteZone', ], ], 'RoutingObjective' => [ 'type' => 'string', 'enum' => [ 'FastestRoute', 'ShortestRoute', ], ], 'SensitiveString' => [ 'type' => 'string', 'sensitive' => true, ], 'SideOfStreetMatchingStrategy' => [ 'type' => 'string', 'enum' => [ 'AnyStreet', 'DividedStreetOnly', ], ], 'SnapToRoadsRequest' => [ 'type' => 'structure', 'required' => [ 'TracePoints', ], 'members' => [ 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], 'SnappedGeometryFormat' => [ 'shape' => 'GeometryFormat', ], 'SnapRadius' => [ 'shape' => 'SnapToRoadsRequestSnapRadiusLong', ], 'TracePoints' => [ 'shape' => 'SnapToRoadsRequestTracePointsList', ], 'TravelMode' => [ 'shape' => 'RoadSnapTravelMode', ], 'TravelModeOptions' => [ 'shape' => 'RoadSnapTravelModeOptions', ], ], ], 'SnapToRoadsRequestSnapRadiusLong' => [ 'type' => 'long', 'max' => 10000, 'min' => 0, ], 'SnapToRoadsRequestTracePointsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoadSnapTracePoint', ], 'max' => 5000, 'min' => 2, ], 'SnapToRoadsResponse' => [ 'type' => 'structure', 'required' => [ 'Notices', 'PricingBucket', 'SnappedGeometryFormat', 'SnappedTracePoints', ], 'members' => [ 'Notices' => [ 'shape' => 'RoadSnapNoticeList', ], 'PricingBucket' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amz-geo-pricing-bucket', ], 'SnappedGeometry' => [ 'shape' => 'RoadSnapSnappedGeometry', ], 'SnappedGeometryFormat' => [ 'shape' => 'GeometryFormat', ], 'SnappedTracePoints' => [ 'shape' => 'RoadSnapSnappedTracePointList', ], ], ], 'SpeedKilometersPerHour' => [ 'type' => 'double', 'min' => 0.0, ], 'String' => [ 'type' => 'string', ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'TimeOfDay' => [ 'type' => 'string', 'pattern' => '([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9](Z|[+-]([0-1]?[0-9]|2[0-3]):[0-5][0-9])', ], 'TimeThresholdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TimeThresholdListMemberLong', ], 'max' => 5, 'min' => 1, ], 'TimeThresholdListMemberLong' => [ 'type' => 'long', 'max' => 10800, 'min' => 0, ], 'TimestampWithTimezoneOffset' => [ 'type' => 'string', 'pattern' => '([1-2][0-9]{3})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60)(\\.[0-9]{0,9})?(Z|[+-]([01][0-9]|2[0-3]):[0-5][0-9])', ], 'TrafficUsage' => [ 'type' => 'string', 'enum' => [ 'IgnoreTrafficData', 'UseTrafficData', ], ], 'TruckRoadType' => [ 'type' => 'string', 'max' => 3, 'min' => 1, ], 'TruckRoadTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TruckRoadType', ], 'max' => 12, 'min' => 1, ], 'TunnelRestrictionCode' => [ 'type' => 'string', 'max' => 1, 'min' => 1, ], 'TurnAngle' => [ 'type' => 'double', 'max' => 180, 'min' => -180, ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', 'Reason', 'FieldList', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], 'Reason' => [ 'shape' => 'ValidationExceptionReason', 'locationName' => 'reason', ], 'FieldList' => [ 'shape' => 'ValidationExceptionFieldList', 'locationName' => 'fieldList', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'Name', 'Message', ], 'members' => [ 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'UnknownOperation', 'Missing', 'CannotParse', 'FieldValidationFailed', 'Other', 'UnknownField', ], ], 'WaypointId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'WaypointIndex' => [ 'type' => 'integer', ], 'WaypointOptimizationAccessHours' => [ 'type' => 'structure', 'required' => [ 'From', 'To', ], 'members' => [ 'From' => [ 'shape' => 'WaypointOptimizationAccessHoursEntry', ], 'To' => [ 'shape' => 'WaypointOptimizationAccessHoursEntry', ], ], ], 'WaypointOptimizationAccessHoursEntry' => [ 'type' => 'structure', 'required' => [ 'DayOfWeek', 'TimeOfDay', ], 'members' => [ 'DayOfWeek' => [ 'shape' => 'DayOfWeek', ], 'TimeOfDay' => [ 'shape' => 'TimeOfDay', ], ], ], 'WaypointOptimizationAvoidanceArea' => [ 'type' => 'structure', 'required' => [ 'Geometry', ], 'members' => [ 'Geometry' => [ 'shape' => 'WaypointOptimizationAvoidanceAreaGeometry', ], ], ], 'WaypointOptimizationAvoidanceAreaGeometry' => [ 'type' => 'structure', 'members' => [ 'BoundingBox' => [ 'shape' => 'BoundingBox', ], ], ], 'WaypointOptimizationAvoidanceOptions' => [ 'type' => 'structure', 'members' => [ 'Areas' => [ 'shape' => 'WaypointOptimizationAvoidanceOptionsAreasList', ], 'CarShuttleTrains' => [ 'shape' => 'Boolean', ], 'ControlledAccessHighways' => [ 'shape' => 'Boolean', ], 'DirtRoads' => [ 'shape' => 'Boolean', ], 'Ferries' => [ 'shape' => 'Boolean', ], 'TollRoads' => [ 'shape' => 'Boolean', ], 'Tunnels' => [ 'shape' => 'Boolean', ], 'UTurns' => [ 'shape' => 'Boolean', ], ], ], 'WaypointOptimizationAvoidanceOptionsAreasList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WaypointOptimizationAvoidanceArea', ], 'max' => 20, 'min' => 0, ], 'WaypointOptimizationClusteringAlgorithm' => [ 'type' => 'string', 'enum' => [ 'DrivingDistance', 'TopologySegment', ], ], 'WaypointOptimizationClusteringOptions' => [ 'type' => 'structure', 'required' => [ 'Algorithm', ], 'members' => [ 'Algorithm' => [ 'shape' => 'WaypointOptimizationClusteringAlgorithm', ], 'DrivingDistanceOptions' => [ 'shape' => 'WaypointOptimizationDrivingDistanceOptions', ], ], ], 'WaypointOptimizationConnection' => [ 'type' => 'structure', 'required' => [ 'Distance', 'From', 'RestDuration', 'To', 'TravelDuration', 'WaitDuration', ], 'members' => [ 'Distance' => [ 'shape' => 'DistanceMeters', ], 'From' => [ 'shape' => 'WaypointId', ], 'RestDuration' => [ 'shape' => 'DurationSeconds', ], 'To' => [ 'shape' => 'WaypointId', ], 'TravelDuration' => [ 'shape' => 'DurationSeconds', ], 'WaitDuration' => [ 'shape' => 'DurationSeconds', ], ], ], 'WaypointOptimizationConnectionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WaypointOptimizationConnection', ], ], 'WaypointOptimizationConstraint' => [ 'type' => 'string', 'enum' => [ 'AccessHours', 'AppointmentTime', 'Before', 'Heading', 'ServiceDuration', 'SideOfStreet', ], ], 'WaypointOptimizationDestinationOptions' => [ 'type' => 'structure', 'members' => [ 'AccessHours' => [ 'shape' => 'WaypointOptimizationAccessHours', ], 'AppointmentTime' => [ 'shape' => 'TimestampWithTimezoneOffset', ], 'Heading' => [ 'shape' => 'Heading', ], 'Id' => [ 'shape' => 'WaypointId', ], 'ServiceDuration' => [ 'shape' => 'DurationSeconds', ], 'SideOfStreet' => [ 'shape' => 'WaypointOptimizationSideOfStreetOptions', ], ], ], 'WaypointOptimizationDriverOptions' => [ 'type' => 'structure', 'members' => [ 'RestCycles' => [ 'shape' => 'WaypointOptimizationRestCycles', ], 'RestProfile' => [ 'shape' => 'WaypointOptimizationRestProfile', ], 'TreatServiceTimeAs' => [ 'shape' => 'WaypointOptimizationServiceTimeTreatment', ], ], ], 'WaypointOptimizationDrivingDistanceOptions' => [ 'type' => 'structure', 'required' => [ 'DrivingDistance', ], 'members' => [ 'DrivingDistance' => [ 'shape' => 'DistanceMeters', ], ], ], 'WaypointOptimizationExclusionOptions' => [ 'type' => 'structure', 'required' => [ 'Countries', ], 'members' => [ 'Countries' => [ 'shape' => 'CountryCodeList', ], ], ], 'WaypointOptimizationFailedConstraint' => [ 'type' => 'structure', 'members' => [ 'Constraint' => [ 'shape' => 'WaypointOptimizationConstraint', ], 'Reason' => [ 'shape' => 'String', ], ], ], 'WaypointOptimizationFailedConstraintList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WaypointOptimizationFailedConstraint', ], ], 'WaypointOptimizationHazardousCargoType' => [ 'type' => 'string', 'enum' => [ 'Combustible', 'Corrosive', 'Explosive', 'Flammable', 'Gas', 'HarmfulToWater', 'Organic', 'Other', 'Poison', 'PoisonousInhalation', 'Radioactive', ], ], 'WaypointOptimizationHazardousCargoTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WaypointOptimizationHazardousCargoType', ], ], 'WaypointOptimizationImpedingWaypoint' => [ 'type' => 'structure', 'required' => [ 'FailedConstraints', 'Id', 'Position', ], 'members' => [ 'FailedConstraints' => [ 'shape' => 'WaypointOptimizationFailedConstraintList', ], 'Id' => [ 'shape' => 'WaypointId', ], 'Position' => [ 'shape' => 'Position', ], ], ], 'WaypointOptimizationImpedingWaypointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WaypointOptimizationImpedingWaypoint', ], ], 'WaypointOptimizationOptimizedWaypoint' => [ 'type' => 'structure', 'required' => [ 'DepartureTime', 'Id', 'Position', ], 'members' => [ 'ArrivalTime' => [ 'shape' => 'TimestampWithTimezoneOffset', ], 'ClusterIndex' => [ 'shape' => 'ClusterIndex', ], 'DepartureTime' => [ 'shape' => 'TimestampWithTimezoneOffset', ], 'Id' => [ 'shape' => 'WaypointId', ], 'Position' => [ 'shape' => 'Position', ], ], ], 'WaypointOptimizationOptimizedWaypointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WaypointOptimizationOptimizedWaypoint', ], ], 'WaypointOptimizationOriginOptions' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'WaypointId', ], ], ], 'WaypointOptimizationPedestrianOptions' => [ 'type' => 'structure', 'members' => [ 'Speed' => [ 'shape' => 'WaypointOptimizationPedestrianOptionsSpeedDouble', 'box' => true, ], ], ], 'WaypointOptimizationPedestrianOptionsSpeedDouble' => [ 'type' => 'double', 'max' => 7.2, 'min' => 1.8, ], 'WaypointOptimizationRestCycleDurations' => [ 'type' => 'structure', 'required' => [ 'RestDuration', 'WorkDuration', ], 'members' => [ 'RestDuration' => [ 'shape' => 'DurationSeconds', ], 'WorkDuration' => [ 'shape' => 'DurationSeconds', ], ], ], 'WaypointOptimizationRestCycles' => [ 'type' => 'structure', 'required' => [ 'LongCycle', 'ShortCycle', ], 'members' => [ 'LongCycle' => [ 'shape' => 'WaypointOptimizationRestCycleDurations', ], 'ShortCycle' => [ 'shape' => 'WaypointOptimizationRestCycleDurations', ], ], ], 'WaypointOptimizationRestProfile' => [ 'type' => 'structure', 'required' => [ 'Profile', ], 'members' => [ 'Profile' => [ 'shape' => 'WaypointOptimizationRestProfileProfileString', ], ], ], 'WaypointOptimizationRestProfileProfileString' => [ 'type' => 'string', 'max' => 2, 'min' => 2, ], 'WaypointOptimizationSequencingObjective' => [ 'type' => 'string', 'enum' => [ 'FastestRoute', 'ShortestRoute', ], ], 'WaypointOptimizationServiceTimeTreatment' => [ 'type' => 'string', 'enum' => [ 'Rest', 'Work', ], ], 'WaypointOptimizationSideOfStreetOptions' => [ 'type' => 'structure', 'required' => [ 'Position', ], 'members' => [ 'Position' => [ 'shape' => 'Position', ], 'UseWith' => [ 'shape' => 'SideOfStreetMatchingStrategy', ], ], ], 'WaypointOptimizationTimeBreakdown' => [ 'type' => 'structure', 'required' => [ 'RestDuration', 'ServiceDuration', 'TravelDuration', 'WaitDuration', ], 'members' => [ 'RestDuration' => [ 'shape' => 'DurationSeconds', ], 'ServiceDuration' => [ 'shape' => 'DurationSeconds', ], 'TravelDuration' => [ 'shape' => 'DurationSeconds', ], 'WaitDuration' => [ 'shape' => 'DurationSeconds', ], ], ], 'WaypointOptimizationTrafficOptions' => [ 'type' => 'structure', 'members' => [ 'Usage' => [ 'shape' => 'TrafficUsage', ], ], ], 'WaypointOptimizationTrailerOptions' => [ 'type' => 'structure', 'members' => [ 'TrailerCount' => [ 'shape' => 'WaypointOptimizationTrailerOptionsTrailerCountInteger', ], ], ], 'WaypointOptimizationTrailerOptionsTrailerCountInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 255, 'min' => 0, ], 'WaypointOptimizationTravelMode' => [ 'type' => 'string', 'enum' => [ 'Car', 'Pedestrian', 'Scooter', 'Truck', ], ], 'WaypointOptimizationTravelModeOptions' => [ 'type' => 'structure', 'members' => [ 'Pedestrian' => [ 'shape' => 'WaypointOptimizationPedestrianOptions', ], 'Truck' => [ 'shape' => 'WaypointOptimizationTruckOptions', ], ], ], 'WaypointOptimizationTruckOptions' => [ 'type' => 'structure', 'members' => [ 'GrossWeight' => [ 'shape' => 'WeightKilograms', ], 'HazardousCargos' => [ 'shape' => 'WaypointOptimizationHazardousCargoTypeList', ], 'Height' => [ 'shape' => 'WaypointOptimizationTruckOptionsHeightLong', ], 'Length' => [ 'shape' => 'WaypointOptimizationTruckOptionsLengthLong', ], 'Trailer' => [ 'shape' => 'WaypointOptimizationTrailerOptions', ], 'TruckType' => [ 'shape' => 'WaypointOptimizationTruckType', ], 'TunnelRestrictionCode' => [ 'shape' => 'TunnelRestrictionCode', ], 'WeightPerAxle' => [ 'shape' => 'WeightKilograms', ], 'Width' => [ 'shape' => 'WaypointOptimizationTruckOptionsWidthLong', ], ], ], 'WaypointOptimizationTruckOptionsHeightLong' => [ 'type' => 'long', 'max' => 5000, 'min' => 0, ], 'WaypointOptimizationTruckOptionsLengthLong' => [ 'type' => 'long', 'max' => 30000, 'min' => 0, ], 'WaypointOptimizationTruckOptionsWidthLong' => [ 'type' => 'long', 'max' => 5000, 'min' => 0, ], 'WaypointOptimizationTruckType' => [ 'type' => 'string', 'enum' => [ 'StraightTruck', 'Tractor', ], ], 'WaypointOptimizationWaypoint' => [ 'type' => 'structure', 'required' => [ 'Position', ], 'members' => [ 'AccessHours' => [ 'shape' => 'WaypointOptimizationAccessHours', ], 'AppointmentTime' => [ 'shape' => 'TimestampWithTimezoneOffset', ], 'Before' => [ 'shape' => 'BeforeWaypointsList', ], 'Heading' => [ 'shape' => 'Heading', ], 'Id' => [ 'shape' => 'WaypointId', ], 'Position' => [ 'shape' => 'Position', ], 'ServiceDuration' => [ 'shape' => 'DurationSeconds', ], 'SideOfStreet' => [ 'shape' => 'WaypointOptimizationSideOfStreetOptions', ], ], ], 'WaypointOptimizationWaypointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WaypointOptimizationWaypoint', ], ], 'WeightKilograms' => [ 'type' => 'long', 'max' => 4294967295, 'min' => 0, ], 'WeightPerAxleGroup' => [ 'type' => 'structure', 'members' => [ 'Single' => [ 'shape' => 'WeightKilograms', ], 'Tandem' => [ 'shape' => 'WeightKilograms', ], 'Triple' => [ 'shape' => 'WeightKilograms', ], 'Quad' => [ 'shape' => 'WeightKilograms', ], 'Quint' => [ 'shape' => 'WeightKilograms', ], ], ], ],];
