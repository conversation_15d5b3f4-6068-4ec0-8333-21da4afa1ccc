{"name": "illuminate/support", "description": "The Illuminate Support package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "ext-ctype": "*", "ext-filter": "*", "ext-mbstring": "*", "doctrine/inflector": "^2.0", "illuminate/collections": "^11.0", "illuminate/conditionable": "^11.0", "illuminate/contracts": "^11.0", "illuminate/macroable": "^11.0", "nesbot/carbon": "^2.72.6|^3.8.4", "voku/portable-ascii": "^2.0.2"}, "conflict": {"tightenco/collect": "<5.5.33"}, "replace": {"spatie/once": "*"}, "autoload": {"psr-4": {"Illuminate\\Support\\": ""}, "files": ["functions.php", "helpers.php"]}, "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "suggest": {"illuminate/filesystem": "Required to use the Composer class (^11.0).", "laravel/serializable-closure": "Required to use the once function (^1.3|^2.0).", "league/commonmark": "Required to use Str::markdown() and Stringable::markdown() (^2.7).", "league/uri": "Required to use the Uri class (^7.5.1).", "ramsey/uuid": "Required to use Str::uuid() (^4.7).", "symfony/process": "Required to use the Composer class (^7.0).", "symfony/uid": "Required to use Str::ulid() (^7.0).", "symfony/var-dumper": "Required to use the dd function (^7.0).", "vlucas/phpdotenv": "Required to use the Env class and env helper (^5.6.1)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}