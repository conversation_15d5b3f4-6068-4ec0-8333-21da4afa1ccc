<?php
// This file was auto-generated from sdk-root/src/data/supplychain/2024-01-01/paginators-1.json
return [ 'pagination' => [ 'ListDataIntegrationEvents' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'events', ], 'ListDataIntegrationFlowExecutions' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'flowExecutions', ], 'ListDataIntegrationFlows' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'flows', ], 'ListDataLakeDatasets' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'datasets', ], 'ListDataLakeNamespaces' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'namespaces', ], 'ListInstances' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'instances', ], ],];
