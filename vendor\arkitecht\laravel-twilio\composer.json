{"name": "arkitecht/laravel-twilio", "description": "<PERSON><PERSON> Wrap<PERSON> for the Twilio Rest Client", "type": "library", "require": {"twilio/sdk": "^6.18|^7|^8", "illuminate/support": ">=5"}, "require-dev": {"orchestra/testbench": "^6.13", "phpunit/phpunit": "^9.5"}, "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "autoload": {"psr-4": {"Arkitecht\\Twilio\\": "src/"}}, "extra": {"laravel": {"providers": ["Arkitecht\\Twilio\\Providers\\TwilioServiceProvider"], "aliases": {"Twilio": "Arkitecht\\Twilio\\Facades\\Twilio"}}}}