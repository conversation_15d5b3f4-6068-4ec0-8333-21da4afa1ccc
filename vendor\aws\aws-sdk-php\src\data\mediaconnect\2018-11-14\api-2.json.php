<?php
// This file was auto-generated from sdk-root/src/data/mediaconnect/2018-11-14/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-11-14', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'mediaconnect', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS MediaConnect', 'serviceId' => 'MediaConnect', 'signatureVersion' => 'v4', 'signingName' => 'mediaconnect', 'uid' => 'mediaconnect-2018-11-14', ], 'operations' => [ 'AddBridgeOutputs' => [ 'name' => 'AddBridgeOutputs', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/bridges/{BridgeArn}/outputs', 'responseCode' => 202, ], 'input' => [ 'shape' => 'AddBridgeOutputsRequest', ], 'output' => [ 'shape' => 'AddBridgeOutputsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'AddBridgeSources' => [ 'name' => 'AddBridgeSources', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/bridges/{BridgeArn}/sources', 'responseCode' => 202, ], 'input' => [ 'shape' => 'AddBridgeSourcesRequest', ], 'output' => [ 'shape' => 'AddBridgeSourcesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'AddFlowMediaStreams' => [ 'name' => 'AddFlowMediaStreams', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/flows/{FlowArn}/mediaStreams', 'responseCode' => 201, ], 'input' => [ 'shape' => 'AddFlowMediaStreamsRequest', ], 'output' => [ 'shape' => 'AddFlowMediaStreamsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'AddFlowOutputs' => [ 'name' => 'AddFlowOutputs', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/flows/{FlowArn}/outputs', 'responseCode' => 201, ], 'input' => [ 'shape' => 'AddFlowOutputsRequest', ], 'output' => [ 'shape' => 'AddFlowOutputsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AddFlowOutputs420Exception', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'AddFlowSources' => [ 'name' => 'AddFlowSources', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/flows/{FlowArn}/source', 'responseCode' => 201, ], 'input' => [ 'shape' => 'AddFlowSourcesRequest', ], 'output' => [ 'shape' => 'AddFlowSourcesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'AddFlowVpcInterfaces' => [ 'name' => 'AddFlowVpcInterfaces', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/flows/{FlowArn}/vpcInterfaces', 'responseCode' => 201, ], 'input' => [ 'shape' => 'AddFlowVpcInterfacesRequest', ], 'output' => [ 'shape' => 'AddFlowVpcInterfacesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'CreateBridge' => [ 'name' => 'CreateBridge', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/bridges', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateBridgeRequest', ], 'output' => [ 'shape' => 'CreateBridgeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'CreateBridge420Exception', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'CreateFlow' => [ 'name' => 'CreateFlow', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/flows', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateFlowRequest', ], 'output' => [ 'shape' => 'CreateFlowResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'CreateFlow420Exception', ], ], ], 'CreateGateway' => [ 'name' => 'CreateGateway', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/gateways', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateGatewayRequest', ], 'output' => [ 'shape' => 'CreateGatewayResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'CreateGateway420Exception', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteBridge' => [ 'name' => 'DeleteBridge', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/bridges/{BridgeArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteBridgeRequest', ], 'output' => [ 'shape' => 'DeleteBridgeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'DeleteFlow' => [ 'name' => 'DeleteFlow', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/flows/{FlowArn}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteFlowRequest', ], 'output' => [ 'shape' => 'DeleteFlowResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'DeleteGateway' => [ 'name' => 'DeleteGateway', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/gateways/{GatewayArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteGatewayRequest', ], 'output' => [ 'shape' => 'DeleteGatewayResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'DeregisterGatewayInstance' => [ 'name' => 'DeregisterGatewayInstance', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/gateway-instances/{GatewayInstanceArn}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeregisterGatewayInstanceRequest', ], 'output' => [ 'shape' => 'DeregisterGatewayInstanceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'DescribeBridge' => [ 'name' => 'DescribeBridge', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/bridges/{BridgeArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeBridgeRequest', ], 'output' => [ 'shape' => 'DescribeBridgeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeFlow' => [ 'name' => 'DescribeFlow', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/flows/{FlowArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeFlowRequest', ], 'output' => [ 'shape' => 'DescribeFlowResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeFlowSourceMetadata' => [ 'name' => 'DescribeFlowSourceMetadata', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/flows/{FlowArn}/source-metadata', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeFlowSourceMetadataRequest', ], 'output' => [ 'shape' => 'DescribeFlowSourceMetadataResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeFlowSourceThumbnail' => [ 'name' => 'DescribeFlowSourceThumbnail', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/flows/{FlowArn}/source-thumbnail', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeFlowSourceThumbnailRequest', ], 'output' => [ 'shape' => 'DescribeFlowSourceThumbnailResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeGateway' => [ 'name' => 'DescribeGateway', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/gateways/{GatewayArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeGatewayRequest', ], 'output' => [ 'shape' => 'DescribeGatewayResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeGatewayInstance' => [ 'name' => 'DescribeGatewayInstance', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/gateway-instances/{GatewayInstanceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeGatewayInstanceRequest', ], 'output' => [ 'shape' => 'DescribeGatewayInstanceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeOffering' => [ 'name' => 'DescribeOffering', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/offerings/{OfferingArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeOfferingRequest', ], 'output' => [ 'shape' => 'DescribeOfferingResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeReservation' => [ 'name' => 'DescribeReservation', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/reservations/{ReservationArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeReservationRequest', ], 'output' => [ 'shape' => 'DescribeReservationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'GrantFlowEntitlements' => [ 'name' => 'GrantFlowEntitlements', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/flows/{FlowArn}/entitlements', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GrantFlowEntitlementsRequest', ], 'output' => [ 'shape' => 'GrantFlowEntitlementsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'GrantFlowEntitlements420Exception', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListBridges' => [ 'name' => 'ListBridges', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/bridges', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBridgesRequest', ], 'output' => [ 'shape' => 'ListBridgesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListEntitlements' => [ 'name' => 'ListEntitlements', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/entitlements', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEntitlementsRequest', ], 'output' => [ 'shape' => 'ListEntitlementsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListFlows' => [ 'name' => 'ListFlows', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/flows', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFlowsRequest', ], 'output' => [ 'shape' => 'ListFlowsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListGatewayInstances' => [ 'name' => 'ListGatewayInstances', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/gateway-instances', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListGatewayInstancesRequest', ], 'output' => [ 'shape' => 'ListGatewayInstancesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListGateways' => [ 'name' => 'ListGateways', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/gateways', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListGatewaysRequest', ], 'output' => [ 'shape' => 'ListGatewaysResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListOfferings' => [ 'name' => 'ListOfferings', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/offerings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListOfferingsRequest', ], 'output' => [ 'shape' => 'ListOfferingsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListReservations' => [ 'name' => 'ListReservations', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/reservations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListReservationsRequest', ], 'output' => [ 'shape' => 'ListReservationsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], ], ], 'PurchaseOffering' => [ 'name' => 'PurchaseOffering', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/offerings/{OfferingArn}', 'responseCode' => 201, ], 'input' => [ 'shape' => 'PurchaseOfferingRequest', ], 'output' => [ 'shape' => 'PurchaseOfferingResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'RemoveBridgeOutput' => [ 'name' => 'RemoveBridgeOutput', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/bridges/{BridgeArn}/outputs/{OutputName}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'RemoveBridgeOutputRequest', ], 'output' => [ 'shape' => 'RemoveBridgeOutputResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'RemoveBridgeSource' => [ 'name' => 'RemoveBridgeSource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/bridges/{BridgeArn}/sources/{SourceName}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'RemoveBridgeSourceRequest', ], 'output' => [ 'shape' => 'RemoveBridgeSourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'RemoveFlowMediaStream' => [ 'name' => 'RemoveFlowMediaStream', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/flows/{FlowArn}/mediaStreams/{MediaStreamName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RemoveFlowMediaStreamRequest', ], 'output' => [ 'shape' => 'RemoveFlowMediaStreamResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'RemoveFlowOutput' => [ 'name' => 'RemoveFlowOutput', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/flows/{FlowArn}/outputs/{OutputArn}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'RemoveFlowOutputRequest', ], 'output' => [ 'shape' => 'RemoveFlowOutputResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'RemoveFlowSource' => [ 'name' => 'RemoveFlowSource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/flows/{FlowArn}/source/{SourceArn}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'RemoveFlowSourceRequest', ], 'output' => [ 'shape' => 'RemoveFlowSourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'RemoveFlowVpcInterface' => [ 'name' => 'RemoveFlowVpcInterface', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/flows/{FlowArn}/vpcInterfaces/{VpcInterfaceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RemoveFlowVpcInterfaceRequest', ], 'output' => [ 'shape' => 'RemoveFlowVpcInterfaceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'RevokeFlowEntitlement' => [ 'name' => 'RevokeFlowEntitlement', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/flows/{FlowArn}/entitlements/{EntitlementArn}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'RevokeFlowEntitlementRequest', ], 'output' => [ 'shape' => 'RevokeFlowEntitlementResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'StartFlow' => [ 'name' => 'StartFlow', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/flows/start/{FlowArn}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StartFlowRequest', ], 'output' => [ 'shape' => 'StartFlowResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'StopFlow' => [ 'name' => 'StopFlow', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/flows/stop/{FlowArn}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StopFlowRequest', ], 'output' => [ 'shape' => 'StopFlowResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], ], 'idempotent' => true, ], 'UpdateBridge' => [ 'name' => 'UpdateBridge', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/bridges/{BridgeArn}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateBridgeRequest', ], 'output' => [ 'shape' => 'UpdateBridgeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'UpdateBridgeOutput' => [ 'name' => 'UpdateBridgeOutput', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/bridges/{BridgeArn}/outputs/{OutputName}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateBridgeOutputRequest', ], 'output' => [ 'shape' => 'UpdateBridgeOutputResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'UpdateBridgeSource' => [ 'name' => 'UpdateBridgeSource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/bridges/{BridgeArn}/sources/{SourceName}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateBridgeSourceRequest', ], 'output' => [ 'shape' => 'UpdateBridgeSourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'UpdateBridgeState' => [ 'name' => 'UpdateBridgeState', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/bridges/{BridgeArn}/state', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateBridgeStateRequest', ], 'output' => [ 'shape' => 'UpdateBridgeStateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'UpdateFlow' => [ 'name' => 'UpdateFlow', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/flows/{FlowArn}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateFlowRequest', ], 'output' => [ 'shape' => 'UpdateFlowResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'UpdateFlowEntitlement' => [ 'name' => 'UpdateFlowEntitlement', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/flows/{FlowArn}/entitlements/{EntitlementArn}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateFlowEntitlementRequest', ], 'output' => [ 'shape' => 'UpdateFlowEntitlementResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'UpdateFlowMediaStream' => [ 'name' => 'UpdateFlowMediaStream', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/flows/{FlowArn}/mediaStreams/{MediaStreamName}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateFlowMediaStreamRequest', ], 'output' => [ 'shape' => 'UpdateFlowMediaStreamResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'UpdateFlowOutput' => [ 'name' => 'UpdateFlowOutput', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/flows/{FlowArn}/outputs/{OutputArn}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateFlowOutputRequest', ], 'output' => [ 'shape' => 'UpdateFlowOutputResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'UpdateFlowSource' => [ 'name' => 'UpdateFlowSource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/flows/{FlowArn}/source/{SourceArn}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateFlowSourceRequest', ], 'output' => [ 'shape' => 'UpdateFlowSourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'UpdateGatewayInstance' => [ 'name' => 'UpdateGatewayInstance', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/gateway-instances/{GatewayInstanceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateGatewayInstanceRequest', ], 'output' => [ 'shape' => 'UpdateGatewayInstanceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AddBridgeFlowSourceRequest' => [ 'type' => 'structure', 'required' => [ 'FlowArn', 'Name', ], 'members' => [ 'FlowArn' => [ 'shape' => 'AddBridgeFlowSourceRequestFlowArnString', 'locationName' => 'flowArn', ], 'FlowVpcInterfaceAttachment' => [ 'shape' => 'VpcInterfaceAttachment', 'locationName' => 'flowVpcInterfaceAttachment', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], ], ], 'AddBridgeFlowSourceRequestFlowArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:flow:.+', ], 'AddBridgeNetworkOutputRequest' => [ 'type' => 'structure', 'required' => [ 'IpAddress', 'Name', 'NetworkName', 'Port', 'Protocol', 'Ttl', ], 'members' => [ 'IpAddress' => [ 'shape' => 'String', 'locationName' => 'ipAddress', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'NetworkName' => [ 'shape' => 'String', 'locationName' => 'networkName', ], 'Port' => [ 'shape' => 'Integer', 'locationName' => 'port', ], 'Protocol' => [ 'shape' => 'Protocol', 'locationName' => 'protocol', ], 'Ttl' => [ 'shape' => 'Integer', 'locationName' => 'ttl', ], ], ], 'AddBridgeNetworkSourceRequest' => [ 'type' => 'structure', 'required' => [ 'MulticastIp', 'Name', 'NetworkName', 'Port', 'Protocol', ], 'members' => [ 'MulticastIp' => [ 'shape' => 'String', 'locationName' => 'multicastIp', ], 'MulticastSourceSettings' => [ 'shape' => 'MulticastSourceSettings', 'locationName' => 'multicastSourceSettings', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'NetworkName' => [ 'shape' => 'String', 'locationName' => 'networkName', ], 'Port' => [ 'shape' => 'Integer', 'locationName' => 'port', ], 'Protocol' => [ 'shape' => 'Protocol', 'locationName' => 'protocol', ], ], ], 'AddBridgeOutputRequest' => [ 'type' => 'structure', 'members' => [ 'NetworkOutput' => [ 'shape' => 'AddBridgeNetworkOutputRequest', 'locationName' => 'networkOutput', ], ], ], 'AddBridgeOutputsRequest' => [ 'type' => 'structure', 'required' => [ 'BridgeArn', 'Outputs', ], 'members' => [ 'BridgeArn' => [ 'shape' => 'AddBridgeOutputsRequestBridgeArnString', 'location' => 'uri', 'locationName' => 'BridgeArn', ], 'Outputs' => [ 'shape' => '__listOfAddBridgeOutputRequest', 'locationName' => 'outputs', ], ], ], 'AddBridgeOutputsRequestBridgeArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:bridge:.+', ], 'AddBridgeOutputsResponse' => [ 'type' => 'structure', 'members' => [ 'BridgeArn' => [ 'shape' => 'String', 'locationName' => 'bridgeArn', ], 'Outputs' => [ 'shape' => '__listOfBridgeOutput', 'locationName' => 'outputs', ], ], ], 'AddBridgeSourceRequest' => [ 'type' => 'structure', 'members' => [ 'FlowSource' => [ 'shape' => 'AddBridgeFlowSourceRequest', 'locationName' => 'flowSource', ], 'NetworkSource' => [ 'shape' => 'AddBridgeNetworkSourceRequest', 'locationName' => 'networkSource', ], ], ], 'AddBridgeSourcesRequest' => [ 'type' => 'structure', 'required' => [ 'BridgeArn', 'Sources', ], 'members' => [ 'BridgeArn' => [ 'shape' => 'BridgeArn', 'location' => 'uri', 'locationName' => 'BridgeArn', ], 'Sources' => [ 'shape' => '__listOfAddBridgeSourceRequest', 'locationName' => 'sources', ], ], ], 'AddBridgeSourcesResponse' => [ 'type' => 'structure', 'members' => [ 'BridgeArn' => [ 'shape' => 'String', 'locationName' => 'bridgeArn', ], 'Sources' => [ 'shape' => '__listOfBridgeSource', 'locationName' => 'sources', ], ], ], 'AddEgressGatewayBridgeRequest' => [ 'type' => 'structure', 'required' => [ 'MaxBitrate', ], 'members' => [ 'MaxBitrate' => [ 'shape' => 'Integer', 'locationName' => 'maxBitrate', ], ], ], 'AddFlowMediaStreamsRequest' => [ 'type' => 'structure', 'required' => [ 'FlowArn', 'MediaStreams', ], 'members' => [ 'FlowArn' => [ 'shape' => 'AddFlowMediaStreamsRequestFlowArnString', 'location' => 'uri', 'locationName' => 'FlowArn', ], 'MediaStreams' => [ 'shape' => '__listOfAddMediaStreamRequest', 'locationName' => 'mediaStreams', ], ], ], 'AddFlowMediaStreamsRequestFlowArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:flow:.+', ], 'AddFlowMediaStreamsResponse' => [ 'type' => 'structure', 'members' => [ 'FlowArn' => [ 'shape' => 'String', 'locationName' => 'flowArn', ], 'MediaStreams' => [ 'shape' => '__listOfMediaStream', 'locationName' => 'mediaStreams', ], ], ], 'AddFlowOutputs420Exception' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 420, 'senderFault' => true, ], 'exception' => true, ], 'AddFlowOutputsRequest' => [ 'type' => 'structure', 'required' => [ 'FlowArn', 'Outputs', ], 'members' => [ 'FlowArn' => [ 'shape' => 'AddFlowOutputsRequestFlowArnString', 'location' => 'uri', 'locationName' => 'FlowArn', ], 'Outputs' => [ 'shape' => '__listOfAddOutputRequest', 'locationName' => 'outputs', ], ], ], 'AddFlowOutputsRequestFlowArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:flow:.+', ], 'AddFlowOutputsResponse' => [ 'type' => 'structure', 'members' => [ 'FlowArn' => [ 'shape' => 'String', 'locationName' => 'flowArn', ], 'Outputs' => [ 'shape' => '__listOfOutput', 'locationName' => 'outputs', ], ], ], 'AddFlowSourcesRequest' => [ 'type' => 'structure', 'required' => [ 'FlowArn', 'Sources', ], 'members' => [ 'FlowArn' => [ 'shape' => 'AddFlowSourcesRequestFlowArnString', 'location' => 'uri', 'locationName' => 'FlowArn', ], 'Sources' => [ 'shape' => '__listOfSetSourceRequest', 'locationName' => 'sources', ], ], ], 'AddFlowSourcesRequestFlowArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:flow:.+', ], 'AddFlowSourcesResponse' => [ 'type' => 'structure', 'members' => [ 'FlowArn' => [ 'shape' => 'String', 'locationName' => 'flowArn', ], 'Sources' => [ 'shape' => '__listOfSource', 'locationName' => 'sources', ], ], ], 'AddFlowVpcInterfacesRequest' => [ 'type' => 'structure', 'required' => [ 'FlowArn', 'VpcInterfaces', ], 'members' => [ 'FlowArn' => [ 'shape' => 'AddFlowVpcInterfacesRequestFlowArnString', 'location' => 'uri', 'locationName' => 'FlowArn', ], 'VpcInterfaces' => [ 'shape' => '__listOfVpcInterfaceRequest', 'locationName' => 'vpcInterfaces', ], ], ], 'AddFlowVpcInterfacesRequestFlowArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:flow:.+', ], 'AddFlowVpcInterfacesResponse' => [ 'type' => 'structure', 'members' => [ 'FlowArn' => [ 'shape' => 'String', 'locationName' => 'flowArn', ], 'VpcInterfaces' => [ 'shape' => '__listOfVpcInterface', 'locationName' => 'vpcInterfaces', ], ], ], 'AddIngressGatewayBridgeRequest' => [ 'type' => 'structure', 'required' => [ 'MaxBitrate', 'MaxOutputs', ], 'members' => [ 'MaxBitrate' => [ 'shape' => 'Integer', 'locationName' => 'maxBitrate', ], 'MaxOutputs' => [ 'shape' => 'Integer', 'locationName' => 'maxOutputs', ], ], ], 'AddMaintenance' => [ 'type' => 'structure', 'required' => [ 'MaintenanceDay', 'MaintenanceStartHour', ], 'members' => [ 'MaintenanceDay' => [ 'shape' => 'MaintenanceDay', 'locationName' => 'maintenanceDay', ], 'MaintenanceStartHour' => [ 'shape' => 'String', 'locationName' => 'maintenanceStartHour', ], ], ], 'AddMediaStreamRequest' => [ 'type' => 'structure', 'required' => [ 'MediaStreamId', 'MediaStreamName', 'MediaStreamType', ], 'members' => [ 'Attributes' => [ 'shape' => 'MediaStreamAttributesRequest', 'locationName' => 'attributes', ], 'ClockRate' => [ 'shape' => 'Integer', 'locationName' => 'clockRate', ], 'Description' => [ 'shape' => 'String', 'locationName' => 'description', ], 'MediaStreamId' => [ 'shape' => 'Integer', 'locationName' => 'mediaStreamId', ], 'MediaStreamName' => [ 'shape' => 'String', 'locationName' => 'mediaStreamName', ], 'MediaStreamType' => [ 'shape' => 'MediaStreamType', 'locationName' => 'mediaStreamType', ], 'VideoFormat' => [ 'shape' => 'String', 'locationName' => 'videoFormat', ], ], ], 'AddOutputRequest' => [ 'type' => 'structure', 'required' => [ 'Protocol', ], 'members' => [ 'CidrAllowList' => [ 'shape' => '__listOfString', 'locationName' => 'cidrAllowList', ], 'Description' => [ 'shape' => 'String', 'locationName' => 'description', ], 'Destination' => [ 'shape' => 'String', 'locationName' => 'destination', ], 'Encryption' => [ 'shape' => 'Encryption', 'locationName' => 'encryption', ], 'MaxLatency' => [ 'shape' => 'Integer', 'locationName' => 'maxLatency', ], 'MediaStreamOutputConfigurations' => [ 'shape' => '__listOfMediaStreamOutputConfigurationRequest', 'locationName' => 'mediaStreamOutputConfigurations', ], 'MinLatency' => [ 'shape' => 'Integer', 'locationName' => 'minLatency', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'Port' => [ 'shape' => 'Integer', 'locationName' => 'port', ], 'Protocol' => [ 'shape' => 'Protocol', 'locationName' => 'protocol', ], 'RemoteId' => [ 'shape' => 'String', 'locationName' => 'remoteId', ], 'SenderControlPort' => [ 'shape' => 'Integer', 'locationName' => 'senderControlPort', ], 'SmoothingLatency' => [ 'shape' => 'Integer', 'locationName' => 'smoothingLatency', ], 'StreamId' => [ 'shape' => 'String', 'locationName' => 'streamId', ], 'VpcInterfaceAttachment' => [ 'shape' => 'VpcInterfaceAttachment', 'locationName' => 'vpcInterfaceAttachment', ], 'OutputStatus' => [ 'shape' => 'OutputStatus', 'locationName' => 'outputStatus', ], 'NdiSpeedHqQuality' => [ 'shape' => 'Integer', 'locationName' => 'ndiSpeedHqQuality', ], 'NdiProgramName' => [ 'shape' => 'String', 'locationName' => 'ndiProgramName', ], ], ], 'Algorithm' => [ 'type' => 'string', 'enum' => [ 'aes128', 'aes192', 'aes256', ], ], 'AudioMonitoringSetting' => [ 'type' => 'structure', 'members' => [ 'SilentAudio' => [ 'shape' => 'SilentAudio', 'locationName' => 'silentAudio', ], ], ], 'BadRequestException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'BlackFrames' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'State', 'locationName' => 'state', ], 'ThresholdSeconds' => [ 'shape' => 'Integer', 'locationName' => 'thresholdSeconds', ], ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'Bridge' => [ 'type' => 'structure', 'required' => [ 'BridgeArn', 'BridgeState', 'Name', 'PlacementArn', ], 'members' => [ 'BridgeArn' => [ 'shape' => 'String', 'locationName' => 'bridgeArn', ], 'BridgeMessages' => [ 'shape' => '__listOfMessageDetail', 'locationName' => 'bridgeMessages', ], 'BridgeState' => [ 'shape' => 'BridgeState', 'locationName' => 'bridgeState', ], 'EgressGatewayBridge' => [ 'shape' => 'EgressGatewayBridge', 'locationName' => 'egressGatewayBridge', ], 'IngressGatewayBridge' => [ 'shape' => 'IngressGatewayBridge', 'locationName' => 'ingressGatewayBridge', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'Outputs' => [ 'shape' => '__listOfBridgeOutput', 'locationName' => 'outputs', ], 'PlacementArn' => [ 'shape' => 'String', 'locationName' => 'placementArn', ], 'SourceFailoverConfig' => [ 'shape' => 'FailoverConfig', 'locationName' => 'sourceFailoverConfig', ], 'Sources' => [ 'shape' => '__listOfBridgeSource', 'locationName' => 'sources', ], ], ], 'BridgeArn' => [ 'type' => 'string', ], 'BridgeFlowOutput' => [ 'type' => 'structure', 'required' => [ 'FlowArn', 'FlowSourceArn', 'Name', ], 'members' => [ 'FlowArn' => [ 'shape' => 'String', 'locationName' => 'flowArn', ], 'FlowSourceArn' => [ 'shape' => 'String', 'locationName' => 'flowSourceArn', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], ], ], 'BridgeFlowSource' => [ 'type' => 'structure', 'required' => [ 'FlowArn', 'Name', ], 'members' => [ 'FlowArn' => [ 'shape' => 'String', 'locationName' => 'flowArn', ], 'FlowVpcInterfaceAttachment' => [ 'shape' => 'VpcInterfaceAttachment', 'locationName' => 'flowVpcInterfaceAttachment', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'OutputArn' => [ 'shape' => 'String', 'locationName' => 'outputArn', ], ], ], 'BridgeNetworkOutput' => [ 'type' => 'structure', 'required' => [ 'IpAddress', 'Name', 'NetworkName', 'Port', 'Protocol', 'Ttl', ], 'members' => [ 'IpAddress' => [ 'shape' => 'String', 'locationName' => 'ipAddress', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'NetworkName' => [ 'shape' => 'String', 'locationName' => 'networkName', ], 'Port' => [ 'shape' => 'Integer', 'locationName' => 'port', ], 'Protocol' => [ 'shape' => 'Protocol', 'locationName' => 'protocol', ], 'Ttl' => [ 'shape' => 'Integer', 'locationName' => 'ttl', ], ], ], 'BridgeNetworkSource' => [ 'type' => 'structure', 'required' => [ 'MulticastIp', 'Name', 'NetworkName', 'Port', 'Protocol', ], 'members' => [ 'MulticastIp' => [ 'shape' => 'String', 'locationName' => 'multicastIp', ], 'MulticastSourceSettings' => [ 'shape' => 'MulticastSourceSettings', 'locationName' => 'multicastSourceSettings', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'NetworkName' => [ 'shape' => 'String', 'locationName' => 'networkName', ], 'Port' => [ 'shape' => 'Integer', 'locationName' => 'port', ], 'Protocol' => [ 'shape' => 'Protocol', 'locationName' => 'protocol', ], ], ], 'BridgeOutput' => [ 'type' => 'structure', 'members' => [ 'FlowOutput' => [ 'shape' => 'BridgeFlowOutput', 'locationName' => 'flowOutput', ], 'NetworkOutput' => [ 'shape' => 'BridgeNetworkOutput', 'locationName' => 'networkOutput', ], ], ], 'BridgePlacement' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'LOCKED', ], ], 'BridgeSource' => [ 'type' => 'structure', 'members' => [ 'FlowSource' => [ 'shape' => 'BridgeFlowSource', 'locationName' => 'flowSource', ], 'NetworkSource' => [ 'shape' => 'BridgeNetworkSource', 'locationName' => 'networkSource', ], ], ], 'BridgeState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'STANDBY', 'STARTING', 'DEPLOYING', 'ACTIVE', 'STOPPING', 'DELETING', 'DELETED', 'START_FAILED', 'START_PENDING', 'STOP_FAILED', 'UPDATING', ], ], 'Colorimetry' => [ 'type' => 'string', 'enum' => [ 'BT601', 'BT709', 'BT2020', 'BT2100', 'ST2065-1', 'ST2065-3', 'XYZ', ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'ConnectionStatus' => [ 'type' => 'string', 'enum' => [ 'CONNECTED', 'DISCONNECTED', ], ], 'ContentQualityAnalysisState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'CreateBridge420Exception' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 420, 'senderFault' => true, ], 'exception' => true, ], 'CreateBridgeRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'PlacementArn', 'Sources', ], 'members' => [ 'EgressGatewayBridge' => [ 'shape' => 'AddEgressGatewayBridgeRequest', 'locationName' => 'egressGatewayBridge', ], 'IngressGatewayBridge' => [ 'shape' => 'AddIngressGatewayBridgeRequest', 'locationName' => 'ingressGatewayBridge', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'Outputs' => [ 'shape' => '__listOfAddBridgeOutputRequest', 'locationName' => 'outputs', ], 'PlacementArn' => [ 'shape' => 'String', 'locationName' => 'placementArn', ], 'SourceFailoverConfig' => [ 'shape' => 'FailoverConfig', 'locationName' => 'sourceFailoverConfig', ], 'Sources' => [ 'shape' => '__listOfAddBridgeSourceRequest', 'locationName' => 'sources', ], ], ], 'CreateBridgeResponse' => [ 'type' => 'structure', 'members' => [ 'Bridge' => [ 'shape' => 'Bridge', 'locationName' => 'bridge', ], ], ], 'CreateFlow420Exception' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 420, 'senderFault' => true, ], 'exception' => true, ], 'CreateFlowRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'AvailabilityZone' => [ 'shape' => 'String', 'locationName' => 'availabilityZone', ], 'Entitlements' => [ 'shape' => '__listOfGrantEntitlementRequest', 'locationName' => 'entitlements', ], 'MediaStreams' => [ 'shape' => '__listOfAddMediaStreamRequest', 'locationName' => 'mediaStreams', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'Outputs' => [ 'shape' => '__listOfAddOutputRequest', 'locationName' => 'outputs', ], 'Source' => [ 'shape' => 'SetSourceRequest', 'locationName' => 'source', ], 'SourceFailoverConfig' => [ 'shape' => 'FailoverConfig', 'locationName' => 'sourceFailoverConfig', ], 'Sources' => [ 'shape' => '__listOfSetSourceRequest', 'locationName' => 'sources', ], 'VpcInterfaces' => [ 'shape' => '__listOfVpcInterfaceRequest', 'locationName' => 'vpcInterfaces', ], 'Maintenance' => [ 'shape' => 'AddMaintenance', 'locationName' => 'maintenance', ], 'SourceMonitoringConfig' => [ 'shape' => 'MonitoringConfig', 'locationName' => 'sourceMonitoringConfig', ], 'FlowSize' => [ 'shape' => 'FlowSize', 'locationName' => 'flowSize', ], 'NdiConfig' => [ 'shape' => 'NdiConfig', 'locationName' => 'ndiConfig', ], ], ], 'CreateFlowResponse' => [ 'type' => 'structure', 'members' => [ 'Flow' => [ 'shape' => 'Flow', 'locationName' => 'flow', ], ], ], 'CreateGateway420Exception' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 420, 'senderFault' => true, ], 'exception' => true, ], 'CreateGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'EgressCidrBlocks', 'Name', 'Networks', ], 'members' => [ 'EgressCidrBlocks' => [ 'shape' => '__listOfString', 'locationName' => 'egressCidrBlocks', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'Networks' => [ 'shape' => '__listOfGatewayNetwork', 'locationName' => 'networks', ], ], ], 'CreateGatewayResponse' => [ 'type' => 'structure', 'members' => [ 'Gateway' => [ 'shape' => 'Gateway', 'locationName' => 'gateway', ], ], ], 'DeleteBridgeRequest' => [ 'type' => 'structure', 'required' => [ 'BridgeArn', ], 'members' => [ 'BridgeArn' => [ 'shape' => 'DeleteBridgeRequestBridgeArnString', 'location' => 'uri', 'locationName' => 'BridgeArn', ], ], ], 'DeleteBridgeRequestBridgeArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:bridge:.+', ], 'DeleteBridgeResponse' => [ 'type' => 'structure', 'members' => [ 'BridgeArn' => [ 'shape' => 'String', 'locationName' => 'bridgeArn', ], ], ], 'DeleteFlowRequest' => [ 'type' => 'structure', 'required' => [ 'FlowArn', ], 'members' => [ 'FlowArn' => [ 'shape' => 'DeleteFlowRequestFlowArnString', 'location' => 'uri', 'locationName' => 'FlowArn', ], ], ], 'DeleteFlowRequestFlowArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:flow:.+', ], 'DeleteFlowResponse' => [ 'type' => 'structure', 'members' => [ 'FlowArn' => [ 'shape' => 'String', 'locationName' => 'flowArn', ], 'Status' => [ 'shape' => 'Status', 'locationName' => 'status', ], ], ], 'DeleteGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'GatewayArn', ], 'members' => [ 'GatewayArn' => [ 'shape' => 'DeleteGatewayRequestGatewayArnString', 'location' => 'uri', 'locationName' => 'GatewayArn', ], ], ], 'DeleteGatewayRequestGatewayArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:gateway:.+', ], 'DeleteGatewayResponse' => [ 'type' => 'structure', 'members' => [ 'GatewayArn' => [ 'shape' => 'String', 'locationName' => 'gatewayArn', ], ], ], 'DeregisterGatewayInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'GatewayInstanceArn', ], 'members' => [ 'Force' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'force', ], 'GatewayInstanceArn' => [ 'shape' => 'DeregisterGatewayInstanceRequestGatewayInstanceArnString', 'location' => 'uri', 'locationName' => 'GatewayInstanceArn', ], ], ], 'DeregisterGatewayInstanceRequestGatewayInstanceArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:gateway:.+:instance:.+', ], 'DeregisterGatewayInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'GatewayInstanceArn' => [ 'shape' => 'String', 'locationName' => 'gatewayInstanceArn', ], 'InstanceState' => [ 'shape' => 'InstanceState', 'locationName' => 'instanceState', ], ], ], 'DescribeBridgeRequest' => [ 'type' => 'structure', 'required' => [ 'BridgeArn', ], 'members' => [ 'BridgeArn' => [ 'shape' => 'DescribeBridgeRequestBridgeArnString', 'location' => 'uri', 'locationName' => 'BridgeArn', ], ], ], 'DescribeBridgeRequestBridgeArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:bridge:.+', ], 'DescribeBridgeResponse' => [ 'type' => 'structure', 'members' => [ 'Bridge' => [ 'shape' => 'Bridge', 'locationName' => 'bridge', ], ], ], 'DescribeFlowRequest' => [ 'type' => 'structure', 'required' => [ 'FlowArn', ], 'members' => [ 'FlowArn' => [ 'shape' => 'DescribeFlowRequestFlowArnString', 'location' => 'uri', 'locationName' => 'FlowArn', ], ], ], 'DescribeFlowRequestFlowArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:flow:.+', ], 'DescribeFlowResponse' => [ 'type' => 'structure', 'members' => [ 'Flow' => [ 'shape' => 'Flow', 'locationName' => 'flow', ], 'Messages' => [ 'shape' => 'Messages', 'locationName' => 'messages', ], ], ], 'DescribeFlowSourceMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'FlowArn', ], 'members' => [ 'FlowArn' => [ 'shape' => 'FlowArn', 'location' => 'uri', 'locationName' => 'FlowArn', ], ], ], 'DescribeFlowSourceMetadataResponse' => [ 'type' => 'structure', 'members' => [ 'FlowArn' => [ 'shape' => 'String', 'locationName' => 'flowArn', ], 'Messages' => [ 'shape' => '__listOfMessageDetail', 'locationName' => 'messages', ], 'Timestamp' => [ 'shape' => 'SyntheticTimestamp_date_time', 'locationName' => 'timestamp', ], 'TransportMediaInfo' => [ 'shape' => 'TransportMediaInfo', 'locationName' => 'transportMediaInfo', ], ], ], 'DescribeFlowSourceThumbnailRequest' => [ 'type' => 'structure', 'required' => [ 'FlowArn', ], 'members' => [ 'FlowArn' => [ 'shape' => 'FlowArn', 'location' => 'uri', 'locationName' => 'FlowArn', ], ], ], 'DescribeFlowSourceThumbnailResponse' => [ 'type' => 'structure', 'members' => [ 'ThumbnailDetails' => [ 'shape' => 'ThumbnailDetails', 'locationName' => 'thumbnailDetails', ], ], ], 'DescribeGatewayInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'GatewayInstanceArn', ], 'members' => [ 'GatewayInstanceArn' => [ 'shape' => 'DescribeGatewayInstanceRequestGatewayInstanceArnString', 'location' => 'uri', 'locationName' => 'GatewayInstanceArn', ], ], ], 'DescribeGatewayInstanceRequestGatewayInstanceArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:gateway:.+:instance:.+', ], 'DescribeGatewayInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'GatewayInstance' => [ 'shape' => 'GatewayInstance', 'locationName' => 'gatewayInstance', ], ], ], 'DescribeGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'GatewayArn', ], 'members' => [ 'GatewayArn' => [ 'shape' => 'DescribeGatewayRequestGatewayArnString', 'location' => 'uri', 'locationName' => 'GatewayArn', ], ], ], 'DescribeGatewayRequestGatewayArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:gateway:.+', ], 'DescribeGatewayResponse' => [ 'type' => 'structure', 'members' => [ 'Gateway' => [ 'shape' => 'Gateway', 'locationName' => 'gateway', ], ], ], 'DescribeOfferingRequest' => [ 'type' => 'structure', 'required' => [ 'OfferingArn', ], 'members' => [ 'OfferingArn' => [ 'shape' => 'OfferingArn', 'location' => 'uri', 'locationName' => 'OfferingArn', ], ], ], 'DescribeOfferingResponse' => [ 'type' => 'structure', 'members' => [ 'Offering' => [ 'shape' => 'Offering', 'locationName' => 'offering', ], ], ], 'DescribeReservationRequest' => [ 'type' => 'structure', 'required' => [ 'ReservationArn', ], 'members' => [ 'ReservationArn' => [ 'shape' => 'ReservationArn', 'location' => 'uri', 'locationName' => 'ReservationArn', ], ], ], 'DescribeReservationResponse' => [ 'type' => 'structure', 'members' => [ 'Reservation' => [ 'shape' => 'Reservation', 'locationName' => 'reservation', ], ], ], 'DesiredState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'STANDBY', 'DELETED', ], ], 'DestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'DestinationIp', 'DestinationPort', 'Interface', 'OutboundIp', ], 'members' => [ 'DestinationIp' => [ 'shape' => 'String', 'locationName' => 'destinationIp', ], 'DestinationPort' => [ 'shape' => 'Integer', 'locationName' => 'destinationPort', ], 'Interface' => [ 'shape' => 'Interface', 'locationName' => 'interface', ], 'OutboundIp' => [ 'shape' => 'String', 'locationName' => 'outboundIp', ], ], ], 'DestinationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'DestinationIp', 'DestinationPort', 'Interface', ], 'members' => [ 'DestinationIp' => [ 'shape' => 'String', 'locationName' => 'destinationIp', ], 'DestinationPort' => [ 'shape' => 'Integer', 'locationName' => 'destinationPort', ], 'Interface' => [ 'shape' => 'InterfaceRequest', 'locationName' => 'interface', ], ], ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'DurationUnits' => [ 'type' => 'string', 'enum' => [ 'MONTHS', ], ], 'EgressGatewayBridge' => [ 'type' => 'structure', 'required' => [ 'MaxBitrate', ], 'members' => [ 'InstanceId' => [ 'shape' => 'String', 'locationName' => 'instanceId', ], 'MaxBitrate' => [ 'shape' => 'Integer', 'locationName' => 'maxBitrate', ], ], ], 'EncoderProfile' => [ 'type' => 'string', 'enum' => [ 'main', 'high', ], ], 'EncodingName' => [ 'type' => 'string', 'enum' => [ 'jxsv', 'raw', 'smpte291', 'pcm', ], ], 'EncodingParameters' => [ 'type' => 'structure', 'required' => [ 'CompressionFactor', 'EncoderProfile', ], 'members' => [ 'CompressionFactor' => [ 'shape' => 'Double', 'locationName' => 'compressionFactor', ], 'EncoderProfile' => [ 'shape' => 'EncoderProfile', 'locationName' => 'encoderProfile', ], ], ], 'EncodingParametersRequest' => [ 'type' => 'structure', 'required' => [ 'CompressionFactor', 'EncoderProfile', ], 'members' => [ 'CompressionFactor' => [ 'shape' => 'Double', 'locationName' => 'compressionFactor', ], 'EncoderProfile' => [ 'shape' => 'EncoderProfile', 'locationName' => 'encoderProfile', ], ], ], 'Encryption' => [ 'type' => 'structure', 'required' => [ 'RoleArn', ], 'members' => [ 'Algorithm' => [ 'shape' => 'Algorithm', 'locationName' => 'algorithm', ], 'ConstantInitializationVector' => [ 'shape' => 'String', 'locationName' => 'constantInitializationVector', ], 'DeviceId' => [ 'shape' => 'String', 'locationName' => 'deviceId', ], 'KeyType' => [ 'shape' => 'KeyType', 'locationName' => 'keyType', ], 'Region' => [ 'shape' => 'String', 'locationName' => 'region', ], 'ResourceId' => [ 'shape' => 'String', 'locationName' => 'resourceId', ], 'RoleArn' => [ 'shape' => 'String', 'locationName' => 'roleArn', ], 'SecretArn' => [ 'shape' => 'String', 'locationName' => 'secretArn', ], 'Url' => [ 'shape' => 'String', 'locationName' => 'url', ], ], ], 'Entitlement' => [ 'type' => 'structure', 'required' => [ 'EntitlementArn', 'Name', 'Subscribers', ], 'members' => [ 'DataTransferSubscriberFeePercent' => [ 'shape' => 'Integer', 'locationName' => 'dataTransferSubscriberFeePercent', ], 'Description' => [ 'shape' => 'String', 'locationName' => 'description', ], 'Encryption' => [ 'shape' => 'Encryption', 'locationName' => 'encryption', ], 'EntitlementArn' => [ 'shape' => 'String', 'locationName' => 'entitlementArn', ], 'EntitlementStatus' => [ 'shape' => 'EntitlementStatus', 'locationName' => 'entitlementStatus', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'Subscribers' => [ 'shape' => '__listOfString', 'locationName' => 'subscribers', ], ], ], 'EntitlementStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'FailoverConfig' => [ 'type' => 'structure', 'members' => [ 'FailoverMode' => [ 'shape' => 'FailoverMode', 'locationName' => 'failoverMode', ], 'RecoveryWindow' => [ 'shape' => 'Integer', 'locationName' => 'recoveryWindow', ], 'SourcePriority' => [ 'shape' => 'SourcePriority', 'locationName' => 'sourcePriority', ], 'State' => [ 'shape' => 'State', 'locationName' => 'state', ], ], ], 'FailoverMode' => [ 'type' => 'string', 'enum' => [ 'MERGE', 'FAILOVER', ], ], 'Flow' => [ 'type' => 'structure', 'required' => [ 'AvailabilityZone', 'Entitlements', 'FlowArn', 'Name', 'Outputs', 'Source', 'Status', ], 'members' => [ 'AvailabilityZone' => [ 'shape' => 'String', 'locationName' => 'availabilityZone', ], 'Description' => [ 'shape' => 'String', 'locationName' => 'description', ], 'EgressIp' => [ 'shape' => 'String', 'locationName' => 'egressIp', ], 'Entitlements' => [ 'shape' => '__listOfEntitlement', 'locationName' => 'entitlements', ], 'FlowArn' => [ 'shape' => 'String', 'locationName' => 'flowArn', ], 'MediaStreams' => [ 'shape' => '__listOfMediaStream', 'locationName' => 'mediaStreams', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'Outputs' => [ 'shape' => '__listOfOutput', 'locationName' => 'outputs', ], 'Source' => [ 'shape' => 'Source', 'locationName' => 'source', ], 'SourceFailoverConfig' => [ 'shape' => 'FailoverConfig', 'locationName' => 'sourceFailoverConfig', ], 'Sources' => [ 'shape' => '__listOfSource', 'locationName' => 'sources', ], 'Status' => [ 'shape' => 'Status', 'locationName' => 'status', ], 'VpcInterfaces' => [ 'shape' => '__listOfVpcInterface', 'locationName' => 'vpcInterfaces', ], 'Maintenance' => [ 'shape' => 'Maintenance', 'locationName' => 'maintenance', ], 'SourceMonitoringConfig' => [ 'shape' => 'MonitoringConfig', 'locationName' => 'sourceMonitoringConfig', ], 'FlowSize' => [ 'shape' => 'FlowSize', 'locationName' => 'flowSize', ], 'NdiConfig' => [ 'shape' => 'NdiConfig', 'locationName' => 'ndiConfig', ], ], ], 'FlowArn' => [ 'type' => 'string', ], 'FlowSize' => [ 'type' => 'string', 'enum' => [ 'MEDIUM', 'LARGE', ], ], 'Fmtp' => [ 'type' => 'structure', 'members' => [ 'ChannelOrder' => [ 'shape' => 'String', 'locationName' => 'channelOrder', ], 'Colorimetry' => [ 'shape' => 'Colorimetry', 'locationName' => 'colorimetry', ], 'ExactFramerate' => [ 'shape' => 'String', 'locationName' => 'exactFramerate', ], 'Par' => [ 'shape' => 'String', 'locationName' => 'par', ], 'Range' => [ 'shape' => 'Range', 'locationName' => 'range', ], 'ScanMode' => [ 'shape' => 'ScanMode', 'locationName' => 'scanMode', ], 'Tcs' => [ 'shape' => 'Tcs', 'locationName' => 'tcs', ], ], ], 'FmtpRequest' => [ 'type' => 'structure', 'members' => [ 'ChannelOrder' => [ 'shape' => 'String', 'locationName' => 'channelOrder', ], 'Colorimetry' => [ 'shape' => 'Colorimetry', 'locationName' => 'colorimetry', ], 'ExactFramerate' => [ 'shape' => 'String', 'locationName' => 'exactFramerate', ], 'Par' => [ 'shape' => 'String', 'locationName' => 'par', ], 'Range' => [ 'shape' => 'Range', 'locationName' => 'range', ], 'ScanMode' => [ 'shape' => 'ScanMode', 'locationName' => 'scanMode', ], 'Tcs' => [ 'shape' => 'Tcs', 'locationName' => 'tcs', ], ], ], 'ForbiddenException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'FrameResolution' => [ 'type' => 'structure', 'required' => [ 'FrameHeight', 'FrameWidth', ], 'members' => [ 'FrameHeight' => [ 'shape' => 'Integer', 'locationName' => 'frameHeight', ], 'FrameWidth' => [ 'shape' => 'Integer', 'locationName' => 'frameWidth', ], ], ], 'FrozenFrames' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'State', 'locationName' => 'state', ], 'ThresholdSeconds' => [ 'shape' => 'Integer', 'locationName' => 'thresholdSeconds', ], ], ], 'Gateway' => [ 'type' => 'structure', 'required' => [ 'EgressCidrBlocks', 'GatewayArn', 'Name', 'Networks', ], 'members' => [ 'EgressCidrBlocks' => [ 'shape' => '__listOfString', 'locationName' => 'egressCidrBlocks', ], 'GatewayArn' => [ 'shape' => 'String', 'locationName' => 'gatewayArn', ], 'GatewayMessages' => [ 'shape' => '__listOfMessageDetail', 'locationName' => 'gatewayMessages', ], 'GatewayState' => [ 'shape' => 'GatewayState', 'locationName' => 'gatewayState', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'Networks' => [ 'shape' => '__listOfGatewayNetwork', 'locationName' => 'networks', ], ], ], 'GatewayBridgeSource' => [ 'type' => 'structure', 'required' => [ 'BridgeArn', ], 'members' => [ 'BridgeArn' => [ 'shape' => 'String', 'locationName' => 'bridgeArn', ], 'VpcInterfaceAttachment' => [ 'shape' => 'VpcInterfaceAttachment', 'locationName' => 'vpcInterfaceAttachment', ], ], ], 'GatewayInstance' => [ 'type' => 'structure', 'required' => [ 'BridgePlacement', 'ConnectionStatus', 'GatewayArn', 'GatewayInstanceArn', 'InstanceId', 'InstanceState', 'RunningBridgeCount', ], 'members' => [ 'BridgePlacement' => [ 'shape' => 'BridgePlacement', 'locationName' => 'bridgePlacement', ], 'ConnectionStatus' => [ 'shape' => 'ConnectionStatus', 'locationName' => 'connectionStatus', ], 'GatewayArn' => [ 'shape' => 'String', 'locationName' => 'gatewayArn', ], 'GatewayInstanceArn' => [ 'shape' => 'String', 'locationName' => 'gatewayInstanceArn', ], 'InstanceId' => [ 'shape' => 'String', 'locationName' => 'instanceId', ], 'InstanceMessages' => [ 'shape' => '__listOfMessageDetail', 'locationName' => 'instanceMessages', ], 'InstanceState' => [ 'shape' => 'InstanceState', 'locationName' => 'instanceState', ], 'RunningBridgeCount' => [ 'shape' => 'Integer', 'locationName' => 'runningBridgeCount', ], ], ], 'GatewayNetwork' => [ 'type' => 'structure', 'required' => [ 'CidrBlock', 'Name', ], 'members' => [ 'CidrBlock' => [ 'shape' => 'String', 'locationName' => 'cidrBlock', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], ], ], 'GatewayState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'UPDATING', 'ERROR', 'DELETING', 'DELETED', ], ], 'GrantEntitlementRequest' => [ 'type' => 'structure', 'required' => [ 'Subscribers', ], 'members' => [ 'DataTransferSubscriberFeePercent' => [ 'shape' => 'Integer', 'locationName' => 'dataTransferSubscriberFeePercent', ], 'Description' => [ 'shape' => 'String', 'locationName' => 'description', ], 'Encryption' => [ 'shape' => 'Encryption', 'locationName' => 'encryption', ], 'EntitlementStatus' => [ 'shape' => 'EntitlementStatus', 'locationName' => 'entitlementStatus', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'Subscribers' => [ 'shape' => '__listOfString', 'locationName' => 'subscribers', ], ], ], 'GrantFlowEntitlements420Exception' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 420, 'senderFault' => true, ], 'exception' => true, ], 'GrantFlowEntitlementsRequest' => [ 'type' => 'structure', 'required' => [ 'Entitlements', 'FlowArn', ], 'members' => [ 'Entitlements' => [ 'shape' => '__listOfGrantEntitlementRequest', 'locationName' => 'entitlements', ], 'FlowArn' => [ 'shape' => 'GrantFlowEntitlementsRequestFlowArnString', 'location' => 'uri', 'locationName' => 'FlowArn', ], ], ], 'GrantFlowEntitlementsRequestFlowArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:flow:.+', ], 'GrantFlowEntitlementsResponse' => [ 'type' => 'structure', 'members' => [ 'Entitlements' => [ 'shape' => '__listOfEntitlement', 'locationName' => 'entitlements', ], 'FlowArn' => [ 'shape' => 'String', 'locationName' => 'flowArn', ], ], ], 'IngressGatewayBridge' => [ 'type' => 'structure', 'required' => [ 'MaxBitrate', 'MaxOutputs', ], 'members' => [ 'InstanceId' => [ 'shape' => 'String', 'locationName' => 'instanceId', ], 'MaxBitrate' => [ 'shape' => 'Integer', 'locationName' => 'maxBitrate', ], 'MaxOutputs' => [ 'shape' => 'Integer', 'locationName' => 'maxOutputs', ], ], ], 'InputConfiguration' => [ 'type' => 'structure', 'required' => [ 'InputIp', 'InputPort', 'Interface', ], 'members' => [ 'InputIp' => [ 'shape' => 'String', 'locationName' => 'inputIp', ], 'InputPort' => [ 'shape' => 'Integer', 'locationName' => 'inputPort', ], 'Interface' => [ 'shape' => 'Interface', 'locationName' => 'interface', ], ], ], 'InputConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'InputPort', 'Interface', ], 'members' => [ 'InputPort' => [ 'shape' => 'Integer', 'locationName' => 'inputPort', ], 'Interface' => [ 'shape' => 'InterfaceRequest', 'locationName' => 'interface', ], ], ], 'InstanceState' => [ 'type' => 'string', 'enum' => [ 'REGISTERING', 'ACTIVE', 'DEREGISTERING', 'DEREGISTERED', 'REGISTRATION_ERROR', 'DEREGISTRATION_ERROR', ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'Interface' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], ], ], 'InterfaceRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], ], ], 'InternalServerErrorException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'KeyType' => [ 'type' => 'string', 'enum' => [ 'speke', 'static-key', 'srt-password', ], ], 'ListBridgesRequest' => [ 'type' => 'structure', 'members' => [ 'FilterArn' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'filterArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListBridgesResponse' => [ 'type' => 'structure', 'members' => [ 'Bridges' => [ 'shape' => '__listOfListedBridge', 'locationName' => 'bridges', ], 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], ], ], 'ListEntitlementsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListEntitlementsResponse' => [ 'type' => 'structure', 'members' => [ 'Entitlements' => [ 'shape' => '__listOfListedEntitlement', 'locationName' => 'entitlements', ], 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], ], ], 'ListFlowsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListFlowsResponse' => [ 'type' => 'structure', 'members' => [ 'Flows' => [ 'shape' => '__listOfListedFlow', 'locationName' => 'flows', ], 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], ], ], 'ListGatewayInstancesRequest' => [ 'type' => 'structure', 'members' => [ 'FilterArn' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'filterArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListGatewayInstancesResponse' => [ 'type' => 'structure', 'members' => [ 'Instances' => [ 'shape' => '__listOfListedGatewayInstance', 'locationName' => 'instances', ], 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], ], ], 'ListGatewaysRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListGatewaysResponse' => [ 'type' => 'structure', 'members' => [ 'Gateways' => [ 'shape' => '__listOfListedGateway', 'locationName' => 'gateways', ], 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], ], ], 'ListOfferingsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListOfferingsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], 'Offerings' => [ 'shape' => '__listOfOffering', 'locationName' => 'offerings', ], ], ], 'ListReservationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListReservationsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', 'locationName' => 'nextToken', ], 'Reservations' => [ 'shape' => '__listOfReservation', 'locationName' => 'reservations', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => '__mapOfString', 'locationName' => 'tags', ], ], ], 'ListedBridge' => [ 'type' => 'structure', 'required' => [ 'BridgeArn', 'BridgeState', 'BridgeType', 'Name', 'PlacementArn', ], 'members' => [ 'BridgeArn' => [ 'shape' => 'String', 'locationName' => 'bridgeArn', ], 'BridgeState' => [ 'shape' => 'BridgeState', 'locationName' => 'bridgeState', ], 'BridgeType' => [ 'shape' => 'String', 'locationName' => 'bridgeType', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'PlacementArn' => [ 'shape' => 'String', 'locationName' => 'placementArn', ], ], ], 'ListedEntitlement' => [ 'type' => 'structure', 'required' => [ 'EntitlementArn', 'EntitlementName', ], 'members' => [ 'DataTransferSubscriberFeePercent' => [ 'shape' => 'Integer', 'locationName' => 'dataTransferSubscriberFeePercent', ], 'EntitlementArn' => [ 'shape' => 'String', 'locationName' => 'entitlementArn', ], 'EntitlementName' => [ 'shape' => 'String', 'locationName' => 'entitlementName', ], ], ], 'ListedFlow' => [ 'type' => 'structure', 'required' => [ 'AvailabilityZone', 'Description', 'FlowArn', 'Name', 'SourceType', 'Status', ], 'members' => [ 'AvailabilityZone' => [ 'shape' => 'String', 'locationName' => 'availabilityZone', ], 'Description' => [ 'shape' => 'String', 'locationName' => 'description', ], 'FlowArn' => [ 'shape' => 'String', 'locationName' => 'flowArn', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'SourceType' => [ 'shape' => 'SourceType', 'locationName' => 'sourceType', ], 'Status' => [ 'shape' => 'Status', 'locationName' => 'status', ], 'Maintenance' => [ 'shape' => 'Maintenance', 'locationName' => 'maintenance', ], ], ], 'ListedGateway' => [ 'type' => 'structure', 'required' => [ 'GatewayArn', 'GatewayState', 'Name', ], 'members' => [ 'GatewayArn' => [ 'shape' => 'String', 'locationName' => 'gatewayArn', ], 'GatewayState' => [ 'shape' => 'GatewayState', 'locationName' => 'gatewayState', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], ], ], 'ListedGatewayInstance' => [ 'type' => 'structure', 'required' => [ 'GatewayArn', 'GatewayInstanceArn', 'InstanceId', ], 'members' => [ 'GatewayArn' => [ 'shape' => 'String', 'locationName' => 'gatewayArn', ], 'GatewayInstanceArn' => [ 'shape' => 'String', 'locationName' => 'gatewayInstanceArn', ], 'InstanceId' => [ 'shape' => 'String', 'locationName' => 'instanceId', ], 'InstanceState' => [ 'shape' => 'InstanceState', 'locationName' => 'instanceState', ], ], ], 'Maintenance' => [ 'type' => 'structure', 'members' => [ 'MaintenanceDay' => [ 'shape' => 'MaintenanceDay', 'locationName' => 'maintenanceDay', ], 'MaintenanceDeadline' => [ 'shape' => 'String', 'locationName' => 'maintenanceDeadline', ], 'MaintenanceScheduledDate' => [ 'shape' => 'String', 'locationName' => 'maintenanceScheduledDate', ], 'MaintenanceStartHour' => [ 'shape' => 'String', 'locationName' => 'maintenanceStartHour', ], ], ], 'MaintenanceDay' => [ 'type' => 'string', 'enum' => [ 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday', ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'MediaStream' => [ 'type' => 'structure', 'required' => [ 'Fmt', 'MediaStreamId', 'MediaStreamName', 'MediaStreamType', ], 'members' => [ 'Attributes' => [ 'shape' => 'MediaStreamAttributes', 'locationName' => 'attributes', ], 'ClockRate' => [ 'shape' => 'Integer', 'locationName' => 'clockRate', ], 'Description' => [ 'shape' => 'String', 'locationName' => 'description', ], 'Fmt' => [ 'shape' => 'Integer', 'locationName' => 'fmt', ], 'MediaStreamId' => [ 'shape' => 'Integer', 'locationName' => 'mediaStreamId', ], 'MediaStreamName' => [ 'shape' => 'String', 'locationName' => 'mediaStreamName', ], 'MediaStreamType' => [ 'shape' => 'MediaStreamType', 'locationName' => 'mediaStreamType', ], 'VideoFormat' => [ 'shape' => 'String', 'locationName' => 'videoFormat', ], ], ], 'MediaStreamAttributes' => [ 'type' => 'structure', 'required' => [ 'Fmtp', ], 'members' => [ 'Fmtp' => [ 'shape' => 'Fmtp', 'locationName' => 'fmtp', ], 'Lang' => [ 'shape' => 'String', 'locationName' => 'lang', ], ], ], 'MediaStreamAttributesRequest' => [ 'type' => 'structure', 'members' => [ 'Fmtp' => [ 'shape' => 'FmtpRequest', 'locationName' => 'fmtp', ], 'Lang' => [ 'shape' => 'String', 'locationName' => 'lang', ], ], ], 'MediaStreamOutputConfiguration' => [ 'type' => 'structure', 'required' => [ 'EncodingName', 'MediaStreamName', ], 'members' => [ 'DestinationConfigurations' => [ 'shape' => '__listOfDestinationConfiguration', 'locationName' => 'destinationConfigurations', ], 'EncodingName' => [ 'shape' => 'EncodingName', 'locationName' => 'encodingName', ], 'EncodingParameters' => [ 'shape' => 'EncodingParameters', 'locationName' => 'encodingParameters', ], 'MediaStreamName' => [ 'shape' => 'String', 'locationName' => 'mediaStreamName', ], ], ], 'MediaStreamOutputConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'EncodingName', 'MediaStreamName', ], 'members' => [ 'DestinationConfigurations' => [ 'shape' => '__listOfDestinationConfigurationRequest', 'locationName' => 'destinationConfigurations', ], 'EncodingName' => [ 'shape' => 'EncodingName', 'locationName' => 'encodingName', ], 'EncodingParameters' => [ 'shape' => 'EncodingParametersRequest', 'locationName' => 'encodingParameters', ], 'MediaStreamName' => [ 'shape' => 'String', 'locationName' => 'mediaStreamName', ], ], ], 'MediaStreamSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'EncodingName', 'MediaStreamName', ], 'members' => [ 'EncodingName' => [ 'shape' => 'EncodingName', 'locationName' => 'encodingName', ], 'InputConfigurations' => [ 'shape' => '__listOfInputConfiguration', 'locationName' => 'inputConfigurations', ], 'MediaStreamName' => [ 'shape' => 'String', 'locationName' => 'mediaStreamName', ], ], ], 'MediaStreamSourceConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'EncodingName', 'MediaStreamName', ], 'members' => [ 'EncodingName' => [ 'shape' => 'EncodingName', 'locationName' => 'encodingName', ], 'InputConfigurations' => [ 'shape' => '__listOfInputConfigurationRequest', 'locationName' => 'inputConfigurations', ], 'MediaStreamName' => [ 'shape' => 'String', 'locationName' => 'mediaStreamName', ], ], ], 'MediaStreamType' => [ 'type' => 'string', 'enum' => [ 'video', 'audio', 'ancillary-data', ], ], 'MessageDetail' => [ 'type' => 'structure', 'required' => [ 'Code', 'Message', ], 'members' => [ 'Code' => [ 'shape' => 'String', 'locationName' => 'code', ], 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], 'ResourceName' => [ 'shape' => 'String', 'locationName' => 'resourceName', ], ], ], 'Messages' => [ 'type' => 'structure', 'required' => [ 'Errors', ], 'members' => [ 'Errors' => [ 'shape' => '__listOfString', 'locationName' => 'errors', ], ], ], 'MonitoringConfig' => [ 'type' => 'structure', 'members' => [ 'ThumbnailState' => [ 'shape' => 'ThumbnailState', 'locationName' => 'thumbnailState', ], 'AudioMonitoringSettings' => [ 'shape' => '__listOfAudioMonitoringSetting', 'locationName' => 'audioMonitoringSettings', ], 'ContentQualityAnalysisState' => [ 'shape' => 'ContentQualityAnalysisState', 'locationName' => 'contentQualityAnalysisState', ], 'VideoMonitoringSettings' => [ 'shape' => '__listOfVideoMonitoringSetting', 'locationName' => 'videoMonitoringSettings', ], ], ], 'MulticastSourceSettings' => [ 'type' => 'structure', 'members' => [ 'MulticastSourceIp' => [ 'shape' => 'String', 'locationName' => 'multicastSourceIp', ], ], ], 'NdiConfig' => [ 'type' => 'structure', 'members' => [ 'NdiState' => [ 'shape' => 'NdiState', 'locationName' => 'ndiState', ], 'MachineName' => [ 'shape' => 'String', 'locationName' => 'machineName', ], 'NdiDiscoveryServers' => [ 'shape' => '__listOfNdiDiscoveryServerConfig', 'locationName' => 'ndiDiscoveryServers', ], ], ], 'NdiDiscoveryServerConfig' => [ 'type' => 'structure', 'required' => [ 'DiscoveryServerAddress', 'VpcInterfaceAdapter', ], 'members' => [ 'DiscoveryServerAddress' => [ 'shape' => 'String', 'locationName' => 'discoveryServerAddress', ], 'DiscoveryServerPort' => [ 'shape' => 'Integer', 'locationName' => 'discoveryServerPort', ], 'VpcInterfaceAdapter' => [ 'shape' => 'String', 'locationName' => 'vpcInterfaceAdapter', ], ], ], 'NdiState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'NetworkInterfaceType' => [ 'type' => 'string', 'enum' => [ 'ena', 'efa', ], ], 'NotFoundException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'Offering' => [ 'type' => 'structure', 'required' => [ 'CurrencyCode', 'Duration', 'DurationUnits', 'OfferingArn', 'OfferingDescription', 'PricePerUnit', 'PriceUnits', 'ResourceSpecification', ], 'members' => [ 'CurrencyCode' => [ 'shape' => 'String', 'locationName' => 'currencyCode', ], 'Duration' => [ 'shape' => 'Integer', 'locationName' => 'duration', ], 'DurationUnits' => [ 'shape' => 'DurationUnits', 'locationName' => 'durationUnits', ], 'OfferingArn' => [ 'shape' => 'String', 'locationName' => 'offeringArn', ], 'OfferingDescription' => [ 'shape' => 'String', 'locationName' => 'offeringDescription', ], 'PricePerUnit' => [ 'shape' => 'String', 'locationName' => 'pricePerUnit', ], 'PriceUnits' => [ 'shape' => 'PriceUnits', 'locationName' => 'priceUnits', ], 'ResourceSpecification' => [ 'shape' => 'ResourceSpecification', 'locationName' => 'resourceSpecification', ], ], ], 'OfferingArn' => [ 'type' => 'string', ], 'Output' => [ 'type' => 'structure', 'required' => [ 'Name', 'OutputArn', ], 'members' => [ 'DataTransferSubscriberFeePercent' => [ 'shape' => 'Integer', 'locationName' => 'dataTransferSubscriberFeePercent', ], 'Description' => [ 'shape' => 'String', 'locationName' => 'description', ], 'Destination' => [ 'shape' => 'String', 'locationName' => 'destination', ], 'Encryption' => [ 'shape' => 'Encryption', 'locationName' => 'encryption', ], 'EntitlementArn' => [ 'shape' => 'String', 'locationName' => 'entitlementArn', ], 'ListenerAddress' => [ 'shape' => 'String', 'locationName' => 'listenerAddress', ], 'MediaLiveInputArn' => [ 'shape' => 'String', 'locationName' => 'mediaLiveInputArn', ], 'MediaStreamOutputConfigurations' => [ 'shape' => '__listOfMediaStreamOutputConfiguration', 'locationName' => 'mediaStreamOutputConfigurations', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'OutputArn' => [ 'shape' => 'String', 'locationName' => 'outputArn', ], 'Port' => [ 'shape' => 'Integer', 'locationName' => 'port', ], 'Transport' => [ 'shape' => 'Transport', 'locationName' => 'transport', ], 'VpcInterfaceAttachment' => [ 'shape' => 'VpcInterfaceAttachment', 'locationName' => 'vpcInterfaceAttachment', ], 'BridgeArn' => [ 'shape' => 'String', 'locationName' => 'bridgeArn', ], 'BridgePorts' => [ 'shape' => '__listOfInteger', 'locationName' => 'bridgePorts', ], 'OutputStatus' => [ 'shape' => 'OutputStatus', 'locationName' => 'outputStatus', ], 'PeerIpAddress' => [ 'shape' => 'String', 'locationName' => 'peerIpAddress', ], ], ], 'OutputStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'PriceUnits' => [ 'type' => 'string', 'enum' => [ 'HOURLY', ], ], 'Protocol' => [ 'type' => 'string', 'enum' => [ 'zixi-push', 'rtp-fec', 'rtp', 'zixi-pull', 'rist', 'st2110-jpegxs', 'cdi', 'srt-listener', 'srt-caller', 'fujitsu-qos', 'udp', 'ndi-speed-hq', ], ], 'PurchaseOfferingRequest' => [ 'type' => 'structure', 'required' => [ 'OfferingArn', 'ReservationName', 'Start', ], 'members' => [ 'OfferingArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'OfferingArn', ], 'ReservationName' => [ 'shape' => 'String', 'locationName' => 'reservationName', ], 'Start' => [ 'shape' => 'String', 'locationName' => 'start', ], ], ], 'PurchaseOfferingResponse' => [ 'type' => 'structure', 'members' => [ 'Reservation' => [ 'shape' => 'Reservation', 'locationName' => 'reservation', ], ], ], 'Range' => [ 'type' => 'string', 'enum' => [ 'NARROW', 'FULL', 'FULLPROTECT', ], ], 'RemoveBridgeOutputRequest' => [ 'type' => 'structure', 'required' => [ 'BridgeArn', 'OutputName', ], 'members' => [ 'BridgeArn' => [ 'shape' => 'RemoveBridgeOutputRequestBridgeArnString', 'location' => 'uri', 'locationName' => 'BridgeArn', ], 'OutputName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'OutputName', ], ], ], 'RemoveBridgeOutputRequestBridgeArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:bridge:.+', ], 'RemoveBridgeOutputResponse' => [ 'type' => 'structure', 'members' => [ 'BridgeArn' => [ 'shape' => 'String', 'locationName' => 'bridgeArn', ], 'OutputName' => [ 'shape' => 'String', 'locationName' => 'outputName', ], ], ], 'RemoveBridgeSourceRequest' => [ 'type' => 'structure', 'required' => [ 'BridgeArn', 'SourceName', ], 'members' => [ 'BridgeArn' => [ 'shape' => 'RemoveBridgeSourceRequestBridgeArnString', 'location' => 'uri', 'locationName' => 'BridgeArn', ], 'SourceName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'SourceName', ], ], ], 'RemoveBridgeSourceRequestBridgeArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:bridge:.+', ], 'RemoveBridgeSourceResponse' => [ 'type' => 'structure', 'members' => [ 'BridgeArn' => [ 'shape' => 'String', 'locationName' => 'bridgeArn', ], 'SourceName' => [ 'shape' => 'String', 'locationName' => 'sourceName', ], ], ], 'RemoveFlowMediaStreamRequest' => [ 'type' => 'structure', 'required' => [ 'FlowArn', 'MediaStreamName', ], 'members' => [ 'FlowArn' => [ 'shape' => 'RemoveFlowMediaStreamRequestFlowArnString', 'location' => 'uri', 'locationName' => 'FlowArn', ], 'MediaStreamName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'MediaStreamName', ], ], ], 'RemoveFlowMediaStreamRequestFlowArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:flow:.+', ], 'RemoveFlowMediaStreamResponse' => [ 'type' => 'structure', 'members' => [ 'FlowArn' => [ 'shape' => 'String', 'locationName' => 'flowArn', ], 'MediaStreamName' => [ 'shape' => 'String', 'locationName' => 'mediaStreamName', ], ], ], 'RemoveFlowOutputRequest' => [ 'type' => 'structure', 'required' => [ 'FlowArn', 'OutputArn', ], 'members' => [ 'FlowArn' => [ 'shape' => 'RemoveFlowOutputRequestFlowArnString', 'location' => 'uri', 'locationName' => 'FlowArn', ], 'OutputArn' => [ 'shape' => 'RemoveFlowOutputRequestOutputArnString', 'location' => 'uri', 'locationName' => 'OutputArn', ], ], ], 'RemoveFlowOutputRequestFlowArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:flow:.+', ], 'RemoveFlowOutputRequestOutputArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:output:.+', ], 'RemoveFlowOutputResponse' => [ 'type' => 'structure', 'members' => [ 'FlowArn' => [ 'shape' => 'String', 'locationName' => 'flowArn', ], 'OutputArn' => [ 'shape' => 'String', 'locationName' => 'outputArn', ], ], ], 'RemoveFlowSourceRequest' => [ 'type' => 'structure', 'required' => [ 'FlowArn', 'SourceArn', ], 'members' => [ 'FlowArn' => [ 'shape' => 'RemoveFlowSourceRequestFlowArnString', 'location' => 'uri', 'locationName' => 'FlowArn', ], 'SourceArn' => [ 'shape' => 'RemoveFlowSourceRequestSourceArnString', 'location' => 'uri', 'locationName' => 'SourceArn', ], ], ], 'RemoveFlowSourceRequestFlowArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:flow:.+', ], 'RemoveFlowSourceRequestSourceArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:source:.+', ], 'RemoveFlowSourceResponse' => [ 'type' => 'structure', 'members' => [ 'FlowArn' => [ 'shape' => 'String', 'locationName' => 'flowArn', ], 'SourceArn' => [ 'shape' => 'String', 'locationName' => 'sourceArn', ], ], ], 'RemoveFlowVpcInterfaceRequest' => [ 'type' => 'structure', 'required' => [ 'FlowArn', 'VpcInterfaceName', ], 'members' => [ 'FlowArn' => [ 'shape' => 'RemoveFlowVpcInterfaceRequestFlowArnString', 'location' => 'uri', 'locationName' => 'FlowArn', ], 'VpcInterfaceName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'VpcInterfaceName', ], ], ], 'RemoveFlowVpcInterfaceRequestFlowArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:flow:.+', ], 'RemoveFlowVpcInterfaceResponse' => [ 'type' => 'structure', 'members' => [ 'FlowArn' => [ 'shape' => 'String', 'locationName' => 'flowArn', ], 'NonDeletedNetworkInterfaceIds' => [ 'shape' => '__listOfString', 'locationName' => 'nonDeletedNetworkInterfaceIds', ], 'VpcInterfaceName' => [ 'shape' => 'String', 'locationName' => 'vpcInterfaceName', ], ], ], 'Reservation' => [ 'type' => 'structure', 'required' => [ 'CurrencyCode', 'Duration', 'DurationUnits', 'End', 'OfferingArn', 'OfferingDescription', 'PricePerUnit', 'PriceUnits', 'ReservationArn', 'ReservationName', 'ReservationState', 'ResourceSpecification', 'Start', ], 'members' => [ 'CurrencyCode' => [ 'shape' => 'String', 'locationName' => 'currencyCode', ], 'Duration' => [ 'shape' => 'Integer', 'locationName' => 'duration', ], 'DurationUnits' => [ 'shape' => 'DurationUnits', 'locationName' => 'durationUnits', ], 'End' => [ 'shape' => 'String', 'locationName' => 'end', ], 'OfferingArn' => [ 'shape' => 'String', 'locationName' => 'offeringArn', ], 'OfferingDescription' => [ 'shape' => 'String', 'locationName' => 'offeringDescription', ], 'PricePerUnit' => [ 'shape' => 'String', 'locationName' => 'pricePerUnit', ], 'PriceUnits' => [ 'shape' => 'PriceUnits', 'locationName' => 'priceUnits', ], 'ReservationArn' => [ 'shape' => 'String', 'locationName' => 'reservationArn', ], 'ReservationName' => [ 'shape' => 'String', 'locationName' => 'reservationName', ], 'ReservationState' => [ 'shape' => 'ReservationState', 'locationName' => 'reservationState', ], 'ResourceSpecification' => [ 'shape' => 'ResourceSpecification', 'locationName' => 'resourceSpecification', ], 'Start' => [ 'shape' => 'String', 'locationName' => 'start', ], ], ], 'ReservationArn' => [ 'type' => 'string', ], 'ReservationState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'EXPIRED', 'PROCESSING', 'CANCELED', ], ], 'ResourceSpecification' => [ 'type' => 'structure', 'required' => [ 'ResourceType', ], 'members' => [ 'ReservedBitrate' => [ 'shape' => 'Integer', 'locationName' => 'reservedBitrate', ], 'ResourceType' => [ 'shape' => 'ResourceType', 'locationName' => 'resourceType', ], ], ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'Mbps_Outbound_Bandwidth', ], ], 'RevokeFlowEntitlementRequest' => [ 'type' => 'structure', 'required' => [ 'EntitlementArn', 'FlowArn', ], 'members' => [ 'EntitlementArn' => [ 'shape' => 'RevokeFlowEntitlementRequestEntitlementArnString', 'location' => 'uri', 'locationName' => 'EntitlementArn', ], 'FlowArn' => [ 'shape' => 'RevokeFlowEntitlementRequestFlowArnString', 'location' => 'uri', 'locationName' => 'FlowArn', ], ], ], 'RevokeFlowEntitlementRequestEntitlementArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:entitlement:.+', ], 'RevokeFlowEntitlementRequestFlowArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:flow:.+', ], 'RevokeFlowEntitlementResponse' => [ 'type' => 'structure', 'members' => [ 'EntitlementArn' => [ 'shape' => 'String', 'locationName' => 'entitlementArn', ], 'FlowArn' => [ 'shape' => 'String', 'locationName' => 'flowArn', ], ], ], 'ScanMode' => [ 'type' => 'string', 'enum' => [ 'progressive', 'interlace', 'progressive-segmented-frame', ], ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'SetGatewayBridgeSourceRequest' => [ 'type' => 'structure', 'required' => [ 'BridgeArn', ], 'members' => [ 'BridgeArn' => [ 'shape' => 'SetGatewayBridgeSourceRequestBridgeArnString', 'locationName' => 'bridgeArn', ], 'VpcInterfaceAttachment' => [ 'shape' => 'VpcInterfaceAttachment', 'locationName' => 'vpcInterfaceAttachment', ], ], ], 'SetGatewayBridgeSourceRequestBridgeArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:bridge:.+', ], 'SetSourceRequest' => [ 'type' => 'structure', 'members' => [ 'Decryption' => [ 'shape' => 'Encryption', 'locationName' => 'decryption', ], 'Description' => [ 'shape' => 'String', 'locationName' => 'description', ], 'EntitlementArn' => [ 'shape' => 'SetSourceRequestEntitlementArnString', 'locationName' => 'entitlementArn', ], 'IngestPort' => [ 'shape' => 'Integer', 'locationName' => 'ingestPort', ], 'MaxBitrate' => [ 'shape' => 'Integer', 'locationName' => 'maxBitrate', ], 'MaxLatency' => [ 'shape' => 'Integer', 'locationName' => 'maxLatency', ], 'MaxSyncBuffer' => [ 'shape' => 'Integer', 'locationName' => 'maxSyncBuffer', ], 'MediaStreamSourceConfigurations' => [ 'shape' => '__listOfMediaStreamSourceConfigurationRequest', 'locationName' => 'mediaStreamSourceConfigurations', ], 'MinLatency' => [ 'shape' => 'Integer', 'locationName' => 'minLatency', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'Protocol' => [ 'shape' => 'Protocol', 'locationName' => 'protocol', ], 'SenderControlPort' => [ 'shape' => 'Integer', 'locationName' => 'senderControlPort', ], 'SenderIpAddress' => [ 'shape' => 'String', 'locationName' => 'senderIpAddress', ], 'SourceListenerAddress' => [ 'shape' => 'String', 'locationName' => 'sourceListenerAddress', ], 'SourceListenerPort' => [ 'shape' => 'Integer', 'locationName' => 'sourceListenerPort', ], 'StreamId' => [ 'shape' => 'String', 'locationName' => 'streamId', ], 'VpcInterfaceName' => [ 'shape' => 'String', 'locationName' => 'vpcInterfaceName', ], 'WhitelistCidr' => [ 'shape' => 'String', 'locationName' => 'whitelistCidr', ], 'GatewayBridgeSource' => [ 'shape' => 'SetGatewayBridgeSourceRequest', 'locationName' => 'gatewayBridgeSource', ], ], ], 'SetSourceRequestEntitlementArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:entitlement:.+', ], 'SilentAudio' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'State', 'locationName' => 'state', ], 'ThresholdSeconds' => [ 'shape' => 'Integer', 'locationName' => 'thresholdSeconds', ], ], ], 'Source' => [ 'type' => 'structure', 'required' => [ 'Name', 'SourceArn', ], 'members' => [ 'DataTransferSubscriberFeePercent' => [ 'shape' => 'Integer', 'locationName' => 'dataTransferSubscriberFeePercent', ], 'Decryption' => [ 'shape' => 'Encryption', 'locationName' => 'decryption', ], 'Description' => [ 'shape' => 'String', 'locationName' => 'description', ], 'EntitlementArn' => [ 'shape' => 'String', 'locationName' => 'entitlementArn', ], 'IngestIp' => [ 'shape' => 'String', 'locationName' => 'ingestIp', ], 'IngestPort' => [ 'shape' => 'Integer', 'locationName' => 'ingestPort', ], 'MediaStreamSourceConfigurations' => [ 'shape' => '__listOfMediaStreamSourceConfiguration', 'locationName' => 'mediaStreamSourceConfigurations', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'SenderControlPort' => [ 'shape' => 'Integer', 'locationName' => 'senderControlPort', ], 'SenderIpAddress' => [ 'shape' => 'String', 'locationName' => 'senderIpAddress', ], 'SourceArn' => [ 'shape' => 'String', 'locationName' => 'sourceArn', ], 'Transport' => [ 'shape' => 'Transport', 'locationName' => 'transport', ], 'VpcInterfaceName' => [ 'shape' => 'String', 'locationName' => 'vpcInterfaceName', ], 'WhitelistCidr' => [ 'shape' => 'String', 'locationName' => 'whitelistCidr', ], 'GatewayBridgeSource' => [ 'shape' => 'GatewayBridgeSource', 'locationName' => 'gatewayBridgeSource', ], 'PeerIpAddress' => [ 'shape' => 'String', 'locationName' => 'peerIpAddress', ], ], ], 'SourcePriority' => [ 'type' => 'structure', 'members' => [ 'PrimarySource' => [ 'shape' => 'String', 'locationName' => 'primarySource', ], ], ], 'SourceType' => [ 'type' => 'string', 'enum' => [ 'OWNED', 'ENTITLED', ], ], 'StartFlowRequest' => [ 'type' => 'structure', 'required' => [ 'FlowArn', ], 'members' => [ 'FlowArn' => [ 'shape' => 'StartFlowRequestFlowArnString', 'location' => 'uri', 'locationName' => 'FlowArn', ], ], ], 'StartFlowRequestFlowArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:flow:.+', ], 'StartFlowResponse' => [ 'type' => 'structure', 'members' => [ 'FlowArn' => [ 'shape' => 'String', 'locationName' => 'flowArn', ], 'Status' => [ 'shape' => 'Status', 'locationName' => 'status', ], ], ], 'State' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'Status' => [ 'type' => 'string', 'enum' => [ 'STANDBY', 'ACTIVE', 'UPDATING', 'DELETING', 'STARTING', 'STOPPING', 'ERROR', ], ], 'StopFlowRequest' => [ 'type' => 'structure', 'required' => [ 'FlowArn', ], 'members' => [ 'FlowArn' => [ 'shape' => 'StopFlowRequestFlowArnString', 'location' => 'uri', 'locationName' => 'FlowArn', ], ], ], 'StopFlowRequestFlowArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:flow:.+', ], 'StopFlowResponse' => [ 'type' => 'structure', 'members' => [ 'FlowArn' => [ 'shape' => 'String', 'locationName' => 'flowArn', ], 'Status' => [ 'shape' => 'Status', 'locationName' => 'status', ], ], ], 'String' => [ 'type' => 'string', ], 'SyntheticTimestamp_date_time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => '__mapOfString', 'locationName' => 'tags', ], ], ], 'Tcs' => [ 'type' => 'string', 'enum' => [ 'SDR', 'PQ', 'HLG', 'LINEAR', 'BT2100LINPQ', 'BT2100LINHLG', 'ST2065-1', 'ST428-1', 'DENSITY', ], ], 'ThumbnailDetails' => [ 'type' => 'structure', 'required' => [ 'FlowArn', 'ThumbnailMessages', ], 'members' => [ 'FlowArn' => [ 'shape' => 'String', 'locationName' => 'flowArn', ], 'Thumbnail' => [ 'shape' => 'String', 'locationName' => 'thumbnail', ], 'ThumbnailMessages' => [ 'shape' => '__listOfMessageDetail', 'locationName' => 'thumbnailMessages', ], 'Timecode' => [ 'shape' => 'String', 'locationName' => 'timecode', ], 'Timestamp' => [ 'shape' => 'SyntheticTimestamp_date_time', 'locationName' => 'timestamp', ], ], ], 'ThumbnailState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'TooManyRequestsException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'Transport' => [ 'type' => 'structure', 'required' => [ 'Protocol', ], 'members' => [ 'CidrAllowList' => [ 'shape' => '__listOfString', 'locationName' => 'cidrAllowList', ], 'MaxBitrate' => [ 'shape' => 'Integer', 'locationName' => 'maxBitrate', ], 'MaxLatency' => [ 'shape' => 'Integer', 'locationName' => 'maxLatency', ], 'MaxSyncBuffer' => [ 'shape' => 'Integer', 'locationName' => 'maxSyncBuffer', ], 'MinLatency' => [ 'shape' => 'Integer', 'locationName' => 'minLatency', ], 'Protocol' => [ 'shape' => 'Protocol', 'locationName' => 'protocol', ], 'RemoteId' => [ 'shape' => 'String', 'locationName' => 'remoteId', ], 'SenderControlPort' => [ 'shape' => 'Integer', 'locationName' => 'senderControlPort', ], 'SenderIpAddress' => [ 'shape' => 'String', 'locationName' => 'senderIpAddress', ], 'SmoothingLatency' => [ 'shape' => 'Integer', 'locationName' => 'smoothingLatency', ], 'SourceListenerAddress' => [ 'shape' => 'String', 'locationName' => 'sourceListenerAddress', ], 'SourceListenerPort' => [ 'shape' => 'Integer', 'locationName' => 'sourceListenerPort', ], 'StreamId' => [ 'shape' => 'String', 'locationName' => 'streamId', ], 'NdiSpeedHqQuality' => [ 'shape' => 'Integer', 'locationName' => 'ndiSpeedHqQuality', ], 'NdiProgramName' => [ 'shape' => 'String', 'locationName' => 'ndiProgramName', ], ], ], 'TransportMediaInfo' => [ 'type' => 'structure', 'required' => [ 'Programs', ], 'members' => [ 'Programs' => [ 'shape' => '__listOfTransportStreamProgram', 'locationName' => 'programs', ], ], ], 'TransportStream' => [ 'type' => 'structure', 'required' => [ 'Pid', 'StreamType', ], 'members' => [ 'Channels' => [ 'shape' => 'Integer', 'locationName' => 'channels', ], 'Codec' => [ 'shape' => 'String', 'locationName' => 'codec', ], 'FrameRate' => [ 'shape' => 'String', 'locationName' => 'frameRate', ], 'FrameResolution' => [ 'shape' => 'FrameResolution', 'locationName' => 'frameResolution', ], 'Pid' => [ 'shape' => 'Integer', 'locationName' => 'pid', ], 'SampleRate' => [ 'shape' => 'Integer', 'locationName' => 'sampleRate', ], 'SampleSize' => [ 'shape' => 'Integer', 'locationName' => 'sampleSize', ], 'StreamType' => [ 'shape' => 'String', 'locationName' => 'streamType', ], ], ], 'TransportStreamProgram' => [ 'type' => 'structure', 'required' => [ 'PcrPid', 'ProgramNumber', 'ProgramPid', 'Streams', ], 'members' => [ 'PcrPid' => [ 'shape' => 'Integer', 'locationName' => 'pcrPid', ], 'ProgramName' => [ 'shape' => 'String', 'locationName' => 'programName', ], 'ProgramNumber' => [ 'shape' => 'Integer', 'locationName' => 'programNumber', ], 'ProgramPid' => [ 'shape' => 'Integer', 'locationName' => 'programPid', ], 'Streams' => [ 'shape' => '__listOfTransportStream', 'locationName' => 'streams', ], ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => '__listOfString', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UpdateBridgeFlowSourceRequest' => [ 'type' => 'structure', 'members' => [ 'FlowArn' => [ 'shape' => 'UpdateBridgeFlowSourceRequestFlowArnString', 'locationName' => 'flowArn', ], 'FlowVpcInterfaceAttachment' => [ 'shape' => 'VpcInterfaceAttachment', 'locationName' => 'flowVpcInterfaceAttachment', ], ], ], 'UpdateBridgeFlowSourceRequestFlowArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:flow:.+', ], 'UpdateBridgeNetworkOutputRequest' => [ 'type' => 'structure', 'members' => [ 'IpAddress' => [ 'shape' => 'String', 'locationName' => 'ipAddress', ], 'NetworkName' => [ 'shape' => 'String', 'locationName' => 'networkName', ], 'Port' => [ 'shape' => 'Integer', 'locationName' => 'port', ], 'Protocol' => [ 'shape' => 'Protocol', 'locationName' => 'protocol', ], 'Ttl' => [ 'shape' => 'Integer', 'locationName' => 'ttl', ], ], ], 'UpdateBridgeNetworkSourceRequest' => [ 'type' => 'structure', 'members' => [ 'MulticastIp' => [ 'shape' => 'String', 'locationName' => 'multicastIp', ], 'MulticastSourceSettings' => [ 'shape' => 'MulticastSourceSettings', 'locationName' => 'multicastSourceSettings', ], 'NetworkName' => [ 'shape' => 'String', 'locationName' => 'networkName', ], 'Port' => [ 'shape' => 'Integer', 'locationName' => 'port', ], 'Protocol' => [ 'shape' => 'Protocol', 'locationName' => 'protocol', ], ], ], 'UpdateBridgeOutputRequest' => [ 'type' => 'structure', 'required' => [ 'BridgeArn', 'OutputName', ], 'members' => [ 'BridgeArn' => [ 'shape' => 'UpdateBridgeOutputRequestBridgeArnString', 'location' => 'uri', 'locationName' => 'BridgeArn', ], 'NetworkOutput' => [ 'shape' => 'UpdateBridgeNetworkOutputRequest', 'locationName' => 'networkOutput', ], 'OutputName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'OutputName', ], ], ], 'UpdateBridgeOutputRequestBridgeArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:bridge:.+', ], 'UpdateBridgeOutputResponse' => [ 'type' => 'structure', 'members' => [ 'BridgeArn' => [ 'shape' => 'String', 'locationName' => 'bridgeArn', ], 'Output' => [ 'shape' => 'BridgeOutput', 'locationName' => 'output', ], ], ], 'UpdateBridgeRequest' => [ 'type' => 'structure', 'required' => [ 'BridgeArn', ], 'members' => [ 'BridgeArn' => [ 'shape' => 'UpdateBridgeRequestBridgeArnString', 'location' => 'uri', 'locationName' => 'BridgeArn', ], 'EgressGatewayBridge' => [ 'shape' => 'UpdateEgressGatewayBridgeRequest', 'locationName' => 'egressGatewayBridge', ], 'IngressGatewayBridge' => [ 'shape' => 'UpdateIngressGatewayBridgeRequest', 'locationName' => 'ingressGatewayBridge', ], 'SourceFailoverConfig' => [ 'shape' => 'UpdateFailoverConfig', 'locationName' => 'sourceFailoverConfig', ], ], ], 'UpdateBridgeRequestBridgeArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:bridge:.+', ], 'UpdateBridgeResponse' => [ 'type' => 'structure', 'members' => [ 'Bridge' => [ 'shape' => 'Bridge', 'locationName' => 'bridge', ], ], ], 'UpdateBridgeSourceRequest' => [ 'type' => 'structure', 'required' => [ 'BridgeArn', 'SourceName', ], 'members' => [ 'BridgeArn' => [ 'shape' => 'UpdateBridgeSourceRequestBridgeArnString', 'location' => 'uri', 'locationName' => 'BridgeArn', ], 'FlowSource' => [ 'shape' => 'UpdateBridgeFlowSourceRequest', 'locationName' => 'flowSource', ], 'NetworkSource' => [ 'shape' => 'UpdateBridgeNetworkSourceRequest', 'locationName' => 'networkSource', ], 'SourceName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'SourceName', ], ], ], 'UpdateBridgeSourceRequestBridgeArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:bridge:.+', ], 'UpdateBridgeSourceResponse' => [ 'type' => 'structure', 'members' => [ 'BridgeArn' => [ 'shape' => 'String', 'locationName' => 'bridgeArn', ], 'Source' => [ 'shape' => 'BridgeSource', 'locationName' => 'source', ], ], ], 'UpdateBridgeStateRequest' => [ 'type' => 'structure', 'required' => [ 'BridgeArn', 'DesiredState', ], 'members' => [ 'BridgeArn' => [ 'shape' => 'UpdateBridgeStateRequestBridgeArnString', 'location' => 'uri', 'locationName' => 'BridgeArn', ], 'DesiredState' => [ 'shape' => 'DesiredState', 'locationName' => 'desiredState', ], ], ], 'UpdateBridgeStateRequestBridgeArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:bridge:.+', ], 'UpdateBridgeStateResponse' => [ 'type' => 'structure', 'members' => [ 'BridgeArn' => [ 'shape' => 'String', 'locationName' => 'bridgeArn', ], 'DesiredState' => [ 'shape' => 'DesiredState', 'locationName' => 'desiredState', ], ], ], 'UpdateEgressGatewayBridgeRequest' => [ 'type' => 'structure', 'members' => [ 'MaxBitrate' => [ 'shape' => 'Integer', 'locationName' => 'maxBitrate', ], ], ], 'UpdateEncryption' => [ 'type' => 'structure', 'members' => [ 'Algorithm' => [ 'shape' => 'Algorithm', 'locationName' => 'algorithm', ], 'ConstantInitializationVector' => [ 'shape' => 'String', 'locationName' => 'constantInitializationVector', ], 'DeviceId' => [ 'shape' => 'String', 'locationName' => 'deviceId', ], 'KeyType' => [ 'shape' => 'KeyType', 'locationName' => 'keyType', ], 'Region' => [ 'shape' => 'String', 'locationName' => 'region', ], 'ResourceId' => [ 'shape' => 'String', 'locationName' => 'resourceId', ], 'RoleArn' => [ 'shape' => 'String', 'locationName' => 'roleArn', ], 'SecretArn' => [ 'shape' => 'String', 'locationName' => 'secretArn', ], 'Url' => [ 'shape' => 'String', 'locationName' => 'url', ], ], ], 'UpdateFailoverConfig' => [ 'type' => 'structure', 'members' => [ 'FailoverMode' => [ 'shape' => 'FailoverMode', 'locationName' => 'failoverMode', ], 'RecoveryWindow' => [ 'shape' => 'Integer', 'locationName' => 'recoveryWindow', ], 'SourcePriority' => [ 'shape' => 'SourcePriority', 'locationName' => 'sourcePriority', ], 'State' => [ 'shape' => 'State', 'locationName' => 'state', ], ], ], 'UpdateFlowEntitlementRequest' => [ 'type' => 'structure', 'required' => [ 'EntitlementArn', 'FlowArn', ], 'members' => [ 'Description' => [ 'shape' => 'String', 'locationName' => 'description', ], 'Encryption' => [ 'shape' => 'UpdateEncryption', 'locationName' => 'encryption', ], 'EntitlementArn' => [ 'shape' => 'UpdateFlowEntitlementRequestEntitlementArnString', 'location' => 'uri', 'locationName' => 'EntitlementArn', ], 'EntitlementStatus' => [ 'shape' => 'EntitlementStatus', 'locationName' => 'entitlementStatus', ], 'FlowArn' => [ 'shape' => 'UpdateFlowEntitlementRequestFlowArnString', 'location' => 'uri', 'locationName' => 'FlowArn', ], 'Subscribers' => [ 'shape' => '__listOfString', 'locationName' => 'subscribers', ], ], ], 'UpdateFlowEntitlementRequestEntitlementArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:entitlement:.+', ], 'UpdateFlowEntitlementRequestFlowArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:flow:.+', ], 'UpdateFlowEntitlementResponse' => [ 'type' => 'structure', 'members' => [ 'Entitlement' => [ 'shape' => 'Entitlement', 'locationName' => 'entitlement', ], 'FlowArn' => [ 'shape' => 'String', 'locationName' => 'flowArn', ], ], ], 'UpdateFlowMediaStreamRequest' => [ 'type' => 'structure', 'required' => [ 'FlowArn', 'MediaStreamName', ], 'members' => [ 'Attributes' => [ 'shape' => 'MediaStreamAttributesRequest', 'locationName' => 'attributes', ], 'ClockRate' => [ 'shape' => 'Integer', 'locationName' => 'clockRate', ], 'Description' => [ 'shape' => 'String', 'locationName' => 'description', ], 'FlowArn' => [ 'shape' => 'UpdateFlowMediaStreamRequestFlowArnString', 'location' => 'uri', 'locationName' => 'FlowArn', ], 'MediaStreamName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'MediaStreamName', ], 'MediaStreamType' => [ 'shape' => 'MediaStreamType', 'locationName' => 'mediaStreamType', ], 'VideoFormat' => [ 'shape' => 'String', 'locationName' => 'videoFormat', ], ], ], 'UpdateFlowMediaStreamRequestFlowArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:flow:.+', ], 'UpdateFlowMediaStreamResponse' => [ 'type' => 'structure', 'members' => [ 'FlowArn' => [ 'shape' => 'String', 'locationName' => 'flowArn', ], 'MediaStream' => [ 'shape' => 'MediaStream', 'locationName' => 'mediaStream', ], ], ], 'UpdateFlowOutputRequest' => [ 'type' => 'structure', 'required' => [ 'FlowArn', 'OutputArn', ], 'members' => [ 'CidrAllowList' => [ 'shape' => '__listOfString', 'locationName' => 'cidrAllowList', ], 'Description' => [ 'shape' => 'String', 'locationName' => 'description', ], 'Destination' => [ 'shape' => 'String', 'locationName' => 'destination', ], 'Encryption' => [ 'shape' => 'UpdateEncryption', 'locationName' => 'encryption', ], 'FlowArn' => [ 'shape' => 'UpdateFlowOutputRequestFlowArnString', 'location' => 'uri', 'locationName' => 'FlowArn', ], 'MaxLatency' => [ 'shape' => 'Integer', 'locationName' => 'maxLatency', ], 'MediaStreamOutputConfigurations' => [ 'shape' => '__listOfMediaStreamOutputConfigurationRequest', 'locationName' => 'mediaStreamOutputConfigurations', ], 'MinLatency' => [ 'shape' => 'Integer', 'locationName' => 'minLatency', ], 'OutputArn' => [ 'shape' => 'UpdateFlowOutputRequestOutputArnString', 'location' => 'uri', 'locationName' => 'OutputArn', ], 'Port' => [ 'shape' => 'Integer', 'locationName' => 'port', ], 'Protocol' => [ 'shape' => 'Protocol', 'locationName' => 'protocol', ], 'RemoteId' => [ 'shape' => 'String', 'locationName' => 'remoteId', ], 'SenderControlPort' => [ 'shape' => 'Integer', 'locationName' => 'senderControlPort', ], 'SenderIpAddress' => [ 'shape' => 'String', 'locationName' => 'senderIpAddress', ], 'SmoothingLatency' => [ 'shape' => 'Integer', 'locationName' => 'smoothingLatency', ], 'StreamId' => [ 'shape' => 'String', 'locationName' => 'streamId', ], 'VpcInterfaceAttachment' => [ 'shape' => 'VpcInterfaceAttachment', 'locationName' => 'vpcInterfaceAttachment', ], 'OutputStatus' => [ 'shape' => 'OutputStatus', 'locationName' => 'outputStatus', ], 'NdiProgramName' => [ 'shape' => 'String', 'locationName' => 'ndiProgramName', ], 'NdiSpeedHqQuality' => [ 'shape' => 'Integer', 'locationName' => 'ndiSpeedHqQuality', ], ], ], 'UpdateFlowOutputRequestFlowArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:flow:.+', ], 'UpdateFlowOutputRequestOutputArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:output:.+', ], 'UpdateFlowOutputResponse' => [ 'type' => 'structure', 'members' => [ 'FlowArn' => [ 'shape' => 'String', 'locationName' => 'flowArn', ], 'Output' => [ 'shape' => 'Output', 'locationName' => 'output', ], ], ], 'UpdateFlowRequest' => [ 'type' => 'structure', 'required' => [ 'FlowArn', ], 'members' => [ 'FlowArn' => [ 'shape' => 'UpdateFlowRequestFlowArnString', 'location' => 'uri', 'locationName' => 'FlowArn', ], 'SourceFailoverConfig' => [ 'shape' => 'UpdateFailoverConfig', 'locationName' => 'sourceFailoverConfig', ], 'Maintenance' => [ 'shape' => 'UpdateMaintenance', 'locationName' => 'maintenance', ], 'SourceMonitoringConfig' => [ 'shape' => 'MonitoringConfig', 'locationName' => 'sourceMonitoringConfig', ], 'NdiConfig' => [ 'shape' => 'NdiConfig', 'locationName' => 'ndiConfig', ], ], ], 'UpdateFlowRequestFlowArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:flow:.+', ], 'UpdateFlowResponse' => [ 'type' => 'structure', 'members' => [ 'Flow' => [ 'shape' => 'Flow', 'locationName' => 'flow', ], ], ], 'UpdateFlowSourceRequest' => [ 'type' => 'structure', 'required' => [ 'FlowArn', 'SourceArn', ], 'members' => [ 'Decryption' => [ 'shape' => 'UpdateEncryption', 'locationName' => 'decryption', ], 'Description' => [ 'shape' => 'String', 'locationName' => 'description', ], 'EntitlementArn' => [ 'shape' => 'UpdateFlowSourceRequestEntitlementArnString', 'locationName' => 'entitlementArn', ], 'FlowArn' => [ 'shape' => 'UpdateFlowSourceRequestFlowArnString', 'location' => 'uri', 'locationName' => 'FlowArn', ], 'IngestPort' => [ 'shape' => 'Integer', 'locationName' => 'ingestPort', ], 'MaxBitrate' => [ 'shape' => 'Integer', 'locationName' => 'maxBitrate', ], 'MaxLatency' => [ 'shape' => 'Integer', 'locationName' => 'maxLatency', ], 'MaxSyncBuffer' => [ 'shape' => 'Integer', 'locationName' => 'maxSyncBuffer', ], 'MediaStreamSourceConfigurations' => [ 'shape' => '__listOfMediaStreamSourceConfigurationRequest', 'locationName' => 'mediaStreamSourceConfigurations', ], 'MinLatency' => [ 'shape' => 'Integer', 'locationName' => 'minLatency', ], 'Protocol' => [ 'shape' => 'Protocol', 'locationName' => 'protocol', ], 'SenderControlPort' => [ 'shape' => 'Integer', 'locationName' => 'senderControlPort', ], 'SenderIpAddress' => [ 'shape' => 'String', 'locationName' => 'senderIpAddress', ], 'SourceArn' => [ 'shape' => 'UpdateFlowSourceRequestSourceArnString', 'location' => 'uri', 'locationName' => 'SourceArn', ], 'SourceListenerAddress' => [ 'shape' => 'String', 'locationName' => 'sourceListenerAddress', ], 'SourceListenerPort' => [ 'shape' => 'Integer', 'locationName' => 'sourceListenerPort', ], 'StreamId' => [ 'shape' => 'String', 'locationName' => 'streamId', ], 'VpcInterfaceName' => [ 'shape' => 'String', 'locationName' => 'vpcInterfaceName', ], 'WhitelistCidr' => [ 'shape' => 'String', 'locationName' => 'whitelistCidr', ], 'GatewayBridgeSource' => [ 'shape' => 'UpdateGatewayBridgeSourceRequest', 'locationName' => 'gatewayBridgeSource', ], ], ], 'UpdateFlowSourceRequestEntitlementArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:entitlement:.+', ], 'UpdateFlowSourceRequestFlowArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:flow:.+', ], 'UpdateFlowSourceRequestSourceArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:source:.+', ], 'UpdateFlowSourceResponse' => [ 'type' => 'structure', 'members' => [ 'FlowArn' => [ 'shape' => 'String', 'locationName' => 'flowArn', ], 'Source' => [ 'shape' => 'Source', 'locationName' => 'source', ], ], ], 'UpdateGatewayBridgeSourceRequest' => [ 'type' => 'structure', 'members' => [ 'BridgeArn' => [ 'shape' => 'UpdateGatewayBridgeSourceRequestBridgeArnString', 'locationName' => 'bridgeArn', ], 'VpcInterfaceAttachment' => [ 'shape' => 'VpcInterfaceAttachment', 'locationName' => 'vpcInterfaceAttachment', ], ], ], 'UpdateGatewayBridgeSourceRequestBridgeArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:bridge:.+', ], 'UpdateGatewayInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'GatewayInstanceArn', ], 'members' => [ 'BridgePlacement' => [ 'shape' => 'BridgePlacement', 'locationName' => 'bridgePlacement', ], 'GatewayInstanceArn' => [ 'shape' => 'UpdateGatewayInstanceRequestGatewayInstanceArnString', 'location' => 'uri', 'locationName' => 'GatewayInstanceArn', ], ], ], 'UpdateGatewayInstanceRequestGatewayInstanceArnString' => [ 'type' => 'string', 'pattern' => 'arn:.+:mediaconnect.+:gateway:.+:instance:.+', ], 'UpdateGatewayInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'BridgePlacement' => [ 'shape' => 'BridgePlacement', 'locationName' => 'bridgePlacement', ], 'GatewayInstanceArn' => [ 'shape' => 'String', 'locationName' => 'gatewayInstanceArn', ], ], ], 'UpdateIngressGatewayBridgeRequest' => [ 'type' => 'structure', 'members' => [ 'MaxBitrate' => [ 'shape' => 'Integer', 'locationName' => 'maxBitrate', ], 'MaxOutputs' => [ 'shape' => 'Integer', 'locationName' => 'maxOutputs', ], ], ], 'UpdateMaintenance' => [ 'type' => 'structure', 'members' => [ 'MaintenanceDay' => [ 'shape' => 'MaintenanceDay', 'locationName' => 'maintenanceDay', ], 'MaintenanceScheduledDate' => [ 'shape' => 'String', 'locationName' => 'maintenanceScheduledDate', ], 'MaintenanceStartHour' => [ 'shape' => 'String', 'locationName' => 'maintenanceStartHour', ], ], ], 'VideoMonitoringSetting' => [ 'type' => 'structure', 'members' => [ 'BlackFrames' => [ 'shape' => 'BlackFrames', 'locationName' => 'blackFrames', ], 'FrozenFrames' => [ 'shape' => 'FrozenFrames', 'locationName' => 'frozenFrames', ], ], ], 'VpcInterface' => [ 'type' => 'structure', 'required' => [ 'Name', 'NetworkInterfaceIds', 'NetworkInterfaceType', 'RoleArn', 'SecurityGroupIds', 'SubnetId', ], 'members' => [ 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'NetworkInterfaceIds' => [ 'shape' => '__listOfString', 'locationName' => 'networkInterfaceIds', ], 'NetworkInterfaceType' => [ 'shape' => 'NetworkInterfaceType', 'locationName' => 'networkInterfaceType', ], 'RoleArn' => [ 'shape' => 'String', 'locationName' => 'roleArn', ], 'SecurityGroupIds' => [ 'shape' => '__listOfString', 'locationName' => 'securityGroupIds', ], 'SubnetId' => [ 'shape' => 'String', 'locationName' => 'subnetId', ], ], ], 'VpcInterfaceAttachment' => [ 'type' => 'structure', 'members' => [ 'VpcInterfaceName' => [ 'shape' => 'String', 'locationName' => 'vpcInterfaceName', ], ], ], 'VpcInterfaceRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RoleArn', 'SecurityGroupIds', 'SubnetId', ], 'members' => [ 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'NetworkInterfaceType' => [ 'shape' => 'NetworkInterfaceType', 'locationName' => 'networkInterfaceType', ], 'RoleArn' => [ 'shape' => 'String', 'locationName' => 'roleArn', ], 'SecurityGroupIds' => [ 'shape' => '__listOfString', 'locationName' => 'securityGroupIds', ], 'SubnetId' => [ 'shape' => 'String', 'locationName' => 'subnetId', ], ], ], '__listOfAddBridgeOutputRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddBridgeOutputRequest', ], ], '__listOfAddBridgeSourceRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddBridgeSourceRequest', ], ], '__listOfAddMediaStreamRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddMediaStreamRequest', ], ], '__listOfAddOutputRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddOutputRequest', ], ], '__listOfAudioMonitoringSetting' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudioMonitoringSetting', ], ], '__listOfBridgeOutput' => [ 'type' => 'list', 'member' => [ 'shape' => 'BridgeOutput', ], ], '__listOfBridgeSource' => [ 'type' => 'list', 'member' => [ 'shape' => 'BridgeSource', ], ], '__listOfDestinationConfiguration' => [ 'type' => 'list', 'member' => [ 'shape' => 'DestinationConfiguration', ], ], '__listOfDestinationConfigurationRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'DestinationConfigurationRequest', ], ], '__listOfEntitlement' => [ 'type' => 'list', 'member' => [ 'shape' => 'Entitlement', ], ], '__listOfGatewayNetwork' => [ 'type' => 'list', 'member' => [ 'shape' => 'GatewayNetwork', ], ], '__listOfGrantEntitlementRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'GrantEntitlementRequest', ], ], '__listOfInputConfiguration' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputConfiguration', ], ], '__listOfInputConfigurationRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputConfigurationRequest', ], ], '__listOfInteger' => [ 'type' => 'list', 'member' => [ 'shape' => 'Integer', ], ], '__listOfListedBridge' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListedBridge', ], ], '__listOfListedEntitlement' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListedEntitlement', ], ], '__listOfListedFlow' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListedFlow', ], ], '__listOfListedGateway' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListedGateway', ], ], '__listOfListedGatewayInstance' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListedGatewayInstance', ], ], '__listOfMediaStream' => [ 'type' => 'list', 'member' => [ 'shape' => 'MediaStream', ], ], '__listOfMediaStreamOutputConfiguration' => [ 'type' => 'list', 'member' => [ 'shape' => 'MediaStreamOutputConfiguration', ], ], '__listOfMediaStreamOutputConfigurationRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'MediaStreamOutputConfigurationRequest', ], ], '__listOfMediaStreamSourceConfiguration' => [ 'type' => 'list', 'member' => [ 'shape' => 'MediaStreamSourceConfiguration', ], ], '__listOfMediaStreamSourceConfigurationRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'MediaStreamSourceConfigurationRequest', ], ], '__listOfMessageDetail' => [ 'type' => 'list', 'member' => [ 'shape' => 'MessageDetail', ], ], '__listOfNdiDiscoveryServerConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'NdiDiscoveryServerConfig', ], ], '__listOfOffering' => [ 'type' => 'list', 'member' => [ 'shape' => 'Offering', ], ], '__listOfOutput' => [ 'type' => 'list', 'member' => [ 'shape' => 'Output', ], ], '__listOfReservation' => [ 'type' => 'list', 'member' => [ 'shape' => 'Reservation', ], ], '__listOfSetSourceRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'SetSourceRequest', ], ], '__listOfSource' => [ 'type' => 'list', 'member' => [ 'shape' => 'Source', ], ], '__listOfString' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], '__listOfTransportStream' => [ 'type' => 'list', 'member' => [ 'shape' => 'TransportStream', ], ], '__listOfTransportStreamProgram' => [ 'type' => 'list', 'member' => [ 'shape' => 'TransportStreamProgram', ], ], '__listOfVideoMonitoringSetting' => [ 'type' => 'list', 'member' => [ 'shape' => 'VideoMonitoringSetting', ], ], '__listOfVpcInterface' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcInterface', ], ], '__listOfVpcInterfaceRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcInterfaceRequest', ], ], '__mapOfString' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], ],];
