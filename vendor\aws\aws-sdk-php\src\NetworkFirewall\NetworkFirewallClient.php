<?php
namespace Aws\NetworkFirewall;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS Network Firewall** service.
 * @method \Aws\Result acceptNetworkFirewallTransitGatewayAttachment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise acceptNetworkFirewallTransitGatewayAttachmentAsync(array $args = [])
 * @method \Aws\Result associateAvailabilityZones(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateAvailabilityZonesAsync(array $args = [])
 * @method \Aws\Result associateFirewallPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateFirewallPolicyAsync(array $args = [])
 * @method \Aws\Result associateSubnets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateSubnetsAsync(array $args = [])
 * @method \Aws\Result createFirewall(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createFirewallAsync(array $args = [])
 * @method \Aws\Result createFirewallPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createFirewallPolicyAsync(array $args = [])
 * @method \Aws\Result createRuleGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createRuleGroupAsync(array $args = [])
 * @method \Aws\Result createTLSInspectionConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createTLSInspectionConfigurationAsync(array $args = [])
 * @method \Aws\Result createVpcEndpointAssociation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createVpcEndpointAssociationAsync(array $args = [])
 * @method \Aws\Result deleteFirewall(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteFirewallAsync(array $args = [])
 * @method \Aws\Result deleteFirewallPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteFirewallPolicyAsync(array $args = [])
 * @method \Aws\Result deleteNetworkFirewallTransitGatewayAttachment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteNetworkFirewallTransitGatewayAttachmentAsync(array $args = [])
 * @method \Aws\Result deleteResourcePolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteResourcePolicyAsync(array $args = [])
 * @method \Aws\Result deleteRuleGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteRuleGroupAsync(array $args = [])
 * @method \Aws\Result deleteTLSInspectionConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteTLSInspectionConfigurationAsync(array $args = [])
 * @method \Aws\Result deleteVpcEndpointAssociation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteVpcEndpointAssociationAsync(array $args = [])
 * @method \Aws\Result describeFirewall(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeFirewallAsync(array $args = [])
 * @method \Aws\Result describeFirewallMetadata(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeFirewallMetadataAsync(array $args = [])
 * @method \Aws\Result describeFirewallPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeFirewallPolicyAsync(array $args = [])
 * @method \Aws\Result describeFlowOperation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeFlowOperationAsync(array $args = [])
 * @method \Aws\Result describeLoggingConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeLoggingConfigurationAsync(array $args = [])
 * @method \Aws\Result describeResourcePolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeResourcePolicyAsync(array $args = [])
 * @method \Aws\Result describeRuleGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeRuleGroupAsync(array $args = [])
 * @method \Aws\Result describeRuleGroupMetadata(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeRuleGroupMetadataAsync(array $args = [])
 * @method \Aws\Result describeRuleGroupSummary(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeRuleGroupSummaryAsync(array $args = [])
 * @method \Aws\Result describeTLSInspectionConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeTLSInspectionConfigurationAsync(array $args = [])
 * @method \Aws\Result describeVpcEndpointAssociation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeVpcEndpointAssociationAsync(array $args = [])
 * @method \Aws\Result disassociateAvailabilityZones(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateAvailabilityZonesAsync(array $args = [])
 * @method \Aws\Result disassociateSubnets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateSubnetsAsync(array $args = [])
 * @method \Aws\Result getAnalysisReportResults(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAnalysisReportResultsAsync(array $args = [])
 * @method \Aws\Result listAnalysisReports(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAnalysisReportsAsync(array $args = [])
 * @method \Aws\Result listFirewallPolicies(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listFirewallPoliciesAsync(array $args = [])
 * @method \Aws\Result listFirewalls(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listFirewallsAsync(array $args = [])
 * @method \Aws\Result listFlowOperationResults(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listFlowOperationResultsAsync(array $args = [])
 * @method \Aws\Result listFlowOperations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listFlowOperationsAsync(array $args = [])
 * @method \Aws\Result listRuleGroups(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listRuleGroupsAsync(array $args = [])
 * @method \Aws\Result listTLSInspectionConfigurations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTLSInspectionConfigurationsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result listVpcEndpointAssociations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listVpcEndpointAssociationsAsync(array $args = [])
 * @method \Aws\Result putResourcePolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putResourcePolicyAsync(array $args = [])
 * @method \Aws\Result rejectNetworkFirewallTransitGatewayAttachment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise rejectNetworkFirewallTransitGatewayAttachmentAsync(array $args = [])
 * @method \Aws\Result startAnalysisReport(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startAnalysisReportAsync(array $args = [])
 * @method \Aws\Result startFlowCapture(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startFlowCaptureAsync(array $args = [])
 * @method \Aws\Result startFlowFlush(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startFlowFlushAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateAvailabilityZoneChangeProtection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateAvailabilityZoneChangeProtectionAsync(array $args = [])
 * @method \Aws\Result updateFirewallAnalysisSettings(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateFirewallAnalysisSettingsAsync(array $args = [])
 * @method \Aws\Result updateFirewallDeleteProtection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateFirewallDeleteProtectionAsync(array $args = [])
 * @method \Aws\Result updateFirewallDescription(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateFirewallDescriptionAsync(array $args = [])
 * @method \Aws\Result updateFirewallEncryptionConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateFirewallEncryptionConfigurationAsync(array $args = [])
 * @method \Aws\Result updateFirewallPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateFirewallPolicyAsync(array $args = [])
 * @method \Aws\Result updateFirewallPolicyChangeProtection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateFirewallPolicyChangeProtectionAsync(array $args = [])
 * @method \Aws\Result updateLoggingConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateLoggingConfigurationAsync(array $args = [])
 * @method \Aws\Result updateRuleGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateRuleGroupAsync(array $args = [])
 * @method \Aws\Result updateSubnetChangeProtection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateSubnetChangeProtectionAsync(array $args = [])
 * @method \Aws\Result updateTLSInspectionConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateTLSInspectionConfigurationAsync(array $args = [])
 */
class NetworkFirewallClient extends AwsClient {}
