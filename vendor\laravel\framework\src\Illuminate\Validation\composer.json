{"name": "illuminate/validation", "description": "The Illuminate Validation package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "ext-filter": "*", "ext-mbstring": "*", "brick/math": "^0.9.3|^0.10.2|^0.11|^0.12", "egulias/email-validator": "^3.2.5|^4.0", "illuminate/collections": "^11.0", "illuminate/container": "^11.0", "illuminate/contracts": "^11.0", "illuminate/macroable": "^11.0", "illuminate/support": "^11.0", "illuminate/translation": "^11.0", "symfony/http-foundation": "^7.0", "symfony/mime": "^7.0"}, "autoload": {"psr-4": {"Illuminate\\Validation\\": ""}}, "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "suggest": {"illuminate/database": "Required to use the database presence verifier (^11.0)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}