<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\FlexApi\V2;

use Twilio\Options;
use Twilio\Values;

abstract class FlexUserOptions
{

    /**
     * @param string $email Email of the User.
     * @param string $userSid The unique SID identifier of the Twilio Unified User.
     * @param string $locale The locale preference of the user.
     * @return UpdateFlexUserOptions Options builder
     */
    public static function update(
        
        string $email = Values::NONE,
        string $userSid = Values::NONE,
        string $locale = Values::NONE

    ): UpdateFlexUserOptions
    {
        return new UpdateFlexUserOptions(
            $email,
            $userSid,
            $locale
        );
    }

}


class UpdateFlexUserOptions extends Options
    {
    /**
     * @param string $email Email of the User.
     * @param string $userSid The unique SID identifier of the Twilio Unified User.
     * @param string $locale The locale preference of the user.
     */
    public function __construct(
        
        string $email = Values::NONE,
        string $userSid = Values::NONE,
        string $locale = Values::NONE

    ) {
        $this->options['email'] = $email;
        $this->options['userSid'] = $userSid;
        $this->options['locale'] = $locale;
    }

    /**
     * Email of the User.
     *
     * @param string $email Email of the User.
     * @return $this Fluent Builder
     */
    public function setEmail(string $email): self
    {
        $this->options['email'] = $email;
        return $this;
    }

    /**
     * The unique SID identifier of the Twilio Unified User.
     *
     * @param string $userSid The unique SID identifier of the Twilio Unified User.
     * @return $this Fluent Builder
     */
    public function setUserSid(string $userSid): self
    {
        $this->options['userSid'] = $userSid;
        return $this;
    }

    /**
     * The locale preference of the user.
     *
     * @param string $locale The locale preference of the user.
     * @return $this Fluent Builder
     */
    public function setLocale(string $locale): self
    {
        $this->options['locale'] = $locale;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.FlexApi.V2.UpdateFlexUserOptions ' . $options . ']';
    }
}

