<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Insights
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Insights\V1\Call;

use Twilio\Exceptions\TwilioException;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\InstanceContext;
use Twilio\Serialize;


class AnnotationContext extends InstanceContext
    {
    /**
     * Initialize the AnnotationContext
     *
     * @param Version $version Version that contains the resource
     * @param string $callSid The unique SID identifier of the Call.
     */
    public function __construct(
        Version $version,
        $callSid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'callSid' =>
            $callSid,
        ];

        $this->uri = '/Voice/' . \rawurlencode($callSid)
        .'/Annotation';
    }

    /**
     * Fetch the AnnotationInstance
     *
     * @return AnnotationInstance Fetched AnnotationInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): AnnotationInstance
    {

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json' ]);
        $payload = $this->version->fetch('GET', $this->uri, [], [], $headers);

        return new AnnotationInstance(
            $this->version,
            $payload,
            $this->solution['callSid']
        );
    }


    /**
     * Update the AnnotationInstance
     *
     * @param array|Options $options Optional Arguments
     * @return AnnotationInstance Updated AnnotationInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(array $options = []): AnnotationInstance
    {

        $options = new Values($options);

        $data = Values::of([
            'AnsweredBy' =>
                $options['answeredBy'],
            'ConnectivityIssue' =>
                $options['connectivityIssue'],
            'QualityIssues' =>
                $options['qualityIssues'],
            'Spam' =>
                Serialize::booleanToString($options['spam']),
            'CallScore' =>
                $options['callScore'],
            'Comment' =>
                $options['comment'],
            'Incident' =>
                $options['incident'],
        ]);

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json' ]);
        $payload = $this->version->update('POST', $this->uri, [], $data, $headers);

        return new AnnotationInstance(
            $this->version,
            $payload,
            $this->solution['callSid']
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Insights.V1.AnnotationContext ' . \implode(' ', $context) . ']';
    }
}
