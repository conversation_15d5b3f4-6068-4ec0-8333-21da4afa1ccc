<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Trunking
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Trunking\V1\Trunk;

use Twilio\Options;
use Twilio\Values;

abstract class OriginationUrlOptions
{




    /**
     * @param int $weight The value that determines the relative share of the load the URI should receive compared to other URIs with the same priority. Can be an integer from 1 to 65535, inclusive, and the default is 10. URLs with higher values receive more load than those with lower ones with the same priority.
     * @param int $priority The relative importance of the URI. Can be an integer from 0 to 65535, inclusive, and the default is 10. The lowest number represents the most important URI.
     * @param bool $enabled Whether the URL is enabled. The default is `true`.
     * @param string $friendlyName A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     * @param string $sipUrl The SIP address you want Twilio to route your Origination calls to. This must be a `sip:` schema. `sips` is NOT supported.
     * @return UpdateOriginationUrlOptions Options builder
     */
    public static function update(
        
        int $weight = Values::INT_NONE,
        int $priority = Values::INT_NONE,
        bool $enabled = Values::BOOL_NONE,
        string $friendlyName = Values::NONE,
        string $sipUrl = Values::NONE

    ): UpdateOriginationUrlOptions
    {
        return new UpdateOriginationUrlOptions(
            $weight,
            $priority,
            $enabled,
            $friendlyName,
            $sipUrl
        );
    }

}





class UpdateOriginationUrlOptions extends Options
    {
    /**
     * @param int $weight The value that determines the relative share of the load the URI should receive compared to other URIs with the same priority. Can be an integer from 1 to 65535, inclusive, and the default is 10. URLs with higher values receive more load than those with lower ones with the same priority.
     * @param int $priority The relative importance of the URI. Can be an integer from 0 to 65535, inclusive, and the default is 10. The lowest number represents the most important URI.
     * @param bool $enabled Whether the URL is enabled. The default is `true`.
     * @param string $friendlyName A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     * @param string $sipUrl The SIP address you want Twilio to route your Origination calls to. This must be a `sip:` schema. `sips` is NOT supported.
     */
    public function __construct(
        
        int $weight = Values::INT_NONE,
        int $priority = Values::INT_NONE,
        bool $enabled = Values::BOOL_NONE,
        string $friendlyName = Values::NONE,
        string $sipUrl = Values::NONE

    ) {
        $this->options['weight'] = $weight;
        $this->options['priority'] = $priority;
        $this->options['enabled'] = $enabled;
        $this->options['friendlyName'] = $friendlyName;
        $this->options['sipUrl'] = $sipUrl;
    }

    /**
     * The value that determines the relative share of the load the URI should receive compared to other URIs with the same priority. Can be an integer from 1 to 65535, inclusive, and the default is 10. URLs with higher values receive more load than those with lower ones with the same priority.
     *
     * @param int $weight The value that determines the relative share of the load the URI should receive compared to other URIs with the same priority. Can be an integer from 1 to 65535, inclusive, and the default is 10. URLs with higher values receive more load than those with lower ones with the same priority.
     * @return $this Fluent Builder
     */
    public function setWeight(int $weight): self
    {
        $this->options['weight'] = $weight;
        return $this;
    }

    /**
     * The relative importance of the URI. Can be an integer from 0 to 65535, inclusive, and the default is 10. The lowest number represents the most important URI.
     *
     * @param int $priority The relative importance of the URI. Can be an integer from 0 to 65535, inclusive, and the default is 10. The lowest number represents the most important URI.
     * @return $this Fluent Builder
     */
    public function setPriority(int $priority): self
    {
        $this->options['priority'] = $priority;
        return $this;
    }

    /**
     * Whether the URL is enabled. The default is `true`.
     *
     * @param bool $enabled Whether the URL is enabled. The default is `true`.
     * @return $this Fluent Builder
     */
    public function setEnabled(bool $enabled): self
    {
        $this->options['enabled'] = $enabled;
        return $this;
    }

    /**
     * A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     *
     * @param string $friendlyName A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * The SIP address you want Twilio to route your Origination calls to. This must be a `sip:` schema. `sips` is NOT supported.
     *
     * @param string $sipUrl The SIP address you want Twilio to route your Origination calls to. This must be a `sip:` schema. `sips` is NOT supported.
     * @return $this Fluent Builder
     */
    public function setSipUrl(string $sipUrl): self
    {
        $this->options['sipUrl'] = $sipUrl;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Trunking.V1.UpdateOriginationUrlOptions ' . $options . ']';
    }
}

