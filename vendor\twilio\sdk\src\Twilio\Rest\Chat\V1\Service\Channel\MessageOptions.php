<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Chat
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Chat\V1\Service\Channel;

use Twilio\Options;
use Twilio\Values;

abstract class MessageOptions
{
    /**
     * @param string $from The [identity](https://www.twilio.com/docs/api/chat/guides/identity) of the new message's author. The default value is `system`.
     * @param string $attributes A valid JSON string that contains application-specific data.
     * @return CreateMessageOptions Options builder
     */
    public static function create(
        
        string $from = Values::NONE,
        string $attributes = Values::NONE

    ): CreateMessageOptions
    {
        return new CreateMessageOptions(
            $from,
            $attributes
        );
    }



    /**
     * @param string $order The sort order of the returned messages. Can be: `asc` (ascending) or `desc` (descending) with `asc` as the default.
     * @return ReadMessageOptions Options builder
     */
    public static function read(
        
        string $order = Values::NONE

    ): ReadMessageOptions
    {
        return new ReadMessageOptions(
            $order
        );
    }

    /**
     * @param string $body The message to send to the channel. Can also be an empty string or `null`, which sets the value as an empty string. You can send structured data in the body by serializing it as a string.
     * @param string $attributes A valid JSON string that contains application-specific data.
     * @return UpdateMessageOptions Options builder
     */
    public static function update(
        
        string $body = Values::NONE,
        string $attributes = Values::NONE

    ): UpdateMessageOptions
    {
        return new UpdateMessageOptions(
            $body,
            $attributes
        );
    }

}

class CreateMessageOptions extends Options
    {
    /**
     * @param string $from The [identity](https://www.twilio.com/docs/api/chat/guides/identity) of the new message's author. The default value is `system`.
     * @param string $attributes A valid JSON string that contains application-specific data.
     */
    public function __construct(
        
        string $from = Values::NONE,
        string $attributes = Values::NONE

    ) {
        $this->options['from'] = $from;
        $this->options['attributes'] = $attributes;
    }

    /**
     * The [identity](https://www.twilio.com/docs/api/chat/guides/identity) of the new message's author. The default value is `system`.
     *
     * @param string $from The [identity](https://www.twilio.com/docs/api/chat/guides/identity) of the new message's author. The default value is `system`.
     * @return $this Fluent Builder
     */
    public function setFrom(string $from): self
    {
        $this->options['from'] = $from;
        return $this;
    }

    /**
     * A valid JSON string that contains application-specific data.
     *
     * @param string $attributes A valid JSON string that contains application-specific data.
     * @return $this Fluent Builder
     */
    public function setAttributes(string $attributes): self
    {
        $this->options['attributes'] = $attributes;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Chat.V1.CreateMessageOptions ' . $options . ']';
    }
}



class ReadMessageOptions extends Options
    {
    /**
     * @param string $order The sort order of the returned messages. Can be: `asc` (ascending) or `desc` (descending) with `asc` as the default.
     */
    public function __construct(
        
        string $order = Values::NONE

    ) {
        $this->options['order'] = $order;
    }

    /**
     * The sort order of the returned messages. Can be: `asc` (ascending) or `desc` (descending) with `asc` as the default.
     *
     * @param string $order The sort order of the returned messages. Can be: `asc` (ascending) or `desc` (descending) with `asc` as the default.
     * @return $this Fluent Builder
     */
    public function setOrder(string $order): self
    {
        $this->options['order'] = $order;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Chat.V1.ReadMessageOptions ' . $options . ']';
    }
}

class UpdateMessageOptions extends Options
    {
    /**
     * @param string $body The message to send to the channel. Can also be an empty string or `null`, which sets the value as an empty string. You can send structured data in the body by serializing it as a string.
     * @param string $attributes A valid JSON string that contains application-specific data.
     */
    public function __construct(
        
        string $body = Values::NONE,
        string $attributes = Values::NONE

    ) {
        $this->options['body'] = $body;
        $this->options['attributes'] = $attributes;
    }

    /**
     * The message to send to the channel. Can also be an empty string or `null`, which sets the value as an empty string. You can send structured data in the body by serializing it as a string.
     *
     * @param string $body The message to send to the channel. Can also be an empty string or `null`, which sets the value as an empty string. You can send structured data in the body by serializing it as a string.
     * @return $this Fluent Builder
     */
    public function setBody(string $body): self
    {
        $this->options['body'] = $body;
        return $this;
    }

    /**
     * A valid JSON string that contains application-specific data.
     *
     * @param string $attributes A valid JSON string that contains application-specific data.
     * @return $this Fluent Builder
     */
    public function setAttributes(string $attributes): self
    {
        $this->options['attributes'] = $attributes;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Chat.V1.UpdateMessageOptions ' . $options . ']';
    }
}

