<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Organization Public API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\PreviewIam\Versionless;

use Twilio\Exceptions\TwilioException;
use Twilio\ListResource;
use Twilio\Values;
use Twilio\Version;
use Twilio\InstanceContext;
use Twilio\Rest\PreviewIam\Versionless\Organization\AccountList;
use Twilio\Rest\PreviewIam\Versionless\Organization\UserList;
use Twilio\Rest\PreviewIam\Versionless\Organization\RoleAssignmentList;
use Twilio\Rest\PreviewIam\Versionless\Organization\ResourceTypeList;


/**
 * @property AccountList $accounts
 * @property UserList $users
 * @property RoleAssignmentList $roleAssignments
 * @property ResourceTypeList $resourceTypes
 * @method \Twilio\Rest\PreviewIam\Versionless\Organization\RoleAssignmentContext roleAssignments(string $sid)
 * @method \Twilio\Rest\PreviewIam\Versionless\Organization\AccountContext accounts(string $accountSid)
 * @method \Twilio\Rest\PreviewIam\Versionless\Organization\UserContext users(string $id)
 */
class OrganizationContext extends InstanceContext
    {
    protected $_accounts;
    protected $_users;
    protected $_roleAssignments;
    protected $_resourceTypes;

    /**
     * Initialize the OrganizationContext
     *
     * @param Version $version Version that contains the resource
     * @param string $organizationSid
     */
    public function __construct(
        Version $version,
        $organizationSid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'organizationSid' =>
            $organizationSid,
        ];

        $this->uri = '/' . \rawurlencode($organizationSid)
        .'';
    }

    /**
     * Fetch the OrganizationInstance
     *
     * @return OrganizationInstance Fetched OrganizationInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): OrganizationInstance
    {

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/scim+json' ]);
        $payload = $this->version->fetch('GET', $this->uri, [], [], $headers);

        return new OrganizationInstance(
            $this->version,
            $payload,
            $this->solution['organizationSid']
        );
    }


    /**
     * Access the accounts
     */
    protected function getAccounts(): AccountList
    {
        if (!$this->_accounts) {
            $this->_accounts = new AccountList(
                $this->version,
                $this->solution['organizationSid']
            );
        }

        return $this->_accounts;
    }

    /**
     * Access the users
     */
    protected function getUsers(): UserList
    {
        if (!$this->_users) {
            $this->_users = new UserList(
                $this->version,
                $this->solution['organizationSid']
            );
        }

        return $this->_users;
    }

    /**
     * Access the roleAssignments
     */
    protected function getRoleAssignments(): RoleAssignmentList
    {
        if (!$this->_roleAssignments) {
            $this->_roleAssignments = new RoleAssignmentList(
                $this->version,
                $this->solution['organizationSid']
            );
        }

        return $this->_roleAssignments;
    }

    /**
     * Access the resourceTypes
     */
    protected function getResourceTypes(): ResourceTypeList
    {
        if (!$this->_resourceTypes) {
            $this->_resourceTypes = new ResourceTypeList(
                $this->version,
                $this->solution['organizationSid']
            );
        }

        return $this->_resourceTypes;
    }

    /**
     * Magic getter to lazy load subresources
     *
     * @param string $name Subresource to return
     * @return ListResource The requested subresource
     * @throws TwilioException For unknown subresources
     */
    public function __get(string $name): ListResource
    {
        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown subresource ' . $name);
    }

    /**
     * Magic caller to get resource contexts
     *
     * @param string $name Resource to return
     * @param array $arguments Context parameters
     * @return InstanceContext The requested resource context
     * @throws TwilioException For unknown resource
     */
    public function __call(string $name, array $arguments): InstanceContext
    {
        $property = $this->$name;
        if (\method_exists($property, 'getContext')) {
            return \call_user_func_array(array($property, 'getContext'), $arguments);
        }

        throw new TwilioException('Resource does not have a context');
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.PreviewIam.Versionless.OrganizationContext ' . \implode(' ', $context) . ']';
    }
}
