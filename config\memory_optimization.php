<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Memory Optimization Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for memory optimization
    | strategies including lazy loading, caching, and chunking.
    |
    */

    'lazy_loading' => [
        /*
        |--------------------------------------------------------------------------
        | Default Chunk Size
        |--------------------------------------------------------------------------
        |
        | The default chunk size for processing large datasets.
        | Smaller chunks use less memory but may be slower.
        |
        */
        'default_chunk_size' => env('LAZY_LOADING_CHUNK_SIZE', 100),

        /*
        |--------------------------------------------------------------------------
        | Maximum Chunk Size
        |--------------------------------------------------------------------------
        |
        | The maximum allowed chunk size to prevent memory overflow.
        |
        */
        'max_chunk_size' => env('LAZY_LOADING_MAX_CHUNK_SIZE', 1000),

        /*
        |--------------------------------------------------------------------------
        | Pagination Size
        |--------------------------------------------------------------------------
        |
        | Default pagination size for web interfaces.
        |
        */
        'default_pagination_size' => env('LAZY_LOADING_PAGINATION_SIZE', 50),
        'max_pagination_size' => env('LAZY_LOADING_MAX_PAGINATION_SIZE', 500),

        /*
        |--------------------------------------------------------------------------
        | Lazy Loading Models
        |--------------------------------------------------------------------------
        |
        | Models that should use lazy loading by default.
        |
        */
        'enabled_models' => [
            'App\Models\Invoice',
            'App\Models\Customer',
            'App\Models\InvoiceProduct',
            'App\Models\InvoicePayment',
        ],

        /*
        |--------------------------------------------------------------------------
        | Relationship Loading Strategy
        |--------------------------------------------------------------------------
        |
        | Configure how relationships should be loaded for each model.
        |
        */
        'relationships' => [
            'App\Models\Invoice' => [
                'customer' => ['method' => 'eager', 'columns' => ['id', 'name', 'email']],
                'items' => ['method' => 'lazy', 'limit' => 10],
                'payments' => ['method' => 'lazy', 'limit' => 5],
                'category' => ['method' => 'eager', 'columns' => ['id', 'name']],
            ],
            'App\Models\Customer' => [
                'invoices' => ['method' => 'lazy', 'limit' => 10],
                'activeInvoices' => ['method' => 'count'],
                'recentInvoices' => ['method' => 'eager', 'limit' => 5],
            ],
        ],
    ],

    'caching' => [
        /*
        |--------------------------------------------------------------------------
        | Cache TTL Settings
        |--------------------------------------------------------------------------
        |
        | Time-to-live settings for different types of cached data.
        |
        */
        'ttl' => [
            'short' => env('CACHE_TTL_SHORT', 300),      // 5 minutes
            'medium' => env('CACHE_TTL_MEDIUM', 3600),   // 1 hour
            'long' => env('CACHE_TTL_LONG', 86400),      // 24 hours
            'stats' => env('CACHE_TTL_STATS', 1800),     // 30 minutes
            'search' => env('CACHE_TTL_SEARCH', 300),    // 5 minutes
        ],

        /*
        |--------------------------------------------------------------------------
        | Cache Tags
        |--------------------------------------------------------------------------
        |
        | Tags used for cache invalidation.
        |
        */
        'tags' => [
            'invoices' => ['invoices', 'financial'],
            'customers' => ['customers', 'users'],
            'payments' => ['payments', 'financial'],
            'stats' => ['stats', 'reports'],
            'search' => ['search', 'temporary'],
        ],

        /*
        |--------------------------------------------------------------------------
        | Cache Compression
        |--------------------------------------------------------------------------
        |
        | Enable compression for large cached data.
        |
        */
        'compression' => [
            'enabled' => env('CACHE_COMPRESSION_ENABLED', true),
            'threshold' => env('CACHE_COMPRESSION_THRESHOLD', 1024), // bytes
            'level' => env('CACHE_COMPRESSION_LEVEL', 6), // 1-9
        ],

        /*
        |--------------------------------------------------------------------------
        | Auto Cleanup
        |--------------------------------------------------------------------------
        |
        | Automatic cache cleanup settings.
        |
        */
        'auto_cleanup' => [
            'enabled' => env('CACHE_AUTO_CLEANUP_ENABLED', true),
            'frequency' => env('CACHE_AUTO_CLEANUP_FREQUENCY', 'hourly'),
            'cleanup_expired' => true,
            'cleanup_search_cache' => true,
        ],
    ],

    'memory_monitoring' => [
        /*
        |--------------------------------------------------------------------------
        | Memory Monitoring
        |--------------------------------------------------------------------------
        |
        | Settings for monitoring memory usage.
        |
        */
        'enabled' => env('MEMORY_MONITORING_ENABLED', true),

        /*
        |--------------------------------------------------------------------------
        | Warning Thresholds
        |--------------------------------------------------------------------------
        |
        | Memory usage thresholds for warnings and alerts.
        |
        */
        'warning_threshold' => env('MEMORY_WARNING_THRESHOLD', 0.8), // 80%
        'critical_threshold' => env('MEMORY_CRITICAL_THRESHOLD', 0.9), // 90%

        /*
        |--------------------------------------------------------------------------
        | Logging
        |--------------------------------------------------------------------------
        |
        | Log memory usage information.
        |
        */
        'log_usage' => env('MEMORY_LOG_USAGE', true),
        'log_channel' => env('MEMORY_LOG_CHANNEL', 'daily'),

        /*
        |--------------------------------------------------------------------------
        | Cleanup Actions
        |--------------------------------------------------------------------------
        |
        | Actions to take when memory usage is high.
        |
        */
        'auto_cleanup_on_high_usage' => true,
        'force_garbage_collection' => true,
    ],

    'database_optimization' => [
        /*
        |--------------------------------------------------------------------------
        | Query Optimization
        |--------------------------------------------------------------------------
        |
        | Settings for database query optimization.
        |
        */
        'select_specific_columns' => true,
        'use_indexes' => true,
        'limit_eager_loading' => true,

        /*
        |--------------------------------------------------------------------------
        | Chunking Settings
        |--------------------------------------------------------------------------
        |
        | Settings for processing large datasets in chunks.
        |
        */
        'chunk_size' => [
            'small' => 50,
            'medium' => 100,
            'large' => 500,
        ],

        /*
        |--------------------------------------------------------------------------
        | Connection Settings
        |--------------------------------------------------------------------------
        |
        | Database connection optimization.
        |
        */
        'connection_pooling' => env('DB_CONNECTION_POOLING', false),
        'persistent_connections' => env('DB_PERSISTENT_CONNECTIONS', false),
    ],

    'performance_profiles' => [
        /*
        |--------------------------------------------------------------------------
        | Performance Profiles
        |--------------------------------------------------------------------------
        |
        | Different performance profiles for different scenarios.
        |
        */
        'low_memory' => [
            'chunk_size' => 25,
            'pagination_size' => 20,
            'cache_ttl' => 1800,
            'eager_loading' => false,
        ],

        'balanced' => [
            'chunk_size' => 100,
            'pagination_size' => 50,
            'cache_ttl' => 3600,
            'eager_loading' => true,
        ],

        'high_performance' => [
            'chunk_size' => 500,
            'pagination_size' => 100,
            'cache_ttl' => 7200,
            'eager_loading' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Current Profile
    |--------------------------------------------------------------------------
    |
    | The currently active performance profile.
    |
    */
    'current_profile' => env('MEMORY_OPTIMIZATION_PROFILE', 'balanced'),

];
