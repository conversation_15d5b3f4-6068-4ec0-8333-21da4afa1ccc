<?php
// This file was auto-generated from sdk-root/src/data/ssm-incidents/2018-05-10/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-10', 'endpointPrefix' => 'ssm-incidents', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'SSM Incidents', 'serviceFullName' => 'AWS Systems Manager Incident Manager', 'serviceId' => 'SSM Incidents', 'signatureVersion' => 'v4', 'signingName' => 'ssm-incidents', 'uid' => 'ssm-incidents-2018-05-10', ], 'operations' => [ 'BatchGetIncidentFindings' => [ 'name' => 'BatchGetIncidentFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/batchGetIncidentFindings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchGetIncidentFindingsInput', ], 'output' => [ 'shape' => 'BatchGetIncidentFindingsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateReplicationSet' => [ 'name' => 'CreateReplicationSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/createReplicationSet', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateReplicationSetInput', ], 'output' => [ 'shape' => 'CreateReplicationSetOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateResponsePlan' => [ 'name' => 'CreateResponsePlan', 'http' => [ 'method' => 'POST', 'requestUri' => '/createResponsePlan', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateResponsePlanInput', ], 'output' => [ 'shape' => 'CreateResponsePlanOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateTimelineEvent' => [ 'name' => 'CreateTimelineEvent', 'http' => [ 'method' => 'POST', 'requestUri' => '/createTimelineEvent', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateTimelineEventInput', ], 'output' => [ 'shape' => 'CreateTimelineEventOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteIncidentRecord' => [ 'name' => 'DeleteIncidentRecord', 'http' => [ 'method' => 'POST', 'requestUri' => '/deleteIncidentRecord', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteIncidentRecordInput', ], 'output' => [ 'shape' => 'DeleteIncidentRecordOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteReplicationSet' => [ 'name' => 'DeleteReplicationSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/deleteReplicationSet', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteReplicationSetInput', ], 'output' => [ 'shape' => 'DeleteReplicationSetOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteResourcePolicy' => [ 'name' => 'DeleteResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/deleteResourcePolicy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteResourcePolicyInput', ], 'output' => [ 'shape' => 'DeleteResourcePolicyOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteResponsePlan' => [ 'name' => 'DeleteResponsePlan', 'http' => [ 'method' => 'POST', 'requestUri' => '/deleteResponsePlan', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteResponsePlanInput', ], 'output' => [ 'shape' => 'DeleteResponsePlanOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteTimelineEvent' => [ 'name' => 'DeleteTimelineEvent', 'http' => [ 'method' => 'POST', 'requestUri' => '/deleteTimelineEvent', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteTimelineEventInput', ], 'output' => [ 'shape' => 'DeleteTimelineEventOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'GetIncidentRecord' => [ 'name' => 'GetIncidentRecord', 'http' => [ 'method' => 'GET', 'requestUri' => '/getIncidentRecord', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIncidentRecordInput', ], 'output' => [ 'shape' => 'GetIncidentRecordOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetReplicationSet' => [ 'name' => 'GetReplicationSet', 'http' => [ 'method' => 'GET', 'requestUri' => '/getReplicationSet', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetReplicationSetInput', ], 'output' => [ 'shape' => 'GetReplicationSetOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetResourcePolicies' => [ 'name' => 'GetResourcePolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/getResourcePolicies', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetResourcePoliciesInput', ], 'output' => [ 'shape' => 'GetResourcePoliciesOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetResponsePlan' => [ 'name' => 'GetResponsePlan', 'http' => [ 'method' => 'GET', 'requestUri' => '/getResponsePlan', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetResponsePlanInput', ], 'output' => [ 'shape' => 'GetResponsePlanOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetTimelineEvent' => [ 'name' => 'GetTimelineEvent', 'http' => [ 'method' => 'GET', 'requestUri' => '/getTimelineEvent', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTimelineEventInput', ], 'output' => [ 'shape' => 'GetTimelineEventOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListIncidentFindings' => [ 'name' => 'ListIncidentFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/listIncidentFindings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIncidentFindingsInput', ], 'output' => [ 'shape' => 'ListIncidentFindingsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListIncidentRecords' => [ 'name' => 'ListIncidentRecords', 'http' => [ 'method' => 'POST', 'requestUri' => '/listIncidentRecords', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIncidentRecordsInput', ], 'output' => [ 'shape' => 'ListIncidentRecordsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListRelatedItems' => [ 'name' => 'ListRelatedItems', 'http' => [ 'method' => 'POST', 'requestUri' => '/listRelatedItems', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRelatedItemsInput', ], 'output' => [ 'shape' => 'ListRelatedItemsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListReplicationSets' => [ 'name' => 'ListReplicationSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/listReplicationSets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListReplicationSetsInput', ], 'output' => [ 'shape' => 'ListReplicationSetsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListResponsePlans' => [ 'name' => 'ListResponsePlans', 'http' => [ 'method' => 'POST', 'requestUri' => '/listResponsePlans', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListResponsePlansInput', ], 'output' => [ 'shape' => 'ListResponsePlansOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTimelineEvents' => [ 'name' => 'ListTimelineEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/listTimelineEvents', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTimelineEventsInput', ], 'output' => [ 'shape' => 'ListTimelineEventsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'PutResourcePolicy' => [ 'name' => 'PutResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/putResourcePolicy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutResourcePolicyInput', ], 'output' => [ 'shape' => 'PutResourcePolicyOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartIncident' => [ 'name' => 'StartIncident', 'http' => [ 'method' => 'POST', 'requestUri' => '/startIncident', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartIncidentInput', ], 'output' => [ 'shape' => 'StartIncidentOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateDeletionProtection' => [ 'name' => 'UpdateDeletionProtection', 'http' => [ 'method' => 'POST', 'requestUri' => '/updateDeletionProtection', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateDeletionProtectionInput', ], 'output' => [ 'shape' => 'UpdateDeletionProtectionOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateIncidentRecord' => [ 'name' => 'UpdateIncidentRecord', 'http' => [ 'method' => 'POST', 'requestUri' => '/updateIncidentRecord', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateIncidentRecordInput', ], 'output' => [ 'shape' => 'UpdateIncidentRecordOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateRelatedItems' => [ 'name' => 'UpdateRelatedItems', 'http' => [ 'method' => 'POST', 'requestUri' => '/updateRelatedItems', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateRelatedItemsInput', ], 'output' => [ 'shape' => 'UpdateRelatedItemsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateReplicationSet' => [ 'name' => 'UpdateReplicationSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/updateReplicationSet', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateReplicationSetInput', ], 'output' => [ 'shape' => 'UpdateReplicationSetOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateResponsePlan' => [ 'name' => 'UpdateResponsePlan', 'http' => [ 'method' => 'POST', 'requestUri' => '/updateResponsePlan', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateResponsePlanInput', ], 'output' => [ 'shape' => 'UpdateResponsePlanOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateTimelineEvent' => [ 'name' => 'UpdateTimelineEvent', 'http' => [ 'method' => 'POST', 'requestUri' => '/updateTimelineEvent', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UpdateTimelineEventInput', ], 'output' => [ 'shape' => 'UpdateTimelineEventOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'Action' => [ 'type' => 'structure', 'members' => [ 'ssmAutomation' => [ 'shape' => 'SsmAutomation', ], ], 'union' => true, ], 'ActionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Action', ], 'max' => 1, 'min' => 0, ], 'AddRegionAction' => [ 'type' => 'structure', 'required' => [ 'regionName', ], 'members' => [ 'regionName' => [ 'shape' => 'RegionName', ], 'sseKmsKeyId' => [ 'shape' => 'SseKmsKey', ], ], ], 'Arn' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'pattern' => '^arn:aws(-cn|-us-gov)?:[a-z0-9-]*:[a-z0-9-]*:([0-9]{12})?:.+$', ], 'AttributeValueList' => [ 'type' => 'structure', 'members' => [ 'integerValues' => [ 'shape' => 'IntegerList', ], 'stringValues' => [ 'shape' => 'StringList', ], ], 'union' => true, ], 'AutomationExecution' => [ 'type' => 'structure', 'members' => [ 'ssmExecutionArn' => [ 'shape' => 'Arn', ], ], 'union' => true, ], 'AutomationExecutionSet' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutomationExecution', ], 'max' => 100, 'min' => 0, ], 'BatchGetIncidentFindingsError' => [ 'type' => 'structure', 'required' => [ 'code', 'findingId', 'message', ], 'members' => [ 'code' => [ 'shape' => 'String', ], 'findingId' => [ 'shape' => 'FindingId', ], 'message' => [ 'shape' => 'String', ], ], ], 'BatchGetIncidentFindingsErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetIncidentFindingsError', ], ], 'BatchGetIncidentFindingsInput' => [ 'type' => 'structure', 'required' => [ 'findingIds', 'incidentRecordArn', ], 'members' => [ 'findingIds' => [ 'shape' => 'FindingIdList', ], 'incidentRecordArn' => [ 'shape' => 'Arn', ], ], ], 'BatchGetIncidentFindingsOutput' => [ 'type' => 'structure', 'required' => [ 'errors', 'findings', ], 'members' => [ 'errors' => [ 'shape' => 'BatchGetIncidentFindingsErrorList', ], 'findings' => [ 'shape' => 'FindingList', ], ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ChatChannel' => [ 'type' => 'structure', 'members' => [ 'chatbotSns' => [ 'shape' => 'ChatbotSnsConfigurationSet', ], 'empty' => [ 'shape' => 'EmptyChatChannel', ], ], 'union' => true, ], 'ChatbotSnsConfigurationSet' => [ 'type' => 'list', 'member' => [ 'shape' => 'SnsArn', ], 'max' => 5, 'min' => 1, ], 'ClientToken' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'CloudFormationStackUpdate' => [ 'type' => 'structure', 'required' => [ 'stackArn', 'startTime', ], 'members' => [ 'endTime' => [ 'shape' => 'Timestamp', ], 'stackArn' => [ 'shape' => 'Arn', ], 'startTime' => [ 'shape' => 'Timestamp', ], ], ], 'CodeDeployDeployment' => [ 'type' => 'structure', 'required' => [ 'deploymentGroupArn', 'deploymentId', 'startTime', ], 'members' => [ 'deploymentGroupArn' => [ 'shape' => 'Arn', ], 'deploymentId' => [ 'shape' => 'CodeDeployDeploymentDeploymentIdString', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'startTime' => [ 'shape' => 'Timestamp', ], ], ], 'CodeDeployDeploymentDeploymentIdString' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'Condition' => [ 'type' => 'structure', 'members' => [ 'after' => [ 'shape' => 'Timestamp', ], 'before' => [ 'shape' => 'Timestamp', ], 'equals' => [ 'shape' => 'AttributeValueList', ], ], 'union' => true, ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'resourceIdentifier' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'retryAfter' => [ 'shape' => 'Timestamp', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateReplicationSetInput' => [ 'type' => 'structure', 'required' => [ 'regions', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'regions' => [ 'shape' => 'RegionMapInput', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateReplicationSetOutput' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], ], ], 'CreateResponsePlanInput' => [ 'type' => 'structure', 'required' => [ 'incidentTemplate', 'name', ], 'members' => [ 'actions' => [ 'shape' => 'ActionsList', ], 'chatChannel' => [ 'shape' => 'ChatChannel', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'displayName' => [ 'shape' => 'ResponsePlanDisplayName', ], 'engagements' => [ 'shape' => 'EngagementSet', ], 'incidentTemplate' => [ 'shape' => 'IncidentTemplate', ], 'integrations' => [ 'shape' => 'Integrations', ], 'name' => [ 'shape' => 'ResponsePlanName', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateResponsePlanOutput' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], ], ], 'CreateTimelineEventInput' => [ 'type' => 'structure', 'required' => [ 'eventData', 'eventTime', 'eventType', 'incidentRecordArn', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'eventData' => [ 'shape' => 'EventData', ], 'eventReferences' => [ 'shape' => 'EventReferenceList', ], 'eventTime' => [ 'shape' => 'Timestamp', ], 'eventType' => [ 'shape' => 'TimelineEventType', ], 'incidentRecordArn' => [ 'shape' => 'Arn', ], ], ], 'CreateTimelineEventOutput' => [ 'type' => 'structure', 'required' => [ 'eventId', 'incidentRecordArn', ], 'members' => [ 'eventId' => [ 'shape' => 'UUID', ], 'incidentRecordArn' => [ 'shape' => 'Arn', ], ], ], 'DedupeString' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'DeleteIncidentRecordInput' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], ], ], 'DeleteIncidentRecordOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRegionAction' => [ 'type' => 'structure', 'required' => [ 'regionName', ], 'members' => [ 'regionName' => [ 'shape' => 'RegionName', ], ], ], 'DeleteReplicationSetInput' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'arn', ], ], ], 'DeleteReplicationSetOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteResourcePolicyInput' => [ 'type' => 'structure', 'required' => [ 'policyId', 'resourceArn', ], 'members' => [ 'policyId' => [ 'shape' => 'PolicyId', ], 'resourceArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteResourcePolicyOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteResponsePlanInput' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], ], ], 'DeleteResponsePlanOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTimelineEventInput' => [ 'type' => 'structure', 'required' => [ 'eventId', 'incidentRecordArn', ], 'members' => [ 'eventId' => [ 'shape' => 'UUID', ], 'incidentRecordArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteTimelineEventOutput' => [ 'type' => 'structure', 'members' => [], ], 'DynamicSsmParameterValue' => [ 'type' => 'structure', 'members' => [ 'variable' => [ 'shape' => 'VariableType', ], ], 'union' => true, ], 'DynamicSsmParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'DynamicSsmParametersKeyString', ], 'value' => [ 'shape' => 'DynamicSsmParameterValue', ], 'max' => 200, 'min' => 1, ], 'DynamicSsmParametersKeyString' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'EmptyChatChannel' => [ 'type' => 'structure', 'members' => [], ], 'EngagementSet' => [ 'type' => 'list', 'member' => [ 'shape' => 'SsmContactsArn', ], 'max' => 5, 'min' => 0, ], 'EventData' => [ 'type' => 'string', 'max' => 12000, 'min' => 0, ], 'EventReference' => [ 'type' => 'structure', 'members' => [ 'relatedItemId' => [ 'shape' => 'GeneratedId', ], 'resource' => [ 'shape' => 'Arn', ], ], 'union' => true, ], 'EventReferenceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventReference', ], 'max' => 10, 'min' => 0, ], 'EventSummary' => [ 'type' => 'structure', 'required' => [ 'eventId', 'eventTime', 'eventType', 'eventUpdatedTime', 'incidentRecordArn', ], 'members' => [ 'eventId' => [ 'shape' => 'UUID', ], 'eventReferences' => [ 'shape' => 'EventReferenceList', ], 'eventTime' => [ 'shape' => 'Timestamp', ], 'eventType' => [ 'shape' => 'TimelineEventType', ], 'eventUpdatedTime' => [ 'shape' => 'Timestamp', ], 'incidentRecordArn' => [ 'shape' => 'Arn', ], ], ], 'EventSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventSummary', ], 'max' => 100, 'min' => 0, ], 'ExceptionMessage' => [ 'type' => 'string', ], 'Filter' => [ 'type' => 'structure', 'required' => [ 'condition', 'key', ], 'members' => [ 'condition' => [ 'shape' => 'Condition', ], 'key' => [ 'shape' => 'FilterKeyString', ], ], ], 'FilterKeyString' => [ 'type' => 'string', 'max' => 50, 'min' => 0, ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], 'max' => 5, 'min' => 0, ], 'Finding' => [ 'type' => 'structure', 'required' => [ 'creationTime', 'id', 'lastModifiedTime', ], 'members' => [ 'creationTime' => [ 'shape' => 'Timestamp', ], 'details' => [ 'shape' => 'FindingDetails', ], 'id' => [ 'shape' => 'FindingId', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'FindingDetails' => [ 'type' => 'structure', 'members' => [ 'cloudFormationStackUpdate' => [ 'shape' => 'CloudFormationStackUpdate', ], 'codeDeployDeployment' => [ 'shape' => 'CodeDeployDeployment', ], ], 'union' => true, ], 'FindingId' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'FindingIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingId', ], 'max' => 20, 'min' => 0, ], 'FindingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Finding', ], ], 'FindingSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'lastModifiedTime', ], 'members' => [ 'id' => [ 'shape' => 'FindingId', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'FindingSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingSummary', ], 'max' => 100, 'min' => 0, ], 'GeneratedId' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'pattern' => '^related-item/(ANALYSIS|INCIDENT|METRIC|PARENT|ATTACHMENT|OTHER|AUTOMATION|INVOLVED_RESOURCE|TASK)/([0-9]|[A-F]){32}$', ], 'GetIncidentRecordInput' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'arn', ], ], ], 'GetIncidentRecordOutput' => [ 'type' => 'structure', 'required' => [ 'incidentRecord', ], 'members' => [ 'incidentRecord' => [ 'shape' => 'IncidentRecord', ], ], ], 'GetReplicationSetInput' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'arn', ], ], ], 'GetReplicationSetOutput' => [ 'type' => 'structure', 'required' => [ 'replicationSet', ], 'members' => [ 'replicationSet' => [ 'shape' => 'ReplicationSet', ], ], ], 'GetResourcePoliciesInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'resourceArn', ], ], ], 'GetResourcePoliciesOutput' => [ 'type' => 'structure', 'required' => [ 'resourcePolicies', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'resourcePolicies' => [ 'shape' => 'ResourcePolicyList', ], ], ], 'GetResponsePlanInput' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'arn', ], ], ], 'GetResponsePlanOutput' => [ 'type' => 'structure', 'required' => [ 'arn', 'incidentTemplate', 'name', ], 'members' => [ 'actions' => [ 'shape' => 'ActionsList', ], 'arn' => [ 'shape' => 'Arn', ], 'chatChannel' => [ 'shape' => 'ChatChannel', ], 'displayName' => [ 'shape' => 'ResponsePlanDisplayName', ], 'engagements' => [ 'shape' => 'EngagementSet', ], 'incidentTemplate' => [ 'shape' => 'IncidentTemplate', ], 'integrations' => [ 'shape' => 'Integrations', ], 'name' => [ 'shape' => 'ResponsePlanName', ], ], ], 'GetTimelineEventInput' => [ 'type' => 'structure', 'required' => [ 'eventId', 'incidentRecordArn', ], 'members' => [ 'eventId' => [ 'shape' => 'UUID', 'location' => 'querystring', 'locationName' => 'eventId', ], 'incidentRecordArn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'incidentRecordArn', ], ], ], 'GetTimelineEventOutput' => [ 'type' => 'structure', 'required' => [ 'event', ], 'members' => [ 'event' => [ 'shape' => 'TimelineEvent', ], ], ], 'Impact' => [ 'type' => 'integer', 'box' => true, 'max' => 5, 'min' => 1, ], 'IncidentRecord' => [ 'type' => 'structure', 'required' => [ 'arn', 'creationTime', 'dedupeString', 'impact', 'incidentRecordSource', 'lastModifiedBy', 'lastModifiedTime', 'status', 'title', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'automationExecutions' => [ 'shape' => 'AutomationExecutionSet', ], 'chatChannel' => [ 'shape' => 'ChatChannel', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'dedupeString' => [ 'shape' => 'DedupeString', ], 'impact' => [ 'shape' => 'Impact', ], 'incidentRecordSource' => [ 'shape' => 'IncidentRecordSource', ], 'lastModifiedBy' => [ 'shape' => 'Arn', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], 'notificationTargets' => [ 'shape' => 'NotificationTargetSet', ], 'resolvedTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'IncidentRecordStatus', ], 'summary' => [ 'shape' => 'IncidentSummary', ], 'title' => [ 'shape' => 'IncidentTitle', ], ], ], 'IncidentRecordSource' => [ 'type' => 'structure', 'required' => [ 'createdBy', 'source', ], 'members' => [ 'createdBy' => [ 'shape' => 'Arn', ], 'invokedBy' => [ 'shape' => 'ServicePrincipal', ], 'resourceArn' => [ 'shape' => 'Arn', ], 'source' => [ 'shape' => 'IncidentSource', ], ], ], 'IncidentRecordStatus' => [ 'type' => 'string', 'enum' => [ 'OPEN', 'RESOLVED', ], ], 'IncidentRecordSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'creationTime', 'impact', 'incidentRecordSource', 'status', 'title', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'impact' => [ 'shape' => 'Impact', ], 'incidentRecordSource' => [ 'shape' => 'IncidentRecordSource', ], 'resolvedTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'IncidentRecordStatus', ], 'title' => [ 'shape' => 'IncidentTitle', ], ], ], 'IncidentRecordSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IncidentRecordSummary', ], 'max' => 100, 'min' => 0, ], 'IncidentSource' => [ 'type' => 'string', 'max' => 50, 'min' => 0, ], 'IncidentSummary' => [ 'type' => 'string', 'max' => 8000, 'min' => 0, ], 'IncidentTemplate' => [ 'type' => 'structure', 'required' => [ 'impact', 'title', ], 'members' => [ 'dedupeString' => [ 'shape' => 'DedupeString', ], 'impact' => [ 'shape' => 'Impact', ], 'incidentTags' => [ 'shape' => 'TagMap', ], 'notificationTargets' => [ 'shape' => 'NotificationTargetSet', ], 'summary' => [ 'shape' => 'IncidentSummary', ], 'title' => [ 'shape' => 'IncidentTitle', ], ], ], 'IncidentTitle' => [ 'type' => 'string', 'max' => 200, 'min' => 0, ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'IntegerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Integer', ], 'max' => 100, 'min' => 0, ], 'Integration' => [ 'type' => 'structure', 'members' => [ 'pagerDutyConfiguration' => [ 'shape' => 'PagerDutyConfiguration', ], ], 'union' => true, ], 'Integrations' => [ 'type' => 'list', 'member' => [ 'shape' => 'Integration', ], 'max' => 1, 'min' => 0, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ItemIdentifier' => [ 'type' => 'structure', 'required' => [ 'type', 'value', ], 'members' => [ 'type' => [ 'shape' => 'ItemType', ], 'value' => [ 'shape' => 'ItemValue', ], ], ], 'ItemType' => [ 'type' => 'string', 'enum' => [ 'ANALYSIS', 'INCIDENT', 'METRIC', 'PARENT', 'ATTACHMENT', 'OTHER', 'AUTOMATION', 'INVOLVED_RESOURCE', 'TASK', ], ], 'ItemValue' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'metricDefinition' => [ 'shape' => 'MetricDefinition', ], 'pagerDutyIncidentDetail' => [ 'shape' => 'PagerDutyIncidentDetail', ], 'url' => [ 'shape' => 'Url', ], ], 'union' => true, ], 'ListIncidentFindingsInput' => [ 'type' => 'structure', 'required' => [ 'incidentRecordArn', ], 'members' => [ 'incidentRecordArn' => [ 'shape' => 'Arn', ], 'maxResults' => [ 'shape' => 'ListIncidentFindingsInputMaxResultsInteger', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListIncidentFindingsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 20, 'min' => 1, ], 'ListIncidentFindingsOutput' => [ 'type' => 'structure', 'required' => [ 'findings', ], 'members' => [ 'findings' => [ 'shape' => 'FindingSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListIncidentRecordsInput' => [ 'type' => 'structure', 'members' => [ 'filters' => [ 'shape' => 'FilterList', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListIncidentRecordsOutput' => [ 'type' => 'structure', 'required' => [ 'incidentRecordSummaries', ], 'members' => [ 'incidentRecordSummaries' => [ 'shape' => 'IncidentRecordSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRelatedItemsInput' => [ 'type' => 'structure', 'required' => [ 'incidentRecordArn', ], 'members' => [ 'incidentRecordArn' => [ 'shape' => 'Arn', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRelatedItemsOutput' => [ 'type' => 'structure', 'required' => [ 'relatedItems', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'relatedItems' => [ 'shape' => 'RelatedItemList', ], ], ], 'ListReplicationSetsInput' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListReplicationSetsOutput' => [ 'type' => 'structure', 'required' => [ 'replicationSetArns', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'replicationSetArns' => [ 'shape' => 'ReplicationSetArnList', ], ], ], 'ListResponsePlansInput' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListResponsePlansOutput' => [ 'type' => 'structure', 'required' => [ 'responsePlanSummaries', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'responsePlanSummaries' => [ 'shape' => 'ResponsePlanSummaryList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'required' => [ 'tags', ], 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListTimelineEventsInput' => [ 'type' => 'structure', 'required' => [ 'incidentRecordArn', ], 'members' => [ 'filters' => [ 'shape' => 'FilterList', ], 'incidentRecordArn' => [ 'shape' => 'Arn', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'sortBy' => [ 'shape' => 'TimelineEventSort', ], 'sortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'ListTimelineEventsOutput' => [ 'type' => 'structure', 'required' => [ 'eventSummaries', ], 'members' => [ 'eventSummaries' => [ 'shape' => 'EventSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MetricDefinition' => [ 'type' => 'string', 'max' => 4000, 'min' => 0, ], 'NextToken' => [ 'type' => 'string', 'max' => 2000, 'min' => 0, ], 'NotificationTargetItem' => [ 'type' => 'structure', 'members' => [ 'snsTopicArn' => [ 'shape' => 'Arn', ], ], 'union' => true, ], 'NotificationTargetSet' => [ 'type' => 'list', 'member' => [ 'shape' => 'NotificationTargetItem', ], 'max' => 10, 'min' => 0, ], 'PagerDutyConfiguration' => [ 'type' => 'structure', 'required' => [ 'name', 'pagerDutyIncidentConfiguration', 'secretId', ], 'members' => [ 'name' => [ 'shape' => 'PagerDutyConfigurationNameString', ], 'pagerDutyIncidentConfiguration' => [ 'shape' => 'PagerDutyIncidentConfiguration', ], 'secretId' => [ 'shape' => 'PagerDutyConfigurationSecretIdString', ], ], ], 'PagerDutyConfigurationNameString' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'PagerDutyConfigurationSecretIdString' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'PagerDutyIncidentConfiguration' => [ 'type' => 'structure', 'required' => [ 'serviceId', ], 'members' => [ 'serviceId' => [ 'shape' => 'PagerDutyIncidentConfigurationServiceIdString', ], ], ], 'PagerDutyIncidentConfigurationServiceIdString' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'PagerDutyIncidentDetail' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'autoResolve' => [ 'shape' => 'Boolean', ], 'id' => [ 'shape' => 'PagerDutyIncidentDetailIdString', ], 'secretId' => [ 'shape' => 'PagerDutyIncidentDetailSecretIdString', ], ], ], 'PagerDutyIncidentDetailIdString' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'PagerDutyIncidentDetailSecretIdString' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'Policy' => [ 'type' => 'string', 'max' => 4000, 'min' => 0, ], 'PolicyId' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'PutResourcePolicyInput' => [ 'type' => 'structure', 'required' => [ 'policy', 'resourceArn', ], 'members' => [ 'policy' => [ 'shape' => 'Policy', ], 'resourceArn' => [ 'shape' => 'Arn', ], ], ], 'PutResourcePolicyOutput' => [ 'type' => 'structure', 'required' => [ 'policyId', ], 'members' => [ 'policyId' => [ 'shape' => 'PolicyId', ], ], ], 'RawData' => [ 'type' => 'string', 'max' => 10000, 'min' => 0, ], 'RegionInfo' => [ 'type' => 'structure', 'required' => [ 'status', 'statusUpdateDateTime', ], 'members' => [ 'sseKmsKeyId' => [ 'shape' => 'SseKmsKey', ], 'status' => [ 'shape' => 'RegionStatus', ], 'statusMessage' => [ 'shape' => 'String', ], 'statusUpdateDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'RegionInfoMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'RegionName', ], 'value' => [ 'shape' => 'RegionInfo', ], ], 'RegionMapInput' => [ 'type' => 'map', 'key' => [ 'shape' => 'RegionName', ], 'value' => [ 'shape' => 'RegionMapInputValue', ], 'max' => 3, 'min' => 1, ], 'RegionMapInputValue' => [ 'type' => 'structure', 'members' => [ 'sseKmsKeyId' => [ 'shape' => 'SseKmsKey', ], ], ], 'RegionName' => [ 'type' => 'string', 'max' => 20, 'min' => 0, ], 'RegionStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'CREATING', 'DELETING', 'FAILED', ], ], 'RelatedItem' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'generatedId' => [ 'shape' => 'GeneratedId', ], 'identifier' => [ 'shape' => 'ItemIdentifier', ], 'title' => [ 'shape' => 'RelatedItemTitleString', ], ], ], 'RelatedItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RelatedItem', ], 'max' => 100, 'min' => 0, ], 'RelatedItemTitleString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, ], 'RelatedItemsUpdate' => [ 'type' => 'structure', 'members' => [ 'itemToAdd' => [ 'shape' => 'RelatedItem', ], 'itemToRemove' => [ 'shape' => 'ItemIdentifier', ], ], 'union' => true, ], 'ReplicationSet' => [ 'type' => 'structure', 'required' => [ 'createdBy', 'createdTime', 'deletionProtected', 'lastModifiedBy', 'lastModifiedTime', 'regionMap', 'status', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'createdBy' => [ 'shape' => 'Arn', ], 'createdTime' => [ 'shape' => 'Timestamp', ], 'deletionProtected' => [ 'shape' => 'Boolean', ], 'lastModifiedBy' => [ 'shape' => 'Arn', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], 'regionMap' => [ 'shape' => 'RegionInfoMap', ], 'status' => [ 'shape' => 'ReplicationSetStatus', ], ], ], 'ReplicationSetArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], ], 'ReplicationSetStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'CREATING', 'UPDATING', 'DELETING', 'FAILED', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'resourceIdentifier' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourcePolicy' => [ 'type' => 'structure', 'required' => [ 'policyDocument', 'policyId', 'ramResourceShareRegion', ], 'members' => [ 'policyDocument' => [ 'shape' => 'Policy', ], 'policyId' => [ 'shape' => 'PolicyId', ], 'ramResourceShareRegion' => [ 'shape' => 'String', ], ], ], 'ResourcePolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourcePolicy', ], 'max' => 100, 'min' => 0, ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'RESPONSE_PLAN', 'INCIDENT_RECORD', 'TIMELINE_EVENT', 'REPLICATION_SET', 'RESOURCE_POLICY', ], ], 'ResponsePlanDisplayName' => [ 'type' => 'string', 'max' => 200, 'min' => 0, ], 'ResponsePlanName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-_]*$', ], 'ResponsePlanSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'name', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'displayName' => [ 'shape' => 'ResponsePlanDisplayName', ], 'name' => [ 'shape' => 'ResponsePlanName', ], ], ], 'ResponsePlanSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResponsePlanSummary', ], 'max' => 100, 'min' => 0, ], 'RoleArn' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'pattern' => '^arn:aws(-cn|-us-gov)?:iam::([0-9]{12})?:role/.+$', ], 'ServiceCode' => [ 'type' => 'string', 'enum' => [ 'ssm-incidents', ], ], 'ServicePrincipal' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'quotaCode', 'serviceCode', ], 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'quotaCode' => [ 'shape' => 'String', ], 'resourceIdentifier' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'serviceCode' => [ 'shape' => 'ServiceCode', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SnsArn' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'SseKmsKey' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'SsmAutomation' => [ 'type' => 'structure', 'required' => [ 'documentName', 'roleArn', ], 'members' => [ 'documentName' => [ 'shape' => 'SsmAutomationDocumentNameString', ], 'documentVersion' => [ 'shape' => 'SsmAutomationDocumentVersionString', ], 'dynamicParameters' => [ 'shape' => 'DynamicSsmParameters', ], 'parameters' => [ 'shape' => 'SsmParameters', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'targetAccount' => [ 'shape' => 'SsmTargetAccount', ], ], ], 'SsmAutomationDocumentNameString' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_\\-.:/]{3,128}$', ], 'SsmAutomationDocumentVersionString' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'SsmContactsArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^arn:aws(-cn|-us-gov)?:ssm-contacts:[a-z0-9-]*:([0-9]{12}):contact/[a-z0-9_-]+$', ], 'SsmParameterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'SsmParameterValuesMemberString', ], 'max' => 100, 'min' => 0, ], 'SsmParameterValuesMemberString' => [ 'type' => 'string', 'max' => 512, 'min' => 0, ], 'SsmParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'SsmParametersKeyString', ], 'value' => [ 'shape' => 'SsmParameterValues', ], 'max' => 200, 'min' => 1, ], 'SsmParametersKeyString' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'SsmTargetAccount' => [ 'type' => 'string', 'enum' => [ 'RESPONSE_PLAN_OWNER_ACCOUNT', 'IMPACTED_ACCOUNT', ], ], 'StartIncidentInput' => [ 'type' => 'structure', 'required' => [ 'responsePlanArn', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'impact' => [ 'shape' => 'Impact', ], 'relatedItems' => [ 'shape' => 'RelatedItemList', ], 'responsePlanArn' => [ 'shape' => 'Arn', ], 'title' => [ 'shape' => 'IncidentTitle', ], 'triggerDetails' => [ 'shape' => 'TriggerDetails', ], ], ], 'StartIncidentOutput' => [ 'type' => 'structure', 'required' => [ 'incidentRecordArn', ], 'members' => [ 'incidentRecordArn' => [ 'shape' => 'Arn', ], ], ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringListMemberString', ], 'max' => 100, 'min' => 0, ], 'StringListMemberString' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[A-Za-z0-9 _=@:.+-/]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'TagMapUpdate' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^[A-Za-z0-9 _=@:.+-/]*$', ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', 'quotaCode', 'serviceCode', ], 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'quotaCode' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'ServiceCode', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'TimelineEvent' => [ 'type' => 'structure', 'required' => [ 'eventData', 'eventId', 'eventTime', 'eventType', 'eventUpdatedTime', 'incidentRecordArn', ], 'members' => [ 'eventData' => [ 'shape' => 'EventData', ], 'eventId' => [ 'shape' => 'UUID', ], 'eventReferences' => [ 'shape' => 'EventReferenceList', ], 'eventTime' => [ 'shape' => 'Timestamp', ], 'eventType' => [ 'shape' => 'TimelineEventType', ], 'eventUpdatedTime' => [ 'shape' => 'Timestamp', ], 'incidentRecordArn' => [ 'shape' => 'Arn', ], ], ], 'TimelineEventSort' => [ 'type' => 'string', 'enum' => [ 'EVENT_TIME', ], ], 'TimelineEventType' => [ 'type' => 'string', 'max' => 100, 'min' => 0, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TriggerDetails' => [ 'type' => 'structure', 'required' => [ 'source', 'timestamp', ], 'members' => [ 'rawData' => [ 'shape' => 'RawData', ], 'source' => [ 'shape' => 'IncidentSource', ], 'timestamp' => [ 'shape' => 'Timestamp', ], 'triggerArn' => [ 'shape' => 'Arn', ], ], ], 'UUID' => [ 'type' => 'string', 'max' => 50, 'min' => 0, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDeletionProtectionInput' => [ 'type' => 'structure', 'required' => [ 'arn', 'deletionProtected', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'deletionProtected' => [ 'shape' => 'Boolean', ], ], ], 'UpdateDeletionProtectionOutput' => [ 'type' => 'structure', 'members' => [], ], 'UpdateIncidentRecordInput' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'chatChannel' => [ 'shape' => 'ChatChannel', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'impact' => [ 'shape' => 'Impact', ], 'notificationTargets' => [ 'shape' => 'NotificationTargetSet', ], 'status' => [ 'shape' => 'IncidentRecordStatus', ], 'summary' => [ 'shape' => 'IncidentSummary', ], 'title' => [ 'shape' => 'IncidentTitle', ], ], ], 'UpdateIncidentRecordOutput' => [ 'type' => 'structure', 'members' => [], ], 'UpdateRelatedItemsInput' => [ 'type' => 'structure', 'required' => [ 'incidentRecordArn', 'relatedItemsUpdate', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'incidentRecordArn' => [ 'shape' => 'Arn', ], 'relatedItemsUpdate' => [ 'shape' => 'RelatedItemsUpdate', ], ], ], 'UpdateRelatedItemsOutput' => [ 'type' => 'structure', 'members' => [], ], 'UpdateReplicationSetAction' => [ 'type' => 'structure', 'members' => [ 'addRegionAction' => [ 'shape' => 'AddRegionAction', ], 'deleteRegionAction' => [ 'shape' => 'DeleteRegionAction', ], ], 'union' => true, ], 'UpdateReplicationSetInput' => [ 'type' => 'structure', 'required' => [ 'actions', 'arn', ], 'members' => [ 'actions' => [ 'shape' => 'UpdateReplicationSetInputActionsList', ], 'arn' => [ 'shape' => 'Arn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateReplicationSetInputActionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpdateReplicationSetAction', ], 'max' => 1, 'min' => 1, ], 'UpdateReplicationSetOutput' => [ 'type' => 'structure', 'members' => [], ], 'UpdateResponsePlanInput' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'actions' => [ 'shape' => 'ActionsList', ], 'arn' => [ 'shape' => 'Arn', ], 'chatChannel' => [ 'shape' => 'ChatChannel', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'displayName' => [ 'shape' => 'ResponsePlanDisplayName', ], 'engagements' => [ 'shape' => 'EngagementSet', ], 'incidentTemplateDedupeString' => [ 'shape' => 'DedupeString', ], 'incidentTemplateImpact' => [ 'shape' => 'Impact', ], 'incidentTemplateNotificationTargets' => [ 'shape' => 'NotificationTargetSet', ], 'incidentTemplateSummary' => [ 'shape' => 'IncidentSummary', ], 'incidentTemplateTags' => [ 'shape' => 'TagMapUpdate', ], 'incidentTemplateTitle' => [ 'shape' => 'IncidentTitle', ], 'integrations' => [ 'shape' => 'Integrations', ], ], ], 'UpdateResponsePlanOutput' => [ 'type' => 'structure', 'members' => [], ], 'UpdateTimelineEventInput' => [ 'type' => 'structure', 'required' => [ 'eventId', 'incidentRecordArn', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'eventData' => [ 'shape' => 'EventData', ], 'eventId' => [ 'shape' => 'UUID', ], 'eventReferences' => [ 'shape' => 'EventReferenceList', ], 'eventTime' => [ 'shape' => 'Timestamp', ], 'eventType' => [ 'shape' => 'TimelineEventType', ], 'incidentRecordArn' => [ 'shape' => 'Arn', ], ], ], 'UpdateTimelineEventOutput' => [ 'type' => 'structure', 'members' => [], ], 'Url' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'VariableType' => [ 'type' => 'string', 'enum' => [ 'INCIDENT_RECORD_ARN', 'INVOLVED_RESOURCES', ], ], ],];
