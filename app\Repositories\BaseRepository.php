<?php

namespace App\Repositories;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\LazyCollection;

abstract class BaseRepository
{
    protected $model;
    protected $defaultPerPage = 50;
    protected $maxPerPage = 500;

    public function __construct(Model $model)
    {
        $this->model = $model;
    }

    /**
     * الحصول على جميع السجلات مع Lazy Loading
     */
    public function getAllLazy(array $relations = [], array $columns = ['*']): LazyCollection
    {
        return $this->model->select($columns)
            ->when(!empty($relations), function ($query) use ($relations) {
                return $query->with($relations);
            })
            ->lazy();
    }

    /**
     * الحصول على السجلات مع Pagination
     */
    public function getPaginated(int $perPage = null, array $relations = [], array $columns = ['*']): LengthAwarePaginator
    {
        $perPage = min($perPage ?? $this->defaultPerPage, $this->maxPerPage);
        
        return $this->model->select($columns)
            ->when(!empty($relations), function ($query) use ($relations) {
                return $query->with($relations);
            })
            ->paginate($perPage);
    }

    /**
     * الحصول على السجلات بـ Chunking
     */
    public function getChunked(int $chunkSize = 100, callable $callback = null, array $relations = [], array $columns = ['*'])
    {
        $query = $this->model->select($columns)
            ->when(!empty($relations), function ($query) use ($relations) {
                return $query->with($relations);
            });

        if ($callback) {
            return $query->chunk($chunkSize, $callback);
        }

        return $query->lazy($chunkSize);
    }

    /**
     * البحث مع Lazy Loading
     */
    public function searchLazy(array $criteria, array $relations = [], array $columns = ['*']): LazyCollection
    {
        $query = $this->model->select($columns);

        foreach ($criteria as $field => $value) {
            if (is_array($value)) {
                $query->whereIn($field, $value);
            } else {
                $query->where($field, $value);
            }
        }

        return $query->when(!empty($relations), function ($query) use ($relations) {
            return $query->with($relations);
        })->lazy();
    }

    /**
     * الحصول على سجل واحد مع العلاقات المحددة فقط
     */
    public function findWithRelations(int $id, array $relations = [], array $columns = ['*'])
    {
        return $this->model->select($columns)
            ->when(!empty($relations), function ($query) use ($relations) {
                return $query->with($relations);
            })
            ->find($id);
    }

    /**
     * تحديث السجلات بـ Chunking لتجنب استهلاك الذاكرة
     */
    public function updateInChunks(array $criteria, array $data, int $chunkSize = 100)
    {
        $query = $this->model->newQuery();

        foreach ($criteria as $field => $value) {
            $query->where($field, $value);
        }

        return $query->chunk($chunkSize, function ($records) use ($data) {
            foreach ($records as $record) {
                $record->update($data);
            }
        });
    }

    /**
     * حذف السجلات بـ Chunking
     */
    public function deleteInChunks(array $criteria, int $chunkSize = 100)
    {
        $query = $this->model->newQuery();

        foreach ($criteria as $field => $value) {
            $query->where($field, $value);
        }

        return $query->chunk($chunkSize, function ($records) {
            foreach ($records as $record) {
                $record->delete();
            }
        });
    }

    /**
     * إحصائيات بدون تحميل البيانات في الذاكرة
     */
    public function getStats(array $criteria = [])
    {
        $query = $this->model->newQuery();

        foreach ($criteria as $field => $value) {
            $query->where($field, $value);
        }

        return [
            'count' => $query->count(),
            'sum' => $query->sum('amount') ?? 0,
            'avg' => $query->avg('amount') ?? 0,
            'max' => $query->max('amount') ?? 0,
            'min' => $query->min('amount') ?? 0,
        ];
    }

    /**
     * تصدير البيانات مع Lazy Loading
     */
    public function exportLazy(array $criteria = [], array $columns = ['*'])
    {
        $query = $this->model->select($columns);

        foreach ($criteria as $field => $value) {
            $query->where($field, $value);
        }

        return $query->lazy();
    }
}
