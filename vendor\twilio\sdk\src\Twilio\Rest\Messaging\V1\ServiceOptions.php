<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Messaging\V1;

use Twilio\Options;
use Twilio\Values;

abstract class ServiceOptions
{
    /**
     * @param string $inboundRequestUrl The URL we call using `inbound_method` when a message is received by any phone number or short code in the Service. When this property is `null`, receiving inbound messages is disabled. All messages sent to the Twilio phone number or short code will not be logged and received on the Account. If the `use_inbound_webhook_on_number` field is enabled then the webhook url defined on the phone number will override the `inbound_request_url` defined for the Messaging Service.
     * @param string $inboundMethod The HTTP method we should use to call `inbound_request_url`. Can be `GET` or `POST` and the default is `POST`.
     * @param string $fallbackUrl The URL that we call using `fallback_method` if an error occurs while retrieving or executing the TwiML from the Inbound Request URL. If the `use_inbound_webhook_on_number` field is enabled then the webhook url defined on the phone number will override the `fallback_url` defined for the Messaging Service.
     * @param string $fallbackMethod The HTTP method we should use to call `fallback_url`. Can be: `GET` or `POST`.
     * @param string $statusCallback The URL we should call to [pass status updates](https://www.twilio.com/docs/sms/api/message-resource#message-status-values) about message delivery.
     * @param bool $stickySender Whether to enable [Sticky Sender](https://www.twilio.com/docs/messaging/services#sticky-sender) on the Service instance.
     * @param bool $mmsConverter Whether to enable the [MMS Converter](https://www.twilio.com/docs/messaging/services#mms-converter) for messages sent through the Service instance.
     * @param bool $smartEncoding Whether to enable [Smart Encoding](https://www.twilio.com/docs/messaging/services#smart-encoding) for messages sent through the Service instance.
     * @param string $scanMessageContent
     * @param bool $fallbackToLongCode [OBSOLETE] Former feature used to fallback to long code sender after certain short code message failures.
     * @param bool $areaCodeGeomatch Whether to enable [Area Code Geomatch](https://www.twilio.com/docs/messaging/services#area-code-geomatch) on the Service Instance.
     * @param int $validityPeriod How long, in seconds, messages sent from the Service are valid. Can be an integer from `1` to `14,400`. Default value is `14,400`.
     * @param bool $synchronousValidation Reserved.
     * @param string $usecase A string that describes the scenario in which the Messaging Service will be used. Possible values are `notifications`, `marketing`, `verification`, `discussion`, `poll`, `undeclared`.
     * @param bool $useInboundWebhookOnNumber A boolean value that indicates either the webhook url configured on the phone number will be used or `inbound_request_url`/`fallback_url` url will be called when a message is received from the phone number. If this field is enabled then the webhook url defined on the phone number will override the `inbound_request_url`/`fallback_url` defined for the Messaging Service.
     * @return CreateServiceOptions Options builder
     */
    public static function create(
        
        string $inboundRequestUrl = Values::NONE,
        string $inboundMethod = Values::NONE,
        string $fallbackUrl = Values::NONE,
        string $fallbackMethod = Values::NONE,
        string $statusCallback = Values::NONE,
        bool $stickySender = Values::BOOL_NONE,
        bool $mmsConverter = Values::BOOL_NONE,
        bool $smartEncoding = Values::BOOL_NONE,
        string $scanMessageContent = Values::NONE,
        bool $fallbackToLongCode = Values::BOOL_NONE,
        bool $areaCodeGeomatch = Values::BOOL_NONE,
        int $validityPeriod = Values::INT_NONE,
        bool $synchronousValidation = Values::BOOL_NONE,
        string $usecase = Values::NONE,
        bool $useInboundWebhookOnNumber = Values::BOOL_NONE

    ): CreateServiceOptions
    {
        return new CreateServiceOptions(
            $inboundRequestUrl,
            $inboundMethod,
            $fallbackUrl,
            $fallbackMethod,
            $statusCallback,
            $stickySender,
            $mmsConverter,
            $smartEncoding,
            $scanMessageContent,
            $fallbackToLongCode,
            $areaCodeGeomatch,
            $validityPeriod,
            $synchronousValidation,
            $usecase,
            $useInboundWebhookOnNumber
        );
    }




    /**
     * @param string $friendlyName A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     * @param string $inboundRequestUrl The URL we call using `inbound_method` when a message is received by any phone number or short code in the Service. When this property is `null`, receiving inbound messages is disabled. All messages sent to the Twilio phone number or short code will not be logged and received on the Account. If the `use_inbound_webhook_on_number` field is enabled then the webhook url defined on the phone number will override the `inbound_request_url` defined for the Messaging Service.
     * @param string $inboundMethod The HTTP method we should use to call `inbound_request_url`. Can be `GET` or `POST` and the default is `POST`.
     * @param string $fallbackUrl The URL that we call using `fallback_method` if an error occurs while retrieving or executing the TwiML from the Inbound Request URL. If the `use_inbound_webhook_on_number` field is enabled then the webhook url defined on the phone number will override the `fallback_url` defined for the Messaging Service.
     * @param string $fallbackMethod The HTTP method we should use to call `fallback_url`. Can be: `GET` or `POST`.
     * @param string $statusCallback The URL we should call to [pass status updates](https://www.twilio.com/docs/sms/api/message-resource#message-status-values) about message delivery.
     * @param bool $stickySender Whether to enable [Sticky Sender](https://www.twilio.com/docs/messaging/services#sticky-sender) on the Service instance.
     * @param bool $mmsConverter Whether to enable the [MMS Converter](https://www.twilio.com/docs/messaging/services#mms-converter) for messages sent through the Service instance.
     * @param bool $smartEncoding Whether to enable [Smart Encoding](https://www.twilio.com/docs/messaging/services#smart-encoding) for messages sent through the Service instance.
     * @param string $scanMessageContent
     * @param bool $fallbackToLongCode [OBSOLETE] Former feature used to fallback to long code sender after certain short code message failures.
     * @param bool $areaCodeGeomatch Whether to enable [Area Code Geomatch](https://www.twilio.com/docs/messaging/services#area-code-geomatch) on the Service Instance.
     * @param int $validityPeriod How long, in seconds, messages sent from the Service are valid. Can be an integer from `1` to `14,400`. Default value is `14,400`.
     * @param bool $synchronousValidation Reserved.
     * @param string $usecase A string that describes the scenario in which the Messaging Service will be used. Possible values are `notifications`, `marketing`, `verification`, `discussion`, `poll`, `undeclared`.
     * @param bool $useInboundWebhookOnNumber A boolean value that indicates either the webhook url configured on the phone number will be used or `inbound_request_url`/`fallback_url` url will be called when a message is received from the phone number. If this field is enabled then the webhook url defined on the phone number will override the `inbound_request_url`/`fallback_url` defined for the Messaging Service.
     * @return UpdateServiceOptions Options builder
     */
    public static function update(
        
        string $friendlyName = Values::NONE,
        string $inboundRequestUrl = Values::NONE,
        string $inboundMethod = Values::NONE,
        string $fallbackUrl = Values::NONE,
        string $fallbackMethod = Values::NONE,
        string $statusCallback = Values::NONE,
        bool $stickySender = Values::BOOL_NONE,
        bool $mmsConverter = Values::BOOL_NONE,
        bool $smartEncoding = Values::BOOL_NONE,
        string $scanMessageContent = Values::NONE,
        bool $fallbackToLongCode = Values::BOOL_NONE,
        bool $areaCodeGeomatch = Values::BOOL_NONE,
        int $validityPeriod = Values::INT_NONE,
        bool $synchronousValidation = Values::BOOL_NONE,
        string $usecase = Values::NONE,
        bool $useInboundWebhookOnNumber = Values::BOOL_NONE

    ): UpdateServiceOptions
    {
        return new UpdateServiceOptions(
            $friendlyName,
            $inboundRequestUrl,
            $inboundMethod,
            $fallbackUrl,
            $fallbackMethod,
            $statusCallback,
            $stickySender,
            $mmsConverter,
            $smartEncoding,
            $scanMessageContent,
            $fallbackToLongCode,
            $areaCodeGeomatch,
            $validityPeriod,
            $synchronousValidation,
            $usecase,
            $useInboundWebhookOnNumber
        );
    }

}

class CreateServiceOptions extends Options
    {
    /**
     * @param string $inboundRequestUrl The URL we call using `inbound_method` when a message is received by any phone number or short code in the Service. When this property is `null`, receiving inbound messages is disabled. All messages sent to the Twilio phone number or short code will not be logged and received on the Account. If the `use_inbound_webhook_on_number` field is enabled then the webhook url defined on the phone number will override the `inbound_request_url` defined for the Messaging Service.
     * @param string $inboundMethod The HTTP method we should use to call `inbound_request_url`. Can be `GET` or `POST` and the default is `POST`.
     * @param string $fallbackUrl The URL that we call using `fallback_method` if an error occurs while retrieving or executing the TwiML from the Inbound Request URL. If the `use_inbound_webhook_on_number` field is enabled then the webhook url defined on the phone number will override the `fallback_url` defined for the Messaging Service.
     * @param string $fallbackMethod The HTTP method we should use to call `fallback_url`. Can be: `GET` or `POST`.
     * @param string $statusCallback The URL we should call to [pass status updates](https://www.twilio.com/docs/sms/api/message-resource#message-status-values) about message delivery.
     * @param bool $stickySender Whether to enable [Sticky Sender](https://www.twilio.com/docs/messaging/services#sticky-sender) on the Service instance.
     * @param bool $mmsConverter Whether to enable the [MMS Converter](https://www.twilio.com/docs/messaging/services#mms-converter) for messages sent through the Service instance.
     * @param bool $smartEncoding Whether to enable [Smart Encoding](https://www.twilio.com/docs/messaging/services#smart-encoding) for messages sent through the Service instance.
     * @param string $scanMessageContent
     * @param bool $fallbackToLongCode [OBSOLETE] Former feature used to fallback to long code sender after certain short code message failures.
     * @param bool $areaCodeGeomatch Whether to enable [Area Code Geomatch](https://www.twilio.com/docs/messaging/services#area-code-geomatch) on the Service Instance.
     * @param int $validityPeriod How long, in seconds, messages sent from the Service are valid. Can be an integer from `1` to `14,400`. Default value is `14,400`.
     * @param bool $synchronousValidation Reserved.
     * @param string $usecase A string that describes the scenario in which the Messaging Service will be used. Possible values are `notifications`, `marketing`, `verification`, `discussion`, `poll`, `undeclared`.
     * @param bool $useInboundWebhookOnNumber A boolean value that indicates either the webhook url configured on the phone number will be used or `inbound_request_url`/`fallback_url` url will be called when a message is received from the phone number. If this field is enabled then the webhook url defined on the phone number will override the `inbound_request_url`/`fallback_url` defined for the Messaging Service.
     */
    public function __construct(
        
        string $inboundRequestUrl = Values::NONE,
        string $inboundMethod = Values::NONE,
        string $fallbackUrl = Values::NONE,
        string $fallbackMethod = Values::NONE,
        string $statusCallback = Values::NONE,
        bool $stickySender = Values::BOOL_NONE,
        bool $mmsConverter = Values::BOOL_NONE,
        bool $smartEncoding = Values::BOOL_NONE,
        string $scanMessageContent = Values::NONE,
        bool $fallbackToLongCode = Values::BOOL_NONE,
        bool $areaCodeGeomatch = Values::BOOL_NONE,
        int $validityPeriod = Values::INT_NONE,
        bool $synchronousValidation = Values::BOOL_NONE,
        string $usecase = Values::NONE,
        bool $useInboundWebhookOnNumber = Values::BOOL_NONE

    ) {
        $this->options['inboundRequestUrl'] = $inboundRequestUrl;
        $this->options['inboundMethod'] = $inboundMethod;
        $this->options['fallbackUrl'] = $fallbackUrl;
        $this->options['fallbackMethod'] = $fallbackMethod;
        $this->options['statusCallback'] = $statusCallback;
        $this->options['stickySender'] = $stickySender;
        $this->options['mmsConverter'] = $mmsConverter;
        $this->options['smartEncoding'] = $smartEncoding;
        $this->options['scanMessageContent'] = $scanMessageContent;
        $this->options['fallbackToLongCode'] = $fallbackToLongCode;
        $this->options['areaCodeGeomatch'] = $areaCodeGeomatch;
        $this->options['validityPeriod'] = $validityPeriod;
        $this->options['synchronousValidation'] = $synchronousValidation;
        $this->options['usecase'] = $usecase;
        $this->options['useInboundWebhookOnNumber'] = $useInboundWebhookOnNumber;
    }

    /**
     * The URL we call using `inbound_method` when a message is received by any phone number or short code in the Service. When this property is `null`, receiving inbound messages is disabled. All messages sent to the Twilio phone number or short code will not be logged and received on the Account. If the `use_inbound_webhook_on_number` field is enabled then the webhook url defined on the phone number will override the `inbound_request_url` defined for the Messaging Service.
     *
     * @param string $inboundRequestUrl The URL we call using `inbound_method` when a message is received by any phone number or short code in the Service. When this property is `null`, receiving inbound messages is disabled. All messages sent to the Twilio phone number or short code will not be logged and received on the Account. If the `use_inbound_webhook_on_number` field is enabled then the webhook url defined on the phone number will override the `inbound_request_url` defined for the Messaging Service.
     * @return $this Fluent Builder
     */
    public function setInboundRequestUrl(string $inboundRequestUrl): self
    {
        $this->options['inboundRequestUrl'] = $inboundRequestUrl;
        return $this;
    }

    /**
     * The HTTP method we should use to call `inbound_request_url`. Can be `GET` or `POST` and the default is `POST`.
     *
     * @param string $inboundMethod The HTTP method we should use to call `inbound_request_url`. Can be `GET` or `POST` and the default is `POST`.
     * @return $this Fluent Builder
     */
    public function setInboundMethod(string $inboundMethod): self
    {
        $this->options['inboundMethod'] = $inboundMethod;
        return $this;
    }

    /**
     * The URL that we call using `fallback_method` if an error occurs while retrieving or executing the TwiML from the Inbound Request URL. If the `use_inbound_webhook_on_number` field is enabled then the webhook url defined on the phone number will override the `fallback_url` defined for the Messaging Service.
     *
     * @param string $fallbackUrl The URL that we call using `fallback_method` if an error occurs while retrieving or executing the TwiML from the Inbound Request URL. If the `use_inbound_webhook_on_number` field is enabled then the webhook url defined on the phone number will override the `fallback_url` defined for the Messaging Service.
     * @return $this Fluent Builder
     */
    public function setFallbackUrl(string $fallbackUrl): self
    {
        $this->options['fallbackUrl'] = $fallbackUrl;
        return $this;
    }

    /**
     * The HTTP method we should use to call `fallback_url`. Can be: `GET` or `POST`.
     *
     * @param string $fallbackMethod The HTTP method we should use to call `fallback_url`. Can be: `GET` or `POST`.
     * @return $this Fluent Builder
     */
    public function setFallbackMethod(string $fallbackMethod): self
    {
        $this->options['fallbackMethod'] = $fallbackMethod;
        return $this;
    }

    /**
     * The URL we should call to [pass status updates](https://www.twilio.com/docs/sms/api/message-resource#message-status-values) about message delivery.
     *
     * @param string $statusCallback The URL we should call to [pass status updates](https://www.twilio.com/docs/sms/api/message-resource#message-status-values) about message delivery.
     * @return $this Fluent Builder
     */
    public function setStatusCallback(string $statusCallback): self
    {
        $this->options['statusCallback'] = $statusCallback;
        return $this;
    }

    /**
     * Whether to enable [Sticky Sender](https://www.twilio.com/docs/messaging/services#sticky-sender) on the Service instance.
     *
     * @param bool $stickySender Whether to enable [Sticky Sender](https://www.twilio.com/docs/messaging/services#sticky-sender) on the Service instance.
     * @return $this Fluent Builder
     */
    public function setStickySender(bool $stickySender): self
    {
        $this->options['stickySender'] = $stickySender;
        return $this;
    }

    /**
     * Whether to enable the [MMS Converter](https://www.twilio.com/docs/messaging/services#mms-converter) for messages sent through the Service instance.
     *
     * @param bool $mmsConverter Whether to enable the [MMS Converter](https://www.twilio.com/docs/messaging/services#mms-converter) for messages sent through the Service instance.
     * @return $this Fluent Builder
     */
    public function setMmsConverter(bool $mmsConverter): self
    {
        $this->options['mmsConverter'] = $mmsConverter;
        return $this;
    }

    /**
     * Whether to enable [Smart Encoding](https://www.twilio.com/docs/messaging/services#smart-encoding) for messages sent through the Service instance.
     *
     * @param bool $smartEncoding Whether to enable [Smart Encoding](https://www.twilio.com/docs/messaging/services#smart-encoding) for messages sent through the Service instance.
     * @return $this Fluent Builder
     */
    public function setSmartEncoding(bool $smartEncoding): self
    {
        $this->options['smartEncoding'] = $smartEncoding;
        return $this;
    }

    /**
     * @param string $scanMessageContent
     * @return $this Fluent Builder
     */
    public function setScanMessageContent(string $scanMessageContent): self
    {
        $this->options['scanMessageContent'] = $scanMessageContent;
        return $this;
    }

    /**
     * [OBSOLETE] Former feature used to fallback to long code sender after certain short code message failures.
     *
     * @param bool $fallbackToLongCode [OBSOLETE] Former feature used to fallback to long code sender after certain short code message failures.
     * @return $this Fluent Builder
     */
    public function setFallbackToLongCode(bool $fallbackToLongCode): self
    {
        $this->options['fallbackToLongCode'] = $fallbackToLongCode;
        return $this;
    }

    /**
     * Whether to enable [Area Code Geomatch](https://www.twilio.com/docs/messaging/services#area-code-geomatch) on the Service Instance.
     *
     * @param bool $areaCodeGeomatch Whether to enable [Area Code Geomatch](https://www.twilio.com/docs/messaging/services#area-code-geomatch) on the Service Instance.
     * @return $this Fluent Builder
     */
    public function setAreaCodeGeomatch(bool $areaCodeGeomatch): self
    {
        $this->options['areaCodeGeomatch'] = $areaCodeGeomatch;
        return $this;
    }

    /**
     * How long, in seconds, messages sent from the Service are valid. Can be an integer from `1` to `14,400`. Default value is `14,400`.
     *
     * @param int $validityPeriod How long, in seconds, messages sent from the Service are valid. Can be an integer from `1` to `14,400`. Default value is `14,400`.
     * @return $this Fluent Builder
     */
    public function setValidityPeriod(int $validityPeriod): self
    {
        $this->options['validityPeriod'] = $validityPeriod;
        return $this;
    }

    /**
     * Reserved.
     *
     * @param bool $synchronousValidation Reserved.
     * @return $this Fluent Builder
     */
    public function setSynchronousValidation(bool $synchronousValidation): self
    {
        $this->options['synchronousValidation'] = $synchronousValidation;
        return $this;
    }

    /**
     * A string that describes the scenario in which the Messaging Service will be used. Possible values are `notifications`, `marketing`, `verification`, `discussion`, `poll`, `undeclared`.
     *
     * @param string $usecase A string that describes the scenario in which the Messaging Service will be used. Possible values are `notifications`, `marketing`, `verification`, `discussion`, `poll`, `undeclared`.
     * @return $this Fluent Builder
     */
    public function setUsecase(string $usecase): self
    {
        $this->options['usecase'] = $usecase;
        return $this;
    }

    /**
     * A boolean value that indicates either the webhook url configured on the phone number will be used or `inbound_request_url`/`fallback_url` url will be called when a message is received from the phone number. If this field is enabled then the webhook url defined on the phone number will override the `inbound_request_url`/`fallback_url` defined for the Messaging Service.
     *
     * @param bool $useInboundWebhookOnNumber A boolean value that indicates either the webhook url configured on the phone number will be used or `inbound_request_url`/`fallback_url` url will be called when a message is received from the phone number. If this field is enabled then the webhook url defined on the phone number will override the `inbound_request_url`/`fallback_url` defined for the Messaging Service.
     * @return $this Fluent Builder
     */
    public function setUseInboundWebhookOnNumber(bool $useInboundWebhookOnNumber): self
    {
        $this->options['useInboundWebhookOnNumber'] = $useInboundWebhookOnNumber;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Messaging.V1.CreateServiceOptions ' . $options . ']';
    }
}




class UpdateServiceOptions extends Options
    {
    /**
     * @param string $friendlyName A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     * @param string $inboundRequestUrl The URL we call using `inbound_method` when a message is received by any phone number or short code in the Service. When this property is `null`, receiving inbound messages is disabled. All messages sent to the Twilio phone number or short code will not be logged and received on the Account. If the `use_inbound_webhook_on_number` field is enabled then the webhook url defined on the phone number will override the `inbound_request_url` defined for the Messaging Service.
     * @param string $inboundMethod The HTTP method we should use to call `inbound_request_url`. Can be `GET` or `POST` and the default is `POST`.
     * @param string $fallbackUrl The URL that we call using `fallback_method` if an error occurs while retrieving or executing the TwiML from the Inbound Request URL. If the `use_inbound_webhook_on_number` field is enabled then the webhook url defined on the phone number will override the `fallback_url` defined for the Messaging Service.
     * @param string $fallbackMethod The HTTP method we should use to call `fallback_url`. Can be: `GET` or `POST`.
     * @param string $statusCallback The URL we should call to [pass status updates](https://www.twilio.com/docs/sms/api/message-resource#message-status-values) about message delivery.
     * @param bool $stickySender Whether to enable [Sticky Sender](https://www.twilio.com/docs/messaging/services#sticky-sender) on the Service instance.
     * @param bool $mmsConverter Whether to enable the [MMS Converter](https://www.twilio.com/docs/messaging/services#mms-converter) for messages sent through the Service instance.
     * @param bool $smartEncoding Whether to enable [Smart Encoding](https://www.twilio.com/docs/messaging/services#smart-encoding) for messages sent through the Service instance.
     * @param string $scanMessageContent
     * @param bool $fallbackToLongCode [OBSOLETE] Former feature used to fallback to long code sender after certain short code message failures.
     * @param bool $areaCodeGeomatch Whether to enable [Area Code Geomatch](https://www.twilio.com/docs/messaging/services#area-code-geomatch) on the Service Instance.
     * @param int $validityPeriod How long, in seconds, messages sent from the Service are valid. Can be an integer from `1` to `14,400`. Default value is `14,400`.
     * @param bool $synchronousValidation Reserved.
     * @param string $usecase A string that describes the scenario in which the Messaging Service will be used. Possible values are `notifications`, `marketing`, `verification`, `discussion`, `poll`, `undeclared`.
     * @param bool $useInboundWebhookOnNumber A boolean value that indicates either the webhook url configured on the phone number will be used or `inbound_request_url`/`fallback_url` url will be called when a message is received from the phone number. If this field is enabled then the webhook url defined on the phone number will override the `inbound_request_url`/`fallback_url` defined for the Messaging Service.
     */
    public function __construct(
        
        string $friendlyName = Values::NONE,
        string $inboundRequestUrl = Values::NONE,
        string $inboundMethod = Values::NONE,
        string $fallbackUrl = Values::NONE,
        string $fallbackMethod = Values::NONE,
        string $statusCallback = Values::NONE,
        bool $stickySender = Values::BOOL_NONE,
        bool $mmsConverter = Values::BOOL_NONE,
        bool $smartEncoding = Values::BOOL_NONE,
        string $scanMessageContent = Values::NONE,
        bool $fallbackToLongCode = Values::BOOL_NONE,
        bool $areaCodeGeomatch = Values::BOOL_NONE,
        int $validityPeriod = Values::INT_NONE,
        bool $synchronousValidation = Values::BOOL_NONE,
        string $usecase = Values::NONE,
        bool $useInboundWebhookOnNumber = Values::BOOL_NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
        $this->options['inboundRequestUrl'] = $inboundRequestUrl;
        $this->options['inboundMethod'] = $inboundMethod;
        $this->options['fallbackUrl'] = $fallbackUrl;
        $this->options['fallbackMethod'] = $fallbackMethod;
        $this->options['statusCallback'] = $statusCallback;
        $this->options['stickySender'] = $stickySender;
        $this->options['mmsConverter'] = $mmsConverter;
        $this->options['smartEncoding'] = $smartEncoding;
        $this->options['scanMessageContent'] = $scanMessageContent;
        $this->options['fallbackToLongCode'] = $fallbackToLongCode;
        $this->options['areaCodeGeomatch'] = $areaCodeGeomatch;
        $this->options['validityPeriod'] = $validityPeriod;
        $this->options['synchronousValidation'] = $synchronousValidation;
        $this->options['usecase'] = $usecase;
        $this->options['useInboundWebhookOnNumber'] = $useInboundWebhookOnNumber;
    }

    /**
     * A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     *
     * @param string $friendlyName A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * The URL we call using `inbound_method` when a message is received by any phone number or short code in the Service. When this property is `null`, receiving inbound messages is disabled. All messages sent to the Twilio phone number or short code will not be logged and received on the Account. If the `use_inbound_webhook_on_number` field is enabled then the webhook url defined on the phone number will override the `inbound_request_url` defined for the Messaging Service.
     *
     * @param string $inboundRequestUrl The URL we call using `inbound_method` when a message is received by any phone number or short code in the Service. When this property is `null`, receiving inbound messages is disabled. All messages sent to the Twilio phone number or short code will not be logged and received on the Account. If the `use_inbound_webhook_on_number` field is enabled then the webhook url defined on the phone number will override the `inbound_request_url` defined for the Messaging Service.
     * @return $this Fluent Builder
     */
    public function setInboundRequestUrl(string $inboundRequestUrl): self
    {
        $this->options['inboundRequestUrl'] = $inboundRequestUrl;
        return $this;
    }

    /**
     * The HTTP method we should use to call `inbound_request_url`. Can be `GET` or `POST` and the default is `POST`.
     *
     * @param string $inboundMethod The HTTP method we should use to call `inbound_request_url`. Can be `GET` or `POST` and the default is `POST`.
     * @return $this Fluent Builder
     */
    public function setInboundMethod(string $inboundMethod): self
    {
        $this->options['inboundMethod'] = $inboundMethod;
        return $this;
    }

    /**
     * The URL that we call using `fallback_method` if an error occurs while retrieving or executing the TwiML from the Inbound Request URL. If the `use_inbound_webhook_on_number` field is enabled then the webhook url defined on the phone number will override the `fallback_url` defined for the Messaging Service.
     *
     * @param string $fallbackUrl The URL that we call using `fallback_method` if an error occurs while retrieving or executing the TwiML from the Inbound Request URL. If the `use_inbound_webhook_on_number` field is enabled then the webhook url defined on the phone number will override the `fallback_url` defined for the Messaging Service.
     * @return $this Fluent Builder
     */
    public function setFallbackUrl(string $fallbackUrl): self
    {
        $this->options['fallbackUrl'] = $fallbackUrl;
        return $this;
    }

    /**
     * The HTTP method we should use to call `fallback_url`. Can be: `GET` or `POST`.
     *
     * @param string $fallbackMethod The HTTP method we should use to call `fallback_url`. Can be: `GET` or `POST`.
     * @return $this Fluent Builder
     */
    public function setFallbackMethod(string $fallbackMethod): self
    {
        $this->options['fallbackMethod'] = $fallbackMethod;
        return $this;
    }

    /**
     * The URL we should call to [pass status updates](https://www.twilio.com/docs/sms/api/message-resource#message-status-values) about message delivery.
     *
     * @param string $statusCallback The URL we should call to [pass status updates](https://www.twilio.com/docs/sms/api/message-resource#message-status-values) about message delivery.
     * @return $this Fluent Builder
     */
    public function setStatusCallback(string $statusCallback): self
    {
        $this->options['statusCallback'] = $statusCallback;
        return $this;
    }

    /**
     * Whether to enable [Sticky Sender](https://www.twilio.com/docs/messaging/services#sticky-sender) on the Service instance.
     *
     * @param bool $stickySender Whether to enable [Sticky Sender](https://www.twilio.com/docs/messaging/services#sticky-sender) on the Service instance.
     * @return $this Fluent Builder
     */
    public function setStickySender(bool $stickySender): self
    {
        $this->options['stickySender'] = $stickySender;
        return $this;
    }

    /**
     * Whether to enable the [MMS Converter](https://www.twilio.com/docs/messaging/services#mms-converter) for messages sent through the Service instance.
     *
     * @param bool $mmsConverter Whether to enable the [MMS Converter](https://www.twilio.com/docs/messaging/services#mms-converter) for messages sent through the Service instance.
     * @return $this Fluent Builder
     */
    public function setMmsConverter(bool $mmsConverter): self
    {
        $this->options['mmsConverter'] = $mmsConverter;
        return $this;
    }

    /**
     * Whether to enable [Smart Encoding](https://www.twilio.com/docs/messaging/services#smart-encoding) for messages sent through the Service instance.
     *
     * @param bool $smartEncoding Whether to enable [Smart Encoding](https://www.twilio.com/docs/messaging/services#smart-encoding) for messages sent through the Service instance.
     * @return $this Fluent Builder
     */
    public function setSmartEncoding(bool $smartEncoding): self
    {
        $this->options['smartEncoding'] = $smartEncoding;
        return $this;
    }

    /**
     * @param string $scanMessageContent
     * @return $this Fluent Builder
     */
    public function setScanMessageContent(string $scanMessageContent): self
    {
        $this->options['scanMessageContent'] = $scanMessageContent;
        return $this;
    }

    /**
     * [OBSOLETE] Former feature used to fallback to long code sender after certain short code message failures.
     *
     * @param bool $fallbackToLongCode [OBSOLETE] Former feature used to fallback to long code sender after certain short code message failures.
     * @return $this Fluent Builder
     */
    public function setFallbackToLongCode(bool $fallbackToLongCode): self
    {
        $this->options['fallbackToLongCode'] = $fallbackToLongCode;
        return $this;
    }

    /**
     * Whether to enable [Area Code Geomatch](https://www.twilio.com/docs/messaging/services#area-code-geomatch) on the Service Instance.
     *
     * @param bool $areaCodeGeomatch Whether to enable [Area Code Geomatch](https://www.twilio.com/docs/messaging/services#area-code-geomatch) on the Service Instance.
     * @return $this Fluent Builder
     */
    public function setAreaCodeGeomatch(bool $areaCodeGeomatch): self
    {
        $this->options['areaCodeGeomatch'] = $areaCodeGeomatch;
        return $this;
    }

    /**
     * How long, in seconds, messages sent from the Service are valid. Can be an integer from `1` to `14,400`. Default value is `14,400`.
     *
     * @param int $validityPeriod How long, in seconds, messages sent from the Service are valid. Can be an integer from `1` to `14,400`. Default value is `14,400`.
     * @return $this Fluent Builder
     */
    public function setValidityPeriod(int $validityPeriod): self
    {
        $this->options['validityPeriod'] = $validityPeriod;
        return $this;
    }

    /**
     * Reserved.
     *
     * @param bool $synchronousValidation Reserved.
     * @return $this Fluent Builder
     */
    public function setSynchronousValidation(bool $synchronousValidation): self
    {
        $this->options['synchronousValidation'] = $synchronousValidation;
        return $this;
    }

    /**
     * A string that describes the scenario in which the Messaging Service will be used. Possible values are `notifications`, `marketing`, `verification`, `discussion`, `poll`, `undeclared`.
     *
     * @param string $usecase A string that describes the scenario in which the Messaging Service will be used. Possible values are `notifications`, `marketing`, `verification`, `discussion`, `poll`, `undeclared`.
     * @return $this Fluent Builder
     */
    public function setUsecase(string $usecase): self
    {
        $this->options['usecase'] = $usecase;
        return $this;
    }

    /**
     * A boolean value that indicates either the webhook url configured on the phone number will be used or `inbound_request_url`/`fallback_url` url will be called when a message is received from the phone number. If this field is enabled then the webhook url defined on the phone number will override the `inbound_request_url`/`fallback_url` defined for the Messaging Service.
     *
     * @param bool $useInboundWebhookOnNumber A boolean value that indicates either the webhook url configured on the phone number will be used or `inbound_request_url`/`fallback_url` url will be called when a message is received from the phone number. If this field is enabled then the webhook url defined on the phone number will override the `inbound_request_url`/`fallback_url` defined for the Messaging Service.
     * @return $this Fluent Builder
     */
    public function setUseInboundWebhookOnNumber(bool $useInboundWebhookOnNumber): self
    {
        $this->options['useInboundWebhookOnNumber'] = $useInboundWebhookOnNumber;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Messaging.V1.UpdateServiceOptions ' . $options . ']';
    }
}

