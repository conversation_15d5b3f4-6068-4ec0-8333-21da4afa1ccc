<?php
// This file was auto-generated from sdk-root/src/data/license-manager-user-subscriptions/2018-05-10/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-10', 'endpointPrefix' => 'license-manager-user-subscriptions', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS License Manager User Subscriptions', 'serviceId' => 'License Manager User Subscriptions', 'signatureVersion' => 'v4', 'signingName' => 'license-manager-user-subscriptions', 'uid' => 'license-manager-user-subscriptions-2018-05-10', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AssociateUser' => [ 'name' => 'AssociateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/user/AssociateUser', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateUserRequest', ], 'output' => [ 'shape' => 'AssociateUserResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateLicenseServerEndpoint' => [ 'name' => 'CreateLicenseServerEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/license-server/CreateLicenseServerEndpoint', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateLicenseServerEndpointRequest', ], 'output' => [ 'shape' => 'CreateLicenseServerEndpointResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteLicenseServerEndpoint' => [ 'name' => 'DeleteLicenseServerEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/license-server/DeleteLicenseServerEndpoint', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteLicenseServerEndpointRequest', ], 'output' => [ 'shape' => 'DeleteLicenseServerEndpointResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeregisterIdentityProvider' => [ 'name' => 'DeregisterIdentityProvider', 'http' => [ 'method' => 'POST', 'requestUri' => '/identity-provider/DeregisterIdentityProvider', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeregisterIdentityProviderRequest', ], 'output' => [ 'shape' => 'DeregisterIdentityProviderResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DisassociateUser' => [ 'name' => 'DisassociateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/user/DisassociateUser', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateUserRequest', ], 'output' => [ 'shape' => 'DisassociateUserResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'ListIdentityProviders' => [ 'name' => 'ListIdentityProviders', 'http' => [ 'method' => 'POST', 'requestUri' => '/identity-provider/ListIdentityProviders', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIdentityProvidersRequest', ], 'output' => [ 'shape' => 'ListIdentityProvidersResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListInstances' => [ 'name' => 'ListInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/instance/ListInstances', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListInstancesRequest', ], 'output' => [ 'shape' => 'ListInstancesResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListLicenseServerEndpoints' => [ 'name' => 'ListLicenseServerEndpoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/license-server/ListLicenseServerEndpoints', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListLicenseServerEndpointsRequest', ], 'output' => [ 'shape' => 'ListLicenseServerEndpointsResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListProductSubscriptions' => [ 'name' => 'ListProductSubscriptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/user/ListProductSubscriptions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListProductSubscriptionsRequest', ], 'output' => [ 'shape' => 'ListProductSubscriptionsResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListUserAssociations' => [ 'name' => 'ListUserAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/user/ListUserAssociations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListUserAssociationsRequest', ], 'output' => [ 'shape' => 'ListUserAssociationsResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'RegisterIdentityProvider' => [ 'name' => 'RegisterIdentityProvider', 'http' => [ 'method' => 'POST', 'requestUri' => '/identity-provider/RegisterIdentityProvider', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RegisterIdentityProviderRequest', ], 'output' => [ 'shape' => 'RegisterIdentityProviderResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'StartProductSubscription' => [ 'name' => 'StartProductSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/user/StartProductSubscription', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartProductSubscriptionRequest', ], 'output' => [ 'shape' => 'StartProductSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StopProductSubscription' => [ 'name' => 'StopProductSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/user/StopProductSubscription', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopProductSubscriptionRequest', ], 'output' => [ 'shape' => 'StopProductSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateIdentityProviderSettings' => [ 'name' => 'UpdateIdentityProviderSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/identity-provider/UpdateIdentityProviderSettings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateIdentityProviderSettingsRequest', ], 'output' => [ 'shape' => 'UpdateIdentityProviderSettingsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ActiveDirectoryIdentityProvider' => [ 'type' => 'structure', 'members' => [ 'ActiveDirectorySettings' => [ 'shape' => 'ActiveDirectorySettings', ], 'ActiveDirectoryType' => [ 'shape' => 'ActiveDirectoryType', ], 'DirectoryId' => [ 'shape' => 'Directory', ], ], ], 'ActiveDirectorySettings' => [ 'type' => 'structure', 'members' => [ 'DomainCredentialsProvider' => [ 'shape' => 'CredentialsProvider', ], 'DomainIpv4List' => [ 'shape' => 'ActiveDirectorySettingsDomainIpv4ListList', ], 'DomainName' => [ 'shape' => 'String', ], 'DomainNetworkSettings' => [ 'shape' => 'DomainNetworkSettings', ], ], ], 'ActiveDirectorySettingsDomainIpv4ListList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpV4', ], 'max' => 2, 'min' => 1, ], 'ActiveDirectoryType' => [ 'type' => 'string', 'enum' => [ 'SELF_MANAGED', 'AWS_MANAGED', ], ], 'Arn' => [ 'type' => 'string', 'pattern' => '^arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{1,63}:[a-zA-Z0-9-\\.]{1,510}/[a-zA-Z0-9-\\.]{1,510}$', ], 'AssociateUserRequest' => [ 'type' => 'structure', 'required' => [ 'IdentityProvider', 'InstanceId', 'Username', ], 'members' => [ 'Domain' => [ 'shape' => 'String', ], 'IdentityProvider' => [ 'shape' => 'IdentityProvider', ], 'InstanceId' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'Tags', ], 'Username' => [ 'shape' => 'String', ], ], ], 'AssociateUserResponse' => [ 'type' => 'structure', 'required' => [ 'InstanceUserSummary', ], 'members' => [ 'InstanceUserSummary' => [ 'shape' => 'InstanceUserSummary', ], ], ], 'BoxInteger' => [ 'type' => 'integer', 'box' => true, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, 'fault' => true, ], 'CreateLicenseServerEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'IdentityProviderArn', 'LicenseServerSettings', ], 'members' => [ 'IdentityProviderArn' => [ 'shape' => 'Arn', ], 'LicenseServerSettings' => [ 'shape' => 'LicenseServerSettings', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateLicenseServerEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'IdentityProviderArn' => [ 'shape' => 'Arn', ], 'LicenseServerEndpointArn' => [ 'shape' => 'Arn', ], ], ], 'CredentialsProvider' => [ 'type' => 'structure', 'members' => [ 'SecretsManagerCredentialsProvider' => [ 'shape' => 'SecretsManagerCredentialsProvider', ], ], 'union' => true, ], 'DeleteLicenseServerEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'LicenseServerEndpointArn', 'ServerType', ], 'members' => [ 'LicenseServerEndpointArn' => [ 'shape' => 'Arn', ], 'ServerType' => [ 'shape' => 'ServerType', ], ], ], 'DeleteLicenseServerEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'LicenseServerEndpoint' => [ 'shape' => 'LicenseServerEndpoint', ], ], ], 'DeregisterIdentityProviderRequest' => [ 'type' => 'structure', 'members' => [ 'IdentityProvider' => [ 'shape' => 'IdentityProvider', ], 'IdentityProviderArn' => [ 'shape' => 'Arn', ], 'Product' => [ 'shape' => 'String', ], ], ], 'DeregisterIdentityProviderResponse' => [ 'type' => 'structure', 'required' => [ 'IdentityProviderSummary', ], 'members' => [ 'IdentityProviderSummary' => [ 'shape' => 'IdentityProviderSummary', ], ], ], 'Directory' => [ 'type' => 'string', 'pattern' => '^(d|sd)-[0-9a-f]{10}$', ], 'DisassociateUserRequest' => [ 'type' => 'structure', 'members' => [ 'Domain' => [ 'shape' => 'String', ], 'IdentityProvider' => [ 'shape' => 'IdentityProvider', ], 'InstanceId' => [ 'shape' => 'String', ], 'InstanceUserArn' => [ 'shape' => 'Arn', ], 'Username' => [ 'shape' => 'String', ], ], ], 'DisassociateUserResponse' => [ 'type' => 'structure', 'required' => [ 'InstanceUserSummary', ], 'members' => [ 'InstanceUserSummary' => [ 'shape' => 'InstanceUserSummary', ], ], ], 'DomainNetworkSettings' => [ 'type' => 'structure', 'required' => [ 'Subnets', ], 'members' => [ 'Subnets' => [ 'shape' => 'DomainNetworkSettingsSubnetsList', ], ], ], 'DomainNetworkSettingsSubnetsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Subnet', ], 'min' => 1, ], 'Filter' => [ 'type' => 'structure', 'members' => [ 'Attribute' => [ 'shape' => 'String', ], 'Operation' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], ], 'IdentityProvider' => [ 'type' => 'structure', 'members' => [ 'ActiveDirectoryIdentityProvider' => [ 'shape' => 'ActiveDirectoryIdentityProvider', ], ], 'union' => true, ], 'IdentityProviderSummary' => [ 'type' => 'structure', 'required' => [ 'IdentityProvider', 'Product', 'Settings', 'Status', ], 'members' => [ 'FailureMessage' => [ 'shape' => 'String', ], 'IdentityProvider' => [ 'shape' => 'IdentityProvider', ], 'IdentityProviderArn' => [ 'shape' => 'Arn', ], 'Product' => [ 'shape' => 'String', ], 'Settings' => [ 'shape' => 'Settings', ], 'Status' => [ 'shape' => 'String', ], ], ], 'IdentityProviderSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdentityProviderSummary', ], ], 'InstanceSummary' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Products', 'Status', ], 'members' => [ 'InstanceId' => [ 'shape' => 'String', ], 'LastStatusCheckDate' => [ 'shape' => 'String', ], 'Products' => [ 'shape' => 'StringList', ], 'Status' => [ 'shape' => 'String', ], 'StatusMessage' => [ 'shape' => 'String', ], ], ], 'InstanceSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceSummary', ], ], 'InstanceUserSummary' => [ 'type' => 'structure', 'required' => [ 'IdentityProvider', 'InstanceId', 'Status', 'Username', ], 'members' => [ 'AssociationDate' => [ 'shape' => 'String', ], 'DisassociationDate' => [ 'shape' => 'String', ], 'Domain' => [ 'shape' => 'String', ], 'IdentityProvider' => [ 'shape' => 'IdentityProvider', ], 'InstanceId' => [ 'shape' => 'String', ], 'InstanceUserArn' => [ 'shape' => 'Arn', ], 'Status' => [ 'shape' => 'String', ], 'StatusMessage' => [ 'shape' => 'String', ], 'Username' => [ 'shape' => 'String', ], ], ], 'InstanceUserSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceUserSummary', ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'IpV4' => [ 'type' => 'string', 'pattern' => '^(?:(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])(\\.(?!$)|$)){4}$', ], 'LicenseServer' => [ 'type' => 'structure', 'members' => [ 'HealthStatus' => [ 'shape' => 'LicenseServerHealthStatus', ], 'Ipv4Address' => [ 'shape' => 'String', ], 'ProvisioningStatus' => [ 'shape' => 'LicenseServerEndpointProvisioningStatus', ], ], ], 'LicenseServerEndpoint' => [ 'type' => 'structure', 'members' => [ 'CreationTime' => [ 'shape' => 'Timestamp', ], 'IdentityProviderArn' => [ 'shape' => 'String', ], 'LicenseServerEndpointArn' => [ 'shape' => 'Arn', ], 'LicenseServerEndpointId' => [ 'shape' => 'LicenseServerEndpointId', ], 'LicenseServerEndpointProvisioningStatus' => [ 'shape' => 'LicenseServerEndpointProvisioningStatus', ], 'LicenseServers' => [ 'shape' => 'LicenseServerList', ], 'ServerEndpoint' => [ 'shape' => 'ServerEndpoint', ], 'ServerType' => [ 'shape' => 'ServerType', ], 'StatusMessage' => [ 'shape' => 'String', ], ], ], 'LicenseServerEndpointId' => [ 'type' => 'string', ], 'LicenseServerEndpointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LicenseServerEndpoint', ], ], 'LicenseServerEndpointProvisioningStatus' => [ 'type' => 'string', 'enum' => [ 'PROVISIONING', 'PROVISIONING_FAILED', 'PROVISIONED', 'DELETING', 'DELETION_FAILED', 'DELETED', ], ], 'LicenseServerHealthStatus' => [ 'type' => 'string', 'enum' => [ 'HEALTHY', 'UNHEALTHY', 'NOT_APPLICABLE', ], ], 'LicenseServerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LicenseServer', ], ], 'LicenseServerSettings' => [ 'type' => 'structure', 'required' => [ 'ServerSettings', 'ServerType', ], 'members' => [ 'ServerSettings' => [ 'shape' => 'ServerSettings', ], 'ServerType' => [ 'shape' => 'ServerType', ], ], ], 'ListIdentityProvidersRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxResults' => [ 'shape' => 'BoxInteger', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListIdentityProvidersResponse' => [ 'type' => 'structure', 'required' => [ 'IdentityProviderSummaries', ], 'members' => [ 'IdentityProviderSummaries' => [ 'shape' => 'IdentityProviderSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListInstancesRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxResults' => [ 'shape' => 'BoxInteger', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListInstancesResponse' => [ 'type' => 'structure', 'members' => [ 'InstanceSummaries' => [ 'shape' => 'InstanceSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListLicenseServerEndpointsRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxResults' => [ 'shape' => 'ListLicenseServerEndpointsRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListLicenseServerEndpointsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListLicenseServerEndpointsResponse' => [ 'type' => 'structure', 'members' => [ 'LicenseServerEndpoints' => [ 'shape' => 'LicenseServerEndpointList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListProductSubscriptionsRequest' => [ 'type' => 'structure', 'required' => [ 'IdentityProvider', ], 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'IdentityProvider' => [ 'shape' => 'IdentityProvider', ], 'MaxResults' => [ 'shape' => 'BoxInteger', ], 'NextToken' => [ 'shape' => 'String', ], 'Product' => [ 'shape' => 'String', ], ], ], 'ListProductSubscriptionsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'ProductUserSummaries' => [ 'shape' => 'ProductUserSummaryList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'Tags', ], ], ], 'ListUserAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'IdentityProvider', 'InstanceId', ], 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'IdentityProvider' => [ 'shape' => 'IdentityProvider', ], 'InstanceId' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'BoxInteger', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListUserAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'InstanceUserSummaries' => [ 'shape' => 'InstanceUserSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ProductUserSummary' => [ 'type' => 'structure', 'required' => [ 'IdentityProvider', 'Product', 'Status', 'Username', ], 'members' => [ 'Domain' => [ 'shape' => 'String', ], 'IdentityProvider' => [ 'shape' => 'IdentityProvider', ], 'Product' => [ 'shape' => 'String', ], 'ProductUserArn' => [ 'shape' => 'Arn', ], 'Status' => [ 'shape' => 'String', ], 'StatusMessage' => [ 'shape' => 'String', ], 'SubscriptionEndDate' => [ 'shape' => 'String', ], 'SubscriptionStartDate' => [ 'shape' => 'String', ], 'Username' => [ 'shape' => 'String', ], ], ], 'ProductUserSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProductUserSummary', ], ], 'RdsSalSettings' => [ 'type' => 'structure', 'required' => [ 'RdsSalCredentialsProvider', ], 'members' => [ 'RdsSalCredentialsProvider' => [ 'shape' => 'CredentialsProvider', ], ], ], 'RegisterIdentityProviderRequest' => [ 'type' => 'structure', 'required' => [ 'IdentityProvider', 'Product', ], 'members' => [ 'IdentityProvider' => [ 'shape' => 'IdentityProvider', ], 'Product' => [ 'shape' => 'String', ], 'Settings' => [ 'shape' => 'Settings', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'RegisterIdentityProviderResponse' => [ 'type' => 'structure', 'required' => [ 'IdentityProviderSummary', ], 'members' => [ 'IdentityProviderSummary' => [ 'shape' => 'IdentityProviderSummary', ], ], ], 'ResourceArn' => [ 'type' => 'string', 'pattern' => '^arn:([a-z0-9-\\.]{1,63}):([a-z0-9-\\.]{1,63}):([a-z0-9-\\.]{1,63}):([a-z0-9-\\.]{1,63}):([a-z0-9-\\.]{1,510})/([a-z0-9-\\.]{1,510})$', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'SecretsManagerCredentialsProvider' => [ 'type' => 'structure', 'members' => [ 'SecretId' => [ 'shape' => 'SecretsManagerCredentialsProviderSecretIdString', ], ], ], 'SecretsManagerCredentialsProviderSecretIdString' => [ 'type' => 'string', 'min' => 1, ], 'SecurityGroup' => [ 'type' => 'string', 'max' => 200, 'min' => 5, 'pattern' => '^sg-(([0-9a-z]{8})|([0-9a-z]{17}))$', ], 'ServerEndpoint' => [ 'type' => 'structure', 'members' => [ 'Endpoint' => [ 'shape' => 'String', ], ], ], 'ServerSettings' => [ 'type' => 'structure', 'members' => [ 'RdsSalSettings' => [ 'shape' => 'RdsSalSettings', ], ], 'union' => true, ], 'ServerType' => [ 'type' => 'string', 'enum' => [ 'RDS_SAL', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'Settings' => [ 'type' => 'structure', 'required' => [ 'SecurityGroupId', 'Subnets', ], 'members' => [ 'SecurityGroupId' => [ 'shape' => 'SecurityGroup', ], 'Subnets' => [ 'shape' => 'SettingsSubnetsList', ], ], ], 'SettingsSubnetsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Subnet', ], 'min' => 1, ], 'StartProductSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'IdentityProvider', 'Product', 'Username', ], 'members' => [ 'Domain' => [ 'shape' => 'String', ], 'IdentityProvider' => [ 'shape' => 'IdentityProvider', ], 'Product' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'Tags', ], 'Username' => [ 'shape' => 'String', ], ], ], 'StartProductSubscriptionResponse' => [ 'type' => 'structure', 'required' => [ 'ProductUserSummary', ], 'members' => [ 'ProductUserSummary' => [ 'shape' => 'ProductUserSummary', ], ], ], 'StopProductSubscriptionRequest' => [ 'type' => 'structure', 'members' => [ 'Domain' => [ 'shape' => 'String', ], 'IdentityProvider' => [ 'shape' => 'IdentityProvider', ], 'Product' => [ 'shape' => 'String', ], 'ProductUserArn' => [ 'shape' => 'Arn', ], 'Username' => [ 'shape' => 'String', ], ], ], 'StopProductSubscriptionResponse' => [ 'type' => 'structure', 'required' => [ 'ProductUserSummary', ], 'members' => [ 'ProductUserSummary' => [ 'shape' => 'ProductUserSummary', ], ], ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Subnet' => [ 'type' => 'string', 'pattern' => '^subnet-[a-z0-9]{8,17}', ], 'Subnets' => [ 'type' => 'list', 'member' => [ 'shape' => 'Subnet', ], ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 50, 'min' => 0, 'sensitive' => true, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], 'max' => 50, 'min' => 0, 'sensitive' => true, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateIdentityProviderSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'UpdateSettings', ], 'members' => [ 'IdentityProvider' => [ 'shape' => 'IdentityProvider', ], 'IdentityProviderArn' => [ 'shape' => 'Arn', ], 'Product' => [ 'shape' => 'String', ], 'UpdateSettings' => [ 'shape' => 'UpdateSettings', ], ], ], 'UpdateIdentityProviderSettingsResponse' => [ 'type' => 'structure', 'required' => [ 'IdentityProviderSummary', ], 'members' => [ 'IdentityProviderSummary' => [ 'shape' => 'IdentityProviderSummary', ], ], ], 'UpdateSettings' => [ 'type' => 'structure', 'required' => [ 'AddSubnets', 'RemoveSubnets', ], 'members' => [ 'AddSubnets' => [ 'shape' => 'Subnets', ], 'RemoveSubnets' => [ 'shape' => 'Subnets', ], 'SecurityGroupId' => [ 'shape' => 'SecurityGroup', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], ],];
