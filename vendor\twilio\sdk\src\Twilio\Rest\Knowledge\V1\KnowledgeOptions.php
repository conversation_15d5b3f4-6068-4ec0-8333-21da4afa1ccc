<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Knowledge
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Knowledge\V1;

use Twilio\Options;
use Twilio\Values;

abstract class KnowledgeOptions
{



    /**
     * @param string $tags Json array of tag and value pairs for tag filtering.
     * @return ReadKnowledgeOptions Options builder
     */
    public static function read(
        
        string $tags = Values::NONE

    ): ReadKnowledgeOptions
    {
        return new ReadKnowledgeOptions(
            $tags
        );
    }


}




class ReadKnowledgeOptions extends Options
    {
    /**
     * @param string $tags Json array of tag and value pairs for tag filtering.
     */
    public function __construct(
        
        string $tags = Values::NONE

    ) {
        $this->options['tags'] = $tags;
    }

    /**
     * Json array of tag and value pairs for tag filtering.
     *
     * @param string $tags Json array of tag and value pairs for tag filtering.
     * @return $this Fluent Builder
     */
    public function setTags(string $tags): self
    {
        $this->options['tags'] = $tags;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Knowledge.V1.ReadKnowledgeOptions ' . $options . ']';
    }
}


