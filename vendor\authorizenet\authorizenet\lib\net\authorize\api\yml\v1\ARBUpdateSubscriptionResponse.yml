net\authorize\api\contract\v1\ARBUpdateSubscriptionResponse:
    xml_root_name: ARBUpdateSubscriptionResponse
    xml_root_namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
    properties:
        profile:
            expose: true
            access_type: public_method
            serialized_name: profile
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getProfile
                setter: setProfile
            type: net\authorize\api\contract\v1\CustomerProfileIdType
