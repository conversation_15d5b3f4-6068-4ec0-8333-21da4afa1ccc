<?php

/**
 * This file is part of the ramsey/collection library
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @copyright Copyright (c) <PERSON> <<EMAIL>>
 * @license http://opensource.org/licenses/MIT MIT
 */

declare(strict_types=1);

namespace Ramsey\Collection\Map;

/**
 * `AssociativeArrayMap` represents a standard associative array object.
 *
 * @extends AbstractMap<string, mixed>
 */
class AssociativeArrayMap extends AbstractMap
{
}
