<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Intelligence
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Intelligence\V2;

use Twilio\ListResource;
use Twilio\Version;


class OperatorAttachmentList extends ListResource
    {
    /**
     * Construct the OperatorAttachmentList
     *
     * @param Version $version Version that contains the resource
     */
    public function __construct(
        Version $version
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        ];
    }

    /**
     * Constructs a OperatorAttachmentContext
     *
     * @param string $serviceSid The unique SID identifier of the Service.
     *
     * @param string $operatorSid The unique SID identifier of the Operator. Allows both Custom and Pre-built Operators.
     */
    public function getContext(
        string $serviceSid
        , string $operatorSid
        
    ): OperatorAttachmentContext
    {
        return new OperatorAttachmentContext(
            $this->version,
            $serviceSid,
            $operatorSid
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Intelligence.V2.OperatorAttachmentList]';
    }
}
