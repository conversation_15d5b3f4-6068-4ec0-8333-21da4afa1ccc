<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\ErrorHandler\Internal;

/**
 * This class has been generated by extract-tentative-return-types.php.
 *
 * @internal
 */
class TentativeTypes
{
    public const RETURN_TYPES = [
        'CURLFile' => [
            'getFilename' => 'string',
            'getMimeType' => 'string',
            'getPostFilename' => 'string',
            'setMimeType' => 'void',
            'setPostFilename' => 'void',
        ],
        'DateTimeInterface' => [
            'format' => 'string',
            'getTimezone' => 'DateTimeZone|false',
            'getOffset' => 'int',
            'getTimestamp' => 'int',
            'diff' => 'DateInterval',
            '__wakeup' => 'void',
        ],
        'DateTime' => [
            '__wakeup' => 'void',
            '__set_state' => 'DateTime',
            'createFromImmutable' => 'static',
            'createFromFormat' => 'DateTime|false',
            'getLastErrors' => 'array|false',
            'format' => 'string',
            'modify' => 'DateTime|false',
            'add' => 'DateTime',
            'sub' => 'DateTime',
            'getTimezone' => 'DateTimeZone|false',
            'setTimezone' => 'DateTime',
            'getOffset' => 'int',
            'setTime' => 'DateTime',
            'setDate' => 'DateTime',
            'setISODate' => 'DateTime',
            'setTimestamp' => 'DateTime',
            'getTimestamp' => 'int',
            'diff' => 'DateInterval',
        ],
        'DateTimeImmutable' => [
            '__wakeup' => 'void',
            '__set_state' => 'DateTimeImmutable',
            'createFromFormat' => 'DateTimeImmutable|false',
            'getLastErrors' => 'array|false',
            'format' => 'string',
            'getTimezone' => 'DateTimeZone|false',
            'getOffset' => 'int',
            'getTimestamp' => 'int',
            'diff' => 'DateInterval',
            'modify' => 'DateTimeImmutable|false',
            'add' => 'DateTimeImmutable',
            'sub' => 'DateTimeImmutable',
            'setTimezone' => 'DateTimeImmutable',
            'setTime' => 'DateTimeImmutable',
            'setDate' => 'DateTimeImmutable',
            'setISODate' => 'DateTimeImmutable',
            'setTimestamp' => 'DateTimeImmutable',
            'createFromMutable' => 'static',
        ],
        'DateTimeZone' => [
            'getName' => 'string',
            'getOffset' => 'int',
            'getTransitions' => 'array|false',
            'getLocation' => 'array|false',
            'listAbbreviations' => 'array',
            'listIdentifiers' => 'array',
            '__wakeup' => 'void',
            '__set_state' => 'DateTimeZone',
        ],
        'DateInterval' => [
            'createFromDateString' => 'DateInterval|false',
            'format' => 'string',
            '__wakeup' => 'void',
            '__set_state' => 'DateInterval',
        ],
        'DatePeriod' => [
            'getStartDate' => 'DateTimeInterface',
            'getEndDate' => '?DateTimeInterface',
            'getDateInterval' => 'DateInterval',
            'getRecurrences' => '?int',
            '__wakeup' => 'void',
            '__set_state' => 'DatePeriod',
        ],
        'DOMNode' => [
            'C14N' => 'string|false',
            'C14NFile' => 'int|false',
            'getLineNo' => 'int',
            'getNodePath' => '?string',
            'hasAttributes' => 'bool',
            'hasChildNodes' => 'bool',
            'isDefaultNamespace' => 'bool',
            'isSameNode' => 'bool',
            'isSupported' => 'bool',
            'lookupNamespaceURI' => '?string',
            'lookupPrefix' => '?string',
            'normalize' => 'void',
        ],
        'DOMImplementation' => [
            'getFeature' => 'never',
            'hasFeature' => 'bool',
        ],
        'DOMDocumentFragment' => [
            'appendXML' => 'bool',
        ],
        'DOMNodeList' => [
            'count' => 'int',
        ],
        'DOMCharacterData' => [
            'appendData' => 'bool',
            'insertData' => 'bool',
            'deleteData' => 'bool',
            'replaceData' => 'bool',
        ],
        'DOMAttr' => [
            'isId' => 'bool',
        ],
        'DOMElement' => [
            'getAttribute' => 'string',
            'getAttributeNS' => 'string',
            'getElementsByTagName' => 'DOMNodeList',
            'getElementsByTagNameNS' => 'DOMNodeList',
            'hasAttribute' => 'bool',
            'hasAttributeNS' => 'bool',
            'removeAttribute' => 'bool',
            'removeAttributeNS' => 'void',
            'setAttributeNS' => 'void',
            'setIdAttribute' => 'void',
            'setIdAttributeNS' => 'void',
            'setIdAttributeNode' => 'void',
        ],
        'DOMDocument' => [
            'createComment' => 'DOMComment',
            'createDocumentFragment' => 'DOMDocumentFragment',
            'createTextNode' => 'DOMText',
            'getElementById' => '?DOMElement',
            'getElementsByTagName' => 'DOMNodeList',
            'getElementsByTagNameNS' => 'DOMNodeList',
            'normalizeDocument' => 'void',
            'registerNodeClass' => 'bool',
            'save' => 'int|false',
            'saveHTML' => 'string|false',
            'saveHTMLFile' => 'int|false',
            'saveXML' => 'string|false',
            'schemaValidate' => 'bool',
            'schemaValidateSource' => 'bool',
            'relaxNGValidate' => 'bool',
            'relaxNGValidateSource' => 'bool',
            'validate' => 'bool',
            'xinclude' => 'int|false',
        ],
        'DOMText' => [
            'isWhitespaceInElementContent' => 'bool',
            'isElementContentWhitespace' => 'bool',
        ],
        'DOMNamedNodeMap' => [
            'getNamedItem' => '?DOMNode',
            'getNamedItemNS' => '?DOMNode',
            'item' => '?DOMNode',
            'count' => 'int',
        ],
        'DOMXPath' => [
            'evaluate' => 'mixed',
            'query' => 'mixed',
            'registerNamespace' => 'bool',
            'registerPhpFunctions' => 'void',
        ],
        'finfo' => [
            'file' => 'string|false',
            'buffer' => 'string|false',
        ],
        'IntlPartsIterator' => [
            'getBreakIterator' => 'IntlBreakIterator',
            'getRuleStatus' => 'int',
        ],
        'IntlBreakIterator' => [
            'createCharacterInstance' => '?IntlBreakIterator',
            'createCodePointInstance' => 'IntlCodePointBreakIterator',
            'createLineInstance' => '?IntlBreakIterator',
            'createSentenceInstance' => '?IntlBreakIterator',
            'createTitleInstance' => '?IntlBreakIterator',
            'createWordInstance' => '?IntlBreakIterator',
            'current' => 'int',
            'first' => 'int',
            'following' => 'int',
            'getErrorCode' => 'int',
            'getErrorMessage' => 'string',
            'getLocale' => 'string|false',
            'getPartsIterator' => 'IntlPartsIterator',
            'getText' => '?string',
            'isBoundary' => 'bool',
            'last' => 'int',
            'next' => 'int',
            'preceding' => 'int',
            'previous' => 'int',
            'setText' => '?bool',
        ],
        'IntlRuleBasedBreakIterator' => [
            'getBinaryRules' => 'string|false',
            'getRules' => 'string|false',
            'getRuleStatus' => 'int',
            'getRuleStatusVec' => 'array|false',
        ],
        'IntlCodePointBreakIterator' => [
            'getLastCodePoint' => 'int',
        ],
        'IntlCalendar' => [
            'createInstance' => '?IntlCalendar',
            'equals' => 'bool',
            'fieldDifference' => 'int|false',
            'add' => 'bool',
            'after' => 'bool',
            'before' => 'bool',
            'fromDateTime' => '?IntlCalendar',
            'get' => 'int|false',
            'getActualMaximum' => 'int|false',
            'getActualMinimum' => 'int|false',
            'getAvailableLocales' => 'array',
            'getDayOfWeekType' => 'int|false',
            'getErrorCode' => 'int|false',
            'getErrorMessage' => 'string|false',
            'getFirstDayOfWeek' => 'int|false',
            'getGreatestMinimum' => 'int|false',
            'getKeywordValuesForLocale' => 'IntlIterator|false',
            'getLeastMaximum' => 'int|false',
            'getLocale' => 'string|false',
            'getMaximum' => 'int|false',
            'getMinimalDaysInFirstWeek' => 'int|false',
            'getMinimum' => 'int|false',
            'getNow' => 'float',
            'getRepeatedWallTimeOption' => 'int',
            'getSkippedWallTimeOption' => 'int',
            'getTime' => 'float|false',
            'getTimeZone' => 'IntlTimeZone|false',
            'getType' => 'string',
            'getWeekendTransition' => 'int|false',
            'inDaylightTime' => 'bool',
            'isEquivalentTo' => 'bool',
            'isLenient' => 'bool',
            'isWeekend' => 'bool',
            'roll' => 'bool',
            'isSet' => 'bool',
            'setTime' => 'bool',
            'setTimeZone' => 'bool',
            'toDateTime' => 'DateTime|false',
        ],
        'IntlGregorianCalendar' => [
            'setGregorianChange' => 'bool',
            'getGregorianChange' => 'float',
            'isLeapYear' => 'bool',
        ],
        'Collator' => [
            'create' => '?Collator',
            'compare' => 'int|false',
            'sort' => 'bool',
            'sortWithSortKeys' => 'bool',
            'asort' => 'bool',
            'getAttribute' => 'int|false',
            'setAttribute' => 'bool',
            'getStrength' => 'int',
            'getLocale' => 'string|false',
            'getErrorCode' => 'int|false',
            'getErrorMessage' => 'string|false',
            'getSortKey' => 'string|false',
        ],
        'IntlIterator' => [
            'current' => 'mixed',
            'key' => 'mixed',
            'next' => 'void',
            'rewind' => 'void',
            'valid' => 'bool',
        ],
        'UConverter' => [
            'convert' => 'string|false',
            'fromUCallback' => 'string|int|array|null',
            'getAliases' => 'array|false|null',
            'getAvailable' => 'array',
            'getDestinationEncoding' => 'string|false|null',
            'getDestinationType' => 'int|false|null',
            'getErrorCode' => 'int',
            'getErrorMessage' => '?string',
            'getSourceEncoding' => 'string|false|null',
            'getSourceType' => 'int|false|null',
            'getStandards' => '?array',
            'getSubstChars' => 'string|false|null',
            'reasonText' => 'string',
            'setDestinationEncoding' => 'bool',
            'setSourceEncoding' => 'bool',
            'setSubstChars' => 'bool',
            'toUCallback' => 'string|int|array|null',
            'transcode' => 'string|false',
        ],
        'IntlDateFormatter' => [
            'create' => '?IntlDateFormatter',
            'getDateType' => 'int|false',
            'getTimeType' => 'int|false',
            'getCalendar' => 'int|false',
            'setCalendar' => 'bool',
            'getTimeZoneId' => 'string|false',
            'getCalendarObject' => 'IntlCalendar|false|null',
            'getTimeZone' => 'IntlTimeZone|false',
            'setTimeZone' => '?bool',
            'setPattern' => 'bool',
            'getPattern' => 'string|false',
            'getLocale' => 'string|false',
            'setLenient' => 'void',
            'isLenient' => 'bool',
            'format' => 'string|false',
            'formatObject' => 'string|false',
            'parse' => 'int|float|false',
            'localtime' => 'array|false',
            'getErrorCode' => 'int',
            'getErrorMessage' => 'string',
        ],
        'NumberFormatter' => [
            'create' => '?NumberFormatter',
            'format' => 'string|false',
            'parse' => 'int|float|false',
            'formatCurrency' => 'string|false',
            'parseCurrency' => 'float|false',
            'setAttribute' => 'bool',
            'getAttribute' => 'int|float|false',
            'setTextAttribute' => 'bool',
            'getTextAttribute' => 'string|false',
            'setSymbol' => 'bool',
            'getSymbol' => 'string|false',
            'setPattern' => 'bool',
            'getPattern' => 'string|false',
            'getLocale' => 'string|false',
            'getErrorCode' => 'int',
            'getErrorMessage' => 'string',
        ],
        'Locale' => [
            'getDefault' => 'string',
            'getPrimaryLanguage' => '?string',
            'getScript' => '?string',
            'getRegion' => '?string',
            'getKeywords' => 'array|false|null',
            'getDisplayScript' => 'string|false',
            'getDisplayRegion' => 'string|false',
            'getDisplayName' => 'string|false',
            'getDisplayLanguage' => 'string|false',
            'getDisplayVariant' => 'string|false',
            'composeLocale' => 'string|false',
            'parseLocale' => '?array',
            'getAllVariants' => '?array',
            'filterMatches' => '?bool',
            'lookup' => '?string',
            'canonicalize' => '?string',
            'acceptFromHttp' => 'string|false',
        ],
        'MessageFormatter' => [
            'create' => '?MessageFormatter',
            'format' => 'string|false',
            'formatMessage' => 'string|false',
            'parse' => 'array|false',
            'parseMessage' => 'array|false',
            'setPattern' => 'bool',
            'getPattern' => 'string|false',
            'getLocale' => 'string',
            'getErrorCode' => 'int',
            'getErrorMessage' => 'string',
        ],
        'Normalizer' => [
            'normalize' => 'string|false',
            'isNormalized' => 'bool',
            'getRawDecomposition' => '?string',
        ],
        'ResourceBundle' => [
            'create' => '?ResourceBundle',
            'get' => 'mixed',
            'count' => 'int',
            'getLocales' => 'array|false',
            'getErrorCode' => 'int',
            'getErrorMessage' => 'string',
        ],
        'Spoofchecker' => [
            'isSuspicious' => 'bool',
            'areConfusable' => 'bool',
            'setAllowedLocales' => 'void',
            'setChecks' => 'void',
            'setRestrictionLevel' => 'void',
        ],
        'IntlTimeZone' => [
            'countEquivalentIDs' => 'int|false',
            'createDefault' => 'IntlTimeZone',
            'createEnumeration' => 'IntlIterator|false',
            'createTimeZone' => '?IntlTimeZone',
            'createTimeZoneIDEnumeration' => 'IntlIterator|false',
            'fromDateTimeZone' => '?IntlTimeZone',
            'getCanonicalID' => 'string|false',
            'getDisplayName' => 'string|false',
            'getDSTSavings' => 'int',
            'getEquivalentID' => 'string|false',
            'getErrorCode' => 'int|false',
            'getErrorMessage' => 'string|false',
            'getGMT' => 'IntlTimeZone',
            'getID' => 'string|false',
            'getOffset' => 'bool',
            'getRawOffset' => 'int',
            'getRegion' => 'string|false',
            'getTZDataVersion' => 'string|false',
            'getUnknown' => 'IntlTimeZone',
            'getWindowsID' => 'string|false',
            'getIDForWindowsID' => 'string|false',
            'hasSameRules' => 'bool',
            'toDateTimeZone' => 'DateTimeZone|false',
            'useDaylightTime' => 'bool',
        ],
        'Transliterator' => [
            'create' => '?Transliterator',
            'createFromRules' => '?Transliterator',
            'createInverse' => '?Transliterator',
            'listIDs' => 'array|false',
            'transliterate' => 'string|false',
            'getErrorCode' => 'int|false',
            'getErrorMessage' => 'string|false',
        ],
        'IntlChar' => [
            'hasBinaryProperty' => '?bool',
            'charAge' => '?array',
            'charDigitValue' => '?int',
            'charDirection' => '?int',
            'charFromName' => '?int',
            'charMirror' => 'int|string|null',
            'charName' => '?string',
            'charType' => '?int',
            'chr' => '?string',
            'digit' => 'int|false|null',
            'enumCharNames' => '?bool',
            'enumCharTypes' => 'void',
            'foldCase' => 'int|string|null',
            'forDigit' => 'int',
            'getBidiPairedBracket' => 'int|string|null',
            'getBlockCode' => '?int',
            'getCombiningClass' => '?int',
            'getFC_NFKC_Closure' => 'string|false|null',
            'getIntPropertyMaxValue' => 'int',
            'getIntPropertyMinValue' => 'int',
            'getIntPropertyValue' => '?int',
            'getNumericValue' => '?float',
            'getPropertyEnum' => 'int',
            'getPropertyName' => 'string|false',
            'getPropertyValueEnum' => 'int',
            'getPropertyValueName' => 'string|false',
            'getUnicodeVersion' => 'array',
            'isalnum' => '?bool',
            'isalpha' => '?bool',
            'isbase' => '?bool',
            'isblank' => '?bool',
            'iscntrl' => '?bool',
            'isdefined' => '?bool',
            'isdigit' => '?bool',
            'isgraph' => '?bool',
            'isIDIgnorable' => '?bool',
            'isIDPart' => '?bool',
            'isIDStart' => '?bool',
            'isISOControl' => '?bool',
            'isJavaIDPart' => '?bool',
            'isJavaIDStart' => '?bool',
            'isJavaSpaceChar' => '?bool',
            'islower' => '?bool',
            'isMirrored' => '?bool',
            'isprint' => '?bool',
            'ispunct' => '?bool',
            'isspace' => '?bool',
            'istitle' => '?bool',
            'isUAlphabetic' => '?bool',
            'isULowercase' => '?bool',
            'isupper' => '?bool',
            'isUUppercase' => '?bool',
            'isUWhiteSpace' => '?bool',
            'isWhitespace' => '?bool',
            'isxdigit' => '?bool',
            'ord' => '?int',
            'tolower' => 'int|string|null',
            'totitle' => 'int|string|null',
            'toupper' => 'int|string|null',
        ],
        'JsonSerializable' => [
            'jsonSerialize' => 'mixed',
        ],
        'mysqli' => [
            'autocommit' => 'bool',
            'begin_transaction' => 'bool',
            'change_user' => 'bool',
            'character_set_name' => 'string',
            'commit' => 'bool',
            'connect' => 'bool',
            'dump_debug_info' => 'bool',
            'get_charset' => '?object',
            'get_client_info' => 'string',
            'get_connection_stats' => 'array',
            'get_server_info' => 'string',
            'get_warnings' => 'mysqli_warning|false',
            'kill' => 'bool',
            'multi_query' => 'bool',
            'more_results' => 'bool',
            'next_result' => 'bool',
            'ping' => 'bool',
            'poll' => 'int|false',
            'prepare' => 'mysqli_stmt|false',
            'query' => 'mysqli_result|bool',
            'real_connect' => 'bool',
            'real_escape_string' => 'string',
            'reap_async_query' => 'mysqli_result|bool',
            'escape_string' => 'string',
            'real_query' => 'bool',
            'release_savepoint' => 'bool',
            'rollback' => 'bool',
            'savepoint' => 'bool',
            'select_db' => 'bool',
            'set_charset' => 'bool',
            'options' => 'bool',
            'set_opt' => 'bool',
            'stat' => 'string|false',
            'stmt_init' => 'mysqli_stmt|false',
            'store_result' => 'mysqli_result|false',
            'thread_safe' => 'bool',
            'use_result' => 'mysqli_result|false',
            'refresh' => 'bool',
        ],
        'mysqli_result' => [
            'close' => 'void',
            'free' => 'void',
            'data_seek' => 'bool',
            'fetch_field' => 'object|false',
            'fetch_fields' => 'array',
            'fetch_field_direct' => 'object|false',
            'fetch_all' => 'array',
            'fetch_array' => 'array|null|false',
            'fetch_assoc' => 'array|null|false',
            'fetch_object' => 'object|null|false',
            'fetch_row' => 'array|null|false',
            'field_seek' => 'bool',
            'free_result' => 'void',
        ],
        'mysqli_stmt' => [
            'attr_get' => 'int',
            'attr_set' => 'bool',
            'bind_param' => 'bool',
            'bind_result' => 'bool',
            'data_seek' => 'void',
            'execute' => 'bool',
            'fetch' => '?bool',
            'get_warnings' => 'mysqli_warning|false',
            'result_metadata' => 'mysqli_result|false',
            'more_results' => 'bool',
            'next_result' => 'bool',
            'num_rows' => 'int|string',
            'send_long_data' => 'bool',
            'free_result' => 'void',
            'reset' => 'bool',
            'prepare' => 'bool',
            'store_result' => 'bool',
            'get_result' => 'mysqli_result|false',
        ],
        'OCILob' => [
            'save' => 'bool',
            'import' => 'bool',
            'saveFile' => 'bool',
            'load' => 'string|false',
            'read' => 'string|false',
            'eof' => 'bool',
            'tell' => 'int|false',
            'rewind' => 'bool',
            'seek' => 'bool',
            'size' => 'int|false',
            'write' => 'int|false',
            'append' => 'bool',
            'truncate' => 'bool',
            'erase' => 'int|false',
            'flush' => 'bool',
            'setBuffering' => 'bool',
            'getBuffering' => 'bool',
            'writeToFile' => 'bool',
            'export' => 'bool',
            'writeTemporary' => 'bool',
            'close' => 'bool',
            'free' => 'bool',
        ],
        'OCICollection' => [
            'free' => 'bool',
            'append' => 'bool',
            'getElem' => 'string|float|null|false',
            'assign' => 'bool',
            'assignElem' => 'bool',
            'size' => 'int|false',
            'max' => 'int|false',
            'trim' => 'bool',
        ],
        'PDO' => [
            'beginTransaction' => 'bool',
            'commit' => 'bool',
            'errorCode' => '?string',
            'errorInfo' => 'array',
            'exec' => 'int|false',
            'getAttribute' => 'mixed',
            'getAvailableDrivers' => 'array',
            'inTransaction' => 'bool',
            'lastInsertId' => 'string|false',
            'prepare' => 'PDOStatement|false',
            'query' => 'PDOStatement|false',
            'quote' => 'string|false',
            'rollBack' => 'bool',
            'setAttribute' => 'bool',
        ],
        'PDOStatement' => [
            'bindColumn' => 'bool',
            'bindParam' => 'bool',
            'bindValue' => 'bool',
            'closeCursor' => 'bool',
            'columnCount' => 'int',
            'debugDumpParams' => '?bool',
            'errorCode' => '?string',
            'errorInfo' => 'array',
            'execute' => 'bool',
            'fetch' => 'mixed',
            'fetchAll' => 'array',
            'fetchColumn' => 'mixed',
            'fetchObject' => 'object|false',
            'getAttribute' => 'mixed',
            'getColumnMeta' => 'array|false',
            'nextRowset' => 'bool',
            'rowCount' => 'int',
            'setAttribute' => 'bool',
        ],
        'PDO_PGSql_Ext' => [
            'pgsqlCopyFromArray' => 'bool',
            'pgsqlCopyFromFile' => 'bool',
            'pgsqlCopyToArray' => 'array|false',
            'pgsqlCopyToFile' => 'bool',
            'pgsqlLOBCreate' => 'string|false',
            'pgsqlLOBUnlink' => 'bool',
            'pgsqlGetNotify' => 'array|false',
            'pgsqlGetPid' => 'int',
        ],
        'PDO_SQLite_Ext' => [
            'sqliteCreateFunction' => 'bool',
            'sqliteCreateAggregate' => 'bool',
            'sqliteCreateCollation' => 'bool',
        ],
        'Phar' => [
            'addEmptyDir' => 'void',
            'addFile' => 'void',
            'addFromString' => 'void',
            'buildFromDirectory' => 'array',
            'buildFromIterator' => 'array',
            'compressFiles' => 'void',
            'compress' => '?Phar',
            'decompress' => '?Phar',
            'convertToExecutable' => '?Phar',
            'convertToData' => '?PharData',
            'count' => 'int',
            'extractTo' => 'bool',
            'getAlias' => '?string',
            'getPath' => 'string',
            'getMetadata' => 'mixed',
            'getModified' => 'bool',
            'getSignature' => 'array|false',
            'getStub' => 'string',
            'getVersion' => 'string',
            'hasMetadata' => 'bool',
            'isBuffering' => 'bool',
            'isCompressed' => 'int|false',
            'isFileFormat' => 'bool',
            'isWritable' => 'bool',
            'offsetExists' => 'bool',
            'offsetGet' => 'SplFileInfo',
            'offsetSet' => 'void',
            'offsetUnset' => 'void',
            'setAlias' => 'bool',
            'setDefaultStub' => 'bool',
            'setMetadata' => 'void',
            'setSignatureAlgorithm' => 'void',
            'startBuffering' => 'void',
            'stopBuffering' => 'void',
        ],
        'PharData' => [
            'addEmptyDir' => 'void',
            'addFile' => 'void',
            'addFromString' => 'void',
            'buildFromDirectory' => 'array',
            'buildFromIterator' => 'array',
            'compressFiles' => 'void',
            'compress' => '?PharData',
            'decompress' => '?PharData',
            'convertToExecutable' => '?Phar',
            'convertToData' => '?PharData',
            'count' => 'int',
            'extractTo' => 'bool',
            'getAlias' => '?string',
            'getPath' => 'string',
            'getMetadata' => 'mixed',
            'getModified' => 'bool',
            'getSignature' => 'array|false',
            'getStub' => 'string',
            'getVersion' => 'string',
            'hasMetadata' => 'bool',
            'isBuffering' => 'bool',
            'isCompressed' => 'int|false',
            'isFileFormat' => 'bool',
            'isWritable' => 'bool',
            'offsetExists' => 'bool',
            'offsetGet' => 'SplFileInfo',
            'offsetSet' => 'void',
            'offsetUnset' => 'void',
            'setAlias' => 'bool',
            'setDefaultStub' => 'bool',
            'setMetadata' => 'void',
            'setSignatureAlgorithm' => 'void',
            'startBuffering' => 'void',
            'stopBuffering' => 'void',
        ],
        'PharFileInfo' => [
            'chmod' => 'void',
            'getCompressedSize' => 'int',
            'getCRC32' => 'int',
            'getContent' => 'string',
            'getMetadata' => 'mixed',
            'getPharFlags' => 'int',
            'hasMetadata' => 'bool',
            'isCompressed' => 'bool',
            'isCRCChecked' => 'bool',
            'setMetadata' => 'void',
        ],
        'Reflection' => [
            'getModifierNames' => 'array',
        ],
        'ReflectionFunctionAbstract' => [
            'inNamespace' => 'bool',
            'isClosure' => 'bool',
            'isDeprecated' => 'bool',
            'isInternal' => 'bool',
            'isUserDefined' => 'bool',
            'isGenerator' => 'bool',
            'isVariadic' => 'bool',
            'isStatic' => 'bool',
            'getClosureThis' => '?object',
            'getClosureCalledClass' => '?ReflectionClass',
            'getClosureScopeClass' => '?ReflectionClass',
            'getDocComment' => 'string|false',
            'getEndLine' => 'int|false',
            'getExtension' => '?ReflectionExtension',
            'getExtensionName' => 'string|false',
            'getFileName' => 'string|false',
            'getName' => 'string',
            'getNamespaceName' => 'string',
            'getNumberOfParameters' => 'int',
            'getNumberOfRequiredParameters' => 'int',
            'getParameters' => 'array',
            'getShortName' => 'string',
            'getStartLine' => 'int|false',
            'getStaticVariables' => 'array',
            'returnsReference' => 'bool',
            'hasReturnType' => 'bool',
            'getReturnType' => '?ReflectionType',
        ],
        'ReflectionFunction' => [
            'isDisabled' => 'bool',
            'invoke' => 'mixed',
            'invokeArgs' => 'mixed',
            'getClosure' => 'Closure',
            'getExecutingLine' => 'int',
            'getExecutingFile' => 'string',
            'getTrace' => 'array',
            'getFunction' => 'ReflectionFunctionAbstract',
            'getThis' => '?object',
            'getExecutingGenerator' => 'Generator',
        ],
        'ReflectionMethod' => [
            'isPublic' => 'bool',
            'isPrivate' => 'bool',
            'isProtected' => 'bool',
            'isAbstract' => 'bool',
            'isFinal' => 'bool',
            'isConstructor' => 'bool',
            'isDestructor' => 'bool',
            'getClosure' => 'Closure',
            'getModifiers' => 'int',
            'invoke' => 'mixed',
            'invokeArgs' => 'mixed',
            'getDeclaringClass' => 'ReflectionClass',
            'getPrototype' => 'ReflectionMethod',
            'setAccessible' => 'void',
        ],
        'ReflectionClass' => [
            'getName' => 'string',
            'isInternal' => 'bool',
            'isUserDefined' => 'bool',
            'isAnonymous' => 'bool',
            'isInstantiable' => 'bool',
            'isCloneable' => 'bool',
            'getFileName' => 'string|false',
            'getStartLine' => 'int|false',
            'getEndLine' => 'int|false',
            'getDocComment' => 'string|false',
            'getConstructor' => '?ReflectionMethod',
            'hasMethod' => 'bool',
            'getMethod' => 'ReflectionMethod',
            'getMethods' => 'array',
            'hasProperty' => 'bool',
            'getProperty' => 'ReflectionProperty',
            'getProperties' => 'array',
            'hasConstant' => 'bool',
            'getConstants' => 'array',
            'getReflectionConstants' => 'array',
            'getConstant' => 'mixed',
            'getReflectionConstant' => 'ReflectionClassConstant|false',
            'getInterfaces' => 'array',
            'getInterfaceNames' => 'array',
            'isInterface' => 'bool',
            'getTraits' => 'array',
            'getTraitNames' => 'array',
            'getTraitAliases' => 'array',
            'isTrait' => 'bool',
            'isAbstract' => 'bool',
            'isFinal' => 'bool',
            'getModifiers' => 'int',
            'isInstance' => 'bool',
            'newInstance' => 'object',
            'newInstanceWithoutConstructor' => 'object',
            'newInstanceArgs' => '?object',
            'getParentClass' => 'ReflectionClass|false',
            'isSubclassOf' => 'bool',
            'getStaticProperties' => '?array',
            'getStaticPropertyValue' => 'mixed',
            'setStaticPropertyValue' => 'void',
            'getDefaultProperties' => 'array',
            'isIterable' => 'bool',
            'isIterateable' => 'bool',
            'implementsInterface' => 'bool',
            'getExtension' => '?ReflectionExtension',
            'getExtensionName' => 'string|false',
            'inNamespace' => 'bool',
            'getNamespaceName' => 'string',
            'getShortName' => 'string',
        ],
        'ReflectionProperty' => [
            'getName' => 'string',
            'getValue' => 'mixed',
            'setValue' => 'void',
            'isInitialized' => 'bool',
            'isPublic' => 'bool',
            'isPrivate' => 'bool',
            'isProtected' => 'bool',
            'isStatic' => 'bool',
            'isDefault' => 'bool',
            'getModifiers' => 'int',
            'getDeclaringClass' => 'ReflectionClass',
            'getDocComment' => 'string|false',
            'setAccessible' => 'void',
            'getType' => '?ReflectionType',
            'hasType' => 'bool',
            'getDefaultValue' => 'mixed',
        ],
        'ReflectionClassConstant' => [
            'getName' => 'string',
            'getValue' => 'mixed',
            'isPublic' => 'bool',
            'isPrivate' => 'bool',
            'isProtected' => 'bool',
            'getModifiers' => 'int',
            'getDeclaringClass' => 'ReflectionClass',
            'getDocComment' => 'string|false',
        ],
        'ReflectionParameter' => [
            'getName' => 'string',
            'isPassedByReference' => 'bool',
            'canBePassedByValue' => 'bool',
            'getDeclaringFunction' => 'ReflectionFunctionAbstract',
            'getDeclaringClass' => '?ReflectionClass',
            'getClass' => '?ReflectionClass',
            'hasType' => 'bool',
            'getType' => '?ReflectionType',
            'isArray' => 'bool',
            'isCallable' => 'bool',
            'allowsNull' => 'bool',
            'getPosition' => 'int',
            'isOptional' => 'bool',
            'isDefaultValueAvailable' => 'bool',
            'getDefaultValue' => 'mixed',
            'isDefaultValueConstant' => 'bool',
            'getDefaultValueConstantName' => '?string',
            'isVariadic' => 'bool',
        ],
        'ReflectionType' => [
            'allowsNull' => 'bool',
        ],
        'ReflectionNamedType' => [
            'getName' => 'string',
            'isBuiltin' => 'bool',
        ],
        'ReflectionExtension' => [
            'getName' => 'string',
            'getVersion' => '?string',
            'getFunctions' => 'array',
            'getConstants' => 'array',
            'getINIEntries' => 'array',
            'getClasses' => 'array',
            'getClassNames' => 'array',
            'getDependencies' => 'array',
            'info' => 'void',
            'isPersistent' => 'bool',
            'isTemporary' => 'bool',
        ],
        'ReflectionZendExtension' => [
            'getName' => 'string',
            'getVersion' => 'string',
            'getAuthor' => 'string',
            'getURL' => 'string',
            'getCopyright' => 'string',
        ],
        'SessionHandlerInterface' => [
            'open' => 'bool',
            'close' => 'bool',
            'read' => 'string|false',
            'write' => 'bool',
            'destroy' => 'bool',
            'gc' => 'int|false',
        ],
        'SessionIdInterface' => [
            'create_sid' => 'string',
        ],
        'SessionUpdateTimestampHandlerInterface' => [
            'validateId' => 'bool',
            'updateTimestamp' => 'bool',
        ],
        'SessionHandler' => [
            'open' => 'bool',
            'close' => 'bool',
            'read' => 'string|false',
            'write' => 'bool',
            'destroy' => 'bool',
            'gc' => 'int|false',
            'create_sid' => 'string',
        ],
        'SimpleXMLElement' => [
            'xpath' => 'array|null|false',
            'registerXPathNamespace' => 'bool',
            'asXML' => 'string|bool',
            'saveXML' => 'string|bool',
            'getNamespaces' => 'array',
            'getDocNamespaces' => 'array|false',
            'children' => '?SimpleXMLElement',
            'attributes' => '?SimpleXMLElement',
            'addChild' => '?SimpleXMLElement',
            'addAttribute' => 'void',
            'getName' => 'string',
            'count' => 'int',
            'rewind' => 'void',
            'valid' => 'bool',
            'current' => 'SimpleXMLElement',
            'key' => 'string',
            'next' => 'void',
            'hasChildren' => 'bool',
            'getChildren' => '?SimpleXMLElement',
        ],
        'SNMP' => [
            'close' => 'bool',
            'setSecurity' => 'bool',
            'get' => 'mixed',
            'getnext' => 'mixed',
            'walk' => 'array|false',
            'set' => 'bool',
            'getErrno' => 'int',
            'getError' => 'string',
        ],
        'SoapServer' => [
            'fault' => 'void',
            'addSoapHeader' => 'void',
            'setPersistence' => 'void',
            'setClass' => 'void',
            'setObject' => 'void',
            'getFunctions' => 'array',
            'addFunction' => 'void',
            'handle' => 'void',
        ],
        'SoapClient' => [
            '__call' => 'mixed',
            '__soapCall' => 'mixed',
            '__getFunctions' => '?array',
            '__getTypes' => '?array',
            '__getLastRequest' => '?string',
            '__getLastResponse' => '?string',
            '__getLastRequestHeaders' => '?string',
            '__getLastResponseHeaders' => '?string',
            '__doRequest' => '?string',
            '__setCookie' => 'void',
            '__getCookies' => 'array',
            '__setSoapHeaders' => 'bool',
            '__setLocation' => '?string',
        ],
        'ArrayObject' => [
            'offsetExists' => 'bool',
            'offsetGet' => 'mixed',
            'offsetSet' => 'void',
            'offsetUnset' => 'void',
            'append' => 'void',
            'getArrayCopy' => 'array',
            'count' => 'int',
            'getFlags' => 'int',
            'setFlags' => 'void',
            'asort' => 'bool',
            'ksort' => 'bool',
            'uasort' => 'bool',
            'uksort' => 'bool',
            'natsort' => 'bool',
            'natcasesort' => 'bool',
            'unserialize' => 'void',
            'serialize' => 'string',
            '__serialize' => 'array',
            '__unserialize' => 'void',
            'getIterator' => 'Iterator',
            'exchangeArray' => 'array',
            'setIteratorClass' => 'void',
            'getIteratorClass' => 'string',
            '__debugInfo' => 'array',
        ],
        'ArrayIterator' => [
            'offsetExists' => 'bool',
            'offsetGet' => 'mixed',
            'offsetSet' => 'void',
            'offsetUnset' => 'void',
            'append' => 'void',
            'getArrayCopy' => 'array',
            'count' => 'int',
            'getFlags' => 'int',
            'setFlags' => 'void',
            'asort' => 'bool',
            'ksort' => 'bool',
            'uasort' => 'bool',
            'uksort' => 'bool',
            'natsort' => 'bool',
            'natcasesort' => 'bool',
            'unserialize' => 'void',
            'serialize' => 'string',
            '__serialize' => 'array',
            '__unserialize' => 'void',
            'rewind' => 'void',
            'current' => 'mixed',
            'key' => 'string|int|null',
            'next' => 'void',
            'valid' => 'bool',
            'seek' => 'void',
            '__debugInfo' => 'array',
        ],
        'RecursiveArrayIterator' => [
            'hasChildren' => 'bool',
            'getChildren' => '?RecursiveArrayIterator',
        ],
        'SplFileInfo' => [
            'getPath' => 'string',
            'getFilename' => 'string',
            'getExtension' => 'string',
            'getBasename' => 'string',
            'getPathname' => 'string',
            'getPerms' => 'int|false',
            'getInode' => 'int|false',
            'getSize' => 'int|false',
            'getOwner' => 'int|false',
            'getGroup' => 'int|false',
            'getATime' => 'int|false',
            'getMTime' => 'int|false',
            'getCTime' => 'int|false',
            'getType' => 'string|false',
            'isWritable' => 'bool',
            'isReadable' => 'bool',
            'isExecutable' => 'bool',
            'isFile' => 'bool',
            'isDir' => 'bool',
            'isLink' => 'bool',
            'getLinkTarget' => 'string|false',
            'getRealPath' => 'string|false',
            'getFileInfo' => 'SplFileInfo',
            'getPathInfo' => '?SplFileInfo',
            'openFile' => 'SplFileObject',
            'setFileClass' => 'void',
            'setInfoClass' => 'void',
            '__debugInfo' => 'array',
            '_bad_state_ex' => 'void',
        ],
        'DirectoryIterator' => [
            'getFilename' => 'string',
            'getExtension' => 'string',
            'getBasename' => 'string',
            'isDot' => 'bool',
            'rewind' => 'void',
            'valid' => 'bool',
            'key' => 'mixed',
            'current' => 'mixed',
            'next' => 'void',
            'seek' => 'void',
        ],
        'FilesystemIterator' => [
            'rewind' => 'void',
            'key' => 'string',
            'current' => 'string|SplFileInfo|FilesystemIterator',
            'getFlags' => 'int',
            'setFlags' => 'void',
        ],
        'RecursiveDirectoryIterator' => [
            'hasChildren' => 'bool',
            'getChildren' => 'RecursiveDirectoryIterator',
            'getSubPath' => 'string',
            'getSubPathname' => 'string',
        ],
        'GlobIterator' => [
            'count' => 'int',
        ],
        'SplFileObject' => [
            'rewind' => 'void',
            'eof' => 'bool',
            'valid' => 'bool',
            'fgets' => 'string',
            'fread' => 'string|false',
            'fgetcsv' => 'array|false',
            'fputcsv' => 'int|false',
            'setCsvControl' => 'void',
            'getCsvControl' => 'array',
            'flock' => 'bool',
            'fflush' => 'bool',
            'ftell' => 'int|false',
            'fseek' => 'int',
            'fgetc' => 'string|false',
            'fpassthru' => 'int',
            'fscanf' => 'array|int|null',
            'fwrite' => 'int|false',
            'fstat' => 'array',
            'ftruncate' => 'bool',
            'current' => 'string|array|false',
            'key' => 'int',
            'next' => 'void',
            'setFlags' => 'void',
            'getFlags' => 'int',
            'setMaxLineLen' => 'void',
            'getMaxLineLen' => 'int',
            'hasChildren' => 'false',
            'getChildren' => 'null',
            'seek' => 'void',
            'getCurrentLine' => 'string',
        ],
        'SplDoublyLinkedList' => [
            'add' => 'void',
            'pop' => 'mixed',
            'shift' => 'mixed',
            'push' => 'void',
            'unshift' => 'void',
            'top' => 'mixed',
            'bottom' => 'mixed',
            '__debugInfo' => 'array',
            'count' => 'int',
            'isEmpty' => 'bool',
            'setIteratorMode' => 'int',
            'getIteratorMode' => 'int',
            'offsetExists' => 'bool',
            'offsetGet' => 'mixed',
            'offsetSet' => 'void',
            'offsetUnset' => 'void',
            'rewind' => 'void',
            'current' => 'mixed',
            'key' => 'int',
            'prev' => 'void',
            'next' => 'void',
            'valid' => 'bool',
            'unserialize' => 'void',
            'serialize' => 'string',
            '__serialize' => 'array',
            '__unserialize' => 'void',
        ],
        'SplQueue' => [
            'enqueue' => 'void',
            'dequeue' => 'mixed',
        ],
        'SplFixedArray' => [
            '__wakeup' => 'void',
            'count' => 'int',
            'toArray' => 'array',
            'fromArray' => 'SplFixedArray',
            'getSize' => 'int',
            'offsetExists' => 'bool',
            'offsetGet' => 'mixed',
            'offsetSet' => 'void',
            'offsetUnset' => 'void',
        ],
        'SplPriorityQueue' => [
            'compare' => 'int',
            'setExtractFlags' => 'int',
            'top' => 'mixed',
            'extract' => 'mixed',
            'count' => 'int',
            'isEmpty' => 'bool',
            'rewind' => 'void',
            'current' => 'mixed',
            'key' => 'int',
            'next' => 'void',
            'valid' => 'bool',
            'isCorrupted' => 'bool',
            'getExtractFlags' => 'int',
            '__debugInfo' => 'array',
        ],
        'SplHeap' => [
            'extract' => 'mixed',
            'insert' => 'bool',
            'top' => 'mixed',
            'count' => 'int',
            'isEmpty' => 'bool',
            'rewind' => 'void',
            'current' => 'mixed',
            'key' => 'int',
            'next' => 'void',
            'valid' => 'bool',
            'recoverFromCorruption' => 'bool',
            'compare' => 'int',
            'isCorrupted' => 'bool',
            '__debugInfo' => 'array',
        ],
        'SplMinHeap' => [
            'compare' => 'int',
        ],
        'SplMaxHeap' => [
            'compare' => 'int',
        ],
        'EmptyIterator' => [
            'current' => 'never',
            'next' => 'void',
            'key' => 'never',
            'valid' => 'false',
            'rewind' => 'void',
        ],
        'CallbackFilterIterator' => [
            'accept' => 'bool',
        ],
        'RecursiveCallbackFilterIterator' => [
            'hasChildren' => 'bool',
            'getChildren' => 'RecursiveCallbackFilterIterator',
        ],
        'RecursiveIterator' => [
            'hasChildren' => 'bool',
            'getChildren' => '?RecursiveIterator',
        ],
        'RecursiveIteratorIterator' => [
            'rewind' => 'void',
            'valid' => 'bool',
            'key' => 'mixed',
            'current' => 'mixed',
            'next' => 'void',
            'getDepth' => 'int',
            'getSubIterator' => '?RecursiveIterator',
            'getInnerIterator' => 'RecursiveIterator',
            'beginIteration' => 'void',
            'endIteration' => 'void',
            'callHasChildren' => 'bool',
            'callGetChildren' => '?RecursiveIterator',
            'beginChildren' => 'void',
            'endChildren' => 'void',
            'nextElement' => 'void',
            'setMaxDepth' => 'void',
            'getMaxDepth' => 'int|false',
        ],
        'OuterIterator' => [
            'getInnerIterator' => '?Iterator',
        ],
        'IteratorIterator' => [
            'getInnerIterator' => '?Iterator',
            'rewind' => 'void',
            'valid' => 'bool',
            'key' => 'mixed',
            'current' => 'mixed',
            'next' => 'void',
        ],
        'FilterIterator' => [
            'accept' => 'bool',
            'rewind' => 'void',
            'next' => 'void',
        ],
        'RecursiveFilterIterator' => [
            'hasChildren' => 'bool',
            'getChildren' => '?RecursiveFilterIterator',
        ],
        'ParentIterator' => [
            'accept' => 'bool',
        ],
        'SeekableIterator' => [
            'seek' => 'void',
        ],
        'LimitIterator' => [
            'rewind' => 'void',
            'valid' => 'bool',
            'next' => 'void',
            'seek' => 'int',
            'getPosition' => 'int',
        ],
        'CachingIterator' => [
            'rewind' => 'void',
            'valid' => 'bool',
            'next' => 'void',
            'hasNext' => 'bool',
            'getFlags' => 'int',
            'setFlags' => 'void',
            'offsetGet' => 'mixed',
            'offsetSet' => 'void',
            'offsetUnset' => 'void',
            'offsetExists' => 'bool',
            'getCache' => 'array',
            'count' => 'int',
        ],
        'RecursiveCachingIterator' => [
            'hasChildren' => 'bool',
            'getChildren' => '?RecursiveCachingIterator',
        ],
        'NoRewindIterator' => [
            'rewind' => 'void',
            'valid' => 'bool',
            'key' => 'mixed',
            'current' => 'mixed',
            'next' => 'void',
        ],
        'AppendIterator' => [
            'append' => 'void',
            'rewind' => 'void',
            'valid' => 'bool',
            'current' => 'mixed',
            'next' => 'void',
            'getIteratorIndex' => '?int',
            'getArrayIterator' => 'ArrayIterator',
        ],
        'InfiniteIterator' => [
            'next' => 'void',
        ],
        'RegexIterator' => [
            'accept' => 'bool',
            'getMode' => 'int',
            'setMode' => 'void',
            'getFlags' => 'int',
            'setFlags' => 'void',
            'getRegex' => 'string',
            'getPregFlags' => 'int',
            'setPregFlags' => 'void',
        ],
        'RecursiveRegexIterator' => [
            'accept' => 'bool',
            'hasChildren' => 'bool',
            'getChildren' => 'RecursiveRegexIterator',
        ],
        'RecursiveTreeIterator' => [
            'key' => 'mixed',
            'current' => 'mixed',
            'getPrefix' => 'string',
            'setPostfix' => 'void',
            'setPrefixPart' => 'void',
            'getEntry' => 'string',
            'getPostfix' => 'string',
        ],
        'SplObserver' => [
            'update' => 'void',
        ],
        'SplSubject' => [
            'attach' => 'void',
            'detach' => 'void',
            'notify' => 'void',
        ],
        'SplObjectStorage' => [
            'attach' => 'void',
            'detach' => 'void',
            'contains' => 'bool',
            'addAll' => 'int',
            'removeAll' => 'int',
            'removeAllExcept' => 'int',
            'getInfo' => 'mixed',
            'setInfo' => 'void',
            'count' => 'int',
            'rewind' => 'void',
            'valid' => 'bool',
            'key' => 'int',
            'current' => 'object',
            'next' => 'void',
            'unserialize' => 'void',
            'serialize' => 'string',
            'offsetExists' => 'bool',
            'offsetGet' => 'mixed',
            'offsetSet' => 'void',
            'offsetUnset' => 'void',
            'getHash' => 'string',
            '__serialize' => 'array',
            '__unserialize' => 'void',
            '__debugInfo' => 'array',
        ],
        'MultipleIterator' => [
            'getFlags' => 'int',
            'setFlags' => 'void',
            'attachIterator' => 'void',
            'detachIterator' => 'void',
            'containsIterator' => 'bool',
            'countIterators' => 'int',
            'rewind' => 'void',
            'valid' => 'bool',
            'key' => 'array',
            'current' => 'array',
            'next' => 'void',
            '__debugInfo' => 'array',
        ],
        'SQLite3' => [
            'open' => 'void',
            'version' => 'array',
            'lastInsertRowID' => 'int',
            'lastErrorCode' => 'int',
            'lastExtendedErrorCode' => 'int',
            'lastErrorMsg' => 'string',
            'changes' => 'int',
            'busyTimeout' => 'bool',
            'loadExtension' => 'bool',
            'backup' => 'bool',
            'escapeString' => 'string',
            'prepare' => 'SQLite3Stmt|false',
            'exec' => 'bool',
            'query' => 'SQLite3Result|false',
            'querySingle' => 'mixed',
            'createFunction' => 'bool',
            'createAggregate' => 'bool',
            'createCollation' => 'bool',
            'enableExceptions' => 'bool',
            'enableExtendedResultCodes' => 'bool',
            'setAuthorizer' => 'bool',
        ],
        'SQLite3Stmt' => [
            'bindParam' => 'bool',
            'bindValue' => 'bool',
            'clear' => 'bool',
            'close' => 'bool',
            'execute' => 'SQLite3Result|false',
            'getSQL' => 'string|false',
            'paramCount' => 'int',
            'readOnly' => 'bool',
            'reset' => 'bool',
        ],
        'SQLite3Result' => [
            'numColumns' => 'int',
            'columnName' => 'string|false',
            'columnType' => 'int|false',
            'fetchArray' => 'array|false',
            'reset' => 'bool',
        ],
        'Directory' => [
            'close' => 'void',
            'rewind' => 'void',
            'read' => 'string|false',
        ],
        'php_user_filter' => [
            'filter' => 'int',
            'onCreate' => 'bool',
            'onClose' => 'void',
        ],
        'tidy' => [
            'getOpt' => 'string|int|bool',
            'cleanRepair' => 'bool',
            'parseFile' => 'bool',
            'parseString' => 'bool',
            'repairString' => 'string|false',
            'repairFile' => 'string|false',
            'diagnose' => 'bool',
            'getRelease' => 'string',
            'getConfig' => 'array',
            'getStatus' => 'int',
            'getHtmlVer' => 'int',
            'getOptDoc' => 'string|false',
            'isXhtml' => 'bool',
            'isXml' => 'bool',
            'root' => '?tidyNode',
            'head' => '?tidyNode',
            'html' => '?tidyNode',
            'body' => '?tidyNode',
        ],
        'XMLReader' => [
            'getAttribute' => '?string',
            'getAttributeNo' => '?string',
            'getAttributeNs' => '?string',
            'getParserProperty' => 'bool',
            'isValid' => 'bool',
            'lookupNamespace' => '?string',
            'moveToAttribute' => 'bool',
            'moveToAttributeNo' => 'bool',
            'moveToAttributeNs' => 'bool',
            'moveToElement' => 'bool',
            'moveToFirstAttribute' => 'bool',
            'moveToNextAttribute' => 'bool',
            'read' => 'bool',
            'next' => 'bool',
            'readInnerXml' => 'string',
            'readOuterXml' => 'string',
            'readString' => 'string',
            'setSchema' => 'bool',
            'setParserProperty' => 'bool',
            'setRelaxNGSchema' => 'bool',
            'setRelaxNGSchemaSource' => 'bool',
            'expand' => 'DOMNode|false',
        ],
        'XMLWriter' => [
            'openUri' => 'bool',
            'openMemory' => 'bool',
            'setIndent' => 'bool',
            'setIndentString' => 'bool',
            'startComment' => 'bool',
            'endComment' => 'bool',
            'startAttribute' => 'bool',
            'endAttribute' => 'bool',
            'writeAttribute' => 'bool',
            'startAttributeNs' => 'bool',
            'writeAttributeNs' => 'bool',
            'startElement' => 'bool',
            'endElement' => 'bool',
            'fullEndElement' => 'bool',
            'startElementNs' => 'bool',
            'writeElement' => 'bool',
            'writeElementNs' => 'bool',
            'startPi' => 'bool',
            'endPi' => 'bool',
            'writePi' => 'bool',
            'startCdata' => 'bool',
            'endCdata' => 'bool',
            'writeCdata' => 'bool',
            'text' => 'bool',
            'writeRaw' => 'bool',
            'startDocument' => 'bool',
            'endDocument' => 'bool',
            'writeComment' => 'bool',
            'startDtd' => 'bool',
            'endDtd' => 'bool',
            'writeDtd' => 'bool',
            'startDtdElement' => 'bool',
            'endDtdElement' => 'bool',
            'writeDtdElement' => 'bool',
            'startDtdAttlist' => 'bool',
            'endDtdAttlist' => 'bool',
            'writeDtdAttlist' => 'bool',
            'startDtdEntity' => 'bool',
            'endDtdEntity' => 'bool',
            'writeDtdEntity' => 'bool',
            'outputMemory' => 'string',
            'flush' => 'string|int',
        ],
        'XSLTProcessor' => [
            'importStylesheet' => 'bool',
            'transformToDoc' => 'DOMDocument|false',
            'transformToUri' => 'int',
            'transformToXml' => 'string|null|false',
            'setParameter' => 'bool',
            'getParameter' => 'string|false',
            'removeParameter' => 'bool',
            'hasExsltSupport' => 'bool',
            'registerPHPFunctions' => 'void',
            'setSecurityPrefs' => 'int',
            'getSecurityPrefs' => 'int',
        ],
        'ZipArchive' => [
            'open' => 'bool|int',
            'setPassword' => 'bool',
            'close' => 'bool',
            'count' => 'int',
            'getStatusString' => 'string',
            'addEmptyDir' => 'bool',
            'addFromString' => 'bool',
            'addFile' => 'bool',
            'replaceFile' => 'bool',
            'addGlob' => 'array|false',
            'addPattern' => 'array|false',
            'renameIndex' => 'bool',
            'renameName' => 'bool',
            'setArchiveComment' => 'bool',
            'getArchiveComment' => 'string|false',
            'setCommentIndex' => 'bool',
            'setCommentName' => 'bool',
            'setMtimeIndex' => 'bool',
            'setMtimeName' => 'bool',
            'getCommentIndex' => 'string|false',
            'getCommentName' => 'string|false',
            'deleteIndex' => 'bool',
            'deleteName' => 'bool',
            'statName' => 'array|false',
            'statIndex' => 'array|false',
            'locateName' => 'int|false',
            'getNameIndex' => 'string|false',
            'unchangeArchive' => 'bool',
            'unchangeAll' => 'bool',
            'unchangeIndex' => 'bool',
            'unchangeName' => 'bool',
            'extractTo' => 'bool',
            'getFromName' => 'string|false',
            'getFromIndex' => 'string|false',
            'setExternalAttributesName' => 'bool',
            'setExternalAttributesIndex' => 'bool',
            'getExternalAttributesName' => 'bool',
            'getExternalAttributesIndex' => 'bool',
            'setCompressionName' => 'bool',
            'setCompressionIndex' => 'bool',
            'setEncryptionName' => 'bool',
            'setEncryptionIndex' => 'bool',
            'registerProgressCallback' => 'bool',
            'registerCancelCallback' => 'bool',
        ],
        'Exception' => [
            '__wakeup' => 'void',
        ],
        'Error' => [
            '__wakeup' => 'void',
        ],
        'IteratorAggregate' => [
            'getIterator' => 'Traversable',
        ],
        'Iterator' => [
            'current' => 'mixed',
            'next' => 'void',
            'key' => 'mixed',
            'valid' => 'bool',
            'rewind' => 'void',
        ],
        'ArrayAccess' => [
            'offsetExists' => 'bool',
            'offsetGet' => 'mixed',
            'offsetSet' => 'void',
            'offsetUnset' => 'void',
        ],
        'Countable' => [
            'count' => 'int',
        ],
    ];
}
