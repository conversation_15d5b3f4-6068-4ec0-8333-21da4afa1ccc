<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Invoice extends Model
{
    protected $fillable = [
        'invoice_id',
        'customer_id',
        'issue_date',
        'due_date',
        'ref_number',
        'status',
        'category_id',
        'created_by',
    ];

    public static $statues = [
        'Draft',
        'Sent',
        'Unpaid',
        'Partialy Paid',
        'Paid',
    ];

    /**
     * منع Lazy Loading للعلاقات غير المطلوبة
     */
    public $preventsLazyLoading = false; // سنبدأ بـ false ثم نفعلها تدريجياً

    /**
     * العلاقات التي يجب تحميلها دائماً (فقط الأساسية)
     */
    protected $with = [];

    /**
     * العلاقات التي يجب عدها فقط
     */
    protected $withCount = [];


    public function tax()
    {
        return $this->hasOne('App\Models\Tax', 'id', 'tax_id');
    }

    public function items()
    {
        return $this->hasMany('App\Models\InvoiceProduct', 'invoice_id', 'id')
                    ->select(['id', 'invoice_id', 'product_id', 'quantity', 'price', 'discount', 'tax']);
    }

    public function payments()
    {
        return $this->hasMany('App\Models\InvoicePayment', 'invoice_id', 'id')
                    ->select(['id', 'invoice_id', 'amount', 'date', 'payment_method', 'notes']);
    }

    /**
     * العلاقة مع العناصر - للعرض فقط
     */
    public function itemsForDisplay()
    {
        return $this->hasMany('App\Models\InvoiceProduct', 'invoice_id', 'id')
                    ->select(['id', 'invoice_id', 'product_id', 'quantity', 'price'])
                    ->with('product:id,name,sku');
    }

    /**
     * آخر الدفعات فقط
     */
    public function recentPayments()
    {
        return $this->hasMany('App\Models\InvoicePayment', 'invoice_id', 'id')
                    ->select(['id', 'invoice_id', 'amount', 'date'])
                    ->latest()
                    ->limit(5);
    }

    public function bankpayment()
    {
        return $this->hasMany('App\Models\BankTransfer', 'invoice_id', 'id')->where('type','=','invoice')->where('status','!=','Approved');
    }

    public function customer()
    {
        return $this->hasOne('App\Models\Customer', 'id', 'customer_id');
    }

    public function getSubTotal()
    {
        $subTotal = 0;
        foreach ($this->items as $product) {
            $subTotal += ($product->price * $product->quantity);
        }

        return $subTotal;
    }

    public function getTotalTax()
    {
        $totalTax = 0;
        foreach($this->items as $product)
        {
            $taxes = Utility::totalTaxRate($product->tax);


            $totalTax += ($taxes / 100) * ($product->price * $product->quantity - $product->discount) ;
        }

        return $totalTax;
    }

    public function getTotalDiscount()
    {
        $totalDiscount = 0;
        foreach ($this->items as $product) {
            $totalDiscount += $product->discount;
        }

        return $totalDiscount;
    }

    public function getTotal()
    {
        return ($this->getSubTotal() -$this->getTotalDiscount()) + $this->getTotalTax();
    }


    public function getDue()
    {
        $due = 0;
        foreach ($this->payments as $payment) {
            $due += $payment->amount;
        }

        return ($this->getTotal() - $due) - $this->invoiceTotalCreditNote();
    }

    public static function change_status($invoice_id, $status)
    {

        $invoice         = Invoice::find($invoice_id);
        $invoice->status = $status;
        $invoice->update();
    }

    public function category()
    {
        return $this->hasOne('App\Models\ProductServiceCategory', 'id', 'category_id');
    }

    public function creditNote()
    {

        return $this->hasMany('App\Models\CreditNote', 'invoice', 'id');
    }

    public function invoiceTotalCreditNote()
    {
        return $this->hasMany('App\Models\CreditNote', 'invoice', 'id')->sum('amount');
    }

    public function lastPayments()
    {
        return $this->hasOne('App\Models\InvoicePayment', 'id', 'invoice_id');
    }

    public function taxes()
    {
        return $this->hasOne('App\Models\Tax', 'id', 'tax');
    }
    public static function customers($customer)
    {

        $categoryArr  = explode(',', $customer);
        $unitRate = 0;
        foreach ($categoryArr as $customer) {
            if ($customer == 0) {
                $unitRate = '';
            } else {
                $customer        = Customer::find($customer);
                $unitRate        = $customer->name ?? '-';
            }
        }

        return $unitRate;
    }
    public static function Invoicecategory($category)
    {
        $categoryArr  = explode(',', $category);
        $categoryRate = 0;
        foreach ($categoryArr as $category) {
            $category    = ProductServiceCategory::find($category);
            $categoryRate        = $category->name ?? '-';
        }

        return $categoryRate;
    }
}
