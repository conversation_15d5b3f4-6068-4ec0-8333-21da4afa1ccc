<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Knowledge
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Knowledge\V1\Knowledge;

use Twilio\ListResource;
use Twilio\Version;


class KnowledgeStatusList extends ListResource
    {
    /**
     * Construct the KnowledgeStatusList
     *
     * @param Version $version Version that contains the resource
     * @param string $id the Knowledge ID.
     */
    public function __construct(
        Version $version,
        string $id
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'id' =>
            $id,
        
        ];
    }

    /**
     * Constructs a KnowledgeStatusContext
     */
    public function getContext(
        
    ): KnowledgeStatusContext
    {
        return new KnowledgeStatusContext(
            $this->version,
            $this->solution['id']
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Knowledge.V1.KnowledgeStatusList]';
    }
}
