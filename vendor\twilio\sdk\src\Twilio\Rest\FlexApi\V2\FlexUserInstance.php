<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\FlexApi\V2;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;


/**
 * @property string|null $accountSid
 * @property string|null $instanceSid
 * @property string|null $userSid
 * @property string|null $flexUserSid
 * @property string|null $workerSid
 * @property string|null $workspaceSid
 * @property string|null $flexTeamSid
 * @property string|null $username
 * @property string|null $email
 * @property string|null $locale
 * @property string[]|null $roles
 * @property \DateTime|null $createdDate
 * @property \DateTime|null $updatedDate
 * @property int $version
 * @property string|null $url
 */
class FlexUserInstance extends InstanceResource
{
    /**
     * Initialize the FlexUserInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $instanceSid The unique ID created by Twilio to identify a Flex instance.
     * @param string $flexUserSid The unique id for the flex user to be retrieved.
     */
    public function __construct(Version $version, array $payload, ?string $instanceSid = null, ?string $flexUserSid = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'accountSid' => Values::array_get($payload, 'account_sid'),
            'instanceSid' => Values::array_get($payload, 'instance_sid'),
            'userSid' => Values::array_get($payload, 'user_sid'),
            'flexUserSid' => Values::array_get($payload, 'flex_user_sid'),
            'workerSid' => Values::array_get($payload, 'worker_sid'),
            'workspaceSid' => Values::array_get($payload, 'workspace_sid'),
            'flexTeamSid' => Values::array_get($payload, 'flex_team_sid'),
            'username' => Values::array_get($payload, 'username'),
            'email' => Values::array_get($payload, 'email'),
            'locale' => Values::array_get($payload, 'locale'),
            'roles' => Values::array_get($payload, 'roles'),
            'createdDate' => Deserialize::dateTime(Values::array_get($payload, 'created_date')),
            'updatedDate' => Deserialize::dateTime(Values::array_get($payload, 'updated_date')),
            'version' => Values::array_get($payload, 'version'),
            'url' => Values::array_get($payload, 'url'),
        ];

        $this->solution = ['instanceSid' => $instanceSid ?: $this->properties['instanceSid'], 'flexUserSid' => $flexUserSid ?: $this->properties['flexUserSid'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return FlexUserContext Context for this FlexUserInstance
     */
    protected function proxy(): FlexUserContext
    {
        if (!$this->context) {
            $this->context = new FlexUserContext(
                $this->version,
                $this->solution['instanceSid'],
                $this->solution['flexUserSid']
            );
        }

        return $this->context;
    }

    /**
     * Fetch the FlexUserInstance
     *
     * @return FlexUserInstance Fetched FlexUserInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): FlexUserInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Update the FlexUserInstance
     *
     * @param array|Options $options Optional Arguments
     * @return FlexUserInstance Updated FlexUserInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(array $options = []): FlexUserInstance
    {

        return $this->proxy()->update($options);
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.FlexApi.V2.FlexUserInstance ' . \implode(' ', $context) . ']';
    }
}

