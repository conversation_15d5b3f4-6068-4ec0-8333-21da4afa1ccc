<?php

namespace App\Services\ProjectManagement;

use App\Models\ProjectManagement\Task;
use App\Models\ProjectManagement\Project;
use App\Repositories\ProjectManagement\TaskRepository;
use Illuminate\Support\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Exception;

/**
 * خدمة إدارة المهام
 * 
 * تدير جميع العمليات المتعلقة بالمهام
 * مع دعم العمليات المعقدة والتحقق من الصلاحيات
 * 
 * @package App\Services\ProjectManagement
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
class TaskService
{
    /**
     * مستودع المهام
     * 
     * @var TaskRepository
     */
    protected TaskRepository $taskRepository;

    /**
     * منشئ الخدمة
     * 
     * @param TaskRepository $taskRepository
     */
    public function __construct(TaskRepository $taskRepository)
    {
        $this->taskRepository = $taskRepository;
    }

    /**
     * إنشاء مهمة جديدة
     * 
     * @param array $data بيانات المهمة
     * @return Task
     * @throws Exception
     */
    public function createTask(array $data): Task
    {
        try {
            // التحقق من صحة البيانات
            $this->validateTaskData($data);

            // إضافة بيانات إضافية
            $data['created_by'] = auth()->id();
            $data['status'] = $data['status'] ?? 'todo';
            $data['priority'] = $data['priority'] ?? 'normal';

            // إنشاء المهمة
            $task = $this->taskRepository->create($data);

            // تحديث تقدم المشروع
            if ($task->project_id) {
                $this->updateProjectProgress($task->project_id);
            }

            return $task;

        } catch (Exception $e) {
            throw new Exception("فشل في إنشاء المهمة: " . $e->getMessage());
        }
    }

    /**
     * تحديث مهمة
     * 
     * @param int $taskId معرف المهمة
     * @param array $data البيانات الجديدة
     * @return Task
     * @throws Exception
     */
    public function updateTask(int $taskId, array $data): Task
    {
        try {
            $task = $this->taskRepository->findOrFail($taskId);

            // التحقق من الصلاحيات
            $this->checkTaskPermissions($task, 'update');

            // التحقق من صحة البيانات
            $this->validateTaskData($data, $taskId);

            // تحديث المهمة
            $task = $this->taskRepository->update($taskId, $data);

            // تحديث تقدم المشروع إذا تغيرت الحالة
            if (isset($data['status']) && $task->project_id) {
                $this->updateProjectProgress($task->project_id);
            }

            return $task;

        } catch (Exception $e) {
            throw new Exception("فشل في تحديث المهمة: " . $e->getMessage());
        }
    }

    /**
     * حذف مهمة
     * 
     * @param int $taskId معرف المهمة
     * @return bool
     * @throws Exception
     */
    public function deleteTask(int $taskId): bool
    {
        try {
            $task = $this->taskRepository->findOrFail($taskId);

            // التحقق من الصلاحيات
            $this->checkTaskPermissions($task, 'delete');

            $projectId = $task->project_id;
            $deleted = $this->taskRepository->delete($taskId);

            // تحديث تقدم المشروع
            if ($deleted && $projectId) {
                $this->updateProjectProgress($projectId);
            }

            return $deleted;

        } catch (Exception $e) {
            throw new Exception("فشل في حذف المهمة: " . $e->getMessage());
        }
    }

    /**
     * تعيين مهمة لمستخدم
     * 
     * @param int $taskId معرف المهمة
     * @param int $userId معرف المستخدم
     * @return Task
     * @throws Exception
     */
    public function assignTask(int $taskId, int $userId): Task
    {
        try {
            $task = $this->taskRepository->findOrFail($taskId);

            // التحقق من الصلاحيات
            $this->checkTaskPermissions($task, 'assign');

            return $this->taskRepository->update($taskId, [
                'assigned_to' => $userId,
                'assigned_at' => now()
            ]);

        } catch (Exception $e) {
            throw new Exception("فشل في تعيين المهمة: " . $e->getMessage());
        }
    }

    /**
     * إلغاء تعيين مهمة
     * 
     * @param int $taskId معرف المهمة
     * @return Task
     * @throws Exception
     */
    public function unassignTask(int $taskId): Task
    {
        try {
            $task = $this->taskRepository->findOrFail($taskId);

            // التحقق من الصلاحيات
            $this->checkTaskPermissions($task, 'assign');

            return $this->taskRepository->update($taskId, [
                'assigned_to' => null,
                'assigned_at' => null
            ]);

        } catch (Exception $e) {
            throw new Exception("فشل في إلغاء تعيين المهمة: " . $e->getMessage());
        }
    }

    /**
     * تغيير حالة مهمة
     * 
     * @param int $taskId معرف المهمة
     * @param string $status الحالة الجديدة
     * @return Task
     * @throws Exception
     */
    public function changeTaskStatus(int $taskId, string $status): Task
    {
        try {
            $task = $this->taskRepository->findOrFail($taskId);

            // التحقق من الصلاحيات
            $this->checkTaskPermissions($task, 'update');

            // التحقق من صحة الحالة
            if (!$task->canChangeStatusTo($status)) {
                throw new Exception("لا يمكن تغيير الحالة من {$task->status} إلى {$status}");
            }

            $data = ['status' => $status];

            // إضافة تاريخ الإكمال إذا كانت الحالة مكتملة
            if ($status === 'completed') {
                $data['completed_at'] = now();
            }

            $task = $this->taskRepository->update($taskId, $data);

            // تحديث تقدم المشروع
            if ($task->project_id) {
                $this->updateProjectProgress($task->project_id);
            }

            return $task;

        } catch (Exception $e) {
            throw new Exception("فشل في تغيير حالة المهمة: " . $e->getMessage());
        }
    }

    /**
     * الحصول على مهام مشروع
     * 
     * @param int $projectId معرف المشروع
     * @param array $filters مرشحات إضافية
     * @return Collection
     */
    public function getProjectTasks(int $projectId, array $filters = []): Collection
    {
        $filters['project_id'] = $projectId;
        return $this->taskRepository->getFilteredTasks($filters);
    }

    /**
     * الحصول على مهام مستخدم
     * 
     * @param int $userId معرف المستخدم
     * @param array $filters مرشحات إضافية
     * @return Collection
     */
    public function getUserTasks(int $userId, array $filters = []): Collection
    {
        $filters['assigned_to'] = $userId;
        return $this->taskRepository->getFilteredTasks($filters);
    }

    /**
     * البحث في المهام
     * 
     * @param array $criteria معايير البحث
     * @param int $perPage عدد النتائج في الصفحة
     * @return LengthAwarePaginator
     */
    public function searchTasks(array $criteria, int $perPage = 15): LengthAwarePaginator
    {
        return $this->taskRepository->searchTasks($criteria, $perPage);
    }

    /**
     * الحصول على إحصائيات المهام
     * 
     * @param array $filters مرشحات
     * @return array
     */
    public function getTaskStatistics(array $filters = []): array
    {
        return $this->taskRepository->getTaskStatistics($filters);
    }

    /**
     * الحصول على المهام المتأخرة
     * 
     * @param array $relations العلاقات المطلوب تحميلها
     * @return Collection
     */
    public function getOverdueTasks(array $relations = []): Collection
    {
        return $this->taskRepository->getOverdueTasks($relations);
    }

    /**
     * الحصول على المهام المكتملة
     * 
     * @param array $relations العلاقات المطلوب تحميلها
     * @return Collection
     */
    public function getCompletedTasks(array $relations = []): Collection
    {
        return $this->taskRepository->getCompletedTasks($relations);
    }

    /**
     * تحديث تقدم المشروع بناءً على المهام
     * 
     * @param int $projectId معرف المشروع
     * @return void
     */
    protected function updateProjectProgress(int $projectId): void
    {
        try {
            $project = Project::find($projectId);
            if (!$project) {
                return;
            }

            $totalTasks = $this->taskRepository->count(['project_id' => $projectId]);
            $completedTasks = $this->taskRepository->count([
                'project_id' => $projectId,
                'status' => 'completed'
            ]);

            $progressPercentage = $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100, 2) : 0;

            $project->update(['progress_percentage' => $progressPercentage]);

        } catch (Exception $e) {
            // تسجيل الخطأ دون إيقاف العملية
            logger()->error("فشل في تحديث تقدم المشروع: " . $e->getMessage());
        }
    }

    /**
     * التحقق من صحة بيانات المهمة
     * 
     * @param array $data البيانات
     * @param int|null $taskId معرف المهمة (للتحديث)
     * @throws Exception
     */
    protected function validateTaskData(array $data, ?int $taskId = null): void
    {
        // التحقق من وجود الحقول المطلوبة
        if (!isset($data['title']) || empty(trim($data['title']))) {
            throw new Exception("عنوان المهمة مطلوب");
        }

        if (!isset($data['project_id']) || !is_numeric($data['project_id'])) {
            throw new Exception("معرف المشروع مطلوب وصحيح");
        }

        // التحقق من وجود المشروع
        $project = Project::find($data['project_id']);
        if (!$project) {
            throw new Exception("المشروع غير موجود");
        }

        // التحقق من صحة الحالة
        if (isset($data['status'])) {
            $validStatuses = ['todo', 'in_progress', 'review', 'testing', 'completed', 'blocked'];
            if (!in_array($data['status'], $validStatuses)) {
                throw new Exception("حالة المهمة غير صحيحة");
            }
        }

        // التحقق من صحة الأولوية
        if (isset($data['priority'])) {
            $validPriorities = ['low', 'normal', 'high', 'urgent', 'critical'];
            if (!in_array($data['priority'], $validPriorities)) {
                throw new Exception("أولوية المهمة غير صحيحة");
            }
        }

        // التحقق من صحة تاريخ الاستحقاق
        if (isset($data['due_date']) && !empty($data['due_date'])) {
            try {
                $dueDate = \Carbon\Carbon::parse($data['due_date']);
                if ($dueDate->isPast()) {
                    throw new Exception("تاريخ الاستحقاق لا يمكن أن يكون في الماضي");
                }
            } catch (\Exception $e) {
                throw new Exception("تاريخ الاستحقاق غير صحيح");
            }
        }
    }

    /**
     * التحقق من صلاحيات المهمة
     * 
     * @param Task $task المهمة
     * @param string $action العملية
     * @throws Exception
     */
    protected function checkTaskPermissions(Task $task, string $action): void
    {
        $user = auth()->user();
        
        if (!$user) {
            throw new Exception("يجب تسجيل الدخول أولاً");
        }

        // المدير العام يمكنه فعل كل شيء
        if ($user->hasRole('admin')) {
            return;
        }

        // منشئ المهمة يمكنه فعل كل شيء
        if ($task->created_by === $user->id) {
            return;
        }

        // مدير المشروع يمكنه فعل كل شيء
        if ($task->project && $task->project->manager_id === $user->id) {
            return;
        }

        // المكلف بالمهمة يمكنه تحديث الحالة فقط
        if ($task->assigned_to === $user->id && $action === 'update') {
            return;
        }

        throw new Exception("ليس لديك صلاحية لتنفيذ هذه العملية");
    }
}
