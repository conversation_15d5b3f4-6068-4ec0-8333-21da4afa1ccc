net\authorize\api\contract\v1\GetCustomerPaymentProfileListResponse:
    xml_root_name: getCustomerPaymentProfileListResponse
    xml_root_namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
    properties:
        totalNumInResultSet:
            expose: true
            access_type: public_method
            serialized_name: totalNumInResultSet
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getTotalNumInResultSet
                setter: setTotalNumInResultSet
            type: integer
        paymentProfiles:
            expose: true
            access_type: public_method
            serialized_name: paymentProfiles
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getPaymentProfiles
                setter: setPaymentProfiles
            type: array<net\authorize\api\contract\v1\CustomerPaymentProfileListItemType>
            xml_list:
                inline: false
                skip_when_empty: true
                entry_name: paymentProfile
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
