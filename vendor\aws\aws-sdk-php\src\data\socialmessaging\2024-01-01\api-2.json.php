<?php
// This file was auto-generated from sdk-root/src/data/socialmessaging/2024-01-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2024-01-01', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'social-messaging', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS End User Messaging Social', 'serviceId' => 'SocialMessaging', 'signatureVersion' => 'v4', 'signingName' => 'social-messaging', 'uid' => 'socialmessaging-2024-01-01', ], 'operations' => [ 'AssociateWhatsAppBusinessAccount' => [ 'name' => 'AssociateWhatsAppBusinessAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/whatsapp/signup', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateWhatsAppBusinessAccountInput', ], 'output' => [ 'shape' => 'AssociateWhatsAppBusinessAccountOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParametersException', ], [ 'shape' => 'ThrottledRequestException', ], [ 'shape' => 'DependencyException', ], ], ], 'DeleteWhatsAppMessageMedia' => [ 'name' => 'DeleteWhatsAppMessageMedia', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/whatsapp/media', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteWhatsAppMessageMediaInput', ], 'output' => [ 'shape' => 'DeleteWhatsAppMessageMediaOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParametersException', ], [ 'shape' => 'ThrottledRequestException', ], [ 'shape' => 'AccessDeniedByMetaException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'DependencyException', ], ], 'idempotent' => true, ], 'DisassociateWhatsAppBusinessAccount' => [ 'name' => 'DisassociateWhatsAppBusinessAccount', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/whatsapp/waba/disassociate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateWhatsAppBusinessAccountInput', ], 'output' => [ 'shape' => 'DisassociateWhatsAppBusinessAccountOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParametersException', ], [ 'shape' => 'ThrottledRequestException', ], [ 'shape' => 'DependencyException', ], ], 'idempotent' => true, ], 'GetLinkedWhatsAppBusinessAccount' => [ 'name' => 'GetLinkedWhatsAppBusinessAccount', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/whatsapp/waba/details', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetLinkedWhatsAppBusinessAccountInput', ], 'output' => [ 'shape' => 'GetLinkedWhatsAppBusinessAccountOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParametersException', ], [ 'shape' => 'ThrottledRequestException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'DependencyException', ], ], ], 'GetLinkedWhatsAppBusinessAccountPhoneNumber' => [ 'name' => 'GetLinkedWhatsAppBusinessAccountPhoneNumber', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/whatsapp/waba/phone/details', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetLinkedWhatsAppBusinessAccountPhoneNumberInput', ], 'output' => [ 'shape' => 'GetLinkedWhatsAppBusinessAccountPhoneNumberOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParametersException', ], [ 'shape' => 'ThrottledRequestException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'DependencyException', ], ], ], 'GetWhatsAppMessageMedia' => [ 'name' => 'GetWhatsAppMessageMedia', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/whatsapp/media/get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetWhatsAppMessageMediaInput', ], 'output' => [ 'shape' => 'GetWhatsAppMessageMediaOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParametersException', ], [ 'shape' => 'ThrottledRequestException', ], [ 'shape' => 'AccessDeniedByMetaException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'DependencyException', ], ], ], 'ListLinkedWhatsAppBusinessAccounts' => [ 'name' => 'ListLinkedWhatsAppBusinessAccounts', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/whatsapp/waba/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListLinkedWhatsAppBusinessAccountsInput', ], 'output' => [ 'shape' => 'ListLinkedWhatsAppBusinessAccountsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParametersException', ], [ 'shape' => 'ThrottledRequestException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/tags/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceInput', ], 'output' => [ 'shape' => 'ListTagsForResourceOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParametersException', ], [ 'shape' => 'ThrottledRequestException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'PostWhatsAppMessageMedia' => [ 'name' => 'PostWhatsAppMessageMedia', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/whatsapp/media', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PostWhatsAppMessageMediaInput', ], 'output' => [ 'shape' => 'PostWhatsAppMessageMediaOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParametersException', ], [ 'shape' => 'ThrottledRequestException', ], [ 'shape' => 'AccessDeniedByMetaException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'DependencyException', ], ], ], 'PutWhatsAppBusinessAccountEventDestinations' => [ 'name' => 'PutWhatsAppBusinessAccountEventDestinations', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/whatsapp/waba/eventdestinations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutWhatsAppBusinessAccountEventDestinationsInput', ], 'output' => [ 'shape' => 'PutWhatsAppBusinessAccountEventDestinationsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParametersException', ], [ 'shape' => 'ThrottledRequestException', ], [ 'shape' => 'InternalServiceException', ], ], 'idempotent' => true, ], 'SendWhatsAppMessage' => [ 'name' => 'SendWhatsAppMessage', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/whatsapp/send', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SendWhatsAppMessageInput', ], 'output' => [ 'shape' => 'SendWhatsAppMessageOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParametersException', ], [ 'shape' => 'ThrottledRequestException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'DependencyException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/tags/tag-resource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceInput', ], 'output' => [ 'shape' => 'TagResourceOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParametersException', ], [ 'shape' => 'ThrottledRequestException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/tags/untag-resource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceInput', ], 'output' => [ 'shape' => 'UntagResourceOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParametersException', ], [ 'shape' => 'ThrottledRequestException', ], [ 'shape' => 'InternalServiceException', ], ], ], ], 'shapes' => [ 'AccessDeniedByMetaException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'Arn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => 'arn:.*', ], 'AssociateInProgressToken' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'sensitive' => true, ], 'AssociateWhatsAppBusinessAccountInput' => [ 'type' => 'structure', 'members' => [ 'signupCallback' => [ 'shape' => 'WhatsAppSignupCallback', ], 'setupFinalization' => [ 'shape' => 'WhatsAppSetupFinalization', ], ], ], 'AssociateWhatsAppBusinessAccountOutput' => [ 'type' => 'structure', 'members' => [ 'signupCallbackResult' => [ 'shape' => 'WhatsAppSignupCallbackResult', ], 'statusCode' => [ 'shape' => 'Integer', ], ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'DeleteWhatsAppMessageMediaInput' => [ 'type' => 'structure', 'required' => [ 'mediaId', 'originationPhoneNumberId', ], 'members' => [ 'mediaId' => [ 'shape' => 'WhatsAppMediaId', 'location' => 'querystring', 'locationName' => 'mediaId', ], 'originationPhoneNumberId' => [ 'shape' => 'WhatsAppPhoneNumberId', 'location' => 'querystring', 'locationName' => 'originationPhoneNumberId', ], ], ], 'DeleteWhatsAppMessageMediaOutput' => [ 'type' => 'structure', 'members' => [ 'success' => [ 'shape' => 'Boolean', ], ], ], 'DependencyException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 502, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'DisassociateWhatsAppBusinessAccountInput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'LinkedWhatsAppBusinessAccountId', 'location' => 'querystring', 'locationName' => 'id', ], ], ], 'DisassociateWhatsAppBusinessAccountOutput' => [ 'type' => 'structure', 'members' => [], ], 'ErrorMessage' => [ 'type' => 'string', ], 'EventDestinationArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => 'arn:.*:[a-z-]+([/:](.*))?', ], 'GetLinkedWhatsAppBusinessAccountInput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'LinkedWhatsAppBusinessAccountId', 'location' => 'querystring', 'locationName' => 'id', ], ], ], 'GetLinkedWhatsAppBusinessAccountOutput' => [ 'type' => 'structure', 'members' => [ 'account' => [ 'shape' => 'LinkedWhatsAppBusinessAccount', ], ], ], 'GetLinkedWhatsAppBusinessAccountPhoneNumberInput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'WhatsAppPhoneNumberId', 'location' => 'querystring', 'locationName' => 'id', ], ], ], 'GetLinkedWhatsAppBusinessAccountPhoneNumberOutput' => [ 'type' => 'structure', 'members' => [ 'phoneNumber' => [ 'shape' => 'WhatsAppPhoneNumberDetail', ], 'linkedWhatsAppBusinessAccountId' => [ 'shape' => 'LinkedWhatsAppBusinessAccountId', ], ], ], 'GetWhatsAppMessageMediaInput' => [ 'type' => 'structure', 'required' => [ 'mediaId', 'originationPhoneNumberId', ], 'members' => [ 'mediaId' => [ 'shape' => 'WhatsAppMediaId', ], 'originationPhoneNumberId' => [ 'shape' => 'WhatsAppPhoneNumberId', ], 'metadataOnly' => [ 'shape' => 'Boolean', ], 'destinationS3PresignedUrl' => [ 'shape' => 'S3PresignedUrl', ], 'destinationS3File' => [ 'shape' => 'S3File', ], ], ], 'GetWhatsAppMessageMediaOutput' => [ 'type' => 'structure', 'members' => [ 'mimeType' => [ 'shape' => 'String', ], 'fileSize' => [ 'shape' => 'Long', ], ], ], 'Headers' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServiceException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'InvalidParametersException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'IsoCountryCode' => [ 'type' => 'string', 'pattern' => '[A-Z]{2}', ], 'LinkedAccountWithIncompleteSetup' => [ 'type' => 'map', 'key' => [ 'shape' => 'WhatsAppBusinessAccountId', ], 'value' => [ 'shape' => 'LinkedWhatsAppBusinessAccountIdMetaData', ], ], 'LinkedWhatsAppBusinessAccount' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'wabaId', 'registrationStatus', 'linkDate', 'wabaName', 'eventDestinations', 'phoneNumbers', ], 'members' => [ 'arn' => [ 'shape' => 'LinkedWhatsAppBusinessAccountArn', ], 'id' => [ 'shape' => 'LinkedWhatsAppBusinessAccountId', ], 'wabaId' => [ 'shape' => 'WhatsAppBusinessAccountId', ], 'registrationStatus' => [ 'shape' => 'RegistrationStatus', ], 'linkDate' => [ 'shape' => 'WhatsAppBusinessAccountLinkDate', ], 'wabaName' => [ 'shape' => 'WhatsAppBusinessAccountName', ], 'eventDestinations' => [ 'shape' => 'WhatsAppBusinessAccountEventDestinations', ], 'phoneNumbers' => [ 'shape' => 'WhatsAppPhoneNumberSummaryList', ], ], ], 'LinkedWhatsAppBusinessAccountArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => 'arn:.*:waba/[0-9a-zA-Z]+', ], 'LinkedWhatsAppBusinessAccountId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '.*(^waba-.*$)|(^arn:.*:waba/[0-9a-zA-Z]+$).*', ], 'LinkedWhatsAppBusinessAccountIdMetaData' => [ 'type' => 'structure', 'members' => [ 'accountName' => [ 'shape' => 'WhatsAppBusinessAccountName', ], 'registrationStatus' => [ 'shape' => 'RegistrationStatus', ], 'unregisteredWhatsAppPhoneNumbers' => [ 'shape' => 'WhatsAppPhoneNumberDetailList', ], 'wabaId' => [ 'shape' => 'LinkedWhatsAppBusinessAccountId', ], ], ], 'LinkedWhatsAppBusinessAccountSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'id', 'wabaId', 'registrationStatus', 'linkDate', 'wabaName', 'eventDestinations', ], 'members' => [ 'arn' => [ 'shape' => 'LinkedWhatsAppBusinessAccountArn', ], 'id' => [ 'shape' => 'LinkedWhatsAppBusinessAccountId', ], 'wabaId' => [ 'shape' => 'WhatsAppBusinessAccountId', ], 'registrationStatus' => [ 'shape' => 'RegistrationStatus', ], 'linkDate' => [ 'shape' => 'WhatsAppBusinessAccountLinkDate', ], 'wabaName' => [ 'shape' => 'WhatsAppBusinessAccountName', ], 'eventDestinations' => [ 'shape' => 'WhatsAppBusinessAccountEventDestinations', ], ], ], 'LinkedWhatsAppBusinessAccountSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LinkedWhatsAppBusinessAccountSummary', ], ], 'LinkedWhatsAppPhoneNumberArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => 'arn:.*:phone-number-id/[0-9a-zA-Z]+', ], 'ListLinkedWhatsAppBusinessAccountsInput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListLinkedWhatsAppBusinessAccountsOutput' => [ 'type' => 'structure', 'members' => [ 'linkedAccounts' => [ 'shape' => 'LinkedWhatsAppBusinessAccountSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceOutput' => [ 'type' => 'structure', 'members' => [ 'statusCode' => [ 'shape' => 'Integer', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'NextToken' => [ 'type' => 'string', 'max' => 600, 'min' => 1, ], 'PhoneNumber' => [ 'type' => 'string', 'max' => 20, 'min' => 1, ], 'PostWhatsAppMessageMediaInput' => [ 'type' => 'structure', 'required' => [ 'originationPhoneNumberId', ], 'members' => [ 'originationPhoneNumberId' => [ 'shape' => 'WhatsAppPhoneNumberId', ], 'sourceS3PresignedUrl' => [ 'shape' => 'S3PresignedUrl', ], 'sourceS3File' => [ 'shape' => 'S3File', ], ], ], 'PostWhatsAppMessageMediaOutput' => [ 'type' => 'structure', 'members' => [ 'mediaId' => [ 'shape' => 'WhatsAppMediaId', ], ], ], 'PutWhatsAppBusinessAccountEventDestinationsInput' => [ 'type' => 'structure', 'required' => [ 'id', 'eventDestinations', ], 'members' => [ 'id' => [ 'shape' => 'LinkedWhatsAppBusinessAccountId', ], 'eventDestinations' => [ 'shape' => 'WhatsAppBusinessAccountEventDestinations', ], ], ], 'PutWhatsAppBusinessAccountEventDestinationsOutput' => [ 'type' => 'structure', 'members' => [], ], 'RegistrationStatus' => [ 'type' => 'string', 'enum' => [ 'COMPLETE', 'INCOMPLETE', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RoleArn' => [ 'type' => 'string', 'pattern' => 'arn:aws:iam::\\d{12}:role\\/[a-zA-Z0-9+=,.@\\-_]+', ], 'S3File' => [ 'type' => 'structure', 'required' => [ 'bucketName', 'key', ], 'members' => [ 'bucketName' => [ 'shape' => 'S3FileBucketNameString', ], 'key' => [ 'shape' => 'S3FileKeyString', ], ], 'sensitive' => true, ], 'S3FileBucketNameString' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '[a-z0-9][a-z0-9.-]*[a-z0-9]', ], 'S3FileKeyString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'S3PresignedUrl' => [ 'type' => 'structure', 'required' => [ 'url', 'headers', ], 'members' => [ 'url' => [ 'shape' => 'S3PresignedUrlUrlString', ], 'headers' => [ 'shape' => 'Headers', ], ], 'sensitive' => true, ], 'S3PresignedUrlUrlString' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, 'pattern' => 'https://(.*)s3(.*).amazonaws.com/(.*)', ], 'SendWhatsAppMessageInput' => [ 'type' => 'structure', 'required' => [ 'originationPhoneNumberId', 'message', 'metaApiVersion', ], 'members' => [ 'originationPhoneNumberId' => [ 'shape' => 'WhatsAppPhoneNumberId', ], 'message' => [ 'shape' => 'WhatsAppMessageBlob', ], 'metaApiVersion' => [ 'shape' => 'String', ], ], ], 'SendWhatsAppMessageOutput' => [ 'type' => 'structure', 'members' => [ 'messageId' => [ 'shape' => 'String', ], ], ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'key', ], 'members' => [ 'key' => [ 'shape' => 'TagKeyString', ], 'value' => [ 'shape' => 'TagValueString', ], ], ], 'TagKeyString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceOutput' => [ 'type' => 'structure', 'members' => [ 'statusCode' => [ 'shape' => 'Integer', ], ], ], 'TagValueString' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'ThrottledRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'TwoFactorPin' => [ 'type' => 'string', 'max' => 6, 'min' => 1, 'sensitive' => true, ], 'UntagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', ], 'tagKeys' => [ 'shape' => 'StringList', ], ], ], 'UntagResourceOutput' => [ 'type' => 'structure', 'members' => [ 'statusCode' => [ 'shape' => 'Integer', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'WabaPhoneNumberSetupFinalization' => [ 'type' => 'structure', 'required' => [ 'id', 'twoFactorPin', ], 'members' => [ 'id' => [ 'shape' => 'WhatsAppPhoneNumber', ], 'twoFactorPin' => [ 'shape' => 'TwoFactorPin', ], 'dataLocalizationRegion' => [ 'shape' => 'IsoCountryCode', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'WabaPhoneNumberSetupFinalizationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WabaPhoneNumberSetupFinalization', ], ], 'WabaSetupFinalization' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'WhatsAppBusinessAccountId', ], 'eventDestinations' => [ 'shape' => 'WhatsAppBusinessAccountEventDestinations', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'WhatsAppBusinessAccountEventDestination' => [ 'type' => 'structure', 'required' => [ 'eventDestinationArn', ], 'members' => [ 'eventDestinationArn' => [ 'shape' => 'EventDestinationArn', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'WhatsAppBusinessAccountEventDestinations' => [ 'type' => 'list', 'member' => [ 'shape' => 'WhatsAppBusinessAccountEventDestination', ], 'max' => 1, 'min' => 0, ], 'WhatsAppBusinessAccountId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'WhatsAppBusinessAccountLinkDate' => [ 'type' => 'timestamp', ], 'WhatsAppBusinessAccountName' => [ 'type' => 'string', 'max' => 200, 'min' => 0, ], 'WhatsAppDisplayPhoneNumber' => [ 'type' => 'string', 'max' => 20, 'min' => 0, ], 'WhatsAppMediaId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[A-Za-z0-9]+', ], 'WhatsAppMessageBlob' => [ 'type' => 'blob', 'max' => 2048000, 'min' => 1, 'sensitive' => true, ], 'WhatsAppPhoneNumber' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'WhatsAppPhoneNumberDetail' => [ 'type' => 'structure', 'required' => [ 'arn', 'phoneNumber', 'phoneNumberId', 'metaPhoneNumberId', 'displayPhoneNumberName', 'displayPhoneNumber', 'qualityRating', ], 'members' => [ 'arn' => [ 'shape' => 'LinkedWhatsAppPhoneNumberArn', ], 'phoneNumber' => [ 'shape' => 'PhoneNumber', ], 'phoneNumberId' => [ 'shape' => 'WhatsAppPhoneNumberId', ], 'metaPhoneNumberId' => [ 'shape' => 'WhatsAppPhoneNumber', ], 'displayPhoneNumberName' => [ 'shape' => 'WhatsAppPhoneNumberName', ], 'displayPhoneNumber' => [ 'shape' => 'WhatsAppDisplayPhoneNumber', ], 'qualityRating' => [ 'shape' => 'WhatsAppPhoneNumberQualityRating', ], ], ], 'WhatsAppPhoneNumberDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WhatsAppPhoneNumberDetail', ], ], 'WhatsAppPhoneNumberId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '.*(^phone-number-id-.*$)|(^arn:.*:phone-number-id/[0-9a-zA-Z]+$).*', ], 'WhatsAppPhoneNumberName' => [ 'type' => 'string', 'max' => 200, 'min' => 0, ], 'WhatsAppPhoneNumberQualityRating' => [ 'type' => 'string', 'max' => 10, 'min' => 0, ], 'WhatsAppPhoneNumberSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'phoneNumber', 'phoneNumberId', 'metaPhoneNumberId', 'displayPhoneNumberName', 'displayPhoneNumber', 'qualityRating', ], 'members' => [ 'arn' => [ 'shape' => 'LinkedWhatsAppPhoneNumberArn', ], 'phoneNumber' => [ 'shape' => 'PhoneNumber', ], 'phoneNumberId' => [ 'shape' => 'WhatsAppPhoneNumberId', ], 'metaPhoneNumberId' => [ 'shape' => 'WhatsAppPhoneNumber', ], 'displayPhoneNumberName' => [ 'shape' => 'WhatsAppPhoneNumberName', ], 'displayPhoneNumber' => [ 'shape' => 'WhatsAppDisplayPhoneNumber', ], 'qualityRating' => [ 'shape' => 'WhatsAppPhoneNumberQualityRating', ], ], ], 'WhatsAppPhoneNumberSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WhatsAppPhoneNumberSummary', ], ], 'WhatsAppSetupFinalization' => [ 'type' => 'structure', 'required' => [ 'associateInProgressToken', 'phoneNumbers', ], 'members' => [ 'associateInProgressToken' => [ 'shape' => 'AssociateInProgressToken', ], 'phoneNumbers' => [ 'shape' => 'WabaPhoneNumberSetupFinalizationList', ], 'phoneNumberParent' => [ 'shape' => 'LinkedWhatsAppBusinessAccountId', ], 'waba' => [ 'shape' => 'WabaSetupFinalization', ], ], ], 'WhatsAppSignupCallback' => [ 'type' => 'structure', 'required' => [ 'accessToken', ], 'members' => [ 'accessToken' => [ 'shape' => 'WhatsAppSignupCallbackAccessTokenString', ], ], ], 'WhatsAppSignupCallbackAccessTokenString' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'WhatsAppSignupCallbackResult' => [ 'type' => 'structure', 'members' => [ 'associateInProgressToken' => [ 'shape' => 'AssociateInProgressToken', ], 'linkedAccountsWithIncompleteSetup' => [ 'shape' => 'LinkedAccountWithIncompleteSetup', ], ], ], ],];
