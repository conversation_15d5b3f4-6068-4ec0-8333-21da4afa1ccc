<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Numbers\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\Values;
use Twilio\Version;
use Twilio\InstanceContext;


class PortingWebhookConfigurationDeleteContext extends InstanceContext
    {
    /**
     * Initialize the PortingWebhookConfigurationDeleteContext
     *
     * @param Version $version Version that contains the resource
     * @param string $webhookType The webhook type for the configuration to be delete. `PORT_IN`, `PORT_OUT`
     */
    public function __construct(
        Version $version,
        $webhookType
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'webhookType' =>
            $webhookType,
        ];

        $this->uri = '/Porting/Configuration/Webhook/' . \rawurlencode($webhookType)
        .'';
    }

    /**
     * Delete the PortingWebhookConfigurationDeleteInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded' ]);
        return $this->version->delete('DELETE', $this->uri, [], [], $headers);
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Numbers.V1.PortingWebhookConfigurationDeleteContext ' . \implode(' ', $context) . ']';
    }
}
