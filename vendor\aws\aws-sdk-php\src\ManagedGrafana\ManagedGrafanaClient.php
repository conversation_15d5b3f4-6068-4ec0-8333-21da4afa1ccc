<?php
namespace Aws\ManagedGrafana;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon Managed Grafana** service.
 * @method \Aws\Result associateLicense(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateLicenseAsync(array $args = [])
 * @method \Aws\Result createWorkspace(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createWorkspaceAsync(array $args = [])
 * @method \Aws\Result createWorkspaceApiKey(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createWorkspaceApiKeyAsync(array $args = [])
 * @method \Aws\Result createWorkspaceServiceAccount(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createWorkspaceServiceAccountAsync(array $args = [])
 * @method \Aws\Result createWorkspaceServiceAccountToken(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createWorkspaceServiceAccountTokenAsync(array $args = [])
 * @method \Aws\Result deleteWorkspace(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteWorkspaceAsync(array $args = [])
 * @method \Aws\Result deleteWorkspaceApiKey(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteWorkspaceApiKeyAsync(array $args = [])
 * @method \Aws\Result deleteWorkspaceServiceAccount(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteWorkspaceServiceAccountAsync(array $args = [])
 * @method \Aws\Result deleteWorkspaceServiceAccountToken(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteWorkspaceServiceAccountTokenAsync(array $args = [])
 * @method \Aws\Result describeWorkspace(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeWorkspaceAsync(array $args = [])
 * @method \Aws\Result describeWorkspaceAuthentication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeWorkspaceAuthenticationAsync(array $args = [])
 * @method \Aws\Result describeWorkspaceConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeWorkspaceConfigurationAsync(array $args = [])
 * @method \Aws\Result disassociateLicense(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateLicenseAsync(array $args = [])
 * @method \Aws\Result listPermissions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPermissionsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result listVersions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listVersionsAsync(array $args = [])
 * @method \Aws\Result listWorkspaceServiceAccountTokens(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listWorkspaceServiceAccountTokensAsync(array $args = [])
 * @method \Aws\Result listWorkspaceServiceAccounts(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listWorkspaceServiceAccountsAsync(array $args = [])
 * @method \Aws\Result listWorkspaces(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listWorkspacesAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updatePermissions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updatePermissionsAsync(array $args = [])
 * @method \Aws\Result updateWorkspace(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateWorkspaceAsync(array $args = [])
 * @method \Aws\Result updateWorkspaceAuthentication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateWorkspaceAuthenticationAsync(array $args = [])
 * @method \Aws\Result updateWorkspaceConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateWorkspaceConfigurationAsync(array $args = [])
 */
class ManagedGrafanaClient extends AwsClient {}
