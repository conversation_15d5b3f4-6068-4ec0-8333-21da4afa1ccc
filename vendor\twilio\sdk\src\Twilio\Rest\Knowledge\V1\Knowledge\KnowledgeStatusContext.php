<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Knowledge
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Knowledge\V1\Knowledge;

use Twilio\Exceptions\TwilioException;
use Twilio\Values;
use Twilio\Version;
use Twilio\InstanceContext;


class KnowledgeStatusContext extends InstanceContext
    {
    /**
     * Initialize the KnowledgeStatusContext
     *
     * @param Version $version Version that contains the resource
     * @param string $id the Knowledge ID.
     */
    public function __construct(
        Version $version,
        $id
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'id' =>
            $id,
        ];

        $this->uri = '/Knowledge/' . \rawurlencode($id)
        .'/Status';
    }

    /**
     * Fetch the KnowledgeStatusInstance
     *
     * @return KnowledgeStatusInstance Fetched KnowledgeStatusInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): KnowledgeStatusInstance
    {

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json' ]);
        $payload = $this->version->fetch('GET', $this->uri, [], [], $headers);

        return new KnowledgeStatusInstance(
            $this->version,
            $payload,
            $this->solution['id']
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Knowledge.V1.KnowledgeStatusContext ' . \implode(' ', $context) . ']';
    }
}
