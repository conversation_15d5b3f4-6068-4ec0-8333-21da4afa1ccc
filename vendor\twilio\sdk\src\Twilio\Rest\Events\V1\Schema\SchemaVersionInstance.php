<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Events
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Events\V1\Schema;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;


/**
 * @property string|null $id
 * @property int $schemaVersion
 * @property \DateTime|null $dateCreated
 * @property string|null $url
 * @property string|null $raw
 */
class SchemaVersionInstance extends InstanceResource
{
    /**
     * Initialize the SchemaVersionInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $id The unique identifier of the schema. Each schema can have multiple versions, that share the same id.
     * @param int $schemaVersion The version of the schema
     */
    public function __construct(Version $version, array $payload, string $id, ?int $schemaVersion = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'id' => Values::array_get($payload, 'id'),
            'schemaVersion' => Values::array_get($payload, 'schema_version'),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'url' => Values::array_get($payload, 'url'),
            'raw' => Values::array_get($payload, 'raw'),
        ];

        $this->solution = ['id' => $id, 'schemaVersion' => $schemaVersion ?: $this->properties['schemaVersion'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return SchemaVersionContext Context for this SchemaVersionInstance
     */
    protected function proxy(): SchemaVersionContext
    {
        if (!$this->context) {
            $this->context = new SchemaVersionContext(
                $this->version,
                $this->solution['id'],
                $this->solution['schemaVersion']
            );
        }

        return $this->context;
    }

    /**
     * Fetch the SchemaVersionInstance
     *
     * @return SchemaVersionInstance Fetched SchemaVersionInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): SchemaVersionInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Events.V1.SchemaVersionInstance ' . \implode(' ', $context) . ']';
    }
}

