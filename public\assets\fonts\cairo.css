/* Cairo Font Family - Arabic Typography */

@font-face {
    font-family: 'Cairo';
    src: url('./Cairo-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Cairo';
    src: url('./Cairo-Medium.ttf') format('truetype');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Cairo';
    src: url('./Cairo-SemiBold.ttf') format('truetype');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Cairo';
    src: url('./Cairo-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

/* Arabic Language Styles */
html[lang="ar"], 
html[lang="ar"] body,
.lang-ar,
[dir="rtl"] {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    direction: rtl;
    text-align: right;
}

/* Arabic Text Elements */
html[lang="ar"] h1,
html[lang="ar"] h2,
html[lang="ar"] h3,
html[lang="ar"] h4,
html[lang="ar"] h5,
html[lang="ar"] h6 {
    font-family: 'Cairo', sans-serif !important;
    font-weight: 600;
}

html[lang="ar"] p,
html[lang="ar"] span,
html[lang="ar"] div,
html[lang="ar"] label,
html[lang="ar"] input,
html[lang="ar"] textarea,
html[lang="ar"] select,
html[lang="ar"] button {
    font-family: 'Cairo', sans-serif !important;
}

/* Form Elements for Arabic */
html[lang="ar"] .form-control,
html[lang="ar"] .form-select,
html[lang="ar"] .btn {
    font-family: 'Cairo', sans-serif !important;
    text-align: right;
}

/* Navigation and Menu Items */
html[lang="ar"] .nav-link,
html[lang="ar"] .dropdown-item,
html[lang="ar"] .breadcrumb-item {
    font-family: 'Cairo', sans-serif !important;
}

/* Table Content */
html[lang="ar"] table,
html[lang="ar"] th,
html[lang="ar"] td {
    font-family: 'Cairo', sans-serif !important;
    text-align: right;
}

/* Card and Panel Content */
html[lang="ar"] .card,
html[lang="ar"] .card-body,
html[lang="ar"] .card-title,
html[lang="ar"] .card-text {
    font-family: 'Cairo', sans-serif !important;
}

/* Modal Content */
html[lang="ar"] .modal-content,
html[lang="ar"] .modal-title,
html[lang="ar"] .modal-body {
    font-family: 'Cairo', sans-serif !important;
}

/* Sidebar and Dashboard */
html[lang="ar"] .sidebar,
html[lang="ar"] .dash-sidebar,
html[lang="ar"] .dash-content {
    font-family: 'Cairo', sans-serif !important;
}

/* Responsive Font Sizes for Arabic */
@media (max-width: 768px) {
    html[lang="ar"] {
        font-size: 14px;
    }
}

@media (min-width: 769px) {
    html[lang="ar"] {
        font-size: 16px;
    }
}

/* RTL Layout Adjustments */
html[lang="ar"] .text-left {
    text-align: right !important;
}

html[lang="ar"] .text-right {
    text-align: left !important;
}

html[lang="ar"] .float-left {
    float: right !important;
}

html[lang="ar"] .float-right {
    float: left !important;
}

/* Margin and Padding RTL Adjustments */
html[lang="ar"] .ml-1 { margin-right: 0.25rem !important; margin-left: 0 !important; }
html[lang="ar"] .ml-2 { margin-right: 0.5rem !important; margin-left: 0 !important; }
html[lang="ar"] .ml-3 { margin-right: 1rem !important; margin-left: 0 !important; }
html[lang="ar"] .ml-4 { margin-right: 1.5rem !important; margin-left: 0 !important; }
html[lang="ar"] .ml-5 { margin-right: 3rem !important; margin-left: 0 !important; }

html[lang="ar"] .mr-1 { margin-left: 0.25rem !important; margin-right: 0 !important; }
html[lang="ar"] .mr-2 { margin-left: 0.5rem !important; margin-right: 0 !important; }
html[lang="ar"] .mr-3 { margin-left: 1rem !important; margin-right: 0 !important; }
html[lang="ar"] .mr-4 { margin-left: 1.5rem !important; margin-right: 0 !important; }
html[lang="ar"] .mr-5 { margin-left: 3rem !important; margin-right: 0 !important; }

html[lang="ar"] .pl-1 { padding-right: 0.25rem !important; padding-left: 0 !important; }
html[lang="ar"] .pl-2 { padding-right: 0.5rem !important; padding-left: 0 !important; }
html[lang="ar"] .pl-3 { padding-right: 1rem !important; padding-left: 0 !important; }
html[lang="ar"] .pl-4 { padding-right: 1.5rem !important; padding-left: 0 !important; }
html[lang="ar"] .pl-5 { padding-right: 3rem !important; padding-left: 0 !important; }

html[lang="ar"] .pr-1 { padding-left: 0.25rem !important; padding-right: 0 !important; }
html[lang="ar"] .pr-2 { padding-left: 0.5rem !important; padding-right: 0 !important; }
html[lang="ar"] .pr-3 { padding-left: 1rem !important; padding-right: 0 !important; }
html[lang="ar"] .pr-4 { padding-left: 1.5rem !important; padding-right: 0 !important; }
html[lang="ar"] .pr-5 { padding-left: 3rem !important; padding-right: 0 !important; }

/* Arabic Number Formatting */
html[lang="ar"] .arabic-numbers {
    font-feature-settings: "lnum" 1;
}

/* Improve Arabic Text Rendering */
html[lang="ar"] {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
