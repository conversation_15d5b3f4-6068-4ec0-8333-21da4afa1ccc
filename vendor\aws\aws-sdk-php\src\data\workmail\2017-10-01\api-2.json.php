<?php
// This file was auto-generated from sdk-root/src/data/workmail/2017-10-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-10-01', 'endpointPrefix' => 'workmail', 'jsonVersion' => '1.1', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'Amazon WorkMail', 'serviceId' => 'WorkMail', 'signatureVersion' => 'v4', 'targetPrefix' => 'WorkMailService', 'uid' => 'workmail-2017-10-01', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AssociateDelegateToResource' => [ 'name' => 'AssociateDelegateToResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateDelegateToResourceRequest', ], 'output' => [ 'shape' => 'AssociateDelegateToResourceResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'UnsupportedOperationException', ], ], 'idempotent' => true, ], 'AssociateMemberToGroup' => [ 'name' => 'AssociateMemberToGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateMemberToGroupRequest', ], 'output' => [ 'shape' => 'AssociateMemberToGroupResponse', ], 'errors' => [ [ 'shape' => 'DirectoryServiceAuthenticationFailedException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'UnsupportedOperationException', ], ], 'idempotent' => true, ], 'AssumeImpersonationRole' => [ 'name' => 'AssumeImpersonationRole', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssumeImpersonationRoleRequest', ], 'output' => [ 'shape' => 'AssumeImpersonationRoleResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CancelMailboxExportJob' => [ 'name' => 'CancelMailboxExportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelMailboxExportJobRequest', ], 'output' => [ 'shape' => 'CancelMailboxExportJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'EntityNotFoundException', ], ], 'idempotent' => true, ], 'CreateAlias' => [ 'name' => 'CreateAlias', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAliasRequest', ], 'output' => [ 'shape' => 'CreateAliasResponse', ], 'errors' => [ [ 'shape' => 'EmailAddressInUseException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MailDomainNotFoundException', ], [ 'shape' => 'MailDomainStateException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'LimitExceededException', ], ], 'idempotent' => true, ], 'CreateAvailabilityConfiguration' => [ 'name' => 'CreateAvailabilityConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAvailabilityConfigurationRequest', ], 'output' => [ 'shape' => 'CreateAvailabilityConfigurationResponse', ], 'errors' => [ [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'NameAvailabilityException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LimitExceededException', ], ], 'idempotent' => true, ], 'CreateGroup' => [ 'name' => 'CreateGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateGroupRequest', ], 'output' => [ 'shape' => 'CreateGroupResponse', ], 'errors' => [ [ 'shape' => 'DirectoryServiceAuthenticationFailedException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NameAvailabilityException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'ReservedNameException', ], [ 'shape' => 'UnsupportedOperationException', ], ], 'idempotent' => true, ], 'CreateIdentityCenterApplication' => [ 'name' => 'CreateIdentityCenterApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateIdentityCenterApplicationRequest', ], 'output' => [ 'shape' => 'CreateIdentityCenterApplicationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], ], 'idempotent' => true, ], 'CreateImpersonationRole' => [ 'name' => 'CreateImpersonationRole', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateImpersonationRoleRequest', ], 'output' => [ 'shape' => 'CreateImpersonationRoleResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateMobileDeviceAccessRule' => [ 'name' => 'CreateMobileDeviceAccessRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateMobileDeviceAccessRuleRequest', ], 'output' => [ 'shape' => 'CreateMobileDeviceAccessRuleResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], ], 'CreateOrganization' => [ 'name' => 'CreateOrganization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateOrganizationRequest', ], 'output' => [ 'shape' => 'CreateOrganizationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DirectoryInUseException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'NameAvailabilityException', ], ], 'idempotent' => true, ], 'CreateResource' => [ 'name' => 'CreateResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateResourceRequest', ], 'output' => [ 'shape' => 'CreateResourceResponse', ], 'errors' => [ [ 'shape' => 'DirectoryServiceAuthenticationFailedException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NameAvailabilityException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'ReservedNameException', ], [ 'shape' => 'UnsupportedOperationException', ], ], 'idempotent' => true, ], 'CreateUser' => [ 'name' => 'CreateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUserRequest', ], 'output' => [ 'shape' => 'CreateUserResponse', ], 'errors' => [ [ 'shape' => 'DirectoryServiceAuthenticationFailedException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidPasswordException', ], [ 'shape' => 'NameAvailabilityException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'ReservedNameException', ], [ 'shape' => 'UnsupportedOperationException', ], ], 'idempotent' => true, ], 'DeleteAccessControlRule' => [ 'name' => 'DeleteAccessControlRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAccessControlRuleRequest', ], 'output' => [ 'shape' => 'DeleteAccessControlRuleResponse', ], 'errors' => [ [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], ], 'DeleteAlias' => [ 'name' => 'DeleteAlias', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAliasRequest', ], 'output' => [ 'shape' => 'DeleteAliasResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'DeleteAvailabilityConfiguration' => [ 'name' => 'DeleteAvailabilityConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAvailabilityConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteAvailabilityConfigurationResponse', ], 'errors' => [ [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'DeleteEmailMonitoringConfiguration' => [ 'name' => 'DeleteEmailMonitoringConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEmailMonitoringConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteEmailMonitoringConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'DeleteGroup' => [ 'name' => 'DeleteGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteGroupRequest', ], 'output' => [ 'shape' => 'DeleteGroupResponse', ], 'errors' => [ [ 'shape' => 'DirectoryServiceAuthenticationFailedException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'EntityStateException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'UnsupportedOperationException', ], ], 'idempotent' => true, ], 'DeleteIdentityCenterApplication' => [ 'name' => 'DeleteIdentityCenterApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteIdentityCenterApplicationRequest', ], 'output' => [ 'shape' => 'DeleteIdentityCenterApplicationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'DeleteIdentityProviderConfiguration' => [ 'name' => 'DeleteIdentityProviderConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteIdentityProviderConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteIdentityProviderConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'DeleteImpersonationRole' => [ 'name' => 'DeleteImpersonationRole', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteImpersonationRoleRequest', ], 'output' => [ 'shape' => 'DeleteImpersonationRoleResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], ], 'DeleteMailboxPermissions' => [ 'name' => 'DeleteMailboxPermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteMailboxPermissionsRequest', ], 'output' => [ 'shape' => 'DeleteMailboxPermissionsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'DeleteMobileDeviceAccessOverride' => [ 'name' => 'DeleteMobileDeviceAccessOverride', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteMobileDeviceAccessOverrideRequest', ], 'output' => [ 'shape' => 'DeleteMobileDeviceAccessOverrideResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'DeleteMobileDeviceAccessRule' => [ 'name' => 'DeleteMobileDeviceAccessRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteMobileDeviceAccessRuleRequest', ], 'output' => [ 'shape' => 'DeleteMobileDeviceAccessRuleResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], ], 'DeleteOrganization' => [ 'name' => 'DeleteOrganization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteOrganizationRequest', ], 'output' => [ 'shape' => 'DeleteOrganizationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'DeletePersonalAccessToken' => [ 'name' => 'DeletePersonalAccessToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePersonalAccessTokenRequest', ], 'output' => [ 'shape' => 'DeletePersonalAccessTokenResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'DeleteResource' => [ 'name' => 'DeleteResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteResourceRequest', ], 'output' => [ 'shape' => 'DeleteResourceResponse', ], 'errors' => [ [ 'shape' => 'EntityStateException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'UnsupportedOperationException', ], ], 'idempotent' => true, ], 'DeleteRetentionPolicy' => [ 'name' => 'DeleteRetentionPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRetentionPolicyRequest', ], 'output' => [ 'shape' => 'DeleteRetentionPolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'DeleteUser' => [ 'name' => 'DeleteUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteUserRequest', ], 'output' => [ 'shape' => 'DeleteUserResponse', ], 'errors' => [ [ 'shape' => 'DirectoryServiceAuthenticationFailedException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'EntityStateException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'UnsupportedOperationException', ], ], 'idempotent' => true, ], 'DeregisterFromWorkMail' => [ 'name' => 'DeregisterFromWorkMail', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterFromWorkMailRequest', ], 'output' => [ 'shape' => 'DeregisterFromWorkMailResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'DeregisterMailDomain' => [ 'name' => 'DeregisterMailDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterMailDomainRequest', ], 'output' => [ 'shape' => 'DeregisterMailDomainResponse', ], 'errors' => [ [ 'shape' => 'MailDomainInUseException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidCustomSesConfigurationException', ], ], 'idempotent' => true, ], 'DescribeEmailMonitoringConfiguration' => [ 'name' => 'DescribeEmailMonitoringConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEmailMonitoringConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeEmailMonitoringConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'DescribeEntity' => [ 'name' => 'DescribeEntity', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEntityRequest', ], 'output' => [ 'shape' => 'DescribeEntityResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'DescribeGroup' => [ 'name' => 'DescribeGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeGroupRequest', ], 'output' => [ 'shape' => 'DescribeGroupResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'DescribeIdentityProviderConfiguration' => [ 'name' => 'DescribeIdentityProviderConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeIdentityProviderConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeIdentityProviderConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeInboundDmarcSettings' => [ 'name' => 'DescribeInboundDmarcSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeInboundDmarcSettingsRequest', ], 'output' => [ 'shape' => 'DescribeInboundDmarcSettingsResponse', ], 'errors' => [ [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'DescribeMailboxExportJob' => [ 'name' => 'DescribeMailboxExportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMailboxExportJobRequest', ], 'output' => [ 'shape' => 'DescribeMailboxExportJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'EntityNotFoundException', ], ], 'idempotent' => true, ], 'DescribeOrganization' => [ 'name' => 'DescribeOrganization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeOrganizationRequest', ], 'output' => [ 'shape' => 'DescribeOrganizationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], ], 'idempotent' => true, ], 'DescribeResource' => [ 'name' => 'DescribeResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeResourceRequest', ], 'output' => [ 'shape' => 'DescribeResourceResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'UnsupportedOperationException', ], ], 'idempotent' => true, ], 'DescribeUser' => [ 'name' => 'DescribeUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeUserRequest', ], 'output' => [ 'shape' => 'DescribeUserResponse', ], 'errors' => [ [ 'shape' => 'DirectoryServiceAuthenticationFailedException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'DisassociateDelegateFromResource' => [ 'name' => 'DisassociateDelegateFromResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateDelegateFromResourceRequest', ], 'output' => [ 'shape' => 'DisassociateDelegateFromResourceResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'UnsupportedOperationException', ], ], 'idempotent' => true, ], 'DisassociateMemberFromGroup' => [ 'name' => 'DisassociateMemberFromGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateMemberFromGroupRequest', ], 'output' => [ 'shape' => 'DisassociateMemberFromGroupResponse', ], 'errors' => [ [ 'shape' => 'DirectoryServiceAuthenticationFailedException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'UnsupportedOperationException', ], ], 'idempotent' => true, ], 'GetAccessControlEffect' => [ 'name' => 'GetAccessControlEffect', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAccessControlEffectRequest', ], 'output' => [ 'shape' => 'GetAccessControlEffectResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], ], 'GetDefaultRetentionPolicy' => [ 'name' => 'GetDefaultRetentionPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDefaultRetentionPolicyRequest', ], 'output' => [ 'shape' => 'GetDefaultRetentionPolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'EntityNotFoundException', ], ], 'idempotent' => true, ], 'GetImpersonationRole' => [ 'name' => 'GetImpersonationRole', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetImpersonationRoleRequest', ], 'output' => [ 'shape' => 'GetImpersonationRoleResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetImpersonationRoleEffect' => [ 'name' => 'GetImpersonationRoleEffect', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetImpersonationRoleEffectRequest', ], 'output' => [ 'shape' => 'GetImpersonationRoleEffectResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], ], ], 'GetMailDomain' => [ 'name' => 'GetMailDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMailDomainRequest', ], 'output' => [ 'shape' => 'GetMailDomainResponse', ], 'errors' => [ [ 'shape' => 'MailDomainNotFoundException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'InvalidParameterException', ], ], 'idempotent' => true, ], 'GetMailboxDetails' => [ 'name' => 'GetMailboxDetails', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMailboxDetailsRequest', ], 'output' => [ 'shape' => 'GetMailboxDetailsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'EntityNotFoundException', ], ], 'idempotent' => true, ], 'GetMobileDeviceAccessEffect' => [ 'name' => 'GetMobileDeviceAccessEffect', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMobileDeviceAccessEffectRequest', ], 'output' => [ 'shape' => 'GetMobileDeviceAccessEffectResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], ], 'GetMobileDeviceAccessOverride' => [ 'name' => 'GetMobileDeviceAccessOverride', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMobileDeviceAccessOverrideRequest', ], 'output' => [ 'shape' => 'GetMobileDeviceAccessOverrideResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetPersonalAccessTokenMetadata' => [ 'name' => 'GetPersonalAccessTokenMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPersonalAccessTokenMetadataRequest', ], 'output' => [ 'shape' => 'GetPersonalAccessTokenMetadataResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'ListAccessControlRules' => [ 'name' => 'ListAccessControlRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAccessControlRulesRequest', ], 'output' => [ 'shape' => 'ListAccessControlRulesResponse', ], 'errors' => [ [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], ], 'ListAliases' => [ 'name' => 'ListAliases', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAliasesRequest', ], 'output' => [ 'shape' => 'ListAliasesResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'ListAvailabilityConfigurations' => [ 'name' => 'ListAvailabilityConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAvailabilityConfigurationsRequest', ], 'output' => [ 'shape' => 'ListAvailabilityConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'ListGroupMembers' => [ 'name' => 'ListGroupMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListGroupMembersRequest', ], 'output' => [ 'shape' => 'ListGroupMembersResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'ListGroups' => [ 'name' => 'ListGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListGroupsRequest', ], 'output' => [ 'shape' => 'ListGroupsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'ListGroupsForEntity' => [ 'name' => 'ListGroupsForEntity', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListGroupsForEntityRequest', ], 'output' => [ 'shape' => 'ListGroupsForEntityResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'InvalidParameterException', ], ], 'idempotent' => true, ], 'ListImpersonationRoles' => [ 'name' => 'ListImpersonationRoles', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListImpersonationRolesRequest', ], 'output' => [ 'shape' => 'ListImpersonationRolesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], ], 'ListMailDomains' => [ 'name' => 'ListMailDomains', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListMailDomainsRequest', ], 'output' => [ 'shape' => 'ListMailDomainsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'ListMailboxExportJobs' => [ 'name' => 'ListMailboxExportJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListMailboxExportJobsRequest', ], 'output' => [ 'shape' => 'ListMailboxExportJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'ListMailboxPermissions' => [ 'name' => 'ListMailboxPermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListMailboxPermissionsRequest', ], 'output' => [ 'shape' => 'ListMailboxPermissionsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'ListMobileDeviceAccessOverrides' => [ 'name' => 'ListMobileDeviceAccessOverrides', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListMobileDeviceAccessOverridesRequest', ], 'output' => [ 'shape' => 'ListMobileDeviceAccessOverridesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'ListMobileDeviceAccessRules' => [ 'name' => 'ListMobileDeviceAccessRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListMobileDeviceAccessRulesRequest', ], 'output' => [ 'shape' => 'ListMobileDeviceAccessRulesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], ], 'ListOrganizations' => [ 'name' => 'ListOrganizations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListOrganizationsRequest', ], 'output' => [ 'shape' => 'ListOrganizationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], ], 'idempotent' => true, ], 'ListPersonalAccessTokens' => [ 'name' => 'ListPersonalAccessTokens', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPersonalAccessTokensRequest', ], 'output' => [ 'shape' => 'ListPersonalAccessTokensResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'ListResourceDelegates' => [ 'name' => 'ListResourceDelegates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResourceDelegatesRequest', ], 'output' => [ 'shape' => 'ListResourceDelegatesResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'UnsupportedOperationException', ], ], 'idempotent' => true, ], 'ListResources' => [ 'name' => 'ListResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResourcesRequest', ], 'output' => [ 'shape' => 'ListResourcesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'UnsupportedOperationException', ], ], 'idempotent' => true, ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListUsers' => [ 'name' => 'ListUsers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListUsersRequest', ], 'output' => [ 'shape' => 'ListUsersResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'PutAccessControlRule' => [ 'name' => 'PutAccessControlRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutAccessControlRuleRequest', ], 'output' => [ 'shape' => 'PutAccessControlRuleResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], ], 'PutEmailMonitoringConfiguration' => [ 'name' => 'PutEmailMonitoringConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutEmailMonitoringConfigurationRequest', ], 'output' => [ 'shape' => 'PutEmailMonitoringConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'PutIdentityProviderConfiguration' => [ 'name' => 'PutIdentityProviderConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutIdentityProviderConfigurationRequest', ], 'output' => [ 'shape' => 'PutIdentityProviderConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'PutInboundDmarcSettings' => [ 'name' => 'PutInboundDmarcSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutInboundDmarcSettingsRequest', ], 'output' => [ 'shape' => 'PutInboundDmarcSettingsResponse', ], 'errors' => [ [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'PutMailboxPermissions' => [ 'name' => 'PutMailboxPermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutMailboxPermissionsRequest', ], 'output' => [ 'shape' => 'PutMailboxPermissionsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'PutMobileDeviceAccessOverride' => [ 'name' => 'PutMobileDeviceAccessOverride', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutMobileDeviceAccessOverrideRequest', ], 'output' => [ 'shape' => 'PutMobileDeviceAccessOverrideResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], ], ], 'PutRetentionPolicy' => [ 'name' => 'PutRetentionPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutRetentionPolicyRequest', ], 'output' => [ 'shape' => 'PutRetentionPolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'LimitExceededException', ], ], 'idempotent' => true, ], 'RegisterMailDomain' => [ 'name' => 'RegisterMailDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterMailDomainRequest', ], 'output' => [ 'shape' => 'RegisterMailDomainResponse', ], 'errors' => [ [ 'shape' => 'MailDomainInUseException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], ], 'idempotent' => true, ], 'RegisterToWorkMail' => [ 'name' => 'RegisterToWorkMail', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterToWorkMailRequest', ], 'output' => [ 'shape' => 'RegisterToWorkMailResponse', ], 'errors' => [ [ 'shape' => 'DirectoryServiceAuthenticationFailedException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'EmailAddressInUseException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], [ 'shape' => 'EntityAlreadyRegisteredException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MailDomainNotFoundException', ], [ 'shape' => 'MailDomainStateException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], 'idempotent' => true, ], 'ResetPassword' => [ 'name' => 'ResetPassword', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ResetPasswordRequest', ], 'output' => [ 'shape' => 'ResetPasswordResponse', ], 'errors' => [ [ 'shape' => 'DirectoryServiceAuthenticationFailedException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidPasswordException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'UnsupportedOperationException', ], ], 'idempotent' => true, ], 'StartMailboxExportJob' => [ 'name' => 'StartMailboxExportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartMailboxExportJobRequest', ], 'output' => [ 'shape' => 'StartMailboxExportJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'LimitExceededException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'OrganizationStateException', ], ], ], 'TestAvailabilityConfiguration' => [ 'name' => 'TestAvailabilityConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TestAvailabilityConfigurationRequest', ], 'output' => [ 'shape' => 'TestAvailabilityConfigurationResponse', ], 'errors' => [ [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateAvailabilityConfiguration' => [ 'name' => 'UpdateAvailabilityConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAvailabilityConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateAvailabilityConfigurationResponse', ], 'errors' => [ [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], ], 'idempotent' => true, ], 'UpdateDefaultMailDomain' => [ 'name' => 'UpdateDefaultMailDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDefaultMailDomainRequest', ], 'output' => [ 'shape' => 'UpdateDefaultMailDomainResponse', ], 'errors' => [ [ 'shape' => 'MailDomainNotFoundException', ], [ 'shape' => 'MailDomainStateException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'InvalidParameterException', ], ], 'idempotent' => true, ], 'UpdateGroup' => [ 'name' => 'UpdateGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateGroupRequest', ], 'output' => [ 'shape' => 'UpdateGroupResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'InvalidParameterException', ], ], 'idempotent' => true, ], 'UpdateImpersonationRole' => [ 'name' => 'UpdateImpersonationRole', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateImpersonationRoleRequest', ], 'output' => [ 'shape' => 'UpdateImpersonationRoleResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'UpdateMailboxQuota' => [ 'name' => 'UpdateMailboxQuota', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateMailboxQuotaRequest', ], 'output' => [ 'shape' => 'UpdateMailboxQuotaResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], ], 'idempotent' => true, ], 'UpdateMobileDeviceAccessRule' => [ 'name' => 'UpdateMobileDeviceAccessRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateMobileDeviceAccessRuleRequest', ], 'output' => [ 'shape' => 'UpdateMobileDeviceAccessRuleResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], ], ], 'UpdatePrimaryEmailAddress' => [ 'name' => 'UpdatePrimaryEmailAddress', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePrimaryEmailAddressRequest', ], 'output' => [ 'shape' => 'UpdatePrimaryEmailAddressResponse', ], 'errors' => [ [ 'shape' => 'DirectoryServiceAuthenticationFailedException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'EmailAddressInUseException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'MailDomainNotFoundException', ], [ 'shape' => 'MailDomainStateException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'UnsupportedOperationException', ], ], 'idempotent' => true, ], 'UpdateResource' => [ 'name' => 'UpdateResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateResourceRequest', ], 'output' => [ 'shape' => 'UpdateResourceResponse', ], 'errors' => [ [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'EntityStateException', ], [ 'shape' => 'InvalidConfigurationException', ], [ 'shape' => 'EmailAddressInUseException', ], [ 'shape' => 'MailDomainNotFoundException', ], [ 'shape' => 'MailDomainStateException', ], [ 'shape' => 'NameAvailabilityException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'InvalidParameterException', ], ], 'idempotent' => true, ], 'UpdateUser' => [ 'name' => 'UpdateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateUserRequest', ], 'output' => [ 'shape' => 'UpdateUserResponse', ], 'errors' => [ [ 'shape' => 'DirectoryServiceAuthenticationFailedException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'OrganizationNotFoundException', ], [ 'shape' => 'OrganizationStateException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'EntityStateException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessControlRule' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'AccessControlRuleName', ], 'Effect' => [ 'shape' => 'AccessControlRuleEffect', ], 'Description' => [ 'shape' => 'AccessControlRuleDescription', ], 'IpRanges' => [ 'shape' => 'IpRangeList', ], 'NotIpRanges' => [ 'shape' => 'IpRangeList', ], 'Actions' => [ 'shape' => 'ActionsList', ], 'NotActions' => [ 'shape' => 'ActionsList', ], 'UserIds' => [ 'shape' => 'UserIdList', ], 'NotUserIds' => [ 'shape' => 'UserIdList', ], 'DateCreated' => [ 'shape' => 'Timestamp', ], 'DateModified' => [ 'shape' => 'Timestamp', ], 'ImpersonationRoleIds' => [ 'shape' => 'ImpersonationRoleIdList', ], 'NotImpersonationRoleIds' => [ 'shape' => 'ImpersonationRoleIdList', ], ], ], 'AccessControlRuleAction' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z]+', ], 'AccessControlRuleDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '[\\u0020-\\u00FF]+', ], 'AccessControlRuleEffect' => [ 'type' => 'string', 'enum' => [ 'ALLOW', 'DENY', ], ], 'AccessControlRuleName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'AccessControlRuleNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessControlRuleName', ], 'max' => 10, 'min' => 0, ], 'AccessControlRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessControlRule', ], 'max' => 10, 'min' => 0, ], 'AccessEffect' => [ 'type' => 'string', 'enum' => [ 'ALLOW', 'DENY', ], ], 'ActionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessControlRuleAction', ], 'max' => 10, 'min' => 0, ], 'Aliases' => [ 'type' => 'list', 'member' => [ 'shape' => 'EmailAddress', ], ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => 'arn:aws:workmail:[a-z0-9-]*:[a-z0-9-]+:[A-Za-z0-9][A-Za-z0-9:_/+=,@.-]{0,1023}', ], 'ApplicationArn' => [ 'type' => 'string', 'max' => 1224, 'min' => 10, 'pattern' => '^arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso::\\d{12}:application/(sso)?ins-[a-zA-Z0-9-.]{16}/apl-[a-zA-Z0-9]{16}$', ], 'AssociateDelegateToResourceRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'ResourceId', 'EntityId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'ResourceId' => [ 'shape' => 'EntityIdentifier', ], 'EntityId' => [ 'shape' => 'EntityIdentifier', ], ], ], 'AssociateDelegateToResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateMemberToGroupRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'GroupId', 'MemberId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'GroupId' => [ 'shape' => 'EntityIdentifier', ], 'MemberId' => [ 'shape' => 'EntityIdentifier', ], ], ], 'AssociateMemberToGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssumeImpersonationRoleRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'ImpersonationRoleId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'ImpersonationRoleId' => [ 'shape' => 'ImpersonationRoleId', ], ], ], 'AssumeImpersonationRoleResponse' => [ 'type' => 'structure', 'members' => [ 'Token' => [ 'shape' => 'ImpersonationToken', ], 'ExpiresIn' => [ 'shape' => 'ExpiresIn', ], ], ], 'AvailabilityConfiguration' => [ 'type' => 'structure', 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', ], 'ProviderType' => [ 'shape' => 'AvailabilityProviderType', ], 'EwsProvider' => [ 'shape' => 'RedactedEwsAvailabilityProvider', ], 'LambdaProvider' => [ 'shape' => 'LambdaAvailabilityProvider', ], 'DateCreated' => [ 'shape' => 'Timestamp', ], 'DateModified' => [ 'shape' => 'Timestamp', ], ], ], 'AvailabilityConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AvailabilityConfiguration', ], ], 'AvailabilityProviderType' => [ 'type' => 'string', 'enum' => [ 'EWS', 'LAMBDA', ], ], 'BookingOptions' => [ 'type' => 'structure', 'members' => [ 'AutoAcceptRequests' => [ 'shape' => 'Boolean', ], 'AutoDeclineRecurringRequests' => [ 'shape' => 'Boolean', ], 'AutoDeclineConflictingRequests' => [ 'shape' => 'Boolean', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BooleanObject' => [ 'type' => 'boolean', ], 'CancelMailboxExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'ClientToken', 'JobId', 'OrganizationId', ], 'members' => [ 'ClientToken' => [ 'shape' => 'IdempotencyClientToken', 'idempotencyToken' => true, ], 'JobId' => [ 'shape' => 'MailboxExportJobId', ], 'OrganizationId' => [ 'shape' => 'OrganizationId', ], ], ], 'CancelMailboxExportJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateAliasRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'EntityId', 'Alias', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'EntityId' => [ 'shape' => 'WorkMailIdentifier', ], 'Alias' => [ 'shape' => 'EmailAddress', ], ], ], 'CreateAliasResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateAvailabilityConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'DomainName', ], 'members' => [ 'ClientToken' => [ 'shape' => 'IdempotencyClientToken', 'idempotencyToken' => true, ], 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'DomainName' => [ 'shape' => 'DomainName', ], 'EwsProvider' => [ 'shape' => 'EwsAvailabilityProvider', ], 'LambdaProvider' => [ 'shape' => 'LambdaAvailabilityProvider', ], ], ], 'CreateAvailabilityConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateGroupRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'Name', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'Name' => [ 'shape' => 'GroupName', ], 'HiddenFromGlobalAddressList' => [ 'shape' => 'Boolean', ], ], ], 'CreateGroupResponse' => [ 'type' => 'structure', 'members' => [ 'GroupId' => [ 'shape' => 'WorkMailIdentifier', ], ], ], 'CreateIdentityCenterApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'InstanceArn', ], 'members' => [ 'Name' => [ 'shape' => 'IdentityCenterApplicationName', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'ClientToken' => [ 'shape' => 'IdempotencyClientToken', 'idempotencyToken' => true, ], ], ], 'CreateIdentityCenterApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], ], ], 'CreateImpersonationRoleRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'Name', 'Type', 'Rules', ], 'members' => [ 'ClientToken' => [ 'shape' => 'IdempotencyClientToken', 'idempotencyToken' => true, ], 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'Name' => [ 'shape' => 'ImpersonationRoleName', ], 'Type' => [ 'shape' => 'ImpersonationRoleType', ], 'Description' => [ 'shape' => 'ImpersonationRoleDescription', ], 'Rules' => [ 'shape' => 'ImpersonationRuleList', ], ], ], 'CreateImpersonationRoleResponse' => [ 'type' => 'structure', 'members' => [ 'ImpersonationRoleId' => [ 'shape' => 'ImpersonationRoleId', ], ], ], 'CreateMobileDeviceAccessRuleRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'Name', 'Effect', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'ClientToken' => [ 'shape' => 'IdempotencyClientToken', 'idempotencyToken' => true, ], 'Name' => [ 'shape' => 'MobileDeviceAccessRuleName', ], 'Description' => [ 'shape' => 'MobileDeviceAccessRuleDescription', ], 'Effect' => [ 'shape' => 'MobileDeviceAccessRuleEffect', ], 'DeviceTypes' => [ 'shape' => 'DeviceTypeList', ], 'NotDeviceTypes' => [ 'shape' => 'DeviceTypeList', ], 'DeviceModels' => [ 'shape' => 'DeviceModelList', ], 'NotDeviceModels' => [ 'shape' => 'DeviceModelList', ], 'DeviceOperatingSystems' => [ 'shape' => 'DeviceOperatingSystemList', ], 'NotDeviceOperatingSystems' => [ 'shape' => 'DeviceOperatingSystemList', ], 'DeviceUserAgents' => [ 'shape' => 'DeviceUserAgentList', ], 'NotDeviceUserAgents' => [ 'shape' => 'DeviceUserAgentList', ], ], ], 'CreateMobileDeviceAccessRuleResponse' => [ 'type' => 'structure', 'members' => [ 'MobileDeviceAccessRuleId' => [ 'shape' => 'MobileDeviceAccessRuleId', ], ], ], 'CreateOrganizationRequest' => [ 'type' => 'structure', 'required' => [ 'Alias', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Alias' => [ 'shape' => 'OrganizationName', ], 'ClientToken' => [ 'shape' => 'IdempotencyClientToken', 'idempotencyToken' => true, ], 'Domains' => [ 'shape' => 'Domains', ], 'KmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'EnableInteroperability' => [ 'shape' => 'Boolean', ], ], ], 'CreateOrganizationResponse' => [ 'type' => 'structure', 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], ], ], 'CreateResourceRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'Name', 'Type', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'Name' => [ 'shape' => 'ResourceName', ], 'Type' => [ 'shape' => 'ResourceType', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'HiddenFromGlobalAddressList' => [ 'shape' => 'Boolean', ], ], ], 'CreateResourceResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceId' => [ 'shape' => 'ResourceId', ], ], ], 'CreateUserRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'Name', 'DisplayName', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'Name' => [ 'shape' => 'UserName', ], 'DisplayName' => [ 'shape' => 'UserAttribute', ], 'Password' => [ 'shape' => 'Password', ], 'Role' => [ 'shape' => 'UserRole', ], 'FirstName' => [ 'shape' => 'UserAttribute', ], 'LastName' => [ 'shape' => 'UserAttribute', ], 'HiddenFromGlobalAddressList' => [ 'shape' => 'Boolean', ], 'IdentityProviderUserId' => [ 'shape' => 'IdentityProviderUserId', ], ], ], 'CreateUserResponse' => [ 'type' => 'structure', 'members' => [ 'UserId' => [ 'shape' => 'WorkMailIdentifier', ], ], ], 'Delegate' => [ 'type' => 'structure', 'required' => [ 'Id', 'Type', ], 'members' => [ 'Id' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'MemberType', ], ], ], 'DeleteAccessControlRuleRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'Name', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'Name' => [ 'shape' => 'AccessControlRuleName', ], ], ], 'DeleteAccessControlRuleResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAliasRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'EntityId', 'Alias', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'EntityId' => [ 'shape' => 'WorkMailIdentifier', ], 'Alias' => [ 'shape' => 'EmailAddress', ], ], ], 'DeleteAliasResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAvailabilityConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'DomainName', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'DomainName' => [ 'shape' => 'DomainName', ], ], ], 'DeleteAvailabilityConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteEmailMonitoringConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], ], ], 'DeleteEmailMonitoringConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteGroupRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'GroupId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'GroupId' => [ 'shape' => 'EntityIdentifier', ], ], ], 'DeleteGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteIdentityCenterApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationArn', ], 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], ], ], 'DeleteIdentityCenterApplicationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteIdentityProviderConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], ], ], 'DeleteIdentityProviderConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteImpersonationRoleRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'ImpersonationRoleId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'ImpersonationRoleId' => [ 'shape' => 'ImpersonationRoleId', ], ], ], 'DeleteImpersonationRoleResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteMailboxPermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'EntityId', 'GranteeId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'EntityId' => [ 'shape' => 'EntityIdentifier', ], 'GranteeId' => [ 'shape' => 'EntityIdentifier', ], ], ], 'DeleteMailboxPermissionsResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteMobileDeviceAccessOverrideRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'UserId', 'DeviceId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'UserId' => [ 'shape' => 'EntityIdentifier', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], ], ], 'DeleteMobileDeviceAccessOverrideResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteMobileDeviceAccessRuleRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'MobileDeviceAccessRuleId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'MobileDeviceAccessRuleId' => [ 'shape' => 'MobileDeviceAccessRuleId', ], ], ], 'DeleteMobileDeviceAccessRuleResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteOrganizationRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'DeleteDirectory', ], 'members' => [ 'ClientToken' => [ 'shape' => 'IdempotencyClientToken', 'idempotencyToken' => true, ], 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'DeleteDirectory' => [ 'shape' => 'Boolean', ], 'ForceDelete' => [ 'shape' => 'Boolean', ], 'DeleteIdentityCenterApplication' => [ 'shape' => 'Boolean', ], ], ], 'DeleteOrganizationResponse' => [ 'type' => 'structure', 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'State' => [ 'shape' => 'String', ], ], ], 'DeletePersonalAccessTokenRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'PersonalAccessTokenId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'PersonalAccessTokenId' => [ 'shape' => 'PersonalAccessTokenId', ], ], ], 'DeletePersonalAccessTokenResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteResourceRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'ResourceId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'ResourceId' => [ 'shape' => 'EntityIdentifier', ], ], ], 'DeleteResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRetentionPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'Id', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'Id' => [ 'shape' => 'ShortString', ], ], ], 'DeleteRetentionPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteUserRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'UserId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'UserId' => [ 'shape' => 'EntityIdentifier', ], ], ], 'DeleteUserResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeregisterFromWorkMailRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'EntityId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'EntityId' => [ 'shape' => 'EntityIdentifier', ], ], ], 'DeregisterFromWorkMailResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeregisterMailDomainRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'DomainName', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'DomainName' => [ 'shape' => 'WorkMailDomainName', ], ], ], 'DeregisterMailDomainResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeEmailMonitoringConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], ], ], 'DescribeEmailMonitoringConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'RoleArn' => [ 'shape' => 'RoleArn', ], 'LogGroupArn' => [ 'shape' => 'LogGroupArn', ], ], ], 'DescribeEntityRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'Email', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'Email' => [ 'shape' => 'EmailAddress', ], ], ], 'DescribeEntityResponse' => [ 'type' => 'structure', 'members' => [ 'EntityId' => [ 'shape' => 'WorkMailIdentifier', ], 'Name' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'EntityType', ], ], ], 'DescribeGroupRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'GroupId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'GroupId' => [ 'shape' => 'EntityIdentifier', ], ], ], 'DescribeGroupResponse' => [ 'type' => 'structure', 'members' => [ 'GroupId' => [ 'shape' => 'WorkMailIdentifier', ], 'Name' => [ 'shape' => 'GroupName', ], 'Email' => [ 'shape' => 'EmailAddress', ], 'State' => [ 'shape' => 'EntityState', ], 'EnabledDate' => [ 'shape' => 'Timestamp', ], 'DisabledDate' => [ 'shape' => 'Timestamp', ], 'HiddenFromGlobalAddressList' => [ 'shape' => 'Boolean', ], ], ], 'DescribeIdentityProviderConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], ], ], 'DescribeIdentityProviderConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'AuthenticationMode' => [ 'shape' => 'IdentityProviderAuthenticationMode', ], 'IdentityCenterConfiguration' => [ 'shape' => 'IdentityCenterConfiguration', ], 'PersonalAccessTokenConfiguration' => [ 'shape' => 'PersonalAccessTokenConfiguration', ], ], ], 'DescribeInboundDmarcSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], ], ], 'DescribeInboundDmarcSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'Enforced' => [ 'shape' => 'Boolean', ], ], ], 'DescribeMailboxExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', 'OrganizationId', ], 'members' => [ 'JobId' => [ 'shape' => 'MailboxExportJobId', ], 'OrganizationId' => [ 'shape' => 'OrganizationId', ], ], ], 'DescribeMailboxExportJobResponse' => [ 'type' => 'structure', 'members' => [ 'EntityId' => [ 'shape' => 'WorkMailIdentifier', ], 'Description' => [ 'shape' => 'Description', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'KmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'S3BucketName' => [ 'shape' => 'S3BucketName', ], 'S3Prefix' => [ 'shape' => 'S3ObjectKey', ], 'S3Path' => [ 'shape' => 'S3ObjectKey', ], 'EstimatedProgress' => [ 'shape' => 'Percentage', ], 'State' => [ 'shape' => 'MailboxExportJobState', ], 'ErrorInfo' => [ 'shape' => 'MailboxExportErrorInfo', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeOrganizationRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], ], ], 'DescribeOrganizationResponse' => [ 'type' => 'structure', 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'Alias' => [ 'shape' => 'OrganizationName', ], 'State' => [ 'shape' => 'String', ], 'DirectoryId' => [ 'shape' => 'String', ], 'DirectoryType' => [ 'shape' => 'String', ], 'DefaultMailDomain' => [ 'shape' => 'String', ], 'CompletedDate' => [ 'shape' => 'Timestamp', ], 'ErrorMessage' => [ 'shape' => 'String', ], 'ARN' => [ 'shape' => 'AmazonResourceName', ], 'MigrationAdmin' => [ 'shape' => 'WorkMailIdentifier', ], 'InteroperabilityEnabled' => [ 'shape' => 'Boolean', ], ], ], 'DescribeResourceRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'ResourceId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'ResourceId' => [ 'shape' => 'EntityIdentifier', ], ], ], 'DescribeResourceResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceId' => [ 'shape' => 'ResourceId', ], 'Email' => [ 'shape' => 'EmailAddress', ], 'Name' => [ 'shape' => 'ResourceName', ], 'Type' => [ 'shape' => 'ResourceType', ], 'BookingOptions' => [ 'shape' => 'BookingOptions', ], 'State' => [ 'shape' => 'EntityState', ], 'EnabledDate' => [ 'shape' => 'Timestamp', ], 'DisabledDate' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'HiddenFromGlobalAddressList' => [ 'shape' => 'Boolean', ], ], ], 'DescribeUserRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'UserId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'UserId' => [ 'shape' => 'EntityIdentifier', ], ], ], 'DescribeUserResponse' => [ 'type' => 'structure', 'members' => [ 'UserId' => [ 'shape' => 'WorkMailIdentifier', ], 'Name' => [ 'shape' => 'UserName', ], 'Email' => [ 'shape' => 'EmailAddress', ], 'DisplayName' => [ 'shape' => 'UserAttribute', ], 'State' => [ 'shape' => 'EntityState', ], 'UserRole' => [ 'shape' => 'UserRole', ], 'EnabledDate' => [ 'shape' => 'Timestamp', ], 'DisabledDate' => [ 'shape' => 'Timestamp', ], 'MailboxProvisionedDate' => [ 'shape' => 'Timestamp', ], 'MailboxDeprovisionedDate' => [ 'shape' => 'Timestamp', ], 'FirstName' => [ 'shape' => 'UserAttribute', ], 'LastName' => [ 'shape' => 'UserAttribute', ], 'HiddenFromGlobalAddressList' => [ 'shape' => 'Boolean', ], 'Initials' => [ 'shape' => 'UserAttribute', ], 'Telephone' => [ 'shape' => 'UserAttribute', ], 'Street' => [ 'shape' => 'UserAttribute', ], 'JobTitle' => [ 'shape' => 'UserAttribute', ], 'City' => [ 'shape' => 'UserAttribute', ], 'Company' => [ 'shape' => 'UserAttribute', ], 'ZipCode' => [ 'shape' => 'UserAttribute', ], 'Department' => [ 'shape' => 'UserAttribute', ], 'Country' => [ 'shape' => 'UserAttribute', ], 'Office' => [ 'shape' => 'UserAttribute', ], 'IdentityProviderUserId' => [ 'shape' => 'IdentityProviderUserId', ], 'IdentityProviderIdentityStoreId' => [ 'shape' => 'IdentityProviderIdentityStoreId', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 1023, 'min' => 0, 'pattern' => '[\\S\\s]*', ], 'DeviceId' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '[A-Za-z0-9]+', ], 'DeviceModel' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\u0020-\\u00FF]+', ], 'DeviceModelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceModel', ], 'max' => 10, 'min' => 1, ], 'DeviceOperatingSystem' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\u0020-\\u00FF]+', ], 'DeviceOperatingSystemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceOperatingSystem', ], 'max' => 10, 'min' => 1, ], 'DeviceType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\u0020-\\u00FF]+', ], 'DeviceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceType', ], 'max' => 10, 'min' => 1, ], 'DeviceUserAgent' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\u0020-\\u00FF]+', ], 'DeviceUserAgentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceUserAgent', ], 'max' => 10, 'min' => 1, ], 'DirectoryId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^d-[0-9a-f]{10}$', ], 'DirectoryInUseException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'DirectoryServiceAuthenticationFailedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'DirectoryUnavailableException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'DisassociateDelegateFromResourceRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'ResourceId', 'EntityId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'ResourceId' => [ 'shape' => 'EntityIdentifier', ], 'EntityId' => [ 'shape' => 'EntityIdentifier', ], ], ], 'DisassociateDelegateFromResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateMemberFromGroupRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'GroupId', 'MemberId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'GroupId' => [ 'shape' => 'EntityIdentifier', ], 'MemberId' => [ 'shape' => 'EntityIdentifier', ], ], ], 'DisassociateMemberFromGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'DnsRecord' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Hostname' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'DnsRecordVerificationStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'VERIFIED', 'FAILED', ], ], 'DnsRecords' => [ 'type' => 'list', 'member' => [ 'shape' => 'DnsRecord', ], ], 'Domain' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', ], 'HostedZoneId' => [ 'shape' => 'HostedZoneId', ], ], ], 'DomainName' => [ 'type' => 'string', 'max' => 255, 'min' => 3, 'pattern' => '[a-zA-Z0-9.-]+', ], 'Domains' => [ 'type' => 'list', 'member' => [ 'shape' => 'Domain', ], 'max' => 5, 'min' => 0, ], 'EmailAddress' => [ 'type' => 'string', 'max' => 254, 'min' => 1, 'pattern' => '[a-zA-Z0-9._%+-]{1,64}@[a-zA-Z0-9.-]+\\.[a-zA-Z-]{2,}', ], 'EmailAddressInUseException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'EntityAlreadyRegisteredException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'EntityIdentifier' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9._%+@-]+', ], 'EntityNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'EntityState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', 'DELETED', ], ], 'EntityStateException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'EntityType' => [ 'type' => 'string', 'enum' => [ 'GROUP', 'USER', 'RESOURCE', ], ], 'EwsAvailabilityProvider' => [ 'type' => 'structure', 'required' => [ 'EwsEndpoint', 'EwsUsername', 'EwsPassword', ], 'members' => [ 'EwsEndpoint' => [ 'shape' => 'Url', ], 'EwsUsername' => [ 'shape' => 'ExternalUserName', ], 'EwsPassword' => [ 'shape' => 'Password', ], ], ], 'ExpiresIn' => [ 'type' => 'long', 'box' => true, ], 'ExternalUserName' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[\\u0020-\\u00FF]+', ], 'FolderConfiguration' => [ 'type' => 'structure', 'required' => [ 'Name', 'Action', ], 'members' => [ 'Name' => [ 'shape' => 'FolderName', ], 'Action' => [ 'shape' => 'RetentionAction', ], 'Period' => [ 'shape' => 'RetentionPeriod', ], ], ], 'FolderConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'FolderConfiguration', ], ], 'FolderName' => [ 'type' => 'string', 'enum' => [ 'INBOX', 'DELETED_ITEMS', 'SENT_ITEMS', 'DRAFTS', 'JUNK_EMAIL', ], ], 'GetAccessControlEffectRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'IpAddress', 'Action', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'IpAddress' => [ 'shape' => 'IpAddress', ], 'Action' => [ 'shape' => 'AccessControlRuleAction', ], 'UserId' => [ 'shape' => 'WorkMailIdentifier', ], 'ImpersonationRoleId' => [ 'shape' => 'ImpersonationRoleId', ], ], ], 'GetAccessControlEffectResponse' => [ 'type' => 'structure', 'members' => [ 'Effect' => [ 'shape' => 'AccessControlRuleEffect', ], 'MatchedRules' => [ 'shape' => 'AccessControlRuleNameList', ], ], ], 'GetDefaultRetentionPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], ], ], 'GetDefaultRetentionPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ShortString', ], 'Name' => [ 'shape' => 'ShortString', ], 'Description' => [ 'shape' => 'String', ], 'FolderConfigurations' => [ 'shape' => 'FolderConfigurations', ], ], ], 'GetImpersonationRoleEffectRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'ImpersonationRoleId', 'TargetUser', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'ImpersonationRoleId' => [ 'shape' => 'ImpersonationRoleId', ], 'TargetUser' => [ 'shape' => 'EntityIdentifier', ], ], ], 'GetImpersonationRoleEffectResponse' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'ImpersonationRoleType', ], 'Effect' => [ 'shape' => 'AccessEffect', ], 'MatchedRules' => [ 'shape' => 'ImpersonationMatchedRuleList', ], ], ], 'GetImpersonationRoleRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'ImpersonationRoleId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'ImpersonationRoleId' => [ 'shape' => 'ImpersonationRoleId', ], ], ], 'GetImpersonationRoleResponse' => [ 'type' => 'structure', 'members' => [ 'ImpersonationRoleId' => [ 'shape' => 'ImpersonationRoleId', ], 'Name' => [ 'shape' => 'ImpersonationRoleName', ], 'Type' => [ 'shape' => 'ImpersonationRoleType', ], 'Description' => [ 'shape' => 'ImpersonationRoleDescription', ], 'Rules' => [ 'shape' => 'ImpersonationRuleList', ], 'DateCreated' => [ 'shape' => 'Timestamp', ], 'DateModified' => [ 'shape' => 'Timestamp', ], ], ], 'GetMailDomainRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'DomainName', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'DomainName' => [ 'shape' => 'WorkMailDomainName', ], ], ], 'GetMailDomainResponse' => [ 'type' => 'structure', 'members' => [ 'Records' => [ 'shape' => 'DnsRecords', ], 'IsTestDomain' => [ 'shape' => 'Boolean', ], 'IsDefault' => [ 'shape' => 'Boolean', ], 'OwnershipVerificationStatus' => [ 'shape' => 'DnsRecordVerificationStatus', ], 'DkimVerificationStatus' => [ 'shape' => 'DnsRecordVerificationStatus', ], ], ], 'GetMailboxDetailsRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'UserId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'UserId' => [ 'shape' => 'EntityIdentifier', ], ], ], 'GetMailboxDetailsResponse' => [ 'type' => 'structure', 'members' => [ 'MailboxQuota' => [ 'shape' => 'MailboxQuota', ], 'MailboxSize' => [ 'shape' => 'MailboxSize', ], ], ], 'GetMobileDeviceAccessEffectRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'DeviceType' => [ 'shape' => 'DeviceType', ], 'DeviceModel' => [ 'shape' => 'DeviceModel', ], 'DeviceOperatingSystem' => [ 'shape' => 'DeviceOperatingSystem', ], 'DeviceUserAgent' => [ 'shape' => 'DeviceUserAgent', ], ], ], 'GetMobileDeviceAccessEffectResponse' => [ 'type' => 'structure', 'members' => [ 'Effect' => [ 'shape' => 'MobileDeviceAccessRuleEffect', ], 'MatchedRules' => [ 'shape' => 'MobileDeviceAccessMatchedRuleList', ], ], ], 'GetMobileDeviceAccessOverrideRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'UserId', 'DeviceId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'UserId' => [ 'shape' => 'EntityIdentifier', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], ], ], 'GetMobileDeviceAccessOverrideResponse' => [ 'type' => 'structure', 'members' => [ 'UserId' => [ 'shape' => 'WorkMailIdentifier', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], 'Effect' => [ 'shape' => 'MobileDeviceAccessRuleEffect', ], 'Description' => [ 'shape' => 'MobileDeviceAccessRuleDescription', ], 'DateCreated' => [ 'shape' => 'Timestamp', ], 'DateModified' => [ 'shape' => 'Timestamp', ], ], ], 'GetPersonalAccessTokenMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'PersonalAccessTokenId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'PersonalAccessTokenId' => [ 'shape' => 'PersonalAccessTokenId', ], ], ], 'GetPersonalAccessTokenMetadataResponse' => [ 'type' => 'structure', 'members' => [ 'PersonalAccessTokenId' => [ 'shape' => 'PersonalAccessTokenId', ], 'UserId' => [ 'shape' => 'WorkMailIdentifier', ], 'Name' => [ 'shape' => 'PersonalAccessTokenName', ], 'DateCreated' => [ 'shape' => 'Timestamp', ], 'DateLastUsed' => [ 'shape' => 'Timestamp', ], 'ExpiresTime' => [ 'shape' => 'Timestamp', ], 'Scopes' => [ 'shape' => 'PersonalAccessTokenScopeList', ], ], ], 'Group' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'WorkMailIdentifier', ], 'Email' => [ 'shape' => 'EmailAddress', ], 'Name' => [ 'shape' => 'GroupName', ], 'State' => [ 'shape' => 'EntityState', ], 'EnabledDate' => [ 'shape' => 'Timestamp', ], 'DisabledDate' => [ 'shape' => 'Timestamp', ], ], ], 'GroupIdentifier' => [ 'type' => 'structure', 'members' => [ 'GroupId' => [ 'shape' => 'WorkMailIdentifier', ], 'GroupName' => [ 'shape' => 'GroupName', ], ], ], 'GroupIdentifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupIdentifier', ], ], 'GroupName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\u0020-\\u00FF]+', ], 'Groups' => [ 'type' => 'list', 'member' => [ 'shape' => 'Group', ], ], 'HostedZoneId' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '[^/\\\\]*', ], 'IdempotencyClientToken' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\x21-\\x7e]+', ], 'IdentityCenterApplicationName' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^[\\w+=,.@-]+$', ], 'IdentityCenterConfiguration' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'ApplicationArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], ], ], 'IdentityProviderAuthenticationMode' => [ 'type' => 'string', 'enum' => [ 'IDENTITY_PROVIDER_ONLY', 'IDENTITY_PROVIDER_AND_DIRECTORY', ], ], 'IdentityProviderIdentityStoreId' => [ 'type' => 'string', 'max' => 36, 'min' => 1, 'pattern' => '^d-[0-9a-f]{10}$|^[0-9a-f]{8}\\\\b-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-\\\\b[0-9a-f]{12}$', ], 'IdentityProviderUserId' => [ 'type' => 'string', 'max' => 47, 'min' => 1, 'pattern' => '^([0-9a-f]{10}-|)[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}$', ], 'IdentityProviderUserIdForUpdate' => [ 'type' => 'string', 'max' => 47, 'min' => 0, 'pattern' => '^$|^([0-9a-f]{10}-|)[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}$', ], 'IdentityProviderUserIdPrefix' => [ 'type' => 'string', 'max' => 47, 'min' => 1, 'pattern' => '^[A-Fa-f0-9-]+$', ], 'ImpersonationMatchedRule' => [ 'type' => 'structure', 'members' => [ 'ImpersonationRuleId' => [ 'shape' => 'ImpersonationRuleId', ], 'Name' => [ 'shape' => 'ImpersonationRuleName', ], ], ], 'ImpersonationMatchedRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImpersonationMatchedRule', ], 'max' => 10, 'min' => 0, ], 'ImpersonationRole' => [ 'type' => 'structure', 'members' => [ 'ImpersonationRoleId' => [ 'shape' => 'ImpersonationRoleId', ], 'Name' => [ 'shape' => 'ImpersonationRoleName', ], 'Type' => [ 'shape' => 'ImpersonationRoleType', ], 'DateCreated' => [ 'shape' => 'Timestamp', ], 'DateModified' => [ 'shape' => 'Timestamp', ], ], ], 'ImpersonationRoleDescription' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[^\\x00-\\x09\\x0B\\x0C\\x0E-\\x1F\\x7F\\x3C\\x3E\\x5C]+', ], 'ImpersonationRoleId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'ImpersonationRoleIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImpersonationRoleId', ], 'max' => 10, 'min' => 0, ], 'ImpersonationRoleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImpersonationRole', ], 'max' => 20, 'min' => 0, ], 'ImpersonationRoleName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[^\\x00-\\x1F\\x7F\\x3C\\x3E\\x5C]+', ], 'ImpersonationRoleType' => [ 'type' => 'string', 'enum' => [ 'FULL_ACCESS', 'READ_ONLY', ], ], 'ImpersonationRule' => [ 'type' => 'structure', 'required' => [ 'ImpersonationRuleId', 'Effect', ], 'members' => [ 'ImpersonationRuleId' => [ 'shape' => 'ImpersonationRuleId', ], 'Name' => [ 'shape' => 'ImpersonationRuleName', ], 'Description' => [ 'shape' => 'ImpersonationRuleDescription', ], 'Effect' => [ 'shape' => 'AccessEffect', ], 'TargetUsers' => [ 'shape' => 'TargetUsers', ], 'NotTargetUsers' => [ 'shape' => 'TargetUsers', ], ], ], 'ImpersonationRuleDescription' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[^\\x00-\\x09\\x0B\\x0C\\x0E-\\x1F\\x7F\\x3C\\x3E\\x5C]+', ], 'ImpersonationRuleId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'ImpersonationRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImpersonationRule', ], 'max' => 10, 'min' => 0, ], 'ImpersonationRuleName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[^\\x00-\\x1F\\x7F\\x3C\\x3E\\x5C]+', ], 'ImpersonationToken' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\x21-\\x7e]+', ], 'InstanceArn' => [ 'type' => 'string', 'max' => 1124, 'min' => 10, 'pattern' => '^arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso:::instance/(sso)?ins-[a-zA-Z0-9-.]{16}$', ], 'InvalidConfigurationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidCustomSesConfigurationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidParameterException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidPasswordException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'IpAddress' => [ 'type' => 'string', 'max' => 15, 'min' => 1, 'pattern' => '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$', ], 'IpRange' => [ 'type' => 'string', 'max' => 18, 'min' => 1, 'pattern' => '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])/([0-9]|[12][0-9]|3[0-2])$', ], 'IpRangeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpRange', ], 'max' => 1024, 'min' => 0, ], 'Jobs' => [ 'type' => 'list', 'member' => [ 'shape' => 'MailboxExportJob', ], ], 'KmsKeyArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws:kms:[a-z0-9-]*:[a-z0-9-]+:[A-Za-z0-9][A-Za-z0-9:_/+=,@.-]{0,1023}', ], 'LambdaArn' => [ 'type' => 'string', 'max' => 256, 'min' => 49, 'pattern' => 'arn:aws:lambda:[a-z]{2}-[a-z]+-\\d{1}:\\d{12}:function:[a-zA-Z0-9\\-_\\.]+(:(\\$LATEST|[a-zA-Z0-9\\-_]+))?', ], 'LambdaAvailabilityProvider' => [ 'type' => 'structure', 'required' => [ 'LambdaArn', ], 'members' => [ 'LambdaArn' => [ 'shape' => 'LambdaArn', ], ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ListAccessControlRulesRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], ], ], 'ListAccessControlRulesResponse' => [ 'type' => 'structure', 'members' => [ 'Rules' => [ 'shape' => 'AccessControlRulesList', ], ], ], 'ListAliasesRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'EntityId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'EntityId' => [ 'shape' => 'WorkMailIdentifier', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListAliasesResponse' => [ 'type' => 'structure', 'members' => [ 'Aliases' => [ 'shape' => 'Aliases', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAvailabilityConfigurationsRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAvailabilityConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'AvailabilityConfigurations' => [ 'shape' => 'AvailabilityConfigurationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListGroupMembersRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'GroupId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'GroupId' => [ 'shape' => 'EntityIdentifier', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListGroupMembersResponse' => [ 'type' => 'structure', 'members' => [ 'Members' => [ 'shape' => 'Members', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListGroupsFilters' => [ 'type' => 'structure', 'members' => [ 'NamePrefix' => [ 'shape' => 'String', ], 'PrimaryEmailPrefix' => [ 'shape' => 'String', ], 'State' => [ 'shape' => 'EntityState', ], ], ], 'ListGroupsForEntityFilters' => [ 'type' => 'structure', 'members' => [ 'GroupNamePrefix' => [ 'shape' => 'String', ], ], ], 'ListGroupsForEntityRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'EntityId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'EntityId' => [ 'shape' => 'EntityIdentifier', ], 'Filters' => [ 'shape' => 'ListGroupsForEntityFilters', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListGroupsForEntityResponse' => [ 'type' => 'structure', 'members' => [ 'Groups' => [ 'shape' => 'GroupIdentifiers', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'ListGroupsFilters', ], ], ], 'ListGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'Groups' => [ 'shape' => 'Groups', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListImpersonationRolesRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListImpersonationRolesResponse' => [ 'type' => 'structure', 'members' => [ 'Roles' => [ 'shape' => 'ImpersonationRoleList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMailDomainsRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMailDomainsResponse' => [ 'type' => 'structure', 'members' => [ 'MailDomains' => [ 'shape' => 'MailDomains', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMailboxExportJobsRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListMailboxExportJobsResponse' => [ 'type' => 'structure', 'members' => [ 'Jobs' => [ 'shape' => 'Jobs', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMailboxPermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'EntityId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'EntityId' => [ 'shape' => 'EntityIdentifier', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListMailboxPermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'Permissions' => [ 'shape' => 'Permissions', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMobileDeviceAccessOverridesRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'UserId' => [ 'shape' => 'EntityIdentifier', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListMobileDeviceAccessOverridesResponse' => [ 'type' => 'structure', 'members' => [ 'Overrides' => [ 'shape' => 'MobileDeviceAccessOverridesList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMobileDeviceAccessRulesRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], ], ], 'ListMobileDeviceAccessRulesResponse' => [ 'type' => 'structure', 'members' => [ 'Rules' => [ 'shape' => 'MobileDeviceAccessRulesList', ], ], ], 'ListOrganizationsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListOrganizationsResponse' => [ 'type' => 'structure', 'members' => [ 'OrganizationSummaries' => [ 'shape' => 'OrganizationSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPersonalAccessTokensRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'UserId' => [ 'shape' => 'EntityIdentifier', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListPersonalAccessTokensResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'PersonalAccessTokenSummaries' => [ 'shape' => 'PersonalAccessTokenSummaryList', ], ], ], 'ListResourceDelegatesRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'ResourceId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'ResourceId' => [ 'shape' => 'EntityIdentifier', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListResourceDelegatesResponse' => [ 'type' => 'structure', 'members' => [ 'Delegates' => [ 'shape' => 'ResourceDelegates', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListResourcesFilters' => [ 'type' => 'structure', 'members' => [ 'NamePrefix' => [ 'shape' => 'String', ], 'PrimaryEmailPrefix' => [ 'shape' => 'String', ], 'State' => [ 'shape' => 'EntityState', ], ], ], 'ListResourcesRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'ListResourcesFilters', ], ], ], 'ListResourcesResponse' => [ 'type' => 'structure', 'members' => [ 'Resources' => [ 'shape' => 'Resources', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ListUsersFilters' => [ 'type' => 'structure', 'members' => [ 'UsernamePrefix' => [ 'shape' => 'String', ], 'DisplayNamePrefix' => [ 'shape' => 'UserAttribute', ], 'PrimaryEmailPrefix' => [ 'shape' => 'String', ], 'State' => [ 'shape' => 'EntityState', ], 'IdentityProviderUserIdPrefix' => [ 'shape' => 'IdentityProviderUserIdPrefix', ], ], ], 'ListUsersRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'ListUsersFilters', ], ], ], 'ListUsersResponse' => [ 'type' => 'structure', 'members' => [ 'Users' => [ 'shape' => 'Users', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'LogGroupArn' => [ 'type' => 'string', 'max' => 562, 'min' => 47, 'pattern' => 'arn:aws:logs:[a-z\\-0-9]*:[0-9]{12}:log-group:([\\.\\-_/#A-Za-z0-9]+):\\*$', ], 'MailDomainInUseException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'MailDomainNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'MailDomainStateException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'MailDomainSummary' => [ 'type' => 'structure', 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', ], 'DefaultDomain' => [ 'shape' => 'Boolean', ], ], ], 'MailDomains' => [ 'type' => 'list', 'member' => [ 'shape' => 'MailDomainSummary', ], ], 'MailboxExportErrorInfo' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\S\\s]*', ], 'MailboxExportJob' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'MailboxExportJobId', ], 'EntityId' => [ 'shape' => 'WorkMailIdentifier', ], 'Description' => [ 'shape' => 'Description', ], 'S3BucketName' => [ 'shape' => 'S3BucketName', ], 'S3Path' => [ 'shape' => 'S3ObjectKey', ], 'EstimatedProgress' => [ 'shape' => 'Percentage', ], 'State' => [ 'shape' => 'MailboxExportJobState', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], ], ], 'MailboxExportJobId' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '[A-Za-z0-9-]+', ], 'MailboxExportJobState' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', ], ], 'MailboxQuota' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'MailboxSize' => [ 'type' => 'double', 'min' => 0, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'Member' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'MemberType', ], 'State' => [ 'shape' => 'EntityState', ], 'EnabledDate' => [ 'shape' => 'Timestamp', ], 'DisabledDate' => [ 'shape' => 'Timestamp', ], ], ], 'MemberType' => [ 'type' => 'string', 'enum' => [ 'GROUP', 'USER', ], ], 'Members' => [ 'type' => 'list', 'member' => [ 'shape' => 'Member', ], ], 'MobileDeviceAccessMatchedRule' => [ 'type' => 'structure', 'members' => [ 'MobileDeviceAccessRuleId' => [ 'shape' => 'MobileDeviceAccessRuleId', ], 'Name' => [ 'shape' => 'MobileDeviceAccessRuleName', ], ], ], 'MobileDeviceAccessMatchedRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MobileDeviceAccessMatchedRule', ], 'max' => 10, 'min' => 0, ], 'MobileDeviceAccessOverride' => [ 'type' => 'structure', 'members' => [ 'UserId' => [ 'shape' => 'WorkMailIdentifier', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], 'Effect' => [ 'shape' => 'MobileDeviceAccessRuleEffect', ], 'Description' => [ 'shape' => 'MobileDeviceAccessRuleDescription', ], 'DateCreated' => [ 'shape' => 'Timestamp', ], 'DateModified' => [ 'shape' => 'Timestamp', ], ], ], 'MobileDeviceAccessOverridesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MobileDeviceAccessOverride', ], ], 'MobileDeviceAccessRule' => [ 'type' => 'structure', 'members' => [ 'MobileDeviceAccessRuleId' => [ 'shape' => 'MobileDeviceAccessRuleId', ], 'Name' => [ 'shape' => 'MobileDeviceAccessRuleName', ], 'Description' => [ 'shape' => 'MobileDeviceAccessRuleDescription', ], 'Effect' => [ 'shape' => 'MobileDeviceAccessRuleEffect', ], 'DeviceTypes' => [ 'shape' => 'DeviceTypeList', ], 'NotDeviceTypes' => [ 'shape' => 'DeviceTypeList', ], 'DeviceModels' => [ 'shape' => 'DeviceModelList', ], 'NotDeviceModels' => [ 'shape' => 'DeviceModelList', ], 'DeviceOperatingSystems' => [ 'shape' => 'DeviceOperatingSystemList', ], 'NotDeviceOperatingSystems' => [ 'shape' => 'DeviceOperatingSystemList', ], 'DeviceUserAgents' => [ 'shape' => 'DeviceUserAgentList', ], 'NotDeviceUserAgents' => [ 'shape' => 'DeviceUserAgentList', ], 'DateCreated' => [ 'shape' => 'Timestamp', ], 'DateModified' => [ 'shape' => 'Timestamp', ], ], ], 'MobileDeviceAccessRuleDescription' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\S\\s]+', ], 'MobileDeviceAccessRuleEffect' => [ 'type' => 'string', 'enum' => [ 'ALLOW', 'DENY', ], ], 'MobileDeviceAccessRuleId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'MobileDeviceAccessRuleName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[\\S\\s]+', ], 'MobileDeviceAccessRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MobileDeviceAccessRule', ], 'max' => 10, 'min' => 0, ], 'NameAvailabilityException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'NewResourceDescription' => [ 'type' => 'string', 'max' => 64, 'min' => 0, 'sensitive' => true, ], 'NextToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\S\\s]*|[a-zA-Z0-9/+=]{1,1024}', ], 'OrganizationId' => [ 'type' => 'string', 'max' => 34, 'min' => 34, 'pattern' => '^m-[0-9a-f]{32}$', ], 'OrganizationName' => [ 'type' => 'string', 'max' => 62, 'min' => 1, 'pattern' => '^(?!d-)([\\da-zA-Z]+)([-][\\da-zA-Z]+)*', ], 'OrganizationNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'OrganizationStateException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'OrganizationSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrganizationSummary', ], ], 'OrganizationSummary' => [ 'type' => 'structure', 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'Alias' => [ 'shape' => 'OrganizationName', ], 'DefaultMailDomain' => [ 'shape' => 'DomainName', ], 'ErrorMessage' => [ 'shape' => 'String', ], 'State' => [ 'shape' => 'String', ], ], ], 'Password' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[\\u0020-\\u00FF]+', 'sensitive' => true, ], 'Percentage' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'Permission' => [ 'type' => 'structure', 'required' => [ 'GranteeId', 'GranteeType', 'PermissionValues', ], 'members' => [ 'GranteeId' => [ 'shape' => 'WorkMailIdentifier', ], 'GranteeType' => [ 'shape' => 'MemberType', ], 'PermissionValues' => [ 'shape' => 'PermissionValues', ], ], ], 'PermissionType' => [ 'type' => 'string', 'enum' => [ 'FULL_ACCESS', 'SEND_AS', 'SEND_ON_BEHALF', ], ], 'PermissionValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'PermissionType', ], ], 'Permissions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Permission', ], ], 'PersonalAccessTokenConfiguration' => [ 'type' => 'structure', 'required' => [ 'Status', ], 'members' => [ 'Status' => [ 'shape' => 'PersonalAccessTokenConfigurationStatus', ], 'LifetimeInDays' => [ 'shape' => 'PersonalAccessTokenLifetimeInDays', ], ], ], 'PersonalAccessTokenConfigurationStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'PersonalAccessTokenId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'PersonalAccessTokenLifetimeInDays' => [ 'type' => 'integer', 'box' => true, 'max' => 3653, 'min' => 1, ], 'PersonalAccessTokenName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[^\\x00-\\x1F\\x7F\\x3C\\x3E\\x5C]+', ], 'PersonalAccessTokenScope' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[^\\x00-\\x1F\\x7F\\x3C\\x3E\\x5C]+', ], 'PersonalAccessTokenScopeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PersonalAccessTokenScope', ], 'max' => 10, 'min' => 1, ], 'PersonalAccessTokenSummary' => [ 'type' => 'structure', 'members' => [ 'PersonalAccessTokenId' => [ 'shape' => 'PersonalAccessTokenId', ], 'UserId' => [ 'shape' => 'WorkMailIdentifier', ], 'Name' => [ 'shape' => 'PersonalAccessTokenName', ], 'DateCreated' => [ 'shape' => 'Timestamp', ], 'DateLastUsed' => [ 'shape' => 'Timestamp', ], 'ExpiresTime' => [ 'shape' => 'Timestamp', ], 'Scopes' => [ 'shape' => 'PersonalAccessTokenScopeList', ], ], ], 'PersonalAccessTokenSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PersonalAccessTokenSummary', ], ], 'PolicyDescription' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[\\w\\d\\s\\S\\-!?=,.;:\'_]+', 'sensitive' => true, ], 'PutAccessControlRuleRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Effect', 'Description', 'OrganizationId', ], 'members' => [ 'Name' => [ 'shape' => 'AccessControlRuleName', ], 'Effect' => [ 'shape' => 'AccessControlRuleEffect', ], 'Description' => [ 'shape' => 'AccessControlRuleDescription', ], 'IpRanges' => [ 'shape' => 'IpRangeList', ], 'NotIpRanges' => [ 'shape' => 'IpRangeList', ], 'Actions' => [ 'shape' => 'ActionsList', ], 'NotActions' => [ 'shape' => 'ActionsList', ], 'UserIds' => [ 'shape' => 'UserIdList', ], 'NotUserIds' => [ 'shape' => 'UserIdList', ], 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'ImpersonationRoleIds' => [ 'shape' => 'ImpersonationRoleIdList', ], 'NotImpersonationRoleIds' => [ 'shape' => 'ImpersonationRoleIdList', ], ], ], 'PutAccessControlRuleResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutEmailMonitoringConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'RoleArn', 'LogGroupArn', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'LogGroupArn' => [ 'shape' => 'LogGroupArn', ], ], ], 'PutEmailMonitoringConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutIdentityProviderConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'AuthenticationMode', 'IdentityCenterConfiguration', 'PersonalAccessTokenConfiguration', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'AuthenticationMode' => [ 'shape' => 'IdentityProviderAuthenticationMode', ], 'IdentityCenterConfiguration' => [ 'shape' => 'IdentityCenterConfiguration', ], 'PersonalAccessTokenConfiguration' => [ 'shape' => 'PersonalAccessTokenConfiguration', ], ], ], 'PutIdentityProviderConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutInboundDmarcSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'Enforced', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'Enforced' => [ 'shape' => 'BooleanObject', ], ], ], 'PutInboundDmarcSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutMailboxPermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'EntityId', 'GranteeId', 'PermissionValues', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'EntityId' => [ 'shape' => 'EntityIdentifier', ], 'GranteeId' => [ 'shape' => 'EntityIdentifier', ], 'PermissionValues' => [ 'shape' => 'PermissionValues', ], ], ], 'PutMailboxPermissionsResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutMobileDeviceAccessOverrideRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'UserId', 'DeviceId', 'Effect', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'UserId' => [ 'shape' => 'EntityIdentifier', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], 'Effect' => [ 'shape' => 'MobileDeviceAccessRuleEffect', ], 'Description' => [ 'shape' => 'MobileDeviceAccessRuleDescription', ], ], ], 'PutMobileDeviceAccessOverrideResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutRetentionPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'Name', 'FolderConfigurations', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'Id' => [ 'shape' => 'ShortString', ], 'Name' => [ 'shape' => 'ShortString', ], 'Description' => [ 'shape' => 'PolicyDescription', ], 'FolderConfigurations' => [ 'shape' => 'FolderConfigurations', ], ], ], 'PutRetentionPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'RedactedEwsAvailabilityProvider' => [ 'type' => 'structure', 'members' => [ 'EwsEndpoint' => [ 'shape' => 'Url', ], 'EwsUsername' => [ 'shape' => 'ExternalUserName', ], ], ], 'RegisterMailDomainRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'DomainName', ], 'members' => [ 'ClientToken' => [ 'shape' => 'IdempotencyClientToken', 'idempotencyToken' => true, ], 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'DomainName' => [ 'shape' => 'WorkMailDomainName', ], ], ], 'RegisterMailDomainResponse' => [ 'type' => 'structure', 'members' => [], ], 'RegisterToWorkMailRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'EntityId', 'Email', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'EntityId' => [ 'shape' => 'EntityIdentifier', ], 'Email' => [ 'shape' => 'EmailAddress', ], ], ], 'RegisterToWorkMailResponse' => [ 'type' => 'structure', 'members' => [], ], 'ReservedNameException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResetPasswordRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'UserId', 'Password', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'UserId' => [ 'shape' => 'WorkMailIdentifier', ], 'Password' => [ 'shape' => 'Password', ], ], ], 'ResetPasswordResponse' => [ 'type' => 'structure', 'members' => [], ], 'Resource' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'WorkMailIdentifier', ], 'Email' => [ 'shape' => 'EmailAddress', ], 'Name' => [ 'shape' => 'ResourceName', ], 'Type' => [ 'shape' => 'ResourceType', ], 'State' => [ 'shape' => 'EntityState', ], 'EnabledDate' => [ 'shape' => 'Timestamp', ], 'DisabledDate' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], ], ], 'ResourceDelegates' => [ 'type' => 'list', 'member' => [ 'shape' => 'Delegate', ], ], 'ResourceDescription' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'sensitive' => true, ], 'ResourceId' => [ 'type' => 'string', 'max' => 34, 'min' => 34, 'pattern' => '^r-[0-9a-f]{32}$', ], 'ResourceName' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '[\\w\\-.]+(@[a-zA-Z0-9.\\-]+\\.[a-zA-Z0-9-]{2,})?', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'ROOM', 'EQUIPMENT', ], ], 'Resources' => [ 'type' => 'list', 'member' => [ 'shape' => 'Resource', ], ], 'RetentionAction' => [ 'type' => 'string', 'enum' => [ 'NONE', 'DELETE', 'PERMANENTLY_DELETE', ], ], 'RetentionPeriod' => [ 'type' => 'integer', 'box' => true, 'max' => 730, 'min' => 1, ], 'RoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws:iam:[a-z0-9-]*:[a-z0-9-]+:[A-Za-z0-9][A-Za-z0-9:_/+=,@.-]{0,1023}', ], 'S3BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '[A-Za-z0-9.-]+', ], 'S3ObjectKey' => [ 'type' => 'string', 'max' => 1023, 'min' => 1, 'pattern' => '[A-Za-z0-9!_.*\'()/-]+', ], 'ShortString' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'StartMailboxExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'ClientToken', 'OrganizationId', 'EntityId', 'RoleArn', 'KmsKeyArn', 'S3BucketName', 'S3Prefix', ], 'members' => [ 'ClientToken' => [ 'shape' => 'IdempotencyClientToken', 'idempotencyToken' => true, ], 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'EntityId' => [ 'shape' => 'EntityIdentifier', ], 'Description' => [ 'shape' => 'Description', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'KmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'S3BucketName' => [ 'shape' => 'S3BucketName', ], 'S3Prefix' => [ 'shape' => 'S3ObjectKey', ], ], ], 'StartMailboxExportJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'MailboxExportJobId', ], ], ], 'String' => [ 'type' => 'string', 'max' => 256, ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'Tags', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TargetUsers' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntityIdentifier', ], 'max' => 10, 'min' => 1, ], 'TestAvailabilityConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'DomainName' => [ 'shape' => 'DomainName', ], 'EwsProvider' => [ 'shape' => 'EwsAvailabilityProvider', ], 'LambdaProvider' => [ 'shape' => 'LambdaAvailabilityProvider', ], ], ], 'TestAvailabilityConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'TestPassed' => [ 'shape' => 'Boolean', ], 'FailureReason' => [ 'shape' => 'String', ], ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'UnsupportedOperationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'TagKeys', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAvailabilityConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'DomainName', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'DomainName' => [ 'shape' => 'DomainName', ], 'EwsProvider' => [ 'shape' => 'EwsAvailabilityProvider', ], 'LambdaProvider' => [ 'shape' => 'LambdaAvailabilityProvider', ], ], ], 'UpdateAvailabilityConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDefaultMailDomainRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'DomainName', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'DomainName' => [ 'shape' => 'WorkMailDomainName', ], ], ], 'UpdateDefaultMailDomainResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateGroupRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'GroupId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'GroupId' => [ 'shape' => 'EntityIdentifier', ], 'HiddenFromGlobalAddressList' => [ 'shape' => 'BooleanObject', ], ], ], 'UpdateGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateImpersonationRoleRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'ImpersonationRoleId', 'Name', 'Type', 'Rules', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'ImpersonationRoleId' => [ 'shape' => 'ImpersonationRoleId', ], 'Name' => [ 'shape' => 'ImpersonationRoleName', ], 'Type' => [ 'shape' => 'ImpersonationRoleType', ], 'Description' => [ 'shape' => 'ImpersonationRoleDescription', ], 'Rules' => [ 'shape' => 'ImpersonationRuleList', ], ], ], 'UpdateImpersonationRoleResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateMailboxQuotaRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'UserId', 'MailboxQuota', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'UserId' => [ 'shape' => 'EntityIdentifier', ], 'MailboxQuota' => [ 'shape' => 'MailboxQuota', ], ], ], 'UpdateMailboxQuotaResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateMobileDeviceAccessRuleRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'MobileDeviceAccessRuleId', 'Name', 'Effect', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'MobileDeviceAccessRuleId' => [ 'shape' => 'MobileDeviceAccessRuleId', ], 'Name' => [ 'shape' => 'MobileDeviceAccessRuleName', ], 'Description' => [ 'shape' => 'MobileDeviceAccessRuleDescription', ], 'Effect' => [ 'shape' => 'MobileDeviceAccessRuleEffect', ], 'DeviceTypes' => [ 'shape' => 'DeviceTypeList', ], 'NotDeviceTypes' => [ 'shape' => 'DeviceTypeList', ], 'DeviceModels' => [ 'shape' => 'DeviceModelList', ], 'NotDeviceModels' => [ 'shape' => 'DeviceModelList', ], 'DeviceOperatingSystems' => [ 'shape' => 'DeviceOperatingSystemList', ], 'NotDeviceOperatingSystems' => [ 'shape' => 'DeviceOperatingSystemList', ], 'DeviceUserAgents' => [ 'shape' => 'DeviceUserAgentList', ], 'NotDeviceUserAgents' => [ 'shape' => 'DeviceUserAgentList', ], ], ], 'UpdateMobileDeviceAccessRuleResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdatePrimaryEmailAddressRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'EntityId', 'Email', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'EntityId' => [ 'shape' => 'EntityIdentifier', ], 'Email' => [ 'shape' => 'EmailAddress', ], ], ], 'UpdatePrimaryEmailAddressResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateResourceRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'ResourceId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'ResourceId' => [ 'shape' => 'EntityIdentifier', ], 'Name' => [ 'shape' => 'ResourceName', ], 'BookingOptions' => [ 'shape' => 'BookingOptions', ], 'Description' => [ 'shape' => 'NewResourceDescription', ], 'Type' => [ 'shape' => 'ResourceType', ], 'HiddenFromGlobalAddressList' => [ 'shape' => 'BooleanObject', ], ], ], 'UpdateResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateUserRequest' => [ 'type' => 'structure', 'required' => [ 'OrganizationId', 'UserId', ], 'members' => [ 'OrganizationId' => [ 'shape' => 'OrganizationId', ], 'UserId' => [ 'shape' => 'EntityIdentifier', ], 'Role' => [ 'shape' => 'UserRole', ], 'DisplayName' => [ 'shape' => 'UserAttribute', ], 'FirstName' => [ 'shape' => 'UserAttribute', ], 'LastName' => [ 'shape' => 'UserAttribute', ], 'HiddenFromGlobalAddressList' => [ 'shape' => 'BooleanObject', ], 'Initials' => [ 'shape' => 'UserAttribute', ], 'Telephone' => [ 'shape' => 'UserAttribute', ], 'Street' => [ 'shape' => 'UserAttribute', ], 'JobTitle' => [ 'shape' => 'UserAttribute', ], 'City' => [ 'shape' => 'UserAttribute', ], 'Company' => [ 'shape' => 'UserAttribute', ], 'ZipCode' => [ 'shape' => 'UserAttribute', ], 'Department' => [ 'shape' => 'UserAttribute', ], 'Country' => [ 'shape' => 'UserAttribute', ], 'Office' => [ 'shape' => 'UserAttribute', ], 'IdentityProviderUserId' => [ 'shape' => 'IdentityProviderUserIdForUpdate', ], ], ], 'UpdateUserResponse' => [ 'type' => 'structure', 'members' => [], ], 'Url' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'https?://[A-Za-z0-9.-]+(:[0-9]+)?/.*', ], 'User' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'WorkMailIdentifier', ], 'Email' => [ 'shape' => 'EmailAddress', ], 'Name' => [ 'shape' => 'UserName', ], 'DisplayName' => [ 'shape' => 'String', ], 'State' => [ 'shape' => 'EntityState', ], 'UserRole' => [ 'shape' => 'UserRole', ], 'EnabledDate' => [ 'shape' => 'Timestamp', ], 'DisabledDate' => [ 'shape' => 'Timestamp', ], 'IdentityProviderUserId' => [ 'shape' => 'IdentityProviderUserId', ], 'IdentityProviderIdentityStoreId' => [ 'shape' => 'IdentityProviderIdentityStoreId', ], ], ], 'UserAttribute' => [ 'type' => 'string', 'max' => 256, 'sensitive' => true, ], 'UserIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkMailIdentifier', ], 'max' => 10, 'min' => 0, ], 'UserName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[\\w\\-.]+(@[a-zA-Z0-9.\\-]+\\.[a-zA-Z0-9-]{2,})?', ], 'UserRole' => [ 'type' => 'string', 'enum' => [ 'USER', 'RESOURCE', 'SYSTEM_USER', 'REMOTE_USER', ], ], 'Users' => [ 'type' => 'list', 'member' => [ 'shape' => 'User', ], ], 'WorkMailDomainName' => [ 'type' => 'string', 'max' => 209, 'min' => 3, 'pattern' => '[a-zA-Z0-9.-]+', ], 'WorkMailIdentifier' => [ 'type' => 'string', 'max' => 256, 'min' => 12, ], ],];
