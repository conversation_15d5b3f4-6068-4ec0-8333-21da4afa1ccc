<?php
// This file was auto-generated from sdk-root/src/data/events/2015-10-07/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2015-10-07', 'endpointPrefix' => 'events', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'Amazon CloudWatch Events', 'serviceId' => 'CloudWatch Events', 'signatureVersion' => 'v4', 'targetPrefix' => 'AWSEvents', 'uid' => 'events-2015-10-07', ], 'operations' => [ 'ActivateEventSource' => [ 'name' => 'ActivateEventSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ActivateEventSourceRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidStateException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'OperationDisabledException', ], ], ], 'CancelReplay' => [ 'name' => 'CancelReplay', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelReplayRequest', ], 'output' => [ 'shape' => 'CancelReplayResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'IllegalStatusException', ], [ 'shape' => 'InternalException', ], ], ], 'CreateApiDestination' => [ 'name' => 'CreateApiDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateApiDestinationRequest', ], 'output' => [ 'shape' => 'CreateApiDestinationResponse', ], 'errors' => [ [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalException', ], ], ], 'CreateArchive' => [ 'name' => 'CreateArchive', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateArchiveRequest', ], 'output' => [ 'shape' => 'CreateArchiveResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidEventPatternException', ], ], ], 'CreateConnection' => [ 'name' => 'CreateConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateConnectionRequest', ], 'output' => [ 'shape' => 'CreateConnectionResponse', ], 'errors' => [ [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalException', ], ], ], 'CreateEventBus' => [ 'name' => 'CreateEventBus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEventBusRequest', ], 'output' => [ 'shape' => 'CreateEventBusResponse', ], 'errors' => [ [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidStateException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'OperationDisabledException', ], ], ], 'CreatePartnerEventSource' => [ 'name' => 'CreatePartnerEventSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePartnerEventSourceRequest', ], 'output' => [ 'shape' => 'CreatePartnerEventSourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'OperationDisabledException', ], ], ], 'DeactivateEventSource' => [ 'name' => 'DeactivateEventSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeactivateEventSourceRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidStateException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'OperationDisabledException', ], ], ], 'DeauthorizeConnection' => [ 'name' => 'DeauthorizeConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeauthorizeConnectionRequest', ], 'output' => [ 'shape' => 'DeauthorizeConnectionResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], ], ], 'DeleteApiDestination' => [ 'name' => 'DeleteApiDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteApiDestinationRequest', ], 'output' => [ 'shape' => 'DeleteApiDestinationResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], ], ], 'DeleteArchive' => [ 'name' => 'DeleteArchive', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteArchiveRequest', ], 'output' => [ 'shape' => 'DeleteArchiveResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], ], ], 'DeleteConnection' => [ 'name' => 'DeleteConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteConnectionRequest', ], 'output' => [ 'shape' => 'DeleteConnectionResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], ], ], 'DeleteEventBus' => [ 'name' => 'DeleteEventBus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEventBusRequest', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeletePartnerEventSource' => [ 'name' => 'DeletePartnerEventSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePartnerEventSourceRequest', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'OperationDisabledException', ], ], ], 'DeleteRule' => [ 'name' => 'DeleteRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRuleRequest', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ManagedRuleException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeApiDestination' => [ 'name' => 'DescribeApiDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeApiDestinationRequest', ], 'output' => [ 'shape' => 'DescribeApiDestinationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], ], ], 'DescribeArchive' => [ 'name' => 'DescribeArchive', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeArchiveRequest', ], 'output' => [ 'shape' => 'DescribeArchiveResponse', ], 'errors' => [ [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], ], ], 'DescribeConnection' => [ 'name' => 'DescribeConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeConnectionRequest', ], 'output' => [ 'shape' => 'DescribeConnectionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], ], ], 'DescribeEventBus' => [ 'name' => 'DescribeEventBus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEventBusRequest', ], 'output' => [ 'shape' => 'DescribeEventBusResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], ], ], 'DescribeEventSource' => [ 'name' => 'DescribeEventSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEventSourceRequest', ], 'output' => [ 'shape' => 'DescribeEventSourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'OperationDisabledException', ], ], ], 'DescribePartnerEventSource' => [ 'name' => 'DescribePartnerEventSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePartnerEventSourceRequest', ], 'output' => [ 'shape' => 'DescribePartnerEventSourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'OperationDisabledException', ], ], ], 'DescribeReplay' => [ 'name' => 'DescribeReplay', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReplayRequest', ], 'output' => [ 'shape' => 'DescribeReplayResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], ], ], 'DescribeRule' => [ 'name' => 'DescribeRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRuleRequest', ], 'output' => [ 'shape' => 'DescribeRuleResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], ], ], 'DisableRule' => [ 'name' => 'DisableRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisableRuleRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ManagedRuleException', ], [ 'shape' => 'InternalException', ], ], ], 'EnableRule' => [ 'name' => 'EnableRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EnableRuleRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ManagedRuleException', ], [ 'shape' => 'InternalException', ], ], ], 'ListApiDestinations' => [ 'name' => 'ListApiDestinations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListApiDestinationsRequest', ], 'output' => [ 'shape' => 'ListApiDestinationsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], ], ], 'ListArchives' => [ 'name' => 'ListArchives', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListArchivesRequest', ], 'output' => [ 'shape' => 'ListArchivesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], ], ], 'ListConnections' => [ 'name' => 'ListConnections', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListConnectionsRequest', ], 'output' => [ 'shape' => 'ListConnectionsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], ], ], 'ListEventBuses' => [ 'name' => 'ListEventBuses', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEventBusesRequest', ], 'output' => [ 'shape' => 'ListEventBusesResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], ], ], 'ListEventSources' => [ 'name' => 'ListEventSources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEventSourcesRequest', ], 'output' => [ 'shape' => 'ListEventSourcesResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'OperationDisabledException', ], ], ], 'ListPartnerEventSourceAccounts' => [ 'name' => 'ListPartnerEventSourceAccounts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPartnerEventSourceAccountsRequest', ], 'output' => [ 'shape' => 'ListPartnerEventSourceAccountsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'OperationDisabledException', ], ], ], 'ListPartnerEventSources' => [ 'name' => 'ListPartnerEventSources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPartnerEventSourcesRequest', ], 'output' => [ 'shape' => 'ListPartnerEventSourcesResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'OperationDisabledException', ], ], ], 'ListReplays' => [ 'name' => 'ListReplays', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListReplaysRequest', ], 'output' => [ 'shape' => 'ListReplaysResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], ], ], 'ListRuleNamesByTarget' => [ 'name' => 'ListRuleNamesByTarget', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRuleNamesByTargetRequest', ], 'output' => [ 'shape' => 'ListRuleNamesByTargetResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListRules' => [ 'name' => 'ListRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRulesRequest', ], 'output' => [ 'shape' => 'ListRulesResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], ], ], 'ListTargetsByRule' => [ 'name' => 'ListTargetsByRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTargetsByRuleRequest', ], 'output' => [ 'shape' => 'ListTargetsByRuleResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], ], ], 'PutEvents' => [ 'name' => 'PutEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutEventsRequest', ], 'output' => [ 'shape' => 'PutEventsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], ], ], 'PutPartnerEvents' => [ 'name' => 'PutPartnerEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutPartnerEventsRequest', ], 'output' => [ 'shape' => 'PutPartnerEventsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'OperationDisabledException', ], ], ], 'PutPermission' => [ 'name' => 'PutPermission', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutPermissionRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'PolicyLengthExceededException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'OperationDisabledException', ], ], ], 'PutRule' => [ 'name' => 'PutRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutRuleRequest', ], 'output' => [ 'shape' => 'PutRuleResponse', ], 'errors' => [ [ 'shape' => 'InvalidEventPatternException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ManagedRuleException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'PutTargets' => [ 'name' => 'PutTargets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutTargetsRequest', ], 'output' => [ 'shape' => 'PutTargetsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ManagedRuleException', ], [ 'shape' => 'InternalException', ], ], ], 'RemovePermission' => [ 'name' => 'RemovePermission', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemovePermissionRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'OperationDisabledException', ], ], ], 'RemoveTargets' => [ 'name' => 'RemoveTargets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveTargetsRequest', ], 'output' => [ 'shape' => 'RemoveTargetsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ManagedRuleException', ], [ 'shape' => 'InternalException', ], ], ], 'StartReplay' => [ 'name' => 'StartReplay', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartReplayRequest', ], 'output' => [ 'shape' => 'StartReplayResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'InvalidEventPatternException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ManagedRuleException', ], ], ], 'TestEventPattern' => [ 'name' => 'TestEventPattern', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TestEventPatternRequest', ], 'output' => [ 'shape' => 'TestEventPatternResponse', ], 'errors' => [ [ 'shape' => 'InvalidEventPatternException', ], [ 'shape' => 'InternalException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ManagedRuleException', ], ], ], 'UpdateApiDestination' => [ 'name' => 'UpdateApiDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateApiDestinationRequest', ], 'output' => [ 'shape' => 'UpdateApiDestinationResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'UpdateArchive' => [ 'name' => 'UpdateArchive', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateArchiveRequest', ], 'output' => [ 'shape' => 'UpdateArchiveResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidEventPatternException', ], ], ], 'UpdateConnection' => [ 'name' => 'UpdateConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateConnectionRequest', ], 'output' => [ 'shape' => 'UpdateConnectionResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], ], ], ], 'shapes' => [ 'AccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '\\d{12}', ], 'Action' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => 'events:[a-zA-Z]+', ], 'ActivateEventSourceRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'EventSourceName', ], ], ], 'ApiDestination' => [ 'type' => 'structure', 'members' => [ 'ApiDestinationArn' => [ 'shape' => 'ApiDestinationArn', ], 'Name' => [ 'shape' => 'ApiDestinationName', ], 'ApiDestinationState' => [ 'shape' => 'ApiDestinationState', ], 'ConnectionArn' => [ 'shape' => 'ConnectionArn', ], 'InvocationEndpoint' => [ 'shape' => 'HttpsEndpoint', ], 'HttpMethod' => [ 'shape' => 'ApiDestinationHttpMethod', ], 'InvocationRateLimitPerSecond' => [ 'shape' => 'ApiDestinationInvocationRateLimitPerSecond', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'ApiDestinationArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '^arn:aws([a-z]|\\-)*:events:([a-z]|\\d|\\-)*:([0-9]{12})?:api-destination\\/[\\.\\-_A-Za-z0-9]+\\/[\\-A-Za-z0-9]+$', ], 'ApiDestinationDescription' => [ 'type' => 'string', 'max' => 512, 'pattern' => '.*', ], 'ApiDestinationHttpMethod' => [ 'type' => 'string', 'enum' => [ 'POST', 'GET', 'HEAD', 'OPTIONS', 'PUT', 'PATCH', 'DELETE', ], ], 'ApiDestinationInvocationRateLimitPerSecond' => [ 'type' => 'integer', 'min' => 1, ], 'ApiDestinationName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[\\.\\-_A-Za-z0-9]+', ], 'ApiDestinationResponseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApiDestination', ], ], 'ApiDestinationState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'Archive' => [ 'type' => 'structure', 'members' => [ 'ArchiveName' => [ 'shape' => 'ArchiveName', ], 'EventSourceArn' => [ 'shape' => 'Arn', ], 'State' => [ 'shape' => 'ArchiveState', ], 'StateReason' => [ 'shape' => 'ArchiveStateReason', ], 'RetentionDays' => [ 'shape' => 'RetentionDays', ], 'SizeBytes' => [ 'shape' => 'Long', ], 'EventCount' => [ 'shape' => 'Long', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], ], ], 'ArchiveArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '^arn:aws([a-z]|\\-)*:events:([a-z]|\\d|\\-)*:([0-9]{12})?:.+\\/.+$', ], 'ArchiveDescription' => [ 'type' => 'string', 'max' => 512, 'pattern' => '.*', ], 'ArchiveName' => [ 'type' => 'string', 'max' => 48, 'min' => 1, 'pattern' => '[\\.\\-_A-Za-z0-9]+', ], 'ArchiveResponseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Archive', ], ], 'ArchiveState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', 'CREATING', 'UPDATING', 'CREATE_FAILED', 'UPDATE_FAILED', ], ], 'ArchiveStateReason' => [ 'type' => 'string', 'max' => 512, 'pattern' => '.*', ], 'Arn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, ], 'AssignPublicIp' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'AuthHeaderParameters' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '^[ \\t]*[^\\x00-\\x1F:\\x7F]+([ \\t]+[^\\x00-\\x1F:\\x7F]+)*[ \\t]*$', ], 'AuthHeaderParametersSensitive' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '^[ \\t]*[^\\x00-\\x1F:\\x7F]+([ \\t]+[^\\x00-\\x1F:\\x7F]+)*[ \\t]*$', 'sensitive' => true, ], 'AwsVpcConfiguration' => [ 'type' => 'structure', 'required' => [ 'Subnets', ], 'members' => [ 'Subnets' => [ 'shape' => 'StringList', ], 'SecurityGroups' => [ 'shape' => 'StringList', ], 'AssignPublicIp' => [ 'shape' => 'AssignPublicIp', ], ], ], 'BatchArrayProperties' => [ 'type' => 'structure', 'members' => [ 'Size' => [ 'shape' => 'Integer', ], ], ], 'BatchParameters' => [ 'type' => 'structure', 'required' => [ 'JobDefinition', 'JobName', ], 'members' => [ 'JobDefinition' => [ 'shape' => 'String', ], 'JobName' => [ 'shape' => 'String', ], 'ArrayProperties' => [ 'shape' => 'BatchArrayProperties', ], 'RetryStrategy' => [ 'shape' => 'BatchRetryStrategy', ], ], ], 'BatchRetryStrategy' => [ 'type' => 'structure', 'members' => [ 'Attempts' => [ 'shape' => 'Integer', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'CancelReplayRequest' => [ 'type' => 'structure', 'required' => [ 'ReplayName', ], 'members' => [ 'ReplayName' => [ 'shape' => 'ReplayName', ], ], ], 'CancelReplayResponse' => [ 'type' => 'structure', 'members' => [ 'ReplayArn' => [ 'shape' => 'ReplayArn', ], 'State' => [ 'shape' => 'ReplayState', ], 'StateReason' => [ 'shape' => 'ReplayStateReason', ], ], ], 'CapacityProvider' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'CapacityProviderStrategy' => [ 'type' => 'list', 'member' => [ 'shape' => 'CapacityProviderStrategyItem', ], 'max' => 6, ], 'CapacityProviderStrategyItem' => [ 'type' => 'structure', 'required' => [ 'capacityProvider', ], 'members' => [ 'capacityProvider' => [ 'shape' => 'CapacityProvider', ], 'weight' => [ 'shape' => 'CapacityProviderStrategyItemWeight', ], 'base' => [ 'shape' => 'CapacityProviderStrategyItemBase', ], ], ], 'CapacityProviderStrategyItemBase' => [ 'type' => 'integer', 'max' => 100000, 'min' => 0, ], 'CapacityProviderStrategyItemWeight' => [ 'type' => 'integer', 'max' => 1000, 'min' => 0, ], 'ConcurrentModificationException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Condition' => [ 'type' => 'structure', 'required' => [ 'Type', 'Key', 'Value', ], 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Key' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'Connection' => [ 'type' => 'structure', 'members' => [ 'ConnectionArn' => [ 'shape' => 'ConnectionArn', ], 'Name' => [ 'shape' => 'ConnectionName', ], 'ConnectionState' => [ 'shape' => 'ConnectionState', ], 'StateReason' => [ 'shape' => 'ConnectionStateReason', ], 'AuthorizationType' => [ 'shape' => 'ConnectionAuthorizationType', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastAuthorizedTime' => [ 'shape' => 'Timestamp', ], ], ], 'ConnectionApiKeyAuthResponseParameters' => [ 'type' => 'structure', 'members' => [ 'ApiKeyName' => [ 'shape' => 'AuthHeaderParameters', ], ], ], 'ConnectionArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '^arn:aws([a-z]|\\-)*:events:([a-z]|\\d|\\-)*:([0-9]{12})?:connection\\/[\\.\\-_A-Za-z0-9]+\\/[\\-A-Za-z0-9]+$', ], 'ConnectionAuthResponseParameters' => [ 'type' => 'structure', 'members' => [ 'BasicAuthParameters' => [ 'shape' => 'ConnectionBasicAuthResponseParameters', ], 'OAuthParameters' => [ 'shape' => 'ConnectionOAuthResponseParameters', ], 'ApiKeyAuthParameters' => [ 'shape' => 'ConnectionApiKeyAuthResponseParameters', ], 'InvocationHttpParameters' => [ 'shape' => 'ConnectionHttpParameters', ], ], ], 'ConnectionAuthorizationType' => [ 'type' => 'string', 'enum' => [ 'BASIC', 'OAUTH_CLIENT_CREDENTIALS', 'API_KEY', ], ], 'ConnectionBasicAuthResponseParameters' => [ 'type' => 'structure', 'members' => [ 'Username' => [ 'shape' => 'AuthHeaderParameters', ], ], ], 'ConnectionBodyParameter' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'SensitiveString', ], 'IsValueSecret' => [ 'shape' => 'Boolean', ], ], ], 'ConnectionBodyParametersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectionBodyParameter', ], 'max' => 100, 'min' => 0, ], 'ConnectionDescription' => [ 'type' => 'string', 'max' => 512, 'pattern' => '.*', ], 'ConnectionHeaderParameter' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'HeaderKey', ], 'Value' => [ 'shape' => 'HeaderValueSensitive', ], 'IsValueSecret' => [ 'shape' => 'Boolean', ], ], ], 'ConnectionHeaderParametersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectionHeaderParameter', ], 'max' => 100, 'min' => 0, ], 'ConnectionHttpParameters' => [ 'type' => 'structure', 'members' => [ 'HeaderParameters' => [ 'shape' => 'ConnectionHeaderParametersList', ], 'QueryStringParameters' => [ 'shape' => 'ConnectionQueryStringParametersList', ], 'BodyParameters' => [ 'shape' => 'ConnectionBodyParametersList', ], ], ], 'ConnectionName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[\\.\\-_A-Za-z0-9]+', ], 'ConnectionOAuthClientResponseParameters' => [ 'type' => 'structure', 'members' => [ 'ClientID' => [ 'shape' => 'AuthHeaderParameters', ], ], ], 'ConnectionOAuthHttpMethod' => [ 'type' => 'string', 'enum' => [ 'GET', 'POST', 'PUT', ], ], 'ConnectionOAuthResponseParameters' => [ 'type' => 'structure', 'members' => [ 'ClientParameters' => [ 'shape' => 'ConnectionOAuthClientResponseParameters', ], 'AuthorizationEndpoint' => [ 'shape' => 'HttpsEndpoint', ], 'HttpMethod' => [ 'shape' => 'ConnectionOAuthHttpMethod', ], 'OAuthHttpParameters' => [ 'shape' => 'ConnectionHttpParameters', ], ], ], 'ConnectionQueryStringParameter' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'QueryStringKey', ], 'Value' => [ 'shape' => 'QueryStringValueSensitive', ], 'IsValueSecret' => [ 'shape' => 'Boolean', ], ], ], 'ConnectionQueryStringParametersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectionQueryStringParameter', ], 'max' => 100, 'min' => 0, ], 'ConnectionResponseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Connection', ], ], 'ConnectionState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'UPDATING', 'DELETING', 'AUTHORIZED', 'DEAUTHORIZED', 'AUTHORIZING', 'DEAUTHORIZING', ], ], 'ConnectionStateReason' => [ 'type' => 'string', 'max' => 512, 'pattern' => '.*', ], 'CreateApiDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'ConnectionArn', 'InvocationEndpoint', 'HttpMethod', ], 'members' => [ 'Name' => [ 'shape' => 'ApiDestinationName', ], 'Description' => [ 'shape' => 'ApiDestinationDescription', ], 'ConnectionArn' => [ 'shape' => 'ConnectionArn', ], 'InvocationEndpoint' => [ 'shape' => 'HttpsEndpoint', ], 'HttpMethod' => [ 'shape' => 'ApiDestinationHttpMethod', ], 'InvocationRateLimitPerSecond' => [ 'shape' => 'ApiDestinationInvocationRateLimitPerSecond', ], ], ], 'CreateApiDestinationResponse' => [ 'type' => 'structure', 'members' => [ 'ApiDestinationArn' => [ 'shape' => 'ApiDestinationArn', ], 'ApiDestinationState' => [ 'shape' => 'ApiDestinationState', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'CreateArchiveRequest' => [ 'type' => 'structure', 'required' => [ 'ArchiveName', 'EventSourceArn', ], 'members' => [ 'ArchiveName' => [ 'shape' => 'ArchiveName', ], 'EventSourceArn' => [ 'shape' => 'Arn', ], 'Description' => [ 'shape' => 'ArchiveDescription', ], 'EventPattern' => [ 'shape' => 'EventPattern', ], 'RetentionDays' => [ 'shape' => 'RetentionDays', ], ], ], 'CreateArchiveResponse' => [ 'type' => 'structure', 'members' => [ 'ArchiveArn' => [ 'shape' => 'ArchiveArn', ], 'State' => [ 'shape' => 'ArchiveState', ], 'StateReason' => [ 'shape' => 'ArchiveStateReason', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], ], ], 'CreateConnectionApiKeyAuthRequestParameters' => [ 'type' => 'structure', 'required' => [ 'ApiKeyName', 'ApiKeyValue', ], 'members' => [ 'ApiKeyName' => [ 'shape' => 'AuthHeaderParameters', ], 'ApiKeyValue' => [ 'shape' => 'AuthHeaderParametersSensitive', ], ], ], 'CreateConnectionAuthRequestParameters' => [ 'type' => 'structure', 'members' => [ 'BasicAuthParameters' => [ 'shape' => 'CreateConnectionBasicAuthRequestParameters', ], 'OAuthParameters' => [ 'shape' => 'CreateConnectionOAuthRequestParameters', ], 'ApiKeyAuthParameters' => [ 'shape' => 'CreateConnectionApiKeyAuthRequestParameters', ], 'InvocationHttpParameters' => [ 'shape' => 'ConnectionHttpParameters', ], ], ], 'CreateConnectionBasicAuthRequestParameters' => [ 'type' => 'structure', 'required' => [ 'Username', 'Password', ], 'members' => [ 'Username' => [ 'shape' => 'AuthHeaderParameters', ], 'Password' => [ 'shape' => 'AuthHeaderParametersSensitive', ], ], ], 'CreateConnectionOAuthClientRequestParameters' => [ 'type' => 'structure', 'required' => [ 'ClientID', 'ClientSecret', ], 'members' => [ 'ClientID' => [ 'shape' => 'AuthHeaderParameters', ], 'ClientSecret' => [ 'shape' => 'AuthHeaderParametersSensitive', ], ], ], 'CreateConnectionOAuthRequestParameters' => [ 'type' => 'structure', 'required' => [ 'ClientParameters', 'AuthorizationEndpoint', 'HttpMethod', ], 'members' => [ 'ClientParameters' => [ 'shape' => 'CreateConnectionOAuthClientRequestParameters', ], 'AuthorizationEndpoint' => [ 'shape' => 'HttpsEndpoint', ], 'HttpMethod' => [ 'shape' => 'ConnectionOAuthHttpMethod', ], 'OAuthHttpParameters' => [ 'shape' => 'ConnectionHttpParameters', ], ], ], 'CreateConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'AuthorizationType', 'AuthParameters', ], 'members' => [ 'Name' => [ 'shape' => 'ConnectionName', ], 'Description' => [ 'shape' => 'ConnectionDescription', ], 'AuthorizationType' => [ 'shape' => 'ConnectionAuthorizationType', ], 'AuthParameters' => [ 'shape' => 'CreateConnectionAuthRequestParameters', ], ], ], 'CreateConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectionArn' => [ 'shape' => 'ConnectionArn', ], 'ConnectionState' => [ 'shape' => 'ConnectionState', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'CreateEventBusRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'EventBusName', ], 'EventSourceName' => [ 'shape' => 'EventSourceName', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateEventBusResponse' => [ 'type' => 'structure', 'members' => [ 'EventBusArn' => [ 'shape' => 'String', ], ], ], 'CreatePartnerEventSourceRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Account', ], 'members' => [ 'Name' => [ 'shape' => 'EventSourceName', ], 'Account' => [ 'shape' => 'AccountId', ], ], ], 'CreatePartnerEventSourceResponse' => [ 'type' => 'structure', 'members' => [ 'EventSourceArn' => [ 'shape' => 'String', ], ], ], 'CreatedBy' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'Database' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'DbUser' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'DeactivateEventSourceRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'EventSourceName', ], ], ], 'DeadLetterConfig' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ResourceArn', ], ], ], 'DeauthorizeConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ConnectionName', ], ], ], 'DeauthorizeConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectionArn' => [ 'shape' => 'ConnectionArn', ], 'ConnectionState' => [ 'shape' => 'ConnectionState', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastAuthorizedTime' => [ 'shape' => 'Timestamp', ], ], ], 'DeleteApiDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ApiDestinationName', ], ], ], 'DeleteApiDestinationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteArchiveRequest' => [ 'type' => 'structure', 'required' => [ 'ArchiveName', ], 'members' => [ 'ArchiveName' => [ 'shape' => 'ArchiveName', ], ], ], 'DeleteArchiveResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ConnectionName', ], ], ], 'DeleteConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectionArn' => [ 'shape' => 'ConnectionArn', ], 'ConnectionState' => [ 'shape' => 'ConnectionState', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastAuthorizedTime' => [ 'shape' => 'Timestamp', ], ], ], 'DeleteEventBusRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'EventBusName', ], ], ], 'DeletePartnerEventSourceRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Account', ], 'members' => [ 'Name' => [ 'shape' => 'EventSourceName', ], 'Account' => [ 'shape' => 'AccountId', ], ], ], 'DeleteRuleRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'RuleName', ], 'EventBusName' => [ 'shape' => 'EventBusNameOrArn', ], 'Force' => [ 'shape' => 'Boolean', ], ], ], 'DescribeApiDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ApiDestinationName', ], ], ], 'DescribeApiDestinationResponse' => [ 'type' => 'structure', 'members' => [ 'ApiDestinationArn' => [ 'shape' => 'ApiDestinationArn', ], 'Name' => [ 'shape' => 'ApiDestinationName', ], 'Description' => [ 'shape' => 'ApiDestinationDescription', ], 'ApiDestinationState' => [ 'shape' => 'ApiDestinationState', ], 'ConnectionArn' => [ 'shape' => 'ConnectionArn', ], 'InvocationEndpoint' => [ 'shape' => 'HttpsEndpoint', ], 'HttpMethod' => [ 'shape' => 'ApiDestinationHttpMethod', ], 'InvocationRateLimitPerSecond' => [ 'shape' => 'ApiDestinationInvocationRateLimitPerSecond', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeArchiveRequest' => [ 'type' => 'structure', 'required' => [ 'ArchiveName', ], 'members' => [ 'ArchiveName' => [ 'shape' => 'ArchiveName', ], ], ], 'DescribeArchiveResponse' => [ 'type' => 'structure', 'members' => [ 'ArchiveArn' => [ 'shape' => 'ArchiveArn', ], 'ArchiveName' => [ 'shape' => 'ArchiveName', ], 'EventSourceArn' => [ 'shape' => 'Arn', ], 'Description' => [ 'shape' => 'ArchiveDescription', ], 'EventPattern' => [ 'shape' => 'EventPattern', ], 'State' => [ 'shape' => 'ArchiveState', ], 'StateReason' => [ 'shape' => 'ArchiveStateReason', ], 'RetentionDays' => [ 'shape' => 'RetentionDays', ], 'SizeBytes' => [ 'shape' => 'Long', ], 'EventCount' => [ 'shape' => 'Long', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ConnectionName', ], ], ], 'DescribeConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectionArn' => [ 'shape' => 'ConnectionArn', ], 'Name' => [ 'shape' => 'ConnectionName', ], 'Description' => [ 'shape' => 'ConnectionDescription', ], 'ConnectionState' => [ 'shape' => 'ConnectionState', ], 'StateReason' => [ 'shape' => 'ConnectionStateReason', ], 'AuthorizationType' => [ 'shape' => 'ConnectionAuthorizationType', ], 'SecretArn' => [ 'shape' => 'SecretsManagerSecretArn', ], 'AuthParameters' => [ 'shape' => 'ConnectionAuthResponseParameters', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastAuthorizedTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeEventBusRequest' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'EventBusNameOrArn', ], ], ], 'DescribeEventBusResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Arn' => [ 'shape' => 'String', ], 'Policy' => [ 'shape' => 'String', ], ], ], 'DescribeEventSourceRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'EventSourceName', ], ], ], 'DescribeEventSourceResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'String', ], 'CreatedBy' => [ 'shape' => 'String', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'ExpirationTime' => [ 'shape' => 'Timestamp', ], 'Name' => [ 'shape' => 'String', ], 'State' => [ 'shape' => 'EventSourceState', ], ], ], 'DescribePartnerEventSourceRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'EventSourceName', ], ], ], 'DescribePartnerEventSourceResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], ], ], 'DescribeReplayRequest' => [ 'type' => 'structure', 'required' => [ 'ReplayName', ], 'members' => [ 'ReplayName' => [ 'shape' => 'ReplayName', ], ], ], 'DescribeReplayResponse' => [ 'type' => 'structure', 'members' => [ 'ReplayName' => [ 'shape' => 'ReplayName', ], 'ReplayArn' => [ 'shape' => 'ReplayArn', ], 'Description' => [ 'shape' => 'ReplayDescription', ], 'State' => [ 'shape' => 'ReplayState', ], 'StateReason' => [ 'shape' => 'ReplayStateReason', ], 'EventSourceArn' => [ 'shape' => 'Arn', ], 'Destination' => [ 'shape' => 'ReplayDestination', ], 'EventStartTime' => [ 'shape' => 'Timestamp', ], 'EventEndTime' => [ 'shape' => 'Timestamp', ], 'EventLastReplayedTime' => [ 'shape' => 'Timestamp', ], 'ReplayStartTime' => [ 'shape' => 'Timestamp', ], 'ReplayEndTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeRuleRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'RuleName', ], 'EventBusName' => [ 'shape' => 'EventBusNameOrArn', ], ], ], 'DescribeRuleResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'RuleName', ], 'Arn' => [ 'shape' => 'RuleArn', ], 'EventPattern' => [ 'shape' => 'EventPattern', ], 'ScheduleExpression' => [ 'shape' => 'ScheduleExpression', ], 'State' => [ 'shape' => 'RuleState', ], 'Description' => [ 'shape' => 'RuleDescription', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'ManagedBy' => [ 'shape' => 'ManagedBy', ], 'EventBusName' => [ 'shape' => 'EventBusName', ], 'CreatedBy' => [ 'shape' => 'CreatedBy', ], ], ], 'DisableRuleRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'RuleName', ], 'EventBusName' => [ 'shape' => 'EventBusNameOrArn', ], ], ], 'EcsParameters' => [ 'type' => 'structure', 'required' => [ 'TaskDefinitionArn', ], 'members' => [ 'TaskDefinitionArn' => [ 'shape' => 'Arn', ], 'TaskCount' => [ 'shape' => 'LimitMin1', ], 'LaunchType' => [ 'shape' => 'LaunchType', ], 'NetworkConfiguration' => [ 'shape' => 'NetworkConfiguration', ], 'PlatformVersion' => [ 'shape' => 'String', ], 'Group' => [ 'shape' => 'String', ], 'CapacityProviderStrategy' => [ 'shape' => 'CapacityProviderStrategy', ], 'EnableECSManagedTags' => [ 'shape' => 'Boolean', ], 'EnableExecuteCommand' => [ 'shape' => 'Boolean', ], 'PlacementConstraints' => [ 'shape' => 'PlacementConstraints', ], 'PlacementStrategy' => [ 'shape' => 'PlacementStrategies', ], 'PropagateTags' => [ 'shape' => 'PropagateTags', ], 'ReferenceId' => [ 'shape' => 'ReferenceId', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'EnableRuleRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'RuleName', ], 'EventBusName' => [ 'shape' => 'EventBusNameOrArn', ], ], ], 'ErrorCode' => [ 'type' => 'string', ], 'ErrorMessage' => [ 'type' => 'string', ], 'EventBus' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Arn' => [ 'shape' => 'String', ], 'Policy' => [ 'shape' => 'String', ], ], ], 'EventBusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventBus', ], ], 'EventBusName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[/\\.\\-_A-Za-z0-9]+', ], 'EventBusNameOrArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '(arn:aws[\\w-]*:events:[a-z]{2}-[a-z]+-[\\w-]+:[0-9]{12}:event-bus\\/)?[/\\.\\-_A-Za-z0-9]+', ], 'EventId' => [ 'type' => 'string', ], 'EventPattern' => [ 'type' => 'string', ], 'EventResource' => [ 'type' => 'string', ], 'EventResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventResource', ], ], 'EventSource' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'String', ], 'CreatedBy' => [ 'shape' => 'String', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'ExpirationTime' => [ 'shape' => 'Timestamp', ], 'Name' => [ 'shape' => 'String', ], 'State' => [ 'shape' => 'EventSourceState', ], ], ], 'EventSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventSource', ], ], 'EventSourceName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => 'aws\\.partner(/[\\.\\-_A-Za-z0-9]+){2,}', ], 'EventSourceNamePrefix' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[/\\.\\-_A-Za-z0-9]+', ], 'EventSourceState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'ACTIVE', 'DELETED', ], ], 'EventTime' => [ 'type' => 'timestamp', ], 'HeaderKey' => [ 'type' => 'string', 'max' => 512, 'pattern' => '^[!#$%&\'*+-.^_`|~0-9a-zA-Z]+$', ], 'HeaderParametersMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'HeaderKey', ], 'value' => [ 'shape' => 'HeaderValue', ], ], 'HeaderValue' => [ 'type' => 'string', 'max' => 512, 'pattern' => '^[ \\t]*[\\x20-\\x7E]+([ \\t]+[\\x20-\\x7E]+)*[ \\t]*$', ], 'HeaderValueSensitive' => [ 'type' => 'string', 'max' => 512, 'pattern' => '^[ \\t]*[\\x20-\\x7E]+([ \\t]+[\\x20-\\x7E]+)*[ \\t]*$', 'sensitive' => true, ], 'HttpParameters' => [ 'type' => 'structure', 'members' => [ 'PathParameterValues' => [ 'shape' => 'PathParameterList', ], 'HeaderParameters' => [ 'shape' => 'HeaderParametersMap', ], 'QueryStringParameters' => [ 'shape' => 'QueryStringParametersMap', ], ], ], 'HttpsEndpoint' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^((%[0-9A-Fa-f]{2}|[-()_.!~*\';/?:@\\x26=+$,A-Za-z0-9])+)([).!\';/?:,])?$', ], 'IllegalStatusException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InputTransformer' => [ 'type' => 'structure', 'required' => [ 'InputTemplate', ], 'members' => [ 'InputPathsMap' => [ 'shape' => 'TransformerPaths', ], 'InputTemplate' => [ 'shape' => 'TransformerInput', ], ], ], 'InputTransformerPathKey' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[A-Za-z0-9\\_\\-]+', ], 'Integer' => [ 'type' => 'integer', ], 'InternalException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, 'fault' => true, ], 'InvalidEventPatternException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidStateException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'KinesisParameters' => [ 'type' => 'structure', 'required' => [ 'PartitionKeyPath', ], 'members' => [ 'PartitionKeyPath' => [ 'shape' => 'TargetPartitionKeyPath', ], ], ], 'LaunchType' => [ 'type' => 'string', 'enum' => [ 'EC2', 'FARGATE', 'EXTERNAL', ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'LimitMax100' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'LimitMin1' => [ 'type' => 'integer', 'min' => 1, ], 'ListApiDestinationsRequest' => [ 'type' => 'structure', 'members' => [ 'NamePrefix' => [ 'shape' => 'ApiDestinationName', ], 'ConnectionArn' => [ 'shape' => 'ConnectionArn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'LimitMax100', ], ], ], 'ListApiDestinationsResponse' => [ 'type' => 'structure', 'members' => [ 'ApiDestinations' => [ 'shape' => 'ApiDestinationResponseList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListArchivesRequest' => [ 'type' => 'structure', 'members' => [ 'NamePrefix' => [ 'shape' => 'ArchiveName', ], 'EventSourceArn' => [ 'shape' => 'Arn', ], 'State' => [ 'shape' => 'ArchiveState', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'LimitMax100', ], ], ], 'ListArchivesResponse' => [ 'type' => 'structure', 'members' => [ 'Archives' => [ 'shape' => 'ArchiveResponseList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListConnectionsRequest' => [ 'type' => 'structure', 'members' => [ 'NamePrefix' => [ 'shape' => 'ConnectionName', ], 'ConnectionState' => [ 'shape' => 'ConnectionState', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'LimitMax100', ], ], ], 'ListConnectionsResponse' => [ 'type' => 'structure', 'members' => [ 'Connections' => [ 'shape' => 'ConnectionResponseList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEventBusesRequest' => [ 'type' => 'structure', 'members' => [ 'NamePrefix' => [ 'shape' => 'EventBusName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'LimitMax100', ], ], ], 'ListEventBusesResponse' => [ 'type' => 'structure', 'members' => [ 'EventBuses' => [ 'shape' => 'EventBusList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEventSourcesRequest' => [ 'type' => 'structure', 'members' => [ 'NamePrefix' => [ 'shape' => 'EventSourceNamePrefix', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'LimitMax100', ], ], ], 'ListEventSourcesResponse' => [ 'type' => 'structure', 'members' => [ 'EventSources' => [ 'shape' => 'EventSourceList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPartnerEventSourceAccountsRequest' => [ 'type' => 'structure', 'required' => [ 'EventSourceName', ], 'members' => [ 'EventSourceName' => [ 'shape' => 'EventSourceName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'LimitMax100', ], ], ], 'ListPartnerEventSourceAccountsResponse' => [ 'type' => 'structure', 'members' => [ 'PartnerEventSourceAccounts' => [ 'shape' => 'PartnerEventSourceAccountList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPartnerEventSourcesRequest' => [ 'type' => 'structure', 'required' => [ 'NamePrefix', ], 'members' => [ 'NamePrefix' => [ 'shape' => 'PartnerEventSourceNamePrefix', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'LimitMax100', ], ], ], 'ListPartnerEventSourcesResponse' => [ 'type' => 'structure', 'members' => [ 'PartnerEventSources' => [ 'shape' => 'PartnerEventSourceList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListReplaysRequest' => [ 'type' => 'structure', 'members' => [ 'NamePrefix' => [ 'shape' => 'ReplayName', ], 'State' => [ 'shape' => 'ReplayState', ], 'EventSourceArn' => [ 'shape' => 'Arn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'LimitMax100', ], ], ], 'ListReplaysResponse' => [ 'type' => 'structure', 'members' => [ 'Replays' => [ 'shape' => 'ReplayList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRuleNamesByTargetRequest' => [ 'type' => 'structure', 'required' => [ 'TargetArn', ], 'members' => [ 'TargetArn' => [ 'shape' => 'TargetArn', ], 'EventBusName' => [ 'shape' => 'EventBusNameOrArn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'LimitMax100', ], ], ], 'ListRuleNamesByTargetResponse' => [ 'type' => 'structure', 'members' => [ 'RuleNames' => [ 'shape' => 'RuleNameList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRulesRequest' => [ 'type' => 'structure', 'members' => [ 'NamePrefix' => [ 'shape' => 'RuleName', ], 'EventBusName' => [ 'shape' => 'EventBusNameOrArn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'LimitMax100', ], ], ], 'ListRulesResponse' => [ 'type' => 'structure', 'members' => [ 'Rules' => [ 'shape' => 'RuleResponseList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'Arn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ListTargetsByRuleRequest' => [ 'type' => 'structure', 'required' => [ 'Rule', ], 'members' => [ 'Rule' => [ 'shape' => 'RuleName', ], 'EventBusName' => [ 'shape' => 'EventBusNameOrArn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'LimitMax100', ], ], ], 'ListTargetsByRuleResponse' => [ 'type' => 'structure', 'members' => [ 'Targets' => [ 'shape' => 'TargetList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'Long' => [ 'type' => 'long', ], 'ManagedBy' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'ManagedRuleException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaximumEventAgeInSeconds' => [ 'type' => 'integer', 'max' => 86400, 'min' => 60, ], 'MaximumRetryAttempts' => [ 'type' => 'integer', 'max' => 185, 'min' => 0, ], 'MessageGroupId' => [ 'type' => 'string', ], 'NetworkConfiguration' => [ 'type' => 'structure', 'members' => [ 'awsvpcConfiguration' => [ 'shape' => 'AwsVpcConfiguration', ], ], ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'NonPartnerEventBusName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\.\\-_A-Za-z0-9]+', ], 'NonPartnerEventBusNameOrArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '(arn:aws[\\w-]*:events:[a-z]{2}-[a-z]+-[\\w-]+:[0-9]{12}:event-bus\\/)?[\\.\\-_A-Za-z0-9]+', ], 'OperationDisabledException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'PartnerEventSource' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], ], ], 'PartnerEventSourceAccount' => [ 'type' => 'structure', 'members' => [ 'Account' => [ 'shape' => 'AccountId', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'ExpirationTime' => [ 'shape' => 'Timestamp', ], 'State' => [ 'shape' => 'EventSourceState', ], ], ], 'PartnerEventSourceAccountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartnerEventSourceAccount', ], ], 'PartnerEventSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartnerEventSource', ], ], 'PartnerEventSourceNamePrefix' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => 'aws\\.partner/[\\.\\-_A-Za-z0-9]+/[/\\.\\-_A-Za-z0-9]*', ], 'PathParameter' => [ 'type' => 'string', 'pattern' => '^(?!\\s*$).+', ], 'PathParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PathParameter', ], ], 'PlacementConstraint' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'PlacementConstraintType', ], 'expression' => [ 'shape' => 'PlacementConstraintExpression', ], ], ], 'PlacementConstraintExpression' => [ 'type' => 'string', 'max' => 2000, ], 'PlacementConstraintType' => [ 'type' => 'string', 'enum' => [ 'distinctInstance', 'memberOf', ], ], 'PlacementConstraints' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlacementConstraint', ], 'max' => 10, ], 'PlacementStrategies' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlacementStrategy', ], 'max' => 5, ], 'PlacementStrategy' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'PlacementStrategyType', ], 'field' => [ 'shape' => 'PlacementStrategyField', ], ], ], 'PlacementStrategyField' => [ 'type' => 'string', 'max' => 255, ], 'PlacementStrategyType' => [ 'type' => 'string', 'enum' => [ 'random', 'spread', 'binpack', ], ], 'PolicyLengthExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Principal' => [ 'type' => 'string', 'max' => 12, 'min' => 1, 'pattern' => '(\\d{12}|\\*)', ], 'PropagateTags' => [ 'type' => 'string', 'enum' => [ 'TASK_DEFINITION', ], ], 'PutEventsRequest' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'PutEventsRequestEntryList', ], ], ], 'PutEventsRequestEntry' => [ 'type' => 'structure', 'members' => [ 'Time' => [ 'shape' => 'EventTime', ], 'Source' => [ 'shape' => 'String', ], 'Resources' => [ 'shape' => 'EventResourceList', ], 'DetailType' => [ 'shape' => 'String', ], 'Detail' => [ 'shape' => 'String', ], 'EventBusName' => [ 'shape' => 'NonPartnerEventBusNameOrArn', ], 'TraceHeader' => [ 'shape' => 'TraceHeader', ], ], ], 'PutEventsRequestEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PutEventsRequestEntry', ], 'max' => 10, 'min' => 1, ], 'PutEventsResponse' => [ 'type' => 'structure', 'members' => [ 'FailedEntryCount' => [ 'shape' => 'Integer', ], 'Entries' => [ 'shape' => 'PutEventsResultEntryList', ], ], ], 'PutEventsResultEntry' => [ 'type' => 'structure', 'members' => [ 'EventId' => [ 'shape' => 'EventId', ], 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'PutEventsResultEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PutEventsResultEntry', ], ], 'PutPartnerEventsRequest' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'PutPartnerEventsRequestEntryList', ], ], ], 'PutPartnerEventsRequestEntry' => [ 'type' => 'structure', 'members' => [ 'Time' => [ 'shape' => 'EventTime', ], 'Source' => [ 'shape' => 'EventSourceName', ], 'Resources' => [ 'shape' => 'EventResourceList', ], 'DetailType' => [ 'shape' => 'String', ], 'Detail' => [ 'shape' => 'String', ], ], ], 'PutPartnerEventsRequestEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PutPartnerEventsRequestEntry', ], 'max' => 20, 'min' => 1, ], 'PutPartnerEventsResponse' => [ 'type' => 'structure', 'members' => [ 'FailedEntryCount' => [ 'shape' => 'Integer', ], 'Entries' => [ 'shape' => 'PutPartnerEventsResultEntryList', ], ], ], 'PutPartnerEventsResultEntry' => [ 'type' => 'structure', 'members' => [ 'EventId' => [ 'shape' => 'EventId', ], 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'PutPartnerEventsResultEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PutPartnerEventsResultEntry', ], ], 'PutPermissionRequest' => [ 'type' => 'structure', 'members' => [ 'EventBusName' => [ 'shape' => 'NonPartnerEventBusName', ], 'Action' => [ 'shape' => 'Action', ], 'Principal' => [ 'shape' => 'Principal', ], 'StatementId' => [ 'shape' => 'StatementId', ], 'Condition' => [ 'shape' => 'Condition', ], 'Policy' => [ 'shape' => 'String', ], ], ], 'PutRuleRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'RuleName', ], 'ScheduleExpression' => [ 'shape' => 'ScheduleExpression', ], 'EventPattern' => [ 'shape' => 'EventPattern', ], 'State' => [ 'shape' => 'RuleState', ], 'Description' => [ 'shape' => 'RuleDescription', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Tags' => [ 'shape' => 'TagList', ], 'EventBusName' => [ 'shape' => 'EventBusNameOrArn', ], ], ], 'PutRuleResponse' => [ 'type' => 'structure', 'members' => [ 'RuleArn' => [ 'shape' => 'RuleArn', ], ], ], 'PutTargetsRequest' => [ 'type' => 'structure', 'required' => [ 'Rule', 'Targets', ], 'members' => [ 'Rule' => [ 'shape' => 'RuleName', ], 'EventBusName' => [ 'shape' => 'EventBusNameOrArn', ], 'Targets' => [ 'shape' => 'TargetList', ], ], ], 'PutTargetsResponse' => [ 'type' => 'structure', 'members' => [ 'FailedEntryCount' => [ 'shape' => 'Integer', ], 'FailedEntries' => [ 'shape' => 'PutTargetsResultEntryList', ], ], ], 'PutTargetsResultEntry' => [ 'type' => 'structure', 'members' => [ 'TargetId' => [ 'shape' => 'TargetId', ], 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'PutTargetsResultEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PutTargetsResultEntry', ], ], 'QueryStringKey' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[^\\x00-\\x1F\\x7F]+', ], 'QueryStringParametersMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'QueryStringKey', ], 'value' => [ 'shape' => 'QueryStringValue', ], ], 'QueryStringValue' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[^\\x00-\\x09\\x0B\\x0C\\x0E-\\x1F\\x7F]+', ], 'QueryStringValueSensitive' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[^\\x00-\\x09\\x0B\\x0C\\x0E-\\x1F\\x7F]+', 'sensitive' => true, ], 'RedshiftDataParameters' => [ 'type' => 'structure', 'required' => [ 'Database', 'Sql', ], 'members' => [ 'SecretManagerArn' => [ 'shape' => 'RedshiftSecretManagerArn', ], 'Database' => [ 'shape' => 'Database', ], 'DbUser' => [ 'shape' => 'DbUser', ], 'Sql' => [ 'shape' => 'Sql', ], 'StatementName' => [ 'shape' => 'StatementName', ], 'WithEvent' => [ 'shape' => 'Boolean', ], ], ], 'RedshiftSecretManagerArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '(^arn:aws([a-z]|\\-)*:secretsmanager:[a-z0-9-.]+:.*)|(\\$(\\.[\\w_-]+(\\[(\\d+|\\*)\\])*)*)', ], 'ReferenceId' => [ 'type' => 'string', 'max' => 1024, ], 'RemovePermissionRequest' => [ 'type' => 'structure', 'members' => [ 'StatementId' => [ 'shape' => 'StatementId', ], 'RemoveAllPermissions' => [ 'shape' => 'Boolean', ], 'EventBusName' => [ 'shape' => 'NonPartnerEventBusName', ], ], ], 'RemoveTargetsRequest' => [ 'type' => 'structure', 'required' => [ 'Rule', 'Ids', ], 'members' => [ 'Rule' => [ 'shape' => 'RuleName', ], 'EventBusName' => [ 'shape' => 'EventBusNameOrArn', ], 'Ids' => [ 'shape' => 'TargetIdList', ], 'Force' => [ 'shape' => 'Boolean', ], ], ], 'RemoveTargetsResponse' => [ 'type' => 'structure', 'members' => [ 'FailedEntryCount' => [ 'shape' => 'Integer', ], 'FailedEntries' => [ 'shape' => 'RemoveTargetsResultEntryList', ], ], ], 'RemoveTargetsResultEntry' => [ 'type' => 'structure', 'members' => [ 'TargetId' => [ 'shape' => 'TargetId', ], 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'RemoveTargetsResultEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RemoveTargetsResultEntry', ], ], 'Replay' => [ 'type' => 'structure', 'members' => [ 'ReplayName' => [ 'shape' => 'ReplayName', ], 'EventSourceArn' => [ 'shape' => 'Arn', ], 'State' => [ 'shape' => 'ReplayState', ], 'StateReason' => [ 'shape' => 'ReplayStateReason', ], 'EventStartTime' => [ 'shape' => 'Timestamp', ], 'EventEndTime' => [ 'shape' => 'Timestamp', ], 'EventLastReplayedTime' => [ 'shape' => 'Timestamp', ], 'ReplayStartTime' => [ 'shape' => 'Timestamp', ], 'ReplayEndTime' => [ 'shape' => 'Timestamp', ], ], ], 'ReplayArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '^arn:aws([a-z]|\\-)*:events:([a-z]|\\d|\\-)*:([0-9]{12})?:.+\\/[\\.\\-_A-Za-z0-9]+$', ], 'ReplayDescription' => [ 'type' => 'string', 'max' => 512, 'pattern' => '.*', ], 'ReplayDestination' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'FilterArns' => [ 'shape' => 'ReplayDestinationFilters', ], ], ], 'ReplayDestinationFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], ], 'ReplayList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Replay', ], ], 'ReplayName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[\\.\\-_A-Za-z0-9]+', ], 'ReplayState' => [ 'type' => 'string', 'enum' => [ 'STARTING', 'RUNNING', 'CANCELLING', 'COMPLETED', 'CANCELLED', 'FAILED', ], ], 'ReplayStateReason' => [ 'type' => 'string', 'max' => 512, 'pattern' => '.*', ], 'ResourceAlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ResourceArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RetentionDays' => [ 'type' => 'integer', 'min' => 0, ], 'RetryPolicy' => [ 'type' => 'structure', 'members' => [ 'MaximumRetryAttempts' => [ 'shape' => 'MaximumRetryAttempts', ], 'MaximumEventAgeInSeconds' => [ 'shape' => 'MaximumEventAgeInSeconds', ], ], ], 'RoleArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, ], 'Rule' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'RuleName', ], 'Arn' => [ 'shape' => 'RuleArn', ], 'EventPattern' => [ 'shape' => 'EventPattern', ], 'State' => [ 'shape' => 'RuleState', ], 'Description' => [ 'shape' => 'RuleDescription', ], 'ScheduleExpression' => [ 'shape' => 'ScheduleExpression', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'ManagedBy' => [ 'shape' => 'ManagedBy', ], 'EventBusName' => [ 'shape' => 'EventBusName', ], ], ], 'RuleArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, ], 'RuleDescription' => [ 'type' => 'string', 'max' => 512, ], 'RuleName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[\\.\\-_A-Za-z0-9]+', ], 'RuleNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleName', ], ], 'RuleResponseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Rule', ], ], 'RuleState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'RunCommandParameters' => [ 'type' => 'structure', 'required' => [ 'RunCommandTargets', ], 'members' => [ 'RunCommandTargets' => [ 'shape' => 'RunCommandTargets', ], ], ], 'RunCommandTarget' => [ 'type' => 'structure', 'required' => [ 'Key', 'Values', ], 'members' => [ 'Key' => [ 'shape' => 'RunCommandTargetKey', ], 'Values' => [ 'shape' => 'RunCommandTargetValues', ], ], ], 'RunCommandTargetKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*$', ], 'RunCommandTargetValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'RunCommandTargetValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'RunCommandTargetValue', ], 'max' => 50, 'min' => 1, ], 'RunCommandTargets' => [ 'type' => 'list', 'member' => [ 'shape' => 'RunCommandTarget', ], 'max' => 5, 'min' => 1, ], 'SageMakerPipelineParameter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'SageMakerPipelineParameterName', ], 'Value' => [ 'shape' => 'SageMakerPipelineParameterValue', ], ], ], 'SageMakerPipelineParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SageMakerPipelineParameter', ], 'max' => 200, 'min' => 0, ], 'SageMakerPipelineParameterName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9])*$', ], 'SageMakerPipelineParameterValue' => [ 'type' => 'string', 'max' => 1024, ], 'SageMakerPipelineParameters' => [ 'type' => 'structure', 'members' => [ 'PipelineParameterList' => [ 'shape' => 'SageMakerPipelineParameterList', ], ], ], 'ScheduleExpression' => [ 'type' => 'string', 'max' => 256, ], 'SecretsManagerSecretArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:aws([a-z]|\\-)*:secretsmanager:([a-z]|\\d|\\-)*:([0-9]{12})?:secret:[\\/_+=\\.@\\-A-Za-z0-9]+$', ], 'SensitiveString' => [ 'type' => 'string', 'sensitive' => true, ], 'Sql' => [ 'type' => 'string', 'max' => 100000, 'min' => 1, ], 'SqsParameters' => [ 'type' => 'structure', 'members' => [ 'MessageGroupId' => [ 'shape' => 'MessageGroupId', ], ], ], 'StartReplayRequest' => [ 'type' => 'structure', 'required' => [ 'ReplayName', 'EventSourceArn', 'EventStartTime', 'EventEndTime', 'Destination', ], 'members' => [ 'ReplayName' => [ 'shape' => 'ReplayName', ], 'Description' => [ 'shape' => 'ReplayDescription', ], 'EventSourceArn' => [ 'shape' => 'Arn', ], 'EventStartTime' => [ 'shape' => 'Timestamp', ], 'EventEndTime' => [ 'shape' => 'Timestamp', ], 'Destination' => [ 'shape' => 'ReplayDestination', ], ], ], 'StartReplayResponse' => [ 'type' => 'structure', 'members' => [ 'ReplayArn' => [ 'shape' => 'ReplayArn', ], 'State' => [ 'shape' => 'ReplayState', ], 'StateReason' => [ 'shape' => 'ReplayStateReason', ], 'ReplayStartTime' => [ 'shape' => 'Timestamp', ], ], ], 'StatementId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9-_]+', ], 'StatementName' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'Tags', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'Arn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'Target' => [ 'type' => 'structure', 'required' => [ 'Id', 'Arn', ], 'members' => [ 'Id' => [ 'shape' => 'TargetId', ], 'Arn' => [ 'shape' => 'TargetArn', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Input' => [ 'shape' => 'TargetInput', ], 'InputPath' => [ 'shape' => 'TargetInputPath', ], 'InputTransformer' => [ 'shape' => 'InputTransformer', ], 'KinesisParameters' => [ 'shape' => 'KinesisParameters', ], 'RunCommandParameters' => [ 'shape' => 'RunCommandParameters', ], 'EcsParameters' => [ 'shape' => 'EcsParameters', ], 'BatchParameters' => [ 'shape' => 'BatchParameters', ], 'SqsParameters' => [ 'shape' => 'SqsParameters', ], 'HttpParameters' => [ 'shape' => 'HttpParameters', ], 'RedshiftDataParameters' => [ 'shape' => 'RedshiftDataParameters', ], 'SageMakerPipelineParameters' => [ 'shape' => 'SageMakerPipelineParameters', ], 'DeadLetterConfig' => [ 'shape' => 'DeadLetterConfig', ], 'RetryPolicy' => [ 'shape' => 'RetryPolicy', ], ], ], 'TargetArn' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, ], 'TargetId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[\\.\\-_A-Za-z0-9]+', ], 'TargetIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetId', ], 'max' => 100, 'min' => 1, ], 'TargetInput' => [ 'type' => 'string', 'max' => 8192, ], 'TargetInputPath' => [ 'type' => 'string', 'max' => 256, ], 'TargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Target', ], 'max' => 100, 'min' => 1, ], 'TargetPartitionKeyPath' => [ 'type' => 'string', 'max' => 256, ], 'TestEventPatternRequest' => [ 'type' => 'structure', 'required' => [ 'EventPattern', 'Event', ], 'members' => [ 'EventPattern' => [ 'shape' => 'EventPattern', ], 'Event' => [ 'shape' => 'String', ], ], ], 'TestEventPatternResponse' => [ 'type' => 'structure', 'members' => [ 'Result' => [ 'shape' => 'Boolean', ], ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TraceHeader' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'TransformerInput' => [ 'type' => 'string', 'max' => 8192, 'min' => 1, ], 'TransformerPaths' => [ 'type' => 'map', 'key' => [ 'shape' => 'InputTransformerPathKey', ], 'value' => [ 'shape' => 'TargetInputPath', ], 'max' => 100, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'TagKeys', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'Arn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateApiDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ApiDestinationName', ], 'Description' => [ 'shape' => 'ApiDestinationDescription', ], 'ConnectionArn' => [ 'shape' => 'ConnectionArn', ], 'InvocationEndpoint' => [ 'shape' => 'HttpsEndpoint', ], 'HttpMethod' => [ 'shape' => 'ApiDestinationHttpMethod', ], 'InvocationRateLimitPerSecond' => [ 'shape' => 'ApiDestinationInvocationRateLimitPerSecond', ], ], ], 'UpdateApiDestinationResponse' => [ 'type' => 'structure', 'members' => [ 'ApiDestinationArn' => [ 'shape' => 'ApiDestinationArn', ], 'ApiDestinationState' => [ 'shape' => 'ApiDestinationState', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateArchiveRequest' => [ 'type' => 'structure', 'required' => [ 'ArchiveName', ], 'members' => [ 'ArchiveName' => [ 'shape' => 'ArchiveName', ], 'Description' => [ 'shape' => 'ArchiveDescription', ], 'EventPattern' => [ 'shape' => 'EventPattern', ], 'RetentionDays' => [ 'shape' => 'RetentionDays', ], ], ], 'UpdateArchiveResponse' => [ 'type' => 'structure', 'members' => [ 'ArchiveArn' => [ 'shape' => 'ArchiveArn', ], 'State' => [ 'shape' => 'ArchiveState', ], 'StateReason' => [ 'shape' => 'ArchiveStateReason', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateConnectionApiKeyAuthRequestParameters' => [ 'type' => 'structure', 'members' => [ 'ApiKeyName' => [ 'shape' => 'AuthHeaderParameters', ], 'ApiKeyValue' => [ 'shape' => 'AuthHeaderParametersSensitive', ], ], ], 'UpdateConnectionAuthRequestParameters' => [ 'type' => 'structure', 'members' => [ 'BasicAuthParameters' => [ 'shape' => 'UpdateConnectionBasicAuthRequestParameters', ], 'OAuthParameters' => [ 'shape' => 'UpdateConnectionOAuthRequestParameters', ], 'ApiKeyAuthParameters' => [ 'shape' => 'UpdateConnectionApiKeyAuthRequestParameters', ], 'InvocationHttpParameters' => [ 'shape' => 'ConnectionHttpParameters', ], ], ], 'UpdateConnectionBasicAuthRequestParameters' => [ 'type' => 'structure', 'members' => [ 'Username' => [ 'shape' => 'AuthHeaderParameters', ], 'Password' => [ 'shape' => 'AuthHeaderParametersSensitive', ], ], ], 'UpdateConnectionOAuthClientRequestParameters' => [ 'type' => 'structure', 'members' => [ 'ClientID' => [ 'shape' => 'AuthHeaderParameters', ], 'ClientSecret' => [ 'shape' => 'AuthHeaderParametersSensitive', ], ], ], 'UpdateConnectionOAuthRequestParameters' => [ 'type' => 'structure', 'members' => [ 'ClientParameters' => [ 'shape' => 'UpdateConnectionOAuthClientRequestParameters', ], 'AuthorizationEndpoint' => [ 'shape' => 'HttpsEndpoint', ], 'HttpMethod' => [ 'shape' => 'ConnectionOAuthHttpMethod', ], 'OAuthHttpParameters' => [ 'shape' => 'ConnectionHttpParameters', ], ], ], 'UpdateConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ConnectionName', ], 'Description' => [ 'shape' => 'ConnectionDescription', ], 'AuthorizationType' => [ 'shape' => 'ConnectionAuthorizationType', ], 'AuthParameters' => [ 'shape' => 'UpdateConnectionAuthRequestParameters', ], ], ], 'UpdateConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectionArn' => [ 'shape' => 'ConnectionArn', ], 'ConnectionState' => [ 'shape' => 'ConnectionState', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastAuthorizedTime' => [ 'shape' => 'Timestamp', ], ], ], ],];
