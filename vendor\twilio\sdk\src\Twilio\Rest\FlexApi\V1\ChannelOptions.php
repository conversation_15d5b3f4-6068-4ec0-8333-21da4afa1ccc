<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\FlexApi\V1;

use Twilio\Options;
use Twilio\Values;

abstract class ChannelOptions
{
    /**
     * @param string $target The Target Contact Identity, for example the phone number of an SMS.
     * @param string $chatUniqueName The chat channel's unique name.
     * @param string $preEngagementData The pre-engagement data.
     * @param string $taskSid The SID of the TaskRouter Task. Only valid when integration type is `task`. `null` for integration types `studio` & `external`
     * @param string $taskAttributes The Task attributes to be added for the TaskRouter Task.
     * @param bool $longLived Whether to create the channel as long-lived.
     * @return CreateChannelOptions Options builder
     */
    public static function create(
        
        string $target = Values::NONE,
        string $chatUniqueName = Values::NONE,
        string $preEngagementData = Values::NONE,
        string $taskSid = Values::NONE,
        string $taskAttributes = Values::NONE,
        bool $longLived = Values::BOOL_NONE

    ): CreateChannelOptions
    {
        return new CreateChannelOptions(
            $target,
            $chatUniqueName,
            $preEngagementData,
            $taskSid,
            $taskAttributes,
            $longLived
        );
    }




}

class CreateChannelOptions extends Options
    {
    /**
     * @param string $target The Target Contact Identity, for example the phone number of an SMS.
     * @param string $chatUniqueName The chat channel's unique name.
     * @param string $preEngagementData The pre-engagement data.
     * @param string $taskSid The SID of the TaskRouter Task. Only valid when integration type is `task`. `null` for integration types `studio` & `external`
     * @param string $taskAttributes The Task attributes to be added for the TaskRouter Task.
     * @param bool $longLived Whether to create the channel as long-lived.
     */
    public function __construct(
        
        string $target = Values::NONE,
        string $chatUniqueName = Values::NONE,
        string $preEngagementData = Values::NONE,
        string $taskSid = Values::NONE,
        string $taskAttributes = Values::NONE,
        bool $longLived = Values::BOOL_NONE

    ) {
        $this->options['target'] = $target;
        $this->options['chatUniqueName'] = $chatUniqueName;
        $this->options['preEngagementData'] = $preEngagementData;
        $this->options['taskSid'] = $taskSid;
        $this->options['taskAttributes'] = $taskAttributes;
        $this->options['longLived'] = $longLived;
    }

    /**
     * The Target Contact Identity, for example the phone number of an SMS.
     *
     * @param string $target The Target Contact Identity, for example the phone number of an SMS.
     * @return $this Fluent Builder
     */
    public function setTarget(string $target): self
    {
        $this->options['target'] = $target;
        return $this;
    }

    /**
     * The chat channel's unique name.
     *
     * @param string $chatUniqueName The chat channel's unique name.
     * @return $this Fluent Builder
     */
    public function setChatUniqueName(string $chatUniqueName): self
    {
        $this->options['chatUniqueName'] = $chatUniqueName;
        return $this;
    }

    /**
     * The pre-engagement data.
     *
     * @param string $preEngagementData The pre-engagement data.
     * @return $this Fluent Builder
     */
    public function setPreEngagementData(string $preEngagementData): self
    {
        $this->options['preEngagementData'] = $preEngagementData;
        return $this;
    }

    /**
     * The SID of the TaskRouter Task. Only valid when integration type is `task`. `null` for integration types `studio` & `external`
     *
     * @param string $taskSid The SID of the TaskRouter Task. Only valid when integration type is `task`. `null` for integration types `studio` & `external`
     * @return $this Fluent Builder
     */
    public function setTaskSid(string $taskSid): self
    {
        $this->options['taskSid'] = $taskSid;
        return $this;
    }

    /**
     * The Task attributes to be added for the TaskRouter Task.
     *
     * @param string $taskAttributes The Task attributes to be added for the TaskRouter Task.
     * @return $this Fluent Builder
     */
    public function setTaskAttributes(string $taskAttributes): self
    {
        $this->options['taskAttributes'] = $taskAttributes;
        return $this;
    }

    /**
     * Whether to create the channel as long-lived.
     *
     * @param bool $longLived Whether to create the channel as long-lived.
     * @return $this Fluent Builder
     */
    public function setLongLived(bool $longLived): self
    {
        $this->options['longLived'] = $longLived;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.FlexApi.V1.CreateChannelOptions ' . $options . ']';
    }
}




