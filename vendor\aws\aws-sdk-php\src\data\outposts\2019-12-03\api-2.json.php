<?php
// This file was auto-generated from sdk-root/src/data/outposts/2019-12-03/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2019-12-03', 'endpointPrefix' => 'outposts', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceAbbreviation' => 'Outposts', 'serviceFullName' => 'AWS Outposts', 'serviceId' => 'Outposts', 'signatureVersion' => 'v4', 'signingName' => 'outposts', 'uid' => 'outposts-2019-12-03', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'CancelCapacityTask' => [ 'name' => 'CancelCapacityTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/outposts/{OutpostId}/capacity/{CapacityTaskId}', ], 'input' => [ 'shape' => 'CancelCapacityTaskInput', ], 'output' => [ 'shape' => 'CancelCapacityTaskOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CancelOrder' => [ 'name' => 'CancelOrder', 'http' => [ 'method' => 'POST', 'requestUri' => '/orders/{OrderId}/cancel', ], 'input' => [ 'shape' => 'CancelOrderInput', ], 'output' => [ 'shape' => 'CancelOrderOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateOrder' => [ 'name' => 'CreateOrder', 'http' => [ 'method' => 'POST', 'requestUri' => '/orders', ], 'input' => [ 'shape' => 'CreateOrderInput', ], 'output' => [ 'shape' => 'CreateOrderOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateOutpost' => [ 'name' => 'CreateOutpost', 'http' => [ 'method' => 'POST', 'requestUri' => '/outposts', ], 'input' => [ 'shape' => 'CreateOutpostInput', ], 'output' => [ 'shape' => 'CreateOutpostOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateSite' => [ 'name' => 'CreateSite', 'http' => [ 'method' => 'POST', 'requestUri' => '/sites', ], 'input' => [ 'shape' => 'CreateSiteInput', ], 'output' => [ 'shape' => 'CreateSiteOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'DeleteOutpost' => [ 'name' => 'DeleteOutpost', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/outposts/{OutpostId}', ], 'input' => [ 'shape' => 'DeleteOutpostInput', ], 'output' => [ 'shape' => 'DeleteOutpostOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteSite' => [ 'name' => 'DeleteSite', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/sites/{SiteId}', ], 'input' => [ 'shape' => 'DeleteSiteInput', ], 'output' => [ 'shape' => 'DeleteSiteOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetCapacityTask' => [ 'name' => 'GetCapacityTask', 'http' => [ 'method' => 'GET', 'requestUri' => '/outposts/{OutpostId}/capacity/{CapacityTaskId}', ], 'input' => [ 'shape' => 'GetCapacityTaskInput', ], 'output' => [ 'shape' => 'GetCapacityTaskOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetCatalogItem' => [ 'name' => 'GetCatalogItem', 'http' => [ 'method' => 'GET', 'requestUri' => '/catalog/item/{CatalogItemId}', ], 'input' => [ 'shape' => 'GetCatalogItemInput', ], 'output' => [ 'shape' => 'GetCatalogItemOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetConnection' => [ 'name' => 'GetConnection', 'http' => [ 'method' => 'GET', 'requestUri' => '/connections/{ConnectionId}', ], 'input' => [ 'shape' => 'GetConnectionRequest', ], 'output' => [ 'shape' => 'GetConnectionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetOrder' => [ 'name' => 'GetOrder', 'http' => [ 'method' => 'GET', 'requestUri' => '/orders/{OrderId}', ], 'input' => [ 'shape' => 'GetOrderInput', ], 'output' => [ 'shape' => 'GetOrderOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetOutpost' => [ 'name' => 'GetOutpost', 'http' => [ 'method' => 'GET', 'requestUri' => '/outposts/{OutpostId}', ], 'input' => [ 'shape' => 'GetOutpostInput', ], 'output' => [ 'shape' => 'GetOutpostOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetOutpostInstanceTypes' => [ 'name' => 'GetOutpostInstanceTypes', 'http' => [ 'method' => 'GET', 'requestUri' => '/outposts/{OutpostId}/instanceTypes', ], 'input' => [ 'shape' => 'GetOutpostInstanceTypesInput', ], 'output' => [ 'shape' => 'GetOutpostInstanceTypesOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetOutpostSupportedInstanceTypes' => [ 'name' => 'GetOutpostSupportedInstanceTypes', 'http' => [ 'method' => 'GET', 'requestUri' => '/outposts/{OutpostId}/supportedInstanceTypes', ], 'input' => [ 'shape' => 'GetOutpostSupportedInstanceTypesInput', ], 'output' => [ 'shape' => 'GetOutpostSupportedInstanceTypesOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetSite' => [ 'name' => 'GetSite', 'http' => [ 'method' => 'GET', 'requestUri' => '/sites/{SiteId}', ], 'input' => [ 'shape' => 'GetSiteInput', ], 'output' => [ 'shape' => 'GetSiteOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetSiteAddress' => [ 'name' => 'GetSiteAddress', 'http' => [ 'method' => 'GET', 'requestUri' => '/sites/{SiteId}/address', ], 'input' => [ 'shape' => 'GetSiteAddressInput', ], 'output' => [ 'shape' => 'GetSiteAddressOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListAssetInstances' => [ 'name' => 'ListAssetInstances', 'http' => [ 'method' => 'GET', 'requestUri' => '/outposts/{OutpostId}/assetInstances', ], 'input' => [ 'shape' => 'ListAssetInstancesInput', ], 'output' => [ 'shape' => 'ListAssetInstancesOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListAssets' => [ 'name' => 'ListAssets', 'http' => [ 'method' => 'GET', 'requestUri' => '/outposts/{OutpostId}/assets', ], 'input' => [ 'shape' => 'ListAssetsInput', ], 'output' => [ 'shape' => 'ListAssetsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListBlockingInstancesForCapacityTask' => [ 'name' => 'ListBlockingInstancesForCapacityTask', 'http' => [ 'method' => 'GET', 'requestUri' => '/outposts/{OutpostId}/capacity/{CapacityTaskId}/blockingInstances', ], 'input' => [ 'shape' => 'ListBlockingInstancesForCapacityTaskInput', ], 'output' => [ 'shape' => 'ListBlockingInstancesForCapacityTaskOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListCapacityTasks' => [ 'name' => 'ListCapacityTasks', 'http' => [ 'method' => 'GET', 'requestUri' => '/capacity/tasks', ], 'input' => [ 'shape' => 'ListCapacityTasksInput', ], 'output' => [ 'shape' => 'ListCapacityTasksOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListCatalogItems' => [ 'name' => 'ListCatalogItems', 'http' => [ 'method' => 'GET', 'requestUri' => '/catalog/items', ], 'input' => [ 'shape' => 'ListCatalogItemsInput', ], 'output' => [ 'shape' => 'ListCatalogItemsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListOrders' => [ 'name' => 'ListOrders', 'http' => [ 'method' => 'GET', 'requestUri' => '/list-orders', ], 'input' => [ 'shape' => 'ListOrdersInput', ], 'output' => [ 'shape' => 'ListOrdersOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListOutposts' => [ 'name' => 'ListOutposts', 'http' => [ 'method' => 'GET', 'requestUri' => '/outposts', ], 'input' => [ 'shape' => 'ListOutpostsInput', ], 'output' => [ 'shape' => 'ListOutpostsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListSites' => [ 'name' => 'ListSites', 'http' => [ 'method' => 'GET', 'requestUri' => '/sites', ], 'input' => [ 'shape' => 'ListSitesInput', ], 'output' => [ 'shape' => 'ListSitesOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'NotFoundException', ], ], ], 'StartCapacityTask' => [ 'name' => 'StartCapacityTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/outposts/{OutpostId}/capacity', ], 'input' => [ 'shape' => 'StartCapacityTaskInput', ], 'output' => [ 'shape' => 'StartCapacityTaskOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'StartConnection' => [ 'name' => 'StartConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/connections', ], 'input' => [ 'shape' => 'StartConnectionRequest', ], 'output' => [ 'shape' => 'StartConnectionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{ResourceArn}', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'NotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'NotFoundException', ], ], ], 'UpdateOutpost' => [ 'name' => 'UpdateOutpost', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/outposts/{OutpostId}', ], 'input' => [ 'shape' => 'UpdateOutpostInput', ], 'output' => [ 'shape' => 'UpdateOutpostOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateSite' => [ 'name' => 'UpdateSite', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/sites/{SiteId}', ], 'input' => [ 'shape' => 'UpdateSiteInput', ], 'output' => [ 'shape' => 'UpdateSiteOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateSiteAddress' => [ 'name' => 'UpdateSiteAddress', 'http' => [ 'method' => 'PUT', 'requestUri' => '/sites/{SiteId}/address', ], 'input' => [ 'shape' => 'UpdateSiteAddressInput', ], 'output' => [ 'shape' => 'UpdateSiteAddressOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateSiteRackPhysicalProperties' => [ 'name' => 'UpdateSiteRackPhysicalProperties', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/sites/{SiteId}/rackPhysicalProperties', ], 'input' => [ 'shape' => 'UpdateSiteRackPhysicalPropertiesInput', ], 'output' => [ 'shape' => 'UpdateSiteRackPhysicalPropertiesOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'AWSServiceName' => [ 'type' => 'string', 'enum' => [ 'AWS', 'EC2', 'ELASTICACHE', 'ELB', 'RDS', 'ROUTE53', ], ], 'AWSServiceNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AWSServiceName', ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '\\d{12}', ], 'AccountIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], ], 'Address' => [ 'type' => 'structure', 'required' => [ 'AddressLine1', 'City', 'StateOrRegion', 'PostalCode', 'CountryCode', ], 'members' => [ 'ContactName' => [ 'shape' => 'ContactName', ], 'ContactPhoneNumber' => [ 'shape' => 'ContactPhoneNumber', ], 'AddressLine1' => [ 'shape' => 'AddressLine1', ], 'AddressLine2' => [ 'shape' => 'AddressLine2', ], 'AddressLine3' => [ 'shape' => 'AddressLine3', ], 'City' => [ 'shape' => 'City', ], 'StateOrRegion' => [ 'shape' => 'StateOrRegion', ], 'DistrictOrCounty' => [ 'shape' => 'DistrictOrCounty', ], 'PostalCode' => [ 'shape' => 'PostalCode', ], 'CountryCode' => [ 'shape' => 'CountryCode', ], 'Municipality' => [ 'shape' => 'Municipality', ], ], ], 'AddressLine1' => [ 'type' => 'string', 'max' => 180, 'min' => 1, 'pattern' => '^\\S[\\S ]*$', ], 'AddressLine2' => [ 'type' => 'string', 'max' => 60, 'min' => 0, 'pattern' => '^\\S[\\S ]*$', ], 'AddressLine3' => [ 'type' => 'string', 'max' => 60, 'min' => 0, 'pattern' => '^\\S[\\S ]*$', ], 'AddressType' => [ 'type' => 'string', 'enum' => [ 'SHIPPING_ADDRESS', 'OPERATING_ADDRESS', ], ], 'Arn' => [ 'type' => 'string', 'max' => 1011, 'pattern' => '^(arn:aws([a-z-]+)?:outposts:[a-z\\d-]+:\\d{12}:([a-z\\d-]+)/)[a-z]{2,8}-[a-f0-9]{17}$', ], 'AssetId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^(\\w+)$', ], 'AssetIdInput' => [ 'type' => 'string', 'max' => 10, 'min' => 10, 'pattern' => '\\d{10}', ], 'AssetIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetId', ], ], 'AssetInfo' => [ 'type' => 'structure', 'members' => [ 'AssetId' => [ 'shape' => 'AssetId', ], 'RackId' => [ 'shape' => 'RackId', ], 'AssetType' => [ 'shape' => 'AssetType', ], 'ComputeAttributes' => [ 'shape' => 'ComputeAttributes', ], 'AssetLocation' => [ 'shape' => 'AssetLocation', ], ], ], 'AssetInstance' => [ 'type' => 'structure', 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'InstanceType' => [ 'shape' => 'OutpostInstanceType', ], 'AssetId' => [ 'shape' => 'AssetId', ], 'AccountId' => [ 'shape' => 'AccountId', ], 'AwsServiceName' => [ 'shape' => 'AWSServiceName', ], ], ], 'AssetInstanceCapacityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetInstanceTypeCapacity', ], ], 'AssetInstanceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetInstance', ], ], 'AssetInstanceTypeCapacity' => [ 'type' => 'structure', 'required' => [ 'InstanceType', 'Count', ], 'members' => [ 'InstanceType' => [ 'shape' => 'InstanceTypeName', ], 'Count' => [ 'shape' => 'InstanceTypeCount', ], ], ], 'AssetListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetInfo', ], ], 'AssetLocation' => [ 'type' => 'structure', 'members' => [ 'RackElevation' => [ 'shape' => 'RackElevation', ], ], ], 'AssetState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'RETIRING', 'ISOLATED', ], ], 'AssetType' => [ 'type' => 'string', 'enum' => [ 'COMPUTE', ], ], 'AvailabilityZone' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^([a-zA-Z]+-){1,3}([a-zA-Z]+)?(\\d+[a-zA-Z]?)?$', ], 'AvailabilityZoneId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z]+\\d-[a-zA-Z]+\\d$', ], 'AvailabilityZoneIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AvailabilityZoneId', ], 'max' => 5, 'min' => 1, ], 'AvailabilityZoneList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AvailabilityZone', ], 'max' => 5, 'min' => 1, ], 'BlockingInstance' => [ 'type' => 'structure', 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'AccountId' => [ 'shape' => 'AccountId', ], 'AwsServiceName' => [ 'shape' => 'AWSServiceName', ], ], ], 'BlockingInstancesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BlockingInstance', ], ], 'CIDR' => [ 'type' => 'string', 'max' => 18, 'min' => 9, 'pattern' => '^([0-9]{1,3}\\.){3}[0-9]{1,3}/[0-9]{1,2}$', ], 'CIDRList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CIDR', ], ], 'CancelCapacityTaskInput' => [ 'type' => 'structure', 'required' => [ 'CapacityTaskId', 'OutpostIdentifier', ], 'members' => [ 'CapacityTaskId' => [ 'shape' => 'CapacityTaskId', 'location' => 'uri', 'locationName' => 'CapacityTaskId', ], 'OutpostIdentifier' => [ 'shape' => 'OutpostIdentifier', 'location' => 'uri', 'locationName' => 'OutpostId', ], ], ], 'CancelCapacityTaskOutput' => [ 'type' => 'structure', 'members' => [], ], 'CancelOrderInput' => [ 'type' => 'structure', 'required' => [ 'OrderId', ], 'members' => [ 'OrderId' => [ 'shape' => 'OrderId', 'location' => 'uri', 'locationName' => 'OrderId', ], ], ], 'CancelOrderOutput' => [ 'type' => 'structure', 'members' => [], ], 'CapacityTaskFailure' => [ 'type' => 'structure', 'required' => [ 'Reason', ], 'members' => [ 'Reason' => [ 'shape' => 'CapacityTaskStatusReason', ], 'Type' => [ 'shape' => 'CapacityTaskFailureType', ], ], ], 'CapacityTaskFailureType' => [ 'type' => 'string', 'enum' => [ 'UNSUPPORTED_CAPACITY_CONFIGURATION', 'UNEXPECTED_ASSET_STATE', 'BLOCKING_INSTANCES_NOT_EVACUATED', 'INTERNAL_SERVER_ERROR', 'RESOURCE_NOT_FOUND', ], ], 'CapacityTaskId' => [ 'type' => 'string', 'max' => 21, 'min' => 21, 'pattern' => '^cap-[a-f0-9]{17}$', ], 'CapacityTaskList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CapacityTaskSummary', ], ], 'CapacityTaskStatus' => [ 'type' => 'string', 'enum' => [ 'REQUESTED', 'IN_PROGRESS', 'FAILED', 'COMPLETED', 'WAITING_FOR_EVACUATION', 'CANCELLATION_IN_PROGRESS', 'CANCELLED', ], ], 'CapacityTaskStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CapacityTaskStatus', ], ], 'CapacityTaskStatusReason' => [ 'type' => 'string', 'max' => 128, ], 'CapacityTaskSummary' => [ 'type' => 'structure', 'members' => [ 'CapacityTaskId' => [ 'shape' => 'CapacityTaskId', ], 'OutpostId' => [ 'shape' => 'OutpostId', ], 'OrderId' => [ 'shape' => 'OrderId', ], 'AssetId' => [ 'shape' => 'AssetId', ], 'CapacityTaskStatus' => [ 'shape' => 'CapacityTaskStatus', ], 'CreationDate' => [ 'shape' => 'ISO8601Timestamp', ], 'CompletionDate' => [ 'shape' => 'ISO8601Timestamp', ], 'LastModifiedDate' => [ 'shape' => 'ISO8601Timestamp', ], ], ], 'CatalogItem' => [ 'type' => 'structure', 'members' => [ 'CatalogItemId' => [ 'shape' => 'SkuCode', ], 'ItemStatus' => [ 'shape' => 'CatalogItemStatus', ], 'EC2Capacities' => [ 'shape' => 'EC2CapacityListDefinition', ], 'PowerKva' => [ 'shape' => 'CatalogItemPowerKva', ], 'WeightLbs' => [ 'shape' => 'CatalogItemWeightLbs', ], 'SupportedUplinkGbps' => [ 'shape' => 'SupportedUplinkGbpsListDefinition', ], 'SupportedStorage' => [ 'shape' => 'SupportedStorageList', ], ], ], 'CatalogItemClass' => [ 'type' => 'string', 'enum' => [ 'RACK', 'SERVER', ], ], 'CatalogItemClassList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CatalogItemClass', ], ], 'CatalogItemListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'CatalogItem', ], ], 'CatalogItemPowerKva' => [ 'type' => 'float', 'box' => true, ], 'CatalogItemStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'DISCONTINUED', ], ], 'CatalogItemWeightLbs' => [ 'type' => 'integer', 'box' => true, ], 'City' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '^\\S[\\S ]*$', ], 'CityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'City', ], ], 'ComputeAssetState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'ISOLATED', 'RETIRING', ], ], 'ComputeAttributes' => [ 'type' => 'structure', 'members' => [ 'HostId' => [ 'shape' => 'HostId', ], 'State' => [ 'shape' => 'ComputeAssetState', ], 'InstanceFamilies' => [ 'shape' => 'InstanceFamilies', ], 'InstanceTypeCapacities' => [ 'shape' => 'AssetInstanceCapacityList', ], 'MaxVcpus' => [ 'shape' => 'VCPUCount', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], 'ResourceId' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ConnectionDetails' => [ 'type' => 'structure', 'members' => [ 'ClientPublicKey' => [ 'shape' => 'WireGuardPublicKey', ], 'ServerPublicKey' => [ 'shape' => 'WireGuardPublicKey', ], 'ServerEndpoint' => [ 'shape' => 'ServerEndpoint', ], 'ClientTunnelAddress' => [ 'shape' => 'CIDR', ], 'ServerTunnelAddress' => [ 'shape' => 'CIDR', ], 'AllowedIps' => [ 'shape' => 'CIDRList', ], ], ], 'ConnectionId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^[a-zA-Z0-9+/=]{1,1024}$', ], 'ContactName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^\\S[\\S ]*$', ], 'ContactPhoneNumber' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '^[\\S ]+$', ], 'CountryCode' => [ 'type' => 'string', 'max' => 2, 'min' => 2, 'pattern' => '^[A-Z]{2}$', ], 'CountryCodeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CountryCode', ], ], 'CreateOrderInput' => [ 'type' => 'structure', 'required' => [ 'OutpostIdentifier', 'LineItems', 'PaymentOption', ], 'members' => [ 'OutpostIdentifier' => [ 'shape' => 'OutpostIdentifier', ], 'LineItems' => [ 'shape' => 'LineItemRequestListDefinition', ], 'PaymentOption' => [ 'shape' => 'PaymentOption', ], 'PaymentTerm' => [ 'shape' => 'PaymentTerm', ], ], ], 'CreateOrderOutput' => [ 'type' => 'structure', 'members' => [ 'Order' => [ 'shape' => 'Order', ], ], ], 'CreateOutpostInput' => [ 'type' => 'structure', 'required' => [ 'Name', 'SiteId', ], 'members' => [ 'Name' => [ 'shape' => 'OutpostName', ], 'Description' => [ 'shape' => 'OutpostDescription', ], 'SiteId' => [ 'shape' => 'SiteId', ], 'AvailabilityZone' => [ 'shape' => 'AvailabilityZone', ], 'AvailabilityZoneId' => [ 'shape' => 'AvailabilityZoneId', ], 'Tags' => [ 'shape' => 'TagMap', ], 'SupportedHardwareType' => [ 'shape' => 'SupportedHardwareType', ], ], ], 'CreateOutpostOutput' => [ 'type' => 'structure', 'members' => [ 'Outpost' => [ 'shape' => 'Outpost', ], ], ], 'CreateSiteInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'SiteName', ], 'Description' => [ 'shape' => 'SiteDescription', ], 'Notes' => [ 'shape' => 'SiteNotes', ], 'Tags' => [ 'shape' => 'TagMap', ], 'OperatingAddress' => [ 'shape' => 'Address', ], 'ShippingAddress' => [ 'shape' => 'Address', ], 'RackPhysicalProperties' => [ 'shape' => 'RackPhysicalProperties', ], ], ], 'CreateSiteOutput' => [ 'type' => 'structure', 'members' => [ 'Site' => [ 'shape' => 'Site', ], ], ], 'DeleteOutpostInput' => [ 'type' => 'structure', 'required' => [ 'OutpostId', ], 'members' => [ 'OutpostId' => [ 'shape' => 'OutpostId', 'location' => 'uri', 'locationName' => 'OutpostId', ], ], ], 'DeleteOutpostOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSiteInput' => [ 'type' => 'structure', 'required' => [ 'SiteId', ], 'members' => [ 'SiteId' => [ 'shape' => 'SiteId', 'location' => 'uri', 'locationName' => 'SiteId', ], ], ], 'DeleteSiteOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeviceSerialNumber' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^(\\w+)$', ], 'DistrictOrCounty' => [ 'type' => 'string', 'max' => 60, 'min' => 1, 'pattern' => '^\\S[\\S ]*', ], 'DryRun' => [ 'type' => 'boolean', ], 'EC2Capacity' => [ 'type' => 'structure', 'members' => [ 'Family' => [ 'shape' => 'Family', ], 'MaxSize' => [ 'shape' => 'MaxSize', ], 'Quantity' => [ 'shape' => 'Quantity', ], ], ], 'EC2CapacityListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'EC2Capacity', ], ], 'EC2FamilyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Family', ], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^[\\S \\n]+$', ], 'Family' => [ 'type' => 'string', 'max' => 10, 'min' => 1, 'pattern' => '[a-z0-9]+', ], 'FiberOpticCableType' => [ 'type' => 'string', 'enum' => [ 'SINGLE_MODE', 'MULTI_MODE', ], ], 'GetCapacityTaskInput' => [ 'type' => 'structure', 'required' => [ 'CapacityTaskId', 'OutpostIdentifier', ], 'members' => [ 'CapacityTaskId' => [ 'shape' => 'CapacityTaskId', 'location' => 'uri', 'locationName' => 'CapacityTaskId', ], 'OutpostIdentifier' => [ 'shape' => 'OutpostIdentifier', 'location' => 'uri', 'locationName' => 'OutpostId', ], ], ], 'GetCapacityTaskOutput' => [ 'type' => 'structure', 'members' => [ 'CapacityTaskId' => [ 'shape' => 'CapacityTaskId', ], 'OutpostId' => [ 'shape' => 'OutpostId', ], 'OrderId' => [ 'shape' => 'OrderId', ], 'AssetId' => [ 'shape' => 'AssetId', ], 'RequestedInstancePools' => [ 'shape' => 'RequestedInstancePools', ], 'InstancesToExclude' => [ 'shape' => 'InstancesToExclude', ], 'DryRun' => [ 'shape' => 'DryRun', ], 'CapacityTaskStatus' => [ 'shape' => 'CapacityTaskStatus', ], 'Failed' => [ 'shape' => 'CapacityTaskFailure', ], 'CreationDate' => [ 'shape' => 'ISO8601Timestamp', ], 'CompletionDate' => [ 'shape' => 'ISO8601Timestamp', ], 'LastModifiedDate' => [ 'shape' => 'ISO8601Timestamp', ], 'TaskActionOnBlockingInstances' => [ 'shape' => 'TaskActionOnBlockingInstances', ], ], ], 'GetCatalogItemInput' => [ 'type' => 'structure', 'required' => [ 'CatalogItemId', ], 'members' => [ 'CatalogItemId' => [ 'shape' => 'SkuCode', 'location' => 'uri', 'locationName' => 'CatalogItemId', ], ], ], 'GetCatalogItemOutput' => [ 'type' => 'structure', 'members' => [ 'CatalogItem' => [ 'shape' => 'CatalogItem', ], ], ], 'GetConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectionId', ], 'members' => [ 'ConnectionId' => [ 'shape' => 'ConnectionId', 'location' => 'uri', 'locationName' => 'ConnectionId', ], ], ], 'GetConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectionId' => [ 'shape' => 'ConnectionId', ], 'ConnectionDetails' => [ 'shape' => 'ConnectionDetails', ], ], ], 'GetOrderInput' => [ 'type' => 'structure', 'required' => [ 'OrderId', ], 'members' => [ 'OrderId' => [ 'shape' => 'OrderId', 'location' => 'uri', 'locationName' => 'OrderId', ], ], ], 'GetOrderOutput' => [ 'type' => 'structure', 'members' => [ 'Order' => [ 'shape' => 'Order', ], ], ], 'GetOutpostInput' => [ 'type' => 'structure', 'required' => [ 'OutpostId', ], 'members' => [ 'OutpostId' => [ 'shape' => 'OutpostId', 'location' => 'uri', 'locationName' => 'OutpostId', ], ], ], 'GetOutpostInstanceTypesInput' => [ 'type' => 'structure', 'required' => [ 'OutpostId', ], 'members' => [ 'OutpostId' => [ 'shape' => 'OutpostId', 'location' => 'uri', 'locationName' => 'OutpostId', ], 'NextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults1000', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'GetOutpostInstanceTypesOutput' => [ 'type' => 'structure', 'members' => [ 'InstanceTypes' => [ 'shape' => 'InstanceTypeListDefinition', ], 'NextToken' => [ 'shape' => 'Token', ], 'OutpostId' => [ 'shape' => 'OutpostId', ], 'OutpostArn' => [ 'shape' => 'OutpostArn', ], ], ], 'GetOutpostOutput' => [ 'type' => 'structure', 'members' => [ 'Outpost' => [ 'shape' => 'Outpost', ], ], ], 'GetOutpostSupportedInstanceTypesInput' => [ 'type' => 'structure', 'required' => [ 'OutpostIdentifier', ], 'members' => [ 'OutpostIdentifier' => [ 'shape' => 'OutpostIdentifier', 'location' => 'uri', 'locationName' => 'OutpostId', ], 'OrderId' => [ 'shape' => 'OrderId', 'location' => 'querystring', 'locationName' => 'OrderId', ], 'AssetId' => [ 'shape' => 'AssetIdInput', 'location' => 'querystring', 'locationName' => 'AssetId', ], 'MaxResults' => [ 'shape' => 'MaxResults1000', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'GetOutpostSupportedInstanceTypesOutput' => [ 'type' => 'structure', 'members' => [ 'InstanceTypes' => [ 'shape' => 'InstanceTypeListDefinition', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetSiteAddressInput' => [ 'type' => 'structure', 'required' => [ 'SiteId', 'AddressType', ], 'members' => [ 'SiteId' => [ 'shape' => 'SiteId', 'location' => 'uri', 'locationName' => 'SiteId', ], 'AddressType' => [ 'shape' => 'AddressType', 'location' => 'querystring', 'locationName' => 'AddressType', ], ], ], 'GetSiteAddressOutput' => [ 'type' => 'structure', 'members' => [ 'SiteId' => [ 'shape' => 'SiteId', ], 'AddressType' => [ 'shape' => 'AddressType', ], 'Address' => [ 'shape' => 'Address', ], ], ], 'GetSiteInput' => [ 'type' => 'structure', 'required' => [ 'SiteId', ], 'members' => [ 'SiteId' => [ 'shape' => 'SiteId', 'location' => 'uri', 'locationName' => 'SiteId', ], ], ], 'GetSiteOutput' => [ 'type' => 'structure', 'members' => [ 'Site' => [ 'shape' => 'Site', ], ], ], 'HostId' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '^[A-Za-z0-9-]*$', ], 'HostIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HostId', ], ], 'ISO8601Timestamp' => [ 'type' => 'timestamp', ], 'InstanceFamilies' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceFamilyName', ], ], 'InstanceFamilyName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^(?:.{1,200}/)?(?:[a-z0-9-_A-Z])+$', ], 'InstanceId' => [ 'type' => 'string', 'max' => 32, 'min' => 11, 'pattern' => '^i-[0-9a-z]+$', ], 'InstanceIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceId', ], ], 'InstanceType' => [ 'type' => 'string', ], 'InstanceTypeCapacity' => [ 'type' => 'structure', 'required' => [ 'InstanceType', 'Count', ], 'members' => [ 'InstanceType' => [ 'shape' => 'InstanceTypeName', ], 'Count' => [ 'shape' => 'InstanceTypeCount', ], ], ], 'InstanceTypeCount' => [ 'type' => 'integer', 'max' => 9999, 'min' => 0, ], 'InstanceTypeItem' => [ 'type' => 'structure', 'members' => [ 'InstanceType' => [ 'shape' => 'InstanceType', ], 'VCPUs' => [ 'shape' => 'VCPUCount', ], ], ], 'InstanceTypeListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceTypeItem', ], ], 'InstanceTypeName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-z0-9\\-]+\\.[a-z0-9\\-]+$', ], 'InstancesToExclude' => [ 'type' => 'structure', 'members' => [ 'Instances' => [ 'shape' => 'InstanceIdList', ], 'AccountIds' => [ 'shape' => 'AccountIdList', ], 'Services' => [ 'shape' => 'AWSServiceNameList', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'LifeCycleStatus' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '^[ A-Za-z]+$', ], 'LifeCycleStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LifeCycleStatus', ], 'max' => 5, 'min' => 1, ], 'LineItem' => [ 'type' => 'structure', 'members' => [ 'CatalogItemId' => [ 'shape' => 'SkuCode', ], 'LineItemId' => [ 'shape' => 'LineItemId', ], 'Quantity' => [ 'shape' => 'LineItemQuantity', ], 'Status' => [ 'shape' => 'LineItemStatus', ], 'ShipmentInformation' => [ 'shape' => 'ShipmentInformation', ], 'AssetInformationList' => [ 'shape' => 'LineItemAssetInformationList', ], 'PreviousLineItemId' => [ 'shape' => 'LineItemId', ], 'PreviousOrderId' => [ 'shape' => 'OrderId', ], ], ], 'LineItemAssetInformation' => [ 'type' => 'structure', 'members' => [ 'AssetId' => [ 'shape' => 'AssetId', ], 'MacAddressList' => [ 'shape' => 'MacAddressList', ], ], ], 'LineItemAssetInformationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LineItemAssetInformation', ], ], 'LineItemId' => [ 'type' => 'string', 'pattern' => 'ooi-[a-f0-9]{17}', ], 'LineItemListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'LineItem', ], ], 'LineItemQuantity' => [ 'type' => 'integer', 'min' => 1, ], 'LineItemRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogItemId' => [ 'shape' => 'SkuCode', ], 'Quantity' => [ 'shape' => 'LineItemQuantity', ], ], ], 'LineItemRequestListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'LineItemRequest', ], 'max' => 20, 'min' => 1, ], 'LineItemStatus' => [ 'type' => 'string', 'enum' => [ 'PREPARING', 'BUILDING', 'SHIPPED', 'DELIVERED', 'INSTALLING', 'INSTALLED', 'ERROR', 'CANCELLED', 'REPLACED', ], ], 'LineItemStatusCounts' => [ 'type' => 'map', 'key' => [ 'shape' => 'LineItemStatus', ], 'value' => [ 'shape' => 'LineItemQuantity', ], ], 'ListAssetInstancesInput' => [ 'type' => 'structure', 'required' => [ 'OutpostIdentifier', ], 'members' => [ 'OutpostIdentifier' => [ 'shape' => 'OutpostIdentifier', 'location' => 'uri', 'locationName' => 'OutpostId', ], 'AssetIdFilter' => [ 'shape' => 'AssetIdList', 'location' => 'querystring', 'locationName' => 'AssetIdFilter', ], 'InstanceTypeFilter' => [ 'shape' => 'OutpostInstanceTypeList', 'location' => 'querystring', 'locationName' => 'InstanceTypeFilter', ], 'AccountIdFilter' => [ 'shape' => 'AccountIdList', 'location' => 'querystring', 'locationName' => 'AccountIdFilter', ], 'AwsServiceFilter' => [ 'shape' => 'AWSServiceNameList', 'location' => 'querystring', 'locationName' => 'AwsServiceFilter', ], 'MaxResults' => [ 'shape' => 'MaxResults1000', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListAssetInstancesOutput' => [ 'type' => 'structure', 'members' => [ 'AssetInstances' => [ 'shape' => 'AssetInstanceList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListAssetsInput' => [ 'type' => 'structure', 'required' => [ 'OutpostIdentifier', ], 'members' => [ 'OutpostIdentifier' => [ 'shape' => 'OutpostIdentifier', 'location' => 'uri', 'locationName' => 'OutpostId', ], 'HostIdFilter' => [ 'shape' => 'HostIdList', 'location' => 'querystring', 'locationName' => 'HostIdFilter', ], 'MaxResults' => [ 'shape' => 'MaxResults1000', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'StatusFilter' => [ 'shape' => 'StatusList', 'location' => 'querystring', 'locationName' => 'StatusFilter', ], ], ], 'ListAssetsOutput' => [ 'type' => 'structure', 'members' => [ 'Assets' => [ 'shape' => 'AssetListDefinition', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListBlockingInstancesForCapacityTaskInput' => [ 'type' => 'structure', 'required' => [ 'OutpostIdentifier', 'CapacityTaskId', ], 'members' => [ 'OutpostIdentifier' => [ 'shape' => 'OutpostIdentifier', 'location' => 'uri', 'locationName' => 'OutpostId', ], 'CapacityTaskId' => [ 'shape' => 'CapacityTaskId', 'location' => 'uri', 'locationName' => 'CapacityTaskId', ], 'MaxResults' => [ 'shape' => 'MaxResults1000', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListBlockingInstancesForCapacityTaskOutput' => [ 'type' => 'structure', 'members' => [ 'BlockingInstances' => [ 'shape' => 'BlockingInstancesList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListCapacityTasksInput' => [ 'type' => 'structure', 'members' => [ 'OutpostIdentifierFilter' => [ 'shape' => 'OutpostIdentifier', 'location' => 'querystring', 'locationName' => 'OutpostIdentifierFilter', ], 'MaxResults' => [ 'shape' => 'MaxResults1000', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'CapacityTaskStatusFilter' => [ 'shape' => 'CapacityTaskStatusList', 'location' => 'querystring', 'locationName' => 'CapacityTaskStatusFilter', ], ], ], 'ListCapacityTasksOutput' => [ 'type' => 'structure', 'members' => [ 'CapacityTasks' => [ 'shape' => 'CapacityTaskList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListCatalogItemsInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults1000', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'ItemClassFilter' => [ 'shape' => 'CatalogItemClassList', 'location' => 'querystring', 'locationName' => 'ItemClassFilter', ], 'SupportedStorageFilter' => [ 'shape' => 'SupportedStorageList', 'location' => 'querystring', 'locationName' => 'SupportedStorageFilter', ], 'EC2FamilyFilter' => [ 'shape' => 'EC2FamilyList', 'location' => 'querystring', 'locationName' => 'EC2FamilyFilter', ], ], ], 'ListCatalogItemsOutput' => [ 'type' => 'structure', 'members' => [ 'CatalogItems' => [ 'shape' => 'CatalogItemListDefinition', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListOrdersInput' => [ 'type' => 'structure', 'members' => [ 'OutpostIdentifierFilter' => [ 'shape' => 'OutpostIdentifier', 'location' => 'querystring', 'locationName' => 'OutpostIdentifierFilter', ], 'NextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults1000', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListOrdersOutput' => [ 'type' => 'structure', 'members' => [ 'Orders' => [ 'shape' => 'OrderSummaryListDefinition', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListOutpostsInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults1000', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'LifeCycleStatusFilter' => [ 'shape' => 'LifeCycleStatusList', 'location' => 'querystring', 'locationName' => 'LifeCycleStatusFilter', ], 'AvailabilityZoneFilter' => [ 'shape' => 'AvailabilityZoneList', 'location' => 'querystring', 'locationName' => 'AvailabilityZoneFilter', ], 'AvailabilityZoneIdFilter' => [ 'shape' => 'AvailabilityZoneIdList', 'location' => 'querystring', 'locationName' => 'AvailabilityZoneIdFilter', ], ], ], 'ListOutpostsOutput' => [ 'type' => 'structure', 'members' => [ 'Outposts' => [ 'shape' => 'outpostListDefinition', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListSitesInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults1000', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'OperatingAddressCountryCodeFilter' => [ 'shape' => 'CountryCodeList', 'location' => 'querystring', 'locationName' => 'OperatingAddressCountryCodeFilter', ], 'OperatingAddressStateOrRegionFilter' => [ 'shape' => 'StateOrRegionList', 'location' => 'querystring', 'locationName' => 'OperatingAddressStateOrRegionFilter', ], 'OperatingAddressCityFilter' => [ 'shape' => 'CityList', 'location' => 'querystring', 'locationName' => 'OperatingAddressCityFilter', ], ], ], 'ListSitesOutput' => [ 'type' => 'structure', 'members' => [ 'Sites' => [ 'shape' => 'siteListDefinition', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'MacAddress' => [ 'type' => 'string', 'max' => 17, 'min' => 17, 'pattern' => '^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$', ], 'MacAddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MacAddress', ], ], 'MaxResults1000' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'MaxSize' => [ 'type' => 'string', ], 'MaximumSupportedWeightLbs' => [ 'type' => 'string', 'enum' => [ 'NO_LIMIT', 'MAX_1400_LBS', 'MAX_1600_LBS', 'MAX_1800_LBS', 'MAX_2000_LBS', ], ], 'Municipality' => [ 'type' => 'string', 'max' => 180, 'min' => 0, 'pattern' => '^\\S[\\S ]*$', ], 'NetworkInterfaceDeviceIndex' => [ 'type' => 'integer', 'max' => 1, 'min' => 0, ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'OpticalStandard' => [ 'type' => 'string', 'enum' => [ 'OPTIC_10GBASE_SR', 'OPTIC_10GBASE_IR', 'OPTIC_10GBASE_LR', 'OPTIC_40GBASE_SR', 'OPTIC_40GBASE_ESR', 'OPTIC_40GBASE_IR4_LR4L', 'OPTIC_40GBASE_LR4', 'OPTIC_100GBASE_SR4', 'OPTIC_100GBASE_CWDM4', 'OPTIC_100GBASE_LR4', 'OPTIC_100G_PSM4_MSA', 'OPTIC_1000BASE_LX', 'OPTIC_1000BASE_SX', ], ], 'Order' => [ 'type' => 'structure', 'members' => [ 'OutpostId' => [ 'shape' => 'OutpostIdOnly', ], 'OrderId' => [ 'shape' => 'OrderId', ], 'Status' => [ 'shape' => 'OrderStatus', ], 'LineItems' => [ 'shape' => 'LineItemListDefinition', ], 'PaymentOption' => [ 'shape' => 'PaymentOption', ], 'OrderSubmissionDate' => [ 'shape' => 'ISO8601Timestamp', ], 'OrderFulfilledDate' => [ 'shape' => 'ISO8601Timestamp', ], 'PaymentTerm' => [ 'shape' => 'PaymentTerm', ], 'OrderType' => [ 'shape' => 'OrderType', ], ], ], 'OrderId' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => 'oo-[a-f0-9]{17}$', ], 'OrderStatus' => [ 'type' => 'string', 'enum' => [ 'RECEIVED', 'PENDING', 'PROCESSING', 'INSTALLING', 'FULFILLED', 'CANCELLED', 'PREPARING', 'IN_PROGRESS', 'DELIVERED', 'COMPLETED', 'ERROR', ], ], 'OrderSummary' => [ 'type' => 'structure', 'members' => [ 'OutpostId' => [ 'shape' => 'OutpostIdOnly', ], 'OrderId' => [ 'shape' => 'OrderId', ], 'OrderType' => [ 'shape' => 'OrderType', ], 'Status' => [ 'shape' => 'OrderStatus', ], 'LineItemCountsByStatus' => [ 'shape' => 'LineItemStatusCounts', ], 'OrderSubmissionDate' => [ 'shape' => 'ISO8601Timestamp', ], 'OrderFulfilledDate' => [ 'shape' => 'ISO8601Timestamp', ], ], ], 'OrderSummaryListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrderSummary', ], ], 'OrderType' => [ 'type' => 'string', 'enum' => [ 'OUTPOST', 'REPLACEMENT', ], ], 'Outpost' => [ 'type' => 'structure', 'members' => [ 'OutpostId' => [ 'shape' => 'OutpostId', ], 'OwnerId' => [ 'shape' => 'OwnerId', ], 'OutpostArn' => [ 'shape' => 'OutpostArn', ], 'SiteId' => [ 'shape' => 'SiteId', ], 'Name' => [ 'shape' => 'OutpostName', ], 'Description' => [ 'shape' => 'OutpostDescription', ], 'LifeCycleStatus' => [ 'shape' => 'LifeCycleStatus', ], 'AvailabilityZone' => [ 'shape' => 'AvailabilityZone', ], 'AvailabilityZoneId' => [ 'shape' => 'AvailabilityZoneId', ], 'Tags' => [ 'shape' => 'TagMap', ], 'SiteArn' => [ 'shape' => 'SiteArn', ], 'SupportedHardwareType' => [ 'shape' => 'SupportedHardwareType', ], ], ], 'OutpostArn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^arn:aws([a-z-]+)?:outposts:[a-z\\d-]+:\\d{12}:outpost/op-[a-f0-9]{17}$', ], 'OutpostDescription' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'pattern' => '^[\\S ]*$', ], 'OutpostId' => [ 'type' => 'string', 'max' => 180, 'min' => 1, 'pattern' => '^(arn:aws([a-z-]+)?:outposts:[a-z\\d-]+:\\d{12}:outpost/)?op-[a-f0-9]{17}$', ], 'OutpostIdOnly' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '^op-[a-f0-9]{17}$', ], 'OutpostIdentifier' => [ 'type' => 'string', 'max' => 180, 'min' => 1, 'pattern' => '^(arn:aws([a-z-]+)?:outposts:[a-z\\d-]+:\\d{12}:outpost/)?op-[a-f0-9]{17}$', ], 'OutpostInstanceType' => [ 'type' => 'string', 'max' => 30, 'min' => 3, 'pattern' => '[a-z0-9\\-\\.]+', ], 'OutpostInstanceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutpostInstanceType', ], ], 'OutpostName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[\\S ]+$', ], 'OwnerId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '\\d{12}', ], 'PaymentOption' => [ 'type' => 'string', 'enum' => [ 'ALL_UPFRONT', 'NO_UPFRONT', 'PARTIAL_UPFRONT', ], ], 'PaymentTerm' => [ 'type' => 'string', 'enum' => [ 'THREE_YEARS', 'ONE_YEAR', 'FIVE_YEARS', ], ], 'PostalCode' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '^[a-zA-Z0-9 -]+$', ], 'PowerConnector' => [ 'type' => 'string', 'enum' => [ 'L6_30P', 'IEC309', 'AH530P7W', 'AH532P6W', 'CS8365C', ], ], 'PowerDrawKva' => [ 'type' => 'string', 'enum' => [ 'POWER_5_KVA', 'POWER_10_KVA', 'POWER_15_KVA', 'POWER_30_KVA', ], ], 'PowerFeedDrop' => [ 'type' => 'string', 'enum' => [ 'ABOVE_RACK', 'BELOW_RACK', ], ], 'PowerPhase' => [ 'type' => 'string', 'enum' => [ 'SINGLE_PHASE', 'THREE_PHASE', ], ], 'Quantity' => [ 'type' => 'string', ], 'RackElevation' => [ 'type' => 'float', 'box' => true, 'max' => 99, 'min' => 0, ], 'RackId' => [ 'type' => 'string', 'max' => 20, 'min' => 5, 'pattern' => '^[\\S \\n]+$', ], 'RackPhysicalProperties' => [ 'type' => 'structure', 'members' => [ 'PowerDrawKva' => [ 'shape' => 'PowerDrawKva', ], 'PowerPhase' => [ 'shape' => 'PowerPhase', ], 'PowerConnector' => [ 'shape' => 'PowerConnector', ], 'PowerFeedDrop' => [ 'shape' => 'PowerFeedDrop', ], 'UplinkGbps' => [ 'shape' => 'UplinkGbps', ], 'UplinkCount' => [ 'shape' => 'UplinkCount', ], 'FiberOpticCableType' => [ 'shape' => 'FiberOpticCableType', ], 'OpticalStandard' => [ 'shape' => 'OpticalStandard', ], 'MaximumSupportedWeightLbs' => [ 'shape' => 'MaximumSupportedWeightLbs', ], ], ], 'RequestedInstancePools' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceTypeCapacity', ], ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'OUTPOST', 'ORDER', ], ], 'ServerEndpoint' => [ 'type' => 'string', 'max' => 21, 'min' => 9, 'pattern' => '^([0-9]{1,3}\\.){3}[0-9]{1,3}:[0-9]{1,5}$', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 402, ], 'exception' => true, ], 'ShipmentCarrier' => [ 'type' => 'string', 'enum' => [ 'DHL', 'DBS', 'FEDEX', 'UPS', 'EXPEDITORS', ], ], 'ShipmentInformation' => [ 'type' => 'structure', 'members' => [ 'ShipmentTrackingNumber' => [ 'shape' => 'TrackingId', ], 'ShipmentCarrier' => [ 'shape' => 'ShipmentCarrier', ], ], ], 'Site' => [ 'type' => 'structure', 'members' => [ 'SiteId' => [ 'shape' => 'SiteId', ], 'AccountId' => [ 'shape' => 'AccountId', ], 'Name' => [ 'shape' => 'SiteName', ], 'Description' => [ 'shape' => 'SiteDescription', ], 'Tags' => [ 'shape' => 'TagMap', ], 'SiteArn' => [ 'shape' => 'SiteArn', ], 'Notes' => [ 'shape' => 'SiteNotes', ], 'OperatingAddressCountryCode' => [ 'shape' => 'CountryCode', ], 'OperatingAddressStateOrRegion' => [ 'shape' => 'StateOrRegion', ], 'OperatingAddressCity' => [ 'shape' => 'City', ], 'RackPhysicalProperties' => [ 'shape' => 'RackPhysicalProperties', ], ], ], 'SiteArn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^arn:aws([a-z-]+)?:outposts:[a-z\\d-]+:\\d{12}:site/(os-[a-f0-9]{17})$', ], 'SiteDescription' => [ 'type' => 'string', 'max' => 1001, 'min' => 1, 'pattern' => '^[\\S ]+$', ], 'SiteId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(arn:aws([a-z-]+)?:outposts:[a-z\\d-]+:\\d{12}:site/)?(os-[a-f0-9]{17})$', ], 'SiteName' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^[\\S ]+$', ], 'SiteNotes' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, 'pattern' => '^[\\S \\n]+$', ], 'SkuCode' => [ 'type' => 'string', 'max' => 10, 'min' => 1, 'pattern' => 'OR-[A-Z0-9]{7}', ], 'StartCapacityTaskInput' => [ 'type' => 'structure', 'required' => [ 'OutpostIdentifier', 'InstancePools', ], 'members' => [ 'OutpostIdentifier' => [ 'shape' => 'OutpostIdentifier', 'location' => 'uri', 'locationName' => 'OutpostId', ], 'OrderId' => [ 'shape' => 'OrderId', ], 'AssetId' => [ 'shape' => 'AssetIdInput', ], 'InstancePools' => [ 'shape' => 'RequestedInstancePools', ], 'InstancesToExclude' => [ 'shape' => 'InstancesToExclude', ], 'DryRun' => [ 'shape' => 'DryRun', ], 'TaskActionOnBlockingInstances' => [ 'shape' => 'TaskActionOnBlockingInstances', ], ], ], 'StartCapacityTaskOutput' => [ 'type' => 'structure', 'members' => [ 'CapacityTaskId' => [ 'shape' => 'CapacityTaskId', ], 'OutpostId' => [ 'shape' => 'OutpostId', ], 'OrderId' => [ 'shape' => 'OrderId', ], 'AssetId' => [ 'shape' => 'AssetId', ], 'RequestedInstancePools' => [ 'shape' => 'RequestedInstancePools', ], 'InstancesToExclude' => [ 'shape' => 'InstancesToExclude', ], 'DryRun' => [ 'shape' => 'DryRun', ], 'CapacityTaskStatus' => [ 'shape' => 'CapacityTaskStatus', ], 'Failed' => [ 'shape' => 'CapacityTaskFailure', ], 'CreationDate' => [ 'shape' => 'ISO8601Timestamp', ], 'CompletionDate' => [ 'shape' => 'ISO8601Timestamp', ], 'LastModifiedDate' => [ 'shape' => 'ISO8601Timestamp', ], 'TaskActionOnBlockingInstances' => [ 'shape' => 'TaskActionOnBlockingInstances', ], ], ], 'StartConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'AssetId', 'ClientPublicKey', 'NetworkInterfaceDeviceIndex', ], 'members' => [ 'DeviceSerialNumber' => [ 'shape' => 'DeviceSerialNumber', ], 'AssetId' => [ 'shape' => 'AssetId', ], 'ClientPublicKey' => [ 'shape' => 'WireGuardPublicKey', ], 'NetworkInterfaceDeviceIndex' => [ 'shape' => 'NetworkInterfaceDeviceIndex', ], ], ], 'StartConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectionId' => [ 'shape' => 'ConnectionId', ], 'UnderlayIpAddress' => [ 'shape' => 'UnderlayIpAddress', ], ], ], 'StateOrRegion' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '^\\S[\\S ]*$', ], 'StateOrRegionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StateOrRegion', ], ], 'StatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetState', ], 'max' => 3, 'min' => 1, ], 'String' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^[\\S \\n]+$', ], 'SupportedHardwareType' => [ 'type' => 'string', 'enum' => [ 'RACK', 'SERVER', ], ], 'SupportedStorageEnum' => [ 'type' => 'string', 'enum' => [ 'EBS', 'S3', ], ], 'SupportedStorageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SupportedStorageEnum', ], ], 'SupportedUplinkGbps' => [ 'type' => 'integer', ], 'SupportedUplinkGbpsListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'SupportedUplinkGbps', ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[a-zA-Z+-=._:/]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^[\\S \\n]+$', ], 'TaskActionOnBlockingInstances' => [ 'type' => 'string', 'enum' => [ 'WAIT_FOR_EVACUATION', 'FAIL_TASK', ], ], 'Token' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(\\d+)##(\\S+)$', ], 'TrackingId' => [ 'type' => 'string', 'max' => 42, 'min' => 6, 'pattern' => '^[a-zA-Z0-9]+$', ], 'UnderlayIpAddress' => [ 'type' => 'string', 'max' => 15, 'min' => 7, 'pattern' => '^([0-9]{1,3}\\.){3}[0-9]{1,3}$', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateOutpostInput' => [ 'type' => 'structure', 'required' => [ 'OutpostId', ], 'members' => [ 'OutpostId' => [ 'shape' => 'OutpostId', 'location' => 'uri', 'locationName' => 'OutpostId', ], 'Name' => [ 'shape' => 'OutpostName', ], 'Description' => [ 'shape' => 'OutpostDescription', ], 'SupportedHardwareType' => [ 'shape' => 'SupportedHardwareType', ], ], ], 'UpdateOutpostOutput' => [ 'type' => 'structure', 'members' => [ 'Outpost' => [ 'shape' => 'Outpost', ], ], ], 'UpdateSiteAddressInput' => [ 'type' => 'structure', 'required' => [ 'SiteId', 'AddressType', 'Address', ], 'members' => [ 'SiteId' => [ 'shape' => 'SiteId', 'location' => 'uri', 'locationName' => 'SiteId', ], 'AddressType' => [ 'shape' => 'AddressType', ], 'Address' => [ 'shape' => 'Address', ], ], ], 'UpdateSiteAddressOutput' => [ 'type' => 'structure', 'members' => [ 'AddressType' => [ 'shape' => 'AddressType', ], 'Address' => [ 'shape' => 'Address', ], ], ], 'UpdateSiteInput' => [ 'type' => 'structure', 'required' => [ 'SiteId', ], 'members' => [ 'SiteId' => [ 'shape' => 'SiteId', 'location' => 'uri', 'locationName' => 'SiteId', ], 'Name' => [ 'shape' => 'SiteName', ], 'Description' => [ 'shape' => 'SiteDescription', ], 'Notes' => [ 'shape' => 'SiteNotes', ], ], ], 'UpdateSiteOutput' => [ 'type' => 'structure', 'members' => [ 'Site' => [ 'shape' => 'Site', ], ], ], 'UpdateSiteRackPhysicalPropertiesInput' => [ 'type' => 'structure', 'required' => [ 'SiteId', ], 'members' => [ 'SiteId' => [ 'shape' => 'SiteId', 'location' => 'uri', 'locationName' => 'SiteId', ], 'PowerDrawKva' => [ 'shape' => 'PowerDrawKva', ], 'PowerPhase' => [ 'shape' => 'PowerPhase', ], 'PowerConnector' => [ 'shape' => 'PowerConnector', ], 'PowerFeedDrop' => [ 'shape' => 'PowerFeedDrop', ], 'UplinkGbps' => [ 'shape' => 'UplinkGbps', ], 'UplinkCount' => [ 'shape' => 'UplinkCount', ], 'FiberOpticCableType' => [ 'shape' => 'FiberOpticCableType', ], 'OpticalStandard' => [ 'shape' => 'OpticalStandard', ], 'MaximumSupportedWeightLbs' => [ 'shape' => 'MaximumSupportedWeightLbs', ], ], ], 'UpdateSiteRackPhysicalPropertiesOutput' => [ 'type' => 'structure', 'members' => [ 'Site' => [ 'shape' => 'Site', ], ], ], 'UplinkCount' => [ 'type' => 'string', 'enum' => [ 'UPLINK_COUNT_1', 'UPLINK_COUNT_2', 'UPLINK_COUNT_3', 'UPLINK_COUNT_4', 'UPLINK_COUNT_5', 'UPLINK_COUNT_6', 'UPLINK_COUNT_7', 'UPLINK_COUNT_8', 'UPLINK_COUNT_12', 'UPLINK_COUNT_16', ], ], 'UplinkGbps' => [ 'type' => 'string', 'enum' => [ 'UPLINK_1G', 'UPLINK_10G', 'UPLINK_40G', 'UPLINK_100G', ], ], 'VCPUCount' => [ 'type' => 'integer', 'box' => true, ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'WireGuardPublicKey' => [ 'type' => 'string', 'max' => 44, 'min' => 44, 'pattern' => '^[a-zA-Z0-9/+]{43}=$', ], 'outpostListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'Outpost', ], ], 'siteListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'Site', ], ], ],];
