<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Organization Public API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\PreviewIam\Versionless\Organization;

use Twilio\Exceptions\TwilioException;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\InstanceContext;


class UserContext extends InstanceContext
    {
    /**
     * Initialize the UserContext
     *
     * @param Version $version Version that contains the resource
     * @param string $organizationSid
     * @param string $id
     */
    public function __construct(
        Version $version,
        $organizationSid,
        $id
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'organizationSid' =>
            $organizationSid,
        'id' =>
            $id,
        ];

        $this->uri = '/' . \rawurlencode($organizationSid)
        .'/scim/Users/' . \rawurlencode($id)
        .'';
    }

    /**
     * Delete the UserInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded' ]);
        return $this->version->delete('DELETE', $this->uri, [], [], $headers);
    }


    /**
     * Fetch the UserInstance
     *
     * @return UserInstance Fetched UserInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): UserInstance
    {

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/scim+json' ]);
        $payload = $this->version->fetch('GET', $this->uri, [], [], $headers);

        return new UserInstance(
            $this->version,
            $payload,
            $this->solution['organizationSid'],
            $this->solution['id']
        );
    }


    /**
     * Update the UserInstance
     *
     * @param ScimUser $scimUser
     * @param array|Options $options Optional Arguments
     * @return UserInstance Updated UserInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(ScimUser $scimUser, array $options = []): UserInstance
    {

        $options = new Values($options);

        $headers = Values::of(['Content-Type' => 'application/json', 'Accept' => 'application/scim+json' , 'If-Match' => $options['ifMatch']]);
        $data = $scimUser->toArray();
        $payload = $this->version->update('PUT', $this->uri, [], $data, $headers);

        return new UserInstance(
            $this->version,
            $payload,
            $this->solution['organizationSid'],
            $this->solution['id']
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.PreviewIam.Versionless.UserContext ' . \implode(' ', $context) . ']';
    }
}
