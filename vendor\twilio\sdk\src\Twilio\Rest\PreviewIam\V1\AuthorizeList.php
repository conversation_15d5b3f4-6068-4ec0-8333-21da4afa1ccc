<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Organization Public API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\PreviewIam\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\ListResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;


class AuthorizeList extends ListResource
    {
    /**
     * Construct the AuthorizeList
     *
     * @param Version $version Version that contains the resource
     */
    public function __construct(
        Version $version
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        ];

        $this->uri = '/authorize';
    }

    /**
     * Fetch the AuthorizeInstance
     *
     * @param array|Options $options Optional Arguments
     * @return AuthorizeInstance Fetched AuthorizeInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(array $options = []): AuthorizeInstance
    {

        $options = new Values($options);

        $params = Values::of([
            'response_type' =>
                $options['responseType'],
            'client_id' =>
                $options['clientId'],
            'redirect_uri' =>
                $options['redirectUri'],
            'scope' =>
                $options['scope'],
            'state' =>
                $options['state'],
        ]);

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json' ]);
        $payload = $this->version->fetch('GET', $this->uri, $params, [], $headers);

        return new AuthorizeInstance(
            $this->version,
            $payload
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.PreviewIam.V1.AuthorizeList]';
    }
}
