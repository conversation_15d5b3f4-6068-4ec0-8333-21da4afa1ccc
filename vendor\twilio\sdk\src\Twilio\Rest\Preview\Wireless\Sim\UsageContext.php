<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Preview
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Preview\Wireless\Sim;

use Twilio\Exceptions\TwilioException;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\InstanceContext;


class UsageContext extends InstanceContext
    {
    /**
     * Initialize the UsageContext
     *
     * @param Version $version Version that contains the resource
     * @param string $simSid 
     */
    public function __construct(
        Version $version,
        $simSid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'simSid' =>
            $simSid,
        ];

        $this->uri = '/Sims/' . \rawurlencode($simSid)
        .'/Usage';
    }

    /**
     * Fetch the UsageInstance
     *
     * @param array|Options $options Optional Arguments
     * @return UsageInstance Fetched UsageInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(array $options = []): UsageInstance
    {

        $options = new Values($options);

        $params = Values::of([
            'End' =>
                $options['end'],
            'Start' =>
                $options['start'],
        ]);

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json' ]);
        $payload = $this->version->fetch('GET', $this->uri, $params, [], $headers);

        return new UsageInstance(
            $this->version,
            $payload,
            $this->solution['simSid']
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Preview.Wireless.UsageContext ' . \implode(' ', $context) . ']';
    }
}
