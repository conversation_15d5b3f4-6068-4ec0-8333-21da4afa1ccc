{"name": "konekt/html", "description": "HTML and Form Builders for the Laravel Framework", "license": "MIT", "homepage": "https://laravelcollective.com", "support": {"issues": "https://github.com/artkonekt/html/issues", "source": "https://github.com/artkonekt/html"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>"}], "require": {"php": "^8.1", "illuminate/http": "^10.0|^11.0", "illuminate/routing": "^10.0|^11.0", "illuminate/session": "^10.0|^11.0", "illuminate/support": "^10.0|^11.0", "illuminate/view": "^10.0|^11.0"}, "require-dev": {"illuminate/database": "^10.0|^11.0", "mockery/mockery": "^1.0", "phpunit/phpunit": "^10.0"}, "autoload": {"psr-4": {"Collective\\Html\\": "src/"}, "files": ["src/helpers.php"]}, "extra": {"branch-alias": {"dev-master": "6.5.x-dev"}, "laravel": {"providers": ["Collective\\Html\\HtmlServiceProvider"], "aliases": {"Form": "Collective\\Html\\FormFacade", "Html": "Collective\\Html\\HtmlFacade"}}}, "replace": {"laravelcollective/html": "^6.4"}, "minimum-stability": "dev", "prefer-stable": true}