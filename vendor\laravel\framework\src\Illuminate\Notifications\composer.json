{"name": "illuminate/notifications", "description": "The Illuminate Notifications package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "illuminate/broadcasting": "^11.0", "illuminate/bus": "^11.0", "illuminate/collections": "^11.0", "illuminate/container": "^11.0", "illuminate/contracts": "^11.0", "illuminate/filesystem": "^11.0", "illuminate/mail": "^11.0", "illuminate/queue": "^11.0", "illuminate/support": "^11.0"}, "autoload": {"psr-4": {"Illuminate\\Notifications\\": ""}}, "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "suggest": {"illuminate/database": "Required to use the database transport (^11.0)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}