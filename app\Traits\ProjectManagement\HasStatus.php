<?php

namespace App\Traits\ProjectManagement;

use Illuminate\Database\Eloquent\Builder;

/**
 * Trait للتعامل مع حالات المشاريع والمهام
 * 
 * يوفر طرق مساعدة للتعامل مع الحالات المختلفة
 * وتغيير الحالات مع التحقق من الصلاحيات
 * 
 * @package App\Traits\ProjectManagement
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
trait HasStatus
{
    /**
     * الحالات المتاحة للمشاريع
     * 
     * @var array
     */
    protected static $projectStatuses = [
        'planning' => 'تخطيط',
        'in_progress' => 'قيد التنفيذ',
        'testing' => 'اختبار',
        'completed' => 'مكتمل',
        'on_hold' => 'معلق',
        'cancelled' => 'ملغي'
    ];

    /**
     * الحالات المتاحة للمهام
     * 
     * @var array
     */
    protected static $taskStatuses = [
        'todo' => 'للقيام',
        'in_progress' => 'قيد التنفيذ',
        'review' => 'مراجعة',
        'testing' => 'اختبار',
        'completed' => 'مكتمل',
        'blocked' => 'محجوب'
    ];

    /**
     * الحصول على قائمة الحالات المتاحة
     * 
     * @return array
     */
    public static function getAvailableStatuses(): array
    {
        $modelClass = get_called_class();
        
        if (str_contains($modelClass, 'Project')) {
            return static::$projectStatuses;
        } elseif (str_contains($modelClass, 'Task')) {
            return static::$taskStatuses;
        }
        
        return [];
    }

    /**
     * الحصول على تسمية الحالة الحالية
     * 
     * @return string
     */
    public function getStatusLabel(): string
    {
        $statuses = static::getAvailableStatuses();
        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * الحصول على لون الحالة
     * 
     * @return string
     */
    public function getStatusColor(): string
    {
        $colors = [
            'planning' => 'blue',
            'todo' => 'gray',
            'in_progress' => 'yellow',
            'review' => 'purple',
            'testing' => 'orange',
            'completed' => 'green',
            'on_hold' => 'red',
            'cancelled' => 'red',
            'blocked' => 'red'
        ];

        return $colors[$this->status] ?? 'gray';
    }

    /**
     * الحصول على أيقونة الحالة
     * 
     * @return string
     */
    public function getStatusIcon(): string
    {
        $icons = [
            'planning' => 'fas fa-clipboard-list',
            'todo' => 'fas fa-circle',
            'in_progress' => 'fas fa-spinner',
            'review' => 'fas fa-eye',
            'testing' => 'fas fa-bug',
            'completed' => 'fas fa-check-circle',
            'on_hold' => 'fas fa-pause-circle',
            'cancelled' => 'fas fa-times-circle',
            'blocked' => 'fas fa-ban'
        ];

        return $icons[$this->status] ?? 'fas fa-question-circle';
    }

    /**
     * التحقق من أن الحالة مكتملة
     * 
     * @return bool
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * التحقق من أن الحالة قيد التنفيذ
     * 
     * @return bool
     */
    public function isInProgress(): bool
    {
        return $this->status === 'in_progress';
    }

    /**
     * التحقق من أن الحالة معلقة
     * 
     * @return bool
     */
    public function isOnHold(): bool
    {
        return $this->status === 'on_hold';
    }

    /**
     * التحقق من أن الحالة ملغية
     * 
     * @return bool
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * التحقق من أن الحالة محجوبة (للمهام)
     * 
     * @return bool
     */
    public function isBlocked(): bool
    {
        return $this->status === 'blocked';
    }

    /**
     * التحقق من أن الحالة نشطة (ليست مكتملة أو ملغية)
     * 
     * @return bool
     */
    public function isActive(): bool
    {
        return !in_array($this->status, ['completed', 'cancelled']);
    }

    /**
     * تغيير الحالة
     * 
     * @param string $newStatus الحالة الجديدة
     * @param string|null $reason سبب التغيير
     * @return bool
     */
    public function changeStatus(string $newStatus, ?string $reason = null): bool
    {
        $availableStatuses = array_keys(static::getAvailableStatuses());
        
        if (!in_array($newStatus, $availableStatuses)) {
            return false;
        }

        $oldStatus = $this->status;
        $this->status = $newStatus;

        // إضافة تاريخ الإكمال إذا كانت الحالة مكتملة
        if ($newStatus === 'completed' && $this->hasAttribute('completed_at')) {
            $this->completed_at = now();
        }

        $saved = $this->save();

        // تسجيل تغيير الحالة في سجل الأنشطة
        if ($saved && method_exists($this, 'logActivity')) {
            $this->logActivity('status_changed', [
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'reason' => $reason
            ]);
        }

        return $saved;
    }

    /**
     * الحصول على الحالات التالية المسموحة
     * 
     * @return array
     */
    public function getAllowedNextStatuses(): array
    {
        $transitions = [
            'planning' => ['in_progress', 'on_hold', 'cancelled'],
            'todo' => ['in_progress', 'blocked'],
            'in_progress' => ['review', 'testing', 'completed', 'on_hold', 'blocked'],
            'review' => ['in_progress', 'testing', 'completed'],
            'testing' => ['in_progress', 'completed', 'blocked'],
            'on_hold' => ['in_progress', 'cancelled'],
            'blocked' => ['todo', 'in_progress'],
            'completed' => [], // لا يمكن تغيير الحالة من مكتمل
            'cancelled' => [] // لا يمكن تغيير الحالة من ملغي
        ];

        return $transitions[$this->status] ?? [];
    }

    /**
     * التحقق من إمكانية تغيير الحالة
     * 
     * @param string $newStatus الحالة الجديدة
     * @return bool
     */
    public function canChangeStatusTo(string $newStatus): bool
    {
        return in_array($newStatus, $this->getAllowedNextStatuses());
    }

    /**
     * scope للحالات النشطة
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->whereNotIn('status', ['completed', 'cancelled']);
    }

    /**
     * scope للحالات المكتملة
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeCompleted(Builder $query): Builder
    {
        return $query->where('status', 'completed');
    }

    /**
     * scope للحالات قيد التنفيذ
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeInProgress(Builder $query): Builder
    {
        return $query->where('status', 'in_progress');
    }

    /**
     * scope للحالات المعلقة
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeOnHold(Builder $query): Builder
    {
        return $query->where('status', 'on_hold');
    }

    /**
     * scope للحالات الملغية
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeCancelled(Builder $query): Builder
    {
        return $query->where('status', 'cancelled');
    }

    /**
     * scope للحالات المحجوبة
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeBlocked(Builder $query): Builder
    {
        return $query->where('status', 'blocked');
    }

    /**
     * scope لحالة معينة
     * 
     * @param Builder $query
     * @param string $status
     * @return Builder
     */
    public function scopeWithStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * scope لحالات متعددة
     * 
     * @param Builder $query
     * @param array $statuses
     * @return Builder
     */
    public function scopeWithStatuses(Builder $query, array $statuses): Builder
    {
        return $query->whereIn('status', $statuses);
    }

    /**
     * التحقق من وجود خاصية في النموذج
     * 
     * @param string $attribute
     * @return bool
     */
    protected function hasAttribute(string $attribute): bool
    {
        return in_array($attribute, $this->fillable) || 
               array_key_exists($attribute, $this->attributes);
    }
}
