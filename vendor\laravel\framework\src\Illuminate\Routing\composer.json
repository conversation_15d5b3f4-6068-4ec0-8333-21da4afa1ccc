{"name": "illuminate/routing", "description": "The Illuminate Routing package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "ext-filter": "*", "ext-hash": "*", "illuminate/collections": "^11.0", "illuminate/container": "^11.0", "illuminate/contracts": "^11.0", "illuminate/http": "^11.0", "illuminate/macroable": "^11.0", "illuminate/pipeline": "^11.0", "illuminate/session": "^11.0", "illuminate/support": "^11.0", "symfony/http-foundation": "^7.0.3", "symfony/http-kernel": "^7.0.3", "symfony/routing": "^7.0.3"}, "autoload": {"psr-4": {"Illuminate\\Routing\\": ""}}, "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "suggest": {"illuminate/console": "Required to use the make commands (^11.0).", "php-http/discovery": "Required to use PSR-7 bridging features (^1.15).", "symfony/psr-http-message-bridge": "Required to use PSR-7 bridging features (^7.0)."}, "config": {"sort-packages": true, "allow-plugins": {"php-http/discovery": false}}, "minimum-stability": "dev"}