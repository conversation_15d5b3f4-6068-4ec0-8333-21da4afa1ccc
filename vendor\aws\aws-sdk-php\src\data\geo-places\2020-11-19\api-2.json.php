<?php
// This file was auto-generated from sdk-root/src/data/geo-places/2020-11-19/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-11-19', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'geo-places', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Amazon Location Service Places V2', 'serviceId' => 'Geo Places', 'signatureVersion' => 'v4', 'signingName' => 'geo-places', 'uid' => 'geo-places-2020-11-19', ], 'operations' => [ 'Autocomplete' => [ 'name' => 'Autocomplete', 'http' => [ 'method' => 'POST', 'requestUri' => '/autocomplete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AutocompleteRequest', ], 'output' => [ 'shape' => 'AutocompleteResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'Geocode' => [ 'name' => 'Geocode', 'http' => [ 'method' => 'POST', 'requestUri' => '/geocode', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GeocodeRequest', ], 'output' => [ 'shape' => 'GeocodeResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetPlace' => [ 'name' => 'GetPlace', 'http' => [ 'method' => 'GET', 'requestUri' => '/place/{PlaceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPlaceRequest', ], 'output' => [ 'shape' => 'GetPlaceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ReverseGeocode' => [ 'name' => 'ReverseGeocode', 'http' => [ 'method' => 'POST', 'requestUri' => '/reverse-geocode', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ReverseGeocodeRequest', ], 'output' => [ 'shape' => 'ReverseGeocodeResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'SearchNearby' => [ 'name' => 'SearchNearby', 'http' => [ 'method' => 'POST', 'requestUri' => '/search-nearby', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchNearbyRequest', ], 'output' => [ 'shape' => 'SearchNearbyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'SearchText' => [ 'name' => 'SearchText', 'http' => [ 'method' => 'POST', 'requestUri' => '/search-text', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchTextRequest', ], 'output' => [ 'shape' => 'SearchTextResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'Suggest' => [ 'name' => 'Suggest', 'http' => [ 'method' => 'POST', 'requestUri' => '/suggest', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SuggestRequest', ], 'output' => [ 'shape' => 'SuggestResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccessPoint' => [ 'type' => 'structure', 'members' => [ 'Position' => [ 'shape' => 'Position', ], ], ], 'AccessPointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessPoint', ], 'max' => 100, 'min' => 0, ], 'AccessRestriction' => [ 'type' => 'structure', 'members' => [ 'Restricted' => [ 'shape' => 'SensitiveBoolean', ], 'Categories' => [ 'shape' => 'CategoryList', ], ], ], 'AccessRestrictionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessRestriction', ], 'max' => 100, 'min' => 1, ], 'Address' => [ 'type' => 'structure', 'members' => [ 'Label' => [ 'shape' => 'AddressLabelString', ], 'Country' => [ 'shape' => 'Country', ], 'Region' => [ 'shape' => 'Region', ], 'SubRegion' => [ 'shape' => 'SubRegion', ], 'Locality' => [ 'shape' => 'AddressLocalityString', ], 'District' => [ 'shape' => 'AddressDistrictString', ], 'SubDistrict' => [ 'shape' => 'AddressSubDistrictString', ], 'PostalCode' => [ 'shape' => 'AddressPostalCodeString', ], 'Block' => [ 'shape' => 'AddressBlockString', ], 'SubBlock' => [ 'shape' => 'AddressSubBlockString', ], 'Intersection' => [ 'shape' => 'IntersectionStreetList', ], 'Street' => [ 'shape' => 'AddressStreetString', ], 'StreetComponents' => [ 'shape' => 'StreetComponentsList', ], 'AddressNumber' => [ 'shape' => 'AddressAddressNumberString', ], 'Building' => [ 'shape' => 'AddressBuildingString', ], 'SecondaryAddressComponents' => [ 'shape' => 'SecondaryAddressComponentList', ], ], ], 'AddressAddressNumberString' => [ 'type' => 'string', 'max' => 10, 'min' => 0, 'sensitive' => true, ], 'AddressBlockString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'AddressBuildingString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'AddressComponentMatchScores' => [ 'type' => 'structure', 'members' => [ 'Country' => [ 'shape' => 'MatchScore', ], 'Region' => [ 'shape' => 'MatchScore', ], 'SubRegion' => [ 'shape' => 'MatchScore', ], 'Locality' => [ 'shape' => 'MatchScore', ], 'District' => [ 'shape' => 'MatchScore', ], 'SubDistrict' => [ 'shape' => 'MatchScore', ], 'PostalCode' => [ 'shape' => 'MatchScore', ], 'Block' => [ 'shape' => 'MatchScore', ], 'SubBlock' => [ 'shape' => 'MatchScore', ], 'Intersection' => [ 'shape' => 'AddressComponentMatchScoresIntersectionList', ], 'AddressNumber' => [ 'shape' => 'MatchScore', ], 'Building' => [ 'shape' => 'MatchScore', ], 'SecondaryAddressComponents' => [ 'shape' => 'SecondaryAddressComponentMatchScoreList', ], ], ], 'AddressComponentMatchScoresIntersectionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatchScore', ], 'max' => 2, 'min' => 1, ], 'AddressComponentPhonemes' => [ 'type' => 'structure', 'members' => [ 'Country' => [ 'shape' => 'PhonemeTranscriptionList', ], 'Region' => [ 'shape' => 'PhonemeTranscriptionList', ], 'SubRegion' => [ 'shape' => 'PhonemeTranscriptionList', ], 'Locality' => [ 'shape' => 'PhonemeTranscriptionList', ], 'District' => [ 'shape' => 'PhonemeTranscriptionList', ], 'SubDistrict' => [ 'shape' => 'PhonemeTranscriptionList', ], 'Block' => [ 'shape' => 'PhonemeTranscriptionList', ], 'SubBlock' => [ 'shape' => 'PhonemeTranscriptionList', ], 'Street' => [ 'shape' => 'PhonemeTranscriptionList', ], ], ], 'AddressDistrictString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'AddressLabelString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'AddressLocalityString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'AddressPostalCodeString' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'sensitive' => true, ], 'AddressStreetString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'AddressSubBlockString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'AddressSubDistrictString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'ApiKey' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'sensitive' => true, ], 'AutocompleteAdditionalFeature' => [ 'type' => 'string', 'enum' => [ 'Core', ], ], 'AutocompleteAdditionalFeatureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutocompleteAdditionalFeature', ], 'max' => 1, 'min' => 1, ], 'AutocompleteAddressHighlights' => [ 'type' => 'structure', 'members' => [ 'Label' => [ 'shape' => 'HighlightList', ], 'Country' => [ 'shape' => 'CountryHighlights', ], 'Region' => [ 'shape' => 'RegionHighlights', ], 'SubRegion' => [ 'shape' => 'SubRegionHighlights', ], 'Locality' => [ 'shape' => 'HighlightList', ], 'District' => [ 'shape' => 'HighlightList', ], 'SubDistrict' => [ 'shape' => 'HighlightList', ], 'Street' => [ 'shape' => 'HighlightList', ], 'Block' => [ 'shape' => 'HighlightList', ], 'SubBlock' => [ 'shape' => 'HighlightList', ], 'Intersection' => [ 'shape' => 'IntersectionHighlightsList', ], 'PostalCode' => [ 'shape' => 'HighlightList', ], 'AddressNumber' => [ 'shape' => 'HighlightList', ], 'Building' => [ 'shape' => 'HighlightList', ], ], ], 'AutocompleteFilter' => [ 'type' => 'structure', 'members' => [ 'BoundingBox' => [ 'shape' => 'BoundingBox', ], 'Circle' => [ 'shape' => 'FilterCircle', ], 'IncludeCountries' => [ 'shape' => 'CountryCodeList', ], 'IncludePlaceTypes' => [ 'shape' => 'AutocompleteFilterPlaceTypeList', ], ], ], 'AutocompleteFilterPlaceType' => [ 'type' => 'string', 'enum' => [ 'Locality', 'PostalCode', ], ], 'AutocompleteFilterPlaceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutocompleteFilterPlaceType', ], 'max' => 2, 'min' => 1, ], 'AutocompleteHighlights' => [ 'type' => 'structure', 'members' => [ 'Title' => [ 'shape' => 'HighlightList', ], 'Address' => [ 'shape' => 'AutocompleteAddressHighlights', ], ], ], 'AutocompleteIntendedUse' => [ 'type' => 'string', 'enum' => [ 'SingleUse', ], ], 'AutocompleteRequest' => [ 'type' => 'structure', 'required' => [ 'QueryText', ], 'members' => [ 'QueryText' => [ 'shape' => 'AutocompleteRequestQueryTextString', ], 'MaxResults' => [ 'shape' => 'AutocompleteRequestMaxResultsInteger', ], 'BiasPosition' => [ 'shape' => 'Position', ], 'Filter' => [ 'shape' => 'AutocompleteFilter', ], 'PostalCodeMode' => [ 'shape' => 'PostalCodeMode', ], 'AdditionalFeatures' => [ 'shape' => 'AutocompleteAdditionalFeatureList', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'PoliticalView' => [ 'shape' => 'CountryCode', ], 'IntendedUse' => [ 'shape' => 'AutocompleteIntendedUse', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], ], ], 'AutocompleteRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 20, 'min' => 1, ], 'AutocompleteRequestQueryTextString' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'sensitive' => true, ], 'AutocompleteResponse' => [ 'type' => 'structure', 'required' => [ 'PricingBucket', ], 'members' => [ 'PricingBucket' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amz-geo-pricing-bucket', ], 'ResultItems' => [ 'shape' => 'AutocompleteResultItemList', ], ], ], 'AutocompleteResultItem' => [ 'type' => 'structure', 'required' => [ 'PlaceId', 'PlaceType', 'Title', ], 'members' => [ 'PlaceId' => [ 'shape' => 'AutocompleteResultItemPlaceIdString', ], 'PlaceType' => [ 'shape' => 'PlaceType', ], 'Title' => [ 'shape' => 'AutocompleteResultItemTitleString', ], 'Address' => [ 'shape' => 'Address', ], 'Distance' => [ 'shape' => 'DistanceMeters', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'PoliticalView' => [ 'shape' => 'CountryCode3', ], 'Highlights' => [ 'shape' => 'AutocompleteHighlights', ], ], ], 'AutocompleteResultItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutocompleteResultItem', ], 'max' => 20, 'min' => 0, ], 'AutocompleteResultItemPlaceIdString' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'sensitive' => true, ], 'AutocompleteResultItemTitleString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'BoundingBox' => [ 'type' => 'list', 'member' => [ 'shape' => 'Double', ], 'max' => 4, 'min' => 4, 'sensitive' => true, ], 'BusinessChain' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'BusinessChainNameString', ], 'Id' => [ 'shape' => 'BusinessChainIdString', ], ], ], 'BusinessChainIdString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'sensitive' => true, ], 'BusinessChainList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BusinessChain', ], 'max' => 100, 'min' => 1, ], 'BusinessChainNameString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'sensitive' => true, ], 'Category' => [ 'type' => 'structure', 'required' => [ 'Id', 'Name', ], 'members' => [ 'Id' => [ 'shape' => 'CategoryIdString', ], 'Name' => [ 'shape' => 'CategoryNameString', ], 'LocalizedName' => [ 'shape' => 'CategoryLocalizedNameString', ], 'Primary' => [ 'shape' => 'SensitiveBoolean', ], ], ], 'CategoryIdString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'sensitive' => true, ], 'CategoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Category', ], 'max' => 100, 'min' => 1, ], 'CategoryLocalizedNameString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'sensitive' => true, ], 'CategoryNameString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'sensitive' => true, ], 'ComponentMatchScores' => [ 'type' => 'structure', 'members' => [ 'Title' => [ 'shape' => 'MatchScore', ], 'Address' => [ 'shape' => 'AddressComponentMatchScores', ], ], ], 'ContactDetails' => [ 'type' => 'structure', 'members' => [ 'Label' => [ 'shape' => 'ContactDetailsLabelString', ], 'Value' => [ 'shape' => 'ContactDetailsValueString', ], 'Categories' => [ 'shape' => 'CategoryList', ], ], ], 'ContactDetailsLabelString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'ContactDetailsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactDetails', ], 'max' => 100, 'min' => 1, ], 'ContactDetailsValueString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'Contacts' => [ 'type' => 'structure', 'members' => [ 'Phones' => [ 'shape' => 'ContactDetailsList', ], 'Faxes' => [ 'shape' => 'ContactDetailsList', ], 'Websites' => [ 'shape' => 'ContactDetailsList', ], 'Emails' => [ 'shape' => 'ContactDetailsList', ], ], ], 'Country' => [ 'type' => 'structure', 'members' => [ 'Code2' => [ 'shape' => 'CountryCode2', ], 'Code3' => [ 'shape' => 'CountryCode3', ], 'Name' => [ 'shape' => 'CountryNameString', ], ], ], 'CountryCode' => [ 'type' => 'string', 'max' => 3, 'min' => 2, 'pattern' => '([A-Z]{2}|[A-Z]{3})', 'sensitive' => true, ], 'CountryCode2' => [ 'type' => 'string', 'max' => 2, 'min' => 2, 'pattern' => '[A-Z]{2}', 'sensitive' => true, ], 'CountryCode3' => [ 'type' => 'string', 'max' => 3, 'min' => 3, 'pattern' => '[A-Z]{3}', 'sensitive' => true, ], 'CountryCodeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CountryCode', ], 'max' => 100, 'min' => 1, ], 'CountryHighlights' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'HighlightList', ], 'Name' => [ 'shape' => 'HighlightList', ], ], ], 'CountryNameString' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'sensitive' => true, ], 'DistanceMeters' => [ 'type' => 'long', 'max' => 4294967295, 'min' => 0, 'sensitive' => true, ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'FilterBusinessChainList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterBusinessChainListMemberString', ], 'max' => 10, 'min' => 1, ], 'FilterBusinessChainListMemberString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'sensitive' => true, ], 'FilterCategoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterCategoryListMemberString', ], 'max' => 10, 'min' => 1, ], 'FilterCategoryListMemberString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'sensitive' => true, ], 'FilterCircle' => [ 'type' => 'structure', 'required' => [ 'Center', 'Radius', ], 'members' => [ 'Center' => [ 'shape' => 'Position', ], 'Radius' => [ 'shape' => 'FilterCircleRadiusLong', 'box' => true, ], ], 'sensitive' => true, ], 'FilterCircleRadiusLong' => [ 'type' => 'long', 'max' => 21000000, 'min' => 1, 'sensitive' => true, ], 'FilterFoodTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterFoodTypeListMemberString', ], 'max' => 10, 'min' => 1, ], 'FilterFoodTypeListMemberString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'sensitive' => true, ], 'FoodType' => [ 'type' => 'structure', 'required' => [ 'LocalizedName', ], 'members' => [ 'LocalizedName' => [ 'shape' => 'FoodTypeLocalizedNameString', ], 'Id' => [ 'shape' => 'FoodTypeIdString', ], 'Primary' => [ 'shape' => 'SensitiveBoolean', ], ], ], 'FoodTypeIdString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'sensitive' => true, ], 'FoodTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FoodType', ], 'max' => 100, 'min' => 1, ], 'FoodTypeLocalizedNameString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'sensitive' => true, ], 'GeocodeAdditionalFeature' => [ 'type' => 'string', 'enum' => [ 'TimeZone', 'Access', 'SecondaryAddresses', 'Intersections', ], ], 'GeocodeAdditionalFeatureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GeocodeAdditionalFeature', ], 'max' => 2, 'min' => 1, ], 'GeocodeFilter' => [ 'type' => 'structure', 'members' => [ 'IncludeCountries' => [ 'shape' => 'CountryCodeList', ], 'IncludePlaceTypes' => [ 'shape' => 'GeocodeFilterPlaceTypeList', ], ], ], 'GeocodeFilterPlaceType' => [ 'type' => 'string', 'enum' => [ 'Locality', 'PostalCode', 'Intersection', 'Street', 'PointAddress', 'InterpolatedAddress', ], 'sensitive' => true, ], 'GeocodeFilterPlaceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GeocodeFilterPlaceType', ], 'max' => 6, 'min' => 1, ], 'GeocodeIntendedUse' => [ 'type' => 'string', 'enum' => [ 'SingleUse', 'Storage', ], ], 'GeocodeParsedQuery' => [ 'type' => 'structure', 'members' => [ 'Title' => [ 'shape' => 'ParsedQueryComponentList', ], 'Address' => [ 'shape' => 'GeocodeParsedQueryAddressComponents', ], ], ], 'GeocodeParsedQueryAddressComponents' => [ 'type' => 'structure', 'members' => [ 'Country' => [ 'shape' => 'ParsedQueryComponentList', ], 'Region' => [ 'shape' => 'ParsedQueryComponentList', ], 'SubRegion' => [ 'shape' => 'ParsedQueryComponentList', ], 'Locality' => [ 'shape' => 'ParsedQueryComponentList', ], 'District' => [ 'shape' => 'ParsedQueryComponentList', ], 'SubDistrict' => [ 'shape' => 'ParsedQueryComponentList', ], 'PostalCode' => [ 'shape' => 'ParsedQueryComponentList', ], 'Block' => [ 'shape' => 'ParsedQueryComponentList', ], 'SubBlock' => [ 'shape' => 'ParsedQueryComponentList', ], 'Street' => [ 'shape' => 'ParsedQueryComponentList', ], 'AddressNumber' => [ 'shape' => 'ParsedQueryComponentList', ], 'Building' => [ 'shape' => 'ParsedQueryComponentList', ], 'SecondaryAddressComponents' => [ 'shape' => 'ParsedQuerySecondaryAddressComponentList', ], ], ], 'GeocodeQueryComponents' => [ 'type' => 'structure', 'members' => [ 'Country' => [ 'shape' => 'GeocodeQueryComponentsCountryString', ], 'Region' => [ 'shape' => 'GeocodeQueryComponentsRegionString', ], 'SubRegion' => [ 'shape' => 'GeocodeQueryComponentsSubRegionString', ], 'Locality' => [ 'shape' => 'GeocodeQueryComponentsLocalityString', ], 'District' => [ 'shape' => 'GeocodeQueryComponentsDistrictString', ], 'Street' => [ 'shape' => 'GeocodeQueryComponentsStreetString', ], 'AddressNumber' => [ 'shape' => 'GeocodeQueryComponentsAddressNumberString', ], 'PostalCode' => [ 'shape' => 'GeocodeQueryComponentsPostalCodeString', ], ], ], 'GeocodeQueryComponentsAddressNumberString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[^;]+', 'sensitive' => true, ], 'GeocodeQueryComponentsCountryString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[^;]+', 'sensitive' => true, ], 'GeocodeQueryComponentsDistrictString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[^;]+', 'sensitive' => true, ], 'GeocodeQueryComponentsLocalityString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[^;]+', 'sensitive' => true, ], 'GeocodeQueryComponentsPostalCodeString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[^;]+', 'sensitive' => true, ], 'GeocodeQueryComponentsRegionString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[^;]+', 'sensitive' => true, ], 'GeocodeQueryComponentsStreetString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[^;]+', 'sensitive' => true, ], 'GeocodeQueryComponentsSubRegionString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[^;]+', 'sensitive' => true, ], 'GeocodeRequest' => [ 'type' => 'structure', 'members' => [ 'QueryText' => [ 'shape' => 'GeocodeRequestQueryTextString', ], 'QueryComponents' => [ 'shape' => 'GeocodeQueryComponents', ], 'MaxResults' => [ 'shape' => 'GeocodeRequestMaxResultsInteger', ], 'BiasPosition' => [ 'shape' => 'Position', ], 'Filter' => [ 'shape' => 'GeocodeFilter', ], 'AdditionalFeatures' => [ 'shape' => 'GeocodeAdditionalFeatureList', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'PoliticalView' => [ 'shape' => 'CountryCode', ], 'IntendedUse' => [ 'shape' => 'GeocodeIntendedUse', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], ], ], 'GeocodeRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'GeocodeRequestQueryTextString' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'sensitive' => true, ], 'GeocodeResponse' => [ 'type' => 'structure', 'required' => [ 'PricingBucket', ], 'members' => [ 'PricingBucket' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amz-geo-pricing-bucket', ], 'ResultItems' => [ 'shape' => 'GeocodeResultItemList', ], ], ], 'GeocodeResultItem' => [ 'type' => 'structure', 'required' => [ 'PlaceId', 'PlaceType', 'Title', ], 'members' => [ 'PlaceId' => [ 'shape' => 'GeocodeResultItemPlaceIdString', ], 'PlaceType' => [ 'shape' => 'PlaceType', ], 'Title' => [ 'shape' => 'GeocodeResultItemTitleString', ], 'Address' => [ 'shape' => 'Address', ], 'AddressNumberCorrected' => [ 'shape' => 'SensitiveBoolean', ], 'PostalCodeDetails' => [ 'shape' => 'PostalCodeDetailsList', ], 'Position' => [ 'shape' => 'Position', ], 'Distance' => [ 'shape' => 'DistanceMeters', ], 'MapView' => [ 'shape' => 'BoundingBox', ], 'Categories' => [ 'shape' => 'CategoryList', ], 'FoodTypes' => [ 'shape' => 'FoodTypeList', ], 'AccessPoints' => [ 'shape' => 'AccessPointList', ], 'TimeZone' => [ 'shape' => 'TimeZone', ], 'PoliticalView' => [ 'shape' => 'CountryCode3', ], 'MatchScores' => [ 'shape' => 'MatchScoreDetails', ], 'ParsedQuery' => [ 'shape' => 'GeocodeParsedQuery', ], 'Intersections' => [ 'shape' => 'IntersectionList', ], 'MainAddress' => [ 'shape' => 'RelatedPlace', ], 'SecondaryAddresses' => [ 'shape' => 'RelatedPlaceList', ], ], ], 'GeocodeResultItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GeocodeResultItem', ], 'max' => 100, 'min' => 0, ], 'GeocodeResultItemPlaceIdString' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'sensitive' => true, ], 'GeocodeResultItemTitleString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'GetPlaceAdditionalFeature' => [ 'type' => 'string', 'enum' => [ 'TimeZone', 'Phonemes', 'Access', 'Contact', 'SecondaryAddresses', ], ], 'GetPlaceAdditionalFeatureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GetPlaceAdditionalFeature', ], 'max' => 4, 'min' => 1, ], 'GetPlaceIntendedUse' => [ 'type' => 'string', 'enum' => [ 'SingleUse', 'Storage', ], ], 'GetPlaceRequest' => [ 'type' => 'structure', 'required' => [ 'PlaceId', ], 'members' => [ 'PlaceId' => [ 'shape' => 'GetPlaceRequestPlaceIdString', 'location' => 'uri', 'locationName' => 'PlaceId', ], 'AdditionalFeatures' => [ 'shape' => 'GetPlaceAdditionalFeatureList', 'location' => 'querystring', 'locationName' => 'additional-features', ], 'Language' => [ 'shape' => 'LanguageTag', 'location' => 'querystring', 'locationName' => 'language', ], 'PoliticalView' => [ 'shape' => 'CountryCode', 'location' => 'querystring', 'locationName' => 'political-view', ], 'IntendedUse' => [ 'shape' => 'GetPlaceIntendedUse', 'location' => 'querystring', 'locationName' => 'intended-use', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], ], ], 'GetPlaceRequestPlaceIdString' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'sensitive' => true, ], 'GetPlaceResponse' => [ 'type' => 'structure', 'required' => [ 'PlaceId', 'PlaceType', 'Title', 'PricingBucket', ], 'members' => [ 'PlaceId' => [ 'shape' => 'GetPlaceResponsePlaceIdString', ], 'PlaceType' => [ 'shape' => 'PlaceType', ], 'Title' => [ 'shape' => 'GetPlaceResponseTitleString', ], 'PricingBucket' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amz-geo-pricing-bucket', ], 'Address' => [ 'shape' => 'Address', ], 'AddressNumberCorrected' => [ 'shape' => 'SensitiveBoolean', ], 'PostalCodeDetails' => [ 'shape' => 'PostalCodeDetailsList', ], 'Position' => [ 'shape' => 'Position', ], 'MapView' => [ 'shape' => 'BoundingBox', ], 'Categories' => [ 'shape' => 'CategoryList', ], 'FoodTypes' => [ 'shape' => 'FoodTypeList', ], 'BusinessChains' => [ 'shape' => 'BusinessChainList', ], 'Contacts' => [ 'shape' => 'Contacts', ], 'OpeningHours' => [ 'shape' => 'OpeningHoursList', ], 'AccessPoints' => [ 'shape' => 'AccessPointList', ], 'AccessRestrictions' => [ 'shape' => 'AccessRestrictionList', ], 'TimeZone' => [ 'shape' => 'TimeZone', ], 'PoliticalView' => [ 'shape' => 'CountryCode3', ], 'Phonemes' => [ 'shape' => 'PhonemeDetails', ], 'MainAddress' => [ 'shape' => 'RelatedPlace', ], 'SecondaryAddresses' => [ 'shape' => 'RelatedPlaceList', ], ], ], 'GetPlaceResponsePlaceIdString' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'sensitive' => true, ], 'GetPlaceResponseTitleString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'Highlight' => [ 'type' => 'structure', 'members' => [ 'StartIndex' => [ 'shape' => 'HighlightStartIndexInteger', ], 'EndIndex' => [ 'shape' => 'HighlightEndIndexInteger', ], 'Value' => [ 'shape' => 'HighlightValueString', ], ], ], 'HighlightEndIndexInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'HighlightList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Highlight', ], 'max' => 200, 'min' => 0, ], 'HighlightStartIndexInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'HighlightValueString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'Intersection' => [ 'type' => 'structure', 'required' => [ 'PlaceId', 'Title', ], 'members' => [ 'PlaceId' => [ 'shape' => 'IntersectionPlaceIdString', ], 'Title' => [ 'shape' => 'IntersectionTitleString', ], 'Address' => [ 'shape' => 'Address', ], 'Position' => [ 'shape' => 'Position', ], 'Distance' => [ 'shape' => 'DistanceMeters', 'box' => true, ], 'RouteDistance' => [ 'shape' => 'DistanceMeters', 'box' => true, ], 'MapView' => [ 'shape' => 'BoundingBox', ], 'AccessPoints' => [ 'shape' => 'AccessPointList', ], ], ], 'IntersectionHighlightsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HighlightList', ], 'max' => 100, 'min' => 1, ], 'IntersectionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Intersection', ], 'min' => 1, ], 'IntersectionPlaceIdString' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'sensitive' => true, ], 'IntersectionStreet' => [ 'type' => 'string', 'max' => 200, 'min' => 0, ], 'IntersectionStreetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IntersectionStreet', ], 'max' => 100, 'min' => 1, ], 'IntersectionTitleString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'LanguageTag' => [ 'type' => 'string', 'max' => 35, 'min' => 2, ], 'MatchScore' => [ 'type' => 'double', 'max' => 1, 'min' => 0, ], 'MatchScoreDetails' => [ 'type' => 'structure', 'members' => [ 'Overall' => [ 'shape' => 'MatchScore', ], 'Components' => [ 'shape' => 'ComponentMatchScores', ], ], ], 'OpeningHours' => [ 'type' => 'structure', 'members' => [ 'Display' => [ 'shape' => 'OpeningHoursDisplayList', ], 'OpenNow' => [ 'shape' => 'SensitiveBoolean', ], 'Components' => [ 'shape' => 'OpeningHoursComponentsList', ], 'Categories' => [ 'shape' => 'CategoryList', ], ], ], 'OpeningHoursComponents' => [ 'type' => 'structure', 'members' => [ 'OpenTime' => [ 'shape' => 'OpeningHoursComponentsOpenTimeString', ], 'OpenDuration' => [ 'shape' => 'OpeningHoursComponentsOpenDurationString', ], 'Recurrence' => [ 'shape' => 'OpeningHoursComponentsRecurrenceString', ], ], ], 'OpeningHoursComponentsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpeningHoursComponents', ], 'max' => 100, 'min' => 1, ], 'OpeningHoursComponentsOpenDurationString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'OpeningHoursComponentsOpenTimeString' => [ 'type' => 'string', 'max' => 21, 'min' => 0, 'sensitive' => true, ], 'OpeningHoursComponentsRecurrenceString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'OpeningHoursDisplay' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'OpeningHoursDisplayList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpeningHoursDisplay', ], 'max' => 100, 'min' => 1, ], 'OpeningHoursList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpeningHours', ], 'max' => 100, 'min' => 1, ], 'ParsedQueryComponent' => [ 'type' => 'structure', 'members' => [ 'StartIndex' => [ 'shape' => 'ParsedQueryComponentStartIndexInteger', ], 'EndIndex' => [ 'shape' => 'ParsedQueryComponentEndIndexInteger', ], 'Value' => [ 'shape' => 'ParsedQueryComponentValueString', ], 'QueryComponent' => [ 'shape' => 'ParsedQueryComponentQueryComponentString', ], ], ], 'ParsedQueryComponentEndIndexInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'ParsedQueryComponentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParsedQueryComponent', ], 'max' => 200, 'min' => 0, ], 'ParsedQueryComponentQueryComponentString' => [ 'type' => 'string', 'max' => 11, 'min' => 0, 'sensitive' => true, ], 'ParsedQueryComponentStartIndexInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'ParsedQueryComponentValueString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'ParsedQuerySecondaryAddressComponent' => [ 'type' => 'structure', 'required' => [ 'StartIndex', 'EndIndex', 'Value', 'Number', 'Designator', ], 'members' => [ 'StartIndex' => [ 'shape' => 'ParsedQuerySecondaryAddressComponentStartIndexInteger', ], 'EndIndex' => [ 'shape' => 'ParsedQuerySecondaryAddressComponentEndIndexInteger', ], 'Value' => [ 'shape' => 'ParsedQuerySecondaryAddressComponentValueString', ], 'Number' => [ 'shape' => 'ParsedQuerySecondaryAddressComponentNumberString', ], 'Designator' => [ 'shape' => 'ParsedQuerySecondaryAddressComponentDesignatorString', ], ], ], 'ParsedQuerySecondaryAddressComponentDesignatorString' => [ 'type' => 'string', 'max' => 4, 'min' => 0, 'sensitive' => true, ], 'ParsedQuerySecondaryAddressComponentEndIndexInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'ParsedQuerySecondaryAddressComponentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParsedQuerySecondaryAddressComponent', ], 'max' => 200, 'min' => 0, ], 'ParsedQuerySecondaryAddressComponentNumberString' => [ 'type' => 'string', 'max' => 10, 'min' => 0, 'sensitive' => true, ], 'ParsedQuerySecondaryAddressComponentStartIndexInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'ParsedQuerySecondaryAddressComponentValueString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'PhonemeDetails' => [ 'type' => 'structure', 'members' => [ 'Title' => [ 'shape' => 'PhonemeTranscriptionList', ], 'Address' => [ 'shape' => 'AddressComponentPhonemes', ], ], ], 'PhonemeTranscription' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'PhonemeTranscriptionValueString', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'Preferred' => [ 'shape' => 'SensitiveBoolean', ], ], ], 'PhonemeTranscriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhonemeTranscription', ], 'max' => 100, 'min' => 0, ], 'PhonemeTranscriptionValueString' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'sensitive' => true, ], 'PlaceType' => [ 'type' => 'string', 'enum' => [ 'Country', 'Region', 'SubRegion', 'Locality', 'District', 'SubDistrict', 'PostalCode', 'Block', 'SubBlock', 'Intersection', 'Street', 'PointOfInterest', 'PointAddress', 'InterpolatedAddress', 'SecondaryAddress', ], 'sensitive' => true, ], 'Position' => [ 'type' => 'list', 'member' => [ 'shape' => 'Double', ], 'max' => 2, 'min' => 2, 'sensitive' => true, ], 'PostalAuthority' => [ 'type' => 'string', 'enum' => [ 'Usps', ], 'sensitive' => true, ], 'PostalCodeDetails' => [ 'type' => 'structure', 'members' => [ 'PostalCode' => [ 'shape' => 'PostalCodeDetailsPostalCodeString', ], 'PostalAuthority' => [ 'shape' => 'PostalAuthority', ], 'PostalCodeType' => [ 'shape' => 'PostalCodeType', ], 'UspsZip' => [ 'shape' => 'UspsZip', ], 'UspsZipPlus4' => [ 'shape' => 'UspsZipPlus4', ], ], ], 'PostalCodeDetailsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PostalCodeDetails', ], 'max' => 100, 'min' => 0, ], 'PostalCodeDetailsPostalCodeString' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'sensitive' => true, ], 'PostalCodeMode' => [ 'type' => 'string', 'enum' => [ 'MergeAllSpannedLocalities', 'EnumerateSpannedLocalities', ], ], 'PostalCodeType' => [ 'type' => 'string', 'enum' => [ 'UspsZip', 'UspsZipPlus4', ], 'sensitive' => true, ], 'QueryRefinement' => [ 'type' => 'structure', 'required' => [ 'RefinedTerm', 'OriginalTerm', 'StartIndex', 'EndIndex', ], 'members' => [ 'RefinedTerm' => [ 'shape' => 'QueryRefinementRefinedTermString', ], 'OriginalTerm' => [ 'shape' => 'QueryRefinementOriginalTermString', ], 'StartIndex' => [ 'shape' => 'QueryRefinementStartIndexInteger', ], 'EndIndex' => [ 'shape' => 'QueryRefinementEndIndexInteger', ], ], ], 'QueryRefinementEndIndexInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'QueryRefinementList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueryRefinement', ], 'max' => 10, 'min' => 0, ], 'QueryRefinementOriginalTermString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'QueryRefinementRefinedTermString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'QueryRefinementStartIndexInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'QueryType' => [ 'type' => 'string', 'enum' => [ 'Category', 'BusinessChain', ], ], 'RecordTypeCode' => [ 'type' => 'string', 'enum' => [ 'Firm', 'General', 'HighRise', 'PostOfficeBox', 'Rural', 'Street', ], 'sensitive' => true, ], 'Region' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'RegionCodeString', ], 'Name' => [ 'shape' => 'RegionNameString', ], ], ], 'RegionCodeString' => [ 'type' => 'string', 'max' => 3, 'min' => 0, 'sensitive' => true, ], 'RegionHighlights' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'HighlightList', ], 'Name' => [ 'shape' => 'HighlightList', ], ], ], 'RegionNameString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'RelatedPlace' => [ 'type' => 'structure', 'required' => [ 'PlaceId', 'PlaceType', 'Title', ], 'members' => [ 'PlaceId' => [ 'shape' => 'RelatedPlacePlaceIdString', ], 'PlaceType' => [ 'shape' => 'PlaceType', ], 'Title' => [ 'shape' => 'RelatedPlaceTitleString', ], 'Address' => [ 'shape' => 'Address', ], 'Position' => [ 'shape' => 'Position', ], 'AccessPoints' => [ 'shape' => 'AccessPointList', ], ], ], 'RelatedPlaceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RelatedPlace', ], 'min' => 1, ], 'RelatedPlacePlaceIdString' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'sensitive' => true, ], 'RelatedPlaceTitleString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'ReverseGeocodeAdditionalFeature' => [ 'type' => 'string', 'enum' => [ 'TimeZone', 'Access', 'Intersections', ], ], 'ReverseGeocodeAdditionalFeatureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReverseGeocodeAdditionalFeature', ], 'max' => 2, 'min' => 1, ], 'ReverseGeocodeFilter' => [ 'type' => 'structure', 'members' => [ 'IncludePlaceTypes' => [ 'shape' => 'ReverseGeocodeFilterPlaceTypeList', ], ], ], 'ReverseGeocodeFilterPlaceType' => [ 'type' => 'string', 'enum' => [ 'Locality', 'Intersection', 'Street', 'PointAddress', 'InterpolatedAddress', ], ], 'ReverseGeocodeFilterPlaceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReverseGeocodeFilterPlaceType', ], 'max' => 5, 'min' => 1, ], 'ReverseGeocodeIntendedUse' => [ 'type' => 'string', 'enum' => [ 'SingleUse', 'Storage', ], ], 'ReverseGeocodeRequest' => [ 'type' => 'structure', 'required' => [ 'QueryPosition', ], 'members' => [ 'QueryPosition' => [ 'shape' => 'Position', ], 'QueryRadius' => [ 'shape' => 'ReverseGeocodeRequestQueryRadiusLong', 'box' => true, ], 'MaxResults' => [ 'shape' => 'ReverseGeocodeRequestMaxResultsInteger', ], 'Filter' => [ 'shape' => 'ReverseGeocodeFilter', ], 'AdditionalFeatures' => [ 'shape' => 'ReverseGeocodeAdditionalFeatureList', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'PoliticalView' => [ 'shape' => 'CountryCode', ], 'IntendedUse' => [ 'shape' => 'ReverseGeocodeIntendedUse', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], ], ], 'ReverseGeocodeRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ReverseGeocodeRequestQueryRadiusLong' => [ 'type' => 'long', 'max' => 21000000, 'min' => 1, 'sensitive' => true, ], 'ReverseGeocodeResponse' => [ 'type' => 'structure', 'required' => [ 'PricingBucket', ], 'members' => [ 'PricingBucket' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amz-geo-pricing-bucket', ], 'ResultItems' => [ 'shape' => 'ReverseGeocodeResultItemList', ], ], ], 'ReverseGeocodeResultItem' => [ 'type' => 'structure', 'required' => [ 'PlaceId', 'PlaceType', 'Title', ], 'members' => [ 'PlaceId' => [ 'shape' => 'ReverseGeocodeResultItemPlaceIdString', ], 'PlaceType' => [ 'shape' => 'PlaceType', ], 'Title' => [ 'shape' => 'ReverseGeocodeResultItemTitleString', ], 'Address' => [ 'shape' => 'Address', ], 'AddressNumberCorrected' => [ 'shape' => 'SensitiveBoolean', ], 'PostalCodeDetails' => [ 'shape' => 'PostalCodeDetailsList', ], 'Position' => [ 'shape' => 'Position', ], 'Distance' => [ 'shape' => 'DistanceMeters', ], 'MapView' => [ 'shape' => 'BoundingBox', ], 'Categories' => [ 'shape' => 'CategoryList', ], 'FoodTypes' => [ 'shape' => 'FoodTypeList', ], 'AccessPoints' => [ 'shape' => 'AccessPointList', ], 'TimeZone' => [ 'shape' => 'TimeZone', ], 'PoliticalView' => [ 'shape' => 'CountryCode3', ], 'Intersections' => [ 'shape' => 'IntersectionList', ], ], ], 'ReverseGeocodeResultItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReverseGeocodeResultItem', ], 'max' => 100, 'min' => 0, ], 'ReverseGeocodeResultItemPlaceIdString' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'sensitive' => true, ], 'ReverseGeocodeResultItemTitleString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'SearchNearbyAdditionalFeature' => [ 'type' => 'string', 'enum' => [ 'TimeZone', 'Phonemes', 'Access', 'Contact', ], ], 'SearchNearbyAdditionalFeatureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchNearbyAdditionalFeature', ], 'max' => 4, 'min' => 1, ], 'SearchNearbyFilter' => [ 'type' => 'structure', 'members' => [ 'BoundingBox' => [ 'shape' => 'BoundingBox', ], 'IncludeCountries' => [ 'shape' => 'CountryCodeList', ], 'IncludeCategories' => [ 'shape' => 'FilterCategoryList', ], 'ExcludeCategories' => [ 'shape' => 'FilterCategoryList', ], 'IncludeBusinessChains' => [ 'shape' => 'FilterBusinessChainList', ], 'ExcludeBusinessChains' => [ 'shape' => 'FilterBusinessChainList', ], 'IncludeFoodTypes' => [ 'shape' => 'FilterFoodTypeList', ], 'ExcludeFoodTypes' => [ 'shape' => 'FilterFoodTypeList', ], ], ], 'SearchNearbyIntendedUse' => [ 'type' => 'string', 'enum' => [ 'SingleUse', 'Storage', ], ], 'SearchNearbyRequest' => [ 'type' => 'structure', 'required' => [ 'QueryPosition', ], 'members' => [ 'QueryPosition' => [ 'shape' => 'Position', ], 'QueryRadius' => [ 'shape' => 'SearchNearbyRequestQueryRadiusLong', 'box' => true, ], 'MaxResults' => [ 'shape' => 'SearchNearbyRequestMaxResultsInteger', ], 'Filter' => [ 'shape' => 'SearchNearbyFilter', ], 'AdditionalFeatures' => [ 'shape' => 'SearchNearbyAdditionalFeatureList', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'PoliticalView' => [ 'shape' => 'CountryCode', ], 'IntendedUse' => [ 'shape' => 'SearchNearbyIntendedUse', ], 'NextToken' => [ 'shape' => 'Token', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], ], ], 'SearchNearbyRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'SearchNearbyRequestQueryRadiusLong' => [ 'type' => 'long', 'max' => 21000000, 'min' => 1, 'sensitive' => true, ], 'SearchNearbyResponse' => [ 'type' => 'structure', 'required' => [ 'PricingBucket', ], 'members' => [ 'PricingBucket' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amz-geo-pricing-bucket', ], 'ResultItems' => [ 'shape' => 'SearchNearbyResultItemList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'SearchNearbyResultItem' => [ 'type' => 'structure', 'required' => [ 'PlaceId', 'PlaceType', 'Title', ], 'members' => [ 'PlaceId' => [ 'shape' => 'SearchNearbyResultItemPlaceIdString', ], 'PlaceType' => [ 'shape' => 'PlaceType', ], 'Title' => [ 'shape' => 'SearchNearbyResultItemTitleString', ], 'Address' => [ 'shape' => 'Address', ], 'AddressNumberCorrected' => [ 'shape' => 'SensitiveBoolean', ], 'Position' => [ 'shape' => 'Position', ], 'Distance' => [ 'shape' => 'DistanceMeters', ], 'MapView' => [ 'shape' => 'BoundingBox', ], 'Categories' => [ 'shape' => 'CategoryList', ], 'FoodTypes' => [ 'shape' => 'FoodTypeList', ], 'BusinessChains' => [ 'shape' => 'BusinessChainList', ], 'Contacts' => [ 'shape' => 'Contacts', ], 'OpeningHours' => [ 'shape' => 'OpeningHoursList', ], 'AccessPoints' => [ 'shape' => 'AccessPointList', ], 'AccessRestrictions' => [ 'shape' => 'AccessRestrictionList', ], 'TimeZone' => [ 'shape' => 'TimeZone', ], 'PoliticalView' => [ 'shape' => 'CountryCode3', ], 'Phonemes' => [ 'shape' => 'PhonemeDetails', ], ], ], 'SearchNearbyResultItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchNearbyResultItem', ], 'max' => 100, 'min' => 0, ], 'SearchNearbyResultItemPlaceIdString' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'sensitive' => true, ], 'SearchNearbyResultItemTitleString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'SearchTextAdditionalFeature' => [ 'type' => 'string', 'enum' => [ 'TimeZone', 'Phonemes', 'Access', 'Contact', ], ], 'SearchTextAdditionalFeatureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchTextAdditionalFeature', ], 'max' => 4, 'min' => 1, ], 'SearchTextFilter' => [ 'type' => 'structure', 'members' => [ 'BoundingBox' => [ 'shape' => 'BoundingBox', ], 'Circle' => [ 'shape' => 'FilterCircle', ], 'IncludeCountries' => [ 'shape' => 'CountryCodeList', ], ], ], 'SearchTextIntendedUse' => [ 'type' => 'string', 'enum' => [ 'SingleUse', 'Storage', ], ], 'SearchTextRequest' => [ 'type' => 'structure', 'members' => [ 'QueryText' => [ 'shape' => 'SearchTextRequestQueryTextString', ], 'QueryId' => [ 'shape' => 'SearchTextRequestQueryIdString', ], 'MaxResults' => [ 'shape' => 'SearchTextRequestMaxResultsInteger', ], 'BiasPosition' => [ 'shape' => 'Position', ], 'Filter' => [ 'shape' => 'SearchTextFilter', ], 'AdditionalFeatures' => [ 'shape' => 'SearchTextAdditionalFeatureList', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'PoliticalView' => [ 'shape' => 'CountryCode', ], 'IntendedUse' => [ 'shape' => 'SearchTextIntendedUse', ], 'NextToken' => [ 'shape' => 'Token', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], ], ], 'SearchTextRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'SearchTextRequestQueryIdString' => [ 'type' => 'string', 'max' => 500, 'min' => 1, 'sensitive' => true, ], 'SearchTextRequestQueryTextString' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'sensitive' => true, ], 'SearchTextResponse' => [ 'type' => 'structure', 'required' => [ 'PricingBucket', ], 'members' => [ 'PricingBucket' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amz-geo-pricing-bucket', ], 'ResultItems' => [ 'shape' => 'SearchTextResultItemList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'SearchTextResultItem' => [ 'type' => 'structure', 'required' => [ 'PlaceId', 'PlaceType', 'Title', ], 'members' => [ 'PlaceId' => [ 'shape' => 'SearchTextResultItemPlaceIdString', ], 'PlaceType' => [ 'shape' => 'PlaceType', ], 'Title' => [ 'shape' => 'SearchTextResultItemTitleString', ], 'Address' => [ 'shape' => 'Address', ], 'AddressNumberCorrected' => [ 'shape' => 'SensitiveBoolean', ], 'Position' => [ 'shape' => 'Position', ], 'Distance' => [ 'shape' => 'DistanceMeters', ], 'MapView' => [ 'shape' => 'BoundingBox', ], 'Categories' => [ 'shape' => 'CategoryList', ], 'FoodTypes' => [ 'shape' => 'FoodTypeList', ], 'BusinessChains' => [ 'shape' => 'BusinessChainList', ], 'Contacts' => [ 'shape' => 'Contacts', ], 'OpeningHours' => [ 'shape' => 'OpeningHoursList', ], 'AccessPoints' => [ 'shape' => 'AccessPointList', ], 'AccessRestrictions' => [ 'shape' => 'AccessRestrictionList', ], 'TimeZone' => [ 'shape' => 'TimeZone', ], 'PoliticalView' => [ 'shape' => 'CountryCode3', ], 'Phonemes' => [ 'shape' => 'PhonemeDetails', ], ], ], 'SearchTextResultItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchTextResultItem', ], 'max' => 100, 'min' => 0, ], 'SearchTextResultItemPlaceIdString' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'sensitive' => true, ], 'SearchTextResultItemTitleString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'SecondaryAddressComponent' => [ 'type' => 'structure', 'required' => [ 'Number', ], 'members' => [ 'Number' => [ 'shape' => 'SecondaryAddressComponentNumberString', ], ], ], 'SecondaryAddressComponentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecondaryAddressComponent', ], 'max' => 1, 'min' => 0, ], 'SecondaryAddressComponentMatchScore' => [ 'type' => 'structure', 'members' => [ 'Number' => [ 'shape' => 'MatchScore', ], ], ], 'SecondaryAddressComponentMatchScoreList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecondaryAddressComponentMatchScore', ], ], 'SecondaryAddressComponentNumberString' => [ 'type' => 'string', 'max' => 10, 'min' => 0, 'sensitive' => true, ], 'SensitiveBoolean' => [ 'type' => 'boolean', 'box' => true, 'sensitive' => true, ], 'StreetComponents' => [ 'type' => 'structure', 'members' => [ 'BaseName' => [ 'shape' => 'StreetComponentsBaseNameString', ], 'Type' => [ 'shape' => 'StreetComponentsTypeString', ], 'TypePlacement' => [ 'shape' => 'TypePlacement', ], 'TypeSeparator' => [ 'shape' => 'TypeSeparator', ], 'Prefix' => [ 'shape' => 'StreetComponentsPrefixString', ], 'Suffix' => [ 'shape' => 'StreetComponentsSuffixString', ], 'Direction' => [ 'shape' => 'StreetComponentsDirectionString', ], 'Language' => [ 'shape' => 'LanguageTag', ], ], ], 'StreetComponentsBaseNameString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'StreetComponentsDirectionString' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'sensitive' => true, ], 'StreetComponentsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StreetComponents', ], 'max' => 100, 'min' => 0, ], 'StreetComponentsPrefixString' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'sensitive' => true, ], 'StreetComponentsSuffixString' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'sensitive' => true, ], 'StreetComponentsTypeString' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'sensitive' => true, ], 'String' => [ 'type' => 'string', ], 'SubRegion' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'SubRegionCodeString', ], 'Name' => [ 'shape' => 'SubRegionNameString', ], ], ], 'SubRegionCodeString' => [ 'type' => 'string', 'max' => 3, 'min' => 0, 'sensitive' => true, ], 'SubRegionHighlights' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'HighlightList', ], 'Name' => [ 'shape' => 'HighlightList', ], ], ], 'SubRegionNameString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'SuggestAdditionalFeature' => [ 'type' => 'string', 'enum' => [ 'Core', 'TimeZone', 'Phonemes', 'Access', ], ], 'SuggestAdditionalFeatureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SuggestAdditionalFeature', ], 'max' => 4, 'min' => 1, ], 'SuggestAddressHighlights' => [ 'type' => 'structure', 'members' => [ 'Label' => [ 'shape' => 'HighlightList', ], ], ], 'SuggestFilter' => [ 'type' => 'structure', 'members' => [ 'BoundingBox' => [ 'shape' => 'BoundingBox', ], 'Circle' => [ 'shape' => 'FilterCircle', ], 'IncludeCountries' => [ 'shape' => 'CountryCodeList', ], ], ], 'SuggestHighlights' => [ 'type' => 'structure', 'members' => [ 'Title' => [ 'shape' => 'HighlightList', ], 'Address' => [ 'shape' => 'SuggestAddressHighlights', ], ], ], 'SuggestIntendedUse' => [ 'type' => 'string', 'enum' => [ 'SingleUse', ], ], 'SuggestPlaceResult' => [ 'type' => 'structure', 'members' => [ 'PlaceId' => [ 'shape' => 'SuggestPlaceResultPlaceIdString', ], 'PlaceType' => [ 'shape' => 'PlaceType', ], 'Address' => [ 'shape' => 'Address', ], 'Position' => [ 'shape' => 'Position', ], 'Distance' => [ 'shape' => 'DistanceMeters', ], 'MapView' => [ 'shape' => 'BoundingBox', ], 'Categories' => [ 'shape' => 'CategoryList', ], 'FoodTypes' => [ 'shape' => 'FoodTypeList', ], 'BusinessChains' => [ 'shape' => 'BusinessChainList', ], 'AccessPoints' => [ 'shape' => 'AccessPointList', ], 'AccessRestrictions' => [ 'shape' => 'AccessRestrictionList', ], 'TimeZone' => [ 'shape' => 'TimeZone', ], 'PoliticalView' => [ 'shape' => 'CountryCode3', ], 'Phonemes' => [ 'shape' => 'PhonemeDetails', ], ], ], 'SuggestPlaceResultPlaceIdString' => [ 'type' => 'string', 'max' => 500, 'min' => 1, 'sensitive' => true, ], 'SuggestQueryResult' => [ 'type' => 'structure', 'members' => [ 'QueryId' => [ 'shape' => 'SuggestQueryResultQueryIdString', ], 'QueryType' => [ 'shape' => 'QueryType', ], ], ], 'SuggestQueryResultQueryIdString' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'sensitive' => true, ], 'SuggestRequest' => [ 'type' => 'structure', 'required' => [ 'QueryText', ], 'members' => [ 'QueryText' => [ 'shape' => 'SuggestRequestQueryTextString', ], 'MaxResults' => [ 'shape' => 'SuggestRequestMaxResultsInteger', ], 'MaxQueryRefinements' => [ 'shape' => 'SuggestRequestMaxQueryRefinementsInteger', ], 'BiasPosition' => [ 'shape' => 'Position', ], 'Filter' => [ 'shape' => 'SuggestFilter', ], 'AdditionalFeatures' => [ 'shape' => 'SuggestAdditionalFeatureList', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'PoliticalView' => [ 'shape' => 'CountryCode', ], 'IntendedUse' => [ 'shape' => 'SuggestIntendedUse', ], 'Key' => [ 'shape' => 'ApiKey', 'location' => 'querystring', 'locationName' => 'key', ], ], ], 'SuggestRequestMaxQueryRefinementsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 1, ], 'SuggestRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'SuggestRequestQueryTextString' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'sensitive' => true, ], 'SuggestResponse' => [ 'type' => 'structure', 'required' => [ 'PricingBucket', ], 'members' => [ 'PricingBucket' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amz-geo-pricing-bucket', ], 'ResultItems' => [ 'shape' => 'SuggestResultItemList', ], 'QueryRefinements' => [ 'shape' => 'QueryRefinementList', ], ], ], 'SuggestResultItem' => [ 'type' => 'structure', 'required' => [ 'Title', 'SuggestResultItemType', ], 'members' => [ 'Title' => [ 'shape' => 'SuggestResultItemTitleString', ], 'SuggestResultItemType' => [ 'shape' => 'SuggestResultItemType', ], 'Place' => [ 'shape' => 'SuggestPlaceResult', ], 'Query' => [ 'shape' => 'SuggestQueryResult', ], 'Highlights' => [ 'shape' => 'SuggestHighlights', ], ], ], 'SuggestResultItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SuggestResultItem', ], 'max' => 100, 'min' => 0, ], 'SuggestResultItemTitleString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'SuggestResultItemType' => [ 'type' => 'string', 'enum' => [ 'Place', 'Query', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'TimeZone' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'TimeZoneNameString', ], 'Offset' => [ 'shape' => 'TimeZoneOffsetString', ], 'OffsetSeconds' => [ 'shape' => 'TimeZoneOffsetSecondsLong', ], ], ], 'TimeZoneNameString' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'TimeZoneOffsetSecondsLong' => [ 'type' => 'long', 'min' => 0, 'sensitive' => true, ], 'TimeZoneOffsetString' => [ 'type' => 'string', 'max' => 6, 'min' => 0, 'sensitive' => true, ], 'Token' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, ], 'TypePlacement' => [ 'type' => 'string', 'enum' => [ 'BeforeBaseName', 'AfterBaseName', ], ], 'TypeSeparator' => [ 'type' => 'string', 'max' => 1, 'min' => 0, 'pattern' => '$|^ ', ], 'UspsZip' => [ 'type' => 'structure', 'members' => [ 'ZipClassificationCode' => [ 'shape' => 'ZipClassificationCode', ], ], ], 'UspsZipPlus4' => [ 'type' => 'structure', 'members' => [ 'RecordTypeCode' => [ 'shape' => 'RecordTypeCode', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', 'Reason', 'FieldList', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], 'Reason' => [ 'shape' => 'ValidationExceptionReason', 'locationName' => 'reason', ], 'FieldList' => [ 'shape' => 'ValidationExceptionFieldList', 'locationName' => 'fieldList', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'Name', 'Message', ], 'members' => [ 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'UnknownOperation', 'Missing', 'CannotParse', 'FieldValidationFailed', 'Other', 'UnknownField', ], ], 'ZipClassificationCode' => [ 'type' => 'string', 'enum' => [ 'Military', 'PostOfficeBoxes', 'Unique', ], 'sensitive' => true, ], ],];
