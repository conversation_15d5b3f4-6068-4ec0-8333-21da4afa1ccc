<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Numbers;

use Twilio\Domain;
use Twilio\Exceptions\TwilioException;
use Twilio\InstanceContext;
use Twilio\Rest\Numbers\V1\BulkEligibilityList;
use Twilio\Rest\Numbers\V1\EligibilityList;
use Twilio\Rest\Numbers\V1\PortingPortInList;
use Twilio\Rest\Numbers\V1\PortingPortInPhoneNumberList;
use Twilio\Rest\Numbers\V1\PortingPortabilityList;
use Twilio\Rest\Numbers\V1\PortingWebhookConfigurationList;
use Twilio\Rest\Numbers\V1\PortingWebhookConfigurationDeleteList;
use Twilio\Rest\Numbers\V1\PortingWebhookConfigurationFetchList;
use Twilio\Rest\Numbers\V1\SigningRequestConfigurationList;
use Twilio\Version;

/**
 * @property BulkEligibilityList $bulkEligibilities
 * @property EligibilityList $eligibilities
 * @property PortingPortInList $portingPortIns
 * @property PortingPortInPhoneNumberList $portingPortInPhoneNumber
 * @property PortingPortabilityList $portingPortabilities
 * @property PortingWebhookConfigurationList $portingWebhookConfigurations
 * @property PortingWebhookConfigurationDeleteList $portingWebhookConfigurationsDelete
 * @property PortingWebhookConfigurationFetchList $portingWebhookConfigurationFetch
 * @property SigningRequestConfigurationList $signingRequestConfigurations
 * @method \Twilio\Rest\Numbers\V1\BulkEligibilityContext bulkEligibilities(string $requestId)
 * @method \Twilio\Rest\Numbers\V1\PortingPortInContext portingPortIns(string $portInRequestSid)
 * @method \Twilio\Rest\Numbers\V1\PortingPortInPhoneNumberContext portingPortInPhoneNumber(string $portInRequestSid, string $phoneNumberSid)
 * @method \Twilio\Rest\Numbers\V1\PortingPortabilityContext portingPortabilities(string $phoneNumber)
 * @method \Twilio\Rest\Numbers\V1\PortingWebhookConfigurationDeleteContext portingWebhookConfigurationsDelete(string $webhookType)
 */
class V1 extends Version
{
    protected $_bulkEligibilities;
    protected $_eligibilities;
    protected $_portingPortIns;
    protected $_portingPortInPhoneNumber;
    protected $_portingPortabilities;
    protected $_portingWebhookConfigurations;
    protected $_portingWebhookConfigurationsDelete;
    protected $_portingWebhookConfigurationFetch;
    protected $_signingRequestConfigurations;

    /**
     * Construct the V1 version of Numbers
     *
     * @param Domain $domain Domain that contains the version
     */
    public function __construct(Domain $domain)
    {
        parent::__construct($domain);
        $this->version = 'v1';
    }

    protected function getBulkEligibilities(): BulkEligibilityList
    {
        if (!$this->_bulkEligibilities) {
            $this->_bulkEligibilities = new BulkEligibilityList($this);
        }
        return $this->_bulkEligibilities;
    }

    protected function getEligibilities(): EligibilityList
    {
        if (!$this->_eligibilities) {
            $this->_eligibilities = new EligibilityList($this);
        }
        return $this->_eligibilities;
    }

    protected function getPortingPortIns(): PortingPortInList
    {
        if (!$this->_portingPortIns) {
            $this->_portingPortIns = new PortingPortInList($this);
        }
        return $this->_portingPortIns;
    }

    protected function getPortingPortInPhoneNumber(): PortingPortInPhoneNumberList
    {
        if (!$this->_portingPortInPhoneNumber) {
            $this->_portingPortInPhoneNumber = new PortingPortInPhoneNumberList($this);
        }
        return $this->_portingPortInPhoneNumber;
    }

    protected function getPortingPortabilities(): PortingPortabilityList
    {
        if (!$this->_portingPortabilities) {
            $this->_portingPortabilities = new PortingPortabilityList($this);
        }
        return $this->_portingPortabilities;
    }

    protected function getPortingWebhookConfigurations(): PortingWebhookConfigurationList
    {
        if (!$this->_portingWebhookConfigurations) {
            $this->_portingWebhookConfigurations = new PortingWebhookConfigurationList($this);
        }
        return $this->_portingWebhookConfigurations;
    }

    protected function getPortingWebhookConfigurationsDelete(): PortingWebhookConfigurationDeleteList
    {
        if (!$this->_portingWebhookConfigurationsDelete) {
            $this->_portingWebhookConfigurationsDelete = new PortingWebhookConfigurationDeleteList($this);
        }
        return $this->_portingWebhookConfigurationsDelete;
    }

    protected function getPortingWebhookConfigurationFetch(): PortingWebhookConfigurationFetchList
    {
        if (!$this->_portingWebhookConfigurationFetch) {
            $this->_portingWebhookConfigurationFetch = new PortingWebhookConfigurationFetchList($this);
        }
        return $this->_portingWebhookConfigurationFetch;
    }

    protected function getSigningRequestConfigurations(): SigningRequestConfigurationList
    {
        if (!$this->_signingRequestConfigurations) {
            $this->_signingRequestConfigurations = new SigningRequestConfigurationList($this);
        }
        return $this->_signingRequestConfigurations;
    }

    /**
     * Magic getter to lazy load root resources
     *
     * @param string $name Resource to return
     * @return \Twilio\ListResource The requested resource
     * @throws TwilioException For unknown resource
     */
    public function __get(string $name)
    {
        $method = 'get' . \ucfirst($name);
        if (\method_exists($this, $method)) {
            return $this->$method();
        }

        throw new TwilioException('Unknown resource ' . $name);
    }

    /**
     * Magic caller to get resource contexts
     *
     * @param string $name Resource to return
     * @param array $arguments Context parameters
     * @return InstanceContext The requested resource context
     * @throws TwilioException For unknown resource
     */
    public function __call(string $name, array $arguments): InstanceContext
    {
        $property = $this->$name;
        if (\method_exists($property, 'getContext')) {
            return \call_user_func_array(array($property, 'getContext'), $arguments);
        }

        throw new TwilioException('Resource does not have a context');
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Numbers.V1]';
    }
}
