<?php
namespace Aws\Shield;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS Shield** service.
 * @method \Aws\Result associateDRTLogBucket(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateDRTLogBucketAsync(array $args = [])
 * @method \Aws\Result associateDRTRole(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateDRTRoleAsync(array $args = [])
 * @method \Aws\Result associateHealthCheck(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateHealthCheckAsync(array $args = [])
 * @method \Aws\Result associateProactiveEngagementDetails(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateProactiveEngagementDetailsAsync(array $args = [])
 * @method \Aws\Result createProtection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createProtectionAsync(array $args = [])
 * @method \Aws\Result createProtectionGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createProtectionGroupAsync(array $args = [])
 * @method \Aws\Result createSubscription(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createSubscriptionAsync(array $args = [])
 * @method \Aws\Result deleteProtection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteProtectionAsync(array $args = [])
 * @method \Aws\Result deleteProtectionGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteProtectionGroupAsync(array $args = [])
 * @method \Aws\Result deleteSubscription(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteSubscriptionAsync(array $args = [])
 * @method \Aws\Result describeAttack(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeAttackAsync(array $args = [])
 * @method \Aws\Result describeAttackStatistics(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeAttackStatisticsAsync(array $args = [])
 * @method \Aws\Result describeDRTAccess(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeDRTAccessAsync(array $args = [])
 * @method \Aws\Result describeEmergencyContactSettings(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeEmergencyContactSettingsAsync(array $args = [])
 * @method \Aws\Result describeProtection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeProtectionAsync(array $args = [])
 * @method \Aws\Result describeProtectionGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeProtectionGroupAsync(array $args = [])
 * @method \Aws\Result describeSubscription(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeSubscriptionAsync(array $args = [])
 * @method \Aws\Result disableApplicationLayerAutomaticResponse(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disableApplicationLayerAutomaticResponseAsync(array $args = [])
 * @method \Aws\Result disableProactiveEngagement(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disableProactiveEngagementAsync(array $args = [])
 * @method \Aws\Result disassociateDRTLogBucket(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateDRTLogBucketAsync(array $args = [])
 * @method \Aws\Result disassociateDRTRole(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateDRTRoleAsync(array $args = [])
 * @method \Aws\Result disassociateHealthCheck(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateHealthCheckAsync(array $args = [])
 * @method \Aws\Result enableApplicationLayerAutomaticResponse(array $args = [])
 * @method \GuzzleHttp\Promise\Promise enableApplicationLayerAutomaticResponseAsync(array $args = [])
 * @method \Aws\Result enableProactiveEngagement(array $args = [])
 * @method \GuzzleHttp\Promise\Promise enableProactiveEngagementAsync(array $args = [])
 * @method \Aws\Result getSubscriptionState(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSubscriptionStateAsync(array $args = [])
 * @method \Aws\Result listAttacks(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAttacksAsync(array $args = [])
 * @method \Aws\Result listProtectionGroups(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listProtectionGroupsAsync(array $args = [])
 * @method \Aws\Result listProtections(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listProtectionsAsync(array $args = [])
 * @method \Aws\Result listResourcesInProtectionGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listResourcesInProtectionGroupAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateApplicationLayerAutomaticResponse(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateApplicationLayerAutomaticResponseAsync(array $args = [])
 * @method \Aws\Result updateEmergencyContactSettings(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateEmergencyContactSettingsAsync(array $args = [])
 * @method \Aws\Result updateProtectionGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateProtectionGroupAsync(array $args = [])
 * @method \Aws\Result updateSubscription(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateSubscriptionAsync(array $args = [])
 */
class ShieldClient extends AwsClient {}
