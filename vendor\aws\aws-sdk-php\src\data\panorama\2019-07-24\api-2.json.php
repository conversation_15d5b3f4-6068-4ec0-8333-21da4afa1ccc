<?php
// This file was auto-generated from sdk-root/src/data/panorama/2019-07-24/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2019-07-24', 'endpointPrefix' => 'panorama', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'Panorama', 'serviceFullName' => 'AWS Panorama', 'serviceId' => 'Panorama', 'signatureVersion' => 'v4', 'signingName' => 'panorama', 'uid' => 'panorama-2019-07-24', ], 'operations' => [ 'CreateApplicationInstance' => [ 'name' => 'CreateApplicationInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/application-instances', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateApplicationInstanceRequest', ], 'output' => [ 'shape' => 'CreateApplicationInstanceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateJobForDevices' => [ 'name' => 'CreateJobForDevices', 'http' => [ 'method' => 'POST', 'requestUri' => '/jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateJobForDevicesRequest', ], 'output' => [ 'shape' => 'CreateJobForDevicesResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateNodeFromTemplateJob' => [ 'name' => 'CreateNodeFromTemplateJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/packages/template-job', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateNodeFromTemplateJobRequest', ], 'output' => [ 'shape' => 'CreateNodeFromTemplateJobResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreatePackage' => [ 'name' => 'CreatePackage', 'http' => [ 'method' => 'POST', 'requestUri' => '/packages', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreatePackageRequest', ], 'output' => [ 'shape' => 'CreatePackageResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreatePackageImportJob' => [ 'name' => 'CreatePackageImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/packages/import-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreatePackageImportJobRequest', ], 'output' => [ 'shape' => 'CreatePackageImportJobResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteDevice' => [ 'name' => 'DeleteDevice', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/devices/{DeviceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteDeviceRequest', ], 'output' => [ 'shape' => 'DeleteDeviceResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeletePackage' => [ 'name' => 'DeletePackage', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/packages/{PackageId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeletePackageRequest', ], 'output' => [ 'shape' => 'DeletePackageResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeregisterPackageVersion' => [ 'name' => 'DeregisterPackageVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/packages/{PackageId}/versions/{PackageVersion}/patch/{PatchVersion}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeregisterPackageVersionRequest', ], 'output' => [ 'shape' => 'DeregisterPackageVersionResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeApplicationInstance' => [ 'name' => 'DescribeApplicationInstance', 'http' => [ 'method' => 'GET', 'requestUri' => '/application-instances/{ApplicationInstanceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeApplicationInstanceRequest', ], 'output' => [ 'shape' => 'DescribeApplicationInstanceResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeApplicationInstanceDetails' => [ 'name' => 'DescribeApplicationInstanceDetails', 'http' => [ 'method' => 'GET', 'requestUri' => '/application-instances/{ApplicationInstanceId}/details', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeApplicationInstanceDetailsRequest', ], 'output' => [ 'shape' => 'DescribeApplicationInstanceDetailsResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeDevice' => [ 'name' => 'DescribeDevice', 'http' => [ 'method' => 'GET', 'requestUri' => '/devices/{DeviceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeDeviceRequest', ], 'output' => [ 'shape' => 'DescribeDeviceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeDeviceJob' => [ 'name' => 'DescribeDeviceJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/jobs/{JobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeDeviceJobRequest', ], 'output' => [ 'shape' => 'DescribeDeviceJobResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeNode' => [ 'name' => 'DescribeNode', 'http' => [ 'method' => 'GET', 'requestUri' => '/nodes/{NodeId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeNodeRequest', ], 'output' => [ 'shape' => 'DescribeNodeResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeNodeFromTemplateJob' => [ 'name' => 'DescribeNodeFromTemplateJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/packages/template-job/{JobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeNodeFromTemplateJobRequest', ], 'output' => [ 'shape' => 'DescribeNodeFromTemplateJobResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribePackage' => [ 'name' => 'DescribePackage', 'http' => [ 'method' => 'GET', 'requestUri' => '/packages/metadata/{PackageId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribePackageRequest', ], 'output' => [ 'shape' => 'DescribePackageResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribePackageImportJob' => [ 'name' => 'DescribePackageImportJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/packages/import-jobs/{JobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribePackageImportJobRequest', ], 'output' => [ 'shape' => 'DescribePackageImportJobResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribePackageVersion' => [ 'name' => 'DescribePackageVersion', 'http' => [ 'method' => 'GET', 'requestUri' => '/packages/metadata/{PackageId}/versions/{PackageVersion}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribePackageVersionRequest', ], 'output' => [ 'shape' => 'DescribePackageVersionResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListApplicationInstanceDependencies' => [ 'name' => 'ListApplicationInstanceDependencies', 'http' => [ 'method' => 'GET', 'requestUri' => '/application-instances/{ApplicationInstanceId}/package-dependencies', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListApplicationInstanceDependenciesRequest', ], 'output' => [ 'shape' => 'ListApplicationInstanceDependenciesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListApplicationInstanceNodeInstances' => [ 'name' => 'ListApplicationInstanceNodeInstances', 'http' => [ 'method' => 'GET', 'requestUri' => '/application-instances/{ApplicationInstanceId}/node-instances', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListApplicationInstanceNodeInstancesRequest', ], 'output' => [ 'shape' => 'ListApplicationInstanceNodeInstancesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListApplicationInstances' => [ 'name' => 'ListApplicationInstances', 'http' => [ 'method' => 'GET', 'requestUri' => '/application-instances', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListApplicationInstancesRequest', ], 'output' => [ 'shape' => 'ListApplicationInstancesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDevices' => [ 'name' => 'ListDevices', 'http' => [ 'method' => 'GET', 'requestUri' => '/devices', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDevicesRequest', ], 'output' => [ 'shape' => 'ListDevicesResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDevicesJobs' => [ 'name' => 'ListDevicesJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDevicesJobsRequest', ], 'output' => [ 'shape' => 'ListDevicesJobsResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListNodeFromTemplateJobs' => [ 'name' => 'ListNodeFromTemplateJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/packages/template-job', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListNodeFromTemplateJobsRequest', ], 'output' => [ 'shape' => 'ListNodeFromTemplateJobsResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListNodes' => [ 'name' => 'ListNodes', 'http' => [ 'method' => 'GET', 'requestUri' => '/nodes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListNodesRequest', ], 'output' => [ 'shape' => 'ListNodesResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListPackageImportJobs' => [ 'name' => 'ListPackageImportJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/packages/import-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPackageImportJobsRequest', ], 'output' => [ 'shape' => 'ListPackageImportJobsResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListPackages' => [ 'name' => 'ListPackages', 'http' => [ 'method' => 'GET', 'requestUri' => '/packages', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPackagesRequest', ], 'output' => [ 'shape' => 'ListPackagesResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ProvisionDevice' => [ 'name' => 'ProvisionDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/devices', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ProvisionDeviceRequest', ], 'output' => [ 'shape' => 'ProvisionDeviceResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'RegisterPackageVersion' => [ 'name' => 'RegisterPackageVersion', 'http' => [ 'method' => 'PUT', 'requestUri' => '/packages/{PackageId}/versions/{PackageVersion}/patch/{PatchVersion}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RegisterPackageVersionRequest', ], 'output' => [ 'shape' => 'RegisterPackageVersionResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'RemoveApplicationInstance' => [ 'name' => 'RemoveApplicationInstance', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/application-instances/{ApplicationInstanceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RemoveApplicationInstanceRequest', ], 'output' => [ 'shape' => 'RemoveApplicationInstanceResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'SignalApplicationInstanceNodeInstances' => [ 'name' => 'SignalApplicationInstanceNodeInstances', 'http' => [ 'method' => 'PUT', 'requestUri' => '/application-instances/{ApplicationInstanceId}/node-signals', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SignalApplicationInstanceNodeInstancesRequest', ], 'output' => [ 'shape' => 'SignalApplicationInstanceNodeInstancesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateDeviceMetadata' => [ 'name' => 'UpdateDeviceMetadata', 'http' => [ 'method' => 'PUT', 'requestUri' => '/devices/{DeviceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDeviceMetadataRequest', ], 'output' => [ 'shape' => 'UpdateDeviceMetadataResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AlternateSoftwareMetadata' => [ 'type' => 'structure', 'members' => [ 'Version' => [ 'shape' => 'Version', ], ], ], 'AlternateSoftwares' => [ 'type' => 'list', 'member' => [ 'shape' => 'AlternateSoftwareMetadata', ], ], 'ApplicationInstance' => [ 'type' => 'structure', 'members' => [ 'ApplicationInstanceId' => [ 'shape' => 'ApplicationInstanceId', ], 'Arn' => [ 'shape' => 'ApplicationInstanceArn', ], 'CreatedTime' => [ 'shape' => 'TimeStamp', ], 'DefaultRuntimeContextDevice' => [ 'shape' => 'DefaultRuntimeContextDevice', ], 'DefaultRuntimeContextDeviceName' => [ 'shape' => 'DeviceName', ], 'Description' => [ 'shape' => 'Description', ], 'HealthStatus' => [ 'shape' => 'ApplicationInstanceHealthStatus', ], 'Name' => [ 'shape' => 'ApplicationInstanceName', ], 'RuntimeContextStates' => [ 'shape' => 'ReportedRuntimeContextStates', ], 'Status' => [ 'shape' => 'ApplicationInstanceStatus', ], 'StatusDescription' => [ 'shape' => 'ApplicationInstanceStatusDescription', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'ApplicationInstanceArn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ApplicationInstanceHealthStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'ERROR', 'NOT_AVAILABLE', ], ], 'ApplicationInstanceId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_]+$', ], 'ApplicationInstanceName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_]+$', ], 'ApplicationInstanceStatus' => [ 'type' => 'string', 'enum' => [ 'DEPLOYMENT_PENDING', 'DEPLOYMENT_REQUESTED', 'DEPLOYMENT_IN_PROGRESS', 'DEPLOYMENT_ERROR', 'DEPLOYMENT_SUCCEEDED', 'REMOVAL_PENDING', 'REMOVAL_REQUESTED', 'REMOVAL_IN_PROGRESS', 'REMOVAL_FAILED', 'REMOVAL_SUCCEEDED', 'DEPLOYMENT_FAILED', ], ], 'ApplicationInstanceStatusDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ApplicationInstances' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationInstance', ], ], 'Boolean' => [ 'type' => 'boolean', ], 'Bucket' => [ 'type' => 'string', ], 'BucketName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^.+$', ], 'Certificates' => [ 'type' => 'blob', ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_]+$', ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'Message', 'ResourceId', 'ResourceType', ], 'members' => [ 'ErrorArguments' => [ 'shape' => 'ConflictExceptionErrorArgumentList', ], 'ErrorId' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ConflictExceptionErrorArgument' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'ConflictExceptionErrorArgumentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConflictExceptionErrorArgument', ], ], 'ConnectionType' => [ 'type' => 'string', 'enum' => [ 'STATIC_IP', 'DHCP', ], ], 'CreateApplicationInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'DefaultRuntimeContextDevice', 'ManifestPayload', ], 'members' => [ 'ApplicationInstanceIdToReplace' => [ 'shape' => 'ApplicationInstanceId', ], 'DefaultRuntimeContextDevice' => [ 'shape' => 'DefaultRuntimeContextDevice', ], 'Description' => [ 'shape' => 'Description', ], 'ManifestOverridesPayload' => [ 'shape' => 'ManifestOverridesPayload', ], 'ManifestPayload' => [ 'shape' => 'ManifestPayload', ], 'Name' => [ 'shape' => 'ApplicationInstanceName', ], 'RuntimeRoleArn' => [ 'shape' => 'RuntimeRoleArn', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateApplicationInstanceResponse' => [ 'type' => 'structure', 'required' => [ 'ApplicationInstanceId', ], 'members' => [ 'ApplicationInstanceId' => [ 'shape' => 'ApplicationInstanceId', ], ], ], 'CreateJobForDevicesRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceIds', 'JobType', ], 'members' => [ 'DeviceIds' => [ 'shape' => 'DeviceIdList', ], 'DeviceJobConfig' => [ 'shape' => 'DeviceJobConfig', ], 'JobType' => [ 'shape' => 'JobType', ], ], ], 'CreateJobForDevicesResponse' => [ 'type' => 'structure', 'required' => [ 'Jobs', ], 'members' => [ 'Jobs' => [ 'shape' => 'JobList', ], ], ], 'CreateNodeFromTemplateJobRequest' => [ 'type' => 'structure', 'required' => [ 'NodeName', 'OutputPackageName', 'OutputPackageVersion', 'TemplateParameters', 'TemplateType', ], 'members' => [ 'JobTags' => [ 'shape' => 'JobTagsList', ], 'NodeDescription' => [ 'shape' => 'Description', ], 'NodeName' => [ 'shape' => 'NodeName', ], 'OutputPackageName' => [ 'shape' => 'NodePackageName', ], 'OutputPackageVersion' => [ 'shape' => 'NodePackageVersion', ], 'TemplateParameters' => [ 'shape' => 'TemplateParametersMap', ], 'TemplateType' => [ 'shape' => 'TemplateType', ], ], ], 'CreateNodeFromTemplateJobResponse' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'CreatePackageImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'ClientToken', 'InputConfig', 'JobType', 'OutputConfig', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', ], 'InputConfig' => [ 'shape' => 'PackageImportJobInputConfig', ], 'JobTags' => [ 'shape' => 'JobTagsList', ], 'JobType' => [ 'shape' => 'PackageImportJobType', ], 'OutputConfig' => [ 'shape' => 'PackageImportJobOutputConfig', ], ], ], 'CreatePackageImportJobResponse' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'CreatePackageRequest' => [ 'type' => 'structure', 'required' => [ 'PackageName', ], 'members' => [ 'PackageName' => [ 'shape' => 'NodePackageName', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreatePackageResponse' => [ 'type' => 'structure', 'required' => [ 'StorageLocation', ], 'members' => [ 'Arn' => [ 'shape' => 'NodePackageArn', ], 'PackageId' => [ 'shape' => 'NodePackageId', ], 'StorageLocation' => [ 'shape' => 'StorageLocation', ], ], ], 'CreatedTime' => [ 'type' => 'timestamp', ], 'CurrentSoftware' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'DefaultGateway' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^.+$', ], 'DefaultRuntimeContextDevice' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_]+$', ], 'DeleteDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceId', ], 'members' => [ 'DeviceId' => [ 'shape' => 'DeviceId', 'location' => 'uri', 'locationName' => 'DeviceId', ], ], ], 'DeleteDeviceResponse' => [ 'type' => 'structure', 'members' => [ 'DeviceId' => [ 'shape' => 'DeviceId', ], ], ], 'DeletePackageRequest' => [ 'type' => 'structure', 'required' => [ 'PackageId', ], 'members' => [ 'ForceDelete' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'ForceDelete', ], 'PackageId' => [ 'shape' => 'NodePackageId', 'location' => 'uri', 'locationName' => 'PackageId', ], ], ], 'DeletePackageResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeregisterPackageVersionRequest' => [ 'type' => 'structure', 'required' => [ 'PackageId', 'PackageVersion', 'PatchVersion', ], 'members' => [ 'OwnerAccount' => [ 'shape' => 'PackageOwnerAccount', 'location' => 'querystring', 'locationName' => 'OwnerAccount', ], 'PackageId' => [ 'shape' => 'NodePackageId', 'location' => 'uri', 'locationName' => 'PackageId', ], 'PackageVersion' => [ 'shape' => 'NodePackageVersion', 'location' => 'uri', 'locationName' => 'PackageVersion', ], 'PatchVersion' => [ 'shape' => 'NodePackagePatchVersion', 'location' => 'uri', 'locationName' => 'PatchVersion', ], 'UpdatedLatestPatchVersion' => [ 'shape' => 'NodePackagePatchVersion', 'location' => 'querystring', 'locationName' => 'UpdatedLatestPatchVersion', ], ], ], 'DeregisterPackageVersionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeApplicationInstanceDetailsRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationInstanceId', ], 'members' => [ 'ApplicationInstanceId' => [ 'shape' => 'ApplicationInstanceId', 'location' => 'uri', 'locationName' => 'ApplicationInstanceId', ], ], ], 'DescribeApplicationInstanceDetailsResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationInstanceId' => [ 'shape' => 'ApplicationInstanceId', ], 'ApplicationInstanceIdToReplace' => [ 'shape' => 'ApplicationInstanceId', ], 'CreatedTime' => [ 'shape' => 'TimeStamp', ], 'DefaultRuntimeContextDevice' => [ 'shape' => 'DefaultRuntimeContextDevice', ], 'Description' => [ 'shape' => 'Description', ], 'ManifestOverridesPayload' => [ 'shape' => 'ManifestOverridesPayload', ], 'ManifestPayload' => [ 'shape' => 'ManifestPayload', ], 'Name' => [ 'shape' => 'ApplicationInstanceName', ], ], ], 'DescribeApplicationInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationInstanceId', ], 'members' => [ 'ApplicationInstanceId' => [ 'shape' => 'ApplicationInstanceId', 'location' => 'uri', 'locationName' => 'ApplicationInstanceId', ], ], ], 'DescribeApplicationInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationInstanceId' => [ 'shape' => 'ApplicationInstanceId', ], 'ApplicationInstanceIdToReplace' => [ 'shape' => 'ApplicationInstanceId', ], 'Arn' => [ 'shape' => 'ApplicationInstanceArn', ], 'CreatedTime' => [ 'shape' => 'TimeStamp', ], 'DefaultRuntimeContextDevice' => [ 'shape' => 'DefaultRuntimeContextDevice', ], 'DefaultRuntimeContextDeviceName' => [ 'shape' => 'DeviceName', ], 'Description' => [ 'shape' => 'Description', ], 'HealthStatus' => [ 'shape' => 'ApplicationInstanceHealthStatus', ], 'LastUpdatedTime' => [ 'shape' => 'TimeStamp', ], 'Name' => [ 'shape' => 'ApplicationInstanceName', ], 'RuntimeContextStates' => [ 'shape' => 'ReportedRuntimeContextStates', ], 'RuntimeRoleArn' => [ 'shape' => 'RuntimeRoleArn', ], 'Status' => [ 'shape' => 'ApplicationInstanceStatus', ], 'StatusDescription' => [ 'shape' => 'ApplicationInstanceStatusDescription', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'DescribeDeviceJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'JobId', ], ], ], 'DescribeDeviceJobResponse' => [ 'type' => 'structure', 'members' => [ 'CreatedTime' => [ 'shape' => 'UpdateCreatedTime', ], 'DeviceArn' => [ 'shape' => 'DeviceArn', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], 'DeviceName' => [ 'shape' => 'DeviceName', ], 'DeviceType' => [ 'shape' => 'DeviceType', ], 'ImageVersion' => [ 'shape' => 'ImageVersion', ], 'JobId' => [ 'shape' => 'JobId', ], 'JobType' => [ 'shape' => 'JobType', ], 'Status' => [ 'shape' => 'UpdateProgress', ], ], ], 'DescribeDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceId', ], 'members' => [ 'DeviceId' => [ 'shape' => 'DeviceId', 'location' => 'uri', 'locationName' => 'DeviceId', ], ], ], 'DescribeDeviceResponse' => [ 'type' => 'structure', 'members' => [ 'AlternateSoftwares' => [ 'shape' => 'AlternateSoftwares', ], 'Arn' => [ 'shape' => 'DeviceArn', ], 'Brand' => [ 'shape' => 'DeviceBrand', ], 'CreatedTime' => [ 'shape' => 'CreatedTime', ], 'CurrentNetworkingStatus' => [ 'shape' => 'NetworkStatus', ], 'CurrentSoftware' => [ 'shape' => 'CurrentSoftware', ], 'Description' => [ 'shape' => 'Description', ], 'DeviceAggregatedStatus' => [ 'shape' => 'DeviceAggregatedStatus', ], 'DeviceConnectionStatus' => [ 'shape' => 'DeviceConnectionStatus', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], 'LatestAlternateSoftware' => [ 'shape' => 'LatestAlternateSoftware', ], 'LatestDeviceJob' => [ 'shape' => 'LatestDeviceJob', ], 'LatestSoftware' => [ 'shape' => 'LatestSoftware', ], 'LeaseExpirationTime' => [ 'shape' => 'LeaseExpirationTime', ], 'Name' => [ 'shape' => 'DeviceName', ], 'NetworkingConfiguration' => [ 'shape' => 'NetworkPayload', ], 'ProvisioningStatus' => [ 'shape' => 'DeviceStatus', ], 'SerialNumber' => [ 'shape' => 'DeviceSerialNumber', ], 'Tags' => [ 'shape' => 'TagMap', ], 'Type' => [ 'shape' => 'DeviceType', ], ], ], 'DescribeNodeFromTemplateJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'JobId', ], ], ], 'DescribeNodeFromTemplateJobResponse' => [ 'type' => 'structure', 'required' => [ 'CreatedTime', 'JobId', 'LastUpdatedTime', 'NodeName', 'OutputPackageName', 'OutputPackageVersion', 'Status', 'StatusMessage', 'TemplateParameters', 'TemplateType', ], 'members' => [ 'CreatedTime' => [ 'shape' => 'CreatedTime', ], 'JobId' => [ 'shape' => 'JobId', ], 'JobTags' => [ 'shape' => 'JobTagsList', ], 'LastUpdatedTime' => [ 'shape' => 'LastUpdatedTime', ], 'NodeDescription' => [ 'shape' => 'Description', ], 'NodeName' => [ 'shape' => 'NodeName', ], 'OutputPackageName' => [ 'shape' => 'NodePackageName', ], 'OutputPackageVersion' => [ 'shape' => 'NodePackageVersion', ], 'Status' => [ 'shape' => 'NodeFromTemplateJobStatus', ], 'StatusMessage' => [ 'shape' => 'NodeFromTemplateJobStatusMessage', ], 'TemplateParameters' => [ 'shape' => 'TemplateParametersMap', ], 'TemplateType' => [ 'shape' => 'TemplateType', ], ], ], 'DescribeNodeRequest' => [ 'type' => 'structure', 'required' => [ 'NodeId', ], 'members' => [ 'NodeId' => [ 'shape' => 'NodeId', 'location' => 'uri', 'locationName' => 'NodeId', ], 'OwnerAccount' => [ 'shape' => 'PackageOwnerAccount', 'location' => 'querystring', 'locationName' => 'OwnerAccount', ], ], ], 'DescribeNodeResponse' => [ 'type' => 'structure', 'required' => [ 'Category', 'CreatedTime', 'Description', 'LastUpdatedTime', 'Name', 'NodeId', 'NodeInterface', 'OwnerAccount', 'PackageId', 'PackageName', 'PackageVersion', 'PatchVersion', ], 'members' => [ 'AssetName' => [ 'shape' => 'NodeAssetName', ], 'Category' => [ 'shape' => 'NodeCategory', ], 'CreatedTime' => [ 'shape' => 'TimeStamp', ], 'Description' => [ 'shape' => 'Description', ], 'LastUpdatedTime' => [ 'shape' => 'TimeStamp', ], 'Name' => [ 'shape' => 'NodeName', ], 'NodeId' => [ 'shape' => 'NodeId', ], 'NodeInterface' => [ 'shape' => 'NodeInterface', ], 'OwnerAccount' => [ 'shape' => 'PackageOwnerAccount', ], 'PackageArn' => [ 'shape' => 'NodePackageArn', ], 'PackageId' => [ 'shape' => 'NodePackageId', ], 'PackageName' => [ 'shape' => 'NodePackageName', ], 'PackageVersion' => [ 'shape' => 'NodePackageVersion', ], 'PatchVersion' => [ 'shape' => 'NodePackagePatchVersion', ], ], ], 'DescribePackageImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'JobId', ], ], ], 'DescribePackageImportJobResponse' => [ 'type' => 'structure', 'required' => [ 'CreatedTime', 'InputConfig', 'JobId', 'JobType', 'LastUpdatedTime', 'Output', 'OutputConfig', 'Status', 'StatusMessage', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', ], 'CreatedTime' => [ 'shape' => 'CreatedTime', ], 'InputConfig' => [ 'shape' => 'PackageImportJobInputConfig', ], 'JobId' => [ 'shape' => 'JobId', ], 'JobTags' => [ 'shape' => 'JobTagsList', ], 'JobType' => [ 'shape' => 'PackageImportJobType', ], 'LastUpdatedTime' => [ 'shape' => 'LastUpdatedTime', ], 'Output' => [ 'shape' => 'PackageImportJobOutput', ], 'OutputConfig' => [ 'shape' => 'PackageImportJobOutputConfig', ], 'Status' => [ 'shape' => 'PackageImportJobStatus', ], 'StatusMessage' => [ 'shape' => 'PackageImportJobStatusMessage', ], ], ], 'DescribePackageRequest' => [ 'type' => 'structure', 'required' => [ 'PackageId', ], 'members' => [ 'PackageId' => [ 'shape' => 'NodePackageId', 'location' => 'uri', 'locationName' => 'PackageId', ], ], ], 'DescribePackageResponse' => [ 'type' => 'structure', 'required' => [ 'Arn', 'CreatedTime', 'PackageId', 'PackageName', 'StorageLocation', 'Tags', ], 'members' => [ 'Arn' => [ 'shape' => 'NodePackageArn', ], 'CreatedTime' => [ 'shape' => 'TimeStamp', ], 'PackageId' => [ 'shape' => 'NodePackageId', ], 'PackageName' => [ 'shape' => 'NodePackageName', ], 'ReadAccessPrincipalArns' => [ 'shape' => 'PrincipalArnsList', ], 'StorageLocation' => [ 'shape' => 'StorageLocation', ], 'Tags' => [ 'shape' => 'TagMap', ], 'WriteAccessPrincipalArns' => [ 'shape' => 'PrincipalArnsList', ], ], ], 'DescribePackageVersionRequest' => [ 'type' => 'structure', 'required' => [ 'PackageId', 'PackageVersion', ], 'members' => [ 'OwnerAccount' => [ 'shape' => 'PackageOwnerAccount', 'location' => 'querystring', 'locationName' => 'OwnerAccount', ], 'PackageId' => [ 'shape' => 'NodePackageId', 'location' => 'uri', 'locationName' => 'PackageId', ], 'PackageVersion' => [ 'shape' => 'NodePackageVersion', 'location' => 'uri', 'locationName' => 'PackageVersion', ], 'PatchVersion' => [ 'shape' => 'NodePackagePatchVersion', 'location' => 'querystring', 'locationName' => 'PatchVersion', ], ], ], 'DescribePackageVersionResponse' => [ 'type' => 'structure', 'required' => [ 'IsLatestPatch', 'PackageId', 'PackageName', 'PackageVersion', 'PatchVersion', 'Status', ], 'members' => [ 'IsLatestPatch' => [ 'shape' => 'Boolean', ], 'OwnerAccount' => [ 'shape' => 'PackageOwnerAccount', ], 'PackageArn' => [ 'shape' => 'NodePackageArn', ], 'PackageId' => [ 'shape' => 'NodePackageId', ], 'PackageName' => [ 'shape' => 'NodePackageName', ], 'PackageVersion' => [ 'shape' => 'NodePackageVersion', ], 'PatchVersion' => [ 'shape' => 'NodePackagePatchVersion', ], 'RegisteredTime' => [ 'shape' => 'TimeStamp', ], 'Status' => [ 'shape' => 'PackageVersionStatus', ], 'StatusDescription' => [ 'shape' => 'PackageVersionStatusDescription', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^.*$', ], 'DesiredState' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'STOPPED', 'REMOVED', ], ], 'Device' => [ 'type' => 'structure', 'members' => [ 'Brand' => [ 'shape' => 'DeviceBrand', ], 'CreatedTime' => [ 'shape' => 'CreatedTime', ], 'CurrentSoftware' => [ 'shape' => 'CurrentSoftware', ], 'Description' => [ 'shape' => 'Description', ], 'DeviceAggregatedStatus' => [ 'shape' => 'DeviceAggregatedStatus', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], 'LastUpdatedTime' => [ 'shape' => 'LastUpdatedTime', ], 'LatestDeviceJob' => [ 'shape' => 'LatestDeviceJob', ], 'LeaseExpirationTime' => [ 'shape' => 'LeaseExpirationTime', ], 'Name' => [ 'shape' => 'DeviceName', ], 'ProvisioningStatus' => [ 'shape' => 'DeviceStatus', ], 'Tags' => [ 'shape' => 'TagMap', ], 'Type' => [ 'shape' => 'DeviceType', ], ], ], 'DeviceAggregatedStatus' => [ 'type' => 'string', 'enum' => [ 'ERROR', 'AWAITING_PROVISIONING', 'PENDING', 'FAILED', 'DELETING', 'ONLINE', 'OFFLINE', 'LEASE_EXPIRED', 'UPDATE_NEEDED', 'REBOOTING', ], ], 'DeviceArn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'DeviceBrand' => [ 'type' => 'string', 'enum' => [ 'AWS_PANORAMA', 'LENOVO', ], ], 'DeviceConnectionStatus' => [ 'type' => 'string', 'enum' => [ 'ONLINE', 'OFFLINE', 'AWAITING_CREDENTIALS', 'NOT_AVAILABLE', 'ERROR', ], ], 'DeviceId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_]+$', ], 'DeviceIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceId', ], 'max' => 1, 'min' => 1, ], 'DeviceJob' => [ 'type' => 'structure', 'members' => [ 'CreatedTime' => [ 'shape' => 'CreatedTime', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], 'DeviceName' => [ 'shape' => 'DeviceName', ], 'JobId' => [ 'shape' => 'JobId', ], 'JobType' => [ 'shape' => 'JobType', ], ], ], 'DeviceJobConfig' => [ 'type' => 'structure', 'members' => [ 'OTAJobConfig' => [ 'shape' => 'OTAJobConfig', ], ], ], 'DeviceJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceJob', ], ], 'DeviceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Device', ], ], 'DeviceName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_]+$', ], 'DeviceReportedStatus' => [ 'type' => 'string', 'enum' => [ 'STOPPING', 'STOPPED', 'STOP_ERROR', 'REMOVAL_FAILED', 'REMOVAL_IN_PROGRESS', 'STARTING', 'RUNNING', 'INSTALL_ERROR', 'LAUNCHED', 'LAUNCH_ERROR', 'INSTALL_IN_PROGRESS', ], ], 'DeviceSerialNumber' => [ 'type' => 'string', 'pattern' => '^[0-9]{1,20}$', ], 'DeviceStatus' => [ 'type' => 'string', 'enum' => [ 'AWAITING_PROVISIONING', 'PENDING', 'SUCCEEDED', 'FAILED', 'ERROR', 'DELETING', ], ], 'DeviceType' => [ 'type' => 'string', 'enum' => [ 'PANORAMA_APPLIANCE_DEVELOPER_KIT', 'PANORAMA_APPLIANCE', ], ], 'Dns' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^.+$', ], 'DnsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Dns', ], ], 'EthernetPayload' => [ 'type' => 'structure', 'required' => [ 'ConnectionType', ], 'members' => [ 'ConnectionType' => [ 'shape' => 'ConnectionType', ], 'StaticIpConnectionInfo' => [ 'shape' => 'StaticIpConnectionInfo', ], ], ], 'EthernetStatus' => [ 'type' => 'structure', 'members' => [ 'ConnectionStatus' => [ 'shape' => 'NetworkConnectionStatus', ], 'HwAddress' => [ 'shape' => 'HwAddress', ], 'IpAddress' => [ 'shape' => 'IpAddress', ], ], ], 'HwAddress' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ImageVersion' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^.+$', ], 'InputPortList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeInputPort', ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'RetryAfterSeconds' => [ 'shape' => 'RetryAfterSeconds', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'IotThingName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'IpAddress' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d))(:(6553[0-5]|655[0-2]\\d|65[0-4]\\d{2}|6[0-4]\\d{3}|[1-5]\\d{4}|[1-9]\\d{0,3}))?$', ], 'IpAddressOrServerName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '(^([a-z0-9]+(-[a-z0-9]+)*\\.)+[a-z]{2,}$)|(^((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d))(:(6553[0-5]|655[0-2]\\d|65[0-4]\\d{2}|6[0-4]\\d{3}|[1-5]\\d{4}|[1-9]\\d{0,3}))?$)', ], 'Job' => [ 'type' => 'structure', 'members' => [ 'DeviceId' => [ 'shape' => 'DeviceId', ], 'JobId' => [ 'shape' => 'JobId', ], ], ], 'JobId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_]+$', ], 'JobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Job', ], ], 'JobResourceTags' => [ 'type' => 'structure', 'required' => [ 'ResourceType', 'Tags', ], 'members' => [ 'ResourceType' => [ 'shape' => 'JobResourceType', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'JobResourceType' => [ 'type' => 'string', 'enum' => [ 'PACKAGE', ], ], 'JobTagsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobResourceTags', ], ], 'JobType' => [ 'type' => 'string', 'enum' => [ 'OTA', 'REBOOT', ], ], 'LastUpdatedTime' => [ 'type' => 'timestamp', ], 'LatestAlternateSoftware' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'LatestDeviceJob' => [ 'type' => 'structure', 'members' => [ 'ImageVersion' => [ 'shape' => 'ImageVersion', ], 'JobType' => [ 'shape' => 'JobType', ], 'Status' => [ 'shape' => 'UpdateProgress', ], ], ], 'LatestSoftware' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'LeaseExpirationTime' => [ 'type' => 'timestamp', ], 'ListApplicationInstanceDependenciesRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationInstanceId', ], 'members' => [ 'ApplicationInstanceId' => [ 'shape' => 'ApplicationInstanceId', 'location' => 'uri', 'locationName' => 'ApplicationInstanceId', ], 'MaxResults' => [ 'shape' => 'MaxSize25', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListApplicationInstanceDependenciesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'PackageObjects' => [ 'shape' => 'PackageObjects', ], ], ], 'ListApplicationInstanceNodeInstancesRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationInstanceId', ], 'members' => [ 'ApplicationInstanceId' => [ 'shape' => 'ApplicationInstanceId', 'location' => 'uri', 'locationName' => 'ApplicationInstanceId', ], 'MaxResults' => [ 'shape' => 'MaxSize25', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListApplicationInstanceNodeInstancesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'NodeInstances' => [ 'shape' => 'NodeInstances', ], ], ], 'ListApplicationInstancesRequest' => [ 'type' => 'structure', 'members' => [ 'DeviceId' => [ 'shape' => 'DeviceId', 'location' => 'querystring', 'locationName' => 'deviceId', ], 'MaxResults' => [ 'shape' => 'MaxSize25', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'StatusFilter' => [ 'shape' => 'StatusFilter', 'location' => 'querystring', 'locationName' => 'statusFilter', ], ], ], 'ListApplicationInstancesResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationInstances' => [ 'shape' => 'ApplicationInstances', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDevicesJobsRequest' => [ 'type' => 'structure', 'members' => [ 'DeviceId' => [ 'shape' => 'DeviceId', 'location' => 'querystring', 'locationName' => 'DeviceId', ], 'MaxResults' => [ 'shape' => 'MaxSize25', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListDevicesJobsResponse' => [ 'type' => 'structure', 'members' => [ 'DeviceJobs' => [ 'shape' => 'DeviceJobList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDevicesRequest' => [ 'type' => 'structure', 'members' => [ 'DeviceAggregatedStatusFilter' => [ 'shape' => 'DeviceAggregatedStatus', 'location' => 'querystring', 'locationName' => 'DeviceAggregatedStatusFilter', ], 'MaxResults' => [ 'shape' => 'MaxSize25', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NameFilter' => [ 'shape' => 'NameFilter', 'location' => 'querystring', 'locationName' => 'NameFilter', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'SortBy' => [ 'shape' => 'ListDevicesSortBy', 'location' => 'querystring', 'locationName' => 'SortBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', 'location' => 'querystring', 'locationName' => 'SortOrder', ], ], ], 'ListDevicesResponse' => [ 'type' => 'structure', 'required' => [ 'Devices', ], 'members' => [ 'Devices' => [ 'shape' => 'DeviceList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDevicesSortBy' => [ 'type' => 'string', 'enum' => [ 'DEVICE_ID', 'CREATED_TIME', 'NAME', 'DEVICE_AGGREGATED_STATUS', ], ], 'ListNodeFromTemplateJobsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxSize25', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListNodeFromTemplateJobsResponse' => [ 'type' => 'structure', 'required' => [ 'NodeFromTemplateJobs', ], 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'NodeFromTemplateJobs' => [ 'shape' => 'NodeFromTemplateJobList', ], ], ], 'ListNodesRequest' => [ 'type' => 'structure', 'members' => [ 'Category' => [ 'shape' => 'NodeCategory', 'location' => 'querystring', 'locationName' => 'category', ], 'MaxResults' => [ 'shape' => 'MaxSize25', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'OwnerAccount' => [ 'shape' => 'PackageOwnerAccount', 'location' => 'querystring', 'locationName' => 'ownerAccount', ], 'PackageName' => [ 'shape' => 'NodePackageName', 'location' => 'querystring', 'locationName' => 'packageName', ], 'PackageVersion' => [ 'shape' => 'NodePackageVersion', 'location' => 'querystring', 'locationName' => 'packageVersion', ], 'PatchVersion' => [ 'shape' => 'NodePackagePatchVersion', 'location' => 'querystring', 'locationName' => 'patchVersion', ], ], ], 'ListNodesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'Nodes' => [ 'shape' => 'NodesList', ], ], ], 'ListPackageImportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxSize25', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListPackageImportJobsResponse' => [ 'type' => 'structure', 'required' => [ 'PackageImportJobs', ], 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'PackageImportJobs' => [ 'shape' => 'PackageImportJobList', ], ], ], 'ListPackagesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxSize25', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListPackagesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'Packages' => [ 'shape' => 'PackageList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'ManifestOverridesPayload' => [ 'type' => 'structure', 'members' => [ 'PayloadData' => [ 'shape' => 'ManifestOverridesPayloadData', ], ], 'union' => true, ], 'ManifestOverridesPayloadData' => [ 'type' => 'string', 'max' => 51200, 'min' => 0, 'pattern' => '^.*$', ], 'ManifestPayload' => [ 'type' => 'structure', 'members' => [ 'PayloadData' => [ 'shape' => 'ManifestPayloadData', ], ], 'union' => true, ], 'ManifestPayloadData' => [ 'type' => 'string', 'max' => 51200, 'min' => 1, 'pattern' => '^.+$', ], 'MarkLatestPatch' => [ 'type' => 'boolean', ], 'Mask' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^.+$', ], 'MaxConnections' => [ 'type' => 'integer', ], 'MaxSize25' => [ 'type' => 'integer', 'max' => 25, 'min' => 0, ], 'NameFilter' => [ 'type' => 'string', ], 'NetworkConnectionStatus' => [ 'type' => 'string', 'enum' => [ 'CONNECTED', 'NOT_CONNECTED', 'CONNECTING', ], ], 'NetworkPayload' => [ 'type' => 'structure', 'members' => [ 'Ethernet0' => [ 'shape' => 'EthernetPayload', ], 'Ethernet1' => [ 'shape' => 'EthernetPayload', ], 'Ntp' => [ 'shape' => 'NtpPayload', ], ], ], 'NetworkStatus' => [ 'type' => 'structure', 'members' => [ 'Ethernet0Status' => [ 'shape' => 'EthernetStatus', ], 'Ethernet1Status' => [ 'shape' => 'EthernetStatus', ], 'LastUpdatedTime' => [ 'shape' => 'LastUpdatedTime', ], 'NtpStatus' => [ 'shape' => 'NtpStatus', ], ], ], 'NextToken' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '^.+$', ], 'Node' => [ 'type' => 'structure', 'required' => [ 'Category', 'CreatedTime', 'Name', 'NodeId', 'PackageId', 'PackageName', 'PackageVersion', 'PatchVersion', ], 'members' => [ 'Category' => [ 'shape' => 'NodeCategory', ], 'CreatedTime' => [ 'shape' => 'TimeStamp', ], 'Description' => [ 'shape' => 'Description', ], 'Name' => [ 'shape' => 'NodeName', ], 'NodeId' => [ 'shape' => 'NodeId', ], 'OwnerAccount' => [ 'shape' => 'PackageOwnerAccount', ], 'PackageArn' => [ 'shape' => 'NodePackageArn', ], 'PackageId' => [ 'shape' => 'NodePackageId', ], 'PackageName' => [ 'shape' => 'NodePackageName', ], 'PackageVersion' => [ 'shape' => 'NodePackageVersion', ], 'PatchVersion' => [ 'shape' => 'NodePackagePatchVersion', ], ], ], 'NodeAssetName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_]+$', ], 'NodeCategory' => [ 'type' => 'string', 'enum' => [ 'BUSINESS_LOGIC', 'ML_MODEL', 'MEDIA_SOURCE', 'MEDIA_SINK', ], ], 'NodeFromTemplateJob' => [ 'type' => 'structure', 'members' => [ 'CreatedTime' => [ 'shape' => 'CreatedTime', ], 'JobId' => [ 'shape' => 'JobId', ], 'NodeName' => [ 'shape' => 'NodeName', ], 'Status' => [ 'shape' => 'NodeFromTemplateJobStatus', ], 'StatusMessage' => [ 'shape' => 'NodeFromTemplateJobStatusMessage', ], 'TemplateType' => [ 'shape' => 'TemplateType', ], ], ], 'NodeFromTemplateJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeFromTemplateJob', ], ], 'NodeFromTemplateJobStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'SUCCEEDED', 'FAILED', ], ], 'NodeFromTemplateJobStatusMessage' => [ 'type' => 'string', ], 'NodeId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_\\.]+$', ], 'NodeInputPort' => [ 'type' => 'structure', 'members' => [ 'DefaultValue' => [ 'shape' => 'PortDefaultValue', ], 'Description' => [ 'shape' => 'Description', ], 'MaxConnections' => [ 'shape' => 'MaxConnections', ], 'Name' => [ 'shape' => 'PortName', ], 'Type' => [ 'shape' => 'PortType', ], ], ], 'NodeInstance' => [ 'type' => 'structure', 'required' => [ 'CurrentStatus', 'NodeInstanceId', ], 'members' => [ 'CurrentStatus' => [ 'shape' => 'NodeInstanceStatus', ], 'NodeId' => [ 'shape' => 'NodeId', ], 'NodeInstanceId' => [ 'shape' => 'NodeInstanceId', ], 'NodeName' => [ 'shape' => 'NodeName', ], 'PackageName' => [ 'shape' => 'NodePackageName', ], 'PackagePatchVersion' => [ 'shape' => 'NodePackagePatchVersion', ], 'PackageVersion' => [ 'shape' => 'NodePackageVersion', ], ], ], 'NodeInstanceId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_]+$', ], 'NodeInstanceStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'ERROR', 'NOT_AVAILABLE', 'PAUSED', ], ], 'NodeInstances' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeInstance', ], ], 'NodeInterface' => [ 'type' => 'structure', 'required' => [ 'Inputs', 'Outputs', ], 'members' => [ 'Inputs' => [ 'shape' => 'InputPortList', ], 'Outputs' => [ 'shape' => 'OutputPortList', ], ], ], 'NodeName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_]+$', ], 'NodeOutputPort' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'Description', ], 'Name' => [ 'shape' => 'PortName', ], 'Type' => [ 'shape' => 'PortType', ], ], ], 'NodePackageArn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'NodePackageId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_\\/]+$', ], 'NodePackageName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_]+$', ], 'NodePackagePatchVersion' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-z0-9]+$', ], 'NodePackageVersion' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^([0-9]+)\\.([0-9]+)$', ], 'NodeSignal' => [ 'type' => 'structure', 'required' => [ 'NodeInstanceId', 'Signal', ], 'members' => [ 'NodeInstanceId' => [ 'shape' => 'NodeInstanceId', ], 'Signal' => [ 'shape' => 'NodeSignalValue', ], ], ], 'NodeSignalList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeSignal', ], 'min' => 1, ], 'NodeSignalValue' => [ 'type' => 'string', 'enum' => [ 'PAUSE', 'RESUME', ], ], 'NodesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Node', ], ], 'NtpPayload' => [ 'type' => 'structure', 'required' => [ 'NtpServers', ], 'members' => [ 'NtpServers' => [ 'shape' => 'NtpServerList', ], ], ], 'NtpServerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpAddressOrServerName', ], 'max' => 5, 'min' => 0, ], 'NtpServerName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'NtpStatus' => [ 'type' => 'structure', 'members' => [ 'ConnectionStatus' => [ 'shape' => 'NetworkConnectionStatus', ], 'IpAddress' => [ 'shape' => 'IpAddress', ], 'NtpServerName' => [ 'shape' => 'NtpServerName', ], ], ], 'OTAJobConfig' => [ 'type' => 'structure', 'required' => [ 'ImageVersion', ], 'members' => [ 'AllowMajorVersionUpdate' => [ 'shape' => 'Boolean', ], 'ImageVersion' => [ 'shape' => 'ImageVersion', ], ], ], 'Object' => [ 'type' => 'string', ], 'ObjectKey' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^.+$', ], 'OutPutS3Location' => [ 'type' => 'structure', 'required' => [ 'BucketName', 'ObjectKey', ], 'members' => [ 'BucketName' => [ 'shape' => 'BucketName', ], 'ObjectKey' => [ 'shape' => 'ObjectKey', ], ], ], 'OutputPortList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeOutputPort', ], ], 'PackageImportJob' => [ 'type' => 'structure', 'members' => [ 'CreatedTime' => [ 'shape' => 'CreatedTime', ], 'JobId' => [ 'shape' => 'JobId', ], 'JobType' => [ 'shape' => 'PackageImportJobType', ], 'LastUpdatedTime' => [ 'shape' => 'LastUpdatedTime', ], 'Status' => [ 'shape' => 'PackageImportJobStatus', ], 'StatusMessage' => [ 'shape' => 'PackageImportJobStatusMessage', ], ], ], 'PackageImportJobInputConfig' => [ 'type' => 'structure', 'members' => [ 'PackageVersionInputConfig' => [ 'shape' => 'PackageVersionInputConfig', ], ], ], 'PackageImportJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PackageImportJob', ], ], 'PackageImportJobOutput' => [ 'type' => 'structure', 'required' => [ 'OutputS3Location', 'PackageId', 'PackageVersion', 'PatchVersion', ], 'members' => [ 'OutputS3Location' => [ 'shape' => 'OutPutS3Location', ], 'PackageId' => [ 'shape' => 'NodePackageId', ], 'PackageVersion' => [ 'shape' => 'NodePackageVersion', ], 'PatchVersion' => [ 'shape' => 'NodePackagePatchVersion', ], ], ], 'PackageImportJobOutputConfig' => [ 'type' => 'structure', 'members' => [ 'PackageVersionOutputConfig' => [ 'shape' => 'PackageVersionOutputConfig', ], ], ], 'PackageImportJobStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'SUCCEEDED', 'FAILED', ], ], 'PackageImportJobStatusMessage' => [ 'type' => 'string', ], 'PackageImportJobType' => [ 'type' => 'string', 'enum' => [ 'NODE_PACKAGE_VERSION', 'MARKETPLACE_NODE_PACKAGE_VERSION', ], ], 'PackageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PackageListItem', ], ], 'PackageListItem' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'NodePackageArn', ], 'CreatedTime' => [ 'shape' => 'TimeStamp', ], 'PackageId' => [ 'shape' => 'NodePackageId', ], 'PackageName' => [ 'shape' => 'NodePackageName', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'PackageObject' => [ 'type' => 'structure', 'required' => [ 'Name', 'PackageVersion', 'PatchVersion', ], 'members' => [ 'Name' => [ 'shape' => 'NodePackageName', ], 'PackageVersion' => [ 'shape' => 'NodePackageVersion', ], 'PatchVersion' => [ 'shape' => 'NodePackagePatchVersion', ], ], ], 'PackageObjects' => [ 'type' => 'list', 'member' => [ 'shape' => 'PackageObject', ], ], 'PackageOwnerAccount' => [ 'type' => 'string', 'max' => 12, 'min' => 1, 'pattern' => '^[0-9a-z\\_]+$', ], 'PackageVersionInputConfig' => [ 'type' => 'structure', 'required' => [ 'S3Location', ], 'members' => [ 'S3Location' => [ 'shape' => 'S3Location', ], ], ], 'PackageVersionOutputConfig' => [ 'type' => 'structure', 'required' => [ 'PackageName', 'PackageVersion', ], 'members' => [ 'MarkLatest' => [ 'shape' => 'MarkLatestPatch', ], 'PackageName' => [ 'shape' => 'NodePackageName', ], 'PackageVersion' => [ 'shape' => 'NodePackageVersion', ], ], ], 'PackageVersionStatus' => [ 'type' => 'string', 'enum' => [ 'REGISTER_PENDING', 'REGISTER_COMPLETED', 'FAILED', 'DELETING', ], ], 'PackageVersionStatusDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'PortDefaultValue' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'PortName' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\_]+$', ], 'PortType' => [ 'type' => 'string', 'enum' => [ 'BOOLEAN', 'STRING', 'INT32', 'FLOAT32', 'MEDIA', ], ], 'PrincipalArn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^arn:[a-z0-9][-.a-z0-9]{0,62}:iam::[0-9]{12}:[a-zA-Z0-9+=,.@\\-_/]+$', ], 'PrincipalArnsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PrincipalArn', ], ], 'ProvisionDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Description' => [ 'shape' => 'Description', ], 'Name' => [ 'shape' => 'DeviceName', ], 'NetworkingConfiguration' => [ 'shape' => 'NetworkPayload', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'ProvisionDeviceResponse' => [ 'type' => 'structure', 'required' => [ 'Arn', 'Status', ], 'members' => [ 'Arn' => [ 'shape' => 'DeviceArn', ], 'Certificates' => [ 'shape' => 'Certificates', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], 'IotThingName' => [ 'shape' => 'IotThingName', ], 'Status' => [ 'shape' => 'DeviceStatus', ], ], ], 'Region' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^.+$', ], 'RegisterPackageVersionRequest' => [ 'type' => 'structure', 'required' => [ 'PackageId', 'PackageVersion', 'PatchVersion', ], 'members' => [ 'MarkLatest' => [ 'shape' => 'MarkLatestPatch', ], 'OwnerAccount' => [ 'shape' => 'PackageOwnerAccount', ], 'PackageId' => [ 'shape' => 'NodePackageId', 'location' => 'uri', 'locationName' => 'PackageId', ], 'PackageVersion' => [ 'shape' => 'NodePackageVersion', 'location' => 'uri', 'locationName' => 'PackageVersion', ], 'PatchVersion' => [ 'shape' => 'NodePackagePatchVersion', 'location' => 'uri', 'locationName' => 'PatchVersion', ], ], ], 'RegisterPackageVersionResponse' => [ 'type' => 'structure', 'members' => [], ], 'RemoveApplicationInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationInstanceId', ], 'members' => [ 'ApplicationInstanceId' => [ 'shape' => 'ApplicationInstanceId', 'location' => 'uri', 'locationName' => 'ApplicationInstanceId', ], ], ], 'RemoveApplicationInstanceResponse' => [ 'type' => 'structure', 'members' => [], ], 'ReportedRuntimeContextState' => [ 'type' => 'structure', 'required' => [ 'DesiredState', 'DeviceReportedStatus', 'DeviceReportedTime', 'RuntimeContextName', ], 'members' => [ 'DesiredState' => [ 'shape' => 'DesiredState', ], 'DeviceReportedStatus' => [ 'shape' => 'DeviceReportedStatus', ], 'DeviceReportedTime' => [ 'shape' => 'TimeStamp', ], 'RuntimeContextName' => [ 'shape' => 'RuntimeContextName', ], ], ], 'ReportedRuntimeContextStates' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReportedRuntimeContextState', ], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^.+$', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'Message', 'ResourceId', 'ResourceType', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RetryAfterSeconds' => [ 'type' => 'integer', ], 'RuntimeContextName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^.+$', ], 'RuntimeRoleArn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^arn:[a-z0-9][-.a-z0-9]{0,62}:iam::[0-9]{12}:role/.+$', ], 'S3Location' => [ 'type' => 'structure', 'required' => [ 'BucketName', 'ObjectKey', ], 'members' => [ 'BucketName' => [ 'shape' => 'BucketName', ], 'ObjectKey' => [ 'shape' => 'ObjectKey', ], 'Region' => [ 'shape' => 'Region', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'Message', 'QuotaCode', 'ServiceCode', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'QuotaCode' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], 'ServiceCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SignalApplicationInstanceNodeInstancesRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationInstanceId', 'NodeSignals', ], 'members' => [ 'ApplicationInstanceId' => [ 'shape' => 'ApplicationInstanceId', 'location' => 'uri', 'locationName' => 'ApplicationInstanceId', ], 'NodeSignals' => [ 'shape' => 'NodeSignalList', ], ], ], 'SignalApplicationInstanceNodeInstancesResponse' => [ 'type' => 'structure', 'required' => [ 'ApplicationInstanceId', ], 'members' => [ 'ApplicationInstanceId' => [ 'shape' => 'ApplicationInstanceId', ], ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'StaticIpConnectionInfo' => [ 'type' => 'structure', 'required' => [ 'DefaultGateway', 'Dns', 'IpAddress', 'Mask', ], 'members' => [ 'DefaultGateway' => [ 'shape' => 'DefaultGateway', ], 'Dns' => [ 'shape' => 'DnsList', ], 'IpAddress' => [ 'shape' => 'IpAddress', ], 'Mask' => [ 'shape' => 'Mask', ], ], ], 'StatusFilter' => [ 'type' => 'string', 'enum' => [ 'DEPLOYMENT_SUCCEEDED', 'DEPLOYMENT_ERROR', 'REMOVAL_SUCCEEDED', 'REMOVAL_FAILED', 'PROCESSING_DEPLOYMENT', 'PROCESSING_REMOVAL', 'DEPLOYMENT_FAILED', ], ], 'StorageLocation' => [ 'type' => 'structure', 'required' => [ 'BinaryPrefixLocation', 'Bucket', 'GeneratedPrefixLocation', 'ManifestPrefixLocation', 'RepoPrefixLocation', ], 'members' => [ 'BinaryPrefixLocation' => [ 'shape' => 'Object', ], 'Bucket' => [ 'shape' => 'Bucket', ], 'GeneratedPrefixLocation' => [ 'shape' => 'Object', ], 'ManifestPrefixLocation' => [ 'shape' => 'Object', ], 'RepoPrefixLocation' => [ 'shape' => 'Object', ], ], ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^.+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^.*$', ], 'TemplateKey' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^.+$', ], 'TemplateParametersMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TemplateKey', ], 'value' => [ 'shape' => 'TemplateValue', ], ], 'TemplateType' => [ 'type' => 'string', 'enum' => [ 'RTSP_CAMERA_STREAM', ], ], 'TemplateValue' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^.+$', 'sensitive' => true, ], 'TimeStamp' => [ 'type' => 'timestamp', ], 'Token' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '^.+$', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateCreatedTime' => [ 'type' => 'timestamp', ], 'UpdateDeviceMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceId', ], 'members' => [ 'Description' => [ 'shape' => 'Description', ], 'DeviceId' => [ 'shape' => 'DeviceId', 'location' => 'uri', 'locationName' => 'DeviceId', ], ], ], 'UpdateDeviceMetadataResponse' => [ 'type' => 'structure', 'members' => [ 'DeviceId' => [ 'shape' => 'DeviceId', ], ], ], 'UpdateProgress' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'IN_PROGRESS', 'VERIFYING', 'REBOOTING', 'DOWNLOADING', 'COMPLETED', 'FAILED', ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'ErrorArguments' => [ 'shape' => 'ValidationExceptionErrorArgumentList', ], 'ErrorId' => [ 'shape' => 'String', ], 'Fields' => [ 'shape' => 'ValidationExceptionFieldList', ], 'Message' => [ 'shape' => 'String', ], 'Reason' => [ 'shape' => 'ValidationExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionErrorArgument' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionErrorArgumentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionErrorArgument', ], ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'Message', 'Name', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'UNKNOWN_OPERATION', 'CANNOT_PARSE', 'FIELD_VALIDATION_FAILED', 'OTHER', ], ], 'Version' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], ],];
