<?php
// This file was auto-generated from sdk-root/src/data/opsworks/2013-02-18/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2013-02-18', 'endpointPrefix' => 'opsworks', 'jsonVersion' => '1.1', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'AWS OpsWorks', 'serviceId' => 'OpsWorks', 'signatureVersion' => 'v4', 'targetPrefix' => 'OpsWorks_20130218', 'uid' => 'opsworks-2013-02-18', ], 'operations' => [ 'AssignInstance' => [ 'name' => 'AssignInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssignInstanceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'AssignVolume' => [ 'name' => 'AssignVolume', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssignVolumeRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'AssociateElasticIp' => [ 'name' => 'AssociateElasticIp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateElasticIpRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'AttachElasticLoadBalancer' => [ 'name' => 'AttachElasticLoadBalancer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AttachElasticLoadBalancerRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CloneStack' => [ 'name' => 'CloneStack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CloneStackRequest', ], 'output' => [ 'shape' => 'CloneStackResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateApp' => [ 'name' => 'CreateApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAppRequest', ], 'output' => [ 'shape' => 'CreateAppResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateDeployment' => [ 'name' => 'CreateDeployment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDeploymentRequest', ], 'output' => [ 'shape' => 'CreateDeploymentResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateInstance' => [ 'name' => 'CreateInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateInstanceRequest', ], 'output' => [ 'shape' => 'CreateInstanceResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateLayer' => [ 'name' => 'CreateLayer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLayerRequest', ], 'output' => [ 'shape' => 'CreateLayerResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateStack' => [ 'name' => 'CreateStack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateStackRequest', ], 'output' => [ 'shape' => 'CreateStackResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'CreateUserProfile' => [ 'name' => 'CreateUserProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUserProfileRequest', ], 'output' => [ 'shape' => 'CreateUserProfileResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'DeleteApp' => [ 'name' => 'DeleteApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAppRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteInstance' => [ 'name' => 'DeleteInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteInstanceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteLayer' => [ 'name' => 'DeleteLayer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteLayerRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteStack' => [ 'name' => 'DeleteStack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteStackRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteUserProfile' => [ 'name' => 'DeleteUserProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteUserProfileRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeregisterEcsCluster' => [ 'name' => 'DeregisterEcsCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterEcsClusterRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeregisterElasticIp' => [ 'name' => 'DeregisterElasticIp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterElasticIpRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeregisterInstance' => [ 'name' => 'DeregisterInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterInstanceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeregisterRdsDbInstance' => [ 'name' => 'DeregisterRdsDbInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterRdsDbInstanceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeregisterVolume' => [ 'name' => 'DeregisterVolume', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterVolumeRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeAgentVersions' => [ 'name' => 'DescribeAgentVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAgentVersionsRequest', ], 'output' => [ 'shape' => 'DescribeAgentVersionsResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeApps' => [ 'name' => 'DescribeApps', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAppsRequest', ], 'output' => [ 'shape' => 'DescribeAppsResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeCommands' => [ 'name' => 'DescribeCommands', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCommandsRequest', ], 'output' => [ 'shape' => 'DescribeCommandsResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeDeployments' => [ 'name' => 'DescribeDeployments', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDeploymentsRequest', ], 'output' => [ 'shape' => 'DescribeDeploymentsResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeEcsClusters' => [ 'name' => 'DescribeEcsClusters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEcsClustersRequest', ], 'output' => [ 'shape' => 'DescribeEcsClustersResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeElasticIps' => [ 'name' => 'DescribeElasticIps', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeElasticIpsRequest', ], 'output' => [ 'shape' => 'DescribeElasticIpsResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeElasticLoadBalancers' => [ 'name' => 'DescribeElasticLoadBalancers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeElasticLoadBalancersRequest', ], 'output' => [ 'shape' => 'DescribeElasticLoadBalancersResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeInstances' => [ 'name' => 'DescribeInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeInstancesRequest', ], 'output' => [ 'shape' => 'DescribeInstancesResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeLayers' => [ 'name' => 'DescribeLayers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLayersRequest', ], 'output' => [ 'shape' => 'DescribeLayersResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeLoadBasedAutoScaling' => [ 'name' => 'DescribeLoadBasedAutoScaling', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLoadBasedAutoScalingRequest', ], 'output' => [ 'shape' => 'DescribeLoadBasedAutoScalingResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeMyUserProfile' => [ 'name' => 'DescribeMyUserProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'output' => [ 'shape' => 'DescribeMyUserProfileResult', ], ], 'DescribeOperatingSystems' => [ 'name' => 'DescribeOperatingSystems', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'output' => [ 'shape' => 'DescribeOperatingSystemsResponse', ], ], 'DescribePermissions' => [ 'name' => 'DescribePermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePermissionsRequest', ], 'output' => [ 'shape' => 'DescribePermissionsResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeRaidArrays' => [ 'name' => 'DescribeRaidArrays', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRaidArraysRequest', ], 'output' => [ 'shape' => 'DescribeRaidArraysResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeRdsDbInstances' => [ 'name' => 'DescribeRdsDbInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRdsDbInstancesRequest', ], 'output' => [ 'shape' => 'DescribeRdsDbInstancesResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeServiceErrors' => [ 'name' => 'DescribeServiceErrors', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeServiceErrorsRequest', ], 'output' => [ 'shape' => 'DescribeServiceErrorsResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeStackProvisioningParameters' => [ 'name' => 'DescribeStackProvisioningParameters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStackProvisioningParametersRequest', ], 'output' => [ 'shape' => 'DescribeStackProvisioningParametersResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeStackSummary' => [ 'name' => 'DescribeStackSummary', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStackSummaryRequest', ], 'output' => [ 'shape' => 'DescribeStackSummaryResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeStacks' => [ 'name' => 'DescribeStacks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStacksRequest', ], 'output' => [ 'shape' => 'DescribeStacksResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeTimeBasedAutoScaling' => [ 'name' => 'DescribeTimeBasedAutoScaling', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTimeBasedAutoScalingRequest', ], 'output' => [ 'shape' => 'DescribeTimeBasedAutoScalingResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeUserProfiles' => [ 'name' => 'DescribeUserProfiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeUserProfilesRequest', ], 'output' => [ 'shape' => 'DescribeUserProfilesResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeVolumes' => [ 'name' => 'DescribeVolumes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeVolumesRequest', ], 'output' => [ 'shape' => 'DescribeVolumesResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DetachElasticLoadBalancer' => [ 'name' => 'DetachElasticLoadBalancer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetachElasticLoadBalancerRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DisassociateElasticIp' => [ 'name' => 'DisassociateElasticIp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateElasticIpRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetHostnameSuggestion' => [ 'name' => 'GetHostnameSuggestion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetHostnameSuggestionRequest', ], 'output' => [ 'shape' => 'GetHostnameSuggestionResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GrantAccess' => [ 'name' => 'GrantAccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GrantAccessRequest', ], 'output' => [ 'shape' => 'GrantAccessResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTags' => [ 'name' => 'ListTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsRequest', ], 'output' => [ 'shape' => 'ListTagsResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'RebootInstance' => [ 'name' => 'RebootInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RebootInstanceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'RegisterEcsCluster' => [ 'name' => 'RegisterEcsCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterEcsClusterRequest', ], 'output' => [ 'shape' => 'RegisterEcsClusterResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'RegisterElasticIp' => [ 'name' => 'RegisterElasticIp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterElasticIpRequest', ], 'output' => [ 'shape' => 'RegisterElasticIpResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'RegisterInstance' => [ 'name' => 'RegisterInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterInstanceRequest', ], 'output' => [ 'shape' => 'RegisterInstanceResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'RegisterRdsDbInstance' => [ 'name' => 'RegisterRdsDbInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterRdsDbInstanceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'RegisterVolume' => [ 'name' => 'RegisterVolume', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterVolumeRequest', ], 'output' => [ 'shape' => 'RegisterVolumeResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'SetLoadBasedAutoScaling' => [ 'name' => 'SetLoadBasedAutoScaling', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetLoadBasedAutoScalingRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'SetPermission' => [ 'name' => 'SetPermission', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetPermissionRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'SetTimeBasedAutoScaling' => [ 'name' => 'SetTimeBasedAutoScaling', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetTimeBasedAutoScalingRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StartInstance' => [ 'name' => 'StartInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartInstanceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StartStack' => [ 'name' => 'StartStack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartStackRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StopInstance' => [ 'name' => 'StopInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopInstanceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StopStack' => [ 'name' => 'StopStack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopStackRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UnassignInstance' => [ 'name' => 'UnassignInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UnassignInstanceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UnassignVolume' => [ 'name' => 'UnassignVolume', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UnassignVolumeRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateApp' => [ 'name' => 'UpdateApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAppRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateElasticIp' => [ 'name' => 'UpdateElasticIp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateElasticIpRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateInstance' => [ 'name' => 'UpdateInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateInstanceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateLayer' => [ 'name' => 'UpdateLayer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateLayerRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateMyUserProfile' => [ 'name' => 'UpdateMyUserProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateMyUserProfileRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'UpdateRdsDbInstance' => [ 'name' => 'UpdateRdsDbInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRdsDbInstanceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateStack' => [ 'name' => 'UpdateStack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateStackRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateUserProfile' => [ 'name' => 'UpdateUserProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateUserProfileRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateVolume' => [ 'name' => 'UpdateVolume', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateVolumeRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], ], 'shapes' => [ 'AgentVersion' => [ 'type' => 'structure', 'members' => [ 'Version' => [ 'shape' => 'String', ], 'ConfigurationManager' => [ 'shape' => 'StackConfigurationManager', ], ], ], 'AgentVersions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentVersion', ], ], 'App' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => 'String', ], 'StackId' => [ 'shape' => 'String', ], 'Shortname' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'DataSources' => [ 'shape' => 'DataSources', ], 'Type' => [ 'shape' => 'AppType', ], 'AppSource' => [ 'shape' => 'Source', ], 'Domains' => [ 'shape' => 'Strings', ], 'EnableSsl' => [ 'shape' => 'Boolean', ], 'SslConfiguration' => [ 'shape' => 'SslConfiguration', ], 'Attributes' => [ 'shape' => 'AppAttributes', ], 'CreatedAt' => [ 'shape' => 'String', ], 'Environment' => [ 'shape' => 'EnvironmentVariables', ], ], ], 'AppAttributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'AppAttributesKeys', ], 'value' => [ 'shape' => 'String', ], ], 'AppAttributesKeys' => [ 'type' => 'string', 'enum' => [ 'DocumentRoot', 'RailsEnv', 'AutoBundleOnDeploy', 'AwsFlowRubySettings', ], ], 'AppType' => [ 'type' => 'string', 'enum' => [ 'aws-flow-ruby', 'java', 'rails', 'php', 'nodejs', 'static', 'other', ], ], 'Apps' => [ 'type' => 'list', 'member' => [ 'shape' => 'App', ], ], 'Architecture' => [ 'type' => 'string', 'enum' => [ 'x86_64', 'i386', ], ], 'AssignInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'LayerIds', ], 'members' => [ 'InstanceId' => [ 'shape' => 'String', ], 'LayerIds' => [ 'shape' => 'Strings', ], ], ], 'AssignVolumeRequest' => [ 'type' => 'structure', 'required' => [ 'VolumeId', ], 'members' => [ 'VolumeId' => [ 'shape' => 'String', ], 'InstanceId' => [ 'shape' => 'String', ], ], ], 'AssociateElasticIpRequest' => [ 'type' => 'structure', 'required' => [ 'ElasticIp', ], 'members' => [ 'ElasticIp' => [ 'shape' => 'String', ], 'InstanceId' => [ 'shape' => 'String', ], ], ], 'AttachElasticLoadBalancerRequest' => [ 'type' => 'structure', 'required' => [ 'ElasticLoadBalancerName', 'LayerId', ], 'members' => [ 'ElasticLoadBalancerName' => [ 'shape' => 'String', ], 'LayerId' => [ 'shape' => 'String', ], ], ], 'AutoScalingThresholds' => [ 'type' => 'structure', 'members' => [ 'InstanceCount' => [ 'shape' => 'Integer', ], 'ThresholdsWaitTime' => [ 'shape' => 'Minute', ], 'IgnoreMetricsTime' => [ 'shape' => 'Minute', ], 'CpuThreshold' => [ 'shape' => 'Double', ], 'MemoryThreshold' => [ 'shape' => 'Double', ], 'LoadThreshold' => [ 'shape' => 'Double', ], 'Alarms' => [ 'shape' => 'Strings', ], ], ], 'AutoScalingType' => [ 'type' => 'string', 'enum' => [ 'load', 'timer', ], ], 'BlockDeviceMapping' => [ 'type' => 'structure', 'members' => [ 'DeviceName' => [ 'shape' => 'String', ], 'NoDevice' => [ 'shape' => 'String', ], 'VirtualName' => [ 'shape' => 'String', ], 'Ebs' => [ 'shape' => 'EbsBlockDevice', ], ], ], 'BlockDeviceMappings' => [ 'type' => 'list', 'member' => [ 'shape' => 'BlockDeviceMapping', ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ChefConfiguration' => [ 'type' => 'structure', 'members' => [ 'ManageBerkshelf' => [ 'shape' => 'Boolean', ], 'BerkshelfVersion' => [ 'shape' => 'String', ], ], ], 'CloneStackRequest' => [ 'type' => 'structure', 'required' => [ 'SourceStackId', 'ServiceRoleArn', ], 'members' => [ 'SourceStackId' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'Region' => [ 'shape' => 'String', ], 'VpcId' => [ 'shape' => 'String', ], 'Attributes' => [ 'shape' => 'StackAttributes', ], 'ServiceRoleArn' => [ 'shape' => 'String', ], 'DefaultInstanceProfileArn' => [ 'shape' => 'String', ], 'DefaultOs' => [ 'shape' => 'String', ], 'HostnameTheme' => [ 'shape' => 'String', ], 'DefaultAvailabilityZone' => [ 'shape' => 'String', ], 'DefaultSubnetId' => [ 'shape' => 'String', ], 'CustomJson' => [ 'shape' => 'String', ], 'ConfigurationManager' => [ 'shape' => 'StackConfigurationManager', ], 'ChefConfiguration' => [ 'shape' => 'ChefConfiguration', ], 'UseCustomCookbooks' => [ 'shape' => 'Boolean', ], 'UseOpsworksSecurityGroups' => [ 'shape' => 'Boolean', ], 'CustomCookbooksSource' => [ 'shape' => 'Source', ], 'DefaultSshKeyName' => [ 'shape' => 'String', ], 'ClonePermissions' => [ 'shape' => 'Boolean', ], 'CloneAppIds' => [ 'shape' => 'Strings', ], 'DefaultRootDeviceType' => [ 'shape' => 'RootDeviceType', ], 'AgentVersion' => [ 'shape' => 'String', ], ], ], 'CloneStackResult' => [ 'type' => 'structure', 'members' => [ 'StackId' => [ 'shape' => 'String', ], ], ], 'CloudWatchLogsConfiguration' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'LogStreams' => [ 'shape' => 'CloudWatchLogsLogStreams', ], ], ], 'CloudWatchLogsEncoding' => [ 'type' => 'string', 'enum' => [ 'ascii', 'big5', 'big5hkscs', 'cp037', 'cp424', 'cp437', 'cp500', 'cp720', 'cp737', 'cp775', 'cp850', 'cp852', 'cp855', 'cp856', 'cp857', 'cp858', 'cp860', 'cp861', 'cp862', 'cp863', 'cp864', 'cp865', 'cp866', 'cp869', 'cp874', 'cp875', 'cp932', 'cp949', 'cp950', 'cp1006', 'cp1026', 'cp1140', 'cp1250', 'cp1251', 'cp1252', 'cp1253', 'cp1254', 'cp1255', 'cp1256', 'cp1257', 'cp1258', 'euc_jp', 'euc_jis_2004', 'euc_jisx0213', 'euc_kr', 'gb2312', 'gbk', 'gb18030', 'hz', 'iso2022_jp', 'iso2022_jp_1', 'iso2022_jp_2', 'iso2022_jp_2004', 'iso2022_jp_3', 'iso2022_jp_ext', 'iso2022_kr', 'latin_1', 'iso8859_2', 'iso8859_3', 'iso8859_4', 'iso8859_5', 'iso8859_6', 'iso8859_7', 'iso8859_8', 'iso8859_9', 'iso8859_10', 'iso8859_13', 'iso8859_14', 'iso8859_15', 'iso8859_16', 'johab', 'koi8_r', 'koi8_u', 'mac_cyrillic', 'mac_greek', 'mac_iceland', 'mac_latin2', 'mac_roman', 'mac_turkish', 'ptcp154', 'shift_jis', 'shift_jis_2004', 'shift_jisx0213', 'utf_32', 'utf_32_be', 'utf_32_le', 'utf_16', 'utf_16_be', 'utf_16_le', 'utf_7', 'utf_8', 'utf_8_sig', ], ], 'CloudWatchLogsInitialPosition' => [ 'type' => 'string', 'enum' => [ 'start_of_file', 'end_of_file', ], ], 'CloudWatchLogsLogStream' => [ 'type' => 'structure', 'members' => [ 'LogGroupName' => [ 'shape' => 'String', ], 'DatetimeFormat' => [ 'shape' => 'String', ], 'TimeZone' => [ 'shape' => 'CloudWatchLogsTimeZone', ], 'File' => [ 'shape' => 'String', ], 'FileFingerprintLines' => [ 'shape' => 'String', ], 'MultiLineStartPattern' => [ 'shape' => 'String', ], 'InitialPosition' => [ 'shape' => 'CloudWatchLogsInitialPosition', ], 'Encoding' => [ 'shape' => 'CloudWatchLogsEncoding', ], 'BufferDuration' => [ 'shape' => 'Integer', ], 'BatchCount' => [ 'shape' => 'Integer', ], 'BatchSize' => [ 'shape' => 'Integer', ], ], ], 'CloudWatchLogsLogStreams' => [ 'type' => 'list', 'member' => [ 'shape' => 'CloudWatchLogsLogStream', ], ], 'CloudWatchLogsTimeZone' => [ 'type' => 'string', 'enum' => [ 'LOCAL', 'UTC', ], ], 'Command' => [ 'type' => 'structure', 'members' => [ 'CommandId' => [ 'shape' => 'String', ], 'InstanceId' => [ 'shape' => 'String', ], 'DeploymentId' => [ 'shape' => 'String', ], 'CreatedAt' => [ 'shape' => 'DateTime', ], 'AcknowledgedAt' => [ 'shape' => 'DateTime', ], 'CompletedAt' => [ 'shape' => 'DateTime', ], 'Status' => [ 'shape' => 'String', ], 'ExitCode' => [ 'shape' => 'Integer', ], 'LogUrl' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'String', ], ], ], 'Commands' => [ 'type' => 'list', 'member' => [ 'shape' => 'Command', ], ], 'CreateAppRequest' => [ 'type' => 'structure', 'required' => [ 'StackId', 'Name', 'Type', ], 'members' => [ 'StackId' => [ 'shape' => 'String', ], 'Shortname' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'DataSources' => [ 'shape' => 'DataSources', ], 'Type' => [ 'shape' => 'AppType', ], 'AppSource' => [ 'shape' => 'Source', ], 'Domains' => [ 'shape' => 'Strings', ], 'EnableSsl' => [ 'shape' => 'Boolean', ], 'SslConfiguration' => [ 'shape' => 'SslConfiguration', ], 'Attributes' => [ 'shape' => 'AppAttributes', ], 'Environment' => [ 'shape' => 'EnvironmentVariables', ], ], ], 'CreateAppResult' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => 'String', ], ], ], 'CreateDeploymentRequest' => [ 'type' => 'structure', 'required' => [ 'StackId', 'Command', ], 'members' => [ 'StackId' => [ 'shape' => 'String', ], 'AppId' => [ 'shape' => 'String', ], 'InstanceIds' => [ 'shape' => 'Strings', ], 'LayerIds' => [ 'shape' => 'Strings', ], 'Command' => [ 'shape' => 'DeploymentCommand', ], 'Comment' => [ 'shape' => 'String', ], 'CustomJson' => [ 'shape' => 'String', ], ], ], 'CreateDeploymentResult' => [ 'type' => 'structure', 'members' => [ 'DeploymentId' => [ 'shape' => 'String', ], ], ], 'CreateInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'StackId', 'LayerIds', 'InstanceType', ], 'members' => [ 'StackId' => [ 'shape' => 'String', ], 'LayerIds' => [ 'shape' => 'Strings', ], 'InstanceType' => [ 'shape' => 'String', ], 'AutoScalingType' => [ 'shape' => 'AutoScalingType', ], 'Hostname' => [ 'shape' => 'String', ], 'Os' => [ 'shape' => 'String', ], 'AmiId' => [ 'shape' => 'String', ], 'SshKeyName' => [ 'shape' => 'String', ], 'AvailabilityZone' => [ 'shape' => 'String', ], 'VirtualizationType' => [ 'shape' => 'String', ], 'SubnetId' => [ 'shape' => 'String', ], 'Architecture' => [ 'shape' => 'Architecture', ], 'RootDeviceType' => [ 'shape' => 'RootDeviceType', ], 'BlockDeviceMappings' => [ 'shape' => 'BlockDeviceMappings', ], 'InstallUpdatesOnBoot' => [ 'shape' => 'Boolean', ], 'EbsOptimized' => [ 'shape' => 'Boolean', ], 'AgentVersion' => [ 'shape' => 'String', ], 'Tenancy' => [ 'shape' => 'String', ], ], ], 'CreateInstanceResult' => [ 'type' => 'structure', 'members' => [ 'InstanceId' => [ 'shape' => 'String', ], ], ], 'CreateLayerRequest' => [ 'type' => 'structure', 'required' => [ 'StackId', 'Type', 'Name', 'Shortname', ], 'members' => [ 'StackId' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'LayerType', ], 'Name' => [ 'shape' => 'String', ], 'Shortname' => [ 'shape' => 'String', ], 'Attributes' => [ 'shape' => 'LayerAttributes', ], 'CloudWatchLogsConfiguration' => [ 'shape' => 'CloudWatchLogsConfiguration', ], 'CustomInstanceProfileArn' => [ 'shape' => 'String', ], 'CustomJson' => [ 'shape' => 'String', ], 'CustomSecurityGroupIds' => [ 'shape' => 'Strings', ], 'Packages' => [ 'shape' => 'Strings', ], 'VolumeConfigurations' => [ 'shape' => 'VolumeConfigurations', ], 'EnableAutoHealing' => [ 'shape' => 'Boolean', ], 'AutoAssignElasticIps' => [ 'shape' => 'Boolean', ], 'AutoAssignPublicIps' => [ 'shape' => 'Boolean', ], 'CustomRecipes' => [ 'shape' => 'Recipes', ], 'InstallUpdatesOnBoot' => [ 'shape' => 'Boolean', ], 'UseEbsOptimizedInstances' => [ 'shape' => 'Boolean', ], 'LifecycleEventConfiguration' => [ 'shape' => 'LifecycleEventConfiguration', ], ], ], 'CreateLayerResult' => [ 'type' => 'structure', 'members' => [ 'LayerId' => [ 'shape' => 'String', ], ], ], 'CreateStackRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Region', 'ServiceRoleArn', 'DefaultInstanceProfileArn', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Region' => [ 'shape' => 'String', ], 'VpcId' => [ 'shape' => 'String', ], 'Attributes' => [ 'shape' => 'StackAttributes', ], 'ServiceRoleArn' => [ 'shape' => 'String', ], 'DefaultInstanceProfileArn' => [ 'shape' => 'String', ], 'DefaultOs' => [ 'shape' => 'String', ], 'HostnameTheme' => [ 'shape' => 'String', ], 'DefaultAvailabilityZone' => [ 'shape' => 'String', ], 'DefaultSubnetId' => [ 'shape' => 'String', ], 'CustomJson' => [ 'shape' => 'String', ], 'ConfigurationManager' => [ 'shape' => 'StackConfigurationManager', ], 'ChefConfiguration' => [ 'shape' => 'ChefConfiguration', ], 'UseCustomCookbooks' => [ 'shape' => 'Boolean', ], 'UseOpsworksSecurityGroups' => [ 'shape' => 'Boolean', ], 'CustomCookbooksSource' => [ 'shape' => 'Source', ], 'DefaultSshKeyName' => [ 'shape' => 'String', ], 'DefaultRootDeviceType' => [ 'shape' => 'RootDeviceType', ], 'AgentVersion' => [ 'shape' => 'String', ], ], ], 'CreateStackResult' => [ 'type' => 'structure', 'members' => [ 'StackId' => [ 'shape' => 'String', ], ], ], 'CreateUserProfileRequest' => [ 'type' => 'structure', 'required' => [ 'IamUserArn', ], 'members' => [ 'IamUserArn' => [ 'shape' => 'String', ], 'SshUsername' => [ 'shape' => 'String', ], 'SshPublicKey' => [ 'shape' => 'String', ], 'AllowSelfManagement' => [ 'shape' => 'Boolean', ], ], ], 'CreateUserProfileResult' => [ 'type' => 'structure', 'members' => [ 'IamUserArn' => [ 'shape' => 'String', ], ], ], 'DailyAutoScalingSchedule' => [ 'type' => 'map', 'key' => [ 'shape' => 'Hour', ], 'value' => [ 'shape' => 'Switch', ], ], 'DataSource' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'String', ], 'Arn' => [ 'shape' => 'String', ], 'DatabaseName' => [ 'shape' => 'String', ], ], ], 'DataSources' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSource', ], ], 'DateTime' => [ 'type' => 'string', ], 'DeleteAppRequest' => [ 'type' => 'structure', 'required' => [ 'AppId', ], 'members' => [ 'AppId' => [ 'shape' => 'String', ], ], ], 'DeleteInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'String', ], 'DeleteElasticIp' => [ 'shape' => 'Boolean', ], 'DeleteVolumes' => [ 'shape' => 'Boolean', ], ], ], 'DeleteLayerRequest' => [ 'type' => 'structure', 'required' => [ 'LayerId', ], 'members' => [ 'LayerId' => [ 'shape' => 'String', ], ], ], 'DeleteStackRequest' => [ 'type' => 'structure', 'required' => [ 'StackId', ], 'members' => [ 'StackId' => [ 'shape' => 'String', ], ], ], 'DeleteUserProfileRequest' => [ 'type' => 'structure', 'required' => [ 'IamUserArn', ], 'members' => [ 'IamUserArn' => [ 'shape' => 'String', ], ], ], 'Deployment' => [ 'type' => 'structure', 'members' => [ 'DeploymentId' => [ 'shape' => 'String', ], 'StackId' => [ 'shape' => 'String', ], 'AppId' => [ 'shape' => 'String', ], 'CreatedAt' => [ 'shape' => 'DateTime', ], 'CompletedAt' => [ 'shape' => 'DateTime', ], 'Duration' => [ 'shape' => 'Integer', ], 'IamUserArn' => [ 'shape' => 'String', ], 'Comment' => [ 'shape' => 'String', ], 'Command' => [ 'shape' => 'DeploymentCommand', ], 'Status' => [ 'shape' => 'String', ], 'CustomJson' => [ 'shape' => 'String', ], 'InstanceIds' => [ 'shape' => 'Strings', ], ], ], 'DeploymentCommand' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'DeploymentCommandName', ], 'Args' => [ 'shape' => 'DeploymentCommandArgs', ], ], ], 'DeploymentCommandArgs' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'Strings', ], ], 'DeploymentCommandName' => [ 'type' => 'string', 'enum' => [ 'install_dependencies', 'update_dependencies', 'update_custom_cookbooks', 'execute_recipes', 'configure', 'setup', 'deploy', 'rollback', 'start', 'stop', 'restart', 'undeploy', ], ], 'Deployments' => [ 'type' => 'list', 'member' => [ 'shape' => 'Deployment', ], ], 'DeregisterEcsClusterRequest' => [ 'type' => 'structure', 'required' => [ 'EcsClusterArn', ], 'members' => [ 'EcsClusterArn' => [ 'shape' => 'String', ], ], ], 'DeregisterElasticIpRequest' => [ 'type' => 'structure', 'required' => [ 'ElasticIp', ], 'members' => [ 'ElasticIp' => [ 'shape' => 'String', ], ], ], 'DeregisterInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'String', ], ], ], 'DeregisterRdsDbInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'RdsDbInstanceArn', ], 'members' => [ 'RdsDbInstanceArn' => [ 'shape' => 'String', ], ], ], 'DeregisterVolumeRequest' => [ 'type' => 'structure', 'required' => [ 'VolumeId', ], 'members' => [ 'VolumeId' => [ 'shape' => 'String', ], ], ], 'DescribeAgentVersionsRequest' => [ 'type' => 'structure', 'members' => [ 'StackId' => [ 'shape' => 'String', ], 'ConfigurationManager' => [ 'shape' => 'StackConfigurationManager', ], ], ], 'DescribeAgentVersionsResult' => [ 'type' => 'structure', 'members' => [ 'AgentVersions' => [ 'shape' => 'AgentVersions', ], ], ], 'DescribeAppsRequest' => [ 'type' => 'structure', 'members' => [ 'StackId' => [ 'shape' => 'String', ], 'AppIds' => [ 'shape' => 'Strings', ], ], ], 'DescribeAppsResult' => [ 'type' => 'structure', 'members' => [ 'Apps' => [ 'shape' => 'Apps', ], ], ], 'DescribeCommandsRequest' => [ 'type' => 'structure', 'members' => [ 'DeploymentId' => [ 'shape' => 'String', ], 'InstanceId' => [ 'shape' => 'String', ], 'CommandIds' => [ 'shape' => 'Strings', ], ], ], 'DescribeCommandsResult' => [ 'type' => 'structure', 'members' => [ 'Commands' => [ 'shape' => 'Commands', ], ], ], 'DescribeDeploymentsRequest' => [ 'type' => 'structure', 'members' => [ 'StackId' => [ 'shape' => 'String', ], 'AppId' => [ 'shape' => 'String', ], 'DeploymentIds' => [ 'shape' => 'Strings', ], ], ], 'DescribeDeploymentsResult' => [ 'type' => 'structure', 'members' => [ 'Deployments' => [ 'shape' => 'Deployments', ], ], ], 'DescribeEcsClustersRequest' => [ 'type' => 'structure', 'members' => [ 'EcsClusterArns' => [ 'shape' => 'Strings', ], 'StackId' => [ 'shape' => 'String', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'Integer', ], ], ], 'DescribeEcsClustersResult' => [ 'type' => 'structure', 'members' => [ 'EcsClusters' => [ 'shape' => 'EcsClusters', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeElasticIpsRequest' => [ 'type' => 'structure', 'members' => [ 'InstanceId' => [ 'shape' => 'String', ], 'StackId' => [ 'shape' => 'String', ], 'Ips' => [ 'shape' => 'Strings', ], ], ], 'DescribeElasticIpsResult' => [ 'type' => 'structure', 'members' => [ 'ElasticIps' => [ 'shape' => 'ElasticIps', ], ], ], 'DescribeElasticLoadBalancersRequest' => [ 'type' => 'structure', 'members' => [ 'StackId' => [ 'shape' => 'String', ], 'LayerIds' => [ 'shape' => 'Strings', ], ], ], 'DescribeElasticLoadBalancersResult' => [ 'type' => 'structure', 'members' => [ 'ElasticLoadBalancers' => [ 'shape' => 'ElasticLoadBalancers', ], ], ], 'DescribeInstancesRequest' => [ 'type' => 'structure', 'members' => [ 'StackId' => [ 'shape' => 'String', ], 'LayerId' => [ 'shape' => 'String', ], 'InstanceIds' => [ 'shape' => 'Strings', ], ], ], 'DescribeInstancesResult' => [ 'type' => 'structure', 'members' => [ 'Instances' => [ 'shape' => 'Instances', ], ], ], 'DescribeLayersRequest' => [ 'type' => 'structure', 'members' => [ 'StackId' => [ 'shape' => 'String', ], 'LayerIds' => [ 'shape' => 'Strings', ], ], ], 'DescribeLayersResult' => [ 'type' => 'structure', 'members' => [ 'Layers' => [ 'shape' => 'Layers', ], ], ], 'DescribeLoadBasedAutoScalingRequest' => [ 'type' => 'structure', 'required' => [ 'LayerIds', ], 'members' => [ 'LayerIds' => [ 'shape' => 'Strings', ], ], ], 'DescribeLoadBasedAutoScalingResult' => [ 'type' => 'structure', 'members' => [ 'LoadBasedAutoScalingConfigurations' => [ 'shape' => 'LoadBasedAutoScalingConfigurations', ], ], ], 'DescribeMyUserProfileResult' => [ 'type' => 'structure', 'members' => [ 'UserProfile' => [ 'shape' => 'SelfUserProfile', ], ], ], 'DescribeOperatingSystemsResponse' => [ 'type' => 'structure', 'members' => [ 'OperatingSystems' => [ 'shape' => 'OperatingSystems', ], ], ], 'DescribePermissionsRequest' => [ 'type' => 'structure', 'members' => [ 'IamUserArn' => [ 'shape' => 'String', ], 'StackId' => [ 'shape' => 'String', ], ], ], 'DescribePermissionsResult' => [ 'type' => 'structure', 'members' => [ 'Permissions' => [ 'shape' => 'Permissions', ], ], ], 'DescribeRaidArraysRequest' => [ 'type' => 'structure', 'members' => [ 'InstanceId' => [ 'shape' => 'String', ], 'StackId' => [ 'shape' => 'String', ], 'RaidArrayIds' => [ 'shape' => 'Strings', ], ], ], 'DescribeRaidArraysResult' => [ 'type' => 'structure', 'members' => [ 'RaidArrays' => [ 'shape' => 'RaidArrays', ], ], ], 'DescribeRdsDbInstancesRequest' => [ 'type' => 'structure', 'required' => [ 'StackId', ], 'members' => [ 'StackId' => [ 'shape' => 'String', ], 'RdsDbInstanceArns' => [ 'shape' => 'Strings', ], ], ], 'DescribeRdsDbInstancesResult' => [ 'type' => 'structure', 'members' => [ 'RdsDbInstances' => [ 'shape' => 'RdsDbInstances', ], ], ], 'DescribeServiceErrorsRequest' => [ 'type' => 'structure', 'members' => [ 'StackId' => [ 'shape' => 'String', ], 'InstanceId' => [ 'shape' => 'String', ], 'ServiceErrorIds' => [ 'shape' => 'Strings', ], ], ], 'DescribeServiceErrorsResult' => [ 'type' => 'structure', 'members' => [ 'ServiceErrors' => [ 'shape' => 'ServiceErrors', ], ], ], 'DescribeStackProvisioningParametersRequest' => [ 'type' => 'structure', 'required' => [ 'StackId', ], 'members' => [ 'StackId' => [ 'shape' => 'String', ], ], ], 'DescribeStackProvisioningParametersResult' => [ 'type' => 'structure', 'members' => [ 'AgentInstallerUrl' => [ 'shape' => 'String', ], 'Parameters' => [ 'shape' => 'Parameters', ], ], ], 'DescribeStackSummaryRequest' => [ 'type' => 'structure', 'required' => [ 'StackId', ], 'members' => [ 'StackId' => [ 'shape' => 'String', ], ], ], 'DescribeStackSummaryResult' => [ 'type' => 'structure', 'members' => [ 'StackSummary' => [ 'shape' => 'StackSummary', ], ], ], 'DescribeStacksRequest' => [ 'type' => 'structure', 'members' => [ 'StackIds' => [ 'shape' => 'Strings', ], ], ], 'DescribeStacksResult' => [ 'type' => 'structure', 'members' => [ 'Stacks' => [ 'shape' => 'Stacks', ], ], ], 'DescribeTimeBasedAutoScalingRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceIds', ], 'members' => [ 'InstanceIds' => [ 'shape' => 'Strings', ], ], ], 'DescribeTimeBasedAutoScalingResult' => [ 'type' => 'structure', 'members' => [ 'TimeBasedAutoScalingConfigurations' => [ 'shape' => 'TimeBasedAutoScalingConfigurations', ], ], ], 'DescribeUserProfilesRequest' => [ 'type' => 'structure', 'members' => [ 'IamUserArns' => [ 'shape' => 'Strings', ], ], ], 'DescribeUserProfilesResult' => [ 'type' => 'structure', 'members' => [ 'UserProfiles' => [ 'shape' => 'UserProfiles', ], ], ], 'DescribeVolumesRequest' => [ 'type' => 'structure', 'members' => [ 'InstanceId' => [ 'shape' => 'String', ], 'StackId' => [ 'shape' => 'String', ], 'RaidArrayId' => [ 'shape' => 'String', ], 'VolumeIds' => [ 'shape' => 'Strings', ], ], ], 'DescribeVolumesResult' => [ 'type' => 'structure', 'members' => [ 'Volumes' => [ 'shape' => 'Volumes', ], ], ], 'DetachElasticLoadBalancerRequest' => [ 'type' => 'structure', 'required' => [ 'ElasticLoadBalancerName', 'LayerId', ], 'members' => [ 'ElasticLoadBalancerName' => [ 'shape' => 'String', ], 'LayerId' => [ 'shape' => 'String', ], ], ], 'DisassociateElasticIpRequest' => [ 'type' => 'structure', 'required' => [ 'ElasticIp', ], 'members' => [ 'ElasticIp' => [ 'shape' => 'String', ], ], ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'EbsBlockDevice' => [ 'type' => 'structure', 'members' => [ 'SnapshotId' => [ 'shape' => 'String', ], 'Iops' => [ 'shape' => 'Integer', ], 'VolumeSize' => [ 'shape' => 'Integer', ], 'VolumeType' => [ 'shape' => 'VolumeType', ], 'DeleteOnTermination' => [ 'shape' => 'Boolean', ], ], ], 'EcsCluster' => [ 'type' => 'structure', 'members' => [ 'EcsClusterArn' => [ 'shape' => 'String', ], 'EcsClusterName' => [ 'shape' => 'String', ], 'StackId' => [ 'shape' => 'String', ], 'RegisteredAt' => [ 'shape' => 'DateTime', ], ], ], 'EcsClusters' => [ 'type' => 'list', 'member' => [ 'shape' => 'EcsCluster', ], ], 'ElasticIp' => [ 'type' => 'structure', 'members' => [ 'Ip' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'Domain' => [ 'shape' => 'String', ], 'Region' => [ 'shape' => 'String', ], 'InstanceId' => [ 'shape' => 'String', ], ], ], 'ElasticIps' => [ 'type' => 'list', 'member' => [ 'shape' => 'ElasticIp', ], ], 'ElasticLoadBalancer' => [ 'type' => 'structure', 'members' => [ 'ElasticLoadBalancerName' => [ 'shape' => 'String', ], 'Region' => [ 'shape' => 'String', ], 'DnsName' => [ 'shape' => 'String', ], 'StackId' => [ 'shape' => 'String', ], 'LayerId' => [ 'shape' => 'String', ], 'VpcId' => [ 'shape' => 'String', ], 'AvailabilityZones' => [ 'shape' => 'Strings', ], 'SubnetIds' => [ 'shape' => 'Strings', ], 'Ec2InstanceIds' => [ 'shape' => 'Strings', ], ], ], 'ElasticLoadBalancers' => [ 'type' => 'list', 'member' => [ 'shape' => 'ElasticLoadBalancer', ], ], 'EnvironmentVariable' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], 'Secure' => [ 'shape' => 'Boolean', ], ], ], 'EnvironmentVariables' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnvironmentVariable', ], ], 'GetHostnameSuggestionRequest' => [ 'type' => 'structure', 'required' => [ 'LayerId', ], 'members' => [ 'LayerId' => [ 'shape' => 'String', ], ], ], 'GetHostnameSuggestionResult' => [ 'type' => 'structure', 'members' => [ 'LayerId' => [ 'shape' => 'String', ], 'Hostname' => [ 'shape' => 'String', ], ], ], 'GrantAccessRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'String', ], 'ValidForInMinutes' => [ 'shape' => 'ValidForInMinutes', ], ], ], 'GrantAccessResult' => [ 'type' => 'structure', 'members' => [ 'TemporaryCredential' => [ 'shape' => 'TemporaryCredential', ], ], ], 'Hour' => [ 'type' => 'string', ], 'Instance' => [ 'type' => 'structure', 'members' => [ 'AgentVersion' => [ 'shape' => 'String', ], 'AmiId' => [ 'shape' => 'String', ], 'Architecture' => [ 'shape' => 'Architecture', ], 'Arn' => [ 'shape' => 'String', ], 'AutoScalingType' => [ 'shape' => 'AutoScalingType', ], 'AvailabilityZone' => [ 'shape' => 'String', ], 'BlockDeviceMappings' => [ 'shape' => 'BlockDeviceMappings', ], 'CreatedAt' => [ 'shape' => 'DateTime', ], 'EbsOptimized' => [ 'shape' => 'Boolean', ], 'Ec2InstanceId' => [ 'shape' => 'String', ], 'EcsClusterArn' => [ 'shape' => 'String', ], 'EcsContainerInstanceArn' => [ 'shape' => 'String', ], 'ElasticIp' => [ 'shape' => 'String', ], 'Hostname' => [ 'shape' => 'String', ], 'InfrastructureClass' => [ 'shape' => 'String', ], 'InstallUpdatesOnBoot' => [ 'shape' => 'Boolean', ], 'InstanceId' => [ 'shape' => 'String', ], 'InstanceProfileArn' => [ 'shape' => 'String', ], 'InstanceType' => [ 'shape' => 'String', ], 'LastServiceErrorId' => [ 'shape' => 'String', ], 'LayerIds' => [ 'shape' => 'Strings', ], 'Os' => [ 'shape' => 'String', ], 'Platform' => [ 'shape' => 'String', ], 'PrivateDns' => [ 'shape' => 'String', ], 'PrivateIp' => [ 'shape' => 'String', ], 'PublicDns' => [ 'shape' => 'String', ], 'PublicIp' => [ 'shape' => 'String', ], 'RegisteredBy' => [ 'shape' => 'String', ], 'ReportedAgentVersion' => [ 'shape' => 'String', ], 'ReportedOs' => [ 'shape' => 'ReportedOs', ], 'RootDeviceType' => [ 'shape' => 'RootDeviceType', ], 'RootDeviceVolumeId' => [ 'shape' => 'String', ], 'SecurityGroupIds' => [ 'shape' => 'Strings', ], 'SshHostDsaKeyFingerprint' => [ 'shape' => 'String', ], 'SshHostRsaKeyFingerprint' => [ 'shape' => 'String', ], 'SshKeyName' => [ 'shape' => 'String', ], 'StackId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'SubnetId' => [ 'shape' => 'String', ], 'Tenancy' => [ 'shape' => 'String', ], 'VirtualizationType' => [ 'shape' => 'VirtualizationType', ], ], ], 'InstanceIdentity' => [ 'type' => 'structure', 'members' => [ 'Document' => [ 'shape' => 'String', ], 'Signature' => [ 'shape' => 'String', ], ], ], 'Instances' => [ 'type' => 'list', 'member' => [ 'shape' => 'Instance', ], ], 'InstancesCount' => [ 'type' => 'structure', 'members' => [ 'Assigning' => [ 'shape' => 'Integer', ], 'Booting' => [ 'shape' => 'Integer', ], 'ConnectionLost' => [ 'shape' => 'Integer', ], 'Deregistering' => [ 'shape' => 'Integer', ], 'Online' => [ 'shape' => 'Integer', ], 'Pending' => [ 'shape' => 'Integer', ], 'Rebooting' => [ 'shape' => 'Integer', ], 'Registered' => [ 'shape' => 'Integer', ], 'Registering' => [ 'shape' => 'Integer', ], 'Requested' => [ 'shape' => 'Integer', ], 'RunningSetup' => [ 'shape' => 'Integer', ], 'SetupFailed' => [ 'shape' => 'Integer', ], 'ShuttingDown' => [ 'shape' => 'Integer', ], 'StartFailed' => [ 'shape' => 'Integer', ], 'StopFailed' => [ 'shape' => 'Integer', ], 'Stopped' => [ 'shape' => 'Integer', ], 'Stopping' => [ 'shape' => 'Integer', ], 'Terminated' => [ 'shape' => 'Integer', ], 'Terminating' => [ 'shape' => 'Integer', ], 'Unassigning' => [ 'shape' => 'Integer', ], ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'Layer' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'String', ], 'StackId' => [ 'shape' => 'String', ], 'LayerId' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'LayerType', ], 'Name' => [ 'shape' => 'String', ], 'Shortname' => [ 'shape' => 'String', ], 'Attributes' => [ 'shape' => 'LayerAttributes', ], 'CloudWatchLogsConfiguration' => [ 'shape' => 'CloudWatchLogsConfiguration', ], 'CustomInstanceProfileArn' => [ 'shape' => 'String', ], 'CustomJson' => [ 'shape' => 'String', ], 'CustomSecurityGroupIds' => [ 'shape' => 'Strings', ], 'DefaultSecurityGroupNames' => [ 'shape' => 'Strings', ], 'Packages' => [ 'shape' => 'Strings', ], 'VolumeConfigurations' => [ 'shape' => 'VolumeConfigurations', ], 'EnableAutoHealing' => [ 'shape' => 'Boolean', ], 'AutoAssignElasticIps' => [ 'shape' => 'Boolean', ], 'AutoAssignPublicIps' => [ 'shape' => 'Boolean', ], 'DefaultRecipes' => [ 'shape' => 'Recipes', ], 'CustomRecipes' => [ 'shape' => 'Recipes', ], 'CreatedAt' => [ 'shape' => 'DateTime', ], 'InstallUpdatesOnBoot' => [ 'shape' => 'Boolean', ], 'UseEbsOptimizedInstances' => [ 'shape' => 'Boolean', ], 'LifecycleEventConfiguration' => [ 'shape' => 'LifecycleEventConfiguration', ], ], ], 'LayerAttributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'LayerAttributesKeys', ], 'value' => [ 'shape' => 'String', ], ], 'LayerAttributesKeys' => [ 'type' => 'string', 'enum' => [ 'EcsClusterArn', 'EnableHaproxyStats', 'HaproxyStatsUrl', 'HaproxyStatsUser', 'HaproxyStatsPassword', 'HaproxyHealthCheckUrl', 'HaproxyHealthCheckMethod', 'MysqlRootPassword', 'MysqlRootPasswordUbiquitous', 'GangliaUrl', 'GangliaUser', 'GangliaPassword', 'MemcachedMemory', 'NodejsVersion', 'RubyVersion', 'RubygemsVersion', 'ManageBundler', 'BundlerVersion', 'RailsStack', 'PassengerVersion', 'Jvm', 'JvmVersion', 'JvmOptions', 'JavaAppServer', 'JavaAppServerVersion', ], ], 'LayerType' => [ 'type' => 'string', 'enum' => [ 'aws-flow-ruby', 'ecs-cluster', 'java-app', 'lb', 'web', 'php-app', 'rails-app', 'nodejs-app', 'memcached', 'db-master', 'monitoring-master', 'custom', ], ], 'Layers' => [ 'type' => 'list', 'member' => [ 'shape' => 'Layer', ], ], 'LifecycleEventConfiguration' => [ 'type' => 'structure', 'members' => [ 'Shutdown' => [ 'shape' => 'ShutdownEventConfiguration', ], ], ], 'ListTagsRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsResult' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'Tags', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'LoadBasedAutoScalingConfiguration' => [ 'type' => 'structure', 'members' => [ 'LayerId' => [ 'shape' => 'String', ], 'Enable' => [ 'shape' => 'Boolean', ], 'UpScaling' => [ 'shape' => 'AutoScalingThresholds', ], 'DownScaling' => [ 'shape' => 'AutoScalingThresholds', ], ], ], 'LoadBasedAutoScalingConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoadBasedAutoScalingConfiguration', ], ], 'MaxResults' => [ 'type' => 'integer', ], 'Minute' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'NextToken' => [ 'type' => 'string', ], 'OperatingSystem' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Id' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'String', ], 'ConfigurationManagers' => [ 'shape' => 'OperatingSystemConfigurationManagers', ], 'ReportedName' => [ 'shape' => 'String', ], 'ReportedVersion' => [ 'shape' => 'String', ], 'Supported' => [ 'shape' => 'Boolean', ], ], ], 'OperatingSystemConfigurationManager' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Version' => [ 'shape' => 'String', ], ], ], 'OperatingSystemConfigurationManagers' => [ 'type' => 'list', 'member' => [ 'shape' => 'OperatingSystemConfigurationManager', ], ], 'OperatingSystems' => [ 'type' => 'list', 'member' => [ 'shape' => 'OperatingSystem', ], ], 'Parameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'Permission' => [ 'type' => 'structure', 'members' => [ 'StackId' => [ 'shape' => 'String', ], 'IamUserArn' => [ 'shape' => 'String', ], 'AllowSsh' => [ 'shape' => 'Boolean', ], 'AllowSudo' => [ 'shape' => 'Boolean', ], 'Level' => [ 'shape' => 'String', ], ], ], 'Permissions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Permission', ], ], 'RaidArray' => [ 'type' => 'structure', 'members' => [ 'RaidArrayId' => [ 'shape' => 'String', ], 'InstanceId' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'RaidLevel' => [ 'shape' => 'Integer', ], 'NumberOfDisks' => [ 'shape' => 'Integer', ], 'Size' => [ 'shape' => 'Integer', ], 'Device' => [ 'shape' => 'String', ], 'MountPoint' => [ 'shape' => 'String', ], 'AvailabilityZone' => [ 'shape' => 'String', ], 'CreatedAt' => [ 'shape' => 'DateTime', ], 'StackId' => [ 'shape' => 'String', ], 'VolumeType' => [ 'shape' => 'String', ], 'Iops' => [ 'shape' => 'Integer', ], ], ], 'RaidArrays' => [ 'type' => 'list', 'member' => [ 'shape' => 'RaidArray', ], ], 'RdsDbInstance' => [ 'type' => 'structure', 'members' => [ 'RdsDbInstanceArn' => [ 'shape' => 'String', ], 'DbInstanceIdentifier' => [ 'shape' => 'String', ], 'DbUser' => [ 'shape' => 'String', ], 'DbPassword' => [ 'shape' => 'String', ], 'Region' => [ 'shape' => 'String', ], 'Address' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'StackId' => [ 'shape' => 'String', ], 'MissingOnRds' => [ 'shape' => 'Boolean', ], ], ], 'RdsDbInstances' => [ 'type' => 'list', 'member' => [ 'shape' => 'RdsDbInstance', ], ], 'RebootInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'String', ], ], ], 'Recipes' => [ 'type' => 'structure', 'members' => [ 'Setup' => [ 'shape' => 'Strings', ], 'Configure' => [ 'shape' => 'Strings', ], 'Deploy' => [ 'shape' => 'Strings', ], 'Undeploy' => [ 'shape' => 'Strings', ], 'Shutdown' => [ 'shape' => 'Strings', ], ], ], 'RegisterEcsClusterRequest' => [ 'type' => 'structure', 'required' => [ 'EcsClusterArn', 'StackId', ], 'members' => [ 'EcsClusterArn' => [ 'shape' => 'String', ], 'StackId' => [ 'shape' => 'String', ], ], ], 'RegisterEcsClusterResult' => [ 'type' => 'structure', 'members' => [ 'EcsClusterArn' => [ 'shape' => 'String', ], ], ], 'RegisterElasticIpRequest' => [ 'type' => 'structure', 'required' => [ 'ElasticIp', 'StackId', ], 'members' => [ 'ElasticIp' => [ 'shape' => 'String', ], 'StackId' => [ 'shape' => 'String', ], ], ], 'RegisterElasticIpResult' => [ 'type' => 'structure', 'members' => [ 'ElasticIp' => [ 'shape' => 'String', ], ], ], 'RegisterInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'StackId', ], 'members' => [ 'StackId' => [ 'shape' => 'String', ], 'Hostname' => [ 'shape' => 'String', ], 'PublicIp' => [ 'shape' => 'String', ], 'PrivateIp' => [ 'shape' => 'String', ], 'RsaPublicKey' => [ 'shape' => 'String', ], 'RsaPublicKeyFingerprint' => [ 'shape' => 'String', ], 'InstanceIdentity' => [ 'shape' => 'InstanceIdentity', ], ], ], 'RegisterInstanceResult' => [ 'type' => 'structure', 'members' => [ 'InstanceId' => [ 'shape' => 'String', ], ], ], 'RegisterRdsDbInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'StackId', 'RdsDbInstanceArn', 'DbUser', 'DbPassword', ], 'members' => [ 'StackId' => [ 'shape' => 'String', ], 'RdsDbInstanceArn' => [ 'shape' => 'String', ], 'DbUser' => [ 'shape' => 'String', ], 'DbPassword' => [ 'shape' => 'String', ], ], ], 'RegisterVolumeRequest' => [ 'type' => 'structure', 'required' => [ 'StackId', ], 'members' => [ 'Ec2VolumeId' => [ 'shape' => 'String', ], 'StackId' => [ 'shape' => 'String', ], ], ], 'RegisterVolumeResult' => [ 'type' => 'structure', 'members' => [ 'VolumeId' => [ 'shape' => 'String', ], ], ], 'ReportedOs' => [ 'type' => 'structure', 'members' => [ 'Family' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'Version' => [ 'shape' => 'String', ], ], ], 'ResourceArn' => [ 'type' => 'string', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'RootDeviceType' => [ 'type' => 'string', 'enum' => [ 'ebs', 'instance-store', ], ], 'SelfUserProfile' => [ 'type' => 'structure', 'members' => [ 'IamUserArn' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'SshUsername' => [ 'shape' => 'String', ], 'SshPublicKey' => [ 'shape' => 'String', ], ], ], 'ServiceError' => [ 'type' => 'structure', 'members' => [ 'ServiceErrorId' => [ 'shape' => 'String', ], 'StackId' => [ 'shape' => 'String', ], 'InstanceId' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], 'CreatedAt' => [ 'shape' => 'DateTime', ], ], ], 'ServiceErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceError', ], ], 'SetLoadBasedAutoScalingRequest' => [ 'type' => 'structure', 'required' => [ 'LayerId', ], 'members' => [ 'LayerId' => [ 'shape' => 'String', ], 'Enable' => [ 'shape' => 'Boolean', ], 'UpScaling' => [ 'shape' => 'AutoScalingThresholds', ], 'DownScaling' => [ 'shape' => 'AutoScalingThresholds', ], ], ], 'SetPermissionRequest' => [ 'type' => 'structure', 'required' => [ 'StackId', 'IamUserArn', ], 'members' => [ 'StackId' => [ 'shape' => 'String', ], 'IamUserArn' => [ 'shape' => 'String', ], 'AllowSsh' => [ 'shape' => 'Boolean', ], 'AllowSudo' => [ 'shape' => 'Boolean', ], 'Level' => [ 'shape' => 'String', ], ], ], 'SetTimeBasedAutoScalingRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'String', ], 'AutoScalingSchedule' => [ 'shape' => 'WeeklyAutoScalingSchedule', ], ], ], 'ShutdownEventConfiguration' => [ 'type' => 'structure', 'members' => [ 'ExecutionTimeout' => [ 'shape' => 'Integer', ], 'DelayUntilElbConnectionsDrained' => [ 'shape' => 'Boolean', ], ], ], 'Source' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'SourceType', ], 'Url' => [ 'shape' => 'String', ], 'Username' => [ 'shape' => 'String', ], 'Password' => [ 'shape' => 'String', ], 'SshKey' => [ 'shape' => 'String', ], 'Revision' => [ 'shape' => 'String', ], ], ], 'SourceType' => [ 'type' => 'string', 'enum' => [ 'git', 'svn', 'archive', 's3', ], ], 'SslConfiguration' => [ 'type' => 'structure', 'required' => [ 'Certificate', 'PrivateKey', ], 'members' => [ 'Certificate' => [ 'shape' => 'String', ], 'PrivateKey' => [ 'shape' => 'String', ], 'Chain' => [ 'shape' => 'String', ], ], ], 'Stack' => [ 'type' => 'structure', 'members' => [ 'StackId' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'Arn' => [ 'shape' => 'String', ], 'Region' => [ 'shape' => 'String', ], 'VpcId' => [ 'shape' => 'String', ], 'Attributes' => [ 'shape' => 'StackAttributes', ], 'ServiceRoleArn' => [ 'shape' => 'String', ], 'DefaultInstanceProfileArn' => [ 'shape' => 'String', ], 'DefaultOs' => [ 'shape' => 'String', ], 'HostnameTheme' => [ 'shape' => 'String', ], 'DefaultAvailabilityZone' => [ 'shape' => 'String', ], 'DefaultSubnetId' => [ 'shape' => 'String', ], 'CustomJson' => [ 'shape' => 'String', ], 'ConfigurationManager' => [ 'shape' => 'StackConfigurationManager', ], 'ChefConfiguration' => [ 'shape' => 'ChefConfiguration', ], 'UseCustomCookbooks' => [ 'shape' => 'Boolean', ], 'UseOpsworksSecurityGroups' => [ 'shape' => 'Boolean', ], 'CustomCookbooksSource' => [ 'shape' => 'Source', ], 'DefaultSshKeyName' => [ 'shape' => 'String', ], 'CreatedAt' => [ 'shape' => 'DateTime', ], 'DefaultRootDeviceType' => [ 'shape' => 'RootDeviceType', ], 'AgentVersion' => [ 'shape' => 'String', ], ], ], 'StackAttributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'StackAttributesKeys', ], 'value' => [ 'shape' => 'String', ], ], 'StackAttributesKeys' => [ 'type' => 'string', 'enum' => [ 'Color', ], ], 'StackConfigurationManager' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Version' => [ 'shape' => 'String', ], ], ], 'StackSummary' => [ 'type' => 'structure', 'members' => [ 'StackId' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'Arn' => [ 'shape' => 'String', ], 'LayersCount' => [ 'shape' => 'Integer', ], 'AppsCount' => [ 'shape' => 'Integer', ], 'InstancesCount' => [ 'shape' => 'InstancesCount', ], ], ], 'Stacks' => [ 'type' => 'list', 'member' => [ 'shape' => 'Stack', ], ], 'StartInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'String', ], ], ], 'StartStackRequest' => [ 'type' => 'structure', 'required' => [ 'StackId', ], 'members' => [ 'StackId' => [ 'shape' => 'String', ], ], ], 'StopInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'String', ], 'Force' => [ 'shape' => 'Boolean', ], ], ], 'StopStackRequest' => [ 'type' => 'structure', 'required' => [ 'StackId', ], 'members' => [ 'StackId' => [ 'shape' => 'String', ], ], ], 'String' => [ 'type' => 'string', ], 'Strings' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Switch' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'TagValue' => [ 'type' => 'string', ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], 'TemporaryCredential' => [ 'type' => 'structure', 'members' => [ 'Username' => [ 'shape' => 'String', ], 'Password' => [ 'shape' => 'String', ], 'ValidForInMinutes' => [ 'shape' => 'Integer', ], 'InstanceId' => [ 'shape' => 'String', ], ], ], 'TimeBasedAutoScalingConfiguration' => [ 'type' => 'structure', 'members' => [ 'InstanceId' => [ 'shape' => 'String', ], 'AutoScalingSchedule' => [ 'shape' => 'WeeklyAutoScalingSchedule', ], ], ], 'TimeBasedAutoScalingConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'TimeBasedAutoScalingConfiguration', ], ], 'UnassignInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'String', ], ], ], 'UnassignVolumeRequest' => [ 'type' => 'structure', 'required' => [ 'VolumeId', ], 'members' => [ 'VolumeId' => [ 'shape' => 'String', ], ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeys', ], ], ], 'UpdateAppRequest' => [ 'type' => 'structure', 'required' => [ 'AppId', ], 'members' => [ 'AppId' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'DataSources' => [ 'shape' => 'DataSources', ], 'Type' => [ 'shape' => 'AppType', ], 'AppSource' => [ 'shape' => 'Source', ], 'Domains' => [ 'shape' => 'Strings', ], 'EnableSsl' => [ 'shape' => 'Boolean', ], 'SslConfiguration' => [ 'shape' => 'SslConfiguration', ], 'Attributes' => [ 'shape' => 'AppAttributes', ], 'Environment' => [ 'shape' => 'EnvironmentVariables', ], ], ], 'UpdateElasticIpRequest' => [ 'type' => 'structure', 'required' => [ 'ElasticIp', ], 'members' => [ 'ElasticIp' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], ], ], 'UpdateInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'String', ], 'LayerIds' => [ 'shape' => 'Strings', ], 'InstanceType' => [ 'shape' => 'String', ], 'AutoScalingType' => [ 'shape' => 'AutoScalingType', ], 'Hostname' => [ 'shape' => 'String', ], 'Os' => [ 'shape' => 'String', ], 'AmiId' => [ 'shape' => 'String', ], 'SshKeyName' => [ 'shape' => 'String', ], 'Architecture' => [ 'shape' => 'Architecture', ], 'InstallUpdatesOnBoot' => [ 'shape' => 'Boolean', ], 'EbsOptimized' => [ 'shape' => 'Boolean', ], 'AgentVersion' => [ 'shape' => 'String', ], ], ], 'UpdateLayerRequest' => [ 'type' => 'structure', 'required' => [ 'LayerId', ], 'members' => [ 'LayerId' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'Shortname' => [ 'shape' => 'String', ], 'Attributes' => [ 'shape' => 'LayerAttributes', ], 'CloudWatchLogsConfiguration' => [ 'shape' => 'CloudWatchLogsConfiguration', ], 'CustomInstanceProfileArn' => [ 'shape' => 'String', ], 'CustomJson' => [ 'shape' => 'String', ], 'CustomSecurityGroupIds' => [ 'shape' => 'Strings', ], 'Packages' => [ 'shape' => 'Strings', ], 'VolumeConfigurations' => [ 'shape' => 'VolumeConfigurations', ], 'EnableAutoHealing' => [ 'shape' => 'Boolean', ], 'AutoAssignElasticIps' => [ 'shape' => 'Boolean', ], 'AutoAssignPublicIps' => [ 'shape' => 'Boolean', ], 'CustomRecipes' => [ 'shape' => 'Recipes', ], 'InstallUpdatesOnBoot' => [ 'shape' => 'Boolean', ], 'UseEbsOptimizedInstances' => [ 'shape' => 'Boolean', ], 'LifecycleEventConfiguration' => [ 'shape' => 'LifecycleEventConfiguration', ], ], ], 'UpdateMyUserProfileRequest' => [ 'type' => 'structure', 'members' => [ 'SshPublicKey' => [ 'shape' => 'String', ], ], ], 'UpdateRdsDbInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'RdsDbInstanceArn', ], 'members' => [ 'RdsDbInstanceArn' => [ 'shape' => 'String', ], 'DbUser' => [ 'shape' => 'String', ], 'DbPassword' => [ 'shape' => 'String', ], ], ], 'UpdateStackRequest' => [ 'type' => 'structure', 'required' => [ 'StackId', ], 'members' => [ 'StackId' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'Attributes' => [ 'shape' => 'StackAttributes', ], 'ServiceRoleArn' => [ 'shape' => 'String', ], 'DefaultInstanceProfileArn' => [ 'shape' => 'String', ], 'DefaultOs' => [ 'shape' => 'String', ], 'HostnameTheme' => [ 'shape' => 'String', ], 'DefaultAvailabilityZone' => [ 'shape' => 'String', ], 'DefaultSubnetId' => [ 'shape' => 'String', ], 'CustomJson' => [ 'shape' => 'String', ], 'ConfigurationManager' => [ 'shape' => 'StackConfigurationManager', ], 'ChefConfiguration' => [ 'shape' => 'ChefConfiguration', ], 'UseCustomCookbooks' => [ 'shape' => 'Boolean', ], 'CustomCookbooksSource' => [ 'shape' => 'Source', ], 'DefaultSshKeyName' => [ 'shape' => 'String', ], 'DefaultRootDeviceType' => [ 'shape' => 'RootDeviceType', ], 'UseOpsworksSecurityGroups' => [ 'shape' => 'Boolean', ], 'AgentVersion' => [ 'shape' => 'String', ], ], ], 'UpdateUserProfileRequest' => [ 'type' => 'structure', 'required' => [ 'IamUserArn', ], 'members' => [ 'IamUserArn' => [ 'shape' => 'String', ], 'SshUsername' => [ 'shape' => 'String', ], 'SshPublicKey' => [ 'shape' => 'String', ], 'AllowSelfManagement' => [ 'shape' => 'Boolean', ], ], ], 'UpdateVolumeRequest' => [ 'type' => 'structure', 'required' => [ 'VolumeId', ], 'members' => [ 'VolumeId' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'MountPoint' => [ 'shape' => 'String', ], ], ], 'UserProfile' => [ 'type' => 'structure', 'members' => [ 'IamUserArn' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'SshUsername' => [ 'shape' => 'String', ], 'SshPublicKey' => [ 'shape' => 'String', ], 'AllowSelfManagement' => [ 'shape' => 'Boolean', ], ], ], 'UserProfiles' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserProfile', ], ], 'ValidForInMinutes' => [ 'type' => 'integer', 'box' => true, 'max' => 1440, 'min' => 60, ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'VirtualizationType' => [ 'type' => 'string', 'enum' => [ 'paravirtual', 'hvm', ], ], 'Volume' => [ 'type' => 'structure', 'members' => [ 'VolumeId' => [ 'shape' => 'String', ], 'Ec2VolumeId' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'RaidArrayId' => [ 'shape' => 'String', ], 'InstanceId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'Size' => [ 'shape' => 'Integer', ], 'Device' => [ 'shape' => 'String', ], 'MountPoint' => [ 'shape' => 'String', ], 'Region' => [ 'shape' => 'String', ], 'AvailabilityZone' => [ 'shape' => 'String', ], 'VolumeType' => [ 'shape' => 'String', ], 'Iops' => [ 'shape' => 'Integer', ], 'Encrypted' => [ 'shape' => 'Boolean', ], ], ], 'VolumeConfiguration' => [ 'type' => 'structure', 'required' => [ 'MountPoint', 'NumberOfDisks', 'Size', ], 'members' => [ 'MountPoint' => [ 'shape' => 'String', ], 'RaidLevel' => [ 'shape' => 'Integer', ], 'NumberOfDisks' => [ 'shape' => 'Integer', ], 'Size' => [ 'shape' => 'Integer', ], 'VolumeType' => [ 'shape' => 'String', ], 'Iops' => [ 'shape' => 'Integer', ], 'Encrypted' => [ 'shape' => 'Boolean', ], ], ], 'VolumeConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'VolumeConfiguration', ], ], 'VolumeType' => [ 'type' => 'string', 'enum' => [ 'gp2', 'io1', 'standard', ], ], 'Volumes' => [ 'type' => 'list', 'member' => [ 'shape' => 'Volume', ], ], 'WeeklyAutoScalingSchedule' => [ 'type' => 'structure', 'members' => [ 'Monday' => [ 'shape' => 'DailyAutoScalingSchedule', ], 'Tuesday' => [ 'shape' => 'DailyAutoScalingSchedule', ], 'Wednesday' => [ 'shape' => 'DailyAutoScalingSchedule', ], 'Thursday' => [ 'shape' => 'DailyAutoScalingSchedule', ], 'Friday' => [ 'shape' => 'DailyAutoScalingSchedule', ], 'Saturday' => [ 'shape' => 'DailyAutoScalingSchedule', ], 'Sunday' => [ 'shape' => 'DailyAutoScalingSchedule', ], ], ], ],];
