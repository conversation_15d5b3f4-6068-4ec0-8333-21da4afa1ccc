net\authorize\api\contract\v1\TransactionResponseType\EmvResponseAType\TagsAType:
    properties:
        tag:
            expose: true
            access_type: public_method
            serialized_name: tag
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getTag
                setter: setTag
            xml_list:
                inline: true
                entry_name: tag
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            type: array<net\authorize\api\contract\v1\EmvTagType>
