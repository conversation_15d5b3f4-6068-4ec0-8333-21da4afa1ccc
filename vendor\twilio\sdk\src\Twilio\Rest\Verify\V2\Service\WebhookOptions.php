<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Verify
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Verify\V2\Service;

use Twilio\Options;
use Twilio\Values;

abstract class WebhookOptions
{
    /**
     * @param string $status
     * @param string $version
     * @return CreateWebhookOptions Options builder
     */
    public static function create(
        
        string $status = Values::NONE,
        string $version = Values::NONE

    ): CreateWebhookOptions
    {
        return new CreateWebhookOptions(
            $status,
            $version
        );
    }




    /**
     * @param string $friendlyName The string that you assigned to describe the webhook. **This value should not contain PII.**
     * @param string[] $eventTypes The array of events that this Webhook is subscribed to. Possible event types: `*, factor.deleted, factor.created, factor.verified, challenge.approved, challenge.denied`
     * @param string $webhookUrl The URL associated with this Webhook.
     * @param string $status
     * @param string $version
     * @return UpdateWebhookOptions Options builder
     */
    public static function update(
        
        string $friendlyName = Values::NONE,
        array $eventTypes = Values::ARRAY_NONE,
        string $webhookUrl = Values::NONE,
        string $status = Values::NONE,
        string $version = Values::NONE

    ): UpdateWebhookOptions
    {
        return new UpdateWebhookOptions(
            $friendlyName,
            $eventTypes,
            $webhookUrl,
            $status,
            $version
        );
    }

}

class CreateWebhookOptions extends Options
    {
    /**
     * @param string $status
     * @param string $version
     */
    public function __construct(
        
        string $status = Values::NONE,
        string $version = Values::NONE

    ) {
        $this->options['status'] = $status;
        $this->options['version'] = $version;
    }

    /**
     * @param string $status
     * @return $this Fluent Builder
     */
    public function setStatus(string $status): self
    {
        $this->options['status'] = $status;
        return $this;
    }

    /**
     * @param string $version
     * @return $this Fluent Builder
     */
    public function setVersion(string $version): self
    {
        $this->options['version'] = $version;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Verify.V2.CreateWebhookOptions ' . $options . ']';
    }
}




class UpdateWebhookOptions extends Options
    {
    /**
     * @param string $friendlyName The string that you assigned to describe the webhook. **This value should not contain PII.**
     * @param string[] $eventTypes The array of events that this Webhook is subscribed to. Possible event types: `*, factor.deleted, factor.created, factor.verified, challenge.approved, challenge.denied`
     * @param string $webhookUrl The URL associated with this Webhook.
     * @param string $status
     * @param string $version
     */
    public function __construct(
        
        string $friendlyName = Values::NONE,
        array $eventTypes = Values::ARRAY_NONE,
        string $webhookUrl = Values::NONE,
        string $status = Values::NONE,
        string $version = Values::NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
        $this->options['eventTypes'] = $eventTypes;
        $this->options['webhookUrl'] = $webhookUrl;
        $this->options['status'] = $status;
        $this->options['version'] = $version;
    }

    /**
     * The string that you assigned to describe the webhook. **This value should not contain PII.**
     *
     * @param string $friendlyName The string that you assigned to describe the webhook. **This value should not contain PII.**
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * The array of events that this Webhook is subscribed to. Possible event types: `*, factor.deleted, factor.created, factor.verified, challenge.approved, challenge.denied`
     *
     * @param string[] $eventTypes The array of events that this Webhook is subscribed to. Possible event types: `*, factor.deleted, factor.created, factor.verified, challenge.approved, challenge.denied`
     * @return $this Fluent Builder
     */
    public function setEventTypes(array $eventTypes): self
    {
        $this->options['eventTypes'] = $eventTypes;
        return $this;
    }

    /**
     * The URL associated with this Webhook.
     *
     * @param string $webhookUrl The URL associated with this Webhook.
     * @return $this Fluent Builder
     */
    public function setWebhookUrl(string $webhookUrl): self
    {
        $this->options['webhookUrl'] = $webhookUrl;
        return $this;
    }

    /**
     * @param string $status
     * @return $this Fluent Builder
     */
    public function setStatus(string $status): self
    {
        $this->options['status'] = $status;
        return $this;
    }

    /**
     * @param string $version
     * @return $this Fluent Builder
     */
    public function setVersion(string $version): self
    {
        $this->options['version'] = $version;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Verify.V2.UpdateWebhookOptions ' . $options . ']';
    }
}

