net\authorize\api\contract\v1\UpdateSplitTenderGroupRequest:
    xml_root_name: updateSplitTenderGroupRequest
    xml_root_namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
    properties:
        splitTenderId:
            expose: true
            access_type: public_method
            serialized_name: splitTenderId
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getSplitTenderId
                setter: setSplitTenderId
            type: string
        splitTenderStatus:
            expose: true
            access_type: public_method
            serialized_name: splitTenderStatus
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getSplitTenderStatus
                setter: setSplitTenderStatus
            type: string
