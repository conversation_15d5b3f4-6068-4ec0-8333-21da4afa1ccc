<?php
// This file was auto-generated from sdk-root/src/data/keyspacesstreams/2024-09-09/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2024-09-09', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'cassandra-streams', 'jsonVersion' => '1.0', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'Amazon Keyspaces Streams', 'serviceId' => 'KeyspacesStreams', 'signatureVersion' => 'v4', 'signingName' => 'cassandra', 'targetPrefix' => 'KeyspacesStreams', 'uid' => 'keyspacesstreams-2024-09-09', ], 'operations' => [ 'GetRecords' => [ 'name' => 'GetRecords', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRecordsInput', ], 'output' => [ 'shape' => 'GetRecordsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetShardIterator' => [ 'name' => 'GetShardIterator', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetShardIteratorInput', ], 'output' => [ 'shape' => 'GetShardIteratorOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetStream' => [ 'name' => 'GetStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetStreamInput', ], 'output' => [ 'shape' => 'GetStreamOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListStreams' => [ 'name' => 'ListStreams', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStreamsInput', ], 'output' => [ 'shape' => 'ListStreamsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'Blob' => [ 'type' => 'blob', ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'Date' => [ 'type' => 'timestamp', ], 'GetRecordsInput' => [ 'type' => 'structure', 'required' => [ 'shardIterator', ], 'members' => [ 'shardIterator' => [ 'shape' => 'ShardIterator', ], 'maxResults' => [ 'shape' => 'GetRecordsInputMaxResultsInteger', ], ], ], 'GetRecordsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'GetRecordsOutput' => [ 'type' => 'structure', 'members' => [ 'changeRecords' => [ 'shape' => 'RecordList', ], 'nextShardIterator' => [ 'shape' => 'ShardIterator', ], ], ], 'GetShardIteratorInput' => [ 'type' => 'structure', 'required' => [ 'streamArn', 'shardId', 'shardIteratorType', ], 'members' => [ 'streamArn' => [ 'shape' => 'StreamArn', ], 'shardId' => [ 'shape' => 'ShardId', ], 'shardIteratorType' => [ 'shape' => 'ShardIteratorType', ], 'sequenceNumber' => [ 'shape' => 'SequenceNumber', ], ], ], 'GetShardIteratorOutput' => [ 'type' => 'structure', 'members' => [ 'shardIterator' => [ 'shape' => 'ShardIterator', ], ], ], 'GetStreamInput' => [ 'type' => 'structure', 'required' => [ 'streamArn', ], 'members' => [ 'streamArn' => [ 'shape' => 'StreamArn', ], 'maxResults' => [ 'shape' => 'GetStreamInputMaxResultsInteger', ], 'shardFilter' => [ 'shape' => 'ShardFilter', ], 'nextToken' => [ 'shape' => 'ShardIdToken', ], ], ], 'GetStreamInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'GetStreamOutput' => [ 'type' => 'structure', 'required' => [ 'streamArn', 'streamLabel', 'streamStatus', 'streamViewType', 'creationRequestDateTime', 'keyspaceName', 'tableName', ], 'members' => [ 'streamArn' => [ 'shape' => 'StreamArn', ], 'streamLabel' => [ 'shape' => 'String', ], 'streamStatus' => [ 'shape' => 'StreamStatus', ], 'streamViewType' => [ 'shape' => 'StreamViewType', ], 'creationRequestDateTime' => [ 'shape' => 'Date', ], 'keyspaceName' => [ 'shape' => 'KeyspaceName', ], 'tableName' => [ 'shape' => 'TableName', ], 'shards' => [ 'shape' => 'ShardDescriptionList', ], 'nextToken' => [ 'shape' => 'ShardIdToken', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, 'fault' => true, ], 'KeyspaceName' => [ 'type' => 'string', 'max' => 48, 'min' => 1, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9_]{0,47}', ], 'KeyspacesCell' => [ 'type' => 'structure', 'members' => [ 'value' => [ 'shape' => 'KeyspacesCellValue', ], 'metadata' => [ 'shape' => 'KeyspacesMetadata', ], ], ], 'KeyspacesCellList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeyspacesCell', ], ], 'KeyspacesCellMap' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeyspacesCellMapDefinition', ], ], 'KeyspacesCellMapDefinition' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'KeyspacesCellValue', ], 'value' => [ 'shape' => 'KeyspacesCellValue', ], 'metadata' => [ 'shape' => 'KeyspacesMetadata', ], ], ], 'KeyspacesCellValue' => [ 'type' => 'structure', 'members' => [ 'asciiT' => [ 'shape' => 'String', ], 'bigintT' => [ 'shape' => 'String', ], 'blobT' => [ 'shape' => 'Blob', ], 'boolT' => [ 'shape' => 'Boolean', ], 'counterT' => [ 'shape' => 'String', ], 'dateT' => [ 'shape' => 'String', ], 'decimalT' => [ 'shape' => 'String', ], 'doubleT' => [ 'shape' => 'String', ], 'floatT' => [ 'shape' => 'String', ], 'inetT' => [ 'shape' => 'String', ], 'intT' => [ 'shape' => 'String', ], 'listT' => [ 'shape' => 'KeyspacesCellList', ], 'mapT' => [ 'shape' => 'KeyspacesCellMap', ], 'setT' => [ 'shape' => 'KeyspacesCellList', ], 'smallintT' => [ 'shape' => 'String', ], 'textT' => [ 'shape' => 'String', ], 'timeT' => [ 'shape' => 'String', ], 'timestampT' => [ 'shape' => 'String', ], 'timeuuidT' => [ 'shape' => 'String', ], 'tinyintT' => [ 'shape' => 'String', ], 'tupleT' => [ 'shape' => 'KeyspacesCellList', ], 'uuidT' => [ 'shape' => 'String', ], 'varcharT' => [ 'shape' => 'String', ], 'varintT' => [ 'shape' => 'String', ], 'udtT' => [ 'shape' => 'KeyspacesUdtMap', ], ], 'union' => true, ], 'KeyspacesCells' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'KeyspacesCell', ], ], 'KeyspacesKeysMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'KeyspacesCellValue', ], ], 'KeyspacesMetadata' => [ 'type' => 'structure', 'members' => [ 'expirationTime' => [ 'shape' => 'String', ], 'writeTime' => [ 'shape' => 'String', ], ], ], 'KeyspacesRow' => [ 'type' => 'structure', 'members' => [ 'valueCells' => [ 'shape' => 'KeyspacesCells', ], 'staticCells' => [ 'shape' => 'KeyspacesCells', ], 'rowMetadata' => [ 'shape' => 'KeyspacesMetadata', ], ], ], 'KeyspacesUdtMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'KeyspacesCell', ], ], 'ListStreamsInput' => [ 'type' => 'structure', 'members' => [ 'keyspaceName' => [ 'shape' => 'KeyspaceName', ], 'tableName' => [ 'shape' => 'TableName', ], 'maxResults' => [ 'shape' => 'ListStreamsInputMaxResultsInteger', ], 'nextToken' => [ 'shape' => 'StreamArnToken', ], ], ], 'ListStreamsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListStreamsOutput' => [ 'type' => 'structure', 'members' => [ 'streams' => [ 'shape' => 'StreamList', ], 'nextToken' => [ 'shape' => 'StreamArnToken', ], ], ], 'OriginType' => [ 'type' => 'string', 'enum' => [ 'USER', 'REPLICATION', 'TTL', ], ], 'Record' => [ 'type' => 'structure', 'members' => [ 'eventVersion' => [ 'shape' => 'String', ], 'createdAt' => [ 'shape' => 'Date', ], 'origin' => [ 'shape' => 'OriginType', ], 'partitionKeys' => [ 'shape' => 'KeyspacesKeysMap', ], 'clusteringKeys' => [ 'shape' => 'KeyspacesKeysMap', ], 'newImage' => [ 'shape' => 'KeyspacesRow', ], 'oldImage' => [ 'shape' => 'KeyspacesRow', ], 'sequenceNumber' => [ 'shape' => 'SequenceNumber', ], ], ], 'RecordList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Record', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'SequenceNumber' => [ 'type' => 'string', 'max' => 48, 'min' => 21, ], 'SequenceNumberRange' => [ 'type' => 'structure', 'members' => [ 'startingSequenceNumber' => [ 'shape' => 'SequenceNumber', ], 'endingSequenceNumber' => [ 'shape' => 'SequenceNumber', ], ], ], 'Shard' => [ 'type' => 'structure', 'members' => [ 'shardId' => [ 'shape' => 'ShardId', ], 'sequenceNumberRange' => [ 'shape' => 'SequenceNumberRange', ], 'parentShardIds' => [ 'shape' => 'ShardIdList', ], ], ], 'ShardDescriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Shard', ], ], 'ShardFilter' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'ShardFilterType', ], 'shardId' => [ 'shape' => 'ShardId', ], ], ], 'ShardFilterType' => [ 'type' => 'string', 'enum' => [ 'CHILD_SHARDS', ], ], 'ShardId' => [ 'type' => 'string', 'max' => 65, 'min' => 28, ], 'ShardIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ShardId', ], ], 'ShardIdToken' => [ 'type' => 'string', 'max' => 3000, 'min' => 80, ], 'ShardIterator' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'ShardIteratorType' => [ 'type' => 'string', 'enum' => [ 'TRIM_HORIZON', 'LATEST', 'AT_SEQUENCE_NUMBER', 'AFTER_SEQUENCE_NUMBER', ], ], 'Stream' => [ 'type' => 'structure', 'required' => [ 'streamArn', 'keyspaceName', 'tableName', 'streamLabel', ], 'members' => [ 'streamArn' => [ 'shape' => 'StreamArn', ], 'keyspaceName' => [ 'shape' => 'KeyspaceName', ], 'tableName' => [ 'shape' => 'TableName', ], 'streamLabel' => [ 'shape' => 'String', ], ], ], 'StreamArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 37, ], 'StreamArnToken' => [ 'type' => 'string', 'max' => 3000, 'min' => 80, ], 'StreamList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Stream', ], ], 'StreamStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLING', 'ENABLED', 'DISABLING', 'DISABLED', ], ], 'StreamViewType' => [ 'type' => 'string', 'enum' => [ 'NEW_IMAGE', 'OLD_IMAGE', 'NEW_AND_OLD_IMAGES', 'KEYS_ONLY', ], ], 'String' => [ 'type' => 'string', ], 'TableName' => [ 'type' => 'string', 'max' => 48, 'min' => 1, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9_]{0,47}', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'errorCode' => [ 'shape' => 'ValidationExceptionType', ], ], 'exception' => true, ], 'ValidationExceptionType' => [ 'type' => 'string', 'enum' => [ 'InvalidFormat', 'TrimmedDataAccess', 'ExpiredIterator', 'ExpiredNextToken', ], ], ],];
