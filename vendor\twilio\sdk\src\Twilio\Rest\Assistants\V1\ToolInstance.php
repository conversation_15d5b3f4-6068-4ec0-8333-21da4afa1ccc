<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Assistants
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Assistants\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;


/**
 * @property string $accountSid
 * @property string $description
 * @property bool $enabled
 * @property string $id
 * @property array $meta
 * @property string $name
 * @property bool $requiresAuth
 * @property string $type
 * @property string $url
 * @property \DateTime $dateCreated
 * @property \DateTime $dateUpdated
 * @property string[] $policies
 */
class ToolInstance extends InstanceResource
{
    /**
     * Initialize the ToolInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $id The tool ID.
     */
    public function __construct(Version $version, array $payload, ?string $id = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'accountSid' => Values::array_get($payload, 'account_sid'),
            'description' => Values::array_get($payload, 'description'),
            'enabled' => Values::array_get($payload, 'enabled'),
            'id' => Values::array_get($payload, 'id'),
            'meta' => Values::array_get($payload, 'meta'),
            'name' => Values::array_get($payload, 'name'),
            'requiresAuth' => Values::array_get($payload, 'requires_auth'),
            'type' => Values::array_get($payload, 'type'),
            'url' => Values::array_get($payload, 'url'),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'dateUpdated' => Deserialize::dateTime(Values::array_get($payload, 'date_updated')),
            'policies' => Values::array_get($payload, 'policies'),
        ];

        $this->solution = ['id' => $id ?: $this->properties['id'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return ToolContext Context for this ToolInstance
     */
    protected function proxy(): ToolContext
    {
        if (!$this->context) {
            $this->context = new ToolContext(
                $this->version,
                $this->solution['id']
            );
        }

        return $this->context;
    }

    /**
     * Delete the ToolInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->proxy()->delete();
    }

    /**
     * Fetch the ToolInstance
     *
     * @return ToolInstance Fetched ToolInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): ToolInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Update the ToolInstance
     *
     * @return ToolInstance Updated ToolInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(): ToolInstance
    {

        return $this->proxy()->update();
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Assistants.V1.ToolInstance ' . \implode(' ', $context) . ']';
    }
}

