# Changelog

All notable changes to `open-ai` will be documented in this file.

## 3.5 - 2023-02-10

### What's Changed

- Fix "export-ignore" does not work by @assert6 in https://github.com/orhanerday/open-ai/pull/43
- Update README.md by @ali-wells in https://github.com/orhanerday/open-ai/pull/45
- Bump dependabot/fetch-metadata from 1.3.5 to 1.3.6 by @dependabot in https://github.com/orhanerday/open-ai/pull/47
- Add customURL support. by @orhanerday in https://github.com/orhanerday/open-ai/pull/49

### New Contributors

- @assert6 made their first contribution in https://github.com/orhanerday/open-ai/pull/43
- @ali-wells made their first contribution in https://github.com/orhanerday/open-ai/pull/45

**Full Changelog**: https://github.com/orhanerday/open-ai/compare/3.4...3.5

## 3.4 - 2023-01-03

### What's Changed

- Add organization support. by @orhanerday in https://github.com/orhanerday/open-ai/pull/40

**Full Changelog**: https://github.com/orhanerday/open-ai/compare/3.3...3.4

## 3.3 - 2022-12-28

### What's Changed

- Feature request: support stream by @orhanerday in https://github.com/orhanerday/open-ai/pull/37

**Full Changelog**: https://github.com/orhanerday/open-ai/compare/3.2.1...3.3

## 3.2.1 - 2022-12-07

### What's Changed

- Update README.md by @orhanerday

**Full Changelog**: https://github.com/orhanerday/open-ai/compare/3.2...3.2.1

## 3.2 - 2022-12-06

### What's Changed

- Ability to set a timeout value in seconds by @dsampaolo in https://github.com/orhanerday/open-ai/pull/31

### New Contributors

- @dsampaolo made their first contribution in https://github.com/orhanerday/open-ai/pull/31

**Full Changelog**: https://github.com/orhanerday/open-ai/compare/3.1...3.2

## 3.1 - 2022-11-22

### What's Changed

- Bump dependabot/fetch-metadata from 1.3.4 to 1.3.5 by @dependabot in https://github.com/orhanerday/open-ai/pull/28
- Fix tests by @adetch in https://github.com/orhanerday/open-ai/pull/29
- Add current completion endpoint by @adetch in https://github.com/orhanerday/open-ai/pull/30

### New Contributors

- @adetch made their first contribution in https://github.com/orhanerday/open-ai/pull/29

**Full Changelog**: https://github.com/orhanerday/open-ai/compare/3.0...3.1

## 3.0 - 2022-11-04

### What's Changed

- List models by @orhanerday in https://github.com/orhanerday/open-ai/pull/22
- Implement edit feature by @orhanerday in https://github.com/orhanerday/open-ai/pull/23
- Add Images feature by @orhanerday in https://github.com/orhanerday/open-ai/pull/24
- Embeddings feature by @orhanerday in https://github.com/orhanerday/open-ai/pull/25
- Add retrieve file content by @orhanerday in https://github.com/orhanerday/open-ai/pull/26
- Add deprecated methods by @orhanerday in https://github.com/orhanerday/open-ai/pull/27

**Full Changelog**: https://github.com/orhanerday/open-ai/compare/2.3...3.0

## 2.3 - 2022-11-04

### What's Changed

- Fix documentation for Url Class by @Muchwat in https://github.com/orhanerday/open-ai/pull/18
- Bump dependabot/fetch-metadata from 1.3.3 to 1.3.4 by @dependabot in https://github.com/orhanerday/open-ai/pull/19
- feat: Add image generation api by @SheepFromHeaven in https://github.com/orhanerday/open-ai/pull/21

### New Contributors

- @Muchwat made their first contribution in https://github.com/orhanerday/open-ai/pull/18
- @SheepFromHeaven made their first contribution in https://github.com/orhanerday/open-ai/pull/21

**Full Changelog**: https://github.com/orhanerday/open-ai/compare/2.2...2.3

## 2.2 - 2022-09-15

### What's Changed

- API for content filter, unit testing fixed and added documentation for content filtering by @bashar94 in https://github.com/orhanerday/open-ai/pull/17

### New Contributors

- @bashar94 made their first contribution in https://github.com/orhanerday/open-ai/pull/17

**Full Changelog**: https://github.com/orhanerday/open-ai/compare/2.1...2.2

## 2.1 - 2022-05-29

### What's Changed

- Adding more capabilities by @orhanerday in https://github.com/orhanerday/open-ai/pull/11
- Files
- Fine-tuning

### New Contributors

- @orhanerday made their first contribution in https://github.com/orhanerday/open-ai/pull/11

**Full Changelog**: https://github.com/orhanerday/open-ai/compare/2.0...2.1

## 2.0 - 2022-05-28

### What's Changed

- Orhanerday/open-ai now supports file uploads
- Bump dependabot/fetch-metadata from 1.1.1 to 1.2.0 by @dependabot in https://github.com/orhanerday/open-ai/pull/3
- Bump dependabot/fetch-metadata from 1.2.0 to 1.2.1 by @dependabot in https://github.com/orhanerday/open-ai/pull/4
- Bump dependabot/fetch-metadata from 1.2.1 to 1.3.0 by @dependabot in https://github.com/orhanerday/open-ai/pull/5
- Bump actions/checkout from 2 to 3 by @dependabot in https://github.com/orhanerday/open-ai/pull/6
- Bump dependabot/fetch-metadata from 1.3.0 to 1.3.1 by @dependabot in https://github.com/orhanerday/open-ai/pull/7

### New Contributors

- @dependabot made their first contribution in https://github.com/orhanerday/open-ai/pull/3

**Full Changelog**: https://github.com/orhanerday/open-ai/compare/1.1...2.0

## 1.1 - 2022-02-01

### **Big new**

- [orhanerday/open-ai](https://github.com/orhanerday/open-ai) now supports PHP 8

## What's Changed

- Remove duplicate entry from the changelog by @johanvanhelden in https://github.com/orhanerday/open-ai/pull/1
- Allow PHP8 by @mydnic in https://github.com/orhanerday/open-ai/pull/2

## New Contributors

- @johanvanhelden made their first contribution in https://github.com/orhanerday/open-ai/pull/1
- @mydnic made their first contribution in https://github.com/orhanerday/open-ai/pull/2

**Full Changelog**: https://github.com/orhanerday/open-ai/compare/1.0.0...1.1

## 1.0.0 - 2021-12-22

- initial release
