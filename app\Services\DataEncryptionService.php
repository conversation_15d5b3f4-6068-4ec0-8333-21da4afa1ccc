<?php

namespace App\Services;

use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Storage;

class DataEncryptionService
{
    /**
     * تشفير البيانات الحساسة
     */
    public static function encryptSensitiveData($data)
    {
        if (is_array($data)) {
            return array_map([self::class, 'encryptSensitiveData'], $data);
        }
        
        if (is_string($data)) {
            return Crypt::encryptString($data);
        }
        
        return $data;
    }
    
    /**
     * فك تشفير البيانات الحساسة
     */
    public static function decryptSensitiveData($data)
    {
        if (is_array($data)) {
            return array_map([self::class, 'decryptSensitiveData'], $data);
        }
        
        if (is_string($data)) {
            try {
                return Crypt::decryptString($data);
            } catch (\Exception $e) {
                // إذا فشل فك التشفير، إرجاع البيانات كما هي
                return $data;
            }
        }
        
        return $data;
    }
    
    /**
     * تشفير ملف CSV
     */
    public static function encryptCsvFile($filePath)
    {
        if (!Storage::exists($filePath)) {
            return false;
        }
        
        $content = Storage::get($filePath);
        $lines = explode("\n", $content);
        $encryptedLines = [];
        
        foreach ($lines as $index => $line) {
            if ($index === 0 || empty(trim($line))) {
                // الاحتفاظ بالعنوان والسطور الفارغة كما هي
                $encryptedLines[] = $line;
                continue;
            }
            
            $columns = str_getcsv($line);
            $encryptedColumns = [];
            
            foreach ($columns as $columnIndex => $column) {
                // تشفير البيانات الحساسة (IP, Location data)
                if (in_array($columnIndex, [0, 8, 9, 10, 11, 12, 13, 14])) {
                    $encryptedColumns[] = self::encryptSensitiveData($column);
                } else {
                    $encryptedColumns[] = $column;
                }
            }
            
            $encryptedLines[] = implode(',', array_map(function($field) {
                return '"' . str_replace('"', '""', $field) . '"';
            }, $encryptedColumns));
        }
        
        $encryptedContent = implode("\n", $encryptedLines);
        Storage::put($filePath . '.encrypted', $encryptedContent);
        
        return true;
    }
    
    /**
     * إخفاء IP address جزئياً
     */
    public static function maskIpAddress($ip)
    {
        $parts = explode('.', $ip);
        if (count($parts) === 4) {
            return $parts[0] . '.' . $parts[1] . '.***.' . $parts[3];
        }
        return '***';
    }
    
    /**
     * إخفاء البيانات الحساسة للعرض
     */
    public static function maskSensitiveData($data, $type = 'ip')
    {
        switch ($type) {
            case 'ip':
                return self::maskIpAddress($data);
            case 'location':
                return '***';
            case 'coordinates':
                return '***';
            default:
                return substr($data, 0, 3) . '***';
        }
    }
}
