<?php
// This file was auto-generated from sdk-root/src/data/ssm-quicksetup/2018-05-10/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-10', 'endpointPrefix' => 'ssm-quicksetup', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS Systems Manager QuickSetup', 'serviceId' => 'SSM QuickSetup', 'signatureVersion' => 'v4', 'signingName' => 'ssm-quicksetup', 'uid' => 'ssm-quicksetup-2018-05-10', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'CreateConfigurationManager' => [ 'name' => 'CreateConfigurationManager', 'http' => [ 'method' => 'POST', 'requestUri' => '/configurationManager', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateConfigurationManagerInput', ], 'output' => [ 'shape' => 'CreateConfigurationManagerOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteConfigurationManager' => [ 'name' => 'DeleteConfigurationManager', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/configurationManager/{ManagerArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteConfigurationManagerInput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'GetConfiguration' => [ 'name' => 'GetConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/getConfiguration/{ConfigurationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetConfigurationInput', ], 'output' => [ 'shape' => 'GetConfigurationOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetConfigurationManager' => [ 'name' => 'GetConfigurationManager', 'http' => [ 'method' => 'GET', 'requestUri' => '/configurationManager/{ManagerArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetConfigurationManagerInput', ], 'output' => [ 'shape' => 'GetConfigurationManagerOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetServiceSettings' => [ 'name' => 'GetServiceSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/serviceSettings', 'responseCode' => 200, ], 'output' => [ 'shape' => 'GetServiceSettingsOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListConfigurationManagers' => [ 'name' => 'ListConfigurationManagers', 'http' => [ 'method' => 'POST', 'requestUri' => '/listConfigurationManagers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListConfigurationManagersInput', ], 'output' => [ 'shape' => 'ListConfigurationManagersOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListConfigurations' => [ 'name' => 'ListConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/listConfigurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListConfigurationsInput', ], 'output' => [ 'shape' => 'ListConfigurationsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListQuickSetupTypes' => [ 'name' => 'ListQuickSetupTypes', 'http' => [ 'method' => 'GET', 'requestUri' => '/listQuickSetupTypes', 'responseCode' => 200, ], 'output' => [ 'shape' => 'ListQuickSetupTypesOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceInput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceInput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateConfigurationDefinition' => [ 'name' => 'UpdateConfigurationDefinition', 'http' => [ 'method' => 'PUT', 'requestUri' => '/configurationDefinition/{ManagerArn}/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateConfigurationDefinitionInput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateConfigurationManager' => [ 'name' => 'UpdateConfigurationManager', 'http' => [ 'method' => 'PUT', 'requestUri' => '/configurationManager/{ManagerArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateConfigurationManagerInput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateServiceSettings' => [ 'name' => 'UpdateServiceSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/serviceSettings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateServiceSettingsInput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'ConfigurationDefinition' => [ 'type' => 'structure', 'required' => [ 'Parameters', 'Type', ], 'members' => [ 'Id' => [ 'shape' => 'String', ], 'LocalDeploymentAdministrationRoleArn' => [ 'shape' => 'IAMRoleArn', ], 'LocalDeploymentExecutionRoleName' => [ 'shape' => 'ConfigurationDefinitionLocalDeploymentExecutionRoleNameString', ], 'Parameters' => [ 'shape' => 'ConfigurationParametersMap', ], 'Type' => [ 'shape' => 'ConfigurationDefinitionTypeString', ], 'TypeVersion' => [ 'shape' => 'ConfigurationDefinitionTypeVersionString', ], ], ], 'ConfigurationDefinitionInput' => [ 'type' => 'structure', 'required' => [ 'Parameters', 'Type', ], 'members' => [ 'LocalDeploymentAdministrationRoleArn' => [ 'shape' => 'IAMRoleArn', ], 'LocalDeploymentExecutionRoleName' => [ 'shape' => 'ConfigurationDefinitionInputLocalDeploymentExecutionRoleNameString', ], 'Parameters' => [ 'shape' => 'ConfigurationParametersMap', ], 'Type' => [ 'shape' => 'ConfigurationDefinitionInputTypeString', ], 'TypeVersion' => [ 'shape' => 'ConfigurationDefinitionInputTypeVersionString', ], ], ], 'ConfigurationDefinitionInputLocalDeploymentExecutionRoleNameString' => [ 'type' => 'string', 'pattern' => '^[\\w+=,.@-]{1,64}$', ], 'ConfigurationDefinitionInputTypeString' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_\\-.:/]{3,200}$', ], 'ConfigurationDefinitionInputTypeVersionString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'ConfigurationDefinitionLocalDeploymentExecutionRoleNameString' => [ 'type' => 'string', 'pattern' => '^[\\w+=,.@-]{1,64}$', ], 'ConfigurationDefinitionSummariesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigurationDefinitionSummary', ], ], 'ConfigurationDefinitionSummary' => [ 'type' => 'structure', 'members' => [ 'FirstClassParameters' => [ 'shape' => 'ConfigurationParametersMap', ], 'Id' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'String', ], 'TypeVersion' => [ 'shape' => 'String', ], ], ], 'ConfigurationDefinitionTypeString' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9_\\-.:/]{3,200}$', ], 'ConfigurationDefinitionTypeVersionString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'ConfigurationDefinitionsInputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigurationDefinitionInput', ], ], 'ConfigurationDefinitionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigurationDefinition', ], ], 'ConfigurationManagerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigurationManagerSummary', ], ], 'ConfigurationManagerSummary' => [ 'type' => 'structure', 'required' => [ 'ManagerArn', ], 'members' => [ 'ConfigurationDefinitionSummaries' => [ 'shape' => 'ConfigurationDefinitionSummariesList', ], 'Description' => [ 'shape' => 'String', ], 'ManagerArn' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'StatusSummaries' => [ 'shape' => 'StatusSummariesList', ], ], ], 'ConfigurationParametersMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ConfigurationParametersMapKeyString', ], 'value' => [ 'shape' => 'ConfigurationParametersMapValueString', ], ], 'ConfigurationParametersMapKeyString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[A-Za-z0-9+=@_\\/\\s-]+$', ], 'ConfigurationParametersMapValueString' => [ 'type' => 'string', 'max' => 40960, 'min' => 0, ], 'ConfigurationSummary' => [ 'type' => 'structure', 'members' => [ 'Account' => [ 'shape' => 'String', ], 'ConfigurationDefinitionId' => [ 'shape' => 'String', ], 'CreatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'FirstClassParameters' => [ 'shape' => 'ConfigurationParametersMap', ], 'Id' => [ 'shape' => 'String', ], 'ManagerArn' => [ 'shape' => 'String', ], 'Region' => [ 'shape' => 'String', ], 'StatusSummaries' => [ 'shape' => 'StatusSummariesList', ], 'Type' => [ 'shape' => 'String', ], 'TypeVersion' => [ 'shape' => 'String', ], ], ], 'ConfigurationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigurationSummary', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateConfigurationManagerInput' => [ 'type' => 'structure', 'required' => [ 'ConfigurationDefinitions', ], 'members' => [ 'ConfigurationDefinitions' => [ 'shape' => 'ConfigurationDefinitionsInputList', ], 'Description' => [ 'shape' => 'CreateConfigurationManagerInputDescriptionString', ], 'Name' => [ 'shape' => 'CreateConfigurationManagerInputNameString', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateConfigurationManagerInputDescriptionString' => [ 'type' => 'string', 'pattern' => '^.{0,512}$', ], 'CreateConfigurationManagerInputNameString' => [ 'type' => 'string', 'pattern' => '^[ A-Za-z0-9._-]{0,120}$', ], 'CreateConfigurationManagerOutput' => [ 'type' => 'structure', 'required' => [ 'ManagerArn', ], 'members' => [ 'ManagerArn' => [ 'shape' => 'String', ], ], ], 'DeleteConfigurationManagerInput' => [ 'type' => 'structure', 'required' => [ 'ManagerArn', ], 'members' => [ 'ManagerArn' => [ 'shape' => 'DeleteConfigurationManagerInputManagerArnString', 'location' => 'uri', 'locationName' => 'ManagerArn', ], ], ], 'DeleteConfigurationManagerInputManagerArnString' => [ 'type' => 'string', 'pattern' => '^arn:aws:ssm-quicksetup:([^:]+):(\\d{12}):configuration-manager/[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}$', ], 'Filter' => [ 'type' => 'structure', 'required' => [ 'Key', 'Values', ], 'members' => [ 'Key' => [ 'shape' => 'FilterKeyString', ], 'Values' => [ 'shape' => 'FilterValues', ], ], ], 'FilterKeyString' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^[A-Za-z0-9+=@_\\/\\s-]*$', ], 'FilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterValuesMemberString', ], ], 'FilterValuesMemberString' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^[A-Za-z0-9+=@_\\/\\s-]*$', ], 'FiltersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], ], 'GetConfigurationInput' => [ 'type' => 'structure', 'required' => [ 'ConfigurationId', ], 'members' => [ 'ConfigurationId' => [ 'shape' => 'GetConfigurationInputConfigurationIdString', 'location' => 'uri', 'locationName' => 'ConfigurationId', ], ], ], 'GetConfigurationInputConfigurationIdString' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9-_/:]{1,100}$', ], 'GetConfigurationManagerInput' => [ 'type' => 'structure', 'required' => [ 'ManagerArn', ], 'members' => [ 'ManagerArn' => [ 'shape' => 'GetConfigurationManagerInputManagerArnString', 'location' => 'uri', 'locationName' => 'ManagerArn', ], ], ], 'GetConfigurationManagerInputManagerArnString' => [ 'type' => 'string', 'min' => 1, 'pattern' => '^arn:aws:ssm-quicksetup:([^:]+):(\\d{12}):configuration-manager/[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}$', ], 'GetConfigurationManagerOutput' => [ 'type' => 'structure', 'required' => [ 'ManagerArn', ], 'members' => [ 'ConfigurationDefinitions' => [ 'shape' => 'ConfigurationDefinitionsList', ], 'CreatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'Description' => [ 'shape' => 'String', ], 'LastModifiedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'ManagerArn' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'StatusSummaries' => [ 'shape' => 'StatusSummariesList', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'GetConfigurationOutput' => [ 'type' => 'structure', 'members' => [ 'Account' => [ 'shape' => 'String', ], 'ConfigurationDefinitionId' => [ 'shape' => 'String', ], 'CreatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'Id' => [ 'shape' => 'String', ], 'LastModifiedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'ManagerArn' => [ 'shape' => 'String', ], 'Parameters' => [ 'shape' => 'ConfigurationParametersMap', ], 'Region' => [ 'shape' => 'String', ], 'StatusSummaries' => [ 'shape' => 'StatusSummariesList', ], 'Type' => [ 'shape' => 'String', ], 'TypeVersion' => [ 'shape' => 'String', ], ], ], 'GetServiceSettingsOutput' => [ 'type' => 'structure', 'members' => [ 'ServiceSettings' => [ 'shape' => 'ServiceSettings', ], ], ], 'IAMRoleArn' => [ 'type' => 'string', ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'ListConfigurationManagersInput' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FiltersList', ], 'MaxItems' => [ 'shape' => 'ListConfigurationManagersInputMaxItemsInteger', ], 'StartingToken' => [ 'shape' => 'ListConfigurationManagersInputStartingTokenString', ], ], ], 'ListConfigurationManagersInputMaxItemsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListConfigurationManagersInputStartingTokenString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '^[A-Za-z0-9+=@_\\/\\s-]*$', ], 'ListConfigurationManagersOutput' => [ 'type' => 'structure', 'members' => [ 'ConfigurationManagersList' => [ 'shape' => 'ConfigurationManagerList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListConfigurationsInput' => [ 'type' => 'structure', 'members' => [ 'ConfigurationDefinitionId' => [ 'shape' => 'ListConfigurationsInputConfigurationDefinitionIdString', ], 'Filters' => [ 'shape' => 'FiltersList', ], 'ManagerArn' => [ 'shape' => 'ListConfigurationsInputManagerArnString', ], 'MaxItems' => [ 'shape' => 'ListConfigurationsInputMaxItemsInteger', ], 'StartingToken' => [ 'shape' => 'ListConfigurationsInputStartingTokenString', ], ], ], 'ListConfigurationsInputConfigurationDefinitionIdString' => [ 'type' => 'string', 'pattern' => '^[a-z0-9-]{1,20}$', ], 'ListConfigurationsInputManagerArnString' => [ 'type' => 'string', 'pattern' => '^arn:aws:ssm-quicksetup:([^:]+):(\\d{12}):configuration-manager/[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}$', ], 'ListConfigurationsInputMaxItemsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListConfigurationsInputStartingTokenString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '^[A-Za-z0-9+=@_|\\/\\s-]*$', ], 'ListConfigurationsOutput' => [ 'type' => 'structure', 'members' => [ 'ConfigurationsList' => [ 'shape' => 'ConfigurationsList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListQuickSetupTypesOutput' => [ 'type' => 'structure', 'members' => [ 'QuickSetupTypeList' => [ 'shape' => 'QuickSetupTypeList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'Tags', ], ], ], 'QuickSetupTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QuickSetupTypeOutput', ], ], 'QuickSetupTypeOutput' => [ 'type' => 'structure', 'members' => [ 'LatestVersion' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'String', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ServiceSettings' => [ 'type' => 'structure', 'members' => [ 'ExplorerEnablingRoleArn' => [ 'shape' => 'IAMRoleArn', ], ], ], 'Status' => [ 'type' => 'string', 'enum' => [ 'INITIALIZING', 'DEPLOYING', 'SUCCEEDED', 'DELETING', 'STOPPING', 'FAILED', 'STOPPED', 'DELETE_FAILED', 'STOP_FAILED', 'NONE', ], ], 'StatusDetails' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'StatusSummariesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StatusSummary', ], ], 'StatusSummary' => [ 'type' => 'structure', 'required' => [ 'LastUpdatedAt', 'StatusType', ], 'members' => [ 'LastUpdatedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'Status' => [ 'shape' => 'Status', ], 'StatusDetails' => [ 'shape' => 'StatusDetails', ], 'StatusMessage' => [ 'shape' => 'String', ], 'StatusType' => [ 'shape' => 'StatusType', ], ], ], 'StatusType' => [ 'type' => 'string', 'enum' => [ 'Deployment', 'AsyncExecutions', ], ], 'String' => [ 'type' => 'string', ], 'SyntheticTimestamp_date_time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TagEntry' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'TagEntryKeyString', ], 'Value' => [ 'shape' => 'TagEntryValueString', ], ], 'sensitive' => true, ], 'TagEntryKeyString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[A-Za-z0-9 _=@:.+-/]+$', ], 'TagEntryValueString' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^[A-Za-z0-9 _=@:.+-/]+$', ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'TagResourceInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'Tags' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagEntry', ], 'sensitive' => true, ], 'TagsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagsMapKeyString', ], 'value' => [ 'shape' => 'TagsMapValueString', ], 'sensitive' => true, ], 'TagsMapKeyString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[A-Za-z0-9 _=@:.+-/]+$', ], 'TagsMapValueString' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^[A-Za-z0-9 _=@:.+-/]+$', ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'UntagResourceInput' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UpdateConfigurationDefinitionInput' => [ 'type' => 'structure', 'required' => [ 'Id', 'ManagerArn', ], 'members' => [ 'Id' => [ 'shape' => 'UpdateConfigurationDefinitionInputIdString', 'location' => 'uri', 'locationName' => 'Id', ], 'LocalDeploymentAdministrationRoleArn' => [ 'shape' => 'IAMRoleArn', ], 'LocalDeploymentExecutionRoleName' => [ 'shape' => 'UpdateConfigurationDefinitionInputLocalDeploymentExecutionRoleNameString', ], 'ManagerArn' => [ 'shape' => 'UpdateConfigurationDefinitionInputManagerArnString', 'location' => 'uri', 'locationName' => 'ManagerArn', ], 'Parameters' => [ 'shape' => 'ConfigurationParametersMap', ], 'TypeVersion' => [ 'shape' => 'UpdateConfigurationDefinitionInputTypeVersionString', ], ], ], 'UpdateConfigurationDefinitionInputIdString' => [ 'type' => 'string', 'pattern' => '^[a-z0-9-]{1,20}$', ], 'UpdateConfigurationDefinitionInputLocalDeploymentExecutionRoleNameString' => [ 'type' => 'string', 'pattern' => '^[\\w+=,.@-]{1,64}$', ], 'UpdateConfigurationDefinitionInputManagerArnString' => [ 'type' => 'string', 'pattern' => '^arn:aws:ssm-quicksetup:([^:]+):(\\d{12}):configuration-manager/[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}$', ], 'UpdateConfigurationDefinitionInputTypeVersionString' => [ 'type' => 'string', 'pattern' => '^\\d{1,3}(\\.\\d{1,3})?$|^LATEST$', ], 'UpdateConfigurationManagerInput' => [ 'type' => 'structure', 'required' => [ 'ManagerArn', ], 'members' => [ 'Description' => [ 'shape' => 'UpdateConfigurationManagerInputDescriptionString', ], 'ManagerArn' => [ 'shape' => 'UpdateConfigurationManagerInputManagerArnString', 'location' => 'uri', 'locationName' => 'ManagerArn', ], 'Name' => [ 'shape' => 'UpdateConfigurationManagerInputNameString', ], ], ], 'UpdateConfigurationManagerInputDescriptionString' => [ 'type' => 'string', 'pattern' => '^.{0,512}$', ], 'UpdateConfigurationManagerInputManagerArnString' => [ 'type' => 'string', 'pattern' => '^arn:aws:ssm-quicksetup:([^:]+):(\\d{12}):configuration-manager/[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}$', ], 'UpdateConfigurationManagerInputNameString' => [ 'type' => 'string', 'pattern' => '^[ A-Za-z0-9._-]{0,120}$', ], 'UpdateServiceSettingsInput' => [ 'type' => 'structure', 'members' => [ 'ExplorerEnablingRoleArn' => [ 'shape' => 'IAMRoleArn', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], ],];
