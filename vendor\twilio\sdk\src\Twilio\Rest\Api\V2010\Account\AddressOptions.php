<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Api\V2010\Account;

use Twilio\Options;
use Twilio\Values;

abstract class AddressOptions
{
    /**
     * @param string $friendlyName A descriptive string that you create to describe the new address. It can be up to 64 characters long.
     * @param bool $emergencyEnabled Whether to enable emergency calling on the new address. Can be: `true` or `false`.
     * @param bool $autoCorrectAddress Whether we should automatically correct the address. Can be: `true` or `false` and the default is `true`. If empty or `true`, we will correct the address you provide if necessary. If `false`, we won't alter the address you provide.
     * @param string $streetSecondary The additional number and street address of the address.
     * @return CreateAddressOptions Options builder
     */
    public static function create(
        
        string $friendlyName = Values::NONE,
        bool $emergencyEnabled = Values::BOOL_NONE,
        bool $autoCorrectAddress = Values::BOOL_NONE,
        string $streetSecondary = Values::NONE

    ): CreateAddressOptions
    {
        return new CreateAddressOptions(
            $friendlyName,
            $emergencyEnabled,
            $autoCorrectAddress,
            $streetSecondary
        );
    }



    /**
     * @param string $customerName The `customer_name` of the Address resources to read.
     * @param string $friendlyName The string that identifies the Address resources to read.
     * @param bool $emergencyEnabled Whether the address can be associated to a number for emergency calling.
     * @param string $isoCountry The ISO country code of the Address resources to read.
     * @return ReadAddressOptions Options builder
     */
    public static function read(
        
        string $customerName = Values::NONE,
        string $friendlyName = Values::NONE,
        bool $emergencyEnabled = Values::BOOL_NONE,
        string $isoCountry = Values::NONE

    ): ReadAddressOptions
    {
        return new ReadAddressOptions(
            $customerName,
            $friendlyName,
            $emergencyEnabled,
            $isoCountry
        );
    }

    /**
     * @param string $friendlyName A descriptive string that you create to describe the new address. It can be up to 64 characters long for Regulatory Compliance addresses and 32 characters long for Emergency addresses.
     * @param string $customerName The name to associate with the address.
     * @param string $street The number and street address of the address.
     * @param string $city The city of the address.
     * @param string $region The state or region of the address.
     * @param string $postalCode The postal code of the address.
     * @param bool $emergencyEnabled Whether to enable emergency calling on the address. Can be: `true` or `false`.
     * @param bool $autoCorrectAddress Whether we should automatically correct the address. Can be: `true` or `false` and the default is `true`. If empty or `true`, we will correct the address you provide if necessary. If `false`, we won't alter the address you provide.
     * @param string $streetSecondary The additional number and street address of the address.
     * @return UpdateAddressOptions Options builder
     */
    public static function update(
        
        string $friendlyName = Values::NONE,
        string $customerName = Values::NONE,
        string $street = Values::NONE,
        string $city = Values::NONE,
        string $region = Values::NONE,
        string $postalCode = Values::NONE,
        bool $emergencyEnabled = Values::BOOL_NONE,
        bool $autoCorrectAddress = Values::BOOL_NONE,
        string $streetSecondary = Values::NONE

    ): UpdateAddressOptions
    {
        return new UpdateAddressOptions(
            $friendlyName,
            $customerName,
            $street,
            $city,
            $region,
            $postalCode,
            $emergencyEnabled,
            $autoCorrectAddress,
            $streetSecondary
        );
    }

}

class CreateAddressOptions extends Options
    {
    /**
     * @param string $friendlyName A descriptive string that you create to describe the new address. It can be up to 64 characters long.
     * @param bool $emergencyEnabled Whether to enable emergency calling on the new address. Can be: `true` or `false`.
     * @param bool $autoCorrectAddress Whether we should automatically correct the address. Can be: `true` or `false` and the default is `true`. If empty or `true`, we will correct the address you provide if necessary. If `false`, we won't alter the address you provide.
     * @param string $streetSecondary The additional number and street address of the address.
     */
    public function __construct(
        
        string $friendlyName = Values::NONE,
        bool $emergencyEnabled = Values::BOOL_NONE,
        bool $autoCorrectAddress = Values::BOOL_NONE,
        string $streetSecondary = Values::NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
        $this->options['emergencyEnabled'] = $emergencyEnabled;
        $this->options['autoCorrectAddress'] = $autoCorrectAddress;
        $this->options['streetSecondary'] = $streetSecondary;
    }

    /**
     * A descriptive string that you create to describe the new address. It can be up to 64 characters long.
     *
     * @param string $friendlyName A descriptive string that you create to describe the new address. It can be up to 64 characters long.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * Whether to enable emergency calling on the new address. Can be: `true` or `false`.
     *
     * @param bool $emergencyEnabled Whether to enable emergency calling on the new address. Can be: `true` or `false`.
     * @return $this Fluent Builder
     */
    public function setEmergencyEnabled(bool $emergencyEnabled): self
    {
        $this->options['emergencyEnabled'] = $emergencyEnabled;
        return $this;
    }

    /**
     * Whether we should automatically correct the address. Can be: `true` or `false` and the default is `true`. If empty or `true`, we will correct the address you provide if necessary. If `false`, we won't alter the address you provide.
     *
     * @param bool $autoCorrectAddress Whether we should automatically correct the address. Can be: `true` or `false` and the default is `true`. If empty or `true`, we will correct the address you provide if necessary. If `false`, we won't alter the address you provide.
     * @return $this Fluent Builder
     */
    public function setAutoCorrectAddress(bool $autoCorrectAddress): self
    {
        $this->options['autoCorrectAddress'] = $autoCorrectAddress;
        return $this;
    }

    /**
     * The additional number and street address of the address.
     *
     * @param string $streetSecondary The additional number and street address of the address.
     * @return $this Fluent Builder
     */
    public function setStreetSecondary(string $streetSecondary): self
    {
        $this->options['streetSecondary'] = $streetSecondary;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Api.V2010.CreateAddressOptions ' . $options . ']';
    }
}



class ReadAddressOptions extends Options
    {
    /**
     * @param string $customerName The `customer_name` of the Address resources to read.
     * @param string $friendlyName The string that identifies the Address resources to read.
     * @param bool $emergencyEnabled Whether the address can be associated to a number for emergency calling.
     * @param string $isoCountry The ISO country code of the Address resources to read.
     */
    public function __construct(
        
        string $customerName = Values::NONE,
        string $friendlyName = Values::NONE,
        bool $emergencyEnabled = Values::BOOL_NONE,
        string $isoCountry = Values::NONE

    ) {
        $this->options['customerName'] = $customerName;
        $this->options['friendlyName'] = $friendlyName;
        $this->options['emergencyEnabled'] = $emergencyEnabled;
        $this->options['isoCountry'] = $isoCountry;
    }

    /**
     * The `customer_name` of the Address resources to read.
     *
     * @param string $customerName The `customer_name` of the Address resources to read.
     * @return $this Fluent Builder
     */
    public function setCustomerName(string $customerName): self
    {
        $this->options['customerName'] = $customerName;
        return $this;
    }

    /**
     * The string that identifies the Address resources to read.
     *
     * @param string $friendlyName The string that identifies the Address resources to read.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * Whether the address can be associated to a number for emergency calling.
     *
     * @param bool $emergencyEnabled Whether the address can be associated to a number for emergency calling.
     * @return $this Fluent Builder
     */
    public function setEmergencyEnabled(bool $emergencyEnabled): self
    {
        $this->options['emergencyEnabled'] = $emergencyEnabled;
        return $this;
    }

    /**
     * The ISO country code of the Address resources to read.
     *
     * @param string $isoCountry The ISO country code of the Address resources to read.
     * @return $this Fluent Builder
     */
    public function setIsoCountry(string $isoCountry): self
    {
        $this->options['isoCountry'] = $isoCountry;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Api.V2010.ReadAddressOptions ' . $options . ']';
    }
}

class UpdateAddressOptions extends Options
    {
    /**
     * @param string $friendlyName A descriptive string that you create to describe the new address. It can be up to 64 characters long for Regulatory Compliance addresses and 32 characters long for Emergency addresses.
     * @param string $customerName The name to associate with the address.
     * @param string $street The number and street address of the address.
     * @param string $city The city of the address.
     * @param string $region The state or region of the address.
     * @param string $postalCode The postal code of the address.
     * @param bool $emergencyEnabled Whether to enable emergency calling on the address. Can be: `true` or `false`.
     * @param bool $autoCorrectAddress Whether we should automatically correct the address. Can be: `true` or `false` and the default is `true`. If empty or `true`, we will correct the address you provide if necessary. If `false`, we won't alter the address you provide.
     * @param string $streetSecondary The additional number and street address of the address.
     */
    public function __construct(
        
        string $friendlyName = Values::NONE,
        string $customerName = Values::NONE,
        string $street = Values::NONE,
        string $city = Values::NONE,
        string $region = Values::NONE,
        string $postalCode = Values::NONE,
        bool $emergencyEnabled = Values::BOOL_NONE,
        bool $autoCorrectAddress = Values::BOOL_NONE,
        string $streetSecondary = Values::NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
        $this->options['customerName'] = $customerName;
        $this->options['street'] = $street;
        $this->options['city'] = $city;
        $this->options['region'] = $region;
        $this->options['postalCode'] = $postalCode;
        $this->options['emergencyEnabled'] = $emergencyEnabled;
        $this->options['autoCorrectAddress'] = $autoCorrectAddress;
        $this->options['streetSecondary'] = $streetSecondary;
    }

    /**
     * A descriptive string that you create to describe the new address. It can be up to 64 characters long for Regulatory Compliance addresses and 32 characters long for Emergency addresses.
     *
     * @param string $friendlyName A descriptive string that you create to describe the new address. It can be up to 64 characters long for Regulatory Compliance addresses and 32 characters long for Emergency addresses.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * The name to associate with the address.
     *
     * @param string $customerName The name to associate with the address.
     * @return $this Fluent Builder
     */
    public function setCustomerName(string $customerName): self
    {
        $this->options['customerName'] = $customerName;
        return $this;
    }

    /**
     * The number and street address of the address.
     *
     * @param string $street The number and street address of the address.
     * @return $this Fluent Builder
     */
    public function setStreet(string $street): self
    {
        $this->options['street'] = $street;
        return $this;
    }

    /**
     * The city of the address.
     *
     * @param string $city The city of the address.
     * @return $this Fluent Builder
     */
    public function setCity(string $city): self
    {
        $this->options['city'] = $city;
        return $this;
    }

    /**
     * The state or region of the address.
     *
     * @param string $region The state or region of the address.
     * @return $this Fluent Builder
     */
    public function setRegion(string $region): self
    {
        $this->options['region'] = $region;
        return $this;
    }

    /**
     * The postal code of the address.
     *
     * @param string $postalCode The postal code of the address.
     * @return $this Fluent Builder
     */
    public function setPostalCode(string $postalCode): self
    {
        $this->options['postalCode'] = $postalCode;
        return $this;
    }

    /**
     * Whether to enable emergency calling on the address. Can be: `true` or `false`.
     *
     * @param bool $emergencyEnabled Whether to enable emergency calling on the address. Can be: `true` or `false`.
     * @return $this Fluent Builder
     */
    public function setEmergencyEnabled(bool $emergencyEnabled): self
    {
        $this->options['emergencyEnabled'] = $emergencyEnabled;
        return $this;
    }

    /**
     * Whether we should automatically correct the address. Can be: `true` or `false` and the default is `true`. If empty or `true`, we will correct the address you provide if necessary. If `false`, we won't alter the address you provide.
     *
     * @param bool $autoCorrectAddress Whether we should automatically correct the address. Can be: `true` or `false` and the default is `true`. If empty or `true`, we will correct the address you provide if necessary. If `false`, we won't alter the address you provide.
     * @return $this Fluent Builder
     */
    public function setAutoCorrectAddress(bool $autoCorrectAddress): self
    {
        $this->options['autoCorrectAddress'] = $autoCorrectAddress;
        return $this;
    }

    /**
     * The additional number and street address of the address.
     *
     * @param string $streetSecondary The additional number and street address of the address.
     * @return $this Fluent Builder
     */
    public function setStreetSecondary(string $streetSecondary): self
    {
        $this->options['streetSecondary'] = $streetSecondary;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Api.V2010.UpdateAddressOptions ' . $options . ']';
    }
}

