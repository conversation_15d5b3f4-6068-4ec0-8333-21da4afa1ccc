<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Events
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Events;

use Twilio\Domain;
use Twilio\Exceptions\TwilioException;
use Twilio\InstanceContext;
use Twilio\Rest\Events\V1\EventTypeList;
use Twilio\Rest\Events\V1\SchemaList;
use Twilio\Rest\Events\V1\SinkList;
use Twilio\Rest\Events\V1\SubscriptionList;
use Twilio\Version;

/**
 * @property EventTypeList $eventTypes
 * @property SchemaList $schemas
 * @property SinkList $sinks
 * @property SubscriptionList $subscriptions
 * @method \Twilio\Rest\Events\V1\EventTypeContext eventTypes(string $type)
 * @method \Twilio\Rest\Events\V1\SchemaContext schemas(string $id)
 * @method \Twilio\Rest\Events\V1\SinkContext sinks(string $sid)
 * @method \Twilio\Rest\Events\V1\SubscriptionContext subscriptions(string $sid)
 */
class V1 extends Version
{
    protected $_eventTypes;
    protected $_schemas;
    protected $_sinks;
    protected $_subscriptions;

    /**
     * Construct the V1 version of Events
     *
     * @param Domain $domain Domain that contains the version
     */
    public function __construct(Domain $domain)
    {
        parent::__construct($domain);
        $this->version = 'v1';
    }

    protected function getEventTypes(): EventTypeList
    {
        if (!$this->_eventTypes) {
            $this->_eventTypes = new EventTypeList($this);
        }
        return $this->_eventTypes;
    }

    protected function getSchemas(): SchemaList
    {
        if (!$this->_schemas) {
            $this->_schemas = new SchemaList($this);
        }
        return $this->_schemas;
    }

    protected function getSinks(): SinkList
    {
        if (!$this->_sinks) {
            $this->_sinks = new SinkList($this);
        }
        return $this->_sinks;
    }

    protected function getSubscriptions(): SubscriptionList
    {
        if (!$this->_subscriptions) {
            $this->_subscriptions = new SubscriptionList($this);
        }
        return $this->_subscriptions;
    }

    /**
     * Magic getter to lazy load root resources
     *
     * @param string $name Resource to return
     * @return \Twilio\ListResource The requested resource
     * @throws TwilioException For unknown resource
     */
    public function __get(string $name)
    {
        $method = 'get' . \ucfirst($name);
        if (\method_exists($this, $method)) {
            return $this->$method();
        }

        throw new TwilioException('Unknown resource ' . $name);
    }

    /**
     * Magic caller to get resource contexts
     *
     * @param string $name Resource to return
     * @param array $arguments Context parameters
     * @return InstanceContext The requested resource context
     * @throws TwilioException For unknown resource
     */
    public function __call(string $name, array $arguments): InstanceContext
    {
        $property = $this->$name;
        if (\method_exists($property, 'getContext')) {
            return \call_user_func_array(array($property, 'getContext'), $arguments);
        }

        throw new TwilioException('Resource does not have a context');
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Events.V1]';
    }
}
