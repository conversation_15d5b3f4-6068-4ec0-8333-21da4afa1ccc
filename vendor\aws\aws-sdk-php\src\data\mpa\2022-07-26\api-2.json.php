<?php
// This file was auto-generated from sdk-root/src/data/mpa/2022-07-26/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2022-07-26', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'mpa', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS Multi-party Approval', 'serviceId' => 'MPA', 'signatureVersion' => 'v4', 'signingName' => 'mpa', 'uid' => 'mpa-2022-07-26', ], 'operations' => [ 'CancelSession' => [ 'name' => 'CancelSession', 'http' => [ 'method' => 'PUT', 'requestUri' => '/sessions/{SessionArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelSessionRequest', ], 'output' => [ 'shape' => 'CancelSessionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'CreateApprovalTeam' => [ 'name' => 'CreateApprovalTeam', 'http' => [ 'method' => 'POST', 'requestUri' => '/approval-teams', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateApprovalTeamRequest', ], 'output' => [ 'shape' => 'CreateApprovalTeamResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'CreateIdentitySource' => [ 'name' => 'CreateIdentitySource', 'http' => [ 'method' => 'POST', 'requestUri' => '/identity-sources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateIdentitySourceRequest', ], 'output' => [ 'shape' => 'CreateIdentitySourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteIdentitySource' => [ 'name' => 'DeleteIdentitySource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/identity-sources/{IdentitySourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteIdentitySourceRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteInactiveApprovalTeamVersion' => [ 'name' => 'DeleteInactiveApprovalTeamVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/approval-teams/{Arn}/{VersionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteInactiveApprovalTeamVersionRequest', ], 'output' => [ 'shape' => 'DeleteInactiveApprovalTeamVersionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'GetApprovalTeam' => [ 'name' => 'GetApprovalTeam', 'http' => [ 'method' => 'GET', 'requestUri' => '/approval-teams/{Arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetApprovalTeamRequest', ], 'output' => [ 'shape' => 'GetApprovalTeamResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetIdentitySource' => [ 'name' => 'GetIdentitySource', 'http' => [ 'method' => 'GET', 'requestUri' => '/identity-sources/{IdentitySourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIdentitySourceRequest', ], 'output' => [ 'shape' => 'GetIdentitySourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetPolicyVersion' => [ 'name' => 'GetPolicyVersion', 'http' => [ 'method' => 'GET', 'requestUri' => '/policy-versions/{PolicyVersionArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPolicyVersionRequest', ], 'output' => [ 'shape' => 'GetPolicyVersionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetResourcePolicy' => [ 'name' => 'GetResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetResourcePolicy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetResourcePolicyRequest', ], 'output' => [ 'shape' => 'GetResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetSession' => [ 'name' => 'GetSession', 'http' => [ 'method' => 'GET', 'requestUri' => '/sessions/{SessionArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSessionRequest', ], 'output' => [ 'shape' => 'GetSessionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListApprovalTeams' => [ 'name' => 'ListApprovalTeams', 'http' => [ 'method' => 'POST', 'requestUri' => '/approval-teams/?List', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListApprovalTeamsRequest', ], 'output' => [ 'shape' => 'ListApprovalTeamsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListIdentitySources' => [ 'name' => 'ListIdentitySources', 'http' => [ 'method' => 'POST', 'requestUri' => '/identity-sources/?List', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIdentitySourcesRequest', ], 'output' => [ 'shape' => 'ListIdentitySourcesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListPolicies' => [ 'name' => 'ListPolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/policies/?List', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPoliciesRequest', ], 'output' => [ 'shape' => 'ListPoliciesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListPolicyVersions' => [ 'name' => 'ListPolicyVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/policies/{PolicyArn}/?List', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPolicyVersionsRequest', ], 'output' => [ 'shape' => 'ListPolicyVersionsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListResourcePolicies' => [ 'name' => 'ListResourcePolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/resource-policies/{ResourceArn}/?List', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListResourcePoliciesRequest', ], 'output' => [ 'shape' => 'ListResourcePoliciesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListSessions' => [ 'name' => 'ListSessions', 'http' => [ 'method' => 'POST', 'requestUri' => '/approval-teams/{ApprovalTeamArn}/sessions/?List', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSessionsRequest', ], 'output' => [ 'shape' => 'ListSessionsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartActiveApprovalTeamDeletion' => [ 'name' => 'StartActiveApprovalTeamDeletion', 'http' => [ 'method' => 'POST', 'requestUri' => '/approval-teams/{Arn}?Delete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartActiveApprovalTeamDeletionRequest', ], 'output' => [ 'shape' => 'StartActiveApprovalTeamDeletionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateApprovalTeam' => [ 'name' => 'UpdateApprovalTeam', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/approval-teams/{Arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateApprovalTeamRequest', ], 'output' => [ 'shape' => 'UpdateApprovalTeamResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 0, 'pattern' => '\\d{12}', ], 'ActionCompletionStrategy' => [ 'type' => 'string', 'enum' => [ 'AUTO_COMPLETION_UPON_APPROVAL', ], ], 'ActionName' => [ 'type' => 'string', 'max' => 500, 'min' => 0, ], 'ApprovalStrategy' => [ 'type' => 'structure', 'members' => [ 'MofN' => [ 'shape' => 'MofNApprovalStrategy', ], ], 'union' => true, ], 'ApprovalStrategyResponse' => [ 'type' => 'structure', 'members' => [ 'MofN' => [ 'shape' => 'MofNApprovalStrategy', ], ], 'union' => true, ], 'ApprovalTeamArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws(-[^:]+)?:mpa:[a-z0-9-]{1,20}:[0-9]{12}:approval-team/[a-zA-Z0-9._-]+', ], 'ApprovalTeamName' => [ 'type' => 'string', 'max' => 64, 'min' => 0, 'pattern' => '[a-zA-Z0-9._-]+', ], 'ApprovalTeamRequestApprover' => [ 'type' => 'structure', 'required' => [ 'PrimaryIdentityId', 'PrimaryIdentitySourceArn', ], 'members' => [ 'PrimaryIdentityId' => [ 'shape' => 'IdentityId', ], 'PrimaryIdentitySourceArn' => [ 'shape' => 'String', ], ], ], 'ApprovalTeamRequestApprovers' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApprovalTeamRequestApprover', ], 'max' => 20, 'min' => 1, ], 'ApprovalTeamStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', 'DELETING', 'PENDING', ], ], 'ApprovalTeamStatusCode' => [ 'type' => 'string', 'enum' => [ 'VALIDATING', 'PENDING_ACTIVATION', 'FAILED_VALIDATION', 'FAILED_ACTIVATION', 'UPDATE_PENDING_APPROVAL', 'UPDATE_PENDING_ACTIVATION', 'UPDATE_FAILED_APPROVAL', 'UPDATE_FAILED_ACTIVATION', 'UPDATE_FAILED_VALIDATION', 'DELETE_PENDING_APPROVAL', 'DELETE_FAILED_APPROVAL', 'DELETE_FAILED_VALIDATION', ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'CancelSessionRequest' => [ 'type' => 'structure', 'required' => [ 'SessionArn', ], 'members' => [ 'SessionArn' => [ 'shape' => 'SessionArn', 'location' => 'uri', 'locationName' => 'SessionArn', ], ], ], 'CancelSessionResponse' => [ 'type' => 'structure', 'members' => [], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateApprovalTeamRequest' => [ 'type' => 'structure', 'required' => [ 'ApprovalStrategy', 'Approvers', 'Description', 'Policies', 'Name', ], 'members' => [ 'ClientToken' => [ 'shape' => 'Token', 'idempotencyToken' => true, ], 'ApprovalStrategy' => [ 'shape' => 'ApprovalStrategy', ], 'Approvers' => [ 'shape' => 'ApprovalTeamRequestApprovers', ], 'Description' => [ 'shape' => 'Description', ], 'Policies' => [ 'shape' => 'PoliciesReferences', ], 'Name' => [ 'shape' => 'ApprovalTeamName', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateApprovalTeamResponse' => [ 'type' => 'structure', 'members' => [ 'CreationTime' => [ 'shape' => 'IsoTimestamp', ], 'Arn' => [ 'shape' => 'ApprovalTeamArn', ], 'Name' => [ 'shape' => 'String', ], 'VersionId' => [ 'shape' => 'String', ], ], ], 'CreateIdentitySourceRequest' => [ 'type' => 'structure', 'required' => [ 'IdentitySourceParameters', ], 'members' => [ 'IdentitySourceParameters' => [ 'shape' => 'IdentitySourceParameters', ], 'ClientToken' => [ 'shape' => 'Token', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateIdentitySourceResponse' => [ 'type' => 'structure', 'members' => [ 'IdentitySourceType' => [ 'shape' => 'IdentitySourceType', ], 'IdentitySourceArn' => [ 'shape' => 'String', ], 'CreationTime' => [ 'shape' => 'IsoTimestamp', ], ], ], 'DeleteIdentitySourceRequest' => [ 'type' => 'structure', 'required' => [ 'IdentitySourceArn', ], 'members' => [ 'IdentitySourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'IdentitySourceArn', ], ], ], 'DeleteInactiveApprovalTeamVersionRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', 'VersionId', ], 'members' => [ 'Arn' => [ 'shape' => 'ApprovalTeamArn', 'location' => 'uri', 'locationName' => 'Arn', ], 'VersionId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'VersionId', ], ], ], 'DeleteInactiveApprovalTeamVersionResponse' => [ 'type' => 'structure', 'members' => [], ], 'Description' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'sensitive' => true, ], 'Filter' => [ 'type' => 'structure', 'members' => [ 'FieldName' => [ 'shape' => 'FilterField', ], 'Operator' => [ 'shape' => 'Operator', ], 'Value' => [ 'shape' => 'String', ], ], ], 'FilterField' => [ 'type' => 'string', 'enum' => [ 'ActionName', 'ApprovalTeamName', 'VotingTime', 'Vote', 'SessionStatus', 'InitiationTime', ], ], 'Filters' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], 'max' => 10, 'min' => 0, ], 'GetApprovalTeamRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'ApprovalTeamArn', 'location' => 'uri', 'locationName' => 'Arn', ], ], ], 'GetApprovalTeamResponse' => [ 'type' => 'structure', 'members' => [ 'CreationTime' => [ 'shape' => 'IsoTimestamp', ], 'ApprovalStrategy' => [ 'shape' => 'ApprovalStrategyResponse', ], 'NumberOfApprovers' => [ 'shape' => 'Integer', ], 'Approvers' => [ 'shape' => 'GetApprovalTeamResponseApprovers', ], 'Arn' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'Description', ], 'Name' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'ApprovalTeamStatus', ], 'StatusCode' => [ 'shape' => 'ApprovalTeamStatusCode', ], 'StatusMessage' => [ 'shape' => 'Message', ], 'UpdateSessionArn' => [ 'shape' => 'String', ], 'VersionId' => [ 'shape' => 'String', ], 'Policies' => [ 'shape' => 'PoliciesReferences', ], 'LastUpdateTime' => [ 'shape' => 'IsoTimestamp', ], 'PendingUpdate' => [ 'shape' => 'PendingUpdate', ], ], ], 'GetApprovalTeamResponseApprover' => [ 'type' => 'structure', 'members' => [ 'ApproverId' => [ 'shape' => 'ParticipantId', ], 'ResponseTime' => [ 'shape' => 'IsoTimestamp', ], 'PrimaryIdentityId' => [ 'shape' => 'IdentityId', ], 'PrimaryIdentitySourceArn' => [ 'shape' => 'String', ], 'PrimaryIdentityStatus' => [ 'shape' => 'IdentityStatus', ], ], ], 'GetApprovalTeamResponseApprovers' => [ 'type' => 'list', 'member' => [ 'shape' => 'GetApprovalTeamResponseApprover', ], 'max' => 20, 'min' => 0, ], 'GetIdentitySourceRequest' => [ 'type' => 'structure', 'required' => [ 'IdentitySourceArn', ], 'members' => [ 'IdentitySourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'IdentitySourceArn', ], ], ], 'GetIdentitySourceResponse' => [ 'type' => 'structure', 'members' => [ 'IdentitySourceType' => [ 'shape' => 'IdentitySourceType', ], 'IdentitySourceParameters' => [ 'shape' => 'IdentitySourceParametersForGet', ], 'IdentitySourceArn' => [ 'shape' => 'String', ], 'CreationTime' => [ 'shape' => 'IsoTimestamp', ], 'Status' => [ 'shape' => 'IdentitySourceStatus', ], 'StatusCode' => [ 'shape' => 'IdentitySourceStatusCode', ], 'StatusMessage' => [ 'shape' => 'String', ], ], ], 'GetPolicyVersionRequest' => [ 'type' => 'structure', 'required' => [ 'PolicyVersionArn', ], 'members' => [ 'PolicyVersionArn' => [ 'shape' => 'QualifiedPolicyArn', 'location' => 'uri', 'locationName' => 'PolicyVersionArn', ], ], ], 'GetPolicyVersionResponse' => [ 'type' => 'structure', 'required' => [ 'PolicyVersion', ], 'members' => [ 'PolicyVersion' => [ 'shape' => 'PolicyVersion', ], ], ], 'GetResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'PolicyName', 'PolicyType', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], 'PolicyName' => [ 'shape' => 'String', ], 'PolicyType' => [ 'shape' => 'PolicyType', ], ], ], 'GetResourcePolicyResponse' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'PolicyType', 'PolicyName', 'PolicyDocument', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], 'PolicyType' => [ 'shape' => 'PolicyType', ], 'PolicyVersionArn' => [ 'shape' => 'String', ], 'PolicyName' => [ 'shape' => 'PolicyName', ], 'PolicyDocument' => [ 'shape' => 'PolicyDocument', ], ], ], 'GetSessionRequest' => [ 'type' => 'structure', 'required' => [ 'SessionArn', ], 'members' => [ 'SessionArn' => [ 'shape' => 'SessionArn', 'location' => 'uri', 'locationName' => 'SessionArn', ], ], ], 'GetSessionResponse' => [ 'type' => 'structure', 'members' => [ 'SessionArn' => [ 'shape' => 'SessionArn', ], 'ApprovalTeamArn' => [ 'shape' => 'ApprovalTeamArn', ], 'ApprovalTeamName' => [ 'shape' => 'ApprovalTeamName', ], 'ProtectedResourceArn' => [ 'shape' => 'String', ], 'ApprovalStrategy' => [ 'shape' => 'ApprovalStrategyResponse', ], 'NumberOfApprovers' => [ 'shape' => 'Integer', ], 'InitiationTime' => [ 'shape' => 'IsoTimestamp', ], 'ExpirationTime' => [ 'shape' => 'IsoTimestamp', ], 'CompletionTime' => [ 'shape' => 'IsoTimestamp', ], 'Description' => [ 'shape' => 'Description', ], 'Metadata' => [ 'shape' => 'SessionMetadata', ], 'Status' => [ 'shape' => 'SessionStatus', ], 'StatusCode' => [ 'shape' => 'SessionStatusCode', ], 'StatusMessage' => [ 'shape' => 'Message', ], 'ExecutionStatus' => [ 'shape' => 'SessionExecutionStatus', ], 'ActionName' => [ 'shape' => 'ActionName', ], 'RequesterServicePrincipal' => [ 'shape' => 'ServicePrincipal', ], 'RequesterPrincipalArn' => [ 'shape' => 'String', ], 'RequesterAccountId' => [ 'shape' => 'AccountId', ], 'RequesterRegion' => [ 'shape' => 'Region', ], 'RequesterComment' => [ 'shape' => 'RequesterComment', ], 'ActionCompletionStrategy' => [ 'shape' => 'ActionCompletionStrategy', ], 'ApproverResponses' => [ 'shape' => 'GetSessionResponseApproverResponses', ], ], ], 'GetSessionResponseApproverResponse' => [ 'type' => 'structure', 'members' => [ 'ApproverId' => [ 'shape' => 'ParticipantId', ], 'IdentitySourceArn' => [ 'shape' => 'String', ], 'IdentityId' => [ 'shape' => 'IdentityId', ], 'Response' => [ 'shape' => 'SessionResponse', ], 'ResponseTime' => [ 'shape' => 'IsoTimestamp', ], ], ], 'GetSessionResponseApproverResponses' => [ 'type' => 'list', 'member' => [ 'shape' => 'GetSessionResponseApproverResponse', ], 'max' => 20, 'min' => 0, ], 'IamIdentityCenter' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'Region', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'IdcInstanceArn', ], 'Region' => [ 'shape' => 'String', ], ], ], 'IamIdentityCenterForGet' => [ 'type' => 'structure', 'members' => [ 'InstanceArn' => [ 'shape' => 'String', ], 'ApprovalPortalUrl' => [ 'shape' => 'String', ], 'Region' => [ 'shape' => 'String', ], ], ], 'IamIdentityCenterForList' => [ 'type' => 'structure', 'members' => [ 'InstanceArn' => [ 'shape' => 'String', ], 'ApprovalPortalUrl' => [ 'shape' => 'String', ], 'Region' => [ 'shape' => 'String', ], ], ], 'IdcInstanceArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:.+:sso:::instance/(?:sso)?ins-[a-zA-Z0-9-.]{16}', ], 'IdentityId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'IdentitySourceForList' => [ 'type' => 'structure', 'members' => [ 'IdentitySourceType' => [ 'shape' => 'IdentitySourceType', ], 'IdentitySourceParameters' => [ 'shape' => 'IdentitySourceParametersForList', ], 'IdentitySourceArn' => [ 'shape' => 'String', ], 'CreationTime' => [ 'shape' => 'IsoTimestamp', ], 'Status' => [ 'shape' => 'IdentitySourceStatus', ], 'StatusCode' => [ 'shape' => 'IdentitySourceStatusCode', ], 'StatusMessage' => [ 'shape' => 'String', ], ], ], 'IdentitySourceParameters' => [ 'type' => 'structure', 'members' => [ 'IamIdentityCenter' => [ 'shape' => 'IamIdentityCenter', ], ], ], 'IdentitySourceParametersForGet' => [ 'type' => 'structure', 'members' => [ 'IamIdentityCenter' => [ 'shape' => 'IamIdentityCenterForGet', ], ], 'union' => true, ], 'IdentitySourceParametersForList' => [ 'type' => 'structure', 'members' => [ 'IamIdentityCenter' => [ 'shape' => 'IamIdentityCenterForList', ], ], 'union' => true, ], 'IdentitySourceStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'ERROR', ], ], 'IdentitySourceStatusCode' => [ 'type' => 'string', 'enum' => [ 'ACCESS_DENIED', 'DELETION_FAILED', 'IDC_INSTANCE_NOT_FOUND', 'IDC_INSTANCE_NOT_VALID', ], ], 'IdentitySourceType' => [ 'type' => 'string', 'enum' => [ 'IAM_IDENTITY_CENTER', ], ], 'IdentitySources' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdentitySourceForList', ], 'max' => 20, 'min' => 0, ], 'IdentityStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'ACCEPTED', 'REJECTED', 'INVALID', ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'InvalidParameterException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'IsoTimestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'ListApprovalTeamsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListApprovalTeamsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'ApprovalTeams' => [ 'shape' => 'ListApprovalTeamsResponseApprovalTeams', ], ], ], 'ListApprovalTeamsResponseApprovalTeam' => [ 'type' => 'structure', 'members' => [ 'CreationTime' => [ 'shape' => 'IsoTimestamp', ], 'ApprovalStrategy' => [ 'shape' => 'ApprovalStrategyResponse', ], 'NumberOfApprovers' => [ 'shape' => 'Integer', ], 'Arn' => [ 'shape' => 'ApprovalTeamArn', ], 'Name' => [ 'shape' => 'ApprovalTeamName', ], 'Description' => [ 'shape' => 'Description', ], 'Status' => [ 'shape' => 'ApprovalTeamStatus', ], 'StatusCode' => [ 'shape' => 'ApprovalTeamStatusCode', ], 'StatusMessage' => [ 'shape' => 'Message', ], ], ], 'ListApprovalTeamsResponseApprovalTeams' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListApprovalTeamsResponseApprovalTeam', ], 'max' => 20, 'min' => 0, ], 'ListIdentitySourcesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListIdentitySourcesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'IdentitySources' => [ 'shape' => 'IdentitySources', ], ], ], 'ListPoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListPoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'Policies' => [ 'shape' => 'Policies', ], ], ], 'ListPolicyVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'PolicyArn', ], 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'PolicyArn' => [ 'shape' => 'UnqualifiedPolicyArn', 'location' => 'uri', 'locationName' => 'PolicyArn', ], ], ], 'ListPolicyVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'PolicyVersions' => [ 'shape' => 'PolicyVersions', ], ], ], 'ListResourcePoliciesRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListResourcePoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'ResourcePolicies' => [ 'shape' => 'ListResourcePoliciesResponseResourcePolicies', ], ], ], 'ListResourcePoliciesResponseResourcePolicies' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListResourcePoliciesResponseResourcePolicy', ], 'max' => 100, 'min' => 0, ], 'ListResourcePoliciesResponseResourcePolicy' => [ 'type' => 'structure', 'members' => [ 'PolicyArn' => [ 'shape' => 'String', ], 'PolicyType' => [ 'shape' => 'PolicyType', ], 'PolicyName' => [ 'shape' => 'String', ], ], ], 'ListSessionsRequest' => [ 'type' => 'structure', 'required' => [ 'ApprovalTeamArn', ], 'members' => [ 'ApprovalTeamArn' => [ 'shape' => 'ApprovalTeamArn', 'location' => 'uri', 'locationName' => 'ApprovalTeamArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], 'Filters' => [ 'shape' => 'Filters', ], ], ], 'ListSessionsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'Sessions' => [ 'shape' => 'ListSessionsResponseSessions', ], ], ], 'ListSessionsResponseSession' => [ 'type' => 'structure', 'members' => [ 'SessionArn' => [ 'shape' => 'SessionArn', ], 'ApprovalTeamName' => [ 'shape' => 'ApprovalTeamName', ], 'ApprovalTeamArn' => [ 'shape' => 'ApprovalTeamArn', ], 'InitiationTime' => [ 'shape' => 'IsoTimestamp', ], 'ExpirationTime' => [ 'shape' => 'IsoTimestamp', ], 'CompletionTime' => [ 'shape' => 'IsoTimestamp', ], 'Description' => [ 'shape' => 'Description', ], 'ActionName' => [ 'shape' => 'ActionName', ], 'ProtectedResourceArn' => [ 'shape' => 'String', ], 'RequesterServicePrincipal' => [ 'shape' => 'ServicePrincipal', ], 'RequesterPrincipalArn' => [ 'shape' => 'String', ], 'RequesterRegion' => [ 'shape' => 'Region', ], 'RequesterAccountId' => [ 'shape' => 'AccountId', ], 'Status' => [ 'shape' => 'SessionStatus', ], 'StatusCode' => [ 'shape' => 'SessionStatusCode', ], 'StatusMessage' => [ 'shape' => 'Message', ], 'ActionCompletionStrategy' => [ 'shape' => 'ActionCompletionStrategy', ], ], ], 'ListSessionsResponseSessions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListSessionsResponseSession', ], 'max' => 20, 'min' => 0, ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'Tags', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 20, 'min' => 1, ], 'Message' => [ 'type' => 'string', 'max' => 500, 'min' => 0, ], 'MofNApprovalStrategy' => [ 'type' => 'structure', 'required' => [ 'MinApprovalsRequired', ], 'members' => [ 'MinApprovalsRequired' => [ 'shape' => 'MofNApprovalStrategyMinApprovalsRequiredInteger', ], ], ], 'MofNApprovalStrategyMinApprovalsRequiredInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'Operator' => [ 'type' => 'string', 'enum' => [ 'EQ', 'NE', 'GT', 'LT', 'GTE', 'LTE', 'CONTAINS', 'NOT_CONTAINS', 'BETWEEN', ], ], 'ParticipantId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'PendingUpdate' => [ 'type' => 'structure', 'members' => [ 'VersionId' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'ApprovalStrategy' => [ 'shape' => 'ApprovalStrategyResponse', ], 'NumberOfApprovers' => [ 'shape' => 'Integer', ], 'Status' => [ 'shape' => 'ApprovalTeamStatus', ], 'StatusCode' => [ 'shape' => 'ApprovalTeamStatusCode', ], 'StatusMessage' => [ 'shape' => 'Message', ], 'Approvers' => [ 'shape' => 'GetApprovalTeamResponseApprovers', ], 'UpdateInitiationTime' => [ 'shape' => 'IsoTimestamp', ], ], ], 'Policies' => [ 'type' => 'list', 'member' => [ 'shape' => 'Policy', ], 'max' => 20, 'min' => 0, ], 'PoliciesReferences' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolicyReference', ], 'max' => 10, 'min' => 1, ], 'Policy' => [ 'type' => 'structure', 'required' => [ 'Arn', 'DefaultVersion', 'PolicyType', 'Name', ], 'members' => [ 'Arn' => [ 'shape' => 'UnqualifiedPolicyArn', ], 'DefaultVersion' => [ 'shape' => 'PolicyVersionId', ], 'PolicyType' => [ 'shape' => 'PolicyType', ], 'Name' => [ 'shape' => 'PolicyName', ], ], ], 'PolicyDocument' => [ 'type' => 'string', 'max' => 400000, 'min' => 0, 'sensitive' => true, ], 'PolicyName' => [ 'type' => 'string', 'max' => 64, 'min' => 0, ], 'PolicyReference' => [ 'type' => 'structure', 'required' => [ 'PolicyArn', ], 'members' => [ 'PolicyArn' => [ 'shape' => 'QualifiedPolicyArn', ], ], ], 'PolicyStatus' => [ 'type' => 'string', 'enum' => [ 'ATTACHABLE', 'DEPRECATED', ], ], 'PolicyType' => [ 'type' => 'string', 'enum' => [ 'AWS_MANAGED', 'AWS_RAM', ], ], 'PolicyVersion' => [ 'type' => 'structure', 'required' => [ 'Arn', 'PolicyArn', 'VersionId', 'PolicyType', 'IsDefault', 'Name', 'Status', 'CreationTime', 'LastUpdatedTime', 'Document', ], 'members' => [ 'Arn' => [ 'shape' => 'QualifiedPolicyArn', ], 'PolicyArn' => [ 'shape' => 'UnqualifiedPolicyArn', ], 'VersionId' => [ 'shape' => 'PolicyVersionId', ], 'PolicyType' => [ 'shape' => 'PolicyType', ], 'IsDefault' => [ 'shape' => 'Boolean', ], 'Name' => [ 'shape' => 'PolicyName', ], 'Status' => [ 'shape' => 'PolicyStatus', ], 'CreationTime' => [ 'shape' => 'IsoTimestamp', ], 'LastUpdatedTime' => [ 'shape' => 'IsoTimestamp', ], 'Document' => [ 'shape' => 'PolicyDocument', ], ], ], 'PolicyVersionId' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'PolicyVersionSummary' => [ 'type' => 'structure', 'required' => [ 'Arn', 'PolicyArn', 'VersionId', 'PolicyType', 'IsDefault', 'Name', 'Status', 'CreationTime', 'LastUpdatedTime', ], 'members' => [ 'Arn' => [ 'shape' => 'QualifiedPolicyArn', ], 'PolicyArn' => [ 'shape' => 'UnqualifiedPolicyArn', ], 'VersionId' => [ 'shape' => 'PolicyVersionId', ], 'PolicyType' => [ 'shape' => 'PolicyType', ], 'IsDefault' => [ 'shape' => 'Boolean', ], 'Name' => [ 'shape' => 'PolicyName', ], 'Status' => [ 'shape' => 'PolicyStatus', ], 'CreationTime' => [ 'shape' => 'IsoTimestamp', ], 'LastUpdatedTime' => [ 'shape' => 'IsoTimestamp', ], ], ], 'PolicyVersions' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolicyVersionSummary', ], 'max' => 20, 'min' => 0, ], 'QualifiedPolicyArn' => [ 'type' => 'string', 'max' => 1224, 'min' => 0, 'pattern' => 'arn:.{1,63}:mpa:::aws:policy/[a-zA-Z0-9_\\.-]{1,1023}/[a-zA-Z0-9_\\.-]{1,1023}/(?:[\\d]+|\\$DEFAULT)', ], 'Region' => [ 'type' => 'string', 'max' => 100, 'min' => 0, ], 'RequesterComment' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'sensitive' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ServicePrincipal' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SessionArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws(-[^:]+)?:mpa:[a-z0-9-]{1,20}:[0-9]{12}:session/[a-zA-Z0-9._-]+/[a-zA-Z0-9_-]+', ], 'SessionExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'EXECUTED', 'FAILED', 'PENDING', ], ], 'SessionKey' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\p{P}]*', 'sensitive' => true, ], 'SessionMetadata' => [ 'type' => 'map', 'key' => [ 'shape' => 'SessionKey', ], 'value' => [ 'shape' => 'SessionValue', ], 'sensitive' => true, ], 'SessionResponse' => [ 'type' => 'string', 'enum' => [ 'APPROVED', 'REJECTED', 'NO_RESPONSE', ], ], 'SessionStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'CANCELLED', 'APPROVED', 'FAILED', 'CREATING', ], ], 'SessionStatusCode' => [ 'type' => 'string', 'enum' => [ 'REJECTED', 'EXPIRED', 'CONFIGURATION_CHANGED', ], ], 'SessionValue' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\p{P}]*', 'sensitive' => true, ], 'StartActiveApprovalTeamDeletionRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'PendingWindowDays' => [ 'shape' => 'Integer', ], 'Arn' => [ 'shape' => 'ApprovalTeamArn', 'location' => 'uri', 'locationName' => 'Arn', ], ], ], 'StartActiveApprovalTeamDeletionResponse' => [ 'type' => 'structure', 'members' => [ 'DeletionCompletionTime' => [ 'shape' => 'IsoTimestamp', ], 'DeletionStartTime' => [ 'shape' => 'IsoTimestamp', ], ], ], 'String' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'sensitive' => true, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, 'sensitive' => true, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'sensitive' => true, ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'sensitive' => true, ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Token' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, ], 'TooManyTagsException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceName' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'UnqualifiedPolicyArn' => [ 'type' => 'string', 'max' => 1224, 'min' => 0, 'pattern' => 'arn:.{1,63}:mpa:::aws:policy/[a-zA-Z0-9_\\.-]{1,1023}/[a-zA-Z0-9_\\.-]{1,1023}', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateApprovalTeamRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'ApprovalStrategy' => [ 'shape' => 'ApprovalStrategy', ], 'Approvers' => [ 'shape' => 'ApprovalTeamRequestApprovers', ], 'Description' => [ 'shape' => 'Description', ], 'Arn' => [ 'shape' => 'ApprovalTeamArn', 'location' => 'uri', 'locationName' => 'Arn', ], ], ], 'UpdateApprovalTeamResponse' => [ 'type' => 'structure', 'members' => [ 'VersionId' => [ 'shape' => 'String', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], ],];
