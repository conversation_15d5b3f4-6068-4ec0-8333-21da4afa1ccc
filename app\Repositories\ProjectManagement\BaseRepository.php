<?php

namespace App\Repositories\ProjectManagement;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;

/**
 * المستودع الأساسي لنظام إدارة المشاريع
 *
 * هذا المستودع يحتوي على الطرق الأساسية المشتركة بين جميع المستودعات
 * ويطبق نمط Repository Pattern لفصل منطق الوصول للبيانات
 *
 * @package App\Repositories\ProjectManagement
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
abstract class BaseRepository
{
    /**
     * نموذج البيانات المرتبط بالمستودع
     *
     * @var Model
     */
    protected Model $model;

    /**
     * منشئ المستودع الأساسي
     *
     * @param Model $model
     */
    public function __construct(Model $model)
    {
        $this->model = $model;
    }

    /**
     * الحصول على جميع السجلات
     *
     * @param array $columns الأعمدة المطلوبة
     * @param array $relations العلاقات المطلوب تحميلها
     * @return Collection
     */
    public function all(array $columns = ['*'], array $relations = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->get($columns);
    }

    /**
     * الحصول على سجلات مع ترقيم الصفحات
     *
     * @param int $perPage عدد السجلات في الصفحة
     * @param array $columns الأعمدة المطلوبة
     * @param array $relations العلاقات المطلوب تحميلها
     * @return LengthAwarePaginator
     */
    public function paginate(int $perPage = 15, array $columns = ['*'], array $relations = []): LengthAwarePaginator
    {
        $query = $this->model->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->paginate($perPage, $columns);
    }

    /**
     * البحث عن سجل بالمعرف
     *
     * @param int $id المعرف
     * @param array $columns الأعمدة المطلوبة
     * @param array $relations العلاقات المطلوب تحميلها
     * @return Model|null
     */
    public function find(int $id, array $columns = ['*'], array $relations = []): ?Model
    {
        $query = $this->model->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->find($id, $columns);
    }

    /**
     * البحث عن سجل بالمعرف أو إرجاع استثناء
     *
     * @param int $id المعرف
     * @param array $columns الأعمدة المطلوبة
     * @param array $relations العلاقات المطلوب تحميلها
     * @return Model
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
     */
    public function findOrFail(int $id, array $columns = ['*'], array $relations = []): Model
    {
        $query = $this->model->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->findOrFail($id, $columns);
    }

    /**
     * البحث عن سجل بشروط معينة
     *
     * @param array $criteria الشروط
     * @param array $columns الأعمدة المطلوبة
     * @param array $relations العلاقات المطلوب تحميلها
     * @return Model|null
     */
    public function findBy(array $criteria, array $columns = ['*'], array $relations = []): ?Model
    {
        $query = $this->model->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        foreach ($criteria as $field => $value) {
            $query->where($field, $value);
        }

        return $query->first($columns);
    }

    /**
     * البحث عن سجلات بشروط معينة
     *
     * @param array $criteria الشروط
     * @param array $columns الأعمدة المطلوبة
     * @param array $relations العلاقات المطلوب تحميلها
     * @return Collection
     */
    public function findAllBy(array $criteria, array $columns = ['*'], array $relations = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        foreach ($criteria as $field => $value) {
            if (is_array($value)) {
                $query->whereIn($field, $value);
            } else {
                $query->where($field, $value);
            }
        }

        return $query->get($columns);
    }

    /**
     * إنشاء سجل جديد
     *
     * @param array $data البيانات
     * @return Model
     */
    public function create(array $data): Model
    {
        return $this->model->create($data);
    }

    /**
     * تحديث سجل موجود
     *
     * @param int $id المعرف
     * @param array $data البيانات الجديدة
     * @return bool
     */
    public function update(int $id, array $data): bool
    {
        $model = $this->findOrFail($id);
        return $model->update($data);
    }

    /**
     * حذف سجل
     *
     * @param int $id المعرف
     * @return bool
     */
    public function delete(int $id): bool
    {
        $model = $this->findOrFail($id);
        return $model->delete();
    }

    /**
     * حذف سجلات متعددة
     *
     * @param array $ids مصفوفة المعرفات
     * @return int عدد السجلات المحذوفة
     */
    public function deleteMultiple(array $ids): int
    {
        return $this->model->whereIn('id', $ids)->delete();
    }

    /**
     * عد السجلات
     *
     * @param array $criteria الشروط
     * @return int
     */
    public function count(array $criteria = []): int
    {
        $query = $this->model->newQuery();

        foreach ($criteria as $field => $value) {
            if (is_array($value)) {
                $query->whereIn($field, $value);
            } else {
                $query->where($field, $value);
            }
        }

        return $query->count();
    }

    /**
     * التحقق من وجود سجل
     *
     * @param array $criteria الشروط
     * @return bool
     */
    public function exists(array $criteria): bool
    {
        $query = $this->model->newQuery();

        foreach ($criteria as $field => $value) {
            $query->where($field, $value);
        }

        return $query->exists();
    }

    /**
     * البحث النصي
     *
     * @param string $term مصطلح البحث
     * @param array $fields الحقول المراد البحث فيها
     * @param array $relations العلاقات المطلوب تحميلها
     * @return Collection
     */
    public function search(string $term, array $fields = [], array $relations = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($relations)) {
            $query->with($relations);
        }

        if (empty($fields)) {
            // استخدام الحقول القابلة للبحث من النموذج
            $fields = $this->model->getSearchableFields() ?? ['name', 'title'];
        }

        $query->where(function ($q) use ($term, $fields) {
            foreach ($fields as $field) {
                $q->orWhere($field, 'LIKE', "%{$term}%");
            }
        });

        return $query->get();
    }

    /**
     * الحصول على استعلام جديد
     *
     * @return Builder
     */
    public function newQuery(): Builder
    {
        return $this->model->newQuery();
    }

    /**
     * تطبيق نطاق على الاستعلام
     *
     * @param string $scope اسم النطاق
     * @param array $parameters المعاملات
     * @return Builder
     */
    public function scope(string $scope, array $parameters = []): Builder
    {
        $query = $this->model->newQuery();
        return $query->{$scope}(...$parameters);
    }

    /**
     * الحصول على النموذج
     *
     * @return Model
     */
    public function getModel(): Model
    {
        return $this->model;
    }

    /**
     * تعيين نموذج جديد
     *
     * @param Model $model
     * @return $this
     */
    public function setModel(Model $model): self
    {
        $this->model = $model;
        return $this;
    }

    /**
     * إعادة تعيين النموذج إلى حالته الأصلية
     *
     * @return $this
     */
    public function resetModel(): self
    {
        $this->model = app($this->model::class);
        return $this;
    }
}
