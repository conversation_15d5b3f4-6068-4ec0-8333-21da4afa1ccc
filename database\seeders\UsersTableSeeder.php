<?php

namespace Database\Seeders;
use App\Models\User;
use App\Models\Utility;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class UsersTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $arrPermissions = [
            [
                'name' => 'show dashboard',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage user',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create user',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit user',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete user',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create language',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage system settings',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage role',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create role',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit role',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete role',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage permission',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create permission',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit permission',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete permission',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage company settings',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage business settings',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage stripe settings',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage expense',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create expense',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit expense',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete expense',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage invoice',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create invoice',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit invoice',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete invoice',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'show invoice',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create payment invoice',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete payment invoice',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'send invoice',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete invoice product',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'convert invoice',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage plan',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create plan',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit plan',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage constant unit',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create constant unit',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit constant unit',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete constant unit',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage constant tax',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create constant tax',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit constant tax',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete constant tax',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage constant category',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create constant category',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit constant category',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete constant category',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage product & service',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create product & service',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit product & service',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete product & service',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage customer',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create customer',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit customer',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete customer',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'show customer',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage vender',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create vender',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit vender',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete vender',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'show vender',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage bank account',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create bank account',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit bank account',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete bank account',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage transfer',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create transfer',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit transfer',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete transfer',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage constant payment method',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create constant payment method',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit constant payment method',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete constant payment method',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage transaction',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage revenue',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create revenue',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit revenue',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete revenue',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage bill',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create bill',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit bill',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete bill',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'show bill',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage payment',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create payment',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit payment',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete payment',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete bill product',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'buy plan',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'send bill',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create payment bill',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete payment bill',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage order',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'income report',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'expense report',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'income vs expense report',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'invoice report',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'bill report',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'stock report',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'tax report',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'loss & profit report',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage customer payment',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage customer transaction',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage customer invoice',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'vender manage bill',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage vender bill',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage vender payment',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage vender transaction',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage credit note',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create credit note',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit credit note',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete credit note',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage debit note',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create debit note',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit debit note',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete debit note',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'duplicate invoice',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'duplicate bill',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage coupon',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create coupon',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit coupon',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete coupon',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage proposal',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create proposal',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit proposal',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete proposal',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'duplicate proposal',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'show proposal',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'send proposal',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete proposal product',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage customer proposal',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage goal',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create goal',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit goal',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete goal',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage assets',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create assets',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit assets',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete assets',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'statement report',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage constant custom field',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create constant custom field',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit constant custom field',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete constant custom field',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage chart of account',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create chart of account',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit chart of account',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete chart of account',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage journal entry',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create journal entry',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit journal entry',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete journal entry',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'show journal entry',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'balance sheet report',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'ledger report',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'trial balance report',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create budget planner',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'edit budget planner',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'manage budget planner',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'delete budget planner',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'view budget planner',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'manage contract',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create contract',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage customer contract',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'edit contract',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete contract',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'show contract',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'duplicate contract',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete attachment',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete comment',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'delete notes',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'contract description',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'upload attachment',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'add comment',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'add notes',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'send contract mail',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'manage retainer',
                'guard_name' => 'web',
                'created_at' => date('Y - m - d H:i:s'),
                'updated_at' => date('Y - m - d H:i:s'),
            ],
            [
                'name' => 'create retainer',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'edit retainer',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'delete retainer',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'show retainer',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'send retainer',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'duplicate retainer',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'delete retainer product',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'convert invoice proposal',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'convert invoice retainer',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'convert retainer proposal',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'manage constant contract type',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'create constant contract type',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'edit constant contract type',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'delete constant contract type',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],



        ];

        Permission::insert($arrPermissions);


        // customer
        $customerRole       = Role::create(
            [
                'name' => 'customer',
                'created_by' => 0,
            ]
        );
        $customerPermission = [
            'manage customer payment',
            'manage customer transaction',
            'manage customer invoice',
            'show invoice',
            'show proposal',
            'manage customer proposal',
            'show customer',
            'manage customer contract',
            'show contract',
            'contract description',
            'upload attachment',
            'add comment',
            'add notes',
        ];

        $customerRole->givePermissionTo($customerPermission);

        // vender
        $venderRole       = Role::create(
            [
                'name' => 'vender',
                'created_by' => 0,
            ]
        );
        $venderPermission = [
            'vender manage bill',
            'manage vender bill',
            'manage vender payment',
            'manage vender transaction',
            'show vender',
            'show bill',
        ];

        $venderRole->givePermissionTo($venderPermission);

        // company
        $companyRole        = Role::create(
            [
                'name' => 'company',
                'created_by' => 0,
            ]
        );
        $companyPermissions = [
            'show dashboard',
            'manage user',
            'create user',
            'edit user',
            'delete user',
            'manage role',
            'create role',
            'edit role',
            'delete role',
            'manage permission',
            'create permission',
            'edit permission',
            'delete permission',
            'manage company settings',
            'manage business settings',
            'manage expense',
            'create expense',
            'edit expense',
            'delete expense',
            'manage invoice',
            'create invoice',
            'edit invoice',
            'delete invoice',
            'show invoice',
            'manage plan',
            'buy plan',
            'manage product & service',
            'create product & service',
            'delete product & service',
            'edit product & service',
            'manage constant tax',
            'create constant tax',
            'edit constant tax',
            'delete constant tax',
            'manage constant category',
            'create constant category',
            'edit constant category',
            'delete constant category',
            'manage constant unit',
            'create constant unit',
            'edit constant unit',
            'delete constant unit',
            'manage customer',
            'create customer',
            'edit customer',
            'delete customer',
            'show customer',
            'manage vender',
            'create vender',
            'edit vender',
            'delete vender',
            'show vender',
            'manage bank account',
            'create bank account',
            'edit bank account',
            'delete bank account',
            'manage transfer',
            'create transfer',
            'edit transfer',
            'delete transfer',
            'manage revenue',
            'create revenue',
            'edit revenue',
            'delete revenue',
            'manage bill',
            'create bill',
            'edit bill',
            'delete bill',
            'show bill',
            'manage payment',
            'create payment',
            'edit payment',
            'delete payment',
            'delete invoice product',
            'delete bill product',
            'send invoice',
            'create payment invoice',
            'delete payment invoice',
            'send bill',
            'create payment bill',
            'delete payment bill',
            'income report',
            'expense report',
            'income vs expense report',
            'invoice report',
            'bill report',
            'stock report',
            'tax report',
            'loss & profit report',
            'manage transaction',
            'manage order',
            'manage credit note',
            'create credit note',
            'edit credit note',
            'delete credit note',
            'manage debit note',
            'create debit note',
            'edit debit note',
            'delete debit note',
            'duplicate invoice',
            'convert invoice',
            'duplicate bill',
            'manage proposal',
            'create proposal',
            'edit proposal',
            'delete proposal',
            'duplicate proposal',
            'show proposal',
            'send proposal',
            'delete proposal product',
            'manage goal',
            'create goal',
            'edit goal',
            'delete goal',
            'manage assets',
            'create assets',
            'edit assets',
            'delete assets',
            'statement report',
            'manage constant custom field',
            'create constant custom field',
            'edit constant custom field',
            'delete constant custom field',
            'manage chart of account',
            'create chart of account',
            'edit chart of account',
            'delete chart of account',
            'manage journal entry',
            'create journal entry',
            'edit journal entry',
            'delete journal entry',
            'show journal entry',
            'balance sheet report',
            'ledger report',
            'trial balance report',
            'manage budget planner',
            'create budget planner',
            'edit budget planner',
            'delete budget planner',
            'view budget planner',
            'manage contract',
            'create contract',
            'edit contract',
            'delete contract',
            'show contract',
            'duplicate contract',
            'delete attachment',
            'delete comment',
            'delete notes',
            'contract description',
            'upload attachment',
            'add comment',
            'add notes',
            'send contract mail',
            'manage retainer',
            'create retainer',
            'edit retainer',
            'delete retainer',
            'show retainer',
            'send retainer',
            'duplicate retainer',
            'delete retainer product',
            'convert invoice proposal',
            'convert invoice retainer',
            'convert retainer proposal',
            'manage constant contract type',
            'create constant contract type',
            'edit constant contract type',
            'delete constant contract type',



        ];

        $companyRole->givePermissionTo($companyPermissions);

        $company = User::create(
            [
                'name' => 'company',
                'email' => '<EMAIL>',
                'password' => Hash::make('1234'),
                'type' => 'company',
                'lang' => 'en',
                'avatar' => '',
                'created_by' => 0,
            ]
        );
        $company->assignRole($companyRole);

        // accountant
        $accountantRole = Role::create(
            [
                'name' => 'accountant',
                'created_by' => $company->id,
            ]
        );

        $accountantPermission = [
            'show dashboard',
            'manage expense',
            'create expense',
            'edit expense',
            'delete expense',
            'manage invoice',
            'create invoice',
            'edit invoice',
            'delete invoice',
            'show invoice',
            'convert invoice',
            'manage product & service',
            'create product & service',
            'delete product & service',
            'edit product & service',
            'manage constant tax',
            'create constant tax',
            'edit constant tax',
            'delete constant tax',
            'manage constant category',
            'create constant category',
            'edit constant category',
            'delete constant category',
            'manage constant unit',
            'create constant unit',
            'edit constant unit',
            'delete constant unit',
            'manage customer',
            'create customer',
            'edit customer',
            'delete customer',
            'show customer',
            'manage vender',
            'create vender',
            'edit vender',
            'delete vender',
            'show vender',
            'manage bank account',
            'create bank account',
            'edit bank account',
            'delete bank account',
            'manage transfer',
            'create transfer',
            'edit transfer',
            'delete transfer',
            'manage revenue',
            'create revenue',
            'edit revenue',
            'delete revenue',
            'manage bill',
            'create bill',
            'edit bill',
            'delete bill',
            'show bill',
            'manage payment',
            'create payment',
            'edit payment',
            'delete payment',
            'delete invoice product',
            'delete bill product',
            'send invoice',
            'create payment invoice',
            'delete payment invoice',
            'send bill',
            'create payment bill',
            'delete payment bill',
            'income report',
            'expense report',
            'income vs expense report',
            'invoice report',
            'stock report',
            'tax report',
            'loss & profit report',
            'manage transaction',
            'manage credit note',
            'create credit note',
            'edit credit note',
            'delete credit note',
            'manage debit note',
            'create debit note',
            'edit debit note',
            'delete debit note',
            'manage proposal',
            'create proposal',
            'edit proposal',
            'delete proposal',
            'duplicate proposal',
            'send proposal',
            'show proposal',
            'delete proposal product',
            'manage goal',
            'create goal',
            'edit goal',
            'delete goal',
            'manage assets',
            'create assets',
            'edit assets',
            'delete assets',
            'statement report',
            'manage constant custom field',
            'create constant custom field',
            'edit constant custom field',
            'delete constant custom field',
            'manage chart of account',
            'create chart of account',
            'edit chart of account',
            'delete chart of account',
            'manage journal entry',
            'create journal entry',
            'edit journal entry',
            'delete journal entry',
            'show journal entry',
            'balance sheet report',
            'ledger report',
            'trial balance report',
            'manage budget planner',
            'create budget planner',
            'edit budget planner',
            'delete budget planner',
            'view budget planner',
            'manage retainer',
            'create retainer',
            'edit retainer',
            'delete retainer',
            'show retainer',
            'send retainer',
            'duplicate retainer',
            'delete retainer product',
            'convert invoice proposal',
            'convert invoice retainer',
            'convert retainer proposal',
            'manage constant contract type',
            'create constant contract type',
            'edit constant contract type',
            'delete constant contract type',

        ];

        $accountantRole->givePermissionTo($accountantPermission);

        $accountant = User::create(
            [
                'name' => 'accountant',
                'email' => '<EMAIL>',
                'password' => Hash::make('1234'),
                'type' => 'accountant',
                'lang' => 'en',
                'avatar' => '',
                'created_by' => $company->id,
            ]
        );
        $accountant->assignRole($accountantRole);

        \App\Models\BankAccount::create(
            [
                'holder_name' => 'Cash',
                'bank_name' => '',
                'account_number' => '-',
                'opening_balance' => '0.00',
                'contact_number' => '-',
                'bank_address' => '-',
                'created_by' => $company->id,
            ]
        );
        Utility::chartOfAccountTypeData();
        Utility::chartOfAccountData($company);
        $company->defaultEmail();
        $company->userDefaultData();
        Utility::languagecreate();


        $data = [
            ['name'=>'local_storage_validation', 'value'=> 'jpg,jpeg,png,xlsx,xls,csv,pdf', 'created_by'=> 1, 'created_at'=> now(), 'updated_at'=> now()],
            ['name'=>'wasabi_storage_validation', 'value'=> 'jpg,jpeg,png,xlsx,xls,csv,pdf', 'created_by'=> 1, 'created_at'=> now(), 'updated_at'=> now()],
            ['name'=>'s3_storage_validation', 'value'=> 'jpg,jpeg,png,xlsx,xls,csv,pdf', 'created_by'=> 1, 'created_at'=> now(), 'updated_at'=> now()],
            ['name'=>'local_storage_max_upload_size', 'value'=> 2048000, 'created_by'=> 1, 'created_at'=> now(), 'updated_at'=> now()],
            ['name'=>'wasabi_max_upload_size', 'value'=> 2048000, 'created_by'=> 1, 'created_at'=> now(), 'updated_at'=> now()],
            ['name'=>'s3_max_upload_size', 'value'=> 2048000, 'created_by'=> 1, 'created_at'=> now(), 'updated_at'=> now()],
            ['name'=>'storage_setting', 'value'=> 'local', 'created_by'=> 1, 'created_at'=> now(), 'updated_at'=> now()]

        ];

        DB::table('settings')->insert($data);
    }
}
