<?php
// This file was auto-generated from sdk-root/src/data/elasticbeanstalk/2010-12-01/paginators-1.json
return [ 'pagination' => [ 'DescribeApplicationVersions' => [ 'result_key' => 'ApplicationVersions', ], 'DescribeApplications' => [ 'result_key' => 'Applications', ], 'DescribeConfigurationOptions' => [ 'result_key' => 'Options', ], 'DescribeEnvironmentManagedActionHistory' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxItems', 'output_token' => 'NextToken', 'result_key' => 'ManagedActionHistoryItems', ], 'DescribeEnvironments' => [ 'result_key' => 'Environments', ], 'DescribeEvents' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxRecords', 'output_token' => 'NextToken', 'result_key' => 'Events', ], 'ListAvailableSolutionStacks' => [ 'result_key' => 'SolutionStacks', ], 'ListPlatformBranches' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxRecords', 'output_token' => 'NextToken', ], 'ListPlatformVersions' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxRecords', 'output_token' => 'NextToken', 'result_key' => 'PlatformSummaryList', ], ],];
