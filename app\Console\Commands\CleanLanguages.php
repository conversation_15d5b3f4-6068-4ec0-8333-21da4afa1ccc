<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Language;
use App\Models\User;
use App\Models\Customer;
use App\Models\Vender;
use App\Models\EmailTemplateLang;
use App\Models\NotificationTemplateLangs;

class CleanLanguages extends Command
{
    protected $signature = 'clean:languages';
    protected $description = 'Remove all languages except Arabic, English, and French';

    public function handle()
    {
        $keepLangs = ['ar', 'en', 'fr'];
        
        // حذف اللغات من جدول languages
        $deletedCount = Language::whereNotIn('code', $keepLangs)->count();
        Language::whereNotIn('code', $keepLangs)->delete();
        
        // تحديث المستخدمين الذين يستخدمون لغات محذوفة
        User::whereNotIn('lang', $keepLangs)->update(['lang' => 'en']);
        Customer::whereNotIn('lang', $keepLangs)->update(['lang' => 'en']);
        Vender::whereNotIn('lang', $keepLangs)->update(['lang' => 'en']);
        
        // حذف قوالب البريد الإلكتروني للغات المحذوفة
        EmailTemplateLang::whereNotIn('lang', $keepLangs)->delete();
        NotificationTemplateLangs::whereNotIn('lang', $keepLangs)->delete();
        
        $this->info("تم حذف {$deletedCount} لغة من قاعدة البيانات");
        $this->info("تم الاحتفاظ باللغات: العربية، الإنجليزية، الفرنسية فقط");
        $this->info("تم تحديث جميع المستخدمين والعملاء والموردين");
    }
}
