<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // $schedule->command('inspire')->hourly();

        // جدولة أوامر نظام إدارة المشاريع للتعامل مع مليون تسجيل
        $this->scheduleProjectManagementCommands($schedule);
    }

    /**
     * جدولة أوامر نظام إدارة المشاريع
     *
     * @param Schedule $schedule
     */
    protected function scheduleProjectManagementCommands(Schedule $schedule): void
    {
        // مراقبة الأداء كل 5 دقائق
        $schedule->command('pm:monitor-performance --log')
                 ->everyFiveMinutes()
                 ->withoutOverlapping()
                 ->runInBackground()
                 ->appendOutputTo(storage_path('logs/performance-monitor.log'));

        // تنظيف التخزين المؤقت يومياً في الساعة 2:00 صباحاً
        $schedule->command('pm:optimize-database --cache --force')
                 ->dailyAt('02:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // تحديث فهارس البحث أسبوعياً يوم الأحد في الساعة 3:00 صباحاً
        $schedule->command('pm:optimize-database --index --force')
                 ->weeklyOn(0, '03:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // أرشفة البيانات القديمة شهرياً في اليوم الأول من الشهر
        $schedule->command('pm:optimize-database --archive --months=12 --force')
                 ->monthlyOn(1, '04:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // تحليل شامل للأداء أسبوعياً يوم السبت
        $schedule->command('pm:optimize-database --analyze --force')
                 ->weeklyOn(6, '05:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // تنظيف شامل شهرياً
        $schedule->command('pm:optimize-database --all --months=18 --force')
                 ->monthlyOn(15, '06:00')
                 ->withoutOverlapping()
                 ->runInBackground()
                 ->emailOutputOnFailure(config('mail.admin_email', '<EMAIL>'));
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
