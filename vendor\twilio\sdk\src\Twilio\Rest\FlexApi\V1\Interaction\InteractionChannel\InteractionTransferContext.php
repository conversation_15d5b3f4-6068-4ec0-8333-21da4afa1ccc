<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\FlexApi\V1\Interaction\InteractionChannel;

use Twilio\Exceptions\TwilioException;
use Twilio\Values;
use Twilio\Version;
use Twilio\InstanceContext;


class InteractionTransferContext extends InstanceContext
    {
    /**
     * Initialize the InteractionTransferContext
     *
     * @param Version $version Version that contains the resource
     * @param string $interactionSid The Interaction Sid for the Interaction
     * @param string $channelSid The Channel Sid for the Channel.
     * @param string $sid The unique string created by Twilio to identify a Transfer resource.
     */
    public function __construct(
        Version $version,
        $interactionSid,
        $channelSid,
        $sid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'interactionSid' =>
            $interactionSid,
        'channelSid' =>
            $channelSid,
        'sid' =>
            $sid,
        ];

        $this->uri = '/Interactions/' . \rawurlencode($interactionSid)
        .'/Channels/' . \rawurlencode($channelSid)
        .'/Transfers/' . \rawurlencode($sid)
        .'';
    }

    /**
     * Fetch the InteractionTransferInstance
     *
     * @return InteractionTransferInstance Fetched InteractionTransferInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): InteractionTransferInstance
    {

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json' ]);
        $payload = $this->version->fetch('GET', $this->uri, [], [], $headers);

        return new InteractionTransferInstance(
            $this->version,
            $payload,
            $this->solution['interactionSid'],
            $this->solution['channelSid'],
            $this->solution['sid']
        );
    }


    /**
     * Update the InteractionTransferInstance
     *
     * @return InteractionTransferInstance Updated InteractionTransferInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(): InteractionTransferInstance
    {

        $headers = Values::of(['Content-Type' => 'application/json', 'Accept' => 'application/json' ]);
        $data = $body->toArray();
        $payload = $this->version->update('POST', $this->uri, [], $data, $headers);

        return new InteractionTransferInstance(
            $this->version,
            $payload,
            $this->solution['interactionSid'],
            $this->solution['channelSid'],
            $this->solution['sid']
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.FlexApi.V1.InteractionTransferContext ' . \implode(' ', $context) . ']';
    }
}
