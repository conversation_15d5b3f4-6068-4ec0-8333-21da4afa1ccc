<?php
// This file was auto-generated from sdk-root/src/data/resiliencehub/2020-04-30/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-04-30', 'endpointPrefix' => 'resiliencehub', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS Resilience Hub', 'serviceId' => 'resiliencehub', 'signatureVersion' => 'v4', 'signingName' => 'resiliencehub', 'uid' => 'resiliencehub-2020-04-30', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AcceptResourceGroupingRecommendations' => [ 'name' => 'AcceptResourceGroupingRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/accept-resource-grouping-recommendations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AcceptResourceGroupingRecommendationsRequest', ], 'output' => [ 'shape' => 'AcceptResourceGroupingRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'AddDraftAppVersionResourceMappings' => [ 'name' => 'AddDraftAppVersionResourceMappings', 'http' => [ 'method' => 'POST', 'requestUri' => '/add-draft-app-version-resource-mappings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AddDraftAppVersionResourceMappingsRequest', ], 'output' => [ 'shape' => 'AddDraftAppVersionResourceMappingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'BatchUpdateRecommendationStatus' => [ 'name' => 'BatchUpdateRecommendationStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/batch-update-recommendation-status', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchUpdateRecommendationStatusRequest', ], 'output' => [ 'shape' => 'BatchUpdateRecommendationStatusResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateApp' => [ 'name' => 'CreateApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/create-app', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAppRequest', ], 'output' => [ 'shape' => 'CreateAppResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateAppVersionAppComponent' => [ 'name' => 'CreateAppVersionAppComponent', 'http' => [ 'method' => 'POST', 'requestUri' => '/create-app-version-app-component', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAppVersionAppComponentRequest', ], 'output' => [ 'shape' => 'CreateAppVersionAppComponentResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateAppVersionResource' => [ 'name' => 'CreateAppVersionResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/create-app-version-resource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAppVersionResourceRequest', ], 'output' => [ 'shape' => 'CreateAppVersionResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateRecommendationTemplate' => [ 'name' => 'CreateRecommendationTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/create-recommendation-template', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateRecommendationTemplateRequest', ], 'output' => [ 'shape' => 'CreateRecommendationTemplateResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateResiliencyPolicy' => [ 'name' => 'CreateResiliencyPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/create-resiliency-policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateResiliencyPolicyRequest', ], 'output' => [ 'shape' => 'CreateResiliencyPolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteApp' => [ 'name' => 'DeleteApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-app', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAppRequest', ], 'output' => [ 'shape' => 'DeleteAppResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteAppAssessment' => [ 'name' => 'DeleteAppAssessment', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-app-assessment', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAppAssessmentRequest', ], 'output' => [ 'shape' => 'DeleteAppAssessmentResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteAppInputSource' => [ 'name' => 'DeleteAppInputSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-app-input-source', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAppInputSourceRequest', ], 'output' => [ 'shape' => 'DeleteAppInputSourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteAppVersionAppComponent' => [ 'name' => 'DeleteAppVersionAppComponent', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-app-version-app-component', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAppVersionAppComponentRequest', ], 'output' => [ 'shape' => 'DeleteAppVersionAppComponentResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteAppVersionResource' => [ 'name' => 'DeleteAppVersionResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-app-version-resource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAppVersionResourceRequest', ], 'output' => [ 'shape' => 'DeleteAppVersionResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteRecommendationTemplate' => [ 'name' => 'DeleteRecommendationTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-recommendation-template', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteRecommendationTemplateRequest', ], 'output' => [ 'shape' => 'DeleteRecommendationTemplateResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteResiliencyPolicy' => [ 'name' => 'DeleteResiliencyPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-resiliency-policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteResiliencyPolicyRequest', ], 'output' => [ 'shape' => 'DeleteResiliencyPolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeApp' => [ 'name' => 'DescribeApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-app', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeAppRequest', ], 'output' => [ 'shape' => 'DescribeAppResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeAppAssessment' => [ 'name' => 'DescribeAppAssessment', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-app-assessment', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeAppAssessmentRequest', ], 'output' => [ 'shape' => 'DescribeAppAssessmentResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeAppVersion' => [ 'name' => 'DescribeAppVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-app-version', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeAppVersionRequest', ], 'output' => [ 'shape' => 'DescribeAppVersionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeAppVersionAppComponent' => [ 'name' => 'DescribeAppVersionAppComponent', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-app-version-app-component', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeAppVersionAppComponentRequest', ], 'output' => [ 'shape' => 'DescribeAppVersionAppComponentResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeAppVersionResource' => [ 'name' => 'DescribeAppVersionResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-app-version-resource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeAppVersionResourceRequest', ], 'output' => [ 'shape' => 'DescribeAppVersionResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeAppVersionResourcesResolutionStatus' => [ 'name' => 'DescribeAppVersionResourcesResolutionStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-app-version-resources-resolution-status', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeAppVersionResourcesResolutionStatusRequest', ], 'output' => [ 'shape' => 'DescribeAppVersionResourcesResolutionStatusResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeAppVersionTemplate' => [ 'name' => 'DescribeAppVersionTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-app-version-template', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeAppVersionTemplateRequest', ], 'output' => [ 'shape' => 'DescribeAppVersionTemplateResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeDraftAppVersionResourcesImportStatus' => [ 'name' => 'DescribeDraftAppVersionResourcesImportStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-draft-app-version-resources-import-status', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeDraftAppVersionResourcesImportStatusRequest', ], 'output' => [ 'shape' => 'DescribeDraftAppVersionResourcesImportStatusResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeMetricsExport' => [ 'name' => 'DescribeMetricsExport', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-metrics-export', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeMetricsExportRequest', ], 'output' => [ 'shape' => 'DescribeMetricsExportResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeResiliencyPolicy' => [ 'name' => 'DescribeResiliencyPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-resiliency-policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeResiliencyPolicyRequest', ], 'output' => [ 'shape' => 'DescribeResiliencyPolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeResourceGroupingRecommendationTask' => [ 'name' => 'DescribeResourceGroupingRecommendationTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-resource-grouping-recommendation-task', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeResourceGroupingRecommendationTaskRequest', ], 'output' => [ 'shape' => 'DescribeResourceGroupingRecommendationTaskResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ImportResourcesToDraftAppVersion' => [ 'name' => 'ImportResourcesToDraftAppVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/import-resources-to-draft-app-version', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ImportResourcesToDraftAppVersionRequest', ], 'output' => [ 'shape' => 'ImportResourcesToDraftAppVersionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAlarmRecommendations' => [ 'name' => 'ListAlarmRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-alarm-recommendations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAlarmRecommendationsRequest', ], 'output' => [ 'shape' => 'ListAlarmRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAppAssessmentComplianceDrifts' => [ 'name' => 'ListAppAssessmentComplianceDrifts', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-app-assessment-compliance-drifts', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppAssessmentComplianceDriftsRequest', ], 'output' => [ 'shape' => 'ListAppAssessmentComplianceDriftsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAppAssessmentResourceDrifts' => [ 'name' => 'ListAppAssessmentResourceDrifts', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-app-assessment-resource-drifts', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppAssessmentResourceDriftsRequest', ], 'output' => [ 'shape' => 'ListAppAssessmentResourceDriftsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAppAssessments' => [ 'name' => 'ListAppAssessments', 'http' => [ 'method' => 'GET', 'requestUri' => '/list-app-assessments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppAssessmentsRequest', ], 'output' => [ 'shape' => 'ListAppAssessmentsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAppComponentCompliances' => [ 'name' => 'ListAppComponentCompliances', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-app-component-compliances', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppComponentCompliancesRequest', ], 'output' => [ 'shape' => 'ListAppComponentCompliancesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAppComponentRecommendations' => [ 'name' => 'ListAppComponentRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-app-component-recommendations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppComponentRecommendationsRequest', ], 'output' => [ 'shape' => 'ListAppComponentRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAppInputSources' => [ 'name' => 'ListAppInputSources', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-app-input-sources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppInputSourcesRequest', ], 'output' => [ 'shape' => 'ListAppInputSourcesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAppVersionAppComponents' => [ 'name' => 'ListAppVersionAppComponents', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-app-version-app-components', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppVersionAppComponentsRequest', ], 'output' => [ 'shape' => 'ListAppVersionAppComponentsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAppVersionResourceMappings' => [ 'name' => 'ListAppVersionResourceMappings', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-app-version-resource-mappings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppVersionResourceMappingsRequest', ], 'output' => [ 'shape' => 'ListAppVersionResourceMappingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAppVersionResources' => [ 'name' => 'ListAppVersionResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-app-version-resources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppVersionResourcesRequest', ], 'output' => [ 'shape' => 'ListAppVersionResourcesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAppVersions' => [ 'name' => 'ListAppVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-app-versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppVersionsRequest', ], 'output' => [ 'shape' => 'ListAppVersionsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListApps' => [ 'name' => 'ListApps', 'http' => [ 'method' => 'GET', 'requestUri' => '/list-apps', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppsRequest', ], 'output' => [ 'shape' => 'ListAppsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListMetrics' => [ 'name' => 'ListMetrics', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-metrics', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMetricsRequest', ], 'output' => [ 'shape' => 'ListMetricsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListRecommendationTemplates' => [ 'name' => 'ListRecommendationTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/list-recommendation-templates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRecommendationTemplatesRequest', ], 'output' => [ 'shape' => 'ListRecommendationTemplatesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListResiliencyPolicies' => [ 'name' => 'ListResiliencyPolicies', 'http' => [ 'method' => 'GET', 'requestUri' => '/list-resiliency-policies', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListResiliencyPoliciesRequest', ], 'output' => [ 'shape' => 'ListResiliencyPoliciesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListResourceGroupingRecommendations' => [ 'name' => 'ListResourceGroupingRecommendations', 'http' => [ 'method' => 'GET', 'requestUri' => '/list-resource-grouping-recommendations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListResourceGroupingRecommendationsRequest', ], 'output' => [ 'shape' => 'ListResourceGroupingRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListSopRecommendations' => [ 'name' => 'ListSopRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-sop-recommendations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSopRecommendationsRequest', ], 'output' => [ 'shape' => 'ListSopRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListSuggestedResiliencyPolicies' => [ 'name' => 'ListSuggestedResiliencyPolicies', 'http' => [ 'method' => 'GET', 'requestUri' => '/list-suggested-resiliency-policies', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSuggestedResiliencyPoliciesRequest', ], 'output' => [ 'shape' => 'ListSuggestedResiliencyPoliciesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTestRecommendations' => [ 'name' => 'ListTestRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-test-recommendations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTestRecommendationsRequest', ], 'output' => [ 'shape' => 'ListTestRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListUnsupportedAppVersionResources' => [ 'name' => 'ListUnsupportedAppVersionResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-unsupported-app-version-resources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListUnsupportedAppVersionResourcesRequest', ], 'output' => [ 'shape' => 'ListUnsupportedAppVersionResourcesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'PublishAppVersion' => [ 'name' => 'PublishAppVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/publish-app-version', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PublishAppVersionRequest', ], 'output' => [ 'shape' => 'PublishAppVersionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'PutDraftAppVersionTemplate' => [ 'name' => 'PutDraftAppVersionTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/put-draft-app-version-template', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutDraftAppVersionTemplateRequest', ], 'output' => [ 'shape' => 'PutDraftAppVersionTemplateResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'RejectResourceGroupingRecommendations' => [ 'name' => 'RejectResourceGroupingRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/reject-resource-grouping-recommendations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RejectResourceGroupingRecommendationsRequest', ], 'output' => [ 'shape' => 'RejectResourceGroupingRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'RemoveDraftAppVersionResourceMappings' => [ 'name' => 'RemoveDraftAppVersionResourceMappings', 'http' => [ 'method' => 'POST', 'requestUri' => '/remove-draft-app-version-resource-mappings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RemoveDraftAppVersionResourceMappingsRequest', ], 'output' => [ 'shape' => 'RemoveDraftAppVersionResourceMappingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ResolveAppVersionResources' => [ 'name' => 'ResolveAppVersionResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/resolve-app-version-resources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ResolveAppVersionResourcesRequest', ], 'output' => [ 'shape' => 'ResolveAppVersionResourcesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StartAppAssessment' => [ 'name' => 'StartAppAssessment', 'http' => [ 'method' => 'POST', 'requestUri' => '/start-app-assessment', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartAppAssessmentRequest', ], 'output' => [ 'shape' => 'StartAppAssessmentResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StartMetricsExport' => [ 'name' => 'StartMetricsExport', 'http' => [ 'method' => 'POST', 'requestUri' => '/start-metrics-export', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartMetricsExportRequest', ], 'output' => [ 'shape' => 'StartMetricsExportResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StartResourceGroupingRecommendationTask' => [ 'name' => 'StartResourceGroupingRecommendationTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/start-resource-grouping-recommendation-task', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartResourceGroupingRecommendationTaskRequest', ], 'output' => [ 'shape' => 'StartResourceGroupingRecommendationTaskResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateApp' => [ 'name' => 'UpdateApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-app', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAppRequest', ], 'output' => [ 'shape' => 'UpdateAppResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateAppVersion' => [ 'name' => 'UpdateAppVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-app-version', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAppVersionRequest', ], 'output' => [ 'shape' => 'UpdateAppVersionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateAppVersionAppComponent' => [ 'name' => 'UpdateAppVersionAppComponent', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-app-version-app-component', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAppVersionAppComponentRequest', ], 'output' => [ 'shape' => 'UpdateAppVersionAppComponentResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateAppVersionResource' => [ 'name' => 'UpdateAppVersionResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-app-version-resource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAppVersionResourceRequest', ], 'output' => [ 'shape' => 'UpdateAppVersionResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateResiliencyPolicy' => [ 'name' => 'UpdateResiliencyPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-resiliency-policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateResiliencyPolicyRequest', ], 'output' => [ 'shape' => 'UpdateResiliencyPolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], ], 'shapes' => [ 'AcceptGroupingRecommendationEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AcceptGroupingRecommendationEntry', ], 'max' => 30, 'min' => 1, ], 'AcceptGroupingRecommendationEntry' => [ 'type' => 'structure', 'required' => [ 'groupingRecommendationId', ], 'members' => [ 'groupingRecommendationId' => [ 'shape' => 'String255', ], ], ], 'AcceptResourceGroupingRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'entries', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'entries' => [ 'shape' => 'AcceptGroupingRecommendationEntries', ], ], ], 'AcceptResourceGroupingRecommendationsResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'failedEntries', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'failedEntries' => [ 'shape' => 'FailedGroupingRecommendationEntries', ], ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String500', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AddDraftAppVersionResourceMappingsRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'resourceMappings', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'resourceMappings' => [ 'shape' => 'ResourceMappingList', ], ], ], 'AddDraftAppVersionResourceMappingsResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', 'resourceMappings', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'resourceMappings' => [ 'shape' => 'ResourceMappingList', ], ], ], 'AdditionalInfoMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String128WithoutWhitespace', ], 'value' => [ 'shape' => 'AdditionalInfoValueList', ], ], 'AdditionalInfoValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String1024', ], 'max' => 10, 'min' => 1, ], 'Alarm' => [ 'type' => 'structure', 'members' => [ 'alarmArn' => [ 'shape' => 'Arn', ], 'source' => [ 'shape' => 'String255', ], ], ], 'AlarmRecommendation' => [ 'type' => 'structure', 'required' => [ 'name', 'recommendationId', 'referenceId', 'type', ], 'members' => [ 'appComponentName' => [ 'shape' => 'EntityId', 'deprecated' => true, 'deprecatedMessage' => 'An alarm recommendation can be attached to multiple Application Components, hence this property will be replaced by the new property \'appComponentNames\'.', ], 'appComponentNames' => [ 'shape' => 'AppComponentNameList', ], 'description' => [ 'shape' => 'EntityDescription', ], 'items' => [ 'shape' => 'RecommendationItemList', ], 'name' => [ 'shape' => 'String500', ], 'prerequisite' => [ 'shape' => 'String500', ], 'recommendationId' => [ 'shape' => 'Uuid', ], 'recommendationStatus' => [ 'shape' => 'RecommendationStatus', ], 'referenceId' => [ 'shape' => 'SpecReferenceId', ], 'type' => [ 'shape' => 'AlarmType', ], ], ], 'AlarmRecommendationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AlarmRecommendation', ], ], 'AlarmReferenceIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String500', ], 'max' => 200, 'min' => 1, ], 'AlarmType' => [ 'type' => 'string', 'enum' => [ 'Metric', 'Composite', 'Canary', 'Logs', 'Event', ], ], 'App' => [ 'type' => 'structure', 'required' => [ 'appArn', 'creationTime', 'name', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'assessmentSchedule' => [ 'shape' => 'AppAssessmentScheduleType', ], 'awsApplicationArn' => [ 'shape' => 'Arn', ], 'complianceStatus' => [ 'shape' => 'AppComplianceStatusType', ], 'creationTime' => [ 'shape' => 'TimeStamp', ], 'description' => [ 'shape' => 'EntityDescription', ], 'driftStatus' => [ 'shape' => 'AppDriftStatusType', ], 'eventSubscriptions' => [ 'shape' => 'EventSubscriptionList', ], 'lastAppComplianceEvaluationTime' => [ 'shape' => 'TimeStamp', ], 'lastDriftEvaluationTime' => [ 'shape' => 'TimeStamp', ], 'lastResiliencyScoreEvaluationTime' => [ 'shape' => 'TimeStamp', ], 'name' => [ 'shape' => 'EntityName', ], 'permissionModel' => [ 'shape' => 'PermissionModel', ], 'policyArn' => [ 'shape' => 'Arn', ], 'resiliencyScore' => [ 'shape' => 'Double', ], 'rpoInSecs' => [ 'shape' => 'IntegerOptional', ], 'rtoInSecs' => [ 'shape' => 'IntegerOptional', ], 'status' => [ 'shape' => 'AppStatusType', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'AppAssessment' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', 'assessmentStatus', 'invoker', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'assessmentArn' => [ 'shape' => 'Arn', ], 'assessmentName' => [ 'shape' => 'EntityName', ], 'assessmentStatus' => [ 'shape' => 'AssessmentStatus', ], 'compliance' => [ 'shape' => 'AssessmentCompliance', ], 'complianceStatus' => [ 'shape' => 'ComplianceStatus', ], 'cost' => [ 'shape' => 'Cost', ], 'driftStatus' => [ 'shape' => 'DriftStatus', ], 'endTime' => [ 'shape' => 'TimeStamp', ], 'invoker' => [ 'shape' => 'AssessmentInvoker', ], 'message' => [ 'shape' => 'String500', ], 'policy' => [ 'shape' => 'ResiliencyPolicy', ], 'resiliencyScore' => [ 'shape' => 'ResiliencyScore', ], 'resourceErrorsDetails' => [ 'shape' => 'ResourceErrorsDetails', ], 'startTime' => [ 'shape' => 'TimeStamp', ], 'summary' => [ 'shape' => 'AssessmentSummary', ], 'tags' => [ 'shape' => 'TagMap', ], 'versionName' => [ 'shape' => 'EntityVersion', ], ], ], 'AppAssessmentScheduleType' => [ 'type' => 'string', 'enum' => [ 'Disabled', 'Daily', ], ], 'AppAssessmentSummary' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', 'assessmentStatus', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'assessmentArn' => [ 'shape' => 'Arn', ], 'assessmentName' => [ 'shape' => 'EntityName', ], 'assessmentStatus' => [ 'shape' => 'AssessmentStatus', ], 'complianceStatus' => [ 'shape' => 'ComplianceStatus', ], 'cost' => [ 'shape' => 'Cost', ], 'driftStatus' => [ 'shape' => 'DriftStatus', ], 'endTime' => [ 'shape' => 'TimeStamp', ], 'invoker' => [ 'shape' => 'AssessmentInvoker', ], 'message' => [ 'shape' => 'String500', ], 'resiliencyScore' => [ 'shape' => 'Double', ], 'startTime' => [ 'shape' => 'TimeStamp', ], 'versionName' => [ 'shape' => 'EntityVersion', ], ], ], 'AppAssessmentSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppAssessmentSummary', ], ], 'AppComplianceStatusType' => [ 'type' => 'string', 'enum' => [ 'PolicyBreached', 'PolicyMet', 'NotAssessed', 'ChangesDetected', 'NotApplicable', 'MissingPolicy', ], ], 'AppComponent' => [ 'type' => 'structure', 'required' => [ 'name', 'type', ], 'members' => [ 'additionalInfo' => [ 'shape' => 'AdditionalInfoMap', ], 'id' => [ 'shape' => 'EntityName255', ], 'name' => [ 'shape' => 'EntityName255', ], 'type' => [ 'shape' => 'String255', ], ], ], 'AppComponentCompliance' => [ 'type' => 'structure', 'members' => [ 'appComponentName' => [ 'shape' => 'EntityId', ], 'compliance' => [ 'shape' => 'AssessmentCompliance', ], 'cost' => [ 'shape' => 'Cost', ], 'message' => [ 'shape' => 'String500', ], 'resiliencyScore' => [ 'shape' => 'ResiliencyScore', ], 'status' => [ 'shape' => 'ComplianceStatus', ], ], ], 'AppComponentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppComponent', ], ], 'AppComponentNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String255', ], ], 'AppDriftStatusType' => [ 'type' => 'string', 'enum' => [ 'NotChecked', 'NotDetected', 'Detected', ], ], 'AppInputSource' => [ 'type' => 'structure', 'required' => [ 'importType', ], 'members' => [ 'eksSourceClusterNamespace' => [ 'shape' => 'EksSourceClusterNamespace', ], 'importType' => [ 'shape' => 'ResourceMappingType', ], 'resourceCount' => [ 'shape' => 'Integer', ], 'sourceArn' => [ 'shape' => 'Arn', ], 'sourceName' => [ 'shape' => 'String255', ], 'terraformSource' => [ 'shape' => 'TerraformSource', ], ], ], 'AppInputSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppInputSource', ], ], 'AppStatusType' => [ 'type' => 'string', 'enum' => [ 'Active', 'Deleting', ], ], 'AppSummary' => [ 'type' => 'structure', 'required' => [ 'appArn', 'creationTime', 'name', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'assessmentSchedule' => [ 'shape' => 'AppAssessmentScheduleType', ], 'awsApplicationArn' => [ 'shape' => 'Arn', ], 'complianceStatus' => [ 'shape' => 'AppComplianceStatusType', ], 'creationTime' => [ 'shape' => 'TimeStamp', ], 'description' => [ 'shape' => 'EntityDescription', ], 'driftStatus' => [ 'shape' => 'AppDriftStatusType', ], 'lastAppComplianceEvaluationTime' => [ 'shape' => 'TimeStamp', ], 'name' => [ 'shape' => 'EntityName', ], 'resiliencyScore' => [ 'shape' => 'Double', ], 'rpoInSecs' => [ 'shape' => 'IntegerOptional', ], 'rtoInSecs' => [ 'shape' => 'IntegerOptional', ], 'status' => [ 'shape' => 'AppStatusType', ], ], ], 'AppSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppSummary', ], ], 'AppTemplateBody' => [ 'type' => 'string', 'max' => 409600, 'min' => 0, 'pattern' => '^[\\w\\s:,-\\.\'\\/{}\\[\\]:"\\\\]+$', ], 'AppVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppVersionSummary', ], ], 'AppVersionSummary' => [ 'type' => 'structure', 'required' => [ 'appVersion', ], 'members' => [ 'appVersion' => [ 'shape' => 'EntityVersion', ], 'creationTime' => [ 'shape' => 'TimeStamp', ], 'identifier' => [ 'shape' => 'LongOptional', ], 'versionName' => [ 'shape' => 'EntityVersion', ], ], ], 'Arn' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-cn|aws-iso|aws-iso-[a-z]{1}|aws-us-gov):[A-Za-z0-9][A-Za-z0-9_/.-]{0,62}:([a-z]{2}-((iso[a-z]{0,1}-)|(gov-)){0,1}[a-z]+-[0-9]):[0-9]{12}:[A-Za-z0-9/][A-Za-z0-9:_/+.-]{0,1023}$', ], 'ArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], ], 'AssessmentCompliance' => [ 'type' => 'map', 'key' => [ 'shape' => 'DisruptionType', ], 'value' => [ 'shape' => 'DisruptionCompliance', ], ], 'AssessmentInvoker' => [ 'type' => 'string', 'enum' => [ 'User', 'System', ], ], 'AssessmentRiskRecommendation' => [ 'type' => 'structure', 'members' => [ 'appComponents' => [ 'shape' => 'AppComponentNameList', ], 'recommendation' => [ 'shape' => 'String255', ], 'risk' => [ 'shape' => 'String255', ], ], ], 'AssessmentRiskRecommendationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssessmentRiskRecommendation', ], ], 'AssessmentStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'InProgress', 'Failed', 'Success', ], ], 'AssessmentStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssessmentStatus', ], 'max' => 10, 'min' => 1, ], 'AssessmentSummary' => [ 'type' => 'structure', 'members' => [ 'riskRecommendations' => [ 'shape' => 'AssessmentRiskRecommendationList', ], 'summary' => [ 'shape' => 'String500', ], ], ], 'AwsRegion' => [ 'type' => 'string', 'pattern' => '^[a-z]{2}-((iso[a-z]{0,1}-)|(gov-)){0,1}[a-z]+-[0-9]$', ], 'BatchUpdateRecommendationStatusFailedEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchUpdateRecommendationStatusFailedEntry', ], ], 'BatchUpdateRecommendationStatusFailedEntry' => [ 'type' => 'structure', 'required' => [ 'entryId', 'errorMessage', ], 'members' => [ 'entryId' => [ 'shape' => 'String255', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'BatchUpdateRecommendationStatusRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'requestEntries', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'requestEntries' => [ 'shape' => 'UpdateRecommendationStatusRequestEntries', ], ], ], 'BatchUpdateRecommendationStatusResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'failedEntries', 'successfulEntries', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'failedEntries' => [ 'shape' => 'BatchUpdateRecommendationStatusFailedEntries', ], 'successfulEntries' => [ 'shape' => 'BatchUpdateRecommendationStatusSuccessfulEntries', ], ], ], 'BatchUpdateRecommendationStatusSuccessfulEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchUpdateRecommendationStatusSuccessfulEntry', ], ], 'BatchUpdateRecommendationStatusSuccessfulEntry' => [ 'type' => 'structure', 'required' => [ 'entryId', 'excluded', 'referenceId', ], 'members' => [ 'appComponentId' => [ 'shape' => 'EntityName255', ], 'entryId' => [ 'shape' => 'String255', ], 'excludeReason' => [ 'shape' => 'ExcludeRecommendationReason', ], 'excluded' => [ 'shape' => 'BooleanOptional', ], 'item' => [ 'shape' => 'UpdateRecommendationStatusItem', ], 'referenceId' => [ 'shape' => 'SpecReferenceId', ], ], ], 'BooleanOptional' => [ 'type' => 'boolean', 'box' => true, ], 'ClientToken' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[A-Za-z0-9_.-]{0,63}$', ], 'ComplianceDrift' => [ 'type' => 'structure', 'members' => [ 'actualReferenceId' => [ 'shape' => 'String255', ], 'actualValue' => [ 'shape' => 'AssessmentCompliance', ], 'appId' => [ 'shape' => 'String255', ], 'appVersion' => [ 'shape' => 'String255', ], 'diffType' => [ 'shape' => 'DifferenceType', ], 'driftType' => [ 'shape' => 'DriftType', ], 'entityId' => [ 'shape' => 'String255', ], 'entityType' => [ 'shape' => 'String255', ], 'expectedReferenceId' => [ 'shape' => 'String255', ], 'expectedValue' => [ 'shape' => 'AssessmentCompliance', ], ], ], 'ComplianceDriftList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComplianceDrift', ], ], 'ComplianceStatus' => [ 'type' => 'string', 'enum' => [ 'PolicyBreached', 'PolicyMet', 'NotApplicable', 'MissingPolicy', ], ], 'ComponentCompliancesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppComponentCompliance', ], ], 'ComponentRecommendation' => [ 'type' => 'structure', 'required' => [ 'appComponentName', 'configRecommendations', 'recommendationStatus', ], 'members' => [ 'appComponentName' => [ 'shape' => 'EntityId', ], 'configRecommendations' => [ 'shape' => 'ConfigRecommendationList', ], 'recommendationStatus' => [ 'shape' => 'RecommendationComplianceStatus', ], ], ], 'ComponentRecommendationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentRecommendation', ], ], 'Condition' => [ 'type' => 'structure', 'required' => [ 'field', 'operator', ], 'members' => [ 'field' => [ 'shape' => 'String255', ], 'operator' => [ 'shape' => 'ConditionOperatorType', ], 'value' => [ 'shape' => 'String255', ], ], ], 'ConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Condition', ], 'max' => 50, 'min' => 0, ], 'ConditionOperatorType' => [ 'type' => 'string', 'enum' => [ 'Equals', 'NotEquals', 'GreaterThen', 'GreaterOrEquals', 'LessThen', 'LessOrEquals', ], ], 'ConfigRecommendation' => [ 'type' => 'structure', 'required' => [ 'name', 'optimizationType', 'referenceId', ], 'members' => [ 'appComponentName' => [ 'shape' => 'EntityId', ], 'compliance' => [ 'shape' => 'AssessmentCompliance', ], 'cost' => [ 'shape' => 'Cost', ], 'description' => [ 'shape' => 'EntityDescription', ], 'haArchitecture' => [ 'shape' => 'HaArchitecture', ], 'name' => [ 'shape' => 'EntityName', ], 'optimizationType' => [ 'shape' => 'ConfigRecommendationOptimizationType', ], 'recommendationCompliance' => [ 'shape' => 'RecommendationCompliance', ], 'referenceId' => [ 'shape' => 'SpecReferenceId', ], 'suggestedChanges' => [ 'shape' => 'SuggestedChangesList', ], ], ], 'ConfigRecommendationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigRecommendation', ], ], 'ConfigRecommendationOptimizationType' => [ 'type' => 'string', 'enum' => [ 'LeastCost', 'LeastChange', 'BestAZRecovery', 'LeastErrors', 'BestAttainable', 'BestRegionRecovery', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String500', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'Cost' => [ 'type' => 'structure', 'required' => [ 'amount', 'currency', 'frequency', ], 'members' => [ 'amount' => [ 'shape' => 'Double', ], 'currency' => [ 'shape' => 'CurrencyCode', ], 'frequency' => [ 'shape' => 'CostFrequency', ], ], ], 'CostFrequency' => [ 'type' => 'string', 'enum' => [ 'Hourly', 'Daily', 'Monthly', 'Yearly', ], ], 'CreateAppRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'assessmentSchedule' => [ 'shape' => 'AppAssessmentScheduleType', ], 'awsApplicationArn' => [ 'shape' => 'Arn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'EntityDescription', ], 'eventSubscriptions' => [ 'shape' => 'EventSubscriptionList', ], 'name' => [ 'shape' => 'EntityName', ], 'permissionModel' => [ 'shape' => 'PermissionModel', ], 'policyArn' => [ 'shape' => 'Arn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateAppResponse' => [ 'type' => 'structure', 'required' => [ 'app', ], 'members' => [ 'app' => [ 'shape' => 'App', ], ], ], 'CreateAppVersionAppComponentRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'name', 'type', ], 'members' => [ 'additionalInfo' => [ 'shape' => 'AdditionalInfoMap', ], 'appArn' => [ 'shape' => 'Arn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'id' => [ 'shape' => 'String255', ], 'name' => [ 'shape' => 'String255', ], 'type' => [ 'shape' => 'String255', ], ], ], 'CreateAppVersionAppComponentResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appComponent' => [ 'shape' => 'AppComponent', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], ], ], 'CreateAppVersionResourceRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appComponents', 'logicalResourceId', 'physicalResourceId', 'resourceType', ], 'members' => [ 'additionalInfo' => [ 'shape' => 'AdditionalInfoMap', ], 'appArn' => [ 'shape' => 'Arn', ], 'appComponents' => [ 'shape' => 'AppComponentNameList', ], 'awsAccountId' => [ 'shape' => 'CustomerId', ], 'awsRegion' => [ 'shape' => 'AwsRegion', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'logicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'physicalResourceId' => [ 'shape' => 'String2048', ], 'resourceName' => [ 'shape' => 'EntityName', ], 'resourceType' => [ 'shape' => 'String255', ], ], ], 'CreateAppVersionResourceResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'physicalResource' => [ 'shape' => 'PhysicalResource', ], ], ], 'CreateRecommendationTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', 'name', ], 'members' => [ 'assessmentArn' => [ 'shape' => 'Arn', ], 'bucketName' => [ 'shape' => 'EntityName', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'format' => [ 'shape' => 'TemplateFormat', ], 'name' => [ 'shape' => 'EntityName', ], 'recommendationIds' => [ 'shape' => 'RecommendationIdList', ], 'recommendationTypes' => [ 'shape' => 'RenderRecommendationTypeList', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateRecommendationTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'recommendationTemplate' => [ 'shape' => 'RecommendationTemplate', ], ], ], 'CreateResiliencyPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policy', 'policyName', 'tier', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'dataLocationConstraint' => [ 'shape' => 'DataLocationConstraint', ], 'policy' => [ 'shape' => 'DisruptionPolicy', ], 'policyDescription' => [ 'shape' => 'EntityDescription', ], 'policyName' => [ 'shape' => 'EntityName', ], 'tags' => [ 'shape' => 'TagMap', ], 'tier' => [ 'shape' => 'ResiliencyPolicyTier', ], ], ], 'CreateResiliencyPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'policy', ], 'members' => [ 'policy' => [ 'shape' => 'ResiliencyPolicy', ], ], ], 'CurrencyCode' => [ 'type' => 'string', 'max' => 3, 'min' => 0, ], 'CustomerId' => [ 'type' => 'string', 'pattern' => '^[0-9]{12}$', ], 'DataLocationConstraint' => [ 'type' => 'string', 'enum' => [ 'AnyLocation', 'SameContinent', 'SameCountry', ], ], 'DeleteAppAssessmentRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', ], 'members' => [ 'assessmentArn' => [ 'shape' => 'Arn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'DeleteAppAssessmentResponse' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', 'assessmentStatus', ], 'members' => [ 'assessmentArn' => [ 'shape' => 'Arn', ], 'assessmentStatus' => [ 'shape' => 'AssessmentStatus', ], ], ], 'DeleteAppInputSourceRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'eksSourceClusterNamespace' => [ 'shape' => 'EksSourceClusterNamespace', ], 'sourceArn' => [ 'shape' => 'Arn', ], 'terraformSource' => [ 'shape' => 'TerraformSource', ], ], ], 'DeleteAppInputSourceResponse' => [ 'type' => 'structure', 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appInputSource' => [ 'shape' => 'AppInputSource', ], ], ], 'DeleteAppRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'forceDelete' => [ 'shape' => 'BooleanOptional', ], ], ], 'DeleteAppResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteAppVersionAppComponentRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'id', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'id' => [ 'shape' => 'String255', ], ], ], 'DeleteAppVersionAppComponentResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appComponent' => [ 'shape' => 'AppComponent', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], ], ], 'DeleteAppVersionResourceRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'awsAccountId' => [ 'shape' => 'CustomerId', ], 'awsRegion' => [ 'shape' => 'AwsRegion', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'logicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'physicalResourceId' => [ 'shape' => 'String2048', ], 'resourceName' => [ 'shape' => 'EntityName', ], ], ], 'DeleteAppVersionResourceResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'physicalResource' => [ 'shape' => 'PhysicalResource', ], ], ], 'DeleteRecommendationTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'recommendationTemplateArn', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'recommendationTemplateArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteRecommendationTemplateResponse' => [ 'type' => 'structure', 'required' => [ 'recommendationTemplateArn', 'status', ], 'members' => [ 'recommendationTemplateArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'RecommendationTemplateStatus', ], ], ], 'DeleteResiliencyPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyArn', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'policyArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteResiliencyPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'policyArn', ], 'members' => [ 'policyArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeAppAssessmentRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', ], 'members' => [ 'assessmentArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeAppAssessmentResponse' => [ 'type' => 'structure', 'required' => [ 'assessment', ], 'members' => [ 'assessment' => [ 'shape' => 'AppAssessment', ], ], ], 'DescribeAppRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeAppResponse' => [ 'type' => 'structure', 'required' => [ 'app', ], 'members' => [ 'app' => [ 'shape' => 'App', ], ], ], 'DescribeAppVersionAppComponentRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', 'id', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'id' => [ 'shape' => 'String255', ], ], ], 'DescribeAppVersionAppComponentResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appComponent' => [ 'shape' => 'AppComponent', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], ], ], 'DescribeAppVersionRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], ], ], 'DescribeAppVersionResourceRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'awsAccountId' => [ 'shape' => 'CustomerId', ], 'awsRegion' => [ 'shape' => 'AwsRegion', ], 'logicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'physicalResourceId' => [ 'shape' => 'String2048', ], 'resourceName' => [ 'shape' => 'EntityName', ], ], ], 'DescribeAppVersionResourceResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'physicalResource' => [ 'shape' => 'PhysicalResource', ], ], ], 'DescribeAppVersionResourcesResolutionStatusRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'resolutionId' => [ 'shape' => 'String255', ], ], ], 'DescribeAppVersionResourcesResolutionStatusResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', 'resolutionId', 'status', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'errorMessage' => [ 'shape' => 'String500', ], 'resolutionId' => [ 'shape' => 'String255', ], 'status' => [ 'shape' => 'ResourceResolutionStatusType', ], ], ], 'DescribeAppVersionResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'additionalInfo' => [ 'shape' => 'AdditionalInfoMap', ], 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], ], ], 'DescribeAppVersionTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], ], ], 'DescribeAppVersionTemplateResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appTemplateBody', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appTemplateBody' => [ 'shape' => 'AppTemplateBody', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], ], ], 'DescribeDraftAppVersionResourcesImportStatusRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeDraftAppVersionResourcesImportStatusResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', 'status', 'statusChangeTime', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'errorDetails' => [ 'shape' => 'ErrorDetailList', ], 'errorMessage' => [ 'shape' => 'String500', ], 'status' => [ 'shape' => 'ResourceImportStatusType', ], 'statusChangeTime' => [ 'shape' => 'TimeStamp', ], ], ], 'DescribeMetricsExportRequest' => [ 'type' => 'structure', 'required' => [ 'metricsExportId', ], 'members' => [ 'metricsExportId' => [ 'shape' => 'String255', ], ], ], 'DescribeMetricsExportResponse' => [ 'type' => 'structure', 'required' => [ 'metricsExportId', 'status', ], 'members' => [ 'errorMessage' => [ 'shape' => 'String500', ], 'exportLocation' => [ 'shape' => 'S3Location', ], 'metricsExportId' => [ 'shape' => 'String255', ], 'status' => [ 'shape' => 'MetricsExportStatusType', ], ], ], 'DescribeResiliencyPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyArn', ], 'members' => [ 'policyArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeResiliencyPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'policy', ], 'members' => [ 'policy' => [ 'shape' => 'ResiliencyPolicy', ], ], ], 'DescribeResourceGroupingRecommendationTaskRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'groupingId' => [ 'shape' => 'String255', ], ], ], 'DescribeResourceGroupingRecommendationTaskResponse' => [ 'type' => 'structure', 'required' => [ 'groupingId', 'status', ], 'members' => [ 'errorMessage' => [ 'shape' => 'String500', ], 'groupingId' => [ 'shape' => 'String255', ], 'status' => [ 'shape' => 'ResourcesGroupingRecGenStatusType', ], ], ], 'DifferenceType' => [ 'type' => 'string', 'enum' => [ 'NotEqual', 'Added', 'Removed', ], ], 'DisruptionCompliance' => [ 'type' => 'structure', 'required' => [ 'complianceStatus', ], 'members' => [ 'achievableRpoInSecs' => [ 'shape' => 'Seconds', ], 'achievableRtoInSecs' => [ 'shape' => 'Seconds', ], 'complianceStatus' => [ 'shape' => 'ComplianceStatus', ], 'currentRpoInSecs' => [ 'shape' => 'Seconds', ], 'currentRtoInSecs' => [ 'shape' => 'Seconds', ], 'message' => [ 'shape' => 'String500', ], 'rpoDescription' => [ 'shape' => 'String500', ], 'rpoReferenceId' => [ 'shape' => 'String500', ], 'rtoDescription' => [ 'shape' => 'String500', ], 'rtoReferenceId' => [ 'shape' => 'String500', ], ], ], 'DisruptionPolicy' => [ 'type' => 'map', 'key' => [ 'shape' => 'DisruptionType', ], 'value' => [ 'shape' => 'FailurePolicy', ], ], 'DisruptionResiliencyScore' => [ 'type' => 'map', 'key' => [ 'shape' => 'DisruptionType', ], 'value' => [ 'shape' => 'Double', ], ], 'DisruptionType' => [ 'type' => 'string', 'enum' => [ 'Software', 'Hardware', 'AZ', 'Region', ], ], 'DocumentName' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'Double' => [ 'type' => 'double', ], 'DriftStatus' => [ 'type' => 'string', 'enum' => [ 'NotChecked', 'NotDetected', 'Detected', ], ], 'DriftType' => [ 'type' => 'string', 'enum' => [ 'ApplicationCompliance', 'AppComponentResiliencyComplianceStatus', ], ], 'EksNamespace' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-z0-9]([-a-z0-9]*[a-z0-9])?$', ], 'EksNamespaceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EksNamespace', ], ], 'EksSource' => [ 'type' => 'structure', 'required' => [ 'eksClusterArn', 'namespaces', ], 'members' => [ 'eksClusterArn' => [ 'shape' => 'Arn', ], 'namespaces' => [ 'shape' => 'EksNamespaceList', ], ], ], 'EksSourceClusterNamespace' => [ 'type' => 'structure', 'required' => [ 'eksClusterArn', 'namespace', ], 'members' => [ 'eksClusterArn' => [ 'shape' => 'Arn', ], 'namespace' => [ 'shape' => 'EksNamespace', ], ], ], 'EksSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EksSource', ], ], 'EntityDescription' => [ 'type' => 'string', 'max' => 500, 'min' => 0, ], 'EntityId' => [ 'type' => 'string', 'pattern' => '^\\S{1,255}$', ], 'EntityName' => [ 'type' => 'string', 'pattern' => '^[A-Za-z0-9][A-Za-z0-9_\\-]{1,59}$', ], 'EntityName255' => [ 'type' => 'string', 'pattern' => '^[A-Za-z0-9][A-Za-z0-9_\\-]{0,254}$', ], 'EntityNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntityName', ], ], 'EntityVersion' => [ 'type' => 'string', 'pattern' => '^\\S{1,50}$', ], 'ErrorDetail' => [ 'type' => 'structure', 'members' => [ 'errorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'ErrorDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ErrorDetail', ], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 500, 'min' => 0, ], 'EstimatedCostTier' => [ 'type' => 'string', 'enum' => [ 'L1', 'L2', 'L3', 'L4', ], ], 'EventSubscription' => [ 'type' => 'structure', 'required' => [ 'eventType', 'name', ], 'members' => [ 'eventType' => [ 'shape' => 'EventType', ], 'name' => [ 'shape' => 'String255', ], 'snsTopicArn' => [ 'shape' => 'Arn', ], ], ], 'EventSubscriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventSubscription', ], 'max' => 10, 'min' => 0, ], 'EventType' => [ 'type' => 'string', 'enum' => [ 'ScheduledAssessmentFailure', 'DriftDetected', ], ], 'ExcludeRecommendationReason' => [ 'type' => 'string', 'enum' => [ 'AlreadyImplemented', 'NotRelevant', 'ComplexityOfImplementation', ], ], 'Experiment' => [ 'type' => 'structure', 'members' => [ 'experimentArn' => [ 'shape' => 'String255', ], 'experimentTemplateId' => [ 'shape' => 'String255', ], ], ], 'FailedGroupingRecommendationEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedGroupingRecommendationEntry', ], ], 'FailedGroupingRecommendationEntry' => [ 'type' => 'structure', 'required' => [ 'errorMessage', 'groupingRecommendationId', ], 'members' => [ 'errorMessage' => [ 'shape' => 'ErrorMessage', ], 'groupingRecommendationId' => [ 'shape' => 'String255', ], ], ], 'FailurePolicy' => [ 'type' => 'structure', 'required' => [ 'rpoInSecs', 'rtoInSecs', ], 'members' => [ 'rpoInSecs' => [ 'shape' => 'Seconds', ], 'rtoInSecs' => [ 'shape' => 'Seconds', ], ], ], 'Field' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'aggregation' => [ 'shape' => 'FieldAggregationType', ], 'name' => [ 'shape' => 'String255', ], ], ], 'FieldAggregationType' => [ 'type' => 'string', 'enum' => [ 'Min', 'Max', 'Sum', 'Avg', 'Count', ], ], 'FieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Field', ], 'max' => 50, 'min' => 0, ], 'GroupingAppComponent' => [ 'type' => 'structure', 'required' => [ 'appComponentId', 'appComponentName', 'appComponentType', ], 'members' => [ 'appComponentId' => [ 'shape' => 'EntityName255', ], 'appComponentName' => [ 'shape' => 'EntityName255', ], 'appComponentType' => [ 'shape' => 'String255', ], ], ], 'GroupingRecommendation' => [ 'type' => 'structure', 'required' => [ 'confidenceLevel', 'creationTime', 'groupingAppComponent', 'groupingRecommendationId', 'recommendationReasons', 'resources', 'score', 'status', ], 'members' => [ 'confidenceLevel' => [ 'shape' => 'GroupingRecommendationConfidenceLevel', ], 'creationTime' => [ 'shape' => 'TimeStamp', ], 'groupingAppComponent' => [ 'shape' => 'GroupingAppComponent', ], 'groupingRecommendationId' => [ 'shape' => 'String255', ], 'recommendationReasons' => [ 'shape' => 'String255List', ], 'rejectionReason' => [ 'shape' => 'GroupingRecommendationRejectionReason', ], 'resources' => [ 'shape' => 'GroupingResourceList', ], 'score' => [ 'shape' => 'Double', ], 'status' => [ 'shape' => 'GroupingRecommendationStatusType', ], ], ], 'GroupingRecommendationConfidenceLevel' => [ 'type' => 'string', 'enum' => [ 'High', 'Medium', ], ], 'GroupingRecommendationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupingRecommendation', ], ], 'GroupingRecommendationRejectionReason' => [ 'type' => 'string', 'enum' => [ 'DistinctBusinessPurpose', 'SeparateDataConcern', 'DistinctUserGroupHandling', 'Other', ], ], 'GroupingRecommendationStatusType' => [ 'type' => 'string', 'enum' => [ 'Accepted', 'Rejected', 'PendingDecision', ], ], 'GroupingResource' => [ 'type' => 'structure', 'required' => [ 'logicalResourceId', 'physicalResourceId', 'resourceName', 'resourceType', 'sourceAppComponentIds', ], 'members' => [ 'logicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'physicalResourceId' => [ 'shape' => 'PhysicalResourceId', ], 'resourceName' => [ 'shape' => 'String255', ], 'resourceType' => [ 'shape' => 'String255', ], 'sourceAppComponentIds' => [ 'shape' => 'String255List', ], ], ], 'GroupingResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupingResource', ], ], 'HaArchitecture' => [ 'type' => 'string', 'enum' => [ 'MultiSite', 'WarmStandby', 'PilotLight', 'BackupAndRestore', 'NoRecoveryPlan', ], ], 'IamRoleArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-cn|aws-iso|aws-iso-[a-z]{1}|aws-us-gov):iam::[0-9]{12}:role/(([^/][!-~]+/){1,511})?[A-Za-z0-9_+=,.@-]{1,64}$', ], 'IamRoleArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IamRoleArn', ], 'max' => 10, 'min' => 0, ], 'IamRoleName' => [ 'type' => 'string', 'pattern' => '^([^/]([!-~]+/){1,511})?[A-Za-z0-9_+=,.@-]{1,64}$', ], 'ImportResourcesToDraftAppVersionRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'eksSources' => [ 'shape' => 'EksSourceList', ], 'importStrategy' => [ 'shape' => 'ResourceImportStrategyType', ], 'sourceArns' => [ 'shape' => 'ArnList', ], 'terraformSources' => [ 'shape' => 'TerraformSourceList', ], ], ], 'ImportResourcesToDraftAppVersionResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', 'status', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'eksSources' => [ 'shape' => 'EksSourceList', ], 'sourceArns' => [ 'shape' => 'ArnList', ], 'status' => [ 'shape' => 'ResourceImportStatusType', ], 'terraformSources' => [ 'shape' => 'TerraformSourceList', ], ], ], 'Integer' => [ 'type' => 'integer', ], 'IntegerOptional' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String500', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ListAlarmRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', ], 'members' => [ 'assessmentArn' => [ 'shape' => 'Arn', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAlarmRecommendationsResponse' => [ 'type' => 'structure', 'required' => [ 'alarmRecommendations', ], 'members' => [ 'alarmRecommendations' => [ 'shape' => 'AlarmRecommendationList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppAssessmentComplianceDriftsRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', ], 'members' => [ 'assessmentArn' => [ 'shape' => 'Arn', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppAssessmentComplianceDriftsResponse' => [ 'type' => 'structure', 'required' => [ 'complianceDrifts', ], 'members' => [ 'complianceDrifts' => [ 'shape' => 'ComplianceDriftList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppAssessmentResourceDriftsRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', ], 'members' => [ 'assessmentArn' => [ 'shape' => 'Arn', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppAssessmentResourceDriftsResponse' => [ 'type' => 'structure', 'required' => [ 'resourceDrifts', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'resourceDrifts' => [ 'shape' => 'ResourceDriftList', ], ], ], 'ListAppAssessmentsRequest' => [ 'type' => 'structure', 'members' => [ 'appArn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'appArn', ], 'assessmentName' => [ 'shape' => 'EntityName', 'location' => 'querystring', 'locationName' => 'assessmentName', ], 'assessmentStatus' => [ 'shape' => 'AssessmentStatusList', 'location' => 'querystring', 'locationName' => 'assessmentStatus', ], 'complianceStatus' => [ 'shape' => 'ComplianceStatus', 'location' => 'querystring', 'locationName' => 'complianceStatus', ], 'invoker' => [ 'shape' => 'AssessmentInvoker', 'location' => 'querystring', 'locationName' => 'invoker', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'reverseOrder' => [ 'shape' => 'BooleanOptional', 'location' => 'querystring', 'locationName' => 'reverseOrder', ], ], ], 'ListAppAssessmentsResponse' => [ 'type' => 'structure', 'required' => [ 'assessmentSummaries', ], 'members' => [ 'assessmentSummaries' => [ 'shape' => 'AppAssessmentSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppComponentCompliancesRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', ], 'members' => [ 'assessmentArn' => [ 'shape' => 'Arn', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppComponentCompliancesResponse' => [ 'type' => 'structure', 'required' => [ 'componentCompliances', ], 'members' => [ 'componentCompliances' => [ 'shape' => 'ComponentCompliancesList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppComponentRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', ], 'members' => [ 'assessmentArn' => [ 'shape' => 'Arn', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppComponentRecommendationsResponse' => [ 'type' => 'structure', 'required' => [ 'componentRecommendations', ], 'members' => [ 'componentRecommendations' => [ 'shape' => 'ComponentRecommendationList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppInputSourcesRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppInputSourcesResponse' => [ 'type' => 'structure', 'required' => [ 'appInputSources', ], 'members' => [ 'appInputSources' => [ 'shape' => 'AppInputSourceList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppVersionAppComponentsRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppVersionAppComponentsResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appComponents' => [ 'shape' => 'AppComponentList', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppVersionResourceMappingsRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppVersionResourceMappingsResponse' => [ 'type' => 'structure', 'required' => [ 'resourceMappings', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'resourceMappings' => [ 'shape' => 'ResourceMappingList', ], ], ], 'ListAppVersionResourcesRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'resolutionId' => [ 'shape' => 'String255', ], ], ], 'ListAppVersionResourcesResponse' => [ 'type' => 'structure', 'required' => [ 'physicalResources', 'resolutionId', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'physicalResources' => [ 'shape' => 'PhysicalResourceList', ], 'resolutionId' => [ 'shape' => 'String255', ], ], ], 'ListAppVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'endTime' => [ 'shape' => 'TimeStamp', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'startTime' => [ 'shape' => 'TimeStamp', ], ], ], 'ListAppVersionsResponse' => [ 'type' => 'structure', 'required' => [ 'appVersions', ], 'members' => [ 'appVersions' => [ 'shape' => 'AppVersionList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppsRequest' => [ 'type' => 'structure', 'members' => [ 'appArn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'appArn', ], 'awsApplicationArn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'awsApplicationArn', ], 'fromLastAssessmentTime' => [ 'shape' => 'TimeStamp', 'location' => 'querystring', 'locationName' => 'fromLastAssessmentTime', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'name' => [ 'shape' => 'EntityName', 'location' => 'querystring', 'locationName' => 'name', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'reverseOrder' => [ 'shape' => 'BooleanOptional', 'location' => 'querystring', 'locationName' => 'reverseOrder', ], 'toLastAssessmentTime' => [ 'shape' => 'TimeStamp', 'location' => 'querystring', 'locationName' => 'toLastAssessmentTime', ], ], ], 'ListAppsResponse' => [ 'type' => 'structure', 'required' => [ 'appSummaries', ], 'members' => [ 'appSummaries' => [ 'shape' => 'AppSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMetricsRequest' => [ 'type' => 'structure', 'members' => [ 'conditions' => [ 'shape' => 'ConditionList', ], 'dataSource' => [ 'shape' => 'String255', ], 'fields' => [ 'shape' => 'FieldList', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'sorts' => [ 'shape' => 'SortList', ], ], ], 'ListMetricsResponse' => [ 'type' => 'structure', 'required' => [ 'rows', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'rows' => [ 'shape' => 'RowList', ], ], ], 'ListRecommendationTemplatesRequest' => [ 'type' => 'structure', 'members' => [ 'assessmentArn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'assessmentArn', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'name' => [ 'shape' => 'EntityName', 'location' => 'querystring', 'locationName' => 'name', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'recommendationTemplateArn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'recommendationTemplateArn', ], 'reverseOrder' => [ 'shape' => 'BooleanOptional', 'location' => 'querystring', 'locationName' => 'reverseOrder', ], 'status' => [ 'shape' => 'RecommendationTemplateStatusList', 'location' => 'querystring', 'locationName' => 'status', ], ], ], 'ListRecommendationTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'recommendationTemplates' => [ 'shape' => 'RecommendationTemplateList', ], ], ], 'ListResiliencyPoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'policyName' => [ 'shape' => 'EntityName', 'location' => 'querystring', 'locationName' => 'policyName', ], ], ], 'ListResiliencyPoliciesResponse' => [ 'type' => 'structure', 'required' => [ 'resiliencyPolicies', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'resiliencyPolicies' => [ 'shape' => 'ResiliencyPolicies', ], ], ], 'ListResourceGroupingRecommendationsRequest' => [ 'type' => 'structure', 'members' => [ 'appArn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'appArn', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListResourceGroupingRecommendationsResponse' => [ 'type' => 'structure', 'required' => [ 'groupingRecommendations', ], 'members' => [ 'groupingRecommendations' => [ 'shape' => 'GroupingRecommendationList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSopRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', ], 'members' => [ 'assessmentArn' => [ 'shape' => 'Arn', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSopRecommendationsResponse' => [ 'type' => 'structure', 'required' => [ 'sopRecommendations', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'sopRecommendations' => [ 'shape' => 'SopRecommendationList', ], ], ], 'ListSuggestedResiliencyPoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListSuggestedResiliencyPoliciesResponse' => [ 'type' => 'structure', 'required' => [ 'resiliencyPolicies', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'resiliencyPolicies' => [ 'shape' => 'ResiliencyPolicies', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListTestRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', ], 'members' => [ 'assessmentArn' => [ 'shape' => 'Arn', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTestRecommendationsResponse' => [ 'type' => 'structure', 'required' => [ 'testRecommendations', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'testRecommendations' => [ 'shape' => 'TestRecommendationList', ], ], ], 'ListUnsupportedAppVersionResourcesRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'resolutionId' => [ 'shape' => 'String255', ], ], ], 'ListUnsupportedAppVersionResourcesResponse' => [ 'type' => 'structure', 'required' => [ 'resolutionId', 'unsupportedResources', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'resolutionId' => [ 'shape' => 'String255', ], 'unsupportedResources' => [ 'shape' => 'UnsupportedResourceList', ], ], ], 'LogicalResourceId' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'eksSourceName' => [ 'shape' => 'String255', ], 'identifier' => [ 'shape' => 'String255', ], 'logicalStackName' => [ 'shape' => 'String255', ], 'resourceGroupName' => [ 'shape' => 'EntityName', ], 'terraformSourceName' => [ 'shape' => 'String255', ], ], ], 'Long' => [ 'type' => 'long', ], 'LongOptional' => [ 'type' => 'long', 'box' => true, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MetricsExportStatusType' => [ 'type' => 'string', 'enum' => [ 'Pending', 'InProgress', 'Failed', 'Success', ], ], 'NextToken' => [ 'type' => 'string', 'pattern' => '^\\S{1,2000}$', ], 'PermissionModel' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'crossAccountRoleArns' => [ 'shape' => 'IamRoleArnList', ], 'invokerRoleName' => [ 'shape' => 'IamRoleName', ], 'type' => [ 'shape' => 'PermissionModelType', ], ], ], 'PermissionModelType' => [ 'type' => 'string', 'enum' => [ 'LegacyIAMUser', 'RoleBased', ], ], 'PhysicalIdentifierType' => [ 'type' => 'string', 'enum' => [ 'Arn', 'Native', ], ], 'PhysicalResource' => [ 'type' => 'structure', 'required' => [ 'logicalResourceId', 'physicalResourceId', 'resourceType', ], 'members' => [ 'additionalInfo' => [ 'shape' => 'AdditionalInfoMap', ], 'appComponents' => [ 'shape' => 'AppComponentList', ], 'excluded' => [ 'shape' => 'BooleanOptional', ], 'logicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'parentResourceName' => [ 'shape' => 'EntityName', ], 'physicalResourceId' => [ 'shape' => 'PhysicalResourceId', ], 'resourceName' => [ 'shape' => 'EntityName', ], 'resourceType' => [ 'shape' => 'String255', ], 'sourceType' => [ 'shape' => 'ResourceSourceType', ], ], ], 'PhysicalResourceId' => [ 'type' => 'structure', 'required' => [ 'identifier', 'type', ], 'members' => [ 'awsAccountId' => [ 'shape' => 'CustomerId', ], 'awsRegion' => [ 'shape' => 'AwsRegion', ], 'identifier' => [ 'shape' => 'String255', ], 'type' => [ 'shape' => 'PhysicalIdentifierType', ], ], ], 'PhysicalResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhysicalResource', ], ], 'PublishAppVersionRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'versionName' => [ 'shape' => 'EntityVersion', ], ], ], 'PublishAppVersionResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'identifier' => [ 'shape' => 'LongOptional', ], 'versionName' => [ 'shape' => 'EntityVersion', ], ], ], 'PutDraftAppVersionTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appTemplateBody', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appTemplateBody' => [ 'shape' => 'AppTemplateBody', ], ], ], 'PutDraftAppVersionTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], ], ], 'RecommendationCompliance' => [ 'type' => 'map', 'key' => [ 'shape' => 'DisruptionType', ], 'value' => [ 'shape' => 'RecommendationDisruptionCompliance', ], ], 'RecommendationComplianceStatus' => [ 'type' => 'string', 'enum' => [ 'BreachedUnattainable', 'BreachedCanMeet', 'MetCanImprove', 'MissingPolicy', ], ], 'RecommendationDisruptionCompliance' => [ 'type' => 'structure', 'required' => [ 'expectedComplianceStatus', ], 'members' => [ 'expectedComplianceStatus' => [ 'shape' => 'ComplianceStatus', ], 'expectedRpoDescription' => [ 'shape' => 'String500', ], 'expectedRpoInSecs' => [ 'shape' => 'Seconds', ], 'expectedRtoDescription' => [ 'shape' => 'String500', ], 'expectedRtoInSecs' => [ 'shape' => 'Seconds', ], ], ], 'RecommendationIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Uuid', ], 'max' => 200, 'min' => 1, ], 'RecommendationItem' => [ 'type' => 'structure', 'members' => [ 'alreadyImplemented' => [ 'shape' => 'BooleanOptional', ], 'discoveredAlarm' => [ 'shape' => 'Alarm', ], 'excludeReason' => [ 'shape' => 'ExcludeRecommendationReason', ], 'excluded' => [ 'shape' => 'BooleanOptional', ], 'latestDiscoveredExperiment' => [ 'shape' => 'Experiment', ], 'resourceId' => [ 'shape' => 'String500', ], 'targetAccountId' => [ 'shape' => 'CustomerId', ], 'targetRegion' => [ 'shape' => 'AwsRegion', ], ], ], 'RecommendationItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationItem', ], ], 'RecommendationStatus' => [ 'type' => 'string', 'enum' => [ 'Implemented', 'Inactive', 'NotImplemented', 'Excluded', ], ], 'RecommendationTemplate' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', 'format', 'name', 'recommendationTemplateArn', 'recommendationTypes', 'status', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'assessmentArn' => [ 'shape' => 'Arn', ], 'endTime' => [ 'shape' => 'TimeStamp', ], 'format' => [ 'shape' => 'TemplateFormat', ], 'message' => [ 'shape' => 'String500', ], 'name' => [ 'shape' => 'EntityName', ], 'needsReplacements' => [ 'shape' => 'BooleanOptional', ], 'recommendationIds' => [ 'shape' => 'RecommendationIdList', ], 'recommendationTemplateArn' => [ 'shape' => 'Arn', ], 'recommendationTypes' => [ 'shape' => 'RenderRecommendationTypeList', ], 'startTime' => [ 'shape' => 'TimeStamp', ], 'status' => [ 'shape' => 'RecommendationTemplateStatus', ], 'tags' => [ 'shape' => 'TagMap', ], 'templatesLocation' => [ 'shape' => 'S3Location', ], ], ], 'RecommendationTemplateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationTemplate', ], ], 'RecommendationTemplateStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'InProgress', 'Failed', 'Success', ], ], 'RecommendationTemplateStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationTemplateStatus', ], 'max' => 4, 'min' => 1, ], 'RejectGroupingRecommendationEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'RejectGroupingRecommendationEntry', ], 'max' => 30, 'min' => 1, ], 'RejectGroupingRecommendationEntry' => [ 'type' => 'structure', 'required' => [ 'groupingRecommendationId', ], 'members' => [ 'groupingRecommendationId' => [ 'shape' => 'String255', ], 'rejectionReason' => [ 'shape' => 'GroupingRecommendationRejectionReason', ], ], ], 'RejectResourceGroupingRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'entries', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'entries' => [ 'shape' => 'RejectGroupingRecommendationEntries', ], ], ], 'RejectResourceGroupingRecommendationsResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'failedEntries', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'failedEntries' => [ 'shape' => 'FailedGroupingRecommendationEntries', ], ], ], 'RemoveDraftAppVersionResourceMappingsRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appRegistryAppNames' => [ 'shape' => 'EntityNameList', ], 'eksSourceNames' => [ 'shape' => 'String255List', ], 'logicalStackNames' => [ 'shape' => 'String255List', ], 'resourceGroupNames' => [ 'shape' => 'EntityNameList', ], 'resourceNames' => [ 'shape' => 'EntityNameList', ], 'terraformSourceNames' => [ 'shape' => 'String255List', ], ], ], 'RemoveDraftAppVersionResourceMappingsResponse' => [ 'type' => 'structure', 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], ], ], 'RenderRecommendationType' => [ 'type' => 'string', 'enum' => [ 'Alarm', 'Sop', 'Test', ], ], 'RenderRecommendationTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RenderRecommendationType', ], 'max' => 4, 'min' => 1, ], 'ResiliencyPolicies' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResiliencyPolicy', ], ], 'ResiliencyPolicy' => [ 'type' => 'structure', 'members' => [ 'creationTime' => [ 'shape' => 'TimeStamp', ], 'dataLocationConstraint' => [ 'shape' => 'DataLocationConstraint', ], 'estimatedCostTier' => [ 'shape' => 'EstimatedCostTier', ], 'policy' => [ 'shape' => 'DisruptionPolicy', ], 'policyArn' => [ 'shape' => 'Arn', ], 'policyDescription' => [ 'shape' => 'EntityDescription', ], 'policyName' => [ 'shape' => 'EntityName', ], 'tags' => [ 'shape' => 'TagMap', ], 'tier' => [ 'shape' => 'ResiliencyPolicyTier', ], ], ], 'ResiliencyPolicyTier' => [ 'type' => 'string', 'enum' => [ 'MissionCritical', 'Critical', 'Important', 'CoreServices', 'NonCritical', 'NotApplicable', ], ], 'ResiliencyScore' => [ 'type' => 'structure', 'required' => [ 'disruptionScore', 'score', ], 'members' => [ 'componentScore' => [ 'shape' => 'ScoringComponentResiliencyScores', ], 'disruptionScore' => [ 'shape' => 'DisruptionResiliencyScore', ], 'score' => [ 'shape' => 'Double', ], ], ], 'ResiliencyScoreType' => [ 'type' => 'string', 'enum' => [ 'Compliance', 'Test', 'Alarm', 'Sop', ], ], 'ResolveAppVersionResourcesRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], ], ], 'ResolveAppVersionResourcesResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', 'resolutionId', 'status', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'resolutionId' => [ 'shape' => 'String255', ], 'status' => [ 'shape' => 'ResourceResolutionStatusType', ], ], ], 'ResourceDrift' => [ 'type' => 'structure', 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'diffType' => [ 'shape' => 'DifferenceType', ], 'referenceId' => [ 'shape' => 'EntityId', ], 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], ], ], 'ResourceDriftList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceDrift', ], ], 'ResourceError' => [ 'type' => 'structure', 'members' => [ 'logicalResourceId' => [ 'shape' => 'String255', ], 'physicalResourceId' => [ 'shape' => 'String255', ], 'reason' => [ 'shape' => 'ErrorMessage', ], ], ], 'ResourceErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceError', ], ], 'ResourceErrorsDetails' => [ 'type' => 'structure', 'members' => [ 'hasMoreErrors' => [ 'shape' => 'BooleanOptional', ], 'resourceErrors' => [ 'shape' => 'ResourceErrorList', ], ], ], 'ResourceId' => [ 'type' => 'string', 'pattern' => '.*', ], 'ResourceIdentifier' => [ 'type' => 'structure', 'members' => [ 'logicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'resourceType' => [ 'shape' => 'String255', ], ], ], 'ResourceImportStatusType' => [ 'type' => 'string', 'enum' => [ 'Pending', 'InProgress', 'Failed', 'Success', ], ], 'ResourceImportStrategyType' => [ 'type' => 'string', 'enum' => [ 'AddOnly', 'ReplaceAll', ], ], 'ResourceMapping' => [ 'type' => 'structure', 'required' => [ 'mappingType', 'physicalResourceId', ], 'members' => [ 'appRegistryAppName' => [ 'shape' => 'EntityName', ], 'eksSourceName' => [ 'shape' => 'String255', ], 'logicalStackName' => [ 'shape' => 'String255', ], 'mappingType' => [ 'shape' => 'ResourceMappingType', ], 'physicalResourceId' => [ 'shape' => 'PhysicalResourceId', ], 'resourceGroupName' => [ 'shape' => 'EntityName', ], 'resourceName' => [ 'shape' => 'EntityName', ], 'terraformSourceName' => [ 'shape' => 'String255', ], ], ], 'ResourceMappingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceMapping', ], ], 'ResourceMappingType' => [ 'type' => 'string', 'enum' => [ 'CfnStack', 'Resource', 'AppRegistryApp', 'ResourceGroup', 'Terraform', 'EKS', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String500', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourceResolutionStatusType' => [ 'type' => 'string', 'enum' => [ 'Pending', 'InProgress', 'Failed', 'Success', ], ], 'ResourceSourceType' => [ 'type' => 'string', 'enum' => [ 'AppTemplate', 'Discovered', ], ], 'ResourceType' => [ 'type' => 'string', 'pattern' => '.*', ], 'ResourcesGroupingRecGenStatusType' => [ 'type' => 'string', 'enum' => [ 'Pending', 'InProgress', 'Failed', 'Success', ], ], 'RetryAfterSeconds' => [ 'type' => 'integer', 'box' => true, ], 'Row' => [ 'type' => 'list', 'member' => [ 'shape' => 'String255', ], ], 'RowList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Row', ], ], 'S3Location' => [ 'type' => 'structure', 'members' => [ 'bucket' => [ 'shape' => 'String500', ], 'prefix' => [ 'shape' => 'String500', ], ], ], 'S3Url' => [ 'type' => 'string', 'max' => 2000, 'min' => 0, 'pattern' => '^((https://([^/]+)\\.s3((-|\\.)[^/]+)?\\.amazonaws\\.com(.cn)?)|(s3://([^/]+)))/\\S{1,2000}$', ], 'ScoringComponentResiliencyScore' => [ 'type' => 'structure', 'members' => [ 'excludedCount' => [ 'shape' => 'Long', ], 'outstandingCount' => [ 'shape' => 'Long', ], 'possibleScore' => [ 'shape' => 'Double', ], 'score' => [ 'shape' => 'Double', ], ], ], 'ScoringComponentResiliencyScores' => [ 'type' => 'map', 'key' => [ 'shape' => 'ResiliencyScoreType', ], 'value' => [ 'shape' => 'ScoringComponentResiliencyScore', ], ], 'Seconds' => [ 'type' => 'integer', 'min' => 0, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String500', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SopRecommendation' => [ 'type' => 'structure', 'required' => [ 'recommendationId', 'referenceId', 'serviceType', ], 'members' => [ 'appComponentName' => [ 'shape' => 'EntityId', ], 'description' => [ 'shape' => 'String500', ], 'items' => [ 'shape' => 'RecommendationItemList', ], 'name' => [ 'shape' => 'DocumentName', ], 'prerequisite' => [ 'shape' => 'String500', ], 'recommendationId' => [ 'shape' => 'Uuid', ], 'recommendationStatus' => [ 'shape' => 'RecommendationStatus', ], 'referenceId' => [ 'shape' => 'SpecReferenceId', ], 'serviceType' => [ 'shape' => 'SopServiceType', ], ], ], 'SopRecommendationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SopRecommendation', ], ], 'SopServiceType' => [ 'type' => 'string', 'enum' => [ 'SSM', ], ], 'Sort' => [ 'type' => 'structure', 'required' => [ 'field', ], 'members' => [ 'ascending' => [ 'shape' => 'BooleanOptional', ], 'field' => [ 'shape' => 'String255', ], ], ], 'SortList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Sort', ], 'max' => 50, 'min' => 0, ], 'SpecReferenceId' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'StartAppAssessmentRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', 'assessmentName', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'assessmentName' => [ 'shape' => 'EntityName', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'StartAppAssessmentResponse' => [ 'type' => 'structure', 'required' => [ 'assessment', ], 'members' => [ 'assessment' => [ 'shape' => 'AppAssessment', ], ], ], 'StartMetricsExportRequest' => [ 'type' => 'structure', 'members' => [ 'bucketName' => [ 'shape' => 'EntityName', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'StartMetricsExportResponse' => [ 'type' => 'structure', 'required' => [ 'metricsExportId', 'status', ], 'members' => [ 'metricsExportId' => [ 'shape' => 'String255', ], 'status' => [ 'shape' => 'MetricsExportStatusType', ], ], ], 'StartResourceGroupingRecommendationTaskRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], ], ], 'StartResourceGroupingRecommendationTaskResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'groupingId', 'status', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'errorMessage' => [ 'shape' => 'String500', ], 'groupingId' => [ 'shape' => 'String255', ], 'status' => [ 'shape' => 'ResourcesGroupingRecGenStatusType', ], ], ], 'String1024' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'String128WithoutWhitespace' => [ 'type' => 'string', 'pattern' => '^\\S{1,128}$', ], 'String2048' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'String255' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'String255List' => [ 'type' => 'list', 'member' => [ 'shape' => 'String255', ], ], 'String500' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'SuggestedChangesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntityDescription', ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[^\\x00-\\x1f\\x22]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, 'sensitive' => true, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, 'sensitive' => true, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^[^\\x00-\\x1f\\x22]*$', ], 'TemplateFormat' => [ 'type' => 'string', 'enum' => [ 'CfnYaml', 'CfnJson', ], ], 'TerraformSource' => [ 'type' => 'structure', 'required' => [ 's3StateFileUrl', ], 'members' => [ 's3StateFileUrl' => [ 'shape' => 'S3Url', ], ], ], 'TerraformSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TerraformSource', ], ], 'TestRecommendation' => [ 'type' => 'structure', 'required' => [ 'referenceId', ], 'members' => [ 'appComponentId' => [ 'shape' => 'EntityName255', ], 'appComponentName' => [ 'shape' => 'EntityId', ], 'dependsOnAlarms' => [ 'shape' => 'AlarmReferenceIdList', ], 'description' => [ 'shape' => 'String500', ], 'intent' => [ 'shape' => 'EntityDescription', ], 'items' => [ 'shape' => 'RecommendationItemList', ], 'name' => [ 'shape' => 'DocumentName', ], 'prerequisite' => [ 'shape' => 'String500', ], 'recommendationId' => [ 'shape' => 'Uuid', ], 'recommendationStatus' => [ 'shape' => 'RecommendationStatus', ], 'referenceId' => [ 'shape' => 'SpecReferenceId', ], 'risk' => [ 'shape' => 'TestRisk', ], 'type' => [ 'shape' => 'TestType', ], ], ], 'TestRecommendationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TestRecommendation', ], ], 'TestRisk' => [ 'type' => 'string', 'enum' => [ 'Small', 'Medium', 'High', ], ], 'TestType' => [ 'type' => 'string', 'enum' => [ 'Software', 'Hardware', 'AZ', 'Region', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String500', ], 'retryAfterSeconds' => [ 'shape' => 'RetryAfterSeconds', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'TimeStamp' => [ 'type' => 'timestamp', ], 'UnsupportedResource' => [ 'type' => 'structure', 'required' => [ 'logicalResourceId', 'physicalResourceId', 'resourceType', ], 'members' => [ 'logicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'physicalResourceId' => [ 'shape' => 'PhysicalResourceId', ], 'resourceType' => [ 'shape' => 'String255', ], 'unsupportedResourceStatus' => [ 'shape' => 'String255', ], ], ], 'UnsupportedResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UnsupportedResource', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAppRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'assessmentSchedule' => [ 'shape' => 'AppAssessmentScheduleType', ], 'clearResiliencyPolicyArn' => [ 'shape' => 'BooleanOptional', ], 'description' => [ 'shape' => 'EntityDescription', ], 'eventSubscriptions' => [ 'shape' => 'EventSubscriptionList', ], 'permissionModel' => [ 'shape' => 'PermissionModel', ], 'policyArn' => [ 'shape' => 'Arn', ], ], ], 'UpdateAppResponse' => [ 'type' => 'structure', 'required' => [ 'app', ], 'members' => [ 'app' => [ 'shape' => 'App', ], ], ], 'UpdateAppVersionAppComponentRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'id', ], 'members' => [ 'additionalInfo' => [ 'shape' => 'AdditionalInfoMap', ], 'appArn' => [ 'shape' => 'Arn', ], 'id' => [ 'shape' => 'String255', ], 'name' => [ 'shape' => 'String255', ], 'type' => [ 'shape' => 'String255', ], ], ], 'UpdateAppVersionAppComponentResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appComponent' => [ 'shape' => 'AppComponent', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], ], ], 'UpdateAppVersionRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'additionalInfo' => [ 'shape' => 'AdditionalInfoMap', ], 'appArn' => [ 'shape' => 'Arn', ], ], ], 'UpdateAppVersionResourceRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'additionalInfo' => [ 'shape' => 'AdditionalInfoMap', ], 'appArn' => [ 'shape' => 'Arn', ], 'appComponents' => [ 'shape' => 'AppComponentNameList', ], 'awsAccountId' => [ 'shape' => 'CustomerId', ], 'awsRegion' => [ 'shape' => 'AwsRegion', ], 'excluded' => [ 'shape' => 'BooleanOptional', ], 'logicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'physicalResourceId' => [ 'shape' => 'String2048', ], 'resourceName' => [ 'shape' => 'EntityName', ], 'resourceType' => [ 'shape' => 'String255', ], ], ], 'UpdateAppVersionResourceResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'physicalResource' => [ 'shape' => 'PhysicalResource', ], ], ], 'UpdateAppVersionResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'additionalInfo' => [ 'shape' => 'AdditionalInfoMap', ], 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], ], ], 'UpdateRecommendationStatusItem' => [ 'type' => 'structure', 'members' => [ 'resourceId' => [ 'shape' => 'String500', ], 'targetAccountId' => [ 'shape' => 'CustomerId', ], 'targetRegion' => [ 'shape' => 'AwsRegion', ], ], ], 'UpdateRecommendationStatusRequestEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpdateRecommendationStatusRequestEntry', ], 'max' => 50, 'min' => 1, ], 'UpdateRecommendationStatusRequestEntry' => [ 'type' => 'structure', 'required' => [ 'entryId', 'excluded', 'referenceId', ], 'members' => [ 'appComponentId' => [ 'shape' => 'EntityName255', ], 'entryId' => [ 'shape' => 'String255', ], 'excludeReason' => [ 'shape' => 'ExcludeRecommendationReason', ], 'excluded' => [ 'shape' => 'BooleanOptional', ], 'item' => [ 'shape' => 'UpdateRecommendationStatusItem', ], 'referenceId' => [ 'shape' => 'SpecReferenceId', ], ], ], 'UpdateResiliencyPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyArn', ], 'members' => [ 'dataLocationConstraint' => [ 'shape' => 'DataLocationConstraint', ], 'policy' => [ 'shape' => 'DisruptionPolicy', ], 'policyArn' => [ 'shape' => 'Arn', ], 'policyDescription' => [ 'shape' => 'EntityDescription', ], 'policyName' => [ 'shape' => 'EntityName', ], 'tier' => [ 'shape' => 'ResiliencyPolicyTier', ], ], ], 'UpdateResiliencyPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'policy', ], 'members' => [ 'policy' => [ 'shape' => 'ResiliencyPolicy', ], ], ], 'Uuid' => [ 'type' => 'string', 'pattern' => '^[0-9a-f]{8}-[0-9a-f]{4}-[0-5][0-9a-f]{3}-[089ab][0-9a-f]{3}-[0-9a-f]{12}$', ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String500', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], ],];
