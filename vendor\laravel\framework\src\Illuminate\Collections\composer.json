{"name": "illuminate/collections", "description": "The Illuminate Collections package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "illuminate/conditionable": "^11.0", "illuminate/contracts": "^11.0", "illuminate/macroable": "^11.0"}, "autoload": {"psr-4": {"Illuminate\\Support\\": ""}, "files": ["functions.php", "helpers.php"]}, "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "suggest": {"symfony/var-dumper": "Required to use the dump method (^7.0)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}