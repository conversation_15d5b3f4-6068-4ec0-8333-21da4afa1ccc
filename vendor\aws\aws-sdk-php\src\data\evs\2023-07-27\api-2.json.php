<?php
// This file was auto-generated from sdk-root/src/data/evs/2023-07-27/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-07-27', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'evs', 'jsonVersion' => '1.0', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceAbbreviation' => 'EVS', 'serviceFullName' => 'Amazon Elastic VMware Service', 'serviceId' => 'evs', 'signatureVersion' => 'v4', 'signingName' => 'evs', 'targetPrefix' => 'AmazonElasticVMwareService', 'uid' => 'evs-2023-07-27', ], 'operations' => [ 'CreateEnvironment' => [ 'name' => 'CreateEnvironment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEnvironmentRequest', ], 'output' => [ 'shape' => 'CreateEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'CreateEnvironmentHost' => [ 'name' => 'CreateEnvironmentHost', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEnvironmentHostRequest', ], 'output' => [ 'shape' => 'CreateEnvironmentHostResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeleteEnvironment' => [ 'name' => 'DeleteEnvironment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEnvironmentRequest', ], 'output' => [ 'shape' => 'DeleteEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteEnvironmentHost' => [ 'name' => 'DeleteEnvironmentHost', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEnvironmentHostRequest', ], 'output' => [ 'shape' => 'DeleteEnvironmentHostResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'GetEnvironment' => [ 'name' => 'GetEnvironment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetEnvironmentRequest', ], 'output' => [ 'shape' => 'GetEnvironmentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListEnvironmentHosts' => [ 'name' => 'ListEnvironmentHosts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEnvironmentHostsRequest', ], 'output' => [ 'shape' => 'ListEnvironmentHostsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListEnvironmentVlans' => [ 'name' => 'ListEnvironmentVlans', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEnvironmentVlansRequest', ], 'output' => [ 'shape' => 'ListEnvironmentVlansResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListEnvironments' => [ 'name' => 'ListEnvironments', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEnvironmentsRequest', ], 'output' => [ 'shape' => 'ListEnvironmentsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'TagPolicyException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TagPolicyException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'Arn' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => 'arn:aws:evs:[a-z]{2}-[a-z]+-[0-9]:[0-9]{12}:environment/[a-zA-Z0-9_-]+', ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'Check' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'CheckType', ], 'result' => [ 'shape' => 'CheckResult', ], 'impairedSince' => [ 'shape' => 'Timestamp', ], ], ], 'CheckResult' => [ 'type' => 'string', 'enum' => [ 'PASSED', 'FAILED', 'UNKNOWN', ], ], 'CheckType' => [ 'type' => 'string', 'enum' => [ 'KEY_REUSE', 'KEY_COVERAGE', 'REACHABILITY', 'HOST_COUNT', ], ], 'ChecksList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Check', ], ], 'Cidr' => [ 'type' => 'string', 'pattern' => '((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)/(3[0-2]|[1-2][0-9]|[0-9])', ], 'ClientToken' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[!-~]+', ], 'ConnectivityInfo' => [ 'type' => 'structure', 'required' => [ 'privateRouteServerPeerings', ], 'members' => [ 'privateRouteServerPeerings' => [ 'shape' => 'RouteServerPeeringList', ], ], ], 'CreateEnvironmentHostRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'host', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'host' => [ 'shape' => 'HostInfoForCreate', ], ], ], 'CreateEnvironmentHostResponse' => [ 'type' => 'structure', 'members' => [ 'environmentSummary' => [ 'shape' => 'EnvironmentSummary', ], 'host' => [ 'shape' => 'Host', ], ], ], 'CreateEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'vpcId', 'serviceAccessSubnetId', 'vcfVersion', 'termsAccepted', 'licenseInfo', 'initialVlans', 'hosts', 'connectivityInfo', 'vcfHostnames', 'siteId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'environmentName' => [ 'shape' => 'EnvironmentName', ], 'kmsKeyId' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'RequestTagMap', ], 'serviceAccessSecurityGroups' => [ 'shape' => 'ServiceAccessSecurityGroups', ], 'vpcId' => [ 'shape' => 'VpcId', ], 'serviceAccessSubnetId' => [ 'shape' => 'SubnetId', ], 'vcfVersion' => [ 'shape' => 'VcfVersion', ], 'termsAccepted' => [ 'shape' => 'Boolean', ], 'licenseInfo' => [ 'shape' => 'LicenseInfoList', ], 'initialVlans' => [ 'shape' => 'InitialVlans', ], 'hosts' => [ 'shape' => 'HostInfoForCreateList', ], 'connectivityInfo' => [ 'shape' => 'ConnectivityInfo', ], 'vcfHostnames' => [ 'shape' => 'VcfHostnames', ], 'siteId' => [ 'shape' => 'String', ], ], ], 'CreateEnvironmentResponse' => [ 'type' => 'structure', 'members' => [ 'environment' => [ 'shape' => 'Environment', ], ], ], 'DedicatedHostId' => [ 'type' => 'string', 'max' => 25, 'min' => 1, 'pattern' => 'h-[a-f0-9]{8}([a-f0-9]{9})?', ], 'DeleteEnvironmentHostRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', 'hostName', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'hostName' => [ 'shape' => 'HostName', ], ], ], 'DeleteEnvironmentHostResponse' => [ 'type' => 'structure', 'members' => [ 'environmentSummary' => [ 'shape' => 'EnvironmentSummary', ], 'host' => [ 'shape' => 'Host', ], ], ], 'DeleteEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], ], ], 'DeleteEnvironmentResponse' => [ 'type' => 'structure', 'members' => [ 'environment' => [ 'shape' => 'Environment', ], ], ], 'Environment' => [ 'type' => 'structure', 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'environmentState' => [ 'shape' => 'EnvironmentState', ], 'stateDetails' => [ 'shape' => 'StateDetails', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'modifiedAt' => [ 'shape' => 'Timestamp', ], 'environmentArn' => [ 'shape' => 'Arn', ], 'environmentName' => [ 'shape' => 'EnvironmentName', ], 'vpcId' => [ 'shape' => 'VpcId', ], 'serviceAccessSubnetId' => [ 'shape' => 'SubnetId', ], 'vcfVersion' => [ 'shape' => 'VcfVersion', ], 'termsAccepted' => [ 'shape' => 'Boolean', ], 'licenseInfo' => [ 'shape' => 'LicenseInfoList', ], 'siteId' => [ 'shape' => 'String', ], 'environmentStatus' => [ 'shape' => 'CheckResult', ], 'checks' => [ 'shape' => 'ChecksList', ], 'connectivityInfo' => [ 'shape' => 'ConnectivityInfo', ], 'vcfHostnames' => [ 'shape' => 'VcfHostnames', ], 'kmsKeyId' => [ 'shape' => 'String', ], 'serviceAccessSecurityGroups' => [ 'shape' => 'ServiceAccessSecurityGroups', ], 'credentials' => [ 'shape' => 'SecretList', ], ], ], 'EnvironmentId' => [ 'type' => 'string', 'pattern' => '(env-[a-zA-Z0-9]{10})', ], 'EnvironmentName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'EnvironmentState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATED', 'DELETING', 'DELETED', 'CREATE_FAILED', ], ], 'EnvironmentStateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnvironmentState', ], ], 'EnvironmentSummary' => [ 'type' => 'structure', 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', ], 'environmentName' => [ 'shape' => 'EnvironmentName', ], 'vcfVersion' => [ 'shape' => 'VcfVersion', ], 'environmentStatus' => [ 'shape' => 'CheckResult', ], 'environmentState' => [ 'shape' => 'EnvironmentState', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'modifiedAt' => [ 'shape' => 'Timestamp', ], 'environmentArn' => [ 'shape' => 'Arn', ], ], ], 'EnvironmentSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnvironmentSummary', ], ], 'GetEnvironmentRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'environmentId' => [ 'shape' => 'EnvironmentId', ], ], ], 'GetEnvironmentResponse' => [ 'type' => 'structure', 'members' => [ 'environment' => [ 'shape' => 'Environment', ], ], ], 'Host' => [ 'type' => 'structure', 'members' => [ 'hostName' => [ 'shape' => 'HostName', ], 'ipAddress' => [ 'shape' => 'IpAddress', ], 'keyName' => [ 'shape' => 'KeyName', ], 'instanceType' => [ 'shape' => 'InstanceType', ], 'placementGroupId' => [ 'shape' => 'PlacementGroupId', ], 'dedicatedHostId' => [ 'shape' => 'DedicatedHostId', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'modifiedAt' => [ 'shape' => 'Timestamp', ], 'hostState' => [ 'shape' => 'HostState', ], 'stateDetails' => [ 'shape' => 'StateDetails', ], 'ec2InstanceId' => [ 'shape' => 'String', ], 'networkInterfaces' => [ 'shape' => 'NetworkInterfaceList', ], ], ], 'HostInfoForCreate' => [ 'type' => 'structure', 'required' => [ 'hostName', 'keyName', 'instanceType', ], 'members' => [ 'hostName' => [ 'shape' => 'HostName', ], 'keyName' => [ 'shape' => 'KeyName', ], 'instanceType' => [ 'shape' => 'InstanceType', ], 'placementGroupId' => [ 'shape' => 'PlacementGroupId', ], 'dedicatedHostId' => [ 'shape' => 'DedicatedHostId', ], ], ], 'HostInfoForCreateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HostInfoForCreate', ], 'max' => 4, 'min' => 4, ], 'HostList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Host', ], ], 'HostName' => [ 'type' => 'string', 'pattern' => '([a-zA-Z0-9\\-]*)', ], 'HostState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATED', 'UPDATING', 'DELETING', 'DELETED', 'CREATE_FAILED', 'UPDATE_FAILED', ], ], 'InitialVlanInfo' => [ 'type' => 'structure', 'required' => [ 'cidr', ], 'members' => [ 'cidr' => [ 'shape' => 'Cidr', ], ], ], 'InitialVlans' => [ 'type' => 'structure', 'required' => [ 'vmkManagement', 'vmManagement', 'vMotion', 'vSan', 'vTep', 'edgeVTep', 'nsxUplink', 'hcx', 'expansionVlan1', 'expansionVlan2', ], 'members' => [ 'vmkManagement' => [ 'shape' => 'InitialVlanInfo', ], 'vmManagement' => [ 'shape' => 'InitialVlanInfo', ], 'vMotion' => [ 'shape' => 'InitialVlanInfo', ], 'vSan' => [ 'shape' => 'InitialVlanInfo', ], 'vTep' => [ 'shape' => 'InitialVlanInfo', ], 'edgeVTep' => [ 'shape' => 'InitialVlanInfo', ], 'nsxUplink' => [ 'shape' => 'InitialVlanInfo', ], 'hcx' => [ 'shape' => 'InitialVlanInfo', ], 'expansionVlan1' => [ 'shape' => 'InitialVlanInfo', ], 'expansionVlan2' => [ 'shape' => 'InitialVlanInfo', ], ], ], 'InstanceType' => [ 'type' => 'string', 'enum' => [ 'i4i.metal', ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'IpAddress' => [ 'type' => 'string', 'pattern' => '(\\d{1,3}\\.){3}\\d{1,3}', ], 'KeyName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'LicenseInfo' => [ 'type' => 'structure', 'required' => [ 'solutionKey', 'vsanKey', ], 'members' => [ 'solutionKey' => [ 'shape' => 'SolutionKey', ], 'vsanKey' => [ 'shape' => 'VSanLicenseKey', ], ], ], 'LicenseInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LicenseInfo', ], 'max' => 1, 'min' => 1, ], 'ListEnvironmentHostsRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], ], ], 'ListEnvironmentHostsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'environmentHosts' => [ 'shape' => 'HostList', ], ], ], 'ListEnvironmentVlansRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'environmentId' => [ 'shape' => 'EnvironmentId', ], ], ], 'ListEnvironmentVlansResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'environmentVlans' => [ 'shape' => 'VlanList', ], ], ], 'ListEnvironmentsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'state' => [ 'shape' => 'EnvironmentStateList', ], ], ], 'ListEnvironmentsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'environmentSummaries' => [ 'shape' => 'EnvironmentSummaryList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'ResponseTagMap', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'NetworkInterface' => [ 'type' => 'structure', 'members' => [ 'networkInterfaceId' => [ 'shape' => 'NetworkInterfaceId', ], ], ], 'NetworkInterfaceId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'NetworkInterfaceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkInterface', ], 'max' => 2, 'min' => 0, ], 'PaginationToken' => [ 'type' => 'string', ], 'PlacementGroupId' => [ 'type' => 'string', 'max' => 25, 'min' => 1, 'pattern' => 'pg-[a-f0-9]{8}([a-f0-9]{9})?', ], 'RequestTagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 1, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResponseTagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], 'RouteServerPeering' => [ 'type' => 'string', 'max' => 21, 'min' => 3, ], 'RouteServerPeeringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteServerPeering', ], 'max' => 2, 'min' => 2, ], 'Secret' => [ 'type' => 'structure', 'members' => [ 'secretArn' => [ 'shape' => 'String', ], ], ], 'SecretList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Secret', ], ], 'SecurityGroupId' => [ 'type' => 'string', 'max' => 25, 'min' => 3, 'pattern' => 'sg-[0-9a-zA-Z]*', ], 'SecurityGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 2, 'min' => 0, ], 'ServiceAccessSecurityGroups' => [ 'type' => 'structure', 'members' => [ 'securityGroups' => [ 'shape' => 'SecurityGroups', ], ], ], 'SolutionKey' => [ 'type' => 'string', 'pattern' => '[a-zA-Z0-9]{5}-[a-zA-Z0-9]{5}-[a-zA-Z0-9]{5}-[a-zA-Z0-9]{5}-[a-zA-Z0-9]{5}', ], 'StateDetails' => [ 'type' => 'string', ], 'String' => [ 'type' => 'string', ], 'SubnetId' => [ 'type' => 'string', 'max' => 24, 'min' => 15, 'pattern' => 'subnet-[a-f0-9]{8}([a-f0-9]{9})?', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\w.:/=+-@]+', ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 1, ], 'TagPolicyException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', ], 'tags' => [ 'shape' => 'RequestTagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[\\w.:/=+-@]+|', ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', ], ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TooManyTagsException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', ], 'tagKeys' => [ 'shape' => 'TagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'VSanLicenseKey' => [ 'type' => 'string', 'pattern' => '[a-zA-Z0-9]{5}-[a-zA-Z0-9]{5}-[a-zA-Z0-9]{5}-[a-zA-Z0-9]{5}-[a-zA-Z0-9]{5}', ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'name', 'message', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'unknownOperation', 'cannotParse', 'fieldValidationFailed', 'other', ], ], 'VcfHostnames' => [ 'type' => 'structure', 'required' => [ 'vCenter', 'nsx', 'nsxManager1', 'nsxManager2', 'nsxManager3', 'nsxEdge1', 'nsxEdge2', 'sddcManager', 'cloudBuilder', ], 'members' => [ 'vCenter' => [ 'shape' => 'HostName', ], 'nsx' => [ 'shape' => 'HostName', ], 'nsxManager1' => [ 'shape' => 'HostName', ], 'nsxManager2' => [ 'shape' => 'HostName', ], 'nsxManager3' => [ 'shape' => 'HostName', ], 'nsxEdge1' => [ 'shape' => 'HostName', ], 'nsxEdge2' => [ 'shape' => 'HostName', ], 'sddcManager' => [ 'shape' => 'HostName', ], 'cloudBuilder' => [ 'shape' => 'HostName', ], ], ], 'VcfVersion' => [ 'type' => 'string', 'enum' => [ 'VCF-5.2.1', ], ], 'Vlan' => [ 'type' => 'structure', 'members' => [ 'vlanId' => [ 'shape' => 'VlanId', ], 'cidr' => [ 'shape' => 'Cidr', ], 'availabilityZone' => [ 'shape' => 'String', ], 'functionName' => [ 'shape' => 'String', ], 'subnetId' => [ 'shape' => 'SubnetId', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'modifiedAt' => [ 'shape' => 'Timestamp', ], 'vlanState' => [ 'shape' => 'VlanState', ], 'stateDetails' => [ 'shape' => 'StateDetails', ], ], ], 'VlanId' => [ 'type' => 'integer', 'box' => true, ], 'VlanList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Vlan', ], ], 'VlanState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATED', 'DELETING', 'DELETED', 'CREATE_FAILED', ], ], 'VpcId' => [ 'type' => 'string', 'max' => 21, 'min' => 12, 'pattern' => 'vpc-[a-f0-9]{8}([a-f0-9]{9})?', ], ],];
