<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Assistants
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Assistants\V1;

use Twilio\Options;
use Twilio\Values;

abstract class PolicyOptions
{
    /**
     * @param string $toolId The tool ID.
     * @param string $knowledgeId The knowledge ID.
     * @return ReadPolicyOptions Options builder
     */
    public static function read(
        
        string $toolId = Values::NONE,
        string $knowledgeId = Values::NONE

    ): ReadPolicyOptions
    {
        return new ReadPolicyOptions(
            $toolId,
            $knowledgeId
        );
    }

}

class ReadPolicyOptions extends Options
    {
    /**
     * @param string $toolId The tool ID.
     * @param string $knowledgeId The knowledge ID.
     */
    public function __construct(
        
        string $toolId = Values::NONE,
        string $knowledgeId = Values::NONE

    ) {
        $this->options['toolId'] = $toolId;
        $this->options['knowledgeId'] = $knowledgeId;
    }

    /**
     * The tool ID.
     *
     * @param string $toolId The tool ID.
     * @return $this Fluent Builder
     */
    public function setToolId(string $toolId): self
    {
        $this->options['toolId'] = $toolId;
        return $this;
    }

    /**
     * The knowledge ID.
     *
     * @param string $knowledgeId The knowledge ID.
     * @return $this Fluent Builder
     */
    public function setKnowledgeId(string $knowledgeId): self
    {
        $this->options['knowledgeId'] = $knowledgeId;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Assistants.V1.ReadPolicyOptions ' . $options . ']';
    }
}

