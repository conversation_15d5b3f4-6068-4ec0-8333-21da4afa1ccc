<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Notify
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Notify\V1\Service;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;


/**
 * @property string|null $sid
 * @property string|null $accountSid
 * @property string|null $serviceSid
 * @property \DateTime|null $dateCreated
 * @property string[]|null $identities
 * @property string[]|null $tags
 * @property string[]|null $segments
 * @property string $priority
 * @property int $ttl
 * @property string|null $title
 * @property string|null $body
 * @property string|null $sound
 * @property string|null $action
 * @property array|null $data
 * @property array|null $apn
 * @property array|null $gcm
 * @property array|null $fcm
 * @property array|null $sms
 * @property array|null $facebookMessenger
 * @property array|null $alexa
 */
class NotificationInstance extends InstanceResource
{
    /**
     * Initialize the NotificationInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $serviceSid The SID of the [Service](https://www.twilio.com/docs/notify/api/service-resource) to create the resource under.
     */
    public function __construct(Version $version, array $payload, string $serviceSid)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'sid' => Values::array_get($payload, 'sid'),
            'accountSid' => Values::array_get($payload, 'account_sid'),
            'serviceSid' => Values::array_get($payload, 'service_sid'),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'identities' => Values::array_get($payload, 'identities'),
            'tags' => Values::array_get($payload, 'tags'),
            'segments' => Values::array_get($payload, 'segments'),
            'priority' => Values::array_get($payload, 'priority'),
            'ttl' => Values::array_get($payload, 'ttl'),
            'title' => Values::array_get($payload, 'title'),
            'body' => Values::array_get($payload, 'body'),
            'sound' => Values::array_get($payload, 'sound'),
            'action' => Values::array_get($payload, 'action'),
            'data' => Values::array_get($payload, 'data'),
            'apn' => Values::array_get($payload, 'apn'),
            'gcm' => Values::array_get($payload, 'gcm'),
            'fcm' => Values::array_get($payload, 'fcm'),
            'sms' => Values::array_get($payload, 'sms'),
            'facebookMessenger' => Values::array_get($payload, 'facebook_messenger'),
            'alexa' => Values::array_get($payload, 'alexa'),
        ];

        $this->solution = ['serviceSid' => $serviceSid, ];
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Notify.V1.NotificationInstance]';
    }
}

