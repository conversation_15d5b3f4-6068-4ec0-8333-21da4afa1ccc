<?php

namespace App\Models\ProjectManagement;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use App\Models\User;
use App\Traits\ProjectManagement\HasTimestamps;
use App\Traits\ProjectManagement\Searchable;

/**
 * نموذج المعلم (Milestone) المتقدم
 * 
 * المعالم هي نقاط مهمة في دورة حياة المشروع تمثل إنجازات رئيسية
 * أو مراحل مهمة يجب الوصول إليها في تواريخ محددة
 * 
 * @package App\Models\ProjectManagement
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 * 
 * @property int $id معرف المعلم الفريد
 * @property string $name اسم المعلم
 * @property string|null $description وصف المعلم
 * @property int $project_id معرف المشروع
 * @property Carbon $due_date تاريخ الاستحقاق
 * @property Carbon|null $completed_at تاريخ الإنجاز
 * @property string $status حالة المعلم
 * @property int $order_index ترتيب المعلم
 * @property array|null $deliverables المخرجات المطلوبة
 * @property int $created_by معرف منشئ المعلم
 * @property Carbon $created_at تاريخ الإنشاء
 * @property Carbon $updated_at تاريخ آخر تحديث
 * @property Carbon|null $deleted_at تاريخ الحذف الناعم
 */
class Milestone extends Model
{
    use HasFactory, SoftDeletes, HasTimestamps, Searchable;

    /**
     * اسم الجدول في قاعدة البيانات
     * 
     * @var string
     */
    protected $table = 'milestones';

    /**
     * الحقول القابلة للتعبئة الجماعية
     * 
     * @var array<string>
     */
    protected $fillable = [
        'name',
        'description',
        'project_id',
        'due_date',
        'completed_at',
        'status',
        'order_index',
        'deliverables',
        'created_by',
    ];

    /**
     * الحقول المخفية من التسلسل
     * 
     * @var array<string>
     */
    protected $hidden = [
        'deleted_at',
    ];

    /**
     * تحويل أنواع البيانات
     * 
     * @var array<string, string>
     */
    protected $casts = [
        'due_date' => 'date',
        'completed_at' => 'datetime',
        'order_index' => 'integer',
        'deliverables' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * الحقول القابلة للبحث
     * 
     * @var array<string>
     */
    protected $searchable = [
        'name',
        'description',
        'status',
    ];

    /**
     * القيم الافتراضية للحقول
     * 
     * @var array<string, mixed>
     */
    protected $attributes = [
        'status' => 'pending',
        'order_index' => 0,
    ];

    /**
     * حالات المعلم المتاحة
     * 
     * @var array<string, array>
     */
    public const STATUSES = [
        'pending' => [
            'label' => 'في الانتظار',
            'color' => '#6c757d',
            'icon' => 'fas fa-clock',
        ],
        'in_progress' => [
            'label' => 'قيد التنفيذ',
            'color' => '#007bff',
            'icon' => 'fas fa-play',
        ],
        'completed' => [
            'label' => 'مكتمل',
            'color' => '#28a745',
            'icon' => 'fas fa-check-circle',
        ],
        'overdue' => [
            'label' => 'متأخر',
            'color' => '#dc3545',
            'icon' => 'fas fa-exclamation-triangle',
        ],
    ];

    /**
     * أحداث النموذج
     * 
     * @return void
     */
    protected static function boot(): void
    {
        parent::boot();

        // تحديث تاريخ الإنجاز عند تغيير الحالة إلى مكتملة
        static::updating(function ($milestone) {
            if ($milestone->isDirty('status')) {
                if ($milestone->status === 'completed' && $milestone->getOriginal('status') !== 'completed') {
                    $milestone->completed_at = now();
                } elseif ($milestone->status !== 'completed') {
                    $milestone->completed_at = null;
                }
            }
        });

        // تحديث حالة المعلم إلى متأخر إذا تجاوز تاريخ الاستحقاق
        static::saving(function ($milestone) {
            if ($milestone->due_date && $milestone->due_date->isPast() && $milestone->status !== 'completed') {
                $milestone->status = 'overdue';
            }
        });
    }

    /*
    |--------------------------------------------------------------------------
    | العلاقات (Relationships)
    |--------------------------------------------------------------------------
    */

    /**
     * العلاقة مع المشروع
     * 
     * @return BelongsTo
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * العلاقة مع منشئ المعلم
     * 
     * @return BelongsTo
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * العلاقة مع المهام المرتبطة بالمعلم
     * 
     * @return HasMany
     */
    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class);
    }

    /*
    |--------------------------------------------------------------------------
    | النطاقات (Scopes)
    |--------------------------------------------------------------------------
    */

    /**
     * نطاق المعالم المكتملة
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeCompleted(Builder $query): Builder
    {
        return $query->where('status', 'completed');
    }

    /**
     * نطاق المعالم المتأخرة
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeOverdue(Builder $query): Builder
    {
        return $query->where('status', 'overdue')
                    ->orWhere(function ($q) {
                        $q->where('due_date', '<', now())
                          ->where('status', '!=', 'completed');
                    });
    }

    /**
     * نطاق المعالم القادمة
     * 
     * @param Builder $query
     * @param int $days عدد الأيام
     * @return Builder
     */
    public function scopeUpcoming(Builder $query, int $days = 7): Builder
    {
        return $query->where('due_date', '>=', now())
                    ->where('due_date', '<=', now()->addDays($days))
                    ->where('status', '!=', 'completed');
    }

    /**
     * نطاق المعالم حسب المشروع
     * 
     * @param Builder $query
     * @param int $projectId
     * @return Builder
     */
    public function scopeByProject(Builder $query, int $projectId): Builder
    {
        return $query->where('project_id', $projectId);
    }

    /*
    |--------------------------------------------------------------------------
    | الطرق المساعدة (Helper Methods)
    |--------------------------------------------------------------------------
    */

    /**
     * التحقق من تأخر المعلم
     * 
     * @return bool
     */
    public function isOverdue(): bool
    {
        return $this->due_date && 
               $this->due_date->isPast() && 
               $this->status !== 'completed';
    }

    /**
     * التحقق من اكتمال المعلم
     * 
     * @return bool
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * حساب المدة المتبقية للمعلم
     * 
     * @return int|null عدد الأيام المتبقية
     */
    public function daysRemaining(): ?int
    {
        if (!$this->due_date || $this->isCompleted()) {
            return null;
        }

        return now()->diffInDays($this->due_date, false);
    }

    /**
     * حساب نسبة إنجاز المعلم بناءً على المهام المرتبطة
     * 
     * @return float
     */
    public function getProgressPercentage(): float
    {
        $totalTasks = $this->tasks()->count();
        
        if ($totalTasks === 0) {
            return $this->isCompleted() ? 100 : 0;
        }

        $completedTasks = $this->tasks()->where('status', 'completed')->count();
        return round(($completedTasks / $totalTasks) * 100, 2);
    }

    /**
     * الحصول على لون الحالة
     * 
     * @return string
     */
    public function getStatusColorAttribute(): string
    {
        return self::STATUSES[$this->status]['color'] ?? '#6c757d';
    }

    /**
     * الحصول على أيقونة الحالة
     * 
     * @return string
     */
    public function getStatusIconAttribute(): string
    {
        return self::STATUSES[$this->status]['icon'] ?? 'fas fa-question';
    }

    /**
     * الحصول على تسمية الحالة
     * 
     * @return string
     */
    public function getStatusLabelAttribute(): string
    {
        return self::STATUSES[$this->status]['label'] ?? $this->status;
    }

    /**
     * تحديث حالة المعلم بناءً على حالة المهام
     * 
     * @return void
     */
    public function updateStatusBasedOnTasks(): void
    {
        $totalTasks = $this->tasks()->count();
        
        if ($totalTasks === 0) {
            return;
        }

        $completedTasks = $this->tasks()->where('status', 'completed')->count();
        
        if ($completedTasks === $totalTasks) {
            $this->update(['status' => 'completed', 'completed_at' => now()]);
        } elseif ($completedTasks > 0) {
            $this->update(['status' => 'in_progress']);
        }
    }

    /**
     * الحصول على المهام المتبقية
     * 
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRemainingTasks()
    {
        return $this->tasks()->where('status', '!=', 'completed')->get();
    }

    /**
     * الحصول على المهام المكتملة
     * 
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getCompletedTasks()
    {
        return $this->tasks()->where('status', 'completed')->get();
    }
}
