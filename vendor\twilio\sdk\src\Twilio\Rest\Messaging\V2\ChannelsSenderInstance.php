<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Messaging\V2;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Values;
use Twilio\Version;


/**
 * @property string|null $sid
 * @property string $status
 * @property string|null $senderId
 * @property string|null $configuration
 * @property string|null $webhook
 * @property string|null $profile
 * @property string|null $properties
 * @property string[]|null $offlineReasons
 * @property string|null $url
 */
class ChannelsSenderInstance extends InstanceResource
{
    /**
     * Initialize the ChannelsSenderInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $sid A 34 character string that uniquely identifies this Sender.
     */
    public function __construct(Version $version, array $payload, ?string $sid = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'sid' => Values::array_get($payload, 'sid'),
            'status' => Values::array_get($payload, 'status'),
            'senderId' => Values::array_get($payload, 'sender_id'),
            'configuration' => Values::array_get($payload, 'configuration'),
            'webhook' => Values::array_get($payload, 'webhook'),
            'profile' => Values::array_get($payload, 'profile'),
            'properties' => Values::array_get($payload, 'properties'),
            'offlineReasons' => Values::array_get($payload, 'offline_reasons'),
            'url' => Values::array_get($payload, 'url'),
        ];

        $this->solution = ['sid' => $sid ?: $this->properties['sid'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return ChannelsSenderContext Context for this ChannelsSenderInstance
     */
    protected function proxy(): ChannelsSenderContext
    {
        if (!$this->context) {
            $this->context = new ChannelsSenderContext(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->context;
    }

    /**
     * Delete the ChannelsSenderInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->proxy()->delete();
    }

    /**
     * Fetch the ChannelsSenderInstance
     *
     * @return ChannelsSenderInstance Fetched ChannelsSenderInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): ChannelsSenderInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Update the ChannelsSenderInstance
     *
     * @return ChannelsSenderInstance Updated ChannelsSenderInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(): ChannelsSenderInstance
    {

        return $this->proxy()->update();
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Messaging.V2.ChannelsSenderInstance ' . \implode(' ', $context) . ']';
    }
}

