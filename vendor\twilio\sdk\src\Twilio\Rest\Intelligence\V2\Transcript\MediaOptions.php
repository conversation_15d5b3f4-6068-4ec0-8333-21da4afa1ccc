<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Intelligence
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Intelligence\V2\Transcript;

use Twilio\Options;
use Twilio\Values;

abstract class MediaOptions
{
    /**
     * @param bool $redacted Grant access to PII Redacted/Unredacted Media. If redaction is enabled, the default is `true` to access redacted media.
     * @return FetchMediaOptions Options builder
     */
    public static function fetch(
        
        bool $redacted = Values::BOOL_NONE

    ): FetchMediaOptions
    {
        return new FetchMediaOptions(
            $redacted
        );
    }

}

class FetchMediaOptions extends Options
    {
    /**
     * @param bool $redacted Grant access to PII Redacted/Unredacted Media. If redaction is enabled, the default is `true` to access redacted media.
     */
    public function __construct(
        
        bool $redacted = Values::BOOL_NONE

    ) {
        $this->options['redacted'] = $redacted;
    }

    /**
     * Grant access to PII Redacted/Unredacted Media. If redaction is enabled, the default is `true` to access redacted media.
     *
     * @param bool $redacted Grant access to PII Redacted/Unredacted Media. If redaction is enabled, the default is `true` to access redacted media.
     * @return $this Fluent Builder
     */
    public function setRedacted(bool $redacted): self
    {
        $this->options['redacted'] = $redacted;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Intelligence.V2.FetchMediaOptions ' . $options . ']';
    }
}

