<?php
// This file was auto-generated from sdk-root/src/data/rum/2018-05-10/paginators-1.json
return [ 'pagination' => [ 'BatchGetRumMetricDefinitions' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'MetricDefinitions', ], 'GetAppMonitorData' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'Events', ], 'ListAppMonitors' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'AppMonitorSummaries', ], 'ListRumMetricsDestinations' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'Destinations', ], ],];
