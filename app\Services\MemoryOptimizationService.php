<?php

namespace App\Services;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\LazyCollection;

class MemoryOptimizationService
{
    protected $cachePrefix = 'memory_opt_';
    protected $defaultCacheTtl = 3600; // ساعة واحدة

    /**
     * تحسين استعلام مع Lazy Loading
     */
    public function optimizeQuery($model, array $options = [])
    {
        $chunkSize = $options['chunk_size'] ?? 100;
        $relations = $options['relations'] ?? [];
        $columns = $options['columns'] ?? ['*'];
        $useCache = $options['use_cache'] ?? false;
        $cacheKey = $options['cache_key'] ?? null;

        $query = $model->select($columns);

        if (!empty($relations)) {
            $query->with($relations);
        }

        if ($useCache && $cacheKey) {
            return Cache::remember(
                $this->cachePrefix . $cacheKey,
                $this->defaultCacheTtl,
                function () use ($query, $chunkSize) {
                    return $query->lazy($chunkSize);
                }
            );
        }

        return $query->lazy($chunkSize);
    }

    /**
     * معالجة البيانات الكبيرة بـ Chunking
     */
    public function processLargeDataset($model, callable $processor, array $options = [])
    {
        $chunkSize = $options['chunk_size'] ?? 100;
        $relations = $options['relations'] ?? [];
        $conditions = $options['conditions'] ?? [];

        $query = $model->newQuery();

        // تطبيق الشروط
        foreach ($conditions as $field => $value) {
            if (is_array($value)) {
                $query->whereIn($field, $value);
            } else {
                $query->where($field, $value);
            }
        }

        // تحميل العلاقات إذا لزم الأمر
        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query->chunk($chunkSize, $processor);
    }

    /**
     * تحسين الاستعلامات المعقدة
     */
    public function optimizeComplexQuery($baseQuery, array $options = [])
    {
        $useIndex = $options['use_index'] ?? null;
        $selectRaw = $options['select_raw'] ?? null;
        $groupBy = $options['group_by'] ?? null;
        $having = $options['having'] ?? null;

        if ($useIndex) {
            $baseQuery->from(DB::raw($baseQuery->getModel()->getTable() . ' USE INDEX (' . $useIndex . ')'));
        }

        if ($selectRaw) {
            $baseQuery->selectRaw($selectRaw);
        }

        if ($groupBy) {
            $baseQuery->groupBy($groupBy);
        }

        if ($having) {
            $baseQuery->havingRaw($having);
        }

        return $baseQuery;
    }

    /**
     * تحسين العلاقات للنماذج
     */
    public function optimizeModelRelations(Model $model, array $relationConfig = [])
    {
        foreach ($relationConfig as $relation => $config) {
            $method = $config['method'] ?? 'lazy';
            $columns = $config['columns'] ?? ['*'];
            $limit = $config['limit'] ?? null;

            switch ($method) {
                case 'eager':
                    $model->load([$relation => function ($query) use ($columns, $limit) {
                        $query->select($columns);
                        if ($limit) {
                            $query->limit($limit);
                        }
                    }]);
                    break;

                case 'lazy':
                    // العلاقة ستحمل عند الحاجة
                    break;

                case 'count':
                    $model->loadCount($relation);
                    break;
            }
        }

        return $model;
    }

    /**
     * تنظيف الذاكرة
     */
    public function cleanupMemory()
    {
        // تنظيف cache Laravel
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }

        // تنظيف Eloquent model cache
        Model::clearBootedModels();

        return memory_get_usage(true);
    }

    /**
     * مراقبة استهلاك الذاكرة
     */
    public function monitorMemoryUsage($operation = null)
    {
        $memoryUsage = [
            'current' => memory_get_usage(true),
            'peak' => memory_get_peak_usage(true),
            'limit' => ini_get('memory_limit'),
            'operation' => $operation,
            'timestamp' => now(),
        ];

        // تسجيل في log إذا تجاوز حد معين
        $warningThreshold = $this->parseMemoryLimit($memoryUsage['limit']) * 0.8;
        
        if ($memoryUsage['current'] > $warningThreshold) {
            \Log::warning('High memory usage detected', $memoryUsage);
        }

        return $memoryUsage;
    }

    /**
     * تحويل memory limit إلى bytes
     */
    protected function parseMemoryLimit($limit)
    {
        if (is_numeric($limit)) {
            return (int) $limit;
        }

        $unit = strtolower(substr($limit, -1));
        $value = (int) substr($limit, 0, -1);

        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return $value;
        }
    }

    /**
     * تحسين الاستعلامات للتقارير
     */
    public function optimizeReportQuery($model, array $aggregations = [], array $groupBy = [])
    {
        $query = $model->newQuery();

        // إضافة التجميعات
        foreach ($aggregations as $field => $functions) {
            if (is_array($functions)) {
                foreach ($functions as $function) {
                    $query->selectRaw("{$function}({$field}) as {$field}_{$function}");
                }
            } else {
                $query->selectRaw("{$functions}({$field}) as {$field}_{$functions}");
            }
        }

        // إضافة التجميع
        if (!empty($groupBy)) {
            $query->groupBy($groupBy);
        }

        return $query;
    }

    /**
     * تحسين البحث في النصوص
     */
    public function optimizeTextSearch($model, $searchTerm, array $fields = [], array $options = [])
    {
        $useFullText = $options['use_fulltext'] ?? false;
        $caseSensitive = $options['case_sensitive'] ?? false;
        $exactMatch = $options['exact_match'] ?? false;

        $query = $model->newQuery();

        if ($useFullText && !empty($fields)) {
            $fieldsStr = implode(',', $fields);
            $query->whereRaw("MATCH({$fieldsStr}) AGAINST(? IN BOOLEAN MODE)", [$searchTerm]);
        } else {
            $query->where(function ($q) use ($fields, $searchTerm, $caseSensitive, $exactMatch) {
                foreach ($fields as $field) {
                    $operator = $exactMatch ? '=' : 'LIKE';
                    $value = $exactMatch ? $searchTerm : "%{$searchTerm}%";
                    
                    if ($caseSensitive) {
                        $q->orWhere($field, $operator, $value);
                    } else {
                        $q->orWhere(DB::raw("LOWER({$field})"), $operator, strtolower($value));
                    }
                }
            });
        }

        return $query;
    }
}
