<?php
namespace Aws\MediaPackageV2;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS Elemental MediaPackage v2** service.
 * @method \Aws\Result cancelHarvestJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelHarvestJobAsync(array $args = [])
 * @method \Aws\Result createChannel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createChannelAsync(array $args = [])
 * @method \Aws\Result createChannelGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createChannelGroupAsync(array $args = [])
 * @method \Aws\Result createHarvestJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createHarvestJobAsync(array $args = [])
 * @method \Aws\Result createOriginEndpoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createOriginEndpointAsync(array $args = [])
 * @method \Aws\Result deleteChannel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteChannelAsync(array $args = [])
 * @method \Aws\Result deleteChannelGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteChannelGroupAsync(array $args = [])
 * @method \Aws\Result deleteChannelPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteChannelPolicyAsync(array $args = [])
 * @method \Aws\Result deleteOriginEndpoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteOriginEndpointAsync(array $args = [])
 * @method \Aws\Result deleteOriginEndpointPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteOriginEndpointPolicyAsync(array $args = [])
 * @method \Aws\Result getChannel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getChannelAsync(array $args = [])
 * @method \Aws\Result getChannelGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getChannelGroupAsync(array $args = [])
 * @method \Aws\Result getChannelPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getChannelPolicyAsync(array $args = [])
 * @method \Aws\Result getHarvestJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getHarvestJobAsync(array $args = [])
 * @method \Aws\Result getOriginEndpoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getOriginEndpointAsync(array $args = [])
 * @method \Aws\Result getOriginEndpointPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getOriginEndpointPolicyAsync(array $args = [])
 * @method \Aws\Result listChannelGroups(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listChannelGroupsAsync(array $args = [])
 * @method \Aws\Result listChannels(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listChannelsAsync(array $args = [])
 * @method \Aws\Result listHarvestJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listHarvestJobsAsync(array $args = [])
 * @method \Aws\Result listOriginEndpoints(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listOriginEndpointsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result putChannelPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putChannelPolicyAsync(array $args = [])
 * @method \Aws\Result putOriginEndpointPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putOriginEndpointPolicyAsync(array $args = [])
 * @method \Aws\Result resetChannelState(array $args = [])
 * @method \GuzzleHttp\Promise\Promise resetChannelStateAsync(array $args = [])
 * @method \Aws\Result resetOriginEndpointState(array $args = [])
 * @method \GuzzleHttp\Promise\Promise resetOriginEndpointStateAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateChannel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateChannelAsync(array $args = [])
 * @method \Aws\Result updateChannelGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateChannelGroupAsync(array $args = [])
 * @method \Aws\Result updateOriginEndpoint(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateOriginEndpointAsync(array $args = [])
 */
class MediaPackageV2Client extends AwsClient {}
