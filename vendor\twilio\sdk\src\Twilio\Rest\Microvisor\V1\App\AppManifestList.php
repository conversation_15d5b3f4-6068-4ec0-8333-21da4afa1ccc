<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Microvisor
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Microvisor\V1\App;

use Twilio\ListResource;
use Twilio\Version;


class AppManifestList extends ListResource
    {
    /**
     * Construct the AppManifestList
     *
     * @param Version $version Version that contains the resource
     * @param string $appSid A 34-character string that uniquely identifies this App.
     */
    public function __construct(
        Version $version,
        string $appSid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'appSid' =>
            $appSid,
        
        ];
    }

    /**
     * Constructs a AppManifestContext
     */
    public function getContext(
        
    ): AppManifestContext
    {
        return new AppManifestContext(
            $this->version,
            $this->solution['appSid']
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Microvisor.V1.AppManifestList]';
    }
}
