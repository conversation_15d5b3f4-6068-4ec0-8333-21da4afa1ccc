<?php
// This file was auto-generated from sdk-root/src/data/s3control/2018-08-20/paginators-1.json
return [ 'pagination' => [ 'ListAccessGrants' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', ], 'ListAccessGrantsInstances' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', ], 'ListAccessGrantsLocations' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', ], 'ListAccessPoints' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', ], 'ListAccessPointsForDirectoryBuckets' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'AccessPointList', ], 'ListAccessPointsForObjectLambda' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'ObjectLambdaAccessPointList', ], 'ListCallerAccessGrants' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'CallerAccessGrantsList', ], 'ListJobs' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', ], 'ListMultiRegionAccessPoints' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', ], 'ListRegionalBuckets' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', ], 'ListStorageLensConfigurations' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', ], 'ListStorageLensGroups' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', ], ],];
