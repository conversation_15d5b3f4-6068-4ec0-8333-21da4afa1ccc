<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Numbers\V2;

use Twilio\Options;
use Twilio\Values;

abstract class BulkHostedNumberOrderOptions
{

    /**
     * @param string $orderStatus Order status can be used for filtering on Hosted Number Order status values. To see a complete list of order statuses, please check 'https://www.twilio.com/docs/phone-numbers/hosted-numbers/hosted-numbers-api/hosted-number-order-resource#status-values'.
     * @return FetchBulkHostedNumberOrderOptions Options builder
     */
    public static function fetch(
        
        string $orderStatus = Values::NONE

    ): FetchBulkHostedNumberOrderOptions
    {
        return new FetchBulkHostedNumberOrderOptions(
            $orderStatus
        );
    }

}


class FetchBulkHostedNumberOrderOptions extends Options
    {
    /**
     * @param string $orderStatus Order status can be used for filtering on Hosted Number Order status values. To see a complete list of order statuses, please check 'https://www.twilio.com/docs/phone-numbers/hosted-numbers/hosted-numbers-api/hosted-number-order-resource#status-values'.
     */
    public function __construct(
        
        string $orderStatus = Values::NONE

    ) {
        $this->options['orderStatus'] = $orderStatus;
    }

    /**
     * Order status can be used for filtering on Hosted Number Order status values. To see a complete list of order statuses, please check 'https://www.twilio.com/docs/phone-numbers/hosted-numbers/hosted-numbers-api/hosted-number-order-resource#status-values'.
     *
     * @param string $orderStatus Order status can be used for filtering on Hosted Number Order status values. To see a complete list of order statuses, please check 'https://www.twilio.com/docs/phone-numbers/hosted-numbers/hosted-numbers-api/hosted-number-order-resource#status-values'.
     * @return $this Fluent Builder
     */
    public function setOrderStatus(string $orderStatus): self
    {
        $this->options['orderStatus'] = $orderStatus;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Numbers.V2.FetchBulkHostedNumberOrderOptions ' . $options . ']';
    }
}

