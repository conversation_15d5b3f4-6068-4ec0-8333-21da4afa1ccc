<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Numbers\V1;

use Twilio\Options;
use Twilio\Values;

abstract class PortingPortabilityOptions
{
    /**
     * @param string $targetAccountSid Account Sid to which the number will be ported. This can be used to determine if a sub account already has the number in its inventory or a different sub account. If this is not provided, the authenticated account will be assumed to be the target account.
     * @param string $addressSid Address Sid of customer to which the number will be ported.
     * @return FetchPortingPortabilityOptions Options builder
     */
    public static function fetch(
        
        string $targetAccountSid = Values::NONE,
        string $addressSid = Values::NONE

    ): FetchPortingPortabilityOptions
    {
        return new FetchPortingPortabilityOptions(
            $targetAccountSid,
            $addressSid
        );
    }

}

class FetchPortingPortabilityOptions extends Options
    {
    /**
     * @param string $targetAccountSid Account Sid to which the number will be ported. This can be used to determine if a sub account already has the number in its inventory or a different sub account. If this is not provided, the authenticated account will be assumed to be the target account.
     * @param string $addressSid Address Sid of customer to which the number will be ported.
     */
    public function __construct(
        
        string $targetAccountSid = Values::NONE,
        string $addressSid = Values::NONE

    ) {
        $this->options['targetAccountSid'] = $targetAccountSid;
        $this->options['addressSid'] = $addressSid;
    }

    /**
     * Account Sid to which the number will be ported. This can be used to determine if a sub account already has the number in its inventory or a different sub account. If this is not provided, the authenticated account will be assumed to be the target account.
     *
     * @param string $targetAccountSid Account Sid to which the number will be ported. This can be used to determine if a sub account already has the number in its inventory or a different sub account. If this is not provided, the authenticated account will be assumed to be the target account.
     * @return $this Fluent Builder
     */
    public function setTargetAccountSid(string $targetAccountSid): self
    {
        $this->options['targetAccountSid'] = $targetAccountSid;
        return $this;
    }

    /**
     * Address Sid of customer to which the number will be ported.
     *
     * @param string $addressSid Address Sid of customer to which the number will be ported.
     * @return $this Fluent Builder
     */
    public function setAddressSid(string $addressSid): self
    {
        $this->options['addressSid'] = $addressSid;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Numbers.V1.FetchPortingPortabilityOptions ' . $options . ']';
    }
}

