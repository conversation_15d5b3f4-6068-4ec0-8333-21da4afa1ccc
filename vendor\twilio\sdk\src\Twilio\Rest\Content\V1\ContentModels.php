<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Content
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Content\V1;

use Twilio\Values;
abstract class ContentModels
{
    /**
     * @property string $body
    */
    public static function createTwilioText(array $payload = []): TwilioText
    {
        return new TwilioText($payload);
    }

    /**
     * @property string $body
     * @property string[] $media
    */
    public static function createTwilioMedia(array $payload = []): TwilioMedia
    {
        return new TwilioMedia($payload);
    }

    /**
     * @property string $latitude
     * @property string $longitude
     * @property string $label
     * @property string $id
     * @property string $address
    */
    public static function createTwilioLocation(array $payload = []): TwilioLocation
    {
        return new TwilioLocation($payload);
    }

    /**
     * @property string $id
     * @property string $item
     * @property string $description
    */
    public static function createListItem(array $payload = []): ListItem
    {
        return new ListItem($payload);
    }

    /**
     * @property string $body
     * @property string $button
     * @property ListItem[] $items
    */
    public static function createTwilioListPicker(array $payload = []): TwilioListPicker
    {
        return new TwilioListPicker($payload);
    }

    /**
     * @property string $type
     * @property string $title
     * @property string $url
     * @property string $phone
     * @property string $code
     * @property string $id
    */
    public static function createCallToActionAction(array $payload = []): CallToActionAction
    {
        return new CallToActionAction($payload);
    }

    /**
     * @property string $body
     * @property CallToActionAction[] $actions
    */
    public static function createTwilioCallToAction(array $payload = []): TwilioCallToAction
    {
        return new TwilioCallToAction($payload);
    }

    /**
     * @property string $type
     * @property string $title
     * @property string $id
    */
    public static function createQuickReplyAction(array $payload = []): QuickReplyAction
    {
        return new QuickReplyAction($payload);
    }

    /**
     * @property string $body
     * @property QuickReplyAction[] $actions
    */
    public static function createTwilioQuickReply(array $payload = []): TwilioQuickReply
    {
        return new TwilioQuickReply($payload);
    }

    /**
     * @property string $type
     * @property string $title
     * @property string $url
     * @property string $phone
     * @property string $id
     * @property string $code
    */
    public static function createCardAction(array $payload = []): CardAction
    {
        return new CardAction($payload);
    }

    /**
     * @property string $title
     * @property string $subtitle
     * @property string[] $media
     * @property CardAction[] $actions
    */
    public static function createTwilioCard(array $payload = []): TwilioCard
    {
        return new TwilioCard($payload);
    }

    /**
     * @property string $id
     * @property string $sectionTitle
     * @property string $name
     * @property string $mediaUrl
     * @property string $price
     * @property string $description
    */
    public static function createCatalogItem(array $payload = []): CatalogItem
    {
        return new CatalogItem($payload);
    }

    /**
     * @property string $title
     * @property string $body
     * @property string $subtitle
     * @property string $id
     * @property CatalogItem[] $items
     * @property string $dynamicItems
    */
    public static function createTwilioCatalog(array $payload = []): TwilioCatalog
    {
        return new TwilioCatalog($payload);
    }

    /**
     * @property string $type
     * @property string $title
     * @property string $url
     * @property string $phone
     * @property string $id
    */
    public static function createCarouselAction(array $payload = []): CarouselAction
    {
        return new CarouselAction($payload);
    }

    /**
     * @property string $title
     * @property string $body
     * @property string $media
     * @property CarouselAction[] $actions
    */
    public static function createCarouselCard(array $payload = []): CarouselCard
    {
        return new CarouselCard($payload);
    }

    /**
     * @property string $body
     * @property CarouselCard[] $cards
    */
    public static function createTwilioCarousel(array $payload = []): TwilioCarousel
    {
        return new TwilioCarousel($payload);
    }

    /**
     * @property string $label
     * @property string $type
    */
    public static function createFlowsPageComponent(array $payload = []): FlowsPageComponent
    {
        return new FlowsPageComponent($payload);
    }

    /**
     * @property string $id
     * @property string $nextPageId
     * @property string $title
     * @property string $subtitle
     * @property FlowsPageComponent[] $layout
    */
    public static function createFlowsPage(array $payload = []): FlowsPage
    {
        return new FlowsPage($payload);
    }

    /**
     * @property string $body
     * @property string $buttonText
     * @property string $subtitle
     * @property string $mediaUrl
     * @property FlowsPage[] $pages
     * @property string $type
    */
    public static function createTwilioFlows(array $payload = []): TwilioFlows
    {
        return new TwilioFlows($payload);
    }

    /**
     * @property string $id
     * @property string $title
     * @property string $timeSlots
    */
    public static function createTwilioSchedule(array $payload = []): TwilioSchedule
    {
        return new TwilioSchedule($payload);
    }

    /**
     * @property string $body
     * @property string $footer
     * @property string[] $media
     * @property string $headerText
     * @property CardAction[] $actions
    */
    public static function createWhatsappCard(array $payload = []): WhatsappCard
    {
        return new WhatsappCard($payload);
    }

    /**
     * @property string $type
     * @property string $copyCodeText
    */
    public static function createAuthenticationAction(array $payload = []): AuthenticationAction
    {
        return new AuthenticationAction($payload);
    }

    /**
     * @property bool $addSecurityRecommendation
     * @property string $codeExpirationMinutes
     * @property AuthenticationAction[] $actions
    */
    public static function createWhatsappAuthentication(array $payload = []): WhatsappAuthentication
    {
        return new WhatsappAuthentication($payload);
    }

    /**
     * @property TwilioText $twilioText
     * @property TwilioMedia $twilioMedia
     * @property TwilioLocation $twilioLocation
     * @property TwilioListPicker $twilioListPicker
     * @property TwilioCallToAction $twilioCallToAction
     * @property TwilioQuickReply $twilioQuickReply
     * @property TwilioCard $twilioCard
     * @property TwilioCatalog $twilioCatalog
     * @property TwilioCarousel $twilioCarousel
     * @property TwilioFlows $twilioFlows
     * @property TwilioSchedule $twilioSchedule
     * @property WhatsappCard $whatsappCard
     * @property WhatsappAuthentication $whatsappAuthentication
    */
    public static function createTypes(array $payload = []): Types
    {
        return new Types($payload);
    }

    /**
     * @property string $friendlyName User defined name of the content
     * @property array<string,string> $variables Key value pairs of variable name to value
     * @property string $language Language code for the content
     * @property Types $types
    */
    public static function createContentCreateRequest(array $payload = []): ContentCreateRequest
    {
        return new ContentCreateRequest($payload);
    }

}

class TwilioText implements \JsonSerializable
{
    /**
     * @property string $body
    */
        protected $body;
    public function __construct(array $payload = []) {
        $this->body = Values::array_get($payload, 'body');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'body' => $this->body
        ];
    }
}

class TwilioMedia implements \JsonSerializable
{
    /**
     * @property string $body
     * @property string[] $media
    */
        protected $body;
        protected $media;
    public function __construct(array $payload = []) {
        $this->body = Values::array_get($payload, 'body');
        $this->media = Values::array_get($payload, 'media');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'body' => $this->body,
            'media' => $this->media
        ];
    }
}

class TwilioLocation implements \JsonSerializable
{
    /**
     * @property string $latitude
     * @property string $longitude
     * @property string $label
     * @property string $id
     * @property string $address
    */
        protected $latitude;
        protected $longitude;
        protected $label;
        protected $id;
        protected $address;
    public function __construct(array $payload = []) {
        $this->latitude = Values::array_get($payload, 'latitude');
        $this->longitude = Values::array_get($payload, 'longitude');
        $this->label = Values::array_get($payload, 'label');
        $this->id = Values::array_get($payload, 'id');
        $this->address = Values::array_get($payload, 'address');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'label' => $this->label,
            'id' => $this->id,
            'address' => $this->address
        ];
    }
}

class ListItem implements \JsonSerializable
{
    /**
     * @property string $id
     * @property string $item
     * @property string $description
    */
        protected $id;
        protected $item;
        protected $description;
    public function __construct(array $payload = []) {
        $this->id = Values::array_get($payload, 'id');
        $this->item = Values::array_get($payload, 'item');
        $this->description = Values::array_get($payload, 'description');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'id' => $this->id,
            'item' => $this->item,
            'description' => $this->description
        ];
    }
}

class TwilioListPicker implements \JsonSerializable
{
    /**
     * @property string $body
     * @property string $button
     * @property ListItem[] $items
    */
        protected $body;
        protected $button;
        protected $items;
    public function __construct(array $payload = []) {
        $this->body = Values::array_get($payload, 'body');
        $this->button = Values::array_get($payload, 'button');
        $this->items = Values::array_get($payload, 'items');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'body' => $this->body,
            'button' => $this->button,
            'items' => $this->items
        ];
    }
}

class CallToActionAction implements \JsonSerializable
{
    /**
     * @property string $type
     * @property string $title
     * @property string $url
     * @property string $phone
     * @property string $code
     * @property string $id
    */
        protected $type;
        protected $title;
        protected $url;
        protected $phone;
        protected $code;
        protected $id;
    public function __construct(array $payload = []) {
        $this->type = Values::array_get($payload, 'type');
        $this->title = Values::array_get($payload, 'title');
        $this->url = Values::array_get($payload, 'url');
        $this->phone = Values::array_get($payload, 'phone');
        $this->code = Values::array_get($payload, 'code');
        $this->id = Values::array_get($payload, 'id');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'type' => $this->type,
            'title' => $this->title,
            'url' => $this->url,
            'phone' => $this->phone,
            'code' => $this->code,
            'id' => $this->id
        ];
    }
}

class TwilioCallToAction implements \JsonSerializable
{
    /**
     * @property string $body
     * @property CallToActionAction[] $actions
    */
        protected $body;
        protected $actions;
    public function __construct(array $payload = []) {
        $this->body = Values::array_get($payload, 'body');
        $this->actions = Values::array_get($payload, 'actions');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'body' => $this->body,
            'actions' => $this->actions
        ];
    }
}

class QuickReplyAction implements \JsonSerializable
{
    /**
     * @property string $type
     * @property string $title
     * @property string $id
    */
        protected $type;
        protected $title;
        protected $id;
    public function __construct(array $payload = []) {
        $this->type = Values::array_get($payload, 'type');
        $this->title = Values::array_get($payload, 'title');
        $this->id = Values::array_get($payload, 'id');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'type' => $this->type,
            'title' => $this->title,
            'id' => $this->id
        ];
    }
}

class TwilioQuickReply implements \JsonSerializable
{
    /**
     * @property string $body
     * @property QuickReplyAction[] $actions
    */
        protected $body;
        protected $actions;
    public function __construct(array $payload = []) {
        $this->body = Values::array_get($payload, 'body');
        $this->actions = Values::array_get($payload, 'actions');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'body' => $this->body,
            'actions' => $this->actions
        ];
    }
}

class CardAction implements \JsonSerializable
{
    /**
     * @property string $type
     * @property string $title
     * @property string $url
     * @property string $phone
     * @property string $id
     * @property string $code
    */
        protected $type;
        protected $title;
        protected $url;
        protected $phone;
        protected $id;
        protected $code;
    public function __construct(array $payload = []) {
        $this->type = Values::array_get($payload, 'type');
        $this->title = Values::array_get($payload, 'title');
        $this->url = Values::array_get($payload, 'url');
        $this->phone = Values::array_get($payload, 'phone');
        $this->id = Values::array_get($payload, 'id');
        $this->code = Values::array_get($payload, 'code');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'type' => $this->type,
            'title' => $this->title,
            'url' => $this->url,
            'phone' => $this->phone,
            'id' => $this->id,
            'code' => $this->code
        ];
    }
}

class TwilioCard implements \JsonSerializable
{
    /**
     * @property string $title
     * @property string $subtitle
     * @property string[] $media
     * @property CardAction[] $actions
    */
        protected $title;
        protected $subtitle;
        protected $media;
        protected $actions;
    public function __construct(array $payload = []) {
        $this->title = Values::array_get($payload, 'title');
        $this->subtitle = Values::array_get($payload, 'subtitle');
        $this->media = Values::array_get($payload, 'media');
        $this->actions = Values::array_get($payload, 'actions');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'title' => $this->title,
            'subtitle' => $this->subtitle,
            'media' => $this->media,
            'actions' => $this->actions
        ];
    }
}

class CatalogItem implements \JsonSerializable
{
    /**
     * @property string $id
     * @property string $sectionTitle
     * @property string $name
     * @property string $mediaUrl
     * @property string $price
     * @property string $description
    */
        protected $id;
        protected $sectionTitle;
        protected $name;
        protected $mediaUrl;
        protected $price;
        protected $description;
    public function __construct(array $payload = []) {
        $this->id = Values::array_get($payload, 'id');
        $this->sectionTitle = Values::array_get($payload, 'section_title');
        $this->name = Values::array_get($payload, 'name');
        $this->mediaUrl = Values::array_get($payload, 'media_url');
        $this->price = Values::array_get($payload, 'price');
        $this->description = Values::array_get($payload, 'description');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'id' => $this->id,
            'section_title' => $this->sectionTitle,
            'name' => $this->name,
            'media_url' => $this->mediaUrl,
            'price' => $this->price,
            'description' => $this->description
        ];
    }
}

class TwilioCatalog implements \JsonSerializable
{
    /**
     * @property string $title
     * @property string $body
     * @property string $subtitle
     * @property string $id
     * @property CatalogItem[] $items
     * @property string $dynamicItems
    */
        protected $title;
        protected $body;
        protected $subtitle;
        protected $id;
        protected $items;
        protected $dynamicItems;
    public function __construct(array $payload = []) {
        $this->title = Values::array_get($payload, 'title');
        $this->body = Values::array_get($payload, 'body');
        $this->subtitle = Values::array_get($payload, 'subtitle');
        $this->id = Values::array_get($payload, 'id');
        $this->items = Values::array_get($payload, 'items');
        $this->dynamicItems = Values::array_get($payload, 'dynamic_items');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'title' => $this->title,
            'body' => $this->body,
            'subtitle' => $this->subtitle,
            'id' => $this->id,
            'items' => $this->items,
            'dynamic_items' => $this->dynamicItems
        ];
    }
}

class CarouselAction implements \JsonSerializable
{
    /**
     * @property string $type
     * @property string $title
     * @property string $url
     * @property string $phone
     * @property string $id
    */
        protected $type;
        protected $title;
        protected $url;
        protected $phone;
        protected $id;
    public function __construct(array $payload = []) {
        $this->type = Values::array_get($payload, 'type');
        $this->title = Values::array_get($payload, 'title');
        $this->url = Values::array_get($payload, 'url');
        $this->phone = Values::array_get($payload, 'phone');
        $this->id = Values::array_get($payload, 'id');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'type' => $this->type,
            'title' => $this->title,
            'url' => $this->url,
            'phone' => $this->phone,
            'id' => $this->id
        ];
    }
}

class CarouselCard implements \JsonSerializable
{
    /**
     * @property string $title
     * @property string $body
     * @property string $media
     * @property CarouselAction[] $actions
    */
        protected $title;
        protected $body;
        protected $media;
        protected $actions;
    public function __construct(array $payload = []) {
        $this->title = Values::array_get($payload, 'title');
        $this->body = Values::array_get($payload, 'body');
        $this->media = Values::array_get($payload, 'media');
        $this->actions = Values::array_get($payload, 'actions');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'title' => $this->title,
            'body' => $this->body,
            'media' => $this->media,
            'actions' => $this->actions
        ];
    }
}

class TwilioCarousel implements \JsonSerializable
{
    /**
     * @property string $body
     * @property CarouselCard[] $cards
    */
        protected $body;
        protected $cards;
    public function __construct(array $payload = []) {
        $this->body = Values::array_get($payload, 'body');
        $this->cards = Values::array_get($payload, 'cards');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'body' => $this->body,
            'cards' => $this->cards
        ];
    }
}

class FlowsPageComponent implements \JsonSerializable
{
    /**
     * @property string $label
     * @property string $type
    */
        protected $label;
        protected $type;
    public function __construct(array $payload = []) {
        $this->label = Values::array_get($payload, 'label');
        $this->type = Values::array_get($payload, 'type');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'label' => $this->label,
            'type' => $this->type
        ];
    }
}

class FlowsPage implements \JsonSerializable
{
    /**
     * @property string $id
     * @property string $nextPageId
     * @property string $title
     * @property string $subtitle
     * @property FlowsPageComponent[] $layout
    */
        protected $id;
        protected $nextPageId;
        protected $title;
        protected $subtitle;
        protected $layout;
    public function __construct(array $payload = []) {
        $this->id = Values::array_get($payload, 'id');
        $this->nextPageId = Values::array_get($payload, 'next_page_id');
        $this->title = Values::array_get($payload, 'title');
        $this->subtitle = Values::array_get($payload, 'subtitle');
        $this->layout = Values::array_get($payload, 'layout');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'id' => $this->id,
            'next_page_id' => $this->nextPageId,
            'title' => $this->title,
            'subtitle' => $this->subtitle,
            'layout' => $this->layout
        ];
    }
}

class TwilioFlows implements \JsonSerializable
{
    /**
     * @property string $body
     * @property string $buttonText
     * @property string $subtitle
     * @property string $mediaUrl
     * @property FlowsPage[] $pages
     * @property string $type
    */
        protected $body;
        protected $buttonText;
        protected $subtitle;
        protected $mediaUrl;
        protected $pages;
        protected $type;
    public function __construct(array $payload = []) {
        $this->body = Values::array_get($payload, 'body');
        $this->buttonText = Values::array_get($payload, 'button_text');
        $this->subtitle = Values::array_get($payload, 'subtitle');
        $this->mediaUrl = Values::array_get($payload, 'media_url');
        $this->pages = Values::array_get($payload, 'pages');
        $this->type = Values::array_get($payload, 'type');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'body' => $this->body,
            'button_text' => $this->buttonText,
            'subtitle' => $this->subtitle,
            'media_url' => $this->mediaUrl,
            'pages' => $this->pages,
            'type' => $this->type
        ];
    }
}

class TwilioSchedule implements \JsonSerializable
{
    /**
     * @property string $id
     * @property string $title
     * @property string $timeSlots
    */
        protected $id;
        protected $title;
        protected $timeSlots;
    public function __construct(array $payload = []) {
        $this->id = Values::array_get($payload, 'id');
        $this->title = Values::array_get($payload, 'title');
        $this->timeSlots = Values::array_get($payload, 'timeSlots');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'timeSlots' => $this->timeSlots
        ];
    }
}

class WhatsappCard implements \JsonSerializable
{
    /**
     * @property string $body
     * @property string $footer
     * @property string[] $media
     * @property string $headerText
     * @property CardAction[] $actions
    */
        protected $body;
        protected $footer;
        protected $media;
        protected $headerText;
        protected $actions;
    public function __construct(array $payload = []) {
        $this->body = Values::array_get($payload, 'body');
        $this->footer = Values::array_get($payload, 'footer');
        $this->media = Values::array_get($payload, 'media');
        $this->headerText = Values::array_get($payload, 'header_text');
        $this->actions = Values::array_get($payload, 'actions');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'body' => $this->body,
            'footer' => $this->footer,
            'media' => $this->media,
            'header_text' => $this->headerText,
            'actions' => $this->actions
        ];
    }
}

class AuthenticationAction implements \JsonSerializable
{
    /**
     * @property string $type
     * @property string $copyCodeText
    */
        protected $type;
        protected $copyCodeText;
    public function __construct(array $payload = []) {
        $this->type = Values::array_get($payload, 'type');
        $this->copyCodeText = Values::array_get($payload, 'copy_code_text');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'type' => $this->type,
            'copy_code_text' => $this->copyCodeText
        ];
    }
}

class WhatsappAuthentication implements \JsonSerializable
{
    /**
     * @property bool $addSecurityRecommendation
     * @property string $codeExpirationMinutes
     * @property AuthenticationAction[] $actions
    */
        protected $addSecurityRecommendation;
        protected $codeExpirationMinutes;
        protected $actions;
    public function __construct(array $payload = []) {
        $this->addSecurityRecommendation = Values::array_get($payload, 'add_security_recommendation');
        $this->codeExpirationMinutes = Values::array_get($payload, 'code_expiration_minutes');
        $this->actions = Values::array_get($payload, 'actions');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'add_security_recommendation' => $this->addSecurityRecommendation,
            'code_expiration_minutes' => $this->codeExpirationMinutes,
            'actions' => $this->actions
        ];
    }
}

class Types implements \JsonSerializable
{
    /**
     * @property TwilioText $twilioText
     * @property TwilioMedia $twilioMedia
     * @property TwilioLocation $twilioLocation
     * @property TwilioListPicker $twilioListPicker
     * @property TwilioCallToAction $twilioCallToAction
     * @property TwilioQuickReply $twilioQuickReply
     * @property TwilioCard $twilioCard
     * @property TwilioCatalog $twilioCatalog
     * @property TwilioCarousel $twilioCarousel
     * @property TwilioFlows $twilioFlows
     * @property TwilioSchedule $twilioSchedule
     * @property WhatsappCard $whatsappCard
     * @property WhatsappAuthentication $whatsappAuthentication
    */
        protected $twilioText;
        protected $twilioMedia;
        protected $twilioLocation;
        protected $twilioListPicker;
        protected $twilioCallToAction;
        protected $twilioQuickReply;
        protected $twilioCard;
        protected $twilioCatalog;
        protected $twilioCarousel;
        protected $twilioFlows;
        protected $twilioSchedule;
        protected $whatsappCard;
        protected $whatsappAuthentication;
    public function __construct(array $payload = []) {
        $this->twilioText = Values::array_get($payload, 'twilio/text');
        $this->twilioMedia = Values::array_get($payload, 'twilio/media');
        $this->twilioLocation = Values::array_get($payload, 'twilio/location');
        $this->twilioListPicker = Values::array_get($payload, 'twilio/list-picker');
        $this->twilioCallToAction = Values::array_get($payload, 'twilio/call-to-action');
        $this->twilioQuickReply = Values::array_get($payload, 'twilio/quick-reply');
        $this->twilioCard = Values::array_get($payload, 'twilio/card');
        $this->twilioCatalog = Values::array_get($payload, 'twilio/catalog');
        $this->twilioCarousel = Values::array_get($payload, 'twilio/carousel');
        $this->twilioFlows = Values::array_get($payload, 'twilio/flows');
        $this->twilioSchedule = Values::array_get($payload, 'twilio/schedule');
        $this->whatsappCard = Values::array_get($payload, 'whatsapp/card');
        $this->whatsappAuthentication = Values::array_get($payload, 'whatsapp/authentication');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'twilio/text' => $this->twilioText,
            'twilio/media' => $this->twilioMedia,
            'twilio/location' => $this->twilioLocation,
            'twilio/list-picker' => $this->twilioListPicker,
            'twilio/call-to-action' => $this->twilioCallToAction,
            'twilio/quick-reply' => $this->twilioQuickReply,
            'twilio/card' => $this->twilioCard,
            'twilio/catalog' => $this->twilioCatalog,
            'twilio/carousel' => $this->twilioCarousel,
            'twilio/flows' => $this->twilioFlows,
            'twilio/schedule' => $this->twilioSchedule,
            'whatsapp/card' => $this->whatsappCard,
            'whatsapp/authentication' => $this->whatsappAuthentication
        ];
    }
}

class ContentCreateRequest implements \JsonSerializable
{
    /**
     * @property string $friendlyName User defined name of the content
     * @property array<string,string> $variables Key value pairs of variable name to value
     * @property string $language Language code for the content
     * @property Types $types
    */
        protected $friendlyName;
        protected $variables;
        protected $language;
        protected $types;
    public function __construct(array $payload = []) {
        $this->friendlyName = Values::array_get($payload, 'friendly_name');
        $this->variables = Values::array_get($payload, 'variables');
        $this->language = Values::array_get($payload, 'language');
        $this->types = Values::array_get($payload, 'types');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'friendly_name' => $this->friendlyName,
            'variables' => $this->variables,
            'language' => $this->language,
            'types' => $this->types
        ];
    }
}

