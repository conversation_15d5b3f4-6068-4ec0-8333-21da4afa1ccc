<?php
// This file was auto-generated from sdk-root/src/data/workspaces-instances/2022-07-26/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2022-07-26', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'workspaces-instances', 'jsonVersion' => '1.0', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'Amazon Workspaces Instances', 'serviceId' => 'Workspaces Instances', 'signatureVersion' => 'v4', 'signingName' => 'workspaces-instances', 'targetPrefix' => 'EUCMIFrontendAPIService', 'uid' => 'workspaces-instances-2022-07-26', ], 'operations' => [ 'AssociateVolume' => [ 'name' => 'AssociateVolume', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateVolumeRequest', ], 'output' => [ 'shape' => 'AssociateVolumeResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateVolume' => [ 'name' => 'CreateVolume', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateVolumeRequest', ], 'output' => [ 'shape' => 'CreateVolumeResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'CreateWorkspaceInstance' => [ 'name' => 'CreateWorkspaceInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateWorkspaceInstanceRequest', ], 'output' => [ 'shape' => 'CreateWorkspaceInstanceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteVolume' => [ 'name' => 'DeleteVolume', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteVolumeRequest', ], 'output' => [ 'shape' => 'DeleteVolumeResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteWorkspaceInstance' => [ 'name' => 'DeleteWorkspaceInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteWorkspaceInstanceRequest', ], 'output' => [ 'shape' => 'DeleteWorkspaceInstanceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'DisassociateVolume' => [ 'name' => 'DisassociateVolume', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateVolumeRequest', ], 'output' => [ 'shape' => 'DisassociateVolumeResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetWorkspaceInstance' => [ 'name' => 'GetWorkspaceInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetWorkspaceInstanceRequest', ], 'output' => [ 'shape' => 'GetWorkspaceInstanceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListInstanceTypes' => [ 'name' => 'ListInstanceTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListInstanceTypesRequest', ], 'output' => [ 'shape' => 'ListInstanceTypesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListRegions' => [ 'name' => 'ListRegions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRegionsRequest', ], 'output' => [ 'shape' => 'ListRegionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListWorkspaceInstances' => [ 'name' => 'ListWorkspaceInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListWorkspaceInstancesRequest', ], 'output' => [ 'shape' => 'ListWorkspaceInstancesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'ARN' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => 'arn:.*', ], 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'AmdSevSnpEnum' => [ 'type' => 'string', 'enum' => [ 'enabled', 'disabled', ], ], 'AssociateVolumeRequest' => [ 'type' => 'structure', 'required' => [ 'WorkspaceInstanceId', 'VolumeId', 'Device', ], 'members' => [ 'WorkspaceInstanceId' => [ 'shape' => 'WorkspaceInstanceId', ], 'VolumeId' => [ 'shape' => 'VolumeId', ], 'Device' => [ 'shape' => 'DeviceName', ], ], ], 'AssociateVolumeResponse' => [ 'type' => 'structure', 'members' => [], ], 'AutoRecoveryEnum' => [ 'type' => 'string', 'enum' => [ 'disabled', 'default', ], ], 'AvailabilityZone' => [ 'type' => 'string', 'pattern' => '[a-z]{2}-[a-z]+-\\d[a-z](-[a-z0-9]+)?', ], 'BandwidthWeightingEnum' => [ 'type' => 'string', 'enum' => [ 'default', 'vpc-1', 'ebs-1', ], ], 'BlockDeviceMappingRequest' => [ 'type' => 'structure', 'members' => [ 'DeviceName' => [ 'shape' => 'DeviceName', ], 'Ebs' => [ 'shape' => 'EbsBlockDevice', ], 'NoDevice' => [ 'shape' => 'DeviceName', ], 'VirtualName' => [ 'shape' => 'VirtualName', ], ], ], 'BlockDeviceMappings' => [ 'type' => 'list', 'member' => [ 'shape' => 'BlockDeviceMappingRequest', ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'CapacityReservationPreferenceEnum' => [ 'type' => 'string', 'enum' => [ 'capacity-reservations-only', 'open', 'none', ], ], 'CapacityReservationSpecification' => [ 'type' => 'structure', 'members' => [ 'CapacityReservationPreference' => [ 'shape' => 'CapacityReservationPreferenceEnum', ], 'CapacityReservationTarget' => [ 'shape' => 'CapacityReservationTarget', ], ], ], 'CapacityReservationTarget' => [ 'type' => 'structure', 'members' => [ 'CapacityReservationId' => [ 'shape' => 'String128', ], 'CapacityReservationResourceGroupArn' => [ 'shape' => 'ARN', ], ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'sensitive' => true, ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'Message', 'ResourceId', 'ResourceType', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ConnectionTrackingSpecificationRequest' => [ 'type' => 'structure', 'members' => [ 'TcpEstablishedTimeout' => [ 'shape' => 'NonNegativeInteger', ], 'UdpStreamTimeout' => [ 'shape' => 'NonNegativeInteger', ], 'UdpTimeout' => [ 'shape' => 'NonNegativeInteger', ], ], ], 'CpuCreditsEnum' => [ 'type' => 'string', 'enum' => [ 'standard', 'unlimited', ], ], 'CpuOptionsRequest' => [ 'type' => 'structure', 'members' => [ 'AmdSevSnp' => [ 'shape' => 'AmdSevSnpEnum', ], 'CoreCount' => [ 'shape' => 'NonNegativeInteger', ], 'ThreadsPerCore' => [ 'shape' => 'NonNegativeInteger', ], ], ], 'CreateVolumeRequest' => [ 'type' => 'structure', 'required' => [ 'AvailabilityZone', ], 'members' => [ 'AvailabilityZone' => [ 'shape' => 'String64', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'Encrypted' => [ 'shape' => 'Boolean', ], 'Iops' => [ 'shape' => 'NonNegativeInteger', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'SizeInGB' => [ 'shape' => 'NonNegativeInteger', ], 'SnapshotId' => [ 'shape' => 'SnapshotId', ], 'TagSpecifications' => [ 'shape' => 'TagSpecifications', ], 'Throughput' => [ 'shape' => 'NonNegativeInteger', ], 'VolumeType' => [ 'shape' => 'VolumeTypeEnum', ], ], ], 'CreateVolumeResponse' => [ 'type' => 'structure', 'members' => [ 'VolumeId' => [ 'shape' => 'VolumeId', ], ], ], 'CreateWorkspaceInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'ManagedInstance', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], 'ManagedInstance' => [ 'shape' => 'ManagedInstanceRequest', ], ], ], 'CreateWorkspaceInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'WorkspaceInstanceId' => [ 'shape' => 'WorkspaceInstanceId', ], ], ], 'CreditSpecificationRequest' => [ 'type' => 'structure', 'members' => [ 'CpuCredits' => [ 'shape' => 'CpuCreditsEnum', ], ], ], 'DeleteVolumeRequest' => [ 'type' => 'structure', 'required' => [ 'VolumeId', ], 'members' => [ 'VolumeId' => [ 'shape' => 'VolumeId', ], ], ], 'DeleteVolumeResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWorkspaceInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'WorkspaceInstanceId', ], 'members' => [ 'WorkspaceInstanceId' => [ 'shape' => 'WorkspaceInstanceId', ], ], ], 'DeleteWorkspaceInstanceResponse' => [ 'type' => 'structure', 'members' => [], ], 'Description' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'pattern' => '[\\S\\s]*', ], 'DeviceName' => [ 'type' => 'string', 'max' => 32, 'min' => 0, ], 'DisassociateModeEnum' => [ 'type' => 'string', 'enum' => [ 'FORCE', 'NO_FORCE', ], ], 'DisassociateVolumeRequest' => [ 'type' => 'structure', 'required' => [ 'WorkspaceInstanceId', 'VolumeId', ], 'members' => [ 'WorkspaceInstanceId' => [ 'shape' => 'WorkspaceInstanceId', ], 'VolumeId' => [ 'shape' => 'VolumeId', ], 'Device' => [ 'shape' => 'DeviceName', ], 'DisassociateMode' => [ 'shape' => 'DisassociateModeEnum', ], ], ], 'DisassociateVolumeResponse' => [ 'type' => 'structure', 'members' => [], ], 'EC2InstanceError' => [ 'type' => 'structure', 'members' => [ 'EC2ErrorCode' => [ 'shape' => 'String', ], 'EC2ExceptionType' => [ 'shape' => 'String', ], 'EC2ErrorMessage' => [ 'shape' => 'String', ], ], ], 'EC2InstanceErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'EC2InstanceError', ], ], 'EC2ManagedInstance' => [ 'type' => 'structure', 'members' => [ 'InstanceId' => [ 'shape' => 'String', ], ], ], 'EbsBlockDevice' => [ 'type' => 'structure', 'members' => [ 'VolumeType' => [ 'shape' => 'VolumeTypeEnum', ], 'Encrypted' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'Iops' => [ 'shape' => 'NonNegativeInteger', ], 'Throughput' => [ 'shape' => 'NonNegativeInteger', ], 'VolumeSize' => [ 'shape' => 'NonNegativeInteger', ], ], ], 'EnaSrdSpecificationRequest' => [ 'type' => 'structure', 'members' => [ 'EnaSrdEnabled' => [ 'shape' => 'Boolean', ], 'EnaSrdUdpSpecification' => [ 'shape' => 'EnaSrdUdpSpecificationRequest', ], ], ], 'EnaSrdUdpSpecificationRequest' => [ 'type' => 'structure', 'members' => [ 'EnaSrdUdpEnabled' => [ 'shape' => 'Boolean', ], ], ], 'EnclaveOptionsRequest' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'GetWorkspaceInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'WorkspaceInstanceId', ], 'members' => [ 'WorkspaceInstanceId' => [ 'shape' => 'WorkspaceInstanceId', ], ], ], 'GetWorkspaceInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'WorkspaceInstanceErrors' => [ 'shape' => 'WorkspaceInstanceErrors', ], 'EC2InstanceErrors' => [ 'shape' => 'EC2InstanceErrors', ], 'ProvisionState' => [ 'shape' => 'ProvisionStateEnum', ], 'WorkspaceInstanceId' => [ 'shape' => 'WorkspaceInstanceId', ], 'EC2ManagedInstance' => [ 'shape' => 'EC2ManagedInstance', ], ], ], 'HibernationOptionsRequest' => [ 'type' => 'structure', 'members' => [ 'Configured' => [ 'shape' => 'Boolean', ], ], ], 'HostId' => [ 'type' => 'string', 'pattern' => 'h-[0-9a-zA-Z]{1,63}', ], 'HostnameTypeEnum' => [ 'type' => 'string', 'enum' => [ 'ip-name', 'resource-name', ], ], 'HttpEndpointEnum' => [ 'type' => 'string', 'enum' => [ 'enabled', 'disabled', ], ], 'HttpProtocolIpv6Enum' => [ 'type' => 'string', 'enum' => [ 'enabled', 'disabled', ], ], 'HttpPutResponseHopLimit' => [ 'type' => 'integer', 'box' => true, 'max' => 64, 'min' => 1, ], 'HttpTokensEnum' => [ 'type' => 'string', 'enum' => [ 'optional', 'required', ], ], 'IamInstanceProfileSpecification' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'String64', ], ], ], 'ImageId' => [ 'type' => 'string', 'pattern' => 'ami-[0-9a-zA-Z]{1,63}', ], 'InstanceInterruptionBehaviorEnum' => [ 'type' => 'string', 'enum' => [ 'hibernate', 'stop', ], ], 'InstanceIpv6Address' => [ 'type' => 'structure', 'members' => [ 'Ipv6Address' => [ 'shape' => 'Ipv6Address', ], 'IsPrimaryIpv6' => [ 'shape' => 'Boolean', ], ], ], 'InstanceMaintenanceOptionsRequest' => [ 'type' => 'structure', 'members' => [ 'AutoRecovery' => [ 'shape' => 'AutoRecoveryEnum', ], ], ], 'InstanceMarketOptionsRequest' => [ 'type' => 'structure', 'members' => [ 'MarketType' => [ 'shape' => 'MarketTypeEnum', ], 'SpotOptions' => [ 'shape' => 'SpotMarketOptions', ], ], ], 'InstanceMetadataOptionsRequest' => [ 'type' => 'structure', 'members' => [ 'HttpEndpoint' => [ 'shape' => 'HttpEndpointEnum', ], 'HttpProtocolIpv6' => [ 'shape' => 'HttpProtocolIpv6Enum', ], 'HttpPutResponseHopLimit' => [ 'shape' => 'HttpPutResponseHopLimit', ], 'HttpTokens' => [ 'shape' => 'HttpTokensEnum', ], 'InstanceMetadataTags' => [ 'shape' => 'InstanceMetadataTagsEnum', ], ], ], 'InstanceMetadataTagsEnum' => [ 'type' => 'string', 'enum' => [ 'enabled', 'disabled', ], ], 'InstanceNetworkInterfaceSpecification' => [ 'type' => 'structure', 'members' => [ 'AssociateCarrierIpAddress' => [ 'shape' => 'Boolean', ], 'AssociatePublicIpAddress' => [ 'shape' => 'Boolean', ], 'ConnectionTrackingSpecification' => [ 'shape' => 'ConnectionTrackingSpecificationRequest', ], 'Description' => [ 'shape' => 'Description', ], 'DeviceIndex' => [ 'shape' => 'NonNegativeInteger', ], 'EnaSrdSpecification' => [ 'shape' => 'EnaSrdSpecificationRequest', ], 'InterfaceType' => [ 'shape' => 'InterfaceTypeEnum', ], 'Ipv4Prefixes' => [ 'shape' => 'Ipv4Prefixes', ], 'Ipv4PrefixCount' => [ 'shape' => 'NonNegativeInteger', ], 'Ipv6AddressCount' => [ 'shape' => 'NonNegativeInteger', ], 'Ipv6Addresses' => [ 'shape' => 'Ipv6Addresses', ], 'Ipv6Prefixes' => [ 'shape' => 'Ipv6Prefixes', ], 'Ipv6PrefixCount' => [ 'shape' => 'NonNegativeInteger', ], 'NetworkCardIndex' => [ 'shape' => 'NonNegativeInteger', ], 'NetworkInterfaceId' => [ 'shape' => 'NetworkInterfaceId', ], 'PrimaryIpv6' => [ 'shape' => 'Boolean', ], 'PrivateIpAddress' => [ 'shape' => 'Ipv4Address', ], 'PrivateIpAddresses' => [ 'shape' => 'PrivateIpAddresses', ], 'SecondaryPrivateIpAddressCount' => [ 'shape' => 'NonNegativeInteger', ], 'Groups' => [ 'shape' => 'SecurityGroupIds', ], 'SubnetId' => [ 'shape' => 'SubnetId', ], ], ], 'InstanceNetworkPerformanceOptionsRequest' => [ 'type' => 'structure', 'members' => [ 'BandwidthWeighting' => [ 'shape' => 'BandwidthWeightingEnum', ], ], ], 'InstanceType' => [ 'type' => 'string', 'pattern' => '([a-z0-9-]+)\\.([a-z0-9]+)', ], 'InstanceTypeInfo' => [ 'type' => 'structure', 'members' => [ 'InstanceType' => [ 'shape' => 'InstanceType', ], ], ], 'InstanceTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceTypeInfo', ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InterfaceTypeEnum' => [ 'type' => 'string', 'enum' => [ 'interface', 'efa', 'efa-only', ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'RetryAfterSeconds' => [ 'shape' => 'Integer', ], ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'Ipv4Address' => [ 'type' => 'string', 'pattern' => '(\\b25[0-5]|\\b2[0-4][0-9]|\\b[01]?[0-9][0-9]?)(\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}', 'sensitive' => true, ], 'Ipv4Prefix' => [ 'type' => 'string', 'pattern' => '.*(?:(?:\\d|[01]?\\d\\d|2[0-4]\\d|25[0-5])\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d|\\d)(?:/\\d{1,2})?.*', ], 'Ipv4PrefixSpecificationRequest' => [ 'type' => 'structure', 'members' => [ 'Ipv4Prefix' => [ 'shape' => 'Ipv4Prefix', ], ], ], 'Ipv4Prefixes' => [ 'type' => 'list', 'member' => [ 'shape' => 'Ipv4PrefixSpecificationRequest', ], ], 'Ipv6Address' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'sensitive' => true, ], 'Ipv6Addresses' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceIpv6Address', ], ], 'Ipv6Prefix' => [ 'type' => 'string', 'pattern' => '(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\\/([0-9]{1,2}|1[01][0-9]|12[0-8])', ], 'Ipv6PrefixSpecificationRequest' => [ 'type' => 'structure', 'members' => [ 'Ipv6Prefix' => [ 'shape' => 'Ipv6Prefix', ], ], ], 'Ipv6Prefixes' => [ 'type' => 'list', 'member' => [ 'shape' => 'Ipv6PrefixSpecificationRequest', ], ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'sensitive' => true, ], 'LicenseConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'LicenseConfigurationArn' => [ 'shape' => 'ARN', ], ], ], 'LicenseSpecifications' => [ 'type' => 'list', 'member' => [ 'shape' => 'LicenseConfigurationRequest', ], ], 'ListInstanceTypesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListInstanceTypesResponse' => [ 'type' => 'structure', 'required' => [ 'InstanceTypes', ], 'members' => [ 'InstanceTypes' => [ 'shape' => 'InstanceTypes', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRegionsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRegionsResponse' => [ 'type' => 'structure', 'required' => [ 'Regions', ], 'members' => [ 'Regions' => [ 'shape' => 'RegionList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'WorkspaceInstanceId', ], 'members' => [ 'WorkspaceInstanceId' => [ 'shape' => 'WorkspaceInstanceId', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ListWorkspaceInstancesRequest' => [ 'type' => 'structure', 'members' => [ 'ProvisionStates' => [ 'shape' => 'ProvisionStates', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListWorkspaceInstancesResponse' => [ 'type' => 'structure', 'required' => [ 'WorkspaceInstances', ], 'members' => [ 'WorkspaceInstances' => [ 'shape' => 'WorkspaceInstances', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ManagedInstanceRequest' => [ 'type' => 'structure', 'members' => [ 'BlockDeviceMappings' => [ 'shape' => 'BlockDeviceMappings', ], 'CapacityReservationSpecification' => [ 'shape' => 'CapacityReservationSpecification', ], 'CpuOptions' => [ 'shape' => 'CpuOptionsRequest', ], 'CreditSpecification' => [ 'shape' => 'CreditSpecificationRequest', ], 'DisableApiStop' => [ 'shape' => 'Boolean', ], 'EbsOptimized' => [ 'shape' => 'Boolean', ], 'EnablePrimaryIpv6' => [ 'shape' => 'Boolean', ], 'EnclaveOptions' => [ 'shape' => 'EnclaveOptionsRequest', ], 'HibernationOptions' => [ 'shape' => 'HibernationOptionsRequest', ], 'IamInstanceProfile' => [ 'shape' => 'IamInstanceProfileSpecification', ], 'ImageId' => [ 'shape' => 'ImageId', ], 'InstanceMarketOptions' => [ 'shape' => 'InstanceMarketOptionsRequest', ], 'InstanceType' => [ 'shape' => 'InstanceType', ], 'Ipv6Addresses' => [ 'shape' => 'Ipv6Addresses', ], 'Ipv6AddressCount' => [ 'shape' => 'NonNegativeInteger', ], 'KernelId' => [ 'shape' => 'String128', ], 'KeyName' => [ 'shape' => 'String64', ], 'LicenseSpecifications' => [ 'shape' => 'LicenseSpecifications', ], 'MaintenanceOptions' => [ 'shape' => 'InstanceMaintenanceOptionsRequest', ], 'MetadataOptions' => [ 'shape' => 'InstanceMetadataOptionsRequest', ], 'Monitoring' => [ 'shape' => 'RunInstancesMonitoringEnabled', ], 'NetworkInterfaces' => [ 'shape' => 'NetworkInterfaces', ], 'NetworkPerformanceOptions' => [ 'shape' => 'InstanceNetworkPerformanceOptionsRequest', ], 'Placement' => [ 'shape' => 'Placement', ], 'PrivateDnsNameOptions' => [ 'shape' => 'PrivateDnsNameOptionsRequest', ], 'PrivateIpAddress' => [ 'shape' => 'Ipv4Address', ], 'RamdiskId' => [ 'shape' => 'String128', ], 'SecurityGroupIds' => [ 'shape' => 'SecurityGroupIds', ], 'SecurityGroups' => [ 'shape' => 'SecurityGroupNames', ], 'SubnetId' => [ 'shape' => 'SubnetId', ], 'TagSpecifications' => [ 'shape' => 'TagSpecifications', ], 'UserData' => [ 'shape' => 'UserData', ], ], ], 'MarketTypeEnum' => [ 'type' => 'string', 'enum' => [ 'spot', 'capacity-block', ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 1, ], 'NetworkInterfaceId' => [ 'type' => 'string', 'pattern' => 'eni-[0-9a-zA-Z]{1,63}', ], 'NetworkInterfaces' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceNetworkInterfaceSpecification', ], ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'sensitive' => true, ], 'NonNegativeInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'Placement' => [ 'type' => 'structure', 'members' => [ 'Affinity' => [ 'shape' => 'String64', ], 'AvailabilityZone' => [ 'shape' => 'AvailabilityZone', ], 'GroupId' => [ 'shape' => 'PlacementGroupId', ], 'GroupName' => [ 'shape' => 'String64', ], 'HostId' => [ 'shape' => 'HostId', ], 'HostResourceGroupArn' => [ 'shape' => 'ARN', ], 'PartitionNumber' => [ 'shape' => 'NonNegativeInteger', ], 'Tenancy' => [ 'shape' => 'TenancyEnum', ], ], ], 'PlacementGroupId' => [ 'type' => 'string', 'pattern' => 'pg-[0-9a-zA-Z]{1,63}', ], 'PrivateDnsNameOptionsRequest' => [ 'type' => 'structure', 'members' => [ 'HostnameType' => [ 'shape' => 'HostnameTypeEnum', ], 'EnableResourceNameDnsARecord' => [ 'shape' => 'Boolean', ], 'EnableResourceNameDnsAAAARecord' => [ 'shape' => 'Boolean', ], ], ], 'PrivateIpAddressSpecification' => [ 'type' => 'structure', 'members' => [ 'Primary' => [ 'shape' => 'Boolean', ], 'PrivateIpAddress' => [ 'shape' => 'Ipv4Address', ], ], ], 'PrivateIpAddresses' => [ 'type' => 'list', 'member' => [ 'shape' => 'PrivateIpAddressSpecification', ], ], 'ProvisionStateEnum' => [ 'type' => 'string', 'enum' => [ 'ALLOCATING', 'ALLOCATED', 'DEALLOCATING', 'DEALLOCATED', 'ERROR_ALLOCATING', 'ERROR_DEALLOCATING', ], ], 'ProvisionStates' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProvisionStateEnum', ], ], 'Region' => [ 'type' => 'structure', 'members' => [ 'RegionName' => [ 'shape' => 'RegionName', ], ], ], 'RegionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Region', ], ], 'RegionName' => [ 'type' => 'string', 'pattern' => '[-0-9a-z]{1,31}', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'Message', 'ResourceId', 'ResourceType', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourceTypeEnum' => [ 'type' => 'string', 'enum' => [ 'instance', 'volume', 'spot-instances-request', 'network-interface', ], ], 'RunInstancesMonitoringEnabled' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'SecurityGroupId' => [ 'type' => 'string', 'pattern' => 'sg-[0-9a-zA-Z]{1,63}', ], 'SecurityGroupIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], ], 'SecurityGroupName' => [ 'type' => 'string', 'pattern' => '(?!sg-)[\\w .:/()#,@\\[\\]+=&;{}!$*-]{0,255}', ], 'SecurityGroupNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupName', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'Message', 'ResourceId', 'ResourceType', 'ServiceCode', 'QuotaCode', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], 'ServiceCode' => [ 'shape' => 'String', ], 'QuotaCode' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'SnapshotId' => [ 'type' => 'string', 'pattern' => 'snap-[0-9a-zA-Z]{1,63}', ], 'SpotInstanceTypeEnum' => [ 'type' => 'string', 'enum' => [ 'one-time', 'persistent', ], ], 'SpotMarketOptions' => [ 'type' => 'structure', 'members' => [ 'BlockDurationMinutes' => [ 'shape' => 'NonNegativeInteger', ], 'InstanceInterruptionBehavior' => [ 'shape' => 'InstanceInterruptionBehaviorEnum', ], 'MaxPrice' => [ 'shape' => 'String64', ], 'SpotInstanceType' => [ 'shape' => 'SpotInstanceTypeEnum', ], 'ValidUntilUtc' => [ 'shape' => 'Timestamp', ], ], ], 'String' => [ 'type' => 'string', ], 'String128' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'String64' => [ 'type' => 'string', 'max' => 64, 'min' => 0, ], 'SubnetId' => [ 'type' => 'string', 'pattern' => 'subnet-[0-9a-zA-Z]{1,63}', ], 'Tag' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]+)', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 30, 'min' => 1, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 30, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'WorkspaceInstanceId', 'Tags', ], 'members' => [ 'WorkspaceInstanceId' => [ 'shape' => 'WorkspaceInstanceId', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagSpecification' => [ 'type' => 'structure', 'members' => [ 'ResourceType' => [ 'shape' => 'ResourceTypeEnum', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagSpecifications' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagSpecification', ], 'max' => 30, 'min' => 0, ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)', ], 'TenancyEnum' => [ 'type' => 'string', 'enum' => [ 'default', 'dedicated', 'host', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ServiceCode' => [ 'shape' => 'String', ], 'QuotaCode' => [ 'shape' => 'String', ], 'RetryAfterSeconds' => [ 'shape' => 'Integer', ], ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'WorkspaceInstanceId', 'TagKeys', ], 'members' => [ 'WorkspaceInstanceId' => [ 'shape' => 'WorkspaceInstanceId', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UserData' => [ 'type' => 'string', 'max' => 16000, 'min' => 0, 'sensitive' => true, ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', 'Reason', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'Reason' => [ 'shape' => 'ValidationExceptionReason', ], 'FieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'Name', 'Reason', 'Message', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Reason' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'UNKNOWN_OPERATION', 'UNSUPPORTED_OPERATION', 'CANNOT_PARSE', 'FIELD_VALIDATION_FAILED', 'DEPENDENCY_FAILURE', 'OTHER', ], ], 'VirtualName' => [ 'type' => 'string', 'pattern' => 'ephemeral(0|[1-9][0-9]{0,2})', ], 'VolumeId' => [ 'type' => 'string', 'pattern' => 'vol-[0-9a-zA-Z]{1,63}', ], 'VolumeTypeEnum' => [ 'type' => 'string', 'enum' => [ 'standard', 'io1', 'io2', 'gp2', 'sc1', 'st1', 'gp3', ], ], 'WorkspaceInstance' => [ 'type' => 'structure', 'members' => [ 'ProvisionState' => [ 'shape' => 'ProvisionStateEnum', ], 'WorkspaceInstanceId' => [ 'shape' => 'WorkspaceInstanceId', ], 'EC2ManagedInstance' => [ 'shape' => 'EC2ManagedInstance', ], ], ], 'WorkspaceInstanceError' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'String', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'WorkspaceInstanceErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkspaceInstanceError', ], ], 'WorkspaceInstanceId' => [ 'type' => 'string', 'max' => 70, 'min' => 15, 'pattern' => 'wsinst-[0-9a-zA-Z]{8,63}', ], 'WorkspaceInstances' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkspaceInstance', ], ], ],];
