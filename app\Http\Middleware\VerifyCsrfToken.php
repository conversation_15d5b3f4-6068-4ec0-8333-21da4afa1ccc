<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array
     */
    protected $except = [
        '/customer/invoice/paytm*/',
        '/customer/invoice/flaterwave*/',
        '/invoice/razorpay*/',
        '/customer/retainer/paytm*/',
        'iyzipay/callback/*',
        'invoice/iyzipay/callback/*',
        'retainer/iyzipay/callback/*',
        'paytab-success/*',
        'retainer-paytab-success/*',
        '/aamarpay*'
    ];
}
