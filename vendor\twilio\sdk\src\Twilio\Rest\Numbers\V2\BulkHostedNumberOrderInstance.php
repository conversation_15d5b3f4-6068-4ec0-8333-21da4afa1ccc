<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Numbers\V2;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;


/**
 * @property string|null $bulkHostingSid
 * @property string $requestStatus
 * @property string|null $friendlyName
 * @property string|null $notificationEmail
 * @property \DateTime|null $dateCreated
 * @property \DateTime|null $dateCompleted
 * @property string|null $url
 * @property int $totalCount
 * @property object[]|null $results
 */
class BulkHostedNumberOrderInstance extends InstanceResource
{
    /**
     * Initialize the BulkHostedNumberOrderInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $bulkHostingSid A 34 character string that uniquely identifies this BulkHostedNumberOrder.
     */
    public function __construct(Version $version, array $payload, ?string $bulkHostingSid = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'bulkHostingSid' => Values::array_get($payload, 'bulk_hosting_sid'),
            'requestStatus' => Values::array_get($payload, 'request_status'),
            'friendlyName' => Values::array_get($payload, 'friendly_name'),
            'notificationEmail' => Values::array_get($payload, 'notification_email'),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'dateCompleted' => Deserialize::dateTime(Values::array_get($payload, 'date_completed')),
            'url' => Values::array_get($payload, 'url'),
            'totalCount' => Values::array_get($payload, 'total_count'),
            'results' => Values::array_get($payload, 'results'),
        ];

        $this->solution = ['bulkHostingSid' => $bulkHostingSid ?: $this->properties['bulkHostingSid'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return BulkHostedNumberOrderContext Context for this BulkHostedNumberOrderInstance
     */
    protected function proxy(): BulkHostedNumberOrderContext
    {
        if (!$this->context) {
            $this->context = new BulkHostedNumberOrderContext(
                $this->version,
                $this->solution['bulkHostingSid']
            );
        }

        return $this->context;
    }

    /**
     * Fetch the BulkHostedNumberOrderInstance
     *
     * @param array|Options $options Optional Arguments
     * @return BulkHostedNumberOrderInstance Fetched BulkHostedNumberOrderInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(array $options = []): BulkHostedNumberOrderInstance
    {

        return $this->proxy()->fetch($options);
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Numbers.V2.BulkHostedNumberOrderInstance ' . \implode(' ', $context) . ']';
    }
}

