<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Taskrouter
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Taskrouter\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\ListResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\InstanceContext;
use Twilio\Serialize;
use Twilio\Rest\Taskrouter\V1\Workspace\TaskQueueList;
use Twilio\Rest\Taskrouter\V1\Workspace\EventList;
use Twilio\Rest\Taskrouter\V1\Workspace\TaskChannelList;
use Twilio\Rest\Taskrouter\V1\Workspace\ActivityList;
use Twilio\Rest\Taskrouter\V1\Workspace\WorkerList;
use Twilio\Rest\Taskrouter\V1\Workspace\WorkflowList;
use Twilio\Rest\Taskrouter\V1\Workspace\TaskList;
use Twilio\Rest\Taskrouter\V1\Workspace\WorkspaceCumulativeStatisticsList;
use Twilio\Rest\Taskrouter\V1\Workspace\WorkspaceRealTimeStatisticsList;
use Twilio\Rest\Taskrouter\V1\Workspace\WorkspaceStatisticsList;


/**
 * @property TaskQueueList $taskQueues
 * @property EventList $events
 * @property TaskChannelList $taskChannels
 * @property ActivityList $activities
 * @property WorkerList $workers
 * @property WorkflowList $workflows
 * @property TaskList $tasks
 * @property WorkspaceCumulativeStatisticsList $cumulativeStatistics
 * @property WorkspaceRealTimeStatisticsList $realTimeStatistics
 * @property WorkspaceStatisticsList $statistics
 * @method \Twilio\Rest\Taskrouter\V1\Workspace\WorkspaceCumulativeStatisticsContext cumulativeStatistics()
 * @method \Twilio\Rest\Taskrouter\V1\Workspace\TaskQueueContext taskQueues(string $sid)
 * @method \Twilio\Rest\Taskrouter\V1\Workspace\ActivityContext activities(string $sid)
 * @method \Twilio\Rest\Taskrouter\V1\Workspace\WorkspaceRealTimeStatisticsContext realTimeStatistics()
 * @method \Twilio\Rest\Taskrouter\V1\Workspace\TaskContext tasks(string $sid)
 * @method \Twilio\Rest\Taskrouter\V1\Workspace\WorkflowContext workflows(string $sid)
 * @method \Twilio\Rest\Taskrouter\V1\Workspace\EventContext events(string $sid)
 * @method \Twilio\Rest\Taskrouter\V1\Workspace\WorkerContext workers(string $sid)
 * @method \Twilio\Rest\Taskrouter\V1\Workspace\WorkspaceStatisticsContext statistics()
 * @method \Twilio\Rest\Taskrouter\V1\Workspace\TaskChannelContext taskChannels(string $sid)
 */
class WorkspaceContext extends InstanceContext
    {
    protected $_taskQueues;
    protected $_events;
    protected $_taskChannels;
    protected $_activities;
    protected $_workers;
    protected $_workflows;
    protected $_tasks;
    protected $_cumulativeStatistics;
    protected $_realTimeStatistics;
    protected $_statistics;

    /**
     * Initialize the WorkspaceContext
     *
     * @param Version $version Version that contains the resource
     * @param string $sid The SID of the Workspace resource to delete.
     */
    public function __construct(
        Version $version,
        $sid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'sid' =>
            $sid,
        ];

        $this->uri = '/Workspaces/' . \rawurlencode($sid)
        .'';
    }

    /**
     * Delete the WorkspaceInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded' ]);
        return $this->version->delete('DELETE', $this->uri, [], [], $headers);
    }


    /**
     * Fetch the WorkspaceInstance
     *
     * @return WorkspaceInstance Fetched WorkspaceInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): WorkspaceInstance
    {

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json' ]);
        $payload = $this->version->fetch('GET', $this->uri, [], [], $headers);

        return new WorkspaceInstance(
            $this->version,
            $payload,
            $this->solution['sid']
        );
    }


    /**
     * Update the WorkspaceInstance
     *
     * @param array|Options $options Optional Arguments
     * @return WorkspaceInstance Updated WorkspaceInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(array $options = []): WorkspaceInstance
    {

        $options = new Values($options);

        $data = Values::of([
            'DefaultActivitySid' =>
                $options['defaultActivitySid'],
            'EventCallbackUrl' =>
                $options['eventCallbackUrl'],
            'EventsFilter' =>
                $options['eventsFilter'],
            'FriendlyName' =>
                $options['friendlyName'],
            'MultiTaskEnabled' =>
                Serialize::booleanToString($options['multiTaskEnabled']),
            'TimeoutActivitySid' =>
                $options['timeoutActivitySid'],
            'PrioritizeQueueOrder' =>
                $options['prioritizeQueueOrder'],
        ]);

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json' ]);
        $payload = $this->version->update('POST', $this->uri, [], $data, $headers);

        return new WorkspaceInstance(
            $this->version,
            $payload,
            $this->solution['sid']
        );
    }


    /**
     * Access the taskQueues
     */
    protected function getTaskQueues(): TaskQueueList
    {
        if (!$this->_taskQueues) {
            $this->_taskQueues = new TaskQueueList(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->_taskQueues;
    }

    /**
     * Access the events
     */
    protected function getEvents(): EventList
    {
        if (!$this->_events) {
            $this->_events = new EventList(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->_events;
    }

    /**
     * Access the taskChannels
     */
    protected function getTaskChannels(): TaskChannelList
    {
        if (!$this->_taskChannels) {
            $this->_taskChannels = new TaskChannelList(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->_taskChannels;
    }

    /**
     * Access the activities
     */
    protected function getActivities(): ActivityList
    {
        if (!$this->_activities) {
            $this->_activities = new ActivityList(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->_activities;
    }

    /**
     * Access the workers
     */
    protected function getWorkers(): WorkerList
    {
        if (!$this->_workers) {
            $this->_workers = new WorkerList(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->_workers;
    }

    /**
     * Access the workflows
     */
    protected function getWorkflows(): WorkflowList
    {
        if (!$this->_workflows) {
            $this->_workflows = new WorkflowList(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->_workflows;
    }

    /**
     * Access the tasks
     */
    protected function getTasks(): TaskList
    {
        if (!$this->_tasks) {
            $this->_tasks = new TaskList(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->_tasks;
    }

    /**
     * Access the cumulativeStatistics
     */
    protected function getCumulativeStatistics(): WorkspaceCumulativeStatisticsList
    {
        if (!$this->_cumulativeStatistics) {
            $this->_cumulativeStatistics = new WorkspaceCumulativeStatisticsList(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->_cumulativeStatistics;
    }

    /**
     * Access the realTimeStatistics
     */
    protected function getRealTimeStatistics(): WorkspaceRealTimeStatisticsList
    {
        if (!$this->_realTimeStatistics) {
            $this->_realTimeStatistics = new WorkspaceRealTimeStatisticsList(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->_realTimeStatistics;
    }

    /**
     * Access the statistics
     */
    protected function getStatistics(): WorkspaceStatisticsList
    {
        if (!$this->_statistics) {
            $this->_statistics = new WorkspaceStatisticsList(
                $this->version,
                $this->solution['sid']
            );
        }

        return $this->_statistics;
    }

    /**
     * Magic getter to lazy load subresources
     *
     * @param string $name Subresource to return
     * @return ListResource The requested subresource
     * @throws TwilioException For unknown subresources
     */
    public function __get(string $name): ListResource
    {
        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown subresource ' . $name);
    }

    /**
     * Magic caller to get resource contexts
     *
     * @param string $name Resource to return
     * @param array $arguments Context parameters
     * @return InstanceContext The requested resource context
     * @throws TwilioException For unknown resource
     */
    public function __call(string $name, array $arguments): InstanceContext
    {
        $property = $this->$name;
        if (\method_exists($property, 'getContext')) {
            return \call_user_func_array(array($property, 'getContext'), $arguments);
        }

        throw new TwilioException('Resource does not have a context');
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Taskrouter.V1.WorkspaceContext ' . \implode(' ', $context) . ']';
    }
}
