name: PHPStan

on:
  push:
    paths:
      - '**.php'
      - 'phpstan.neon.dist'
  pull_request:
    paths:
      - '**.php'
      - 'phpstan.neon.dist'

jobs:
  phpstan:
    name: phpstan
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.0'
          coverage: none

      - name: Install composer dependencies
        uses: ramsey/composer-install@v2

      - name: Run PHPStan
        run: ./vendor/bin/phpstan --error-format=github
