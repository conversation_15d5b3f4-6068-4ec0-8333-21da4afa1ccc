<?php
namespace Aws\MediaLive;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS Elemental MediaLive** service.
 * @method \Aws\Result acceptInputDeviceTransfer(array $args = [])
 * @method \GuzzleHttp\Promise\Promise acceptInputDeviceTransferAsync(array $args = [])
 * @method \Aws\Result batchDelete(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchDeleteAsync(array $args = [])
 * @method \Aws\Result batchStart(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchStartAsync(array $args = [])
 * @method \Aws\Result batchStop(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchStopAsync(array $args = [])
 * @method \Aws\Result batchUpdateSchedule(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchUpdateScheduleAsync(array $args = [])
 * @method \Aws\Result cancelInputDeviceTransfer(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelInputDeviceTransferAsync(array $args = [])
 * @method \Aws\Result claimDevice(array $args = [])
 * @method \GuzzleHttp\Promise\Promise claimDeviceAsync(array $args = [])
 * @method \Aws\Result createChannel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createChannelAsync(array $args = [])
 * @method \Aws\Result createInput(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createInputAsync(array $args = [])
 * @method \Aws\Result createInputSecurityGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createInputSecurityGroupAsync(array $args = [])
 * @method \Aws\Result createMultiplex(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createMultiplexAsync(array $args = [])
 * @method \Aws\Result createMultiplexProgram(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createMultiplexProgramAsync(array $args = [])
 * @method \Aws\Result createPartnerInput(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createPartnerInputAsync(array $args = [])
 * @method \Aws\Result createTags(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createTagsAsync(array $args = [])
 * @method \Aws\Result deleteChannel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteChannelAsync(array $args = [])
 * @method \Aws\Result deleteInput(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteInputAsync(array $args = [])
 * @method \Aws\Result deleteInputSecurityGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteInputSecurityGroupAsync(array $args = [])
 * @method \Aws\Result deleteMultiplex(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteMultiplexAsync(array $args = [])
 * @method \Aws\Result deleteMultiplexProgram(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteMultiplexProgramAsync(array $args = [])
 * @method \Aws\Result deleteReservation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteReservationAsync(array $args = [])
 * @method \Aws\Result deleteSchedule(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteScheduleAsync(array $args = [])
 * @method \Aws\Result deleteTags(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteTagsAsync(array $args = [])
 * @method \Aws\Result describeAccountConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeAccountConfigurationAsync(array $args = [])
 * @method \Aws\Result describeChannel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeChannelAsync(array $args = [])
 * @method \Aws\Result describeInput(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeInputAsync(array $args = [])
 * @method \Aws\Result describeInputDevice(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeInputDeviceAsync(array $args = [])
 * @method \Aws\Result describeInputDeviceThumbnail(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeInputDeviceThumbnailAsync(array $args = [])
 * @method \Aws\Result describeInputSecurityGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeInputSecurityGroupAsync(array $args = [])
 * @method \Aws\Result describeMultiplex(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeMultiplexAsync(array $args = [])
 * @method \Aws\Result describeMultiplexProgram(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeMultiplexProgramAsync(array $args = [])
 * @method \Aws\Result describeOffering(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeOfferingAsync(array $args = [])
 * @method \Aws\Result describeReservation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeReservationAsync(array $args = [])
 * @method \Aws\Result describeSchedule(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeScheduleAsync(array $args = [])
 * @method \Aws\Result describeThumbnails(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeThumbnailsAsync(array $args = [])
 * @method \Aws\Result listChannels(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listChannelsAsync(array $args = [])
 * @method \Aws\Result listInputDeviceTransfers(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listInputDeviceTransfersAsync(array $args = [])
 * @method \Aws\Result listInputDevices(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listInputDevicesAsync(array $args = [])
 * @method \Aws\Result listInputSecurityGroups(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listInputSecurityGroupsAsync(array $args = [])
 * @method \Aws\Result listInputs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listInputsAsync(array $args = [])
 * @method \Aws\Result listMultiplexPrograms(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMultiplexProgramsAsync(array $args = [])
 * @method \Aws\Result listMultiplexes(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMultiplexesAsync(array $args = [])
 * @method \Aws\Result listOfferings(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listOfferingsAsync(array $args = [])
 * @method \Aws\Result listReservations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listReservationsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result purchaseOffering(array $args = [])
 * @method \GuzzleHttp\Promise\Promise purchaseOfferingAsync(array $args = [])
 * @method \Aws\Result rebootInputDevice(array $args = [])
 * @method \GuzzleHttp\Promise\Promise rebootInputDeviceAsync(array $args = [])
 * @method \Aws\Result rejectInputDeviceTransfer(array $args = [])
 * @method \GuzzleHttp\Promise\Promise rejectInputDeviceTransferAsync(array $args = [])
 * @method \Aws\Result startChannel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startChannelAsync(array $args = [])
 * @method \Aws\Result startInputDevice(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startInputDeviceAsync(array $args = [])
 * @method \Aws\Result startInputDeviceMaintenanceWindow(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startInputDeviceMaintenanceWindowAsync(array $args = [])
 * @method \Aws\Result startMultiplex(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startMultiplexAsync(array $args = [])
 * @method \Aws\Result stopChannel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopChannelAsync(array $args = [])
 * @method \Aws\Result stopInputDevice(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopInputDeviceAsync(array $args = [])
 * @method \Aws\Result stopMultiplex(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopMultiplexAsync(array $args = [])
 * @method \Aws\Result transferInputDevice(array $args = [])
 * @method \GuzzleHttp\Promise\Promise transferInputDeviceAsync(array $args = [])
 * @method \Aws\Result updateAccountConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateAccountConfigurationAsync(array $args = [])
 * @method \Aws\Result updateChannel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateChannelAsync(array $args = [])
 * @method \Aws\Result updateChannelClass(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateChannelClassAsync(array $args = [])
 * @method \Aws\Result updateInput(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateInputAsync(array $args = [])
 * @method \Aws\Result updateInputDevice(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateInputDeviceAsync(array $args = [])
 * @method \Aws\Result updateInputSecurityGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateInputSecurityGroupAsync(array $args = [])
 * @method \Aws\Result updateMultiplex(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateMultiplexAsync(array $args = [])
 * @method \Aws\Result updateMultiplexProgram(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateMultiplexProgramAsync(array $args = [])
 * @method \Aws\Result updateReservation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateReservationAsync(array $args = [])
 * @method \Aws\Result restartChannelPipelines(array $args = [])
 * @method \GuzzleHttp\Promise\Promise restartChannelPipelinesAsync(array $args = [])
 * @method \Aws\Result createCloudWatchAlarmTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createCloudWatchAlarmTemplateAsync(array $args = [])
 * @method \Aws\Result createCloudWatchAlarmTemplateGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createCloudWatchAlarmTemplateGroupAsync(array $args = [])
 * @method \Aws\Result createEventBridgeRuleTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createEventBridgeRuleTemplateAsync(array $args = [])
 * @method \Aws\Result createEventBridgeRuleTemplateGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createEventBridgeRuleTemplateGroupAsync(array $args = [])
 * @method \Aws\Result createSignalMap(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createSignalMapAsync(array $args = [])
 * @method \Aws\Result deleteCloudWatchAlarmTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteCloudWatchAlarmTemplateAsync(array $args = [])
 * @method \Aws\Result deleteCloudWatchAlarmTemplateGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteCloudWatchAlarmTemplateGroupAsync(array $args = [])
 * @method \Aws\Result deleteEventBridgeRuleTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteEventBridgeRuleTemplateAsync(array $args = [])
 * @method \Aws\Result deleteEventBridgeRuleTemplateGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteEventBridgeRuleTemplateGroupAsync(array $args = [])
 * @method \Aws\Result deleteSignalMap(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteSignalMapAsync(array $args = [])
 * @method \Aws\Result getCloudWatchAlarmTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCloudWatchAlarmTemplateAsync(array $args = [])
 * @method \Aws\Result getCloudWatchAlarmTemplateGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCloudWatchAlarmTemplateGroupAsync(array $args = [])
 * @method \Aws\Result getEventBridgeRuleTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getEventBridgeRuleTemplateAsync(array $args = [])
 * @method \Aws\Result getEventBridgeRuleTemplateGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getEventBridgeRuleTemplateGroupAsync(array $args = [])
 * @method \Aws\Result getSignalMap(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSignalMapAsync(array $args = [])
 * @method \Aws\Result listCloudWatchAlarmTemplateGroups(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCloudWatchAlarmTemplateGroupsAsync(array $args = [])
 * @method \Aws\Result listCloudWatchAlarmTemplates(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCloudWatchAlarmTemplatesAsync(array $args = [])
 * @method \Aws\Result listEventBridgeRuleTemplateGroups(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listEventBridgeRuleTemplateGroupsAsync(array $args = [])
 * @method \Aws\Result listEventBridgeRuleTemplates(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listEventBridgeRuleTemplatesAsync(array $args = [])
 * @method \Aws\Result listSignalMaps(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSignalMapsAsync(array $args = [])
 * @method \Aws\Result startDeleteMonitorDeployment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startDeleteMonitorDeploymentAsync(array $args = [])
 * @method \Aws\Result startMonitorDeployment(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startMonitorDeploymentAsync(array $args = [])
 * @method \Aws\Result startUpdateSignalMap(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startUpdateSignalMapAsync(array $args = [])
 * @method \Aws\Result updateCloudWatchAlarmTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateCloudWatchAlarmTemplateAsync(array $args = [])
 * @method \Aws\Result updateCloudWatchAlarmTemplateGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateCloudWatchAlarmTemplateGroupAsync(array $args = [])
 * @method \Aws\Result updateEventBridgeRuleTemplate(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateEventBridgeRuleTemplateAsync(array $args = [])
 * @method \Aws\Result updateEventBridgeRuleTemplateGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateEventBridgeRuleTemplateGroupAsync(array $args = [])
 * @method \Aws\Result createChannelPlacementGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createChannelPlacementGroupAsync(array $args = [])
 * @method \Aws\Result createCluster(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createClusterAsync(array $args = [])
 * @method \Aws\Result createNetwork(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createNetworkAsync(array $args = [])
 * @method \Aws\Result createNode(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createNodeAsync(array $args = [])
 * @method \Aws\Result createNodeRegistrationScript(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createNodeRegistrationScriptAsync(array $args = [])
 * @method \Aws\Result deleteChannelPlacementGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteChannelPlacementGroupAsync(array $args = [])
 * @method \Aws\Result deleteCluster(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteClusterAsync(array $args = [])
 * @method \Aws\Result deleteNetwork(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteNetworkAsync(array $args = [])
 * @method \Aws\Result deleteNode(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteNodeAsync(array $args = [])
 * @method \Aws\Result describeChannelPlacementGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeChannelPlacementGroupAsync(array $args = [])
 * @method \Aws\Result describeCluster(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeClusterAsync(array $args = [])
 * @method \Aws\Result describeNetwork(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeNetworkAsync(array $args = [])
 * @method \Aws\Result describeNode(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeNodeAsync(array $args = [])
 * @method \Aws\Result listChannelPlacementGroups(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listChannelPlacementGroupsAsync(array $args = [])
 * @method \Aws\Result listClusters(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listClustersAsync(array $args = [])
 * @method \Aws\Result listNetworks(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listNetworksAsync(array $args = [])
 * @method \Aws\Result listNodes(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listNodesAsync(array $args = [])
 * @method \Aws\Result updateChannelPlacementGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateChannelPlacementGroupAsync(array $args = [])
 * @method \Aws\Result updateCluster(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateClusterAsync(array $args = [])
 * @method \Aws\Result updateNetwork(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateNetworkAsync(array $args = [])
 * @method \Aws\Result updateNode(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateNodeAsync(array $args = [])
 * @method \Aws\Result updateNodeState(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateNodeStateAsync(array $args = [])
 * @method \Aws\Result listVersions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listVersionsAsync(array $args = [])
 * @method \Aws\Result createSdiSource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createSdiSourceAsync(array $args = [])
 * @method \Aws\Result deleteSdiSource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteSdiSourceAsync(array $args = [])
 * @method \Aws\Result describeSdiSource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeSdiSourceAsync(array $args = [])
 * @method \Aws\Result listSdiSources(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSdiSourcesAsync(array $args = [])
 * @method \Aws\Result updateSdiSource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateSdiSourceAsync(array $args = [])
 */
class MediaLiveClient extends AwsClient {}
