<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ProtectSensitiveFiles
{
    /**
     * الملفات والمجلدات المحمية
     */
    protected $protectedPaths = [
        'storage/uploads/sample',
        'storage/logs',
        '.env',
        'config/',
        'database/',
    ];

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $requestPath = $request->path();
        
        // التحقق من محاولة الوصول للملفات المحمية
        foreach ($this->protectedPaths as $protectedPath) {
            if (str_starts_with($requestPath, $protectedPath)) {
                // التحقق من صلاحيات المستخدم
                if (!auth()->check() || !auth()->user()->can('access sensitive files')) {
                    abort(403, 'Access denied to sensitive files');
                }
            }
        }
        
        // التحقق من محاولة الوصول لملفات CSV
        if (str_ends_with($requestPath, '.csv') || str_ends_with($requestPath, '.log')) {
            if (!auth()->check() || !auth()->user()->can('view analytics')) {
                abort(403, 'Access denied to data files');
            }
        }
        
        return $next($request);
    }
}
