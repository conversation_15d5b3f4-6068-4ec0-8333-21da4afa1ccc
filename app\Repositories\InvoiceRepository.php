<?php

namespace App\Repositories;

use App\Models\Invoice;
use App\Services\SmartCacheService;
use Illuminate\Support\LazyCollection;

class InvoiceRepository extends BaseRepository
{
    protected $cache;

    public function __construct(Invoice $model)
    {
        parent::__construct($model);
        $this->cache = app(SmartCacheService::class);
    }

    /**
     * الحصول على الفواتير مع العلاقات الأساسية فقط
     */
    public function getInvoicesWithBasicRelations($perPage = 50)
    {
        return $this->model->select([
                'id', 'invoice_id', 'customer_id', 'status',
                'send_date', 'due_date', 'total', 'created_by'
            ])
            ->with([
                'customer:id,name,email',
                'category:id,name'
            ])
            ->paginate($perPage);
    }

    /**
     * الحصول على تفاصيل الفاتورة مع جميع العلاقات (lazy loaded)
     */
    public function getInvoiceDetails($id)
    {
        return $this->model->select('*')
            ->with([
                'customer',
                'items' => function($query) {
                    $query->select('id', 'invoice_id', 'product_id', 'quantity', 'price', 'discount', 'tax');
                },
                'payments' => function($query) {
                    $query->select('id', 'invoice_id', 'amount', 'date', 'payment_method');
                },
                'tax:id,name,rate'
            ])
            ->find($id);
    }

    /**
     * الحصول على الفواتير للعميل مع Lazy Loading
     */
    public function getCustomerInvoicesLazy($customerId): LazyCollection
    {
        return $this->model->select([
                'id', 'invoice_id', 'status', 'send_date',
                'due_date', 'total', 'customer_id'
            ])
            ->where('customer_id', $customerId)
            ->lazy();
    }

    /**
     * إحصائيات الفواتير بدون تحميل البيانات مع Cache
     */
    public function getInvoiceStats($customerId = null, $dateFrom = null, $dateTo = null)
    {
        $cacheKey = 'invoice_stats_' . md5($customerId . $dateFrom . $dateTo);

        return $this->cache->rememberStats($cacheKey, function() use ($customerId, $dateFrom, $dateTo) {
            $query = $this->model->newQuery();

            if ($customerId) {
                $query->where('customer_id', $customerId);
            }

            if ($dateFrom) {
                $query->where('issue_date', '>=', $dateFrom);
            }

            if ($dateTo) {
                $query->where('issue_date', '<=', $dateTo);
            }

            return [
                'total_invoices' => $query->count(),
                'total_amount' => $query->sum('total'),
                'paid_amount' => $query->where('status', 'Paid')->sum('total'),
                'pending_amount' => $query->whereIn('status', ['Sent', 'Unpaid'])->sum('total'),
                'overdue_count' => $query->whereIn('status', ['Sent', 'Unpaid'])
                                       ->where('due_date', '<', now())
                                       ->count(),
            ];
        }, [
            'tags' => ['invoices', 'stats'],
            'ttl' => 1800 // 30 دقيقة
        ]);
    }

    /**
     * البحث في الفواتير مع Lazy Loading و Cache
     */
    public function searchInvoicesLazy($searchTerm, $filters = [])
    {
        return $this->cache->rememberSearch($searchTerm, $filters, function() use ($searchTerm, $filters) {
            $query = $this->model->select([
                    'id', 'invoice_id', 'customer_id', 'status',
                    'issue_date', 'due_date', 'total'
                ])
                ->with('customer:id,name,email');

            // البحث في رقم الفاتورة أو اسم العميل
            $query->where(function($q) use ($searchTerm) {
                $q->where('invoice_id', 'like', "%{$searchTerm}%")
                  ->orWhereHas('customer', function($customerQuery) use ($searchTerm) {
                      $customerQuery->where('name', 'like', "%{$searchTerm}%");
                  });
            });

            // تطبيق الفلاتر
            if (isset($filters['status'])) {
                $query->where('status', $filters['status']);
            }

            if (isset($filters['date_from'])) {
                $query->where('issue_date', '>=', $filters['date_from']);
            }

            if (isset($filters['date_to'])) {
                $query->where('issue_date', '<=', $filters['date_to']);
            }

            return $query->lazy();
        }, [
            'tags' => ['invoices', 'search'],
            'ttl' => 300 // 5 دقائق
        ]);
    }

    /**
     * تصدير الفواتير مع Lazy Loading
     */
    public function exportInvoicesLazy($filters = [])
    {
        $query = $this->model->select([
                'invoice_id', 'customer_id', 'status', 'send_date',
                'due_date', 'total', 'created_at'
            ])
            ->with('customer:id,name,email,contact');

        foreach ($filters as $field => $value) {
            if ($value !== null) {
                $query->where($field, $value);
            }
        }

        return $query->lazy();
    }

    /**
     * معالجة الفواتير المتأخرة بـ Chunking
     */
    public function processOverdueInvoices(callable $callback)
    {
        return $this->model->where('status', 'pending')
            ->where('due_date', '<', now())
            ->chunk(100, $callback);
    }

    /**
     * حساب الإجماليات الشهرية بدون تحميل البيانات
     */
    public function getMonthlyTotals($year = null)
    {
        $year = $year ?? date('Y');

        return $this->model->selectRaw('
                MONTH(send_date) as month,
                COUNT(*) as invoice_count,
                SUM(total) as total_amount,
                SUM(CASE WHEN status = "paid" THEN total ELSE 0 END) as paid_amount
            ')
            ->whereYear('send_date', $year)
            ->groupBy('month')
            ->orderBy('month')
            ->get();
    }
}
