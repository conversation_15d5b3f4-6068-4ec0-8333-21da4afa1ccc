<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\FlexApi\V1\Plugin;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;


/**
 * @property string|null $sid
 * @property string|null $pluginSid
 * @property string|null $accountSid
 * @property string|null $version
 * @property string|null $pluginUrl
 * @property string|null $changelog
 * @property bool|null $private
 * @property bool|null $archived
 * @property bool|null $validated
 * @property \DateTime|null $dateCreated
 * @property string|null $url
 */
class PluginVersionsInstance extends InstanceResource
{
    /**
     * Initialize the PluginVersionsInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $pluginSid The SID of the Flex Plugin the resource to belongs to.
     * @param string $sid The SID of the Flex Plugin Version resource to fetch.
     */
    public function __construct(Version $version, array $payload, string $pluginSid, ?string $sid = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'sid' => Values::array_get($payload, 'sid'),
            'pluginSid' => Values::array_get($payload, 'plugin_sid'),
            'accountSid' => Values::array_get($payload, 'account_sid'),
            'version' => Values::array_get($payload, 'version'),
            'pluginUrl' => Values::array_get($payload, 'plugin_url'),
            'changelog' => Values::array_get($payload, 'changelog'),
            'private' => Values::array_get($payload, 'private'),
            'archived' => Values::array_get($payload, 'archived'),
            'validated' => Values::array_get($payload, 'validated'),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'url' => Values::array_get($payload, 'url'),
        ];

        $this->solution = ['pluginSid' => $pluginSid, 'sid' => $sid ?: $this->properties['sid'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return PluginVersionsContext Context for this PluginVersionsInstance
     */
    protected function proxy(): PluginVersionsContext
    {
        if (!$this->context) {
            $this->context = new PluginVersionsContext(
                $this->version,
                $this->solution['pluginSid'],
                $this->solution['sid']
            );
        }

        return $this->context;
    }

    /**
     * Fetch the PluginVersionsInstance
     *
     * @param array|Options $options Optional Arguments
     * @return PluginVersionsInstance Fetched PluginVersionsInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(array $options = []): PluginVersionsInstance
    {

        return $this->proxy()->fetch($options);
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.FlexApi.V1.PluginVersionsInstance ' . \implode(' ', $context) . ']';
    }
}

