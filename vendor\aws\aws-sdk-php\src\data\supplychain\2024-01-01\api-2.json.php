<?php
// This file was auto-generated from sdk-root/src/data/supplychain/2024-01-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2024-01-01', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'scn', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS Supply Chain', 'serviceId' => 'SupplyChain', 'signatureVersion' => 'v4', 'signingName' => 'scn', 'uid' => 'supplychain-2024-01-01', ], 'operations' => [ 'CreateBillOfMaterialsImportJob' => [ 'name' => 'CreateBillOfMaterialsImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/api/configuration/instances/{instanceId}/bill-of-materials-import-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateBillOfMaterialsImportJobRequest', ], 'output' => [ 'shape' => 'CreateBillOfMaterialsImportJobResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'CreateDataIntegrationFlow' => [ 'name' => 'CreateDataIntegrationFlow', 'http' => [ 'method' => 'PUT', 'requestUri' => '/api/data-integration/instance/{instanceId}/data-integration-flows/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateDataIntegrationFlowRequest', ], 'output' => [ 'shape' => 'CreateDataIntegrationFlowResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'CreateDataLakeDataset' => [ 'name' => 'CreateDataLakeDataset', 'http' => [ 'method' => 'PUT', 'requestUri' => '/api/datalake/instance/{instanceId}/namespaces/{namespace}/datasets/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateDataLakeDatasetRequest', ], 'output' => [ 'shape' => 'CreateDataLakeDatasetResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'CreateDataLakeNamespace' => [ 'name' => 'CreateDataLakeNamespace', 'http' => [ 'method' => 'PUT', 'requestUri' => '/api/datalake/instance/{instanceId}/namespaces/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateDataLakeNamespaceRequest', ], 'output' => [ 'shape' => 'CreateDataLakeNamespaceResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'CreateInstance' => [ 'name' => 'CreateInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/api/instance', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateInstanceRequest', ], 'output' => [ 'shape' => 'CreateInstanceResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteDataIntegrationFlow' => [ 'name' => 'DeleteDataIntegrationFlow', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/api/data-integration/instance/{instanceId}/data-integration-flows/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteDataIntegrationFlowRequest', ], 'output' => [ 'shape' => 'DeleteDataIntegrationFlowResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteDataLakeDataset' => [ 'name' => 'DeleteDataLakeDataset', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/api/datalake/instance/{instanceId}/namespaces/{namespace}/datasets/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteDataLakeDatasetRequest', ], 'output' => [ 'shape' => 'DeleteDataLakeDatasetResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteDataLakeNamespace' => [ 'name' => 'DeleteDataLakeNamespace', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/api/datalake/instance/{instanceId}/namespaces/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteDataLakeNamespaceRequest', ], 'output' => [ 'shape' => 'DeleteDataLakeNamespaceResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteInstance' => [ 'name' => 'DeleteInstance', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/api/instance/{instanceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteInstanceRequest', ], 'output' => [ 'shape' => 'DeleteInstanceResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'GetBillOfMaterialsImportJob' => [ 'name' => 'GetBillOfMaterialsImportJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/configuration/instances/{instanceId}/bill-of-materials-import-jobs/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBillOfMaterialsImportJobRequest', ], 'output' => [ 'shape' => 'GetBillOfMaterialsImportJobResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetDataIntegrationEvent' => [ 'name' => 'GetDataIntegrationEvent', 'http' => [ 'method' => 'GET', 'requestUri' => '/api-data/data-integration/instance/{instanceId}/data-integration-events/{eventId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDataIntegrationEventRequest', ], 'output' => [ 'shape' => 'GetDataIntegrationEventResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetDataIntegrationFlow' => [ 'name' => 'GetDataIntegrationFlow', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/data-integration/instance/{instanceId}/data-integration-flows/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDataIntegrationFlowRequest', ], 'output' => [ 'shape' => 'GetDataIntegrationFlowResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetDataIntegrationFlowExecution' => [ 'name' => 'GetDataIntegrationFlowExecution', 'http' => [ 'method' => 'GET', 'requestUri' => '/api-data/data-integration/instance/{instanceId}/data-integration-flows/{flowName}/executions/{executionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDataIntegrationFlowExecutionRequest', ], 'output' => [ 'shape' => 'GetDataIntegrationFlowExecutionResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetDataLakeDataset' => [ 'name' => 'GetDataLakeDataset', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/datalake/instance/{instanceId}/namespaces/{namespace}/datasets/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDataLakeDatasetRequest', ], 'output' => [ 'shape' => 'GetDataLakeDatasetResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetDataLakeNamespace' => [ 'name' => 'GetDataLakeNamespace', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/datalake/instance/{instanceId}/namespaces/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDataLakeNamespaceRequest', ], 'output' => [ 'shape' => 'GetDataLakeNamespaceResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetInstance' => [ 'name' => 'GetInstance', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/instance/{instanceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetInstanceRequest', ], 'output' => [ 'shape' => 'GetInstanceResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListDataIntegrationEvents' => [ 'name' => 'ListDataIntegrationEvents', 'http' => [ 'method' => 'GET', 'requestUri' => '/api-data/data-integration/instance/{instanceId}/data-integration-events', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDataIntegrationEventsRequest', ], 'output' => [ 'shape' => 'ListDataIntegrationEventsResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListDataIntegrationFlowExecutions' => [ 'name' => 'ListDataIntegrationFlowExecutions', 'http' => [ 'method' => 'GET', 'requestUri' => '/api-data/data-integration/instance/{instanceId}/data-integration-flows/{flowName}/executions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDataIntegrationFlowExecutionsRequest', ], 'output' => [ 'shape' => 'ListDataIntegrationFlowExecutionsResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListDataIntegrationFlows' => [ 'name' => 'ListDataIntegrationFlows', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/data-integration/instance/{instanceId}/data-integration-flows', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDataIntegrationFlowsRequest', ], 'output' => [ 'shape' => 'ListDataIntegrationFlowsResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListDataLakeDatasets' => [ 'name' => 'ListDataLakeDatasets', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/datalake/instance/{instanceId}/namespaces/{namespace}/datasets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDataLakeDatasetsRequest', ], 'output' => [ 'shape' => 'ListDataLakeDatasetsResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListDataLakeNamespaces' => [ 'name' => 'ListDataLakeNamespaces', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/datalake/instance/{instanceId}/namespaces', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDataLakeNamespacesRequest', ], 'output' => [ 'shape' => 'ListDataLakeNamespacesResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListInstances' => [ 'name' => 'ListInstances', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/instance', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListInstancesRequest', ], 'output' => [ 'shape' => 'ListInstancesResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/api/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'SendDataIntegrationEvent' => [ 'name' => 'SendDataIntegrationEvent', 'http' => [ 'method' => 'POST', 'requestUri' => '/api-data/data-integration/instance/{instanceId}/data-integration-events', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SendDataIntegrationEventRequest', ], 'output' => [ 'shape' => 'SendDataIntegrationEventResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/api/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/api/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'UpdateDataIntegrationFlow' => [ 'name' => 'UpdateDataIntegrationFlow', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/api/data-integration/instance/{instanceId}/data-integration-flows/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDataIntegrationFlowRequest', ], 'output' => [ 'shape' => 'UpdateDataIntegrationFlowResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateDataLakeDataset' => [ 'name' => 'UpdateDataLakeDataset', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/api/datalake/instance/{instanceId}/namespaces/{namespace}/datasets/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDataLakeDatasetRequest', ], 'output' => [ 'shape' => 'UpdateDataLakeDatasetResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateDataLakeNamespace' => [ 'name' => 'UpdateDataLakeNamespace', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/api/datalake/instance/{instanceId}/namespaces/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDataLakeNamespaceRequest', ], 'output' => [ 'shape' => 'UpdateDataLakeNamespaceResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateInstance' => [ 'name' => 'UpdateInstance', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/api/instance/{instanceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateInstanceRequest', ], 'output' => [ 'shape' => 'UpdateInstanceResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AscResourceArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 20, 'pattern' => 'arn:aws:scn(?::([a-z0-9-]+):([0-9]+):instance)?/([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})[-_./A-Za-z0-9]*', ], 'AwsAccountId' => [ 'type' => 'string', 'pattern' => '[0-9]{12}', ], 'BillOfMaterialsImportJob' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'jobId', 'status', 's3uri', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', ], 'jobId' => [ 'shape' => 'UUID', ], 'status' => [ 'shape' => 'ConfigurationJobStatus', ], 's3uri' => [ 'shape' => 'ConfigurationS3Uri', ], 'message' => [ 'shape' => 'String', ], ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ClientToken' => [ 'type' => 'string', 'max' => 126, 'min' => 33, ], 'ConfigurationJobStatus' => [ 'type' => 'string', 'enum' => [ 'NEW', 'FAILED', 'IN_PROGRESS', 'QUEUED', 'SUCCESS', ], ], 'ConfigurationS3Uri' => [ 'type' => 'string', 'min' => 10, 'pattern' => '[sS]3://[a-z0-9][a-z0-9.-]{1,61}[a-z0-9]/.+', ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateBillOfMaterialsImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', 's3uri', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 's3uri' => [ 'shape' => 'ConfigurationS3Uri', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateBillOfMaterialsImportJobResponse' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'UUID', ], ], ], 'CreateDataIntegrationFlowRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'name', 'sources', 'transformation', 'target', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'name' => [ 'shape' => 'DataIntegrationFlowName', 'location' => 'uri', 'locationName' => 'name', ], 'sources' => [ 'shape' => 'DataIntegrationFlowSourceList', ], 'transformation' => [ 'shape' => 'DataIntegrationFlowTransformation', ], 'target' => [ 'shape' => 'DataIntegrationFlowTarget', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateDataIntegrationFlowResponse' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'name', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', ], 'name' => [ 'shape' => 'DataIntegrationFlowName', ], ], ], 'CreateDataLakeDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'namespace', 'name', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'namespace' => [ 'shape' => 'DataLakeNamespaceName', 'location' => 'uri', 'locationName' => 'namespace', ], 'name' => [ 'shape' => 'DataLakeDatasetName', 'location' => 'uri', 'locationName' => 'name', ], 'schema' => [ 'shape' => 'DataLakeDatasetSchema', ], 'description' => [ 'shape' => 'DataLakeDatasetDescription', ], 'partitionSpec' => [ 'shape' => 'DataLakeDatasetPartitionSpec', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateDataLakeDatasetResponse' => [ 'type' => 'structure', 'required' => [ 'dataset', ], 'members' => [ 'dataset' => [ 'shape' => 'DataLakeDataset', ], ], ], 'CreateDataLakeNamespaceRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'name', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'name' => [ 'shape' => 'DataLakeNamespaceName', 'location' => 'uri', 'locationName' => 'name', ], 'description' => [ 'shape' => 'DataLakeNamespaceDescription', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateDataLakeNamespaceResponse' => [ 'type' => 'structure', 'required' => [ 'namespace', ], 'members' => [ 'namespace' => [ 'shape' => 'DataLakeNamespace', ], ], ], 'CreateInstanceRequest' => [ 'type' => 'structure', 'members' => [ 'instanceName' => [ 'shape' => 'InstanceName', ], 'instanceDescription' => [ 'shape' => 'InstanceDescription', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'webAppDnsDomain' => [ 'shape' => 'InstanceWebAppDnsDomain', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateInstanceResponse' => [ 'type' => 'structure', 'required' => [ 'instance', ], 'members' => [ 'instance' => [ 'shape' => 'Instance', ], ], ], 'DataIntegrationDatasetArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 20, 'pattern' => 'arn:aws:scn:([a-z0-9-]+):([0-9]+):instance/([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})/namespaces/[^/]+/datasets/[^/]+', ], 'DataIntegrationEvent' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'eventId', 'eventType', 'eventGroupId', 'eventTimestamp', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', ], 'eventId' => [ 'shape' => 'UUID', ], 'eventType' => [ 'shape' => 'DataIntegrationEventType', ], 'eventGroupId' => [ 'shape' => 'DataIntegrationEventGroupId', ], 'eventTimestamp' => [ 'shape' => 'Timestamp', ], 'datasetTargetDetails' => [ 'shape' => 'DataIntegrationEventDatasetTargetDetails', ], ], ], 'DataIntegrationEventData' => [ 'type' => 'string', 'max' => 1048576, 'min' => 1, 'sensitive' => true, ], 'DataIntegrationEventDatasetLoadExecutionDetails' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'DataIntegrationEventDatasetLoadStatus', ], 'message' => [ 'shape' => 'String', ], ], ], 'DataIntegrationEventDatasetLoadStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'IN_PROGRESS', 'FAILED', ], ], 'DataIntegrationEventDatasetOperationType' => [ 'type' => 'string', 'enum' => [ 'APPEND', 'UPSERT', 'DELETE', ], ], 'DataIntegrationEventDatasetTargetConfiguration' => [ 'type' => 'structure', 'required' => [ 'datasetIdentifier', 'operationType', ], 'members' => [ 'datasetIdentifier' => [ 'shape' => 'DataIntegrationDatasetArn', ], 'operationType' => [ 'shape' => 'DataIntegrationEventDatasetOperationType', ], ], ], 'DataIntegrationEventDatasetTargetDetails' => [ 'type' => 'structure', 'required' => [ 'datasetIdentifier', 'operationType', 'datasetLoadExecution', ], 'members' => [ 'datasetIdentifier' => [ 'shape' => 'DataIntegrationDatasetArn', ], 'operationType' => [ 'shape' => 'DataIntegrationEventDatasetOperationType', ], 'datasetLoadExecution' => [ 'shape' => 'DataIntegrationEventDatasetLoadExecutionDetails', ], ], ], 'DataIntegrationEventGroupId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'DataIntegrationEventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataIntegrationEvent', ], ], 'DataIntegrationEventMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 20, 'min' => 1, ], 'DataIntegrationEventNextToken' => [ 'type' => 'string', 'max' => 65535, 'min' => 1, ], 'DataIntegrationEventType' => [ 'type' => 'string', 'enum' => [ 'scn.data.forecast', 'scn.data.inventorylevel', 'scn.data.inboundorder', 'scn.data.inboundorderline', 'scn.data.inboundorderlineschedule', 'scn.data.outboundorderline', 'scn.data.outboundshipment', 'scn.data.processheader', 'scn.data.processoperation', 'scn.data.processproduct', 'scn.data.reservation', 'scn.data.shipment', 'scn.data.shipmentstop', 'scn.data.shipmentstoporder', 'scn.data.supplyplan', 'scn.data.dataset', ], ], 'DataIntegrationFlow' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'name', 'sources', 'transformation', 'target', 'createdTime', 'lastModifiedTime', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', ], 'name' => [ 'shape' => 'DataIntegrationFlowName', ], 'sources' => [ 'shape' => 'DataIntegrationFlowSourceList', ], 'transformation' => [ 'shape' => 'DataIntegrationFlowTransformation', ], 'target' => [ 'shape' => 'DataIntegrationFlowTarget', ], 'createdTime' => [ 'shape' => 'Timestamp', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'DataIntegrationFlowDatasetOptions' => [ 'type' => 'structure', 'members' => [ 'loadType' => [ 'shape' => 'DataIntegrationFlowLoadType', ], 'dedupeRecords' => [ 'shape' => 'Boolean', ], 'dedupeStrategy' => [ 'shape' => 'DataIntegrationFlowDedupeStrategy', ], ], ], 'DataIntegrationFlowDatasetSource' => [ 'type' => 'structure', 'required' => [ 'datasetIdentifier', ], 'members' => [ 'datasetIdentifier' => [ 'shape' => 'DataIntegrationDatasetArn', ], ], ], 'DataIntegrationFlowDatasetSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'datasetIdentifier', ], 'members' => [ 'datasetIdentifier' => [ 'shape' => 'DatasetIdentifier', ], 'options' => [ 'shape' => 'DataIntegrationFlowDatasetOptions', ], ], ], 'DataIntegrationFlowDatasetTargetConfiguration' => [ 'type' => 'structure', 'required' => [ 'datasetIdentifier', ], 'members' => [ 'datasetIdentifier' => [ 'shape' => 'DatasetIdentifier', ], 'options' => [ 'shape' => 'DataIntegrationFlowDatasetOptions', ], ], ], 'DataIntegrationFlowDedupeStrategy' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'DataIntegrationFlowDedupeStrategyType', ], 'fieldPriority' => [ 'shape' => 'DataIntegrationFlowFieldPriorityDedupeStrategyConfiguration', ], ], ], 'DataIntegrationFlowDedupeStrategyType' => [ 'type' => 'string', 'enum' => [ 'FIELD_PRIORITY', ], ], 'DataIntegrationFlowExecution' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'flowName', 'executionId', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', ], 'flowName' => [ 'shape' => 'DataIntegrationFlowName', ], 'executionId' => [ 'shape' => 'UUID', ], 'status' => [ 'shape' => 'DataIntegrationFlowExecutionStatus', ], 'sourceInfo' => [ 'shape' => 'DataIntegrationFlowExecutionSourceInfo', ], 'message' => [ 'shape' => 'String', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'outputMetadata' => [ 'shape' => 'DataIntegrationFlowExecutionOutputMetadata', ], ], ], 'DataIntegrationFlowExecutionDiagnosticReportsRootS3URI' => [ 'type' => 'string', 'pattern' => 's3://[a-z0-9][a-z0-9.-]{1,61}[a-z0-9]/.{1,1024}', ], 'DataIntegrationFlowExecutionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataIntegrationFlowExecution', ], ], 'DataIntegrationFlowExecutionMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 20, 'min' => 1, ], 'DataIntegrationFlowExecutionNextToken' => [ 'type' => 'string', 'max' => 65535, 'min' => 1, ], 'DataIntegrationFlowExecutionOutputMetadata' => [ 'type' => 'structure', 'members' => [ 'diagnosticReportsRootS3URI' => [ 'shape' => 'DataIntegrationFlowExecutionDiagnosticReportsRootS3URI', ], ], ], 'DataIntegrationFlowExecutionSourceInfo' => [ 'type' => 'structure', 'required' => [ 'sourceType', ], 'members' => [ 'sourceType' => [ 'shape' => 'DataIntegrationFlowSourceType', ], 's3Source' => [ 'shape' => 'DataIntegrationFlowS3Source', ], 'datasetSource' => [ 'shape' => 'DataIntegrationFlowDatasetSource', ], ], ], 'DataIntegrationFlowExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'IN_PROGRESS', 'FAILED', ], ], 'DataIntegrationFlowFieldPriorityDedupeField' => [ 'type' => 'structure', 'required' => [ 'name', 'sortOrder', ], 'members' => [ 'name' => [ 'shape' => 'DataIntegrationFlowFieldPriorityDedupeFieldName', ], 'sortOrder' => [ 'shape' => 'DataIntegrationFlowFieldPriorityDedupeSortOrder', ], ], ], 'DataIntegrationFlowFieldPriorityDedupeFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataIntegrationFlowFieldPriorityDedupeField', ], 'max' => 10, 'min' => 1, ], 'DataIntegrationFlowFieldPriorityDedupeFieldName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-z0-9_]+', ], 'DataIntegrationFlowFieldPriorityDedupeSortOrder' => [ 'type' => 'string', 'enum' => [ 'ASC', 'DESC', ], ], 'DataIntegrationFlowFieldPriorityDedupeStrategyConfiguration' => [ 'type' => 'structure', 'required' => [ 'fields', ], 'members' => [ 'fields' => [ 'shape' => 'DataIntegrationFlowFieldPriorityDedupeFieldList', ], ], ], 'DataIntegrationFlowFileType' => [ 'type' => 'string', 'enum' => [ 'CSV', 'PARQUET', 'JSON', ], ], 'DataIntegrationFlowList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataIntegrationFlow', ], ], 'DataIntegrationFlowLoadType' => [ 'type' => 'string', 'enum' => [ 'INCREMENTAL', 'REPLACE', ], ], 'DataIntegrationFlowMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 20, 'min' => 0, ], 'DataIntegrationFlowName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[A-Za-z0-9-]+', ], 'DataIntegrationFlowNextToken' => [ 'type' => 'string', 'max' => 65535, 'min' => 1, ], 'DataIntegrationFlowS3Options' => [ 'type' => 'structure', 'members' => [ 'fileType' => [ 'shape' => 'DataIntegrationFlowFileType', ], ], ], 'DataIntegrationFlowS3Prefix' => [ 'type' => 'string', 'max' => 700, 'min' => 0, 'pattern' => '[/A-Za-z0-9._-]+', ], 'DataIntegrationFlowS3Source' => [ 'type' => 'structure', 'required' => [ 'bucketName', 'key', ], 'members' => [ 'bucketName' => [ 'shape' => 'S3BucketName', ], 'key' => [ 'shape' => 'DataIntegrationS3ObjectKey', ], ], ], 'DataIntegrationFlowS3SourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'bucketName', 'prefix', ], 'members' => [ 'bucketName' => [ 'shape' => 'S3BucketName', ], 'prefix' => [ 'shape' => 'DataIntegrationFlowS3Prefix', ], 'options' => [ 'shape' => 'DataIntegrationFlowS3Options', ], ], ], 'DataIntegrationFlowS3TargetConfiguration' => [ 'type' => 'structure', 'required' => [ 'bucketName', 'prefix', ], 'members' => [ 'bucketName' => [ 'shape' => 'S3BucketName', ], 'prefix' => [ 'shape' => 'DataIntegrationFlowS3Prefix', ], 'options' => [ 'shape' => 'DataIntegrationFlowS3Options', ], ], ], 'DataIntegrationFlowSQLQuery' => [ 'type' => 'string', 'max' => 65535, 'min' => 1, 'sensitive' => true, ], 'DataIntegrationFlowSQLTransformationConfiguration' => [ 'type' => 'structure', 'required' => [ 'query', ], 'members' => [ 'query' => [ 'shape' => 'DataIntegrationFlowSQLQuery', ], ], ], 'DataIntegrationFlowSource' => [ 'type' => 'structure', 'required' => [ 'sourceType', 'sourceName', ], 'members' => [ 'sourceType' => [ 'shape' => 'DataIntegrationFlowSourceType', ], 'sourceName' => [ 'shape' => 'DataIntegrationFlowSourceName', ], 's3Source' => [ 'shape' => 'DataIntegrationFlowS3SourceConfiguration', ], 'datasetSource' => [ 'shape' => 'DataIntegrationFlowDatasetSourceConfiguration', ], ], ], 'DataIntegrationFlowSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataIntegrationFlowSource', ], 'max' => 40, 'min' => 1, ], 'DataIntegrationFlowSourceName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[A-Za-z0-9_]+', ], 'DataIntegrationFlowSourceType' => [ 'type' => 'string', 'enum' => [ 'S3', 'DATASET', ], ], 'DataIntegrationFlowTarget' => [ 'type' => 'structure', 'required' => [ 'targetType', ], 'members' => [ 'targetType' => [ 'shape' => 'DataIntegrationFlowTargetType', ], 's3Target' => [ 'shape' => 'DataIntegrationFlowS3TargetConfiguration', ], 'datasetTarget' => [ 'shape' => 'DataIntegrationFlowDatasetTargetConfiguration', ], ], ], 'DataIntegrationFlowTargetType' => [ 'type' => 'string', 'enum' => [ 'S3', 'DATASET', ], ], 'DataIntegrationFlowTransformation' => [ 'type' => 'structure', 'required' => [ 'transformationType', ], 'members' => [ 'transformationType' => [ 'shape' => 'DataIntegrationFlowTransformationType', ], 'sqlTransformation' => [ 'shape' => 'DataIntegrationFlowSQLTransformationConfiguration', ], ], ], 'DataIntegrationFlowTransformationType' => [ 'type' => 'string', 'enum' => [ 'SQL', 'NONE', ], ], 'DataIntegrationS3ObjectKey' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[/A-Za-z0-9._:*()\'!=?&+;@-]+', ], 'DataLakeDataset' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'namespace', 'name', 'arn', 'schema', 'createdTime', 'lastModifiedTime', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', ], 'namespace' => [ 'shape' => 'DataLakeNamespaceName', ], 'name' => [ 'shape' => 'DataLakeDatasetName', ], 'arn' => [ 'shape' => 'AscResourceArn', ], 'schema' => [ 'shape' => 'DataLakeDatasetSchema', ], 'description' => [ 'shape' => 'DataLakeDatasetDescription', ], 'partitionSpec' => [ 'shape' => 'DataLakeDatasetPartitionSpec', ], 'createdTime' => [ 'shape' => 'Timestamp', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'DataLakeDatasetDescription' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'DataLakeDatasetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataLakeDataset', ], 'max' => 20, 'min' => 0, ], 'DataLakeDatasetMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 20, 'min' => 0, ], 'DataLakeDatasetName' => [ 'type' => 'string', 'max' => 75, 'min' => 1, 'pattern' => '[a-z0-9_]+', ], 'DataLakeDatasetNextToken' => [ 'type' => 'string', 'max' => 65535, 'min' => 1, ], 'DataLakeDatasetPartitionField' => [ 'type' => 'structure', 'required' => [ 'name', 'transform', ], 'members' => [ 'name' => [ 'shape' => 'DataLakeDatasetSchemaFieldName', ], 'transform' => [ 'shape' => 'DataLakeDatasetPartitionFieldTransform', ], ], ], 'DataLakeDatasetPartitionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataLakeDatasetPartitionField', ], 'max' => 10, 'min' => 1, ], 'DataLakeDatasetPartitionFieldTransform' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'DataLakeDatasetPartitionTransformType', ], ], ], 'DataLakeDatasetPartitionSpec' => [ 'type' => 'structure', 'required' => [ 'fields', ], 'members' => [ 'fields' => [ 'shape' => 'DataLakeDatasetPartitionFieldList', ], ], ], 'DataLakeDatasetPartitionTransformType' => [ 'type' => 'string', 'enum' => [ 'YEAR', 'MONTH', 'DAY', 'HOUR', 'IDENTITY', ], ], 'DataLakeDatasetPrimaryKeyField' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'DataLakeDatasetSchemaFieldName', ], ], ], 'DataLakeDatasetPrimaryKeyFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataLakeDatasetPrimaryKeyField', ], 'max' => 20, 'min' => 1, ], 'DataLakeDatasetSchema' => [ 'type' => 'structure', 'required' => [ 'name', 'fields', ], 'members' => [ 'name' => [ 'shape' => 'DataLakeDatasetSchemaName', ], 'fields' => [ 'shape' => 'DataLakeDatasetSchemaFieldList', ], 'primaryKeys' => [ 'shape' => 'DataLakeDatasetPrimaryKeyFieldList', ], ], ], 'DataLakeDatasetSchemaField' => [ 'type' => 'structure', 'required' => [ 'name', 'type', 'isRequired', ], 'members' => [ 'name' => [ 'shape' => 'DataLakeDatasetSchemaFieldName', ], 'type' => [ 'shape' => 'DataLakeDatasetSchemaFieldType', ], 'isRequired' => [ 'shape' => 'Boolean', ], ], ], 'DataLakeDatasetSchemaFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataLakeDatasetSchemaField', ], 'max' => 500, 'min' => 1, ], 'DataLakeDatasetSchemaFieldName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-z0-9_]+', ], 'DataLakeDatasetSchemaFieldType' => [ 'type' => 'string', 'enum' => [ 'INT', 'DOUBLE', 'STRING', 'TIMESTAMP', 'LONG', ], ], 'DataLakeDatasetSchemaName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[A-Za-z0-9]+', ], 'DataLakeNamespace' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'name', 'arn', 'createdTime', 'lastModifiedTime', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', ], 'name' => [ 'shape' => 'DataLakeNamespaceName', ], 'arn' => [ 'shape' => 'AscResourceArn', ], 'description' => [ 'shape' => 'DataLakeNamespaceDescription', ], 'createdTime' => [ 'shape' => 'Timestamp', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'DataLakeNamespaceDescription' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'DataLakeNamespaceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataLakeNamespace', ], 'max' => 20, 'min' => 1, ], 'DataLakeNamespaceMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 20, 'min' => 1, ], 'DataLakeNamespaceName' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '[a-z0-9_]+', ], 'DataLakeNamespaceNextToken' => [ 'type' => 'string', 'max' => 65535, 'min' => 1, ], 'DatasetIdentifier' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => '[-_/A-Za-z0-9:]+', ], 'DeleteDataIntegrationFlowRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'name', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'name' => [ 'shape' => 'DataIntegrationFlowName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteDataIntegrationFlowResponse' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'name', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', ], 'name' => [ 'shape' => 'DataIntegrationFlowName', ], ], ], 'DeleteDataLakeDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'namespace', 'name', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'namespace' => [ 'shape' => 'DataLakeNamespaceName', 'location' => 'uri', 'locationName' => 'namespace', ], 'name' => [ 'shape' => 'DataLakeDatasetName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteDataLakeDatasetResponse' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'namespace', 'name', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', ], 'namespace' => [ 'shape' => 'DataLakeNamespaceName', ], 'name' => [ 'shape' => 'DataLakeDatasetName', ], ], ], 'DeleteDataLakeNamespaceRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'name', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'name' => [ 'shape' => 'DataLakeNamespaceName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteDataLakeNamespaceResponse' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'name', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', ], 'name' => [ 'shape' => 'DataLakeNamespaceName', ], ], ], 'DeleteInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], ], ], 'DeleteInstanceResponse' => [ 'type' => 'structure', 'required' => [ 'instance', ], 'members' => [ 'instance' => [ 'shape' => 'Instance', ], ], ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'GetBillOfMaterialsImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'jobId', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'jobId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'jobId', ], ], ], 'GetBillOfMaterialsImportJobResponse' => [ 'type' => 'structure', 'required' => [ 'job', ], 'members' => [ 'job' => [ 'shape' => 'BillOfMaterialsImportJob', ], ], ], 'GetDataIntegrationEventRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'eventId', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'eventId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'eventId', ], ], ], 'GetDataIntegrationEventResponse' => [ 'type' => 'structure', 'required' => [ 'event', ], 'members' => [ 'event' => [ 'shape' => 'DataIntegrationEvent', ], ], ], 'GetDataIntegrationFlowExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'flowName', 'executionId', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'flowName' => [ 'shape' => 'DataIntegrationFlowName', 'location' => 'uri', 'locationName' => 'flowName', ], 'executionId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'executionId', ], ], ], 'GetDataIntegrationFlowExecutionResponse' => [ 'type' => 'structure', 'required' => [ 'flowExecution', ], 'members' => [ 'flowExecution' => [ 'shape' => 'DataIntegrationFlowExecution', ], ], ], 'GetDataIntegrationFlowRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'name', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'name' => [ 'shape' => 'DataIntegrationFlowName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetDataIntegrationFlowResponse' => [ 'type' => 'structure', 'required' => [ 'flow', ], 'members' => [ 'flow' => [ 'shape' => 'DataIntegrationFlow', ], ], ], 'GetDataLakeDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'namespace', 'name', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'namespace' => [ 'shape' => 'DataLakeNamespaceName', 'location' => 'uri', 'locationName' => 'namespace', ], 'name' => [ 'shape' => 'DataLakeDatasetName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetDataLakeDatasetResponse' => [ 'type' => 'structure', 'required' => [ 'dataset', ], 'members' => [ 'dataset' => [ 'shape' => 'DataLakeDataset', ], ], ], 'GetDataLakeNamespaceRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'name', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'name' => [ 'shape' => 'DataLakeNamespaceName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'GetDataLakeNamespaceResponse' => [ 'type' => 'structure', 'required' => [ 'namespace', ], 'members' => [ 'namespace' => [ 'shape' => 'DataLakeNamespace', ], ], ], 'GetInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], ], ], 'GetInstanceResponse' => [ 'type' => 'structure', 'required' => [ 'instance', ], 'members' => [ 'instance' => [ 'shape' => 'Instance', ], ], ], 'Instance' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'awsAccountId', 'state', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', ], 'awsAccountId' => [ 'shape' => 'AwsAccountId', ], 'state' => [ 'shape' => 'InstanceState', ], 'errorMessage' => [ 'shape' => 'String', ], 'webAppDnsDomain' => [ 'shape' => 'InstanceWebAppDnsDomain', ], 'createdTime' => [ 'shape' => 'Timestamp', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], 'instanceName' => [ 'shape' => 'InstanceName', ], 'instanceDescription' => [ 'shape' => 'InstanceDescription', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'versionNumber' => [ 'shape' => 'Double', ], ], ], 'InstanceDescription' => [ 'type' => 'string', 'max' => 501, 'min' => 0, 'pattern' => '([a-zA-Z0-9., _ʼ\'%-]){0,500}', ], 'InstanceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Instance', ], ], 'InstanceMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 20, 'min' => 0, ], 'InstanceName' => [ 'type' => 'string', 'max' => 63, 'min' => 0, 'pattern' => '(?![ _ʼ\'%-])[a-zA-Z0-9 _ʼ\'%-]{0,62}[a-zA-Z0-9]', ], 'InstanceNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceName', ], 'max' => 10, 'min' => 0, ], 'InstanceNextToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'InstanceState' => [ 'type' => 'string', 'enum' => [ 'Initializing', 'Active', 'CreateFailed', 'DeleteFailed', 'Deleting', 'Deleted', ], ], 'InstanceStateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceState', ], 'max' => 6, 'min' => 0, ], 'InstanceWebAppDnsDomain' => [ 'type' => 'string', 'pattern' => '(?![-])[a-zA-Z0-9-]{1,62}[a-zA-Z0-9]', ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'KmsKeyArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => 'arn:[a-z0-9][-.a-z0-9]{0,62}:kms:([a-z0-9][-.a-z0-9]{0,62})?:([a-z0-9][-.a-z0-9]{0,62})?:key/.{0,1019}', ], 'ListDataIntegrationEventsRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'eventType' => [ 'shape' => 'DataIntegrationEventType', 'location' => 'querystring', 'locationName' => 'eventType', ], 'nextToken' => [ 'shape' => 'DataIntegrationEventNextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'DataIntegrationEventMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListDataIntegrationEventsResponse' => [ 'type' => 'structure', 'required' => [ 'events', ], 'members' => [ 'events' => [ 'shape' => 'DataIntegrationEventList', ], 'nextToken' => [ 'shape' => 'DataIntegrationEventNextToken', ], ], ], 'ListDataIntegrationFlowExecutionsRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'flowName', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'flowName' => [ 'shape' => 'DataIntegrationFlowName', 'location' => 'uri', 'locationName' => 'flowName', ], 'nextToken' => [ 'shape' => 'DataIntegrationFlowExecutionNextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'DataIntegrationFlowExecutionMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListDataIntegrationFlowExecutionsResponse' => [ 'type' => 'structure', 'required' => [ 'flowExecutions', ], 'members' => [ 'flowExecutions' => [ 'shape' => 'DataIntegrationFlowExecutionList', ], 'nextToken' => [ 'shape' => 'DataIntegrationFlowExecutionNextToken', ], ], ], 'ListDataIntegrationFlowsRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'nextToken' => [ 'shape' => 'DataIntegrationFlowNextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'DataIntegrationFlowMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListDataIntegrationFlowsResponse' => [ 'type' => 'structure', 'required' => [ 'flows', ], 'members' => [ 'flows' => [ 'shape' => 'DataIntegrationFlowList', ], 'nextToken' => [ 'shape' => 'DataIntegrationFlowNextToken', ], ], ], 'ListDataLakeDatasetsRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'namespace', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'namespace' => [ 'shape' => 'DataLakeNamespaceName', 'location' => 'uri', 'locationName' => 'namespace', ], 'nextToken' => [ 'shape' => 'DataLakeDatasetNextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'DataLakeDatasetMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListDataLakeDatasetsResponse' => [ 'type' => 'structure', 'required' => [ 'datasets', ], 'members' => [ 'datasets' => [ 'shape' => 'DataLakeDatasetList', ], 'nextToken' => [ 'shape' => 'DataLakeDatasetNextToken', ], ], ], 'ListDataLakeNamespacesRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'nextToken' => [ 'shape' => 'DataLakeNamespaceNextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'DataLakeNamespaceMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListDataLakeNamespacesResponse' => [ 'type' => 'structure', 'required' => [ 'namespaces', ], 'members' => [ 'namespaces' => [ 'shape' => 'DataLakeNamespaceList', ], 'nextToken' => [ 'shape' => 'DataLakeNamespaceNextToken', ], ], ], 'ListInstancesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'InstanceNextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'InstanceMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'instanceNameFilter' => [ 'shape' => 'InstanceNameList', 'location' => 'querystring', 'locationName' => 'instanceNameFilter', ], 'instanceStateFilter' => [ 'shape' => 'InstanceStateList', 'location' => 'querystring', 'locationName' => 'instanceStateFilter', ], ], ], 'ListInstancesResponse' => [ 'type' => 'structure', 'required' => [ 'instances', ], 'members' => [ 'instances' => [ 'shape' => 'InstanceList', ], 'nextToken' => [ 'shape' => 'InstanceNextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AscResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'required' => [ 'tags', ], 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'S3BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '[a-z0-9][a-z0-9.-]*[a-z0-9]', ], 'SendDataIntegrationEventRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'eventType', 'data', 'eventGroupId', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'eventType' => [ 'shape' => 'DataIntegrationEventType', ], 'data' => [ 'shape' => 'DataIntegrationEventData', ], 'eventGroupId' => [ 'shape' => 'DataIntegrationEventGroupId', ], 'eventTimestamp' => [ 'shape' => 'SyntheticTimestamp_epoch_seconds', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'datasetTarget' => [ 'shape' => 'DataIntegrationEventDatasetTargetConfiguration', ], ], ], 'SendDataIntegrationEventResponse' => [ 'type' => 'structure', 'required' => [ 'eventId', ], 'members' => [ 'eventId' => [ 'shape' => 'UUID', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'String' => [ 'type' => 'string', ], 'SyntheticTimestamp_epoch_seconds' => [ 'type' => 'timestamp', 'timestampFormat' => 'unixTimestamp', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AscResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UUID' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AscResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDataIntegrationFlowRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'name', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'name' => [ 'shape' => 'DataIntegrationFlowName', 'location' => 'uri', 'locationName' => 'name', ], 'sources' => [ 'shape' => 'DataIntegrationFlowSourceList', ], 'transformation' => [ 'shape' => 'DataIntegrationFlowTransformation', ], 'target' => [ 'shape' => 'DataIntegrationFlowTarget', ], ], ], 'UpdateDataIntegrationFlowResponse' => [ 'type' => 'structure', 'required' => [ 'flow', ], 'members' => [ 'flow' => [ 'shape' => 'DataIntegrationFlow', ], ], ], 'UpdateDataLakeDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'namespace', 'name', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'namespace' => [ 'shape' => 'DataLakeNamespaceName', 'location' => 'uri', 'locationName' => 'namespace', ], 'name' => [ 'shape' => 'DataLakeDatasetName', 'location' => 'uri', 'locationName' => 'name', ], 'description' => [ 'shape' => 'DataLakeDatasetDescription', ], ], ], 'UpdateDataLakeDatasetResponse' => [ 'type' => 'structure', 'required' => [ 'dataset', ], 'members' => [ 'dataset' => [ 'shape' => 'DataLakeDataset', ], ], ], 'UpdateDataLakeNamespaceRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'name', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'name' => [ 'shape' => 'DataLakeNamespaceName', 'location' => 'uri', 'locationName' => 'name', ], 'description' => [ 'shape' => 'DataLakeNamespaceDescription', ], ], ], 'UpdateDataLakeNamespaceResponse' => [ 'type' => 'structure', 'required' => [ 'namespace', ], 'members' => [ 'namespace' => [ 'shape' => 'DataLakeNamespace', ], ], ], 'UpdateInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'instanceId', ], 'members' => [ 'instanceId' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'instanceId', ], 'instanceName' => [ 'shape' => 'InstanceName', ], 'instanceDescription' => [ 'shape' => 'InstanceDescription', ], ], ], 'UpdateInstanceResponse' => [ 'type' => 'structure', 'required' => [ 'instance', ], 'members' => [ 'instance' => [ 'shape' => 'Instance', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], ],];
