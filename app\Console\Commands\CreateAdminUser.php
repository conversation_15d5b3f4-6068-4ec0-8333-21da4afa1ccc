<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CreateAdminUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create:admin';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create admin user for Hesabiai';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $user = \App\Models\User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('123456'),
            'type' => 'company',
            'lang' => 'en',
            'created_by' => 1
        ]);

        $this->info('Admin user created successfully!');
        $this->info('Email: <EMAIL>');
        $this->info('Password: 123456');
        $this->info('User ID: ' . $user->id);
    }
}
