<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Conversations
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Conversations\V1\Service;

use Twilio\Options;
use Twilio\Values;

abstract class UserOptions
{
    /**
     * @param string $friendlyName The string that you assigned to describe the resource.
     * @param string $attributes The JSON Object string that stores application-specific data. If attributes have not been set, `{}` is returned.
     * @param string $roleSid The SID of a service-level [Role](https://www.twilio.com/docs/conversations/api/role-resource) to assign to the user.
     * @param string $xTwilioWebhookEnabled The X-Twilio-Webhook-Enabled HTTP request header
     * @return CreateUserOptions Options builder
     */
    public static function create(
        
        string $friendlyName = Values::NONE,
        string $attributes = Values::NONE,
        string $roleSid = Values::NONE,
        string $xTwilioWebhookEnabled = Values::NONE

    ): CreateUserOptions
    {
        return new CreateUserOptions(
            $friendlyName,
            $attributes,
            $roleSid,
            $xTwilioWebhookEnabled
        );
    }

    /**
     * @param string $xTwilioWebhookEnabled The X-Twilio-Webhook-Enabled HTTP request header
     * @return DeleteUserOptions Options builder
     */
    public static function delete(
        
        string $xTwilioWebhookEnabled = Values::NONE

    ): DeleteUserOptions
    {
        return new DeleteUserOptions(
            $xTwilioWebhookEnabled
        );
    }



    /**
     * @param string $friendlyName The string that you assigned to describe the resource.
     * @param string $attributes The JSON Object string that stores application-specific data. If attributes have not been set, `{}` is returned.
     * @param string $roleSid The SID of a service-level [Role](https://www.twilio.com/docs/conversations/api/role-resource) to assign to the user.
     * @param string $xTwilioWebhookEnabled The X-Twilio-Webhook-Enabled HTTP request header
     * @return UpdateUserOptions Options builder
     */
    public static function update(
        
        string $friendlyName = Values::NONE,
        string $attributes = Values::NONE,
        string $roleSid = Values::NONE,
        string $xTwilioWebhookEnabled = Values::NONE

    ): UpdateUserOptions
    {
        return new UpdateUserOptions(
            $friendlyName,
            $attributes,
            $roleSid,
            $xTwilioWebhookEnabled
        );
    }

}

class CreateUserOptions extends Options
    {
    /**
     * @param string $friendlyName The string that you assigned to describe the resource.
     * @param string $attributes The JSON Object string that stores application-specific data. If attributes have not been set, `{}` is returned.
     * @param string $roleSid The SID of a service-level [Role](https://www.twilio.com/docs/conversations/api/role-resource) to assign to the user.
     * @param string $xTwilioWebhookEnabled The X-Twilio-Webhook-Enabled HTTP request header
     */
    public function __construct(
        
        string $friendlyName = Values::NONE,
        string $attributes = Values::NONE,
        string $roleSid = Values::NONE,
        string $xTwilioWebhookEnabled = Values::NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
        $this->options['attributes'] = $attributes;
        $this->options['roleSid'] = $roleSid;
        $this->options['xTwilioWebhookEnabled'] = $xTwilioWebhookEnabled;
    }

    /**
     * The string that you assigned to describe the resource.
     *
     * @param string $friendlyName The string that you assigned to describe the resource.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * The JSON Object string that stores application-specific data. If attributes have not been set, `{}` is returned.
     *
     * @param string $attributes The JSON Object string that stores application-specific data. If attributes have not been set, `{}` is returned.
     * @return $this Fluent Builder
     */
    public function setAttributes(string $attributes): self
    {
        $this->options['attributes'] = $attributes;
        return $this;
    }

    /**
     * The SID of a service-level [Role](https://www.twilio.com/docs/conversations/api/role-resource) to assign to the user.
     *
     * @param string $roleSid The SID of a service-level [Role](https://www.twilio.com/docs/conversations/api/role-resource) to assign to the user.
     * @return $this Fluent Builder
     */
    public function setRoleSid(string $roleSid): self
    {
        $this->options['roleSid'] = $roleSid;
        return $this;
    }

    /**
     * The X-Twilio-Webhook-Enabled HTTP request header
     *
     * @param string $xTwilioWebhookEnabled The X-Twilio-Webhook-Enabled HTTP request header
     * @return $this Fluent Builder
     */
    public function setXTwilioWebhookEnabled(string $xTwilioWebhookEnabled): self
    {
        $this->options['xTwilioWebhookEnabled'] = $xTwilioWebhookEnabled;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Conversations.V1.CreateUserOptions ' . $options . ']';
    }
}

class DeleteUserOptions extends Options
    {
    /**
     * @param string $xTwilioWebhookEnabled The X-Twilio-Webhook-Enabled HTTP request header
     */
    public function __construct(
        
        string $xTwilioWebhookEnabled = Values::NONE

    ) {
        $this->options['xTwilioWebhookEnabled'] = $xTwilioWebhookEnabled;
    }

    /**
     * The X-Twilio-Webhook-Enabled HTTP request header
     *
     * @param string $xTwilioWebhookEnabled The X-Twilio-Webhook-Enabled HTTP request header
     * @return $this Fluent Builder
     */
    public function setXTwilioWebhookEnabled(string $xTwilioWebhookEnabled): self
    {
        $this->options['xTwilioWebhookEnabled'] = $xTwilioWebhookEnabled;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Conversations.V1.DeleteUserOptions ' . $options . ']';
    }
}



class UpdateUserOptions extends Options
    {
    /**
     * @param string $friendlyName The string that you assigned to describe the resource.
     * @param string $attributes The JSON Object string that stores application-specific data. If attributes have not been set, `{}` is returned.
     * @param string $roleSid The SID of a service-level [Role](https://www.twilio.com/docs/conversations/api/role-resource) to assign to the user.
     * @param string $xTwilioWebhookEnabled The X-Twilio-Webhook-Enabled HTTP request header
     */
    public function __construct(
        
        string $friendlyName = Values::NONE,
        string $attributes = Values::NONE,
        string $roleSid = Values::NONE,
        string $xTwilioWebhookEnabled = Values::NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
        $this->options['attributes'] = $attributes;
        $this->options['roleSid'] = $roleSid;
        $this->options['xTwilioWebhookEnabled'] = $xTwilioWebhookEnabled;
    }

    /**
     * The string that you assigned to describe the resource.
     *
     * @param string $friendlyName The string that you assigned to describe the resource.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * The JSON Object string that stores application-specific data. If attributes have not been set, `{}` is returned.
     *
     * @param string $attributes The JSON Object string that stores application-specific data. If attributes have not been set, `{}` is returned.
     * @return $this Fluent Builder
     */
    public function setAttributes(string $attributes): self
    {
        $this->options['attributes'] = $attributes;
        return $this;
    }

    /**
     * The SID of a service-level [Role](https://www.twilio.com/docs/conversations/api/role-resource) to assign to the user.
     *
     * @param string $roleSid The SID of a service-level [Role](https://www.twilio.com/docs/conversations/api/role-resource) to assign to the user.
     * @return $this Fluent Builder
     */
    public function setRoleSid(string $roleSid): self
    {
        $this->options['roleSid'] = $roleSid;
        return $this;
    }

    /**
     * The X-Twilio-Webhook-Enabled HTTP request header
     *
     * @param string $xTwilioWebhookEnabled The X-Twilio-Webhook-Enabled HTTP request header
     * @return $this Fluent Builder
     */
    public function setXTwilioWebhookEnabled(string $xTwilioWebhookEnabled): self
    {
        $this->options['xTwilioWebhookEnabled'] = $xTwilioWebhookEnabled;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Conversations.V1.UpdateUserOptions ' . $options . ']';
    }
}

