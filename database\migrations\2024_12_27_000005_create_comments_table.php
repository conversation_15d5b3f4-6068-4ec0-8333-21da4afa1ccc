<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * إنشاء جدول التعليقات
 * 
 * جدول متعدد الاستخدامات للتعليقات على المشاريع والمهام
 * مع دعم التعليقات المتداخلة والإشارات
 * 
 * @package Database\Migrations
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
return new class extends Migration
{
    /**
     * تشغيل الـ Migration
     * 
     * @return void
     */
    public function up(): void
    {
        Schema::create('comments', function (Blueprint $table) {
            // المعرف الأساسي
            $table->id()->comment('معرف التعليق الفريد');
            
            // محتوى التعليق
            $table->text('content')->comment('محتوى التعليق');
            
            // معلومات المستخدم
            $table->foreignId('user_id')
                  ->constrained('users')
                  ->onDelete('cascade')
                  ->comment('معرف المستخدم الذي كتب التعليق');
            
            // العلاقة متعددة الأشكال (Polymorphic)
            $table->string('commentable_type')->comment('نوع الكائن المعلق عليه');
            $table->unsignedBigInteger('commentable_id')->comment('معرف الكائن المعلق عليه');
            
            // التعليقات المتداخلة
            $table->foreignId('parent_id')
                  ->nullable()
                  ->constrained('comments')
                  ->onDelete('cascade')
                  ->comment('معرف التعليق الأب للردود');
            
            // خصائص التعليق
            $table->boolean('is_private')->default(false)->comment('تعليق خاص (مرئي للفريق فقط)');
            $table->boolean('is_pinned')->default(false)->comment('تعليق مثبت');
            
            // الإشارات في التعليق
            $table->json('mentions')->nullable()->comment('قائمة معرفات المستخدمين المذكورين');
            
            // بيانات إضافية
            $table->json('metadata')->nullable()->comment('بيانات إضافية مثل المرفقات، التنسيق، إلخ');
            
            // طوابع زمنية
            $table->timestamps();
            $table->softDeletes();
            
            // الفهارس
            $table->index(['commentable_type', 'commentable_id'], 'idx_comments_commentable');
            $table->index(['user_id', 'created_at'], 'idx_comments_user_date');
            $table->index(['parent_id', 'created_at'], 'idx_comments_parent_date');
            $table->index(['is_private', 'is_pinned'], 'idx_comments_flags');
            $table->index(['commentable_type', 'commentable_id', 'is_private'], 'idx_comments_visibility');
            
            // فهرس نصي للبحث في المحتوى
            $table->fullText(['content'], 'idx_comments_content_search');
        });
        
        DB::statement("ALTER TABLE comments COMMENT = 'جدول التعليقات - تعليقات متعددة الاستخدامات مع دعم التداخل والإشارات'");
    }

    /**
     * التراجع عن الـ Migration
     * 
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('comments');
    }
};
