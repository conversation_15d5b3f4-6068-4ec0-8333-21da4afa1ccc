<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Iam
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Iam\V1;

use Twilio\Options;
use Twilio\Values;

abstract class NewApiKeyOptions
{
    /**
     * @param string $friendlyName A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     * @param string $keyType
     * @param array $policy The \\\\`Policy\\\\` object is a collection that specifies the allowed Twilio permissions for the restricted key. For more information on the permissions available with restricted API keys, refer to the [Twilio documentation](https://www.twilio.com/docs/iam/api-keys/restricted-api-keys#permissions-available-with-restricted-api-keys).
     * @return CreateNewApiKeyOptions Options builder
     */
    public static function create(
        
        string $friendlyName = Values::NONE,
        string $keyType = Values::NONE,
        array $policy = Values::ARRAY_NONE

    ): CreateNewApiKeyOptions
    {
        return new CreateNewApiKeyOptions(
            $friendlyName,
            $keyType,
            $policy
        );
    }

}

class CreateNewApiKeyOptions extends Options
    {
    /**
     * @param string $friendlyName A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     * @param string $keyType
     * @param array $policy The \\\\`Policy\\\\` object is a collection that specifies the allowed Twilio permissions for the restricted key. For more information on the permissions available with restricted API keys, refer to the [Twilio documentation](https://www.twilio.com/docs/iam/api-keys/restricted-api-keys#permissions-available-with-restricted-api-keys).
     */
    public function __construct(
        
        string $friendlyName = Values::NONE,
        string $keyType = Values::NONE,
        array $policy = Values::ARRAY_NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
        $this->options['keyType'] = $keyType;
        $this->options['policy'] = $policy;
    }

    /**
     * A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     *
     * @param string $friendlyName A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * @param string $keyType
     * @return $this Fluent Builder
     */
    public function setKeyType(string $keyType): self
    {
        $this->options['keyType'] = $keyType;
        return $this;
    }

    /**
     * The \\\\`Policy\\\\` object is a collection that specifies the allowed Twilio permissions for the restricted key. For more information on the permissions available with restricted API keys, refer to the [Twilio documentation](https://www.twilio.com/docs/iam/api-keys/restricted-api-keys#permissions-available-with-restricted-api-keys).
     *
     * @param array $policy The \\\\`Policy\\\\` object is a collection that specifies the allowed Twilio permissions for the restricted key. For more information on the permissions available with restricted API keys, refer to the [Twilio documentation](https://www.twilio.com/docs/iam/api-keys/restricted-api-keys#permissions-available-with-restricted-api-keys).
     * @return $this Fluent Builder
     */
    public function setPolicy(array $policy): self
    {
        $this->options['policy'] = $policy;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Iam.V1.CreateNewApiKeyOptions ' . $options . ']';
    }
}

