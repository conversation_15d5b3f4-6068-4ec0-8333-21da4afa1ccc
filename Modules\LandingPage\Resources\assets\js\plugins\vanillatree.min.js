!function(e,t){"function"==typeof define&&define.amd?define(t):"object"==typeof module&&module.exports?module.exports=t():e.Vanilla<PERSON>ree=t()}(this,function(){"use strict";var n,i,r,a,s,l,t,o,c,d,h,u,p=(n=window,i=document,r=[],a=/\.(.+)/,s=0,l="EventListener",t="MatchesSelector",(u=function(e,t){return new u.i(e,t)}).i=function(e,t){r.push.apply(this,e?e.nodeType||e==n?[e]:""+e===e?/</.test(e)?((o=i.createElement(t||"q")).innerHTML=e,o.children):(t&&u(t)[0]||i).querySelectorAll(e):/f/.test(typeof e)?/c/.test(i.readyState)?e():u(i).on("DOMContentLoaded",e):e:r)},u.i[h="prototype"]=(u.extend=function(e){for(d=arguments,o=1;o<d.length;o++)if(h=d[o])for(c in h)e[c]=h[c];return e})(u.fn=u[h]=r,{on:function(t,n){return t=t.split(a),this.map(function(e){(a[o=t[0]+(e.b$=e.b$||++s)]=a[o]||[]).push([n,t[1]]),e["add"+l](t[0],n)}),this},off:function(t,n){return t=t.split(a),h="remove"+l,this.map(function(e){if(o=(d=a[t[0]+e.b$])&&d.length)for(;c=d[--o];)n&&n!=c[0]||t[1]&&t[1]!=c[1]||(e[h](t[0],c[0]),d.splice(o,1));else!t[1]&&e[h](t[0],n)}),this},is:function(e){return!!(c=(o=this[0])&&(o.matches||o["webkit"+t]||o["moz"+t]||o["ms"+t]))&&c.call(o,e)}}),u),f=function(e,t){return p.extend(document.createElement(e),t)},e=function(e,i){var t=this,n=t.container=p(e)[0],r=t.tree=n.appendChild(f("ul",{className:"vtree"}));t.placeholder=i&&i.placeholder,t._placeholder(),t.leafs={},r.addEventListener("click",function(e){p(e.target).is(".vtree-leaf-label")?t.select(e.target.parentNode.getAttribute("data-vtree-id")):p(e.target).is(".vtree-toggle")&&t.toggle(e.target.parentNode.getAttribute("data-vtree-id"))}),i&&i.contextmenu&&(r.addEventListener("contextmenu",function(t){var n;if(p(".vtree-contextmenu").forEach(function(e){e.parentNode.removeChild(e)}),p(t.target).is(".vtree-leaf-label")){t.preventDefault(),t.stopPropagation(),n=f("menu",{className:"vtree-contextmenu"});var e=t.target.getBoundingClientRect();p.extend(n.style,{top:(t.target.offsetTop+e.height).toString()+"px",left:t.target.offsetLeft.toString()+"px",display:"block"}),i.contextmenu.forEach(function(e){n.appendChild(f("li",{className:"vtree-contextmenu-item",innerHTML:e.label})).addEventListener("click",e.action.bind(e,t.target.parentNode.getAttribute("data-vtree-id")))}),t.target.parentNode.appendChild(n)}}),document.addEventListener("click",function(e){2!==e.button&&p(".vtree-contextmenu").forEach(function(e){e.parentNode.removeChild(e)})}))};return e.prototype={constructor:e,_dispatch:function(t,n){var i;try{i=new CustomEvent("vtree-"+t,{bubbles:!0,cancelable:!0,detail:{id:n}})}catch(e){(i=document.createEvent("CustomEvent")).initCustomEvent("vtree-"+t,!0,!0,{id:n})}return(this.getLeaf(n,!0)||this.tree).dispatchEvent(i),this},_placeholder:function(){var e;return!this.tree.children.length&&this.placeholder?this.tree.innerHTML='<li class="vtree-placeholder">'+this.placeholder+"</li>":(e=this.tree.querySelector(".vtree-placeholder"))&&this.tree.removeChild(e),this},getLeaf:function(e,t){var n=p('[data-vtree-id="'+e+'"]',this.tree)[0];if(!t&&!n)throw Error('No VanillaTree leaf with id "'+e+'"');return n},getChildList:function(e){var t,n;return e?(n=this.getLeaf(e),(t=p("ul",n)[0])||(t=n.appendChild(f("ul",{className:"vtree-subtree"})))):t=this.tree,t},add:function(e){var t,n=f("li",{className:"vtree-leaf"}),i=this.getChildList(e.parent);return n.setAttribute("data-vtree-id",t=e.id||Math.random()),n.appendChild(f("span",{className:"vtree-toggle"})),n.appendChild(f("a",{className:"vtree-leaf-label",innerHTML:e.label})),i.appendChild(n),i!==this.tree&&i.parentNode.classList.add("vtree-has-children"),(this.leafs[t]=e).opened||this.close(t),e.selected&&this.select(t),this._placeholder()._dispatch("add",t)},move:function(e,t){var n=this.getLeaf(e),i=n.parentNode,r=this.getLeaf(t,!0);return r&&r.classList.add("vtree-has-children"),this.getChildList(t).appendChild(n),i.parentNode.classList.toggle("vtree-has-children",!!i.children.length),this._dispatch("move",e)},remove:function(e){var t=this.getLeaf(e),n=t.parentNode;return n.removeChild(t),n.parentNode.classList.toggle("vtree-has-children",!!n.children.length),this._placeholder()._dispatch("remove",e)},open:function(e){return this.getLeaf(e).classList.remove("closed"),this._dispatch("open",e)},close:function(e){return this.getLeaf(e).classList.add("closed"),this._dispatch("close",e)},toggle:function(e){return this[this.getLeaf(e).classList.contains("closed")?"open":"close"](e)},select:function(e){var t=this.getLeaf(e);return t.classList.contains("vtree-selected")||(p("li.vtree-leaf",this.tree).forEach(function(e){e.classList.remove("vtree-selected")}),t.classList.add("vtree-selected"),this._dispatch("select",e)),this}},e});
