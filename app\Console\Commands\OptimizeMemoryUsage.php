<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\MemoryOptimizationService;
use App\Models\Invoice;
use App\Models\Customer;
use Illuminate\Support\Facades\DB;

class OptimizeMemoryUsage extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'optimize:memory {--test : Run in test mode} {--chunk-size=100 : Chunk size for processing}';

    /**
     * The console command description.
     */
    protected $description = 'Optimize memory usage by cleaning up and reorganizing data';

    protected $memoryOptimizer;

    public function __construct(MemoryOptimizationService $memoryOptimizer)
    {
        parent::__construct();
        $this->memoryOptimizer = $memoryOptimizer;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting memory optimization...');
        
        $testMode = $this->option('test');
        $chunkSize = (int) $this->option('chunk-size');

        // مراقبة الذاكرة في البداية
        $initialMemory = $this->memoryOptimizer->monitorMemoryUsage('optimization_start');
        $this->info("Initial memory usage: " . $this->formatBytes($initialMemory['current']));

        if ($testMode) {
            $this->runTests();
        } else {
            $this->runOptimization($chunkSize);
        }

        // مراقبة الذاكرة في النهاية
        $finalMemory = $this->memoryOptimizer->monitorMemoryUsage('optimization_end');
        $this->info("Final memory usage: " . $this->formatBytes($finalMemory['current']));
        
        $saved = $initialMemory['current'] - $finalMemory['current'];
        $this->info("Memory saved: " . $this->formatBytes($saved));

        $this->info('Memory optimization completed!');
    }

    protected function runTests()
    {
        $this->info('Running memory optimization tests...');

        // اختبار تحميل الفواتير
        $this->testInvoiceLoading();
        
        // اختبار تحميل العملاء
        $this->testCustomerLoading();
        
        // اختبار الاستعلامات المعقدة
        $this->testComplexQueries();
    }

    protected function testInvoiceLoading()
    {
        $this->info('Testing invoice loading...');
        
        $memoryBefore = memory_get_usage(true);
        
        // الطريقة القديمة (غير محسنة)
        $this->line('Loading invoices (old way)...');
        $invoicesOld = Invoice::with(['customer', 'items', 'payments'])->get();
        $memoryOld = memory_get_usage(true) - $memoryBefore;
        
        // تنظيف الذاكرة
        unset($invoicesOld);
        $this->memoryOptimizer->cleanupMemory();
        
        $memoryBefore = memory_get_usage(true);
        
        // الطريقة الجديدة (محسنة)
        $this->line('Loading invoices (optimized way)...');
        $invoicesNew = Invoice::select(['id', 'invoice_id', 'customer_id', 'status', 'total'])
                             ->with(['customer:id,name'])
                             ->lazy();
        
        $count = 0;
        foreach ($invoicesNew as $invoice) {
            $count++;
            if ($count >= 100) break; // تحديد العدد للاختبار
        }
        
        $memoryNew = memory_get_usage(true) - $memoryBefore;
        
        $this->table(['Method', 'Memory Usage', 'Improvement'], [
            ['Old (eager loading)', $this->formatBytes($memoryOld), '-'],
            ['New (lazy loading)', $this->formatBytes($memoryNew), $this->formatBytes($memoryOld - $memoryNew)]
        ]);
    }

    protected function testCustomerLoading()
    {
        $this->info('Testing customer loading...');
        
        $memoryBefore = memory_get_usage(true);
        
        // الطريقة القديمة
        $this->line('Loading customers (old way)...');
        $customersOld = Customer::get();
        $memoryOld = memory_get_usage(true) - $memoryBefore;
        
        unset($customersOld);
        $this->memoryOptimizer->cleanupMemory();
        
        $memoryBefore = memory_get_usage(true);
        
        // الطريقة الجديدة
        $this->line('Loading customers (optimized way)...');
        $customersNew = Customer::select(['id', 'name', 'email', 'contact'])->lazy();
        
        $count = 0;
        foreach ($customersNew as $customer) {
            $count++;
            if ($count >= 100) break;
        }
        
        $memoryNew = memory_get_usage(true) - $memoryBefore;
        
        $this->table(['Method', 'Memory Usage', 'Improvement'], [
            ['Old (full loading)', $this->formatBytes($memoryOld), '-'],
            ['New (selective loading)', $this->formatBytes($memoryNew), $this->formatBytes($memoryOld - $memoryNew)]
        ]);
    }

    protected function testComplexQueries()
    {
        $this->info('Testing complex queries...');
        
        // اختبار استعلام التقارير
        $memoryBefore = memory_get_usage(true);
        
        $reportData = DB::table('invoices')
                       ->select(DB::raw('
                           MONTH(issue_date) as month,
                           COUNT(*) as invoice_count,
                           SUM(total) as total_amount
                       '))
                       ->whereYear('issue_date', date('Y'))
                       ->groupBy('month')
                       ->get();
        
        $memoryUsed = memory_get_usage(true) - $memoryBefore;
        
        $this->info("Report query memory usage: " . $this->formatBytes($memoryUsed));
        $this->info("Report data count: " . $reportData->count());
    }

    protected function runOptimization($chunkSize)
    {
        $this->info('Running memory optimization...');

        // تنظيف البيانات القديمة
        $this->optimizeOldData($chunkSize);
        
        // تحسين الفهارس
        $this->optimizeIndexes();
        
        // تنظيف الذاكرة
        $this->memoryOptimizer->cleanupMemory();
    }

    protected function optimizeOldData($chunkSize)
    {
        $this->info('Optimizing old data...');
        
        // حذف الفواتير المسودة القديمة
        $oldDrafts = Invoice::where('status', 'Draft')
                           ->where('created_at', '<', now()->subMonths(6))
                           ->count();
        
        if ($oldDrafts > 0) {
            $this->info("Found {$oldDrafts} old draft invoices");
            
            if ($this->confirm('Delete old draft invoices?')) {
                Invoice::where('status', 'Draft')
                       ->where('created_at', '<', now()->subMonths(6))
                       ->chunk($chunkSize, function ($invoices) {
                           foreach ($invoices as $invoice) {
                               $invoice->delete();
                           }
                       });
                
                $this->info('Old draft invoices deleted');
            }
        }
    }

    protected function optimizeIndexes()
    {
        $this->info('Checking database indexes...');
        
        // فحص الفهارس المهمة
        $indexes = [
            'invoices' => ['customer_id', 'status', 'issue_date', 'created_by'],
            'customers' => ['created_by', 'is_active'],
            'invoice_products' => ['invoice_id', 'product_id'],
            'invoice_payments' => ['invoice_id', 'date']
        ];
        
        foreach ($indexes as $table => $columns) {
            foreach ($columns as $column) {
                $indexExists = DB::select("SHOW INDEX FROM {$table} WHERE Column_name = ?", [$column]);
                
                if (empty($indexExists)) {
                    $this->warn("Missing index on {$table}.{$column}");
                } else {
                    $this->info("✓ Index exists on {$table}.{$column}");
                }
            }
        }
    }

    protected function formatBytes($bytes)
    {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }
}
