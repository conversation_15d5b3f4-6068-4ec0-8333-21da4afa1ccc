<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Marketplace
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Marketplace\V1;

use Twilio\Options;
use Twilio\Values;

abstract class ModuleDataOptions
{
    /**
     * @param string $moduleInfo A JSON object containing essential attributes that define a Listing.
     * @param string $configuration A JSON object for providing Listing-specific configuration. Contains button setup, notification URL, and more.
     * @return CreateModuleDataOptions Options builder
     */
    public static function create(
        
        string $moduleInfo = Values::NONE,
        string $configuration = Values::NONE

    ): CreateModuleDataOptions
    {
        return new CreateModuleDataOptions(
            $moduleInfo,
            $configuration
        );
    }


}

class CreateModuleDataOptions extends Options
    {
    /**
     * @param string $moduleInfo A JSON object containing essential attributes that define a Listing.
     * @param string $configuration A JSON object for providing Listing-specific configuration. Contains button setup, notification URL, and more.
     */
    public function __construct(
        
        string $moduleInfo = Values::NONE,
        string $configuration = Values::NONE

    ) {
        $this->options['moduleInfo'] = $moduleInfo;
        $this->options['configuration'] = $configuration;
    }

    /**
     * A JSON object containing essential attributes that define a Listing.
     *
     * @param string $moduleInfo A JSON object containing essential attributes that define a Listing.
     * @return $this Fluent Builder
     */
    public function setModuleInfo(string $moduleInfo): self
    {
        $this->options['moduleInfo'] = $moduleInfo;
        return $this;
    }

    /**
     * A JSON object for providing Listing-specific configuration. Contains button setup, notification URL, and more.
     *
     * @param string $configuration A JSON object for providing Listing-specific configuration. Contains button setup, notification URL, and more.
     * @return $this Fluent Builder
     */
    public function setConfiguration(string $configuration): self
    {
        $this->options['configuration'] = $configuration;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Marketplace.V1.CreateModuleDataOptions ' . $options . ']';
    }
}


