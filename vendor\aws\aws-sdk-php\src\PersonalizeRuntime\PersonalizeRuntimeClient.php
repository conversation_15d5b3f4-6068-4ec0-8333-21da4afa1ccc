<?php
namespace Aws\PersonalizeRuntime;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon Personalize Runtime** service.
 * @method \Aws\Result getActionRecommendations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getActionRecommendationsAsync(array $args = [])
 * @method \Aws\Result getPersonalizedRanking(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getPersonalizedRankingAsync(array $args = [])
 * @method \Aws\Result getRecommendations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getRecommendationsAsync(array $args = [])
 */
class PersonalizeRuntimeClient extends AwsClient {}
