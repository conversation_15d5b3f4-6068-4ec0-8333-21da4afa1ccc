<?php

namespace App\Traits\ProjectManagement;

use Illuminate\Database\Eloquent\Builder;

/**
 * Trait للبحث المتقدم في المشاريع والمهام
 * 
 * يوفر إمكانيات بحث متقدمة مع دعم البحث النصي
 * والفلترة المتقدمة والبحث في العلاقات
 * 
 * @package App\Traits\ProjectManagement
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
trait Searchable
{
    /**
     * الحقول القابلة للبحث (يجب تعريفها في النموذج)
     * 
     * @var array
     */
    // protected $searchable = ['name', 'description'];

    /**
     * الحصول على الحقول القابلة للبحث
     * 
     * @return array
     */
    public function getSearchableFields(): array
    {
        return $this->searchable ?? ['name'];
    }

    /**
     * scope للبحث النصي العام
     * 
     * @param Builder $query
     * @param string $term مصطلح البحث
     * @return Builder
     */
    public function scopeSearch(Builder $query, string $term): Builder
    {
        if (empty($term)) {
            return $query;
        }

        $searchableFields = $this->getSearchableFields();
        
        return $query->where(function ($q) use ($term, $searchableFields) {
            foreach ($searchableFields as $field) {
                $q->orWhere($field, 'LIKE', "%{$term}%");
            }
        });
    }

    /**
     * scope للبحث المتقدم مع خيارات متعددة
     * 
     * @param Builder $query
     * @param array $filters مرشحات البحث
     * @return Builder
     */
    public function scopeAdvancedSearch(Builder $query, array $filters): Builder
    {
        // البحث النصي
        if (!empty($filters['search'])) {
            $query->search($filters['search']);
        }

        // فلترة حسب الحالة
        if (!empty($filters['status'])) {
            if (is_array($filters['status'])) {
                $query->whereIn('status', $filters['status']);
            } else {
                $query->where('status', $filters['status']);
            }
        }

        // فلترة حسب الأولوية
        if (!empty($filters['priority'])) {
            if (is_array($filters['priority'])) {
                $query->whereIn('priority', $filters['priority']);
            } else {
                $query->where('priority', $filters['priority']);
            }
        }

        // فلترة حسب التاريخ
        if (!empty($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        // فلترة حسب المستخدم المنشئ
        if (!empty($filters['created_by'])) {
            $query->where('created_by', $filters['created_by']);
        }

        return $query;
    }

    /**
     * scope للبحث في حقل معين
     * 
     * @param Builder $query
     * @param string $field الحقل
     * @param string $term مصطلح البحث
     * @return Builder
     */
    public function scopeSearchInField(Builder $query, string $field, string $term): Builder
    {
        return $query->where($field, 'LIKE', "%{$term}%");
    }

    /**
     * scope للبحث الدقيق (exact match)
     * 
     * @param Builder $query
     * @param string $field الحقل
     * @param string $value القيمة
     * @return Builder
     */
    public function scopeExactSearch(Builder $query, string $field, string $value): Builder
    {
        return $query->where($field, $value);
    }

    /**
     * scope للبحث بالبداية
     * 
     * @param Builder $query
     * @param string $field الحقل
     * @param string $term مصطلح البحث
     * @return Builder
     */
    public function scopeStartsWith(Builder $query, string $field, string $term): Builder
    {
        return $query->where($field, 'LIKE', "{$term}%");
    }

    /**
     * scope للبحث بالنهاية
     * 
     * @param Builder $query
     * @param string $field الحقل
     * @param string $term مصطلح البحث
     * @return Builder
     */
    public function scopeEndsWith(Builder $query, string $field, string $term): Builder
    {
        return $query->where($field, 'LIKE', "%{$term}");
    }

    /**
     * scope للبحث في العلاقات
     * 
     * @param Builder $query
     * @param string $relation العلاقة
     * @param string $field الحقل في العلاقة
     * @param string $term مصطلح البحث
     * @return Builder
     */
    public function scopeSearchInRelation(Builder $query, string $relation, string $field, string $term): Builder
    {
        return $query->whereHas($relation, function ($q) use ($field, $term) {
            $q->where($field, 'LIKE', "%{$term}%");
        });
    }

    /**
     * scope للبحث في المستخدمين المرتبطين
     * 
     * @param Builder $query
     * @param string $term مصطلح البحث
     * @return Builder
     */
    public function scopeSearchByUser(Builder $query, string $term): Builder
    {
        return $query->where(function ($q) use ($term) {
            // البحث في المنشئ
            $q->whereHas('creator', function ($userQuery) use ($term) {
                $userQuery->where('name', 'LIKE', "%{$term}%")
                         ->orWhere('email', 'LIKE', "%{$term}%");
            });

            // البحث في المدير (للمشاريع)
            if (method_exists($this, 'manager')) {
                $q->orWhereHas('manager', function ($userQuery) use ($term) {
                    $userQuery->where('name', 'LIKE', "%{$term}%")
                             ->orWhere('email', 'LIKE', "%{$term}%");
                });
            }

            // البحث في المكلف (للمهام)
            if (method_exists($this, 'assignedUser')) {
                $q->orWhereHas('assignedUser', function ($userQuery) use ($term) {
                    $userQuery->where('name', 'LIKE', "%{$term}%")
                             ->orWhere('email', 'LIKE', "%{$term}%");
                });
            }
        });
    }

    /**
     * scope للبحث حسب النطاق الزمني
     * 
     * @param Builder $query
     * @param string $period النطاق (today, week, month, year)
     * @param string $field الحقل الزمني (افتراضي: created_at)
     * @return Builder
     */
    public function scopeSearchByPeriod(Builder $query, string $period, string $field = 'created_at'): Builder
    {
        switch ($period) {
            case 'today':
                return $query->whereDate($field, today());
                
            case 'yesterday':
                return $query->whereDate($field, today()->subDay());
                
            case 'week':
                return $query->whereBetween($field, [
                    now()->startOfWeek(),
                    now()->endOfWeek()
                ]);
                
            case 'last_week':
                return $query->whereBetween($field, [
                    now()->subWeek()->startOfWeek(),
                    now()->subWeek()->endOfWeek()
                ]);
                
            case 'month':
                return $query->whereMonth($field, now()->month)
                            ->whereYear($field, now()->year);
                
            case 'last_month':
                return $query->whereMonth($field, now()->subMonth()->month)
                            ->whereYear($field, now()->subMonth()->year);
                
            case 'year':
                return $query->whereYear($field, now()->year);
                
            case 'last_year':
                return $query->whereYear($field, now()->subYear()->year);
                
            default:
                return $query;
        }
    }

    /**
     * scope للبحث المتقدم مع ترتيب حسب الصلة
     * 
     * @param Builder $query
     * @param string $term مصطلح البحث
     * @return Builder
     */
    public function scopeSearchWithRelevance(Builder $query, string $term): Builder
    {
        if (empty($term)) {
            return $query;
        }

        $searchableFields = $this->getSearchableFields();
        $relevanceScore = '';
        $conditions = [];

        foreach ($searchableFields as $index => $field) {
            $weight = count($searchableFields) - $index; // وزن أعلى للحقول الأولى
            
            $conditions[] = "CASE WHEN {$field} LIKE '%{$term}%' THEN {$weight} ELSE 0 END";
        }

        $relevanceScore = implode(' + ', $conditions);

        return $query->selectRaw("*, ({$relevanceScore}) as relevance_score")
                    ->where(function ($q) use ($term, $searchableFields) {
                        foreach ($searchableFields as $field) {
                            $q->orWhere($field, 'LIKE', "%{$term}%");
                        }
                    })
                    ->orderByDesc('relevance_score');
    }

    /**
     * scope للبحث مع تمييز النص
     * 
     * @param Builder $query
     * @param string $term مصطلح البحث
     * @param string $field الحقل
     * @return Builder
     */
    public function scopeSearchWithHighlight(Builder $query, string $term, string $field): Builder
    {
        return $query->selectRaw("
            *, 
            REPLACE({$field}, '{$term}', '<mark>{$term}</mark>') as highlighted_{$field}
        ")->where($field, 'LIKE', "%{$term}%");
    }

    /**
     * scope للبحث الضبابي (fuzzy search)
     * 
     * @param Builder $query
     * @param string $term مصطلح البحث
     * @param int $distance المسافة المسموحة للاختلاف
     * @return Builder
     */
    public function scopeFuzzySearch(Builder $query, string $term, int $distance = 2): Builder
    {
        $searchableFields = $this->getSearchableFields();
        
        return $query->where(function ($q) use ($term, $searchableFields, $distance) {
            foreach ($searchableFields as $field) {
                // البحث الدقيق أولاً
                $q->orWhere($field, 'LIKE', "%{$term}%");
                
                // ثم البحث الضبابي
                $q->orWhereRaw("LEVENSHTEIN({$field}, ?) <= ?", [$term, $distance]);
            }
        });
    }

    /**
     * الحصول على نتائج البحث مع إحصائيات
     * 
     * @param string $term مصطلح البحث
     * @param array $filters مرشحات إضافية
     * @return array
     */
    public function searchWithStats(string $term, array $filters = []): array
    {
        $query = static::query();
        
        // تطبيق البحث والمرشحات
        $query->advancedSearch(array_merge($filters, ['search' => $term]));
        
        $total = $query->count();
        $results = $query->get();
        
        return [
            'term' => $term,
            'total' => $total,
            'results' => $results,
            'filters_applied' => $filters,
            'execution_time' => microtime(true) - LARAVEL_START
        ];
    }

    /**
     * الحصول على اقتراحات البحث
     * 
     * @param string $term مصطلح البحث الجزئي
     * @param int $limit عدد الاقتراحات
     * @return array
     */
    public function getSearchSuggestions(string $term, int $limit = 10): array
    {
        $searchableFields = $this->getSearchableFields();
        $suggestions = [];
        
        foreach ($searchableFields as $field) {
            $results = static::query()
                ->select($field)
                ->where($field, 'LIKE', "%{$term}%")
                ->distinct()
                ->limit($limit)
                ->pluck($field)
                ->toArray();
                
            $suggestions = array_merge($suggestions, $results);
        }
        
        return array_unique(array_slice($suggestions, 0, $limit));
    }
}
