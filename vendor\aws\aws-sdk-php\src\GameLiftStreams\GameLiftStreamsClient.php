<?php
namespace Aws\GameLiftStreams;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon GameLift Streams** service.
 * @method \Aws\Result addStreamGroupLocations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise addStreamGroupLocationsAsync(array $args = [])
 * @method \Aws\Result associateApplications(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateApplicationsAsync(array $args = [])
 * @method \Aws\Result createApplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createApplicationAsync(array $args = [])
 * @method \Aws\Result createStreamGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createStreamGroupAsync(array $args = [])
 * @method \Aws\Result createStreamSessionConnection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createStreamSessionConnectionAsync(array $args = [])
 * @method \Aws\Result deleteApplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteApplicationAsync(array $args = [])
 * @method \Aws\Result deleteStreamGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteStreamGroupAsync(array $args = [])
 * @method \Aws\Result disassociateApplications(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateApplicationsAsync(array $args = [])
 * @method \Aws\Result exportStreamSessionFiles(array $args = [])
 * @method \GuzzleHttp\Promise\Promise exportStreamSessionFilesAsync(array $args = [])
 * @method \Aws\Result getApplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getApplicationAsync(array $args = [])
 * @method \Aws\Result getStreamGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getStreamGroupAsync(array $args = [])
 * @method \Aws\Result getStreamSession(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getStreamSessionAsync(array $args = [])
 * @method \Aws\Result listApplications(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listApplicationsAsync(array $args = [])
 * @method \Aws\Result listStreamGroups(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listStreamGroupsAsync(array $args = [])
 * @method \Aws\Result listStreamSessions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listStreamSessionsAsync(array $args = [])
 * @method \Aws\Result listStreamSessionsByAccount(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listStreamSessionsByAccountAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result removeStreamGroupLocations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise removeStreamGroupLocationsAsync(array $args = [])
 * @method \Aws\Result startStreamSession(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startStreamSessionAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result terminateStreamSession(array $args = [])
 * @method \GuzzleHttp\Promise\Promise terminateStreamSessionAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateApplication(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateApplicationAsync(array $args = [])
 * @method \Aws\Result updateStreamGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateStreamGroupAsync(array $args = [])
 */
class GameLiftStreamsClient extends AwsClient {}
