<?php
// This file was auto-generated from sdk-root/src/data/observabilityadmin/2018-05-10/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-10', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'observabilityadmin', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'CloudWatch Observability Admin Service', 'serviceId' => 'ObservabilityAdmin', 'signatureVersion' => 'v4', 'signingName' => 'observabilityadmin', 'uid' => 'observabilityadmin-2018-05-10', ], 'operations' => [ 'GetTelemetryEvaluationStatus' => [ 'name' => 'GetTelemetryEvaluationStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetTelemetryEvaluationStatus', 'responseCode' => 200, ], 'output' => [ 'shape' => 'GetTelemetryEvaluationStatusOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetTelemetryEvaluationStatusForOrganization' => [ 'name' => 'GetTelemetryEvaluationStatusForOrganization', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetTelemetryEvaluationStatusForOrganization', 'responseCode' => 200, ], 'output' => [ 'shape' => 'GetTelemetryEvaluationStatusForOrganizationOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListResourceTelemetry' => [ 'name' => 'ListResourceTelemetry', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListResourceTelemetry', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListResourceTelemetryInput', ], 'output' => [ 'shape' => 'ListResourceTelemetryOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListResourceTelemetryForOrganization' => [ 'name' => 'ListResourceTelemetryForOrganization', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListResourceTelemetryForOrganization', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListResourceTelemetryForOrganizationInput', ], 'output' => [ 'shape' => 'ListResourceTelemetryForOrganizationOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'StartTelemetryEvaluation' => [ 'name' => 'StartTelemetryEvaluation', 'http' => [ 'method' => 'POST', 'requestUri' => '/StartTelemetryEvaluation', 'responseCode' => 200, ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'StartTelemetryEvaluationForOrganization' => [ 'name' => 'StartTelemetryEvaluationForOrganization', 'http' => [ 'method' => 'POST', 'requestUri' => '/StartTelemetryEvaluationForOrganization', 'responseCode' => 200, ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'StopTelemetryEvaluation' => [ 'name' => 'StopTelemetryEvaluation', 'http' => [ 'method' => 'POST', 'requestUri' => '/StopTelemetryEvaluation', 'responseCode' => 200, ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'StopTelemetryEvaluationForOrganization' => [ 'name' => 'StopTelemetryEvaluationForOrganization', 'http' => [ 'method' => 'POST', 'requestUri' => '/StopTelemetryEvaluationForOrganization', 'responseCode' => 200, ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'amznErrorType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amzn-ErrorType', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'AccountIdentifier' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '[0-9]{12}', ], 'AccountIdentifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountIdentifier', ], 'max' => 10, 'min' => 0, ], 'FailureReason' => [ 'type' => 'string', ], 'GetTelemetryEvaluationStatusForOrganizationOutput' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'Status', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], ], ], 'GetTelemetryEvaluationStatusOutput' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'Status', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'amznErrorType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amzn-ErrorType', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ListResourceTelemetryForOrganizationInput' => [ 'type' => 'structure', 'members' => [ 'AccountIdentifiers' => [ 'shape' => 'AccountIdentifiers', ], 'ResourceIdentifierPrefix' => [ 'shape' => 'ResourceIdentifierPrefix', ], 'ResourceTypes' => [ 'shape' => 'ResourceTypes', ], 'TelemetryConfigurationState' => [ 'shape' => 'TelemetryConfigurationState', ], 'ResourceTags' => [ 'shape' => 'TagMapInput', ], 'MaxResults' => [ 'shape' => 'ListResourceTelemetryForOrganizationMaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListResourceTelemetryForOrganizationMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'ListResourceTelemetryForOrganizationOutput' => [ 'type' => 'structure', 'members' => [ 'TelemetryConfigurations' => [ 'shape' => 'TelemetryConfigurations', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListResourceTelemetryInput' => [ 'type' => 'structure', 'members' => [ 'ResourceIdentifierPrefix' => [ 'shape' => 'ResourceIdentifierPrefix', ], 'ResourceTypes' => [ 'shape' => 'ResourceTypes', ], 'TelemetryConfigurationState' => [ 'shape' => 'TelemetryConfigurationState', ], 'ResourceTags' => [ 'shape' => 'TagMapInput', ], 'MaxResults' => [ 'shape' => 'ListResourceTelemetryMaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListResourceTelemetryMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'ListResourceTelemetryOutput' => [ 'type' => 'structure', 'members' => [ 'TelemetryConfigurations' => [ 'shape' => 'TelemetryConfigurations', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'NextToken' => [ 'type' => 'string', ], 'ResourceIdentifier' => [ 'type' => 'string', ], 'ResourceIdentifierPrefix' => [ 'type' => 'string', 'max' => 768, 'min' => 3, ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'AWS::EC2::Instance', 'AWS::EC2::VPC', 'AWS::Lambda::Function', ], ], 'ResourceTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceType', ], 'max' => 5, 'min' => 1, ], 'Status' => [ 'type' => 'string', 'enum' => [ 'NOT_STARTED', 'STARTING', 'FAILED_START', 'RUNNING', 'STOPPING', 'FAILED_STOP', 'STOPPED', ], ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)', ], 'TagMapInput' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 0, ], 'TagMapOutput' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)', ], 'TelemetryConfiguration' => [ 'type' => 'structure', 'members' => [ 'AccountIdentifier' => [ 'shape' => 'AccountIdentifier', ], 'TelemetryConfigurationState' => [ 'shape' => 'TelemetryConfigurationState', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'ResourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'ResourceTags' => [ 'shape' => 'TagMapOutput', ], 'LastUpdateTimeStamp' => [ 'shape' => 'Long', ], ], ], 'TelemetryConfigurationState' => [ 'type' => 'map', 'key' => [ 'shape' => 'TelemetryType', ], 'value' => [ 'shape' => 'TelemetryState', ], ], 'TelemetryConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'TelemetryConfiguration', ], ], 'TelemetryState' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', 'NotApplicable', ], ], 'TelemetryType' => [ 'type' => 'string', 'enum' => [ 'Logs', 'Metrics', 'Traces', ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], ],];
