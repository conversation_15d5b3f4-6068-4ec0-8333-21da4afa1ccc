<?php
// This file was auto-generated from sdk-root/src/data/iot-jobs-data/2017-09-29/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-09-29', 'endpointPrefix' => 'data.jobs.iot', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS IoT Jobs Data Plane', 'serviceId' => 'IoT Jobs Data Plane', 'signatureVersion' => 'v4', 'signingName' => 'iot-jobs-data', 'uid' => 'iot-jobs-data-2017-09-29', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'DescribeJobExecution' => [ 'name' => 'DescribeJobExecution', 'http' => [ 'method' => 'GET', 'requestUri' => '/things/{thingName}/jobs/{jobId}', ], 'input' => [ 'shape' => 'DescribeJobExecutionRequest', ], 'output' => [ 'shape' => 'DescribeJobExecutionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'CertificateValidationException', ], [ 'shape' => 'TerminalStateException', ], ], ], 'GetPendingJobExecutions' => [ 'name' => 'GetPendingJobExecutions', 'http' => [ 'method' => 'GET', 'requestUri' => '/things/{thingName}/jobs', ], 'input' => [ 'shape' => 'GetPendingJobExecutionsRequest', ], 'output' => [ 'shape' => 'GetPendingJobExecutionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'CertificateValidationException', ], ], ], 'StartCommandExecution' => [ 'name' => 'StartCommandExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/command-executions', ], 'input' => [ 'shape' => 'StartCommandExecutionRequest', ], 'output' => [ 'shape' => 'StartCommandExecutionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartNextPendingJobExecution' => [ 'name' => 'StartNextPendingJobExecution', 'http' => [ 'method' => 'PUT', 'requestUri' => '/things/{thingName}/jobs/$next', ], 'input' => [ 'shape' => 'StartNextPendingJobExecutionRequest', ], 'output' => [ 'shape' => 'StartNextPendingJobExecutionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'CertificateValidationException', ], ], ], 'UpdateJobExecution' => [ 'name' => 'UpdateJobExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/things/{thingName}/jobs/{jobId}', ], 'input' => [ 'shape' => 'UpdateJobExecutionRequest', ], 'output' => [ 'shape' => 'UpdateJobExecutionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'CertificateValidationException', ], [ 'shape' => 'InvalidStateTransitionException', ], ], ], ], 'shapes' => [ 'ApproximateSecondsBeforeTimedOut' => [ 'type' => 'long', ], 'BinaryBlob' => [ 'type' => 'blob', ], 'BinaryParameterValue' => [ 'type' => 'blob', 'min' => 1, ], 'BooleanParameterValue' => [ 'type' => 'boolean', ], 'CertificateValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ClientRequestTokenV2' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[\\x21-\\x7E]+$', ], 'CommandArn' => [ 'type' => 'string', ], 'CommandExecutionId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'CommandExecutionParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'CommandParameterName', ], 'value' => [ 'shape' => 'CommandParameterValue', ], 'min' => 1, ], 'CommandExecutionTimeoutInSeconds' => [ 'type' => 'long', 'min' => 1, ], 'CommandParameterName' => [ 'type' => 'string', 'max' => 192, 'min' => 1, 'pattern' => '^[.$a-zA-Z0-9_-]+$', ], 'CommandParameterValue' => [ 'type' => 'structure', 'members' => [ 'S' => [ 'shape' => 'StringParameterValue', ], 'B' => [ 'shape' => 'BooleanParameterValue', ], 'I' => [ 'shape' => 'IntegerParameterValue', ], 'L' => [ 'shape' => 'LongParameterValue', ], 'D' => [ 'shape' => 'DoubleParameterValue', ], 'BIN' => [ 'shape' => 'BinaryParameterValue', ], 'UL' => [ 'shape' => 'UnsignedLongParameterValue', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], 'resourceId' => [ 'shape' => 'resourceId', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'DescribeJobExecutionJobId' => [ 'type' => 'string', 'pattern' => '[a-zA-Z0-9_-]+|^\\$next', ], 'DescribeJobExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', 'thingName', ], 'members' => [ 'jobId' => [ 'shape' => 'DescribeJobExecutionJobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], 'includeJobDocument' => [ 'shape' => 'IncludeJobDocument', 'location' => 'querystring', 'locationName' => 'includeJobDocument', ], 'executionNumber' => [ 'shape' => 'ExecutionNumber', 'location' => 'querystring', 'locationName' => 'executionNumber', ], ], ], 'DescribeJobExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'execution' => [ 'shape' => 'JobExecution', ], ], ], 'DetailsKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9:_-]+', ], 'DetailsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'DetailsKey', ], 'value' => [ 'shape' => 'DetailsValue', ], ], 'DetailsValue' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[^\\p{C}]+', ], 'DoubleParameterValue' => [ 'type' => 'double', ], 'ExecutionNumber' => [ 'type' => 'long', ], 'ExpectedVersion' => [ 'type' => 'long', ], 'GetPendingJobExecutionsRequest' => [ 'type' => 'structure', 'required' => [ 'thingName', ], 'members' => [ 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], ], ], 'GetPendingJobExecutionsResponse' => [ 'type' => 'structure', 'members' => [ 'inProgressJobs' => [ 'shape' => 'JobExecutionSummaryList', ], 'queuedJobs' => [ 'shape' => 'JobExecutionSummaryList', ], ], ], 'IncludeExecutionState' => [ 'type' => 'boolean', ], 'IncludeJobDocument' => [ 'type' => 'boolean', ], 'IntegerParameterValue' => [ 'type' => 'integer', ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidStateTransitionException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'JobDocument' => [ 'type' => 'string', 'max' => 32768, ], 'JobExecution' => [ 'type' => 'structure', 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'thingName' => [ 'shape' => 'ThingName', ], 'status' => [ 'shape' => 'JobExecutionStatus', ], 'statusDetails' => [ 'shape' => 'DetailsMap', ], 'queuedAt' => [ 'shape' => 'QueuedAt', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'lastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], 'approximateSecondsBeforeTimedOut' => [ 'shape' => 'ApproximateSecondsBeforeTimedOut', ], 'versionNumber' => [ 'shape' => 'VersionNumber', ], 'executionNumber' => [ 'shape' => 'ExecutionNumber', ], 'jobDocument' => [ 'shape' => 'JobDocument', ], ], ], 'JobExecutionState' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'JobExecutionStatus', ], 'statusDetails' => [ 'shape' => 'DetailsMap', ], 'versionNumber' => [ 'shape' => 'VersionNumber', ], ], ], 'JobExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'QUEUED', 'IN_PROGRESS', 'SUCCEEDED', 'FAILED', 'TIMED_OUT', 'REJECTED', 'REMOVED', 'CANCELED', ], ], 'JobExecutionSummary' => [ 'type' => 'structure', 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'queuedAt' => [ 'shape' => 'QueuedAt', ], 'startedAt' => [ 'shape' => 'StartedAt', ], 'lastUpdatedAt' => [ 'shape' => 'LastUpdatedAt', ], 'versionNumber' => [ 'shape' => 'VersionNumber', ], 'executionNumber' => [ 'shape' => 'ExecutionNumber', ], ], ], 'JobExecutionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobExecutionSummary', ], ], 'JobId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'LastUpdatedAt' => [ 'type' => 'long', ], 'LongParameterValue' => [ 'type' => 'long', ], 'QueuedAt' => [ 'type' => 'long', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 402, ], 'exception' => true, ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], 'StartCommandExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'targetArn', 'commandArn', ], 'members' => [ 'targetArn' => [ 'shape' => 'TargetArn', ], 'commandArn' => [ 'shape' => 'CommandArn', ], 'parameters' => [ 'shape' => 'CommandExecutionParameterMap', ], 'executionTimeoutSeconds' => [ 'shape' => 'CommandExecutionTimeoutInSeconds', ], 'clientToken' => [ 'shape' => 'ClientRequestTokenV2', 'idempotencyToken' => true, ], ], ], 'StartCommandExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'executionId' => [ 'shape' => 'CommandExecutionId', ], ], ], 'StartNextPendingJobExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'thingName', ], 'members' => [ 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], 'statusDetails' => [ 'shape' => 'DetailsMap', ], 'stepTimeoutInMinutes' => [ 'shape' => 'StepTimeoutInMinutes', ], ], ], 'StartNextPendingJobExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'execution' => [ 'shape' => 'JobExecution', ], ], ], 'StartedAt' => [ 'type' => 'long', ], 'StepTimeoutInMinutes' => [ 'type' => 'long', ], 'StringParameterValue' => [ 'type' => 'string', 'min' => 1, ], 'TargetArn' => [ 'type' => 'string', 'max' => 2048, ], 'TerminalStateException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 410, ], 'exception' => true, ], 'ThingName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9:_-]+', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], 'payload' => [ 'shape' => 'BinaryBlob', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'UnsignedLongParameterValue' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '^[0-9]*$', ], 'UpdateJobExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', 'thingName', 'status', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], 'status' => [ 'shape' => 'JobExecutionStatus', ], 'statusDetails' => [ 'shape' => 'DetailsMap', ], 'stepTimeoutInMinutes' => [ 'shape' => 'StepTimeoutInMinutes', ], 'expectedVersion' => [ 'shape' => 'ExpectedVersion', ], 'includeJobExecutionState' => [ 'shape' => 'IncludeExecutionState', ], 'includeJobDocument' => [ 'shape' => 'IncludeJobDocument', ], 'executionNumber' => [ 'shape' => 'ExecutionNumber', ], ], ], 'UpdateJobExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'executionState' => [ 'shape' => 'JobExecutionState', ], 'jobDocument' => [ 'shape' => 'JobDocument', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'VersionNumber' => [ 'type' => 'long', ], 'errorMessage' => [ 'type' => 'string', ], 'resourceId' => [ 'type' => 'string', ], ],];
