<?php

namespace Spatie\LaravelIgnition\Solutions\SolutionProviders;

use Spatie\Ignition\Contracts\HasSolutionsForThrowable;
use Spatie\ErrorSolutions\SolutionProviders\Laravel\LazyLoadingViolationSolutionProvider as BaseLazyLoadingViolationSolutionProviderAlias;

class LazyLoadingViolationSolutionProvider extends BaseLazyLoadingViolationSolutionProviderAlias implements HasSolutionsForThrowable
{

}
