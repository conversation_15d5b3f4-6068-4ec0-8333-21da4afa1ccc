<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Messaging\V1;

use Twilio\Options;
use Twilio\Values;

abstract class ExternalCampaignOptions
{
    /**
     * @param bool $cnpMigration Customers should use this flag during the ERC registration process to indicate to <PERSON><PERSON><PERSON> that the campaign being registered is undergoing CNP migration. It is important for the user to first trigger the CNP migration process for said campaign in their CSP portal and have <PERSON><PERSON><PERSON> accept the sharing request, before making this api call.
     * @return CreateExternalCampaignOptions Options builder
     */
    public static function create(
        
        bool $cnpMigration = Values::BOOL_NONE

    ): CreateExternalCampaignOptions
    {
        return new CreateExternalCampaignOptions(
            $cnpMigration
        );
    }

}

class CreateExternalCampaignOptions extends Options
    {
    /**
     * @param bool $cnpMigration Customers should use this flag during the ERC registration process to indicate to Twilio that the campaign being registered is undergoing CNP migration. It is important for the user to first trigger the CNP migration process for said campaign in their CSP portal and have Twilio accept the sharing request, before making this api call.
     */
    public function __construct(
        
        bool $cnpMigration = Values::BOOL_NONE

    ) {
        $this->options['cnpMigration'] = $cnpMigration;
    }

    /**
     * Customers should use this flag during the ERC registration process to indicate to Twilio that the campaign being registered is undergoing CNP migration. It is important for the user to first trigger the CNP migration process for said campaign in their CSP portal and have Twilio accept the sharing request, before making this api call.
     *
     * @param bool $cnpMigration Customers should use this flag during the ERC registration process to indicate to Twilio that the campaign being registered is undergoing CNP migration. It is important for the user to first trigger the CNP migration process for said campaign in their CSP portal and have Twilio accept the sharing request, before making this api call.
     * @return $this Fluent Builder
     */
    public function setCnpMigration(bool $cnpMigration): self
    {
        $this->options['cnpMigration'] = $cnpMigration;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Messaging.V1.CreateExternalCampaignOptions ' . $options . ']';
    }
}

