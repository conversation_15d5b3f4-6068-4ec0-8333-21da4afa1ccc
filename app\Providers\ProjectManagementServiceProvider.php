<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\View;
use App\Services\ProjectManagement\ProjectService;
use App\Services\ProjectManagement\TaskService;
use App\Services\ProjectManagement\TeamService;
use App\Services\ProjectManagement\ReportService;
use App\Services\ProjectManagement\NotificationService;
use App\Repositories\ProjectManagement\ProjectRepository;
use App\Repositories\ProjectManagement\TaskRepository;
use App\Repositories\ProjectManagement\TeamRepository;
use App\Repositories\ProjectManagement\TimeTrackingRepository;

/**
 * مزود خدمات نظام إدارة المشاريع المتقدم
 * 
 * هذا المزود مسؤول عن تسجيل جميع الخدمات والمستودعات والسياسات
 * الخاصة بنظام إدارة المشاريع في حاوي الحقن التابعي
 * 
 * @package App\Providers
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
class ProjectManagementServiceProvider extends ServiceProvider
{
    /**
     * تسجيل خدمات النظام في حاوي الحقن التابعي
     * 
     * يتم استدعاء هذه الطريقة أثناء مرحلة التسجيل لتحديد
     * كيفية إنشاء وحقن التبعيات المختلفة
     * 
     * @return void
     */
    public function register(): void
    {
        // تسجيل ملف التكوين الخاص بنظام إدارة المشاريع
        $this->mergeConfigFrom(
            __DIR__.'/../../config/project_management.php',
            'project_management'
        );

        // تسجيل المستودعات (Repositories) كخدمات مفردة
        $this->registerRepositories();

        // تسجيل الخدمات (Services) مع حقن التبعيات المطلوبة
        $this->registerServices();

        // تسجيل الواجهات (Interfaces) مع تنفيذاتها الملموسة
        $this->registerInterfaces();
    }

    /**
     * تشغيل خدمات النظام بعد تحميل جميع المزودين
     * 
     * يتم استدعاء هذه الطريقة بعد تسجيل جميع المزودين
     * لتنفيذ المهام التي تتطلب وجود جميع الخدمات
     * 
     * @return void
     */
    public function boot(): void
    {
        // نشر ملفات التكوين والموارد
        $this->publishResources();

        // تسجيل السياسات الأمنية
        $this->registerPolicies();

        // تسجيل مشاركة البيانات مع العروض
        $this->registerViewComposers();

        // تسجيل الأوامر المخصصة
        $this->registerCommands();

        // تسجيل مستمعي الأحداث
        $this->registerEventListeners();
    }

    /**
     * تسجيل المستودعات في حاوي الحقن التابعي
     * 
     * المستودعات مسؤولة عن التفاعل المباشر مع قاعدة البيانات
     * وتطبيق نمط Repository Pattern لفصل منطق الوصول للبيانات
     * 
     * @return void
     */
    protected function registerRepositories(): void
    {
        // مستودع المشاريع - يدير جميع عمليات CRUD للمشاريع
        $this->app->singleton(ProjectRepository::class, function ($app) {
            return new ProjectRepository();
        });

        // مستودع المهام - يدير المهام وعلاقاتها بالمشاريع
        $this->app->singleton(TaskRepository::class, function ($app) {
            return new TaskRepository();
        });

        // مستودع الفرق - يدير أعضاء الفريق والأدوار
        $this->app->singleton(TeamRepository::class, function ($app) {
            return new TeamRepository();
        });

        // مستودع تتبع الوقت - يدير سجلات الوقت المستغرق
        $this->app->singleton(TimeTrackingRepository::class, function ($app) {
            return new TimeTrackingRepository();
        });
    }

    /**
     * تسجيل الخدمات مع حقن التبعيات المطلوبة
     * 
     * الخدمات تحتوي على منطق العمل الأساسي وتستخدم
     * المستودعات للوصول إلى البيانات
     * 
     * @return void
     */
    protected function registerServices(): void
    {
        // خدمة إدارة المشاريع - تحتوي على منطق العمل الأساسي للمشاريع
        $this->app->singleton(ProjectService::class, function ($app) {
            return new ProjectService(
                $app->make(ProjectRepository::class),
                $app->make(TaskRepository::class),
                $app->make(NotificationService::class)
            );
        });

        // خدمة إدارة المهام - تدير دورة حياة المهام
        $this->app->singleton(TaskService::class, function ($app) {
            return new TaskService(
                $app->make(TaskRepository::class),
                $app->make(TimeTrackingRepository::class),
                $app->make(NotificationService::class)
            );
        });

        // خدمة إدارة الفرق - تدير أعضاء الفريق والصلاحيات
        $this->app->singleton(TeamService::class, function ($app) {
            return new TeamService(
                $app->make(TeamRepository::class),
                $app->make(NotificationService::class)
            );
        });

        // خدمة التقارير - تولد التقارير والتحليلات المختلفة
        $this->app->singleton(ReportService::class, function ($app) {
            return new ReportService(
                $app->make(ProjectRepository::class),
                $app->make(TaskRepository::class),
                $app->make(TimeTrackingRepository::class)
            );
        });

        // خدمة الإشعارات - تدير إرسال الإشعارات المختلفة
        $this->app->singleton(NotificationService::class, function ($app) {
            return new NotificationService();
        });
    }

    /**
     * تسجيل الواجهات مع تنفيذاتها الملموسة
     * 
     * هذا يسمح بتطبيق مبدأ Dependency Inversion Principle
     * ويجعل النظام أكثر مرونة وقابلية للاختبار
     * 
     * @return void
     */
    protected function registerInterfaces(): void
    {
        // يمكن إضافة تسجيل الواجهات هنا عند الحاجة
        // مثال:
        // $this->app->bind(ProjectServiceInterface::class, ProjectService::class);
    }

    /**
     * نشر ملفات التكوين والموارد
     * 
     * يسمح للمطورين بتخصيص ملفات التكوين والعروض
     * حسب احتياجاتهم الخاصة
     * 
     * @return void
     */
    protected function publishResources(): void
    {
        // نشر ملف التكوين
        $this->publishes([
            __DIR__.'/../../config/project_management.php' => config_path('project_management.php'),
        ], 'project-management-config');

        // نشر ملفات العروض (Views)
        $this->publishes([
            __DIR__.'/../../resources/views/project-management' => resource_path('views/project-management'),
        ], 'project-management-views');

        // نشر الأصول (Assets)
        $this->publishes([
            __DIR__.'/../../public/assets/project-management' => public_path('assets/project-management'),
        ], 'project-management-assets');
    }

    /**
     * تسجيل السياسات الأمنية
     * 
     * تحديد من يمكنه الوصول إلى الموارد المختلفة
     * وتنفيذ العمليات المختلفة
     * 
     * @return void
     */
    protected function registerPolicies(): void
    {
        // سياسة المشاريع
        Gate::define('view-projects', function ($user) {
            return $user->hasPermissionTo('view projects');
        });

        Gate::define('create-projects', function ($user) {
            return $user->hasPermissionTo('create projects');
        });

        Gate::define('edit-projects', function ($user) {
            return $user->hasPermissionTo('edit projects');
        });

        Gate::define('delete-projects', function ($user) {
            return $user->hasPermissionTo('delete projects');
        });

        // سياسة المهام
        Gate::define('view-tasks', function ($user) {
            return $user->hasPermissionTo('view tasks');
        });

        Gate::define('create-tasks', function ($user) {
            return $user->hasPermissionTo('create tasks');
        });

        Gate::define('edit-tasks', function ($user) {
            return $user->hasPermissionTo('edit tasks');
        });

        Gate::define('delete-tasks', function ($user) {
            return $user->hasPermissionTo('delete tasks');
        });
    }

    /**
     * تسجيل مشاركة البيانات مع العروض
     * 
     * مشاركة البيانات الشائعة مع جميع عروض نظام إدارة المشاريع
     * 
     * @return void
     */
    protected function registerViewComposers(): void
    {
        // مشاركة إعدادات النظام مع جميع العروض
        View::composer('project-management.*', function ($view) {
            $view->with([
                'projectStatuses' => config('project_management.projects.statuses'),
                'taskStatuses' => config('project_management.tasks.statuses'),
                'priorities' => config('project_management.projects.priorities'),
                'teamRoles' => config('project_management.team.roles'),
            ]);
        });
    }

    /**
     * تسجيل الأوامر المخصصة
     * 
     * تسجيل أوامر Artisan المخصصة لنظام إدارة المشاريع
     * 
     * @return void
     */
    protected function registerCommands(): void
    {
        if ($this->app->runningInConsole()) {
            $this->commands([
                // يمكن إضافة الأوامر المخصصة هنا
                // \App\Console\Commands\ProjectManagement\GenerateReportCommand::class,
                // \App\Console\Commands\ProjectManagement\CleanupOldDataCommand::class,
            ]);
        }
    }

    /**
     * تسجيل مستمعي الأحداث
     * 
     * ربط الأحداث بمستمعيها لتنفيذ المهام التلقائية
     * 
     * @return void
     */
    protected function registerEventListeners(): void
    {
        // يمكن إضافة مستمعي الأحداث هنا
        // Event::listen(ProjectCreated::class, SendProjectNotification::class);
        // Event::listen(TaskCompleted::class, UpdateProjectProgress::class);
    }

    /**
     * الحصول على الخدمات المقدمة من هذا المزود
     * 
     * @return array
     */
    public function provides(): array
    {
        return [
            ProjectService::class,
            TaskService::class,
            TeamService::class,
            ReportService::class,
            NotificationService::class,
            ProjectRepository::class,
            TaskRepository::class,
            TeamRepository::class,
            TimeTrackingRepository::class,
        ];
    }
}
