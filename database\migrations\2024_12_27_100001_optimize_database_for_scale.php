<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

/**
 * تحسين قاعدة البيانات للتعامل مع مليون تسجيل
 * 
 * هذا الـ Migration يضيف تحسينات متقدمة للأداء والفهرسة
 * لضمان الأداء الأمثل مع قواعد البيانات الكبيرة
 * 
 * @package Database\Migrations
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
return new class extends Migration
{
    /**
     * تشغيل الـ Migration
     * 
     * @return void
     */
    public function up(): void
    {
        // تحسين جدول المشاريع
        $this->optimizeProjectsTable();
        
        // تحسين جدول المهام
        $this->optimizeTasksTable();
        
        // تحسين جدول سجلات الوقت
        $this->optimizeTimeEntriesTable();
        
        // تحسين جدول التعليقات
        $this->optimizeCommentsTable();
        
        // تحسين جدول الملفات
        $this->optimizeFilesTable();
        
        // إضافة فهارس مركبة متقدمة
        $this->addAdvancedIndexes();
        
        // تحسين إعدادات قاعدة البيانات
        $this->optimizeDatabaseSettings();
        
        // إنشاء جداول التقسيم للبيانات الكبيرة
        $this->createPartitionedTables();
    }

    /**
     * تحسين جدول المشاريع
     * 
     * @return void
     */
    protected function optimizeProjectsTable(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            // فهارس محسنة للبحث السريع
            $table->index(['status', 'priority', 'created_at'], 'idx_projects_status_priority_date');
            $table->index(['client_id', 'status', 'end_date'], 'idx_projects_client_status_end');
            $table->index(['manager_id', 'status', 'priority'], 'idx_projects_manager_status_priority');
            
            // فهرس للمشاريع النشطة مع التواريخ
            $table->index(['status', 'start_date', 'end_date'], 'idx_projects_active_dates');
            
            // فهرس للميزانية والتكلفة
            $table->index(['budget', 'spent_budget'], 'idx_projects_budget_spent');
            
            // فهرس مركب للتقارير
            $table->index(['type', 'status', 'created_at'], 'idx_projects_type_status_date');
        });

        // إضافة فهرس جزئي للمشاريع النشطة فقط
        DB::statement("
            CREATE INDEX idx_projects_active_only 
            ON projects (id, name, progress_percentage) 
            WHERE status IN ('planning', 'in_progress', 'testing')
        ");
    }

    /**
     * تحسين جدول المهام
     * 
     * @return void
     */
    protected function optimizeTasksTable(): void
    {
        Schema::table('tasks', function (Blueprint $table) {
            // فهارس محسنة للاستعلامات الشائعة
            $table->index(['project_id', 'status', 'priority', 'due_date'], 'idx_tasks_project_status_priority_due');
            $table->index(['assigned_to', 'status', 'due_date'], 'idx_tasks_assigned_status_due');
            $table->index(['parent_task_id', 'status', 'order_index'], 'idx_tasks_parent_status_order');
            
            // فهرس للمهام المتأخرة
            $table->index(['due_date', 'status'], 'idx_tasks_overdue');
            
            // فهرس للمهام حسب النوع والحالة
            $table->index(['type', 'status', 'created_at'], 'idx_tasks_type_status_date');
            
            // فهرس للساعات والتقديرات
            $table->index(['estimated_hours', 'actual_hours'], 'idx_tasks_hours');
        });

        // فهرس جزئي للمهام النشطة
        DB::statement("
            CREATE INDEX idx_tasks_active_only 
            ON tasks (id, title, assigned_to, due_date) 
            WHERE status IN ('todo', 'in_progress', 'review')
        ");

        // فهرس للمهام المتأخرة فقط
        DB::statement("
            CREATE INDEX idx_tasks_overdue_only 
            ON tasks (id, title, assigned_to, due_date, project_id) 
            WHERE due_date < CURRENT_DATE AND status != 'completed'
        ");
    }

    /**
     * تحسين جدول سجلات الوقت
     * 
     * @return void
     */
    protected function optimizeTimeEntriesTable(): void
    {
        Schema::table('time_entries', function (Blueprint $table) {
            // فهارس محسنة للتقارير الزمنية
            $table->index(['date', 'user_id', 'project_id'], 'idx_time_date_user_project');
            $table->index(['project_id', 'date', 'is_billable'], 'idx_time_project_date_billable');
            $table->index(['user_id', 'date', 'hours'], 'idx_time_user_date_hours');
            
            // فهرس للفوترة
            $table->index(['is_billable', 'invoice_id', 'total_cost'], 'idx_time_billing');
            
            // فهرس للتقارير الشهرية
            $table->index([DB::raw('YEAR(date)'), DB::raw('MONTH(date)'), 'user_id'], 'idx_time_monthly');
        });

        // فهرس جزئي للسجلات غير المفوترة
        DB::statement("
            CREATE INDEX idx_time_unbilled_only 
            ON time_entries (id, user_id, project_id, hours, total_cost) 
            WHERE is_billable = 1 AND invoice_id IS NULL
        ");
    }

    /**
     * تحسين جدول التعليقات
     * 
     * @return void
     */
    protected function optimizeCommentsTable(): void
    {
        Schema::table('comments', function (Blueprint $table) {
            // فهرس محسن للتعليقات حسب النوع والكائن
            $table->index(['commentable_type', 'commentable_id', 'created_at'], 'idx_comments_polymorphic_date');
            $table->index(['user_id', 'commentable_type', 'created_at'], 'idx_comments_user_type_date');
            
            // فهرس للتعليقات المثبتة والخاصة
            $table->index(['is_pinned', 'is_private', 'created_at'], 'idx_comments_flags_date');
            
            // فهرس للردود
            $table->index(['parent_id', 'created_at'], 'idx_comments_replies');
        });
    }

    /**
     * تحسين جدول الملفات
     * 
     * @return void
     */
    protected function optimizeFilesTable(): void
    {
        Schema::table('project_files', function (Blueprint $table) {
            // فهارس محسنة للملفات
            $table->index(['fileable_type', 'fileable_id', 'category'], 'idx_files_polymorphic_category');
            $table->index(['uploaded_by', 'created_at', 'size'], 'idx_files_uploader_date_size');
            $table->index(['mime_type', 'category', 'is_public'], 'idx_files_type_category_public');
            
            // فهرس للملفات الكبيرة
            $table->index(['size', 'created_at'], 'idx_files_size_date');
        });
    }

    /**
     * إضافة فهارس مركبة متقدمة
     * 
     * @return void
     */
    protected function addAdvancedIndexes(): void
    {
        // فهرس مركب للمشاريع والمهام
        DB::statement("
            CREATE INDEX idx_project_task_summary 
            ON tasks (project_id, status, COUNT(*)) 
        ");

        // فهرس للإحصائيات السريعة
        DB::statement("
            CREATE INDEX idx_project_stats 
            ON projects (status, type, priority, created_at, progress_percentage)
        ");

        // فهرس للبحث النصي المحسن
        DB::statement("
            ALTER TABLE projects 
            ADD FULLTEXT(name, description) 
            WITH PARSER ngram
        ");

        DB::statement("
            ALTER TABLE tasks 
            ADD FULLTEXT(title, description) 
            WITH PARSER ngram
        ");
    }

    /**
     * تحسين إعدادات قاعدة البيانات
     * 
     * @return void
     */
    protected function optimizeDatabaseSettings(): void
    {
        // تحسين إعدادات MySQL للأداء العالي
        $optimizations = [
            "SET GLOBAL innodb_buffer_pool_size = 1073741824", // 1GB
            "SET GLOBAL innodb_log_file_size = 268435456",     // 256MB
            "SET GLOBAL innodb_flush_log_at_trx_commit = 2",
            "SET GLOBAL innodb_file_per_table = 1",
            "SET GLOBAL query_cache_size = 134217728",         // 128MB
            "SET GLOBAL query_cache_type = 1",
            "SET GLOBAL tmp_table_size = 134217728",           // 128MB
            "SET GLOBAL max_heap_table_size = 134217728",      // 128MB
        ];

        foreach ($optimizations as $sql) {
            try {
                DB::statement($sql);
            } catch (Exception $e) {
                // تجاهل الأخطاء في حالة عدم وجود صلاحيات
                Log::warning("تعذر تطبيق تحسين قاعدة البيانات: " . $e->getMessage());
            }
        }
    }

    /**
     * إنشاء جداول التقسيم للبيانات الكبيرة
     * 
     * @return void
     */
    protected function createPartitionedTables(): void
    {
        // تقسيم جدول سجلات الوقت حسب التاريخ
        DB::statement("
            ALTER TABLE time_entries 
            PARTITION BY RANGE (YEAR(date)) (
                PARTITION p2024 VALUES LESS THAN (2025),
                PARTITION p2025 VALUES LESS THAN (2026),
                PARTITION p2026 VALUES LESS THAN (2027),
                PARTITION p2027 VALUES LESS THAN (2028),
                PARTITION p_future VALUES LESS THAN MAXVALUE
            )
        ");

        // تقسيم جدول سجل الأنشطة حسب التاريخ
        DB::statement("
            ALTER TABLE activity_logs 
            PARTITION BY RANGE (YEAR(created_at)) (
                PARTITION p2024 VALUES LESS THAN (2025),
                PARTITION p2025 VALUES LESS THAN (2026),
                PARTITION p2026 VALUES LESS THAN (2027),
                PARTITION p2027 VALUES LESS THAN (2028),
                PARTITION p_future VALUES LESS THAN MAXVALUE
            )
        ");
    }

    /**
     * التراجع عن الـ Migration
     * 
     * @return void
     */
    public function down(): void
    {
        // إزالة الفهارس المضافة
        Schema::table('projects', function (Blueprint $table) {
            $table->dropIndex('idx_projects_status_priority_date');
            $table->dropIndex('idx_projects_client_status_end');
            $table->dropIndex('idx_projects_manager_status_priority');
            $table->dropIndex('idx_projects_active_dates');
            $table->dropIndex('idx_projects_budget_spent');
            $table->dropIndex('idx_projects_type_status_date');
        });

        Schema::table('tasks', function (Blueprint $table) {
            $table->dropIndex('idx_tasks_project_status_priority_due');
            $table->dropIndex('idx_tasks_assigned_status_due');
            $table->dropIndex('idx_tasks_parent_status_order');
            $table->dropIndex('idx_tasks_overdue');
            $table->dropIndex('idx_tasks_type_status_date');
            $table->dropIndex('idx_tasks_hours');
        });

        Schema::table('time_entries', function (Blueprint $table) {
            $table->dropIndex('idx_time_date_user_project');
            $table->dropIndex('idx_time_project_date_billable');
            $table->dropIndex('idx_time_user_date_hours');
            $table->dropIndex('idx_time_billing');
        });

        Schema::table('comments', function (Blueprint $table) {
            $table->dropIndex('idx_comments_polymorphic_date');
            $table->dropIndex('idx_comments_user_type_date');
            $table->dropIndex('idx_comments_flags_date');
            $table->dropIndex('idx_comments_replies');
        });

        Schema::table('project_files', function (Blueprint $table) {
            $table->dropIndex('idx_files_polymorphic_category');
            $table->dropIndex('idx_files_uploader_date_size');
            $table->dropIndex('idx_files_type_category_public');
            $table->dropIndex('idx_files_size_date');
        });

        // إزالة الفهارس الجزئية
        DB::statement("DROP INDEX IF EXISTS idx_projects_active_only ON projects");
        DB::statement("DROP INDEX IF EXISTS idx_tasks_active_only ON tasks");
        DB::statement("DROP INDEX IF EXISTS idx_tasks_overdue_only ON tasks");
        DB::statement("DROP INDEX IF EXISTS idx_time_unbilled_only ON time_entries");

        // إزالة التقسيم
        DB::statement("ALTER TABLE time_entries REMOVE PARTITIONING");
        DB::statement("ALTER TABLE activity_logs REMOVE PARTITIONING");
    }
};
