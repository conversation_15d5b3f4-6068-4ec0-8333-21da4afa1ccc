net\authorize\api\contract\v1\CcAuthenticationType:
    properties:
        authenticationIndicator:
            expose: true
            access_type: public_method
            serialized_name: authenticationIndicator
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getAuthenticationIndicator
                setter: setAuthenticationIndicator
            type: string
        cardholderAuthenticationValue:
            expose: true
            access_type: public_method
            serialized_name: cardholderAuthenticationValue
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getCardholderAuthenticationValue
                setter: setCardholderAuthenticationValue
            type: string
