var TypeAhead=function(e,t){var i=this;return i.element=e,i.candidates=t||[],i.list=new TypeAheadList(i),this.minLength=3,i.limit=5,i.query="",i.selected=null,i.list.draw(),i.element.addEventListener("keyup",function(e){i.handleKeyUp.call(i,e.keyCode)},!1),i.element.addEventListener("keydown",function(e){i.handleKeyDown.call(i,e.keyCode)&&e.preventDefault()}),i.element.addEventListener("focus",function(){i.handleFocus.call(i)}),i.element.addEventListener("blur",function(){i.handleBlur.call(i)}),i};TypeAhead.prototype.handleKeyUp=function(e){if(13!==e&&38!==e&&40!==e){if(this.query=this.filter(this.element.value),this.list.clear(),this.query.length<this.minLength)return void this.list.draw();var t=this;this.getCandidates(function(e){for(var i=0;i<e.length&&(t.list.add(e[i]),t.limit===!1||i!==t.limit);i++);t.list.draw()})}},TypeAhead.prototype.handleKeyDown=function(e){return 13!==e||this.list.isEmpty()?38===e?(this.list.previous(),!0):40===e?(this.list.next(),!0):!1:(this.value(this.list.items[this.list.active]),this.list.hide(),!0)},TypeAhead.prototype.handleBlur=function(){this.list.hide()},TypeAhead.prototype.handleFocus=function(){this.list.isEmpty()||this.list.show()},TypeAhead.prototype.filter=function(e){return e=e.toLowerCase()},TypeAhead.prototype.match=function(e){return 0===e.indexOf(this.query)},TypeAhead.prototype.value=function(e){if(this.selected=e,this.element.value=this.getItemValue(e),document.createEvent){var t=document.createEvent("HTMLEvents");t.initEvent("change",!0,!1),this.element.dispatchEvent(t)}else this.element.fireEvent("onchange")},TypeAhead.prototype.getCandidates=function(e){for(var t=[],i=0;i<this.candidates.length;i++){var n=this.getItemValue(this.candidates[i]);this.match(this.filter(n))&&t.push(this.candidates[i])}e(t)},TypeAhead.prototype.getItemValue=function(e){return e},TypeAhead.prototype.highlight=function(e){return this.getItemValue(e).replace(new RegExp("^("+this.query+")","ig"),function(e,t){return"<strong>"+t+"</strong>"})};var TypeAheadList=function(e){var t=this;return t.typeAhead=e,t.items=[],t.active=0,t.element=document.createElement("ul"),e.element.parentNode.insertBefore(t.element,e.element.nextSibling),t};TypeAheadList.prototype.show=function(){this.element.style.display="block"},TypeAheadList.prototype.hide=function(){this.element.style.display="none"},TypeAheadList.prototype.add=function(e){this.items.push(e)},TypeAheadList.prototype.clear=function(){this.items=[],this.active=0},TypeAheadList.prototype.isEmpty=function(){return 0===this.element.children.length},TypeAheadList.prototype.draw=function(){if(this.element.innerHTML="",0===this.items.length)return void this.hide();for(var e=0;e<this.items.length;e++)this.drawItem(this.items[e],this.active===e);this.show()},TypeAheadList.prototype.drawItem=function(e,t){var i=document.createElement("li"),n=document.createElement("a");t&&(i.className+=" active"),n.innerHTML=this.typeAhead.highlight(e),i.appendChild(n),this.element.appendChild(i);var s=this;i.addEventListener("mousedown",function(){s.handleMouseDown.call(s,e)})},TypeAheadList.prototype.handleMouseDown=function(e){this.typeAhead.value(e),this.clear(),this.draw()},TypeAheadList.prototype.move=function(e){this.active=e,this.draw()},TypeAheadList.prototype.previous=function(){this.move(0===this.active?this.items.length-1:this.active-1)},TypeAheadList.prototype.next=function(){this.move(this.active===this.items.length-1?0:this.active+1)};