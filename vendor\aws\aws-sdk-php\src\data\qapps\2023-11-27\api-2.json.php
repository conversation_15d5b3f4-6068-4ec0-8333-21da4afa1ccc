<?php
// This file was auto-generated from sdk-root/src/data/qapps/2023-11-27/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-11-27', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'data.qapps', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'QApps', 'serviceId' => 'QApps', 'signatureVersion' => 'v4', 'signingName' => 'qapps', 'uid' => 'qapps-2023-11-27', ], 'operations' => [ 'AssociateLibraryItemReview' => [ 'name' => 'AssociateLibraryItemReview', 'http' => [ 'method' => 'POST', 'requestUri' => '/catalog.associateItemRating', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateLibraryItemReviewInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'AssociateQAppWithUser' => [ 'name' => 'AssociateQAppWithUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/apps.install', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateQAppWithUserInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'BatchCreateCategory' => [ 'name' => 'BatchCreateCategory', 'http' => [ 'method' => 'POST', 'requestUri' => '/catalog.createCategories', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchCreateCategoryInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'BatchDeleteCategory' => [ 'name' => 'BatchDeleteCategory', 'http' => [ 'method' => 'POST', 'requestUri' => '/catalog.deleteCategories', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchDeleteCategoryInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'BatchUpdateCategory' => [ 'name' => 'BatchUpdateCategory', 'http' => [ 'method' => 'POST', 'requestUri' => '/catalog.updateCategories', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchUpdateCategoryInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateLibraryItem' => [ 'name' => 'CreateLibraryItem', 'http' => [ 'method' => 'POST', 'requestUri' => '/catalog.createItem', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateLibraryItemInput', ], 'output' => [ 'shape' => 'CreateLibraryItemOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreatePresignedUrl' => [ 'name' => 'CreatePresignedUrl', 'http' => [ 'method' => 'POST', 'requestUri' => '/apps.createPresignedUrl', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreatePresignedUrlInput', ], 'output' => [ 'shape' => 'CreatePresignedUrlOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateQApp' => [ 'name' => 'CreateQApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/apps.create', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateQAppInput', ], 'output' => [ 'shape' => 'CreateQAppOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ContentTooLargeException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteLibraryItem' => [ 'name' => 'DeleteLibraryItem', 'http' => [ 'method' => 'POST', 'requestUri' => '/catalog.deleteItem', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteLibraryItemInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteQApp' => [ 'name' => 'DeleteQApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/apps.delete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteQAppInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DescribeQAppPermissions' => [ 'name' => 'DescribeQAppPermissions', 'http' => [ 'method' => 'GET', 'requestUri' => '/apps.describeQAppPermissions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeQAppPermissionsInput', ], 'output' => [ 'shape' => 'DescribeQAppPermissionsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DisassociateLibraryItemReview' => [ 'name' => 'DisassociateLibraryItemReview', 'http' => [ 'method' => 'POST', 'requestUri' => '/catalog.disassociateItemRating', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateLibraryItemReviewInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DisassociateQAppFromUser' => [ 'name' => 'DisassociateQAppFromUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/apps.uninstall', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateQAppFromUserInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ExportQAppSessionData' => [ 'name' => 'ExportQAppSessionData', 'http' => [ 'method' => 'POST', 'requestUri' => '/runtime.exportQAppSessionData', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ExportQAppSessionDataInput', ], 'output' => [ 'shape' => 'ExportQAppSessionDataOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetLibraryItem' => [ 'name' => 'GetLibraryItem', 'http' => [ 'method' => 'GET', 'requestUri' => '/catalog.getItem', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetLibraryItemInput', ], 'output' => [ 'shape' => 'GetLibraryItemOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetQApp' => [ 'name' => 'GetQApp', 'http' => [ 'method' => 'GET', 'requestUri' => '/apps.get', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetQAppInput', ], 'output' => [ 'shape' => 'GetQAppOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetQAppSession' => [ 'name' => 'GetQAppSession', 'http' => [ 'method' => 'GET', 'requestUri' => '/runtime.getQAppSession', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetQAppSessionInput', ], 'output' => [ 'shape' => 'GetQAppSessionOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetQAppSessionMetadata' => [ 'name' => 'GetQAppSessionMetadata', 'http' => [ 'method' => 'GET', 'requestUri' => '/runtime.getQAppSessionMetadata', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetQAppSessionMetadataInput', ], 'output' => [ 'shape' => 'GetQAppSessionMetadataOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ImportDocument' => [ 'name' => 'ImportDocument', 'http' => [ 'method' => 'POST', 'requestUri' => '/apps.importDocument', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ImportDocumentInput', ], 'output' => [ 'shape' => 'ImportDocumentOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ContentTooLargeException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListCategories' => [ 'name' => 'ListCategories', 'http' => [ 'method' => 'GET', 'requestUri' => '/catalog.listCategories', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCategoriesInput', ], 'output' => [ 'shape' => 'ListCategoriesOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListLibraryItems' => [ 'name' => 'ListLibraryItems', 'http' => [ 'method' => 'GET', 'requestUri' => '/catalog.list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListLibraryItemsInput', ], 'output' => [ 'shape' => 'ListLibraryItemsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListQAppSessionData' => [ 'name' => 'ListQAppSessionData', 'http' => [ 'method' => 'GET', 'requestUri' => '/runtime.listQAppSessionData', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListQAppSessionDataInput', ], 'output' => [ 'shape' => 'ListQAppSessionDataOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListQApps' => [ 'name' => 'ListQApps', 'http' => [ 'method' => 'GET', 'requestUri' => '/apps.list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListQAppsInput', ], 'output' => [ 'shape' => 'ListQAppsOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceARN}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'PredictQApp' => [ 'name' => 'PredictQApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/apps.predictQApp', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PredictQAppInput', ], 'output' => [ 'shape' => 'PredictQAppOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'StartQAppSession' => [ 'name' => 'StartQAppSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/runtime.startQAppSession', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartQAppSessionInput', ], 'output' => [ 'shape' => 'StartQAppSessionOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'StopQAppSession' => [ 'name' => 'StopQAppSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/runtime.deleteMiniAppRun', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopQAppSessionInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceARN}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceARN}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdateLibraryItem' => [ 'name' => 'UpdateLibraryItem', 'http' => [ 'method' => 'POST', 'requestUri' => '/catalog.updateItem', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateLibraryItemInput', ], 'output' => [ 'shape' => 'UpdateLibraryItemOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateLibraryItemMetadata' => [ 'name' => 'UpdateLibraryItemMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/catalog.updateItemMetadata', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateLibraryItemMetadataInput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdateQApp' => [ 'name' => 'UpdateQApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/apps.update', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateQAppInput', ], 'output' => [ 'shape' => 'UpdateQAppOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ContentTooLargeException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateQAppPermissions' => [ 'name' => 'UpdateQAppPermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/apps.updateQAppPermissions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateQAppPermissionsInput', ], 'output' => [ 'shape' => 'UpdateQAppPermissionsOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateQAppSession' => [ 'name' => 'UpdateQAppSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/runtime.updateQAppSession', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateQAppSessionInput', ], 'output' => [ 'shape' => 'UpdateQAppSessionOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateQAppSessionMetadata' => [ 'name' => 'UpdateQAppSessionMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/runtime.updateQAppSessionMetadata', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateQAppSessionMetadataInput', ], 'output' => [ 'shape' => 'UpdateQAppSessionMetadataOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'ActionIdentifier' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, ], 'AppArn' => [ 'type' => 'string', ], 'AppDefinition' => [ 'type' => 'structure', 'required' => [ 'appDefinitionVersion', 'cards', ], 'members' => [ 'appDefinitionVersion' => [ 'shape' => 'String', ], 'cards' => [ 'shape' => 'CardModelList', ], 'canEdit' => [ 'shape' => 'Boolean', ], ], ], 'AppDefinitionInput' => [ 'type' => 'structure', 'required' => [ 'cards', ], 'members' => [ 'cards' => [ 'shape' => 'CardList', ], 'initialPrompt' => [ 'shape' => 'InitialPrompt', ], ], ], 'AppRequiredCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppRequiredCapability', ], ], 'AppRequiredCapability' => [ 'type' => 'string', 'enum' => [ 'FileUpload', 'CreatorMode', 'RetrievalMode', 'PluginMode', ], ], 'AppStatus' => [ 'type' => 'string', 'enum' => [ 'PUBLISHED', 'DRAFT', 'DELETED', ], ], 'AppVersion' => [ 'type' => 'integer', 'box' => true, 'max' => 2147483647, 'min' => 0, ], 'AssociateLibraryItemReviewInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'libraryItemId', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'libraryItemId' => [ 'shape' => 'UUID', ], ], ], 'AssociateQAppWithUserInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'appId', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'appId' => [ 'shape' => 'UUID', ], ], ], 'AttributeFilter' => [ 'type' => 'structure', 'members' => [ 'andAllFilters' => [ 'shape' => 'AttributeFilters', ], 'orAllFilters' => [ 'shape' => 'AttributeFilters', ], 'notFilter' => [ 'shape' => 'AttributeFilter', ], 'equalsTo' => [ 'shape' => 'DocumentAttribute', ], 'containsAll' => [ 'shape' => 'DocumentAttribute', ], 'containsAny' => [ 'shape' => 'DocumentAttribute', ], 'greaterThan' => [ 'shape' => 'DocumentAttribute', ], 'greaterThanOrEquals' => [ 'shape' => 'DocumentAttribute', ], 'lessThan' => [ 'shape' => 'DocumentAttribute', ], 'lessThanOrEquals' => [ 'shape' => 'DocumentAttribute', ], ], ], 'AttributeFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeFilter', ], ], 'BatchCreateCategoryInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'categories', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'categories' => [ 'shape' => 'BatchCreateCategoryInputCategoryList', ], ], ], 'BatchCreateCategoryInputCategory' => [ 'type' => 'structure', 'required' => [ 'title', ], 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'title' => [ 'shape' => 'BatchCreateCategoryInputCategoryTitleString', ], 'color' => [ 'shape' => 'BatchCreateCategoryInputCategoryColorString', ], ], ], 'BatchCreateCategoryInputCategoryColorString' => [ 'type' => 'string', 'max' => 7, 'min' => 4, 'pattern' => '#([A-Fa-f0-9]{3}|[A-Fa-f0-9]{6})', ], 'BatchCreateCategoryInputCategoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchCreateCategoryInputCategory', ], 'max' => 10, 'min' => 0, ], 'BatchCreateCategoryInputCategoryTitleString' => [ 'type' => 'string', 'max' => 30, 'min' => 1, 'pattern' => '[a-zA-Z0-9_]+( [a-zA-Z0-9_]+)*', ], 'BatchDeleteCategoryInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'categories', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'categories' => [ 'shape' => 'DeleteCategoryInputList', ], ], ], 'BatchUpdateCategoryInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'categories', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'categories' => [ 'shape' => 'CategoryListInput', ], ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'Card' => [ 'type' => 'structure', 'members' => [ 'textInput' => [ 'shape' => 'TextInputCard', ], 'qQuery' => [ 'shape' => 'QQueryCard', ], 'qPlugin' => [ 'shape' => 'QPluginCard', ], 'fileUpload' => [ 'shape' => 'FileUploadCard', ], 'formInput' => [ 'shape' => 'FormInputCard', ], ], 'union' => true, ], 'CardInput' => [ 'type' => 'structure', 'members' => [ 'textInput' => [ 'shape' => 'TextInputCardInput', ], 'qQuery' => [ 'shape' => 'QQueryCardInput', ], 'qPlugin' => [ 'shape' => 'QPluginCardInput', ], 'fileUpload' => [ 'shape' => 'FileUploadCardInput', ], 'formInput' => [ 'shape' => 'FormInputCardInput', ], ], 'union' => true, ], 'CardList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CardInput', ], 'max' => 20, 'min' => 0, ], 'CardModelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Card', ], 'max' => 20, 'min' => 0, ], 'CardOutputSource' => [ 'type' => 'string', 'enum' => [ 'approved-sources', 'llm', ], ], 'CardStatus' => [ 'type' => 'structure', 'required' => [ 'currentState', 'currentValue', ], 'members' => [ 'currentState' => [ 'shape' => 'ExecutionStatus', ], 'currentValue' => [ 'shape' => 'String', ], 'submissions' => [ 'shape' => 'SubmissionList', ], ], ], 'CardStatusMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'UUID', ], 'value' => [ 'shape' => 'CardStatus', ], ], 'CardType' => [ 'type' => 'string', 'enum' => [ 'text-input', 'q-query', 'file-upload', 'q-plugin', 'form-input', ], ], 'CardValue' => [ 'type' => 'structure', 'required' => [ 'cardId', 'value', ], 'members' => [ 'cardId' => [ 'shape' => 'UUID', ], 'value' => [ 'shape' => 'CardValueValueString', ], 'submissionMutation' => [ 'shape' => 'SubmissionMutation', ], ], ], 'CardValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CardValue', ], 'max' => 20, 'min' => 0, ], 'CardValueValueString' => [ 'type' => 'string', 'max' => 40000, 'min' => 0, ], 'CategoriesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Category', ], 'max' => 10, 'min' => 0, ], 'Category' => [ 'type' => 'structure', 'required' => [ 'id', 'title', ], 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'title' => [ 'shape' => 'String', ], 'color' => [ 'shape' => 'String', ], 'appCount' => [ 'shape' => 'Integer', ], ], ], 'CategoryIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UUID', ], 'max' => 3, 'min' => 0, ], 'CategoryInput' => [ 'type' => 'structure', 'required' => [ 'id', 'title', ], 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'title' => [ 'shape' => 'CategoryInputTitleString', ], 'color' => [ 'shape' => 'CategoryInputColorString', ], ], ], 'CategoryInputColorString' => [ 'type' => 'string', 'max' => 7, 'min' => 4, 'pattern' => '#([A-Fa-f0-9]{3}|[A-Fa-f0-9]{6})', ], 'CategoryInputTitleString' => [ 'type' => 'string', 'max' => 30, 'min' => 1, 'pattern' => '[a-zA-Z0-9_]+( [a-zA-Z0-9_]+)*', ], 'CategoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Category', ], 'max' => 3, 'min' => 0, ], 'CategoryListInput' => [ 'type' => 'list', 'member' => [ 'shape' => 'CategoryInput', ], 'max' => 10, 'min' => 0, ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ContentTooLargeException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 413, 'senderFault' => true, ], 'exception' => true, ], 'ConversationMessage' => [ 'type' => 'structure', 'required' => [ 'body', 'type', ], 'members' => [ 'body' => [ 'shape' => 'ConversationMessageBodyString', ], 'type' => [ 'shape' => 'Sender', ], ], ], 'ConversationMessageBodyString' => [ 'type' => 'string', 'max' => 7000, 'min' => 0, ], 'CreateLibraryItemInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'appId', 'appVersion', 'categories', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'appId' => [ 'shape' => 'UUID', ], 'appVersion' => [ 'shape' => 'AppVersion', ], 'categories' => [ 'shape' => 'CategoryIdList', ], ], ], 'CreateLibraryItemOutput' => [ 'type' => 'structure', 'required' => [ 'libraryItemId', 'status', 'createdAt', 'createdBy', 'ratingCount', ], 'members' => [ 'libraryItemId' => [ 'shape' => 'UUID', ], 'status' => [ 'shape' => 'String', ], 'createdAt' => [ 'shape' => 'QAppsTimestamp', ], 'createdBy' => [ 'shape' => 'String', ], 'updatedAt' => [ 'shape' => 'QAppsTimestamp', ], 'updatedBy' => [ 'shape' => 'String', ], 'ratingCount' => [ 'shape' => 'Integer', ], 'isVerified' => [ 'shape' => 'Boolean', ], ], ], 'CreatePresignedUrlInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'cardId', 'appId', 'fileContentsSha256', 'fileName', 'scope', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'cardId' => [ 'shape' => 'UUID', ], 'appId' => [ 'shape' => 'UUID', ], 'fileContentsSha256' => [ 'shape' => 'CreatePresignedUrlInputFileContentsSha256String', ], 'fileName' => [ 'shape' => 'Filename', ], 'scope' => [ 'shape' => 'DocumentScope', ], 'sessionId' => [ 'shape' => 'UUID', ], ], ], 'CreatePresignedUrlInputFileContentsSha256String' => [ 'type' => 'string', 'pattern' => '[A-Za-z0-9+/]{43}=$|^[A-Za-z0-9+/]{42}==$|^[A-Za-z0-9+/]{44}', ], 'CreatePresignedUrlOutput' => [ 'type' => 'structure', 'required' => [ 'fileId', 'presignedUrl', 'presignedUrlFields', 'presignedUrlExpiration', ], 'members' => [ 'fileId' => [ 'shape' => 'String', ], 'presignedUrl' => [ 'shape' => 'String', ], 'presignedUrlFields' => [ 'shape' => 'PresignedUrlFields', ], 'presignedUrlExpiration' => [ 'shape' => 'QAppsTimestamp', ], ], ], 'CreateQAppInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'title', 'appDefinition', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'title' => [ 'shape' => 'Title', ], 'description' => [ 'shape' => 'Description', ], 'appDefinition' => [ 'shape' => 'AppDefinitionInput', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateQAppOutput' => [ 'type' => 'structure', 'required' => [ 'appId', 'appArn', 'title', 'appVersion', 'status', 'createdAt', 'createdBy', 'updatedAt', 'updatedBy', ], 'members' => [ 'appId' => [ 'shape' => 'UUID', ], 'appArn' => [ 'shape' => 'AppArn', ], 'title' => [ 'shape' => 'Title', ], 'description' => [ 'shape' => 'Description', ], 'initialPrompt' => [ 'shape' => 'InitialPrompt', ], 'appVersion' => [ 'shape' => 'AppVersion', ], 'status' => [ 'shape' => 'AppStatus', ], 'createdAt' => [ 'shape' => 'QAppsTimestamp', ], 'createdBy' => [ 'shape' => 'String', ], 'updatedAt' => [ 'shape' => 'QAppsTimestamp', ], 'updatedBy' => [ 'shape' => 'String', ], 'requiredCapabilities' => [ 'shape' => 'AppRequiredCapabilities', ], ], ], 'Default' => [ 'type' => 'string', 'max' => 500, 'min' => 0, ], 'DeleteCategoryInputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UUID', ], 'max' => 10, 'min' => 0, ], 'DeleteLibraryItemInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'libraryItemId', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'libraryItemId' => [ 'shape' => 'UUID', ], ], ], 'DeleteQAppInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'appId', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'appId' => [ 'shape' => 'UUID', ], ], ], 'DependencyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'DescribeQAppPermissionsInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'appId', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'appId' => [ 'shape' => 'UUID', 'location' => 'querystring', 'locationName' => 'appId', ], ], ], 'DescribeQAppPermissionsOutput' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => 'String', ], 'appId' => [ 'shape' => 'String', ], 'permissions' => [ 'shape' => 'PermissionsOutputList', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 500, 'min' => 0, ], 'DisassociateLibraryItemReviewInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'libraryItemId', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'libraryItemId' => [ 'shape' => 'UUID', ], ], ], 'DisassociateQAppFromUserInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'appId', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'appId' => [ 'shape' => 'UUID', ], ], ], 'Document' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'DocumentAttribute' => [ 'type' => 'structure', 'required' => [ 'name', 'value', ], 'members' => [ 'name' => [ 'shape' => 'DocumentAttributeKey', ], 'value' => [ 'shape' => 'DocumentAttributeValue', ], ], ], 'DocumentAttributeKey' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[a-zA-Z0-9_][a-zA-Z0-9_-]*', ], 'DocumentAttributeStringListValue' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlatoString', ], ], 'DocumentAttributeValue' => [ 'type' => 'structure', 'members' => [ 'stringValue' => [ 'shape' => 'DocumentAttributeValueStringValueString', ], 'stringListValue' => [ 'shape' => 'DocumentAttributeStringListValue', ], 'longValue' => [ 'shape' => 'Long', ], 'dateValue' => [ 'shape' => 'Timestamp', ], ], 'union' => true, ], 'DocumentAttributeValueStringValueString' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'DocumentScope' => [ 'type' => 'string', 'enum' => [ 'APPLICATION', 'SESSION', ], ], 'ExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'WAITING', 'COMPLETED', 'ERROR', ], ], 'ExportQAppSessionDataInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'sessionId', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'sessionId' => [ 'shape' => 'UUID', ], ], ], 'ExportQAppSessionDataOutput' => [ 'type' => 'structure', 'required' => [ 'csvFileLink', 'expiresAt', 'sessionArn', ], 'members' => [ 'csvFileLink' => [ 'shape' => 'String', ], 'expiresAt' => [ 'shape' => 'QAppsTimestamp', ], 'sessionArn' => [ 'shape' => 'String', ], ], ], 'FileUploadCard' => [ 'type' => 'structure', 'required' => [ 'id', 'title', 'dependencies', 'type', ], 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'title' => [ 'shape' => 'Title', ], 'dependencies' => [ 'shape' => 'DependencyList', ], 'type' => [ 'shape' => 'CardType', ], 'filename' => [ 'shape' => 'String', ], 'fileId' => [ 'shape' => 'String', ], 'allowOverride' => [ 'shape' => 'Boolean', ], ], ], 'FileUploadCardInput' => [ 'type' => 'structure', 'required' => [ 'title', 'id', 'type', ], 'members' => [ 'title' => [ 'shape' => 'Title', ], 'id' => [ 'shape' => 'UUID', ], 'type' => [ 'shape' => 'CardType', ], 'filename' => [ 'shape' => 'Filename', ], 'fileId' => [ 'shape' => 'UUID', ], 'allowOverride' => [ 'shape' => 'Boolean', ], ], ], 'Filename' => [ 'type' => 'string', 'max' => 100, 'min' => 0, ], 'FormInputCard' => [ 'type' => 'structure', 'required' => [ 'id', 'title', 'dependencies', 'type', 'metadata', ], 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'title' => [ 'shape' => 'Title', ], 'dependencies' => [ 'shape' => 'DependencyList', ], 'type' => [ 'shape' => 'CardType', ], 'metadata' => [ 'shape' => 'FormInputCardMetadata', ], 'computeMode' => [ 'shape' => 'InputCardComputeMode', ], ], ], 'FormInputCardInput' => [ 'type' => 'structure', 'required' => [ 'title', 'id', 'type', 'metadata', ], 'members' => [ 'title' => [ 'shape' => 'Title', ], 'id' => [ 'shape' => 'UUID', ], 'type' => [ 'shape' => 'CardType', ], 'metadata' => [ 'shape' => 'FormInputCardMetadata', ], 'computeMode' => [ 'shape' => 'InputCardComputeMode', ], ], ], 'FormInputCardMetadata' => [ 'type' => 'structure', 'required' => [ 'schema', ], 'members' => [ 'schema' => [ 'shape' => 'FormInputCardMetadataSchema', ], ], ], 'FormInputCardMetadataSchema' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'GetLibraryItemInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'libraryItemId', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'libraryItemId' => [ 'shape' => 'UUID', 'location' => 'querystring', 'locationName' => 'libraryItemId', ], 'appId' => [ 'shape' => 'UUID', 'location' => 'querystring', 'locationName' => 'appId', ], ], ], 'GetLibraryItemOutput' => [ 'type' => 'structure', 'required' => [ 'libraryItemId', 'appId', 'appVersion', 'categories', 'status', 'createdAt', 'createdBy', 'ratingCount', ], 'members' => [ 'libraryItemId' => [ 'shape' => 'UUID', ], 'appId' => [ 'shape' => 'UUID', ], 'appVersion' => [ 'shape' => 'AppVersion', ], 'categories' => [ 'shape' => 'CategoryList', ], 'status' => [ 'shape' => 'String', ], 'createdAt' => [ 'shape' => 'QAppsTimestamp', ], 'createdBy' => [ 'shape' => 'String', ], 'updatedAt' => [ 'shape' => 'QAppsTimestamp', ], 'updatedBy' => [ 'shape' => 'String', ], 'ratingCount' => [ 'shape' => 'Integer', ], 'isRatedByUser' => [ 'shape' => 'Boolean', ], 'userCount' => [ 'shape' => 'Integer', ], 'isVerified' => [ 'shape' => 'Boolean', ], ], ], 'GetQAppInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'appId', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'appId' => [ 'shape' => 'UUID', 'location' => 'querystring', 'locationName' => 'appId', ], 'appVersion' => [ 'shape' => 'AppVersion', 'location' => 'querystring', 'locationName' => 'appVersion', ], ], ], 'GetQAppOutput' => [ 'type' => 'structure', 'required' => [ 'appId', 'appArn', 'title', 'appVersion', 'status', 'createdAt', 'createdBy', 'updatedAt', 'updatedBy', 'appDefinition', ], 'members' => [ 'appId' => [ 'shape' => 'UUID', ], 'appArn' => [ 'shape' => 'AppArn', ], 'title' => [ 'shape' => 'Title', ], 'description' => [ 'shape' => 'Description', ], 'initialPrompt' => [ 'shape' => 'InitialPrompt', ], 'appVersion' => [ 'shape' => 'AppVersion', ], 'status' => [ 'shape' => 'AppStatus', ], 'createdAt' => [ 'shape' => 'QAppsTimestamp', ], 'createdBy' => [ 'shape' => 'String', ], 'updatedAt' => [ 'shape' => 'QAppsTimestamp', ], 'updatedBy' => [ 'shape' => 'String', ], 'requiredCapabilities' => [ 'shape' => 'AppRequiredCapabilities', ], 'appDefinition' => [ 'shape' => 'AppDefinition', ], ], ], 'GetQAppSessionInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'sessionId', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'sessionId' => [ 'shape' => 'UUID', 'location' => 'querystring', 'locationName' => 'sessionId', ], ], ], 'GetQAppSessionMetadataInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'sessionId', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'sessionId' => [ 'shape' => 'UUID', 'location' => 'querystring', 'locationName' => 'sessionId', ], ], ], 'GetQAppSessionMetadataOutput' => [ 'type' => 'structure', 'required' => [ 'sessionId', 'sessionArn', 'sharingConfiguration', ], 'members' => [ 'sessionId' => [ 'shape' => 'UUID', ], 'sessionArn' => [ 'shape' => 'String', ], 'sessionName' => [ 'shape' => 'SessionName', ], 'sharingConfiguration' => [ 'shape' => 'SessionSharingConfiguration', ], 'sessionOwner' => [ 'shape' => 'Boolean', ], ], ], 'GetQAppSessionOutput' => [ 'type' => 'structure', 'required' => [ 'sessionId', 'sessionArn', 'status', 'cardStatus', ], 'members' => [ 'sessionId' => [ 'shape' => 'String', ], 'sessionArn' => [ 'shape' => 'String', ], 'sessionName' => [ 'shape' => 'SessionName', ], 'appVersion' => [ 'shape' => 'AppVersion', ], 'latestPublishedAppVersion' => [ 'shape' => 'AppVersion', ], 'status' => [ 'shape' => 'ExecutionStatus', ], 'cardStatus' => [ 'shape' => 'CardStatusMap', ], 'userIsHost' => [ 'shape' => 'Boolean', ], ], ], 'ImportDocumentInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'cardId', 'appId', 'fileContentsBase64', 'fileName', 'scope', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'cardId' => [ 'shape' => 'UUID', ], 'appId' => [ 'shape' => 'UUID', ], 'fileContentsBase64' => [ 'shape' => 'String', ], 'fileName' => [ 'shape' => 'Filename', ], 'scope' => [ 'shape' => 'DocumentScope', ], 'sessionId' => [ 'shape' => 'UUID', ], ], ], 'ImportDocumentOutput' => [ 'type' => 'structure', 'members' => [ 'fileId' => [ 'shape' => 'String', ], ], ], 'InitialPrompt' => [ 'type' => 'string', 'max' => 10000, 'min' => 0, ], 'InputCardComputeMode' => [ 'type' => 'string', 'enum' => [ 'append', 'replace', ], ], 'InstanceId' => [ 'type' => 'string', ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'LibraryItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LibraryItemMember', ], ], 'LibraryItemMember' => [ 'type' => 'structure', 'required' => [ 'libraryItemId', 'appId', 'appVersion', 'categories', 'status', 'createdAt', 'createdBy', 'ratingCount', ], 'members' => [ 'libraryItemId' => [ 'shape' => 'UUID', ], 'appId' => [ 'shape' => 'UUID', ], 'appVersion' => [ 'shape' => 'AppVersion', ], 'categories' => [ 'shape' => 'CategoryList', ], 'status' => [ 'shape' => 'String', ], 'createdAt' => [ 'shape' => 'QAppsTimestamp', ], 'createdBy' => [ 'shape' => 'String', ], 'updatedAt' => [ 'shape' => 'QAppsTimestamp', ], 'updatedBy' => [ 'shape' => 'String', ], 'ratingCount' => [ 'shape' => 'Integer', ], 'isRatedByUser' => [ 'shape' => 'Boolean', ], 'userCount' => [ 'shape' => 'Integer', ], 'isVerified' => [ 'shape' => 'Boolean', ], ], ], 'LibraryItemStatus' => [ 'type' => 'string', 'enum' => [ 'PUBLISHED', 'DISABLED', ], ], 'ListCategoriesInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], ], ], 'ListCategoriesOutput' => [ 'type' => 'structure', 'members' => [ 'categories' => [ 'shape' => 'CategoriesList', ], ], ], 'ListLibraryItemsInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'limit' => [ 'shape' => 'PageLimit', 'location' => 'querystring', 'locationName' => 'limit', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'categoryId' => [ 'shape' => 'UUID', 'location' => 'querystring', 'locationName' => 'categoryId', ], ], ], 'ListLibraryItemsOutput' => [ 'type' => 'structure', 'members' => [ 'libraryItems' => [ 'shape' => 'LibraryItemList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListQAppSessionDataInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'sessionId', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'sessionId' => [ 'shape' => 'UUID', 'location' => 'querystring', 'locationName' => 'sessionId', ], ], ], 'ListQAppSessionDataOutput' => [ 'type' => 'structure', 'required' => [ 'sessionId', 'sessionArn', ], 'members' => [ 'sessionId' => [ 'shape' => 'UUID', ], 'sessionArn' => [ 'shape' => 'String', ], 'sessionData' => [ 'shape' => 'QAppSessionDataList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListQAppsInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'limit' => [ 'shape' => 'PageLimit', 'location' => 'querystring', 'locationName' => 'limit', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListQAppsOutput' => [ 'type' => 'structure', 'required' => [ 'apps', ], 'members' => [ 'apps' => [ 'shape' => 'UserAppsList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', ], 'members' => [ 'resourceARN' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceARN', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'Tags', ], ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'MemoryReferenceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'PageLimit' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'PaginationToken' => [ 'type' => 'string', 'max' => 300, 'min' => 0, ], 'PermissionInput' => [ 'type' => 'structure', 'required' => [ 'action', 'principal', ], 'members' => [ 'action' => [ 'shape' => 'PermissionInputActionEnum', ], 'principal' => [ 'shape' => 'PermissionInputPrincipalString', ], ], ], 'PermissionInputActionEnum' => [ 'type' => 'string', 'enum' => [ 'read', 'write', ], 'max' => 20, 'min' => 1, ], 'PermissionInputPrincipalString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'PermissionOutput' => [ 'type' => 'structure', 'required' => [ 'action', 'principal', ], 'members' => [ 'action' => [ 'shape' => 'PermissionOutputActionEnum', ], 'principal' => [ 'shape' => 'PrincipalOutput', ], ], ], 'PermissionOutputActionEnum' => [ 'type' => 'string', 'enum' => [ 'read', 'write', ], 'max' => 20, 'min' => 1, ], 'PermissionsOutputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PermissionOutput', ], ], 'Placeholder' => [ 'type' => 'string', 'max' => 500, 'min' => 0, ], 'PlatoString' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'PluginId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, ], 'PluginType' => [ 'type' => 'string', 'enum' => [ 'SERVICE_NOW', 'SALESFORCE', 'JIRA', 'ZENDESK', 'CUSTOM', 'ASANA', 'ATLASSIAN_CONFLUENCE', 'GOOGLE_CALENDAR', 'JIRA_CLOUD', 'MICROSOFT_EXCHANGE', 'MICROSOFT_TEAMS', 'PAGERDUTY_ADVANCE', 'SALESFORCE_CRM', 'SERVICENOW_NOW_PLATFORM', 'SMARTSHEET', 'ZENDESK_SUITE', ], ], 'PredictAppDefinition' => [ 'type' => 'structure', 'required' => [ 'title', 'appDefinition', ], 'members' => [ 'title' => [ 'shape' => 'Title', ], 'description' => [ 'shape' => 'Description', ], 'appDefinition' => [ 'shape' => 'AppDefinitionInput', ], ], ], 'PredictQAppInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'options' => [ 'shape' => 'PredictQAppInputOptions', ], ], ], 'PredictQAppInputOptions' => [ 'type' => 'structure', 'members' => [ 'conversation' => [ 'shape' => 'PredictQAppInputOptionsConversationList', ], 'problemStatement' => [ 'shape' => 'PredictQAppInputOptionsProblemStatementString', ], ], 'union' => true, ], 'PredictQAppInputOptionsConversationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConversationMessage', ], 'max' => 25, 'min' => 1, ], 'PredictQAppInputOptionsProblemStatementString' => [ 'type' => 'string', 'max' => 10000, 'min' => 0, ], 'PredictQAppOutput' => [ 'type' => 'structure', 'required' => [ 'app', 'problemStatement', ], 'members' => [ 'app' => [ 'shape' => 'PredictAppDefinition', ], 'problemStatement' => [ 'shape' => 'String', ], ], ], 'PresignedUrlFields' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'PrincipalOutput' => [ 'type' => 'structure', 'members' => [ 'userId' => [ 'shape' => 'PrincipalOutputUserIdString', ], 'userType' => [ 'shape' => 'PrincipalOutputUserTypeEnum', ], 'email' => [ 'shape' => 'PrincipalOutputEmailString', ], ], ], 'PrincipalOutputEmailString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'PrincipalOutputUserIdString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'PrincipalOutputUserTypeEnum' => [ 'type' => 'string', 'enum' => [ 'owner', 'user', ], 'max' => 256, 'min' => 1, ], 'Prompt' => [ 'type' => 'string', 'max' => 50000, 'min' => 0, ], 'QAppSessionData' => [ 'type' => 'structure', 'required' => [ 'cardId', 'user', ], 'members' => [ 'cardId' => [ 'shape' => 'UUID', ], 'value' => [ 'shape' => 'Document', ], 'user' => [ 'shape' => 'User', ], 'submissionId' => [ 'shape' => 'UUID', ], 'timestamp' => [ 'shape' => 'QAppsTimestamp', ], ], ], 'QAppSessionDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QAppSessionData', ], ], 'QAppsTimestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'QPluginCard' => [ 'type' => 'structure', 'required' => [ 'id', 'title', 'dependencies', 'type', 'prompt', 'pluginType', 'pluginId', ], 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'title' => [ 'shape' => 'Title', ], 'dependencies' => [ 'shape' => 'DependencyList', ], 'type' => [ 'shape' => 'CardType', ], 'prompt' => [ 'shape' => 'Prompt', ], 'pluginType' => [ 'shape' => 'PluginType', ], 'pluginId' => [ 'shape' => 'String', ], 'actionIdentifier' => [ 'shape' => 'ActionIdentifier', ], ], ], 'QPluginCardInput' => [ 'type' => 'structure', 'required' => [ 'title', 'id', 'type', 'prompt', 'pluginId', ], 'members' => [ 'title' => [ 'shape' => 'Title', ], 'id' => [ 'shape' => 'UUID', ], 'type' => [ 'shape' => 'CardType', ], 'prompt' => [ 'shape' => 'Prompt', ], 'pluginId' => [ 'shape' => 'PluginId', ], 'actionIdentifier' => [ 'shape' => 'ActionIdentifier', ], ], ], 'QQueryCard' => [ 'type' => 'structure', 'required' => [ 'id', 'title', 'dependencies', 'type', 'prompt', 'outputSource', ], 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'title' => [ 'shape' => 'Title', ], 'dependencies' => [ 'shape' => 'DependencyList', ], 'type' => [ 'shape' => 'CardType', ], 'prompt' => [ 'shape' => 'Prompt', ], 'outputSource' => [ 'shape' => 'CardOutputSource', ], 'attributeFilter' => [ 'shape' => 'AttributeFilter', ], 'memoryReferences' => [ 'shape' => 'MemoryReferenceList', ], ], ], 'QQueryCardInput' => [ 'type' => 'structure', 'required' => [ 'title', 'id', 'type', 'prompt', ], 'members' => [ 'title' => [ 'shape' => 'Title', ], 'id' => [ 'shape' => 'UUID', ], 'type' => [ 'shape' => 'CardType', ], 'prompt' => [ 'shape' => 'Prompt', ], 'outputSource' => [ 'shape' => 'CardOutputSource', ], 'attributeFilter' => [ 'shape' => 'AttributeFilter', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'Sender' => [ 'type' => 'string', 'enum' => [ 'USER', 'SYSTEM', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', 'serviceCode', 'quotaCode', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SessionName' => [ 'type' => 'string', 'max' => 100, 'min' => 0, ], 'SessionSharingAcceptResponses' => [ 'type' => 'boolean', 'box' => true, ], 'SessionSharingConfiguration' => [ 'type' => 'structure', 'required' => [ 'enabled', ], 'members' => [ 'enabled' => [ 'shape' => 'SessionSharingEnabled', ], 'acceptResponses' => [ 'shape' => 'SessionSharingAcceptResponses', ], 'revealCards' => [ 'shape' => 'SessionSharingRevealCards', ], ], ], 'SessionSharingEnabled' => [ 'type' => 'boolean', 'box' => true, ], 'SessionSharingRevealCards' => [ 'type' => 'boolean', 'box' => true, ], 'StartQAppSessionInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'appId', 'appVersion', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'appId' => [ 'shape' => 'UUID', ], 'appVersion' => [ 'shape' => 'AppVersion', ], 'initialValues' => [ 'shape' => 'CardValueList', ], 'sessionId' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'StartQAppSessionOutput' => [ 'type' => 'structure', 'required' => [ 'sessionId', 'sessionArn', ], 'members' => [ 'sessionId' => [ 'shape' => 'String', ], 'sessionArn' => [ 'shape' => 'String', ], ], ], 'StopQAppSessionInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'sessionId', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'sessionId' => [ 'shape' => 'UUID', ], ], ], 'String' => [ 'type' => 'string', ], 'Submission' => [ 'type' => 'structure', 'members' => [ 'value' => [ 'shape' => 'Document', ], 'submissionId' => [ 'shape' => 'UUID', ], 'timestamp' => [ 'shape' => 'QAppsTimestamp', ], ], ], 'SubmissionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Submission', ], ], 'SubmissionMutation' => [ 'type' => 'structure', 'required' => [ 'submissionId', 'mutationType', ], 'members' => [ 'submissionId' => [ 'shape' => 'UUID', ], 'mutationType' => [ 'shape' => 'SubmissionMutationKind', ], ], ], 'SubmissionMutationKind' => [ 'type' => 'string', 'enum' => [ 'edit', 'delete', 'add', ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tags', ], 'members' => [ 'resourceARN' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceARN', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], 'TextInputCard' => [ 'type' => 'structure', 'required' => [ 'id', 'title', 'dependencies', 'type', ], 'members' => [ 'id' => [ 'shape' => 'UUID', ], 'title' => [ 'shape' => 'Title', ], 'dependencies' => [ 'shape' => 'DependencyList', ], 'type' => [ 'shape' => 'CardType', ], 'placeholder' => [ 'shape' => 'Placeholder', ], 'defaultValue' => [ 'shape' => 'Default', ], ], ], 'TextInputCardInput' => [ 'type' => 'structure', 'required' => [ 'title', 'id', 'type', ], 'members' => [ 'title' => [ 'shape' => 'Title', ], 'id' => [ 'shape' => 'UUID', ], 'type' => [ 'shape' => 'CardType', ], 'placeholder' => [ 'shape' => 'Placeholder', ], 'defaultValue' => [ 'shape' => 'Default', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', 'serviceCode', 'quotaCode', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'Title' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'pattern' => '[^{}\\\\"<>]+', ], 'UUID' => [ 'type' => 'string', 'pattern' => '[\\da-f]{8}-[\\da-f]{4}-[45][\\da-f]{3}-[89ABab][\\da-f]{3}-[\\da-f]{12}', ], 'UnauthorizedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 401, 'senderFault' => true, ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tagKeys', ], 'members' => [ 'resourceARN' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceARN', ], 'tagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateLibraryItemInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'libraryItemId', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'libraryItemId' => [ 'shape' => 'UUID', ], 'status' => [ 'shape' => 'LibraryItemStatus', ], 'categories' => [ 'shape' => 'CategoryIdList', ], ], ], 'UpdateLibraryItemMetadataInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'libraryItemId', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'libraryItemId' => [ 'shape' => 'UUID', ], 'isVerified' => [ 'shape' => 'Boolean', ], ], ], 'UpdateLibraryItemOutput' => [ 'type' => 'structure', 'required' => [ 'libraryItemId', 'appId', 'appVersion', 'categories', 'status', 'createdAt', 'createdBy', 'ratingCount', ], 'members' => [ 'libraryItemId' => [ 'shape' => 'UUID', ], 'appId' => [ 'shape' => 'UUID', ], 'appVersion' => [ 'shape' => 'AppVersion', ], 'categories' => [ 'shape' => 'CategoryList', ], 'status' => [ 'shape' => 'String', ], 'createdAt' => [ 'shape' => 'QAppsTimestamp', ], 'createdBy' => [ 'shape' => 'String', ], 'updatedAt' => [ 'shape' => 'QAppsTimestamp', ], 'updatedBy' => [ 'shape' => 'String', ], 'ratingCount' => [ 'shape' => 'Integer', ], 'isRatedByUser' => [ 'shape' => 'Boolean', ], 'userCount' => [ 'shape' => 'Integer', ], 'isVerified' => [ 'shape' => 'Boolean', ], ], ], 'UpdateQAppInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'appId', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'appId' => [ 'shape' => 'UUID', ], 'title' => [ 'shape' => 'Title', ], 'description' => [ 'shape' => 'Description', ], 'appDefinition' => [ 'shape' => 'AppDefinitionInput', ], ], ], 'UpdateQAppOutput' => [ 'type' => 'structure', 'required' => [ 'appId', 'appArn', 'title', 'appVersion', 'status', 'createdAt', 'createdBy', 'updatedAt', 'updatedBy', ], 'members' => [ 'appId' => [ 'shape' => 'UUID', ], 'appArn' => [ 'shape' => 'AppArn', ], 'title' => [ 'shape' => 'Title', ], 'description' => [ 'shape' => 'Description', ], 'initialPrompt' => [ 'shape' => 'InitialPrompt', ], 'appVersion' => [ 'shape' => 'AppVersion', ], 'status' => [ 'shape' => 'AppStatus', ], 'createdAt' => [ 'shape' => 'QAppsTimestamp', ], 'createdBy' => [ 'shape' => 'String', ], 'updatedAt' => [ 'shape' => 'QAppsTimestamp', ], 'updatedBy' => [ 'shape' => 'String', ], 'requiredCapabilities' => [ 'shape' => 'AppRequiredCapabilities', ], ], ], 'UpdateQAppPermissionsInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'appId', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'appId' => [ 'shape' => 'UUID', ], 'grantPermissions' => [ 'shape' => 'UpdateQAppPermissionsInputGrantPermissionsList', ], 'revokePermissions' => [ 'shape' => 'UpdateQAppPermissionsInputRevokePermissionsList', ], ], ], 'UpdateQAppPermissionsInputGrantPermissionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PermissionInput', ], 'max' => 100, 'min' => 0, ], 'UpdateQAppPermissionsInputRevokePermissionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PermissionInput', ], 'max' => 100, 'min' => 0, ], 'UpdateQAppPermissionsOutput' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => 'String', ], 'appId' => [ 'shape' => 'String', ], 'permissions' => [ 'shape' => 'PermissionsOutputList', ], ], ], 'UpdateQAppSessionInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'sessionId', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'sessionId' => [ 'shape' => 'UUID', ], 'values' => [ 'shape' => 'CardValueList', ], ], ], 'UpdateQAppSessionMetadataInput' => [ 'type' => 'structure', 'required' => [ 'instanceId', 'sessionId', 'sharingConfiguration', ], 'members' => [ 'instanceId' => [ 'shape' => 'InstanceId', 'location' => 'header', 'locationName' => 'instance-id', ], 'sessionId' => [ 'shape' => 'UUID', ], 'sessionName' => [ 'shape' => 'SessionName', ], 'sharingConfiguration' => [ 'shape' => 'SessionSharingConfiguration', ], ], ], 'UpdateQAppSessionMetadataOutput' => [ 'type' => 'structure', 'required' => [ 'sessionId', 'sessionArn', 'sharingConfiguration', ], 'members' => [ 'sessionId' => [ 'shape' => 'UUID', ], 'sessionArn' => [ 'shape' => 'String', ], 'sessionName' => [ 'shape' => 'SessionName', ], 'sharingConfiguration' => [ 'shape' => 'SessionSharingConfiguration', ], ], ], 'UpdateQAppSessionOutput' => [ 'type' => 'structure', 'required' => [ 'sessionId', 'sessionArn', ], 'members' => [ 'sessionId' => [ 'shape' => 'String', ], 'sessionArn' => [ 'shape' => 'String', ], ], ], 'User' => [ 'type' => 'structure', 'members' => [ 'userId' => [ 'shape' => 'UserId', ], ], ], 'UserAppItem' => [ 'type' => 'structure', 'required' => [ 'appId', 'appArn', 'title', 'createdAt', ], 'members' => [ 'appId' => [ 'shape' => 'UUID', ], 'appArn' => [ 'shape' => 'AppArn', ], 'title' => [ 'shape' => 'Title', ], 'description' => [ 'shape' => 'Description', ], 'createdAt' => [ 'shape' => 'QAppsTimestamp', ], 'canEdit' => [ 'shape' => 'Boolean', ], 'status' => [ 'shape' => 'String', ], 'isVerified' => [ 'shape' => 'Boolean', ], ], ], 'UserAppsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserAppItem', ], ], 'UserId' => [ 'type' => 'string', ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], ],];
