<?php

namespace App\Traits\ProjectManagement;

use Illuminate\Database\Eloquent\Builder;

/**
 * Trait للتعامل مع أولويات المشاريع والمهام
 * 
 * يوفر طرق مساعدة للتعامل مع الأولويات المختلفة
 * وترتيب العناصر حسب الأولوية
 * 
 * @package App\Traits\ProjectManagement
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
trait HasPriority
{
    /**
     * الأولويات المتاحة
     * 
     * @var array
     */
    protected static $priorities = [
        'low' => [
            'label' => 'منخفضة',
            'value' => 1,
            'color' => 'green',
            'icon' => 'fas fa-arrow-down'
        ],
        'normal' => [
            'label' => 'عادية',
            'value' => 2,
            'color' => 'blue',
            'icon' => 'fas fa-minus'
        ],
        'high' => [
            'label' => 'عالية',
            'value' => 3,
            'color' => 'orange',
            'icon' => 'fas fa-arrow-up'
        ],
        'urgent' => [
            'label' => 'عاجلة',
            'value' => 4,
            'color' => 'red',
            'icon' => 'fas fa-exclamation-triangle'
        ],
        'critical' => [
            'label' => 'حرجة',
            'value' => 5,
            'color' => 'red',
            'icon' => 'fas fa-fire'
        ]
    ];

    /**
     * الحصول على قائمة الأولويات المتاحة
     * 
     * @return array
     */
    public static function getAvailablePriorities(): array
    {
        return static::$priorities;
    }

    /**
     * الحصول على قائمة الأولويات للاختيار
     * 
     * @return array
     */
    public static function getPriorityOptions(): array
    {
        $options = [];
        foreach (static::$priorities as $key => $priority) {
            $options[$key] = $priority['label'];
        }
        return $options;
    }

    /**
     * الحصول على تسمية الأولوية الحالية
     * 
     * @return string
     */
    public function getPriorityLabel(): string
    {
        $priorities = static::getAvailablePriorities();
        return $priorities[$this->priority]['label'] ?? $this->priority;
    }

    /**
     * الحصول على قيمة الأولوية الرقمية
     * 
     * @return int
     */
    public function getPriorityValue(): int
    {
        $priorities = static::getAvailablePriorities();
        return $priorities[$this->priority]['value'] ?? 0;
    }

    /**
     * الحصول على لون الأولوية
     * 
     * @return string
     */
    public function getPriorityColor(): string
    {
        $priorities = static::getAvailablePriorities();
        return $priorities[$this->priority]['color'] ?? 'gray';
    }

    /**
     * الحصول على أيقونة الأولوية
     * 
     * @return string
     */
    public function getPriorityIcon(): string
    {
        $priorities = static::getAvailablePriorities();
        return $priorities[$this->priority]['icon'] ?? 'fas fa-question-circle';
    }

    /**
     * الحصول على شارة الأولوية HTML
     * 
     * @return string
     */
    public function getPriorityBadge(): string
    {
        $label = $this->getPriorityLabel();
        $color = $this->getPriorityColor();
        $icon = $this->getPriorityIcon();
        
        return "<span class=\"badge badge-{$color}\">
                    <i class=\"{$icon}\"></i> {$label}
                </span>";
    }

    /**
     * التحقق من أن الأولوية منخفضة
     * 
     * @return bool
     */
    public function isLowPriority(): bool
    {
        return $this->priority === 'low';
    }

    /**
     * التحقق من أن الأولوية عادية
     * 
     * @return bool
     */
    public function isNormalPriority(): bool
    {
        return $this->priority === 'normal';
    }

    /**
     * التحقق من أن الأولوية عالية
     * 
     * @return bool
     */
    public function isHighPriority(): bool
    {
        return $this->priority === 'high';
    }

    /**
     * التحقق من أن الأولوية عاجلة
     * 
     * @return bool
     */
    public function isUrgentPriority(): bool
    {
        return $this->priority === 'urgent';
    }

    /**
     * التحقق من أن الأولوية حرجة
     * 
     * @return bool
     */
    public function isCriticalPriority(): bool
    {
        return $this->priority === 'critical';
    }

    /**
     * التحقق من أن الأولوية عالية أو أعلى
     * 
     * @return bool
     */
    public function isHighOrAbove(): bool
    {
        return in_array($this->priority, ['high', 'urgent', 'critical']);
    }

    /**
     * التحقق من أن الأولوية عاجلة أو حرجة
     * 
     * @return bool
     */
    public function isUrgentOrCritical(): bool
    {
        return in_array($this->priority, ['urgent', 'critical']);
    }

    /**
     * تغيير الأولوية
     * 
     * @param string $newPriority الأولوية الجديدة
     * @param string|null $reason سبب التغيير
     * @return bool
     */
    public function changePriority(string $newPriority, ?string $reason = null): bool
    {
        $availablePriorities = array_keys(static::getAvailablePriorities());
        
        if (!in_array($newPriority, $availablePriorities)) {
            return false;
        }

        $oldPriority = $this->priority;
        $this->priority = $newPriority;
        $saved = $this->save();

        // تسجيل تغيير الأولوية في سجل الأنشطة
        if ($saved && method_exists($this, 'logActivity')) {
            $this->logActivity('priority_changed', [
                'old_priority' => $oldPriority,
                'new_priority' => $newPriority,
                'reason' => $reason
            ]);
        }

        return $saved;
    }

    /**
     * رفع الأولوية درجة واحدة
     * 
     * @return bool
     */
    public function increasePriority(): bool
    {
        $priorities = ['low', 'normal', 'high', 'urgent', 'critical'];
        $currentIndex = array_search($this->priority, $priorities);
        
        if ($currentIndex !== false && $currentIndex < count($priorities) - 1) {
            return $this->changePriority($priorities[$currentIndex + 1], 'Priority increased');
        }
        
        return false;
    }

    /**
     * خفض الأولوية درجة واحدة
     * 
     * @return bool
     */
    public function decreasePriority(): bool
    {
        $priorities = ['low', 'normal', 'high', 'urgent', 'critical'];
        $currentIndex = array_search($this->priority, $priorities);
        
        if ($currentIndex !== false && $currentIndex > 0) {
            return $this->changePriority($priorities[$currentIndex - 1], 'Priority decreased');
        }
        
        return false;
    }

    /**
     * scope للأولوية المنخفضة
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeLowPriority(Builder $query): Builder
    {
        return $query->where('priority', 'low');
    }

    /**
     * scope للأولوية العادية
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeNormalPriority(Builder $query): Builder
    {
        return $query->where('priority', 'normal');
    }

    /**
     * scope للأولوية العالية
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeHighPriority(Builder $query): Builder
    {
        return $query->where('priority', 'high');
    }

    /**
     * scope للأولوية العاجلة
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeUrgentPriority(Builder $query): Builder
    {
        return $query->where('priority', 'urgent');
    }

    /**
     * scope للأولوية الحرجة
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeCriticalPriority(Builder $query): Builder
    {
        return $query->where('priority', 'critical');
    }

    /**
     * scope للأولوية العالية أو أعلى
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeHighOrAbove(Builder $query): Builder
    {
        return $query->whereIn('priority', ['high', 'urgent', 'critical']);
    }

    /**
     * scope للأولوية العاجلة أو الحرجة
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeUrgentOrCritical(Builder $query): Builder
    {
        return $query->whereIn('priority', ['urgent', 'critical']);
    }

    /**
     * scope لأولوية معينة
     * 
     * @param Builder $query
     * @param string $priority
     * @return Builder
     */
    public function scopeWithPriority(Builder $query, string $priority): Builder
    {
        return $query->where('priority', $priority);
    }

    /**
     * scope لأولويات متعددة
     * 
     * @param Builder $query
     * @param array $priorities
     * @return Builder
     */
    public function scopeWithPriorities(Builder $query, array $priorities): Builder
    {
        return $query->whereIn('priority', $priorities);
    }

    /**
     * scope للترتيب حسب الأولوية (الأعلى أولاً)
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeOrderByPriority(Builder $query): Builder
    {
        $priorityOrder = "CASE priority 
                         WHEN 'critical' THEN 5
                         WHEN 'urgent' THEN 4
                         WHEN 'high' THEN 3
                         WHEN 'normal' THEN 2
                         WHEN 'low' THEN 1
                         ELSE 0 END";
        
        return $query->orderByRaw("{$priorityOrder} DESC");
    }

    /**
     * scope للترتيب حسب الأولوية (الأقل أولاً)
     * 
     * @param Builder $query
     * @return Builder
     */
    public function scopeOrderByPriorityAsc(Builder $query): Builder
    {
        $priorityOrder = "CASE priority 
                         WHEN 'critical' THEN 5
                         WHEN 'urgent' THEN 4
                         WHEN 'high' THEN 3
                         WHEN 'normal' THEN 2
                         WHEN 'low' THEN 1
                         ELSE 0 END";
        
        return $query->orderByRaw("{$priorityOrder} ASC");
    }
}
