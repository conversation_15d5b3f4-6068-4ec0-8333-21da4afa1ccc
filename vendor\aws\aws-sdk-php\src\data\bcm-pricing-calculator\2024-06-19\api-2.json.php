<?php
// This file was auto-generated from sdk-root/src/data/bcm-pricing-calculator/2024-06-19/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2024-06-19', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'bcm-pricing-calculator', 'jsonVersion' => '1.0', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'AWS Billing and Cost Management Pricing Calculator', 'serviceId' => 'BCM Pricing Calculator', 'signatureVersion' => 'v4', 'signingName' => 'bcm-pricing-calculator', 'targetPrefix' => 'AWSBCMPricingCalculator', 'uid' => 'bcm-pricing-calculator-2024-06-19', ], 'operations' => [ 'BatchCreateBillScenarioCommitmentModification' => [ 'name' => 'BatchCreateBillScenarioCommitmentModification', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchCreateBillScenarioCommitmentModificationRequest', ], 'output' => [ 'shape' => 'BatchCreateBillScenarioCommitmentModificationResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'BatchCreateBillScenarioUsageModification' => [ 'name' => 'BatchCreateBillScenarioUsageModification', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchCreateBillScenarioUsageModificationRequest', ], 'output' => [ 'shape' => 'BatchCreateBillScenarioUsageModificationResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'BatchCreateWorkloadEstimateUsage' => [ 'name' => 'BatchCreateWorkloadEstimateUsage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchCreateWorkloadEstimateUsageRequest', ], 'output' => [ 'shape' => 'BatchCreateWorkloadEstimateUsageResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'BatchDeleteBillScenarioCommitmentModification' => [ 'name' => 'BatchDeleteBillScenarioCommitmentModification', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDeleteBillScenarioCommitmentModificationRequest', ], 'output' => [ 'shape' => 'BatchDeleteBillScenarioCommitmentModificationResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'BatchDeleteBillScenarioUsageModification' => [ 'name' => 'BatchDeleteBillScenarioUsageModification', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDeleteBillScenarioUsageModificationRequest', ], 'output' => [ 'shape' => 'BatchDeleteBillScenarioUsageModificationResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'BatchDeleteWorkloadEstimateUsage' => [ 'name' => 'BatchDeleteWorkloadEstimateUsage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDeleteWorkloadEstimateUsageRequest', ], 'output' => [ 'shape' => 'BatchDeleteWorkloadEstimateUsageResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'BatchUpdateBillScenarioCommitmentModification' => [ 'name' => 'BatchUpdateBillScenarioCommitmentModification', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchUpdateBillScenarioCommitmentModificationRequest', ], 'output' => [ 'shape' => 'BatchUpdateBillScenarioCommitmentModificationResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'BatchUpdateBillScenarioUsageModification' => [ 'name' => 'BatchUpdateBillScenarioUsageModification', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchUpdateBillScenarioUsageModificationRequest', ], 'output' => [ 'shape' => 'BatchUpdateBillScenarioUsageModificationResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'BatchUpdateWorkloadEstimateUsage' => [ 'name' => 'BatchUpdateWorkloadEstimateUsage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchUpdateWorkloadEstimateUsageRequest', ], 'output' => [ 'shape' => 'BatchUpdateWorkloadEstimateUsageResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'CreateBillEstimate' => [ 'name' => 'CreateBillEstimate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateBillEstimateRequest', ], 'output' => [ 'shape' => 'CreateBillEstimateResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'CreateBillScenario' => [ 'name' => 'CreateBillScenario', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateBillScenarioRequest', ], 'output' => [ 'shape' => 'CreateBillScenarioResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'CreateWorkloadEstimate' => [ 'name' => 'CreateWorkloadEstimate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateWorkloadEstimateRequest', ], 'output' => [ 'shape' => 'CreateWorkloadEstimateResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteBillEstimate' => [ 'name' => 'DeleteBillEstimate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteBillEstimateRequest', ], 'output' => [ 'shape' => 'DeleteBillEstimateResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteBillScenario' => [ 'name' => 'DeleteBillScenario', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteBillScenarioRequest', ], 'output' => [ 'shape' => 'DeleteBillScenarioResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteWorkloadEstimate' => [ 'name' => 'DeleteWorkloadEstimate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteWorkloadEstimateRequest', ], 'output' => [ 'shape' => 'DeleteWorkloadEstimateResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'GetBillEstimate' => [ 'name' => 'GetBillEstimate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBillEstimateRequest', ], 'output' => [ 'shape' => 'GetBillEstimateResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetBillScenario' => [ 'name' => 'GetBillScenario', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBillScenarioRequest', ], 'output' => [ 'shape' => 'GetBillScenarioResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetPreferences' => [ 'name' => 'GetPreferences', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPreferencesRequest', ], 'output' => [ 'shape' => 'GetPreferencesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetWorkloadEstimate' => [ 'name' => 'GetWorkloadEstimate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetWorkloadEstimateRequest', ], 'output' => [ 'shape' => 'GetWorkloadEstimateResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListBillEstimateCommitments' => [ 'name' => 'ListBillEstimateCommitments', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBillEstimateCommitmentsRequest', ], 'output' => [ 'shape' => 'ListBillEstimateCommitmentsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListBillEstimateInputCommitmentModifications' => [ 'name' => 'ListBillEstimateInputCommitmentModifications', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBillEstimateInputCommitmentModificationsRequest', ], 'output' => [ 'shape' => 'ListBillEstimateInputCommitmentModificationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListBillEstimateInputUsageModifications' => [ 'name' => 'ListBillEstimateInputUsageModifications', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBillEstimateInputUsageModificationsRequest', ], 'output' => [ 'shape' => 'ListBillEstimateInputUsageModificationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListBillEstimateLineItems' => [ 'name' => 'ListBillEstimateLineItems', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBillEstimateLineItemsRequest', ], 'output' => [ 'shape' => 'ListBillEstimateLineItemsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListBillEstimates' => [ 'name' => 'ListBillEstimates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBillEstimatesRequest', ], 'output' => [ 'shape' => 'ListBillEstimatesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListBillScenarioCommitmentModifications' => [ 'name' => 'ListBillScenarioCommitmentModifications', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBillScenarioCommitmentModificationsRequest', ], 'output' => [ 'shape' => 'ListBillScenarioCommitmentModificationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListBillScenarioUsageModifications' => [ 'name' => 'ListBillScenarioUsageModifications', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBillScenarioUsageModificationsRequest', ], 'output' => [ 'shape' => 'ListBillScenarioUsageModificationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListBillScenarios' => [ 'name' => 'ListBillScenarios', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBillScenariosRequest', ], 'output' => [ 'shape' => 'ListBillScenariosResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListWorkloadEstimateUsage' => [ 'name' => 'ListWorkloadEstimateUsage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListWorkloadEstimateUsageRequest', ], 'output' => [ 'shape' => 'ListWorkloadEstimateUsageResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListWorkloadEstimates' => [ 'name' => 'ListWorkloadEstimates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListWorkloadEstimatesRequest', ], 'output' => [ 'shape' => 'ListWorkloadEstimatesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateBillEstimate' => [ 'name' => 'UpdateBillEstimate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateBillEstimateRequest', ], 'output' => [ 'shape' => 'UpdateBillEstimateResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdateBillScenario' => [ 'name' => 'UpdateBillScenario', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateBillScenarioRequest', ], 'output' => [ 'shape' => 'UpdateBillScenarioResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdatePreferences' => [ 'name' => 'UpdatePreferences', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePreferencesRequest', ], 'output' => [ 'shape' => 'UpdatePreferencesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdateWorkloadEstimate' => [ 'name' => 'UpdateWorkloadEstimate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateWorkloadEstimateRequest', ], 'output' => [ 'shape' => 'UpdateWorkloadEstimateResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DataUnavailableException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'AccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '\\d{12}', ], 'AddReservedInstanceAction' => [ 'type' => 'structure', 'members' => [ 'reservedInstancesOfferingId' => [ 'shape' => 'Uuid', ], 'instanceCount' => [ 'shape' => 'ReservedInstanceInstanceCount', ], ], ], 'AddSavingsPlanAction' => [ 'type' => 'structure', 'members' => [ 'savingsPlanOfferingId' => [ 'shape' => 'Uuid', ], 'commitment' => [ 'shape' => 'SavingsPlanCommitment', ], ], ], 'Arn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[-a-z0-9]*:bcm-pricing-calculator:[-a-z0-9]*:[0-9]{12}:[-a-z0-9/:_]+', ], 'AvailabilityZone' => [ 'type' => 'string', 'max' => 32, 'min' => 0, 'pattern' => '[-a-zA-Z0-9\\.\\-_:, \\/()]*', ], 'BatchCreateBillScenarioCommitmentModificationEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchCreateBillScenarioCommitmentModificationEntry', ], 'max' => 25, 'min' => 1, ], 'BatchCreateBillScenarioCommitmentModificationEntry' => [ 'type' => 'structure', 'required' => [ 'key', 'usageAccountId', 'commitmentAction', ], 'members' => [ 'key' => [ 'shape' => 'Key', ], 'group' => [ 'shape' => 'UsageGroup', ], 'usageAccountId' => [ 'shape' => 'AccountId', ], 'commitmentAction' => [ 'shape' => 'BillScenarioCommitmentModificationAction', ], ], ], 'BatchCreateBillScenarioCommitmentModificationError' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'Key', ], 'errorMessage' => [ 'shape' => 'String', ], 'errorCode' => [ 'shape' => 'BatchCreateBillScenarioCommitmentModificationErrorCode', ], ], ], 'BatchCreateBillScenarioCommitmentModificationErrorCode' => [ 'type' => 'string', 'enum' => [ 'CONFLICT', 'INTERNAL_SERVER_ERROR', 'INVALID_ACCOUNT', ], ], 'BatchCreateBillScenarioCommitmentModificationErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchCreateBillScenarioCommitmentModificationError', ], ], 'BatchCreateBillScenarioCommitmentModificationItem' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'Key', ], 'id' => [ 'shape' => 'ResourceId', ], 'group' => [ 'shape' => 'UsageGroup', ], 'usageAccountId' => [ 'shape' => 'AccountId', ], 'commitmentAction' => [ 'shape' => 'BillScenarioCommitmentModificationAction', ], ], ], 'BatchCreateBillScenarioCommitmentModificationItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchCreateBillScenarioCommitmentModificationItem', ], ], 'BatchCreateBillScenarioCommitmentModificationRequest' => [ 'type' => 'structure', 'required' => [ 'billScenarioId', 'commitmentModifications', ], 'members' => [ 'billScenarioId' => [ 'shape' => 'ResourceId', ], 'commitmentModifications' => [ 'shape' => 'BatchCreateBillScenarioCommitmentModificationEntries', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'BatchCreateBillScenarioCommitmentModificationResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'BatchCreateBillScenarioCommitmentModificationItems', ], 'errors' => [ 'shape' => 'BatchCreateBillScenarioCommitmentModificationErrors', ], ], ], 'BatchCreateBillScenarioUsageModificationEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchCreateBillScenarioUsageModificationEntry', ], 'max' => 25, 'min' => 1, ], 'BatchCreateBillScenarioUsageModificationEntry' => [ 'type' => 'structure', 'required' => [ 'serviceCode', 'usageType', 'operation', 'key', 'usageAccountId', ], 'members' => [ 'serviceCode' => [ 'shape' => 'ServiceCode', ], 'usageType' => [ 'shape' => 'UsageType', ], 'operation' => [ 'shape' => 'Operation', ], 'availabilityZone' => [ 'shape' => 'AvailabilityZone', ], 'key' => [ 'shape' => 'Key', ], 'group' => [ 'shape' => 'UsageGroup', ], 'usageAccountId' => [ 'shape' => 'AccountId', ], 'amounts' => [ 'shape' => 'UsageAmounts', ], 'historicalUsage' => [ 'shape' => 'HistoricalUsageEntity', ], ], ], 'BatchCreateBillScenarioUsageModificationError' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'Key', ], 'errorMessage' => [ 'shape' => 'String', ], 'errorCode' => [ 'shape' => 'BatchCreateBillScenarioUsageModificationErrorCode', ], ], ], 'BatchCreateBillScenarioUsageModificationErrorCode' => [ 'type' => 'string', 'enum' => [ 'BAD_REQUEST', 'NOT_FOUND', 'CONFLICT', 'INTERNAL_SERVER_ERROR', ], ], 'BatchCreateBillScenarioUsageModificationErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchCreateBillScenarioUsageModificationError', ], ], 'BatchCreateBillScenarioUsageModificationItem' => [ 'type' => 'structure', 'required' => [ 'serviceCode', 'usageType', 'operation', ], 'members' => [ 'serviceCode' => [ 'shape' => 'ServiceCode', ], 'usageType' => [ 'shape' => 'UsageType', ], 'operation' => [ 'shape' => 'Operation', ], 'location' => [ 'shape' => 'String', ], 'availabilityZone' => [ 'shape' => 'AvailabilityZone', ], 'id' => [ 'shape' => 'ResourceId', ], 'group' => [ 'shape' => 'UsageGroup', ], 'usageAccountId' => [ 'shape' => 'AccountId', ], 'quantities' => [ 'shape' => 'UsageQuantities', ], 'historicalUsage' => [ 'shape' => 'HistoricalUsageEntity', ], 'key' => [ 'shape' => 'Key', ], ], ], 'BatchCreateBillScenarioUsageModificationItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchCreateBillScenarioUsageModificationItem', ], ], 'BatchCreateBillScenarioUsageModificationRequest' => [ 'type' => 'structure', 'required' => [ 'billScenarioId', 'usageModifications', ], 'members' => [ 'billScenarioId' => [ 'shape' => 'ResourceId', ], 'usageModifications' => [ 'shape' => 'BatchCreateBillScenarioUsageModificationEntries', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'BatchCreateBillScenarioUsageModificationResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'BatchCreateBillScenarioUsageModificationItems', ], 'errors' => [ 'shape' => 'BatchCreateBillScenarioUsageModificationErrors', ], ], ], 'BatchCreateWorkloadEstimateUsageCode' => [ 'type' => 'string', 'enum' => [ 'BAD_REQUEST', 'NOT_FOUND', 'CONFLICT', 'INTERNAL_SERVER_ERROR', ], ], 'BatchCreateWorkloadEstimateUsageEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchCreateWorkloadEstimateUsageEntry', ], 'max' => 25, 'min' => 1, ], 'BatchCreateWorkloadEstimateUsageEntry' => [ 'type' => 'structure', 'required' => [ 'serviceCode', 'usageType', 'operation', 'key', 'usageAccountId', 'amount', ], 'members' => [ 'serviceCode' => [ 'shape' => 'ServiceCode', ], 'usageType' => [ 'shape' => 'UsageType', ], 'operation' => [ 'shape' => 'Operation', ], 'key' => [ 'shape' => 'Key', ], 'group' => [ 'shape' => 'UsageGroup', ], 'usageAccountId' => [ 'shape' => 'AccountId', ], 'amount' => [ 'shape' => 'Double', ], 'historicalUsage' => [ 'shape' => 'HistoricalUsageEntity', ], ], ], 'BatchCreateWorkloadEstimateUsageError' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'Key', ], 'errorCode' => [ 'shape' => 'BatchCreateWorkloadEstimateUsageCode', ], 'errorMessage' => [ 'shape' => 'String', ], ], ], 'BatchCreateWorkloadEstimateUsageErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchCreateWorkloadEstimateUsageError', ], ], 'BatchCreateWorkloadEstimateUsageItem' => [ 'type' => 'structure', 'required' => [ 'serviceCode', 'usageType', 'operation', ], 'members' => [ 'serviceCode' => [ 'shape' => 'ServiceCode', ], 'usageType' => [ 'shape' => 'UsageType', ], 'operation' => [ 'shape' => 'Operation', ], 'location' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'ResourceId', ], 'usageAccountId' => [ 'shape' => 'AccountId', ], 'group' => [ 'shape' => 'UsageGroup', ], 'quantity' => [ 'shape' => 'WorkloadEstimateUsageQuantity', ], 'cost' => [ 'shape' => 'Double', ], 'currency' => [ 'shape' => 'CurrencyCode', ], 'status' => [ 'shape' => 'WorkloadEstimateCostStatus', ], 'historicalUsage' => [ 'shape' => 'HistoricalUsageEntity', ], 'key' => [ 'shape' => 'Key', ], ], ], 'BatchCreateWorkloadEstimateUsageItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchCreateWorkloadEstimateUsageItem', ], ], 'BatchCreateWorkloadEstimateUsageRequest' => [ 'type' => 'structure', 'required' => [ 'workloadEstimateId', 'usage', ], 'members' => [ 'workloadEstimateId' => [ 'shape' => 'ResourceId', ], 'usage' => [ 'shape' => 'BatchCreateWorkloadEstimateUsageEntries', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'BatchCreateWorkloadEstimateUsageResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'BatchCreateWorkloadEstimateUsageItems', ], 'errors' => [ 'shape' => 'BatchCreateWorkloadEstimateUsageErrors', ], ], ], 'BatchDeleteBillScenarioCommitmentModificationEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceId', ], 'max' => 25, 'min' => 1, ], 'BatchDeleteBillScenarioCommitmentModificationError' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'errorCode' => [ 'shape' => 'BatchDeleteBillScenarioCommitmentModificationErrorCode', ], 'errorMessage' => [ 'shape' => 'String', ], ], ], 'BatchDeleteBillScenarioCommitmentModificationErrorCode' => [ 'type' => 'string', 'enum' => [ 'BAD_REQUEST', 'CONFLICT', 'INTERNAL_SERVER_ERROR', ], ], 'BatchDeleteBillScenarioCommitmentModificationErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchDeleteBillScenarioCommitmentModificationError', ], ], 'BatchDeleteBillScenarioCommitmentModificationRequest' => [ 'type' => 'structure', 'required' => [ 'billScenarioId', 'ids', ], 'members' => [ 'billScenarioId' => [ 'shape' => 'ResourceId', ], 'ids' => [ 'shape' => 'BatchDeleteBillScenarioCommitmentModificationEntries', ], ], ], 'BatchDeleteBillScenarioCommitmentModificationResponse' => [ 'type' => 'structure', 'members' => [ 'errors' => [ 'shape' => 'BatchDeleteBillScenarioCommitmentModificationErrors', ], ], ], 'BatchDeleteBillScenarioUsageModificationEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceId', ], 'max' => 25, 'min' => 1, ], 'BatchDeleteBillScenarioUsageModificationError' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'errorMessage' => [ 'shape' => 'String', ], 'errorCode' => [ 'shape' => 'BatchDeleteBillScenarioUsageModificationErrorCode', ], ], ], 'BatchDeleteBillScenarioUsageModificationErrorCode' => [ 'type' => 'string', 'enum' => [ 'BAD_REQUEST', 'CONFLICT', 'INTERNAL_SERVER_ERROR', ], ], 'BatchDeleteBillScenarioUsageModificationErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchDeleteBillScenarioUsageModificationError', ], ], 'BatchDeleteBillScenarioUsageModificationRequest' => [ 'type' => 'structure', 'required' => [ 'billScenarioId', 'ids', ], 'members' => [ 'billScenarioId' => [ 'shape' => 'ResourceId', ], 'ids' => [ 'shape' => 'BatchDeleteBillScenarioUsageModificationEntries', ], ], ], 'BatchDeleteBillScenarioUsageModificationResponse' => [ 'type' => 'structure', 'members' => [ 'errors' => [ 'shape' => 'BatchDeleteBillScenarioUsageModificationErrors', ], ], ], 'BatchDeleteWorkloadEstimateUsageEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceId', ], 'max' => 25, 'min' => 1, ], 'BatchDeleteWorkloadEstimateUsageError' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'errorMessage' => [ 'shape' => 'String', ], 'errorCode' => [ 'shape' => 'WorkloadEstimateUpdateUsageErrorCode', ], ], ], 'BatchDeleteWorkloadEstimateUsageErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchDeleteWorkloadEstimateUsageError', ], ], 'BatchDeleteWorkloadEstimateUsageRequest' => [ 'type' => 'structure', 'required' => [ 'workloadEstimateId', 'ids', ], 'members' => [ 'workloadEstimateId' => [ 'shape' => 'ResourceId', ], 'ids' => [ 'shape' => 'BatchDeleteWorkloadEstimateUsageEntries', ], ], ], 'BatchDeleteWorkloadEstimateUsageResponse' => [ 'type' => 'structure', 'members' => [ 'errors' => [ 'shape' => 'BatchDeleteWorkloadEstimateUsageErrors', ], ], ], 'BatchUpdateBillScenarioCommitmentModificationEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchUpdateBillScenarioCommitmentModificationEntry', ], 'max' => 25, 'min' => 1, ], 'BatchUpdateBillScenarioCommitmentModificationEntry' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'group' => [ 'shape' => 'UsageGroup', ], ], ], 'BatchUpdateBillScenarioCommitmentModificationError' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'errorCode' => [ 'shape' => 'BatchUpdateBillScenarioCommitmentModificationErrorCode', ], 'errorMessage' => [ 'shape' => 'String', ], ], ], 'BatchUpdateBillScenarioCommitmentModificationErrorCode' => [ 'type' => 'string', 'enum' => [ 'BAD_REQUEST', 'NOT_FOUND', 'CONFLICT', 'INTERNAL_SERVER_ERROR', ], ], 'BatchUpdateBillScenarioCommitmentModificationErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchUpdateBillScenarioCommitmentModificationError', ], ], 'BatchUpdateBillScenarioCommitmentModificationRequest' => [ 'type' => 'structure', 'required' => [ 'billScenarioId', 'commitmentModifications', ], 'members' => [ 'billScenarioId' => [ 'shape' => 'ResourceId', ], 'commitmentModifications' => [ 'shape' => 'BatchUpdateBillScenarioCommitmentModificationEntries', ], ], ], 'BatchUpdateBillScenarioCommitmentModificationResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'BillScenarioCommitmentModificationItems', ], 'errors' => [ 'shape' => 'BatchUpdateBillScenarioCommitmentModificationErrors', ], ], ], 'BatchUpdateBillScenarioUsageModificationEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchUpdateBillScenarioUsageModificationEntry', ], 'max' => 25, 'min' => 1, ], 'BatchUpdateBillScenarioUsageModificationEntry' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'group' => [ 'shape' => 'UsageGroup', ], 'amounts' => [ 'shape' => 'UsageAmounts', ], ], ], 'BatchUpdateBillScenarioUsageModificationError' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'errorMessage' => [ 'shape' => 'String', ], 'errorCode' => [ 'shape' => 'BatchUpdateBillScenarioUsageModificationErrorCode', ], ], ], 'BatchUpdateBillScenarioUsageModificationErrorCode' => [ 'type' => 'string', 'enum' => [ 'BAD_REQUEST', 'NOT_FOUND', 'CONFLICT', 'INTERNAL_SERVER_ERROR', ], ], 'BatchUpdateBillScenarioUsageModificationErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchUpdateBillScenarioUsageModificationError', ], ], 'BatchUpdateBillScenarioUsageModificationRequest' => [ 'type' => 'structure', 'required' => [ 'billScenarioId', 'usageModifications', ], 'members' => [ 'billScenarioId' => [ 'shape' => 'ResourceId', ], 'usageModifications' => [ 'shape' => 'BatchUpdateBillScenarioUsageModificationEntries', ], ], ], 'BatchUpdateBillScenarioUsageModificationResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'BillScenarioUsageModificationItems', ], 'errors' => [ 'shape' => 'BatchUpdateBillScenarioUsageModificationErrors', ], ], ], 'BatchUpdateWorkloadEstimateUsageEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchUpdateWorkloadEstimateUsageEntry', ], 'max' => 25, 'min' => 1, ], 'BatchUpdateWorkloadEstimateUsageEntry' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'group' => [ 'shape' => 'UsageGroup', ], 'amount' => [ 'shape' => 'Double', ], ], ], 'BatchUpdateWorkloadEstimateUsageError' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'errorMessage' => [ 'shape' => 'String', ], 'errorCode' => [ 'shape' => 'WorkloadEstimateUpdateUsageErrorCode', ], ], ], 'BatchUpdateWorkloadEstimateUsageErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchUpdateWorkloadEstimateUsageError', ], ], 'BatchUpdateWorkloadEstimateUsageRequest' => [ 'type' => 'structure', 'required' => [ 'workloadEstimateId', 'usage', ], 'members' => [ 'workloadEstimateId' => [ 'shape' => 'ResourceId', ], 'usage' => [ 'shape' => 'BatchUpdateWorkloadEstimateUsageEntries', ], ], ], 'BatchUpdateWorkloadEstimateUsageResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'WorkloadEstimateUsageItems', ], 'errors' => [ 'shape' => 'BatchUpdateWorkloadEstimateUsageErrors', ], ], ], 'BillEstimateCommitmentSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BillEstimateCommitmentSummary', ], ], 'BillEstimateCommitmentSummary' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'purchaseAgreementType' => [ 'shape' => 'PurchaseAgreementType', ], 'offeringId' => [ 'shape' => 'Uuid', ], 'usageAccountId' => [ 'shape' => 'AccountId', ], 'region' => [ 'shape' => 'String', ], 'termLength' => [ 'shape' => 'String', ], 'paymentOption' => [ 'shape' => 'String', ], 'upfrontPayment' => [ 'shape' => 'CostAmount', ], 'monthlyPayment' => [ 'shape' => 'CostAmount', ], ], ], 'BillEstimateCostSummary' => [ 'type' => 'structure', 'members' => [ 'totalCostDifference' => [ 'shape' => 'CostDifference', ], 'serviceCostDifferences' => [ 'shape' => 'ServiceCostDifferenceMap', ], ], ], 'BillEstimateInputCommitmentModificationSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BillEstimateInputCommitmentModificationSummary', ], ], 'BillEstimateInputCommitmentModificationSummary' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'group' => [ 'shape' => 'UsageGroup', ], 'usageAccountId' => [ 'shape' => 'AccountId', ], 'commitmentAction' => [ 'shape' => 'BillScenarioCommitmentModificationAction', ], ], ], 'BillEstimateInputUsageModificationSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BillEstimateInputUsageModificationSummary', ], ], 'BillEstimateInputUsageModificationSummary' => [ 'type' => 'structure', 'required' => [ 'serviceCode', 'usageType', 'operation', ], 'members' => [ 'serviceCode' => [ 'shape' => 'ServiceCode', ], 'usageType' => [ 'shape' => 'UsageType', ], 'operation' => [ 'shape' => 'Operation', ], 'location' => [ 'shape' => 'String', ], 'availabilityZone' => [ 'shape' => 'AvailabilityZone', ], 'id' => [ 'shape' => 'ResourceId', ], 'group' => [ 'shape' => 'UsageGroup', ], 'usageAccountId' => [ 'shape' => 'AccountId', ], 'quantities' => [ 'shape' => 'UsageQuantities', ], 'historicalUsage' => [ 'shape' => 'HistoricalUsageEntity', ], ], ], 'BillEstimateLineItemSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BillEstimateLineItemSummary', ], ], 'BillEstimateLineItemSummary' => [ 'type' => 'structure', 'required' => [ 'serviceCode', 'usageType', 'operation', ], 'members' => [ 'serviceCode' => [ 'shape' => 'ServiceCode', ], 'usageType' => [ 'shape' => 'UsageType', ], 'operation' => [ 'shape' => 'Operation', ], 'location' => [ 'shape' => 'String', ], 'availabilityZone' => [ 'shape' => 'AvailabilityZone', ], 'id' => [ 'shape' => 'ResourceId', ], 'lineItemId' => [ 'shape' => 'String', ], 'lineItemType' => [ 'shape' => 'String', ], 'payerAccountId' => [ 'shape' => 'AccountId', ], 'usageAccountId' => [ 'shape' => 'AccountId', ], 'estimatedUsageQuantity' => [ 'shape' => 'UsageQuantityResult', ], 'estimatedCost' => [ 'shape' => 'CostAmount', ], 'historicalUsageQuantity' => [ 'shape' => 'UsageQuantityResult', ], 'historicalCost' => [ 'shape' => 'CostAmount', ], 'savingsPlanArns' => [ 'shape' => 'SavingsPlanArns', ], ], ], 'BillEstimateName' => [ 'type' => 'string', 'max' => 64, 'min' => 0, 'pattern' => '[a-zA-Z0-9-]+', ], 'BillEstimateStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'COMPLETE', 'FAILED', ], ], 'BillEstimateSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BillEstimateSummary', ], ], 'BillEstimateSummary' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'name' => [ 'shape' => 'BillEstimateName', ], 'status' => [ 'shape' => 'BillEstimateStatus', ], 'billInterval' => [ 'shape' => 'BillInterval', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'expiresAt' => [ 'shape' => 'Timestamp', ], ], ], 'BillInterval' => [ 'type' => 'structure', 'members' => [ 'start' => [ 'shape' => 'Timestamp', ], 'end' => [ 'shape' => 'Timestamp', ], ], ], 'BillScenarioCommitmentModificationAction' => [ 'type' => 'structure', 'members' => [ 'addReservedInstanceAction' => [ 'shape' => 'AddReservedInstanceAction', ], 'addSavingsPlanAction' => [ 'shape' => 'AddSavingsPlanAction', ], 'negateReservedInstanceAction' => [ 'shape' => 'NegateReservedInstanceAction', ], 'negateSavingsPlanAction' => [ 'shape' => 'NegateSavingsPlanAction', ], ], 'union' => true, ], 'BillScenarioCommitmentModificationItem' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'usageAccountId' => [ 'shape' => 'AccountId', ], 'group' => [ 'shape' => 'UsageGroup', ], 'commitmentAction' => [ 'shape' => 'BillScenarioCommitmentModificationAction', ], ], ], 'BillScenarioCommitmentModificationItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'BillScenarioCommitmentModificationItem', ], ], 'BillScenarioName' => [ 'type' => 'string', 'max' => 64, 'min' => 0, 'pattern' => '[a-zA-Z0-9-]+', ], 'BillScenarioStatus' => [ 'type' => 'string', 'enum' => [ 'READY', 'LOCKED', 'FAILED', 'STALE', ], ], 'BillScenarioSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BillScenarioSummary', ], ], 'BillScenarioSummary' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'name' => [ 'shape' => 'BillScenarioName', ], 'billInterval' => [ 'shape' => 'BillInterval', ], 'status' => [ 'shape' => 'BillScenarioStatus', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'expiresAt' => [ 'shape' => 'Timestamp', ], 'failureMessage' => [ 'shape' => 'String', ], ], ], 'BillScenarioUsageModificationItem' => [ 'type' => 'structure', 'required' => [ 'serviceCode', 'usageType', 'operation', ], 'members' => [ 'serviceCode' => [ 'shape' => 'ServiceCode', ], 'usageType' => [ 'shape' => 'UsageType', ], 'operation' => [ 'shape' => 'Operation', ], 'location' => [ 'shape' => 'String', ], 'availabilityZone' => [ 'shape' => 'AvailabilityZone', ], 'id' => [ 'shape' => 'ResourceId', ], 'group' => [ 'shape' => 'UsageGroup', ], 'usageAccountId' => [ 'shape' => 'AccountId', ], 'quantities' => [ 'shape' => 'UsageQuantities', ], 'historicalUsage' => [ 'shape' => 'HistoricalUsageEntity', ], ], ], 'BillScenarioUsageModificationItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'BillScenarioUsageModificationItem', ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[\\u0021-\\u007E]+', ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'CostAmount' => [ 'type' => 'structure', 'members' => [ 'amount' => [ 'shape' => 'Double', ], 'currency' => [ 'shape' => 'CurrencyCode', ], ], ], 'CostDifference' => [ 'type' => 'structure', 'members' => [ 'historicalCost' => [ 'shape' => 'CostAmount', ], 'estimatedCost' => [ 'shape' => 'CostAmount', ], ], ], 'CreateBillEstimateRequest' => [ 'type' => 'structure', 'required' => [ 'billScenarioId', 'name', ], 'members' => [ 'billScenarioId' => [ 'shape' => 'ResourceId', ], 'name' => [ 'shape' => 'BillEstimateName', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateBillEstimateResponse' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'name' => [ 'shape' => 'BillEstimateName', ], 'status' => [ 'shape' => 'BillEstimateStatus', ], 'failureMessage' => [ 'shape' => 'String', ], 'billInterval' => [ 'shape' => 'BillInterval', ], 'costSummary' => [ 'shape' => 'BillEstimateCostSummary', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'expiresAt' => [ 'shape' => 'Timestamp', ], ], ], 'CreateBillScenarioRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'BillScenarioName', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateBillScenarioResponse' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'name' => [ 'shape' => 'BillScenarioName', ], 'billInterval' => [ 'shape' => 'BillInterval', ], 'status' => [ 'shape' => 'BillScenarioStatus', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'expiresAt' => [ 'shape' => 'Timestamp', ], 'failureMessage' => [ 'shape' => 'String', ], ], ], 'CreateWorkloadEstimateRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'WorkloadEstimateName', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'rateType' => [ 'shape' => 'WorkloadEstimateRateType', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateWorkloadEstimateResponse' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'name' => [ 'shape' => 'WorkloadEstimateName', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'expiresAt' => [ 'shape' => 'Timestamp', ], 'rateType' => [ 'shape' => 'WorkloadEstimateRateType', ], 'rateTimestamp' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'WorkloadEstimateStatus', ], 'totalCost' => [ 'shape' => 'Double', ], 'costCurrency' => [ 'shape' => 'CurrencyCode', ], 'failureMessage' => [ 'shape' => 'String', ], ], ], 'CurrencyCode' => [ 'type' => 'string', 'enum' => [ 'USD', ], ], 'DataUnavailableException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'DeleteBillEstimateRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'ResourceId', ], ], ], 'DeleteBillEstimateResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteBillScenarioRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'ResourceId', ], ], ], 'DeleteBillScenarioResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWorkloadEstimateRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'ResourceId', ], ], ], 'DeleteWorkloadEstimateResponse' => [ 'type' => 'structure', 'members' => [], ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'Expression' => [ 'type' => 'structure', 'members' => [ 'and' => [ 'shape' => 'ExpressionList', ], 'or' => [ 'shape' => 'ExpressionList', ], 'not' => [ 'shape' => 'Expression', ], 'costCategories' => [ 'shape' => 'ExpressionFilter', ], 'dimensions' => [ 'shape' => 'ExpressionFilter', ], 'tags' => [ 'shape' => 'ExpressionFilter', ], ], ], 'ExpressionFilter' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'String', ], 'matchOptions' => [ 'shape' => 'StringList', ], 'values' => [ 'shape' => 'StringList', ], ], ], 'ExpressionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Expression', ], ], 'FilterTimestamp' => [ 'type' => 'structure', 'members' => [ 'afterTimestamp' => [ 'shape' => 'Timestamp', ], 'beforeTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'GetBillEstimateRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'ResourceId', ], ], ], 'GetBillEstimateResponse' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'name' => [ 'shape' => 'BillEstimateName', ], 'status' => [ 'shape' => 'BillEstimateStatus', ], 'failureMessage' => [ 'shape' => 'String', ], 'billInterval' => [ 'shape' => 'BillInterval', ], 'costSummary' => [ 'shape' => 'BillEstimateCostSummary', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'expiresAt' => [ 'shape' => 'Timestamp', ], ], ], 'GetBillScenarioRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'ResourceId', ], ], ], 'GetBillScenarioResponse' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'name' => [ 'shape' => 'BillScenarioName', ], 'billInterval' => [ 'shape' => 'BillInterval', ], 'status' => [ 'shape' => 'BillScenarioStatus', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'expiresAt' => [ 'shape' => 'Timestamp', ], 'failureMessage' => [ 'shape' => 'String', ], ], ], 'GetPreferencesRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetPreferencesResponse' => [ 'type' => 'structure', 'members' => [ 'managementAccountRateTypeSelections' => [ 'shape' => 'RateTypes', ], 'memberAccountRateTypeSelections' => [ 'shape' => 'RateTypes', ], 'standaloneAccountRateTypeSelections' => [ 'shape' => 'RateTypes', ], ], ], 'GetWorkloadEstimateRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'ResourceId', ], ], ], 'GetWorkloadEstimateResponse' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'name' => [ 'shape' => 'WorkloadEstimateName', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'expiresAt' => [ 'shape' => 'Timestamp', ], 'rateType' => [ 'shape' => 'WorkloadEstimateRateType', ], 'rateTimestamp' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'WorkloadEstimateStatus', ], 'totalCost' => [ 'shape' => 'Double', ], 'costCurrency' => [ 'shape' => 'CurrencyCode', ], 'failureMessage' => [ 'shape' => 'String', ], ], ], 'HistoricalUsageEntity' => [ 'type' => 'structure', 'required' => [ 'serviceCode', 'usageType', 'operation', 'usageAccountId', 'billInterval', 'filterExpression', ], 'members' => [ 'serviceCode' => [ 'shape' => 'ServiceCode', ], 'usageType' => [ 'shape' => 'UsageType', ], 'operation' => [ 'shape' => 'Operation', ], 'location' => [ 'shape' => 'String', ], 'usageAccountId' => [ 'shape' => 'AccountId', ], 'billInterval' => [ 'shape' => 'BillInterval', ], 'filterExpression' => [ 'shape' => 'Expression', ], ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', ], ], 'exception' => true, 'fault' => true, ], 'Key' => [ 'type' => 'string', 'max' => 10, 'min' => 0, 'pattern' => '[a-zA-Z0-9]*', ], 'ListBillEstimateCommitmentsRequest' => [ 'type' => 'structure', 'required' => [ 'billEstimateId', ], 'members' => [ 'billEstimateId' => [ 'shape' => 'ResourceId', ], 'nextToken' => [ 'shape' => 'NextPageToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListBillEstimateCommitmentsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'BillEstimateCommitmentSummaries', ], 'nextToken' => [ 'shape' => 'NextPageToken', ], ], ], 'ListBillEstimateInputCommitmentModificationsRequest' => [ 'type' => 'structure', 'required' => [ 'billEstimateId', ], 'members' => [ 'billEstimateId' => [ 'shape' => 'ResourceId', ], 'nextToken' => [ 'shape' => 'NextPageToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListBillEstimateInputCommitmentModificationsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'BillEstimateInputCommitmentModificationSummaries', ], 'nextToken' => [ 'shape' => 'NextPageToken', ], ], ], 'ListBillEstimateInputUsageModificationsRequest' => [ 'type' => 'structure', 'required' => [ 'billEstimateId', ], 'members' => [ 'billEstimateId' => [ 'shape' => 'ResourceId', ], 'filters' => [ 'shape' => 'ListUsageFilters', ], 'nextToken' => [ 'shape' => 'NextPageToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListBillEstimateInputUsageModificationsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'BillEstimateInputUsageModificationSummaries', ], 'nextToken' => [ 'shape' => 'NextPageToken', ], ], ], 'ListBillEstimateLineItemsFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'values', ], 'members' => [ 'name' => [ 'shape' => 'ListBillEstimateLineItemsFilterName', ], 'values' => [ 'shape' => 'ListBillEstimateLineItemsFilterValues', ], 'matchOption' => [ 'shape' => 'MatchOption', ], ], ], 'ListBillEstimateLineItemsFilterName' => [ 'type' => 'string', 'enum' => [ 'USAGE_ACCOUNT_ID', 'SERVICE_CODE', 'USAGE_TYPE', 'OPERATION', 'LOCATION', 'LINE_ITEM_TYPE', ], ], 'ListBillEstimateLineItemsFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ListBillEstimateLineItemsFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListBillEstimateLineItemsFilter', ], ], 'ListBillEstimateLineItemsRequest' => [ 'type' => 'structure', 'required' => [ 'billEstimateId', ], 'members' => [ 'billEstimateId' => [ 'shape' => 'ResourceId', ], 'filters' => [ 'shape' => 'ListBillEstimateLineItemsFilters', ], 'nextToken' => [ 'shape' => 'NextPageToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListBillEstimateLineItemsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'BillEstimateLineItemSummaries', ], 'nextToken' => [ 'shape' => 'NextPageToken', ], ], ], 'ListBillEstimatesFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'values', ], 'members' => [ 'name' => [ 'shape' => 'ListBillEstimatesFilterName', ], 'values' => [ 'shape' => 'ListBillEstimatesFilterValues', ], 'matchOption' => [ 'shape' => 'MatchOption', ], ], ], 'ListBillEstimatesFilterName' => [ 'type' => 'string', 'enum' => [ 'STATUS', 'NAME', ], ], 'ListBillEstimatesFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ListBillEstimatesFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListBillEstimatesFilter', ], ], 'ListBillEstimatesRequest' => [ 'type' => 'structure', 'members' => [ 'filters' => [ 'shape' => 'ListBillEstimatesFilters', ], 'createdAtFilter' => [ 'shape' => 'FilterTimestamp', ], 'expiresAtFilter' => [ 'shape' => 'FilterTimestamp', ], 'nextToken' => [ 'shape' => 'NextPageToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListBillEstimatesResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'BillEstimateSummaries', ], 'nextToken' => [ 'shape' => 'NextPageToken', ], ], ], 'ListBillScenarioCommitmentModificationsRequest' => [ 'type' => 'structure', 'required' => [ 'billScenarioId', ], 'members' => [ 'billScenarioId' => [ 'shape' => 'ResourceId', ], 'nextToken' => [ 'shape' => 'NextPageToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListBillScenarioCommitmentModificationsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'BillScenarioCommitmentModificationItems', ], 'nextToken' => [ 'shape' => 'NextPageToken', ], ], ], 'ListBillScenarioUsageModificationsRequest' => [ 'type' => 'structure', 'required' => [ 'billScenarioId', ], 'members' => [ 'billScenarioId' => [ 'shape' => 'ResourceId', ], 'filters' => [ 'shape' => 'ListUsageFilters', ], 'nextToken' => [ 'shape' => 'NextPageToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListBillScenarioUsageModificationsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'BillScenarioUsageModificationItems', ], 'nextToken' => [ 'shape' => 'NextPageToken', ], ], ], 'ListBillScenariosFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'values', ], 'members' => [ 'name' => [ 'shape' => 'ListBillScenariosFilterName', ], 'values' => [ 'shape' => 'ListBillScenariosFilterValues', ], 'matchOption' => [ 'shape' => 'MatchOption', ], ], ], 'ListBillScenariosFilterName' => [ 'type' => 'string', 'enum' => [ 'STATUS', 'NAME', ], ], 'ListBillScenariosFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ListBillScenariosFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListBillScenariosFilter', ], ], 'ListBillScenariosRequest' => [ 'type' => 'structure', 'members' => [ 'filters' => [ 'shape' => 'ListBillScenariosFilters', ], 'createdAtFilter' => [ 'shape' => 'FilterTimestamp', ], 'expiresAtFilter' => [ 'shape' => 'FilterTimestamp', ], 'nextToken' => [ 'shape' => 'NextPageToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListBillScenariosResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'BillScenarioSummaries', ], 'nextToken' => [ 'shape' => 'NextPageToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'Tags', ], ], ], 'ListUsageFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'values', ], 'members' => [ 'name' => [ 'shape' => 'ListUsageFilterName', ], 'values' => [ 'shape' => 'ListUsageFilterValues', ], 'matchOption' => [ 'shape' => 'MatchOption', ], ], ], 'ListUsageFilterName' => [ 'type' => 'string', 'enum' => [ 'USAGE_ACCOUNT_ID', 'SERVICE_CODE', 'USAGE_TYPE', 'OPERATION', 'LOCATION', 'USAGE_GROUP', 'HISTORICAL_USAGE_ACCOUNT_ID', 'HISTORICAL_SERVICE_CODE', 'HISTORICAL_USAGE_TYPE', 'HISTORICAL_OPERATION', 'HISTORICAL_LOCATION', ], ], 'ListUsageFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ListUsageFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListUsageFilter', ], ], 'ListWorkloadEstimateUsageRequest' => [ 'type' => 'structure', 'required' => [ 'workloadEstimateId', ], 'members' => [ 'workloadEstimateId' => [ 'shape' => 'ResourceId', ], 'filters' => [ 'shape' => 'ListUsageFilters', ], 'nextToken' => [ 'shape' => 'NextPageToken', ], 'maxResults' => [ 'shape' => 'WorkloadEstimateUsageMaxResults', ], ], ], 'ListWorkloadEstimateUsageResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'WorkloadEstimateUsageItems', ], 'nextToken' => [ 'shape' => 'NextPageToken', ], ], ], 'ListWorkloadEstimatesFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'values', ], 'members' => [ 'name' => [ 'shape' => 'ListWorkloadEstimatesFilterName', ], 'values' => [ 'shape' => 'ListWorkloadEstimatesFilterValues', ], 'matchOption' => [ 'shape' => 'MatchOption', ], ], ], 'ListWorkloadEstimatesFilterName' => [ 'type' => 'string', 'enum' => [ 'STATUS', 'NAME', ], ], 'ListWorkloadEstimatesFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ListWorkloadEstimatesFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListWorkloadEstimatesFilter', ], ], 'ListWorkloadEstimatesRequest' => [ 'type' => 'structure', 'members' => [ 'createdAtFilter' => [ 'shape' => 'FilterTimestamp', ], 'expiresAtFilter' => [ 'shape' => 'FilterTimestamp', ], 'filters' => [ 'shape' => 'ListWorkloadEstimatesFilters', ], 'nextToken' => [ 'shape' => 'NextPageToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListWorkloadEstimatesResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'WorkloadEstimateSummaries', ], 'nextToken' => [ 'shape' => 'NextPageToken', ], ], ], 'MatchOption' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'STARTS_WITH', 'CONTAINS', ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 1, ], 'NegateReservedInstanceAction' => [ 'type' => 'structure', 'members' => [ 'reservedInstancesId' => [ 'shape' => 'Uuid', ], ], ], 'NegateSavingsPlanAction' => [ 'type' => 'structure', 'members' => [ 'savingsPlanId' => [ 'shape' => 'Uuid', ], ], ], 'NextPageToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[\\S\\s]*', ], 'Operation' => [ 'type' => 'string', 'max' => 32, 'min' => 0, 'pattern' => '[-a-zA-Z0-9\\.\\-_:, \\/()]*', ], 'PurchaseAgreementType' => [ 'type' => 'string', 'enum' => [ 'SAVINGS_PLANS', 'RESERVED_INSTANCE', ], ], 'RateType' => [ 'type' => 'string', 'enum' => [ 'BEFORE_DISCOUNTS', 'AFTER_DISCOUNTS', 'AFTER_DISCOUNTS_AND_COMMITMENTS', ], ], 'RateTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'RateType', ], 'max' => 3, 'min' => 1, ], 'ReservedInstanceInstanceCount' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'ResourceId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourceTagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\w\\s:+=@/-]+', ], 'ResourceTagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceTagKey', ], 'max' => 200, 'min' => 0, ], 'ResourceTagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[\\w\\s:+=@/-]*', ], 'SavingsPlanArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SavingsPlanCommitment' => [ 'type' => 'double', 'box' => true, 'max' => 1000000, 'min' => 0.001, ], 'ServiceCode' => [ 'type' => 'string', 'max' => 32, 'min' => 0, 'pattern' => '[-a-zA-Z0-9\\.\\-_:, \\/()]*', ], 'ServiceCostDifferenceMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'CostDifference', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'arn', 'tags', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'ResourceTagKey', ], 'value' => [ 'shape' => 'ResourceTagValue', ], 'max' => 200, 'min' => 0, ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', ], ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'arn', 'tagKeys', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'tagKeys' => [ 'shape' => 'ResourceTagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateBillEstimateRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'ResourceId', ], 'name' => [ 'shape' => 'BillEstimateName', ], 'expiresAt' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateBillEstimateResponse' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'name' => [ 'shape' => 'BillEstimateName', ], 'status' => [ 'shape' => 'BillEstimateStatus', ], 'failureMessage' => [ 'shape' => 'String', ], 'billInterval' => [ 'shape' => 'BillInterval', ], 'costSummary' => [ 'shape' => 'BillEstimateCostSummary', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'expiresAt' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateBillScenarioRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'ResourceId', ], 'name' => [ 'shape' => 'BillScenarioName', ], 'expiresAt' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateBillScenarioResponse' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'name' => [ 'shape' => 'BillScenarioName', ], 'billInterval' => [ 'shape' => 'BillInterval', ], 'status' => [ 'shape' => 'BillScenarioStatus', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'expiresAt' => [ 'shape' => 'Timestamp', ], 'failureMessage' => [ 'shape' => 'String', ], ], ], 'UpdatePreferencesRequest' => [ 'type' => 'structure', 'members' => [ 'managementAccountRateTypeSelections' => [ 'shape' => 'RateTypes', ], 'memberAccountRateTypeSelections' => [ 'shape' => 'RateTypes', ], 'standaloneAccountRateTypeSelections' => [ 'shape' => 'RateTypes', ], ], ], 'UpdatePreferencesResponse' => [ 'type' => 'structure', 'members' => [ 'managementAccountRateTypeSelections' => [ 'shape' => 'RateTypes', ], 'memberAccountRateTypeSelections' => [ 'shape' => 'RateTypes', ], 'standaloneAccountRateTypeSelections' => [ 'shape' => 'RateTypes', ], ], ], 'UpdateWorkloadEstimateRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'ResourceId', ], 'name' => [ 'shape' => 'WorkloadEstimateName', ], 'expiresAt' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateWorkloadEstimateResponse' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'name' => [ 'shape' => 'WorkloadEstimateName', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'expiresAt' => [ 'shape' => 'Timestamp', ], 'rateType' => [ 'shape' => 'WorkloadEstimateRateType', ], 'rateTimestamp' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'WorkloadEstimateStatus', ], 'totalCost' => [ 'shape' => 'Double', ], 'costCurrency' => [ 'shape' => 'CurrencyCode', ], 'failureMessage' => [ 'shape' => 'String', ], ], ], 'UsageAmount' => [ 'type' => 'structure', 'required' => [ 'startHour', 'amount', ], 'members' => [ 'startHour' => [ 'shape' => 'Timestamp', ], 'amount' => [ 'shape' => 'Double', ], ], ], 'UsageAmounts' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageAmount', ], ], 'UsageGroup' => [ 'type' => 'string', 'max' => 30, 'min' => 0, 'pattern' => '[a-zA-Z0-9-]*', ], 'UsageQuantities' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageQuantity', ], ], 'UsageQuantity' => [ 'type' => 'structure', 'members' => [ 'startHour' => [ 'shape' => 'Timestamp', ], 'unit' => [ 'shape' => 'String', ], 'amount' => [ 'shape' => 'Double', ], ], ], 'UsageQuantityResult' => [ 'type' => 'structure', 'members' => [ 'amount' => [ 'shape' => 'Double', ], 'unit' => [ 'shape' => 'String', ], ], ], 'UsageType' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '[-a-zA-Z0-9\\.\\-_:, \\/()]*', ], 'Uuid' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'name', 'message', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'unknownOperation', 'cannotParse', 'fieldValidationFailed', 'invalidRequestFromMember', 'disallowedRate', 'other', ], ], 'WorkloadEstimateCostStatus' => [ 'type' => 'string', 'enum' => [ 'VALID', 'INVALID', 'STALE', ], ], 'WorkloadEstimateName' => [ 'type' => 'string', 'max' => 64, 'min' => 0, 'pattern' => '[a-zA-Z0-9-]+', ], 'WorkloadEstimateRateType' => [ 'type' => 'string', 'enum' => [ 'BEFORE_DISCOUNTS', 'AFTER_DISCOUNTS', 'AFTER_DISCOUNTS_AND_COMMITMENTS', ], ], 'WorkloadEstimateStatus' => [ 'type' => 'string', 'enum' => [ 'UPDATING', 'VALID', 'INVALID', 'ACTION_NEEDED', ], ], 'WorkloadEstimateSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkloadEstimateSummary', ], ], 'WorkloadEstimateSummary' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ResourceId', ], 'name' => [ 'shape' => 'WorkloadEstimateName', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'expiresAt' => [ 'shape' => 'Timestamp', ], 'rateType' => [ 'shape' => 'WorkloadEstimateRateType', ], 'rateTimestamp' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'WorkloadEstimateStatus', ], 'totalCost' => [ 'shape' => 'Double', ], 'costCurrency' => [ 'shape' => 'CurrencyCode', ], 'failureMessage' => [ 'shape' => 'String', ], ], ], 'WorkloadEstimateUpdateUsageErrorCode' => [ 'type' => 'string', 'enum' => [ 'BAD_REQUEST', 'NOT_FOUND', 'CONFLICT', 'INTERNAL_SERVER_ERROR', ], ], 'WorkloadEstimateUsageItem' => [ 'type' => 'structure', 'required' => [ 'serviceCode', 'usageType', 'operation', ], 'members' => [ 'serviceCode' => [ 'shape' => 'ServiceCode', ], 'usageType' => [ 'shape' => 'UsageType', ], 'operation' => [ 'shape' => 'Operation', ], 'location' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'ResourceId', ], 'usageAccountId' => [ 'shape' => 'AccountId', ], 'group' => [ 'shape' => 'UsageGroup', ], 'quantity' => [ 'shape' => 'WorkloadEstimateUsageQuantity', ], 'cost' => [ 'shape' => 'Double', ], 'currency' => [ 'shape' => 'CurrencyCode', ], 'status' => [ 'shape' => 'WorkloadEstimateCostStatus', ], 'historicalUsage' => [ 'shape' => 'HistoricalUsageEntity', ], ], ], 'WorkloadEstimateUsageItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkloadEstimateUsageItem', ], ], 'WorkloadEstimateUsageMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 300, 'min' => 1, ], 'WorkloadEstimateUsageQuantity' => [ 'type' => 'structure', 'members' => [ 'unit' => [ 'shape' => 'String', ], 'amount' => [ 'shape' => 'Double', ], ], ], ],];
