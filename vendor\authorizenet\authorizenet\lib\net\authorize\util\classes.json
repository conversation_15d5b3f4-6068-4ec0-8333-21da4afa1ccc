{"net\\authorize\\api\\contract\\v1\\ANetApiRequestType": {"properties": {"merchantAuthentication": {"expose": true, "access_type": "public_method", "serialized_name": "merchantAuthentication", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMerchantAuthentication", "setter": "setMerchantAuthentication"}, "type": "net\\authorize\\api\\contract\\v1\\MerchantAuthenticationType"}, "clientId": {"expose": true, "access_type": "public_method", "serialized_name": "clientId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getClientId", "setter": "setClientId"}, "type": "string"}, "refId": {"expose": true, "access_type": "public_method", "serialized_name": "refId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getRefId", "setter": "setRefId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\ANetApiResponseType": {"properties": {"refId": {"expose": true, "access_type": "public_method", "serialized_name": "refId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getRefId", "setter": "setRefId"}, "type": "string"}, "messages": {"expose": true, "access_type": "public_method", "serialized_name": "messages", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMessages", "setter": "setMessages"}, "type": "net\\authorize\\api\\contract\\v1\\MessagesType"}, "sessionToken": {"expose": true, "access_type": "public_method", "serialized_name": "sessionToken", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSessionToken", "setter": "setSessionToken"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\ARBCancelSubscriptionRequest": {"xml_root_name": "ARBCancelSubscriptionRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"subscriptionId": {"expose": true, "access_type": "public_method", "serialized_name": "subscriptionId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubscriptionId", "setter": "setSubscriptionId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\ARBCancelSubscriptionResponse": {"xml_root_name": "ARBCancelSubscriptionResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": []}, "net\\authorize\\api\\contract\\v1\\ARBCreateSubscriptionRequest": {"xml_root_name": "ARBCreateSubscriptionRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"subscription": {"expose": true, "access_type": "public_method", "serialized_name": "subscription", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubscription", "setter": "setSubscription"}, "type": "net\\authorize\\api\\contract\\v1\\ARBSubscriptionType"}}}, "net\\authorize\\api\\contract\\v1\\ARBCreateSubscriptionResponse": {"xml_root_name": "ARBCreateSubscriptionResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"subscriptionId": {"expose": true, "access_type": "public_method", "serialized_name": "subscriptionId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubscriptionId", "setter": "setSubscriptionId"}, "type": "string"}, "profile": {"expose": true, "access_type": "public_method", "serialized_name": "profile", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProfile", "setter": "setProfile"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerProfileIdType"}}}, "net\\authorize\\api\\contract\\v1\\ARBGetSubscriptionListRequest": {"xml_root_name": "ARBGetSubscriptionListRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"searchType": {"expose": true, "access_type": "public_method", "serialized_name": "searchType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSearchType", "setter": "setSearchType"}, "type": "string"}, "sorting": {"expose": true, "access_type": "public_method", "serialized_name": "sorting", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSorting", "setter": "setSorting"}, "type": "net\\authorize\\api\\contract\\v1\\ARBGetSubscriptionListSortingType"}, "paging": {"expose": true, "access_type": "public_method", "serialized_name": "paging", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaging", "setter": "setPaging"}, "type": "net\\authorize\\api\\contract\\v1\\PagingType"}}}, "net\\authorize\\api\\contract\\v1\\ARBGetSubscriptionListResponse": {"xml_root_name": "ARBGetSubscriptionListResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"totalNumInResultSet": {"expose": true, "access_type": "public_method", "serialized_name": "totalNumInResultSet", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTotalNumInResultSet", "setter": "setTotalNumInResultSet"}, "type": "integer"}, "subscriptionDetails": {"expose": true, "access_type": "public_method", "serialized_name": "subscriptionDetails", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubscriptionDetails", "setter": "setSubscriptionDetails"}, "type": "array<net\\authorize\\api\\contract\\v1\\SubscriptionDetailType>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "subscriptionDetail", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}}}, "net\\authorize\\api\\contract\\v1\\ARBGetSubscriptionListSortingType": {"properties": {"orderBy": {"expose": true, "access_type": "public_method", "serialized_name": "orderBy", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOrderBy", "setter": "setOrderBy"}, "type": "string"}, "orderDescending": {"expose": true, "access_type": "public_method", "serialized_name": "orderDescending", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOrderDescending", "setter": "setOrderDescending"}, "type": "boolean"}}}, "net\\authorize\\api\\contract\\v1\\ARBGetSubscriptionRequest": {"xml_root_name": "ARBGetSubscriptionRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"subscriptionId": {"expose": true, "access_type": "public_method", "serialized_name": "subscriptionId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubscriptionId", "setter": "setSubscriptionId"}, "type": "string"}, "includeTransactions": {"expose": true, "access_type": "public_method", "serialized_name": "includeTransactions", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getIncludeTransactions", "setter": "setIncludeTransactions"}, "type": "boolean"}}}, "net\\authorize\\api\\contract\\v1\\ARBGetSubscriptionResponse": {"xml_root_name": "ARBGetSubscriptionResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"subscription": {"expose": true, "access_type": "public_method", "serialized_name": "subscription", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubscription", "setter": "setSubscription"}, "type": "net\\authorize\\api\\contract\\v1\\ARBSubscriptionMaskedType"}}}, "net\\authorize\\api\\contract\\v1\\ARBGetSubscriptionStatusRequest": {"xml_root_name": "ARBGetSubscriptionStatusRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"subscriptionId": {"expose": true, "access_type": "public_method", "serialized_name": "subscriptionId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubscriptionId", "setter": "setSubscriptionId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\ARBGetSubscriptionStatusResponse": {"xml_root_name": "ARBGetSubscriptionStatusResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"status": {"expose": true, "access_type": "public_method", "serialized_name": "status", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getStatus", "setter": "setStatus"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\ARBSubscriptionMaskedType": {"properties": {"name": {"expose": true, "access_type": "public_method", "serialized_name": "name", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getName", "setter": "setName"}, "type": "string"}, "paymentSchedule": {"expose": true, "access_type": "public_method", "serialized_name": "paymentSchedule", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaymentSchedule", "setter": "setPaymentSchedule"}, "type": "net\\authorize\\api\\contract\\v1\\PaymentScheduleType"}, "amount": {"expose": true, "access_type": "public_method", "serialized_name": "amount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAmount", "setter": "setAmount"}, "type": "float"}, "trialAmount": {"expose": true, "access_type": "public_method", "serialized_name": "trialAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTrialAmount", "setter": "setTrialAmount"}, "type": "float"}, "status": {"expose": true, "access_type": "public_method", "serialized_name": "status", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getStatus", "setter": "setStatus"}, "type": "string"}, "profile": {"expose": true, "access_type": "public_method", "serialized_name": "profile", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProfile", "setter": "setProfile"}, "type": "net\\authorize\\api\\contract\\v1\\SubscriptionCustomerProfileType"}, "order": {"expose": true, "access_type": "public_method", "serialized_name": "order", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOrder", "setter": "setOrder"}, "type": "net\\authorize\\api\\contract\\v1\\OrderType"}, "arbTransactions": {"expose": true, "access_type": "public_method", "serialized_name": "arbTransactions", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getArbTransactions", "setter": "setArbTransactions"}, "type": "array<net\\authorize\\api\\contract\\v1\\ArbTransactionType>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "arbTransaction", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}}}, "net\\authorize\\api\\contract\\v1\\ARBSubscriptionType": {"properties": {"name": {"expose": true, "access_type": "public_method", "serialized_name": "name", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getName", "setter": "setName"}, "type": "string"}, "paymentSchedule": {"expose": true, "access_type": "public_method", "serialized_name": "paymentSchedule", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaymentSchedule", "setter": "setPaymentSchedule"}, "type": "net\\authorize\\api\\contract\\v1\\PaymentScheduleType"}, "amount": {"expose": true, "access_type": "public_method", "serialized_name": "amount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAmount", "setter": "setAmount"}, "type": "float"}, "trialAmount": {"expose": true, "access_type": "public_method", "serialized_name": "trialAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTrialAmount", "setter": "setTrialAmount"}, "type": "float"}, "payment": {"expose": true, "access_type": "public_method", "serialized_name": "payment", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPayment", "setter": "setPayment"}, "type": "net\\authorize\\api\\contract\\v1\\PaymentType"}, "order": {"expose": true, "access_type": "public_method", "serialized_name": "order", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOrder", "setter": "setOrder"}, "type": "net\\authorize\\api\\contract\\v1\\OrderType"}, "customer": {"expose": true, "access_type": "public_method", "serialized_name": "customer", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomer", "setter": "setCustomer"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerType"}, "billTo": {"expose": true, "access_type": "public_method", "serialized_name": "billTo", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getBillTo", "setter": "setBillTo"}, "type": "net\\authorize\\api\\contract\\v1\\NameAndAddressType"}, "shipTo": {"expose": true, "access_type": "public_method", "serialized_name": "shipTo", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getShipTo", "setter": "setShipTo"}, "type": "net\\authorize\\api\\contract\\v1\\NameAndAddressType"}, "profile": {"expose": true, "access_type": "public_method", "serialized_name": "profile", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProfile", "setter": "setProfile"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerProfileIdType"}}}, "net\\authorize\\api\\contract\\v1\\ARBUpdateSubscriptionRequest": {"xml_root_name": "ARBUpdateSubscriptionRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"subscriptionId": {"expose": true, "access_type": "public_method", "serialized_name": "subscriptionId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubscriptionId", "setter": "setSubscriptionId"}, "type": "string"}, "subscription": {"expose": true, "access_type": "public_method", "serialized_name": "subscription", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubscription", "setter": "setSubscription"}, "type": "net\\authorize\\api\\contract\\v1\\ARBSubscriptionType"}}}, "net\\authorize\\api\\contract\\v1\\ARBUpdateSubscriptionResponse": {"xml_root_name": "ARBUpdateSubscriptionResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"profile": {"expose": true, "access_type": "public_method", "serialized_name": "profile", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProfile", "setter": "setProfile"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerProfileIdType"}}}, "net\\authorize\\api\\contract\\v1\\ArbTransactionType": {"properties": {"transId": {"expose": true, "access_type": "public_method", "serialized_name": "transId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransId", "setter": "setTransId"}, "type": "string"}, "response": {"expose": true, "access_type": "public_method", "serialized_name": "response", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getResponse", "setter": "setResponse"}, "type": "string"}, "submitTimeUTC": {"expose": true, "access_type": "public_method", "serialized_name": "submitTimeUTC", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubmitTimeUTC", "setter": "setSubmitTimeUTC"}, "type": "GoetasWebservices\\Xsd\\XsdToPhp\\XMLSchema\\DateTime"}, "payNum": {"expose": true, "access_type": "public_method", "serialized_name": "payNum", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPayNum", "setter": "setPayNum"}, "type": "integer"}, "attemptNum": {"expose": true, "access_type": "public_method", "serialized_name": "attemptNum", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAttemptNum", "setter": "setAttemptNum"}, "type": "integer"}}}, "net\\authorize\\api\\contract\\v1\\ArrayOfSettingType": {"properties": {"setting": {"expose": true, "access_type": "public_method", "serialized_name": "setting", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSetting", "setter": "setSetting"}, "xml_list": {"inline": true, "entry_name": "setting", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "type": "array<net\\authorize\\api\\contract\\v1\\SettingType>"}}}, "net\\authorize\\api\\contract\\v1\\AuDeleteType": {"properties": {"creditCard": {"expose": true, "access_type": "public_method", "serialized_name": "creditCard", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCreditCard", "setter": "setCreditCard"}, "type": "net\\authorize\\api\\contract\\v1\\CreditCardMaskedType"}}}, "net\\authorize\\api\\contract\\v1\\AuDetailsType": {"properties": {"customerProfileID": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileID", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileID", "setter": "setCustomerProfileID"}, "type": "integer"}, "customerPaymentProfileID": {"expose": true, "access_type": "public_method", "serialized_name": "customerPaymentProfileID", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerPaymentProfileID", "setter": "setCustomerPaymentProfileID"}, "type": "integer"}, "firstName": {"expose": true, "access_type": "public_method", "serialized_name": "firstName", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getFirstName", "setter": "setFirstName"}, "type": "string"}, "lastName": {"expose": true, "access_type": "public_method", "serialized_name": "lastName", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getLastName", "setter": "setLastName"}, "type": "string"}, "updateTimeUTC": {"expose": true, "access_type": "public_method", "serialized_name": "updateTimeUTC", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getUpdateTimeUTC", "setter": "setUpdateTimeUTC"}, "type": "string"}, "auReasonCode": {"expose": true, "access_type": "public_method", "serialized_name": "auReasonCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAuReasonCode", "setter": "setAuReasonCode"}, "type": "string"}, "reasonDescription": {"expose": true, "access_type": "public_method", "serialized_name": "reasonDescription", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getReasonDescription", "setter": "setReasonDescription"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\AuResponseType": {"properties": {"auReasonCode": {"expose": true, "access_type": "public_method", "serialized_name": "auReasonCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAuReasonCode", "setter": "setAuReasonCode"}, "type": "string"}, "profileCount": {"expose": true, "access_type": "public_method", "serialized_name": "profileCount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProfileCount", "setter": "setProfileCount"}, "type": "integer"}, "reasonDescription": {"expose": true, "access_type": "public_method", "serialized_name": "reasonDescription", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getReasonDescription", "setter": "setReasonDescription"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\AuUpdateType": {"properties": {"newCreditCard": {"expose": true, "access_type": "public_method", "serialized_name": "newCreditCard", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getNewCreditCard", "setter": "setNewCreditCard"}, "type": "net\\authorize\\api\\contract\\v1\\CreditCardMaskedType"}, "oldCreditCard": {"expose": true, "access_type": "public_method", "serialized_name": "oldCreditCard", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOldCreditCard", "setter": "setOldCreditCard"}, "type": "net\\authorize\\api\\contract\\v1\\CreditCardMaskedType"}}}, "net\\authorize\\api\\contract\\v1\\AuthenticateTestRequest": {"xml_root_name": "authenticateTestRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": []}, "net\\authorize\\api\\contract\\v1\\AuthenticateTestResponse": {"xml_root_name": "authenticateTestResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": []}, "net\\authorize\\api\\contract\\v1\\AuthorizationIndicatorType": {"properties": {"authorizationIndicator": {"expose": true, "access_type": "public_method", "serialized_name": "authorizationIndicator", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAuthorizationIndicator", "setter": "setAuthorizationIndicator"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\BankAccountMaskedType": {"properties": {"accountType": {"expose": true, "access_type": "public_method", "serialized_name": "accountType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAccountType", "setter": "setAccountType"}, "type": "string"}, "routingNumber": {"expose": true, "access_type": "public_method", "serialized_name": "routingNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getRoutingNumber", "setter": "setRoutingNumber"}, "type": "string"}, "accountNumber": {"expose": true, "access_type": "public_method", "serialized_name": "accountNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAccountNumber", "setter": "setAccountNumber"}, "type": "string"}, "nameOnAccount": {"expose": true, "access_type": "public_method", "serialized_name": "nameOnAccount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getNameOnAccount", "setter": "setNameOnAccount"}, "type": "string"}, "echeckType": {"expose": true, "access_type": "public_method", "serialized_name": "echeckType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEcheckType", "setter": "setEcheckType"}, "type": "string"}, "bankName": {"expose": true, "access_type": "public_method", "serialized_name": "bankName", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getBankName", "setter": "setBankName"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\BankAccountType": {"properties": {"accountType": {"expose": true, "access_type": "public_method", "serialized_name": "accountType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAccountType", "setter": "setAccountType"}, "type": "string"}, "routingNumber": {"expose": true, "access_type": "public_method", "serialized_name": "routingNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getRoutingNumber", "setter": "setRoutingNumber"}, "type": "string"}, "accountNumber": {"expose": true, "access_type": "public_method", "serialized_name": "accountNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAccountNumber", "setter": "setAccountNumber"}, "type": "string"}, "nameOnAccount": {"expose": true, "access_type": "public_method", "serialized_name": "nameOnAccount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getNameOnAccount", "setter": "setNameOnAccount"}, "type": "string"}, "echeckType": {"expose": true, "access_type": "public_method", "serialized_name": "echeckType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEcheckType", "setter": "setEcheckType"}, "type": "string"}, "bankName": {"expose": true, "access_type": "public_method", "serialized_name": "bankName", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getBankName", "setter": "setBankName"}, "type": "string"}, "checkNumber": {"expose": true, "access_type": "public_method", "serialized_name": "checkNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCheckNumber", "setter": "setCheckNumber"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\BatchDetailsType": {"properties": {"batchId": {"expose": true, "access_type": "public_method", "serialized_name": "batchId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getBatchId", "setter": "setBatchId"}, "type": "string"}, "settlementTimeUTC": {"expose": true, "access_type": "public_method", "serialized_name": "settlementTimeUTC", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSettlementTimeUTC", "setter": "setSettlementTimeUTC"}, "type": "GoetasWebservices\\Xsd\\XsdToPhp\\XMLSchema\\DateTime"}, "settlementTimeLocal": {"expose": true, "access_type": "public_method", "serialized_name": "settlementTimeLocal", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSettlementTimeLocal", "setter": "setSettlementTimeLocal"}, "type": "GoetasWebservices\\Xsd\\XsdToPhp\\XMLSchema\\DateTime"}, "settlementState": {"expose": true, "access_type": "public_method", "serialized_name": "settlementState", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSettlementState", "setter": "setSettlementState"}, "type": "string"}, "paymentMethod": {"expose": true, "access_type": "public_method", "serialized_name": "paymentMethod", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaymentMethod", "setter": "setPaymentMethod"}, "type": "string"}, "marketType": {"expose": true, "access_type": "public_method", "serialized_name": "marketType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMarketType", "setter": "setMarketType"}, "type": "string"}, "product": {"expose": true, "access_type": "public_method", "serialized_name": "product", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProduct", "setter": "setProduct"}, "type": "string"}, "statistics": {"expose": true, "access_type": "public_method", "serialized_name": "statistics", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getStatistics", "setter": "setStatistics"}, "type": "array<net\\authorize\\api\\contract\\v1\\BatchStatisticType>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "statistic", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}}}, "net\\authorize\\api\\contract\\v1\\BatchStatisticType": {"properties": {"accountType": {"expose": true, "access_type": "public_method", "serialized_name": "accountType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAccountType", "setter": "setAccountType"}, "type": "string"}, "chargeAmount": {"expose": true, "access_type": "public_method", "serialized_name": "chargeAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getChargeAmount", "setter": "setChargeAmount"}, "type": "float"}, "chargeCount": {"expose": true, "access_type": "public_method", "serialized_name": "chargeCount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getChargeCount", "setter": "setChargeCount"}, "type": "integer"}, "refundAmount": {"expose": true, "access_type": "public_method", "serialized_name": "refundAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getRefundAmount", "setter": "setRefundAmount"}, "type": "float"}, "refundCount": {"expose": true, "access_type": "public_method", "serialized_name": "refundCount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getRefundCount", "setter": "setRefundCount"}, "type": "integer"}, "voidCount": {"expose": true, "access_type": "public_method", "serialized_name": "voidCount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getVoidCount", "setter": "setVoidCount"}, "type": "integer"}, "declineCount": {"expose": true, "access_type": "public_method", "serialized_name": "declineCount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDeclineCount", "setter": "setDeclineCount"}, "type": "integer"}, "errorCount": {"expose": true, "access_type": "public_method", "serialized_name": "errorCount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getErrorCount", "setter": "setErrorCount"}, "type": "integer"}, "returnedItemAmount": {"expose": true, "access_type": "public_method", "serialized_name": "returnedItemAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getReturnedItemAmount", "setter": "setReturnedItemAmount"}, "type": "float"}, "returnedItemCount": {"expose": true, "access_type": "public_method", "serialized_name": "returnedItemCount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getReturnedItemCount", "setter": "setReturnedItemCount"}, "type": "integer"}, "chargebackAmount": {"expose": true, "access_type": "public_method", "serialized_name": "chargebackAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getChargebackAmount", "setter": "setChargebackAmount"}, "type": "float"}, "chargebackCount": {"expose": true, "access_type": "public_method", "serialized_name": "chargebackCount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getChargebackCount", "setter": "setChargebackCount"}, "type": "integer"}, "correctionNoticeCount": {"expose": true, "access_type": "public_method", "serialized_name": "correctionNoticeCount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCorrectionNoticeCount", "setter": "setCorrectionNoticeCount"}, "type": "integer"}, "chargeChargeBackAmount": {"expose": true, "access_type": "public_method", "serialized_name": "chargeChargeBackAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getChargeChargeBackAmount", "setter": "setChargeChargeBackAmount"}, "type": "float"}, "chargeChargeBackCount": {"expose": true, "access_type": "public_method", "serialized_name": "chargeChargeBackCount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getChargeChargeBackCount", "setter": "setChargeChargeBackCount"}, "type": "integer"}, "refundChargeBackAmount": {"expose": true, "access_type": "public_method", "serialized_name": "refundChargeBackAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getRefundChargeBackAmount", "setter": "setRefundChargeBackAmount"}, "type": "float"}, "refundChargeBackCount": {"expose": true, "access_type": "public_method", "serialized_name": "refundChargeBackCount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getRefundChargeBackCount", "setter": "setRefundChargeBackCount"}, "type": "integer"}, "chargeReturnedItemsAmount": {"expose": true, "access_type": "public_method", "serialized_name": "chargeReturnedItemsAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getChargeReturnedItemsAmount", "setter": "setChargeReturnedItemsAmount"}, "type": "float"}, "chargeReturnedItemsCount": {"expose": true, "access_type": "public_method", "serialized_name": "chargeReturnedItemsCount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getChargeReturnedItemsCount", "setter": "setChargeReturnedItemsCount"}, "type": "integer"}, "refundReturnedItemsAmount": {"expose": true, "access_type": "public_method", "serialized_name": "refundReturnedItemsAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getRefundReturnedItemsAmount", "setter": "setRefundReturnedItemsAmount"}, "type": "float"}, "refundReturnedItemsCount": {"expose": true, "access_type": "public_method", "serialized_name": "refundReturnedItemsCount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getRefundReturnedItemsCount", "setter": "setRefundReturnedItemsCount"}, "type": "integer"}}}, "net\\authorize\\api\\contract\\v1\\CardArtType": {"properties": {"cardBrand": {"expose": true, "access_type": "public_method", "serialized_name": "card<PERSON>rand", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCardBrand", "setter": "setCardBrand"}, "type": "string"}, "cardImageHeight": {"expose": true, "access_type": "public_method", "serialized_name": "cardImageHeight", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCardImageHeight", "setter": "setCardImageHeight"}, "type": "string"}, "cardImageUrl": {"expose": true, "access_type": "public_method", "serialized_name": "cardImageUrl", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCardImageUrl", "setter": "setCardImageUrl"}, "type": "string"}, "cardImageWidth": {"expose": true, "access_type": "public_method", "serialized_name": "cardImageWidth", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCardImageWidth", "setter": "setCardImageWidth"}, "type": "string"}, "cardType": {"expose": true, "access_type": "public_method", "serialized_name": "cardType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCardType", "setter": "setCardType"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\CcAuthenticationType": {"properties": {"authenticationIndicator": {"expose": true, "access_type": "public_method", "serialized_name": "authenticationIndicator", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAuthenticationIndicator", "setter": "setAuthenticationIndicator"}, "type": "string"}, "cardholderAuthenticationValue": {"expose": true, "access_type": "public_method", "serialized_name": "cardholderAuthenticationValue", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCardholderAuthenticationValue", "setter": "setCardholderAuthenticationValue"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\ContactDetailType": {"properties": {"email": {"expose": true, "access_type": "public_method", "serialized_name": "email", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEmail", "setter": "setEmail"}, "type": "string"}, "firstName": {"expose": true, "access_type": "public_method", "serialized_name": "firstName", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getFirstName", "setter": "setFirstName"}, "type": "string"}, "lastName": {"expose": true, "access_type": "public_method", "serialized_name": "lastName", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getLastName", "setter": "setLastName"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\CreateCustomerPaymentProfileRequest": {"xml_root_name": "createCustomerPaymentProfileRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "paymentProfile": {"expose": true, "access_type": "public_method", "serialized_name": "paymentProfile", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaymentProfile", "setter": "setPaymentProfile"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerPaymentProfileType"}, "validationMode": {"expose": true, "access_type": "public_method", "serialized_name": "validationMode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getValidationMode", "setter": "setValidationMode"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\CreateCustomerPaymentProfileResponse": {"xml_root_name": "createCustomerPaymentProfileResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "customerPaymentProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerPaymentProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerPaymentProfileId", "setter": "setCustomerPaymentProfileId"}, "type": "string"}, "validationDirectResponse": {"expose": true, "access_type": "public_method", "serialized_name": "validationDirectResponse", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getValidationDirectResponse", "setter": "setValidationDirectResponse"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\CreateCustomerProfileFromTransactionRequest": {"xml_root_name": "createCustomerProfileFromTransactionRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"transId": {"expose": true, "access_type": "public_method", "serialized_name": "transId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransId", "setter": "setTransId"}, "type": "string"}, "customer": {"expose": true, "access_type": "public_method", "serialized_name": "customer", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomer", "setter": "setCustomer"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerProfileBaseType"}, "customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "defaultPaymentProfile": {"expose": true, "access_type": "public_method", "serialized_name": "defaultPaymentProfile", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDefaultPaymentProfile", "setter": "setDefaultPaymentProfile"}, "type": "boolean"}, "defaultShippingAddress": {"expose": true, "access_type": "public_method", "serialized_name": "defaultShippingAddress", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDefaultShippingAddress", "setter": "setDefaultShippingAddress"}, "type": "boolean"}, "profileType": {"expose": true, "access_type": "public_method", "serialized_name": "profileType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProfileType", "setter": "setProfileType"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\CreateCustomerProfileRequest": {"xml_root_name": "createCustomerProfileRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"profile": {"expose": true, "access_type": "public_method", "serialized_name": "profile", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProfile", "setter": "setProfile"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerProfileType"}, "validationMode": {"expose": true, "access_type": "public_method", "serialized_name": "validationMode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getValidationMode", "setter": "setValidationMode"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\CreateCustomerProfileResponse": {"xml_root_name": "createCustomerProfileResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "customerPaymentProfileIdList": {"expose": true, "access_type": "public_method", "serialized_name": "customerPaymentProfileIdList", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerPaymentProfileIdList", "setter": "setCustomerPaymentProfileIdList"}, "type": "array<string>", "xml_list": {"inline": false, "skip_when_empty": false, "entry_name": "numericString", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "customerShippingAddressIdList": {"expose": true, "access_type": "public_method", "serialized_name": "customerShippingAddressIdList", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerShippingAddressIdList", "setter": "setCustomerShippingAddressIdList"}, "type": "array<string>", "xml_list": {"inline": false, "skip_when_empty": false, "entry_name": "numericString", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "validationDirectResponseList": {"expose": true, "access_type": "public_method", "serialized_name": "validationDirectResponseList", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getValidationDirectResponseList", "setter": "setValidationDirectResponseList"}, "type": "array<string>", "xml_list": {"inline": false, "skip_when_empty": false, "entry_name": "string", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}}}, "net\\authorize\\api\\contract\\v1\\CreateCustomerProfileTransactionRequest": {"xml_root_name": "createCustomerProfileTransactionRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"transaction": {"expose": true, "access_type": "public_method", "serialized_name": "transaction", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransaction", "setter": "setTransaction"}, "type": "net\\authorize\\api\\contract\\v1\\ProfileTransactionType"}, "extraOptions": {"expose": true, "access_type": "public_method", "serialized_name": "extraOptions", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getExtraOptions", "setter": "setExtraOptions"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\CreateCustomerProfileTransactionResponse": {"xml_root_name": "createCustomerProfileTransactionResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"transactionResponse": {"expose": true, "access_type": "public_method", "serialized_name": "transactionResponse", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransactionResponse", "setter": "setTransactionResponse"}, "type": "net\\authorize\\api\\contract\\v1\\TransactionResponseType"}, "directResponse": {"expose": true, "access_type": "public_method", "serialized_name": "directResponse", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDirectResponse", "setter": "setDirectResponse"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\CreateCustomerShippingAddressRequest": {"xml_root_name": "createCustomerShippingAddressRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "address": {"expose": true, "access_type": "public_method", "serialized_name": "address", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "get<PERSON><PERSON><PERSON>", "setter": "<PERSON><PERSON><PERSON><PERSON>"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerAddressType"}, "defaultShippingAddress": {"expose": true, "access_type": "public_method", "serialized_name": "defaultShippingAddress", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDefaultShippingAddress", "setter": "setDefaultShippingAddress"}, "type": "boolean"}}}, "net\\authorize\\api\\contract\\v1\\CreateCustomerShippingAddressResponse": {"xml_root_name": "createCustomerShippingAddressResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "customerAddressId": {"expose": true, "access_type": "public_method", "serialized_name": "customerAddressId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerAddressId", "setter": "setCustomerAddressId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\CreateProfileResponseType": {"properties": {"messages": {"expose": true, "access_type": "public_method", "serialized_name": "messages", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMessages", "setter": "setMessages"}, "type": "net\\authorize\\api\\contract\\v1\\MessagesType"}, "customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "customerPaymentProfileIdList": {"expose": true, "access_type": "public_method", "serialized_name": "customerPaymentProfileIdList", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerPaymentProfileIdList", "setter": "setCustomerPaymentProfileIdList"}, "type": "array<string>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "numericString", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "customerShippingAddressIdList": {"expose": true, "access_type": "public_method", "serialized_name": "customerShippingAddressIdList", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerShippingAddressIdList", "setter": "setCustomerShippingAddressIdList"}, "type": "array<string>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "numericString", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}}}, "net\\authorize\\api\\contract\\v1\\CreateTransactionRequest": {"xml_root_name": "createTransactionRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"transactionRequest": {"expose": true, "access_type": "public_method", "serialized_name": "transactionRequest", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransactionRequest", "setter": "setTransactionRequest"}, "type": "net\\authorize\\api\\contract\\v1\\TransactionRequestType"}}}, "net\\authorize\\api\\contract\\v1\\CreateTransactionResponse": {"xml_root_name": "createTransactionResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"transactionResponse": {"expose": true, "access_type": "public_method", "serialized_name": "transactionResponse", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransactionResponse", "setter": "setTransactionResponse"}, "type": "net\\authorize\\api\\contract\\v1\\TransactionResponseType"}, "profileResponse": {"expose": true, "access_type": "public_method", "serialized_name": "profileResponse", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProfileResponse", "setter": "setProfileResponse"}, "type": "net\\authorize\\api\\contract\\v1\\CreateProfileResponseType"}}}, "net\\authorize\\api\\contract\\v1\\CreditCardMaskedType": {"properties": {"cardNumber": {"expose": true, "access_type": "public_method", "serialized_name": "cardNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCardNumber", "setter": "setCardNumber"}, "type": "string"}, "expirationDate": {"expose": true, "access_type": "public_method", "serialized_name": "expirationDate", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getExpirationDate", "setter": "setExpirationDate"}, "type": "string"}, "cardType": {"expose": true, "access_type": "public_method", "serialized_name": "cardType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCardType", "setter": "setCardType"}, "type": "string"}, "cardArt": {"expose": true, "access_type": "public_method", "serialized_name": "cardArt", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCardArt", "setter": "setCardArt"}, "type": "net\\authorize\\api\\contract\\v1\\CardArtType"}, "issuerNumber": {"expose": true, "access_type": "public_method", "serialized_name": "issuerNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getIssuerNumber", "setter": "setIssuerNumber"}, "type": "string"}, "isPaymentToken": {"expose": true, "access_type": "public_method", "serialized_name": "isPaymentToken", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getIsPaymentToken", "setter": "setIsPaymentToken"}, "type": "boolean"}}}, "net\\authorize\\api\\contract\\v1\\CreditCardSimpleType": {"properties": {"cardNumber": {"expose": true, "access_type": "public_method", "serialized_name": "cardNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCardNumber", "setter": "setCardNumber"}, "type": "string"}, "expirationDate": {"expose": true, "access_type": "public_method", "serialized_name": "expirationDate", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getExpirationDate", "setter": "setExpirationDate"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\CreditCardTrackType": {"properties": {"track1": {"expose": true, "access_type": "public_method", "serialized_name": "track1", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTrack1", "setter": "setTrack1"}, "type": "string"}, "track2": {"expose": true, "access_type": "public_method", "serialized_name": "track2", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTrack2", "setter": "setTrack2"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\CreditCardType": {"properties": {"cardCode": {"expose": true, "access_type": "public_method", "serialized_name": "cardCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCardCode", "setter": "setCardCode"}, "type": "string"}, "isPaymentToken": {"expose": true, "access_type": "public_method", "serialized_name": "isPaymentToken", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getIsPaymentToken", "setter": "setIsPaymentToken"}, "type": "boolean"}, "cryptogram": {"expose": true, "access_type": "public_method", "serialized_name": "cryptogram", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCryptogram", "setter": "setCryptogram"}, "type": "string"}, "tokenRequestorName": {"expose": true, "access_type": "public_method", "serialized_name": "tokenRequestorName", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTokenRequestorName", "setter": "setTokenRequestorName"}, "type": "string"}, "tokenRequestorId": {"expose": true, "access_type": "public_method", "serialized_name": "tokenRequestorId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTokenRequestorId", "setter": "setTokenRequestorId"}, "type": "string"}, "tokenRequestorEci": {"expose": true, "access_type": "public_method", "serialized_name": "tokenRequestorEci", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTokenRequestorEci", "setter": "setTokenRequestorEci"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\CustomerAddressExType": {"properties": {"customerAddressId": {"expose": true, "access_type": "public_method", "serialized_name": "customerAddressId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerAddressId", "setter": "setCustomerAddressId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\CustomerAddressType": {"properties": {"phoneNumber": {"expose": true, "access_type": "public_method", "serialized_name": "phoneNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPhoneNumber", "setter": "setPhoneNumber"}, "type": "string"}, "faxNumber": {"expose": true, "access_type": "public_method", "serialized_name": "faxNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getFaxNumber", "setter": "setFaxNumber"}, "type": "string"}, "email": {"expose": true, "access_type": "public_method", "serialized_name": "email", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEmail", "setter": "setEmail"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\CustomerDataType": {"properties": {"type": {"expose": true, "access_type": "public_method", "serialized_name": "type", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getType", "setter": "setType"}, "type": "string"}, "id": {"expose": true, "access_type": "public_method", "serialized_name": "id", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getId", "setter": "setId"}, "type": "string"}, "email": {"expose": true, "access_type": "public_method", "serialized_name": "email", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEmail", "setter": "setEmail"}, "type": "string"}, "driversLicense": {"expose": true, "access_type": "public_method", "serialized_name": "driversLicense", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDriversLicense", "setter": "setDriversLicense"}, "type": "net\\authorize\\api\\contract\\v1\\DriversLicenseType"}, "taxId": {"expose": true, "access_type": "public_method", "serialized_name": "taxId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTaxId", "setter": "setTaxId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\CustomerPaymentProfileBaseType": {"properties": {"customerType": {"expose": true, "access_type": "public_method", "serialized_name": "customerType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerType", "setter": "setCustomerType"}, "type": "string"}, "billTo": {"expose": true, "access_type": "public_method", "serialized_name": "billTo", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getBillTo", "setter": "setBillTo"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerAddressType"}}}, "net\\authorize\\api\\contract\\v1\\CustomerPaymentProfileExType": {"properties": {"customerPaymentProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerPaymentProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerPaymentProfileId", "setter": "setCustomerPaymentProfileId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\CustomerPaymentProfileListItemType": {"properties": {"defaultPaymentProfile": {"expose": true, "access_type": "public_method", "serialized_name": "defaultPaymentProfile", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDefaultPaymentProfile", "setter": "setDefaultPaymentProfile"}, "type": "boolean"}, "customerPaymentProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerPaymentProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerPaymentProfileId", "setter": "setCustomerPaymentProfileId"}, "type": "integer"}, "customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "integer"}, "billTo": {"expose": true, "access_type": "public_method", "serialized_name": "billTo", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getBillTo", "setter": "setBillTo"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerAddressType"}, "payment": {"expose": true, "access_type": "public_method", "serialized_name": "payment", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPayment", "setter": "setPayment"}, "type": "net\\authorize\\api\\contract\\v1\\PaymentMaskedType"}, "originalNetworkTransId": {"expose": true, "access_type": "public_method", "serialized_name": "originalNetworkTransId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOriginalNetworkTransId", "setter": "setOriginalNetworkTransId"}, "type": "string"}, "originalAuthAmount": {"expose": true, "access_type": "public_method", "serialized_name": "originalAuthAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOriginalAuthAmount", "setter": "setOriginalAuthAmount"}, "type": "float"}, "excludeFromAccountUpdater": {"expose": true, "access_type": "public_method", "serialized_name": "excludeFromAccountUpdater", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getExcludeFromAccountUpdater", "setter": "setExcludeFromAccountUpdater"}, "type": "boolean"}}}, "net\\authorize\\api\\contract\\v1\\CustomerPaymentProfileMaskedType": {"properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "customerPaymentProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerPaymentProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerPaymentProfileId", "setter": "setCustomerPaymentProfileId"}, "type": "string"}, "defaultPaymentProfile": {"expose": true, "access_type": "public_method", "serialized_name": "defaultPaymentProfile", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDefaultPaymentProfile", "setter": "setDefaultPaymentProfile"}, "type": "boolean"}, "payment": {"expose": true, "access_type": "public_method", "serialized_name": "payment", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPayment", "setter": "setPayment"}, "type": "net\\authorize\\api\\contract\\v1\\PaymentMaskedType"}, "driversLicense": {"expose": true, "access_type": "public_method", "serialized_name": "driversLicense", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDriversLicense", "setter": "setDriversLicense"}, "type": "net\\authorize\\api\\contract\\v1\\DriversLicenseMaskedType"}, "taxId": {"expose": true, "access_type": "public_method", "serialized_name": "taxId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTaxId", "setter": "setTaxId"}, "type": "string"}, "subscriptionIds": {"expose": true, "access_type": "public_method", "serialized_name": "subscriptionIds", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubscriptionIds", "setter": "setSubscriptionIds"}, "type": "array<string>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "subscriptionId", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "originalNetworkTransId": {"expose": true, "access_type": "public_method", "serialized_name": "originalNetworkTransId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOriginalNetworkTransId", "setter": "setOriginalNetworkTransId"}, "type": "string"}, "originalAuthAmount": {"expose": true, "access_type": "public_method", "serialized_name": "originalAuthAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOriginalAuthAmount", "setter": "setOriginalAuthAmount"}, "type": "float"}, "excludeFromAccountUpdater": {"expose": true, "access_type": "public_method", "serialized_name": "excludeFromAccountUpdater", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getExcludeFromAccountUpdater", "setter": "setExcludeFromAccountUpdater"}, "type": "boolean"}}}, "net\\authorize\\api\\contract\\v1\\CustomerPaymentProfileSortingType": {"properties": {"orderBy": {"expose": true, "access_type": "public_method", "serialized_name": "orderBy", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOrderBy", "setter": "setOrderBy"}, "type": "string"}, "orderDescending": {"expose": true, "access_type": "public_method", "serialized_name": "orderDescending", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOrderDescending", "setter": "setOrderDescending"}, "type": "boolean"}}}, "net\\authorize\\api\\contract\\v1\\CustomerPaymentProfileType": {"properties": {"payment": {"expose": true, "access_type": "public_method", "serialized_name": "payment", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPayment", "setter": "setPayment"}, "type": "net\\authorize\\api\\contract\\v1\\PaymentType"}, "driversLicense": {"expose": true, "access_type": "public_method", "serialized_name": "driversLicense", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDriversLicense", "setter": "setDriversLicense"}, "type": "net\\authorize\\api\\contract\\v1\\DriversLicenseType"}, "taxId": {"expose": true, "access_type": "public_method", "serialized_name": "taxId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTaxId", "setter": "setTaxId"}, "type": "string"}, "defaultPaymentProfile": {"expose": true, "access_type": "public_method", "serialized_name": "defaultPaymentProfile", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDefaultPaymentProfile", "setter": "setDefaultPaymentProfile"}, "type": "boolean"}, "subsequentAuthInformation": {"expose": true, "access_type": "public_method", "serialized_name": "subsequentAuthInformation", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubsequentAuthInformation", "setter": "setSubsequentAuthInformation"}, "type": "net\\authorize\\api\\contract\\v1\\SubsequentAuthInformationType"}, "excludeFromAccountUpdater": {"expose": true, "access_type": "public_method", "serialized_name": "excludeFromAccountUpdater", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getExcludeFromAccountUpdater", "setter": "setExcludeFromAccountUpdater"}, "type": "boolean"}}}, "net\\authorize\\api\\contract\\v1\\CustomerProfileBaseType": {"properties": {"merchantCustomerId": {"expose": true, "access_type": "public_method", "serialized_name": "merchantCustomerId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMerchantCustomerId", "setter": "setMerchantCustomerId"}, "type": "string"}, "description": {"expose": true, "access_type": "public_method", "serialized_name": "description", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDescription", "setter": "setDescription"}, "type": "string"}, "email": {"expose": true, "access_type": "public_method", "serialized_name": "email", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEmail", "setter": "setEmail"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\CustomerProfileExType": {"properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\CustomerProfileIdType": {"properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "customerPaymentProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerPaymentProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerPaymentProfileId", "setter": "setCustomerPaymentProfileId"}, "type": "string"}, "customerAddressId": {"expose": true, "access_type": "public_method", "serialized_name": "customerAddressId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerAddressId", "setter": "setCustomerAddressId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\CustomerProfileInfoExType": {"properties": {"profileType": {"expose": true, "access_type": "public_method", "serialized_name": "profileType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProfileType", "setter": "setProfileType"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\CustomerProfileMaskedType": {"properties": {"paymentProfiles": {"expose": true, "access_type": "public_method", "serialized_name": "paymentProfiles", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaymentProfiles", "setter": "setPaymentProfiles"}, "xml_list": {"inline": true, "entry_name": "paymentProfiles", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "type": "array<net\\authorize\\api\\contract\\v1\\CustomerPaymentProfileMaskedType>"}, "shipToList": {"expose": true, "access_type": "public_method", "serialized_name": "shipToList", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getShipToList", "setter": "setShipToList"}, "xml_list": {"inline": true, "entry_name": "shipToList", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "type": "array<net\\authorize\\api\\contract\\v1\\CustomerAddressExType>"}, "profileType": {"expose": true, "access_type": "public_method", "serialized_name": "profileType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProfileType", "setter": "setProfileType"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\CustomerProfilePaymentType": {"properties": {"createProfile": {"expose": true, "access_type": "public_method", "serialized_name": "createProfile", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCreateProfile", "setter": "setCreateProfile"}, "type": "boolean"}, "customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "paymentProfile": {"expose": true, "access_type": "public_method", "serialized_name": "paymentProfile", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaymentProfile", "setter": "setPaymentProfile"}, "type": "net\\authorize\\api\\contract\\v1\\PaymentProfileType"}, "shippingProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "shippingProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getShippingProfileId", "setter": "setShippingProfileId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\CustomerProfileSummaryType": {"properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "description": {"expose": true, "access_type": "public_method", "serialized_name": "description", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDescription", "setter": "setDescription"}, "type": "string"}, "merchantCustomerId": {"expose": true, "access_type": "public_method", "serialized_name": "merchantCustomerId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMerchantCustomerId", "setter": "setMerchantCustomerId"}, "type": "string"}, "email": {"expose": true, "access_type": "public_method", "serialized_name": "email", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEmail", "setter": "setEmail"}, "type": "string"}, "createdDate": {"expose": true, "access_type": "public_method", "serialized_name": "createdDate", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCreatedDate", "setter": "setCreatedDate"}, "type": "GoetasWebservices\\Xsd\\XsdToPhp\\XMLSchema\\DateTime"}}}, "net\\authorize\\api\\contract\\v1\\CustomerProfileType": {"properties": {"paymentProfiles": {"expose": true, "access_type": "public_method", "serialized_name": "paymentProfiles", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaymentProfiles", "setter": "setPaymentProfiles"}, "xml_list": {"inline": true, "entry_name": "paymentProfiles", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "type": "array<net\\authorize\\api\\contract\\v1\\CustomerPaymentProfileType>"}, "shipToList": {"expose": true, "access_type": "public_method", "serialized_name": "shipToList", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getShipToList", "setter": "setShipToList"}, "xml_list": {"inline": true, "entry_name": "shipToList", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "type": "array<net\\authorize\\api\\contract\\v1\\CustomerAddressType>"}, "profileType": {"expose": true, "access_type": "public_method", "serialized_name": "profileType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProfileType", "setter": "setProfileType"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\CustomerType": {"properties": {"type": {"expose": true, "access_type": "public_method", "serialized_name": "type", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getType", "setter": "setType"}, "type": "string"}, "id": {"expose": true, "access_type": "public_method", "serialized_name": "id", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getId", "setter": "setId"}, "type": "string"}, "email": {"expose": true, "access_type": "public_method", "serialized_name": "email", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEmail", "setter": "setEmail"}, "type": "string"}, "phoneNumber": {"expose": true, "access_type": "public_method", "serialized_name": "phoneNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPhoneNumber", "setter": "setPhoneNumber"}, "type": "string"}, "faxNumber": {"expose": true, "access_type": "public_method", "serialized_name": "faxNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getFaxNumber", "setter": "setFaxNumber"}, "type": "string"}, "driversLicense": {"expose": true, "access_type": "public_method", "serialized_name": "driversLicense", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDriversLicense", "setter": "setDriversLicense"}, "type": "net\\authorize\\api\\contract\\v1\\DriversLicenseType"}, "taxId": {"expose": true, "access_type": "public_method", "serialized_name": "taxId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTaxId", "setter": "setTaxId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\DecryptPaymentDataRequest": {"xml_root_name": "decryptPaymentDataRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"opaqueData": {"expose": true, "access_type": "public_method", "serialized_name": "opaqueData", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOpaqueData", "setter": "setOpaqueData"}, "type": "net\\authorize\\api\\contract\\v1\\OpaqueDataType"}, "callId": {"expose": true, "access_type": "public_method", "serialized_name": "callId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCallId", "setter": "setCallId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\DecryptPaymentDataResponse": {"xml_root_name": "decryptPaymentDataResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"shippingInfo": {"expose": true, "access_type": "public_method", "serialized_name": "shippingInfo", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getShippingInfo", "setter": "setShippingInfo"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerAddressType"}, "billingInfo": {"expose": true, "access_type": "public_method", "serialized_name": "billingInfo", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getBillingInfo", "setter": "setBillingInfo"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerAddressType"}, "cardInfo": {"expose": true, "access_type": "public_method", "serialized_name": "cardInfo", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCardInfo", "setter": "setCardInfo"}, "type": "net\\authorize\\api\\contract\\v1\\CreditCardMaskedType"}, "paymentDetails": {"expose": true, "access_type": "public_method", "serialized_name": "paymentDetails", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaymentDetails", "setter": "setPaymentDetails"}, "type": "net\\authorize\\api\\contract\\v1\\PaymentDetailsType"}}}, "net\\authorize\\api\\contract\\v1\\DeleteCustomerPaymentProfileRequest": {"xml_root_name": "deleteCustomerPaymentProfileRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "customerPaymentProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerPaymentProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerPaymentProfileId", "setter": "setCustomerPaymentProfileId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\DeleteCustomerPaymentProfileResponse": {"xml_root_name": "deleteCustomerPaymentProfileResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": []}, "net\\authorize\\api\\contract\\v1\\DeleteCustomerProfileRequest": {"xml_root_name": "deleteCustomerProfileRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\DeleteCustomerProfileResponse": {"xml_root_name": "deleteCustomerProfileResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": []}, "net\\authorize\\api\\contract\\v1\\DeleteCustomerShippingAddressRequest": {"xml_root_name": "deleteCustomerShippingAddressRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "customerAddressId": {"expose": true, "access_type": "public_method", "serialized_name": "customerAddressId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerAddressId", "setter": "setCustomerAddressId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\DeleteCustomerShippingAddressResponse": {"xml_root_name": "deleteCustomerShippingAddressResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": []}, "net\\authorize\\api\\contract\\v1\\DriversLicenseMaskedType": {"properties": {"number": {"expose": true, "access_type": "public_method", "serialized_name": "number", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getNumber", "setter": "setNumber"}, "type": "string"}, "state": {"expose": true, "access_type": "public_method", "serialized_name": "state", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getState", "setter": "setState"}, "type": "string"}, "dateOfBirth": {"expose": true, "access_type": "public_method", "serialized_name": "dateOfBirth", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDateOfBirth", "setter": "setDateOfBirth"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\DriversLicenseType": {"properties": {"number": {"expose": true, "access_type": "public_method", "serialized_name": "number", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getNumber", "setter": "setNumber"}, "type": "string"}, "state": {"expose": true, "access_type": "public_method", "serialized_name": "state", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getState", "setter": "setState"}, "type": "string"}, "dateOfBirth": {"expose": true, "access_type": "public_method", "serialized_name": "dateOfBirth", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDateOfBirth", "setter": "setDateOfBirth"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\EmailSettingsType": {"properties": {"version": {"expose": true, "access_type": "public_method", "serialized_name": "version", "accessor": {"getter": "getVersion", "setter": "setVersion"}, "xml_attribute": true, "type": "integer"}}}, "net\\authorize\\api\\contract\\v1\\EmvTagType": {"properties": {"name": {"expose": true, "access_type": "public_method", "serialized_name": "name", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getName", "setter": "setName"}, "type": "string"}, "value": {"expose": true, "access_type": "public_method", "serialized_name": "value", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getValue", "setter": "setValue"}, "type": "string"}, "formatted": {"expose": true, "access_type": "public_method", "serialized_name": "formatted", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getFormatted", "setter": "setFormatted"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\EncryptedTrackDataType": {"properties": {"formOfPayment": {"expose": true, "access_type": "public_method", "serialized_name": "FormOfPayment", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getFormOfPayment", "setter": "setFormOfPayment"}, "type": "net\\authorize\\api\\contract\\v1\\KeyBlockType"}}}, "net\\authorize\\api\\contract\\v1\\EnumCollection": {"xml_root_name": "EnumCollection", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"customerProfileSummaryType": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileSummaryType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileSummaryType", "setter": "setCustomerProfileSummaryType"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerProfileSummaryType"}, "paymentSimpleType": {"expose": true, "access_type": "public_method", "serialized_name": "paymentSimpleType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaymentSimpleType", "setter": "setPaymentSimpleType"}, "type": "net\\authorize\\api\\contract\\v1\\PaymentSimpleType"}, "accountTypeEnum": {"expose": true, "access_type": "public_method", "serialized_name": "accountTypeEnum", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAccountTypeEnum", "setter": "setAccountTypeEnum"}, "type": "string"}, "cardTypeEnum": {"expose": true, "access_type": "public_method", "serialized_name": "cardTypeEnum", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCardTypeEnum", "setter": "setCardTypeEnum"}, "type": "string"}, "fDSFilterActionEnum": {"expose": true, "access_type": "public_method", "serialized_name": "FDSFilterActionEnum", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getFDSFilterActionEnum", "setter": "setFDSFilterActionEnum"}, "type": "string"}, "permissionsEnum": {"expose": true, "access_type": "public_method", "serialized_name": "permissionsEnum", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPermissionsEnum", "setter": "setPermissionsEnum"}, "type": "string"}, "settingNameEnum": {"expose": true, "access_type": "public_method", "serialized_name": "settingNameEnum", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSettingNameEnum", "setter": "setSettingNameEnum"}, "type": "string"}, "settlementStateEnum": {"expose": true, "access_type": "public_method", "serialized_name": "settlementStateEnum", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSettlementStateEnum", "setter": "setSettlementStateEnum"}, "type": "string"}, "transactionStatusEnum": {"expose": true, "access_type": "public_method", "serialized_name": "transactionStatusEnum", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransactionStatusEnum", "setter": "setTransactionStatusEnum"}, "type": "string"}, "transactionTypeEnum": {"expose": true, "access_type": "public_method", "serialized_name": "transactionTypeEnum", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransactionTypeEnum", "setter": "setTransactionTypeEnum"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\ErrorResponse": {"xml_root_name": "ErrorResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "net\\authorize\\api\\contract\\v1\\ExtendedAmountType": {"properties": {"amount": {"expose": true, "access_type": "public_method", "serialized_name": "amount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAmount", "setter": "setAmount"}, "type": "float"}, "name": {"expose": true, "access_type": "public_method", "serialized_name": "name", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getName", "setter": "setName"}, "type": "string"}, "description": {"expose": true, "access_type": "public_method", "serialized_name": "description", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDescription", "setter": "setDescription"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\FDSFilterType": {"properties": {"name": {"expose": true, "access_type": "public_method", "serialized_name": "name", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getName", "setter": "setName"}, "type": "string"}, "action": {"expose": true, "access_type": "public_method", "serialized_name": "action", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAction", "setter": "setAction"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\FingerPrintType": {"properties": {"hashValue": {"expose": true, "access_type": "public_method", "serialized_name": "hashValue", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getHashValue", "setter": "setHashValue"}, "type": "string"}, "sequence": {"expose": true, "access_type": "public_method", "serialized_name": "sequence", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSequence", "setter": "setSequence"}, "type": "string"}, "timestamp": {"expose": true, "access_type": "public_method", "serialized_name": "timestamp", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTimestamp", "setter": "setTimestamp"}, "type": "string"}, "currencyCode": {"expose": true, "access_type": "public_method", "serialized_name": "currencyCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCurrencyCode", "setter": "setCurrencyCode"}, "type": "string"}, "amount": {"expose": true, "access_type": "public_method", "serialized_name": "amount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAmount", "setter": "setAmount"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\FraudInformationType": {"properties": {"fraudFilterList": {"expose": true, "access_type": "public_method", "serialized_name": "fraudFilterList", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getFraudFilterList", "setter": "setFraudFilterList"}, "type": "array<string>", "xml_list": {"inline": false, "skip_when_empty": false, "entry_name": "fraudFilter", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "fraudAction": {"expose": true, "access_type": "public_method", "serialized_name": "fraudAction", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getFraudAction", "setter": "setFraudAction"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\GetAUJobDetailsRequest": {"xml_root_name": "getAUJobDetailsRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"month": {"expose": true, "access_type": "public_method", "serialized_name": "month", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMonth", "setter": "setMonth"}, "type": "string"}, "modifiedTypeFilter": {"expose": true, "access_type": "public_method", "serialized_name": "modifiedTypeFilter", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getModifiedTypeFilter", "setter": "setModifiedTypeFilter"}, "type": "string"}, "paging": {"expose": true, "access_type": "public_method", "serialized_name": "paging", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaging", "setter": "setPaging"}, "type": "net\\authorize\\api\\contract\\v1\\PagingType"}}}, "net\\authorize\\api\\contract\\v1\\GetAUJobDetailsResponse": {"xml_root_name": "getAUJobDetailsResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"totalNumInResultSet": {"expose": true, "access_type": "public_method", "serialized_name": "totalNumInResultSet", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTotalNumInResultSet", "setter": "setTotalNumInResultSet"}, "type": "integer"}, "auDetails": {"expose": true, "access_type": "public_method", "serialized_name": "auDetails", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAuDetails", "setter": "setAuDetails"}, "type": "net\\authorize\\api\\contract\\v1\\ListOfAUDetailsType"}}}, "net\\authorize\\api\\contract\\v1\\GetAUJobSummaryRequest": {"xml_root_name": "getAUJobSummaryRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"month": {"expose": true, "access_type": "public_method", "serialized_name": "month", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMonth", "setter": "setMonth"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\GetAUJobSummaryResponse": {"xml_root_name": "getAUJobSummaryResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"auSummary": {"expose": true, "access_type": "public_method", "serialized_name": "auSummar<PERSON>", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAuSummary", "setter": "setAuSummary"}, "type": "array<net\\authorize\\api\\contract\\v1\\AuResponseType>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "auResponse", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}}}, "net\\authorize\\api\\contract\\v1\\GetBatchStatisticsRequest": {"xml_root_name": "getBatchStatisticsRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"batchId": {"expose": true, "access_type": "public_method", "serialized_name": "batchId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getBatchId", "setter": "setBatchId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\GetBatchStatisticsResponse": {"xml_root_name": "getBatchStatisticsResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"batch": {"expose": true, "access_type": "public_method", "serialized_name": "batch", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getBatch", "setter": "setBatch"}, "type": "net\\authorize\\api\\contract\\v1\\BatchDetailsType"}}}, "net\\authorize\\api\\contract\\v1\\GetCustomerPaymentProfileListRequest": {"xml_root_name": "getCustomerPaymentProfileListRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"searchType": {"expose": true, "access_type": "public_method", "serialized_name": "searchType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSearchType", "setter": "setSearchType"}, "type": "string"}, "month": {"expose": true, "access_type": "public_method", "serialized_name": "month", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMonth", "setter": "setMonth"}, "type": "string"}, "sorting": {"expose": true, "access_type": "public_method", "serialized_name": "sorting", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSorting", "setter": "setSorting"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerPaymentProfileSortingType"}, "paging": {"expose": true, "access_type": "public_method", "serialized_name": "paging", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaging", "setter": "setPaging"}, "type": "net\\authorize\\api\\contract\\v1\\PagingType"}}}, "net\\authorize\\api\\contract\\v1\\GetCustomerPaymentProfileListResponse": {"xml_root_name": "getCustomerPaymentProfileListResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"totalNumInResultSet": {"expose": true, "access_type": "public_method", "serialized_name": "totalNumInResultSet", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTotalNumInResultSet", "setter": "setTotalNumInResultSet"}, "type": "integer"}, "paymentProfiles": {"expose": true, "access_type": "public_method", "serialized_name": "paymentProfiles", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaymentProfiles", "setter": "setPaymentProfiles"}, "type": "array<net\\authorize\\api\\contract\\v1\\CustomerPaymentProfileListItemType>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "paymentProfile", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}}}, "net\\authorize\\api\\contract\\v1\\GetCustomerPaymentProfileNonceRequest": {"xml_root_name": "getCustomerPaymentProfileNonceRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"connectedAccessToken": {"expose": true, "access_type": "public_method", "serialized_name": "connectedAccessToken", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getConnectedAccessToken", "setter": "setConnectedAccessToken"}, "type": "string"}, "customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "customerPaymentProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerPaymentProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerPaymentProfileId", "setter": "setCustomerPaymentProfileId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\GetCustomerPaymentProfileNonceResponse": {"xml_root_name": "getCustomerPaymentProfileNonceResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"opaqueData": {"expose": true, "access_type": "public_method", "serialized_name": "opaqueData", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOpaqueData", "setter": "setOpaqueData"}, "type": "net\\authorize\\api\\contract\\v1\\OpaqueDataType"}}}, "net\\authorize\\api\\contract\\v1\\GetCustomerPaymentProfileRequest": {"xml_root_name": "getCustomerPaymentProfileRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "customerPaymentProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerPaymentProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerPaymentProfileId", "setter": "setCustomerPaymentProfileId"}, "type": "string"}, "unmaskExpirationDate": {"expose": true, "access_type": "public_method", "serialized_name": "unmaskExpirationDate", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getUnmaskExpirationDate", "setter": "setUnmaskExpirationDate"}, "type": "boolean"}, "includeIssuerInfo": {"expose": true, "access_type": "public_method", "serialized_name": "includeIssuerInfo", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getIncludeIssuerInfo", "setter": "setIncludeIssuerInfo"}, "type": "boolean"}}}, "net\\authorize\\api\\contract\\v1\\GetCustomerPaymentProfileResponse": {"xml_root_name": "getCustomerPaymentProfileResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"paymentProfile": {"expose": true, "access_type": "public_method", "serialized_name": "paymentProfile", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaymentProfile", "setter": "setPaymentProfile"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerPaymentProfileMaskedType"}}}, "net\\authorize\\api\\contract\\v1\\GetCustomerProfileIdsRequest": {"xml_root_name": "getCustomerProfileIdsRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": []}, "net\\authorize\\api\\contract\\v1\\GetCustomerProfileIdsResponse": {"xml_root_name": "getCustomerProfileIdsResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"ids": {"expose": true, "access_type": "public_method", "serialized_name": "ids", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getIds", "setter": "setIds"}, "type": "array<string>", "xml_list": {"inline": false, "skip_when_empty": false, "entry_name": "numericString", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}}}, "net\\authorize\\api\\contract\\v1\\GetCustomerProfileRequest": {"xml_root_name": "getCustomerProfileRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "merchantCustomerId": {"expose": true, "access_type": "public_method", "serialized_name": "merchantCustomerId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMerchantCustomerId", "setter": "setMerchantCustomerId"}, "type": "string"}, "email": {"expose": true, "access_type": "public_method", "serialized_name": "email", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEmail", "setter": "setEmail"}, "type": "string"}, "unmaskExpirationDate": {"expose": true, "access_type": "public_method", "serialized_name": "unmaskExpirationDate", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getUnmaskExpirationDate", "setter": "setUnmaskExpirationDate"}, "type": "boolean"}, "includeIssuerInfo": {"expose": true, "access_type": "public_method", "serialized_name": "includeIssuerInfo", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getIncludeIssuerInfo", "setter": "setIncludeIssuerInfo"}, "type": "boolean"}}}, "net\\authorize\\api\\contract\\v1\\GetCustomerProfileResponse": {"xml_root_name": "getCustomerProfileResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"profile": {"expose": true, "access_type": "public_method", "serialized_name": "profile", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProfile", "setter": "setProfile"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerProfileMaskedType"}, "subscriptionIds": {"expose": true, "access_type": "public_method", "serialized_name": "subscriptionIds", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubscriptionIds", "setter": "setSubscriptionIds"}, "type": "array<string>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "subscriptionId", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}}}, "net\\authorize\\api\\contract\\v1\\GetCustomerShippingAddressRequest": {"xml_root_name": "getCustomerShippingAddressRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "customerAddressId": {"expose": true, "access_type": "public_method", "serialized_name": "customerAddressId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerAddressId", "setter": "setCustomerAddressId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\GetCustomerShippingAddressResponse": {"xml_root_name": "getCustomerShippingAddressResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"defaultShippingAddress": {"expose": true, "access_type": "public_method", "serialized_name": "defaultShippingAddress", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDefaultShippingAddress", "setter": "setDefaultShippingAddress"}, "type": "boolean"}, "address": {"expose": true, "access_type": "public_method", "serialized_name": "address", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "get<PERSON><PERSON><PERSON>", "setter": "<PERSON><PERSON><PERSON><PERSON>"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerAddressExType"}, "subscriptionIds": {"expose": true, "access_type": "public_method", "serialized_name": "subscriptionIds", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubscriptionIds", "setter": "setSubscriptionIds"}, "type": "array<string>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "subscriptionId", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}}}, "net\\authorize\\api\\contract\\v1\\GetHostedPaymentPageRequest": {"xml_root_name": "getHostedPaymentPageRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"transactionRequest": {"expose": true, "access_type": "public_method", "serialized_name": "transactionRequest", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransactionRequest", "setter": "setTransactionRequest"}, "type": "net\\authorize\\api\\contract\\v1\\TransactionRequestType"}, "hostedPaymentSettings": {"expose": true, "access_type": "public_method", "serialized_name": "hostedPaymentSettings", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getHostedPaymentSettings", "setter": "setHostedPaymentSettings"}, "type": "array<net\\authorize\\api\\contract\\v1\\SettingType>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "setting", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}}}, "net\\authorize\\api\\contract\\v1\\GetHostedPaymentPageResponse": {"xml_root_name": "getHostedPaymentPageResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"token": {"expose": true, "access_type": "public_method", "serialized_name": "token", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getToken", "setter": "setToken"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\GetHostedProfilePageRequest": {"xml_root_name": "getHostedProfilePageRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "hostedProfileSettings": {"expose": true, "access_type": "public_method", "serialized_name": "hostedProfileSettings", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getHostedProfileSettings", "setter": "setHostedProfileSettings"}, "type": "array<net\\authorize\\api\\contract\\v1\\SettingType>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "setting", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}}}, "net\\authorize\\api\\contract\\v1\\GetHostedProfilePageResponse": {"xml_root_name": "getHostedProfilePageResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"token": {"expose": true, "access_type": "public_method", "serialized_name": "token", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getToken", "setter": "setToken"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\GetMerchantDetailsRequest": {"xml_root_name": "getMerchantDetailsRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": []}, "net\\authorize\\api\\contract\\v1\\GetMerchantDetailsResponse": {"xml_root_name": "getMerchantDetailsResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"isTestMode": {"expose": true, "access_type": "public_method", "serialized_name": "isTestMode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getIsTestMode", "setter": "setIsTestMode"}, "type": "boolean"}, "processors": {"expose": true, "access_type": "public_method", "serialized_name": "processors", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProcessors", "setter": "setProcessors"}, "type": "array<net\\authorize\\api\\contract\\v1\\ProcessorType>", "xml_list": {"inline": false, "skip_when_empty": false, "entry_name": "processor", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "merchantName": {"expose": true, "access_type": "public_method", "serialized_name": "merchantName", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMerchantName", "setter": "setMerchantName"}, "type": "string"}, "gatewayId": {"expose": true, "access_type": "public_method", "serialized_name": "gatewayId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getGatewayId", "setter": "setGatewayId"}, "type": "string"}, "marketTypes": {"expose": true, "access_type": "public_method", "serialized_name": "marketTypes", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMarketTypes", "setter": "setMarketTypes"}, "type": "array<string>", "xml_list": {"inline": false, "skip_when_empty": false, "entry_name": "marketType", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "productCodes": {"expose": true, "access_type": "public_method", "serialized_name": "productCodes", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProductCodes", "setter": "setProductCodes"}, "type": "array<string>", "xml_list": {"inline": false, "skip_when_empty": false, "entry_name": "productCode", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "paymentMethods": {"expose": true, "access_type": "public_method", "serialized_name": "paymentMethods", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaymentMethods", "setter": "setPaymentMethods"}, "type": "array<string>", "xml_list": {"inline": false, "skip_when_empty": false, "entry_name": "paymentMethod", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "currencies": {"expose": true, "access_type": "public_method", "serialized_name": "currencies", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCurrencies", "setter": "setCurrencies"}, "type": "array<string>", "xml_list": {"inline": false, "skip_when_empty": false, "entry_name": "currency", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "publicClientKey": {"expose": true, "access_type": "public_method", "serialized_name": "publicClientKey", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPublicClientKey", "setter": "setPublicClientKey"}, "type": "string"}, "businessInformation": {"expose": true, "access_type": "public_method", "serialized_name": "businessInformation", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getBusinessInformation", "setter": "setBusinessInformation"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerAddressType"}, "merchantTimeZone": {"expose": true, "access_type": "public_method", "serialized_name": "merchantTimeZone", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMerchantTimeZone", "setter": "setMerchantTimeZone"}, "type": "string"}, "contactDetails": {"expose": true, "access_type": "public_method", "serialized_name": "contactDetails", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getContactDetails", "setter": "setContactDetails"}, "type": "array<net\\authorize\\api\\contract\\v1\\ContactDetailType>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "contactDetail", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}}}, "net\\authorize\\api\\contract\\v1\\GetSettledBatchListRequest": {"xml_root_name": "getSettledBatchListRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"includeStatistics": {"expose": true, "access_type": "public_method", "serialized_name": "includeStatistics", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getIncludeStatistics", "setter": "setIncludeStatistics"}, "type": "boolean"}, "firstSettlementDate": {"expose": true, "access_type": "public_method", "serialized_name": "firstSettlementDate", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getFirstSettlementDate", "setter": "setFirstSettlementDate"}, "type": "GoetasWebservices\\Xsd\\XsdToPhp\\XMLSchema\\DateTime"}, "lastSettlementDate": {"expose": true, "access_type": "public_method", "serialized_name": "lastSettlementDate", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getLastSettlementDate", "setter": "setLastSettlementDate"}, "type": "GoetasWebservices\\Xsd\\XsdToPhp\\XMLSchema\\DateTime"}}}, "net\\authorize\\api\\contract\\v1\\GetSettledBatchListResponse": {"xml_root_name": "getSettledBatchListResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"batchList": {"expose": true, "access_type": "public_method", "serialized_name": "batchList", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getBatchList", "setter": "setBatchList"}, "type": "array<net\\authorize\\api\\contract\\v1\\BatchDetailsType>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "batch", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}}}, "net\\authorize\\api\\contract\\v1\\GetTransactionDetailsRequest": {"xml_root_name": "getTransactionDetailsRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"transId": {"expose": true, "access_type": "public_method", "serialized_name": "transId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransId", "setter": "setTransId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\GetTransactionDetailsResponse": {"xml_root_name": "getTransactionDetailsResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"transaction": {"expose": true, "access_type": "public_method", "serialized_name": "transaction", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransaction", "setter": "setTransaction"}, "type": "net\\authorize\\api\\contract\\v1\\TransactionDetailsType"}, "clientId": {"expose": true, "access_type": "public_method", "serialized_name": "clientId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getClientId", "setter": "setClientId"}, "type": "string"}, "transrefId": {"expose": true, "access_type": "public_method", "serialized_name": "transrefId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransrefId", "setter": "setTransrefId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\GetTransactionListForCustomerRequest": {"xml_root_name": "getTransactionListForCustomerRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "customerPaymentProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerPaymentProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerPaymentProfileId", "setter": "setCustomerPaymentProfileId"}, "type": "string"}, "sorting": {"expose": true, "access_type": "public_method", "serialized_name": "sorting", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSorting", "setter": "setSorting"}, "type": "net\\authorize\\api\\contract\\v1\\TransactionListSortingType"}, "paging": {"expose": true, "access_type": "public_method", "serialized_name": "paging", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaging", "setter": "setPaging"}, "type": "net\\authorize\\api\\contract\\v1\\PagingType"}}}, "net\\authorize\\api\\contract\\v1\\GetTransactionListRequest": {"xml_root_name": "getTransactionListRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"batchId": {"expose": true, "access_type": "public_method", "serialized_name": "batchId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getBatchId", "setter": "setBatchId"}, "type": "string"}, "sorting": {"expose": true, "access_type": "public_method", "serialized_name": "sorting", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSorting", "setter": "setSorting"}, "type": "net\\authorize\\api\\contract\\v1\\TransactionListSortingType"}, "paging": {"expose": true, "access_type": "public_method", "serialized_name": "paging", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaging", "setter": "setPaging"}, "type": "net\\authorize\\api\\contract\\v1\\PagingType"}}}, "net\\authorize\\api\\contract\\v1\\GetTransactionListResponse": {"xml_root_name": "getTransactionListResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"transactions": {"expose": true, "access_type": "public_method", "serialized_name": "transactions", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransactions", "setter": "setTransactions"}, "type": "array<net\\authorize\\api\\contract\\v1\\TransactionSummaryType>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "transaction", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "totalNumInResultSet": {"expose": true, "access_type": "public_method", "serialized_name": "totalNumInResultSet", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTotalNumInResultSet", "setter": "setTotalNumInResultSet"}, "type": "integer"}}}, "net\\authorize\\api\\contract\\v1\\GetUnsettledTransactionListRequest": {"xml_root_name": "getUnsettledTransactionListRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"status": {"expose": true, "access_type": "public_method", "serialized_name": "status", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getStatus", "setter": "setStatus"}, "type": "string"}, "sorting": {"expose": true, "access_type": "public_method", "serialized_name": "sorting", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSorting", "setter": "setSorting"}, "type": "net\\authorize\\api\\contract\\v1\\TransactionListSortingType"}, "paging": {"expose": true, "access_type": "public_method", "serialized_name": "paging", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaging", "setter": "setPaging"}, "type": "net\\authorize\\api\\contract\\v1\\PagingType"}}}, "net\\authorize\\api\\contract\\v1\\GetUnsettledTransactionListResponse": {"xml_root_name": "getUnsettledTransactionListResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"transactions": {"expose": true, "access_type": "public_method", "serialized_name": "transactions", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransactions", "setter": "setTransactions"}, "type": "array<net\\authorize\\api\\contract\\v1\\TransactionSummaryType>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "transaction", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "totalNumInResultSet": {"expose": true, "access_type": "public_method", "serialized_name": "totalNumInResultSet", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTotalNumInResultSet", "setter": "setTotalNumInResultSet"}, "type": "integer"}}}, "net\\authorize\\api\\contract\\v1\\HeldTransactionRequestType": {"properties": {"action": {"expose": true, "access_type": "public_method", "serialized_name": "action", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAction", "setter": "setAction"}, "type": "string"}, "refTransId": {"expose": true, "access_type": "public_method", "serialized_name": "refTransId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getRefTransId", "setter": "setRefTransId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\ImpersonationAuthenticationType": {"properties": {"partnerLoginId": {"expose": true, "access_type": "public_method", "serialized_name": "partnerLoginId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPartnerLoginId", "setter": "setPartnerLoginId"}, "type": "string"}, "partnerTransactionKey": {"expose": true, "access_type": "public_method", "serialized_name": "partnerTransactionKey", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPartnerTransactionKey", "setter": "setPartnerTransactionKey"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\IsAliveRequest": {"xml_root_name": "isAliveRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"refId": {"expose": true, "access_type": "public_method", "serialized_name": "refId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getRefId", "setter": "setRefId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\IsAliveResponse": {"xml_root_name": "isAliveResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": []}, "net\\authorize\\api\\contract\\v1\\KeyBlockType": {"properties": {"value": {"expose": true, "access_type": "public_method", "serialized_name": "Value", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getValue", "setter": "setValue"}, "type": "net\\authorize\\api\\contract\\v1\\KeyValueType"}}}, "net\\authorize\\api\\contract\\v1\\KeyManagementSchemeType\\DUKPTAType\\DeviceInfoAType": {"properties": {"description": {"expose": true, "access_type": "public_method", "serialized_name": "Description", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDescription", "setter": "setDescription"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\KeyManagementSchemeType\\DUKPTAType\\EncryptedDataAType": {"properties": {"value": {"expose": true, "access_type": "public_method", "serialized_name": "Value", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getValue", "setter": "setValue"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\KeyManagementSchemeType\\DUKPTAType\\ModeAType": {"properties": {"pIN": {"expose": true, "access_type": "public_method", "serialized_name": "PIN", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPIN", "setter": "setPIN"}, "type": "string"}, "data": {"expose": true, "access_type": "public_method", "serialized_name": "Data", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getData", "setter": "setData"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\KeyManagementSchemeType\\DUKPTAType": {"properties": {"operation": {"expose": true, "access_type": "public_method", "serialized_name": "Operation", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOperation", "setter": "setOperation"}, "type": "string"}, "mode": {"expose": true, "access_type": "public_method", "serialized_name": "Mode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMode", "setter": "setMode"}, "type": "net\\authorize\\api\\contract\\v1\\KeyManagementSchemeType\\DUKPTAType\\ModeAType"}, "deviceInfo": {"expose": true, "access_type": "public_method", "serialized_name": "DeviceInfo", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDeviceInfo", "setter": "setDeviceInfo"}, "type": "net\\authorize\\api\\contract\\v1\\KeyManagementSchemeType\\DUKPTAType\\DeviceInfoAType"}, "encryptedData": {"expose": true, "access_type": "public_method", "serialized_name": "EncryptedData", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEncryptedData", "setter": "setEncryptedData"}, "type": "net\\authorize\\api\\contract\\v1\\KeyManagementSchemeType\\DUKPTAType\\EncryptedDataAType"}}}, "net\\authorize\\api\\contract\\v1\\KeyManagementSchemeType": {"properties": {"dUKPT": {"expose": true, "access_type": "public_method", "serialized_name": "DUKPT", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDUKPT", "setter": "setDUKPT"}, "type": "net\\authorize\\api\\contract\\v1\\KeyManagementSchemeType\\DUKPTAType"}}}, "net\\authorize\\api\\contract\\v1\\KeyValueType": {"properties": {"encoding": {"expose": true, "access_type": "public_method", "serialized_name": "Encoding", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEncoding", "setter": "setEncoding"}, "type": "string"}, "encryptionAlgorithm": {"expose": true, "access_type": "public_method", "serialized_name": "EncryptionAlgorithm", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEncryptionAlgorithm", "setter": "setEncryptionAlgorithm"}, "type": "string"}, "scheme": {"expose": true, "access_type": "public_method", "serialized_name": "Scheme", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getScheme", "setter": "setScheme"}, "type": "net\\authorize\\api\\contract\\v1\\KeyManagementSchemeType"}}}, "net\\authorize\\api\\contract\\v1\\LineItemType": {"properties": {"itemId": {"expose": true, "access_type": "public_method", "serialized_name": "itemId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getItemId", "setter": "setItemId"}, "type": "string"}, "name": {"expose": true, "access_type": "public_method", "serialized_name": "name", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getName", "setter": "setName"}, "type": "string"}, "description": {"expose": true, "access_type": "public_method", "serialized_name": "description", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDescription", "setter": "setDescription"}, "type": "string"}, "quantity": {"expose": true, "access_type": "public_method", "serialized_name": "quantity", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getQuantity", "setter": "setQuantity"}, "type": "float"}, "unitPrice": {"expose": true, "access_type": "public_method", "serialized_name": "unitPrice", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getUnitPrice", "setter": "setUnitPrice"}, "type": "float"}, "taxable": {"expose": true, "access_type": "public_method", "serialized_name": "taxable", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTaxable", "setter": "setTaxable"}, "type": "boolean"}, "unitOfMeasure": {"expose": true, "access_type": "public_method", "serialized_name": "unitOfMeasure", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getUnitOfMeasure", "setter": "setUnitOfMeasure"}, "type": "string"}, "typeOfSupply": {"expose": true, "access_type": "public_method", "serialized_name": "typeOfSupply", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTypeOfSupply", "setter": "setTypeOfSupply"}, "type": "string"}, "taxRate": {"expose": true, "access_type": "public_method", "serialized_name": "taxRate", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTaxRate", "setter": "setTaxRate"}, "type": "float"}, "taxAmount": {"expose": true, "access_type": "public_method", "serialized_name": "taxAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTaxAmount", "setter": "setTaxAmount"}, "type": "float"}, "nationalTax": {"expose": true, "access_type": "public_method", "serialized_name": "nationalTax", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getNationalTax", "setter": "setNationalTax"}, "type": "float"}, "localTax": {"expose": true, "access_type": "public_method", "serialized_name": "localTax", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getLocalTax", "setter": "setLocalTax"}, "type": "float"}, "vatRate": {"expose": true, "access_type": "public_method", "serialized_name": "vatRate", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getVatRate", "setter": "setVatRate"}, "type": "float"}, "alternateTaxId": {"expose": true, "access_type": "public_method", "serialized_name": "alternateTaxId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAlternateTaxId", "setter": "setAlternateTaxId"}, "type": "string"}, "alternateTaxType": {"expose": true, "access_type": "public_method", "serialized_name": "alternateTaxType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAlternateTaxType", "setter": "setAlternateTaxType"}, "type": "string"}, "alternateTaxTypeApplied": {"expose": true, "access_type": "public_method", "serialized_name": "alternateTaxTypeApplied", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAlternateTaxTypeApplied", "setter": "setAlternateTaxTypeApplied"}, "type": "string"}, "alternateTaxRate": {"expose": true, "access_type": "public_method", "serialized_name": "alternateTaxRate", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAlternateTaxRate", "setter": "setAlternateTaxRate"}, "type": "float"}, "alternateTaxAmount": {"expose": true, "access_type": "public_method", "serialized_name": "alternateTaxAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAlternateTaxAmount", "setter": "setAlternateTaxAmount"}, "type": "float"}, "totalAmount": {"expose": true, "access_type": "public_method", "serialized_name": "totalAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTotalAmount", "setter": "setTotalAmount"}, "type": "float"}, "commodityCode": {"expose": true, "access_type": "public_method", "serialized_name": "commodityCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCommodityCode", "setter": "setCommodityCode"}, "type": "string"}, "productCode": {"expose": true, "access_type": "public_method", "serialized_name": "productCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProductCode", "setter": "setProductCode"}, "type": "string"}, "productSKU": {"expose": true, "access_type": "public_method", "serialized_name": "productSKU", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProductSKU", "setter": "setProductSKU"}, "type": "string"}, "discountRate": {"expose": true, "access_type": "public_method", "serialized_name": "discountRate", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDiscountRate", "setter": "setDiscountRate"}, "type": "float"}, "discountAmount": {"expose": true, "access_type": "public_method", "serialized_name": "discountAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDiscountAmount", "setter": "setDiscountAmount"}, "type": "float"}, "taxIncludedInTotal": {"expose": true, "access_type": "public_method", "serialized_name": "taxIncludedInTotal", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTaxIncludedInTotal", "setter": "setTaxIncludedInTotal"}, "type": "boolean"}, "taxIsAfterDiscount": {"expose": true, "access_type": "public_method", "serialized_name": "taxIsAfterDiscount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTaxIsAfterDiscount", "setter": "setTaxIsAfterDiscount"}, "type": "boolean"}}}, "net\\authorize\\api\\contract\\v1\\ListOfAUDetailsType": {"properties": {"auUpdate": {"expose": true, "access_type": "public_method", "serialized_name": "auUpdate", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAuUpdate", "setter": "setAuUpdate"}, "xml_list": {"inline": true, "entry_name": "auUpdate", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "type": "array<net\\authorize\\api\\contract\\v1\\AuUpdateType>"}, "auDelete": {"expose": true, "access_type": "public_method", "serialized_name": "auDelete", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAuDelete", "setter": "setAuDelete"}, "xml_list": {"inline": true, "entry_name": "auDelete", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "type": "array<net\\authorize\\api\\contract\\v1\\AuDeleteType>"}}}, "net\\authorize\\api\\contract\\v1\\LogoutRequest": {"xml_root_name": "logoutRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": []}, "net\\authorize\\api\\contract\\v1\\LogoutResponse": {"xml_root_name": "logoutResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": []}, "net\\authorize\\api\\contract\\v1\\MerchantAuthenticationType": {"properties": {"name": {"expose": true, "access_type": "public_method", "serialized_name": "name", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getName", "setter": "setName"}, "type": "string"}, "transactionKey": {"expose": true, "access_type": "public_method", "serialized_name": "transactionKey", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransactionKey", "setter": "setTransactionKey"}, "type": "string"}, "sessionToken": {"expose": true, "access_type": "public_method", "serialized_name": "sessionToken", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSessionToken", "setter": "setSessionToken"}, "type": "string"}, "password": {"expose": true, "access_type": "public_method", "serialized_name": "password", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPassword", "setter": "setPassword"}, "type": "string"}, "impersonationAuthentication": {"expose": true, "access_type": "public_method", "serialized_name": "impersonation<PERSON><PERSON><PERSON><PERSON>tion", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getImpersonationAuthentication", "setter": "setImpersonationAuthentication"}, "type": "net\\authorize\\api\\contract\\v1\\ImpersonationAuthenticationType"}, "fingerPrint": {"expose": true, "access_type": "public_method", "serialized_name": "fingerPrint", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getFingerPrint", "setter": "setFingerPrint"}, "type": "net\\authorize\\api\\contract\\v1\\FingerPrintType"}, "clientKey": {"expose": true, "access_type": "public_method", "serialized_name": "client<PERSON>ey", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getClientKey", "setter": "setClientKey"}, "type": "string"}, "accessToken": {"expose": true, "access_type": "public_method", "serialized_name": "accessToken", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAccessToken", "setter": "setAccessToken"}, "type": "string"}, "mobileDeviceId": {"expose": true, "access_type": "public_method", "serialized_name": "mobileDeviceId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMobileDeviceId", "setter": "setMobileDeviceId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\MerchantContactType": {"properties": {"merchantName": {"expose": true, "access_type": "public_method", "serialized_name": "merchantName", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMerchantName", "setter": "setMerchantName"}, "type": "string"}, "merchantAddress": {"expose": true, "access_type": "public_method", "serialized_name": "merchantAddress", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMerchantAddress", "setter": "setMerchantAddress"}, "type": "string"}, "merchantCity": {"expose": true, "access_type": "public_method", "serialized_name": "merchantCity", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMerchantCity", "setter": "setMerchantCity"}, "type": "string"}, "merchantState": {"expose": true, "access_type": "public_method", "serialized_name": "merchantState", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMerchantState", "setter": "setMerchantState"}, "type": "string"}, "merchantZip": {"expose": true, "access_type": "public_method", "serialized_name": "merchantZip", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMerchantZip", "setter": "setMerchantZip"}, "type": "string"}, "merchantPhone": {"expose": true, "access_type": "public_method", "serialized_name": "merchantPhone", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMerchantPhone", "setter": "setMerchantPhone"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\MessagesType\\MessageAType": {"properties": {"code": {"expose": true, "access_type": "public_method", "serialized_name": "code", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCode", "setter": "setCode"}, "type": "string"}, "text": {"expose": true, "access_type": "public_method", "serialized_name": "text", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getText", "setter": "setText"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\MessagesType": {"properties": {"resultCode": {"expose": true, "access_type": "public_method", "serialized_name": "resultCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getResultCode", "setter": "setResultCode"}, "type": "string"}, "message": {"expose": true, "access_type": "public_method", "serialized_name": "message", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMessage", "setter": "setMessage"}, "xml_list": {"inline": true, "entry_name": "message", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "type": "array<net\\authorize\\api\\contract\\v1\\MessagesType\\MessageAType>"}}}, "net\\authorize\\api\\contract\\v1\\MobileDeviceLoginRequest": {"xml_root_name": "mobileDeviceLoginRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": []}, "net\\authorize\\api\\contract\\v1\\MobileDeviceLoginResponse": {"xml_root_name": "mobileDeviceLoginResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"merchantContact": {"expose": true, "access_type": "public_method", "serialized_name": "merchantContact", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMerchantContact", "setter": "setMerchantContact"}, "type": "net\\authorize\\api\\contract\\v1\\MerchantContactType"}, "userPermissions": {"expose": true, "access_type": "public_method", "serialized_name": "userPermissions", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getUserPermissions", "setter": "setUserPermissions"}, "type": "array<net\\authorize\\api\\contract\\v1\\PermissionType>", "xml_list": {"inline": false, "skip_when_empty": false, "entry_name": "permission", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "merchantAccount": {"expose": true, "access_type": "public_method", "serialized_name": "merchantAccount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMerchantAccount", "setter": "setMerchantAccount"}, "type": "net\\authorize\\api\\contract\\v1\\TransRetailInfoType"}}}, "net\\authorize\\api\\contract\\v1\\MobileDeviceRegistrationRequest": {"xml_root_name": "mobileDeviceRegistrationRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"mobileDevice": {"expose": true, "access_type": "public_method", "serialized_name": "mobileDevice", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMobileDevice", "setter": "setMobileDevice"}, "type": "net\\authorize\\api\\contract\\v1\\MobileDeviceType"}}}, "net\\authorize\\api\\contract\\v1\\MobileDeviceRegistrationResponse": {"xml_root_name": "mobileDeviceRegistrationResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": []}, "net\\authorize\\api\\contract\\v1\\MobileDeviceType": {"properties": {"mobileDeviceId": {"expose": true, "access_type": "public_method", "serialized_name": "mobileDeviceId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMobileDeviceId", "setter": "setMobileDeviceId"}, "type": "string"}, "description": {"expose": true, "access_type": "public_method", "serialized_name": "description", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDescription", "setter": "setDescription"}, "type": "string"}, "phoneNumber": {"expose": true, "access_type": "public_method", "serialized_name": "phoneNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPhoneNumber", "setter": "setPhoneNumber"}, "type": "string"}, "devicePlatform": {"expose": true, "access_type": "public_method", "serialized_name": "devicePlatform", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDevicePlatform", "setter": "setDevicePlatform"}, "type": "string"}, "deviceActivation": {"expose": true, "access_type": "public_method", "serialized_name": "deviceActivation", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDeviceActivation", "setter": "setDeviceActivation"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\NameAndAddressType": {"properties": {"firstName": {"expose": true, "access_type": "public_method", "serialized_name": "firstName", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getFirstName", "setter": "setFirstName"}, "type": "string"}, "lastName": {"expose": true, "access_type": "public_method", "serialized_name": "lastName", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getLastName", "setter": "setLastName"}, "type": "string"}, "company": {"expose": true, "access_type": "public_method", "serialized_name": "company", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCompany", "setter": "setCompany"}, "type": "string"}, "address": {"expose": true, "access_type": "public_method", "serialized_name": "address", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "get<PERSON><PERSON><PERSON>", "setter": "<PERSON><PERSON><PERSON><PERSON>"}, "type": "string"}, "city": {"expose": true, "access_type": "public_method", "serialized_name": "city", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCity", "setter": "setCity"}, "type": "string"}, "state": {"expose": true, "access_type": "public_method", "serialized_name": "state", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getState", "setter": "setState"}, "type": "string"}, "zip": {"expose": true, "access_type": "public_method", "serialized_name": "zip", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "get<PERSON><PERSON>", "setter": "<PERSON><PERSON><PERSON>"}, "type": "string"}, "country": {"expose": true, "access_type": "public_method", "serialized_name": "country", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCountry", "setter": "setCountry"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\OpaqueDataType": {"properties": {"dataDescriptor": {"expose": true, "access_type": "public_method", "serialized_name": "dataDescriptor", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDataDescriptor", "setter": "setDataDescriptor"}, "type": "string"}, "dataValue": {"expose": true, "access_type": "public_method", "serialized_name": "dataValue", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDataValue", "setter": "setDataValue"}, "type": "string"}, "dataKey": {"expose": true, "access_type": "public_method", "serialized_name": "dataKey", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDataKey", "setter": "setData<PERSON>ey"}, "type": "string"}, "expirationTimeStamp": {"expose": true, "access_type": "public_method", "serialized_name": "expirationTimeStamp", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getExpirationTimeStamp", "setter": "setExpirationTimeStamp"}, "type": "GoetasWebservices\\Xsd\\XsdToPhp\\XMLSchema\\DateTime"}}}, "net\\authorize\\api\\contract\\v1\\OrderExType": {"properties": {"purchaseOrderNumber": {"expose": true, "access_type": "public_method", "serialized_name": "purchaseOrderNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPurchaseOrderNumber", "setter": "setPurchaseOrderNumber"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\OrderType": {"properties": {"invoiceNumber": {"expose": true, "access_type": "public_method", "serialized_name": "invoiceNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getInvoiceNumber", "setter": "setInvoiceNumber"}, "type": "string"}, "description": {"expose": true, "access_type": "public_method", "serialized_name": "description", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDescription", "setter": "setDescription"}, "type": "string"}, "discountAmount": {"expose": true, "access_type": "public_method", "serialized_name": "discountAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDiscountAmount", "setter": "setDiscountAmount"}, "type": "float"}, "taxIsAfterDiscount": {"expose": true, "access_type": "public_method", "serialized_name": "taxIsAfterDiscount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTaxIsAfterDiscount", "setter": "setTaxIsAfterDiscount"}, "type": "boolean"}, "totalTaxTypeCode": {"expose": true, "access_type": "public_method", "serialized_name": "totalTaxTypeCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTotalTaxTypeCode", "setter": "setTotalTaxTypeCode"}, "type": "string"}, "purchaserVATRegistrationNumber": {"expose": true, "access_type": "public_method", "serialized_name": "purchaserVATRegistrationNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPurchaserVATRegistrationNumber", "setter": "setPurchaserVATRegistrationNumber"}, "type": "string"}, "merchantVATRegistrationNumber": {"expose": true, "access_type": "public_method", "serialized_name": "merchantVATRegistrationNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMerchantVATRegistrationNumber", "setter": "setMerchantVATRegistrationNumber"}, "type": "string"}, "vatInvoiceReferenceNumber": {"expose": true, "access_type": "public_method", "serialized_name": "vatInvoiceReferenceNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getVatInvoiceReferenceNumber", "setter": "setVatInvoiceReferenceNumber"}, "type": "string"}, "purchaserCode": {"expose": true, "access_type": "public_method", "serialized_name": "purchaserCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPurchaserCode", "setter": "setPurchaserCode"}, "type": "string"}, "summaryCommodityCode": {"expose": true, "access_type": "public_method", "serialized_name": "summaryCommodityCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSummaryCommodityCode", "setter": "setSummaryCommodityCode"}, "type": "string"}, "purchaseOrderDateUTC": {"expose": true, "access_type": "public_method", "serialized_name": "purchaseOrderDateUTC", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPurchaseOrderDateUTC", "setter": "setPurchaseOrderDateUTC"}, "type": "DateTime<'Y-m-d'>"}, "supplierOrderReference": {"expose": true, "access_type": "public_method", "serialized_name": "supplierOrderReference", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSupplierOrderReference", "setter": "setSupplierOrderReference"}, "type": "string"}, "authorizedContactName": {"expose": true, "access_type": "public_method", "serialized_name": "authorizedContactName", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAuthorizedContactName", "setter": "setAuthorizedContactName"}, "type": "string"}, "cardAcceptorRefNumber": {"expose": true, "access_type": "public_method", "serialized_name": "cardAcceptorRefNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCardAcceptorRefNumber", "setter": "setCardAcceptorRefNumber"}, "type": "string"}, "amexDataTAA1": {"expose": true, "access_type": "public_method", "serialized_name": "amexDataTAA1", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAmexDataTAA1", "setter": "setAmexDataTAA1"}, "type": "string"}, "amexDataTAA2": {"expose": true, "access_type": "public_method", "serialized_name": "amexDataTAA2", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAmexDataTAA2", "setter": "setAmexDataTAA2"}, "type": "string"}, "amexDataTAA3": {"expose": true, "access_type": "public_method", "serialized_name": "amexDataTAA3", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAmexDataTAA3", "setter": "setAmexDataTAA3"}, "type": "string"}, "amexDataTAA4": {"expose": true, "access_type": "public_method", "serialized_name": "amexDataTAA4", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAmexDataTAA4", "setter": "setAmexDataTAA4"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\OtherTaxType": {"properties": {"nationalTaxAmount": {"expose": true, "access_type": "public_method", "serialized_name": "nationalTaxAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getNationalTaxAmount", "setter": "setNationalTaxAmount"}, "type": "float"}, "localTaxAmount": {"expose": true, "access_type": "public_method", "serialized_name": "localTaxAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getLocalTaxAmount", "setter": "setLocalTaxAmount"}, "type": "float"}, "alternateTaxAmount": {"expose": true, "access_type": "public_method", "serialized_name": "alternateTaxAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAlternateTaxAmount", "setter": "setAlternateTaxAmount"}, "type": "float"}, "alternateTaxId": {"expose": true, "access_type": "public_method", "serialized_name": "alternateTaxId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAlternateTaxId", "setter": "setAlternateTaxId"}, "type": "string"}, "vatTaxRate": {"expose": true, "access_type": "public_method", "serialized_name": "vatTaxRate", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getVatTaxRate", "setter": "setVatTaxRate"}, "type": "float"}, "vatTaxAmount": {"expose": true, "access_type": "public_method", "serialized_name": "vatTaxAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getVatTaxAmount", "setter": "setVatTaxAmount"}, "type": "float"}}}, "net\\authorize\\api\\contract\\v1\\PagingType": {"properties": {"limit": {"expose": true, "access_type": "public_method", "serialized_name": "limit", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getLimit", "setter": "setLimit"}, "type": "integer"}, "offset": {"expose": true, "access_type": "public_method", "serialized_name": "offset", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOffset", "setter": "setOffset"}, "type": "integer"}}}, "net\\authorize\\api\\contract\\v1\\PayPalType": {"properties": {"successUrl": {"expose": true, "access_type": "public_method", "serialized_name": "successUrl", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSuccessUrl", "setter": "setSuccessUrl"}, "type": "string"}, "cancelUrl": {"expose": true, "access_type": "public_method", "serialized_name": "cancelUrl", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCancelUrl", "setter": "setCancelUrl"}, "type": "string"}, "paypalLc": {"expose": true, "access_type": "public_method", "serialized_name": "paypalLc", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaypalLc", "setter": "setPaypalLc"}, "type": "string"}, "paypalHdrImg": {"expose": true, "access_type": "public_method", "serialized_name": "paypalHdrImg", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaypalHdrImg", "setter": "setPaypalHdrImg"}, "type": "string"}, "paypalPayflowcolor": {"expose": true, "access_type": "public_method", "serialized_name": "paypalPayflowcolor", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaypalPayflowcolor", "setter": "setPaypalPayflowcolor"}, "type": "string"}, "payerID": {"expose": true, "access_type": "public_method", "serialized_name": "payerID", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPayerID", "setter": "setPayerID"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\PaymentDetailsType": {"properties": {"currency": {"expose": true, "access_type": "public_method", "serialized_name": "currency", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCurrency", "setter": "setCurrency"}, "type": "string"}, "promoCode": {"expose": true, "access_type": "public_method", "serialized_name": "promoCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPromoCode", "setter": "setPromoCode"}, "type": "string"}, "misc": {"expose": true, "access_type": "public_method", "serialized_name": "misc", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMisc", "setter": "setMisc"}, "type": "string"}, "giftWrap": {"expose": true, "access_type": "public_method", "serialized_name": "giftWrap", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getGiftWrap", "setter": "setGiftWrap"}, "type": "string"}, "discount": {"expose": true, "access_type": "public_method", "serialized_name": "discount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDiscount", "setter": "setDiscount"}, "type": "string"}, "tax": {"expose": true, "access_type": "public_method", "serialized_name": "tax", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTax", "setter": "setTax"}, "type": "string"}, "shippingHandling": {"expose": true, "access_type": "public_method", "serialized_name": "shippingHandling", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getShippingHandling", "setter": "setShippingHandling"}, "type": "string"}, "subTotal": {"expose": true, "access_type": "public_method", "serialized_name": "subTotal", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubTotal", "setter": "setSubTotal"}, "type": "string"}, "orderID": {"expose": true, "access_type": "public_method", "serialized_name": "orderID", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOrderID", "setter": "setOrderID"}, "type": "string"}, "amount": {"expose": true, "access_type": "public_method", "serialized_name": "amount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAmount", "setter": "setAmount"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\PaymentEmvType": {"properties": {"emvData": {"expose": true, "access_type": "public_method", "serialized_name": "emvData", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEmvData", "setter": "setEmvData"}, "type": "W3\\XMLSchema\\2001\\AnyType"}, "emvDescriptor": {"expose": true, "access_type": "public_method", "serialized_name": "emvDescriptor", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEmvDescriptor", "setter": "setEmvDescriptor"}, "type": "W3\\XMLSchema\\2001\\AnyType"}, "emvVersion": {"expose": true, "access_type": "public_method", "serialized_name": "emvVersion", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEmvVersion", "setter": "setEmvVersion"}, "type": "W3\\XMLSchema\\2001\\AnyType"}}}, "net\\authorize\\api\\contract\\v1\\PaymentMaskedType": {"properties": {"creditCard": {"expose": true, "access_type": "public_method", "serialized_name": "creditCard", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCreditCard", "setter": "setCreditCard"}, "type": "net\\authorize\\api\\contract\\v1\\CreditCardMaskedType"}, "bankAccount": {"expose": true, "access_type": "public_method", "serialized_name": "bankAccount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getBankAccount", "setter": "set<PERSON>ankAccount"}, "type": "net\\authorize\\api\\contract\\v1\\BankAccountMaskedType"}, "tokenInformation": {"expose": true, "access_type": "public_method", "serialized_name": "tokenInformation", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTokenInformation", "setter": "setTokenInformation"}, "type": "net\\authorize\\api\\contract\\v1\\TokenMaskedType"}}}, "net\\authorize\\api\\contract\\v1\\PaymentProfileType": {"properties": {"paymentProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "paymentProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaymentProfileId", "setter": "setPaymentProfileId"}, "type": "string"}, "cardCode": {"expose": true, "access_type": "public_method", "serialized_name": "cardCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCardCode", "setter": "setCardCode"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\PaymentScheduleType\\IntervalAType": {"properties": {"length": {"expose": true, "access_type": "public_method", "serialized_name": "length", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "<PERSON><PERSON><PERSON><PERSON>", "setter": "<PERSON><PERSON><PERSON><PERSON>"}, "type": "integer"}, "unit": {"expose": true, "access_type": "public_method", "serialized_name": "unit", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getUnit", "setter": "setUnit"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\PaymentScheduleType": {"properties": {"interval": {"expose": true, "access_type": "public_method", "serialized_name": "interval", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getInterval", "setter": "setInterval"}, "type": "net\\authorize\\api\\contract\\v1\\PaymentScheduleType\\IntervalAType"}, "startDate": {"expose": true, "access_type": "public_method", "serialized_name": "startDate", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getStartDate", "setter": "setStartDate"}, "type": "DateTime<'Y-m-d'>"}, "totalOccurrences": {"expose": true, "access_type": "public_method", "serialized_name": "totalOccurrences", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTotalOccurrences", "setter": "setTotalOccurrences"}, "type": "integer"}, "trialOccurrences": {"expose": true, "access_type": "public_method", "serialized_name": "trialOccurrences", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTrialOccurrences", "setter": "setTrialOccurrences"}, "type": "integer"}}}, "net\\authorize\\api\\contract\\v1\\PaymentSimpleType": {"properties": {"creditCard": {"expose": true, "access_type": "public_method", "serialized_name": "creditCard", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCreditCard", "setter": "setCreditCard"}, "type": "net\\authorize\\api\\contract\\v1\\CreditCardSimpleType"}, "bankAccount": {"expose": true, "access_type": "public_method", "serialized_name": "bankAccount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getBankAccount", "setter": "set<PERSON>ankAccount"}, "type": "net\\authorize\\api\\contract\\v1\\BankAccountType"}}}, "net\\authorize\\api\\contract\\v1\\PaymentType": {"properties": {"creditCard": {"expose": true, "access_type": "public_method", "serialized_name": "creditCard", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCreditCard", "setter": "setCreditCard"}, "type": "net\\authorize\\api\\contract\\v1\\CreditCardType"}, "bankAccount": {"expose": true, "access_type": "public_method", "serialized_name": "bankAccount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getBankAccount", "setter": "set<PERSON>ankAccount"}, "type": "net\\authorize\\api\\contract\\v1\\BankAccountType"}, "trackData": {"expose": true, "access_type": "public_method", "serialized_name": "trackData", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTrackData", "setter": "setTrackData"}, "type": "net\\authorize\\api\\contract\\v1\\CreditCardTrackType"}, "encryptedTrackData": {"expose": true, "access_type": "public_method", "serialized_name": "encryptedTrackData", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEncryptedTrackData", "setter": "setEncryptedTrackData"}, "type": "net\\authorize\\api\\contract\\v1\\EncryptedTrackDataType"}, "payPal": {"expose": true, "access_type": "public_method", "serialized_name": "payPal", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPayPal", "setter": "setPayPal"}, "type": "net\\authorize\\api\\contract\\v1\\PayPalType"}, "opaqueData": {"expose": true, "access_type": "public_method", "serialized_name": "opaqueData", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOpaqueData", "setter": "setOpaqueData"}, "type": "net\\authorize\\api\\contract\\v1\\OpaqueDataType"}, "emv": {"expose": true, "access_type": "public_method", "serialized_name": "emv", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEmv", "setter": "setEmv"}, "type": "net\\authorize\\api\\contract\\v1\\PaymentEmvType"}, "dataSource": {"expose": true, "access_type": "public_method", "serialized_name": "dataSource", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDataSource", "setter": "setDataSource"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\PermissionType": {"properties": {"permissionName": {"expose": true, "access_type": "public_method", "serialized_name": "permissionName", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPermissionName", "setter": "setPermissionName"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\ProcessingOptionsType": {"properties": {"isFirstRecurringPayment": {"expose": true, "access_type": "public_method", "serialized_name": "isFirstRecurringPayment", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getIsFirstRecurringPayment", "setter": "setIsFirstRecurringPayment"}, "type": "boolean"}, "isFirstSubsequentAuth": {"expose": true, "access_type": "public_method", "serialized_name": "isFirstSubsequentAuth", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getIsFirstSubsequentAuth", "setter": "setIsFirstSubsequentAuth"}, "type": "boolean"}, "isSubsequentAuth": {"expose": true, "access_type": "public_method", "serialized_name": "isSubsequentAuth", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getIsSubsequentAuth", "setter": "setIsSubsequentAuth"}, "type": "boolean"}, "isStoredCredentials": {"expose": true, "access_type": "public_method", "serialized_name": "isStoredCredentials", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getIsStoredCredentials", "setter": "setIsStoredCredentials"}, "type": "boolean"}}}, "net\\authorize\\api\\contract\\v1\\ProcessorType": {"properties": {"name": {"expose": true, "access_type": "public_method", "serialized_name": "name", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getName", "setter": "setName"}, "type": "string"}, "id": {"expose": true, "access_type": "public_method", "serialized_name": "id", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getId", "setter": "setId"}, "type": "integer"}, "cardTypes": {"expose": true, "access_type": "public_method", "serialized_name": "cardTypes", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCardTypes", "setter": "setCardTypes"}, "type": "array<string>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "cardType", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}}}, "net\\authorize\\api\\contract\\v1\\ProfileTransAmountType": {"properties": {"amount": {"expose": true, "access_type": "public_method", "serialized_name": "amount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAmount", "setter": "setAmount"}, "type": "float"}, "tax": {"expose": true, "access_type": "public_method", "serialized_name": "tax", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTax", "setter": "setTax"}, "type": "net\\authorize\\api\\contract\\v1\\ExtendedAmountType"}, "shipping": {"expose": true, "access_type": "public_method", "serialized_name": "shipping", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getShipping", "setter": "setShipping"}, "type": "net\\authorize\\api\\contract\\v1\\ExtendedAmountType"}, "duty": {"expose": true, "access_type": "public_method", "serialized_name": "duty", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "get<PERSON>uty", "setter": "<PERSON><PERSON><PERSON><PERSON>"}, "type": "net\\authorize\\api\\contract\\v1\\ExtendedAmountType"}, "lineItems": {"expose": true, "access_type": "public_method", "serialized_name": "lineItems", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getLineItems", "setter": "setLineItems"}, "xml_list": {"inline": true, "entry_name": "lineItems", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "type": "array<net\\authorize\\api\\contract\\v1\\LineItemType>"}}}, "net\\authorize\\api\\contract\\v1\\ProfileTransAuthCaptureType": {"properties": []}, "net\\authorize\\api\\contract\\v1\\ProfileTransAuthOnlyType": {"properties": []}, "net\\authorize\\api\\contract\\v1\\ProfileTransCaptureOnlyType": {"properties": {"approvalCode": {"expose": true, "access_type": "public_method", "serialized_name": "approvalCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getApprovalCode", "setter": "setApprovalCode"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\ProfileTransOrderType": {"properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "customerPaymentProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerPaymentProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerPaymentProfileId", "setter": "setCustomerPaymentProfileId"}, "type": "string"}, "customerShippingAddressId": {"expose": true, "access_type": "public_method", "serialized_name": "customerShippingAddressId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerShippingAddressId", "setter": "setCustomerShippingAddressId"}, "type": "string"}, "order": {"expose": true, "access_type": "public_method", "serialized_name": "order", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOrder", "setter": "setOrder"}, "type": "net\\authorize\\api\\contract\\v1\\OrderExType"}, "taxExempt": {"expose": true, "access_type": "public_method", "serialized_name": "taxExempt", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTaxExempt", "setter": "setTaxExempt"}, "type": "boolean"}, "recurringBilling": {"expose": true, "access_type": "public_method", "serialized_name": "recurringBilling", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getRecurringBilling", "setter": "setRecurringBilling"}, "type": "boolean"}, "cardCode": {"expose": true, "access_type": "public_method", "serialized_name": "cardCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCardCode", "setter": "setCardCode"}, "type": "string"}, "splitTenderId": {"expose": true, "access_type": "public_method", "serialized_name": "splitTenderId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSplitTenderId", "setter": "setSplitTenderId"}, "type": "string"}, "processingOptions": {"expose": true, "access_type": "public_method", "serialized_name": "processingOptions", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProcessingOptions", "setter": "setProcessingOptions"}, "type": "net\\authorize\\api\\contract\\v1\\ProcessingOptionsType"}, "subsequentAuthInformation": {"expose": true, "access_type": "public_method", "serialized_name": "subsequentAuthInformation", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubsequentAuthInformation", "setter": "setSubsequentAuthInformation"}, "type": "net\\authorize\\api\\contract\\v1\\SubsequentAuthInformationType"}, "authorizationIndicatorType": {"expose": true, "access_type": "public_method", "serialized_name": "authorizationIndicatorType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAuthorizationIndicatorType", "setter": "setAuthorizationIndicatorType"}, "type": "net\\authorize\\api\\contract\\v1\\AuthorizationIndicatorType"}}}, "net\\authorize\\api\\contract\\v1\\ProfileTransPriorAuthCaptureType": {"properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "customerPaymentProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerPaymentProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerPaymentProfileId", "setter": "setCustomerPaymentProfileId"}, "type": "string"}, "customerShippingAddressId": {"expose": true, "access_type": "public_method", "serialized_name": "customerShippingAddressId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerShippingAddressId", "setter": "setCustomerShippingAddressId"}, "type": "string"}, "transId": {"expose": true, "access_type": "public_method", "serialized_name": "transId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransId", "setter": "setTransId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\ProfileTransRefundType": {"properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "customerPaymentProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerPaymentProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerPaymentProfileId", "setter": "setCustomerPaymentProfileId"}, "type": "string"}, "customerShippingAddressId": {"expose": true, "access_type": "public_method", "serialized_name": "customerShippingAddressId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerShippingAddressId", "setter": "setCustomerShippingAddressId"}, "type": "string"}, "creditCardNumberMasked": {"expose": true, "access_type": "public_method", "serialized_name": "creditCardNumberMasked", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCreditCardNumberMasked", "setter": "setCreditCardNumberMasked"}, "type": "string"}, "bankRoutingNumberMasked": {"expose": true, "access_type": "public_method", "serialized_name": "bankRoutingNumberMasked", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getBankRoutingNumberMasked", "setter": "setBankRoutingNumberMasked"}, "type": "string"}, "bankAccountNumberMasked": {"expose": true, "access_type": "public_method", "serialized_name": "bankAccountNumberMasked", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getBankAccountNumberMasked", "setter": "setBankAccountNumberMasked"}, "type": "string"}, "order": {"expose": true, "access_type": "public_method", "serialized_name": "order", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOrder", "setter": "setOrder"}, "type": "net\\authorize\\api\\contract\\v1\\OrderExType"}, "transId": {"expose": true, "access_type": "public_method", "serialized_name": "transId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransId", "setter": "setTransId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\ProfileTransVoidType": {"properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "customerPaymentProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerPaymentProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerPaymentProfileId", "setter": "setCustomerPaymentProfileId"}, "type": "string"}, "customerShippingAddressId": {"expose": true, "access_type": "public_method", "serialized_name": "customerShippingAddressId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerShippingAddressId", "setter": "setCustomerShippingAddressId"}, "type": "string"}, "transId": {"expose": true, "access_type": "public_method", "serialized_name": "transId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransId", "setter": "setTransId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\ProfileTransactionType": {"properties": {"profileTransAuthCapture": {"expose": true, "access_type": "public_method", "serialized_name": "profileTransAuthCapture", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProfileTransAuthCapture", "setter": "setProfileTransAuthCapture"}, "type": "net\\authorize\\api\\contract\\v1\\ProfileTransAuthCaptureType"}, "profileTransAuthOnly": {"expose": true, "access_type": "public_method", "serialized_name": "profileTransAuthOnly", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProfileTransAuthOnly", "setter": "setProfileTransAuthOnly"}, "type": "net\\authorize\\api\\contract\\v1\\ProfileTransAuthOnlyType"}, "profileTransPriorAuthCapture": {"expose": true, "access_type": "public_method", "serialized_name": "profileTransPriorAuthCapture", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProfileTransPriorAuthCapture", "setter": "setProfileTransPriorAuthCapture"}, "type": "net\\authorize\\api\\contract\\v1\\ProfileTransPriorAuthCaptureType"}, "profileTransCaptureOnly": {"expose": true, "access_type": "public_method", "serialized_name": "profileTransCaptureOnly", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProfileTransCaptureOnly", "setter": "setProfileTransCaptureOnly"}, "type": "net\\authorize\\api\\contract\\v1\\ProfileTransCaptureOnlyType"}, "profileTransRefund": {"expose": true, "access_type": "public_method", "serialized_name": "profileTransRefund", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProfileTransRefund", "setter": "setProfileTransRefund"}, "type": "net\\authorize\\api\\contract\\v1\\ProfileTransRefundType"}, "profileTransVoid": {"expose": true, "access_type": "public_method", "serialized_name": "profileTransVoid", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProfileTransVoid", "setter": "setProfileTransVoid"}, "type": "net\\authorize\\api\\contract\\v1\\ProfileTransVoidType"}}}, "net\\authorize\\api\\contract\\v1\\ReturnedItemType": {"properties": {"id": {"expose": true, "access_type": "public_method", "serialized_name": "id", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getId", "setter": "setId"}, "type": "string"}, "dateUTC": {"expose": true, "access_type": "public_method", "serialized_name": "dateUTC", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDateUTC", "setter": "setDateUTC"}, "type": "GoetasWebservices\\Xsd\\XsdToPhp\\XMLSchema\\DateTime"}, "dateLocal": {"expose": true, "access_type": "public_method", "serialized_name": "dateLocal", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDateLocal", "setter": "setDateLocal"}, "type": "GoetasWebservices\\Xsd\\XsdToPhp\\XMLSchema\\DateTime"}, "code": {"expose": true, "access_type": "public_method", "serialized_name": "code", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCode", "setter": "setCode"}, "type": "string"}, "description": {"expose": true, "access_type": "public_method", "serialized_name": "description", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDescription", "setter": "setDescription"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\SecurePaymentContainerErrorType": {"properties": {"code": {"expose": true, "access_type": "public_method", "serialized_name": "code", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCode", "setter": "setCode"}, "type": "string"}, "description": {"expose": true, "access_type": "public_method", "serialized_name": "description", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDescription", "setter": "setDescription"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\SecurePaymentContainerRequest": {"xml_root_name": "securePaymentContainerRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"data": {"expose": true, "access_type": "public_method", "serialized_name": "data", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getData", "setter": "setData"}, "type": "net\\authorize\\api\\contract\\v1\\WebCheckOutDataType"}}}, "net\\authorize\\api\\contract\\v1\\SecurePaymentContainerResponse": {"xml_root_name": "securePaymentContainerResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"opaqueData": {"expose": true, "access_type": "public_method", "serialized_name": "opaqueData", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOpaqueData", "setter": "setOpaqueData"}, "type": "net\\authorize\\api\\contract\\v1\\OpaqueDataType"}}}, "net\\authorize\\api\\contract\\v1\\SendCustomerTransactionReceiptRequest": {"xml_root_name": "sendCustomerTransactionReceiptRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"transId": {"expose": true, "access_type": "public_method", "serialized_name": "transId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransId", "setter": "setTransId"}, "type": "string"}, "customerEmail": {"expose": true, "access_type": "public_method", "serialized_name": "customerEmail", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerEmail", "setter": "setCustomerEmail"}, "type": "string"}, "emailSettings": {"expose": true, "access_type": "public_method", "serialized_name": "emailSettings", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEmailSettings", "setter": "setEmailSettings"}, "type": "net\\authorize\\api\\contract\\v1\\EmailSettingsType"}}}, "net\\authorize\\api\\contract\\v1\\SendCustomerTransactionReceiptResponse": {"xml_root_name": "sendCustomerTransactionReceiptResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": []}, "net\\authorize\\api\\contract\\v1\\SettingType": {"properties": {"settingName": {"expose": true, "access_type": "public_method", "serialized_name": "<PERSON><PERSON><PERSON>", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSettingName", "setter": "setSettingName"}, "type": "string"}, "settingValue": {"expose": true, "access_type": "public_method", "serialized_name": "settingValue", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSettingValue", "setter": "setSettingValue"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\SolutionType": {"properties": {"id": {"expose": true, "access_type": "public_method", "serialized_name": "id", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getId", "setter": "setId"}, "type": "string"}, "name": {"expose": true, "access_type": "public_method", "serialized_name": "name", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getName", "setter": "setName"}, "type": "string"}, "vendorName": {"expose": true, "access_type": "public_method", "serialized_name": "vendorName", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getVendorName", "setter": "setVendorName"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\SubMerchantType": {"properties": {"identifier": {"expose": true, "access_type": "public_method", "serialized_name": "identifier", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getIdentifier", "setter": "setIdentifier"}, "type": "string"}, "doingBusinessAs": {"expose": true, "access_type": "public_method", "serialized_name": "doingBusinessAs", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDoingBusinessAs", "setter": "setDoingBusinessAs"}, "type": "string"}, "paymentServiceProviderName": {"expose": true, "access_type": "public_method", "serialized_name": "paymentServiceProviderName", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaymentServiceProviderName", "setter": "setPaymentServiceProviderName"}, "type": "string"}, "paymentServiceFacilitator": {"expose": true, "access_type": "public_method", "serialized_name": "paymentServiceFacilitator", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaymentServiceFacilitator", "setter": "setPaymentServiceFacilitator"}, "type": "string"}, "streetAddress": {"expose": true, "access_type": "public_method", "serialized_name": "streetAddress", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getStreetAddress", "setter": "setStreetAddress"}, "type": "string"}, "phone": {"expose": true, "access_type": "public_method", "serialized_name": "phone", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPhone", "setter": "setPhone"}, "type": "string"}, "email": {"expose": true, "access_type": "public_method", "serialized_name": "email", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEmail", "setter": "setEmail"}, "type": "string"}, "postalCode": {"expose": true, "access_type": "public_method", "serialized_name": "postalCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPostalCode", "setter": "setPostalCode"}, "type": "string"}, "city": {"expose": true, "access_type": "public_method", "serialized_name": "city", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCity", "setter": "setCity"}, "type": "string"}, "regionCode": {"expose": true, "access_type": "public_method", "serialized_name": "regionCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getRegionCode", "setter": "setRegionCode"}, "type": "string"}, "countryCode": {"expose": true, "access_type": "public_method", "serialized_name": "countryCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCountryCode", "setter": "setCountryCode"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\SubscriptionCustomerProfileType": {"properties": {"paymentProfile": {"expose": true, "access_type": "public_method", "serialized_name": "paymentProfile", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaymentProfile", "setter": "setPaymentProfile"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerPaymentProfileMaskedType"}, "shippingProfile": {"expose": true, "access_type": "public_method", "serialized_name": "shippingProfile", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getShippingProfile", "setter": "setShippingProfile"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerAddressExType"}}}, "net\\authorize\\api\\contract\\v1\\SubscriptionDetailType": {"properties": {"id": {"expose": true, "access_type": "public_method", "serialized_name": "id", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getId", "setter": "setId"}, "type": "integer"}, "name": {"expose": true, "access_type": "public_method", "serialized_name": "name", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getName", "setter": "setName"}, "type": "string"}, "status": {"expose": true, "access_type": "public_method", "serialized_name": "status", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getStatus", "setter": "setStatus"}, "type": "string"}, "createTimeStampUTC": {"expose": true, "access_type": "public_method", "serialized_name": "createTimeStampUTC", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCreateTimeStampUTC", "setter": "setCreateTimeStampUTC"}, "type": "GoetasWebservices\\Xsd\\XsdToPhp\\XMLSchema\\DateTime"}, "firstName": {"expose": true, "access_type": "public_method", "serialized_name": "firstName", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getFirstName", "setter": "setFirstName"}, "type": "string"}, "lastName": {"expose": true, "access_type": "public_method", "serialized_name": "lastName", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getLastName", "setter": "setLastName"}, "type": "string"}, "totalOccurrences": {"expose": true, "access_type": "public_method", "serialized_name": "totalOccurrences", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTotalOccurrences", "setter": "setTotalOccurrences"}, "type": "integer"}, "pastOccurrences": {"expose": true, "access_type": "public_method", "serialized_name": "pastOccurrences", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPastOccurrences", "setter": "setPastOccurrences"}, "type": "integer"}, "paymentMethod": {"expose": true, "access_type": "public_method", "serialized_name": "paymentMethod", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaymentMethod", "setter": "setPaymentMethod"}, "type": "string"}, "accountNumber": {"expose": true, "access_type": "public_method", "serialized_name": "accountNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAccountNumber", "setter": "setAccountNumber"}, "type": "string"}, "invoice": {"expose": true, "access_type": "public_method", "serialized_name": "invoice", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getInvoice", "setter": "setInvoice"}, "type": "string"}, "amount": {"expose": true, "access_type": "public_method", "serialized_name": "amount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAmount", "setter": "setAmount"}, "type": "float"}, "currencyCode": {"expose": true, "access_type": "public_method", "serialized_name": "currencyCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCurrencyCode", "setter": "setCurrencyCode"}, "type": "string"}, "customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "integer"}, "customerPaymentProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerPaymentProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerPaymentProfileId", "setter": "setCustomerPaymentProfileId"}, "type": "integer"}, "customerShippingProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerShippingProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerShippingProfileId", "setter": "setCustomerShippingProfileId"}, "type": "integer"}}}, "net\\authorize\\api\\contract\\v1\\SubscriptionPaymentType": {"properties": {"id": {"expose": true, "access_type": "public_method", "serialized_name": "id", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getId", "setter": "setId"}, "type": "integer"}, "payNum": {"expose": true, "access_type": "public_method", "serialized_name": "payNum", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPayNum", "setter": "setPayNum"}, "type": "integer"}}}, "net\\authorize\\api\\contract\\v1\\SubsequentAuthInformationType": {"properties": {"originalNetworkTransId": {"expose": true, "access_type": "public_method", "serialized_name": "originalNetworkTransId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOriginalNetworkTransId", "setter": "setOriginalNetworkTransId"}, "type": "string"}, "originalAuthAmount": {"expose": true, "access_type": "public_method", "serialized_name": "originalAuthAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOriginalAuthAmount", "setter": "setOriginalAuthAmount"}, "type": "float"}, "reason": {"expose": true, "access_type": "public_method", "serialized_name": "reason", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getReason", "setter": "setReason"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\TokenMaskedType": {"properties": {"tokenSource": {"expose": true, "access_type": "public_method", "serialized_name": "tokenSource", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTokenSource", "setter": "setTokenSource"}, "type": "string"}, "tokenNumber": {"expose": true, "access_type": "public_method", "serialized_name": "tokenNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTokenNumber", "setter": "setTokenNumber"}, "type": "string"}, "expirationDate": {"expose": true, "access_type": "public_method", "serialized_name": "expirationDate", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getExpirationDate", "setter": "setExpirationDate"}, "type": "string"}, "tokenRequestorId": {"expose": true, "access_type": "public_method", "serialized_name": "tokenRequestorId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTokenRequestorId", "setter": "setTokenRequestorId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\TransRetailInfoType": {"properties": {"marketType": {"expose": true, "access_type": "public_method", "serialized_name": "marketType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMarketType", "setter": "setMarketType"}, "type": "string"}, "deviceType": {"expose": true, "access_type": "public_method", "serialized_name": "deviceType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDeviceType", "setter": "setDeviceType"}, "type": "string"}, "customerSignature": {"expose": true, "access_type": "public_method", "serialized_name": "customerSignature", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerSignature", "setter": "setCustomerSignature"}, "type": "string"}, "terminalNumber": {"expose": true, "access_type": "public_method", "serialized_name": "terminalNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTerminalNumber", "setter": "setTerminalNumber"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\TransactionDetailsType\\EmvDetailsAType\\TagAType": {"properties": {"tagId": {"expose": true, "access_type": "public_method", "serialized_name": "tagId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTagId", "setter": "setTagId"}, "type": "string"}, "data": {"expose": true, "access_type": "public_method", "serialized_name": "data", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getData", "setter": "setData"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\TransactionDetailsType\\EmvDetailsAType": {"properties": {"tag": {"expose": true, "access_type": "public_method", "serialized_name": "tag", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTag", "setter": "setTag"}, "xml_list": {"inline": true, "entry_name": "tag", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "type": "array<net\\authorize\\api\\contract\\v1\\TransactionDetailsType\\EmvDetailsAType\\TagAType>"}}}, "net\\authorize\\api\\contract\\v1\\TransactionDetailsType": {"properties": {"transId": {"expose": true, "access_type": "public_method", "serialized_name": "transId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransId", "setter": "setTransId"}, "type": "string"}, "refTransId": {"expose": true, "access_type": "public_method", "serialized_name": "refTransId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getRefTransId", "setter": "setRefTransId"}, "type": "string"}, "splitTenderId": {"expose": true, "access_type": "public_method", "serialized_name": "splitTenderId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSplitTenderId", "setter": "setSplitTenderId"}, "type": "string"}, "submitTimeUTC": {"expose": true, "access_type": "public_method", "serialized_name": "submitTimeUTC", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubmitTimeUTC", "setter": "setSubmitTimeUTC"}, "type": "GoetasWebservices\\Xsd\\XsdToPhp\\XMLSchema\\DateTime"}, "submitTimeLocal": {"expose": true, "access_type": "public_method", "serialized_name": "submitTimeLocal", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubmitTimeLocal", "setter": "setSubmitTimeLocal"}, "type": "GoetasWebservices\\Xsd\\XsdToPhp\\XMLSchema\\DateTime"}, "transactionType": {"expose": true, "access_type": "public_method", "serialized_name": "transactionType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransactionType", "setter": "setTransactionType"}, "type": "string"}, "transactionStatus": {"expose": true, "access_type": "public_method", "serialized_name": "transactionStatus", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransactionStatus", "setter": "setTransactionStatus"}, "type": "string"}, "responseCode": {"expose": true, "access_type": "public_method", "serialized_name": "responseCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getResponseCode", "setter": "setResponseCode"}, "type": "integer"}, "responseReasonCode": {"expose": true, "access_type": "public_method", "serialized_name": "responseReasonCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getResponseReasonCode", "setter": "setResponseReasonCode"}, "type": "integer"}, "subscription": {"expose": true, "access_type": "public_method", "serialized_name": "subscription", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubscription", "setter": "setSubscription"}, "type": "net\\authorize\\api\\contract\\v1\\SubscriptionPaymentType"}, "responseReasonDescription": {"expose": true, "access_type": "public_method", "serialized_name": "responseReasonDescription", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getResponseReasonDescription", "setter": "setResponseReasonDescription"}, "type": "string"}, "authCode": {"expose": true, "access_type": "public_method", "serialized_name": "authCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAuthCode", "setter": "setAuthCode"}, "type": "string"}, "aVSResponse": {"expose": true, "access_type": "public_method", "serialized_name": "AVSResponse", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAVSResponse", "setter": "setAVSResponse"}, "type": "string"}, "cardCodeResponse": {"expose": true, "access_type": "public_method", "serialized_name": "cardCodeResponse", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCardCodeResponse", "setter": "setCardCodeResponse"}, "type": "string"}, "cAVVResponse": {"expose": true, "access_type": "public_method", "serialized_name": "CAVVResponse", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCAVVResponse", "setter": "setCAVVResponse"}, "type": "string"}, "fDSFilterAction": {"expose": true, "access_type": "public_method", "serialized_name": "FDSFilterAction", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getFDSFilterAction", "setter": "setFDSFilterAction"}, "type": "string"}, "fDSFilters": {"expose": true, "access_type": "public_method", "serialized_name": "FDSFilters", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getFDSFilters", "setter": "setFDSFilters"}, "type": "array<net\\authorize\\api\\contract\\v1\\FDSFilterType>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "FDSFilter", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "batch": {"expose": true, "access_type": "public_method", "serialized_name": "batch", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getBatch", "setter": "setBatch"}, "type": "net\\authorize\\api\\contract\\v1\\BatchDetailsType"}, "order": {"expose": true, "access_type": "public_method", "serialized_name": "order", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOrder", "setter": "setOrder"}, "type": "net\\authorize\\api\\contract\\v1\\OrderExType"}, "requestedAmount": {"expose": true, "access_type": "public_method", "serialized_name": "requestedAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getRequestedAmount", "setter": "setRequestedAmount"}, "type": "float"}, "authAmount": {"expose": true, "access_type": "public_method", "serialized_name": "authAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAuthAmount", "setter": "setAuthAmount"}, "type": "float"}, "settleAmount": {"expose": true, "access_type": "public_method", "serialized_name": "settleAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSettleAmount", "setter": "setSettleAmount"}, "type": "float"}, "tax": {"expose": true, "access_type": "public_method", "serialized_name": "tax", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTax", "setter": "setTax"}, "type": "net\\authorize\\api\\contract\\v1\\ExtendedAmountType"}, "shipping": {"expose": true, "access_type": "public_method", "serialized_name": "shipping", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getShipping", "setter": "setShipping"}, "type": "net\\authorize\\api\\contract\\v1\\ExtendedAmountType"}, "duty": {"expose": true, "access_type": "public_method", "serialized_name": "duty", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "get<PERSON>uty", "setter": "<PERSON><PERSON><PERSON><PERSON>"}, "type": "net\\authorize\\api\\contract\\v1\\ExtendedAmountType"}, "lineItems": {"expose": true, "access_type": "public_method", "serialized_name": "lineItems", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getLineItems", "setter": "setLineItems"}, "type": "array<net\\authorize\\api\\contract\\v1\\LineItemType>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "lineItem", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "prepaidBalanceRemaining": {"expose": true, "access_type": "public_method", "serialized_name": "prepaidBalanceRemaining", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPrepaidBalanceRemaining", "setter": "setPrepaidBalanceRemaining"}, "type": "float"}, "taxExempt": {"expose": true, "access_type": "public_method", "serialized_name": "taxExempt", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTaxExempt", "setter": "setTaxExempt"}, "type": "boolean"}, "payment": {"expose": true, "access_type": "public_method", "serialized_name": "payment", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPayment", "setter": "setPayment"}, "type": "net\\authorize\\api\\contract\\v1\\PaymentMaskedType"}, "customer": {"expose": true, "access_type": "public_method", "serialized_name": "customer", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomer", "setter": "setCustomer"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerDataType"}, "billTo": {"expose": true, "access_type": "public_method", "serialized_name": "billTo", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getBillTo", "setter": "setBillTo"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerAddressType"}, "shipTo": {"expose": true, "access_type": "public_method", "serialized_name": "shipTo", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getShipTo", "setter": "setShipTo"}, "type": "net\\authorize\\api\\contract\\v1\\NameAndAddressType"}, "recurringBilling": {"expose": true, "access_type": "public_method", "serialized_name": "recurringBilling", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getRecurringBilling", "setter": "setRecurringBilling"}, "type": "boolean"}, "customerIP": {"expose": true, "access_type": "public_method", "serialized_name": "customerIP", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerIP", "setter": "setCustomerIP"}, "type": "string"}, "product": {"expose": true, "access_type": "public_method", "serialized_name": "product", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProduct", "setter": "setProduct"}, "type": "string"}, "entryMode": {"expose": true, "access_type": "public_method", "serialized_name": "entryMode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEntryMode", "setter": "setEntryMode"}, "type": "string"}, "marketType": {"expose": true, "access_type": "public_method", "serialized_name": "marketType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMarketType", "setter": "setMarketType"}, "type": "string"}, "mobileDeviceId": {"expose": true, "access_type": "public_method", "serialized_name": "mobileDeviceId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMobileDeviceId", "setter": "setMobileDeviceId"}, "type": "string"}, "customerSignature": {"expose": true, "access_type": "public_method", "serialized_name": "customerSignature", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerSignature", "setter": "setCustomerSignature"}, "type": "string"}, "returnedItems": {"expose": true, "access_type": "public_method", "serialized_name": "returnedItems", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getReturnedItems", "setter": "setReturnedItems"}, "type": "array<net\\authorize\\api\\contract\\v1\\ReturnedItemType>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "returnedItem", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "solution": {"expose": true, "access_type": "public_method", "serialized_name": "solution", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSolution", "setter": "setSolution"}, "type": "net\\authorize\\api\\contract\\v1\\SolutionType"}, "emvDetails": {"expose": true, "access_type": "public_method", "serialized_name": "emvDetails", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEmvDetails", "setter": "setEmvDetails"}, "type": "array<net\\authorize\\api\\contract\\v1\\TransactionDetailsType\\EmvDetailsAType\\TagAType>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "tag", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "profile": {"expose": true, "access_type": "public_method", "serialized_name": "profile", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProfile", "setter": "setProfile"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerProfileIdType"}, "surcharge": {"expose": true, "access_type": "public_method", "serialized_name": "surcharge", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSurcharge", "setter": "setSurcharge"}, "type": "net\\authorize\\api\\contract\\v1\\ExtendedAmountType"}, "employeeId": {"expose": true, "access_type": "public_method", "serialized_name": "employeeId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEmployeeId", "setter": "setEmployeeId"}, "type": "string"}, "tip": {"expose": true, "access_type": "public_method", "serialized_name": "tip", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTip", "setter": "setTip"}, "type": "net\\authorize\\api\\contract\\v1\\ExtendedAmountType"}, "otherTax": {"expose": true, "access_type": "public_method", "serialized_name": "otherTax", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOtherTax", "setter": "setOtherTax"}, "type": "net\\authorize\\api\\contract\\v1\\OtherTaxType"}, "shipFrom": {"expose": true, "access_type": "public_method", "serialized_name": "shipFrom", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getShipFrom", "setter": "setShipFrom"}, "type": "net\\authorize\\api\\contract\\v1\\NameAndAddressType"}, "networkTransId": {"expose": true, "access_type": "public_method", "serialized_name": "networkTransId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getNetworkTransId", "setter": "setNetworkTransId"}, "type": "string"}, "originalNetworkTransId": {"expose": true, "access_type": "public_method", "serialized_name": "originalNetworkTransId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOriginalNetworkTransId", "setter": "setOriginalNetworkTransId"}, "type": "string"}, "originalAuthAmount": {"expose": true, "access_type": "public_method", "serialized_name": "originalAuthAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOriginalAuthAmount", "setter": "setOriginalAuthAmount"}, "type": "float"}, "authorizationIndicator": {"expose": true, "access_type": "public_method", "serialized_name": "authorizationIndicator", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAuthorizationIndicator", "setter": "setAuthorizationIndicator"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\TransactionListSortingType": {"properties": {"orderBy": {"expose": true, "access_type": "public_method", "serialized_name": "orderBy", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOrderBy", "setter": "setOrderBy"}, "type": "string"}, "orderDescending": {"expose": true, "access_type": "public_method", "serialized_name": "orderDescending", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOrderDescending", "setter": "setOrderDescending"}, "type": "boolean"}}}, "net\\authorize\\api\\contract\\v1\\TransactionRequestType\\UserFieldsAType": {"properties": {"userField": {"expose": true, "access_type": "public_method", "serialized_name": "userField", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getUserField", "setter": "setUserField"}, "xml_list": {"inline": true, "entry_name": "userField", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "type": "array<net\\authorize\\api\\contract\\v1\\UserFieldType>"}}}, "net\\authorize\\api\\contract\\v1\\TransactionRequestType": {"properties": {"transactionType": {"expose": true, "access_type": "public_method", "serialized_name": "transactionType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransactionType", "setter": "setTransactionType"}, "type": "string"}, "amount": {"expose": true, "access_type": "public_method", "serialized_name": "amount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAmount", "setter": "setAmount"}, "type": "float"}, "currencyCode": {"expose": true, "access_type": "public_method", "serialized_name": "currencyCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCurrencyCode", "setter": "setCurrencyCode"}, "type": "string"}, "payment": {"expose": true, "access_type": "public_method", "serialized_name": "payment", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPayment", "setter": "setPayment"}, "type": "net\\authorize\\api\\contract\\v1\\PaymentType"}, "profile": {"expose": true, "access_type": "public_method", "serialized_name": "profile", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProfile", "setter": "setProfile"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerProfilePaymentType"}, "solution": {"expose": true, "access_type": "public_method", "serialized_name": "solution", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSolution", "setter": "setSolution"}, "type": "net\\authorize\\api\\contract\\v1\\SolutionType"}, "callId": {"expose": true, "access_type": "public_method", "serialized_name": "callId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCallId", "setter": "setCallId"}, "type": "string"}, "terminalNumber": {"expose": true, "access_type": "public_method", "serialized_name": "terminalNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTerminalNumber", "setter": "setTerminalNumber"}, "type": "string"}, "authCode": {"expose": true, "access_type": "public_method", "serialized_name": "authCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAuthCode", "setter": "setAuthCode"}, "type": "string"}, "refTransId": {"expose": true, "access_type": "public_method", "serialized_name": "refTransId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getRefTransId", "setter": "setRefTransId"}, "type": "string"}, "splitTenderId": {"expose": true, "access_type": "public_method", "serialized_name": "splitTenderId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSplitTenderId", "setter": "setSplitTenderId"}, "type": "string"}, "order": {"expose": true, "access_type": "public_method", "serialized_name": "order", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOrder", "setter": "setOrder"}, "type": "net\\authorize\\api\\contract\\v1\\OrderType"}, "lineItems": {"expose": true, "access_type": "public_method", "serialized_name": "lineItems", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getLineItems", "setter": "setLineItems"}, "type": "array<net\\authorize\\api\\contract\\v1\\LineItemType>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "lineItem", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "tax": {"expose": true, "access_type": "public_method", "serialized_name": "tax", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTax", "setter": "setTax"}, "type": "net\\authorize\\api\\contract\\v1\\ExtendedAmountType"}, "duty": {"expose": true, "access_type": "public_method", "serialized_name": "duty", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "get<PERSON>uty", "setter": "<PERSON><PERSON><PERSON><PERSON>"}, "type": "net\\authorize\\api\\contract\\v1\\ExtendedAmountType"}, "shipping": {"expose": true, "access_type": "public_method", "serialized_name": "shipping", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getShipping", "setter": "setShipping"}, "type": "net\\authorize\\api\\contract\\v1\\ExtendedAmountType"}, "taxExempt": {"expose": true, "access_type": "public_method", "serialized_name": "taxExempt", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTaxExempt", "setter": "setTaxExempt"}, "type": "boolean"}, "poNumber": {"expose": true, "access_type": "public_method", "serialized_name": "poNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPoNumber", "setter": "setPoNumber"}, "type": "string"}, "customer": {"expose": true, "access_type": "public_method", "serialized_name": "customer", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomer", "setter": "setCustomer"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerDataType"}, "billTo": {"expose": true, "access_type": "public_method", "serialized_name": "billTo", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getBillTo", "setter": "setBillTo"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerAddressType"}, "shipTo": {"expose": true, "access_type": "public_method", "serialized_name": "shipTo", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getShipTo", "setter": "setShipTo"}, "type": "net\\authorize\\api\\contract\\v1\\NameAndAddressType"}, "customerIP": {"expose": true, "access_type": "public_method", "serialized_name": "customerIP", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerIP", "setter": "setCustomerIP"}, "type": "string"}, "cardholderAuthentication": {"expose": true, "access_type": "public_method", "serialized_name": "cardholderAuthentication", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCardholderAuthentication", "setter": "setCardholderAuthentication"}, "type": "net\\authorize\\api\\contract\\v1\\CcAuthenticationType"}, "retail": {"expose": true, "access_type": "public_method", "serialized_name": "retail", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getRetail", "setter": "setRetail"}, "type": "net\\authorize\\api\\contract\\v1\\TransRetailInfoType"}, "employeeId": {"expose": true, "access_type": "public_method", "serialized_name": "employeeId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEmployeeId", "setter": "setEmployeeId"}, "type": "string"}, "transactionSettings": {"expose": true, "access_type": "public_method", "serialized_name": "transactionSettings", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransactionSettings", "setter": "setTransactionSettings"}, "type": "array<net\\authorize\\api\\contract\\v1\\SettingType>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "setting", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "userFields": {"expose": true, "access_type": "public_method", "serialized_name": "userFields", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getU<PERSON><PERSON>ields", "setter": "<PERSON><PERSON><PERSON><PERSON><PERSON>s"}, "type": "array<net\\authorize\\api\\contract\\v1\\UserFieldType>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "userField", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "surcharge": {"expose": true, "access_type": "public_method", "serialized_name": "surcharge", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSurcharge", "setter": "setSurcharge"}, "type": "net\\authorize\\api\\contract\\v1\\ExtendedAmountType"}, "merchantDescriptor": {"expose": true, "access_type": "public_method", "serialized_name": "merchantDescriptor", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMerchantDescriptor", "setter": "setMerchantDescriptor"}, "type": "string"}, "subMerchant": {"expose": true, "access_type": "public_method", "serialized_name": "subMerchant", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubMerchant", "setter": "setSubMerchant"}, "type": "net\\authorize\\api\\contract\\v1\\SubMerchantType"}, "tip": {"expose": true, "access_type": "public_method", "serialized_name": "tip", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTip", "setter": "setTip"}, "type": "net\\authorize\\api\\contract\\v1\\ExtendedAmountType"}, "processingOptions": {"expose": true, "access_type": "public_method", "serialized_name": "processingOptions", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProcessingOptions", "setter": "setProcessingOptions"}, "type": "net\\authorize\\api\\contract\\v1\\ProcessingOptionsType"}, "subsequentAuthInformation": {"expose": true, "access_type": "public_method", "serialized_name": "subsequentAuthInformation", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubsequentAuthInformation", "setter": "setSubsequentAuthInformation"}, "type": "net\\authorize\\api\\contract\\v1\\SubsequentAuthInformationType"}, "otherTax": {"expose": true, "access_type": "public_method", "serialized_name": "otherTax", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getOtherTax", "setter": "setOtherTax"}, "type": "net\\authorize\\api\\contract\\v1\\OtherTaxType"}, "shipFrom": {"expose": true, "access_type": "public_method", "serialized_name": "shipFrom", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getShipFrom", "setter": "setShipFrom"}, "type": "net\\authorize\\api\\contract\\v1\\NameAndAddressType"}, "authorizationIndicatorType": {"expose": true, "access_type": "public_method", "serialized_name": "authorizationIndicatorType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAuthorizationIndicatorType", "setter": "setAuthorizationIndicatorType"}, "type": "net\\authorize\\api\\contract\\v1\\AuthorizationIndicatorType"}}}, "net\\authorize\\api\\contract\\v1\\TransactionResponseType\\EmvResponseAType\\TagsAType": {"properties": {"tag": {"expose": true, "access_type": "public_method", "serialized_name": "tag", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTag", "setter": "setTag"}, "xml_list": {"inline": true, "entry_name": "tag", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "type": "array<net\\authorize\\api\\contract\\v1\\EmvTagType>"}}}, "net\\authorize\\api\\contract\\v1\\TransactionResponseType\\EmvResponseAType": {"properties": {"tlvData": {"expose": true, "access_type": "public_method", "serialized_name": "tlvData", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTlvData", "setter": "setTlvData"}, "type": "string"}, "tags": {"expose": true, "access_type": "public_method", "serialized_name": "tags", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTags", "setter": "setTags"}, "type": "array<net\\authorize\\api\\contract\\v1\\EmvTagType>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "tag", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}}}, "net\\authorize\\api\\contract\\v1\\TransactionResponseType\\ErrorsAType\\ErrorAType": {"properties": {"errorCode": {"expose": true, "access_type": "public_method", "serialized_name": "errorCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getErrorCode", "setter": "setErrorCode"}, "type": "string"}, "errorText": {"expose": true, "access_type": "public_method", "serialized_name": "errorText", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getErrorText", "setter": "setErrorText"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\TransactionResponseType\\ErrorsAType": {"properties": {"error": {"expose": true, "access_type": "public_method", "serialized_name": "error", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getError", "setter": "setError"}, "xml_list": {"inline": true, "entry_name": "error", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "type": "array<net\\authorize\\api\\contract\\v1\\TransactionResponseType\\ErrorsAType\\ErrorAType>"}}}, "net\\authorize\\api\\contract\\v1\\TransactionResponseType\\MessagesAType\\MessageAType": {"properties": {"code": {"expose": true, "access_type": "public_method", "serialized_name": "code", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCode", "setter": "setCode"}, "type": "string"}, "description": {"expose": true, "access_type": "public_method", "serialized_name": "description", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDescription", "setter": "setDescription"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\TransactionResponseType\\MessagesAType": {"properties": {"message": {"expose": true, "access_type": "public_method", "serialized_name": "message", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMessage", "setter": "setMessage"}, "xml_list": {"inline": true, "entry_name": "message", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "type": "array<net\\authorize\\api\\contract\\v1\\TransactionResponseType\\MessagesAType\\MessageAType>"}}}, "net\\authorize\\api\\contract\\v1\\TransactionResponseType\\PrePaidCardAType": {"properties": {"requestedAmount": {"expose": true, "access_type": "public_method", "serialized_name": "requestedAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getRequestedAmount", "setter": "setRequestedAmount"}, "type": "string"}, "approvedAmount": {"expose": true, "access_type": "public_method", "serialized_name": "approvedAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getApprovedAmount", "setter": "setApprovedAmount"}, "type": "string"}, "balanceOnCard": {"expose": true, "access_type": "public_method", "serialized_name": "balanceOnCard", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getBalanceOnCard", "setter": "setBalanceOnCard"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\TransactionResponseType\\SecureAcceptanceAType": {"properties": {"secureAcceptanceUrl": {"expose": true, "access_type": "public_method", "serialized_name": "SecureAcceptanceUrl", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSecureAcceptanceUrl", "setter": "setSecureAcceptanceUrl"}, "type": "string"}, "payerID": {"expose": true, "access_type": "public_method", "serialized_name": "PayerID", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPayerID", "setter": "setPayerID"}, "type": "string"}, "payerEmail": {"expose": true, "access_type": "public_method", "serialized_name": "PayerEmail", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPayerEmail", "setter": "setPayerEmail"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\TransactionResponseType\\SplitTenderPaymentsAType\\SplitTenderPaymentAType": {"properties": {"transId": {"expose": true, "access_type": "public_method", "serialized_name": "transId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransId", "setter": "setTransId"}, "type": "string"}, "responseCode": {"expose": true, "access_type": "public_method", "serialized_name": "responseCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getResponseCode", "setter": "setResponseCode"}, "type": "string"}, "responseToCustomer": {"expose": true, "access_type": "public_method", "serialized_name": "responseToCustomer", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getResponseToCustomer", "setter": "setResponseToCustomer"}, "type": "string"}, "authCode": {"expose": true, "access_type": "public_method", "serialized_name": "authCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAuthCode", "setter": "setAuthCode"}, "type": "string"}, "accountNumber": {"expose": true, "access_type": "public_method", "serialized_name": "accountNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAccountNumber", "setter": "setAccountNumber"}, "type": "string"}, "accountType": {"expose": true, "access_type": "public_method", "serialized_name": "accountType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAccountType", "setter": "setAccountType"}, "type": "string"}, "requestedAmount": {"expose": true, "access_type": "public_method", "serialized_name": "requestedAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getRequestedAmount", "setter": "setRequestedAmount"}, "type": "string"}, "approvedAmount": {"expose": true, "access_type": "public_method", "serialized_name": "approvedAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getApprovedAmount", "setter": "setApprovedAmount"}, "type": "string"}, "balanceOnCard": {"expose": true, "access_type": "public_method", "serialized_name": "balanceOnCard", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getBalanceOnCard", "setter": "setBalanceOnCard"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\TransactionResponseType\\SplitTenderPaymentsAType": {"properties": {"splitTenderPayment": {"expose": true, "access_type": "public_method", "serialized_name": "splitTenderPayment", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSplitTenderPayment", "setter": "setSplitTenderPayment"}, "xml_list": {"inline": true, "entry_name": "splitTenderPayment", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "type": "array<net\\authorize\\api\\contract\\v1\\TransactionResponseType\\SplitTenderPaymentsAType\\SplitTenderPaymentAType>"}}}, "net\\authorize\\api\\contract\\v1\\TransactionResponseType\\UserFieldsAType": {"properties": {"userField": {"expose": true, "access_type": "public_method", "serialized_name": "userField", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getUserField", "setter": "setUserField"}, "xml_list": {"inline": true, "entry_name": "userField", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "type": "array<net\\authorize\\api\\contract\\v1\\UserFieldType>"}}}, "net\\authorize\\api\\contract\\v1\\TransactionResponseType": {"properties": {"responseCode": {"expose": true, "access_type": "public_method", "serialized_name": "responseCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getResponseCode", "setter": "setResponseCode"}, "type": "string"}, "rawResponseCode": {"expose": true, "access_type": "public_method", "serialized_name": "rawResponseCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getRawResponseCode", "setter": "setRawResponseCode"}, "type": "string"}, "authCode": {"expose": true, "access_type": "public_method", "serialized_name": "authCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAuthCode", "setter": "setAuthCode"}, "type": "string"}, "avsResultCode": {"expose": true, "access_type": "public_method", "serialized_name": "avsResultCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAvsResultCode", "setter": "setAvsResultCode"}, "type": "string"}, "cvvResultCode": {"expose": true, "access_type": "public_method", "serialized_name": "cvvResultCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCvvResultCode", "setter": "setCvvResultCode"}, "type": "string"}, "cavvResultCode": {"expose": true, "access_type": "public_method", "serialized_name": "cavvResultCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCavvResultCode", "setter": "setCavvResultCode"}, "type": "string"}, "transId": {"expose": true, "access_type": "public_method", "serialized_name": "transId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransId", "setter": "setTransId"}, "type": "string"}, "refTransID": {"expose": true, "access_type": "public_method", "serialized_name": "refTransID", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getRefTransID", "setter": "setRefTransID"}, "type": "string"}, "transHash": {"expose": true, "access_type": "public_method", "serialized_name": "transHash", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransHash", "setter": "setTransHash"}, "type": "string"}, "testRequest": {"expose": true, "access_type": "public_method", "serialized_name": "testRequest", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTestRequest", "setter": "setTestRequest"}, "type": "string"}, "accountNumber": {"expose": true, "access_type": "public_method", "serialized_name": "accountNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAccountNumber", "setter": "setAccountNumber"}, "type": "string"}, "entryMode": {"expose": true, "access_type": "public_method", "serialized_name": "entryMode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEntryMode", "setter": "setEntryMode"}, "type": "string"}, "accountType": {"expose": true, "access_type": "public_method", "serialized_name": "accountType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAccountType", "setter": "setAccountType"}, "type": "string"}, "splitTenderId": {"expose": true, "access_type": "public_method", "serialized_name": "splitTenderId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSplitTenderId", "setter": "setSplitTenderId"}, "type": "string"}, "prePaidCard": {"expose": true, "access_type": "public_method", "serialized_name": "prePaidCard", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPrePaidCard", "setter": "setPrePaidCard"}, "type": "net\\authorize\\api\\contract\\v1\\TransactionResponseType\\PrePaidCardAType"}, "messages": {"expose": true, "access_type": "public_method", "serialized_name": "messages", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMessages", "setter": "setMessages"}, "type": "array<net\\authorize\\api\\contract\\v1\\TransactionResponseType\\MessagesAType\\MessageAType>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "message", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "errors": {"expose": true, "access_type": "public_method", "serialized_name": "errors", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getErrors", "setter": "setErrors"}, "type": "array<net\\authorize\\api\\contract\\v1\\TransactionResponseType\\ErrorsAType\\ErrorAType>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "error", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "splitTenderPayments": {"expose": true, "access_type": "public_method", "serialized_name": "splitTenderPayments", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSplitTenderPayments", "setter": "setSplitTenderPayments"}, "type": "array<net\\authorize\\api\\contract\\v1\\TransactionResponseType\\SplitTenderPaymentsAType\\SplitTenderPaymentAType>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "splitTenderPayment", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "userFields": {"expose": true, "access_type": "public_method", "serialized_name": "userFields", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getU<PERSON><PERSON>ields", "setter": "<PERSON><PERSON><PERSON><PERSON><PERSON>s"}, "type": "array<net\\authorize\\api\\contract\\v1\\UserFieldType>", "xml_list": {"inline": false, "skip_when_empty": true, "entry_name": "userField", "namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}}, "shipTo": {"expose": true, "access_type": "public_method", "serialized_name": "shipTo", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getShipTo", "setter": "setShipTo"}, "type": "net\\authorize\\api\\contract\\v1\\NameAndAddressType"}, "secureAcceptance": {"expose": true, "access_type": "public_method", "serialized_name": "secureAcceptance", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSecureAcceptance", "setter": "setSecureAcceptance"}, "type": "net\\authorize\\api\\contract\\v1\\TransactionResponseType\\SecureAcceptanceAType"}, "emvResponse": {"expose": true, "access_type": "public_method", "serialized_name": "emvResponse", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getEmvResponse", "setter": "setEmvResponse"}, "type": "net\\authorize\\api\\contract\\v1\\TransactionResponseType\\EmvResponseAType"}, "transHashSha2": {"expose": true, "access_type": "public_method", "serialized_name": "transHashSha2", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransHashSha2", "setter": "setTransHashSha2"}, "type": "string"}, "profile": {"expose": true, "access_type": "public_method", "serialized_name": "profile", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProfile", "setter": "setProfile"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerProfileIdType"}, "networkTransId": {"expose": true, "access_type": "public_method", "serialized_name": "networkTransId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getNetworkTransId", "setter": "setNetworkTransId"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\TransactionSummaryType": {"properties": {"transId": {"expose": true, "access_type": "public_method", "serialized_name": "transId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransId", "setter": "setTransId"}, "type": "string"}, "submitTimeUTC": {"expose": true, "access_type": "public_method", "serialized_name": "submitTimeUTC", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubmitTimeUTC", "setter": "setSubmitTimeUTC"}, "type": "GoetasWebservices\\Xsd\\XsdToPhp\\XMLSchema\\DateTime"}, "submitTimeLocal": {"expose": true, "access_type": "public_method", "serialized_name": "submitTimeLocal", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubmitTimeLocal", "setter": "setSubmitTimeLocal"}, "type": "GoetasWebservices\\Xsd\\XsdToPhp\\XMLSchema\\DateTime"}, "transactionStatus": {"expose": true, "access_type": "public_method", "serialized_name": "transactionStatus", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransactionStatus", "setter": "setTransactionStatus"}, "type": "string"}, "invoiceNumber": {"expose": true, "access_type": "public_method", "serialized_name": "invoiceNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getInvoiceNumber", "setter": "setInvoiceNumber"}, "type": "string"}, "firstName": {"expose": true, "access_type": "public_method", "serialized_name": "firstName", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getFirstName", "setter": "setFirstName"}, "type": "string"}, "lastName": {"expose": true, "access_type": "public_method", "serialized_name": "lastName", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getLastName", "setter": "setLastName"}, "type": "string"}, "accountType": {"expose": true, "access_type": "public_method", "serialized_name": "accountType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAccountType", "setter": "setAccountType"}, "type": "string"}, "accountNumber": {"expose": true, "access_type": "public_method", "serialized_name": "accountNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getAccountNumber", "setter": "setAccountNumber"}, "type": "string"}, "settleAmount": {"expose": true, "access_type": "public_method", "serialized_name": "settleAmount", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSettleAmount", "setter": "setSettleAmount"}, "type": "float"}, "marketType": {"expose": true, "access_type": "public_method", "serialized_name": "marketType", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMarketType", "setter": "setMarketType"}, "type": "string"}, "product": {"expose": true, "access_type": "public_method", "serialized_name": "product", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProduct", "setter": "setProduct"}, "type": "string"}, "mobileDeviceId": {"expose": true, "access_type": "public_method", "serialized_name": "mobileDeviceId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getMobileDeviceId", "setter": "setMobileDeviceId"}, "type": "string"}, "subscription": {"expose": true, "access_type": "public_method", "serialized_name": "subscription", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSubscription", "setter": "setSubscription"}, "type": "net\\authorize\\api\\contract\\v1\\SubscriptionPaymentType"}, "hasReturnedItems": {"expose": true, "access_type": "public_method", "serialized_name": "hasReturnedItems", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getHasReturnedItems", "setter": "setHasReturnedItems"}, "type": "boolean"}, "fraudInformation": {"expose": true, "access_type": "public_method", "serialized_name": "fraudInformation", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getFraudInformation", "setter": "setFraudInformation"}, "type": "net\\authorize\\api\\contract\\v1\\FraudInformationType"}, "profile": {"expose": true, "access_type": "public_method", "serialized_name": "profile", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProfile", "setter": "setProfile"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerProfileIdType"}}}, "net\\authorize\\api\\contract\\v1\\UpdateCustomerPaymentProfileRequest": {"xml_root_name": "updateCustomerPaymentProfileRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "paymentProfile": {"expose": true, "access_type": "public_method", "serialized_name": "paymentProfile", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getPaymentProfile", "setter": "setPaymentProfile"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerPaymentProfileExType"}, "validationMode": {"expose": true, "access_type": "public_method", "serialized_name": "validationMode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getValidationMode", "setter": "setValidationMode"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\UpdateCustomerPaymentProfileResponse": {"xml_root_name": "updateCustomerPaymentProfileResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"validationDirectResponse": {"expose": true, "access_type": "public_method", "serialized_name": "validationDirectResponse", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getValidationDirectResponse", "setter": "setValidationDirectResponse"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\UpdateCustomerProfileRequest": {"xml_root_name": "updateCustomerProfileRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"profile": {"expose": true, "access_type": "public_method", "serialized_name": "profile", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getProfile", "setter": "setProfile"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerProfileInfoExType"}}}, "net\\authorize\\api\\contract\\v1\\UpdateCustomerProfileResponse": {"xml_root_name": "updateCustomerProfileResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": []}, "net\\authorize\\api\\contract\\v1\\UpdateCustomerShippingAddressRequest": {"xml_root_name": "updateCustomerShippingAddressRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "address": {"expose": true, "access_type": "public_method", "serialized_name": "address", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "get<PERSON><PERSON><PERSON>", "setter": "<PERSON><PERSON><PERSON><PERSON>"}, "type": "net\\authorize\\api\\contract\\v1\\CustomerAddressExType"}, "defaultShippingAddress": {"expose": true, "access_type": "public_method", "serialized_name": "defaultShippingAddress", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDefaultShippingAddress", "setter": "setDefaultShippingAddress"}, "type": "boolean"}}}, "net\\authorize\\api\\contract\\v1\\UpdateCustomerShippingAddressResponse": {"xml_root_name": "updateCustomerShippingAddressResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": []}, "net\\authorize\\api\\contract\\v1\\UpdateHeldTransactionRequest": {"xml_root_name": "updateHeldTransactionRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"heldTransactionRequest": {"expose": true, "access_type": "public_method", "serialized_name": "heldTransactionRequest", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getHeldTransactionRequest", "setter": "setHeldTransactionRequest"}, "type": "net\\authorize\\api\\contract\\v1\\HeldTransactionRequestType"}}}, "net\\authorize\\api\\contract\\v1\\UpdateHeldTransactionResponse": {"xml_root_name": "updateHeldTransactionResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"transactionResponse": {"expose": true, "access_type": "public_method", "serialized_name": "transactionResponse", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getTransactionResponse", "setter": "setTransactionResponse"}, "type": "net\\authorize\\api\\contract\\v1\\TransactionResponseType"}}}, "net\\authorize\\api\\contract\\v1\\UpdateMerchantDetailsRequest": {"xml_root_name": "updateMerchantDetailsRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"isTestMode": {"expose": true, "access_type": "public_method", "serialized_name": "isTestMode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getIsTestMode", "setter": "setIsTestMode"}, "type": "boolean"}}}, "net\\authorize\\api\\contract\\v1\\UpdateMerchantDetailsResponse": {"xml_root_name": "updateMerchantDetailsResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": []}, "net\\authorize\\api\\contract\\v1\\UpdateSplitTenderGroupRequest": {"xml_root_name": "updateSplitTenderGroupRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"splitTenderId": {"expose": true, "access_type": "public_method", "serialized_name": "splitTenderId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSplitTenderId", "setter": "setSplitTenderId"}, "type": "string"}, "splitTenderStatus": {"expose": true, "access_type": "public_method", "serialized_name": "splitTenderStatus", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getSplitTenderStatus", "setter": "setSplitTenderStatus"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\UpdateSplitTenderGroupResponse": {"xml_root_name": "updateSplitTenderGroupResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": []}, "net\\authorize\\api\\contract\\v1\\UserFieldType": {"properties": {"name": {"expose": true, "access_type": "public_method", "serialized_name": "name", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getName", "setter": "setName"}, "type": "string"}, "value": {"expose": true, "access_type": "public_method", "serialized_name": "value", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getValue", "setter": "setValue"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\ValidateCustomerPaymentProfileRequest": {"xml_root_name": "validateCustomerPaymentProfileRequest", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"customerProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerProfileId", "setter": "setCustomerProfileId"}, "type": "string"}, "customerPaymentProfileId": {"expose": true, "access_type": "public_method", "serialized_name": "customerPaymentProfileId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerPaymentProfileId", "setter": "setCustomerPaymentProfileId"}, "type": "string"}, "customerShippingAddressId": {"expose": true, "access_type": "public_method", "serialized_name": "customerShippingAddressId", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCustomerShippingAddressId", "setter": "setCustomerShippingAddressId"}, "type": "string"}, "cardCode": {"expose": true, "access_type": "public_method", "serialized_name": "cardCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCardCode", "setter": "setCardCode"}, "type": "string"}, "validationMode": {"expose": true, "access_type": "public_method", "serialized_name": "validationMode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getValidationMode", "setter": "setValidationMode"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\ValidateCustomerPaymentProfileResponse": {"xml_root_name": "validateCustomerPaymentProfileResponse", "xml_root_namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd", "properties": {"directResponse": {"expose": true, "access_type": "public_method", "serialized_name": "directResponse", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getDirectResponse", "setter": "setDirectResponse"}, "type": "string"}}}, "net\\authorize\\api\\contract\\v1\\WebCheckOutDataType": {"properties": {"type": {"expose": true, "access_type": "public_method", "serialized_name": "type", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getType", "setter": "setType"}, "type": "string"}, "id": {"expose": true, "access_type": "public_method", "serialized_name": "id", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getId", "setter": "setId"}, "type": "string"}, "token": {"expose": true, "access_type": "public_method", "serialized_name": "token", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getToken", "setter": "setToken"}, "type": "net\\authorize\\api\\contract\\v1\\WebCheckOutDataTypeTokenType"}, "bankToken": {"expose": true, "access_type": "public_method", "serialized_name": "bankToken", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getBankToken", "setter": "setBankToken"}, "type": "net\\authorize\\api\\contract\\v1\\BankAccountType"}}}, "net\\authorize\\api\\contract\\v1\\WebCheckOutDataTypeTokenType": {"properties": {"cardNumber": {"expose": true, "access_type": "public_method", "serialized_name": "cardNumber", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCardNumber", "setter": "setCardNumber"}, "type": "string"}, "expirationDate": {"expose": true, "access_type": "public_method", "serialized_name": "expirationDate", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getExpirationDate", "setter": "setExpirationDate"}, "type": "string"}, "cardCode": {"expose": true, "access_type": "public_method", "serialized_name": "cardCode", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getCardCode", "setter": "setCardCode"}, "type": "string"}, "zip": {"expose": true, "access_type": "public_method", "serialized_name": "zip", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "get<PERSON><PERSON>", "setter": "<PERSON><PERSON><PERSON>"}, "type": "string"}, "fullName": {"expose": true, "access_type": "public_method", "serialized_name": "fullName", "xml_element": {"namespace": "AnetApi/xml/v1/schema/AnetApiSchema.xsd"}, "accessor": {"getter": "getFullName", "setter": "setFullName"}, "type": "string"}}}}