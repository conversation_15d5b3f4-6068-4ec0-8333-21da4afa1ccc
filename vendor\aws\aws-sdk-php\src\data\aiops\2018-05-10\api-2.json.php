<?php
// This file was auto-generated from sdk-root/src/data/aiops/2018-05-10/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-10', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'aiops', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS AI Ops', 'serviceId' => 'AIOps', 'signatureVersion' => 'v4', 'signingName' => 'aiops', 'uid' => 'aiops-2018-05-10', ], 'operations' => [ 'CreateInvestigationGroup' => [ 'name' => 'CreateInvestigationGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/investigationGroups', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateInvestigationGroupInput', ], 'output' => [ 'shape' => 'CreateInvestigationGroupOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteInvestigationGroup' => [ 'name' => 'DeleteInvestigationGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/investigationGroups/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteInvestigationGroupRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteInvestigationGroupPolicy' => [ 'name' => 'DeleteInvestigationGroupPolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/investigationGroups/{identifier}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteInvestigationGroupPolicyRequest', ], 'output' => [ 'shape' => 'DeleteInvestigationGroupPolicyOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'GetInvestigationGroup' => [ 'name' => 'GetInvestigationGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/investigationGroups/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetInvestigationGroupRequest', ], 'output' => [ 'shape' => 'GetInvestigationGroupResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetInvestigationGroupPolicy' => [ 'name' => 'GetInvestigationGroupPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/investigationGroups/{identifier}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetInvestigationGroupPolicyRequest', ], 'output' => [ 'shape' => 'GetInvestigationGroupPolicyResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListInvestigationGroups' => [ 'name' => 'ListInvestigationGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/investigationGroups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListInvestigationGroupsInput', ], 'output' => [ 'shape' => 'ListInvestigationGroupsOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'PutInvestigationGroupPolicy' => [ 'name' => 'PutInvestigationGroupPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/investigationGroups/{identifier}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutInvestigationGroupPolicyRequest', ], 'output' => [ 'shape' => 'PutInvestigationGroupPolicyResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'UpdateInvestigationGroup' => [ 'name' => 'UpdateInvestigationGroup', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/investigationGroups/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateInvestigationGroupRequest', ], 'output' => [ 'shape' => 'UpdateInvestigationGroupOutput', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ChatConfigurationArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:.*', ], 'ChatConfigurationArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChatConfigurationArn', ], 'max' => 5, 'min' => 1, ], 'ChatbotNotificationChannel' => [ 'type' => 'map', 'key' => [ 'shape' => 'SNSTopicArn', ], 'value' => [ 'shape' => 'ChatConfigurationArns', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateInvestigationGroupInput' => [ 'type' => 'structure', 'required' => [ 'name', 'roleArn', ], 'members' => [ 'name' => [ 'shape' => 'StringWithPatternAndLengthLimits', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'encryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], 'retentionInDays' => [ 'shape' => 'Retention', ], 'tags' => [ 'shape' => 'Tags', ], 'tagKeyBoundaries' => [ 'shape' => 'TagKeyBoundaries', ], 'chatbotNotificationChannel' => [ 'shape' => 'ChatbotNotificationChannel', ], 'isCloudTrailEventHistoryEnabled' => [ 'shape' => 'Boolean', ], 'crossAccountConfigurations' => [ 'shape' => 'CrossAccountConfigurations', ], ], ], 'CreateInvestigationGroupOutput' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'InvestigationGroupArn', ], ], ], 'CrossAccountConfiguration' => [ 'type' => 'structure', 'members' => [ 'sourceRoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'CrossAccountConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'CrossAccountConfiguration', ], 'max' => 25, 'min' => 0, ], 'DeleteInvestigationGroupPolicyOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteInvestigationGroupPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'InvestigationGroupIdentifier', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'DeleteInvestigationGroupRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'InvestigationGroupIdentifier', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'EncryptionConfiguration' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'EncryptionConfigurationType', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'EncryptionConfigurationType' => [ 'type' => 'string', 'enum' => [ 'AWS_OWNED_KEY', 'CUSTOMER_MANAGED_KMS_KEY', ], ], 'ForbiddenException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'GetInvestigationGroupPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'InvestigationGroupIdentifier', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'GetInvestigationGroupPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'investigationGroupArn' => [ 'shape' => 'InvestigationGroupArn', ], 'policy' => [ 'shape' => 'InvestigationGroupPolicyDocument', ], ], ], 'GetInvestigationGroupRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'InvestigationGroupIdentifier', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'GetInvestigationGroupResponse' => [ 'type' => 'structure', 'members' => [ 'createdBy' => [ 'shape' => 'IdentifierStringWithPatternAndLengthLimits', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'lastModifiedBy' => [ 'shape' => 'IdentifierStringWithPatternAndLengthLimits', ], 'lastModifiedAt' => [ 'shape' => 'Timestamp', ], 'name' => [ 'shape' => 'StringWithPatternAndLengthLimits', ], 'arn' => [ 'shape' => 'InvestigationGroupArn', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'encryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], 'retentionInDays' => [ 'shape' => 'Retention', ], 'chatbotNotificationChannel' => [ 'shape' => 'ChatbotNotificationChannel', ], 'tagKeyBoundaries' => [ 'shape' => 'TagKeyBoundaries', ], 'isCloudTrailEventHistoryEnabled' => [ 'shape' => 'Boolean', ], 'crossAccountConfigurations' => [ 'shape' => 'CrossAccountConfigurations', ], ], ], 'IdentifierStringWithPatternAndLengthLimits' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[\\-_\\/A-Za-z0-9:\\.]+', ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InvestigationGroupArn' => [ 'type' => 'string', 'pattern' => 'arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):aiops:[a-zA-Z0-9-]*:[0-9]{12}:investigation-group\\/[A-Za-z0-9]{16}', ], 'InvestigationGroupIdentifier' => [ 'type' => 'string', 'pattern' => '(?:[\\-_A-Za-z0-9]{1,512}|arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):aiops:[a-zA-Z0-9-]*:[0-9]{12}:investigation-group\\/[A-Za-z0-9]{16})', ], 'InvestigationGroupPolicyDocument' => [ 'type' => 'string', 'max' => 32768, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u00FF]+', ], 'InvestigationGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListInvestigationGroupsModel', ], ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => 'arn:.*', ], 'ListInvestigationGroupsInput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'SensitiveStringWithLengthLimits', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ListInvestigationGroupsInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListInvestigationGroupsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'ListInvestigationGroupsModel' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'InvestigationGroupArn', ], 'name' => [ 'shape' => 'StringWithPatternAndLengthLimits', ], ], ], 'ListInvestigationGroupsOutput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'SensitiveStringWithLengthLimits', ], 'investigationGroups' => [ 'shape' => 'InvestigationGroups', ], ], ], 'ListTagsForResourceOutput' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'Tags', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'PutInvestigationGroupPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', 'policy', ], 'members' => [ 'identifier' => [ 'shape' => 'InvestigationGroupIdentifier', 'location' => 'uri', 'locationName' => 'identifier', ], 'policy' => [ 'shape' => 'InvestigationGroupPolicyDocument', ], ], ], 'PutInvestigationGroupPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'investigationGroupArn' => [ 'shape' => 'InvestigationGroupArn', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'Retention' => [ 'type' => 'long', 'box' => true, 'max' => 90, 'min' => 7, ], 'RoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:.*', ], 'SNSTopicArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:.*', ], 'SensitiveStringWithLengthLimits' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'sensitive' => true, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'String' => [ 'type' => 'string', ], 'StringWithPatternAndLengthLimits' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[\\-_A-Za-z0-9\\[\\]\\(\\)\\{\\}\\.: ]+', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]+)', ], 'TagKeyBoundaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)', ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateInvestigationGroupOutput' => [ 'type' => 'structure', 'members' => [], ], 'UpdateInvestigationGroupRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'InvestigationGroupIdentifier', 'location' => 'uri', 'locationName' => 'identifier', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'encryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], 'tagKeyBoundaries' => [ 'shape' => 'TagKeyBoundaries', ], 'chatbotNotificationChannel' => [ 'shape' => 'ChatbotNotificationChannel', ], 'isCloudTrailEventHistoryEnabled' => [ 'shape' => 'Boolean', ], 'crossAccountConfigurations' => [ 'shape' => 'CrossAccountConfigurations', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], ],];
