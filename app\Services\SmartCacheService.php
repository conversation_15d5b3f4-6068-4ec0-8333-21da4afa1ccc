<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class SmartCacheService
{
    protected $defaultTtl = 3600; // ساعة واحدة
    protected $shortTtl = 300;    // 5 دقائق
    protected $longTtl = 86400;   // 24 ساعة
    protected $cachePrefix = 'smart_cache_';

    /**
     * تخزين البيانات مع استراتيجية ذكية
     */
    public function remember($key, $data, array $options = [])
    {
        $ttl = $this->determineTtl($key, $options);
        $tags = $options['tags'] ?? [];
        $compress = $options['compress'] ?? false;

        $cacheKey = $this->buildCacheKey($key, $options);

        if ($compress && is_array($data) || is_object($data)) {
            $data = $this->compressData($data);
        }

        if (!empty($tags)) {
            return Cache::tags($tags)->remember($cacheKey, $ttl, function() use ($data) {
                return is_callable($data) ? $data() : $data;
            });
        }

        return Cache::remember($cacheKey, $ttl, function() use ($data) {
            return is_callable($data) ? $data() : $data;
        });
    }

    /**
     * تخزين البيانات مع Lazy Loading
     */
    public function rememberLazy($key, callable $dataLoader, array $options = [])
    {
        $chunkSize = $options['chunk_size'] ?? 100;
        $ttl = $this->determineTtl($key, $options);
        $tags = $options['tags'] ?? [];

        $cacheKey = $this->buildCacheKey($key, $options);

        // التحقق من وجود البيانات في Cache
        if ($this->has($cacheKey)) {
            return $this->get($cacheKey);
        }

        // تحميل البيانات بـ Lazy Loading
        $lazyData = $dataLoader();
        
        // تخزين البيانات على شكل chunks
        if ($lazyData instanceof \Illuminate\Support\LazyCollection) {
            $chunks = [];
            $chunkIndex = 0;
            
            $lazyData->chunk($chunkSize)->each(function($chunk) use (&$chunks, &$chunkIndex, $cacheKey, $ttl, $tags) {
                $chunkKey = $cacheKey . '_chunk_' . $chunkIndex;
                
                if (!empty($tags)) {
                    Cache::tags($tags)->put($chunkKey, $chunk->toArray(), $ttl);
                } else {
                    Cache::put($chunkKey, $chunk->toArray(), $ttl);
                }
                
                $chunks[] = $chunkKey;
                $chunkIndex++;
            });

            // تخزين فهرس الـ chunks
            $indexKey = $cacheKey . '_index';
            if (!empty($tags)) {
                Cache::tags($tags)->put($indexKey, $chunks, $ttl);
            } else {
                Cache::put($indexKey, $chunks, $ttl);
            }

            return $this->getLazyFromChunks($cacheKey);
        }

        // تخزين البيانات العادية
        if (!empty($tags)) {
            Cache::tags($tags)->put($cacheKey, $lazyData, $ttl);
        } else {
            Cache::put($cacheKey, $lazyData, $ttl);
        }

        return $lazyData;
    }

    /**
     * الحصول على البيانات من الـ chunks
     */
    protected function getLazyFromChunks($cacheKey)
    {
        $indexKey = $cacheKey . '_index';
        $chunks = Cache::get($indexKey, []);

        return collect($chunks)->lazy()->flatMap(function($chunkKey) {
            return collect(Cache::get($chunkKey, []));
        });
    }

    /**
     * تخزين إحصائيات محسوبة
     */
    public function rememberStats($key, callable $calculator, array $options = [])
    {
        $ttl = $options['ttl'] ?? $this->longTtl; // الإحصائيات تحتاج وقت أطول
        $tags = $options['tags'] ?? ['stats'];

        $cacheKey = $this->buildCacheKey($key, $options);

        return Cache::tags($tags)->remember($cacheKey, $ttl, $calculator);
    }

    /**
     * تخزين نتائج البحث
     */
    public function rememberSearch($searchTerm, $filters, callable $searcher, array $options = [])
    {
        $ttl = $options['ttl'] ?? $this->shortTtl; // البحث يحتاج وقت قصير
        $tags = $options['tags'] ?? ['search'];

        $searchKey = md5($searchTerm . serialize($filters));
        $cacheKey = $this->buildCacheKey('search_' . $searchKey, $options);

        return Cache::tags($tags)->remember($cacheKey, $ttl, $searcher);
    }

    /**
     * تحديد مدة التخزين بناءً على نوع البيانات
     */
    protected function determineTtl($key, array $options = [])
    {
        if (isset($options['ttl'])) {
            return $options['ttl'];
        }

        // تحديد TTL بناءً على نوع البيانات
        if (str_contains($key, 'stats') || str_contains($key, 'report')) {
            return $this->longTtl;
        }

        if (str_contains($key, 'search') || str_contains($key, 'filter')) {
            return $this->shortTtl;
        }

        return $this->defaultTtl;
    }

    /**
     * بناء مفتاح Cache مع معرف المستخدم
     */
    protected function buildCacheKey($key, array $options = [])
    {
        $userId = $options['user_id'] ?? auth()->id() ?? 'guest';
        $version = $options['version'] ?? '1';
        
        return $this->cachePrefix . $userId . '_' . $version . '_' . $key;
    }

    /**
     * ضغط البيانات لتوفير الذاكرة
     */
    protected function compressData($data)
    {
        if (is_object($data) && method_exists($data, 'toArray')) {
            $data = $data->toArray();
        }

        return [
            'compressed' => true,
            'data' => gzcompress(serialize($data), 6)
        ];
    }

    /**
     * إلغاء ضغط البيانات
     */
    protected function decompressData($data)
    {
        if (is_array($data) && isset($data['compressed']) && $data['compressed']) {
            return unserialize(gzuncompress($data['data']));
        }

        return $data;
    }

    /**
     * التحقق من وجود البيانات
     */
    public function has($key, array $options = [])
    {
        $cacheKey = $this->buildCacheKey($key, $options);
        return Cache::has($cacheKey);
    }

    /**
     * الحصول على البيانات
     */
    public function get($key, $default = null, array $options = [])
    {
        $cacheKey = $this->buildCacheKey($key, $options);
        $data = Cache::get($cacheKey, $default);

        return $this->decompressData($data);
    }

    /**
     * حذف البيانات
     */
    public function forget($key, array $options = [])
    {
        $cacheKey = $this->buildCacheKey($key, $options);
        return Cache::forget($cacheKey);
    }

    /**
     * حذف البيانات بالـ tags
     */
    public function forgetByTags(array $tags)
    {
        return Cache::tags($tags)->flush();
    }

    /**
     * تنظيف Cache منتهي الصلاحية
     */
    public function cleanup()
    {
        // حذف البيانات القديمة
        $this->forgetByTags(['search']); // حذف نتائج البحث
        
        // تنظيف chunks المنتهية الصلاحية
        $this->cleanupExpiredChunks();

        return true;
    }

    /**
     * تنظيف الـ chunks المنتهية الصلاحية
     */
    protected function cleanupExpiredChunks()
    {
        // البحث عن index keys
        $pattern = $this->cachePrefix . '*_index';
        
        if (Cache::getStore() instanceof \Illuminate\Cache\RedisStore) {
            $keys = Redis::keys($pattern);
            
            foreach ($keys as $indexKey) {
                $chunks = Cache::get($indexKey);
                
                if (is_array($chunks)) {
                    foreach ($chunks as $chunkKey) {
                        if (!Cache::has($chunkKey)) {
                            // إذا كان chunk محذوف، احذف الفهرس أيضاً
                            Cache::forget($indexKey);
                            break;
                        }
                    }
                }
            }
        }
    }

    /**
     * إحصائيات Cache
     */
    public function getStats()
    {
        $stats = [
            'total_keys' => 0,
            'memory_usage' => 0,
            'hit_rate' => 0,
        ];

        if (Cache::getStore() instanceof \Illuminate\Cache\RedisStore) {
            $info = Redis::info('memory');
            $stats['memory_usage'] = $info['used_memory_human'] ?? 'N/A';
            
            $keyspace = Redis::info('keyspace');
            $stats['total_keys'] = $keyspace['db0']['keys'] ?? 0;
        }

        return $stats;
    }
}
