<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * إنشاء جدول سجلات تتبع الوقت
 * 
 * هذا الجدول يحتوي على سجلات الوقت المستغرق في المهام والمشاريع
 * مع إمكانيات متقدمة لحساب التكاليف والفوترة
 * 
 * @package Database\Migrations
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
return new class extends Migration
{
    /**
     * تشغيل الـ Migration
     * 
     * @return void
     */
    public function up(): void
    {
        Schema::create('time_entries', function (Blueprint $table) {
            // المعرف الأساسي
            $table->id()->comment('معرف سجل الوقت الفريد');
            
            // العلاقات الأساسية
            $table->foreignId('user_id')
                  ->constrained('users')
                  ->onDelete('cascade')
                  ->comment('معرف المستخدم الذي سجل الوقت');
                  
            $table->foreignId('project_id')
                  ->constrained('projects')
                  ->onDelete('cascade')
                  ->comment('معرف المشروع');
                  
            $table->foreignId('task_id')
                  ->nullable()
                  ->constrained('tasks')
                  ->onDelete('cascade')
                  ->comment('معرف المهمة (اختياري)');
            
            // وصف العمل المنجز
            $table->text('description')->nullable()->comment('وصف العمل المنجز خلال هذا الوقت');
            
            // معلومات الوقت
            $table->decimal('hours', 8, 2)->default(0)->comment('عدد الساعات المسجلة');
            $table->date('date')->comment('تاريخ العمل');
            $table->timestamp('start_time')->nullable()->comment('وقت بداية العمل');
            $table->timestamp('end_time')->nullable()->comment('وقت انتهاء العمل');
            
            // معلومات الفوترة
            $table->boolean('is_billable')->default(true)->comment('هل هذا الوقت قابل للفوترة');
            $table->decimal('hourly_rate', 8, 2)->nullable()->comment('السعر بالساعة وقت التسجيل');
            $table->decimal('total_cost', 10, 2)->nullable()->comment('التكلفة الإجمالية المحسوبة');
            
            // معلومات الفاتورة
            $table->string('invoice_id', 100)->nullable()->comment('معرف الفاتورة إذا تم فوترة هذا الوقت');
            
            // بيانات إضافية
            $table->json('metadata')->nullable()->comment('بيانات إضافية مثل الموقع، نوع الجهاز، إلخ');
            
            // طوابع زمنية
            $table->timestamps();
            $table->softDeletes();
            
            // الفهارس المركبة لتحسين الأداء
            $table->index(['user_id', 'date'], 'idx_time_entries_user_date');
            $table->index(['project_id', 'date'], 'idx_time_entries_project_date');
            $table->index(['task_id', 'date'], 'idx_time_entries_task_date');
            $table->index(['user_id', 'project_id', 'date'], 'idx_time_entries_user_project_date');
            $table->index(['is_billable', 'invoice_id'], 'idx_time_entries_billing');
            $table->index(['date', 'is_billable'], 'idx_time_entries_date_billable');
            $table->index(['invoice_id'], 'idx_time_entries_invoice');
            
            // فهرس نصي للبحث في الوصف
            $table->fullText(['description'], 'idx_time_entries_description_search');
            
            // قيود إضافية
            $table->check('hours >= 0', 'chk_time_entries_hours_positive');
            $table->check('hourly_rate IS NULL OR hourly_rate >= 0', 'chk_time_entries_rate_positive');
            $table->check('total_cost IS NULL OR total_cost >= 0', 'chk_time_entries_cost_positive');
            $table->check('start_time IS NULL OR end_time IS NULL OR start_time <= end_time', 'chk_time_entries_time_order');
        });
        
        DB::statement("ALTER TABLE time_entries COMMENT = 'جدول سجلات تتبع الوقت - يحتوي على الوقت المستغرق في المهام والمشاريع'");
    }

    /**
     * التراجع عن الـ Migration
     * 
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('time_entries');
    }
};
