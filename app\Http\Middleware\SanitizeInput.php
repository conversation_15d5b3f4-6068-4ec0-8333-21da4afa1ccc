<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SanitizeInput
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $input = $request->all();
        
        array_walk_recursive($input, function (&$input) {
            if (is_string($input)) {
                // إزالة HTML tags الخطيرة
                $input = strip_tags($input, '<p><br><strong><em><ul><ol><li>');
                
                // تنظيف من XSS
                $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
                
                // إزالة المسافات الزائدة
                $input = trim($input);
                
                // إزالة null bytes
                $input = str_replace(chr(0), '', $input);
                
                // إزالة control characters
                $input = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $input);
            }
        });
        
        $request->merge($input);
        
        return $next($request);
    }
}
