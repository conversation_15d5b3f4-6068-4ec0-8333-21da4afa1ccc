<?php


namespace Milon\Barcode\GS1_128;


class AIData
{
    public static $default = [
        '00' => [18, 18, 'Serial Shipping Container Code (SSCC-18)'],
        '01' => [14, 14, 'Global Trade Item Number (GTIN)'],
        '02' => [14, 14, 'GTIN of Contained Trade Items'],
        '10' => [1, 20, 'Batch or Lot Number'],
        '11' => [6, 6, 'Production Date'],
        '12' => [6, 6, 'Due Date'],
        '13' => [6, 6, 'Packaging Date'],
        '15' => [6, 6, 'Best Before Date'],
        '16' => [6, 6, 'Sell By Date'],
        '17' => [6, 6, 'Expiration Date'],
        '20' => [2, 2, 'Internal Product Variant'],
        '21' => [1, 20, 'Serial Number'],
        '22' => [1, 20, 'Consumer Product Variant'],
        '235' => [1, 28, 'Third Party Controlled, Serialised Extension of GTIN (TPX)'],
        '240' => [1, 30, 'Additional Item Identification'],
        '241' => [1, 30, 'Customer Part Number'],
        '242' => [1, 6, 'Made-to-Order Variation Number'],
        '243' => [1, 20, 'Packaging Comnponent Number'],
        '250' => [1, 30, 'Second Serial Number'],
        '251' => [1, 30, 'Reference to Source Entity'],
        '253' => [14, 30, 'Global Document Type Identifier (GDTI)'],
        '254' => [1, 20, 'GLN Extension Component'],
        '255' => [14, 25, 'Global Coupon Number (GCN)'],
        '30' => [1, 8, 'Variable Count of Items (variable measure trade item)'],
        '310y' => [6, 6, 'Net Weight in kilograms (variable measure trade item)'],
        '311y' => [6, 6, 'Length or 1st Dimension, in meters (variable measure trade item)'],
        '312y' => [6, 6, 'Width, Diameter, or 2nd Dimension, in meters (variable measure trade item)'],
        '313y' => [6, 6, 'Depth, Thickness, Height, or 3rd Dimension, in meters (variable measure trade item)'],
        '314y' => [6, 6, 'Area, in square meters (variable measure trade item)'],
        '315y' => [6, 6, 'Net Volume, in liters (variable measure trade item)'],
        '316y' => [6, 6, 'Net Volume, in cubic meters (variable measure trade item)'],
        '320y' => [6, 6, 'Net Weight, in pounds (variable measure trade item)'],
        '321y' => [6, 6, 'Length or 1st Dimension, in inches (variable measure trade item)'],
        '322y' => [6, 6, 'Length or 1st Dimension, in feet (variable measure trade item)'],
        '323y' => [6, 6, 'Length, 1st Dimension, in yards (variable measure trade item)'],
        '324y' => [6, 6, 'Width, Diameter, or 2nd Dimension, in inches (variable measure trade item)'],
        '325y' => [6, 6, 'Width, Diameter, or 2nd Dimension, in feet (variable measure trade item)'],
        '326y' => [6, 6, 'Width, Diameter, or 2nd Dimension, in yards (variable measure trade item)'],
        '327y' => [6, 6, 'Depth, Thickness, Height, or 3rd Dimension, in inches (variable measure trade item)'],
        '328y' => [6, 6, 'Depth, Thickness, Height, or 3rd Dimension, in feet (variable measure trade item)'],
        '329y' => [6, 6, 'Depth, Thickness, Height, or 3rd Dimension, in yards (variable measure trade item)'],
        '330y' => [6, 6, 'Logistic Weight, in kilograms'],
        '331y' => [6, 6, 'Length, or 1st Dimension, in meters'],
        '332y' => [6, 6, 'Width, Diameter, or 2nd Dimension, in meters'],
        '333y' => [6, 6, 'Depth, Thickness, Height, or 3rd Dimension, in meters'],
        '334y' => [6, 6, 'Area, in square meters'],
        '335y' => [6, 6, 'Logistic Volume, in liters'],
        '336y' => [6, 6, 'Logistic Volume, in cubic meters'],
        '337y' => [6, 6, 'Kilograms per square meter'],
        '340y' => [6, 6, 'Logistic Weight, in pounds'],
        '341y' => [6, 6, 'Length or 1st Dimension, in inches'],
        '342y' => [6, 6, 'Length or 1st Dimension, in feet'],
        '343y' => [6, 6, 'Container Length\/1st Dimension in, in yards'],
        '344y' => [6, 6, 'Width, Diameter, or 2nd Dimension, in inches'],
        '345y' => [6, 6, 'Width, Diameter, or 2nd Dimension, in feet'],
        '346y' => [6, 6, 'Width, Diameter, or 2nd Dimension, in yards'],
        '347y' => [6, 6, 'Depth, Thickness, Height, or 3rd Dimension, in inches'],
        '348y' => [6, 6, 'Depth, Thickness, Height, or 3rd Dimension, in feet'],
        '349y' => [6, 6, 'Depth, Thickness, Height, 3rd Dimension, in yards'],
        '350y' => [6, 6, 'Area, in square inches (variable measure trade item)'],
        '351y' => [6, 6, 'Area, in square feet (variable measure trade item)'],
        '352y' => [6, 6, 'Area, in square yards (variable measure trade item)'],
        '353y' => [6, 6, 'Area, in square inches'],
        '354y' => [6, 6, 'Area, in square feet'],
        '355y' => [6, 6, 'Area, in square yards'],
        '356y' => [6, 6, 'Net Weight, in troy ounces (variable measure trade item)'],
        '357y' => [6, 6, 'Net Weight or volume, in ounces (variable measure trade item)'],
        '360y' => [6, 6, 'Net Volume, in quarts (variable measure trade item)'],
        '361y' => [6, 6, 'Net Volume, in U.S. gallons (variable measure trade item)'],
        '362y' => [6, 6, 'Logistic Volume, in quarts'],
        '363y' => [6, 6, 'Logistic Volume, in U.S. gallons'],
        '364y' => [6, 6, 'Net Volume, in cubic inches (variable measure trade item)'],
        '365y' => [6, 6, 'Net Volume, in cubic feet (variable measure trade item)'],
        '366y' => [6, 6, 'Net Volume, in cubic yards (variable measure trade item)'],
        '367y' => [6, 6, 'Logistic Volume, in cubic inches'],
        '368y' => [6, 6, 'Logistic Volume, in cubic feet'],
        '369y' => [6, 6, 'Logistic Volume, in cubic yards'],
        '37' => [1, 8, 'Count of trade items'],
        '390y' => [1, 15, 'Applicable Amount Payable or Coupon Value, in local currency'],
        '391y' => [4, 18, 'Applicable Amount Payable with ISO Currency Code'],
        '392y' => [1, 15, 'Applicable Amount Payable, Single Monetary Area (variable measure trade item)'],
        '393y' => [4, 18, 'Applicable Amount Payable With ISO Currency Code (variable measure trade item)'],
        '394y' => [4, 4, 'Percentage Discount of a Coupon'],
        '395y' => [6, 6, 'Amount Payable per unit of measure single monetary area (variable measure trade item)'],
        '400' => [1, 30, 'Customer\'s Purchase Order Number'],
        '401' => [1, 30, 'Global Identification Number for Consignment (GINC)'],
        '402' => [17, 17, 'Global Shipment Identification Number (GSIN)'],
        '403' => [1, 30, 'Routing Code'],
        '410' => [13, 13, 'Ship To\/Deliver To Global Location Number'],
        '411' => [13, 13, 'Bill To\/Invoice To Global Location Number'],
        '412' => [13, 13, 'Purchased From Global Location Number'],
        '413' => [13, 13, 'Ship For\/Deliver For\/Forward To Global Location Number'],
        '414' => [13, 13, 'Identification of a Physical Location - Global Location Number'],
        '415' => [13, 13, 'Global Location Number of The Invoicing Party'],
        '416' => [13, 13, 'Global Location Number of The Production or Service Location'],
        '417' => [13, 13, 'Party GLN'],
        '420' => [1, 20, 'Ship To\/Deliver To Postal Code Within a Single Postal Authority'],
        '421' => [4, 12, 'Ship To\/Deliver To Postal Code With ISO Country Code'],
        '422' => [3, 3, 'Country of Origin of a Trade Item'],
        '423' => [3, 15, 'Country of Initial Processing'],
        '424' => [3, 3, 'Country of Processing'],
        '425' => [3, 3, 'Country of Disassembly'],
        '426' => [3, 3, 'Country Covering Full Process Chain'],
        '427' => [1, 3, 'Country Subdivision of Origin'],
        '4300' => [1, 35, 'Ship-to \/ Deliver-to company name'],
        '4301' => [1, 35, 'Ship-to \/ Deliver-to contact'],
        '4302' => [1, 70, 'Ship-to \/ Deliver-to address line 1'],
        '4303' => [1, 70, 'Ship-to \/ Deliver-to address line 2'],
        '4304' => [1, 70, 'Ship-to \/ Deliver-to suburb'],
        '4305' => [1, 70, 'Ship-to \/ Deliver-to locality'],
        '4306' => [1, 70, 'Ship-to \/ Deliver-to region'],
        '4307' => [2, 2, 'Ship-to \/ Deliver-to country code'],
        '4308' => [1, 30, 'Ship-to \/ Deliver-to telephone number'],
        '4310' => [1, 35, 'Return-to company name'],
        '4311' => [1, 35, 'Return-to contact'],
        '4312' => [1, 70, 'Return-to address line 1'],
        '4313' => [1, 70, 'Return-to address line 2'],
        '4314' => [1, 70, 'Return-to suburb'],
        '4315' => [1, 70, 'Return-to locality'],
        '4316' => [1, 70, 'Return-to region'],
        '4317' => [2, 2, 'Return-to country code'],
        '4318' => [1, 20, 'Return-to postal code'],
        '4319' => [1, 30, 'Return-to telephone number'],
        '4320' => [1, 35, 'Service code description'],
        '4321' => [1, 1, 'Dangerous goods flag'],
        '4322' => [1, 1, 'Authority to leave'],
        '4323' => [1, 1, 'Signature required flag'],
        '4324' => [10, 10, 'Not before delivery date time'],
        '4325' => [10, 10, 'Not after delivery date time'],
        '4326' => [6, 6, 'Release date'],
        '7001' => [13, 13, 'NATO Stock Number (NSN)'],
        '7002' => [1, 30, 'UN\/ECE Meat Carcasses and Cuts Classification'],
        '7003' => [10, 10, 'Expiration Date and Time'],
        '7004' => [1, 4, 'Active Potency'],
        '7005' => [1, 12, 'Catch Area'],
        '7006' => [6, 6, 'First Freeze Date'],
        '7007' => [6, 12, 'Harvest Date'],
        '7008' => [1, 3, 'Species For Fishery Purposes'],
        '7009' => [1, 10, 'Fishing Gear Type'],
        '7010' => [1, 2, 'Production Method'],
        '7020' => [1, 20, 'Refurbishment Lot ID'],
        '7021' => [1, 20, 'Functional Status'],
        '7022' => [1, 20, 'Revision Status'],
        '7023' => [1, 30, 'Global Individual Asset Identifier (GIAI) of an Assembly'],
        '703y' => [3, 30, 'Number of Processor with ISO Country Code'],
        '7040' => [4, 4, 'GS1 UIC with Extension 1 and Importer index'],
        '710' => [1, 20, 'National Healthcare Reimbursement Number (NHRN) - Germany PZN'],
        '711' => [1, 20, 'National Healthcare Reimbursement Number (NHRN) - France CIP'],
        '712' => [1, 20, 'National Healthcare Reimbursement Number (NHRN) - Spain CN'],
        '713' => [1, 20, 'National Healthcare Reimbursement Number (NHRN) - Brasil DRN'],
        '714' => [1, 20, 'National Healthcare Reimbursement Number (NHRN) - Portugal AIM'],
        '723y' => [2, 30, 'Certification reference'],
        '7240' => [1, 20, 'Protocol ID'],
        '8001' => [14, 14, 'Roll Products - Width\/Length\/Core Diameter\/Direction\/Splices'],
        '8002' => [1, 20, 'Cellular Mobile Telphone Identifier'],
        '8003' => [15, 30, 'Global Returnable Asset Identifier (GRAI)'],
        '8004' => [1, 30, 'Global Individual Asset Identifier (GIAI)'],
        '8005' => [6, 6, 'Price per Unit of Measure'],
        '8006' => [18, 18, 'Identification of an Individual Trade Item Piece'],
        '8007' => [1, 34, 'International Bank Account Number (IBAN)'],
        '8008' => [8, 12, 'Date and Time of Production'],
        '8009' => [1, 50, 'Optically Readable Sensor Indicator'],
        '8010' => [1, 30, 'Component\/Part Identifier (CPID)'],
        '8011' => [1, 12, 'Component\/Part Identifier Serial Number (CPID Serial)'],
        '8012' => [1, 20, 'Software Version'],
        '8013' => [1, 30, 'Global Model Number (GMN)'],
        '8017' => [18, 18, 'Global Service Relation Number to Identify the Relationship Between an Organisation Offering Services and the Provider of Services'],
        '8018' => [18, 18, 'Global Service Relation Number to Identify the Relationship Between an Organisation Offering Services and the Recipient of Services'],
        '8019' => [1, 10, 'Service Relation Instance Number (SRIN)'],
        '8020' => [1, 25, 'Payment Slip Reference Number'],
        '8026' => [18, 18, 'Identification of pieces of a trade item (ITIP) contained in a logistic unit'],
        '8110' => [1, 70, 'Coupon Code Identification for Use in North America'],
        '8111' => [4, 4, 'Loyalty Points of a Coupon'],
        '8112' => [1, 70, 'Paperless Coupon Code Identification for Use in North America (AI 8112)'],
        '8200' => [1, 70, 'Extended Packaging URL'],
        '90' => [1, 30, 'Information Mutually Agreed Between Trading Partners'],
        '91' => [1, 90, 'Internal Company Codes'],
        '92' => [1, 90, 'Internal Company Codes'],
        '93' => [1, 90, 'Internal Company Codes'],
        '94' => [1, 90, 'Internal Company Codes'],
        '95' => [1, 90, 'Internal Company Codes'],
        '96' => [1, 90, 'Internal Company Codes'],
        '97' => [1, 90, 'Internal Company Codes'],
        '98' => [1, 90, 'Internal Company Codes'],
        '99' => [1, 90, 'Internal Company Codes']
    ];
}
