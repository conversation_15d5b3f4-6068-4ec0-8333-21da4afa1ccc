<?php
namespace Aws\Notifications;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AWS User Notifications** service.
 * @method \Aws\Result associateChannel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateChannelAsync(array $args = [])
 * @method \Aws\Result associateManagedNotificationAccountContact(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateManagedNotificationAccountContactAsync(array $args = [])
 * @method \Aws\Result associateManagedNotificationAdditionalChannel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateManagedNotificationAdditionalChannelAsync(array $args = [])
 * @method \Aws\Result createEventRule(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createEventRuleAsync(array $args = [])
 * @method \Aws\Result createNotificationConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createNotificationConfigurationAsync(array $args = [])
 * @method \Aws\Result deleteEventRule(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteEventRuleAsync(array $args = [])
 * @method \Aws\Result deleteNotificationConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteNotificationConfigurationAsync(array $args = [])
 * @method \Aws\Result deregisterNotificationHub(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deregisterNotificationHubAsync(array $args = [])
 * @method \Aws\Result disableNotificationsAccessForOrganization(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disableNotificationsAccessForOrganizationAsync(array $args = [])
 * @method \Aws\Result disassociateChannel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateChannelAsync(array $args = [])
 * @method \Aws\Result disassociateManagedNotificationAccountContact(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateManagedNotificationAccountContactAsync(array $args = [])
 * @method \Aws\Result disassociateManagedNotificationAdditionalChannel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateManagedNotificationAdditionalChannelAsync(array $args = [])
 * @method \Aws\Result enableNotificationsAccessForOrganization(array $args = [])
 * @method \GuzzleHttp\Promise\Promise enableNotificationsAccessForOrganizationAsync(array $args = [])
 * @method \Aws\Result getEventRule(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getEventRuleAsync(array $args = [])
 * @method \Aws\Result getManagedNotificationChildEvent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getManagedNotificationChildEventAsync(array $args = [])
 * @method \Aws\Result getManagedNotificationConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getManagedNotificationConfigurationAsync(array $args = [])
 * @method \Aws\Result getManagedNotificationEvent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getManagedNotificationEventAsync(array $args = [])
 * @method \Aws\Result getNotificationConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getNotificationConfigurationAsync(array $args = [])
 * @method \Aws\Result getNotificationEvent(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getNotificationEventAsync(array $args = [])
 * @method \Aws\Result getNotificationsAccessForOrganization(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getNotificationsAccessForOrganizationAsync(array $args = [])
 * @method \Aws\Result listChannels(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listChannelsAsync(array $args = [])
 * @method \Aws\Result listEventRules(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listEventRulesAsync(array $args = [])
 * @method \Aws\Result listManagedNotificationChannelAssociations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listManagedNotificationChannelAssociationsAsync(array $args = [])
 * @method \Aws\Result listManagedNotificationChildEvents(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listManagedNotificationChildEventsAsync(array $args = [])
 * @method \Aws\Result listManagedNotificationConfigurations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listManagedNotificationConfigurationsAsync(array $args = [])
 * @method \Aws\Result listManagedNotificationEvents(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listManagedNotificationEventsAsync(array $args = [])
 * @method \Aws\Result listNotificationConfigurations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listNotificationConfigurationsAsync(array $args = [])
 * @method \Aws\Result listNotificationEvents(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listNotificationEventsAsync(array $args = [])
 * @method \Aws\Result listNotificationHubs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listNotificationHubsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result registerNotificationHub(array $args = [])
 * @method \GuzzleHttp\Promise\Promise registerNotificationHubAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateEventRule(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateEventRuleAsync(array $args = [])
 * @method \Aws\Result updateNotificationConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateNotificationConfigurationAsync(array $args = [])
 */
class NotificationsClient extends AwsClient {}
