<?php
// This file was auto-generated from sdk-root/src/data/sagemaker-edge/2020-09-23/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-09-23', 'endpointPrefix' => 'edge.sagemaker', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon Sagemaker Edge Manager', 'serviceId' => 'Sagemaker Edge', 'signatureVersion' => 'v4', 'signingName' => 'sagemaker', 'uid' => 'sagemaker-edge-2020-09-23', ], 'operations' => [ 'GetDeployments' => [ 'name' => 'GetDeployments', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetDeployments', ], 'input' => [ 'shape' => 'GetDeploymentsRequest', ], 'output' => [ 'shape' => 'GetDeploymentsResult', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], ], ], 'GetDeviceRegistration' => [ 'name' => 'GetDeviceRegistration', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetDeviceRegistration', ], 'input' => [ 'shape' => 'GetDeviceRegistrationRequest', ], 'output' => [ 'shape' => 'GetDeviceRegistrationResult', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], ], ], 'SendHeartbeat' => [ 'name' => 'SendHeartbeat', 'http' => [ 'method' => 'POST', 'requestUri' => '/SendHeartbeat', ], 'input' => [ 'shape' => 'SendHeartbeatRequest', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], ], ], ], 'shapes' => [ 'CacheTTLSeconds' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'Checksum' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'ChecksumType', ], 'Sum' => [ 'shape' => 'ChecksumString', ], ], ], 'ChecksumString' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-z0-9](-*[a-z0-9])*$', ], 'ChecksumType' => [ 'type' => 'string', 'enum' => [ 'SHA1', ], ], 'Definition' => [ 'type' => 'structure', 'members' => [ 'ModelHandle' => [ 'shape' => 'EntityName', ], 'S3Url' => [ 'shape' => 'S3Uri', ], 'Checksum' => [ 'shape' => 'Checksum', ], 'State' => [ 'shape' => 'ModelState', ], ], ], 'Definitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Definition', ], ], 'DeploymentModel' => [ 'type' => 'structure', 'members' => [ 'ModelHandle' => [ 'shape' => 'EntityName', ], 'ModelName' => [ 'shape' => 'ModelName', ], 'ModelVersion' => [ 'shape' => 'Version', ], 'DesiredState' => [ 'shape' => 'ModelState', ], 'State' => [ 'shape' => 'ModelState', ], 'Status' => [ 'shape' => 'DeploymentStatus', ], 'StatusReason' => [ 'shape' => 'String', ], 'RollbackFailureReason' => [ 'shape' => 'String', ], ], ], 'DeploymentModels' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeploymentModel', ], ], 'DeploymentResult' => [ 'type' => 'structure', 'members' => [ 'DeploymentName' => [ 'shape' => 'EntityName', ], 'DeploymentStatus' => [ 'shape' => 'EntityName', ], 'DeploymentStatusMessage' => [ 'shape' => 'String', ], 'DeploymentStartTime' => [ 'shape' => 'Timestamp', ], 'DeploymentEndTime' => [ 'shape' => 'Timestamp', ], 'DeploymentModels' => [ 'shape' => 'DeploymentModels', ], ], ], 'DeploymentStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCESS', 'FAIL', ], ], 'DeploymentType' => [ 'type' => 'string', 'enum' => [ 'Model', ], ], 'DeviceFleetName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*_*[a-zA-Z0-9])*$', ], 'DeviceName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*_*[a-zA-Z0-9])*$', ], 'DeviceRegistration' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'Dimension' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9\\/])*$', ], 'EdgeDeployment' => [ 'type' => 'structure', 'members' => [ 'DeploymentName' => [ 'shape' => 'EntityName', ], 'Type' => [ 'shape' => 'DeploymentType', ], 'FailureHandlingPolicy' => [ 'shape' => 'FailureHandlingPolicy', ], 'Definitions' => [ 'shape' => 'Definitions', ], ], ], 'EdgeDeployments' => [ 'type' => 'list', 'member' => [ 'shape' => 'EdgeDeployment', ], ], 'EdgeMetric' => [ 'type' => 'structure', 'members' => [ 'Dimension' => [ 'shape' => 'Dimension', ], 'MetricName' => [ 'shape' => 'Metric', ], 'Value' => [ 'shape' => 'Value', ], 'Timestamp' => [ 'shape' => 'Timestamp', ], ], ], 'EdgeMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'EdgeMetric', ], ], 'EntityName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9])*$', ], 'ErrorMessage' => [ 'type' => 'string', ], 'FailureHandlingPolicy' => [ 'type' => 'string', 'enum' => [ 'ROLLBACK_ON_FAILURE', 'DO_NOTHING', ], ], 'GetDeploymentsRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceName', 'DeviceFleetName', ], 'members' => [ 'DeviceName' => [ 'shape' => 'DeviceName', ], 'DeviceFleetName' => [ 'shape' => 'DeviceFleetName', ], ], ], 'GetDeploymentsResult' => [ 'type' => 'structure', 'members' => [ 'Deployments' => [ 'shape' => 'EdgeDeployments', ], ], ], 'GetDeviceRegistrationRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceName', 'DeviceFleetName', ], 'members' => [ 'DeviceName' => [ 'shape' => 'DeviceName', ], 'DeviceFleetName' => [ 'shape' => 'DeviceFleetName', ], ], ], 'GetDeviceRegistrationResult' => [ 'type' => 'structure', 'members' => [ 'DeviceRegistration' => [ 'shape' => 'DeviceRegistration', ], 'CacheTTL' => [ 'shape' => 'CacheTTLSeconds', ], ], ], 'InternalServiceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'Metric' => [ 'type' => 'string', 'max' => 100, 'min' => 4, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9])*$', ], 'Model' => [ 'type' => 'structure', 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'ModelVersion' => [ 'shape' => 'Version', ], 'LatestSampleTime' => [ 'shape' => 'Timestamp', ], 'LatestInference' => [ 'shape' => 'Timestamp', ], 'ModelMetrics' => [ 'shape' => 'EdgeMetrics', ], ], ], 'ModelName' => [ 'type' => 'string', 'max' => 255, 'min' => 4, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9])*$', ], 'ModelState' => [ 'type' => 'string', 'enum' => [ 'DEPLOY', 'UNDEPLOY', ], ], 'Models' => [ 'type' => 'list', 'member' => [ 'shape' => 'Model', ], ], 'S3Uri' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '^s3://([^/]+)/?(.*)$', ], 'SendHeartbeatRequest' => [ 'type' => 'structure', 'required' => [ 'AgentVersion', 'DeviceName', 'DeviceFleetName', ], 'members' => [ 'AgentMetrics' => [ 'shape' => 'EdgeMetrics', ], 'Models' => [ 'shape' => 'Models', ], 'AgentVersion' => [ 'shape' => 'Version', ], 'DeviceName' => [ 'shape' => 'DeviceName', ], 'DeviceFleetName' => [ 'shape' => 'DeviceFleetName', ], 'DeploymentResult' => [ 'shape' => 'DeploymentResult', ], ], ], 'String' => [ 'type' => 'string', ], 'Timestamp' => [ 'type' => 'timestamp', ], 'Value' => [ 'type' => 'double', ], 'Version' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\ \\_\\.]+', ], ],];
