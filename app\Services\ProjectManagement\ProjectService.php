<?php

namespace App\Services\ProjectManagement;

use App\Models\ProjectManagement\Project;
use App\Repositories\ProjectManagement\ProjectRepository;
use App\Repositories\ProjectManagement\TaskRepository;
use App\Services\ProjectManagement\NotificationService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * خدمة إدارة المشاريع المتقدمة
 * 
 * هذه الخدمة تحتوي على منطق العمل الأساسي لإدارة المشاريع
 * وتوفر واجهة موحدة للتفاعل مع المشاريع والعمليات المرتبطة بها
 * 
 * @package App\Services\ProjectManagement
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
class ProjectService
{
    /**
     * مستودع المشاريع
     * 
     * @var ProjectRepository
     */
    protected ProjectRepository $projectRepository;

    /**
     * مستودع المهام
     * 
     * @var TaskRepository
     */
    protected TaskRepository $taskRepository;

    /**
     * خدمة الإشعارات
     * 
     * @var NotificationService
     */
    protected NotificationService $notificationService;

    /**
     * منشئ خدمة المشاريع
     * 
     * @param ProjectRepository $projectRepository
     * @param TaskRepository $taskRepository
     * @param NotificationService $notificationService
     */
    public function __construct(
        ProjectRepository $projectRepository,
        TaskRepository $taskRepository,
        NotificationService $notificationService
    ) {
        $this->projectRepository = $projectRepository;
        $this->taskRepository = $taskRepository;
        $this->notificationService = $notificationService;
    }

    /**
     * إنشاء مشروع جديد
     * 
     * @param array $data بيانات المشروع
     * @return Project
     * @throws Exception
     */
    public function createProject(array $data): Project
    {
        try {
            DB::beginTransaction();

            // التحقق من صحة البيانات
            $this->validateProjectData($data);

            // إعداد البيانات الافتراضية
            $data = $this->prepareProjectData($data);

            // إنشاء المشروع
            $project = $this->projectRepository->create($data);

            // إضافة مدير المشروع كعضو في الفريق
            if (isset($data['manager_id'])) {
                $this->addTeamMember($project->id, $data['manager_id'], 'project_manager');
            }

            // إرسال إشعارات
            $this->notificationService->notifyProjectCreated($project);

            // تسجيل النشاط
            $this->logActivity('project_created', $project, 'تم إنشاء مشروع جديد: ' . $project->name);

            DB::commit();

            Log::info('تم إنشاء مشروع جديد', ['project_id' => $project->id, 'name' => $project->name]);

            return $project;

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('خطأ في إنشاء المشروع', ['error' => $e->getMessage(), 'data' => $data]);
            throw $e;
        }
    }

    /**
     * تحديث مشروع موجود
     * 
     * @param int $projectId معرف المشروع
     * @param array $data البيانات الجديدة
     * @return bool
     * @throws Exception
     */
    public function updateProject(int $projectId, array $data): bool
    {
        try {
            DB::beginTransaction();

            $project = $this->projectRepository->findOrFail($projectId);
            $oldData = $project->toArray();

            // التحقق من صحة البيانات
            $this->validateProjectData($data, $projectId);

            // تحديث المشروع
            $updated = $this->projectRepository->update($projectId, $data);

            if ($updated) {
                $updatedProject = $this->projectRepository->findOrFail($projectId);

                // إرسال إشعارات للتغييرات المهمة
                $this->notifyProjectChanges($project, $updatedProject, $oldData);

                // تسجيل النشاط
                $this->logActivity('project_updated', $updatedProject, 'تم تحديث المشروع: ' . $updatedProject->name, $oldData);
            }

            DB::commit();

            Log::info('تم تحديث المشروع', ['project_id' => $projectId]);

            return $updated;

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('خطأ في تحديث المشروع', ['project_id' => $projectId, 'error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * حذف مشروع
     * 
     * @param int $projectId معرف المشروع
     * @return bool
     * @throws Exception
     */
    public function deleteProject(int $projectId): bool
    {
        try {
            DB::beginTransaction();

            $project = $this->projectRepository->findOrFail($projectId);

            // التحقق من إمكانية الحذف
            $this->validateProjectDeletion($project);

            // حذف المشروع (حذف ناعم)
            $deleted = $this->projectRepository->delete($projectId);

            if ($deleted) {
                // إرسال إشعارات
                $this->notificationService->notifyProjectDeleted($project);

                // تسجيل النشاط
                $this->logActivity('project_deleted', $project, 'تم حذف المشروع: ' . $project->name);
            }

            DB::commit();

            Log::info('تم حذف المشروع', ['project_id' => $projectId, 'name' => $project->name]);

            return $deleted;

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('خطأ في حذف المشروع', ['project_id' => $projectId, 'error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * الحصول على مشروع بالمعرف
     * 
     * @param int $projectId معرف المشروع
     * @param array $relations العلاقات المطلوب تحميلها
     * @return Project
     */
    public function getProject(int $projectId, array $relations = []): Project
    {
        return $this->projectRepository->findOrFail($projectId, ['*'], $relations);
    }

    /**
     * البحث في المشاريع
     * 
     * @param array $filters مرشحات البحث
     * @param int $perPage عدد النتائج في الصفحة
     * @return LengthAwarePaginator
     */
    public function searchProjects(array $filters, int $perPage = 15): LengthAwarePaginator
    {
        return $this->projectRepository->searchProjects($filters, $perPage);
    }

    /**
     * الحصول على إحصائيات المشاريع
     * 
     * @return array
     */
    public function getProjectStatistics(): array
    {
        $basicStats = $this->projectRepository->getProjectStatistics();
        $statusStats = $this->projectRepository->getProjectsByStatus();
        $typeStats = $this->projectRepository->getProjectsByType();
        $priorityStats = $this->projectRepository->getProjectsByPriority();
        $budgetStats = $this->projectRepository->getBudgetStatistics();

        return [
            'basic' => $basicStats,
            'by_status' => $statusStats,
            'by_type' => $typeStats,
            'by_priority' => $priorityStats,
            'budget' => $budgetStats,
        ];
    }

    /**
     * إضافة عضو لفريق المشروع
     * 
     * @param int $projectId معرف المشروع
     * @param int $userId معرف المستخدم
     * @param string $role الدور
     * @param float|null $hourlyRate السعر بالساعة
     * @return bool
     */
    public function addTeamMember(int $projectId, int $userId, string $role, ?float $hourlyRate = null): bool
    {
        try {
            $project = $this->projectRepository->findOrFail($projectId);
            
            $added = $this->projectRepository->addTeamMember($projectId, $userId, $role, $hourlyRate);

            if ($added) {
                // إرسال إشعار للعضو الجديد
                $this->notificationService->notifyTeamMemberAdded($project, $userId, $role);

                // تسجيل النشاط
                $this->logActivity('team_member_added', $project, "تم إضافة عضو جديد للفريق بدور: {$role}");
            }

            return $added;

        } catch (Exception $e) {
            Log::error('خطأ في إضافة عضو الفريق', [
                'project_id' => $projectId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * إزالة عضو من فريق المشروع
     * 
     * @param int $projectId معرف المشروع
     * @param int $userId معرف المستخدم
     * @return bool
     */
    public function removeTeamMember(int $projectId, int $userId): bool
    {
        try {
            $project = $this->projectRepository->findOrFail($projectId);
            
            $removed = $this->projectRepository->removeTeamMember($projectId, $userId);

            if ($removed) {
                // إرسال إشعار
                $this->notificationService->notifyTeamMemberRemoved($project, $userId);

                // تسجيل النشاط
                $this->logActivity('team_member_removed', $project, 'تم إزالة عضو من الفريق');
            }

            return $removed;

        } catch (Exception $e) {
            Log::error('خطأ في إزالة عضو الفريق', [
                'project_id' => $projectId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * تحديث حالة المشروع
     * 
     * @param int $projectId معرف المشروع
     * @param string $status الحالة الجديدة
     * @return bool
     */
    public function updateProjectStatus(int $projectId, string $status): bool
    {
        try {
            $project = $this->projectRepository->findOrFail($projectId);
            $oldStatus = $project->status;

            $updated = $this->projectRepository->update($projectId, ['status' => $status]);

            if ($updated && $oldStatus !== $status) {
                // إرسال إشعارات
                $this->notificationService->notifyProjectStatusChanged($project, $oldStatus, $status);

                // تسجيل النشاط
                $this->logActivity('project_status_changed', $project, "تم تغيير حالة المشروع من {$oldStatus} إلى {$status}");

                // إجراءات خاصة حسب الحالة
                $this->handleStatusChange($project, $status);
            }

            return $updated;

        } catch (Exception $e) {
            Log::error('خطأ في تحديث حالة المشروع', [
                'project_id' => $projectId,
                'status' => $status,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * التحقق من صحة بيانات المشروع
     * 
     * @param array $data البيانات
     * @param int|null $projectId معرف المشروع (للتحديث)
     * @throws Exception
     */
    protected function validateProjectData(array $data, ?int $projectId = null): void
    {
        // التحقق من وجود الاسم
        if (empty($data['name'])) {
            throw new Exception('اسم المشروع مطلوب');
        }

        // التحقق من تفرد الاسم
        $existingProject = $this->projectRepository->findBy(['name' => $data['name']]);
        if ($existingProject && (!$projectId || $existingProject->id !== $projectId)) {
            throw new Exception('اسم المشروع موجود مسبقاً');
        }

        // التحقق من صحة التواريخ
        if (isset($data['start_date']) && isset($data['end_date'])) {
            if ($data['start_date'] > $data['end_date']) {
                throw new Exception('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
            }
        }

        // التحقق من صحة الميزانية
        if (isset($data['budget']) && $data['budget'] < 0) {
            throw new Exception('الميزانية يجب أن تكون قيمة موجبة');
        }
    }

    /**
     * إعداد بيانات المشروع قبل الحفظ
     * 
     * @param array $data البيانات
     * @return array
     */
    protected function prepareProjectData(array $data): array
    {
        // إضافة معرف المنشئ
        $data['created_by'] = auth()->id();

        // إنشاء slug إذا لم يكن موجوداً
        if (empty($data['slug'])) {
            $data['slug'] = \Str::slug($data['name']);
        }

        // تعيين قيم افتراضية
        $data['progress_percentage'] = $data['progress_percentage'] ?? 0;
        $data['spent_budget'] = $data['spent_budget'] ?? 0;

        return $data;
    }

    /**
     * التحقق من إمكانية حذف المشروع
     * 
     * @param Project $project
     * @throws Exception
     */
    protected function validateProjectDeletion(Project $project): void
    {
        // منع حذف المشاريع المكتملة
        if ($project->status === 'completed') {
            throw new Exception('لا يمكن حذف المشاريع المكتملة');
        }

        // التحقق من وجود مهام نشطة
        $activeTasks = $this->taskRepository->count([
            'project_id' => $project->id,
            'status' => ['todo', 'in_progress', 'review']
        ]);

        if ($activeTasks > 0) {
            throw new Exception('لا يمكن حذف المشروع لوجود مهام نشطة');
        }
    }

    /**
     * إرسال إشعارات للتغييرات المهمة في المشروع
     * 
     * @param Project $oldProject
     * @param Project $newProject
     * @param array $oldData
     */
    protected function notifyProjectChanges(Project $oldProject, Project $newProject, array $oldData): void
    {
        // إشعار تغيير المدير
        if ($oldProject->manager_id !== $newProject->manager_id) {
            $this->notificationService->notifyProjectManagerChanged($newProject, $oldProject->manager_id, $newProject->manager_id);
        }

        // إشعار تغيير تاريخ النهاية
        if ($oldProject->end_date !== $newProject->end_date) {
            $this->notificationService->notifyProjectDeadlineChanged($newProject, $oldProject->end_date, $newProject->end_date);
        }
    }

    /**
     * التعامل مع تغيير حالة المشروع
     * 
     * @param Project $project
     * @param string $status
     */
    protected function handleStatusChange(Project $project, string $status): void
    {
        switch ($status) {
            case 'completed':
                // تحديث تاريخ الإنجاز الفعلي
                $this->projectRepository->update($project->id, ['actual_end_date' => now()]);
                break;

            case 'cancelled':
                // إجراءات إلغاء المشروع
                break;
        }
    }

    /**
     * تسجيل نشاط في سجل الأنشطة
     * 
     * @param string $action نوع النشاط
     * @param Project $project المشروع
     * @param string $description الوصف
     * @param array|null $oldData البيانات القديمة
     */
    protected function logActivity(string $action, Project $project, string $description, ?array $oldData = null): void
    {
        // سيتم تنفيذ هذا عند إنشاء خدمة سجل الأنشطة
        Log::info($description, [
            'action' => $action,
            'project_id' => $project->id,
            'user_id' => auth()->id(),
            'old_data' => $oldData,
        ]);
    }
}
