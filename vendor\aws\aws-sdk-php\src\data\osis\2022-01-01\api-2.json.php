<?php
// This file was auto-generated from sdk-root/src/data/osis/2022-01-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2022-01-01', 'endpointPrefix' => 'osis', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Amazon OpenSearch Ingestion', 'serviceId' => 'OSIS', 'signatureVersion' => 'v4', 'uid' => 'osis-2022-01-01', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'CreatePipeline' => [ 'name' => 'CreatePipeline', 'http' => [ 'method' => 'POST', 'requestUri' => '/2022-01-01/osis/createPipeline', ], 'input' => [ 'shape' => 'CreatePipelineRequest', ], 'output' => [ 'shape' => 'CreatePipelineResponse', ], 'errors' => [ [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeletePipeline' => [ 'name' => 'DeletePipeline', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2022-01-01/osis/deletePipeline/{PipelineName}', ], 'input' => [ 'shape' => 'DeletePipelineRequest', ], 'output' => [ 'shape' => 'DeletePipelineResponse', ], 'errors' => [ [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetPipeline' => [ 'name' => 'GetPipeline', 'http' => [ 'method' => 'GET', 'requestUri' => '/2022-01-01/osis/getPipeline/{PipelineName}', ], 'input' => [ 'shape' => 'GetPipelineRequest', ], 'output' => [ 'shape' => 'GetPipelineResponse', ], 'errors' => [ [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetPipelineBlueprint' => [ 'name' => 'GetPipelineBlueprint', 'http' => [ 'method' => 'GET', 'requestUri' => '/2022-01-01/osis/getPipelineBlueprint/{BlueprintName}', ], 'input' => [ 'shape' => 'GetPipelineBlueprintRequest', ], 'output' => [ 'shape' => 'GetPipelineBlueprintResponse', ], 'errors' => [ [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetPipelineChangeProgress' => [ 'name' => 'GetPipelineChangeProgress', 'http' => [ 'method' => 'GET', 'requestUri' => '/2022-01-01/osis/getPipelineChangeProgress/{PipelineName}', ], 'input' => [ 'shape' => 'GetPipelineChangeProgressRequest', ], 'output' => [ 'shape' => 'GetPipelineChangeProgressResponse', ], 'errors' => [ [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListPipelineBlueprints' => [ 'name' => 'ListPipelineBlueprints', 'http' => [ 'method' => 'POST', 'requestUri' => '/2022-01-01/osis/listPipelineBlueprints', ], 'input' => [ 'shape' => 'ListPipelineBlueprintsRequest', ], 'output' => [ 'shape' => 'ListPipelineBlueprintsResponse', ], 'errors' => [ [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidPaginationTokenException', ], ], ], 'ListPipelines' => [ 'name' => 'ListPipelines', 'http' => [ 'method' => 'GET', 'requestUri' => '/2022-01-01/osis/listPipelines', ], 'input' => [ 'shape' => 'ListPipelinesRequest', ], 'output' => [ 'shape' => 'ListPipelinesResponse', ], 'errors' => [ [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidPaginationTokenException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/2022-01-01/osis/listTagsForResource/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StartPipeline' => [ 'name' => 'StartPipeline', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2022-01-01/osis/startPipeline/{PipelineName}', ], 'input' => [ 'shape' => 'StartPipelineRequest', ], 'output' => [ 'shape' => 'StartPipelineResponse', ], 'errors' => [ [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'StopPipeline' => [ 'name' => 'StopPipeline', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2022-01-01/osis/stopPipeline/{PipelineName}', ], 'input' => [ 'shape' => 'StopPipelineRequest', ], 'output' => [ 'shape' => 'StopPipelineResponse', ], 'errors' => [ [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/2022-01-01/osis/tagResource/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/2022-01-01/osis/untagResource/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdatePipeline' => [ 'name' => 'UpdatePipeline', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2022-01-01/osis/updatePipeline/{PipelineName}', ], 'input' => [ 'shape' => 'UpdatePipelineRequest', ], 'output' => [ 'shape' => 'UpdatePipelineResponse', ], 'errors' => [ [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'ValidatePipeline' => [ 'name' => 'ValidatePipeline', 'http' => [ 'method' => 'POST', 'requestUri' => '/2022-01-01/osis/validatePipeline', ], 'input' => [ 'shape' => 'ValidatePipelineRequest', ], 'output' => [ 'shape' => 'ValidatePipelineResponse', ], 'errors' => [ [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'BlueprintFormat' => [ 'type' => 'string', 'pattern' => '(YAML|JSON)', ], 'Boolean' => [ 'type' => 'boolean', ], 'BufferOptions' => [ 'type' => 'structure', 'required' => [ 'PersistentBufferEnabled', ], 'members' => [ 'PersistentBufferEnabled' => [ 'shape' => 'Boolean', ], ], ], 'ChangeProgressStage' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'ChangeProgressStageStatuses', ], 'Description' => [ 'shape' => 'String', ], 'LastUpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'ChangeProgressStageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChangeProgressStage', ], ], 'ChangeProgressStageStatuses' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', ], ], 'ChangeProgressStatus' => [ 'type' => 'structure', 'members' => [ 'StartTime' => [ 'shape' => 'Timestamp', ], 'Status' => [ 'shape' => 'ChangeProgressStatuses', ], 'TotalNumberOfStages' => [ 'shape' => 'Integer', ], 'ChangeProgressStages' => [ 'shape' => 'ChangeProgressStageList', ], ], ], 'ChangeProgressStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChangeProgressStatus', ], ], 'ChangeProgressStatuses' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', ], ], 'CidrBlock' => [ 'type' => 'string', 'pattern' => '^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)/(3[0-2]|[12]?[0-9])$', ], 'CloudWatchLogDestination' => [ 'type' => 'structure', 'required' => [ 'LogGroup', ], 'members' => [ 'LogGroup' => [ 'shape' => 'LogGroup', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'CreatePipelineRequest' => [ 'type' => 'structure', 'required' => [ 'PipelineName', 'MinUnits', 'MaxUnits', 'PipelineConfigurationBody', ], 'members' => [ 'PipelineName' => [ 'shape' => 'PipelineName', ], 'MinUnits' => [ 'shape' => 'PipelineUnits', ], 'MaxUnits' => [ 'shape' => 'PipelineUnits', ], 'PipelineConfigurationBody' => [ 'shape' => 'PipelineConfigurationBody', ], 'LogPublishingOptions' => [ 'shape' => 'LogPublishingOptions', ], 'VpcOptions' => [ 'shape' => 'VpcOptions', ], 'BufferOptions' => [ 'shape' => 'BufferOptions', ], 'EncryptionAtRestOptions' => [ 'shape' => 'EncryptionAtRestOptions', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreatePipelineResponse' => [ 'type' => 'structure', 'members' => [ 'Pipeline' => [ 'shape' => 'Pipeline', ], ], ], 'DeletePipelineRequest' => [ 'type' => 'structure', 'required' => [ 'PipelineName', ], 'members' => [ 'PipelineName' => [ 'shape' => 'PipelineName', 'location' => 'uri', 'locationName' => 'PipelineName', ], ], ], 'DeletePipelineResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisabledOperationException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'EncryptionAtRestOptions' => [ 'type' => 'structure', 'required' => [ 'KmsKeyArn', ], 'members' => [ 'KmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'GetPipelineBlueprintRequest' => [ 'type' => 'structure', 'required' => [ 'BlueprintName', ], 'members' => [ 'BlueprintName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'BlueprintName', ], 'Format' => [ 'shape' => 'BlueprintFormat', 'location' => 'querystring', 'locationName' => 'format', ], ], ], 'GetPipelineBlueprintResponse' => [ 'type' => 'structure', 'members' => [ 'Blueprint' => [ 'shape' => 'PipelineBlueprint', ], 'Format' => [ 'shape' => 'String', ], ], ], 'GetPipelineChangeProgressRequest' => [ 'type' => 'structure', 'required' => [ 'PipelineName', ], 'members' => [ 'PipelineName' => [ 'shape' => 'PipelineName', 'location' => 'uri', 'locationName' => 'PipelineName', ], ], ], 'GetPipelineChangeProgressResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeProgressStatuses' => [ 'shape' => 'ChangeProgressStatusList', ], ], ], 'GetPipelineRequest' => [ 'type' => 'structure', 'required' => [ 'PipelineName', ], 'members' => [ 'PipelineName' => [ 'shape' => 'PipelineName', 'location' => 'uri', 'locationName' => 'PipelineName', ], ], ], 'GetPipelineResponse' => [ 'type' => 'structure', 'members' => [ 'Pipeline' => [ 'shape' => 'Pipeline', ], ], ], 'IngestEndpointUrlsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Integer' => [ 'type' => 'integer', ], 'InternalException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'InvalidPaginationTokenException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'KmsKeyArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 7, ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ListPipelineBlueprintsRequest' => [ 'type' => 'structure', 'members' => [], ], 'ListPipelineBlueprintsResponse' => [ 'type' => 'structure', 'members' => [ 'Blueprints' => [ 'shape' => 'PipelineBlueprintsSummaryList', ], ], ], 'ListPipelinesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListPipelinesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'Pipelines' => [ 'shape' => 'PipelineSummaryList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'PipelineArn', 'location' => 'querystring', 'locationName' => 'arn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'LogGroup' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '\\/aws\\/vendedlogs\\/[\\.\\-_/#A-Za-z0-9]+', ], 'LogPublishingOptions' => [ 'type' => 'structure', 'members' => [ 'IsLoggingEnabled' => [ 'shape' => 'Boolean', ], 'CloudWatchLogDestination' => [ 'shape' => 'CloudWatchLogDestination', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'NextToken' => [ 'type' => 'string', 'max' => 3000, 'min' => 0, 'pattern' => '^([\\s\\S]*)$', ], 'Pipeline' => [ 'type' => 'structure', 'members' => [ 'PipelineName' => [ 'shape' => 'String', ], 'PipelineArn' => [ 'shape' => 'String', ], 'MinUnits' => [ 'shape' => 'Integer', ], 'MaxUnits' => [ 'shape' => 'Integer', ], 'Status' => [ 'shape' => 'PipelineStatus', ], 'StatusReason' => [ 'shape' => 'PipelineStatusReason', ], 'PipelineConfigurationBody' => [ 'shape' => 'String', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'LastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'IngestEndpointUrls' => [ 'shape' => 'IngestEndpointUrlsList', ], 'LogPublishingOptions' => [ 'shape' => 'LogPublishingOptions', ], 'VpcEndpoints' => [ 'shape' => 'VpcEndpointsList', ], 'BufferOptions' => [ 'shape' => 'BufferOptions', ], 'EncryptionAtRestOptions' => [ 'shape' => 'EncryptionAtRestOptions', ], 'VpcEndpointService' => [ 'shape' => 'String', ], 'ServiceVpcEndpoints' => [ 'shape' => 'ServiceVpcEndpointsList', ], 'Destinations' => [ 'shape' => 'PipelineDestinationList', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'PipelineArn' => [ 'type' => 'string', 'max' => 76, 'min' => 46, 'pattern' => '^arn:(aws|aws\\-cn|aws\\-us\\-gov|aws\\-iso|aws\\-iso\\-b):osis:.+:pipeline\\/.+$', ], 'PipelineBlueprint' => [ 'type' => 'structure', 'members' => [ 'BlueprintName' => [ 'shape' => 'String', ], 'PipelineConfigurationBody' => [ 'shape' => 'String', ], 'DisplayName' => [ 'shape' => 'String', ], 'DisplayDescription' => [ 'shape' => 'String', ], 'Service' => [ 'shape' => 'String', ], 'UseCase' => [ 'shape' => 'String', ], ], ], 'PipelineBlueprintSummary' => [ 'type' => 'structure', 'members' => [ 'BlueprintName' => [ 'shape' => 'String', ], 'DisplayName' => [ 'shape' => 'String', ], 'DisplayDescription' => [ 'shape' => 'String', ], 'Service' => [ 'shape' => 'String', ], 'UseCase' => [ 'shape' => 'String', ], ], ], 'PipelineBlueprintsSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PipelineBlueprintSummary', ], ], 'PipelineConfigurationBody' => [ 'type' => 'string', 'max' => 24000, 'min' => 1, ], 'PipelineDestination' => [ 'type' => 'structure', 'members' => [ 'ServiceName' => [ 'shape' => 'String', ], 'Endpoint' => [ 'shape' => 'String', ], ], ], 'PipelineDestinationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PipelineDestination', ], ], 'PipelineName' => [ 'type' => 'string', 'max' => 28, 'min' => 3, 'pattern' => '[a-z][a-z0-9\\-]+', ], 'PipelineStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'UPDATING', 'DELETING', 'CREATE_FAILED', 'UPDATE_FAILED', 'STARTING', 'START_FAILED', 'STOPPING', 'STOPPED', ], ], 'PipelineStatusReason' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'String', ], ], ], 'PipelineSummary' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'PipelineStatus', ], 'StatusReason' => [ 'shape' => 'PipelineStatusReason', ], 'PipelineName' => [ 'shape' => 'PipelineName', ], 'PipelineArn' => [ 'shape' => 'PipelineArn', ], 'MinUnits' => [ 'shape' => 'PipelineUnits', ], 'MaxUnits' => [ 'shape' => 'PipelineUnits', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'LastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'Destinations' => [ 'shape' => 'PipelineDestinationList', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'PipelineSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PipelineSummary', ], ], 'PipelineUnits' => [ 'type' => 'integer', 'min' => 1, ], 'ResourceAlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'SecurityGroupId' => [ 'type' => 'string', 'max' => 20, 'min' => 11, 'pattern' => 'sg-\\w{8}(\\w{9})?', ], 'SecurityGroupIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 12, 'min' => 1, ], 'ServiceVpcEndpoint' => [ 'type' => 'structure', 'members' => [ 'ServiceName' => [ 'shape' => 'VpcEndpointServiceName', ], 'VpcEndpointId' => [ 'shape' => 'String', ], ], ], 'ServiceVpcEndpointsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceVpcEndpoint', ], ], 'StartPipelineRequest' => [ 'type' => 'structure', 'required' => [ 'PipelineName', ], 'members' => [ 'PipelineName' => [ 'shape' => 'PipelineName', 'location' => 'uri', 'locationName' => 'PipelineName', ], ], ], 'StartPipelineResponse' => [ 'type' => 'structure', 'members' => [ 'Pipeline' => [ 'shape' => 'Pipeline', ], ], ], 'StopPipelineRequest' => [ 'type' => 'structure', 'required' => [ 'PipelineName', ], 'members' => [ 'PipelineName' => [ 'shape' => 'PipelineName', 'location' => 'uri', 'locationName' => 'PipelineName', ], ], ], 'StopPipelineResponse' => [ 'type' => 'structure', 'members' => [ 'Pipeline' => [ 'shape' => 'Pipeline', ], ], ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SubnetId' => [ 'type' => 'string', 'max' => 24, 'min' => 15, 'pattern' => 'subnet-\\w{8}(\\w{9})?', ], 'SubnetIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], 'max' => 12, 'min' => 1, ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '.*', ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', 'Tags', ], 'members' => [ 'Arn' => [ 'shape' => 'PipelineArn', 'location' => 'querystring', 'locationName' => 'arn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '.*', ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', 'TagKeys', ], 'members' => [ 'Arn' => [ 'shape' => 'PipelineArn', 'location' => 'querystring', 'locationName' => 'arn', ], 'TagKeys' => [ 'shape' => 'StringList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdatePipelineRequest' => [ 'type' => 'structure', 'required' => [ 'PipelineName', ], 'members' => [ 'PipelineName' => [ 'shape' => 'PipelineName', 'location' => 'uri', 'locationName' => 'PipelineName', ], 'MinUnits' => [ 'shape' => 'PipelineUnits', ], 'MaxUnits' => [ 'shape' => 'PipelineUnits', ], 'PipelineConfigurationBody' => [ 'shape' => 'PipelineConfigurationBody', ], 'LogPublishingOptions' => [ 'shape' => 'LogPublishingOptions', ], 'BufferOptions' => [ 'shape' => 'BufferOptions', ], 'EncryptionAtRestOptions' => [ 'shape' => 'EncryptionAtRestOptions', ], ], ], 'UpdatePipelineResponse' => [ 'type' => 'structure', 'members' => [ 'Pipeline' => [ 'shape' => 'Pipeline', ], ], ], 'ValidatePipelineRequest' => [ 'type' => 'structure', 'required' => [ 'PipelineConfigurationBody', ], 'members' => [ 'PipelineConfigurationBody' => [ 'shape' => 'PipelineConfigurationBody', ], ], ], 'ValidatePipelineResponse' => [ 'type' => 'structure', 'members' => [ 'isValid' => [ 'shape' => 'Boolean', ], 'Errors' => [ 'shape' => 'ValidationMessageList', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ValidationMessage' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], ], 'ValidationMessageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationMessage', ], ], 'VpcAttachmentOptions' => [ 'type' => 'structure', 'required' => [ 'AttachToVpc', ], 'members' => [ 'AttachToVpc' => [ 'shape' => 'Boolean', ], 'CidrBlock' => [ 'shape' => 'CidrBlock', ], ], ], 'VpcEndpoint' => [ 'type' => 'structure', 'members' => [ 'VpcEndpointId' => [ 'shape' => 'String', ], 'VpcId' => [ 'shape' => 'String', ], 'VpcOptions' => [ 'shape' => 'VpcOptions', ], ], ], 'VpcEndpointManagement' => [ 'type' => 'string', 'enum' => [ 'CUSTOMER', 'SERVICE', ], ], 'VpcEndpointServiceName' => [ 'type' => 'string', 'enum' => [ 'OPENSEARCH_SERVERLESS', ], ], 'VpcEndpointsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcEndpoint', ], ], 'VpcOptions' => [ 'type' => 'structure', 'required' => [ 'SubnetIds', ], 'members' => [ 'SubnetIds' => [ 'shape' => 'SubnetIds', ], 'SecurityGroupIds' => [ 'shape' => 'SecurityGroupIds', ], 'VpcAttachmentOptions' => [ 'shape' => 'VpcAttachmentOptions', ], 'VpcEndpointManagement' => [ 'shape' => 'VpcEndpointManagement', ], ], ], ],];
