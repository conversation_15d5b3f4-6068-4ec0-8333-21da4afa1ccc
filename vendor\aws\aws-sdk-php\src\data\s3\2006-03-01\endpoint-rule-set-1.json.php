<?php
// This file was auto-generated from sdk-root/src/data/s3/2006-03-01/endpoint-rule-set-1.json
return [ 'version' => '1.0', 'parameters' => [ 'Bucket' => [ 'required' => false, 'documentation' => 'The S3 bucket used to send the request. This is an optional parameter that will be set automatically for operations that are scoped to an S3 bucket.', 'type' => 'String', ], 'Region' => [ 'builtIn' => 'AWS::Region', 'required' => false, 'documentation' => 'The AWS region used to dispatch the request.', 'type' => 'String', ], 'UseFIPS' => [ 'builtIn' => 'AWS::UseFIPS', 'required' => true, 'default' => false, 'documentation' => 'When true, send this request to the FIPS-compliant regional endpoint. If the configured endpoint does not have a FIPS compliant endpoint, dispatching the request will return an error.', 'type' => 'Boolean', ], 'UseDualStack' => [ 'builtIn' => 'AWS::UseDualStack', 'required' => true, 'default' => false, 'documentation' => 'When true, use the dual-stack endpoint. If the configured endpoint does not support dual-stack, dispatching the request MAY return an error.', 'type' => 'Boolean', ], 'Endpoint' => [ 'builtIn' => 'SDK::Endpoint', 'required' => false, 'documentation' => 'Override the endpoint used to send this request', 'type' => 'String', ], 'ForcePathStyle' => [ 'builtIn' => 'AWS::S3::ForcePathStyle', 'required' => true, 'default' => false, 'documentation' => 'When true, force a path-style endpoint to be used where the bucket name is part of the path.', 'type' => 'Boolean', ], 'Accelerate' => [ 'builtIn' => 'AWS::S3::Accelerate', 'required' => true, 'default' => false, 'documentation' => 'When true, use S3 Accelerate. NOTE: Not all regions support S3 accelerate.', 'type' => 'Boolean', ], 'UseGlobalEndpoint' => [ 'builtIn' => 'AWS::S3::UseGlobalEndpoint', 'required' => true, 'default' => false, 'documentation' => 'Whether the global endpoint should be used, rather then the regional endpoint for us-east-1.', 'type' => 'Boolean', ], 'UseObjectLambdaEndpoint' => [ 'required' => false, 'documentation' => 'Internal parameter to use object lambda endpoint for an operation (eg: WriteGetObjectResponse)', 'type' => 'Boolean', ], 'Key' => [ 'required' => false, 'documentation' => 'The S3 Key used to send the request. This is an optional parameter that will be set automatically for operations that are scoped to an S3 Key.', 'type' => 'String', ], 'Prefix' => [ 'required' => false, 'documentation' => 'The S3 Prefix used to send the request. This is an optional parameter that will be set automatically for operations that are scoped to an S3 Prefix.', 'type' => 'String', ], 'CopySource' => [ 'required' => false, 'documentation' => 'The Copy Source used for Copy Object request. This is an optional parameter that will be set automatically for operations that are scoped to Copy Source.', 'type' => 'String', ], 'DisableAccessPoints' => [ 'required' => false, 'documentation' => 'Internal parameter to disable Access Point Buckets', 'type' => 'Boolean', ], 'DisableMultiRegionAccessPoints' => [ 'builtIn' => 'AWS::S3::DisableMultiRegionAccessPoints', 'required' => true, 'default' => false, 'documentation' => 'Whether multi-region access points (MRAP) should be disabled.', 'type' => 'Boolean', ], 'UseArnRegion' => [ 'builtIn' => 'AWS::S3::UseArnRegion', 'required' => false, 'documentation' => 'When an Access Point ARN is provided and this flag is enabled, the SDK MUST use the ARN\'s region when constructing the endpoint instead of the client\'s configured region.', 'type' => 'Boolean', ], 'UseS3ExpressControlEndpoint' => [ 'required' => false, 'documentation' => 'Internal parameter to indicate whether S3Express operation should use control plane, (ex. CreateBucket)', 'type' => 'Boolean', ], 'DisableS3ExpressSessionAuth' => [ 'required' => false, 'documentation' => 'Parameter to indicate whether S3Express session auth should be disabled', 'type' => 'Boolean', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Region', ], ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'error' => 'Accelerate cannot be used with FIPS', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], 'error' => 'Cannot set dual-stack in combination with a custom endpoint.', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'error' => 'A custom endpoint cannot be combined with FIPS', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], true, ], ], ], 'error' => 'A custom endpoint cannot be combined with S3 Accelerate', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], [ 'fn' => 'aws.partition', 'argv' => [ [ 'ref' => 'Region', ], ], 'assign' => 'partitionResult', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'partitionResult', ], 'name', ], ], 'aws-cn', ], ], ], 'error' => 'Partition does not support FIPS', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Bucket', ], ], ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 0, 6, true, ], 'assign' => 'bucketSuffix', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'bucketSuffix', ], '--x-s3', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], ], 'error' => 'S3Express does not support Dual-stack.', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], true, ], ], ], 'error' => 'S3Express does not support S3 Accelerate.', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'parseURL', 'argv' => [ [ 'ref' => 'Endpoint', ], ], 'assign' => 'url', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'DisableS3ExpressSessionAuth', ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'DisableS3ExpressSessionAuth', ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'url', ], 'isIp', ], ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'uriEncode', 'argv' => [ [ 'ref' => 'Bucket', ], ], 'assign' => 'uri_encoded_bucket', ], ], 'rules' => [ [ 'conditions' => [], 'endpoint' => [ 'url' => '{url#scheme}://{url#authority}/{uri_encoded_bucket}{url#path}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'aws.isVirtualHostableS3Bucket', 'argv' => [ [ 'ref' => 'Bucket', ], false, ], ], ], 'rules' => [ [ 'conditions' => [], 'endpoint' => [ 'url' => '{url#scheme}://{Bucket}.{url#authority}{url#path}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'S3Express bucket name is not a valid virtual hostable name.', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'url', ], 'isIp', ], ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'uriEncode', 'argv' => [ [ 'ref' => 'Bucket', ], ], 'assign' => 'uri_encoded_bucket', ], ], 'rules' => [ [ 'conditions' => [], 'endpoint' => [ 'url' => '{url#scheme}://{url#authority}/{uri_encoded_bucket}{url#path}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'aws.isVirtualHostableS3Bucket', 'argv' => [ [ 'ref' => 'Bucket', ], false, ], ], ], 'rules' => [ [ 'conditions' => [], 'endpoint' => [ 'url' => '{url#scheme}://{Bucket}.{url#authority}{url#path}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'S3Express bucket name is not a valid virtual hostable name.', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'UseS3ExpressControlEndpoint', ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseS3ExpressControlEndpoint', ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'aws.partition', 'argv' => [ [ 'ref' => 'Region', ], ], 'assign' => 'partitionResult', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'uriEncode', 'argv' => [ [ 'ref' => 'Bucket', ], ], 'assign' => 'uri_encoded_bucket', ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://s3express-control-fips.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://s3express-control.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'aws.isVirtualHostableS3Bucket', 'argv' => [ [ 'ref' => 'Bucket', ], false, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'aws.partition', 'argv' => [ [ 'ref' => 'Region', ], ], 'assign' => 'partitionResult', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'DisableS3ExpressSessionAuth', ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'DisableS3ExpressSessionAuth', ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 6, 14, true, ], 'assign' => 's3expressAvailabilityZoneId', ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 14, 16, true, ], 'assign' => 's3expressAvailabilityZoneDelim', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 's3expressAvailabilityZoneDelim', ], '--', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 6, 15, true, ], 'assign' => 's3expressAvailabilityZoneId', ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 15, 17, true, ], 'assign' => 's3expressAvailabilityZoneDelim', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 's3expressAvailabilityZoneDelim', ], '--', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 6, 19, true, ], 'assign' => 's3expressAvailabilityZoneId', ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 19, 21, true, ], 'assign' => 's3expressAvailabilityZoneDelim', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 's3expressAvailabilityZoneDelim', ], '--', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 6, 20, true, ], 'assign' => 's3expressAvailabilityZoneId', ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 20, 22, true, ], 'assign' => 's3expressAvailabilityZoneDelim', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 's3expressAvailabilityZoneDelim', ], '--', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 6, 26, true, ], 'assign' => 's3expressAvailabilityZoneId', ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 26, 28, true, ], 'assign' => 's3expressAvailabilityZoneDelim', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 's3expressAvailabilityZoneDelim', ], '--', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Unrecognized S3Express bucket name format.', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 6, 14, true, ], 'assign' => 's3expressAvailabilityZoneId', ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 14, 16, true, ], 'assign' => 's3expressAvailabilityZoneDelim', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 's3expressAvailabilityZoneDelim', ], '--', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 6, 15, true, ], 'assign' => 's3expressAvailabilityZoneId', ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 15, 17, true, ], 'assign' => 's3expressAvailabilityZoneDelim', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 's3expressAvailabilityZoneDelim', ], '--', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 6, 19, true, ], 'assign' => 's3expressAvailabilityZoneId', ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 19, 21, true, ], 'assign' => 's3expressAvailabilityZoneDelim', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 's3expressAvailabilityZoneDelim', ], '--', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 6, 20, true, ], 'assign' => 's3expressAvailabilityZoneId', ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 20, 22, true, ], 'assign' => 's3expressAvailabilityZoneDelim', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 's3expressAvailabilityZoneDelim', ], '--', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 6, 26, true, ], 'assign' => 's3expressAvailabilityZoneId', ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 26, 28, true, ], 'assign' => 's3expressAvailabilityZoneDelim', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 's3expressAvailabilityZoneDelim', ], '--', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Unrecognized S3Express bucket name format.', 'type' => 'error', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'S3Express bucket name is not a valid virtual hostable name.', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Bucket', ], ], ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 0, 7, true, ], 'assign' => 'accessPointSuffix', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'accessPointSuffix', ], '--xa-s3', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], ], 'error' => 'S3Express does not support Dual-stack.', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], true, ], ], ], 'error' => 'S3Express does not support S3 Accelerate.', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'parseURL', 'argv' => [ [ 'ref' => 'Endpoint', ], ], 'assign' => 'url', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'DisableS3ExpressSessionAuth', ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'DisableS3ExpressSessionAuth', ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'url', ], 'isIp', ], ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'uriEncode', 'argv' => [ [ 'ref' => 'Bucket', ], ], 'assign' => 'uri_encoded_bucket', ], ], 'rules' => [ [ 'conditions' => [], 'endpoint' => [ 'url' => '{url#scheme}://{url#authority}/{uri_encoded_bucket}{url#path}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'aws.isVirtualHostableS3Bucket', 'argv' => [ [ 'ref' => 'Bucket', ], false, ], ], ], 'rules' => [ [ 'conditions' => [], 'endpoint' => [ 'url' => '{url#scheme}://{Bucket}.{url#authority}{url#path}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'S3Express bucket name is not a valid virtual hostable name.', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'url', ], 'isIp', ], ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'uriEncode', 'argv' => [ [ 'ref' => 'Bucket', ], ], 'assign' => 'uri_encoded_bucket', ], ], 'rules' => [ [ 'conditions' => [], 'endpoint' => [ 'url' => '{url#scheme}://{url#authority}/{uri_encoded_bucket}{url#path}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'aws.isVirtualHostableS3Bucket', 'argv' => [ [ 'ref' => 'Bucket', ], false, ], ], ], 'rules' => [ [ 'conditions' => [], 'endpoint' => [ 'url' => '{url#scheme}://{Bucket}.{url#authority}{url#path}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'S3Express bucket name is not a valid virtual hostable name.', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'aws.isVirtualHostableS3Bucket', 'argv' => [ [ 'ref' => 'Bucket', ], false, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'aws.partition', 'argv' => [ [ 'ref' => 'Region', ], ], 'assign' => 'partitionResult', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'DisableS3ExpressSessionAuth', ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'DisableS3ExpressSessionAuth', ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 7, 15, true, ], 'assign' => 's3expressAvailabilityZoneId', ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 15, 17, true, ], 'assign' => 's3expressAvailabilityZoneDelim', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 's3expressAvailabilityZoneDelim', ], '--', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 7, 16, true, ], 'assign' => 's3expressAvailabilityZoneId', ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 16, 18, true, ], 'assign' => 's3expressAvailabilityZoneDelim', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 's3expressAvailabilityZoneDelim', ], '--', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 7, 20, true, ], 'assign' => 's3expressAvailabilityZoneId', ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 20, 22, true, ], 'assign' => 's3expressAvailabilityZoneDelim', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 's3expressAvailabilityZoneDelim', ], '--', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 7, 21, true, ], 'assign' => 's3expressAvailabilityZoneId', ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 21, 23, true, ], 'assign' => 's3expressAvailabilityZoneDelim', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 's3expressAvailabilityZoneDelim', ], '--', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 7, 27, true, ], 'assign' => 's3expressAvailabilityZoneId', ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 27, 29, true, ], 'assign' => 's3expressAvailabilityZoneDelim', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 's3expressAvailabilityZoneDelim', ], '--', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Unrecognized S3Express bucket name format.', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 7, 15, true, ], 'assign' => 's3expressAvailabilityZoneId', ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 15, 17, true, ], 'assign' => 's3expressAvailabilityZoneDelim', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 's3expressAvailabilityZoneDelim', ], '--', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 7, 16, true, ], 'assign' => 's3expressAvailabilityZoneId', ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 16, 18, true, ], 'assign' => 's3expressAvailabilityZoneDelim', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 's3expressAvailabilityZoneDelim', ], '--', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 7, 20, true, ], 'assign' => 's3expressAvailabilityZoneId', ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 20, 22, true, ], 'assign' => 's3expressAvailabilityZoneDelim', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 's3expressAvailabilityZoneDelim', ], '--', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 7, 21, true, ], 'assign' => 's3expressAvailabilityZoneId', ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 21, 23, true, ], 'assign' => 's3expressAvailabilityZoneDelim', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 's3expressAvailabilityZoneDelim', ], '--', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 7, 27, true, ], 'assign' => 's3expressAvailabilityZoneId', ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 27, 29, true, ], 'assign' => 's3expressAvailabilityZoneDelim', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 's3expressAvailabilityZoneDelim', ], '--', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4-s3express', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Unrecognized S3Express bucket name format.', 'type' => 'error', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'S3Express bucket name is not a valid virtual hostable name.', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Bucket', ], ], ], ], ], [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'UseS3ExpressControlEndpoint', ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseS3ExpressControlEndpoint', ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'aws.partition', 'argv' => [ [ 'ref' => 'Region', ], ], 'assign' => 'partitionResult', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'parseURL', 'argv' => [ [ 'ref' => 'Endpoint', ], ], 'assign' => 'url', ], ], 'endpoint' => [ 'url' => '{url#scheme}://{url#authority}{url#path}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://s3express-control-fips.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://s3express-control.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'backend' => 'S3Express', 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3express', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Bucket', ], ], ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 49, 50, true, ], 'assign' => 'hardwareType', ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 8, 12, true, ], 'assign' => 'regionPrefix', ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 0, 7, true, ], 'assign' => 'bucketAliasSuffix', ], [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 32, 49, true, ], 'assign' => 'outpostId', ], [ 'fn' => 'aws.partition', 'argv' => [ [ 'ref' => 'Region', ], ], 'assign' => 'regionPartition', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'bucketAliasSuffix', ], '--op-s3', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isValidHostLabel', 'argv' => [ [ 'ref' => 'outpostId', ], false, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'hardwareType', ], 'e', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'regionPrefix', ], 'beta', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], ], 'error' => 'Expected a endpoint to be specified but no endpoint was found', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'parseURL', 'argv' => [ [ 'ref' => 'Endpoint', ], ], 'assign' => 'url', ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.ec2.{url#authority}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4a', 'signingName' => 's3-outposts', 'signingRegionSet' => [ '*', ], ], [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3-outposts', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.ec2.s3-outposts.{Region}.{regionPartition#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4a', 'signingName' => 's3-outposts', 'signingRegionSet' => [ '*', ], ], [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3-outposts', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'hardwareType', ], 'o', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'regionPrefix', ], 'beta', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], ], 'error' => 'Expected a endpoint to be specified but no endpoint was found', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'parseURL', 'argv' => [ [ 'ref' => 'Endpoint', ], ], 'assign' => 'url', ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.op-{outpostId}.{url#authority}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4a', 'signingName' => 's3-outposts', 'signingRegionSet' => [ '*', ], ], [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3-outposts', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.op-{outpostId}.s3-outposts.{Region}.{regionPartition#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4a', 'signingName' => 's3-outposts', 'signingRegionSet' => [ '*', ], ], [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3-outposts', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Unrecognized hardware type: "Expected hardware type o or e but got {hardwareType}"', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid ARN: The outpost Id must only contain a-z, A-Z, 0-9 and `-`.', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Bucket', ], ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'fn' => 'parseURL', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], ], ], ], 'error' => 'Custom endpoint `{Endpoint}` was not a valid URI', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'ForcePathStyle', ], false, ], ], [ 'fn' => 'aws.isVirtualHostableS3Bucket', 'argv' => [ [ 'ref' => 'Bucket', ], false, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'aws.partition', 'argv' => [ [ 'ref' => 'Region', ], ], 'assign' => 'partitionResult', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isValidHostLabel', 'argv' => [ [ 'ref' => 'Region', ], false, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], true, ], ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'partitionResult', ], 'name', ], ], 'aws-cn', ], ], ], 'error' => 'S3 Accelerate cannot be used in this region', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3-fips.dualstack.us-east-1.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => 'us-east-1', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], true, ], ], ], 'rules' => [ [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3-fips.dualstack.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], false, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3-fips.dualstack.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3-fips.us-east-1.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => 'us-east-1', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], true, ], ], ], 'rules' => [ [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3-fips.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], false, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3-fips.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3-accelerate.dualstack.us-east-1.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => 'us-east-1', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], true, ], ], ], 'rules' => [ [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3-accelerate.dualstack.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], false, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3-accelerate.dualstack.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3.dualstack.us-east-1.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => 'us-east-1', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], true, ], ], ], 'rules' => [ [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3.dualstack.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], false, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3.dualstack.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], false, ], ], [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'parseURL', 'argv' => [ [ 'ref' => 'Endpoint', ], ], 'assign' => 'url', ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'url', ], 'isIp', ], ], true, ], ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], 'endpoint' => [ 'url' => '{url#scheme}://{url#authority}{url#normalizedPath}{Bucket}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => 'us-east-1', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], false, ], ], [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'parseURL', 'argv' => [ [ 'ref' => 'Endpoint', ], ], 'assign' => 'url', ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'url', ], 'isIp', ], ], false, ], ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], 'endpoint' => [ 'url' => '{url#scheme}://{Bucket}.{url#authority}{url#path}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => 'us-east-1', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], false, ], ], [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'parseURL', 'argv' => [ [ 'ref' => 'Endpoint', ], ], 'assign' => 'url', ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'url', ], 'isIp', ], ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'us-east-1', ], ], ], 'endpoint' => [ 'url' => '{url#scheme}://{url#authority}{url#normalizedPath}{Bucket}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => '{url#scheme}://{url#authority}{url#normalizedPath}{Bucket}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], false, ], ], [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'parseURL', 'argv' => [ [ 'ref' => 'Endpoint', ], ], 'assign' => 'url', ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'url', ], 'isIp', ], ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'us-east-1', ], ], ], 'endpoint' => [ 'url' => '{url#scheme}://{Bucket}.{url#authority}{url#path}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => '{url#scheme}://{Bucket}.{url#authority}{url#path}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], false, ], ], [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'parseURL', 'argv' => [ [ 'ref' => 'Endpoint', ], ], 'assign' => 'url', ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'url', ], 'isIp', ], ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], false, ], ], ], 'endpoint' => [ 'url' => '{url#scheme}://{url#authority}{url#normalizedPath}{Bucket}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], false, ], ], [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'parseURL', 'argv' => [ [ 'ref' => 'Endpoint', ], ], 'assign' => 'url', ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'url', ], 'isIp', ], ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], false, ], ], ], 'endpoint' => [ 'url' => '{url#scheme}://{Bucket}.{url#authority}{url#path}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3-accelerate.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => 'us-east-1', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'us-east-1', ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3-accelerate.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3-accelerate.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], false, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3-accelerate.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => 'us-east-1', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'us-east-1', ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{Bucket}.s3.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], false, ], ], ], 'endpoint' => [ 'url' => 'https://{Bucket}.s3.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid region: region was not a valid DNS name.', 'type' => 'error', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'parseURL', 'argv' => [ [ 'ref' => 'Endpoint', ], ], 'assign' => 'url', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'url', ], 'scheme', ], ], 'http', ], ], [ 'fn' => 'aws.isVirtualHostableS3Bucket', 'argv' => [ [ 'ref' => 'Bucket', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'ForcePathStyle', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], false, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'aws.partition', 'argv' => [ [ 'ref' => 'Region', ], ], 'assign' => 'partitionResult', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isValidHostLabel', 'argv' => [ [ 'ref' => 'Region', ], false, ], ], ], 'rules' => [ [ 'conditions' => [], 'endpoint' => [ 'url' => '{url#scheme}://{Bucket}.{url#authority}{url#path}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid region: region was not a valid DNS name.', 'type' => 'error', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'ForcePathStyle', ], false, ], ], [ 'fn' => 'aws.parseArn', 'argv' => [ [ 'ref' => 'Bucket', ], ], 'assign' => 'bucketArn', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'resourceId[0]', ], 'assign' => 'arnType', ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'arnType', ], '', ], ], ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'service', ], ], 's3-object-lambda', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'arnType', ], 'accesspoint', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'resourceId[1]', ], 'assign' => 'accessPointName', ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'accessPointName', ], '', ], ], ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], ], 'error' => 'S3 Object Lambda does not support Dual-stack', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], true, ], ], ], 'error' => 'S3 Object Lambda does not support S3 Accelerate', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'region', ], ], '', ], ], ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'DisableAccessPoints', ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'DisableAccessPoints', ], true, ], ], ], 'error' => 'Access points are not supported for this operation', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'resourceId[2]', ], ], ], ], ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'UseArnRegion', ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseArnRegion', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'region', ], ], '{Region}', ], ], ], ], ], 'error' => 'Invalid configuration: region from ARN `{bucketArn#region}` does not match client region `{Region}` and UseArnRegion is `false`', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'aws.partition', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'region', ], ], ], 'assign' => 'bucketPartition', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'aws.partition', 'argv' => [ [ 'ref' => 'Region', ], ], 'assign' => 'partitionResult', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketPartition', ], 'name', ], ], [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'partitionResult', ], 'name', ], ], ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isValidHostLabel', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'region', ], ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'accountId', ], ], '', ], ], ], 'error' => 'Invalid ARN: Missing account id', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'isValidHostLabel', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'accountId', ], ], false, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isValidHostLabel', 'argv' => [ [ 'ref' => 'accessPointName', ], false, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'parseURL', 'argv' => [ [ 'ref' => 'Endpoint', ], ], 'assign' => 'url', ], ], 'endpoint' => [ 'url' => '{url#scheme}://{accessPointName}-{bucketArn#accountId}.{url#authority}{url#path}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3-object-lambda', 'signingRegion' => '{bucketArn#region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://{accessPointName}-{bucketArn#accountId}.s3-object-lambda-fips.{bucketArn#region}.{bucketPartition#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3-object-lambda', 'signingRegion' => '{bucketArn#region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{accessPointName}-{bucketArn#accountId}.s3-object-lambda.{bucketArn#region}.{bucketPartition#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3-object-lambda', 'signingRegion' => '{bucketArn#region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid ARN: The access point name may only contain a-z, A-Z, 0-9 and `-`. Found: `{accessPointName}`', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid ARN: The account id may only contain a-z, A-Z, 0-9 and `-`. Found: `{bucketArn#accountId}`', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid region in ARN: `{bucketArn#region}` (invalid DNS name)', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Client was configured for partition `{partitionResult#name}` but ARN (`{Bucket}`) has `{bucketPartition#name}`', 'type' => 'error', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid ARN: The ARN may only contain a single resource component after `accesspoint`.', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid ARN: bucket ARN is missing a region', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid ARN: Expected a resource of the format `accesspoint:<accesspoint name>` but no name was provided', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid ARN: Object Lambda ARNs only support `accesspoint` arn types, but found: `{arnType}`', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'arnType', ], 'accesspoint', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'resourceId[1]', ], 'assign' => 'accessPointName', ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'accessPointName', ], '', ], ], ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'region', ], ], '', ], ], ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'arnType', ], 'accesspoint', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'region', ], ], '', ], ], ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'DisableAccessPoints', ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'DisableAccessPoints', ], true, ], ], ], 'error' => 'Access points are not supported for this operation', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'resourceId[2]', ], ], ], ], ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'UseArnRegion', ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseArnRegion', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'region', ], ], '{Region}', ], ], ], ], ], 'error' => 'Invalid configuration: region from ARN `{bucketArn#region}` does not match client region `{Region}` and UseArnRegion is `false`', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'aws.partition', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'region', ], ], ], 'assign' => 'bucketPartition', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'aws.partition', 'argv' => [ [ 'ref' => 'Region', ], ], 'assign' => 'partitionResult', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketPartition', ], 'name', ], ], '{partitionResult#name}', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isValidHostLabel', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'region', ], ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'service', ], ], 's3', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isValidHostLabel', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'accountId', ], ], false, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isValidHostLabel', 'argv' => [ [ 'ref' => 'accessPointName', ], false, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], true, ], ], ], 'error' => 'Access Points do not support S3 Accelerate', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://{accessPointName}-{bucketArn#accountId}.s3-accesspoint-fips.dualstack.{bucketArn#region}.{bucketPartition#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{bucketArn#region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], ], 'endpoint' => [ 'url' => 'https://{accessPointName}-{bucketArn#accountId}.s3-accesspoint-fips.{bucketArn#region}.{bucketPartition#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{bucketArn#region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://{accessPointName}-{bucketArn#accountId}.s3-accesspoint.dualstack.{bucketArn#region}.{bucketPartition#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{bucketArn#region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'parseURL', 'argv' => [ [ 'ref' => 'Endpoint', ], ], 'assign' => 'url', ], ], 'endpoint' => [ 'url' => '{url#scheme}://{accessPointName}-{bucketArn#accountId}.{url#authority}{url#path}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{bucketArn#region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], ], 'endpoint' => [ 'url' => 'https://{accessPointName}-{bucketArn#accountId}.s3-accesspoint.{bucketArn#region}.{bucketPartition#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{bucketArn#region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid ARN: The access point name may only contain a-z, A-Z, 0-9 and `-`. Found: `{accessPointName}`', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid ARN: The account id may only contain a-z, A-Z, 0-9 and `-`. Found: `{bucketArn#accountId}`', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid ARN: The ARN was not for the S3 service, found: {bucketArn#service}', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid region in ARN: `{bucketArn#region}` (invalid DNS name)', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Client was configured for partition `{partitionResult#name}` but ARN (`{Bucket}`) has `{bucketPartition#name}`', 'type' => 'error', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid ARN: The ARN may only contain a single resource component after `accesspoint`.', 'type' => 'error', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'isValidHostLabel', 'argv' => [ [ 'ref' => 'accessPointName', ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], ], 'error' => 'S3 MRAP does not support dual-stack', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'error' => 'S3 MRAP does not support FIPS', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], true, ], ], ], 'error' => 'S3 MRAP does not support S3 Accelerate', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'DisableMultiRegionAccessPoints', ], true, ], ], ], 'error' => 'Invalid configuration: Multi-Region Access Point ARNs are disabled.', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'aws.partition', 'argv' => [ [ 'ref' => 'Region', ], ], 'assign' => 'mrapPartition', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'mrapPartition', ], 'name', ], ], [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'partition', ], ], ], ], ], 'rules' => [ [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{accessPointName}.accesspoint.s3-global.{mrapPartition#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4a', 'signingName' => 's3', 'signingRegionSet' => [ '*', ], ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Client was configured for partition `{mrapPartition#name}` but bucket referred to partition `{bucketArn#partition}`', 'type' => 'error', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid Access Point Name', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid ARN: Expected a resource of the format `accesspoint:<accesspoint name>` but no name was provided', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'service', ], ], 's3-outposts', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], ], 'error' => 'S3 Outposts does not support Dual-stack', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'error' => 'S3 Outposts does not support FIPS', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], true, ], ], ], 'error' => 'S3 Outposts does not support S3 Accelerate', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'resourceId[4]', ], ], ], ], ], 'error' => 'Invalid Arn: Outpost Access Point ARN contains sub resources', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'resourceId[1]', ], 'assign' => 'outpostId', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isValidHostLabel', 'argv' => [ [ 'ref' => 'outpostId', ], false, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'UseArnRegion', ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseArnRegion', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'region', ], ], '{Region}', ], ], ], ], ], 'error' => 'Invalid configuration: region from ARN `{bucketArn#region}` does not match client region `{Region}` and UseArnRegion is `false`', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'aws.partition', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'region', ], ], ], 'assign' => 'bucketPartition', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'aws.partition', 'argv' => [ [ 'ref' => 'Region', ], ], 'assign' => 'partitionResult', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketPartition', ], 'name', ], ], [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'partitionResult', ], 'name', ], ], ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isValidHostLabel', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'region', ], ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isValidHostLabel', 'argv' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'accountId', ], ], false, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'resourceId[2]', ], 'assign' => 'outpostType', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'getAttr', 'argv' => [ [ 'ref' => 'bucketArn', ], 'resourceId[3]', ], 'assign' => 'accessPointName', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'outpostType', ], 'accesspoint', ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'parseURL', 'argv' => [ [ 'ref' => 'Endpoint', ], ], 'assign' => 'url', ], ], 'endpoint' => [ 'url' => 'https://{accessPointName}-{bucketArn#accountId}.{outpostId}.{url#authority}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4a', 'signingName' => 's3-outposts', 'signingRegionSet' => [ '*', ], ], [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3-outposts', 'signingRegion' => '{bucketArn#region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://{accessPointName}-{bucketArn#accountId}.{outpostId}.s3-outposts.{bucketArn#region}.{bucketPartition#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4a', 'signingName' => 's3-outposts', 'signingRegionSet' => [ '*', ], ], [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3-outposts', 'signingRegion' => '{bucketArn#region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Expected an outpost type `accesspoint`, found {outpostType}', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid ARN: expected an access point name', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid ARN: Expected a 4-component resource', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid ARN: The account id may only contain a-z, A-Z, 0-9 and `-`. Found: `{bucketArn#accountId}`', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid region in ARN: `{bucketArn#region}` (invalid DNS name)', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Client was configured for partition `{partitionResult#name}` but ARN (`{Bucket}`) has `{bucketPartition#name}`', 'type' => 'error', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid ARN: The outpost Id may only contain a-z, A-Z, 0-9 and `-`. Found: `{outpostId}`', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid ARN: The Outpost Id was not set', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid ARN: Unrecognized format: {Bucket} (type: {arnType})', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid ARN: No ARN type specified', 'type' => 'error', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'substring', 'argv' => [ [ 'ref' => 'Bucket', ], 0, 4, false, ], 'assign' => 'arnPrefix', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'arnPrefix', ], 'arn:', ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'fn' => 'aws.parseArn', 'argv' => [ [ 'ref' => 'Bucket', ], ], ], ], ], ], ], ], 'error' => 'Invalid ARN: `{Bucket}` was not a valid ARN', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'ForcePathStyle', ], true, ], ], [ 'fn' => 'aws.parseArn', 'argv' => [ [ 'ref' => 'Bucket', ], ], ], ], 'error' => 'Path-style addressing cannot be used with ARN buckets', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'uriEncode', 'argv' => [ [ 'ref' => 'Bucket', ], ], 'assign' => 'uri_encoded_bucket', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'aws.partition', 'argv' => [ [ 'ref' => 'Region', ], ], 'assign' => 'partitionResult', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], false, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], 'endpoint' => [ 'url' => 'https://s3-fips.dualstack.us-east-1.{partitionResult#dnsSuffix}/{uri_encoded_bucket}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => 'us-east-1', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], true, ], ], ], 'rules' => [ [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://s3-fips.dualstack.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], false, ], ], ], 'endpoint' => [ 'url' => 'https://s3-fips.dualstack.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], 'endpoint' => [ 'url' => 'https://s3-fips.us-east-1.{partitionResult#dnsSuffix}/{uri_encoded_bucket}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => 'us-east-1', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], true, ], ], ], 'rules' => [ [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://s3-fips.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], false, ], ], ], 'endpoint' => [ 'url' => 'https://s3-fips.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], 'endpoint' => [ 'url' => 'https://s3.dualstack.us-east-1.{partitionResult#dnsSuffix}/{uri_encoded_bucket}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => 'us-east-1', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], true, ], ], ], 'rules' => [ [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://s3.dualstack.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], false, ], ], ], 'endpoint' => [ 'url' => 'https://s3.dualstack.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'parseURL', 'argv' => [ [ 'ref' => 'Endpoint', ], ], 'assign' => 'url', ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], 'endpoint' => [ 'url' => '{url#scheme}://{url#authority}{url#normalizedPath}{uri_encoded_bucket}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => 'us-east-1', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'parseURL', 'argv' => [ [ 'ref' => 'Endpoint', ], ], 'assign' => 'url', ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'us-east-1', ], ], ], 'endpoint' => [ 'url' => '{url#scheme}://{url#authority}{url#normalizedPath}{uri_encoded_bucket}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => '{url#scheme}://{url#authority}{url#normalizedPath}{uri_encoded_bucket}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'parseURL', 'argv' => [ [ 'ref' => 'Endpoint', ], ], 'assign' => 'url', ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], false, ], ], ], 'endpoint' => [ 'url' => '{url#scheme}://{url#authority}{url#normalizedPath}{uri_encoded_bucket}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], 'endpoint' => [ 'url' => 'https://s3.{partitionResult#dnsSuffix}/{uri_encoded_bucket}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => 'us-east-1', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'us-east-1', ], ], ], 'endpoint' => [ 'url' => 'https://s3.{partitionResult#dnsSuffix}/{uri_encoded_bucket}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://s3.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], false, ], ], ], 'endpoint' => [ 'url' => 'https://s3.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Path-style addressing cannot be used with S3 Accelerate', 'type' => 'error', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'UseObjectLambdaEndpoint', ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseObjectLambdaEndpoint', ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'aws.partition', 'argv' => [ [ 'ref' => 'Region', ], ], 'assign' => 'partitionResult', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isValidHostLabel', 'argv' => [ [ 'ref' => 'Region', ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], ], 'error' => 'S3 Object Lambda does not support Dual-stack', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'Accelerate', ], true, ], ], ], 'error' => 'S3 Object Lambda does not support S3 Accelerate', 'type' => 'error', ], [ 'conditions' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'parseURL', 'argv' => [ [ 'ref' => 'Endpoint', ], ], 'assign' => 'url', ], ], 'endpoint' => [ 'url' => '{url#scheme}://{url#authority}{url#path}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3-object-lambda', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], ], 'endpoint' => [ 'url' => 'https://s3-object-lambda-fips.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3-object-lambda', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://s3-object-lambda.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3-object-lambda', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid region: region was not a valid DNS name.', 'type' => 'error', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Bucket', ], ], ], ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'aws.partition', 'argv' => [ [ 'ref' => 'Region', ], ], 'assign' => 'partitionResult', ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'isValidHostLabel', 'argv' => [ [ 'ref' => 'Region', ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], 'endpoint' => [ 'url' => 'https://s3-fips.dualstack.us-east-1.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => 'us-east-1', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], true, ], ], ], 'rules' => [ [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://s3-fips.dualstack.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], false, ], ], ], 'endpoint' => [ 'url' => 'https://s3-fips.dualstack.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], 'endpoint' => [ 'url' => 'https://s3-fips.us-east-1.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => 'us-east-1', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], true, ], ], ], 'rules' => [ [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://s3-fips.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], true, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], false, ], ], ], 'endpoint' => [ 'url' => 'https://s3-fips.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], 'endpoint' => [ 'url' => 'https://s3.dualstack.us-east-1.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => 'us-east-1', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], true, ], ], ], 'rules' => [ [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://s3.dualstack.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], true, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], false, ], ], ], 'endpoint' => [ 'url' => 'https://s3.dualstack.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'parseURL', 'argv' => [ [ 'ref' => 'Endpoint', ], ], 'assign' => 'url', ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], 'endpoint' => [ 'url' => '{url#scheme}://{url#authority}{url#path}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => 'us-east-1', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'parseURL', 'argv' => [ [ 'ref' => 'Endpoint', ], ], 'assign' => 'url', ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'us-east-1', ], ], ], 'endpoint' => [ 'url' => '{url#scheme}://{url#authority}{url#path}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => '{url#scheme}://{url#authority}{url#path}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], [ 'fn' => 'parseURL', 'argv' => [ [ 'ref' => 'Endpoint', ], ], 'assign' => 'url', ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], false, ], ], ], 'endpoint' => [ 'url' => '{url#scheme}://{url#authority}{url#path}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], 'endpoint' => [ 'url' => 'https://s3.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => 'us-east-1', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], true, ], ], ], 'rules' => [ [ 'conditions' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'us-east-1', ], ], ], 'endpoint' => [ 'url' => 'https://s3.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], [ 'conditions' => [], 'endpoint' => [ 'url' => 'https://s3.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [ [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseFIPS', ], false, ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseDualStack', ], false, ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'isSet', 'argv' => [ [ 'ref' => 'Endpoint', ], ], ], ], ], [ 'fn' => 'not', 'argv' => [ [ 'fn' => 'stringEquals', 'argv' => [ [ 'ref' => 'Region', ], 'aws-global', ], ], ], ], [ 'fn' => 'booleanEquals', 'argv' => [ [ 'ref' => 'UseGlobalEndpoint', ], false, ], ], ], 'endpoint' => [ 'url' => 'https://s3.{Region}.{partitionResult#dnsSuffix}', 'properties' => [ 'authSchemes' => [ [ 'disableDoubleEncoding' => true, 'name' => 'sigv4', 'signingName' => 's3', 'signingRegion' => '{Region}', ], ], ], 'headers' => [], ], 'type' => 'endpoint', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'Invalid region: region was not a valid DNS name.', 'type' => 'error', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], ], 'type' => 'tree', ], [ 'conditions' => [], 'error' => 'A region must be set when sending requests to S3.', 'type' => 'error', ], ],];
