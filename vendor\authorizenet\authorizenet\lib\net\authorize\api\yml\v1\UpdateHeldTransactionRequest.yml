net\authorize\api\contract\v1\UpdateHeldTransactionRequest:
    xml_root_name: updateHeldTransactionRequest
    xml_root_namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
    properties:
        heldTransactionRequest:
            expose: true
            access_type: public_method
            serialized_name: heldTransactionRequest
            xml_element:
                namespace: AnetApi/xml/v1/schema/AnetApiSchema.xsd
            accessor:
                getter: getHeldTransactionRequest
                setter: setHeldTransactionRequest
            type: net\authorize\api\contract\v1\HeldTransactionRequestType
