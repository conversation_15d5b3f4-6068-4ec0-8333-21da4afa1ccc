<?php

namespace App\Services\ProjectManagement;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Exception;

/**
 * خدمة التخزين المؤقت المتقدمة
 * 
 * هذه الخدمة تدير التخزين المؤقت للاستعلامات المعقدة والإحصائيات
 * لتحسين الأداء مع قواعد البيانات الكبيرة
 * 
 * @package App\Services\ProjectManagement
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */
class CacheService
{
    /**
     * مدة التخزين المؤقت الافتراضية (بالدقائق)
     */
    const DEFAULT_TTL = 60;

    /**
     * مدة التخزين المؤقت للإحصائيات (بالدقائق)
     */
    const STATISTICS_TTL = 30;

    /**
     * مدة التخزين المؤقت للتقارير (بالدقائق)
     */
    const REPORTS_TTL = 120;

    /**
     * الحصول على إحصائيات المشاريع مع التخزين المؤقت
     * 
     * @param array $filters المرشحات
     * @param int $ttl مدة التخزين المؤقت
     * @return array الإحصائيات
     */
    public function getProjectStatistics(array $filters = [], int $ttl = self::STATISTICS_TTL): array
    {
        $cacheKey = $this->generateCacheKey('project_statistics', $filters);
        
        return $this->rememberInDatabase($cacheKey, 'statistics', $ttl, function() use ($filters) {
            return $this->calculateProjectStatistics($filters);
        });
    }

    /**
     * الحصول على إحصائيات المهام مع التخزين المؤقت
     * 
     * @param array $filters المرشحات
     * @param int $ttl مدة التخزين المؤقت
     * @return array الإحصائيات
     */
    public function getTaskStatistics(array $filters = [], int $ttl = self::STATISTICS_TTL): array
    {
        $cacheKey = $this->generateCacheKey('task_statistics', $filters);
        
        return $this->rememberInDatabase($cacheKey, 'statistics', $ttl, function() use ($filters) {
            return $this->calculateTaskStatistics($filters);
        });
    }

    /**
     * الحصول على تقرير الأداء مع التخزين المؤقت
     * 
     * @param array $parameters معاملات التقرير
     * @param int $ttl مدة التخزين المؤقت
     * @return array التقرير
     */
    public function getPerformanceReport(array $parameters = [], int $ttl = self::REPORTS_TTL): array
    {
        $cacheKey = $this->generateCacheKey('performance_report', $parameters);
        
        return $this->rememberInDatabase($cacheKey, 'reports', $ttl, function() use ($parameters) {
            return $this->generatePerformanceReport($parameters);
        });
    }

    /**
     * البحث المتقدم مع التخزين المؤقت
     * 
     * @param string $query استعلام البحث
     * @param array $filters المرشحات
     * @param int $ttl مدة التخزين المؤقت
     * @return array نتائج البحث
     */
    public function advancedSearch(string $query, array $filters = [], int $ttl = self::DEFAULT_TTL): array
    {
        $cacheKey = $this->generateCacheKey('advanced_search', [
            'query' => $query,
            'filters' => $filters
        ]);
        
        return $this->rememberInDatabase($cacheKey, 'search', $ttl, function() use ($query, $filters) {
            return $this->performAdvancedSearch($query, $filters);
        });
    }

    /**
     * تخزين مؤقت في قاعدة البيانات مع fallback للذاكرة
     * 
     * @param string $key مفتاح التخزين
     * @param string $category فئة البيانات
     * @param int $ttl مدة التخزين
     * @param callable $callback دالة الحساب
     * @return mixed البيانات
     */
    protected function rememberInDatabase(string $key, string $category, int $ttl, callable $callback)
    {
        try {
            // البحث في التخزين المؤقت لقاعدة البيانات أولاً
            $cached = $this->getFromDatabaseCache($key);
            
            if ($cached !== null) {
                // تحديث إحصائيات الاستخدام
                $this->updateCacheHitStats($key);
                return $cached;
            }

            // إذا لم توجد البيانات، احسبها
            $data = $callback();
            
            // حفظ في قاعدة البيانات والذاكرة
            $this->storeToDatabaseCache($key, $category, $data, $ttl);
            $this->storeToMemoryCache($key, $data, $ttl);
            
            return $data;

        } catch (Exception $e) {
            Log::error("خطأ في التخزين المؤقت", [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            
            // fallback للذاكرة فقط
            return Cache::remember($key, $ttl * 60, $callback);
        }
    }

    /**
     * الحصول من التخزين المؤقت لقاعدة البيانات
     * 
     * @param string $key المفتاح
     * @return mixed|null البيانات أو null
     */
    protected function getFromDatabaseCache(string $key)
    {
        $cached = DB::table('statistics_cache')
            ->where('cache_key', $key)
            ->where('expires_at', '>', now())
            ->first();

        if ($cached) {
            return json_decode($cached->data, true);
        }

        return null;
    }

    /**
     * حفظ في التخزين المؤقت لقاعدة البيانات
     * 
     * @param string $key المفتاح
     * @param string $category الفئة
     * @param mixed $data البيانات
     * @param int $ttl مدة التخزين
     */
    protected function storeToDatabaseCache(string $key, string $category, $data, int $ttl): void
    {
        DB::table('statistics_cache')->updateOrInsert(
            ['cache_key' => $key],
            [
                'category' => $category,
                'data' => json_encode($data),
                'filters' => json_encode([]),
                'calculated_at' => now(),
                'expires_at' => now()->addMinutes($ttl),
                'hit_count' => 0,
                'last_accessed_at' => now()
            ]
        );
    }

    /**
     * حفظ في التخزين المؤقت للذاكرة
     * 
     * @param string $key المفتاح
     * @param mixed $data البيانات
     * @param int $ttl مدة التخزين
     */
    protected function storeToMemoryCache(string $key, $data, int $ttl): void
    {
        Cache::put($key, $data, $ttl * 60);
    }

    /**
     * تحديث إحصائيات استخدام التخزين المؤقت
     * 
     * @param string $key المفتاح
     */
    protected function updateCacheHitStats(string $key): void
    {
        DB::table('statistics_cache')
            ->where('cache_key', $key)
            ->increment('hit_count');
            
        DB::table('statistics_cache')
            ->where('cache_key', $key)
            ->update(['last_accessed_at' => now()]);
    }

    /**
     * حساب إحصائيات المشاريع
     * 
     * @param array $filters المرشحات
     * @return array الإحصائيات
     */
    protected function calculateProjectStatistics(array $filters): array
    {
        $query = DB::table('projects')->whereNull('deleted_at');
        
        // تطبيق المرشحات
        $this->applyFilters($query, $filters);
        
        $stats = [
            'total' => $query->count(),
            'by_status' => $this->getProjectsByStatus($filters),
            'by_type' => $this->getProjectsByType($filters),
            'by_priority' => $this->getProjectsByPriority($filters),
            'budget_stats' => $this->getBudgetStatistics($filters),
            'timeline_stats' => $this->getTimelineStatistics($filters),
            'calculated_at' => now()->toDateTimeString()
        ];

        return $stats;
    }

    /**
     * حساب إحصائيات المهام
     * 
     * @param array $filters المرشحات
     * @return array الإحصائيات
     */
    protected function calculateTaskStatistics(array $filters): array
    {
        $query = DB::table('tasks')->whereNull('deleted_at');
        
        // تطبيق المرشحات
        $this->applyFilters($query, $filters);
        
        $stats = [
            'total' => $query->count(),
            'by_status' => $this->getTasksByStatus($filters),
            'by_type' => $this->getTasksByType($filters),
            'by_priority' => $this->getTasksByPriority($filters),
            'completion_rate' => $this->getTaskCompletionRate($filters),
            'overdue_count' => $this->getOverdueTasksCount($filters),
            'calculated_at' => now()->toDateTimeString()
        ];

        return $stats;
    }

    /**
     * إنتاج تقرير الأداء
     * 
     * @param array $parameters المعاملات
     * @return array التقرير
     */
    protected function generatePerformanceReport(array $parameters): array
    {
        $dateFrom = $parameters['date_from'] ?? now()->subMonth()->toDateString();
        $dateTo = $parameters['date_to'] ?? now()->toDateString();
        
        $report = [
            'period' => [
                'from' => $dateFrom,
                'to' => $dateTo
            ],
            'projects_performance' => $this->getProjectsPerformance($dateFrom, $dateTo),
            'tasks_performance' => $this->getTasksPerformance($dateFrom, $dateTo),
            'team_performance' => $this->getTeamPerformance($dateFrom, $dateTo),
            'time_tracking' => $this->getTimeTrackingStats($dateFrom, $dateTo),
            'generated_at' => now()->toDateTimeString()
        ];

        return $report;
    }

    /**
     * تنفيذ البحث المتقدم
     * 
     * @param string $query استعلام البحث
     * @param array $filters المرشحات
     * @return array النتائج
     */
    protected function performAdvancedSearch(string $query, array $filters): array
    {
        $results = [];
        
        // البحث في فهرس البحث المحسن
        $searchResults = DB::table('search_index')
            ->where('searchable_content', 'LIKE', "%{$query}%")
            ->orWhereRaw("JSON_SEARCH(keywords, 'one', ?) IS NOT NULL", ["%{$query}%"])
            ->orderByDesc('relevance_score')
            ->limit(100)
            ->get();

        foreach ($searchResults as $result) {
            $entityData = $this->getEntityData($result->entity_type, $result->entity_id);
            if ($entityData) {
                $results[] = [
                    'type' => $result->entity_type,
                    'id' => $result->entity_id,
                    'data' => $entityData,
                    'relevance_score' => $result->relevance_score
                ];
            }
        }

        return [
            'query' => $query,
            'total_results' => count($results),
            'results' => $results,
            'searched_at' => now()->toDateTimeString()
        ];
    }

    /**
     * الحصول على بيانات الكائن
     * 
     * @param string $entityType نوع الكائن
     * @param int $entityId معرف الكائن
     * @return array|null البيانات
     */
    protected function getEntityData(string $entityType, int $entityId): ?array
    {
        switch ($entityType) {
            case 'projects':
                return DB::table('projects')
                    ->where('id', $entityId)
                    ->whereNull('deleted_at')
                    ->first();
                    
            case 'tasks':
                return DB::table('tasks')
                    ->where('id', $entityId)
                    ->whereNull('deleted_at')
                    ->first();
                    
            default:
                return null;
        }
    }

    /**
     * تطبيق المرشحات على الاستعلام
     * 
     * @param \Illuminate\Database\Query\Builder $query الاستعلام
     * @param array $filters المرشحات
     */
    protected function applyFilters($query, array $filters): void
    {
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        
        if (!empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }
        
        if (!empty($filters['priority'])) {
            $query->where('priority', $filters['priority']);
        }
        
        if (!empty($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }
        
        if (!empty($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }
    }

    /**
     * إنتاج مفتاح التخزين المؤقت
     * 
     * @param string $prefix البادئة
     * @param array $data البيانات
     * @return string المفتاح
     */
    protected function generateCacheKey(string $prefix, array $data): string
    {
        $dataString = serialize($data);
        $hash = md5($dataString);
        return "pm_{$prefix}_{$hash}";
    }

    /**
     * مسح التخزين المؤقت حسب النمط
     * 
     * @param string $pattern النمط
     * @return int عدد المفاتيح المحذوفة
     */
    public function clearCacheByPattern(string $pattern): int
    {
        try {
            // مسح من قاعدة البيانات
            $deletedFromDb = DB::table('statistics_cache')
                ->where('cache_key', 'LIKE', $pattern)
                ->delete();

            // مسح من الذاكرة (إذا كان Redis متاحاً)
            if (Cache::getStore() instanceof \Illuminate\Cache\RedisStore) {
                $keys = Cache::getRedis()->keys($pattern);
                if (!empty($keys)) {
                    Cache::getRedis()->del($keys);
                }
            }

            Log::info("تم مسح التخزين المؤقت", [
                'pattern' => $pattern,
                'deleted_count' => $deletedFromDb
            ]);

            return $deletedFromDb;

        } catch (Exception $e) {
            Log::error("خطأ في مسح التخزين المؤقت", [
                'pattern' => $pattern,
                'error' => $e->getMessage()
            ]);
            
            return 0;
        }
    }

    /**
     * إحصائيات التخزين المؤقت
     * 
     * @return array الإحصائيات
     */
    public function getCacheStatistics(): array
    {
        try {
            $stats = DB::table('statistics_cache')
                ->selectRaw('
                    category,
                    COUNT(*) as total_entries,
                    SUM(hit_count) as total_hits,
                    AVG(hit_count) as avg_hits,
                    COUNT(CASE WHEN expires_at > NOW() THEN 1 END) as active_entries,
                    COUNT(CASE WHEN expires_at <= NOW() THEN 1 END) as expired_entries
                ')
                ->groupBy('category')
                ->get();

            return [
                'by_category' => $stats,
                'total_cache_size' => DB::table('statistics_cache')->count(),
                'generated_at' => now()->toDateTimeString()
            ];

        } catch (Exception $e) {
            Log::error("خطأ في الحصول على إحصائيات التخزين المؤقت", [
                'error' => $e->getMessage()
            ]);
            
            return [];
        }
    }

    // طرق مساعدة للإحصائيات (سيتم تنفيذها حسب الحاجة)
    protected function getProjectsByStatus($filters) { /* تنفيذ */ }
    protected function getProjectsByType($filters) { /* تنفيذ */ }
    protected function getProjectsByPriority($filters) { /* تنفيذ */ }
    protected function getBudgetStatistics($filters) { /* تنفيذ */ }
    protected function getTimelineStatistics($filters) { /* تنفيذ */ }
    protected function getTasksByStatus($filters) { /* تنفيذ */ }
    protected function getTasksByType($filters) { /* تنفيذ */ }
    protected function getTasksByPriority($filters) { /* تنفيذ */ }
    protected function getTaskCompletionRate($filters) { /* تنفيذ */ }
    protected function getOverdueTasksCount($filters) { /* تنفيذ */ }
    protected function getProjectsPerformance($from, $to) { /* تنفيذ */ }
    protected function getTasksPerformance($from, $to) { /* تنفيذ */ }
    protected function getTeamPerformance($from, $to) { /* تنفيذ */ }
    protected function getTimeTrackingStats($from, $to) { /* تنفيذ */ }
}
