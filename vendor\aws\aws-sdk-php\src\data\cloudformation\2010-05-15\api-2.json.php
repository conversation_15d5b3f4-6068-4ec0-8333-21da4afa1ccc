<?php
// This file was auto-generated from sdk-root/src/data/cloudformation/2010-05-15/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2010-05-15', 'endpointPrefix' => 'cloudformation', 'protocol' => 'query', 'protocols' => [ 'query', ], 'serviceFullName' => 'AWS CloudFormation', 'serviceId' => 'CloudFormation', 'signatureVersion' => 'v4', 'uid' => 'cloudformation-2010-05-15', 'xmlNamespace' => 'http://cloudformation.amazonaws.com/doc/2010-05-15/', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'ActivateOrganizationsAccess' => [ 'name' => 'ActivateOrganizationsAccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ActivateOrganizationsAccessInput', ], 'output' => [ 'shape' => 'ActivateOrganizationsAccessOutput', 'resultWrapper' => 'ActivateOrganizationsAccessResult', ], 'errors' => [ [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'OperationNotFoundException', ], ], ], 'ActivateType' => [ 'name' => 'ActivateType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ActivateTypeInput', ], 'output' => [ 'shape' => 'ActivateTypeOutput', 'resultWrapper' => 'ActivateTypeResult', ], 'errors' => [ [ 'shape' => 'CFNRegistryException', ], [ 'shape' => 'TypeNotFoundException', ], ], 'idempotent' => true, ], 'BatchDescribeTypeConfigurations' => [ 'name' => 'BatchDescribeTypeConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDescribeTypeConfigurationsInput', ], 'output' => [ 'shape' => 'BatchDescribeTypeConfigurationsOutput', 'resultWrapper' => 'BatchDescribeTypeConfigurationsResult', ], 'errors' => [ [ 'shape' => 'TypeConfigurationNotFoundException', ], [ 'shape' => 'CFNRegistryException', ], ], ], 'CancelUpdateStack' => [ 'name' => 'CancelUpdateStack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelUpdateStackInput', ], 'errors' => [ [ 'shape' => 'TokenAlreadyExistsException', ], ], ], 'ContinueUpdateRollback' => [ 'name' => 'ContinueUpdateRollback', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ContinueUpdateRollbackInput', ], 'output' => [ 'shape' => 'ContinueUpdateRollbackOutput', 'resultWrapper' => 'ContinueUpdateRollbackResult', ], 'errors' => [ [ 'shape' => 'TokenAlreadyExistsException', ], ], ], 'CreateChangeSet' => [ 'name' => 'CreateChangeSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateChangeSetInput', ], 'output' => [ 'shape' => 'CreateChangeSetOutput', 'resultWrapper' => 'CreateChangeSetResult', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InsufficientCapabilitiesException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateGeneratedTemplate' => [ 'name' => 'CreateGeneratedTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateGeneratedTemplateInput', ], 'output' => [ 'shape' => 'CreateGeneratedTemplateOutput', 'resultWrapper' => 'CreateGeneratedTemplateResult', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConcurrentResourcesLimitExceededException', ], ], ], 'CreateStack' => [ 'name' => 'CreateStack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateStackInput', ], 'output' => [ 'shape' => 'CreateStackOutput', 'resultWrapper' => 'CreateStackResult', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'TokenAlreadyExistsException', ], [ 'shape' => 'InsufficientCapabilitiesException', ], ], ], 'CreateStackInstances' => [ 'name' => 'CreateStackInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateStackInstancesInput', ], 'output' => [ 'shape' => 'CreateStackInstancesOutput', 'resultWrapper' => 'CreateStackInstancesResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], [ 'shape' => 'OperationInProgressException', ], [ 'shape' => 'OperationIdAlreadyExistsException', ], [ 'shape' => 'StaleRequestException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateStackRefactor' => [ 'name' => 'CreateStackRefactor', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateStackRefactorInput', ], 'output' => [ 'shape' => 'CreateStackRefactorOutput', 'resultWrapper' => 'CreateStackRefactorResult', ], ], 'CreateStackSet' => [ 'name' => 'CreateStackSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateStackSetInput', ], 'output' => [ 'shape' => 'CreateStackSetOutput', 'resultWrapper' => 'CreateStackSetResult', ], 'errors' => [ [ 'shape' => 'NameAlreadyExistsException', ], [ 'shape' => 'CreatedButModifiedException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'DeactivateOrganizationsAccess' => [ 'name' => 'DeactivateOrganizationsAccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeactivateOrganizationsAccessInput', ], 'output' => [ 'shape' => 'DeactivateOrganizationsAccessOutput', 'resultWrapper' => 'DeactivateOrganizationsAccessResult', ], 'errors' => [ [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'OperationNotFoundException', ], ], ], 'DeactivateType' => [ 'name' => 'DeactivateType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeactivateTypeInput', ], 'output' => [ 'shape' => 'DeactivateTypeOutput', 'resultWrapper' => 'DeactivateTypeResult', ], 'errors' => [ [ 'shape' => 'CFNRegistryException', ], [ 'shape' => 'TypeNotFoundException', ], ], 'idempotent' => true, ], 'DeleteChangeSet' => [ 'name' => 'DeleteChangeSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteChangeSetInput', ], 'output' => [ 'shape' => 'DeleteChangeSetOutput', 'resultWrapper' => 'DeleteChangeSetResult', ], 'errors' => [ [ 'shape' => 'InvalidChangeSetStatusException', ], ], ], 'DeleteGeneratedTemplate' => [ 'name' => 'DeleteGeneratedTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteGeneratedTemplateInput', ], 'errors' => [ [ 'shape' => 'GeneratedTemplateNotFoundException', ], [ 'shape' => 'ConcurrentResourcesLimitExceededException', ], ], ], 'DeleteStack' => [ 'name' => 'DeleteStack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteStackInput', ], 'errors' => [ [ 'shape' => 'TokenAlreadyExistsException', ], ], ], 'DeleteStackInstances' => [ 'name' => 'DeleteStackInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteStackInstancesInput', ], 'output' => [ 'shape' => 'DeleteStackInstancesOutput', 'resultWrapper' => 'DeleteStackInstancesResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], [ 'shape' => 'OperationInProgressException', ], [ 'shape' => 'OperationIdAlreadyExistsException', ], [ 'shape' => 'StaleRequestException', ], [ 'shape' => 'InvalidOperationException', ], ], ], 'DeleteStackSet' => [ 'name' => 'DeleteStackSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteStackSetInput', ], 'output' => [ 'shape' => 'DeleteStackSetOutput', 'resultWrapper' => 'DeleteStackSetResult', ], 'errors' => [ [ 'shape' => 'StackSetNotEmptyException', ], [ 'shape' => 'OperationInProgressException', ], ], ], 'DeregisterType' => [ 'name' => 'DeregisterType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterTypeInput', ], 'output' => [ 'shape' => 'DeregisterTypeOutput', 'resultWrapper' => 'DeregisterTypeResult', ], 'errors' => [ [ 'shape' => 'CFNRegistryException', ], [ 'shape' => 'TypeNotFoundException', ], ], 'idempotent' => true, ], 'DescribeAccountLimits' => [ 'name' => 'DescribeAccountLimits', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAccountLimitsInput', ], 'output' => [ 'shape' => 'DescribeAccountLimitsOutput', 'resultWrapper' => 'DescribeAccountLimitsResult', ], ], 'DescribeChangeSet' => [ 'name' => 'DescribeChangeSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeChangeSetInput', ], 'output' => [ 'shape' => 'DescribeChangeSetOutput', 'resultWrapper' => 'DescribeChangeSetResult', ], 'errors' => [ [ 'shape' => 'ChangeSetNotFoundException', ], ], ], 'DescribeChangeSetHooks' => [ 'name' => 'DescribeChangeSetHooks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeChangeSetHooksInput', ], 'output' => [ 'shape' => 'DescribeChangeSetHooksOutput', 'resultWrapper' => 'DescribeChangeSetHooksResult', ], 'errors' => [ [ 'shape' => 'ChangeSetNotFoundException', ], ], ], 'DescribeGeneratedTemplate' => [ 'name' => 'DescribeGeneratedTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeGeneratedTemplateInput', ], 'output' => [ 'shape' => 'DescribeGeneratedTemplateOutput', 'resultWrapper' => 'DescribeGeneratedTemplateResult', ], 'errors' => [ [ 'shape' => 'GeneratedTemplateNotFoundException', ], ], ], 'DescribeOrganizationsAccess' => [ 'name' => 'DescribeOrganizationsAccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeOrganizationsAccessInput', ], 'output' => [ 'shape' => 'DescribeOrganizationsAccessOutput', 'resultWrapper' => 'DescribeOrganizationsAccessResult', ], 'errors' => [ [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'OperationNotFoundException', ], ], ], 'DescribePublisher' => [ 'name' => 'DescribePublisher', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePublisherInput', ], 'output' => [ 'shape' => 'DescribePublisherOutput', 'resultWrapper' => 'DescribePublisherResult', ], 'errors' => [ [ 'shape' => 'CFNRegistryException', ], ], 'idempotent' => true, ], 'DescribeResourceScan' => [ 'name' => 'DescribeResourceScan', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeResourceScanInput', ], 'output' => [ 'shape' => 'DescribeResourceScanOutput', 'resultWrapper' => 'DescribeResourceScanResult', ], 'errors' => [ [ 'shape' => 'ResourceScanNotFoundException', ], ], ], 'DescribeStackDriftDetectionStatus' => [ 'name' => 'DescribeStackDriftDetectionStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStackDriftDetectionStatusInput', ], 'output' => [ 'shape' => 'DescribeStackDriftDetectionStatusOutput', 'resultWrapper' => 'DescribeStackDriftDetectionStatusResult', ], ], 'DescribeStackEvents' => [ 'name' => 'DescribeStackEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStackEventsInput', ], 'output' => [ 'shape' => 'DescribeStackEventsOutput', 'resultWrapper' => 'DescribeStackEventsResult', ], ], 'DescribeStackInstance' => [ 'name' => 'DescribeStackInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStackInstanceInput', ], 'output' => [ 'shape' => 'DescribeStackInstanceOutput', 'resultWrapper' => 'DescribeStackInstanceResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], [ 'shape' => 'StackInstanceNotFoundException', ], ], ], 'DescribeStackRefactor' => [ 'name' => 'DescribeStackRefactor', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStackRefactorInput', ], 'output' => [ 'shape' => 'DescribeStackRefactorOutput', 'resultWrapper' => 'DescribeStackRefactorResult', ], 'errors' => [ [ 'shape' => 'StackRefactorNotFoundException', ], ], ], 'DescribeStackResource' => [ 'name' => 'DescribeStackResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStackResourceInput', ], 'output' => [ 'shape' => 'DescribeStackResourceOutput', 'resultWrapper' => 'DescribeStackResourceResult', ], ], 'DescribeStackResourceDrifts' => [ 'name' => 'DescribeStackResourceDrifts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStackResourceDriftsInput', ], 'output' => [ 'shape' => 'DescribeStackResourceDriftsOutput', 'resultWrapper' => 'DescribeStackResourceDriftsResult', ], ], 'DescribeStackResources' => [ 'name' => 'DescribeStackResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStackResourcesInput', ], 'output' => [ 'shape' => 'DescribeStackResourcesOutput', 'resultWrapper' => 'DescribeStackResourcesResult', ], ], 'DescribeStackSet' => [ 'name' => 'DescribeStackSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStackSetInput', ], 'output' => [ 'shape' => 'DescribeStackSetOutput', 'resultWrapper' => 'DescribeStackSetResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], ], ], 'DescribeStackSetOperation' => [ 'name' => 'DescribeStackSetOperation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStackSetOperationInput', ], 'output' => [ 'shape' => 'DescribeStackSetOperationOutput', 'resultWrapper' => 'DescribeStackSetOperationResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], [ 'shape' => 'OperationNotFoundException', ], ], ], 'DescribeStacks' => [ 'name' => 'DescribeStacks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStacksInput', ], 'output' => [ 'shape' => 'DescribeStacksOutput', 'resultWrapper' => 'DescribeStacksResult', ], ], 'DescribeType' => [ 'name' => 'DescribeType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTypeInput', ], 'output' => [ 'shape' => 'DescribeTypeOutput', 'resultWrapper' => 'DescribeTypeResult', ], 'errors' => [ [ 'shape' => 'CFNRegistryException', ], [ 'shape' => 'TypeNotFoundException', ], ], 'idempotent' => true, ], 'DescribeTypeRegistration' => [ 'name' => 'DescribeTypeRegistration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTypeRegistrationInput', ], 'output' => [ 'shape' => 'DescribeTypeRegistrationOutput', 'resultWrapper' => 'DescribeTypeRegistrationResult', ], 'errors' => [ [ 'shape' => 'CFNRegistryException', ], ], 'idempotent' => true, ], 'DetectStackDrift' => [ 'name' => 'DetectStackDrift', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetectStackDriftInput', ], 'output' => [ 'shape' => 'DetectStackDriftOutput', 'resultWrapper' => 'DetectStackDriftResult', ], ], 'DetectStackResourceDrift' => [ 'name' => 'DetectStackResourceDrift', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetectStackResourceDriftInput', ], 'output' => [ 'shape' => 'DetectStackResourceDriftOutput', 'resultWrapper' => 'DetectStackResourceDriftResult', ], ], 'DetectStackSetDrift' => [ 'name' => 'DetectStackSetDrift', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetectStackSetDriftInput', ], 'output' => [ 'shape' => 'DetectStackSetDriftOutput', 'resultWrapper' => 'DetectStackSetDriftResult', ], 'errors' => [ [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'OperationInProgressException', ], [ 'shape' => 'StackSetNotFoundException', ], ], ], 'EstimateTemplateCost' => [ 'name' => 'EstimateTemplateCost', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EstimateTemplateCostInput', ], 'output' => [ 'shape' => 'EstimateTemplateCostOutput', 'resultWrapper' => 'EstimateTemplateCostResult', ], ], 'ExecuteChangeSet' => [ 'name' => 'ExecuteChangeSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ExecuteChangeSetInput', ], 'output' => [ 'shape' => 'ExecuteChangeSetOutput', 'resultWrapper' => 'ExecuteChangeSetResult', ], 'errors' => [ [ 'shape' => 'InvalidChangeSetStatusException', ], [ 'shape' => 'ChangeSetNotFoundException', ], [ 'shape' => 'InsufficientCapabilitiesException', ], [ 'shape' => 'TokenAlreadyExistsException', ], ], ], 'ExecuteStackRefactor' => [ 'name' => 'ExecuteStackRefactor', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ExecuteStackRefactorInput', ], ], 'GetGeneratedTemplate' => [ 'name' => 'GetGeneratedTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetGeneratedTemplateInput', ], 'output' => [ 'shape' => 'GetGeneratedTemplateOutput', 'resultWrapper' => 'GetGeneratedTemplateResult', ], 'errors' => [ [ 'shape' => 'GeneratedTemplateNotFoundException', ], ], ], 'GetStackPolicy' => [ 'name' => 'GetStackPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetStackPolicyInput', ], 'output' => [ 'shape' => 'GetStackPolicyOutput', 'resultWrapper' => 'GetStackPolicyResult', ], ], 'GetTemplate' => [ 'name' => 'GetTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTemplateInput', ], 'output' => [ 'shape' => 'GetTemplateOutput', 'resultWrapper' => 'GetTemplateResult', ], 'errors' => [ [ 'shape' => 'ChangeSetNotFoundException', ], ], ], 'GetTemplateSummary' => [ 'name' => 'GetTemplateSummary', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTemplateSummaryInput', ], 'output' => [ 'shape' => 'GetTemplateSummaryOutput', 'resultWrapper' => 'GetTemplateSummaryResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], ], ], 'ImportStacksToStackSet' => [ 'name' => 'ImportStacksToStackSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ImportStacksToStackSetInput', ], 'output' => [ 'shape' => 'ImportStacksToStackSetOutput', 'resultWrapper' => 'ImportStacksToStackSetResult', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'StackSetNotFoundException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'OperationInProgressException', ], [ 'shape' => 'OperationIdAlreadyExistsException', ], [ 'shape' => 'StackNotFoundException', ], [ 'shape' => 'StaleRequestException', ], ], ], 'ListChangeSets' => [ 'name' => 'ListChangeSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListChangeSetsInput', ], 'output' => [ 'shape' => 'ListChangeSetsOutput', 'resultWrapper' => 'ListChangeSetsResult', ], ], 'ListExports' => [ 'name' => 'ListExports', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListExportsInput', ], 'output' => [ 'shape' => 'ListExportsOutput', 'resultWrapper' => 'ListExportsResult', ], ], 'ListGeneratedTemplates' => [ 'name' => 'ListGeneratedTemplates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListGeneratedTemplatesInput', ], 'output' => [ 'shape' => 'ListGeneratedTemplatesOutput', 'resultWrapper' => 'ListGeneratedTemplatesResult', ], ], 'ListHookResults' => [ 'name' => 'ListHookResults', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListHookResultsInput', ], 'output' => [ 'shape' => 'ListHookResultsOutput', 'resultWrapper' => 'ListHookResultsResult', ], 'errors' => [ [ 'shape' => 'HookResultNotFoundException', ], ], ], 'ListImports' => [ 'name' => 'ListImports', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListImportsInput', ], 'output' => [ 'shape' => 'ListImportsOutput', 'resultWrapper' => 'ListImportsResult', ], ], 'ListResourceScanRelatedResources' => [ 'name' => 'ListResourceScanRelatedResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResourceScanRelatedResourcesInput', ], 'output' => [ 'shape' => 'ListResourceScanRelatedResourcesOutput', 'resultWrapper' => 'ListResourceScanRelatedResourcesResult', ], 'errors' => [ [ 'shape' => 'ResourceScanNotFoundException', ], [ 'shape' => 'ResourceScanInProgressException', ], ], ], 'ListResourceScanResources' => [ 'name' => 'ListResourceScanResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResourceScanResourcesInput', ], 'output' => [ 'shape' => 'ListResourceScanResourcesOutput', 'resultWrapper' => 'ListResourceScanResourcesResult', ], 'errors' => [ [ 'shape' => 'ResourceScanNotFoundException', ], [ 'shape' => 'ResourceScanInProgressException', ], ], ], 'ListResourceScans' => [ 'name' => 'ListResourceScans', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResourceScansInput', ], 'output' => [ 'shape' => 'ListResourceScansOutput', 'resultWrapper' => 'ListResourceScansResult', ], ], 'ListStackInstanceResourceDrifts' => [ 'name' => 'ListStackInstanceResourceDrifts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStackInstanceResourceDriftsInput', ], 'output' => [ 'shape' => 'ListStackInstanceResourceDriftsOutput', 'resultWrapper' => 'ListStackInstanceResourceDriftsResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], [ 'shape' => 'StackInstanceNotFoundException', ], [ 'shape' => 'OperationNotFoundException', ], ], ], 'ListStackInstances' => [ 'name' => 'ListStackInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStackInstancesInput', ], 'output' => [ 'shape' => 'ListStackInstancesOutput', 'resultWrapper' => 'ListStackInstancesResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], ], ], 'ListStackRefactorActions' => [ 'name' => 'ListStackRefactorActions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStackRefactorActionsInput', ], 'output' => [ 'shape' => 'ListStackRefactorActionsOutput', 'resultWrapper' => 'ListStackRefactorActionsResult', ], ], 'ListStackRefactors' => [ 'name' => 'ListStackRefactors', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStackRefactorsInput', ], 'output' => [ 'shape' => 'ListStackRefactorsOutput', 'resultWrapper' => 'ListStackRefactorsResult', ], ], 'ListStackResources' => [ 'name' => 'ListStackResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStackResourcesInput', ], 'output' => [ 'shape' => 'ListStackResourcesOutput', 'resultWrapper' => 'ListStackResourcesResult', ], ], 'ListStackSetAutoDeploymentTargets' => [ 'name' => 'ListStackSetAutoDeploymentTargets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStackSetAutoDeploymentTargetsInput', ], 'output' => [ 'shape' => 'ListStackSetAutoDeploymentTargetsOutput', 'resultWrapper' => 'ListStackSetAutoDeploymentTargetsResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], ], ], 'ListStackSetOperationResults' => [ 'name' => 'ListStackSetOperationResults', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStackSetOperationResultsInput', ], 'output' => [ 'shape' => 'ListStackSetOperationResultsOutput', 'resultWrapper' => 'ListStackSetOperationResultsResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], [ 'shape' => 'OperationNotFoundException', ], ], ], 'ListStackSetOperations' => [ 'name' => 'ListStackSetOperations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStackSetOperationsInput', ], 'output' => [ 'shape' => 'ListStackSetOperationsOutput', 'resultWrapper' => 'ListStackSetOperationsResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], ], ], 'ListStackSets' => [ 'name' => 'ListStackSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStackSetsInput', ], 'output' => [ 'shape' => 'ListStackSetsOutput', 'resultWrapper' => 'ListStackSetsResult', ], ], 'ListStacks' => [ 'name' => 'ListStacks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStacksInput', ], 'output' => [ 'shape' => 'ListStacksOutput', 'resultWrapper' => 'ListStacksResult', ], ], 'ListTypeRegistrations' => [ 'name' => 'ListTypeRegistrations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTypeRegistrationsInput', ], 'output' => [ 'shape' => 'ListTypeRegistrationsOutput', 'resultWrapper' => 'ListTypeRegistrationsResult', ], 'errors' => [ [ 'shape' => 'CFNRegistryException', ], ], 'idempotent' => true, ], 'ListTypeVersions' => [ 'name' => 'ListTypeVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTypeVersionsInput', ], 'output' => [ 'shape' => 'ListTypeVersionsOutput', 'resultWrapper' => 'ListTypeVersionsResult', ], 'errors' => [ [ 'shape' => 'CFNRegistryException', ], ], 'idempotent' => true, ], 'ListTypes' => [ 'name' => 'ListTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTypesInput', ], 'output' => [ 'shape' => 'ListTypesOutput', 'resultWrapper' => 'ListTypesResult', ], 'errors' => [ [ 'shape' => 'CFNRegistryException', ], ], 'idempotent' => true, ], 'PublishType' => [ 'name' => 'PublishType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PublishTypeInput', ], 'output' => [ 'shape' => 'PublishTypeOutput', 'resultWrapper' => 'PublishTypeResult', ], 'errors' => [ [ 'shape' => 'CFNRegistryException', ], [ 'shape' => 'TypeNotFoundException', ], ], 'idempotent' => true, ], 'RecordHandlerProgress' => [ 'name' => 'RecordHandlerProgress', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RecordHandlerProgressInput', ], 'output' => [ 'shape' => 'RecordHandlerProgressOutput', 'resultWrapper' => 'RecordHandlerProgressResult', ], 'errors' => [ [ 'shape' => 'InvalidStateTransitionException', ], [ 'shape' => 'OperationStatusCheckFailedException', ], ], 'idempotent' => true, ], 'RegisterPublisher' => [ 'name' => 'RegisterPublisher', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterPublisherInput', ], 'output' => [ 'shape' => 'RegisterPublisherOutput', 'resultWrapper' => 'RegisterPublisherResult', ], 'errors' => [ [ 'shape' => 'CFNRegistryException', ], ], 'idempotent' => true, ], 'RegisterType' => [ 'name' => 'RegisterType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterTypeInput', ], 'output' => [ 'shape' => 'RegisterTypeOutput', 'resultWrapper' => 'RegisterTypeResult', ], 'errors' => [ [ 'shape' => 'CFNRegistryException', ], ], 'idempotent' => true, ], 'RollbackStack' => [ 'name' => 'RollbackStack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RollbackStackInput', ], 'output' => [ 'shape' => 'RollbackStackOutput', 'resultWrapper' => 'RollbackStackResult', ], 'errors' => [ [ 'shape' => 'TokenAlreadyExistsException', ], ], ], 'SetStackPolicy' => [ 'name' => 'SetStackPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetStackPolicyInput', ], ], 'SetTypeConfiguration' => [ 'name' => 'SetTypeConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetTypeConfigurationInput', ], 'output' => [ 'shape' => 'SetTypeConfigurationOutput', 'resultWrapper' => 'SetTypeConfigurationResult', ], 'errors' => [ [ 'shape' => 'CFNRegistryException', ], [ 'shape' => 'TypeNotFoundException', ], ], ], 'SetTypeDefaultVersion' => [ 'name' => 'SetTypeDefaultVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetTypeDefaultVersionInput', ], 'output' => [ 'shape' => 'SetTypeDefaultVersionOutput', 'resultWrapper' => 'SetTypeDefaultVersionResult', ], 'errors' => [ [ 'shape' => 'CFNRegistryException', ], [ 'shape' => 'TypeNotFoundException', ], ], 'idempotent' => true, ], 'SignalResource' => [ 'name' => 'SignalResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SignalResourceInput', ], ], 'StartResourceScan' => [ 'name' => 'StartResourceScan', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartResourceScanInput', ], 'output' => [ 'shape' => 'StartResourceScanOutput', 'resultWrapper' => 'StartResourceScanResult', ], 'errors' => [ [ 'shape' => 'ResourceScanInProgressException', ], [ 'shape' => 'ResourceScanLimitExceededException', ], ], ], 'StopStackSetOperation' => [ 'name' => 'StopStackSetOperation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopStackSetOperationInput', ], 'output' => [ 'shape' => 'StopStackSetOperationOutput', 'resultWrapper' => 'StopStackSetOperationResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], [ 'shape' => 'OperationNotFoundException', ], [ 'shape' => 'InvalidOperationException', ], ], ], 'TestType' => [ 'name' => 'TestType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TestTypeInput', ], 'output' => [ 'shape' => 'TestTypeOutput', 'resultWrapper' => 'TestTypeResult', ], 'errors' => [ [ 'shape' => 'CFNRegistryException', ], [ 'shape' => 'TypeNotFoundException', ], ], 'idempotent' => true, ], 'UpdateGeneratedTemplate' => [ 'name' => 'UpdateGeneratedTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateGeneratedTemplateInput', ], 'output' => [ 'shape' => 'UpdateGeneratedTemplateOutput', 'resultWrapper' => 'UpdateGeneratedTemplateResult', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'GeneratedTemplateNotFoundException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'UpdateStack' => [ 'name' => 'UpdateStack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateStackInput', ], 'output' => [ 'shape' => 'UpdateStackOutput', 'resultWrapper' => 'UpdateStackResult', ], 'errors' => [ [ 'shape' => 'InsufficientCapabilitiesException', ], [ 'shape' => 'TokenAlreadyExistsException', ], ], ], 'UpdateStackInstances' => [ 'name' => 'UpdateStackInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateStackInstancesInput', ], 'output' => [ 'shape' => 'UpdateStackInstancesOutput', 'resultWrapper' => 'UpdateStackInstancesResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], [ 'shape' => 'StackInstanceNotFoundException', ], [ 'shape' => 'OperationInProgressException', ], [ 'shape' => 'OperationIdAlreadyExistsException', ], [ 'shape' => 'StaleRequestException', ], [ 'shape' => 'InvalidOperationException', ], ], ], 'UpdateStackSet' => [ 'name' => 'UpdateStackSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateStackSetInput', ], 'output' => [ 'shape' => 'UpdateStackSetOutput', 'resultWrapper' => 'UpdateStackSetResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], [ 'shape' => 'OperationInProgressException', ], [ 'shape' => 'OperationIdAlreadyExistsException', ], [ 'shape' => 'StaleRequestException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'StackInstanceNotFoundException', ], ], ], 'UpdateTerminationProtection' => [ 'name' => 'UpdateTerminationProtection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTerminationProtectionInput', ], 'output' => [ 'shape' => 'UpdateTerminationProtectionOutput', 'resultWrapper' => 'UpdateTerminationProtectionResult', ], ], 'ValidateTemplate' => [ 'name' => 'ValidateTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ValidateTemplateInput', ], 'output' => [ 'shape' => 'ValidateTemplateOutput', 'resultWrapper' => 'ValidateTemplateResult', ], ], ], 'shapes' => [ 'AcceptTermsAndConditions' => [ 'type' => 'boolean', ], 'Account' => [ 'type' => 'string', 'pattern' => '^[0-9]{12}$', ], 'AccountFilterType' => [ 'type' => 'string', 'enum' => [ 'NONE', 'INTERSECTION', 'DIFFERENCE', 'UNION', ], ], 'AccountGateResult' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'AccountGateStatus', ], 'StatusReason' => [ 'shape' => 'AccountGateStatusReason', ], ], ], 'AccountGateStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'FAILED', 'SKIPPED', ], ], 'AccountGateStatusReason' => [ 'type' => 'string', ], 'AccountLimit' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'LimitName', ], 'Value' => [ 'shape' => 'LimitValue', ], ], ], 'AccountLimitList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountLimit', ], ], 'AccountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Account', ], ], 'AccountsUrl' => [ 'type' => 'string', 'max' => 5120, 'min' => 1, 'pattern' => '(s3://|http(s?)://).+', ], 'ActivateOrganizationsAccessInput' => [ 'type' => 'structure', 'members' => [], ], 'ActivateOrganizationsAccessOutput' => [ 'type' => 'structure', 'members' => [], ], 'ActivateTypeInput' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'ThirdPartyType', ], 'PublicTypeArn' => [ 'shape' => 'ThirdPartyTypeArn', ], 'PublisherId' => [ 'shape' => 'PublisherId', ], 'TypeName' => [ 'shape' => 'TypeName', ], 'TypeNameAlias' => [ 'shape' => 'TypeName', ], 'AutoUpdate' => [ 'shape' => 'AutoUpdate', ], 'LoggingConfig' => [ 'shape' => 'LoggingConfig', ], 'ExecutionRoleArn' => [ 'shape' => 'RoleArn', ], 'VersionBump' => [ 'shape' => 'VersionBump', ], 'MajorVersion' => [ 'shape' => 'MajorVersion', ], ], ], 'ActivateTypeOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PrivateTypeArn', ], ], ], 'AfterContext' => [ 'type' => 'string', ], 'AfterValue' => [ 'type' => 'string', ], 'AllowedValue' => [ 'type' => 'string', ], 'AllowedValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'AllowedValue', ], ], 'AlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'AlreadyExistsException', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Arn' => [ 'type' => 'string', ], 'AttributeChangeType' => [ 'type' => 'string', 'enum' => [ 'Add', 'Remove', 'Modify', ], ], 'AutoDeployment' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'AutoDeploymentNullable', ], 'RetainStacksOnAccountRemoval' => [ 'shape' => 'RetainStacksOnAccountRemovalNullable', ], ], ], 'AutoDeploymentNullable' => [ 'type' => 'boolean', ], 'AutoUpdate' => [ 'type' => 'boolean', ], 'BatchDescribeTypeConfigurationsError' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], 'TypeConfigurationIdentifier' => [ 'shape' => 'TypeConfigurationIdentifier', ], ], ], 'BatchDescribeTypeConfigurationsErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchDescribeTypeConfigurationsError', ], ], 'BatchDescribeTypeConfigurationsInput' => [ 'type' => 'structure', 'required' => [ 'TypeConfigurationIdentifiers', ], 'members' => [ 'TypeConfigurationIdentifiers' => [ 'shape' => 'TypeConfigurationIdentifiers', ], ], ], 'BatchDescribeTypeConfigurationsOutput' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'BatchDescribeTypeConfigurationsErrors', ], 'UnprocessedTypeConfigurations' => [ 'shape' => 'UnprocessedTypeConfigurations', ], 'TypeConfigurations' => [ 'shape' => 'TypeConfigurationDetailsList', ], ], ], 'BeforeContext' => [ 'type' => 'string', ], 'BeforeValue' => [ 'type' => 'string', ], 'BoxedInteger' => [ 'type' => 'integer', 'box' => true, ], 'BoxedMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'CFNRegistryException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'code' => 'CFNRegistryException', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'CallAs' => [ 'type' => 'string', 'enum' => [ 'SELF', 'DELEGATED_ADMIN', ], ], 'CancelUpdateStackInput' => [ 'type' => 'structure', 'required' => [ 'StackName', ], 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], ], ], 'Capabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'Capability', ], ], 'CapabilitiesReason' => [ 'type' => 'string', ], 'Capability' => [ 'type' => 'string', 'enum' => [ 'CAPABILITY_IAM', 'CAPABILITY_NAMED_IAM', 'CAPABILITY_AUTO_EXPAND', ], ], 'Category' => [ 'type' => 'string', 'enum' => [ 'REGISTERED', 'ACTIVATED', 'THIRD_PARTY', 'AWS_TYPES', ], ], 'CausingEntity' => [ 'type' => 'string', ], 'Change' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'ChangeType', ], 'HookInvocationCount' => [ 'shape' => 'HookInvocationCount', ], 'ResourceChange' => [ 'shape' => 'ResourceChange', ], ], ], 'ChangeAction' => [ 'type' => 'string', 'enum' => [ 'Add', 'Modify', 'Remove', 'Import', 'Dynamic', ], ], 'ChangeSetHook' => [ 'type' => 'structure', 'members' => [ 'InvocationPoint' => [ 'shape' => 'HookInvocationPoint', ], 'FailureMode' => [ 'shape' => 'HookFailureMode', ], 'TypeName' => [ 'shape' => 'HookTypeName', ], 'TypeVersionId' => [ 'shape' => 'HookTypeVersionId', ], 'TypeConfigurationVersionId' => [ 'shape' => 'HookTypeConfigurationVersionId', ], 'TargetDetails' => [ 'shape' => 'ChangeSetHookTargetDetails', ], ], ], 'ChangeSetHookResourceTargetDetails' => [ 'type' => 'structure', 'members' => [ 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'ResourceType' => [ 'shape' => 'HookTargetTypeName', ], 'ResourceAction' => [ 'shape' => 'ChangeAction', ], ], ], 'ChangeSetHookTargetDetails' => [ 'type' => 'structure', 'members' => [ 'TargetType' => [ 'shape' => 'HookTargetType', ], 'ResourceTargetDetails' => [ 'shape' => 'ChangeSetHookResourceTargetDetails', ], ], ], 'ChangeSetHooks' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChangeSetHook', ], ], 'ChangeSetHooksStatus' => [ 'type' => 'string', 'enum' => [ 'PLANNING', 'PLANNED', 'UNAVAILABLE', ], ], 'ChangeSetId' => [ 'type' => 'string', 'min' => 1, 'pattern' => 'arn:[-a-zA-Z0-9:/]*', ], 'ChangeSetName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z][-a-zA-Z0-9]*', ], 'ChangeSetNameOrId' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '[a-zA-Z][-a-zA-Z0-9]*|arn:[-a-zA-Z0-9:/]*', ], 'ChangeSetNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ChangeSetNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ChangeSetStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_PENDING', 'CREATE_IN_PROGRESS', 'CREATE_COMPLETE', 'DELETE_PENDING', 'DELETE_IN_PROGRESS', 'DELETE_COMPLETE', 'DELETE_FAILED', 'FAILED', ], ], 'ChangeSetStatusReason' => [ 'type' => 'string', ], 'ChangeSetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChangeSetSummary', ], ], 'ChangeSetSummary' => [ 'type' => 'structure', 'members' => [ 'StackId' => [ 'shape' => 'StackId', ], 'StackName' => [ 'shape' => 'StackName', ], 'ChangeSetId' => [ 'shape' => 'ChangeSetId', ], 'ChangeSetName' => [ 'shape' => 'ChangeSetName', ], 'ExecutionStatus' => [ 'shape' => 'ExecutionStatus', ], 'Status' => [ 'shape' => 'ChangeSetStatus', ], 'StatusReason' => [ 'shape' => 'ChangeSetStatusReason', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'Description' => [ 'shape' => 'Description', ], 'IncludeNestedStacks' => [ 'shape' => 'IncludeNestedStacks', ], 'ParentChangeSetId' => [ 'shape' => 'ChangeSetId', ], 'RootChangeSetId' => [ 'shape' => 'ChangeSetId', ], 'ImportExistingResources' => [ 'shape' => 'ImportExistingResources', ], ], ], 'ChangeSetType' => [ 'type' => 'string', 'enum' => [ 'CREATE', 'UPDATE', 'IMPORT', ], ], 'ChangeSource' => [ 'type' => 'string', 'enum' => [ 'ResourceReference', 'ParameterReference', 'ResourceAttribute', 'DirectModification', 'Automatic', ], ], 'ChangeType' => [ 'type' => 'string', 'enum' => [ 'Resource', ], ], 'Changes' => [ 'type' => 'list', 'member' => [ 'shape' => 'Change', ], ], 'ClientRequestToken' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9][-a-zA-Z0-9]*', ], 'ClientToken' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'ConcurrencyMode' => [ 'type' => 'string', 'enum' => [ 'STRICT_FAILURE_TOLERANCE', 'SOFT_FAILURE_TOLERANCE', ], ], 'ConcurrentResourcesLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ConcurrentResourcesLimitExceeded', 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'ConfigurationSchema' => [ 'type' => 'string', 'max' => 60000, 'min' => 1, 'pattern' => '[\\s\\S]+', ], 'ConnectionArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => 'arn:aws(-[\\w]+)*:.+:.+:[0-9]{12}:.+', ], 'ContinueUpdateRollbackInput' => [ 'type' => 'structure', 'required' => [ 'StackName', ], 'members' => [ 'StackName' => [ 'shape' => 'StackNameOrId', ], 'RoleARN' => [ 'shape' => 'RoleARN', ], 'ResourcesToSkip' => [ 'shape' => 'ResourcesToSkip', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], ], ], 'ContinueUpdateRollbackOutput' => [ 'type' => 'structure', 'members' => [], ], 'CreateChangeSetInput' => [ 'type' => 'structure', 'required' => [ 'StackName', 'ChangeSetName', ], 'members' => [ 'StackName' => [ 'shape' => 'StackNameOrId', ], 'TemplateBody' => [ 'shape' => 'TemplateBody', ], 'TemplateURL' => [ 'shape' => 'TemplateURL', ], 'UsePreviousTemplate' => [ 'shape' => 'UsePreviousTemplate', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'ResourceTypes' => [ 'shape' => 'ResourceTypes', ], 'RoleARN' => [ 'shape' => 'RoleARN', ], 'RollbackConfiguration' => [ 'shape' => 'RollbackConfiguration', ], 'NotificationARNs' => [ 'shape' => 'NotificationARNs', ], 'Tags' => [ 'shape' => 'Tags', ], 'ChangeSetName' => [ 'shape' => 'ChangeSetName', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], 'Description' => [ 'shape' => 'Description', ], 'ChangeSetType' => [ 'shape' => 'ChangeSetType', ], 'ResourcesToImport' => [ 'shape' => 'ResourcesToImport', ], 'IncludeNestedStacks' => [ 'shape' => 'IncludeNestedStacks', ], 'OnStackFailure' => [ 'shape' => 'OnStackFailure', ], 'ImportExistingResources' => [ 'shape' => 'ImportExistingResources', ], ], ], 'CreateChangeSetOutput' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ChangeSetId', ], 'StackId' => [ 'shape' => 'StackId', ], ], ], 'CreateGeneratedTemplateInput' => [ 'type' => 'structure', 'required' => [ 'GeneratedTemplateName', ], 'members' => [ 'Resources' => [ 'shape' => 'ResourceDefinitions', ], 'GeneratedTemplateName' => [ 'shape' => 'GeneratedTemplateName', ], 'StackName' => [ 'shape' => 'StackName', ], 'TemplateConfiguration' => [ 'shape' => 'TemplateConfiguration', ], ], ], 'CreateGeneratedTemplateOutput' => [ 'type' => 'structure', 'members' => [ 'GeneratedTemplateId' => [ 'shape' => 'GeneratedTemplateId', ], ], ], 'CreateStackInput' => [ 'type' => 'structure', 'required' => [ 'StackName', ], 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'TemplateBody' => [ 'shape' => 'TemplateBody', ], 'TemplateURL' => [ 'shape' => 'TemplateURL', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'DisableRollback' => [ 'shape' => 'DisableRollback', ], 'RollbackConfiguration' => [ 'shape' => 'RollbackConfiguration', ], 'TimeoutInMinutes' => [ 'shape' => 'TimeoutMinutes', ], 'NotificationARNs' => [ 'shape' => 'NotificationARNs', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'ResourceTypes' => [ 'shape' => 'ResourceTypes', ], 'RoleARN' => [ 'shape' => 'RoleARN', ], 'OnFailure' => [ 'shape' => 'OnFailure', ], 'StackPolicyBody' => [ 'shape' => 'StackPolicyBody', ], 'StackPolicyURL' => [ 'shape' => 'StackPolicyURL', ], 'Tags' => [ 'shape' => 'Tags', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'EnableTerminationProtection' => [ 'shape' => 'EnableTerminationProtection', ], 'RetainExceptOnCreate' => [ 'shape' => 'RetainExceptOnCreate', ], ], ], 'CreateStackInstancesInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', 'Regions', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'Accounts' => [ 'shape' => 'AccountList', ], 'DeploymentTargets' => [ 'shape' => 'DeploymentTargets', ], 'Regions' => [ 'shape' => 'RegionList', ], 'ParameterOverrides' => [ 'shape' => 'Parameters', ], 'OperationPreferences' => [ 'shape' => 'StackSetOperationPreferences', ], 'OperationId' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'CallAs' => [ 'shape' => 'CallAs', ], ], ], 'CreateStackInstancesOutput' => [ 'type' => 'structure', 'members' => [ 'OperationId' => [ 'shape' => 'ClientRequestToken', ], ], ], 'CreateStackOutput' => [ 'type' => 'structure', 'members' => [ 'StackId' => [ 'shape' => 'StackId', ], ], ], 'CreateStackRefactorInput' => [ 'type' => 'structure', 'required' => [ 'StackDefinitions', ], 'members' => [ 'Description' => [ 'shape' => 'Description', ], 'EnableStackCreation' => [ 'shape' => 'EnableStackCreation', ], 'ResourceMappings' => [ 'shape' => 'ResourceMappings', ], 'StackDefinitions' => [ 'shape' => 'StackDefinitions', ], ], ], 'CreateStackRefactorOutput' => [ 'type' => 'structure', 'required' => [ 'StackRefactorId', ], 'members' => [ 'StackRefactorId' => [ 'shape' => 'StackRefactorId', ], ], ], 'CreateStackSetInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'Description' => [ 'shape' => 'Description', ], 'TemplateBody' => [ 'shape' => 'TemplateBody', ], 'TemplateURL' => [ 'shape' => 'TemplateURL', ], 'StackId' => [ 'shape' => 'StackId', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'Tags' => [ 'shape' => 'Tags', ], 'AdministrationRoleARN' => [ 'shape' => 'RoleARN', ], 'ExecutionRoleName' => [ 'shape' => 'ExecutionRoleName', ], 'PermissionModel' => [ 'shape' => 'PermissionModels', ], 'AutoDeployment' => [ 'shape' => 'AutoDeployment', ], 'CallAs' => [ 'shape' => 'CallAs', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'ManagedExecution' => [ 'shape' => 'ManagedExecution', ], ], ], 'CreateStackSetOutput' => [ 'type' => 'structure', 'members' => [ 'StackSetId' => [ 'shape' => 'StackSetId', ], ], ], 'CreatedButModifiedException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CreatedButModifiedException', 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreationTime' => [ 'type' => 'timestamp', ], 'DeactivateOrganizationsAccessInput' => [ 'type' => 'structure', 'members' => [], ], 'DeactivateOrganizationsAccessOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeactivateTypeInput' => [ 'type' => 'structure', 'members' => [ 'TypeName' => [ 'shape' => 'TypeName', ], 'Type' => [ 'shape' => 'ThirdPartyType', ], 'Arn' => [ 'shape' => 'PrivateTypeArn', ], ], ], 'DeactivateTypeOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteChangeSetInput' => [ 'type' => 'structure', 'required' => [ 'ChangeSetName', ], 'members' => [ 'ChangeSetName' => [ 'shape' => 'ChangeSetNameOrId', ], 'StackName' => [ 'shape' => 'StackNameOrId', ], ], ], 'DeleteChangeSetOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteGeneratedTemplateInput' => [ 'type' => 'structure', 'required' => [ 'GeneratedTemplateName', ], 'members' => [ 'GeneratedTemplateName' => [ 'shape' => 'GeneratedTemplateName', ], ], ], 'DeleteStackInput' => [ 'type' => 'structure', 'required' => [ 'StackName', ], 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'RetainResources' => [ 'shape' => 'RetainResources', ], 'RoleARN' => [ 'shape' => 'RoleARN', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'DeletionMode' => [ 'shape' => 'DeletionMode', ], ], ], 'DeleteStackInstancesInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', 'Regions', 'RetainStacks', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'Accounts' => [ 'shape' => 'AccountList', ], 'DeploymentTargets' => [ 'shape' => 'DeploymentTargets', ], 'Regions' => [ 'shape' => 'RegionList', ], 'OperationPreferences' => [ 'shape' => 'StackSetOperationPreferences', ], 'RetainStacks' => [ 'shape' => 'RetainStacks', ], 'OperationId' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'CallAs' => [ 'shape' => 'CallAs', ], ], ], 'DeleteStackInstancesOutput' => [ 'type' => 'structure', 'members' => [ 'OperationId' => [ 'shape' => 'ClientRequestToken', ], ], ], 'DeleteStackSetInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'CallAs' => [ 'shape' => 'CallAs', ], ], ], 'DeleteStackSetOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeletionMode' => [ 'type' => 'string', 'enum' => [ 'STANDARD', 'FORCE_DELETE_STACK', ], ], 'DeletionTime' => [ 'type' => 'timestamp', ], 'DeploymentTargets' => [ 'type' => 'structure', 'members' => [ 'Accounts' => [ 'shape' => 'AccountList', ], 'AccountsUrl' => [ 'shape' => 'AccountsUrl', ], 'OrganizationalUnitIds' => [ 'shape' => 'OrganizationalUnitIdList', ], 'AccountFilterType' => [ 'shape' => 'AccountFilterType', ], ], ], 'DeprecatedStatus' => [ 'type' => 'string', 'enum' => [ 'LIVE', 'DEPRECATED', ], ], 'DeregisterTypeInput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PrivateTypeArn', ], 'Type' => [ 'shape' => 'RegistryType', ], 'TypeName' => [ 'shape' => 'TypeName', ], 'VersionId' => [ 'shape' => 'TypeVersionId', ], ], ], 'DeregisterTypeOutput' => [ 'type' => 'structure', 'members' => [], ], 'DescribeAccountLimitsInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeAccountLimitsOutput' => [ 'type' => 'structure', 'members' => [ 'AccountLimits' => [ 'shape' => 'AccountLimitList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeChangeSetHooksInput' => [ 'type' => 'structure', 'required' => [ 'ChangeSetName', ], 'members' => [ 'ChangeSetName' => [ 'shape' => 'ChangeSetNameOrId', ], 'StackName' => [ 'shape' => 'StackNameOrId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], ], ], 'DescribeChangeSetHooksOutput' => [ 'type' => 'structure', 'members' => [ 'ChangeSetId' => [ 'shape' => 'ChangeSetId', ], 'ChangeSetName' => [ 'shape' => 'ChangeSetName', ], 'Hooks' => [ 'shape' => 'ChangeSetHooks', ], 'Status' => [ 'shape' => 'ChangeSetHooksStatus', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'StackId' => [ 'shape' => 'StackId', ], 'StackName' => [ 'shape' => 'StackName', ], ], ], 'DescribeChangeSetInput' => [ 'type' => 'structure', 'required' => [ 'ChangeSetName', ], 'members' => [ 'ChangeSetName' => [ 'shape' => 'ChangeSetNameOrId', ], 'StackName' => [ 'shape' => 'StackNameOrId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'IncludePropertyValues' => [ 'shape' => 'IncludePropertyValues', ], ], ], 'DescribeChangeSetOutput' => [ 'type' => 'structure', 'members' => [ 'ChangeSetName' => [ 'shape' => 'ChangeSetName', ], 'ChangeSetId' => [ 'shape' => 'ChangeSetId', ], 'StackId' => [ 'shape' => 'StackId', ], 'StackName' => [ 'shape' => 'StackName', ], 'Description' => [ 'shape' => 'Description', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'ExecutionStatus' => [ 'shape' => 'ExecutionStatus', ], 'Status' => [ 'shape' => 'ChangeSetStatus', ], 'StatusReason' => [ 'shape' => 'ChangeSetStatusReason', ], 'NotificationARNs' => [ 'shape' => 'NotificationARNs', ], 'RollbackConfiguration' => [ 'shape' => 'RollbackConfiguration', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'Tags' => [ 'shape' => 'Tags', ], 'Changes' => [ 'shape' => 'Changes', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'IncludeNestedStacks' => [ 'shape' => 'IncludeNestedStacks', ], 'ParentChangeSetId' => [ 'shape' => 'ChangeSetId', ], 'RootChangeSetId' => [ 'shape' => 'ChangeSetId', ], 'OnStackFailure' => [ 'shape' => 'OnStackFailure', ], 'ImportExistingResources' => [ 'shape' => 'ImportExistingResources', ], ], ], 'DescribeGeneratedTemplateInput' => [ 'type' => 'structure', 'required' => [ 'GeneratedTemplateName', ], 'members' => [ 'GeneratedTemplateName' => [ 'shape' => 'GeneratedTemplateName', ], ], ], 'DescribeGeneratedTemplateOutput' => [ 'type' => 'structure', 'members' => [ 'GeneratedTemplateId' => [ 'shape' => 'GeneratedTemplateId', ], 'GeneratedTemplateName' => [ 'shape' => 'GeneratedTemplateName', ], 'Resources' => [ 'shape' => 'ResourceDetails', ], 'Status' => [ 'shape' => 'GeneratedTemplateStatus', ], 'StatusReason' => [ 'shape' => 'TemplateStatusReason', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'LastUpdatedTime' => [ 'shape' => 'LastUpdatedTime', ], 'Progress' => [ 'shape' => 'TemplateProgress', ], 'StackId' => [ 'shape' => 'StackId', ], 'TemplateConfiguration' => [ 'shape' => 'TemplateConfiguration', ], 'TotalWarnings' => [ 'shape' => 'TotalWarnings', ], ], ], 'DescribeOrganizationsAccessInput' => [ 'type' => 'structure', 'members' => [ 'CallAs' => [ 'shape' => 'CallAs', ], ], ], 'DescribeOrganizationsAccessOutput' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'OrganizationStatus', ], ], ], 'DescribePublisherInput' => [ 'type' => 'structure', 'members' => [ 'PublisherId' => [ 'shape' => 'PublisherId', ], ], ], 'DescribePublisherOutput' => [ 'type' => 'structure', 'members' => [ 'PublisherId' => [ 'shape' => 'PublisherId', ], 'PublisherStatus' => [ 'shape' => 'PublisherStatus', ], 'IdentityProvider' => [ 'shape' => 'IdentityProvider', ], 'PublisherProfile' => [ 'shape' => 'PublisherProfile', ], ], ], 'DescribeResourceScanInput' => [ 'type' => 'structure', 'required' => [ 'ResourceScanId', ], 'members' => [ 'ResourceScanId' => [ 'shape' => 'ResourceScanId', ], ], ], 'DescribeResourceScanOutput' => [ 'type' => 'structure', 'members' => [ 'ResourceScanId' => [ 'shape' => 'ResourceScanId', ], 'Status' => [ 'shape' => 'ResourceScanStatus', ], 'StatusReason' => [ 'shape' => 'ResourceScanStatusReason', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'PercentageCompleted' => [ 'shape' => 'PercentageCompleted', ], 'ResourceTypes' => [ 'shape' => 'ResourceTypes', ], 'ResourcesScanned' => [ 'shape' => 'ResourcesScanned', ], 'ResourcesRead' => [ 'shape' => 'ResourcesRead', ], 'ScanFilters' => [ 'shape' => 'ScanFilters', ], ], ], 'DescribeStackDriftDetectionStatusInput' => [ 'type' => 'structure', 'required' => [ 'StackDriftDetectionId', ], 'members' => [ 'StackDriftDetectionId' => [ 'shape' => 'StackDriftDetectionId', ], ], ], 'DescribeStackDriftDetectionStatusOutput' => [ 'type' => 'structure', 'required' => [ 'StackId', 'StackDriftDetectionId', 'DetectionStatus', 'Timestamp', ], 'members' => [ 'StackId' => [ 'shape' => 'StackId', ], 'StackDriftDetectionId' => [ 'shape' => 'StackDriftDetectionId', ], 'StackDriftStatus' => [ 'shape' => 'StackDriftStatus', ], 'DetectionStatus' => [ 'shape' => 'StackDriftDetectionStatus', ], 'DetectionStatusReason' => [ 'shape' => 'StackDriftDetectionStatusReason', ], 'DriftedStackResourceCount' => [ 'shape' => 'BoxedInteger', ], 'Timestamp' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeStackEventsInput' => [ 'type' => 'structure', 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeStackEventsOutput' => [ 'type' => 'structure', 'members' => [ 'StackEvents' => [ 'shape' => 'StackEvents', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeStackInstanceInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', 'StackInstanceAccount', 'StackInstanceRegion', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'StackInstanceAccount' => [ 'shape' => 'Account', ], 'StackInstanceRegion' => [ 'shape' => 'Region', ], 'CallAs' => [ 'shape' => 'CallAs', ], ], ], 'DescribeStackInstanceOutput' => [ 'type' => 'structure', 'members' => [ 'StackInstance' => [ 'shape' => 'StackInstance', ], ], ], 'DescribeStackRefactorInput' => [ 'type' => 'structure', 'required' => [ 'StackRefactorId', ], 'members' => [ 'StackRefactorId' => [ 'shape' => 'StackRefactorId', ], ], ], 'DescribeStackRefactorOutput' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'Description', ], 'StackRefactorId' => [ 'shape' => 'StackRefactorId', ], 'StackIds' => [ 'shape' => 'StackIds', ], 'ExecutionStatus' => [ 'shape' => 'StackRefactorExecutionStatus', ], 'ExecutionStatusReason' => [ 'shape' => 'ExecutionStatusReason', ], 'Status' => [ 'shape' => 'StackRefactorStatus', ], 'StatusReason' => [ 'shape' => 'StackRefactorStatusReason', ], ], ], 'DescribeStackResourceDriftsInput' => [ 'type' => 'structure', 'required' => [ 'StackName', ], 'members' => [ 'StackName' => [ 'shape' => 'StackNameOrId', ], 'StackResourceDriftStatusFilters' => [ 'shape' => 'StackResourceDriftStatusFilters', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'BoxedMaxResults', ], ], ], 'DescribeStackResourceDriftsOutput' => [ 'type' => 'structure', 'required' => [ 'StackResourceDrifts', ], 'members' => [ 'StackResourceDrifts' => [ 'shape' => 'StackResourceDrifts', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeStackResourceInput' => [ 'type' => 'structure', 'required' => [ 'StackName', 'LogicalResourceId', ], 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], ], ], 'DescribeStackResourceOutput' => [ 'type' => 'structure', 'members' => [ 'StackResourceDetail' => [ 'shape' => 'StackResourceDetail', ], ], ], 'DescribeStackResourcesInput' => [ 'type' => 'structure', 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'PhysicalResourceId' => [ 'shape' => 'PhysicalResourceId', ], ], ], 'DescribeStackResourcesOutput' => [ 'type' => 'structure', 'members' => [ 'StackResources' => [ 'shape' => 'StackResources', ], ], ], 'DescribeStackSetInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'CallAs' => [ 'shape' => 'CallAs', ], ], ], 'DescribeStackSetOperationInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', 'OperationId', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'OperationId' => [ 'shape' => 'ClientRequestToken', ], 'CallAs' => [ 'shape' => 'CallAs', ], ], ], 'DescribeStackSetOperationOutput' => [ 'type' => 'structure', 'members' => [ 'StackSetOperation' => [ 'shape' => 'StackSetOperation', ], ], ], 'DescribeStackSetOutput' => [ 'type' => 'structure', 'members' => [ 'StackSet' => [ 'shape' => 'StackSet', ], ], ], 'DescribeStacksInput' => [ 'type' => 'structure', 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeStacksOutput' => [ 'type' => 'structure', 'members' => [ 'Stacks' => [ 'shape' => 'Stacks', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeTypeInput' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'RegistryType', ], 'TypeName' => [ 'shape' => 'TypeName', ], 'Arn' => [ 'shape' => 'TypeArn', ], 'VersionId' => [ 'shape' => 'TypeVersionId', ], 'PublisherId' => [ 'shape' => 'PublisherId', ], 'PublicVersionNumber' => [ 'shape' => 'PublicVersionNumber', ], ], ], 'DescribeTypeOutput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'TypeArn', ], 'Type' => [ 'shape' => 'RegistryType', ], 'TypeName' => [ 'shape' => 'TypeName', ], 'DefaultVersionId' => [ 'shape' => 'TypeVersionId', ], 'IsDefaultVersion' => [ 'shape' => 'IsDefaultVersion', ], 'TypeTestsStatus' => [ 'shape' => 'TypeTestsStatus', ], 'TypeTestsStatusDescription' => [ 'shape' => 'TypeTestsStatusDescription', ], 'Description' => [ 'shape' => 'Description', ], 'Schema' => [ 'shape' => 'TypeSchema', ], 'ProvisioningType' => [ 'shape' => 'ProvisioningType', ], 'DeprecatedStatus' => [ 'shape' => 'DeprecatedStatus', ], 'LoggingConfig' => [ 'shape' => 'LoggingConfig', ], 'RequiredActivatedTypes' => [ 'shape' => 'RequiredActivatedTypes', ], 'ExecutionRoleArn' => [ 'shape' => 'RoleArn', ], 'Visibility' => [ 'shape' => 'Visibility', ], 'SourceUrl' => [ 'shape' => 'OptionalSecureUrl', ], 'DocumentationUrl' => [ 'shape' => 'OptionalSecureUrl', ], 'LastUpdated' => [ 'shape' => 'Timestamp', ], 'TimeCreated' => [ 'shape' => 'Timestamp', ], 'ConfigurationSchema' => [ 'shape' => 'ConfigurationSchema', ], 'PublisherId' => [ 'shape' => 'PublisherId', ], 'OriginalTypeName' => [ 'shape' => 'TypeName', ], 'OriginalTypeArn' => [ 'shape' => 'TypeArn', ], 'PublicVersionNumber' => [ 'shape' => 'PublicVersionNumber', ], 'LatestPublicVersion' => [ 'shape' => 'PublicVersionNumber', ], 'IsActivated' => [ 'shape' => 'IsActivated', ], 'AutoUpdate' => [ 'shape' => 'AutoUpdate', ], ], ], 'DescribeTypeRegistrationInput' => [ 'type' => 'structure', 'required' => [ 'RegistrationToken', ], 'members' => [ 'RegistrationToken' => [ 'shape' => 'RegistrationToken', ], ], ], 'DescribeTypeRegistrationOutput' => [ 'type' => 'structure', 'members' => [ 'ProgressStatus' => [ 'shape' => 'RegistrationStatus', ], 'Description' => [ 'shape' => 'Description', ], 'TypeArn' => [ 'shape' => 'TypeArn', ], 'TypeVersionArn' => [ 'shape' => 'TypeArn', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'DetailedStatus' => [ 'type' => 'string', 'enum' => [ 'CONFIGURATION_COMPLETE', 'VALIDATION_FAILED', ], ], 'DetectStackDriftInput' => [ 'type' => 'structure', 'required' => [ 'StackName', ], 'members' => [ 'StackName' => [ 'shape' => 'StackNameOrId', ], 'LogicalResourceIds' => [ 'shape' => 'LogicalResourceIds', ], ], ], 'DetectStackDriftOutput' => [ 'type' => 'structure', 'required' => [ 'StackDriftDetectionId', ], 'members' => [ 'StackDriftDetectionId' => [ 'shape' => 'StackDriftDetectionId', ], ], ], 'DetectStackResourceDriftInput' => [ 'type' => 'structure', 'required' => [ 'StackName', 'LogicalResourceId', ], 'members' => [ 'StackName' => [ 'shape' => 'StackNameOrId', ], 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], ], ], 'DetectStackResourceDriftOutput' => [ 'type' => 'structure', 'required' => [ 'StackResourceDrift', ], 'members' => [ 'StackResourceDrift' => [ 'shape' => 'StackResourceDrift', ], ], ], 'DetectStackSetDriftInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetNameOrId', ], 'OperationPreferences' => [ 'shape' => 'StackSetOperationPreferences', ], 'OperationId' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'CallAs' => [ 'shape' => 'CallAs', ], ], ], 'DetectStackSetDriftOutput' => [ 'type' => 'structure', 'members' => [ 'OperationId' => [ 'shape' => 'ClientRequestToken', ], ], ], 'DetectionReason' => [ 'type' => 'string', ], 'DifferenceType' => [ 'type' => 'string', 'enum' => [ 'ADD', 'REMOVE', 'NOT_EQUAL', ], ], 'DisableRollback' => [ 'type' => 'boolean', ], 'DriftedStackInstancesCount' => [ 'type' => 'integer', 'min' => 0, ], 'EnableStackCreation' => [ 'type' => 'boolean', ], 'EnableTerminationProtection' => [ 'type' => 'boolean', ], 'ErrorCode' => [ 'type' => 'string', 'max' => 3, 'min' => 3, ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'EstimateTemplateCostInput' => [ 'type' => 'structure', 'members' => [ 'TemplateBody' => [ 'shape' => 'TemplateBody', ], 'TemplateURL' => [ 'shape' => 'TemplateURL', ], 'Parameters' => [ 'shape' => 'Parameters', ], ], ], 'EstimateTemplateCostOutput' => [ 'type' => 'structure', 'members' => [ 'Url' => [ 'shape' => 'Url', ], ], ], 'EvaluationType' => [ 'type' => 'string', 'enum' => [ 'Static', 'Dynamic', ], ], 'EventId' => [ 'type' => 'string', ], 'ExecuteChangeSetInput' => [ 'type' => 'structure', 'required' => [ 'ChangeSetName', ], 'members' => [ 'ChangeSetName' => [ 'shape' => 'ChangeSetNameOrId', ], 'StackName' => [ 'shape' => 'StackNameOrId', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'DisableRollback' => [ 'shape' => 'DisableRollback', ], 'RetainExceptOnCreate' => [ 'shape' => 'RetainExceptOnCreate', ], ], ], 'ExecuteChangeSetOutput' => [ 'type' => 'structure', 'members' => [], ], 'ExecuteStackRefactorInput' => [ 'type' => 'structure', 'required' => [ 'StackRefactorId', ], 'members' => [ 'StackRefactorId' => [ 'shape' => 'StackRefactorId', ], ], ], 'ExecutionRoleName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z_0-9+=,.@-]+', ], 'ExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'UNAVAILABLE', 'AVAILABLE', 'EXECUTE_IN_PROGRESS', 'EXECUTE_COMPLETE', 'EXECUTE_FAILED', 'OBSOLETE', ], ], 'ExecutionStatusReason' => [ 'type' => 'string', ], 'Export' => [ 'type' => 'structure', 'members' => [ 'ExportingStackId' => [ 'shape' => 'StackId', ], 'Name' => [ 'shape' => 'ExportName', ], 'Value' => [ 'shape' => 'ExportValue', ], ], ], 'ExportName' => [ 'type' => 'string', ], 'ExportValue' => [ 'type' => 'string', ], 'Exports' => [ 'type' => 'list', 'member' => [ 'shape' => 'Export', ], ], 'FailedStackInstancesCount' => [ 'type' => 'integer', 'min' => 0, ], 'FailureToleranceCount' => [ 'type' => 'integer', 'min' => 0, ], 'FailureTolerancePercentage' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'GeneratedTemplateDeletionPolicy' => [ 'type' => 'string', 'enum' => [ 'DELETE', 'RETAIN', ], ], 'GeneratedTemplateId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'GeneratedTemplateName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'GeneratedTemplateNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'GeneratedTemplateNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'GeneratedTemplateResourceStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'IN_PROGRESS', 'FAILED', 'COMPLETE', ], ], 'GeneratedTemplateStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_PENDING', 'UPDATE_PENDING', 'DELETE_PENDING', 'CREATE_IN_PROGRESS', 'UPDATE_IN_PROGRESS', 'DELETE_IN_PROGRESS', 'FAILED', 'COMPLETE', ], ], 'GeneratedTemplateUpdateReplacePolicy' => [ 'type' => 'string', 'enum' => [ 'DELETE', 'RETAIN', ], ], 'GetGeneratedTemplateInput' => [ 'type' => 'structure', 'required' => [ 'GeneratedTemplateName', ], 'members' => [ 'Format' => [ 'shape' => 'TemplateFormat', ], 'GeneratedTemplateName' => [ 'shape' => 'GeneratedTemplateName', ], ], ], 'GetGeneratedTemplateOutput' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'GeneratedTemplateStatus', ], 'TemplateBody' => [ 'shape' => 'TemplateBody', ], ], ], 'GetStackPolicyInput' => [ 'type' => 'structure', 'required' => [ 'StackName', ], 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], ], ], 'GetStackPolicyOutput' => [ 'type' => 'structure', 'members' => [ 'StackPolicyBody' => [ 'shape' => 'StackPolicyBody', ], ], ], 'GetTemplateInput' => [ 'type' => 'structure', 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'ChangeSetName' => [ 'shape' => 'ChangeSetNameOrId', ], 'TemplateStage' => [ 'shape' => 'TemplateStage', ], ], ], 'GetTemplateOutput' => [ 'type' => 'structure', 'members' => [ 'TemplateBody' => [ 'shape' => 'TemplateBody', ], 'StagesAvailable' => [ 'shape' => 'StageList', ], ], ], 'GetTemplateSummaryInput' => [ 'type' => 'structure', 'members' => [ 'TemplateBody' => [ 'shape' => 'TemplateBody', ], 'TemplateURL' => [ 'shape' => 'TemplateURL', ], 'StackName' => [ 'shape' => 'StackNameOrId', ], 'StackSetName' => [ 'shape' => 'StackSetNameOrId', ], 'CallAs' => [ 'shape' => 'CallAs', ], 'TemplateSummaryConfig' => [ 'shape' => 'TemplateSummaryConfig', ], ], ], 'GetTemplateSummaryOutput' => [ 'type' => 'structure', 'members' => [ 'Parameters' => [ 'shape' => 'ParameterDeclarations', ], 'Description' => [ 'shape' => 'Description', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'CapabilitiesReason' => [ 'shape' => 'CapabilitiesReason', ], 'ResourceTypes' => [ 'shape' => 'ResourceTypes', ], 'Version' => [ 'shape' => 'Version', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'DeclaredTransforms' => [ 'shape' => 'TransformsList', ], 'ResourceIdentifierSummaries' => [ 'shape' => 'ResourceIdentifierSummaries', ], 'Warnings' => [ 'shape' => 'Warnings', ], ], ], 'HandlerErrorCode' => [ 'type' => 'string', 'enum' => [ 'NotUpdatable', 'InvalidRequest', 'AccessDenied', 'InvalidCredentials', 'AlreadyExists', 'NotFound', 'ResourceConflict', 'Throttling', 'ServiceLimitExceeded', 'NotStabilized', 'GeneralServiceException', 'ServiceInternalError', 'NetworkFailure', 'InternalFailure', 'InvalidTypeConfiguration', 'HandlerInternalFailure', 'NonCompliant', 'Unknown', 'UnsupportedTarget', ], ], 'HookFailureMode' => [ 'type' => 'string', 'enum' => [ 'FAIL', 'WARN', ], ], 'HookInvocationCount' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'HookInvocationPoint' => [ 'type' => 'string', 'enum' => [ 'PRE_PROVISION', ], ], 'HookResultId' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '[a-zA-Z][-a-zA-Z0-9]*|arn:[-a-zA-Z0-9:/]*|^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', ], 'HookResultNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'HookResultNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'HookResultSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'HookResultSummary', ], ], 'HookResultSummary' => [ 'type' => 'structure', 'members' => [ 'InvocationPoint' => [ 'shape' => 'HookInvocationPoint', ], 'FailureMode' => [ 'shape' => 'HookFailureMode', ], 'TypeName' => [ 'shape' => 'HookTypeName', ], 'TypeVersionId' => [ 'shape' => 'HookTypeVersionId', ], 'TypeConfigurationVersionId' => [ 'shape' => 'HookTypeConfigurationVersionId', ], 'Status' => [ 'shape' => 'HookStatus', ], 'HookStatusReason' => [ 'shape' => 'HookStatusReason', ], ], ], 'HookStatus' => [ 'type' => 'string', 'enum' => [ 'HOOK_IN_PROGRESS', 'HOOK_COMPLETE_SUCCEEDED', 'HOOK_COMPLETE_FAILED', 'HOOK_FAILED', ], ], 'HookStatusReason' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'HookTargetType' => [ 'type' => 'string', 'enum' => [ 'RESOURCE', ], ], 'HookTargetTypeName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[a-zA-Z0-9]{2,64}::[a-zA-Z0-9]{2,64}::[a-zA-Z0-9]{2,64}$', ], 'HookType' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'HookTypeConfigurationVersionId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[A-Za-z0-9-]+', ], 'HookTypeName' => [ 'type' => 'string', 'max' => 196, 'min' => 10, ], 'HookTypeVersionId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[A-Za-z0-9-]+', ], 'IdentityProvider' => [ 'type' => 'string', 'enum' => [ 'AWS_Marketplace', 'GitHub', 'Bitbucket', ], ], 'ImportExistingResources' => [ 'type' => 'boolean', ], 'ImportStacksToStackSetInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetNameOrId', ], 'StackIds' => [ 'shape' => 'StackIdList', ], 'StackIdsUrl' => [ 'shape' => 'StackIdsUrl', ], 'OrganizationalUnitIds' => [ 'shape' => 'OrganizationalUnitIdList', ], 'OperationPreferences' => [ 'shape' => 'StackSetOperationPreferences', ], 'OperationId' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'CallAs' => [ 'shape' => 'CallAs', ], ], ], 'ImportStacksToStackSetOutput' => [ 'type' => 'structure', 'members' => [ 'OperationId' => [ 'shape' => 'ClientRequestToken', ], ], ], 'Imports' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackName', ], ], 'InProgressStackInstancesCount' => [ 'type' => 'integer', 'min' => 0, ], 'InSyncStackInstancesCount' => [ 'type' => 'integer', 'min' => 0, ], 'IncludeNestedStacks' => [ 'type' => 'boolean', ], 'IncludePropertyValues' => [ 'type' => 'boolean', ], 'InsufficientCapabilitiesException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InsufficientCapabilitiesException', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidChangeSetStatusException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidChangeSetStatus', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidOperationException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidOperationException', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidStateTransitionException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidStateTransition', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'IsActivated' => [ 'type' => 'boolean', ], 'IsDefaultConfiguration' => [ 'type' => 'boolean', ], 'IsDefaultVersion' => [ 'type' => 'boolean', ], 'JazzLogicalResourceIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogicalResourceId', ], 'max' => 500, 'min' => 1, ], 'JazzResourceIdentifierProperties' => [ 'type' => 'map', 'key' => [ 'shape' => 'JazzResourceIdentifierPropertyKey', ], 'value' => [ 'shape' => 'JazzResourceIdentifierPropertyValue', ], ], 'JazzResourceIdentifierPropertyKey' => [ 'type' => 'string', ], 'JazzResourceIdentifierPropertyValue' => [ 'type' => 'string', ], 'Key' => [ 'type' => 'string', ], 'LastUpdatedTime' => [ 'type' => 'timestamp', ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'LimitExceededException', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'LimitName' => [ 'type' => 'string', ], 'LimitValue' => [ 'type' => 'integer', ], 'ListChangeSetsInput' => [ 'type' => 'structure', 'required' => [ 'StackName', ], 'members' => [ 'StackName' => [ 'shape' => 'StackNameOrId', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListChangeSetsOutput' => [ 'type' => 'structure', 'members' => [ 'Summaries' => [ 'shape' => 'ChangeSetSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListExportsInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListExportsOutput' => [ 'type' => 'structure', 'members' => [ 'Exports' => [ 'shape' => 'Exports', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListGeneratedTemplatesInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListGeneratedTemplatesOutput' => [ 'type' => 'structure', 'members' => [ 'Summaries' => [ 'shape' => 'TemplateSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListHookResultsInput' => [ 'type' => 'structure', 'required' => [ 'TargetType', 'TargetId', ], 'members' => [ 'TargetType' => [ 'shape' => 'ListHookResultsTargetType', ], 'TargetId' => [ 'shape' => 'HookResultId', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListHookResultsOutput' => [ 'type' => 'structure', 'members' => [ 'TargetType' => [ 'shape' => 'ListHookResultsTargetType', ], 'TargetId' => [ 'shape' => 'HookResultId', ], 'HookResults' => [ 'shape' => 'HookResultSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListHookResultsTargetType' => [ 'type' => 'string', 'enum' => [ 'CHANGE_SET', 'STACK', 'RESOURCE', 'CLOUD_CONTROL', ], ], 'ListImportsInput' => [ 'type' => 'structure', 'required' => [ 'ExportName', ], 'members' => [ 'ExportName' => [ 'shape' => 'ExportName', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListImportsOutput' => [ 'type' => 'structure', 'members' => [ 'Imports' => [ 'shape' => 'Imports', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListResourceScanRelatedResourcesInput' => [ 'type' => 'structure', 'required' => [ 'ResourceScanId', 'Resources', ], 'members' => [ 'ResourceScanId' => [ 'shape' => 'ResourceScanId', ], 'Resources' => [ 'shape' => 'ScannedResourceIdentifiers', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'BoxedMaxResults', ], ], ], 'ListResourceScanRelatedResourcesOutput' => [ 'type' => 'structure', 'members' => [ 'RelatedResources' => [ 'shape' => 'RelatedResources', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListResourceScanResourcesInput' => [ 'type' => 'structure', 'required' => [ 'ResourceScanId', ], 'members' => [ 'ResourceScanId' => [ 'shape' => 'ResourceScanId', ], 'ResourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'ResourceTypePrefix' => [ 'shape' => 'ResourceTypePrefix', ], 'TagKey' => [ 'shape' => 'TagKey', ], 'TagValue' => [ 'shape' => 'TagValue', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'ResourceScannerMaxResults', ], ], ], 'ListResourceScanResourcesOutput' => [ 'type' => 'structure', 'members' => [ 'Resources' => [ 'shape' => 'ScannedResources', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListResourceScansInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'ResourceScannerMaxResults', ], 'ScanTypeFilter' => [ 'shape' => 'ScanType', ], ], ], 'ListResourceScansOutput' => [ 'type' => 'structure', 'members' => [ 'ResourceScanSummaries' => [ 'shape' => 'ResourceScanSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStackInstanceResourceDriftsInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', 'StackInstanceAccount', 'StackInstanceRegion', 'OperationId', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetNameOrId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'StackInstanceResourceDriftStatuses' => [ 'shape' => 'StackResourceDriftStatusFilters', ], 'StackInstanceAccount' => [ 'shape' => 'Account', ], 'StackInstanceRegion' => [ 'shape' => 'Region', ], 'OperationId' => [ 'shape' => 'ClientRequestToken', ], 'CallAs' => [ 'shape' => 'CallAs', ], ], ], 'ListStackInstanceResourceDriftsOutput' => [ 'type' => 'structure', 'members' => [ 'Summaries' => [ 'shape' => 'StackInstanceResourceDriftsSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStackInstancesInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'StackInstanceFilters', ], 'StackInstanceAccount' => [ 'shape' => 'Account', ], 'StackInstanceRegion' => [ 'shape' => 'Region', ], 'CallAs' => [ 'shape' => 'CallAs', ], ], ], 'ListStackInstancesOutput' => [ 'type' => 'structure', 'members' => [ 'Summaries' => [ 'shape' => 'StackInstanceSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStackRefactorActionsInput' => [ 'type' => 'structure', 'required' => [ 'StackRefactorId', ], 'members' => [ 'StackRefactorId' => [ 'shape' => 'StackRefactorId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListStackRefactorActionsOutput' => [ 'type' => 'structure', 'required' => [ 'StackRefactorActions', ], 'members' => [ 'StackRefactorActions' => [ 'shape' => 'StackRefactorActions', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStackRefactorsInput' => [ 'type' => 'structure', 'members' => [ 'ExecutionStatusFilter' => [ 'shape' => 'StackRefactorExecutionStatusFilter', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListStackRefactorsOutput' => [ 'type' => 'structure', 'required' => [ 'StackRefactorSummaries', ], 'members' => [ 'StackRefactorSummaries' => [ 'shape' => 'StackRefactorSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStackResourcesInput' => [ 'type' => 'structure', 'required' => [ 'StackName', ], 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStackResourcesOutput' => [ 'type' => 'structure', 'members' => [ 'StackResourceSummaries' => [ 'shape' => 'StackResourceSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStackSetAutoDeploymentTargetsInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetNameOrId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'CallAs' => [ 'shape' => 'CallAs', ], ], ], 'ListStackSetAutoDeploymentTargetsOutput' => [ 'type' => 'structure', 'members' => [ 'Summaries' => [ 'shape' => 'StackSetAutoDeploymentTargetSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStackSetOperationResultsInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', 'OperationId', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'OperationId' => [ 'shape' => 'ClientRequestToken', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'CallAs' => [ 'shape' => 'CallAs', ], 'Filters' => [ 'shape' => 'OperationResultFilters', ], ], ], 'ListStackSetOperationResultsOutput' => [ 'type' => 'structure', 'members' => [ 'Summaries' => [ 'shape' => 'StackSetOperationResultSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStackSetOperationsInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'CallAs' => [ 'shape' => 'CallAs', ], ], ], 'ListStackSetOperationsOutput' => [ 'type' => 'structure', 'members' => [ 'Summaries' => [ 'shape' => 'StackSetOperationSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStackSetsInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Status' => [ 'shape' => 'StackSetStatus', ], 'CallAs' => [ 'shape' => 'CallAs', ], ], ], 'ListStackSetsOutput' => [ 'type' => 'structure', 'members' => [ 'Summaries' => [ 'shape' => 'StackSetSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStacksInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'StackStatusFilter' => [ 'shape' => 'StackStatusFilter', ], ], ], 'ListStacksOutput' => [ 'type' => 'structure', 'members' => [ 'StackSummaries' => [ 'shape' => 'StackSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTypeRegistrationsInput' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'RegistryType', ], 'TypeName' => [ 'shape' => 'TypeName', ], 'TypeArn' => [ 'shape' => 'TypeArn', ], 'RegistrationStatusFilter' => [ 'shape' => 'RegistrationStatus', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTypeRegistrationsOutput' => [ 'type' => 'structure', 'members' => [ 'RegistrationTokenList' => [ 'shape' => 'RegistrationTokenList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTypeVersionsInput' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'RegistryType', ], 'TypeName' => [ 'shape' => 'TypeName', ], 'Arn' => [ 'shape' => 'TypeArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'DeprecatedStatus' => [ 'shape' => 'DeprecatedStatus', ], 'PublisherId' => [ 'shape' => 'PublisherId', ], ], ], 'ListTypeVersionsOutput' => [ 'type' => 'structure', 'members' => [ 'TypeVersionSummaries' => [ 'shape' => 'TypeVersionSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTypesInput' => [ 'type' => 'structure', 'members' => [ 'Visibility' => [ 'shape' => 'Visibility', ], 'ProvisioningType' => [ 'shape' => 'ProvisioningType', ], 'DeprecatedStatus' => [ 'shape' => 'DeprecatedStatus', ], 'Type' => [ 'shape' => 'RegistryType', ], 'Filters' => [ 'shape' => 'TypeFilters', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTypesOutput' => [ 'type' => 'structure', 'members' => [ 'TypeSummaries' => [ 'shape' => 'TypeSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'LogGroupName' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[\\.\\-_/#A-Za-z0-9]+', ], 'LoggingConfig' => [ 'type' => 'structure', 'required' => [ 'LogRoleArn', 'LogGroupName', ], 'members' => [ 'LogRoleArn' => [ 'shape' => 'RoleArn', ], 'LogGroupName' => [ 'shape' => 'LogGroupName', ], ], ], 'LogicalIdHierarchy' => [ 'type' => 'string', ], 'LogicalResourceId' => [ 'type' => 'string', ], 'LogicalResourceIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogicalResourceId', ], 'max' => 200, 'min' => 1, ], 'MajorVersion' => [ 'type' => 'long', 'max' => 100000, 'min' => 1, ], 'ManagedByStack' => [ 'type' => 'boolean', ], 'ManagedExecution' => [ 'type' => 'structure', 'members' => [ 'Active' => [ 'shape' => 'ManagedExecutionNullable', ], ], ], 'ManagedExecutionNullable' => [ 'type' => 'boolean', ], 'MaxConcurrentCount' => [ 'type' => 'integer', 'min' => 1, ], 'MaxConcurrentPercentage' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'Metadata' => [ 'type' => 'string', ], 'ModuleInfo' => [ 'type' => 'structure', 'members' => [ 'TypeHierarchy' => [ 'shape' => 'TypeHierarchy', ], 'LogicalIdHierarchy' => [ 'shape' => 'LogicalIdHierarchy', ], ], ], 'MonitoringTimeInMinutes' => [ 'type' => 'integer', 'max' => 180, 'min' => 0, ], 'NameAlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'NameAlreadyExistsException', 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'NextToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'NoEcho' => [ 'type' => 'boolean', ], 'NotificationARN' => [ 'type' => 'string', ], 'NotificationARNs' => [ 'type' => 'list', 'member' => [ 'shape' => 'NotificationARN', ], 'max' => 5, ], 'NumberOfResources' => [ 'type' => 'integer', 'min' => 0, ], 'OnFailure' => [ 'type' => 'string', 'enum' => [ 'DO_NOTHING', 'ROLLBACK', 'DELETE', ], ], 'OnStackFailure' => [ 'type' => 'string', 'enum' => [ 'DO_NOTHING', 'ROLLBACK', 'DELETE', ], ], 'OperationIdAlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'OperationIdAlreadyExistsException', 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'OperationInProgressException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'OperationInProgressException', 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'OperationNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'OperationNotFoundException', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'OperationResultFilter' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'OperationResultFilterName', ], 'Values' => [ 'shape' => 'OperationResultFilterValues', ], ], ], 'OperationResultFilterName' => [ 'type' => 'string', 'enum' => [ 'OPERATION_RESULT_STATUS', ], ], 'OperationResultFilterValues' => [ 'type' => 'string', 'max' => 9, 'min' => 6, 'pattern' => '^\\S{6,9}$', ], 'OperationResultFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'OperationResultFilter', ], 'max' => 1, ], 'OperationStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'IN_PROGRESS', 'SUCCESS', 'FAILED', ], ], 'OperationStatusCheckFailedException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ConditionalCheckFailed', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'OptionalSecureUrl' => [ 'type' => 'string', 'max' => 4096, ], 'OrganizationStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', 'DISABLED_PERMANENTLY', ], ], 'OrganizationalUnitId' => [ 'type' => 'string', 'pattern' => '^(ou-[a-z0-9]{4,32}-[a-z0-9]{8,32}|r-[a-z0-9]{4,32})$', ], 'OrganizationalUnitIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrganizationalUnitId', ], ], 'Output' => [ 'type' => 'structure', 'members' => [ 'OutputKey' => [ 'shape' => 'OutputKey', ], 'OutputValue' => [ 'shape' => 'OutputValue', ], 'Description' => [ 'shape' => 'Description', ], 'ExportName' => [ 'shape' => 'ExportName', ], ], ], 'OutputKey' => [ 'type' => 'string', ], 'OutputValue' => [ 'type' => 'string', ], 'Outputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'Output', ], ], 'Parameter' => [ 'type' => 'structure', 'members' => [ 'ParameterKey' => [ 'shape' => 'ParameterKey', ], 'ParameterValue' => [ 'shape' => 'ParameterValue', ], 'UsePreviousValue' => [ 'shape' => 'UsePreviousValue', ], 'ResolvedValue' => [ 'shape' => 'ParameterValue', ], ], ], 'ParameterConstraints' => [ 'type' => 'structure', 'members' => [ 'AllowedValues' => [ 'shape' => 'AllowedValues', ], ], ], 'ParameterDeclaration' => [ 'type' => 'structure', 'members' => [ 'ParameterKey' => [ 'shape' => 'ParameterKey', ], 'DefaultValue' => [ 'shape' => 'ParameterValue', ], 'ParameterType' => [ 'shape' => 'ParameterType', ], 'NoEcho' => [ 'shape' => 'NoEcho', ], 'Description' => [ 'shape' => 'Description', ], 'ParameterConstraints' => [ 'shape' => 'ParameterConstraints', ], ], ], 'ParameterDeclarations' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParameterDeclaration', ], ], 'ParameterKey' => [ 'type' => 'string', ], 'ParameterType' => [ 'type' => 'string', ], 'ParameterValue' => [ 'type' => 'string', ], 'Parameters' => [ 'type' => 'list', 'member' => [ 'shape' => 'Parameter', ], ], 'PercentageCompleted' => [ 'type' => 'double', ], 'PermissionModels' => [ 'type' => 'string', 'enum' => [ 'SERVICE_MANAGED', 'SELF_MANAGED', ], ], 'PhysicalResourceId' => [ 'type' => 'string', ], 'PhysicalResourceIdContext' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhysicalResourceIdContextKeyValuePair', ], 'max' => 5, ], 'PhysicalResourceIdContextKeyValuePair' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'Key', ], 'Value' => [ 'shape' => 'Value', ], ], ], 'PolicyAction' => [ 'type' => 'string', 'enum' => [ 'Delete', 'Retain', 'Snapshot', 'ReplaceAndDelete', 'ReplaceAndRetain', 'ReplaceAndSnapshot', ], ], 'PrivateTypeArn' => [ 'type' => 'string', 'max' => 1024, 'pattern' => 'arn:aws[A-Za-z0-9-]{0,64}:cloudformation:[A-Za-z0-9-]{1,64}:[0-9]{12}:type/.+', ], 'Properties' => [ 'type' => 'string', ], 'PropertyDescription' => [ 'type' => 'string', ], 'PropertyDifference' => [ 'type' => 'structure', 'required' => [ 'PropertyPath', 'ExpectedValue', 'ActualValue', 'DifferenceType', ], 'members' => [ 'PropertyPath' => [ 'shape' => 'PropertyPath', ], 'ExpectedValue' => [ 'shape' => 'PropertyValue', ], 'ActualValue' => [ 'shape' => 'PropertyValue', ], 'DifferenceType' => [ 'shape' => 'DifferenceType', ], ], ], 'PropertyDifferences' => [ 'type' => 'list', 'member' => [ 'shape' => 'PropertyDifference', ], ], 'PropertyName' => [ 'type' => 'string', ], 'PropertyPath' => [ 'type' => 'string', ], 'PropertyValue' => [ 'type' => 'string', ], 'ProvisioningType' => [ 'type' => 'string', 'enum' => [ 'NON_PROVISIONABLE', 'IMMUTABLE', 'FULLY_MUTABLE', ], ], 'PublicVersionNumber' => [ 'type' => 'string', 'min' => 5, 'pattern' => '^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(.*)$', ], 'PublishTypeInput' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'ThirdPartyType', ], 'Arn' => [ 'shape' => 'PrivateTypeArn', ], 'TypeName' => [ 'shape' => 'TypeName', ], 'PublicVersionNumber' => [ 'shape' => 'PublicVersionNumber', ], ], ], 'PublishTypeOutput' => [ 'type' => 'structure', 'members' => [ 'PublicTypeArn' => [ 'shape' => 'TypeArn', ], ], ], 'PublisherId' => [ 'type' => 'string', 'max' => 40, 'min' => 1, 'pattern' => '[0-9a-zA-Z]{12,40}', ], 'PublisherName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[\\s\\S]+', ], 'PublisherProfile' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '(http:|https:)+[^\\s]+[\\w]', ], 'PublisherStatus' => [ 'type' => 'string', 'enum' => [ 'VERIFIED', 'UNVERIFIED', ], ], 'Reason' => [ 'type' => 'string', ], 'RecordHandlerProgressInput' => [ 'type' => 'structure', 'required' => [ 'BearerToken', 'OperationStatus', ], 'members' => [ 'BearerToken' => [ 'shape' => 'ClientToken', ], 'OperationStatus' => [ 'shape' => 'OperationStatus', ], 'CurrentOperationStatus' => [ 'shape' => 'OperationStatus', ], 'StatusMessage' => [ 'shape' => 'StatusMessage', ], 'ErrorCode' => [ 'shape' => 'HandlerErrorCode', ], 'ResourceModel' => [ 'shape' => 'ResourceModel', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], ], ], 'RecordHandlerProgressOutput' => [ 'type' => 'structure', 'members' => [], ], 'RefreshAllResources' => [ 'type' => 'boolean', ], 'Region' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9-]{1,128}$', ], 'RegionConcurrencyType' => [ 'type' => 'string', 'enum' => [ 'SEQUENTIAL', 'PARALLEL', ], ], 'RegionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Region', ], ], 'RegisterPublisherInput' => [ 'type' => 'structure', 'members' => [ 'AcceptTermsAndConditions' => [ 'shape' => 'AcceptTermsAndConditions', ], 'ConnectionArn' => [ 'shape' => 'ConnectionArn', ], ], ], 'RegisterPublisherOutput' => [ 'type' => 'structure', 'members' => [ 'PublisherId' => [ 'shape' => 'PublisherId', ], ], ], 'RegisterTypeInput' => [ 'type' => 'structure', 'required' => [ 'TypeName', 'SchemaHandlerPackage', ], 'members' => [ 'Type' => [ 'shape' => 'RegistryType', ], 'TypeName' => [ 'shape' => 'TypeName', ], 'SchemaHandlerPackage' => [ 'shape' => 'S3Url', ], 'LoggingConfig' => [ 'shape' => 'LoggingConfig', ], 'ExecutionRoleArn' => [ 'shape' => 'RoleArn', ], 'ClientRequestToken' => [ 'shape' => 'RequestToken', ], ], ], 'RegisterTypeOutput' => [ 'type' => 'structure', 'members' => [ 'RegistrationToken' => [ 'shape' => 'RegistrationToken', ], ], ], 'RegistrationStatus' => [ 'type' => 'string', 'enum' => [ 'COMPLETE', 'IN_PROGRESS', 'FAILED', ], ], 'RegistrationToken' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9][-a-zA-Z0-9]*', ], 'RegistrationTokenList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistrationToken', ], ], 'RegistryType' => [ 'type' => 'string', 'enum' => [ 'RESOURCE', 'MODULE', 'HOOK', ], ], 'RelatedResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScannedResource', ], ], 'Replacement' => [ 'type' => 'string', 'enum' => [ 'True', 'False', 'Conditional', ], ], 'RequestToken' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9][-a-zA-Z0-9]*', ], 'RequiredActivatedType' => [ 'type' => 'structure', 'members' => [ 'TypeNameAlias' => [ 'shape' => 'TypeName', ], 'OriginalTypeName' => [ 'shape' => 'TypeName', ], 'PublisherId' => [ 'shape' => 'PublisherId', ], 'SupportedMajorVersions' => [ 'shape' => 'SupportedMajorVersions', ], ], ], 'RequiredActivatedTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'RequiredActivatedType', ], ], 'RequiredProperty' => [ 'type' => 'boolean', ], 'RequiresRecreation' => [ 'type' => 'string', 'enum' => [ 'Never', 'Conditionally', 'Always', ], ], 'ResourceAttribute' => [ 'type' => 'string', 'enum' => [ 'Properties', 'Metadata', 'CreationPolicy', 'UpdatePolicy', 'DeletionPolicy', 'UpdateReplacePolicy', 'Tags', ], ], 'ResourceChange' => [ 'type' => 'structure', 'members' => [ 'PolicyAction' => [ 'shape' => 'PolicyAction', ], 'Action' => [ 'shape' => 'ChangeAction', ], 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'PhysicalResourceId' => [ 'shape' => 'PhysicalResourceId', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'Replacement' => [ 'shape' => 'Replacement', ], 'Scope' => [ 'shape' => 'Scope', ], 'Details' => [ 'shape' => 'ResourceChangeDetails', ], 'ChangeSetId' => [ 'shape' => 'ChangeSetId', ], 'ModuleInfo' => [ 'shape' => 'ModuleInfo', ], 'BeforeContext' => [ 'shape' => 'BeforeContext', ], 'AfterContext' => [ 'shape' => 'AfterContext', ], ], ], 'ResourceChangeDetail' => [ 'type' => 'structure', 'members' => [ 'Target' => [ 'shape' => 'ResourceTargetDefinition', ], 'Evaluation' => [ 'shape' => 'EvaluationType', ], 'ChangeSource' => [ 'shape' => 'ChangeSource', ], 'CausingEntity' => [ 'shape' => 'CausingEntity', ], ], ], 'ResourceChangeDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceChangeDetail', ], ], 'ResourceDefinition' => [ 'type' => 'structure', 'required' => [ 'ResourceType', 'ResourceIdentifier', ], 'members' => [ 'ResourceType' => [ 'shape' => 'ResourceType', ], 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'ResourceIdentifier' => [ 'shape' => 'ResourceIdentifierProperties', ], ], ], 'ResourceDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceDefinition', ], 'max' => 500, 'min' => 1, ], 'ResourceDetail' => [ 'type' => 'structure', 'members' => [ 'ResourceType' => [ 'shape' => 'ResourceType', ], 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'ResourceIdentifier' => [ 'shape' => 'ResourceIdentifierProperties', ], 'ResourceStatus' => [ 'shape' => 'GeneratedTemplateResourceStatus', ], 'ResourceStatusReason' => [ 'shape' => 'ResourceStatusReason', ], 'Warnings' => [ 'shape' => 'WarningDetails', ], ], ], 'ResourceDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceDetail', ], 'max' => 500, 'min' => 1, ], 'ResourceIdentifier' => [ 'type' => 'string', ], 'ResourceIdentifierProperties' => [ 'type' => 'map', 'key' => [ 'shape' => 'ResourceIdentifierPropertyKey', ], 'value' => [ 'shape' => 'ResourceIdentifierPropertyValue', ], 'max' => 256, 'min' => 1, ], 'ResourceIdentifierPropertyKey' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'ResourceIdentifierPropertyValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'ResourceIdentifierSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceIdentifierSummary', ], ], 'ResourceIdentifierSummary' => [ 'type' => 'structure', 'members' => [ 'ResourceType' => [ 'shape' => 'ResourceType', ], 'LogicalResourceIds' => [ 'shape' => 'LogicalResourceIds', ], 'ResourceIdentifiers' => [ 'shape' => 'ResourceIdentifiers', ], ], ], 'ResourceIdentifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceIdentifierPropertyKey', ], ], 'ResourceLocation' => [ 'type' => 'structure', 'required' => [ 'StackName', 'LogicalResourceId', ], 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], ], ], 'ResourceMapping' => [ 'type' => 'structure', 'required' => [ 'Source', 'Destination', ], 'members' => [ 'Source' => [ 'shape' => 'ResourceLocation', ], 'Destination' => [ 'shape' => 'ResourceLocation', ], ], ], 'ResourceMappings' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceMapping', ], ], 'ResourceModel' => [ 'type' => 'string', 'max' => 16384, 'min' => 1, ], 'ResourceProperties' => [ 'type' => 'string', ], 'ResourcePropertyPath' => [ 'type' => 'string', ], 'ResourceScanId' => [ 'type' => 'string', ], 'ResourceScanInProgressException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ResourceScanInProgress', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ResourceScanLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ResourceScanLimitExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ResourceScanNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ResourceScanNotFound', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ResourceScanStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'FAILED', 'COMPLETE', 'EXPIRED', ], ], 'ResourceScanStatusReason' => [ 'type' => 'string', ], 'ResourceScanSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceScanSummary', ], ], 'ResourceScanSummary' => [ 'type' => 'structure', 'members' => [ 'ResourceScanId' => [ 'shape' => 'ResourceScanId', ], 'Status' => [ 'shape' => 'ResourceScanStatus', ], 'StatusReason' => [ 'shape' => 'ResourceScanStatusReason', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'PercentageCompleted' => [ 'shape' => 'PercentageCompleted', ], 'ScanType' => [ 'shape' => 'ScanType', ], ], ], 'ResourceScannerMaxResults' => [ 'type' => 'integer', 'box' => true, ], 'ResourceSignalStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCESS', 'FAILURE', ], ], 'ResourceSignalUniqueId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ResourceStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'CREATE_COMPLETE', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', 'DELETE_COMPLETE', 'DELETE_SKIPPED', 'UPDATE_IN_PROGRESS', 'UPDATE_FAILED', 'UPDATE_COMPLETE', 'IMPORT_FAILED', 'IMPORT_COMPLETE', 'IMPORT_IN_PROGRESS', 'IMPORT_ROLLBACK_IN_PROGRESS', 'IMPORT_ROLLBACK_FAILED', 'IMPORT_ROLLBACK_COMPLETE', 'EXPORT_FAILED', 'EXPORT_COMPLETE', 'EXPORT_IN_PROGRESS', 'EXPORT_ROLLBACK_IN_PROGRESS', 'EXPORT_ROLLBACK_FAILED', 'EXPORT_ROLLBACK_COMPLETE', 'UPDATE_ROLLBACK_IN_PROGRESS', 'UPDATE_ROLLBACK_COMPLETE', 'UPDATE_ROLLBACK_FAILED', 'ROLLBACK_IN_PROGRESS', 'ROLLBACK_COMPLETE', 'ROLLBACK_FAILED', ], ], 'ResourceStatusReason' => [ 'type' => 'string', ], 'ResourceTargetDefinition' => [ 'type' => 'structure', 'members' => [ 'Attribute' => [ 'shape' => 'ResourceAttribute', ], 'Name' => [ 'shape' => 'PropertyName', ], 'RequiresRecreation' => [ 'shape' => 'RequiresRecreation', ], 'Path' => [ 'shape' => 'ResourcePropertyPath', ], 'BeforeValue' => [ 'shape' => 'BeforeValue', ], 'AfterValue' => [ 'shape' => 'AfterValue', ], 'AttributeChangeType' => [ 'shape' => 'AttributeChangeType', ], ], ], 'ResourceToImport' => [ 'type' => 'structure', 'required' => [ 'ResourceType', 'LogicalResourceId', 'ResourceIdentifier', ], 'members' => [ 'ResourceType' => [ 'shape' => 'ResourceType', ], 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'ResourceIdentifier' => [ 'shape' => 'ResourceIdentifierProperties', ], ], ], 'ResourceToSkip' => [ 'type' => 'string', 'pattern' => '[a-zA-Z0-9]+|[a-zA-Z][-a-zA-Z0-9]*\\.[a-zA-Z0-9]+', ], 'ResourceType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ResourceTypeFilter' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'ResourceTypeFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceTypeFilter', ], 'max' => 100, ], 'ResourceTypePrefix' => [ 'type' => 'string', ], 'ResourceTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceType', ], ], 'ResourcesFailed' => [ 'type' => 'integer', 'min' => 0, ], 'ResourcesPending' => [ 'type' => 'integer', 'min' => 0, ], 'ResourcesProcessing' => [ 'type' => 'integer', 'min' => 0, ], 'ResourcesRead' => [ 'type' => 'integer', ], 'ResourcesScanned' => [ 'type' => 'integer', ], 'ResourcesSucceeded' => [ 'type' => 'integer', 'min' => 0, ], 'ResourcesToImport' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceToImport', ], 'max' => 200, ], 'ResourcesToSkip' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceToSkip', ], ], 'RetainExceptOnCreate' => [ 'type' => 'boolean', ], 'RetainResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogicalResourceId', ], ], 'RetainStacks' => [ 'type' => 'boolean', ], 'RetainStacksNullable' => [ 'type' => 'boolean', ], 'RetainStacksOnAccountRemovalNullable' => [ 'type' => 'boolean', ], 'RoleARN' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, ], 'RoleArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => 'arn:.+:iam::[0-9]{12}:role/.+', ], 'RollbackConfiguration' => [ 'type' => 'structure', 'members' => [ 'RollbackTriggers' => [ 'shape' => 'RollbackTriggers', ], 'MonitoringTimeInMinutes' => [ 'shape' => 'MonitoringTimeInMinutes', ], ], ], 'RollbackStackInput' => [ 'type' => 'structure', 'required' => [ 'StackName', ], 'members' => [ 'StackName' => [ 'shape' => 'StackNameOrId', ], 'RoleARN' => [ 'shape' => 'RoleARN', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'RetainExceptOnCreate' => [ 'shape' => 'RetainExceptOnCreate', ], ], ], 'RollbackStackOutput' => [ 'type' => 'structure', 'members' => [ 'StackId' => [ 'shape' => 'StackId', ], ], ], 'RollbackTrigger' => [ 'type' => 'structure', 'required' => [ 'Arn', 'Type', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Type' => [ 'shape' => 'Type', ], ], ], 'RollbackTriggers' => [ 'type' => 'list', 'member' => [ 'shape' => 'RollbackTrigger', ], 'max' => 5, ], 'S3Bucket' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '[\\s\\S]+', ], 'S3Url' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'ScanFilter' => [ 'type' => 'structure', 'members' => [ 'Types' => [ 'shape' => 'ResourceTypeFilters', ], ], ], 'ScanFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScanFilter', ], 'max' => 1, 'min' => 1, ], 'ScanType' => [ 'type' => 'string', 'enum' => [ 'FULL', 'PARTIAL', ], ], 'ScannedResource' => [ 'type' => 'structure', 'members' => [ 'ResourceType' => [ 'shape' => 'ResourceType', ], 'ResourceIdentifier' => [ 'shape' => 'JazzResourceIdentifierProperties', ], 'ManagedByStack' => [ 'shape' => 'ManagedByStack', ], ], ], 'ScannedResourceIdentifier' => [ 'type' => 'structure', 'required' => [ 'ResourceType', 'ResourceIdentifier', ], 'members' => [ 'ResourceType' => [ 'shape' => 'ResourceType', ], 'ResourceIdentifier' => [ 'shape' => 'JazzResourceIdentifierProperties', ], ], ], 'ScannedResourceIdentifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScannedResourceIdentifier', ], ], 'ScannedResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScannedResource', ], ], 'Scope' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceAttribute', ], ], 'SetStackPolicyInput' => [ 'type' => 'structure', 'required' => [ 'StackName', ], 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'StackPolicyBody' => [ 'shape' => 'StackPolicyBody', ], 'StackPolicyURL' => [ 'shape' => 'StackPolicyURL', ], ], ], 'SetTypeConfigurationInput' => [ 'type' => 'structure', 'required' => [ 'Configuration', ], 'members' => [ 'TypeArn' => [ 'shape' => 'TypeArn', ], 'Configuration' => [ 'shape' => 'TypeConfiguration', ], 'ConfigurationAlias' => [ 'shape' => 'TypeConfigurationAlias', ], 'TypeName' => [ 'shape' => 'TypeName', ], 'Type' => [ 'shape' => 'ThirdPartyType', ], ], ], 'SetTypeConfigurationOutput' => [ 'type' => 'structure', 'members' => [ 'ConfigurationArn' => [ 'shape' => 'TypeConfigurationArn', ], ], ], 'SetTypeDefaultVersionInput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'PrivateTypeArn', ], 'Type' => [ 'shape' => 'RegistryType', ], 'TypeName' => [ 'shape' => 'TypeName', ], 'VersionId' => [ 'shape' => 'TypeVersionId', ], ], ], 'SetTypeDefaultVersionOutput' => [ 'type' => 'structure', 'members' => [], ], 'SignalResourceInput' => [ 'type' => 'structure', 'required' => [ 'StackName', 'LogicalResourceId', 'UniqueId', 'Status', ], 'members' => [ 'StackName' => [ 'shape' => 'StackNameOrId', ], 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'UniqueId' => [ 'shape' => 'ResourceSignalUniqueId', ], 'Status' => [ 'shape' => 'ResourceSignalStatus', ], ], ], 'Stack' => [ 'type' => 'structure', 'required' => [ 'StackName', 'CreationTime', 'StackStatus', ], 'members' => [ 'StackId' => [ 'shape' => 'StackId', ], 'StackName' => [ 'shape' => 'StackName', ], 'ChangeSetId' => [ 'shape' => 'ChangeSetId', ], 'Description' => [ 'shape' => 'Description', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'DeletionTime' => [ 'shape' => 'DeletionTime', ], 'LastUpdatedTime' => [ 'shape' => 'LastUpdatedTime', ], 'RollbackConfiguration' => [ 'shape' => 'RollbackConfiguration', ], 'StackStatus' => [ 'shape' => 'StackStatus', ], 'StackStatusReason' => [ 'shape' => 'StackStatusReason', ], 'DisableRollback' => [ 'shape' => 'DisableRollback', ], 'NotificationARNs' => [ 'shape' => 'NotificationARNs', ], 'TimeoutInMinutes' => [ 'shape' => 'TimeoutMinutes', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'Outputs' => [ 'shape' => 'Outputs', ], 'RoleARN' => [ 'shape' => 'RoleARN', ], 'Tags' => [ 'shape' => 'Tags', ], 'EnableTerminationProtection' => [ 'shape' => 'EnableTerminationProtection', ], 'ParentId' => [ 'shape' => 'StackId', ], 'RootId' => [ 'shape' => 'StackId', ], 'DriftInformation' => [ 'shape' => 'StackDriftInformation', ], 'RetainExceptOnCreate' => [ 'shape' => 'RetainExceptOnCreate', ], 'DeletionMode' => [ 'shape' => 'DeletionMode', ], 'DetailedStatus' => [ 'shape' => 'DetailedStatus', ], ], ], 'StackDefinition' => [ 'type' => 'structure', 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'TemplateBody' => [ 'shape' => 'TemplateBody', ], 'TemplateURL' => [ 'shape' => 'TemplateURL', ], ], ], 'StackDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackDefinition', ], ], 'StackDriftDetectionId' => [ 'type' => 'string', 'max' => 36, 'min' => 1, ], 'StackDriftDetectionStatus' => [ 'type' => 'string', 'enum' => [ 'DETECTION_IN_PROGRESS', 'DETECTION_FAILED', 'DETECTION_COMPLETE', ], ], 'StackDriftDetectionStatusReason' => [ 'type' => 'string', ], 'StackDriftInformation' => [ 'type' => 'structure', 'required' => [ 'StackDriftStatus', ], 'members' => [ 'StackDriftStatus' => [ 'shape' => 'StackDriftStatus', ], 'LastCheckTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'StackDriftInformationSummary' => [ 'type' => 'structure', 'required' => [ 'StackDriftStatus', ], 'members' => [ 'StackDriftStatus' => [ 'shape' => 'StackDriftStatus', ], 'LastCheckTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'StackDriftStatus' => [ 'type' => 'string', 'enum' => [ 'DRIFTED', 'IN_SYNC', 'UNKNOWN', 'NOT_CHECKED', ], ], 'StackEvent' => [ 'type' => 'structure', 'required' => [ 'StackId', 'EventId', 'StackName', 'Timestamp', ], 'members' => [ 'StackId' => [ 'shape' => 'StackId', ], 'EventId' => [ 'shape' => 'EventId', ], 'StackName' => [ 'shape' => 'StackName', ], 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'PhysicalResourceId' => [ 'shape' => 'PhysicalResourceId', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'Timestamp' => [ 'shape' => 'Timestamp', ], 'ResourceStatus' => [ 'shape' => 'ResourceStatus', ], 'ResourceStatusReason' => [ 'shape' => 'ResourceStatusReason', ], 'ResourceProperties' => [ 'shape' => 'ResourceProperties', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'HookType' => [ 'shape' => 'HookType', ], 'HookStatus' => [ 'shape' => 'HookStatus', ], 'HookStatusReason' => [ 'shape' => 'HookStatusReason', ], 'HookInvocationPoint' => [ 'shape' => 'HookInvocationPoint', ], 'HookFailureMode' => [ 'shape' => 'HookFailureMode', ], 'DetailedStatus' => [ 'shape' => 'DetailedStatus', ], ], ], 'StackEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackEvent', ], ], 'StackId' => [ 'type' => 'string', ], 'StackIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackId', ], ], 'StackIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackId', ], ], 'StackIdsUrl' => [ 'type' => 'string', 'max' => 5120, 'min' => 1, 'pattern' => '(s3://|http(s?)://).+', ], 'StackInstance' => [ 'type' => 'structure', 'members' => [ 'StackSetId' => [ 'shape' => 'StackSetId', ], 'Region' => [ 'shape' => 'Region', ], 'Account' => [ 'shape' => 'Account', ], 'StackId' => [ 'shape' => 'StackId', ], 'ParameterOverrides' => [ 'shape' => 'Parameters', ], 'Status' => [ 'shape' => 'StackInstanceStatus', ], 'StackInstanceStatus' => [ 'shape' => 'StackInstanceComprehensiveStatus', ], 'StatusReason' => [ 'shape' => 'Reason', ], 'OrganizationalUnitId' => [ 'shape' => 'OrganizationalUnitId', ], 'DriftStatus' => [ 'shape' => 'StackDriftStatus', ], 'LastDriftCheckTimestamp' => [ 'shape' => 'Timestamp', ], 'LastOperationId' => [ 'shape' => 'ClientRequestToken', ], ], ], 'StackInstanceComprehensiveStatus' => [ 'type' => 'structure', 'members' => [ 'DetailedStatus' => [ 'shape' => 'StackInstanceDetailedStatus', ], ], ], 'StackInstanceDetailedStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'RUNNING', 'SUCCEEDED', 'FAILED', 'CANCELLED', 'INOPERABLE', 'SKIPPED_SUSPENDED_ACCOUNT', 'FAILED_IMPORT', ], ], 'StackInstanceFilter' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'StackInstanceFilterName', ], 'Values' => [ 'shape' => 'StackInstanceFilterValues', ], ], ], 'StackInstanceFilterName' => [ 'type' => 'string', 'enum' => [ 'DETAILED_STATUS', 'LAST_OPERATION_ID', 'DRIFT_STATUS', ], ], 'StackInstanceFilterValues' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^\\S{1,128}$', ], 'StackInstanceFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackInstanceFilter', ], 'max' => 3, ], 'StackInstanceNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'StackInstanceNotFoundException', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'StackInstanceResourceDriftsSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackInstanceResourceDriftsSummary', ], ], 'StackInstanceResourceDriftsSummary' => [ 'type' => 'structure', 'required' => [ 'StackId', 'LogicalResourceId', 'ResourceType', 'StackResourceDriftStatus', 'Timestamp', ], 'members' => [ 'StackId' => [ 'shape' => 'StackId', ], 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'PhysicalResourceId' => [ 'shape' => 'PhysicalResourceId', ], 'PhysicalResourceIdContext' => [ 'shape' => 'PhysicalResourceIdContext', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'PropertyDifferences' => [ 'shape' => 'PropertyDifferences', ], 'StackResourceDriftStatus' => [ 'shape' => 'StackResourceDriftStatus', ], 'Timestamp' => [ 'shape' => 'Timestamp', ], ], ], 'StackInstanceStatus' => [ 'type' => 'string', 'enum' => [ 'CURRENT', 'OUTDATED', 'INOPERABLE', ], ], 'StackInstanceSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackInstanceSummary', ], ], 'StackInstanceSummary' => [ 'type' => 'structure', 'members' => [ 'StackSetId' => [ 'shape' => 'StackSetId', ], 'Region' => [ 'shape' => 'Region', ], 'Account' => [ 'shape' => 'Account', ], 'StackId' => [ 'shape' => 'StackId', ], 'Status' => [ 'shape' => 'StackInstanceStatus', ], 'StatusReason' => [ 'shape' => 'Reason', ], 'StackInstanceStatus' => [ 'shape' => 'StackInstanceComprehensiveStatus', ], 'OrganizationalUnitId' => [ 'shape' => 'OrganizationalUnitId', ], 'DriftStatus' => [ 'shape' => 'StackDriftStatus', ], 'LastDriftCheckTimestamp' => [ 'shape' => 'Timestamp', ], 'LastOperationId' => [ 'shape' => 'ClientRequestToken', ], ], ], 'StackName' => [ 'type' => 'string', ], 'StackNameOrId' => [ 'type' => 'string', 'min' => 1, 'pattern' => '([a-zA-Z][-a-zA-Z0-9]*)|(arn:\\b(aws|aws-us-gov|aws-cn)\\b:[-a-zA-Z0-9:/._+]*)', ], 'StackNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'StackNotFoundException', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'StackPolicyBody' => [ 'type' => 'string', 'max' => 16384, 'min' => 1, ], 'StackPolicyDuringUpdateBody' => [ 'type' => 'string', 'max' => 16384, 'min' => 1, ], 'StackPolicyDuringUpdateURL' => [ 'type' => 'string', 'max' => 5120, 'min' => 1, ], 'StackPolicyURL' => [ 'type' => 'string', 'max' => 5120, 'min' => 1, ], 'StackRefactorAction' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'StackRefactorActionType', ], 'Entity' => [ 'shape' => 'StackRefactorActionEntity', ], 'PhysicalResourceId' => [ 'shape' => 'PhysicalResourceId', ], 'ResourceIdentifier' => [ 'shape' => 'StackRefactorResourceIdentifier', ], 'Description' => [ 'shape' => 'Description', ], 'Detection' => [ 'shape' => 'StackRefactorDetection', ], 'DetectionReason' => [ 'shape' => 'DetectionReason', ], 'TagResources' => [ 'shape' => 'StackRefactorTagResources', ], 'UntagResources' => [ 'shape' => 'StackRefactorUntagResources', ], 'ResourceMapping' => [ 'shape' => 'ResourceMapping', ], ], ], 'StackRefactorActionEntity' => [ 'type' => 'string', 'enum' => [ 'RESOURCE', 'STACK', ], ], 'StackRefactorActionType' => [ 'type' => 'string', 'enum' => [ 'MOVE', 'CREATE', ], ], 'StackRefactorActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackRefactorAction', ], ], 'StackRefactorDetection' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'MANUAL', ], ], 'StackRefactorExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'UNAVAILABLE', 'AVAILABLE', 'OBSOLETE', 'EXECUTE_IN_PROGRESS', 'EXECUTE_COMPLETE', 'EXECUTE_FAILED', 'ROLLBACK_IN_PROGRESS', 'ROLLBACK_COMPLETE', 'ROLLBACK_FAILED', ], ], 'StackRefactorExecutionStatusFilter' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackRefactorExecutionStatus', ], ], 'StackRefactorId' => [ 'type' => 'string', ], 'StackRefactorNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'StackRefactorNotFoundException', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'StackRefactorResourceIdentifier' => [ 'type' => 'string', ], 'StackRefactorStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'CREATE_COMPLETE', 'CREATE_FAILED', 'DELETE_IN_PROGRESS', 'DELETE_COMPLETE', 'DELETE_FAILED', ], ], 'StackRefactorStatusReason' => [ 'type' => 'string', ], 'StackRefactorSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackRefactorSummary', ], ], 'StackRefactorSummary' => [ 'type' => 'structure', 'members' => [ 'StackRefactorId' => [ 'shape' => 'StackRefactorId', ], 'Description' => [ 'shape' => 'Description', ], 'ExecutionStatus' => [ 'shape' => 'StackRefactorExecutionStatus', ], 'ExecutionStatusReason' => [ 'shape' => 'ExecutionStatusReason', ], 'Status' => [ 'shape' => 'StackRefactorStatus', ], 'StatusReason' => [ 'shape' => 'StackRefactorStatusReason', ], ], ], 'StackRefactorTagResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'StackRefactorUntagResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'StackResource' => [ 'type' => 'structure', 'required' => [ 'LogicalResourceId', 'ResourceType', 'Timestamp', 'ResourceStatus', ], 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'StackId' => [ 'shape' => 'StackId', ], 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'PhysicalResourceId' => [ 'shape' => 'PhysicalResourceId', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'Timestamp' => [ 'shape' => 'Timestamp', ], 'ResourceStatus' => [ 'shape' => 'ResourceStatus', ], 'ResourceStatusReason' => [ 'shape' => 'ResourceStatusReason', ], 'Description' => [ 'shape' => 'Description', ], 'DriftInformation' => [ 'shape' => 'StackResourceDriftInformation', ], 'ModuleInfo' => [ 'shape' => 'ModuleInfo', ], ], ], 'StackResourceDetail' => [ 'type' => 'structure', 'required' => [ 'LogicalResourceId', 'ResourceType', 'LastUpdatedTimestamp', 'ResourceStatus', ], 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'StackId' => [ 'shape' => 'StackId', ], 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'PhysicalResourceId' => [ 'shape' => 'PhysicalResourceId', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], 'ResourceStatus' => [ 'shape' => 'ResourceStatus', ], 'ResourceStatusReason' => [ 'shape' => 'ResourceStatusReason', ], 'Description' => [ 'shape' => 'Description', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'DriftInformation' => [ 'shape' => 'StackResourceDriftInformation', ], 'ModuleInfo' => [ 'shape' => 'ModuleInfo', ], ], ], 'StackResourceDrift' => [ 'type' => 'structure', 'required' => [ 'StackId', 'LogicalResourceId', 'ResourceType', 'StackResourceDriftStatus', 'Timestamp', ], 'members' => [ 'StackId' => [ 'shape' => 'StackId', ], 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'PhysicalResourceId' => [ 'shape' => 'PhysicalResourceId', ], 'PhysicalResourceIdContext' => [ 'shape' => 'PhysicalResourceIdContext', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'ExpectedProperties' => [ 'shape' => 'Properties', ], 'ActualProperties' => [ 'shape' => 'Properties', ], 'PropertyDifferences' => [ 'shape' => 'PropertyDifferences', ], 'StackResourceDriftStatus' => [ 'shape' => 'StackResourceDriftStatus', ], 'Timestamp' => [ 'shape' => 'Timestamp', ], 'ModuleInfo' => [ 'shape' => 'ModuleInfo', ], ], ], 'StackResourceDriftInformation' => [ 'type' => 'structure', 'required' => [ 'StackResourceDriftStatus', ], 'members' => [ 'StackResourceDriftStatus' => [ 'shape' => 'StackResourceDriftStatus', ], 'LastCheckTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'StackResourceDriftInformationSummary' => [ 'type' => 'structure', 'required' => [ 'StackResourceDriftStatus', ], 'members' => [ 'StackResourceDriftStatus' => [ 'shape' => 'StackResourceDriftStatus', ], 'LastCheckTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'StackResourceDriftStatus' => [ 'type' => 'string', 'enum' => [ 'IN_SYNC', 'MODIFIED', 'DELETED', 'NOT_CHECKED', ], ], 'StackResourceDriftStatusFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackResourceDriftStatus', ], 'max' => 4, 'min' => 1, ], 'StackResourceDrifts' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackResourceDrift', ], ], 'StackResourceSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackResourceSummary', ], ], 'StackResourceSummary' => [ 'type' => 'structure', 'required' => [ 'LogicalResourceId', 'ResourceType', 'LastUpdatedTimestamp', 'ResourceStatus', ], 'members' => [ 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'PhysicalResourceId' => [ 'shape' => 'PhysicalResourceId', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], 'ResourceStatus' => [ 'shape' => 'ResourceStatus', ], 'ResourceStatusReason' => [ 'shape' => 'ResourceStatusReason', ], 'DriftInformation' => [ 'shape' => 'StackResourceDriftInformationSummary', ], 'ModuleInfo' => [ 'shape' => 'ModuleInfo', ], ], ], 'StackResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackResource', ], ], 'StackSet' => [ 'type' => 'structure', 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'StackSetId' => [ 'shape' => 'StackSetId', ], 'Description' => [ 'shape' => 'Description', ], 'Status' => [ 'shape' => 'StackSetStatus', ], 'TemplateBody' => [ 'shape' => 'TemplateBody', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'Tags' => [ 'shape' => 'Tags', ], 'StackSetARN' => [ 'shape' => 'StackSetARN', ], 'AdministrationRoleARN' => [ 'shape' => 'RoleARN', ], 'ExecutionRoleName' => [ 'shape' => 'ExecutionRoleName', ], 'StackSetDriftDetectionDetails' => [ 'shape' => 'StackSetDriftDetectionDetails', ], 'AutoDeployment' => [ 'shape' => 'AutoDeployment', ], 'PermissionModel' => [ 'shape' => 'PermissionModels', ], 'OrganizationalUnitIds' => [ 'shape' => 'OrganizationalUnitIdList', ], 'ManagedExecution' => [ 'shape' => 'ManagedExecution', ], 'Regions' => [ 'shape' => 'RegionList', ], ], ], 'StackSetARN' => [ 'type' => 'string', ], 'StackSetAutoDeploymentTargetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackSetAutoDeploymentTargetSummary', ], ], 'StackSetAutoDeploymentTargetSummary' => [ 'type' => 'structure', 'members' => [ 'OrganizationalUnitId' => [ 'shape' => 'OrganizationalUnitId', ], 'Regions' => [ 'shape' => 'RegionList', ], ], ], 'StackSetDriftDetectionDetails' => [ 'type' => 'structure', 'members' => [ 'DriftStatus' => [ 'shape' => 'StackSetDriftStatus', ], 'DriftDetectionStatus' => [ 'shape' => 'StackSetDriftDetectionStatus', ], 'LastDriftCheckTimestamp' => [ 'shape' => 'Timestamp', ], 'TotalStackInstancesCount' => [ 'shape' => 'TotalStackInstancesCount', ], 'DriftedStackInstancesCount' => [ 'shape' => 'DriftedStackInstancesCount', ], 'InSyncStackInstancesCount' => [ 'shape' => 'InSyncStackInstancesCount', ], 'InProgressStackInstancesCount' => [ 'shape' => 'InProgressStackInstancesCount', ], 'FailedStackInstancesCount' => [ 'shape' => 'FailedStackInstancesCount', ], ], ], 'StackSetDriftDetectionStatus' => [ 'type' => 'string', 'enum' => [ 'COMPLETED', 'FAILED', 'PARTIAL_SUCCESS', 'IN_PROGRESS', 'STOPPED', ], ], 'StackSetDriftStatus' => [ 'type' => 'string', 'enum' => [ 'DRIFTED', 'IN_SYNC', 'NOT_CHECKED', ], ], 'StackSetId' => [ 'type' => 'string', ], 'StackSetName' => [ 'type' => 'string', ], 'StackSetNameOrId' => [ 'type' => 'string', 'pattern' => '[a-zA-Z][-a-zA-Z0-9]*(?::[a-zA-Z0-9]{8}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{12})?', ], 'StackSetNotEmptyException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'StackSetNotEmptyException', 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'StackSetNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'StackSetNotFoundException', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'StackSetOperation' => [ 'type' => 'structure', 'members' => [ 'OperationId' => [ 'shape' => 'ClientRequestToken', ], 'StackSetId' => [ 'shape' => 'StackSetId', ], 'Action' => [ 'shape' => 'StackSetOperationAction', ], 'Status' => [ 'shape' => 'StackSetOperationStatus', ], 'OperationPreferences' => [ 'shape' => 'StackSetOperationPreferences', ], 'RetainStacks' => [ 'shape' => 'RetainStacksNullable', ], 'AdministrationRoleARN' => [ 'shape' => 'RoleARN', ], 'ExecutionRoleName' => [ 'shape' => 'ExecutionRoleName', ], 'CreationTimestamp' => [ 'shape' => 'Timestamp', ], 'EndTimestamp' => [ 'shape' => 'Timestamp', ], 'DeploymentTargets' => [ 'shape' => 'DeploymentTargets', ], 'StackSetDriftDetectionDetails' => [ 'shape' => 'StackSetDriftDetectionDetails', ], 'StatusReason' => [ 'shape' => 'StackSetOperationStatusReason', ], 'StatusDetails' => [ 'shape' => 'StackSetOperationStatusDetails', ], ], ], 'StackSetOperationAction' => [ 'type' => 'string', 'enum' => [ 'CREATE', 'UPDATE', 'DELETE', 'DETECT_DRIFT', ], ], 'StackSetOperationPreferences' => [ 'type' => 'structure', 'members' => [ 'RegionConcurrencyType' => [ 'shape' => 'RegionConcurrencyType', ], 'RegionOrder' => [ 'shape' => 'RegionList', ], 'FailureToleranceCount' => [ 'shape' => 'FailureToleranceCount', ], 'FailureTolerancePercentage' => [ 'shape' => 'FailureTolerancePercentage', ], 'MaxConcurrentCount' => [ 'shape' => 'MaxConcurrentCount', ], 'MaxConcurrentPercentage' => [ 'shape' => 'MaxConcurrentPercentage', ], 'ConcurrencyMode' => [ 'shape' => 'ConcurrencyMode', ], ], ], 'StackSetOperationResultStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'RUNNING', 'SUCCEEDED', 'FAILED', 'CANCELLED', ], ], 'StackSetOperationResultSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackSetOperationResultSummary', ], ], 'StackSetOperationResultSummary' => [ 'type' => 'structure', 'members' => [ 'Account' => [ 'shape' => 'Account', ], 'Region' => [ 'shape' => 'Region', ], 'Status' => [ 'shape' => 'StackSetOperationResultStatus', ], 'StatusReason' => [ 'shape' => 'Reason', ], 'AccountGateResult' => [ 'shape' => 'AccountGateResult', ], 'OrganizationalUnitId' => [ 'shape' => 'OrganizationalUnitId', ], ], ], 'StackSetOperationStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'SUCCEEDED', 'FAILED', 'STOPPING', 'STOPPED', 'QUEUED', ], ], 'StackSetOperationStatusDetails' => [ 'type' => 'structure', 'members' => [ 'FailedStackInstancesCount' => [ 'shape' => 'FailedStackInstancesCount', ], ], ], 'StackSetOperationStatusReason' => [ 'type' => 'string', ], 'StackSetOperationSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackSetOperationSummary', ], ], 'StackSetOperationSummary' => [ 'type' => 'structure', 'members' => [ 'OperationId' => [ 'shape' => 'ClientRequestToken', ], 'Action' => [ 'shape' => 'StackSetOperationAction', ], 'Status' => [ 'shape' => 'StackSetOperationStatus', ], 'CreationTimestamp' => [ 'shape' => 'Timestamp', ], 'EndTimestamp' => [ 'shape' => 'Timestamp', ], 'StatusReason' => [ 'shape' => 'StackSetOperationStatusReason', ], 'StatusDetails' => [ 'shape' => 'StackSetOperationStatusDetails', ], 'OperationPreferences' => [ 'shape' => 'StackSetOperationPreferences', ], ], ], 'StackSetStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'DELETED', ], ], 'StackSetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackSetSummary', ], ], 'StackSetSummary' => [ 'type' => 'structure', 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'StackSetId' => [ 'shape' => 'StackSetId', ], 'Description' => [ 'shape' => 'Description', ], 'Status' => [ 'shape' => 'StackSetStatus', ], 'AutoDeployment' => [ 'shape' => 'AutoDeployment', ], 'PermissionModel' => [ 'shape' => 'PermissionModels', ], 'DriftStatus' => [ 'shape' => 'StackDriftStatus', ], 'LastDriftCheckTimestamp' => [ 'shape' => 'Timestamp', ], 'ManagedExecution' => [ 'shape' => 'ManagedExecution', ], ], ], 'StackStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'CREATE_COMPLETE', 'ROLLBACK_IN_PROGRESS', 'ROLLBACK_FAILED', 'ROLLBACK_COMPLETE', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', 'DELETE_COMPLETE', 'UPDATE_IN_PROGRESS', 'UPDATE_COMPLETE_CLEANUP_IN_PROGRESS', 'UPDATE_COMPLETE', 'UPDATE_FAILED', 'UPDATE_ROLLBACK_IN_PROGRESS', 'UPDATE_ROLLBACK_FAILED', 'UPDATE_ROLLBACK_COMPLETE_CLEANUP_IN_PROGRESS', 'UPDATE_ROLLBACK_COMPLETE', 'REVIEW_IN_PROGRESS', 'IMPORT_IN_PROGRESS', 'IMPORT_COMPLETE', 'IMPORT_ROLLBACK_IN_PROGRESS', 'IMPORT_ROLLBACK_FAILED', 'IMPORT_ROLLBACK_COMPLETE', ], ], 'StackStatusFilter' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackStatus', ], ], 'StackStatusReason' => [ 'type' => 'string', ], 'StackSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackSummary', ], ], 'StackSummary' => [ 'type' => 'structure', 'required' => [ 'StackName', 'CreationTime', 'StackStatus', ], 'members' => [ 'StackId' => [ 'shape' => 'StackId', ], 'StackName' => [ 'shape' => 'StackName', ], 'TemplateDescription' => [ 'shape' => 'TemplateDescription', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'LastUpdatedTime' => [ 'shape' => 'LastUpdatedTime', ], 'DeletionTime' => [ 'shape' => 'DeletionTime', ], 'StackStatus' => [ 'shape' => 'StackStatus', ], 'StackStatusReason' => [ 'shape' => 'StackStatusReason', ], 'ParentId' => [ 'shape' => 'StackId', ], 'RootId' => [ 'shape' => 'StackId', ], 'DriftInformation' => [ 'shape' => 'StackDriftInformationSummary', ], ], ], 'Stacks' => [ 'type' => 'list', 'member' => [ 'shape' => 'Stack', ], ], 'StageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TemplateStage', ], ], 'StaleRequestException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'StaleRequestException', 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'StartResourceScanInput' => [ 'type' => 'structure', 'members' => [ 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'ScanFilters' => [ 'shape' => 'ScanFilters', ], ], ], 'StartResourceScanOutput' => [ 'type' => 'structure', 'members' => [ 'ResourceScanId' => [ 'shape' => 'ResourceScanId', ], ], ], 'StatusMessage' => [ 'type' => 'string', 'max' => 1024, ], 'StopStackSetOperationInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', 'OperationId', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'OperationId' => [ 'shape' => 'ClientRequestToken', ], 'CallAs' => [ 'shape' => 'CallAs', ], ], ], 'StopStackSetOperationOutput' => [ 'type' => 'structure', 'members' => [], ], 'SupportedMajorVersion' => [ 'type' => 'integer', 'max' => 65535, 'min' => 1, ], 'SupportedMajorVersions' => [ 'type' => 'list', 'member' => [ 'shape' => 'SupportedMajorVersion', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'Tags' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 50, ], 'TemplateBody' => [ 'type' => 'string', 'min' => 1, ], 'TemplateConfiguration' => [ 'type' => 'structure', 'members' => [ 'DeletionPolicy' => [ 'shape' => 'GeneratedTemplateDeletionPolicy', ], 'UpdateReplacePolicy' => [ 'shape' => 'GeneratedTemplateUpdateReplacePolicy', ], ], ], 'TemplateDescription' => [ 'type' => 'string', ], 'TemplateFormat' => [ 'type' => 'string', 'enum' => [ 'JSON', 'YAML', ], ], 'TemplateParameter' => [ 'type' => 'structure', 'members' => [ 'ParameterKey' => [ 'shape' => 'ParameterKey', ], 'DefaultValue' => [ 'shape' => 'ParameterValue', ], 'NoEcho' => [ 'shape' => 'NoEcho', ], 'Description' => [ 'shape' => 'Description', ], ], ], 'TemplateParameters' => [ 'type' => 'list', 'member' => [ 'shape' => 'TemplateParameter', ], ], 'TemplateProgress' => [ 'type' => 'structure', 'members' => [ 'ResourcesSucceeded' => [ 'shape' => 'ResourcesSucceeded', ], 'ResourcesFailed' => [ 'shape' => 'ResourcesFailed', ], 'ResourcesProcessing' => [ 'shape' => 'ResourcesProcessing', ], 'ResourcesPending' => [ 'shape' => 'ResourcesPending', ], ], ], 'TemplateStage' => [ 'type' => 'string', 'enum' => [ 'Original', 'Processed', ], ], 'TemplateStatusReason' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'TemplateSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'TemplateSummary', ], ], 'TemplateSummary' => [ 'type' => 'structure', 'members' => [ 'GeneratedTemplateId' => [ 'shape' => 'GeneratedTemplateId', ], 'GeneratedTemplateName' => [ 'shape' => 'GeneratedTemplateName', ], 'Status' => [ 'shape' => 'GeneratedTemplateStatus', ], 'StatusReason' => [ 'shape' => 'TemplateStatusReason', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'LastUpdatedTime' => [ 'shape' => 'LastUpdatedTime', ], 'NumberOfResources' => [ 'shape' => 'NumberOfResources', ], ], ], 'TemplateSummaryConfig' => [ 'type' => 'structure', 'members' => [ 'TreatUnrecognizedResourceTypesAsWarnings' => [ 'shape' => 'TreatUnrecognizedResourceTypesAsWarnings', ], ], ], 'TemplateURL' => [ 'type' => 'string', 'max' => 5120, 'min' => 1, ], 'TestTypeInput' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'TypeArn', ], 'Type' => [ 'shape' => 'ThirdPartyType', ], 'TypeName' => [ 'shape' => 'TypeName', ], 'VersionId' => [ 'shape' => 'TypeVersionId', ], 'LogDeliveryBucket' => [ 'shape' => 'S3Bucket', ], ], ], 'TestTypeOutput' => [ 'type' => 'structure', 'members' => [ 'TypeVersionArn' => [ 'shape' => 'TypeArn', ], ], ], 'ThirdPartyType' => [ 'type' => 'string', 'enum' => [ 'RESOURCE', 'MODULE', 'HOOK', ], ], 'ThirdPartyTypeArn' => [ 'type' => 'string', 'max' => 1024, 'pattern' => 'arn:aws[A-Za-z0-9-]{0,64}:cloudformation:[A-Za-z0-9-]{1,64}::type/.+/[0-9a-zA-Z]{12,40}/.+', ], 'TimeoutMinutes' => [ 'type' => 'integer', 'min' => 1, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TokenAlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TokenAlreadyExistsException', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TotalStackInstancesCount' => [ 'type' => 'integer', 'min' => 0, ], 'TotalWarnings' => [ 'type' => 'integer', 'min' => 0, ], 'TransformName' => [ 'type' => 'string', ], 'TransformsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TransformName', ], ], 'TreatUnrecognizedResourceTypesAsWarnings' => [ 'type' => 'boolean', ], 'Type' => [ 'type' => 'string', ], 'TypeArn' => [ 'type' => 'string', 'max' => 1024, 'pattern' => 'arn:aws[A-Za-z0-9-]{0,64}:cloudformation:[A-Za-z0-9-]{1,64}:([0-9]{12})?:type/.+', ], 'TypeConfiguration' => [ 'type' => 'string', 'max' => 204800, 'min' => 1, 'pattern' => '[\\s\\S]+', ], 'TypeConfigurationAlias' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[a-zA-Z0-9]{1,256}$', ], 'TypeConfigurationArn' => [ 'type' => 'string', 'max' => 1024, 'pattern' => 'arn:aws[A-Za-z0-9-]{0,64}:cloudformation:[A-Za-z0-9-]{1,64}:([0-9]{12})?:type-configuration/.+', ], 'TypeConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'TypeConfigurationArn', ], 'Alias' => [ 'shape' => 'TypeConfigurationAlias', ], 'Configuration' => [ 'shape' => 'TypeConfiguration', ], 'LastUpdated' => [ 'shape' => 'Timestamp', ], 'TypeArn' => [ 'shape' => 'TypeArn', ], 'TypeName' => [ 'shape' => 'TypeName', ], 'IsDefaultConfiguration' => [ 'shape' => 'IsDefaultConfiguration', ], ], ], 'TypeConfigurationDetailsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TypeConfigurationDetails', ], ], 'TypeConfigurationIdentifier' => [ 'type' => 'structure', 'members' => [ 'TypeArn' => [ 'shape' => 'TypeArn', ], 'TypeConfigurationAlias' => [ 'shape' => 'TypeConfigurationAlias', ], 'TypeConfigurationArn' => [ 'shape' => 'TypeConfigurationArn', ], 'Type' => [ 'shape' => 'ThirdPartyType', ], 'TypeName' => [ 'shape' => 'TypeName', ], ], ], 'TypeConfigurationIdentifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'TypeConfigurationIdentifier', ], 'min' => 1, ], 'TypeConfigurationNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TypeConfigurationNotFoundException', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'TypeFilters' => [ 'type' => 'structure', 'members' => [ 'Category' => [ 'shape' => 'Category', ], 'PublisherId' => [ 'shape' => 'PublisherId', ], 'TypeNamePrefix' => [ 'shape' => 'TypeNamePrefix', ], ], ], 'TypeHierarchy' => [ 'type' => 'string', ], 'TypeName' => [ 'type' => 'string', 'max' => 204, 'min' => 10, 'pattern' => '[A-Za-z0-9]{2,64}::[A-Za-z0-9]{2,64}::[A-Za-z0-9]{2,64}(::MODULE){0,1}', ], 'TypeNamePrefix' => [ 'type' => 'string', 'max' => 204, 'min' => 1, 'pattern' => '([A-Za-z0-9]{2,64}::){0,2}([A-Za-z0-9]{2,64}:?){0,1}', ], 'TypeNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TypeNotFoundException', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'TypeSchema' => [ 'type' => 'string', 'max' => 16777216, 'min' => 1, ], 'TypeSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'TypeSummary', ], ], 'TypeSummary' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'RegistryType', ], 'TypeName' => [ 'shape' => 'TypeName', ], 'DefaultVersionId' => [ 'shape' => 'TypeVersionId', ], 'TypeArn' => [ 'shape' => 'TypeArn', ], 'LastUpdated' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'Description', ], 'PublisherId' => [ 'shape' => 'PublisherId', ], 'OriginalTypeName' => [ 'shape' => 'TypeName', ], 'PublicVersionNumber' => [ 'shape' => 'PublicVersionNumber', ], 'LatestPublicVersion' => [ 'shape' => 'PublicVersionNumber', ], 'PublisherIdentity' => [ 'shape' => 'IdentityProvider', ], 'PublisherName' => [ 'shape' => 'PublisherName', ], 'IsActivated' => [ 'shape' => 'IsActivated', ], ], ], 'TypeTestsStatus' => [ 'type' => 'string', 'enum' => [ 'PASSED', 'FAILED', 'IN_PROGRESS', 'NOT_TESTED', ], ], 'TypeTestsStatusDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\s\\S]+', ], 'TypeVersionId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[A-Za-z0-9-]+', ], 'TypeVersionSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'TypeVersionSummary', ], ], 'TypeVersionSummary' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'RegistryType', ], 'TypeName' => [ 'shape' => 'TypeName', ], 'VersionId' => [ 'shape' => 'TypeVersionId', ], 'IsDefaultVersion' => [ 'shape' => 'IsDefaultVersion', ], 'Arn' => [ 'shape' => 'TypeArn', ], 'TimeCreated' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'Description', ], 'PublicVersionNumber' => [ 'shape' => 'PublicVersionNumber', ], ], ], 'UnprocessedTypeConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'TypeConfigurationIdentifier', ], ], 'UpdateGeneratedTemplateInput' => [ 'type' => 'structure', 'required' => [ 'GeneratedTemplateName', ], 'members' => [ 'GeneratedTemplateName' => [ 'shape' => 'GeneratedTemplateName', ], 'NewGeneratedTemplateName' => [ 'shape' => 'GeneratedTemplateName', ], 'AddResources' => [ 'shape' => 'ResourceDefinitions', ], 'RemoveResources' => [ 'shape' => 'JazzLogicalResourceIds', ], 'RefreshAllResources' => [ 'shape' => 'RefreshAllResources', ], 'TemplateConfiguration' => [ 'shape' => 'TemplateConfiguration', ], ], ], 'UpdateGeneratedTemplateOutput' => [ 'type' => 'structure', 'members' => [ 'GeneratedTemplateId' => [ 'shape' => 'GeneratedTemplateId', ], ], ], 'UpdateStackInput' => [ 'type' => 'structure', 'required' => [ 'StackName', ], 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'TemplateBody' => [ 'shape' => 'TemplateBody', ], 'TemplateURL' => [ 'shape' => 'TemplateURL', ], 'UsePreviousTemplate' => [ 'shape' => 'UsePreviousTemplate', ], 'StackPolicyDuringUpdateBody' => [ 'shape' => 'StackPolicyDuringUpdateBody', ], 'StackPolicyDuringUpdateURL' => [ 'shape' => 'StackPolicyDuringUpdateURL', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'ResourceTypes' => [ 'shape' => 'ResourceTypes', ], 'RoleARN' => [ 'shape' => 'RoleARN', ], 'RollbackConfiguration' => [ 'shape' => 'RollbackConfiguration', ], 'StackPolicyBody' => [ 'shape' => 'StackPolicyBody', ], 'StackPolicyURL' => [ 'shape' => 'StackPolicyURL', ], 'NotificationARNs' => [ 'shape' => 'NotificationARNs', ], 'Tags' => [ 'shape' => 'Tags', ], 'DisableRollback' => [ 'shape' => 'DisableRollback', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'RetainExceptOnCreate' => [ 'shape' => 'RetainExceptOnCreate', ], ], ], 'UpdateStackInstancesInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', 'Regions', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetNameOrId', ], 'Accounts' => [ 'shape' => 'AccountList', ], 'DeploymentTargets' => [ 'shape' => 'DeploymentTargets', ], 'Regions' => [ 'shape' => 'RegionList', ], 'ParameterOverrides' => [ 'shape' => 'Parameters', ], 'OperationPreferences' => [ 'shape' => 'StackSetOperationPreferences', ], 'OperationId' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'CallAs' => [ 'shape' => 'CallAs', ], ], ], 'UpdateStackInstancesOutput' => [ 'type' => 'structure', 'members' => [ 'OperationId' => [ 'shape' => 'ClientRequestToken', ], ], ], 'UpdateStackOutput' => [ 'type' => 'structure', 'members' => [ 'StackId' => [ 'shape' => 'StackId', ], ], ], 'UpdateStackSetInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'Description' => [ 'shape' => 'Description', ], 'TemplateBody' => [ 'shape' => 'TemplateBody', ], 'TemplateURL' => [ 'shape' => 'TemplateURL', ], 'UsePreviousTemplate' => [ 'shape' => 'UsePreviousTemplate', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'Tags' => [ 'shape' => 'Tags', ], 'OperationPreferences' => [ 'shape' => 'StackSetOperationPreferences', ], 'AdministrationRoleARN' => [ 'shape' => 'RoleARN', ], 'ExecutionRoleName' => [ 'shape' => 'ExecutionRoleName', ], 'DeploymentTargets' => [ 'shape' => 'DeploymentTargets', ], 'PermissionModel' => [ 'shape' => 'PermissionModels', ], 'AutoDeployment' => [ 'shape' => 'AutoDeployment', ], 'OperationId' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Accounts' => [ 'shape' => 'AccountList', ], 'Regions' => [ 'shape' => 'RegionList', ], 'CallAs' => [ 'shape' => 'CallAs', ], 'ManagedExecution' => [ 'shape' => 'ManagedExecution', ], ], ], 'UpdateStackSetOutput' => [ 'type' => 'structure', 'members' => [ 'OperationId' => [ 'shape' => 'ClientRequestToken', ], ], ], 'UpdateTerminationProtectionInput' => [ 'type' => 'structure', 'required' => [ 'EnableTerminationProtection', 'StackName', ], 'members' => [ 'EnableTerminationProtection' => [ 'shape' => 'EnableTerminationProtection', ], 'StackName' => [ 'shape' => 'StackNameOrId', ], ], ], 'UpdateTerminationProtectionOutput' => [ 'type' => 'structure', 'members' => [ 'StackId' => [ 'shape' => 'StackId', ], ], ], 'Url' => [ 'type' => 'string', ], 'UsePreviousTemplate' => [ 'type' => 'boolean', ], 'UsePreviousValue' => [ 'type' => 'boolean', ], 'ValidateTemplateInput' => [ 'type' => 'structure', 'members' => [ 'TemplateBody' => [ 'shape' => 'TemplateBody', ], 'TemplateURL' => [ 'shape' => 'TemplateURL', ], ], ], 'ValidateTemplateOutput' => [ 'type' => 'structure', 'members' => [ 'Parameters' => [ 'shape' => 'TemplateParameters', ], 'Description' => [ 'shape' => 'Description', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'CapabilitiesReason' => [ 'shape' => 'CapabilitiesReason', ], 'DeclaredTransforms' => [ 'shape' => 'TransformsList', ], ], ], 'Value' => [ 'type' => 'string', ], 'Version' => [ 'type' => 'string', ], 'VersionBump' => [ 'type' => 'string', 'enum' => [ 'MAJOR', 'MINOR', ], ], 'Visibility' => [ 'type' => 'string', 'enum' => [ 'PUBLIC', 'PRIVATE', ], ], 'WarningDetail' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'WarningType', ], 'Properties' => [ 'shape' => 'WarningProperties', ], ], ], 'WarningDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'WarningDetail', ], ], 'WarningProperties' => [ 'type' => 'list', 'member' => [ 'shape' => 'WarningProperty', ], ], 'WarningProperty' => [ 'type' => 'structure', 'members' => [ 'PropertyPath' => [ 'shape' => 'PropertyPath', ], 'Required' => [ 'shape' => 'RequiredProperty', ], 'Description' => [ 'shape' => 'PropertyDescription', ], ], ], 'WarningType' => [ 'type' => 'string', 'enum' => [ 'MUTUALLY_EXCLUSIVE_PROPERTIES', 'UNSUPPORTED_PROPERTIES', 'MUTUALLY_EXCLUSIVE_TYPES', 'EXCLUDED_PROPERTIES', ], ], 'Warnings' => [ 'type' => 'structure', 'members' => [ 'UnrecognizedResourceTypes' => [ 'shape' => 'ResourceTypes', ], ], ], ],];
