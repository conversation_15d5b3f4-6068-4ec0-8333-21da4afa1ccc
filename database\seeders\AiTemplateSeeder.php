<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AiTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $template = [
            [
                'template_name' => 'description',
                'prompt' => "Write a long creative product description for: ##title## \n\nTarget audience is: ##audience## \n\nUse this description: ##description## \n\nTone of generated text must be:\n ##tone_language## \n\n",
                'module' => 'product & service',
                'field_json' => '{"field":[{"label":"Product name","placeholder":"e.g. VR, Honda","field_type":"text_box","field_name":"title"},{"label":"Audience","placeholder":"e.g. Women, Aliens","field_type":"text_box","field_name":"audience"},{"label":"Product Description","placeholder":"e.g. VR is an innovative device that can allow you to be part of virtual world","field_type":"textarea","field_name":"description"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'name',
                'prompt' => "Write a long creative product description for: ##title## \n\nTarget audience is: ##audience## \n\nUse this description: ##description## \n\nTone of generated text must be:\n ##tone_language## \n\n",
                'module' => 'product & service',
                'field_json' => '{"field":[{"label":"Product name","placeholder":"e.g. VR, Honda","field_type":"text_box","field_name":"title"},{"label":"Audience","placeholder":"e.g. Women, Aliens","field_type":"text_box","field_name":"audience"},{"label":"Product Description","placeholder":"e.g. VR is an innovative device that can allow you to be part of virtual world","field_type":"textarea","field_name":"description"}]}',
                'is_tone' => '1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'description',
                'prompt' => "Generate content for confirming a successful payment transfer. Write a message to inform the recipient that the payment transfer has been successfully completed. The content should be concise, informative. Include the necessary  details,##note## to convey the successful transfe information.plase not cotent should be without header,footer",
                'module' => 'transfer',
                'field_json' => '{"field":[{"label":"Notes","placeholder":"e.g. any notes","field_type":"textarea","field_name":"note"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'name',
                'prompt' => "please suggest subscription plan  name  for this  :  ##description##  for my business",
                'module' => 'plan',
                'field_json' => '{"field":[{"label":"What is your plan about?","placeholder":"e.g. Describe your plan details ","field_type":"textarea","field_name":"description"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'description',
                'prompt' => "please suggest subscription plan description for this : ##title##: for my business",
                'module' => 'plan',
                'field_json' => '{"field":[{"label":"What is your plan title?","placeholder":"e.g. Pro Resller,Exclusive Access","field_type":"text_box","field_name":"title"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'name',
                'prompt' => "give 10 catchy only name of Offer or discount Coupon for : ##keywords##",
                'module' => 'coupon',
                'field_json' => '{"field":[{"label":"Seed words","placeholder":"e.g. ","field_type":"text_box","field_name":"keywords"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'meta_keywords',
                'prompt' => "Write SEO meta title for:\n\n ##description## \n\nWebsite name is:\n ##title## \n\nSeed words:\n ##keywords## \n\n",
                'module' => 'seo settings',
                'field_json' => '{"field":[{"label":"Website Name","placeholder":"e.g. Amazon, Google","field_type":"text_box","field_name":"title"},{"label":"Website Description","placeholder":"e.g. Describe what your website or business do","field_type":"textarea","field_name":"description"},{"label":"Keywords","placeholder":"e.g.  cloud services, databases","field_type":"text_box","field_name":"keywords"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'meta_description',
                'prompt' => "Write SEO meta description for:\n\n ##description## \n\nWebsite name is:\n ##title## \n\nSeed words:\n ##keywords## \n\n",
                'module' => 'seo settings',
                'field_json' => '{"field":[{"label":"Seed words","placeholder":"e.g.  Store","field_type":"text_box","field_name":"keywords"},{"label":"Store Description","placeholder":"e.g. Store product details","field_type":"textarea","field_name":"description"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'cookie_title',
                'prompt' => "please suggest me cookie title for this ##description## website which i can use in my website cookie",
                'module' => 'cookie',
                'field_json' => '{"field":[{"label":"Website name or info","placeholder":"e.g. example website ","field_type":"textarea","field_name":"title"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'cookie_description',
                'prompt' => "please suggest me  Cookie description for this cookie title ##title## which i can use in my website cookie",
                'module' => 'cookie',
                'field_json' => '{"field":[{"label":"Cookie Title ","placeholder":"e.g. example website ","field_type":"text_box","field_name":"title"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'strictly_cookie_title',
                'prompt' => "please suggest me only Strictly Cookie Title for this ##description##  website which i can use in my website cookie",
                'module' => 'cookie',
                'field_json' => '{"field":[{"label":"Website name or info","placeholder":"e.g. example website ","field_type":"textarea","field_name":"title"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'strictly_cookie_description',
                'prompt' => "please suggest me Strictly Cookie description for this Strictly cookie title ##title## which i can use in my website cookie",
                'module' => 'cookie',
                'field_json' => '{"field":[{"label":"Strictly Cookie Title ","placeholder":"e.g. example website ","field_type":"text_box","field_name":"title"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'contactus_url',
                'prompt' => "I need assistance in crafting compelling content for my ##web_name## website's 'Contact Us' page of my website. The page should provide relevant information to users, encourage them to reach out for inquiries, support, and feedback, and reflect the unique value proposition of my business.",
                'module' => 'cookie',
                'field_json' => '{"field":[{"label":"Websit Name","placeholder":"e.g. example website ","field_type":"text_box","field_name":"web_name"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'name',
                'prompt' => "Generate a list of account names for the '##keywords##' category in a company's chart of accounts, specifically focusing on ##type##. These account names should accurately represent various types of ##type## owned by the company and help track their financial value. Ensure that the account names are specific and provide meaningful insights into the company's fixed asset holdings. Aim for a diverse range of fixed asset categories to cover common types of ##keywords## found in businesses.",
                'module' => 'chart of accounts',
                'field_json' => '{"field":[{"label":"Account","placeholder":"e.g.  Asset,Liabilities","field_type":"text_box","field_name":"keywords"},{"label":"Store Description","placeholder":"e.g. Current Asset,Current Liability","field_type":"text_box","field_name":"type"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'description',
                'prompt' => "You are creating a chart of accounts for a company, and one of the entries is 'Inventory ##name## .' Write a descriptive explanation for this chart of accounts entry that accurately conveys its purpose and nature. The description should provide a clear understanding of what Inventory ##name##  encompass within the financial records of the company. Consider explaining the concept of Inventory ##name## , their significance in financial reporting, and any relevant information that would help users of the chart of accounts understand this entry.",
                'module' => 'chart of accounts',
                'field_json' => '{"field":[{"label":"Chart of Account Name","placeholder":"e.g.  Lease Liabilities,Inventory Expenses","field_type":"text_box","field_name":"name"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'description',
                'prompt' => "generate description for this title ##title##",
                'module' => 'journal account',
                'field_json' => '{"field":[{"label":" Title ","placeholder":"e.g.Accounts Receivable,Office Equipment","field_type":"textarea","field_name":"title"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'subject',
                'prompt' => "generate contract subject for this contract description ##description##",
                'module' => 'contract',
                'field_json' => '{"field":[{"label":"Contract Description","placeholder":"e.g.","field_type":"textarea","field_name":"description"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'description',
                'prompt' => "generate contract description for this contract subject ##subject##",
                'module' => 'contract',
                'field_json' => '{"field":[{"label":"Contract Subject","placeholder":"e.g.","field_type":"textarea","field_name":"subject"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'notes',
                'prompt' => "generate contract brief description for title '##name##' and cover all point that sutiable to contract title",
                'module' => 'contract',
                'field_json' => '{"field":[{"label":"Contract Name","placeholder":"e.g. product return condition ","field_type":"text_box","field_name":"name"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'note',
                'prompt' => "generate short and valuable note for contract title '##name##'",
                'module' => 'contract',
                'field_json' => '{"field":[{"label":"Contract Name","placeholder":"e.g. product return condition ","field_type":"text_box","field_name":"name"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'name',
                'prompt' => "list out only just name of asset that can used in ##field_type## .the use of asset must be  for ##description##",
                'module' => 'assets',
                'field_json' => '{"field":[{"label":"Asset Field","placeholder":"IT Company ,hospital,grocery store","field_type":"text_box","field_name":"field_type"},{"label":"Use of asset","placeholder":"website develop,for patient","field_type":"textarea","field_name":"description"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'description',
                'prompt' => "Generate a descriptive response for a given ##title##. The response should be detailed, engaging, and informative, providing relevant information and capturing the reader's interest",
                'module' => 'assets',
                'field_json' => '{"field":[{"label":"Asset name","placeholder":"","field_type":"text_box","field_name":"title"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'name',
                'prompt' => "give list of suitable category name for ##type##",
                'module' => 'category',
                'field_json' => '{"field":[{"label":"Category Type","placeholder":"e.g.product,service,income,expense","field_type":"text_box","field_name":"type"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'name',
                'prompt' => "give suitable form field for ##name## module",
                'module' => 'custom field',
                'field_json' => '{"field":[{"label":"Module Name","placeholder":"e.g. user,contract","field_type":"text_box","field_name":"name"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'content',
                'prompt' => "Generate a meeting notification message for an ##topic## meeting. Include the date, time, location, and a brief agenda with three key discussion points.",
                'module' => 'notification template',
                'field_json' => '{"field":[{"label":"Notification Message","placeholder":"e.g.brief explanation of the purpose or background of the notification","field_type":"textarea","field_name":"topic"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name' => 'content',
                'prompt' => "generate email template for ##type##",
                'module' => 'email template',
                'field_json' => '{"field":[{"label":"Email Type","placeholder":"e.g. new user,new client","field_type":"text_box","field_name":"type"}]}',
                'is_tone' => '0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
        ];

        DB::table('template')->insert($template);
    }
}
