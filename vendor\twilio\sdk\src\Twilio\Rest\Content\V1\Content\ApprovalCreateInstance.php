<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Content
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Content\V1\Content;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Values;
use Twilio\Version;


/**
 * @property string|null $name
 * @property string|null $category
 * @property string|null $contentType
 * @property string|null $status
 * @property string|null $rejectionReason
 * @property bool|null $allowCategoryChange
 */
class ApprovalCreateInstance extends InstanceResource
{
    /**
     * Initialize the ApprovalCreateInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $contentSid 
     */
    public function __construct(Version $version, array $payload, string $contentSid)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'name' => Values::array_get($payload, 'name'),
            'category' => Values::array_get($payload, 'category'),
            'contentType' => Values::array_get($payload, 'content_type'),
            'status' => Values::array_get($payload, 'status'),
            'rejectionReason' => Values::array_get($payload, 'rejection_reason'),
            'allowCategoryChange' => Values::array_get($payload, 'allow_category_change'),
        ];

        $this->solution = ['contentSid' => $contentSid, ];
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Content.V1.ApprovalCreateInstance]';
    }
}

